import type { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { useRef } from 'react';
import { useDatabaseContext } from '../contexts/DatabaseContext';

/**
 * 极简的 SQLite Hook
 * 使用 DatabaseContext 获取数据库连接和状态
 * 不再需要自己管理状态和订阅
 */
export const useSQLiteDB = () => {
  const dbConnection = useRef<SQLiteDBConnection | null>(null);
  const { databaseService, isInitialized, isInitializing, error } = useDatabaseContext();

  // 获取数据库连接
  const getConnection = async (): Promise<SQLiteDBConnection | null> => {
    if (isInitialized && !dbConnection.current) {
      try {
        const connection = await databaseService.getConnection();
        if (connection) {
          dbConnection.current = connection;
        }
        return connection;
      } catch (err) {
        console.error('[useSQLiteDB] Error getting database connection:', err);
        return null;
      }
    }
    return dbConnection.current;
  };

  // 如果已初始化但还没有连接，尝试获取连接
  if (isInitialized && !dbConnection.current) {
    getConnection();
  }

  return {
    db: dbConnection.current,
    dbConnection,
    isDatabaseInitialised: isInitialized,
    isInitializing,
    error,
    dbName: 'mindful_mood_db',
    databaseService,
    getConnection,
  };
};

export function generateId(): string {
  // Generates a unique ID using current timestamp and random string
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 11);
}

export default useSQLiteDB;
