/**
 * Input 组件测试
 * 测试UI输入框组件的各种功能
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Input } from '../input';

// Mock cn function
vi.mock('@/lib/utils', () => ({
  cn: vi.fn((...classes) => classes.filter(Boolean).join(' '))
}));

describe('Input', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render input element', () => {
      render(<Input />);
      
      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });

    it('should render with default type text', () => {
      render(<Input />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'text');
    });

    it('should apply default classes', () => {
      render(<Input />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('flex', 'h-10', 'w-full', 'rounded-md');
    });

    it('should render with custom className', () => {
      render(<Input className="custom-class" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('custom-class');
    });
  });

  describe('Input Types', () => {
    it('should render text input', () => {
      render(<Input type="text" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'text');
    });

    it('should render password input', () => {
      render(<Input type="password" />);
      
      const input = screen.getByDisplayRole('textbox', { hidden: true }) || 
                   document.querySelector('input[type="password"]');
      expect(input).toHaveAttribute('type', 'password');
    });

    it('should render email input', () => {
      render(<Input type="email" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'email');
    });

    it('should render number input', () => {
      render(<Input type="number" />);
      
      const input = screen.getByRole('spinbutton');
      expect(input).toHaveAttribute('type', 'number');
    });

    it('should render search input', () => {
      render(<Input type="search" />);
      
      const input = screen.getByRole('searchbox');
      expect(input).toHaveAttribute('type', 'search');
    });

    it('should render tel input', () => {
      render(<Input type="tel" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'tel');
    });

    it('should render url input', () => {
      render(<Input type="url" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'url');
    });

    it('should render date input', () => {
      render(<Input type="date" />);
      
      const input = document.querySelector('input[type="date"]');
      expect(input).toHaveAttribute('type', 'date');
    });

    it('should render time input', () => {
      render(<Input type="time" />);
      
      const input = document.querySelector('input[type="time"]');
      expect(input).toHaveAttribute('type', 'time');
    });

    it('should render file input', () => {
      render(<Input type="file" />);
      
      const input = document.querySelector('input[type="file"]');
      expect(input).toHaveAttribute('type', 'file');
    });
  });

  describe('Props and Attributes', () => {
    it('should apply placeholder', () => {
      render(<Input placeholder="Enter text here" />);
      
      const input = screen.getByPlaceholderText('Enter text here');
      expect(input).toBeInTheDocument();
    });

    it('should apply value', () => {
      render(<Input value="test value" readOnly />);
      
      const input = screen.getByDisplayValue('test value');
      expect(input).toBeInTheDocument();
    });

    it('should apply defaultValue', () => {
      render(<Input defaultValue="default text" />);
      
      const input = screen.getByDisplayValue('default text');
      expect(input).toBeInTheDocument();
    });

    it('should apply disabled state', () => {
      render(<Input disabled />);
      
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('should apply readOnly state', () => {
      render(<Input readOnly />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('readonly');
    });

    it('should apply required attribute', () => {
      render(<Input required />);
      
      const input = screen.getByRole('textbox');
      expect(input).toBeRequired();
    });

    it('should apply custom id', () => {
      render(<Input id="custom-input" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('id', 'custom-input');
    });

    it('should apply name attribute', () => {
      render(<Input name="username" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('name', 'username');
    });

    it('should apply maxLength', () => {
      render(<Input maxLength={10} />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('maxLength', '10');
    });

    it('should apply minLength', () => {
      render(<Input minLength={3} />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('minLength', '3');
    });
  });

  describe('Event Handling', () => {
    it('should handle onChange events', async () => {
      const handleChange = vi.fn();
      const user = userEvent.setup();
      
      render(<Input onChange={handleChange} />);
      
      const input = screen.getByRole('textbox');
      await user.type(input, 'test');
      
      expect(handleChange).toHaveBeenCalled();
    });

    it('should handle onFocus events', () => {
      const handleFocus = vi.fn();
      render(<Input onFocus={handleFocus} />);
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      expect(handleFocus).toHaveBeenCalledTimes(1);
    });

    it('should handle onBlur events', () => {
      const handleBlur = vi.fn();
      render(<Input onBlur={handleBlur} />);
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      fireEvent.blur(input);
      
      expect(handleBlur).toHaveBeenCalledTimes(1);
    });

    it('should handle onKeyDown events', () => {
      const handleKeyDown = vi.fn();
      render(<Input onKeyDown={handleKeyDown} />);
      
      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'Enter' });
      
      expect(handleKeyDown).toHaveBeenCalledTimes(1);
    });

    it('should handle onKeyUp events', () => {
      const handleKeyUp = vi.fn();
      render(<Input onKeyUp={handleKeyUp} />);
      
      const input = screen.getByRole('textbox');
      fireEvent.keyUp(input, { key: 'Enter' });
      
      expect(handleKeyUp).toHaveBeenCalledTimes(1);
    });

    it('should handle onKeyPress events', () => {
      const handleKeyPress = vi.fn();
      render(<Input onKeyPress={handleKeyPress} />);
      
      const input = screen.getByRole('textbox');
      fireEvent.keyPress(input, { key: 'a' });
      
      expect(handleKeyPress).toHaveBeenCalledTimes(1);
    });
  });

  describe('User Interactions', () => {
    it('should allow typing text', async () => {
      const user = userEvent.setup();
      render(<Input />);
      
      const input = screen.getByRole('textbox');
      await user.type(input, 'Hello World');
      
      expect(input).toHaveValue('Hello World');
    });

    it('should allow clearing text', async () => {
      const user = userEvent.setup();
      render(<Input defaultValue="Initial text" />);
      
      const input = screen.getByRole('textbox');
      await user.clear(input);
      
      expect(input).toHaveValue('');
    });

    it('should allow selecting text', async () => {
      const user = userEvent.setup();
      render(<Input defaultValue="Select this text" />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.keyboard('{Control>}a{/Control}');
      
      expect(input.selectionStart).toBe(0);
      expect(input.selectionEnd).toBe(16);
    });

    it('should handle paste events', async () => {
      const user = userEvent.setup();
      render(<Input />);
      
      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.paste('Pasted text');
      
      expect(input).toHaveValue('Pasted text');
    });
  });

  describe('Accessibility', () => {
    it('should be focusable', () => {
      render(<Input />);
      
      const input = screen.getByRole('textbox');
      input.focus();
      expect(document.activeElement).toBe(input);
    });

    it('should not be focusable when disabled', () => {
      render(<Input disabled />);
      
      const input = screen.getByRole('textbox');
      input.focus();
      expect(document.activeElement).not.toBe(input);
    });

    it('should support aria-label', () => {
      render(<Input aria-label="Username input" />);
      
      const input = screen.getByLabelText('Username input');
      expect(input).toBeInTheDocument();
    });

    it('should support aria-describedby', () => {
      render(
        <>
          <Input aria-describedby="help-text" />
          <div id="help-text">Enter your username</div>
        </>
      );
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-describedby', 'help-text');
    });

    it('should support aria-invalid', () => {
      render(<Input aria-invalid="true" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });

    it('should work with labels', () => {
      render(
        <>
          <label htmlFor="username">Username</label>
          <Input id="username" />
        </>
      );
      
      const input = screen.getByLabelText('Username');
      expect(input).toBeInTheDocument();
    });
  });

  describe('Form Integration', () => {
    it('should work in forms', () => {
      const handleSubmit = vi.fn((e) => e.preventDefault());
      
      render(
        <form onSubmit={handleSubmit}>
          <Input name="username" defaultValue="testuser" />
          <button type="submit">Submit</button>
        </form>
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleSubmit).toHaveBeenCalledTimes(1);
    });

    it('should validate required fields', async () => {
      const user = userEvent.setup();
      
      render(
        <form>
          <Input required name="required-field" />
          <button type="submit">Submit</button>
        </form>
      );
      
      const input = screen.getByRole('textbox');
      const button = screen.getByRole('button');
      
      await user.click(button);
      
      expect(input).toBeInvalid();
    });

    it('should handle form reset', () => {
      render(
        <form>
          <Input defaultValue="initial" name="test-input" />
          <button type="reset">Reset</button>
        </form>
      );
      
      const input = screen.getByRole('textbox');
      const resetButton = screen.getByRole('button');
      
      // Change the value
      fireEvent.change(input, { target: { value: 'changed' } });
      expect(input).toHaveValue('changed');
      
      // Reset the form
      fireEvent.click(resetButton);
      expect(input).toHaveValue('initial');
    });
  });

  describe('Number Input Specific', () => {
    it('should handle min and max for number inputs', () => {
      render(<Input type="number" min={0} max={100} />);
      
      const input = screen.getByRole('spinbutton');
      expect(input).toHaveAttribute('min', '0');
      expect(input).toHaveAttribute('max', '100');
    });

    it('should handle step for number inputs', () => {
      render(<Input type="number" step={0.1} />);
      
      const input = screen.getByRole('spinbutton');
      expect(input).toHaveAttribute('step', '0.1');
    });
  });

  describe('File Input Specific', () => {
    it('should handle accept attribute for file inputs', () => {
      render(<Input type="file" accept=".jpg,.png" />);
      
      const input = document.querySelector('input[type="file"]');
      expect(input).toHaveAttribute('accept', '.jpg,.png');
    });

    it('should handle multiple attribute for file inputs', () => {
      render(<Input type="file" multiple />);
      
      const input = document.querySelector('input[type="file"]');
      expect(input).toHaveAttribute('multiple');
    });
  });

  describe('Forward Ref', () => {
    it('should forward ref correctly', () => {
      const ref = { current: null };
      
      render(<Input ref={ref} />);
      
      expect(ref.current).toBeInstanceOf(HTMLInputElement);
    });

    it('should allow ref methods to be called', () => {
      let inputRef: HTMLInputElement | null = null;
      
      render(<Input ref={(el) => { inputRef = el; }} />);
      
      expect(inputRef).toBeInstanceOf(HTMLInputElement);
      expect(inputRef?.tagName).toBe('INPUT');
    });

    it('should allow calling focus through ref', () => {
      let inputRef: HTMLInputElement | null = null;
      
      render(<Input ref={(el) => { inputRef = el; }} />);
      
      inputRef?.focus();
      expect(document.activeElement).toBe(inputRef);
    });
  });

  describe('Error Handling', () => {
    it('should handle undefined onChange gracefully', () => {
      render(<Input onChange={undefined} />);
      
      const input = screen.getByRole('textbox');
      expect(() => fireEvent.change(input, { target: { value: 'test' } })).not.toThrow();
    });

    it('should handle null value', () => {
      render(<Input value={null as any} readOnly />);
      
      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });

    it('should handle undefined value', () => {
      render(<Input value={undefined} />);
      
      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('should not cause unnecessary re-renders', () => {
      const renderSpy = vi.fn();
      
      const TestInput = (props: any) => {
        renderSpy();
        return <Input {...props} />;
      };
      
      const { rerender } = render(<TestInput />);
      expect(renderSpy).toHaveBeenCalledTimes(1);
      
      // Re-render with same props
      rerender(<TestInput />);
      expect(renderSpy).toHaveBeenCalledTimes(2);
    });

    it('should handle rapid typing', async () => {
      const handleChange = vi.fn();
      const user = userEvent.setup();
      
      render(<Input onChange={handleChange} />);
      
      const input = screen.getByRole('textbox');
      await user.type(input, 'rapid typing test');
      
      expect(handleChange).toHaveBeenCalled();
      expect(input).toHaveValue('rapid typing test');
    });
  });
});
