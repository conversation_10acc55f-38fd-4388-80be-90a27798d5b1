/**
 * Translation Key Fixer
 *
 * This script not only detects missing translation keys but also generates
 * SQL and JSON updates to fix the issues.
 */

import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  srcDir: path.resolve(__dirname, '../src'),
  seedDir: path.resolve(__dirname, '../public/seeds'),
  localesDir: path.resolve(__dirname, '../src/locales'),
  uiLabelsSeedFile: 'ui_labels.sql',
  uiLabelTranslationsSeedFile: 'ui_label_translations.sql',
  jsonFiles: ['en.json', 'zh.json'],
  fileExtensions: ['.tsx', '.ts'],
  excludeDirs: ['node_modules', 'dist', 'build', '.git'],
  translationFunctionNames: ['t'],
  outputDir: path.resolve(__dirname, '../translation-fixes'),
  languages: ['en', 'zh'],
};

// Regular expressions for finding translation keys
const createTranslationKeyRegex = (funcName) => {
  return new RegExp(`${funcName}\\(\\s*['"]([^'"]+)['"]`, 'g');
};

// Regular expressions for extracting keys from SQL files
const uiLabelsKeyRegex =
  /INSERT\s+OR\s+IGNORE\s+INTO\s+ui_labels\s+\(\s*key\s*,\s*default_text\s*\)\s+VALUES\s*\(?\s*['"]([^'"]+)['"],\s*['"]([^'"]+)['"]/g;
const uiLabelTranslationsKeyRegex =
  /INSERT\s+OR\s+IGNORE\s+INTO\s+ui_label_translations\s+\(\s*label_key\s*,\s*language_code\s*,\s*translated_text\s*\)\s+VALUES\s*\(?\s*['"]([^'"]+)['"],\s*['"]([^'"]+)['"],\s*['"]([^'"]+)['"]/g;

/**
 * Recursively scans a directory for files with specified extensions
 */
async function scanDirectory(dir, extensions, excludeDirs) {
  const files = [];
  const entries = await fs.readdir(dir);

  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stats = await fs.stat(fullPath);

    if (stats.isDirectory()) {
      if (!excludeDirs.includes(entry)) {
        const subDirFiles = await scanDirectory(fullPath, extensions, excludeDirs);
        files.push(...subDirFiles);
      }
    } else if (stats.isFile() && extensions.some((ext) => entry.endsWith(ext))) {
      files.push(fullPath);
    }
  }

  return files;
}

/**
 * Extracts translation keys from code files
 */
async function extractKeysFromCode(files, translationFunctionNames) {
  const keys = new Set();
  const keyLocations = new Map(); // Track where each key is used

  for (const file of files) {
    const content = await fs.readFile(file, 'utf8');
    const relativePath = path.relative(config.srcDir, file);

    for (const funcName of translationFunctionNames) {
      const regex = createTranslationKeyRegex(funcName);
      let match;

      while ((match = regex.exec(content)) !== null) {
        const key = match[1];
        keys.add(key);

        if (!keyLocations.has(key)) {
          keyLocations.set(key, []);
        }
        keyLocations
          .get(key)
          .push({ file: relativePath, line: getLineNumber(content, match.index) });
      }
    }
  }

  return { keys, keyLocations };
}

/**
 * Gets the line number for a position in text
 */
function getLineNumber(text, position) {
  const lines = text.substring(0, position).split('\n');
  return lines.length;
}

/**
 * Extracts keys and their default text from SQL seed files
 */
async function extractKeysFromSeedFiles(seedDir, uiLabelsFile, uiLabelTranslationsFile) {
  const keys = new Set();
  const keyToDefaultText = new Map();
  const keyToTranslations = new Map();

  // Extract from ui_labels.sql
  const uiLabelsPath = path.join(seedDir, uiLabelsFile);
  try {
    await fs.access(uiLabelsPath);
    const content = await fs.readFile(uiLabelsPath, 'utf8');
    let match;

    while ((match = uiLabelsKeyRegex.exec(content)) !== null) {
      const key = match[1];
      const defaultText = match[2];
      keys.add(key);
      keyToDefaultText.set(key, defaultText);
    }
  } catch (error) {
    // File doesn't exist or can't be accessed
    console.warn(`Warning: Could not access ${uiLabelsPath}`);
  }

  // Extract from ui_label_translations.sql
  const uiLabelTranslationsPath = path.join(seedDir, uiLabelTranslationsFile);
  try {
    await fs.access(uiLabelTranslationsPath);
    const content = await fs.readFile(uiLabelTranslationsPath, 'utf8');
    let match;

    while ((match = uiLabelTranslationsKeyRegex.exec(content)) !== null) {
      const key = match[1];
      const lang = match[2];
      const text = match[3];
      keys.add(key);

      if (!keyToTranslations.has(key)) {
        keyToTranslations.set(key, new Map());
      }
      keyToTranslations.get(key).set(lang, text);
    }
  } catch (error) {
    // File doesn't exist or can't be accessed
    console.warn(`Warning: Could not access ${uiLabelTranslationsPath}`);
  }

  return { keys, keyToDefaultText, keyToTranslations };
}

/**
 * Extracts keys from JSON translation files
 */
async function extractKeysFromJsonFiles(localesDir, jsonFiles) {
  const keys = new Set();
  const keyToJsonTranslations = new Map();
  const jsonContents = new Map();

  for (const jsonFile of jsonFiles) {
    const jsonPath = path.join(localesDir, jsonFile);
    try {
      await fs.access(jsonPath);
      const content = await fs.readFile(jsonPath, 'utf8');
      const json = JSON.parse(content);
      const lang = jsonFile.replace('.json', '');
      jsonContents.set(lang, json);

      // Add all keys from the flattened JSON
      const flattenedKeys = flattenObject(json);
      Object.entries(flattenedKeys).forEach(([key, value]) => {
        keys.add(key);

        if (!keyToJsonTranslations.has(key)) {
          keyToJsonTranslations.set(key, new Map());
        }
        keyToJsonTranslations.get(key).set(lang, value);
      });
    } catch (error) {
      // File doesn't exist or can't be accessed
      console.warn(`Warning: Could not access ${jsonPath}`);
    }
  }

  return { keys, keyToJsonTranslations, jsonContents };
}

/**
 * Flattens a nested object into a single-level object with dot-notation keys
 */
function flattenObject(obj, prefix = '') {
  return Object.keys(obj).reduce((acc, key) => {
    const prefixedKey = prefix ? `${prefix}.${key}` : key;

    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      Object.assign(acc, flattenObject(obj[key], prefixedKey));
    } else {
      acc[prefixedKey] = obj[key];
    }

    return acc;
  }, {});
}

/**
 * Compares sets and returns differences
 */
function compareSets(setA, setB, labelA, labelB) {
  const onlyInA = [...setA].filter((key) => !setB.has(key));
  const onlyInB = [...setB].filter((key) => !setA.has(key));

  return {
    [`onlyIn${labelA}`]: onlyInA,
    [`onlyIn${labelB}`]: onlyInB,
  };
}

/**
 * Generates SQL statements to add missing keys to seed files
 */
function generateSqlFixes(missingInSeed, keyToJsonTranslations, languages) {
  let uiLabelsSql = '';
  let uiLabelTranslationsSql = '';

  for (const key of missingInSeed) {
    // Get default text from JSON (use English or first available)
    let defaultText = key;
    if (keyToJsonTranslations.has(key) && keyToJsonTranslations.get(key).has('en')) {
      defaultText = keyToJsonTranslations.get(key).get('en');
    }

    // Add to ui_labels.sql
    uiLabelsSql += `INSERT OR IGNORE INTO ui_labels (key, default_text) VALUES ('${key}', '${escapeSQL(defaultText)}');\n`;

    // Add to ui_label_translations.sql for each language
    for (const lang of languages) {
      let translatedText = defaultText;
      if (keyToJsonTranslations.has(key) && keyToJsonTranslations.get(key).has(lang)) {
        translatedText = keyToJsonTranslations.get(key).get(lang);
      }

      uiLabelTranslationsSql += `INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('${key}', '${lang}', '${escapeSQL(translatedText)}');\n`;
    }
  }

  return { uiLabelsSql, uiLabelTranslationsSql };
}

/**
 * Escapes special characters in SQL strings
 */
function escapeSQL(str) {
  return str.replace(/'/g, "''");
}

/**
 * Generates JSON updates for missing keys
 */
function generateJsonFixes(missingInJson, keyToDefaultText, keyToTranslations, jsonContents) {
  const jsonFixes = new Map();

  for (const [lang, content] of jsonContents.entries()) {
    const updatedContent = { ...content };

    for (const key of missingInJson) {
      // Get translation from seed files or use default text
      let translatedText = key;

      if (keyToTranslations.has(key) && keyToTranslations.get(key).has(lang)) {
        translatedText = keyToTranslations.get(key).get(lang);
      } else if (lang === 'en' && keyToDefaultText.has(key)) {
        translatedText = keyToDefaultText.get(key);
      }

      // Add to JSON
      updatedContent[key] = translatedText;
    }

    jsonFixes.set(lang, updatedContent);
  }

  return jsonFixes;
}

/**
 * Ensures output directory exists
 */
async function ensureOutputDir(dir) {
  try {
    await fs.access(dir);
  } catch (error) {
    await fs.mkdir(dir, { recursive: true });
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('Scanning for translation keys...');

    // Scan code files
    const codeFiles = await scanDirectory(config.srcDir, config.fileExtensions, config.excludeDirs);
    console.log(`Found ${codeFiles.length} code files to scan.`);

    // Extract keys from different sources
    const { keys: codeKeys, keyLocations } = await extractKeysFromCode(
      codeFiles,
      config.translationFunctionNames
    );
    console.log(`Found ${codeKeys.size} unique translation keys in code.`);

    const {
      keys: seedKeys,
      keyToDefaultText,
      keyToTranslations,
    } = await extractKeysFromSeedFiles(
      config.seedDir,
      config.uiLabelsSeedFile,
      config.uiLabelTranslationsSeedFile
    );
    console.log(`Found ${seedKeys.size} unique translation keys in seed files.`);

    const {
      keys: jsonKeys,
      keyToJsonTranslations,
      jsonContents,
    } = await extractKeysFromJsonFiles(config.localesDir, config.jsonFiles);
    console.log(`Found ${jsonKeys.size} unique translation keys in JSON files.`);

    // Compare keys
    const codeVsSeed = compareSets(codeKeys, seedKeys, 'Code', 'Seed');
    const codeVsJson = compareSets(codeKeys, jsonKeys, 'Code', 'Json');
    const seedVsJson = compareSets(seedKeys, jsonKeys, 'Seed', 'Json');

    // Output results
    console.log('\n=== TRANSLATION KEY ANALYSIS ===\n');

    console.log('Keys used in code but missing from seed files:');
    if (codeVsSeed.onlyInCode.length > 0) {
      codeVsSeed.onlyInCode.forEach((key) => {
        const locations = keyLocations.get(key);
        const locationStr = locations
          ? locations.map((loc) => `${loc.file}:${loc.line}`).join(', ')
          : 'unknown';
        console.log(`  - ${key} (used in: ${locationStr})`);
      });
    } else {
      console.log('  None');
    }

    console.log('\nKeys used in code but missing from JSON files:');
    if (codeVsJson.onlyInCode.length > 0) {
      codeVsJson.onlyInCode.forEach((key) => {
        const locations = keyLocations.get(key);
        const locationStr = locations
          ? locations.map((loc) => `${loc.file}:${loc.line}`).join(', ')
          : 'unknown';
        console.log(`  - ${key} (used in: ${locationStr})`);
      });
    } else {
      console.log('  None');
    }

    // Generate fixes
    if (codeVsSeed.onlyInCode.length > 0 || codeVsJson.onlyInCode.length > 0) {
      console.log('\n=== GENERATING FIXES ===\n');

      // Ensure output directory exists
      await ensureOutputDir(config.outputDir);

      // Generate SQL fixes
      if (codeVsSeed.onlyInCode.length > 0) {
        const { uiLabelsSql, uiLabelTranslationsSql } = generateSqlFixes(
          codeVsSeed.onlyInCode,
          keyToJsonTranslations,
          config.languages
        );

        const uiLabelsSqlPath = path.join(config.outputDir, 'missing_ui_labels.sql');
        const uiLabelTranslationsSqlPath = path.join(
          config.outputDir,
          'missing_ui_label_translations.sql'
        );

        await fs.writeFile(uiLabelsSqlPath, uiLabelsSql);
        await fs.writeFile(uiLabelTranslationsSqlPath, uiLabelTranslationsSql);

        console.log('Generated SQL fixes for missing keys in seed files:');
        console.log(`  - ${uiLabelsSqlPath}`);
        console.log(`  - ${uiLabelTranslationsSqlPath}`);
      }

      // Generate JSON fixes
      if (codeVsJson.onlyInCode.length > 0) {
        const jsonFixes = generateJsonFixes(
          codeVsJson.onlyInCode,
          keyToDefaultText,
          keyToTranslations,
          jsonContents
        );

        for (const [lang, content] of jsonFixes.entries()) {
          const jsonPath = path.join(config.outputDir, `${lang}_updated.json`);
          await fs.writeFile(jsonPath, JSON.stringify(content, null, 2));
          console.log(`Generated JSON fixes for ${lang}: ${jsonPath}`);
        }
      }
    }

    console.log('\n=== ANALYSIS COMPLETE ===');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the script
main();
