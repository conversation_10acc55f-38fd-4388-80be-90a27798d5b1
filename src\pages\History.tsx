import EmotionMapDisplay from '@/components/history/EmotionMapDisplay';
import { GestureContainer, PullToRefresh } from '@/components/ui/gesture';
import { Loading } from '@/components/ui/loading';
import { PageTransition } from '@/components/ui/transition';
import { useLanguage } from '@/contexts/LanguageContext';
import { Services } from '@/services';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

// Quiz会话历史数据类型
interface QuizSession {
  id: string;
  user_id: string;
  pack_id: string;
  status: 'IN_PROGRESS' | 'COMPLETED' | 'ABANDONED';
  start_time: string;
  end_time?: string;
  completion_percentage: number;
  answered_questions: number;
  total_questions: number;
  metadata?: {
    completion_time_seconds?: number;
    emotion_path?: string[];
    total_score?: number;
    severity_level?: string;
  };
}

// Helper to get days in a month (simplified)
const getDaysInMonth = (year: number, month: number) => new Date(year, month + 1, 0).getDate();
const getDayOfWeek = (year: number, month: number, day: number) =>
  new Date(year, month, day).getDay(); // 0 (Sun) - 6 (Sat)

const History = () => {
  const { t } = useLanguage();
  const [view, setView] = useState<'timeline' | 'calendar' | 'map'>('timeline');

  // 新的状态管理 - 使用Quiz会话数据
  const [history, setHistory] = useState<QuizSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // 数据加载 Effect - 使用模拟Quiz会话数据
  useEffect(() => {
    const loadHistory = async () => {
      try {
        setIsLoading(true);

        // 使用真实的Quiz服务
        const quizSessionService = await Services.quizSession();

        // 获取用户的Quiz会话历史（假设用户ID为test_user_001）
        const userId = 'test_user_001'; // 在实际应用中，这应该从用户上下文获取
        const sessionsResult = await quizSessionService.getUserSessions(userId, 50);

        if (!sessionsResult.success) {
          throw new Error(sessionsResult.error);
        }

        const sessions = sessionsResult.data || [];
        setHistory(sessions);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load history';
        toast.error(t('errors.failed_to_load_history', { details: errorMessage }));
      } finally {
        setIsLoading(false);
      }
    };

    loadHistory();
  }, [t]);

  const handleRefresh = async () => {
    try {
      setIsLoading(true);

      // 使用真实的Quiz服务重新加载数据
      const quizSessionService = await Services.quizSession();
      const userId = 'test_user_001'; // 在实际应用中，这应该从用户上下文获取
      const sessionsResult = await quizSessionService.getUserSessions(userId, 50);

      if (!sessionsResult.success) {
        throw new Error(sessionsResult.error);
      }

      const sessions = sessionsResult.data || [];
      setHistory(sessions);
      setSelectedDate(null); // Reset selected date on refresh
      toast.success(t('history.refreshed') || 'History refreshed');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh history';
      toast.error(t('errors.failed_to_load_history', { details: errorMessage }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSwipeLeft = () => {
    if (view === 'timeline') setView('calendar');
    else if (view === 'calendar') setView('map');
  };

  const handleSwipeRight = () => {
    if (view === 'map') setView('calendar');
    else if (view === 'calendar') setView('timeline');
  };

  const entriesByDate = useMemo(() => {
    const map = new Map<string, QuizSession[]>();
    history?.forEach((session) => {
      // 处理时间戳，Quiz会话使用start_time
      const timestamp = new Date(session.start_time);
      const dateStr = timestamp.toISOString().split('T')[0];
      if (!map.has(dateStr)) {
        map.set(dateStr, []);
      }
      map.get(dateStr)?.push(session);
    });
    return map;
  }, [history]);

  const datesWithEntries = useMemo(() => {
    return new Set(Array.from(entriesByDate.keys()));
  }, [entriesByDate]);

  const renderCalendarView = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth(); // 0-11
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getDayOfWeek(year, month, 1); // 0 (Sun) - 6 (Sat)

    const weekdays = [
      t('history.sun'),
      t('history.mon'),
      t('history.tue'),
      t('history.wed'),
      t('history.thu'),
      t('history.fri'),
      t('history.sat'),
    ];

    const blanks = Array(firstDayOfMonth).fill(null);
    const daysArray = Array.from({ length: daysInMonth }, (_, i) => i + 1);

    const selectedDateEntries = selectedDate
      ? entriesByDate.get(selectedDate.toISOString().split('T')[0]) || []
      : [];

    return (
      <div>
        <div className="flex justify-between items-center mb-4">
          <button
            onClick={() => setCurrentMonth(new Date(year, month - 1, 1))}
            className="px-2 py-1 bg-muted rounded"
          >
            &lt;
          </button>
          <h2 className="text-lg font-semibold">
            {currentMonth.toLocaleString('default', { month: 'long' })} {year}
          </h2>
          <button
            onClick={() => setCurrentMonth(new Date(year, month + 1, 1))}
            className="px-2 py-1 bg-muted rounded"
          >
            &gt;
          </button>
        </div>
        <div className="grid grid-cols-7 gap-1 text-center text-sm">
          {weekdays.map((day) => (
            <div key={day} className="font-medium text-muted-foreground">
              {day}
            </div>
          ))}
          {blanks.map((_, i) => (
            <div key={`blank-${i}`} />
          ))}
          {daysArray.map((day) => {
            const dateObj = new Date(year, month, day);
            const dateStr = dateObj.toISOString().split('T')[0];
            const hasEntry = datesWithEntries.has(dateStr);
            const isSelected = selectedDate?.toISOString().split('T')[0] === dateStr;

            return (
              <button
                key={day}
                onClick={() => {
                  if (hasEntry) setSelectedDate(dateObj);
                  else setSelectedDate(null);
                }}
                className={`p-2 rounded-md aspect-square flex items-center justify-center
                  ${hasEntry ? 'bg-primary/20 hover:bg-primary/40' : 'hover:bg-muted/50'}
                  ${isSelected ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : ''}
                  ${!hasEntry && !isSelected ? 'text-muted-foreground' : ''}
                `}
                disabled={
                  !hasEntry &&
                  !isSelected &&
                  !datesWithEntries.has(dateStr) &&
                  selectedDate !== null
                }
              >
                {day}
              </button>
            );
          })}
        </div>
        {selectedDate && selectedDateEntries.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">
              {t('history.entries_for_date', { date: selectedDate.toLocaleDateString() })}
            </h3>
            <div className="space-y-4">{selectedDateEntries.map(renderTimelineEntry)}</div>
          </div>
        )}
        {selectedDate &&
          selectedDateEntries.length === 0 &&
          datesWithEntries.has(selectedDate.toISOString().split('T')[0]) && (
            <div className="mt-6 text-center text-muted-foreground">
              <p>{t('history.no_entries_for_selected_date')}</p>
            </div>
          )}
      </div>
    );
  };

  const renderTimelineEntry = (session: QuizSession) => {
    // 安全处理时间戳
    const startTime = new Date(session.start_time);
    const endTime = session.end_time ? new Date(session.end_time) : null;

    // 验证时间戳有效性
    if (Number.isNaN(startTime.getTime())) {
      console.warn('Invalid timestamp in session:', session.id);
      return null;
    }

    // 获取Quiz类型和结果信息
    const isEmotionQuiz = session.pack_id.includes('mood');
    const isTCMQuiz = session.pack_id.includes('tcm');

    let quizType = 'Quiz';
    let resultInfo = '';

    if (isEmotionQuiz) {
      quizType = 'Emotion Tracking';
      if (session.metadata?.emotion_path) {
        resultInfo = session.metadata.emotion_path.join(' → ');
      }
    } else if (isTCMQuiz) {
      quizType = 'TCM Assessment';
      if (session.metadata?.total_score) {
        resultInfo = `Score: ${session.metadata.total_score} (${session.metadata.severity_level || 'unknown'})`;
      }
    }

    // 状态颜色
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'COMPLETED': return 'text-green-600';
        case 'IN_PROGRESS': return 'text-yellow-600';
        case 'ABANDONED': return 'text-red-600';
        default: return 'text-gray-600';
      }
    };

    // 计算持续时间
    const getDuration = () => {
      if (session.metadata?.completion_time_seconds) {
        const minutes = Math.floor(session.metadata.completion_time_seconds / 60);
        const seconds = session.metadata.completion_time_seconds % 60;
        return `${minutes}m ${seconds}s`;
      }
      return 'N/A';
    };

    return (
      <div key={session.id} className="p-4 rounded-lg border bg-card">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-muted-foreground">
            {startTime.toLocaleDateString()} {startTime.toLocaleTimeString()}
          </span>
          <span className={`font-medium text-sm ${getStatusColor(session.status)}`}>
            {session.status}
          </span>
        </div>

        <div className="flex justify-between items-center mb-2">
          <span className="font-medium">{quizType}</span>
          <span className="text-sm text-muted-foreground">
            {session.completion_percentage}% ({session.answered_questions}/{session.total_questions})
          </span>
        </div>

        {resultInfo && (
          <p className="mt-2 text-sm font-medium text-primary">{resultInfo}</p>
        )}

        <div className="mt-2 flex justify-between text-xs text-muted-foreground">
          <span>Duration: {getDuration()}</span>
          {endTime && (
            <span>Ended: {endTime.toLocaleTimeString()}</span>
          )}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return <Loading size="lg" text={t('app.loading')} />;
  }

  return (
    <PageTransition>
      <div className="container mx-auto p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">{t('history.title')}</h1>
          <div className="flex gap-2">
            <button
              onClick={() => setView('timeline')}
              className={`px-3 py-1 rounded-full text-sm ${
                view === 'timeline'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted hover:bg-muted-foreground/20'
              }`}
            >
              {t('history.timeline')}
            </button>
            <button
              onClick={() => setView('calendar')}
              className={`px-3 py-1 rounded-full text-sm ${
                view === 'calendar'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted hover:bg-muted-foreground/20'
              }`}
            >
              {t('history.calendar')}
            </button>
            <button
              onClick={() => setView('map')}
              className={`px-3 py-1 rounded-full text-sm ${
                view === 'map'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted hover:bg-muted-foreground/20'
              }`}
            >
              {t('history.map')}
            </button>
          </div>
        </div>

        <PullToRefresh onRefresh={handleRefresh}>
          <GestureContainer
            onSwipeLeft={handleSwipeLeft}
            onSwipeRight={handleSwipeRight}
            className="min-h-[60vh]"
          >
            {view === 'timeline' &&
              (history?.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[60vh] text-muted-foreground">
                  <p>{t('history.no_entries')}</p>
                  <p className="text-sm mt-2">{t('history.empty_state')}</p>
                </div>
              ) : (
                <div className="space-y-4">{history?.map(renderTimelineEntry)}</div>
              ))}
            {view === 'calendar' &&
              (history?.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[60vh] text-muted-foreground">
                  <p>{t('history.no_entries_for_calendar')}</p>
                  <p className="text-sm mt-2">{t('history.empty_state_calendar')}</p>
                </div>
              ) : (
                renderCalendarView()
              ))}
            {view === 'map' && <EmotionMapDisplay />}
          </GestureContainer>
        </PullToRefresh>
      </div>
    </PageTransition>
  );
};

export default History;
