import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import { AlertCircle, Home, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ErrorPageProps {
  type?: '404' | '500' | 'network' | 'auth' | 'unknown';
  message?: string;
  retryAction?: () => void;
}

const ErrorPage = ({ type = 'unknown', message, retryAction }: ErrorPageProps) => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  const getErrorContent = () => {
    switch (type) {
      case '404':
        return {
          icon: '🔍',
          title: '404',
          message: message || t('error.not_found'),
          action: () => navigate('/'),
        };
      case '500':
        return {
          icon: '💥',
          title: '500',
          message: message || t('error.server_error'),
          action: () => window.location.reload(),
        };
      case 'network':
        return {
          icon: '📡',
          title: t('error.network_error'),
          message: message || t('error.network_message'),
          action: retryAction || (() => window.location.reload()),
        };
      case 'auth':
        return {
          icon: '🔒',
          title: t('error.auth_error'),
          message: message || t('error.auth_message'),
          action: () => navigate('/login'),
        };
      default:
        return {
          icon: '❓',
          title: t('error.unknown_error'),
          message: message || t('error.unknown_message'),
          action: () => navigate('/'),
        };
    }
  };

  const content = getErrorContent();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 text-center bg-background">
      <div className="w-full max-w-md space-y-6">
        <div className="text-6xl mb-4 animate-bounce">{content.icon}</div>

        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">{content.title}</h1>
          <p className="text-xl text-muted-foreground">{content.message}</p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="default" size="lg" onClick={content.action} className="w-full sm:w-auto">
            {type === 'network' ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                {t('error.retry')}
              </>
            ) : (
              <>
                <Home className="mr-2 h-4 w-4" />
                {t('error.go_home')}
              </>
            )}
          </Button>

          <Button
            variant="outline"
            size="lg"
            onClick={() => navigate(-1)}
            className="w-full sm:w-auto"
          >
            {t('error.go_back')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ErrorPage;
