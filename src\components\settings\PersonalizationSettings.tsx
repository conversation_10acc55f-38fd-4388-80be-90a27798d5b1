import { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useTheme } from "@/contexts/ThemeContext";
import { useUserConfig } from "@/contexts/UserConfigContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Palette, Moon, Sun, Monitor, ChevronDown, ChevronRight,
  Sparkles, Eye, Brush, Wand2, Crown, Settings
} from "lucide-react";
import { toast } from "sonner";

// 导入详细配置组件
import AdvancedViewConfigOptions from "@/components/settings/AdvancedViewConfigOptions";
import ColorModeSelector from "@/components/settings/ColorModeSelector";
import SkinSelector from "@/components/settings/SkinSelector";

interface PersonalizationSettingsProps {
  userLevel: 'beginner' | 'regular' | 'advanced' | 'vip';
}

const PersonalizationSettings: React.FC<PersonalizationSettingsProps> = ({ userLevel }) => {
  const { t } = useLanguage();
  const { theme, setTheme } = useTheme();
  const { userConfig, updateUserConfig } = useUserConfig();

  // Collapsible 状态管理
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    theme: true,
    colorMode: userLevel !== 'beginner',
    viewType: userLevel !== 'beginner',
    skinCustomization: false,
    advancedOptions: false
  });

  // 切换 collapsible 状态
  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 处理主题更改
  const handleThemeChange = (newTheme: "light" | "dark" | "system") => {
    setTheme(newTheme);
    toast.success(t('settings.theme_changed', '主题已更改'));
  };

  // 处理深色模式切换
  const handleDarkModeToggle = (enabled: boolean) => {
    updateUserConfig({ dark_mode: enabled });
    toast.success(t('settings.dark_mode_changed', '深色模式已更改'));
  };

  // 处理颜色模式更改
  const handleColorModeChange = (colorMode: string) => {
    updateUserConfig({ color_mode: colorMode });
    toast.success(t('settings.color_mode_changed', '颜色模式已更改'));
  };

  // 处理视图类型更改
  const handleViewTypeChange = (viewType: string) => {
    updateUserConfig({ preferred_view_type: viewType });
    toast.success(t('settings.view_type_changed', '视图类型已更改'));
  };

  // 渲染配置预设
  const renderConfigPresets = () => {
    if (userLevel === 'beginner') {
      return (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Sparkles className="h-5 w-5" />
              <span>{t('settings.quick_setup', '快速设置')}</span>
              <Badge variant="secondary">{t('settings.recommended', '推荐')}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-start space-y-2"
                onClick={() => {
                  updateUserConfig({
                    dark_mode: false,
                    color_mode: 'warm',
                    preferred_view_type: 'wheel'
                  });
                  toast.success(t('settings.preset_applied', '预设已应用'));
                }}
              >
                <div className="flex items-center space-x-2">
                  <Sun className="h-4 w-4" />
                  <span className="font-medium">{t('settings.preset.comfortable', '舒适模式')}</span>
                </div>
                <p className="text-xs text-muted-foreground text-left">
                  {t('settings.preset.comfortable.desc', '浅色主题 + 暖色调 + 轮盘视图')}
                </p>
              </Button>

              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-start space-y-2"
                onClick={() => {
                  updateUserConfig({
                    dark_mode: true,
                    color_mode: 'cool',
                    preferred_view_type: 'card'
                  });
                  toast.success(t('settings.preset_applied', '预设已应用'));
                }}
              >
                <div className="flex items-center space-x-2">
                  <Moon className="h-4 w-4" />
                  <span className="font-medium">{t('settings.preset.focus', '专注模式')}</span>
                </div>
                <p className="text-xs text-muted-foreground text-left">
                  {t('settings.preset.focus.desc', '深色主题 + 冷色调 + 卡片视图')}
                </p>
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }
    return null;
  };

  // 渲染基础主题设置
  const renderThemeSettings = () => (
    <Collapsible open={openSections.theme} onOpenChange={() => toggleSection('theme')}>
      <CollapsibleTrigger asChild>
        <Button variant="ghost" className="w-full justify-between p-4 h-auto">
          <div className="flex items-center space-x-2">
            <Palette className="h-5 w-5" />
            <span className="font-medium">{t('settings.theme', '主题设置')}</span>
          </div>
          {openSections.theme ?
            <ChevronDown className="h-4 w-4" /> :
            <ChevronRight className="h-4 w-4" />
          }
        </Button>
      </CollapsibleTrigger>

      <CollapsibleContent className="px-4 pb-4">
        <div className="space-y-4 pt-2">
          {/* 系统主题选择 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              {t('settings.system_theme', '系统主题')}
            </Label>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant={theme === "light" ? "default" : "outline"}
                size="sm"
                onClick={() => handleThemeChange("light")}
                className="flex items-center space-x-1"
              >
                <Sun className="h-4 w-4" />
                <span>{t('settings.light', '浅色')}</span>
              </Button>
              <Button
                variant={theme === "dark" ? "default" : "outline"}
                size="sm"
                onClick={() => handleThemeChange("dark")}
                className="flex items-center space-x-1"
              >
                <Moon className="h-4 w-4" />
                <span>{t('settings.dark', '深色')}</span>
              </Button>
              <Button
                variant={theme === "system" ? "default" : "outline"}
                size="sm"
                onClick={() => handleThemeChange("system")}
                className="flex items-center space-x-1"
              >
                <Monitor className="h-4 w-4" />
                <span>{t('settings.auto', '自动')}</span>
              </Button>
            </div>
          </div>

          {/* 深色模式开关 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('settings.dark_mode', '深色模式')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.dark_mode.desc', '覆盖系统主题设置')}
              </p>
            </div>
            <Switch
              checked={userConfig?.dark_mode || false}
              onCheckedChange={handleDarkModeToggle}
            />
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-2">
        <Palette className="h-6 w-6" />
        <h2 className="text-2xl font-bold">
          {t('settings.personalization', '个性化设置')}
        </h2>
        {userLevel === 'vip' && (
          <Badge variant="destructive" className="flex items-center space-x-1">
            <Crown className="h-3 w-3" />
            <span>VIP</span>
          </Badge>
        )}
      </div>

      {/* 配置预设 (仅新手用户) */}
      {renderConfigPresets()}

      {/* 主要设置卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>{t('settings.appearance', '外观设置')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 基础主题设置 */}
          {renderThemeSettings()}

          <Separator />

          {/* 颜色模式设置 */}
          {userLevel !== 'beginner' && (
            <>
              <ColorModeSelector
                userLevel={userLevel}
                currentColorMode={userConfig?.color_mode || 'warm'}
                onColorModeChange={handleColorModeChange}
                isOpen={openSections.colorMode}
                onToggle={() => toggleSection('colorMode')}
              />
              <Separator />
            </>
          )}

          {/* 视图类型设置 */}
          {userLevel !== 'beginner' && (
            <>
              <Collapsible open={openSections.viewType} onOpenChange={() => toggleSection('viewType')}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="w-full justify-between p-4 h-auto">
                    <div className="flex items-center space-x-2">
                      <Settings className="h-5 w-5" />
                      <span className="font-medium">{t('settings.view_type', '视图类型')}</span>
                    </div>
                    {openSections.viewType ?
                      <ChevronDown className="h-4 w-4" /> :
                      <ChevronRight className="h-4 w-4" />
                    }
                  </Button>
                </CollapsibleTrigger>

                <CollapsibleContent className="px-4 pb-4">
                  <div className="space-y-4 pt-2">
                    <Select
                      value={userConfig?.preferred_view_type || 'wheel'}
                      onValueChange={handleViewTypeChange}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="wheel">{t('view_type.wheel', '轮盘视图')}</SelectItem>
                        <SelectItem value="card">{t('view_type.card', '卡片视图')}</SelectItem>
                        <SelectItem value="bubble">{t('view_type.bubble', '气泡视图')}</SelectItem>
                        {(userLevel === 'advanced' || userLevel === 'vip') && (
                          <>
                            <SelectItem value="galaxy">{t('view_type.galaxy', '星系视图')}</SelectItem>
                            <SelectItem value="tree">{t('view_type.tree', '树形视图')}</SelectItem>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </CollapsibleContent>
              </Collapsible>
              <Separator />
            </>
          )}
        </CardContent>
      </Card>

      {/* 皮肤自定义 */}
      {(userLevel === 'advanced' || userLevel === 'vip') && (
        <SkinSelector
          userLevel={userLevel}
          isOpen={openSections.skinCustomization}
          onToggle={() => toggleSection('skinCustomization')}
        />
      )}

      {/* 详细视图配置 */}
      {(userLevel === 'advanced' || userLevel === 'vip') && (
        <AdvancedViewConfigOptions
          userLevel={userLevel}
          viewType={userConfig?.preferred_view_type || 'wheel'}
          isOpen={openSections.advancedOptions}
          onToggle={() => toggleSection('advancedOptions')}
        />
      )}
    </div>
  );
};

export default PersonalizationSettings;
