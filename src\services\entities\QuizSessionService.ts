/**
 * Quiz会话服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 */

import { BaseService } from '../base/BaseService';
import { QuizSessionRepository } from './QuizSessionRepository';
import { QuizSession } from '../../types/schema/base';
import { CreateQuizSessionInput, UpdateQuizSessionInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export interface QuizSessionStats {
  total_sessions: number;
  completed_sessions: number;
  completion_rate: number;
  average_completion_time_minutes: number;
  most_popular_packs: Array<{
    pack_id: string;
    session_count: number;
  }>;
}

export class QuizSessionService extends BaseService<
  QuizSession,
  CreateQuizSessionInput,
  UpdateQuizSessionInput
> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new QuizSessionRepository(db);
    super(repository);
  }

  /**
   * 创建新的Quiz会话
   */
  async createSession(input: CreateQuizSessionInput): Promise<ServiceResult<QuizSession>> {
    try {
      // 验证输入
      await this.validateCreate(input);

      // 调用Repository创建会话
      const session = await this.repository.create(input);

      // 发射业务事件
      this.emit('sessionCreated', session);

      return this.createSuccessResult(session);
    } catch (error) {
      return this.createErrorResult('Failed to create quiz session', error);
    }
  }

  /**
   * 开始Quiz会话
   */
  async startSession(sessionId: string): Promise<ServiceResult<QuizSession>> {
    try {
      const updates: UpdateQuizSessionInput = {
        status: 'IN_PROGRESS'
      };

      const session = await this.repository.update(sessionId, updates);

      this.emit('sessionStarted', session);
      return this.createSuccessResult(session);
    } catch (error) {
      return this.createErrorResult('Failed to start quiz session', error);
    }
  }

  /**
   * 完成Quiz会话
   */
  async completeSession(sessionId: string): Promise<ServiceResult<QuizSession>> {
    try {
      const updates: UpdateQuizSessionInput = {
        status: 'COMPLETED',
        end_time: new Date().toISOString(),
        completion_percentage: 100
      };

      const session = await this.repository.update(sessionId, updates);

      this.emit('sessionCompleted', session);
      return this.createSuccessResult(session);
    } catch (error) {
      return this.createErrorResult('Failed to complete quiz session', error);
    }
  }

  /**
   * 暂停Quiz会话
   */
  async pauseSession(sessionId: string): Promise<ServiceResult<QuizSession>> {
    try {
      const updates: UpdateQuizSessionInput = {
        status: 'PAUSED'
      };

      const session = await this.repository.update(sessionId, updates);

      this.emit('sessionPaused', session);
      return this.createSuccessResult(session);
    } catch (error) {
      return this.createErrorResult('Failed to pause quiz session', error);
    }
  }

  /**
   * 恢复Quiz会话
   */
  async resumeSession(sessionId: string): Promise<ServiceResult<QuizSession>> {
    try {
      const updates: UpdateQuizSessionInput = {
        status: 'IN_PROGRESS'
      };

      const session = await this.repository.update(sessionId, updates);

      this.emit('sessionResumed', session);
      return this.createSuccessResult(session);
    } catch (error) {
      return this.createErrorResult('Failed to resume quiz session', error);
    }
  }

  /**
   * 更新会话进度
   */
  async updateProgress(
    sessionId: string,
    currentQuestionIndex: number,
    totalQuestions?: number
  ): Promise<ServiceResult<boolean>> {
    try {
      const updates: UpdateQuizSessionInput = {
        current_question_index: currentQuestionIndex
      };

      if (totalQuestions !== undefined) {
        updates.total_questions = totalQuestions;
        updates.answered_questions = currentQuestionIndex;

        const completionPercentage = totalQuestions > 0
          ? Math.round((currentQuestionIndex / totalQuestions) * 100)
          : 0;
        updates.completion_percentage = completionPercentage;

        // 自动完成逻辑
        if (completionPercentage >= 100) {
          updates.status = 'COMPLETED';
          updates.end_time = new Date().toISOString();
        }
      }

      await this.repository.update(sessionId, updates);

      this.emit('progressUpdated', { sessionId, currentQuestionIndex, totalQuestions });
      return this.createSuccessResult(true);
    } catch (error) {
      return this.createErrorResult('Failed to update session progress', error);
    }
  }

  /**
   * 获取用户会话
   */
  async getUserSessions(userId: string, limit: number = 20): Promise<ServiceResult<QuizSession[]>> {
    try {
      const sessions = await (this.repository as QuizSessionRepository).findByUserId(userId);
      return this.createSuccessResult(sessions.slice(0, limit));
    } catch (error) {
      return this.createErrorResult('Failed to get user sessions', error);
    }
  }

  /**
   * 获取用户活跃会话
   */
  async getUserActiveSessions(userId: string): Promise<ServiceResult<QuizSession[]>> {
    try {
      const sessions = await (this.repository as QuizSessionRepository).findActiveByUserId(userId);
      return this.createSuccessResult(sessions);
    } catch (error) {
      return this.createErrorResult('Failed to get user active sessions', error);
    }
  }

  /**
   * 获取用户已完成会话
   */
  async getUserCompletedSessions(userId: string, limit: number = 20): Promise<ServiceResult<QuizSession[]>> {
    try {
      const sessions = await (this.repository as QuizSessionRepository).findCompletedByUserId(userId, limit);
      return this.createSuccessResult(sessions);
    } catch (error) {
      return this.createErrorResult('Failed to get user completed sessions', error);
    }
  }

  /**
   * 获取包的会话
   */
  async getPackSessions(packId: string, limit: number = 50): Promise<ServiceResult<QuizSession[]>> {
    try {
      const sessions = await (this.repository as QuizSessionRepository).findByPackId(packId);
      return this.createSuccessResult(sessions.slice(0, limit));
    } catch (error) {
      return this.createErrorResult('Failed to get pack sessions', error);
    }
  }

  /**
   * 获取用户Quiz统计
   */
  async getUserQuizStats(userId: string): Promise<ServiceResult<QuizSessionStats>> {
    try {
      const allSessions = await (this.repository as QuizSessionRepository).findByUserId(userId);
      const completedSessions = allSessions.filter(s => s.status === 'COMPLETED');

      // 计算平均完成时间
      const completionTimes = completedSessions
        .filter(s => s.start_time && s.end_time)
        .map(s => {
          const start = new Date(s.start_time!).getTime();
          const end = new Date(s.end_time!).getTime();
          return (end - start) / (1000 * 60); // 转换为分钟
        });

      const averageCompletionTime = completionTimes.length > 0
        ? Math.round(completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length)
        : 0;

      // 统计最受欢迎的包
      const packCounts: { [packId: string]: number } = {};
      allSessions.forEach(session => {
        packCounts[session.pack_id] = (packCounts[session.pack_id] || 0) + 1;
      });

      const mostPopularPacks = Object.entries(packCounts)
        .map(([pack_id, session_count]) => ({ pack_id, session_count }))
        .sort((a, b) => b.session_count - a.session_count)
        .slice(0, 5);

      const stats: QuizSessionStats = {
        total_sessions: allSessions.length,
        completed_sessions: completedSessions.length,
        completion_rate: allSessions.length > 0
          ? Math.round((completedSessions.length / allSessions.length) * 100)
          : 0,
        average_completion_time_minutes: averageCompletionTime,
        most_popular_packs: mostPopularPacks
      };

      return this.createSuccessResult(stats);
    } catch (error) {
      return this.createErrorResult('Failed to get user quiz stats', error);
    }
  }

  // 实现抽象方法
  protected async validateCreate(data: CreateQuizSessionInput): Promise<void> {
    if (!data.pack_id || data.pack_id.trim().length === 0) {
      throw new Error('Pack ID is required');
    }
    if (!data.user_id || data.user_id.trim().length === 0) {
      throw new Error('User ID is required');
    }
  }

  protected async validateUpdate(data: UpdateQuizSessionInput): Promise<void> {
    if (data.current_question_index !== undefined && data.current_question_index < 0) {
      throw new Error('Current question index must be non-negative');
    }
    if (data.total_questions !== undefined && data.total_questions < 0) {
      throw new Error('Total questions must be non-negative');
    }
    if (data.completion_percentage !== undefined && (data.completion_percentage < 0 || data.completion_percentage > 100)) {
      throw new Error('Completion percentage must be between 0 and 100');
    }
  }
}
