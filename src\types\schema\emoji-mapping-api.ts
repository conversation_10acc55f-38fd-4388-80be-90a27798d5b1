/**
 * Emoji映射API Schema定义
 * 
 * 定义emoji映射管理相关的API输入输出类型
 */

import { z } from 'zod';
import { IdSchema } from './base';

// ==================== Emoji映射配置 Schema ====================

/**
 * 获取选项展现配置输入 Schema
 */
export const GetOptionPresentationInputSchema = z.object({
  pack_id: IdSchema,
  option_value: z.string().min(1),
  question_id: IdSchema.optional(), // 可选，用于问题特定映射
});

/**
 * 选项展现配置响应 Schema
 */
export const OptionPresentationResponseSchema = z.object({
  emoji: z.string(),
  color: z.string(),
  animation: z.string(),
  source: z.enum(['system', 'pack', 'user', 'question']),
});

/**
 * Emoji映射配置 Schema
 */
export const EmojiMappingConfigSchema = z.object({
  primary: z.string(),
  alternatives: z.array(z.string()),
});

/**
 * 更新用户emoji映射输入 Schema
 */
export const UpdateUserEmojiMappingInputSchema = z.object({
  option_value: z.string().min(1),
  emoji_mapping: EmojiMappingConfigSchema,
  color: z.string().optional(),
  animation: z.string().optional(),
});

/**
 * 更新问题emoji覆盖输入 Schema
 */
export const UpdateQuestionEmojiOverrideInputSchema = z.object({
  question_id: IdSchema,
  option_value: z.string().min(1),
  emoji_mapping: EmojiMappingConfigSchema,
  color: z.string().optional(),
  animation: z.string().optional(),
  override_reason: z.string().optional(),
});

/**
 * 获取可用emoji输入 Schema
 */
export const GetAvailableEmojisInputSchema = z.object({
  pack_id: IdSchema,
  option_value: z.string().min(1),
  question_id: IdSchema.optional(),
});

/**
 * 可用emoji响应 Schema
 */
export const AvailableEmojisResponseSchema = z.object({
  emojis: z.array(z.string()),
  current_emoji: z.string(),
  source: z.enum(['system', 'pack', 'user', 'question']),
});

/**
 * 批量更新emoji映射输入 Schema
 */
export const BatchUpdateEmojiMappingInputSchema = z.object({
  mappings: z.array(z.object({
    option_value: z.string().min(1),
    emoji_mapping: EmojiMappingConfigSchema,
    color: z.string().optional(),
    animation: z.string().optional(),
  })),
  scope: z.enum(['user_global', 'pack_specific', 'question_specific']),
  target_id: IdSchema.optional(), // pack_id 或 question_id，根据scope确定
});

/**
 * 重置emoji映射输入 Schema
 */
export const ResetEmojiMappingInputSchema = z.object({
  scope: z.enum(['user_global', 'pack_specific', 'question_specific']),
  target_id: IdSchema.optional(), // pack_id 或 question_id，根据scope确定
  option_values: z.array(z.string()).optional(), // 可选，指定要重置的选项
});

/**
 * 获取emoji映射配置输入 Schema
 */
export const GetEmojiMappingConfigInputSchema = z.object({
  pack_id: IdSchema,
  question_id: IdSchema.optional(),
  include_alternatives: z.boolean().default(true),
  include_metadata: z.boolean().default(false),
});

/**
 * Emoji映射配置响应 Schema
 */
export const EmojiMappingConfigResponseSchema = z.object({
  pack_id: IdSchema,
  question_id: IdSchema.optional(),
  mappings: z.record(z.string(), z.object({
    emoji: z.string(),
    color: z.string(),
    animation: z.string(),
    alternatives: z.array(z.string()).optional(),
    source: z.enum(['system', 'pack', 'user', 'question']),
    metadata: z.record(z.any()).optional(),
  })),
  theme_info: z.object({
    theme_name: z.string(),
    theme_description: z.string().optional(),
    supports_alternatives: z.boolean(),
    supports_user_customization: z.boolean(),
  }).optional(),
});

/**
 * 导入emoji主题输入 Schema
 */
export const ImportEmojiThemeInputSchema = z.object({
  theme_name: z.string().min(1),
  theme_data: z.record(z.string(), EmojiMappingConfigSchema),
  apply_to: z.enum(['user_global', 'pack_specific', 'question_specific']),
  target_id: IdSchema.optional(),
  override_existing: z.boolean().default(false),
});

/**
 * 导出emoji主题输入 Schema
 */
export const ExportEmojiThemeInputSchema = z.object({
  scope: z.enum(['user_global', 'pack_specific', 'question_specific']),
  target_id: IdSchema.optional(),
  include_system_defaults: z.boolean().default(false),
  format: z.enum(['json', 'csv']).default('json'),
});

/**
 * 搜索emoji输入 Schema
 */
export const SearchEmojiInputSchema = z.object({
  query: z.string().min(1),
  category: z.string().optional(),
  limit: z.number().int().min(1).max(100).default(20),
  include_alternatives: z.boolean().default(true),
});

/**
 * 搜索emoji响应 Schema
 */
export const SearchEmojiResponseSchema = z.object({
  results: z.array(z.object({
    emoji: z.string(),
    name: z.string(),
    category: z.string(),
    keywords: z.array(z.string()),
    unicode: z.string(),
  })),
  total: z.number().int(),
  query: z.string(),
});

// ==================== 类型导出 ====================

export type GetOptionPresentationInput = z.infer<typeof GetOptionPresentationInputSchema>;
export type OptionPresentationResponse = z.infer<typeof OptionPresentationResponseSchema>;
export type EmojiMappingConfig = z.infer<typeof EmojiMappingConfigSchema>;
export type UpdateUserEmojiMappingInput = z.infer<typeof UpdateUserEmojiMappingInputSchema>;
export type UpdateQuestionEmojiOverrideInput = z.infer<typeof UpdateQuestionEmojiOverrideInputSchema>;
export type GetAvailableEmojisInput = z.infer<typeof GetAvailableEmojisInputSchema>;
export type AvailableEmojisResponse = z.infer<typeof AvailableEmojisResponseSchema>;
export type BatchUpdateEmojiMappingInput = z.infer<typeof BatchUpdateEmojiMappingInputSchema>;
export type ResetEmojiMappingInput = z.infer<typeof ResetEmojiMappingInputSchema>;
export type GetEmojiMappingConfigInput = z.infer<typeof GetEmojiMappingConfigInputSchema>;
export type EmojiMappingConfigResponse = z.infer<typeof EmojiMappingConfigResponseSchema>;
export type ImportEmojiThemeInput = z.infer<typeof ImportEmojiThemeInputSchema>;
export type ExportEmojiThemeInput = z.infer<typeof ExportEmojiThemeInputSchema>;
export type SearchEmojiInput = z.infer<typeof SearchEmojiInputSchema>;
export type SearchEmojiResponse = z.infer<typeof SearchEmojiResponseSchema>;

// ==================== 通用响应类型 ====================

/**
 * 通用API响应 Schema
 */
export const EmojiApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: z.string().optional(),
    message: z.string().optional(),
  });

export type EmojiApiResponse<T> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};
