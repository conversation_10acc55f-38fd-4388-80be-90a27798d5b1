-- Temporarily disable foreign keys for this script
PRAGMA foreign_keys=OFF;

-- Populate ui_label_translations table (English)
INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES
    ('app.title', 'en', 'Mindful Mood'),
    ('app.home', 'en', 'Home'),
    ('app.history', 'en', 'History'),
    ('app.analytics', 'en', 'Analytics'),
    ('app.settings', 'en', 'Settings'),
    ('app.loading', 'en', 'Loading application...'),

    -- Navigation labels
    ('nav.home', 'en', 'Home'),
    ('nav.history', 'en', 'History'),
    ('nav.analytics', 'en', 'Analytics'),
    ('nav.settings', 'en', 'Settings'),
    ('mood.log', 'en', 'Log Mood'),
    ('mood.how_feeling', 'en', 'How are you feeling today?'),
    ('mood.select_primary', 'en', 'Select primary emotion'),
    ('mood.select_secondary', 'en', 'Select secondary emotion'),
    ('mood.select_tertiary', 'en', 'Select tertiary emotion'),
    ('mood.intensity', 'en', 'Intensity'),
    ('mood.tags', 'en', 'Tags'),
    ('mood.add_tags', 'en', 'Add tags'),
    ('mood.tags_hint', 'en', 'Separate with commas'),
    ('mood.reflection', 'en', 'Reflection'),
    ('mood.add_reflection', 'en', 'Add reflection (optional)'),
    ('mood.save', 'en', 'Save Entry'),
    ('mood.save_success', 'en', 'Mood saved successfully'),
    ('mood.select_error', 'en', 'Please select at least primary and secondary emotions'),
    ('mood.select_error_all_tiers', 'en', 'Please select primary, secondary, and tertiary emotions before saving.'),
    ('mood.tags_too_long', 'en', 'Tag length cannot exceed 20 characters'),
    ('mood.reflection_too_long', 'en', 'Reflection cannot exceed 500 characters'),
    ('mood.validation_error', 'en', 'Please check your input'),
    ('mood.draft_saved', 'en', 'Draft saved'),
    ('mood.draft_loaded', 'en', 'Previous draft loaded'),
    ('mood.common_tags', 'en', 'Common Tags'),
    ('mood.suggested_tags', 'en', 'Suggested Tags'),
    ('mood.tag_trends', 'en', 'Tag Trends'),
    ('mood.tag_trend_up', 'en', 'Up'),
    ('mood.tag_trend_down', 'en', 'Down'),
    ('mood.tag_trend_stable', 'en', 'Stable'),
    ('mood.tag_suggestions', 'en', 'Smart Suggestions'),
    ('mood.tag_usage', 'en', 'Usage Count'),
    ('mood.tag_recent', 'en', 'Recently Used'),
    ('mood.tag_popular', 'en', 'Popular Tags'),
    ('mood.remove_primary', 'en', 'Remove primary emotion selection and start over'),
    ('mood.remove_secondary', 'en', 'Remove secondary emotion selection and go back to secondary choice'),
    ('mood.remove_tertiary', 'en', 'Remove tertiary emotion selection and choose a different tertiary emotion'),
    ('mood.error_loading_tags', 'en', 'Error loading tags.'),
    ('history.title', 'en', 'Mood History'),
    ('history.filter', 'en', 'Filter'),
    ('history.timeline', 'en', 'Timeline'),
    ('history.calendar', 'en', 'Calendar'),
    ('history.no_entries', 'en', 'No entries yet'),
    ('history.empty_state', 'en', 'Your mood entries will appear here'),
    ('history.today', 'en', 'Today'),
    ('history.yesterday', 'en', 'Yesterday'),
    ('history.details', 'en', 'Details'),
    ('analytics.title', 'en', 'Insights & Analytics'),
    ('analytics.no_data', 'en', 'No data to analyze yet'),
    ('analytics.empty_state', 'en', 'Start tracking your moods to see analytics'),
    ('analytics.total_entries', 'en', 'Total Entries'),
    ('analytics.streak', 'en', 'Current Streak'),
    ('analytics.top_emotion', 'en', 'Top Emotion'),
    ('analytics.avg_intensity', 'en', 'Avg. Intensity'),
    ('analytics.emotions_distribution', 'en', 'Emotions Distribution'),
    ('analytics.weekly_pattern', 'en', 'Weekly Pattern'),
    ('analytics.common_tags', 'en', 'Most Used Tags'),
    ('analytics.week', 'en', 'Week'),
    ('analytics.month', 'en', 'Month'),
    ('analytics.year', 'en', 'Year'),
    ('analytics.days', 'en', 'days'),
    ('analytics.intensity', 'en', 'Intensity'),
    ('settings.title', 'en', 'Settings'),
    ('settings.language', 'en', 'Language'),
    ('settings.language.english', 'en', 'English'),
    ('settings.language.chinese', 'en', 'Chinese'),
    ('settings.theme', 'en', 'Theme'),
    ('settings.theme.light', 'en', 'Light'),
    ('settings.theme.dark', 'en', 'Dark'),
    ('settings.theme.system', 'en', 'System'),
    ('settings.theme_changed', 'en', 'Theme updated'),
    ('settings.notifications', 'en', 'Notifications'),
    ('settings.notifications.enable', 'en', 'Enable Notifications'),
    ('settings.notifications_enabled', 'en', 'Notifications enabled'),
    ('settings.notifications_disabled', 'en', 'Notifications disabled'),
    ('settings.reminders', 'en', 'Daily Reminders'),
    ('settings.reminders.enable', 'en', 'Enable Reminders'),
    ('settings.reminders_enabled', 'en', 'Daily reminders enabled'),
    ('settings.reminders_disabled', 'en', 'Daily reminders disabled'),
    ('settings.reminders.time', 'en', 'Reminder Time'),
    ('settings.export', 'en', 'Export Data'),
    ('settings.export_data', 'en', 'Export as JSON'),
    ('settings.export_success', 'en', 'Data exported successfully'),
    ('settings.privacy', 'en', 'Privacy Policy'),
    ('settings.about', 'en', 'About'),
    ('settings.emotion_style', 'en', 'Emotion Navigation Style'),
    ('settings.emotion_style_changed', 'en', 'Emotion navigation style updated'),
    ('settings.data_sync', 'en', 'Data Sync'),
    ('settings.data_sync_enable_first', 'en', 'Please enable online sync first.'),
    ('settings.data_sync_enable_online', 'en', 'Enable Online Sync'),
    ('settings.data_sync_syncing', 'en', 'Syncing...'),
    ('settings.data_sync_sync_now', 'en', 'Sync Now'),
    ('settings.data_sync_syncing_in_progress', 'en', 'Synchronization in progress...'),
    ('settings.data_sync_last_sync_status', 'en', 'Last Sync'),
    ('settings.data_sync_uploaded', 'en', 'Uploaded: {{count}}'),
    ('settings.data_sync_downloaded', 'en', 'Downloaded: {{count}}'),
    ('settings.data_sync_updated_by_server', 'en', 'Updated by server: {{count}}'),
    ('settings.data_sync_error', 'en', 'Sync Error'),
    ('error.not_found', 'en', 'Page not found'),
    ('error.go_home', 'en', 'Go back home'),
    ('error.go_back', 'en', 'Go back'),
    ('error.retry', 'en', 'Retry'),
    ('error.server_error', 'en', 'Server Error'),
    ('error.network_error', 'en', 'Network Error'),
    ('error.network_message', 'en', 'There seems to be a problem with your network connection'),
    ('error.auth_error', 'en', 'Authentication Error'),
    ('error.auth_message', 'en', 'Please log in to access this page'),
    ('error.unknown_error', 'en', 'Unknown Error'),
    ('error.unknown_message', 'en', 'Something unexpected happened, please try again later'),
    ('error.generic', 'en', 'An unexpected error occurred.'),
    ('error.failed_to_save_data', 'en', 'Failed to save data.'),
    ('error.failed_to_delete_data', 'en', 'Failed to delete data.'),
    ('error.db_not_initialized', 'en', 'Database not initialized. Please try again later.'),
    ('gesture.swipe_left', 'en', 'Swipe Left'),
    ('gesture.swipe_right', 'en', 'Swipe Right'),
    ('gesture.swipe_up', 'en', 'Swipe Up'),
    ('gesture.swipe_down', 'en', 'Swipe Down'),
    ('gesture.pull_to_refresh', 'en', 'Pull to Refresh'),
    ('gesture.refreshing', 'en', 'Refreshing...'),
    ('gesture.tap', 'en', 'Tap'),
    ('gesture.double_tap', 'en', 'Double Tap'),
    ('gesture.long_press', 'en', 'Long Press'),
    ('gesture.pinch', 'en', 'Pinch'),
    ('gesture.rotate', 'en', 'Rotate'),
    ('mood.style.wheel', 'en', 'Classic Wheel'),
    ('mood.style.galaxy', 'en', 'Galaxy View'),
    ('mood.style.layered', 'en', 'Card Layout'),
    ('mood.style.floating', 'en', 'Floating Bubbles'),
    ('mood.tier.primary', 'en', 'Primary Emotion'),
    ('mood.tier.secondary', 'en', 'Secondary Emotion'),
    ('mood.tier.tertiary', 'en', 'Tertiary Emotion'),
    ('mood.galaxy.select_planet', 'en', 'Select an emotion planet'),
    ('mood.galaxy.select_region', 'en', 'Select an emotion region'),
    ('mood.galaxy.select_crystal', 'en', 'Select an emotion crystal'),
    ('mood.layered.select_primary', 'en', 'How are you feeling?'),
    ('mood.layered.select_secondary', 'en', 'More specifically?'),
    ('mood.layered.select_tertiary', 'en', 'Choose the exact feeling'),
    ('mood.layered.selected', 'en', 'You selected'),
    ('mood.layered.path', 'en', 'Your emotion path'),
    ('errors.failed_to_load_emotions', 'en', 'Failed to load emotion data. Details: {{details}}'),
    ('errors.failed_to_load_history', 'en', 'Failed to load history. Details: {{details}}'),
    ('errors.failed_to_load_analytics', 'en', 'Failed to load analytics data. Details: {{details}}'),
    ('errors.failed_to_load_tags', 'en', 'Failed to load tags. Details: {{details}}'),
    ('sidebar.toggle', 'en', 'Toggle Sidebar'),
    ('settings.toast.language_changed_to_english', 'en', 'Language changed to English'),
    ('settings.toast.language_changed_to_chinese', 'en', 'Language changed to Chinese'),
    ('history.sun', 'en', 'Sun'),
    ('history.mon', 'en', 'Mon'),
    ('history.tue', 'en', 'Tue'),
    ('history.wed', 'en', 'Wed'),
    ('history.thu', 'en', 'Thu'),
    ('history.fri', 'en', 'Fri'),
    ('history.sat', 'en', 'Sat'),
    ('history.map', 'en', 'Map'),
    ('history.map_title', 'en', 'Discovered Emotion Regions'),
    ('history.map_empty_title', 'en', 'Your Emotion Map Awaits'),
    ('history.map_empty_subtitle', 'en', 'Start logging your moods to discover regions on your personal emotion map!'),
    ('history.map_region_explored', 'en', 'Region Explored'),
    ('history.map_region_undiscovered', 'en', 'Undiscovered'),
    ('history.map_start_logging_prompt', 'en', 'Start logging your moods to discover and explore these regions on your personal emotion map!'),

    -- Error messages (already defined above)

    -- Calendar empty states
    ('history.no_entries_for_selected_date', 'en', 'No entries found for this date.'),
    ('history.no_reflection', 'en', 'No reflection for this entry.'),
    ('history.no_entries_for_calendar', 'en', 'No mood entries to display in calendar.'),
    ('history.empty_state_calendar', 'en', 'Record your moods to see them in the calendar.'),
    ('history.entries_for_date', 'en', 'Entries for {{date}}'),

    -- Settings
    ('settings.emoji_set_title', 'en', 'Emoji Set Style'),
    ('settings.loading_emoji_sets', 'en', 'Loading emoji sets'),
    ('settings.emoji_set_changed', 'en', 'Emoji set updated'),
    ('settings.emoji_set_change_failed', 'en', 'Failed to update emoji set'),
    ('settings.no_emoji_sets_available', 'en', 'No emoji sets available at the moment.'),
    ('settings.exporting_data', 'en', 'Exporting data...'),
    ('settings.export_data_csv', 'en', 'Export as CSV'),
    ('settings.exporting_data_csv', 'en', 'Exporting CSV...'),
    ('settings.export_error_csv', 'en', 'CSV Export Failed'),

    -- TierEditor labels (English)
    ('tier_editor.tiers', 'en', 'Emotion Tiers'),
    ('tier_editor.emoji_mapping', 'en', 'Emoji Mapping'),
    ('tier_editor.emoji_sets_load_error', 'en', 'Failed to load emoji sets'),
    ('tier_editor.no_tiers', 'en', 'No tiers'),
    ('tier_editor.create_first', 'en', 'Create first tier'),
    ('tier_editor.no_tiers_title', 'en', 'Start creating emotion tiers'),
    ('tier_editor.no_tiers_description', 'en', 'Emotion tiers are the foundation for building emotion data. You need to create at least one tier before you can add emotions.'),
    ('tier_editor.level_1_short', 'en', 'L1'),
    ('tier_editor.level_2_short', 'en', 'L2'),
    ('tier_editor.level_3_short', 'en', 'L3'),
    ('tier_editor.level_1', 'en', 'Level 1 (Primary Emotions)'),
    ('tier_editor.level_2', 'en', 'Level 2 (Secondary Emotions)'),
    ('tier_editor.level_3', 'en', 'Level 3 (Detailed Emotions)'),
    ('tier_editor.level_1_description', 'en', 'Level 1 emotions'),
    ('tier_editor.level_2_description', 'en', 'Level 2 emotions'),
    ('tier_editor.level_3_description', 'en', 'Level 3 emotions'),
    ('tier_editor.cannot_delete_parent', 'en', 'Cannot delete tier with child tiers'),
    ('tier_editor.delete_tier_title', 'en', 'Delete Tier'),
    ('tier_editor.delete_tier_description', 'en', 'Are you sure you want to delete this tier? This will delete all emotions in this tier and cannot be undone.'),
    ('tier_editor.edit_tier', 'en', 'Edit Tier'),
    ('tier_editor.edit_tier_description', 'en', 'Modify tier information'),
    ('tier_editor.add_tier', 'en', 'Add Tier'),
    ('tier_editor.add_tier_description', 'en', 'Create new emotion tier'),
    ('tier_editor.manage_tier', 'en', 'Manage Tier'),
    ('tier_editor.manage_tier_description', 'en', 'Manage emotions in tier'),
    ('tier_editor.tier_info', 'en', 'Tier Information'),
    ('tier_editor.level', 'en', 'Level'),
    ('tier_editor.parent', 'en', 'Parent Tier'),
    ('tier_editor.name_placeholder', 'en', 'Enter tier name'),
    ('tier_editor.select_level', 'en', 'Select level'),
    ('tier_editor.select_parent', 'en', 'Select parent tier'),
    ('tier_editor.emoji_mapping_description', 'en', 'Set corresponding emoji for each emotion in different emoji sets'),
    ('tier_editor.select_emoji_set', 'en', 'Select Emoji Set'),
    ('tier_editor.select_emoji_set_placeholder', 'en', 'Please select an emoji set'),

    -- Common labels (English)
    ('common.add', 'en', 'Add'),
    ('common.edit', 'en', 'Edit'),
    ('common.delete', 'en', 'Delete'),
    ('common.cancel', 'en', 'Cancel'),
    ('common.save', 'en', 'Save'),
    ('common.close', 'en', 'Close'),
    ('common.manage', 'en', 'Manage'),
    ('common.name', 'en', 'Name'),
    ('common.loading', 'en', 'Loading...'),
    ('common.default', 'en', 'Default'),
    ('common.saving', 'en', 'Saving...'),
    ('common.save_all', 'en', 'Save All'),

    -- EmotionEditor labels (English)
    ('emotion_editor.emotions', 'en', 'Emotions'),
    ('emotion_editor.no_emotions', 'en', 'No emotions'),
    ('emotion_editor.create_first', 'en', 'Create first emotion'),
    ('emotion_editor.name_placeholder', 'en', 'Enter emotion name'),
    ('emotion_editor.emoji', 'en', 'Emoji'),
    ('emotion_editor.choose_emoji', 'en', 'Choose emoji'),
    ('emotion_editor.color', 'en', 'Color'),
    ('emotion_editor.add_emotion', 'en', 'Add Emotion'),
    ('emotion_editor.edit_emotion', 'en', 'Edit Emotion'),
    ('emotion_editor.delete_emotion_title', 'en', 'Delete Emotion'),
    ('emotion_editor.delete_emotion_description', 'en', 'Are you sure you want to delete this emotion? This action cannot be undone.'),

    -- EmotionDataEditor labels (English)
    ('emotion_editor.create_data', 'en', 'Create Emotion Data'),
    ('emotion_editor.edit_data', 'en', 'Edit Emotion Data'),
    ('emotion_editor.new_emotion_data', 'en', 'New Emotion Data'),
    ('emotion_editor.from_template', 'en', 'From Template'),
    ('emotion_editor.system_default_name_hint', 'en', 'System default emotion data name cannot be modified'),
    ('emotion_editor.general', 'en', 'General'),
    ('emotion_editor.tiers', 'en', 'Tiers'),
    ('emotion_editor.preview', 'en', 'Preview'),
    ('emotion_editor.description', 'en', 'Description'),
    ('emotion_editor.description_placeholder', 'en', 'Enter emotion data description'),
    ('emotion_editor.actions', 'en', 'Actions'),
    ('emotion_editor.duplicate', 'en', 'Duplicate'),
    ('emotion_editor.export', 'en', 'Export'),
    ('emotion_editor.import', 'en', 'Import'),
    ('emotion_editor.activate', 'en', 'Activate'),
    ('emotion_editor.delete_data', 'en', 'Delete Data'),
    ('emotion_editor.import_data', 'en', 'Import Data'),
    ('emotion_editor.import_description', 'en', 'Import emotion data from JSON'),
    ('emotion_editor.import_placeholder', 'en', 'Paste JSON data here...'),
    ('emotion_editor.import_error', 'en', 'Import failed'),
    ('emotion_editor.import_success', 'en', 'Data imported successfully'),

    -- EmojiMappingEditor labels (English)
    ('emoji_mapping.load_error', 'en', 'Failed to load emoji mappings'),
    ('emoji_mapping.save_success', 'en', 'Emoji mappings saved successfully'),
    ('emoji_mapping.save_error', 'en', 'Failed to save emoji mappings'),
    ('emoji_mapping.loading', 'en', 'Loading emoji mappings...'),
    ('emoji_mapping.search_emotions', 'en', 'Search emotions...'),
    ('emoji_mapping.no_emotions_found', 'en', 'No matching emotions found'),
    ('emoji_mapping.no_emotions', 'en', 'No emotions available');

-- Populate ui_label_translations table (Chinese)
INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES
    ('app.title', 'zh', '心情记录'),
    ('app.home', 'zh', '首页'),
    ('app.history', 'zh', '历史'),
    ('app.analytics', 'zh', '分析'),
    ('app.settings', 'zh', '设置'),
    ('app.loading', 'zh', '应用加载中...'),

    -- Navigation labels
    ('nav.home', 'zh', '首页'),
    ('nav.history', 'zh', '历史'),
    ('nav.analytics', 'zh', '分析'),
    ('nav.settings', 'zh', '设置'),
    ('mood.log', 'zh', '记录心情'),
    ('mood.how_feeling', 'zh', '你今天感觉如何？'),
    ('mood.select_primary', 'zh', '选择主要情绪'),
    ('mood.select_secondary', 'zh', '选择次要情绪'),
    ('mood.select_tertiary', 'zh', '选择具体情绪'),
    ('mood.intensity', 'zh', '强度'),
    ('mood.tags', 'zh', '标签'),
    ('mood.add_tags', 'zh', '添加标签'),
    ('mood.tags_hint', 'zh', '用逗号分隔'),
    ('mood.reflection', 'zh', '反思'),
    ('mood.add_reflection', 'zh', '添加反思（可选）'),
    ('mood.save', 'zh', '保存记录'),
    ('mood.save_success', 'zh', '心情已成功保存'),
    ('mood.select_error', 'zh', '请至少选择主要和次要情绪'),
    ('mood.select_error_all_tiers', 'zh', '请在保存前选择一级、二级和三级情绪。'),
    ('mood.tags_too_long', 'zh', '标签长度不能超过20个字符'),
    ('mood.reflection_too_long', 'zh', '反思内容不能超过500个字符'),
    ('mood.validation_error', 'zh', '请检查输入内容'),
    ('mood.draft_saved', 'zh', '草稿已保存'),
    ('mood.draft_loaded', 'zh', '已加载上次的草稿'),
    ('mood.common_tags', 'zh', '常用标签'),
    ('mood.suggested_tags', 'zh', '推荐标签'),
    ('mood.tag_trends', 'zh', '标签趋势'),
    ('mood.tag_trend_up', 'zh', '上升'),
    ('mood.tag_trend_down', 'zh', '下降'),
    ('mood.tag_trend_stable', 'zh', '稳定'),
    ('mood.tag_suggestions', 'zh', '智能推荐'),
    ('mood.tag_usage', 'zh', '使用次数'),
    ('mood.tag_recent', 'zh', '最近使用'),
    ('mood.tag_popular', 'zh', '热门标签'),
    ('mood.remove_primary', 'zh', '移除一级情绪选择并重新开始'),
    ('mood.remove_secondary', 'zh', '移除二级情绪选择并返回二级选择'),
    ('mood.remove_tertiary', 'zh', '移除三级情绪选择并重新选择三级情绪'),
    ('mood.error_loading_tags', 'zh', '加载标签出错。'),
    ('history.title', 'zh', '情绪历史'),
    ('history.filter', 'zh', '筛选'),
    ('history.timeline', 'zh', '时间轴'),
    ('history.calendar', 'zh', '日历'),
    ('history.no_entries', 'zh', '暂无记录'),
    ('history.empty_state', 'zh', '您的心情记录将显示在这里'),
    ('history.today', 'zh', '今天'),
    ('history.yesterday', 'zh', '昨天'),
    ('history.details', 'zh', '详情'),
    ('analytics.title', 'zh', '洞察与分析'),
    ('analytics.no_data', 'zh', '暂无分析数据'),
    ('analytics.empty_state', 'zh', '开始记录心情以查看分析'),
    ('analytics.total_entries', 'zh', '记录总数'),
    ('analytics.streak', 'zh', '当前连续记录'),
    ('analytics.top_emotion', 'zh', '最常见情绪'),
    ('analytics.avg_intensity', 'zh', '平均强度'),
    ('analytics.emotions_distribution', 'zh', '情绪分布'),
    ('analytics.weekly_pattern', 'zh', '每周模式'),
    ('analytics.common_tags', 'zh', '常用标签'),
    ('analytics.week', 'zh', '周'),
    ('analytics.month', 'zh', '月'),
    ('analytics.year', 'zh', '年'),
    ('analytics.days', 'zh', '天'),
    ('analytics.intensity', 'zh', '强度'),
    ('settings.title', 'zh', '设置'),
    ('settings.language', 'zh', '语言'),
    ('settings.language.english', 'zh', 'English'),
    ('settings.language.chinese', 'zh', '中文'),
    ('settings.theme', 'zh', '主题'),
    ('settings.theme.light', 'zh', '浅色'),
    ('settings.theme.dark', 'zh', '深色'),
    ('settings.theme.system', 'zh', '跟随系统'),
    ('settings.theme_changed', 'zh', '主题已更新'),
    ('settings.notifications', 'zh', '通知'),
    ('settings.notifications.enable', 'zh', '启用通知'),
    ('settings.notifications_enabled', 'zh', '通知已启用'),
    ('settings.notifications_disabled', 'zh', '通知已禁用'),
    ('settings.reminders', 'zh', '每日提醒'),
    ('settings.reminders.enable', 'zh', '启用提醒'),
    ('settings.reminders_enabled', 'zh', '每日提醒已启用'),
    ('settings.reminders_disabled', 'zh', '每日提醒已禁用'),
    ('settings.reminders.time', 'zh', '提醒时间'),
    ('settings.export', 'zh', '导出数据'),
    ('settings.export_data', 'zh', '导出为JSON'),
    ('settings.export_success', 'zh', '数据导出成功'),
    ('settings.privacy', 'zh', '隐私政策'),
    ('settings.about', 'zh', '关于'),
    ('settings.emotion_style', 'zh', '情绪导航样式'),
    ('settings.emotion_style_changed', 'zh', '情绪导航样式已更新'),
    ('settings.data_sync', 'zh', '数据同步'),
    ('settings.data_sync_enable_first', 'zh', '请先启用在线同步。'),
    ('settings.data_sync_enable_online', 'zh', '启用在线同步'),
    ('settings.data_sync_syncing', 'zh', '同步中...'),
    ('settings.data_sync_sync_now', 'zh', '立即同步'),
    ('settings.data_sync_syncing_in_progress', 'zh', '同步正在进行中...'),
    ('settings.data_sync_last_sync_status', 'zh', '上次同步'),
    ('settings.data_sync_uploaded', 'zh', '已上传：{{count}}'),
    ('settings.data_sync_downloaded', 'zh', '已下载：{{count}}'),
    ('settings.data_sync_updated_by_server', 'zh', '服务器已更新：{{count}}'),
    ('settings.data_sync_error', 'zh', '同步错误'),
    ('error.not_found', 'zh', '页面未找到'),
    ('error.go_home', 'zh', '返回首页'),
    ('error.go_back', 'zh', '返回上一页'),
    ('error.retry', 'zh', '重试'),
    ('error.server_error', 'zh', '服务器错误'),
    ('error.network_error', 'zh', '网络错误'),
    ('error.network_message', 'zh', '网络连接出现问题，请检查您的网络设置'),
    ('error.auth_error', 'zh', '认证错误'),
    ('error.auth_message', 'zh', '请先登录后再访问此页面'),
    ('error.unknown_error', 'zh', '未知错误'),
    ('error.unknown_message', 'zh', '发生了一些意外情况，请稍后重试'),
    ('error.generic', 'zh', '发生了一个意外错误。'),
    ('error.failed_to_save_data', 'zh', '保存数据失败。'),
    ('error.failed_to_delete_data', 'zh', '删除数据失败。'),
    ('error.db_not_initialized', 'zh', '数据库尚未初始化。请稍后再试。'),
    ('gesture.swipe_left', 'zh', '向左滑动'),
    ('gesture.swipe_right', 'zh', '向右滑动'),
    ('gesture.swipe_up', 'zh', '向上滑动'),
    ('gesture.swipe_down', 'zh', '向下滑动'),
    ('gesture.pull_to_refresh', 'zh', '下拉刷新'),
    ('gesture.refreshing', 'zh', '正在刷新...'),
    ('gesture.tap', 'zh', '点击'),
    ('gesture.double_tap', 'zh', '双击'),
    ('gesture.long_press', 'zh', '长按'),
    ('gesture.pinch', 'zh', '捏合'),
    ('gesture.rotate', 'zh', '旋转'),
    ('mood.style.wheel', 'zh', '经典轮盘'),
    ('mood.style.galaxy', 'zh', '情绪宇宙'),
    ('mood.style.layered', 'zh', '卡片布局'),
    ('mood.style.floating', 'zh', '浮动气泡'),
    ('mood.tier.primary', 'zh', '一级情绪'),
    ('mood.tier.secondary', 'zh', '二级情绪'),
    ('mood.tier.tertiary', 'zh', '三级情绪'),
    ('mood.galaxy.select_planet', 'zh', '选择情绪星球'),
    ('mood.galaxy.select_region', 'zh', '选择情绪区域'),
    ('mood.galaxy.select_crystal', 'zh', '选择情绪结晶'),
    ('mood.layered.select_primary', 'zh', '你现在感觉如何？'),
    ('mood.layered.select_secondary', 'zh', '更具体一点？'),
    ('mood.layered.select_tertiary', 'zh', '选择最确切的感受'),
    ('mood.layered.selected', 'zh', '你选择了'),
    ('mood.layered.path', 'zh', '你的情绪路径'),

    ('sidebar.toggle', 'zh', '切换侧边栏'),
    ('settings.toast.language_changed_to_english', 'zh', 'Language changed to English'),
    ('settings.toast.language_changed_to_chinese', 'zh', '语言已更改为中文'),
    ('history.sun', 'zh', '日'),
    ('history.mon', 'zh', '一'),
    ('history.tue', 'zh', '二'),
    ('history.wed', 'zh', '三'),
    ('history.thu', 'zh', '四'),
    ('history.fri', 'zh', '五'),
    ('history.sat', 'zh', '六'),
    ('history.map', 'zh', '地图'),
    ('history.map_title', 'zh', '情绪区域图'),
    ('history.map_empty_title', 'zh', '你的情绪地图等待发现'),
    ('history.map_empty_subtitle', 'zh', '开始记录你的心情，在你的个人情绪地图上发现新区域吧！'),
    ('history.map_region_explored', 'zh', '区域已探索'),
    ('history.map_region_undiscovered', 'zh', '未探索'),
    ('history.map_start_logging_prompt', 'zh', '开始记录你的心情，在你的个人情绪地图上发现并探索这些区域吧！'),

    -- Error messages (already defined above)

    -- Calendar empty states
    ('history.no_entries_for_selected_date', 'zh', '该日期没有找到记录。'),
    ('history.no_reflection', 'zh', '此记录没有反思内容。'),
    ('history.no_entries_for_calendar', 'zh', '日历中没有心情记录可显示。'),
    ('history.empty_state_calendar', 'zh', '记录你的心情，在日历中查看它们。'),
    ('history.entries_for_date', 'zh', '{{date}}的记录'),

    -- Settings
    ('settings.emoji_set_title', 'zh', '表情符号样式'),
    ('settings.loading_emoji_sets', 'zh', '加载表情符号集'),
    ('settings.emoji_set_changed', 'zh', '表情符号集已更新'),
    ('settings.emoji_set_change_failed', 'zh', '更新表情符号集失败'),
    ('settings.no_emoji_sets_available', 'zh', '当前没有可用的表情符号集。'),
    ('settings.exporting_data', 'zh', '导出数据中...'),
    ('settings.export_data_csv', 'zh', '导出为CSV'),
    ('settings.exporting_data_csv', 'zh', '导出CSV中...'),
    ('settings.export_error_csv', 'zh', 'CSV导出失败'),

    -- TierEditor labels (Chinese)
    ('tier_editor.tiers', 'zh', '情绪层级'),
    ('tier_editor.emoji_mapping', 'zh', 'Emoji 映射'),
    ('tier_editor.emoji_sets_load_error', 'zh', '加载表情集失败'),
    ('tier_editor.no_tiers', 'zh', '没有层级'),
    ('tier_editor.create_first', 'zh', '创建第一个层级'),
    ('tier_editor.no_tiers_title', 'zh', '开始创建情绪层级'),
    ('tier_editor.no_tiers_description', 'zh', '情绪层级是构建情绪数据的基础。您需要先创建至少一个层级，然后才能添加情绪。'),
    ('tier_editor.level_1_short', 'zh', 'L1'),
    ('tier_editor.level_2_short', 'zh', 'L2'),
    ('tier_editor.level_3_short', 'zh', 'L3'),
    ('tier_editor.level_1', 'zh', '一级 (主要情绪)'),
    ('tier_editor.level_2', 'zh', '二级 (次要情绪)'),
    ('tier_editor.level_3', 'zh', '三级 (细分情绪)'),
    ('tier_editor.level_1_description', 'zh', '一级情绪'),
    ('tier_editor.level_2_description', 'zh', '二级情绪'),
    ('tier_editor.level_3_description', 'zh', '三级情绪'),
    ('tier_editor.cannot_delete_parent', 'zh', '无法删除有子层级的层级'),
    ('tier_editor.delete_tier_title', 'zh', '删除层级'),
    ('tier_editor.delete_tier_description', 'zh', '确定要删除这个层级吗？此操作将删除该层级中的所有情绪，且无法恢复。'),
    ('tier_editor.edit_tier', 'zh', '编辑层级'),
    ('tier_editor.edit_tier_description', 'zh', '修改层级信息'),
    ('tier_editor.add_tier', 'zh', '添加层级'),
    ('tier_editor.add_tier_description', 'zh', '创建新的情绪层级'),
    ('tier_editor.manage_tier', 'zh', '管理层级'),
    ('tier_editor.manage_tier_description', 'zh', '管理层级中的情绪'),
    ('tier_editor.tier_info', 'zh', '层级信息'),
    ('tier_editor.level', 'zh', '级别'),
    ('tier_editor.parent', 'zh', '父层级'),
    ('tier_editor.name_placeholder', 'zh', '输入层级名称'),
    ('tier_editor.select_level', 'zh', '选择级别'),
    ('tier_editor.select_parent', 'zh', '选择父层级'),
    ('tier_editor.emoji_mapping_description', 'zh', '为每个情绪在不同表情集中设置对应的 emoji'),
    ('tier_editor.select_emoji_set', 'zh', '选择表情集'),
    ('tier_editor.select_emoji_set_placeholder', 'zh', '请选择表情集'),

    -- Common labels (Chinese)
    ('common.add', 'zh', '添加'),
    ('common.edit', 'zh', '编辑'),
    ('common.delete', 'zh', '删除'),
    ('common.cancel', 'zh', '取消'),
    ('common.save', 'zh', '保存'),
    ('common.close', 'zh', '关闭'),
    ('common.manage', 'zh', '管理'),
    ('common.name', 'zh', '名称'),
    ('common.loading', 'zh', '加载中...'),
    ('common.default', 'zh', '默认'),
    ('common.saving', 'zh', '保存中...'),
    ('common.save_all', 'zh', '保存全部'),

    -- EmotionEditor labels (Chinese)
    ('emotion_editor.emotions', 'zh', '情绪'),
    ('emotion_editor.no_emotions', 'zh', '没有情绪'),
    ('emotion_editor.create_first', 'zh', '创建第一个情绪'),
    ('emotion_editor.name_placeholder', 'zh', '输入情绪名称'),
    ('emotion_editor.emoji', 'zh', '表情'),
    ('emotion_editor.choose_emoji', 'zh', '选择表情'),
    ('emotion_editor.color', 'zh', '颜色'),
    ('emotion_editor.add_emotion', 'zh', '添加情绪'),
    ('emotion_editor.edit_emotion', 'zh', '编辑情绪'),
    ('emotion_editor.delete_emotion_title', 'zh', '删除情绪'),
    ('emotion_editor.delete_emotion_description', 'zh', '确定要删除这个情绪吗？此操作无法撤销。'),

    -- EmotionDataEditor labels (Chinese)
    ('emotion_editor.create_data', 'zh', '创建情绪数据'),
    ('emotion_editor.edit_data', 'zh', '编辑情绪数据'),
    ('emotion_editor.new_emotion_data', 'zh', '新情绪数据'),
    ('emotion_editor.from_template', 'zh', '来自模板'),
    ('emotion_editor.system_default_name_hint', 'zh', '系统默认情绪数据名称不可修改'),
    ('emotion_editor.general', 'zh', '常规'),
    ('emotion_editor.tiers', 'zh', '层级'),
    ('emotion_editor.preview', 'zh', '预览'),
    ('emotion_editor.description', 'zh', '描述'),
    ('emotion_editor.description_placeholder', 'zh', '输入情绪数据描述'),
    ('emotion_editor.actions', 'zh', '操作'),
    ('emotion_editor.duplicate', 'zh', '复制'),
    ('emotion_editor.export', 'zh', '导出'),
    ('emotion_editor.import', 'zh', '导入'),
    ('emotion_editor.activate', 'zh', '激活'),
    ('emotion_editor.delete_data', 'zh', '删除数据'),
    ('emotion_editor.import_data', 'zh', '导入数据'),
    ('emotion_editor.import_description', 'zh', '从 JSON 导入情绪数据'),
    ('emotion_editor.import_placeholder', 'zh', '在此粘贴 JSON 数据...'),
    ('emotion_editor.import_error', 'zh', '导入失败'),
    ('emotion_editor.import_success', 'zh', '数据导入成功'),

    -- EmojiMappingEditor labels (Chinese)
    ('emoji_mapping.load_error', 'zh', '加载 emoji 映射失败'),
    ('emoji_mapping.save_success', 'zh', 'Emoji 映射保存成功'),
    ('emoji_mapping.save_error', 'zh', '保存 emoji 映射失败'),
    ('emoji_mapping.loading', 'zh', '加载 emoji 映射中...'),
    ('emoji_mapping.search_emotions', 'zh', '搜索情绪...'),
    ('emoji_mapping.no_emotions_found', 'zh', '未找到匹配的情绪'),
    ('emoji_mapping.no_emotions', 'zh', '没有可用的情绪');

-- Re-enable foreign keys
PRAGMA foreign_keys=ON;