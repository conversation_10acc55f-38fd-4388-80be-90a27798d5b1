{"app.title": "<PERSON><PERSON>", "app.loading": "Loading application...", "app.home": "Home", "app.history": "History", "app.analytics": "Analytics", "app.settings": "Settings", "mood.log": "<PERSON><PERSON>", "mood.how_feeling": "How are you feeling today?", "mood.select_primary": "Select primary emotion", "mood.select_secondary": "Select secondary emotion", "mood.select_tertiary": "Select tertiary emotion", "mood.intensity": "Intensity", "mood.location": "Location", "mood.tags": "Tags", "mood.add_tags": "Add tags", "mood.tags_hint": "Separate with commas", "mood.reflection": "Reflection", "mood.add_reflection": "Add reflection (optional)", "mood.save": "Save Entry", "mood.save_success": "<PERSON><PERSON> saved successfully", "mood.select_error": "Please select at least primary and secondary emotions", "mood.select_error_all_tiers": "Please select primary, secondary, and tertiary emotions before saving.", "mood.tags_too_long": "Tag length cannot exceed 20 characters", "mood.reflection_too_long": "Reflection cannot exceed 500 characters", "mood.validation_error": "Please check your input", "mood.draft_saved": "Draft saved", "mood.draft_loaded": "Previous draft loaded", "mood.common_tags": "Common Tags", "mood.suggested_tags": "Suggested Tags", "mood.tag_trends": "Tag Trends", "mood.tag_trend_up": "Up", "mood.tag_trend_down": "Down", "mood.tag_trend_stable": "Stable", "mood.tag_suggestions": "Smart Suggestions", "mood.tag_usage": "Usage Count", "mood.tag_recent": "Recently Used", "mood.tag_popular": "Popular Tags", "mood.remove_primary": "Remove primary emotion selection and start over", "mood.remove_secondary": "Remove secondary emotion selection and go back to secondary choice", "mood.remove_tertiary": "Remove tertiary emotion selection and choose a different tertiary emotion", "mood.tertiary_selected_message": "If you want to change your selection, click the X button next to the emotion name above.", "history.title": "Mood History", "history.filter": "Filter", "history.timeline": "Timeline", "history.calendar": "Calendar", "history.map": "Map", "history.no_entries": "No entries yet", "history.empty_state": "Your mood entries will appear here", "history.today": "Today", "history.yesterday": "Yesterday", "history.details": "Details", "history.sun": "Sun", "history.mon": "Mon", "history.tue": "<PERSON><PERSON>", "history.wed": "Wed", "history.thu": "<PERSON>hu", "history.fri": "<PERSON><PERSON>", "history.sat": "Sat", "history.map_title": "Discovered Emotion Regions", "history.map_empty_title": "Your Emotion Map Awaits", "history.map_empty_subtitle": "Start logging your moods to discover regions on your personal emotion map!", "history.map_region_explored": "Region Explored", "history.map_region_undiscovered": "Undiscovered", "history.map_start_logging_prompt": "Start logging your moods to discover and explore these regions on your personal emotion map!", "history.date_range": "Date Range", "history.this_week": "This Week", "history.this_month": "This Month", "history.custom_range": "Custom Range", "history.no_entries_for_selected_date": "No entries found for this date.", "history.no_reflection": "No reflection for this entry.", "history.no_entries_for_calendar": "No mood entries to display in calendar.", "history.empty_state_calendar": "Record your moods to see them in the calendar.", "history.entries_for_date": "Entries for {{date}}", "analytics.title": "Insights & Analytics", "analytics.no_data": "No data to analyze yet", "analytics.empty_state": "Start tracking your moods to see analytics", "analytics.total_entries": "Total Entries", "analytics.streak": "Current Streak", "analytics.top_emotion": "Top Emotion", "analytics.avg_intensity": "Avg. Intensity", "analytics.emotions_distribution": "Emotions Distribution", "analytics.weekly_pattern": "Weekly Pattern", "analytics.common_tags": "Most Used Tags", "analytics.week": "Week", "analytics.month": "Month", "analytics.year": "Year", "analytics.days": "days", "analytics.intensity": "Intensity", "analytics.period": "Period", "analytics.all_time": "All Time", "analytics.mood_trends": "Mood Trends", "analytics.emotion_distribution": "Emotion Distribution", "analytics.tag_analysis": "Tag Analysis", "settings.title": "Settings", "settings.language": "Language", "settings.language.english": "English", "settings.language.chinese": "中文", "settings.theme": "Theme", "settings.theme.light": "Light", "settings.theme.dark": "Dark", "settings.theme.system": "System", "settings.theme_changed": "Theme updated", "settings.color_mode": "Color Mode", "settings.color_mode.warm": "Warm Colors", "settings.color_mode.cool": "Cool Colors", "settings.color_mode.mixed": "Mixed Colors", "settings.color_mode.game": "Game Style", "settings.color_test": "Color Contrast Test", "settings.game_style_demo": "Game Style Demo", "settings.wheel_test": "Wheel Test", "settings.display_skin": "Display Skin", "settings.display_options": "Display Options", "settings.accessibility": "Accessibility", "settings.reduce_motion": "Reduce Motion", "settings.notifications": "Notifications", "settings.notifications.enable": "Enable Notifications", "settings.notifications_enabled": "Notifications enabled", "settings.notifications_disabled": "Notifications disabled", "settings.reminders": "Daily Reminders", "settings.reminders.enable": "Enable Reminders", "settings.reminders_enabled": "Daily reminders enabled", "settings.reminders_disabled": "Daily reminders disabled", "settings.reminders.time": "Reminder Time", "settings.export": "Export Data", "settings.export_data": "Export as JSON", "settings.export_success": "Data exported successfully", "settings.exporting_data": "Exporting data...", "settings.export_data_csv": "Export as CSV", "settings.exporting_data_csv": "Exporting CSV...", "settings.export_error": "Export failed", "settings.export_error_csv": "CSV Export Failed", "settings.privacy": "Privacy Policy", "settings.about": "About", "settings.account": "Account", "settings.help": "Help", "settings.feedback": "<PERSON><PERSON><PERSON>", "settings.emotion_style": "Emotion Navigation Style", "settings.emotion_style_changed": "Emotion navigation style updated", "settings.emoji_set_title": "Emoji Set Style", "settings.loading_emoji_sets": "Loading emoji sets", "settings.emoji_set_changed": "Emoji set updated", "settings.emoji_set_change_failed": "Failed to update emoji set", "settings.no_emoji_sets_available": "No emoji sets available at the moment.", "settings.data_sync": "Data Sync", "settings.data_sync_enable_first": "Please enable online sync first.", "settings.data_sync_enable_online": "Enable Online Sync", "settings.data_sync_syncing": "Syncing...", "settings.data_sync_sync_now": "Sync Now", "settings.data_sync_syncing_in_progress": "Synchronization in progress...", "settings.data_sync_initializing": "Initializing Turso database...", "settings.data_sync_initializing_in_progress": "Initializing Turso database...", "settings.data_sync_last_sync_status": "Last Sync", "settings.data_sync_uploaded": "Uploaded: {{count}}", "settings.data_sync_downloaded": "Downloaded: {{count}}", "settings.data_sync_updated_by_server": "Updated by server: {{count}}", "settings.data_sync_error": "Sync Error", "settings.toast.language_changed_to_english": "Language changed to English", "settings.toast.language_changed_to_chinese": "语言已更改为中文", "settings.custom_skin": "Custom Skin", "settings.custom_skin.description": "Create and manage your own custom skins. You can create from scratch or modify existing skins.", "settings.custom_skin.create": "Create Custom Skin", "settings.custom_skin.import": "Import Skin", "settings.custom_skin.migrate_from_wheel": "Migrate from Custom Wheel", "settings.custom_skin.create_title": "Create Custom Skin", "settings.custom_skin.create_description": "Create your own custom skin. You can start from scratch or modify an existing skin.", "settings.custom_skin.name": "Skin Name", "settings.custom_skin.name_placeholder": "Enter skin name", "settings.custom_skin.description_field": "Description", "settings.custom_skin.description_placeholder": "Enter skin description (optional)", "settings.custom_skin.based_on": "Based on Existing Skin", "settings.custom_skin.create_button": "Create Skin", "settings.custom_skin.import_title": "Import Skin", "settings.custom_skin.import_description": "Paste skin data to import a skin.", "settings.custom_skin.import_data": "Import Data", "settings.custom_skin.import_data_placeholder": "Paste skin data (JSON format)", "settings.custom_skin.import_button": "Import", "settings.custom_skin.migrate_title": "Migrate from Custom Wheel", "settings.custom_skin.migrate_description": "Select a custom wheel to migrate its configuration to a new custom skin.", "settings.custom_skin.select_wheel": "Select Custom Wheel", "settings.custom_skin.migrate_button": "Migrate", "settings.custom_skin.name_required": "Please enter a skin name", "settings.custom_skin.base_skin_not_found": "Base skin not found", "settings.custom_skin.create_success": "Skin created successfully", "settings.custom_skin.create_error": "Failed to create skin", "settings.custom_skin.import_data_required": "Please enter import data", "settings.custom_skin.import_invalid_data": "Invalid import data", "settings.custom_skin.import_success": "Skin imported successfully", "settings.custom_skin.import_error": "Import failed, please check data format", "settings.custom_skin.wheel_not_found": "Custom wheel not found", "settings.custom_skin.migrate_success": "Successfully migrated from custom wheel", "settings.custom_skin.migrate_error": "Migration failed", "settings.feature_coming_soon": "This feature is coming soon", "settings.custom_skin.created": "Custom skin created", "settings.skin.filter_by_category": "Filter by Category", "settings.skin.filter_by_view_type": "Filter by View Type", "settings.skin.view_type.all": "All", "settings.skin.view_type.wheel": "Wheel", "settings.skin.view_type.card": "Card", "settings.skin.view_type.bubble": "Bubble", "settings.skin.view_type.galaxy": "Galaxy", "settings.skin.view_type.list": "List", "settings.skin.view_type.grid": "Grid", "settings.skin.supported_view_types": "Supported View Types", "settings.skin.preview_view_type": "Preview View Type", "settings.skin.category.all": "All", "settings.skin.category.free": "Free", "settings.skin.category.paid": "Paid", "settings.skin.category.premium": "Premium", "settings.skin.category.custom": "Custom", "settings.skin.no_preview": "No preview", "settings.skin.no_description": "No description available", "settings.skin.locked": "Locked", "settings.skin.points": "Points", "settings.skin.current": "Currently Active", "settings.skin.use": "Use This Skin", "settings.skin.unlock": "Unlock", "settings.skin.preview": "Preview", "settings.skin.confirm_unlock": "Are you sure you want to unlock this skin?", "settings.skin_unlocked": "Skin unlocked", "settings.skin.unlock_failed": "Failed to unlock skin", "settings.skin_changed_for_view_type": "Skin changed to {{skinName}} to support {{viewType}} view type", "settings.no_skin_supports_view_type": "No available skin supports {{viewType}} view type", "settings.emotion_data": "Emotion Data", "settings.emotion_data.active": "Currently Active", "settings.emotion_data.no_description": "No description", "settings.emotion_data.tiers": "Tiers", "settings.emotion_data.emotions": "Emotions", "settings.emotion_data.current": "Currently in use", "settings.emotion_data.use": "Use this data", "settings.emotion_data.create": "Create from template", "settings.emotion_data.create_from_scratch": "Create from scratch", "settings.emotion_data_changed": "Emotion data changed", "settings.emotion_data_change_failed": "Failed to change emotion data", "settings.display_mode": "Display Mode", "settings.render_engine": "Render Engine", "settings.view_type": "View Type", "settings.view_config": "View Config", "settings.select_view_type": "Select View Type", "settings.view_type.wheel": "Wheel", "settings.view_type.card": "Card", "settings.view_type.bubble": "Bubble", "settings.view_type.galaxy": "Galaxy", "settings.view_config.select_skin": "Select a skin for this view type", "settings.view_config.skin_description": "Choose a skin to customize the appearance of your selected view type. Different skins offer various visual styles and effects.", "settings.view_config.browse_shop": "Browse Skin Shop", "settings.view_config.no_skins_available": "No skins available for this view type. Unlock or create new skins to enhance your experience.", "settings.view_config.unlock_more": "Unlock More", "settings.view_config.browse_more_skins": "Browse More Skins", "settings.view_config.description": "Configure the appearance and behavior of the selected view type", "settings.wheel_view_settings": "Wheel View Settings", "settings.card_view_settings": "Card View Settings", "settings.bubble_view_settings": "Bubble View Settings", "settings.galaxy_view_settings": "Galaxy View Settings", "settings.display_options.description": "These settings control how emotions are displayed in navigation. Different skins may support different options.", "settings.display_mode.description": "Choose how emotions are displayed in navigation", "settings.display_mode.text": "Text Only", "settings.display_mode.text.description": "Show only the text name of emotions", "settings.display_mode.emoji": "Emoji Only", "settings.display_mode.emoji.description": "Show only the emoji of emotions", "settings.display_mode.textEmoji": "Text and Emoji", "settings.display_mode.textEmoji.description": "Show both text name and emoji of emotions", "settings.render_engine.description": "Choose the technology used to render emotion navigation", "settings.render_engine.d3": "D3.js", "settings.render_engine.d3.description": "Use D3.js library for rendering, suitable for most devices", "settings.render_engine.svg": "SVG", "settings.render_engine.svg.description": "Use SVG for rendering, suitable for low-performance devices", "settings.render_engine.r3f": "React Three Fiber", "settings.render_engine.r3f.description": "Use 3D rendering, suitable for high-performance devices", "settings.view_type.description": "Choose the view type for emotion navigation", "settings.view_type.basic": "Basic View Types", "settings.view_type.list_grid": "List & Grid View Types", "settings.view_type.advanced": "Advanced View Types", "settings.view_type.wheel.description": "Circular wheel layout, emotions arranged in sectors", "settings.view_type.card.description": "Card layout, emotions displayed as cards", "settings.view_type.bubble.description": "Bubble layout, emotions displayed as floating bubbles", "settings.view_type.list": "List", "settings.view_type.list.description": "List layout, emotions displayed as a vertical list", "settings.view_type.grid": "Grid", "settings.view_type.grid.description": "Grid layout, emotions displayed in a grid", "settings.view_type.galaxy.description": "Galaxy layout, emotions displayed as planets orbiting a center", "settings.view_type.tree": "Tree", "settings.view_type.tree.description": "Tree layout, emotions displayed in a hierarchical structure", "settings.view_type.flow": "Flow", "settings.view_type.flow.description": "Flow chart layout, showing relationships between emotions", "settings.view_type.tagCloud": "Tag Cloud", "settings.view_type.tagCloud.description": "Tag cloud layout, emotions displayed with varying sizes based on importance", "settings.view_config.browse_skins": "Browse Skins", "settings.view_config.customize_appearance": "Customize Appearance", "settings.view_config.view_more_skins": "View More Skins", "settings.wheel_size": "Wheel Size", "settings.bubble_size": "Bubble Size", "settings.card_size": "<PERSON>", "settings.grid_columns": "<PERSON><PERSON>", "settings.star_size": "Star Size", "settings.orbit_size": "Orbit Size", "settings.background_star_density": "Background Star Density", "settings.node_size": "Node Size", "settings.tree_type": "Tree Type", "settings.select_tree_type": "Select tree type", "settings.tree_type.vertical": "Vertical", "settings.tree_type.horizontal": "Horizontal", "settings.tree_type.radial": "Radial", "settings.tree_type.mindmap": "Mind Map", "settings.edge_style": "Edge Style", "settings.select_edge_style": "Select edge style", "settings.edge_style.straight": "Straight", "settings.edge_style.curved": "Curved", "settings.edge_style.orthogonal": "Orthogonal", "settings.edge_style.bezier": "<PERSON><PERSON>", "settings.layout_direction": "Layout Direction", "settings.select_layout_direction": "Select layout direction", "settings.layout_direction.lr": "Left to Right", "settings.layout_direction.rl": "Right to Left", "settings.layout_direction.tb": "Top to Bottom", "settings.layout_direction.bt": "Bottom to Top", "settings.font_size_range": "Font Size Range", "settings.cloud_shape": "Cloud Shape", "settings.select_cloud_shape": "Select cloud shape", "settings.cloud_shape.circle": "Circle", "settings.cloud_shape.rectangle": "Rectangle", "settings.cloud_shape.triangle": "Triangle", "settings.cloud_shape.star": "Star", "settings.cloud_shape.custom": "Custom", "settings.skin.sample_emotion_data": "Sample Emotion Data", "settings.skin.basic_emotions": "Basic Emotions", "emotions.happy": "Happy", "emotions.sad": "Sad", "emotions.angry": "Angry", "emotions.fear": "Fear", "emotions.surprise": "Surprise", "emotions.disgust": "<PERSON><PERSON><PERSON><PERSON>", "common.close": "Close", "common.general": "General", "common.tiers": "Tiers", "common.preview": "Preview", "common.name": "Name", "common.description": "Description", "common.save": "Save", "common.cancel": "Cancel", "common.delete": "Delete", "common.duplicate": "Duplicate", "common.export": "Export", "common.import": "Import", "common.confirm_delete": "Confirm Delete", "common.paste_json": "Paste JSON data...", "emotion_editor.new_emotion_data": "New Emotion Data", "emotion_editor.create_data": "Create Emotion Data", "emotion_editor.edit_data": "Edit Emotion Data", "emotion_editor.loading": "Loading...", "emotion_editor.import_error": "Import failed, incorrect data format", "emotion_editor.delete_warning": "This action will permanently delete this emotion data and cannot be undone.", "emotion_editor.name_placeholder": "Enter emotion data name", "emotion_editor.description_placeholder": "Enter emotion data description", "emotion_editor.import_description": "Paste emotion data JSON to import", "emotion_editor.from_template": "From Template", "emotion_editor.data_not_found": "Emotion data not found", "emotion_editor.update_success": "Emotion data updated successfully", "emotion_editor.create_success": "Emotion data created successfully", "emotion_editor.save_error": "Failed to save emotion data", "emotion_editor.delete_success": "Emotion data deleted successfully", "emotion_editor.delete_error": "Failed to delete emotion data", "emotion_editor.duplicate_success": "Emotion data duplicated successfully", "emotion_editor.duplicate_error": "Failed to duplicate emotion data", "emotion_editor.import_success": "Emotion data imported successfully", "emotion_editor.export_error": "Failed to export emotion data", "error.not_found": "Page not found", "error.go_home": "Go back home", "error.go_back": "Go back", "error.retry": "Retry", "error.server_error": "Server Error", "error.network_error": "Network Error", "error.network_message": "There seems to be a problem with your network connection", "error.auth_error": "Authentication Error", "error.auth_message": "Please log in to access this page", "error.unknown_error": "Unknown Error", "error.unknown_message": "Something unexpected happened, please try again later", "error.try_again": "Please try again", "error.loading_failed": "Loading failed", "error.generic": "An unexpected error occurred.", "error.failed_to_save_data": "Failed to save data.", "error.failed_to_delete_data": "Failed to delete data.", "error.db_not_initialized": "Database not initialized. Please try again later.", "mood.error_loading_tags": "Error loading tags.", "common.edit": "Edit", "common.confirm": "Confirm", "common.loading": "Loading...", "common.search": "Search", "common.filter": "Filter", "common.sort": "Sort", "common.yes": "Yes", "common.no": "No", "gesture.swipe_left": "Swipe Left", "gesture.swipe_right": "Swipe Right", "gesture.swipe_up": "Swipe Up", "gesture.swipe_down": "Swipe Down", "gesture.pull_to_refresh": "Pull to Refresh", "gesture.refreshing": "Refreshing...", "gesture.tap": "Tap", "gesture.double_tap": "Double Tap", "gesture.long_press": "Long Press", "gesture.pinch": "Pinch", "gesture.rotate": "Rotate", "mood.style.d3wheel": "d3 Wheel", "mood.style.wheel": "Classic Wheel", "mood.style.galaxy": "Galaxy View", "mood.style.layered": "Card Layout", "mood.style.floating": "Floating Bubbles", "mood.style.classicwheel": "Classic Wheel (Text)", "mood.style.emojiwheel": "Emoji Wheel", "mood.style.precisewheel": "Precise Wheel", "mood.style.r3fwheel": "R3F 3D Wheel", "mood.tier.primary": "Primary Emotion", "mood.tier.secondary": "Secondary Emotion", "mood.tier.tertiary": "Tertiary Emotion", "mood.galaxy.select_planet": "Select an emotion planet", "mood.galaxy.select_region": "Select an emotion region", "mood.galaxy.select_crystal": "Select an emotion crystal", "mood.layered.select_primary": "How are you feeling?", "mood.layered.select_secondary": "More specifically?", "mood.layered.select_tertiary": "Choose the exact feeling", "mood.layered.selected": "You selected", "mood.layered.path": "Your emotion path", "errors.failed_to_load_emotions": "Failed to load emotion data. Details: {{details}}", "errors.failed_to_load_history": "Failed to load history. Details: {{details}}", "errors.failed_to_load_analytics": "Failed to load analytics data. Details: {{details}}", "errors.failed_to_load_tags": "Failed to load tags. Details: {{details}}", "skin_preview.tier": "Tier", "skin_preview.view": "View", "skin_preview.impl": "Implementation", "tier_editor.level_1_short": "L1", "tier_editor.level_2_short": "L2", "tier_editor.level_3_short": "L3"}