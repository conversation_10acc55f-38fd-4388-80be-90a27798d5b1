/**
 * 情绪编辑器组件
 * 用于管理层级中的情绪
 */

import { useEmoji } from '@/contexts/EmojiContext';
import { useLanguage } from '@/contexts/LanguageContext';
import type { EmojiItem, Emotion, EmotionTier } from '@/types';
import EmojiPicker, { type EmojiClickData } from 'emoji-picker-react';
import { Edit, Plus, Smile, Trash2 } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { HexColorPicker } from 'react-colorful';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';
import { Button } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';

interface EmotionEditorProps {
  tier: EmotionTier;
  onChange: (emotions: Emotion[]) => void;
  isMobile?: boolean;
}

/**
 * 情绪编辑器组件
 */
export const EmotionEditor: React.FC<EmotionEditorProps> = ({
  tier,
  onChange,
  isMobile: propIsMobile,
}) => {
  const { t } = useLanguage();
  const { availableEmojiSets, activeEmojiSet } = useEmoji();

  const [isAddingEmotion, setIsAddingEmotion] = useState(false);
  const [isEditingEmotion, setIsEditingEmotion] = useState(false);
  const [editingEmotionId, setEditingEmotionId] = useState<string | null>(null);
  const [newEmotionName, setNewEmotionName] = useState('');
  const [newEmotionEmoji, setNewEmotionEmoji] = useState('😊');
  const [newEmotionEmojiSets, setNewEmotionEmojiSets] = useState<Record<string, EmojiItem>>({});
  const [newEmotionColor, setNewEmotionColor] = useState('#FFD700');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [activeEmojiSetId, setActiveEmojiSetId] = useState<string>('');

  // 检测是否为移动端
  const [isMobile, setIsMobile] = useState(
    propIsMobile !== undefined
      ? propIsMobile
      : typeof window !== 'undefined' && window.innerWidth < 768
  );

  // 当 propIsMobile 属性变化时更新 isMobile 状态
  useEffect(() => {
    if (propIsMobile !== undefined) {
      setIsMobile(propIsMobile);
    }
  }, [propIsMobile]);

  // 监听窗口大小变化
  useEffect(() => {
    // 如果提供了 propIsMobile 属性，则不需要监听窗口大小变化
    if (propIsMobile !== undefined) return;

    const handleResize = () => {
      const newIsMobile = window.innerWidth < 768;
      setIsMobile(newIsMobile);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [propIsMobile]);

  // 初始化活动表情集
  useEffect(() => {
    if (activeEmojiSet) {
      setActiveEmojiSetId(activeEmojiSet.id);
    }
  }, [activeEmojiSet]);

  // 确保 emotions 数组总是存在
  const emotions = tier.emotions || [];

  // 处理添加情绪
  const handleAddEmotion = () => {
    if (!newEmotionName.trim()) return;

    // 创建基本情绪对象
    const newEmotion: Emotion = {
      id: `emotion_${Date.now()}`,
      name: newEmotionName,
      emoji: newEmotionEmoji,
      color: newEmotionColor,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_deleted: false,
    };

    const updatedEmotions = [...emotions, newEmotion];
    onChange(updatedEmotions);

    // 重置表单
    resetForm();
  };

  // 处理更新情绪
  const handleUpdateEmotion = () => {
    if (!editingEmotionId || !newEmotionName.trim()) return;

    const emotionIndex = emotions.findIndex((emotion) => emotion.id === editingEmotionId);
    if (emotionIndex === -1) return;

    // 获取原始情绪对象
    const originalEmotion = emotions[emotionIndex];

    // 创建更新后的情绪对象
    const updatedEmotion: Emotion = {
      ...originalEmotion,
      name: newEmotionName,
      emoji: newEmotionEmoji,
      color: newEmotionColor,
      updated_at: new Date().toISOString(),
    };

    const updatedEmotions = [...emotions];
    updatedEmotions[emotionIndex] = updatedEmotion;

    onChange(updatedEmotions);

    // 重置表单
    resetForm();
  };

  // 处理删除情绪
  const handleDeleteEmotion = (emotionId: string) => {
    const updatedEmotions = emotions.filter((emotion) => emotion.id !== emotionId);
    onChange(updatedEmotions);
  };

  // 处理编辑情绪
  const handleEditEmotion = (emotion: Emotion) => {
    setEditingEmotionId(emotion.id);
    setNewEmotionName(emotion.name);
    setNewEmotionEmoji(emotion.emoji || '😊');
    setNewEmotionEmojiSets({}); // 暂时设为空对象，因为当前类型不支持 emoji_sets
    setNewEmotionColor(emotion.color || '#FFD700');
    setIsEditingEmotion(true);

    // 如果有活动表情集，设置为当前表情集
    if (activeEmojiSet) {
      setActiveEmojiSetId(activeEmojiSet.id);
    }
  };

  // 处理Emoji选择
  const handleEmojiSelect = (emojiData: EmojiClickData) => {
    // 更新显示的表情
    setNewEmotionEmoji(emojiData.emoji);

    // 更新表情集映射
    if (activeEmojiSetId) {
      const updatedEmojiSets = { ...newEmotionEmojiSets };
      updatedEmojiSets[activeEmojiSetId] = {
        id: `${activeEmojiSetId}-${Date.now()}`,
        emoji_set_id: activeEmojiSetId,
        emotion_id: '', // 将在保存时设置
        unicode_char: emojiData.emoji,
        unicode: emojiData.emoji, // 向后兼容
        created_at: new Date().toISOString(),
      };
      setNewEmotionEmojiSets(updatedEmojiSets);
    }
  };

  // 处理表情集选择
  const handleEmojiSetChange = (emojiSetId: string) => {
    setActiveEmojiSetId(emojiSetId);

    // 如果已经有这个表情集的表情，显示它
    if (newEmotionEmojiSets[emojiSetId]?.unicode) {
      setNewEmotionEmoji(newEmotionEmojiSets[emojiSetId].unicode || '😶');
    }
  };

  // 重置表单
  const resetForm = () => {
    setNewEmotionName('');
    setNewEmotionEmoji('😊');
    setNewEmotionEmojiSets({});
    setNewEmotionColor('#FFD700');
    setEditingEmotionId(null);
    setIsAddingEmotion(false);
    setIsEditingEmotion(false);
    setShowColorPicker(false);

    // 重置为当前活动表情集
    if (activeEmojiSet) {
      setActiveEmojiSetId(activeEmojiSet.id);
    }
  };

  return (
    <div className="emotion-editor">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">
          {t('emotion_editor.emotions', { fallback: '情绪' })} ({emotions.length})
        </h3>

        <Button variant="outline" size="sm" onClick={() => setIsAddingEmotion(true)}>
          <Plus className="h-4 w-4 mr-1" />
          {t('common.add', { fallback: '添加' })}
        </Button>
      </div>

      {emotions.length === 0 ? (
        <Card>
          <CardContent className="text-center py-6">
            <p className="text-muted-foreground mb-2">
              {t('emotion_editor.no_emotions', { fallback: '没有情绪' })}
            </p>
            <Button variant="outline" size="sm" onClick={() => setIsAddingEmotion(true)}>
              <Plus className="h-4 w-4 mr-1" />
              {t('emotion_editor.create_first', { fallback: '创建第一个情绪' })}
            </Button>
          </CardContent>
        </Card>
      ) : isMobile ? (
        // 移动端垂直布局
        <div className="space-y-3">
          {emotions.map((emotion) => (
            <Card key={emotion.id} className="overflow-hidden">
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{emotion.emoji}</div>
                    <div>
                      <div className="font-medium">{emotion.name}</div>
                      <div className="flex items-center gap-2 mt-1">
                        <div
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: emotion.color || '#FFD700' }}
                        />
                        <span className="text-xs text-muted-foreground">{emotion.color}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-1">
                    <Button variant="ghost" size="icon" onClick={() => handleEditEmotion(emotion)}>
                      <Edit className="h-4 w-4" />
                    </Button>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="icon" className="text-destructive">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent
                        className={
                          isMobile ? 'w-full h-auto max-w-none p-0 rounded-none border-0' : ''
                        }
                      >
                        <div className={isMobile ? 'h-full flex flex-col' : ''}>
                          <AlertDialogHeader className={isMobile ? 'px-4 py-3 border-b' : ''}>
                            <AlertDialogTitle>
                              {t('common.confirm_delete', { fallback: '确认删除' })}
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              {t('emotion_editor.delete_warning', {
                                fallback: '此操作将删除此情绪，无法恢复。',
                              })}
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter
                            className={isMobile ? 'px-4 py-3 border-t mt-auto' : ''}
                          >
                            <AlertDialogCancel>
                              {t('common.cancel', { fallback: '取消' })}
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteEmotion(emotion.id)}
                              className="bg-destructive text-destructive-foreground"
                            >
                              {t('common.delete', { fallback: '删除' })}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </div>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        // 桌面端表格布局
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  {t('emotion_editor.emoji', { fallback: '表情' })}
                </TableHead>
                <TableHead>{t('emotion_editor.name', { fallback: '名称' })}</TableHead>
                <TableHead className="w-24">
                  {t('emotion_editor.color', { fallback: '颜色' })}
                </TableHead>
                <TableHead className="w-24 text-right">
                  {t('common.actions', { fallback: '操作' })}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {emotions.map((emotion) => (
                <TableRow key={emotion.id}>
                  <TableCell className="text-2xl">{emotion.emoji}</TableCell>
                  <TableCell>{emotion.name}</TableCell>
                  <TableCell>
                    <div
                      className="w-6 h-6 rounded-full border"
                      style={{ backgroundColor: emotion.color || '#FFD700' }}
                    />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditEmotion(emotion)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="icon" className="text-destructive">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              {t('common.confirm_delete', { fallback: '确认删除' })}
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              {t('emotion_editor.delete_warning', {
                                fallback: '此操作将删除此情绪，无法恢复。',
                              })}
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>
                              {t('common.cancel', { fallback: '取消' })}
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteEmotion(emotion.id)}
                              className="bg-destructive text-destructive-foreground"
                            >
                              {t('common.delete', { fallback: '删除' })}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* 添加情绪对话框 */}
      <Dialog open={isAddingEmotion} onOpenChange={setIsAddingEmotion}>
        <DialogContent
          className={`${isMobile ? 'w-full h-[100vh] max-w-none p-0 rounded-none border-0' : ''}`}
        >
          <div className={`${isMobile ? 'h-full flex flex-col' : ''}`}>
            <DialogHeader className={`${isMobile ? 'px-4 py-3 border-b' : ''}`}>
              <DialogTitle>{t('emotion_editor.add_emotion', { fallback: '添加情绪' })}</DialogTitle>
              <DialogDescription>
                {t('emotion_editor.add_emotion_description', { fallback: '创建新的情绪' })}
              </DialogDescription>
            </DialogHeader>

            <div className={`space-y-4 ${isMobile ? 'flex-1 overflow-y-auto px-4 py-2' : ''}`}>
              <div className="space-y-2">
                <Label htmlFor="emotionName">{t('common.name', { fallback: '名称' })}</Label>
                <Input
                  id="emotionName"
                  value={newEmotionName}
                  onChange={(e) => setNewEmotionName(e.target.value)}
                  placeholder={t('emotion_editor.name_placeholder', { fallback: '输入情绪名称' })}
                />
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>{t('emotion_editor.emoji_set', { fallback: '表情集' })}</Label>
                  <Select value={activeEmojiSetId} onValueChange={handleEmojiSetChange}>
                    <SelectTrigger className="w-full">
                      <SelectValue
                        placeholder={t('emotion_editor.select_emoji_set', {
                          fallback: '选择表情集',
                        })}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {availableEmojiSets.map((set) => (
                        <SelectItem key={set.id} value={set.id}>
                          {set.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>{t('emotion_editor.emoji', { fallback: '表情' })}</Label>
                  <div className="flex items-center gap-2">
                    <div className="text-2xl border rounded-md p-2 w-12 h-12 flex items-center justify-center">
                      {newEmotionEmoji}
                    </div>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline">
                          <Smile className="h-4 w-4 mr-1" />
                          {t('emotion_editor.choose_emoji', { fallback: '选择表情' })}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0" align="start">
                        <EmojiPicker onEmojiClick={handleEmojiSelect} width={300} height={400} />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* 显示当前表情集的表情预览 */}
                {Object.keys(newEmotionEmojiSets).length > 0 && (
                  <div className="space-y-2">
                    <Label>{t('emotion_editor.emoji_preview', { fallback: '表情预览' })}</Label>
                    <div className="flex flex-wrap gap-2 p-2 border rounded-md">
                      {Object.entries(newEmotionEmojiSets).map(([setId, emojiItem]) => {
                        const emojiSet = availableEmojiSets.find((set) => set.id === setId);
                        return (
                          <div
                            key={setId}
                            className="flex flex-col items-center gap-1 p-2 border rounded-md"
                          >
                            <div className="text-2xl">{emojiItem.unicode || '❓'}</div>
                            <div className="text-xs text-muted-foreground">
                              {emojiSet?.name || setId}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label>{t('emotion_editor.color', { fallback: '颜色' })}</Label>
                <div className="flex items-center gap-2">
                  <div
                    className="w-12 h-12 rounded-md border cursor-pointer"
                    style={{ backgroundColor: newEmotionColor }}
                    onClick={() => setShowColorPicker(!showColorPicker)}
                  />

                  <Input
                    value={newEmotionColor}
                    onChange={(e) => setNewEmotionColor(e.target.value)}
                    placeholder="#FFD700"
                  />
                </div>

                {showColorPicker && (
                  <div className="mt-2">
                    <HexColorPicker color={newEmotionColor} onChange={setNewEmotionColor} />
                  </div>
                )}
              </div>
            </div>

            <DialogFooter className={`${isMobile ? 'px-4 py-3 border-t mt-auto' : 'mt-4'}`}>
              <Button variant="outline" onClick={() => setIsAddingEmotion(false)}>
                {t('common.cancel', { fallback: '取消' })}
              </Button>
              <Button onClick={handleAddEmotion}>{t('common.add', { fallback: '添加' })}</Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>

      {/* 编辑情绪对话框 */}
      <Dialog open={isEditingEmotion} onOpenChange={setIsEditingEmotion}>
        <DialogContent
          className={`${isMobile ? 'w-full h-[100vh] max-w-none p-0 rounded-none border-0' : ''}`}
        >
          <div className={`${isMobile ? 'h-full flex flex-col' : ''}`}>
            <DialogHeader className={`${isMobile ? 'px-4 py-3 border-b' : ''}`}>
              <DialogTitle>
                {t('emotion_editor.edit_emotion', { fallback: '编辑情绪' })}
              </DialogTitle>
              <DialogDescription>
                {t('emotion_editor.edit_emotion_description', { fallback: '修改情绪信息' })}
              </DialogDescription>
            </DialogHeader>

            <div className={`space-y-4 ${isMobile ? 'flex-1 overflow-y-auto px-4 py-2' : ''}`}>
              <div className="space-y-2">
                <Label htmlFor="editEmotionName">{t('common.name', { fallback: '名称' })}</Label>
                <Input
                  id="editEmotionName"
                  value={newEmotionName}
                  onChange={(e) => setNewEmotionName(e.target.value)}
                  placeholder={t('emotion_editor.name_placeholder', { fallback: '输入情绪名称' })}
                />
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>{t('emotion_editor.emoji_set', { fallback: '表情集' })}</Label>
                  <Select value={activeEmojiSetId} onValueChange={handleEmojiSetChange}>
                    <SelectTrigger className="w-full">
                      <SelectValue
                        placeholder={t('emotion_editor.select_emoji_set', {
                          fallback: '选择表情集',
                        })}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {availableEmojiSets.map((set) => (
                        <SelectItem key={set.id} value={set.id}>
                          {set.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>{t('emotion_editor.emoji', { fallback: '表情' })}</Label>
                  <div className="flex items-center gap-2">
                    <div className="text-2xl border rounded-md p-2 w-12 h-12 flex items-center justify-center">
                      {newEmotionEmoji}
                    </div>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline">
                          <Smile className="h-4 w-4 mr-1" />
                          {t('emotion_editor.choose_emoji', { fallback: '选择表情' })}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0" align="start">
                        <EmojiPicker onEmojiClick={handleEmojiSelect} width={300} height={400} />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* 显示当前表情集的表情预览 */}
                {Object.keys(newEmotionEmojiSets).length > 0 && (
                  <div className="space-y-2">
                    <Label>{t('emotion_editor.emoji_preview', { fallback: '表情预览' })}</Label>
                    <div className="flex flex-wrap gap-2 p-2 border rounded-md">
                      {Object.entries(newEmotionEmojiSets).map(([setId, emojiItem]) => {
                        const emojiSet = availableEmojiSets.find((set) => set.id === setId);
                        return (
                          <div
                            key={setId}
                            className="flex flex-col items-center gap-1 p-2 border rounded-md"
                          >
                            <div className="text-2xl">{emojiItem.unicode || '❓'}</div>
                            <div className="text-xs text-muted-foreground">
                              {emojiSet?.name || setId}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label>{t('emotion_editor.color', { fallback: '颜色' })}</Label>
                <div className="flex items-center gap-2">
                  <div
                    className="w-12 h-12 rounded-md border cursor-pointer"
                    style={{ backgroundColor: newEmotionColor }}
                    onClick={() => setShowColorPicker(!showColorPicker)}
                  />

                  <Input
                    value={newEmotionColor}
                    onChange={(e) => setNewEmotionColor(e.target.value)}
                    placeholder="#FFD700"
                  />
                </div>

                {showColorPicker && (
                  <div className="mt-2">
                    <HexColorPicker color={newEmotionColor} onChange={setNewEmotionColor} />
                  </div>
                )}
              </div>
            </div>

            <DialogFooter className={`${isMobile ? 'px-4 py-3 border-t mt-auto' : 'mt-4'}`}>
              <Button variant="outline" onClick={() => setIsEditingEmotion(false)}>
                {t('common.cancel', { fallback: '取消' })}
              </Button>
              <Button onClick={handleUpdateEmotion}>
                {t('common.save', { fallback: '保存' })}
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
