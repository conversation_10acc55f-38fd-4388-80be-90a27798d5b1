import { MoodForm } from '@/components/mood/MoodForm';
import QuizTierNavigation from '@/components/quiz/QuizTierNavigation';
import { Loading } from '@/components/ui/loading';
import { PageTransition } from '@/components/ui/transition';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import { useNewHomeData } from '@/hooks/useNewHomeData';
import { useQuizSession } from '@/hooks/useQuizSession';
import { QuizQuestionOption, QuizPack } from '@/types/schema/base';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, RefreshCw, Play } from 'lucide-react';

const NewHome = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { userConfig } = useUserConfig();

  // 量表选择状态
  const [selectedQuizPackId, setSelectedQuizPackId] = useState<string>('');
  const [availableQuizPacks, setAvailableQuizPacks] = useState<QuizPack[]>([]);

  // 使用新的数据hooks
  const {
    defaultQuiz,
    recommendedQuizPacks,
    isLoading: homeDataLoading,
    error: homeDataError,
    loadDefaultQuiz,
    getQuizPacksByCategory
  } = useNewHomeData(userConfig?.id);

  const {
    startQuizSession,
    submitAnswer,
    isLoading: sessionLoading
  } = useQuizSession();

  // 当前选择状态
  const [currentTierIndex, setCurrentTierIndex] = useState<number>(0);
  const [selectedEmotions, setSelectedEmotions] = useState<
    Array<{
      id: string;
      text: string;
      value: string;
      tierLevel: number;
    }>
  >([]);

  // 选择路径（用于Quiz导航）
  const [selectedPath, setSelectedPath] = useState<{[questionId: string]: QuizQuestionOption}>({});

  // 合并加载状态和错误状态
  const isLoading = homeDataLoading || sessionLoading;
  const error = homeDataError;

  // 初始化可用量表和默认选择
  useEffect(() => {
    const initializeQuizPacks = async () => {
      try {
        // 获取所有可用的量表
        const emotionPacks = await getQuizPacksByCategory('emotion');
        const tcmPacks = await getQuizPacksByCategory('tcm');
        const dailyPacks = await getQuizPacksByCategory('daily');
        const assessmentPacks = await getQuizPacksByCategory('assessment');

        const allPacks = [...emotionPacks, ...tcmPacks, ...dailyPacks, ...assessmentPacks, ...recommendedQuizPacks];

        // 去重
        const uniquePacks = allPacks.filter((pack, index, self) =>
          index === self.findIndex(p => p.id === pack.id)
        );

        setAvailableQuizPacks(uniquePacks);

        // 设置默认选择（如果还没有选择的话）
        if (!selectedQuizPackId && uniquePacks.length > 0) {
          const defaultPack = uniquePacks.find(pack => pack.category === 'emotion') || uniquePacks[0];
          setSelectedQuizPackId(defaultPack.id);
        }
      } catch (error) {
        console.error('Failed to initialize quiz packs:', error);
      }
    };

    if (recommendedQuizPacks.length > 0) {
      initializeQuizPacks();
    }
  }, [recommendedQuizPacks, getQuizPacksByCategory, selectedQuizPackId]);

  // 当选择的量表改变时，重新加载数据
  useEffect(() => {
    if (selectedQuizPackId) {
      loadDefaultQuiz(selectedQuizPackId);
      // 重置当前状态
      setCurrentTierIndex(0);
      setSelectedEmotions([]);
      setSelectedPath({});
    }
  }, [selectedQuizPackId, loadDefaultQuiz]);

  // 处理量表切换
  const handleQuizPackChange = (packId: string) => {
    setSelectedQuizPackId(packId);
  };

  // 跳转到设置页面
  const handleGoToSettings = () => {
    navigate('/quiz-settings');
  };

  // 获取当前问题
  const getCurrentQuestion = () => {
    if (!defaultQuiz?.questions) return null;

    const currentTier = currentTierIndex + 1;
    return defaultQuiz.questions.find(q => q.tier_level === currentTier) || null;
  };



  // 情绪选择处理函数
  const handleEmotionSelect = (selectedOption: QuizQuestionOption) => {
    const newSelection = {
      id: selectedOption.id,
      text: selectedOption.option_text,
      value: selectedOption.option_value,
      tierLevel: currentTierIndex + 1
    };

    const newSelectedEmotions = [...selectedEmotions.slice(0, currentTierIndex), newSelection];
    setSelectedEmotions(newSelectedEmotions);

    // 更新选择路径
    const currentQuestion = getCurrentQuestion();
    if (currentQuestion) {
      setSelectedPath(prev => ({
        ...prev,
        [currentQuestion.id]: selectedOption
      }));
    }

    // 检查是否有下一层级
    const maxTiers = defaultQuiz?.questions.length || 1;
    if (currentTierIndex < maxTiers - 1) {
      setCurrentTierIndex(currentTierIndex + 1);
    } else {
      // 已到最后一层，可以保存
      setCurrentTierIndex(maxTiers);
    }
  };

  // 处理返回上一层级
  const handleTierBack = () => {
    if (currentTierIndex > 0) {
      const newSelectedEmotions = selectedEmotions.slice(0, currentTierIndex - 1);
      setSelectedEmotions(newSelectedEmotions);
      setCurrentTierIndex(currentTierIndex - 1);
    }
  };

  // 处理保存
  const handleSave = async (data: { intensity: number; tags: string[]; reflection: string }) => {
    try {
      // 这里需要实现新的保存逻辑，使用Quiz会话
      if (!defaultQuiz || selectedEmotions.length === 0) {
        toast.error('请先选择情绪');
        return;
      }

      // 创建Quiz会话并提交答案
      if (userConfig?.id && defaultQuiz.pack.id) {
        await startQuizSession(defaultQuiz.pack.id, userConfig.id);

        // 提交选择的情绪作为答案
        for (const emotion of selectedEmotions) {
          await submitAnswer({
            selectedOptionIds: [emotion.id],
            answerValue: emotion.value,
            answerText: emotion.text,
            confidenceScore: data.intensity,
            responseTimeMs: Date.now()
          });
        }

        toast.success(t('success.mood_saved') || '情绪记录已保存');
        navigate('/history');
      } else {
        toast.error('保存失败：用户信息或数据集信息缺失');
      }
    } catch (error) {
      console.error('Failed to save mood entry:', error);
      toast.error('保存失败：' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  if (isLoading) return <Loading />;
  if (error) return <div>Error: {error}</div>;

  // 获取当前问题
  const currentQuestion = getCurrentQuestion();

  // 如果没有当前问题，显示完成状态
  if (!currentQuestion) {
    return (
      <PageTransition>
        <div>
          <MoodForm
            emotionDataId={defaultQuiz?.pack.id || ''}
            mode="multiTier"
            selectedEmotions={selectedEmotions as any}
            onSave={handleSave}
          />
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="space-y-4">
        {/* 量表选择器 */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                当前量表
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGoToSettings}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                配置
              </Button>
            </CardTitle>
            <CardDescription>
              选择您要进行的量表测试，配置页面可以个性化问题和显示方式
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Select value={selectedQuizPackId} onValueChange={handleQuizPackChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择量表..." />
                </SelectTrigger>
                <SelectContent>
                  {availableQuizPacks.map((pack) => (
                    <SelectItem key={pack.id} value={pack.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{pack.name}</span>
                        <Badge variant={pack.category === 'emotion' ? 'default' : 'secondary'} className="ml-2">
                          {pack.category === 'emotion' ? '情绪' :
                           pack.category === 'tcm' ? '中医' :
                           pack.category === 'daily' ? '日常' :
                           pack.category === 'assessment' ? '评估' : pack.category}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* 当前选中量表信息 */}
              {selectedQuizPackId && defaultQuiz && (
                <div className="p-3 bg-muted/50 rounded-lg">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{defaultQuiz.pack.name}</h4>
                      <Badge variant="outline">
                        {defaultQuiz.questions.length} 个问题
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {defaultQuiz.pack.description}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>难度: {defaultQuiz.pack.difficulty_level}</span>
                      <span>•</span>
                      <span>预计时间: {defaultQuiz.pack.estimated_duration_minutes} 分钟</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quiz导航 */}
        <QuizTierNavigation
          currentQuestion={currentQuestion}
          onSelect={handleEmotionSelect}
          onBack={handleTierBack}
          selectedPath={selectedPath}
          packId={defaultQuiz?.pack.id}
        />

        {/* 当到达最后一层时显示保存表单 */}
        {currentTierIndex >= (defaultQuiz?.questions.length || 0) && (
          <MoodForm
            emotionDataId={defaultQuiz?.pack.id || ''}
            mode="multiTier"
            selectedEmotions={selectedEmotions as any}
            onSave={handleSave}
          />
        )}
      </div>
    </PageTransition>
  );
};

export default NewHome;
