import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, User, Crown, Sparkles, Zap, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';

interface MobileSettingsHeaderProps {
  userType: 'beginner' | 'regular' | 'advanced' | 'vip' | 'accessibility';
  onBack?: () => void;
  showBackButton?: boolean;
}

const MobileSettingsHeader: React.FC<MobileSettingsHeaderProps> = ({
  userType,
  onBack,
  showBackButton = false
}) => {
  const { t } = useLanguage();

  // 获取用户类型图标和颜色
  const getUserTypeInfo = () => {
    switch (userType) {
      case 'beginner':
        return {
          icon: Sparkles,
          label: '新手',
          color: 'text-green-600',
          bgColor: 'bg-green-50 dark:bg-green-950',
          borderColor: 'border-green-200 dark:border-green-800'
        };
      case 'advanced':
        return {
          icon: Zap,
          label: '高级',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 dark:bg-blue-950',
          borderColor: 'border-blue-200 dark:border-blue-800'
        };
      case 'vip':
        return {
          icon: Crown,
          label: 'VIP',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50 dark:bg-purple-950',
          borderColor: 'border-purple-200 dark:border-purple-800'
        };
      case 'accessibility':
        return {
          icon: Eye,
          label: '无障碍',
          color: 'text-orange-600',
          bgColor: 'bg-orange-50 dark:bg-orange-950',
          borderColor: 'border-orange-200 dark:border-orange-800'
        };
      default:
        return {
          icon: User,
          label: '普通',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50 dark:bg-gray-950',
          borderColor: 'border-gray-200 dark:border-gray-800'
        };
    }
  };

  const userTypeInfo = getUserTypeInfo();
  const IconComponent = userTypeInfo.icon;

  return (
    <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-lg border-b border-border">
      <div className="max-w-[550px] mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* 左侧：返回按钮（可选）和标题 */}
          <div className="flex items-center space-x-3">
            {showBackButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="p-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary" />
              <h1 className="text-lg font-semibold">
                {t('app.settings') || '设置'}
              </h1>
            </div>
          </div>

          {/* 右侧：用户类型徽章 */}
          <div className={`flex items-center space-x-2 px-3 py-1.5 rounded-full border ${userTypeInfo.bgColor} ${userTypeInfo.borderColor}`}>
            <IconComponent className={`h-3.5 w-3.5 ${userTypeInfo.color}`} />
            <span className={`text-sm font-medium ${userTypeInfo.color}`}>
              {userTypeInfo.label}
            </span>
          </div>
        </div>

        {/* 用户类型说明（可选） */}
        {userType === 'beginner' && (
          <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <p className="text-xs text-blue-700 dark:text-blue-300">
              🌟 新手模式：为您精选最重要的设置选项
            </p>
          </div>
        )}

        {userType === 'vip' && (
          <div className="mt-3 p-2 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950 rounded-lg">
            <p className="text-xs text-purple-700 dark:text-purple-300">
              👑 VIP专享：解锁所有高级功能和个性化选项
            </p>
          </div>
        )}

        {userType === 'accessibility' && (
          <div className="mt-3 p-2 bg-orange-50 dark:bg-orange-950 rounded-lg">
            <p className="text-xs text-orange-700 dark:text-orange-300">
              ♿ 无障碍模式：专为特殊需求用户优化的界面
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileSettingsHeader;
