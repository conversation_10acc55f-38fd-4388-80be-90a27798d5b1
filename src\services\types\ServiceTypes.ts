/**
 * 服务层类型定义
 */

import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

// 基础查询过滤器
export interface BaseFilter {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

// 分页结果
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 服务操作结果
export interface ServiceResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

// 批量操作结果
export interface BatchResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
}

// 基础服务接口
export interface BaseService<T, TCreate, TUpdate> {
  create(data: TCreate): Promise<ServiceResult<T>>;
  getById(id: string): Promise<ServiceResult<T>>;
  update(id: string, data: TUpdate): Promise<ServiceResult<T>>;
  delete(id: string): Promise<ServiceResult<boolean>>;
  list(filters?: BaseFilter): Promise<ServiceResult<T[]>>;
  count(filters?: any): Promise<ServiceResult<number>>;
}

// 可同步服务接口
export interface SyncableService<T> extends BaseService<T, any, any> {
  getUnsynced(userId: string): Promise<ServiceResult<T[]>>;
  markAsSynced(id: string, serverId?: string): Promise<ServiceResult<void>>;
  markAsConflicted(id: string, reason: string): Promise<ServiceResult<void>>;
  upsertFromServer(data: T[]): Promise<ServiceResult<BatchResult>>;
  getLastSyncTimestamp(userId: string): Promise<ServiceResult<string>>;
  updateLastSyncTimestamp(userId: string, timestamp: string): Promise<ServiceResult<void>>;
}

// 数据库上下文
export interface DatabaseContext {
  db: SQLiteDBConnection;
  connection?: any; // For cloud database connections
  userId?: string;
  language?: string;
  transaction?: boolean;
}

// 仓储接口
export interface Repository<T, TCreate, TUpdate> {
  create(context: DatabaseContext, data: TCreate): Promise<T>;
  findById(context: DatabaseContext, id: string): Promise<T | null>;
  update(context: DatabaseContext, id: string, data: TUpdate): Promise<T>;
  delete(context: DatabaseContext, id: string): Promise<boolean>;
  findMany(context: DatabaseContext, filters?: any): Promise<T[]>;
  count(context: DatabaseContext, filters?: any): Promise<number>;
}

// 事务管理器接口
export interface TransactionManager {
  execute<T>(
    db: SQLiteDBConnection,
    operation: (context: DatabaseContext) => Promise<T>
  ): Promise<T>;
}

// 服务配置
export interface ServiceConfig {
  enableLogging?: boolean;
  enableMetrics?: boolean;
  defaultPageSize?: number;
  maxPageSize?: number;
}

// 服务错误类型
export enum ServiceErrorType {
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  CONFLICT = 'CONFLICT',
  UNKNOWN = 'UNKNOWN',
}

// 服务错误类
export class ServiceError extends Error {
  constructor(
    public type: ServiceErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ServiceError';
  }
}

// 验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// 验证器接口
export interface Validator<T> {
  validate(data: T): ValidationResult;
}

// 缓存接口
export interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
}

// 事件类型
export interface ServiceEvent<T = any> {
  type: string;
  data: T;
  timestamp: Date;
  userId?: string;
}

// 事件监听器
export type EventListener<T = any> = (event: ServiceEvent<T>) => void;

// 事件发射器接口
export interface EventEmitter {
  on<T>(eventType: string, listener: EventListener<T>): void;
  off<T>(eventType: string, listener: EventListener<T>): void;
  emit<T>(eventType: string, data: T, userId?: string): void;
}

// 审计日志
export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  entityType: string;
  entityId: string;
  oldData?: any;
  newData?: any;
  timestamp: Date;
  ip?: string;
  userAgent?: string;
}

// 性能指标
export interface PerformanceMetrics {
  operationType: string;
  duration: number;
  success: boolean;
  timestamp: Date;
  metadata?: any;
}
