# MoodWheel 协作指南

## 分支管理
- **主分支**：`main`，用于生产环境代码。
- **开发分支**：`feature/*`，用于新功能开发。
- **修复分支**：`fix/*`，用于修复 bug。
- **合并流程**：开发完成后，提交 PR 到 `main` 分支，进行代码审查。

---

## 代码审查
- **审查内容**：代码风格、功能正确性、测试覆盖率、性能影响等。
- **工具**：GitHub PR 或 GitLab MR。
- **流程**：
  1. 提交 PR 前进行自测，确保代码符合规范并通过测试。
  2. 至少一名团队成员审查并批准后，方可合并。

---

## 文档更新
- **README**：及时更新项目简介、功能说明、开发指南等。
- **协作文档**：更新 API 文档、UI 设计指南、测试指南、部署指南等。
- **提交规范**：文档更新需单独提交，提交信息格式为 `docs: <description>`。

---

## 沟通机制
- **日常沟通**：使用 Slack 或 Discord 进行实时沟通。
- **周会**：每周固定时间进行项目进度同步和问题讨论。
- **问题跟踪**：使用 GitHub Issues 或 Jira 跟踪任务和 bug。

---

## 协作工具
- **代码托管**：GitHub / GitLab
- **项目管理**：Jira / Trello
- **文档协作**：Notion / Confluence
- **设计协作**：Figma

---

## 注意事项
- 保持代码整洁，遵循 ESLint 和 TypeScript 规范。
- 及时同步团队信息，避免信息孤岛。
- 尊重团队协作流程，确保项目顺利推进。 