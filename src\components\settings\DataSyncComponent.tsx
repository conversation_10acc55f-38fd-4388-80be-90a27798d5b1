import SyncSettings from '@/components/sync/SyncSettings';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { useDatabaseContext } from '@/contexts/DatabaseContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useNetwork } from '@/contexts/NetworkContext';
import { useSync } from '@/contexts/SyncContext';
import { Services } from '@/services';
import { OnlineServices } from '@/services/online';
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Cloud,
  CloudOff,
  RefreshCw,
  Server,
  Settings2,
  XCircle,
} from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';

/**
 * 新的同步状态接口 - 对齐服务端实现
 */
interface ModernSyncStatus {
  isSyncing: boolean;
  lastSyncTime: Date | null;
  syncProgress: number; // 0-100
  error: string | null;
  pendingChanges: number;
  uploadedCount?: number;
  downloadedCount?: number;
  conflicts?: number;
}

/**
 * 数据同步组件 (更新版)
 * 基于新的服务端架构和tRPC实现
 */
const DataSyncComponent: React.FC = () => {
  const { t } = useLanguage();
  const { isOnline, isInternetReachable } = useNetwork();
  const { databaseService, isInitialized } = useDatabaseContext();
  const { onlineSyncEnabled, toggleOnlineSync } = useSync();

  // 新的同步状态管理
  const [modernSyncStatus, setModernSyncStatus] = useState<ModernSyncStatus>({
    isSyncing: false,
    lastSyncTime: null,
    syncProgress: 0,
    error: null,
    pendingChanges: 0,
  });

  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // 获取待同步的数据数量
  const getPendingChangesCount = useCallback(async (): Promise<number> => {
    if (!isInitialized || !databaseService) {
      return 0;
    }

    try {
      // 查询所有需要同步的数据
      const moodEntryService = await Services.moodEntry();
      const unsyncedEntries = await moodEntryService.getUnsynced('default-user'); // TODO: 使用实际用户ID

      return unsyncedEntries.success ? unsyncedEntries.data?.length || 0 : 0;
    } catch (error) {
      console.error('Failed to get pending changes count:', error);
      return 0;
    }
  }, [isInitialized, databaseService]);

  // 执行现代化的数据同步 - 基于tRPC和服务端实现
  const performModernSync = useCallback(async (): Promise<void> => {
    if (!onlineSyncEnabled || !isOnline || !isInternetReachable) {
      toast.error(t('sync.no_internet'));
      return;
    }

    if (!isInitialized || !databaseService) {
      toast.error(t('sync.database_not_ready'));
      return;
    }

    setModernSyncStatus((prev) => ({
      ...prev,
      isSyncing: true,
      error: null,
      syncProgress: 0,
    }));

    try {
      // 1. 获取待同步数据
      setModernSyncStatus((prev) => ({ ...prev, syncProgress: 10 }));
      const pendingCount = await getPendingChangesCount();

      setModernSyncStatus((prev) => ({
        ...prev,
        pendingChanges: pendingCount,
        syncProgress: 20,
      }));

      // 2. 准备同步数据
      const moodEntryService = await Services.moodEntry();
      const unsyncedEntries = await moodEntryService.getUnsynced('default-user'); // TODO: 使用实际用户ID

      setModernSyncStatus((prev) => ({ ...prev, syncProgress: 30 }));

      // 3. 调用服务端同步API
      const apiClient = OnlineServices.api;
      const syncRequest = {
        userId: 'default-user', // TODO: 使用实际用户ID
        lastSyncTimestamp: localStorage.getItem('lastSyncTimestamp') || undefined,
        moodEntriesToUpload: unsyncedEntries.success ? unsyncedEntries.data : [],
        emotionSelectionsToUpload: [], // TODO: 实现情绪选择同步
        userConfigsToUpload: [], // TODO: 实现用户配置同步
        tagsToUpload: [], // TODO: 实现标签同步
      };

      setModernSyncStatus((prev) => ({ ...prev, syncProgress: 50 }));

      // 调用服务端同步端点
      const syncResult = await apiClient.call('sync.performFullSync', syncRequest);

      setModernSyncStatus((prev) => ({ ...prev, syncProgress: 80 }));

      if (syncResult.success) {
        // 4. 处理同步结果
        // 标记本地数据为已同步
        if (unsyncedEntries.success && unsyncedEntries.data) {
          for (const entry of unsyncedEntries.data) {
            await moodEntryService.markAsSynced(entry.id);
          }
        }

        // 5. 保存同步时间戳
        localStorage.setItem('lastSyncTimestamp', syncResult.serverTimestamp);

        setModernSyncStatus((prev) => ({
          ...prev,
          isSyncing: false,
          lastSyncTime: new Date().toISOString(),
          syncProgress: 100,
          uploadedCount: syncResult.uploadedCount,
          downloadedCount: syncResult.downloadedCount,
          conflicts: syncResult.conflicts?.length || 0,
          pendingChanges: 0,
        }));

        toast.success(
          t('sync.completed', {
            uploaded: syncResult.uploadedCount.toString(),
            downloaded: syncResult.downloadedCount.toString(),
          })
        );
      } else {
        throw new Error(syncResult.error || 'Sync failed');
      }
    } catch (error) {
      console.error('Modern sync failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown sync error';

      setModernSyncStatus((prev) => ({
        ...prev,
        isSyncing: false,
        error: errorMessage,
      }));

      toast.error(`${t('sync.error')}: ${errorMessage}`);
    }
  }, [
    onlineSyncEnabled,
    isOnline,
    isInternetReachable,
    isInitialized,
    databaseService,
    getPendingChangesCount,
    t,
  ]);

  // 处理手动同步 - 使用现代化实现
  const handleManualSync = async () => {
    if (!onlineSyncEnabled) {
      toast.warning(t('sync.disabled'));
      return;
    }

    await performModernSync();
  };

  // 获取网络状态图标
  const getNetworkStatusIcon = () => {
    if (!isOnline) {
      return <CloudOff className="h-4 w-4 text-destructive" />;
    }

    if (!isInternetReachable) {
      return <AlertTriangle className="h-4 w-4 text-warning" />;
    }

    return <Cloud className="h-4 w-4 text-primary" />;
  };

  // 获取同步状态图标
  const getSyncStatusIcon = () => {
    if (modernSyncStatus.error) {
      return <XCircle className="h-4 w-4 text-destructive" />;
    }

    if (modernSyncStatus.isSyncing) {
      return <RefreshCw className="h-4 w-4 animate-spin text-primary" />;
    }

    if (modernSyncStatus.lastSyncTime) {
      return <CheckCircle className="h-4 w-4 text-success" />;
    }

    return <Clock className="h-4 w-4 text-muted-foreground" />;
  };

  // 自动获取待同步数据数量
  useEffect(() => {
    if (isInitialized && onlineSyncEnabled) {
      getPendingChangesCount().then((count) => {
        setModernSyncStatus((prev) => ({
          ...prev,
          pendingChanges: count,
        }));
      });
    }
  }, [isInitialized, onlineSyncEnabled, getPendingChangesCount]);

  return (
    <div className="space-y-4">
      {/* 基本同步控制 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Server className="h-4 w-4 mr-2" />
          <Label htmlFor="online-sync">{t('settings.data_sync_enable_online')}</Label>
        </div>
        <Switch id="online-sync" checked={onlineSyncEnabled} onCheckedChange={toggleOnlineSync} />
      </div>

      {/* 网络状态指示器 */}
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center">
          {getNetworkStatusIcon()}
          <span className="ml-2">
            {isOnline
              ? isInternetReachable
                ? t('sync.connected')
                : t('sync.no_internet')
              : t('sync.offline')}
          </span>
        </div>

        <div className="flex items-center gap-2">
          {getSyncStatusIcon()}
          <Badge
            variant={
              modernSyncStatus.isSyncing
                ? 'outline'
                : modernSyncStatus.pendingChanges > 0
                  ? 'secondary'
                  : 'default'
            }
          >
            {modernSyncStatus.isSyncing
              ? t('sync.syncing')
              : modernSyncStatus.pendingChanges > 0
                ? t('sync.pending_items_count', { count: String(modernSyncStatus.pendingChanges) })
                : t('sync.up_to_date')}
          </Badge>
        </div>
      </div>

      {/* 同步进度条 */}
      {modernSyncStatus.isSyncing && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>{t('sync.syncing')}</span>
            <span>{modernSyncStatus.syncProgress}%</span>
          </div>
          <Progress value={modernSyncStatus.syncProgress} className="h-2" />
        </div>
      )}

      {/* 同步状态详情 */}
      {(modernSyncStatus.lastSyncTime || modernSyncStatus.error) && (
        <div className="text-sm space-y-1">
          {modernSyncStatus.lastSyncTime && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>
                {t('sync.last_sync')}: {modernSyncStatus.lastSyncTime.toLocaleString()}
              </span>
            </div>
          )}

          {modernSyncStatus.uploadedCount !== undefined &&
            modernSyncStatus.downloadedCount !== undefined && (
              <div className="flex items-center gap-4 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <span>↑ {modernSyncStatus.uploadedCount}</span>
                </div>
                <div className="flex items-center gap-1">
                  <span>↓ {modernSyncStatus.downloadedCount}</span>
                </div>
                {modernSyncStatus.conflicts !== undefined && modernSyncStatus.conflicts > 0 && (
                  <div className="flex items-center gap-1 text-warning">
                    <AlertTriangle className="h-3 w-3" />
                    <span>{modernSyncStatus.conflicts} conflicts</span>
                  </div>
                )}
              </div>
            )}

          {modernSyncStatus.error && (
            <div className="flex items-center gap-2 text-destructive">
              <XCircle className="h-3 w-3" />
              <span>{modernSyncStatus.error}</span>
            </div>
          )}
        </div>
      )}

      {/* 同步操作按钮 */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          className="flex-1 flex items-center justify-center gap-2"
          onClick={handleManualSync}
          disabled={modernSyncStatus.isSyncing || !onlineSyncEnabled || !isOnline || !isInitialized}
        >
          {modernSyncStatus.isSyncing ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          {modernSyncStatus.isSyncing
            ? t('settings.data_sync_syncing')
            : !isInitialized
              ? t('settings.data_sync_initializing')
              : t('settings.data_sync_sync_now')}
        </Button>

        <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="icon">
              <Settings2 className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <SyncSettings onClose={() => setIsSettingsOpen(false)} />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default DataSyncComponent;
