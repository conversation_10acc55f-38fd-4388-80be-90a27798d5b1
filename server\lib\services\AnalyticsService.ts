/**
 * 分析服务
 * 提供数据分析和统计功能
 */

import { executeQuery } from '../database/index.js';

// 导入统一的类型定义
import {
  type AnalyticsQuery,
  type MoodAnalytics,
  type EmotionUsageStats,
  type UserActivityStats
} from '../../../src/types/schema/api.js';

// 从 Schema 推断的子类型
type PeriodData = MoodAnalytics['periodData'] extends (infer T)[] | undefined ? T : never;
type EmotionStat = EmotionUsageStats['topEmotions'] extends (infer T)[] ? T : never;
type EmotionDistribution = EmotionUsageStats['emotionDistribution'] extends (infer T)[] ? T : never;
type TierAnalysis = EmotionUsageStats['tierAnalysis'] extends (infer T)[] ? T : never;
type ActivityPattern = UserActivityStats['activityPattern'] extends (infer T)[] ? T : never;

export class AnalyticsService {
  private static instance: AnalyticsService;

  private constructor() {}

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  /**
   * 获取心情分析数据
   */
  async getMoodAnalytics(query: AnalyticsQuery): Promise<{ success: boolean; data?: MoodAnalytics; error?: string }> {
    try {
      const { userId, startDate, endDate, groupBy } = query;

      // 构建基础查询
      let whereClause = 'WHERE user_id = ?';
      const args: any[] = [userId];

      if (startDate) {
        whereClause += ' AND timestamp >= ?';
        args.push(startDate);
      }

      if (endDate) {
        whereClause += ' AND timestamp <= ?';
        args.push(endDate);
      }

      // 获取基础统计数据
      const basicStatsResult = await executeQuery({
        sql: `
          SELECT
            COUNT(*) as total_entries,
            AVG(intensity) as avg_intensity,
            MIN(intensity) as min_intensity,
            MAX(intensity) as max_intensity
          FROM mood_entries
          ${whereClause}
        `,
        args
      });

      const basicStats = basicStatsResult.rows[0];

      // 获取趋势数据
      const trendResult = await executeQuery({
        sql: `
          SELECT
            DATE(timestamp) as date,
            AVG(intensity) as avg_intensity
          FROM mood_entries
          ${whereClause}
          GROUP BY DATE(timestamp)
          ORDER BY date DESC
          LIMIT 30
        `,
        args
      });

      // 计算趋势
      const moodTrend = this.calculateMoodTrend(trendResult.rows);

      // 获取分组数据
      let periodData: PeriodData[] = [];
      if (groupBy) {
        periodData = await this.getPeriodData(userId, startDate, endDate, groupBy);
      }

      const analytics: MoodAnalytics = {
        totalEntries: parseInt(basicStats.total_entries),
        averageIntensity: parseFloat(basicStats.avg_intensity) || 0,
        minIntensity: parseInt(basicStats.min_intensity) || 0,
        maxIntensity: parseInt(basicStats.max_intensity) || 0,
        moodTrend,
        periodData
      };

      return {
        success: true,
        data: analytics
      };
    } catch (error) {
      console.error('[AnalyticsService] Mood analytics error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get mood analytics'
      };
    }
  }

  /**
   * 获取情绪使用统计
   */
  async getEmotionUsageStats(query: AnalyticsQuery): Promise<{ success: boolean; data?: EmotionUsageStats; error?: string }> {
    try {
      const { userId, startDate, endDate } = query;

      // 构建时间过滤条件
      let timeFilter = '';
      const args: any[] = [userId];

      if (startDate || endDate) {
        timeFilter = 'AND me.timestamp';
        if (startDate && endDate) {
          timeFilter += ' BETWEEN ? AND ?';
          args.push(startDate, endDate);
        } else if (startDate) {
          timeFilter += ' >= ?';
          args.push(startDate);
        } else if (endDate) {
          timeFilter += ' <= ?';
          args.push(endDate);
        }
      }

      // 获取情绪使用统计
      const emotionStatsResult = await executeQuery({
        sql: `
          SELECT
            e.id as emotion_id,
            e.name as emotion_name,
            COUNT(*) as count,
            AVG(me.intensity) as avg_intensity
          FROM emotion_selections es
          JOIN emotions e ON es.emotion_id = e.id
          JOIN mood_entries me ON es.mood_entry_id = me.id
          WHERE me.user_id = ? ${timeFilter}
          GROUP BY e.id, e.name
          ORDER BY count DESC
        `,
        args
      });

      const totalSelections = emotionStatsResult.rows.reduce((sum: number, row: any) => sum + parseInt(row.count), 0);

      const topEmotions: EmotionStat[] = emotionStatsResult.rows.map((row: any) => ({
        emotionId: row.emotion_id,
        emotionName: row.emotion_name,
        count: parseInt(row.count),
        percentage: totalSelections > 0 ? (parseInt(row.count) / totalSelections) * 100 : 0,
        averageIntensity: parseFloat(row.avg_intensity) || 0
      }));

      // 获取情绪分布（按层级）
      const distributionResult = await executeQuery({
        sql: `
          SELECT
            e.id as emotion_id,
            e.name as emotion_name,
            es.tier_level,
            COUNT(*) as count
          FROM emotion_selections es
          JOIN emotions e ON es.emotion_id = e.id
          JOIN mood_entries me ON es.mood_entry_id = me.id
          WHERE me.user_id = ? ${timeFilter}
          GROUP BY e.id, e.name, es.tier_level
          ORDER BY es.tier_level, count DESC
        `,
        args
      });

      const emotionDistribution: EmotionDistribution[] = distributionResult.rows.map((row: any) => ({
        emotionId: row.emotion_id,
        emotionName: row.emotion_name,
        tierLevel: parseInt(row.tier_level),
        count: parseInt(row.count)
      }));

      // 获取层级分析
      const tierAnalysisResult = await executeQuery({
        sql: `
          SELECT
            es.tier_level,
            COUNT(*) as count,
            GROUP_CONCAT(e.name) as emotions
          FROM emotion_selections es
          JOIN emotions e ON es.emotion_id = e.id
          JOIN mood_entries me ON es.mood_entry_id = me.id
          WHERE me.user_id = ? ${timeFilter}
          GROUP BY es.tier_level
          ORDER BY es.tier_level
        `,
        args
      });

      const tierAnalysis: TierAnalysis[] = tierAnalysisResult.rows.map((row: any) => ({
        tierLevel: parseInt(row.tier_level),
        count: parseInt(row.count),
        percentage: totalSelections > 0 ? (parseInt(row.count) / totalSelections) * 100 : 0,
        topEmotions: row.emotions ? row.emotions.split(',').slice(0, 5) : []
      }));

      const stats: EmotionUsageStats = {
        totalSelections,
        topEmotions,
        emotionDistribution,
        tierAnalysis
      };

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('[AnalyticsService] Emotion usage stats error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get emotion usage stats'
      };
    }
  }

  /**
   * 获取用户活动统计
   */
  async getUserActivityStats(userId: string, query?: Partial<AnalyticsQuery>): Promise<{ success: boolean; data?: UserActivityStats; error?: string }> {
    try {
      const { startDate, endDate } = query || {};

      // 构建时间过滤条件
      let whereClause = 'WHERE user_id = ?';
      const args: any[] = [userId];

      if (startDate) {
        whereClause += ' AND timestamp >= ?';
        args.push(startDate);
      }

      if (endDate) {
        whereClause += ' AND timestamp <= ?';
        args.push(endDate);
      }

      // 获取活动天数统计
      const activityResult = await executeQuery({
        sql: `
          SELECT
            COUNT(DISTINCT DATE(timestamp)) as active_days,
            COUNT(*) as total_entries,
            MIN(DATE(timestamp)) as first_date,
            MAX(DATE(timestamp)) as last_date
          FROM mood_entries
          ${whereClause}
        `,
        args
      });

      const activityData = activityResult.rows[0];
      const activeDays = parseInt(activityData.active_days) || 0;
      const totalEntries = parseInt(activityData.total_entries) || 0;

      // 计算总天数
      const firstDate = new Date(activityData.first_date || new Date());
      const lastDate = new Date(activityData.last_date || new Date());
      const totalDays = Math.ceil((lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      // 获取活动模式（按星期几）
      const patternResult = await executeQuery({
        sql: `
          SELECT
            strftime('%w', timestamp) as day_of_week,
            COUNT(*) as entry_count,
            AVG(intensity) as avg_intensity
          FROM mood_entries
          ${whereClause}
          GROUP BY strftime('%w', timestamp)
          ORDER BY day_of_week
        `,
        args
      });

      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const activityPattern: ActivityPattern[] = patternResult.rows.map((row: any) => ({
        dayOfWeek: parseInt(row.day_of_week),
        dayName: dayNames[parseInt(row.day_of_week)],
        entryCount: parseInt(row.entry_count),
        averageIntensity: parseFloat(row.avg_intensity) || 0
      }));

      // 计算连续记录天数（简化实现）
      const streakCurrent = await this.calculateCurrentStreak(userId);
      const streakLongest = await this.calculateLongestStreak(userId);

      const stats: UserActivityStats = {
        totalDays,
        activeDays,
        streakCurrent,
        streakLongest,
        averageEntriesPerDay: activeDays > 0 ? totalEntries / activeDays : 0,
        activityPattern
      };

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('[AnalyticsService] User activity stats error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get user activity stats'
      };
    }
  }

  /**
   * 获取分组数据
   */
  private async getPeriodData(userId: string, startDate?: string, endDate?: string, groupBy?: string): Promise<PeriodData[]> {
    let dateFormat = '';
    let groupByClause = '';

    switch (groupBy) {
      case 'day':
        dateFormat = '%Y-%m-%d';
        groupByClause = 'DATE(timestamp)';
        break;
      case 'week':
        dateFormat = '%Y-W%W';
        groupByClause = 'strftime("%Y-W%W", timestamp)';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        groupByClause = 'strftime("%Y-%m", timestamp)';
        break;
      case 'year':
        dateFormat = '%Y';
        groupByClause = 'strftime("%Y", timestamp)';
        break;
      default:
        return [];
    }

    let whereClause = 'WHERE user_id = ?';
    const args: any[] = [userId];

    if (startDate) {
      whereClause += ' AND timestamp >= ?';
      args.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND timestamp <= ?';
      args.push(endDate);
    }

    const result = await executeQuery({
      sql: `
        SELECT
          strftime('${dateFormat}', timestamp) as period,
          COUNT(*) as entry_count,
          AVG(intensity) as avg_intensity,
          MIN(intensity) as min_intensity,
          MAX(intensity) as max_intensity
        FROM mood_entries
        ${whereClause}
        GROUP BY ${groupByClause}
        ORDER BY period DESC
      `,
      args
    });

    return result.rows.map((row: any) => ({
      period: row.period,
      entryCount: parseInt(row.entry_count),
      averageIntensity: parseFloat(row.avg_intensity) || 0,
      minIntensity: parseInt(row.min_intensity) || 0,
      maxIntensity: parseInt(row.max_intensity) || 0
    }));
  }

  /**
   * 计算心情趋势
   */
  private calculateMoodTrend(trendData: any[]): 'improving' | 'declining' | 'stable' {
    if (trendData.length < 2) return 'stable';

    const recent = trendData.slice(0, Math.min(7, trendData.length));
    const older = trendData.slice(Math.min(7, trendData.length));

    if (recent.length === 0 || older.length === 0) return 'stable';

    const recentAvg = recent.reduce((sum, item) => sum + parseFloat(item.avg_intensity), 0) / recent.length;
    const olderAvg = older.reduce((sum, item) => sum + parseFloat(item.avg_intensity), 0) / older.length;

    const difference = recentAvg - olderAvg;

    if (difference > 5) return 'improving';
    if (difference < -5) return 'declining';
    return 'stable';
  }

  /**
   * 计算当前连续记录天数
   */
  private async calculateCurrentStreak(userId: string): Promise<number> {
    // 简化实现，实际应该计算连续的日期
    const result = await executeQuery({
      sql: `
        SELECT COUNT(DISTINCT DATE(timestamp)) as streak
        FROM mood_entries
        WHERE user_id = ?
        AND timestamp >= date('now', '-30 days')
      `,
      args: [userId]
    });

    return parseInt(result.rows[0]?.streak) || 0;
  }

  /**
   * 计算最长连续记录天数
   */
  private async calculateLongestStreak(userId: string): Promise<number> {
    // 简化实现，实际应该计算历史最长连续天数
    const result = await executeQuery({
      sql: `
        SELECT COUNT(DISTINCT DATE(timestamp)) as longest_streak
        FROM mood_entries
        WHERE user_id = ?
      `,
      args: [userId]
    });

    return parseInt(result.rows[0]?.longest_streak) || 0;
  }
}
