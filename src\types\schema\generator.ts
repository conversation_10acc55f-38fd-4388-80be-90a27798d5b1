/**
 * Schema 生成工具
 *
 * 提供从 SQL 定义生成 Zod Schema 的工具函数
 * 与 base.ts 中的手动定义协同工作，用于验证和扩展
 */

import { z } from 'zod';

// ==================== SQL 到 Zod 类型映射工具 ====================

/**
 * SQL 类型到 Zod Schema 的映射
 */
export const SQL_TO_ZOD_MAPPING = {
  'TEXT': () => z.string(),
  'TEXT PRIMARY KEY': () => z.string(),
  'TEXT NOT NULL': () => z.string(),
  'TEXT UNIQUE': () => z.string(),
  'TEXT UNIQUE NOT NULL': () => z.string(),
  'INTEGER': () => z.number().int(),
  'INTEGER PRIMARY KEY': () => z.number().int(),
  'INTEGER NOT NULL': () => z.number().int(),
  'INTEGER DEFAULT 0': () => z.number().int().default(0),
  'BOOLEAN': () => z.boolean(),
  'BOOLEAN DEFAULT 0': () => z.boolean().default(false),
  'BOOLEAN DEFAULT 1': () => z.boolean().default(true),
  'BOOLEAN DEFAULT TRUE': () => z.boolean().default(true),
  'BOOLEAN DEFAULT FALSE': () => z.boolean().default(false),
  'REAL': () => z.number(),
  'TIMESTAMP': () => z.string().datetime(),
  'TIMESTAMP DEFAULT CURRENT_TIMESTAMP': () => z.string().datetime(),
  'DATE': () => z.string().date(),
  'BLOB': () => z.string() // Base64 encoded
} as const;

/**
 * 字段约束到 Zod 修饰符的映射
 */
export const CONSTRAINT_TO_ZOD_MAPPING = {
  'NOT NULL': (schema: z.ZodTypeAny) => schema,
  'UNIQUE': (schema: z.ZodTypeAny) => schema,
  'PRIMARY KEY': (schema: z.ZodTypeAny) => schema,
  'DEFAULT': (schema: z.ZodTypeAny, value: any) => {
    if (schema instanceof z.ZodString) return schema.default(value);
    if (schema instanceof z.ZodNumber) return schema.default(Number(value));
    if (schema instanceof z.ZodBoolean) return schema.default(Boolean(value));
    return schema;
  },
  'CHECK': (schema: z.ZodTypeAny, condition: string) => {
    // 解析 CHECK 约束并应用相应的验证
    if (condition.includes('BETWEEN')) {
      const match = condition.match(/BETWEEN (\d+) AND (\d+)/);
      if (match && schema instanceof z.ZodNumber) {
        return schema.min(Number(match[1])).max(Number(match[2]));
      }
    }
    return schema;
  }
} as const;

// ==================== 表结构定义 ====================

/**
 * 数据库字段定义
 */
export interface DatabaseField {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: any;
  primaryKey: boolean;
  unique: boolean;
  foreignKey?: {
    table: string;
    column: string;
    onDelete?: string;
    onUpdate?: string;
  };
  check?: string;
}

/**
 * 数据库表定义
 */
export interface DatabaseTable {
  name: string;
  fields: DatabaseField[];
  indexes?: Array<{
    name: string;
    fields: string[];
    unique: boolean;
  }>;
}

// ==================== Schema 生成器 ====================

/**
 * 从数据库字段生成 Zod Schema
 */
export function generateZodSchemaFromField(field: DatabaseField): z.ZodTypeAny {
  // 基础类型映射
  let schema: z.ZodTypeAny;

  // 处理特殊字段名称
  if (field.name.endsWith('_at') || field.name.includes('timestamp')) {
    schema = z.string().datetime();
  } else if (field.name.endsWith('_id') || field.name === 'id') {
    schema = z.string().min(1);
  } else if (field.name.includes('email')) {
    schema = z.string().email();
  } else if (field.name.includes('url')) {
    schema = z.string().url();
  } else if (field.name.includes('color') && field.type === 'TEXT') {
    schema = z.string().regex(/^#[0-9A-Fa-f]{6}$/);
  } else {
    // 根据 SQL 类型映射
    const typeKey = field.type.toUpperCase();
    const mapper = SQL_TO_ZOD_MAPPING[typeKey as keyof typeof SQL_TO_ZOD_MAPPING];
    schema = mapper ? mapper() : z.string();
  }

  // 应用约束
  if (field.check) {
    schema = CONSTRAINT_TO_ZOD_MAPPING.CHECK(schema, field.check);
  }

  if (field.defaultValue !== undefined) {
    schema = CONSTRAINT_TO_ZOD_MAPPING.DEFAULT(schema, field.defaultValue);
  }

  // 处理可空性
  if (field.nullable && !field.primaryKey) {
    schema = schema.optional();
  }

  return schema;
}

/**
 * 从数据库表生成 Zod Schema
 */
export function generateZodSchemaFromTable(table: DatabaseTable): z.ZodObject<any> {
  const schemaFields: Record<string, z.ZodTypeAny> = {};

  for (const field of table.fields) {
    schemaFields[field.name] = generateZodSchemaFromField(field);
  }

  return z.object(schemaFields);
}

// ==================== 类型生成工具 ====================

/**
 * 生成 TypeScript 接口代码
 */
export function generateTypeScriptInterface(
  table: DatabaseTable,
  interfaceName: string
): string {
  const fields = table.fields.map(field => {
    let type = 'string';

    // 类型映射
    if (field.type.includes('INTEGER') || field.type.includes('REAL')) {
      type = 'number';
    } else if (field.type.includes('BOOLEAN')) {
      type = 'boolean';
    } else if (field.name.endsWith('_at') || field.name.includes('timestamp')) {
      type = 'string'; // ISO datetime string
    }

    // 可空性
    const optional = field.nullable && !field.primaryKey ? '?' : '';

    return `  ${field.name}${optional}: ${type};`;
  }).join('\n');

  return `export interface ${interfaceName} {\n${fields}\n}`;
}

/**
 * 生成 Zod Schema 代码
 */
export function generateZodSchemaCode(
  table: DatabaseTable,
  schemaName: string
): string {
  const fields = table.fields.map(field => {
    const zodSchema = generateZodSchemaFromField(field);
    return `  ${field.name}: ${zodSchemaToString(zodSchema)}`;
  }).join(',\n');

  return `export const ${schemaName} = z.object({\n${fields}\n});`;
}

/**
 * 将 Zod Schema 转换为字符串表示
 */
function zodSchemaToString(schema: z.ZodTypeAny): string {
  // 这是一个简化的实现，实际应用中可能需要更复杂的逻辑
  if (schema instanceof z.ZodString) {
    return 'z.string()';
  } else if (schema instanceof z.ZodNumber) {
    return 'z.number()';
  } else if (schema instanceof z.ZodBoolean) {
    return 'z.boolean()';
  } else if (schema instanceof z.ZodOptional) {
    return `${zodSchemaToString(schema._def.innerType)}.optional()`;
  }
  return 'z.any()';
}

// ==================== Schema 验证工具 ====================

/**
 * 验证数据库字段定义与 Zod Schema 的一致性
 */
export function validateSchemaConsistency(
  table: DatabaseTable,
  zodSchema: z.ZodObject<any>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const schemaShape = zodSchema.shape;

  // 检查字段是否存在于 Zod Schema 中
  for (const field of table.fields) {
    if (!(field.name in schemaShape)) {
      errors.push(`Field '${field.name}' exists in table definition but not in Zod schema`);
    }
  }

  // 检查 Zod Schema 中的字段是否存在于表定义中
  for (const fieldName in schemaShape) {
    const tableField = table.fields.find(f => f.name === fieldName);
    if (!tableField) {
      errors.push(`Field '${fieldName}' exists in Zod schema but not in table definition`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 从 SQL CREATE TABLE 语句解析表结构
 */
export function parseCreateTableStatement(sql: string): DatabaseTable | null {
  // 简化的 SQL 解析器，实际应用中可能需要更复杂的实现
  const tableNameMatch = sql.match(/CREATE TABLE (?:IF NOT EXISTS )?(\w+)/i);
  if (!tableNameMatch) return null;

  const tableName = tableNameMatch[1];
  const fields: DatabaseField[] = [];

  // 提取字段定义（这是一个简化的实现）
  const fieldMatches = sql.match(/\(([^)]+)\)/);
  if (!fieldMatches) return null;

  const fieldDefinitions = fieldMatches[1].split(',');

  for (const fieldDef of fieldDefinitions) {
    const trimmed = fieldDef.trim();
    if (trimmed.startsWith('FOREIGN KEY') || trimmed.startsWith('PRIMARY KEY') || trimmed.startsWith('UNIQUE')) {
      continue; // 跳过约束定义
    }

    const parts = trimmed.split(/\s+/);
    if (parts.length < 2) continue;

    const fieldName = parts[0];
    const fieldType = parts.slice(1).join(' ');

    fields.push({
      name: fieldName,
      type: fieldType,
      nullable: !fieldType.includes('NOT NULL'),
      primaryKey: fieldType.includes('PRIMARY KEY'),
      unique: fieldType.includes('UNIQUE')
    });
  }

  return {
    name: tableName,
    fields
  };
}

