/**
 * Quiz类型展示页面
 * 展示不同类型的Quiz包，包括情绪轮盘、中医评估等
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import {
  Heart,
  Brain,
  Stethoscope,
  Target,
  BookOpen,
  Users,
  Clock,
  Star,
  Play,
  ArrowRight
} from 'lucide-react';
import {
  useEmotionWheelQuiz,
  useTCMAssessmentQuiz
} from '../hooks/useQuiz';

const QuizTypes: React.FC = () => {
  const navigate = useNavigate();
  const [selectedType, setSelectedType] = useState<string | null>(null);

  // 获取不同类型的Quiz包
  const emotionWheelQuiz = useEmotionWheelQuiz();
  const tcmAssessmentQuiz = useTCMAssessmentQuiz();

  // 暂时使用空数组，等待tRPC路由集成完成
  const surveyQuizPacks = { data: [] };
  const personalityQuizPacks = { data: [] };

  // Quiz类型配置
  const quizTypes = [
    {
      id: 'emotion_wheel',
      name: '情绪轮盘',
      description: '通过直观的轮盘界面探索和记录您的情绪状态',
      icon: Heart,
      color: 'bg-pink-500',
      features: ['三层情绪联动', '实时情绪分析', '个性化建议'],
      packs: [], // 暂时使用空数组，等待实现
      isLoading: emotionWheelQuiz.isCreating,
      startQuiz: emotionWheelQuiz.startEmotionWheelQuiz
    },
    {
      id: 'tcm_assessment',
      name: '中医体质评估',
      description: '基于传统中医理论的体质评估和健康建议',
      icon: Stethoscope,
      color: 'bg-green-500',
      features: ['脏腑功能评估', '证素状态分析', '个性化调理建议'],
      packs: [], // 暂时使用空数组，等待实现
      isLoading: tcmAssessmentQuiz.isCreating,
      startQuiz: tcmAssessmentQuiz.startTCMAssessment
    },
    {
      id: 'survey',
      name: '问卷调查',
      description: '多样化的问卷调查，了解不同维度的个人特征',
      icon: BookOpen,
      color: 'bg-blue-500',
      features: ['多种题型', '灵活配置', '详细报告'],
      packs: surveyQuizPacks?.data || [],
      isLoading: false,
      startQuiz: null
    },
    {
      id: 'personality_test',
      name: '性格测试',
      description: '深入了解您的性格特征和行为模式',
      icon: Brain,
      color: 'bg-purple-500',
      features: ['科学测评', '性格分析', '发展建议'],
      packs: personalityQuizPacks?.data || [],
      isLoading: false,
      startQuiz: null
    }
  ];

  // 处理Quiz启动
  const handleStartQuiz = async (type: any) => {
    if (type.startQuiz) {
      try {
        const result = await type.startQuiz('current-user'); // 应该从认证上下文获取
        if (result.success) {
          navigate(`/quiz-session/${result.data.id}`);
        }
      } catch (error) {
        console.error('Failed to start quiz:', error);
      }
    } else {
      // 对于没有专用启动方法的类型，跳转到Quiz启动器
      navigate(`/quiz-launcher?type=${type.id}`);
    }
  };

  // 渲染Quiz类型卡片
  const renderQuizTypeCard = (type: any) => {
    const IconComponent = type.icon;

    return (
      <Card
        key={type.id}
        className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
          selectedType === type.id ? 'ring-2 ring-blue-500' : ''
        }`}
        onClick={() => setSelectedType(selectedType === type.id ? null : type.id)}
      >
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`p-3 rounded-lg ${type.color} text-white`}>
                <IconComponent className="h-6 w-6" />
              </div>
              <div>
                <CardTitle className="text-lg">{type.name}</CardTitle>
                <p className="text-sm text-gray-500 mt-1">
                  {type.packs.length} 个可用测评
                </p>
              </div>
            </div>
            <ArrowRight className={`h-5 w-5 text-gray-400 transition-transform ${
              selectedType === type.id ? 'rotate-90' : ''
            }`} />
          </div>
        </CardHeader>

        <CardContent>
          <p className="text-gray-600 mb-4">{type.description}</p>

          {/* 特性标签 */}
          <div className="flex flex-wrap gap-2 mb-4">
            {type.features.map((feature: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {feature}
              </Badge>
            ))}
          </div>

          {/* 展开的详细信息 */}
          {selectedType === type.id && (
            <div className="mt-4 pt-4 border-t">
              {type.isLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">加载中...</p>
                </div>
              ) : type.packs.length > 0 ? (
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">可用测评：</h4>
                  {type.packs.slice(0, 3).map((pack: any) => (
                    <div key={pack.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">{pack.name}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                          <span className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {pack.estimated_duration_minutes || 10}分钟
                          </span>
                          <span className="flex items-center">
                            <Users className="h-3 w-3 mr-1" />
                            {pack.stats?.total_sessions || 0}人完成
                          </span>
                          <span className="flex items-center">
                            <Star className="h-3 w-3 mr-1 text-yellow-500" />
                            {pack.stats?.average_rating || 4.5}
                          </span>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStartQuiz(type);
                        }}
                        disabled={type.isLoading}
                      >
                        <Play className="h-3 w-3 mr-1" />
                        开始
                      </Button>
                    </div>
                  ))}

                  {type.packs.length > 3 && (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/quiz-launcher?type=${type.id}`);
                      }}
                    >
                      查看全部 {type.packs.length} 个测评
                    </Button>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <Target className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">暂无可用测评</p>
                </div>
              )}

              {/* 快速开始按钮 */}
              {type.packs.length > 0 && (
                <Button
                  className="w-full mt-4"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleStartQuiz(type);
                  }}
                  disabled={type.isLoading}
                >
                  <Play className="h-4 w-4 mr-2" />
                  {type.isLoading ? '启动中...' : `开始${type.name}`}
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">测评类型</h1>
          <p className="text-gray-600">
            选择适合您的测评类型，开始探索内心世界
          </p>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {quizTypes.map(type => renderQuizTypeCard(type))}
        </div>

        {/* 底部说明 */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              为什么选择我们的测评？
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="text-center">
                <div className="bg-blue-100 rounded-full p-3 w-12 h-12 mx-auto mb-2">
                  <Brain className="h-6 w-6 text-blue-600" />
                </div>
                <h4 className="font-medium text-gray-900">科学专业</h4>
                <p className="text-sm text-gray-600">基于心理学和中医理论</p>
              </div>
              <div className="text-center">
                <div className="bg-green-100 rounded-full p-3 w-12 h-12 mx-auto mb-2">
                  <Target className="h-6 w-6 text-green-600" />
                </div>
                <h4 className="font-medium text-gray-900">个性化</h4>
                <p className="text-sm text-gray-600">针对性的建议和指导</p>
              </div>
              <div className="text-center">
                <div className="bg-purple-100 rounded-full p-3 w-12 h-12 mx-auto mb-2">
                  <Heart className="h-6 w-6 text-purple-600" />
                </div>
                <h4 className="font-medium text-gray-900">易于使用</h4>
                <p className="text-sm text-gray-600">直观的界面和流程</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizTypes;
