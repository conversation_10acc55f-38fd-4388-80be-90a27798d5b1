import fs from 'node:fs';
import path from 'node:path';

// 递归查找所有 SQL 文件
function findSqlFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      findSqlFiles(filePath, fileList);
    } else if (path.extname(file).toLowerCase() === '.sql') {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// 检查文件中是否有 <= 运算符
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lessThanEqualIndex = content.indexOf('<=');

  if (lessThanEqualIndex !== -1) {
    console.log(`Found <= operator in ${filePath} at position ${lessThanEqualIndex}`);

    // 显示上下文
    const start = Math.max(0, lessThanEqualIndex - 50);
    const end = Math.min(content.length, lessThanEqualIndex + 50);
    console.log(
      `Context: "${content.substring(start, lessThanEqualIndex)}<<<HERE>>>${content.substring(lessThanEqualIndex, end)}"`
    );

    // 显示行号
    const lines = content.substring(0, lessThanEqualIndex).split('\n');
    const lineNumber = lines.length;
    const column = lines[lines.length - 1].length + 1;
    console.log(`Line number: ${lineNumber}, Column: ${column}`);

    // 修复文件
    const fixedContent = content.replace(/<=\s*/g, '< = ');
    fs.writeFileSync(filePath, fixedContent);
    console.log(` ${filePath}`);
  }
}

// 主函数
function main() {
  const sqlFiles = findSqlFiles('.');
  console.log(`Found ${sqlFiles.length} SQL files`);

  sqlFiles.forEach((file) => {
    console.log(`Checking ${file}...`);
    checkFile(file);
  });
}

main();
