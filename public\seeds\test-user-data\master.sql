-- =============================================
-- 测试数据主控制文件 (新配置系统)
-- 根据最新配置系统重构的完整测试数据集
-- 包含新的Quiz系统和分离的配置架构
-- 更新时间: 2024-12-19 (配置系统重构)
-- =============================================

-- 输出开始信息
SELECT '=== Starting Test Data Initialization (New Config System) ===' AS info;

-- 禁用外键约束以便按任意顺序插入数据
PRAGMA foreign_keys=OFF;

-- 0. 初始化新配置系统 (清除旧配置)
SELECT '--- Initializing New Configuration System ---' AS info;
.read public/seeds/config/new_config_system.sql

-- 1. 用户数据 (基础数据，其他表依赖)
SELECT '--- Loading Users ---' AS info;
.read public/seeds/test-user-data/users.sql

-- 2. 全局应用配置 (新配置系统)
SELECT '--- Loading Global App Configs ---' AS info;
.read public/seeds/config/global_app_configs.sql

-- 3. Quiz个性化配置 (新配置系统)
SELECT '--- Loading Quiz Preferences Configs ---' AS info;
.read public/seeds/config/quiz_preferences_configs.sql

-- 4. Quiz包覆盖配置 (新配置系统)
SELECT '--- Loading Quiz Pack Overrides ---' AS info;
.read public/seeds/config/quiz_pack_overrides_new.sql

-- 5. Quiz系统测试数据 (依赖新配置系统)
SELECT '--- Loading Quiz System Test Data ---' AS info;
.read public/seeds/test-user-data/quiz_sessions_test.sql
.read public/seeds/test-user-data/quiz_answers_test.sql

-- 6. 情绪记录 (依赖用户和情绪数据集)
SELECT '--- Loading Mood Entries ---' AS info;
.read public/seeds/test-user-data/mood_entries.sql

-- 7. 用户偏好设置 (依赖用户)
SELECT '--- Loading User Preferences ---' AS info;
.read public/seeds/test-user-data/user_preferences.sql

-- 8. 用户连续记录 (依赖用户)
SELECT '--- Loading User Streaks ---' AS info;
.read public/seeds/test-user-data/user_streaks.sql

-- 9. 情绪记录标签关联 (依赖情绪记录)
SELECT '--- Loading Mood Entry Tags ---' AS info;
.read public/seeds/test-user-data/mood_entry_tags.sql

-- 重新启用外键约束
PRAGMA foreign_keys=ON;

-- 验证新配置系统数据完整性
SELECT '=== New Configuration System Verification ===' AS info;

-- 检查用户数据
SELECT 'Users: ' || COUNT(*) || ' records' AS summary FROM users;

-- 检查新配置系统
SELECT 'Global App Configs: ' || COUNT(*) || ' records' AS summary FROM user_configs;
SELECT 'Quiz Preferences: ' || COUNT(*) || ' records' AS summary FROM user_quiz_preferences;
SELECT 'Quiz Pack Overrides: ' || COUNT(*) || ' records' AS summary FROM quiz_pack_overrides;
SELECT 'Quiz Session Configs: ' || COUNT(*) || ' records' AS summary FROM quiz_session_configs;

-- 检查情绪数据集
SELECT 'Emotion Data Sets: ' || COUNT(*) || ' records' AS summary FROM emotion_data_sets;
SELECT 'Emotion Data Set Tiers: ' || COUNT(*) || ' records' AS summary FROM emotion_data_set_tiers;
SELECT 'Emotions: ' || COUNT(*) || ' records' AS summary FROM emotions;

-- 检查Quiz系统数据
SELECT 'Quiz Packs: ' || COUNT(*) || ' records' AS summary FROM quiz_packs;
SELECT 'Quiz Sessions: ' || COUNT(*) || ' records' AS summary FROM quiz_sessions;
SELECT 'Quiz Answers: ' || COUNT(*) || ' records' AS summary FROM quiz_answers;
SELECT 'Quiz Results: ' || COUNT(*) || ' records' AS summary FROM quiz_results;

-- 检查情绪相关数据
SELECT 'Mood Entries: ' || COUNT(*) || ' records' AS summary FROM mood_entries;

-- 检查关联数据
SELECT 'User Preferences: ' || COUNT(*) || ' records' AS summary FROM user_preferences;
SELECT 'User Streaks: ' || COUNT(*) || ' records' AS summary FROM user_streaks;

-- 检查新配置系统数据关联完整性
SELECT '--- Checking New Config System Relationships ---' AS info;

-- 检查全局应用配置关联
SELECT
    'Global Config Relations: ' ||
    (SELECT COUNT(*) FROM user_configs uc
     JOIN users u ON uc.user_id = u.id) ||
    ' valid relationships' AS relationship_check;

-- 检查Quiz偏好配置关联
SELECT
    'Quiz Preferences Relations: ' ||
    (SELECT COUNT(*) FROM user_quiz_preferences uqp
     JOIN users u ON uqp.user_id = u.id) ||
    ' valid relationships' AS relationship_check;

-- 检查Quiz包覆盖配置关联
SELECT
    'Pack Overrides Relations: ' ||
    (SELECT COUNT(*) FROM quiz_pack_overrides qpo
     JOIN users u ON qpo.user_id = u.id) ||
    ' valid relationships' AS relationship_check;

-- 检查情绪记录是否正确关联到用户
SELECT
    'Mood Entry Relations: ' ||
    (SELECT COUNT(*) FROM mood_entries me
     JOIN users u ON me.user_id = u.id) ||
    ' valid relationships' AS relationship_check;

-- 显示测试用户概览 (新配置系统)
SELECT '--- Test Users Overview (New Config System) ---' AS info;
SELECT
    u.id,
    u.username,
    u.display_name,
    u.is_vip,
    u.vip_tier,
    gc.theme_mode,
    gc.language,
    JSON_EXTRACT(qp.presentation_config, '$.layer1_user_choice.preferred_view_type') AS preferred_view_type,
    JSON_EXTRACT(qp.presentation_config, '$.layer1_user_choice.active_skin_id') AS active_skin_id,
    qp.personalization_level
FROM users u
LEFT JOIN user_configs gc ON u.id = gc.user_id AND gc.is_active = 1
LEFT JOIN user_quiz_preferences qp ON u.id = qp.user_id AND qp.is_active = 1
WHERE u.id LIKE 'test-user-%'
ORDER BY u.id;

-- 显示情绪记录概览
SELECT '--- Recent Mood Entries Overview ---' AS info;
SELECT
    me.id,
    u.username,
    me.emotion_data_set_id,
    me.intensity,
    me.energy_level,
    me.stress_level,
    datetime(me.timestamp) AS entry_time
FROM mood_entries me
JOIN users u ON me.user_id = u.id
ORDER BY me.timestamp DESC
LIMIT 10;

-- 显示Quiz包概览
SELECT '--- Quiz Packs Overview ---' AS info;
SELECT
    qp.id,
    qp.name,
    JSON_EXTRACT(qp.metadata, '$.category') AS category,
    JSON_EXTRACT(qp.metadata, '$.difficulty_level') AS difficulty,
    JSON_EXTRACT(qp.metadata, '$.estimated_duration_minutes') AS duration_min,
    qp.emotion_data_set_id,
    qp.is_active,
    qp.is_default
FROM quiz_packs qp
ORDER BY qp.sort_order;

-- 显示情绪数据集概览
SELECT '--- Emotion Data Sets Overview ---' AS info;
SELECT
    eds.id,
    eds.name,
    eds.data_structure_type,
    eds.tier_count,
    eds.total_emotions_count,
    COUNT(edst.id) AS actual_tiers
FROM emotion_data_sets eds
LEFT JOIN emotion_data_set_tiers edst ON eds.id = edst.data_set_id
GROUP BY eds.id, eds.name, eds.data_structure_type, eds.tier_count, eds.total_emotions_count
ORDER BY eds.id;

-- 显示Quiz会话概览
SELECT '--- Quiz Sessions Overview ---' AS info;
SELECT
    qs.id,
    qs.pack_id,
    qs.user_id,
    qs.status,
    qs.completion_percentage,
    qs.answered_questions,
    datetime(qs.start_time) AS start_time,
    CASE
        WHEN qs.end_time IS NOT NULL THEN datetime(qs.end_time)
        ELSE 'In Progress'
    END AS end_time
FROM quiz_sessions qs
ORDER BY qs.start_time DESC;

-- 显示新配置系统概览
SELECT '--- New Configuration System Overview ---' AS info;
SELECT
    'Global Configs: ' || COUNT(*) || ' records' AS config_summary
FROM user_configs;

SELECT
    'Quiz Preferences: ' || COUNT(*) || ' records' AS config_summary
FROM user_quiz_preferences;

SELECT
    'Pack Overrides: ' || COUNT(*) || ' records' AS config_summary
FROM quiz_pack_overrides;

-- 显示配置系统特性
SELECT '--- Configuration System Features ---' AS info;
SELECT 'Feature: Global App Settings (theme, language, notifications, accessibility)' AS feature;
SELECT 'Feature: 6-Layer Quiz Personalization (dataset, user choice, rendering, skin, view detail, accessibility)' AS feature;
SELECT 'Feature: Pack-Specific Overrides (per-quiz customization)' AS feature;
SELECT 'Feature: Session Config Generation (merged final configuration)' AS feature;
SELECT 'Feature: Offline-First with Online Sync (hybrid architecture)' AS feature;

SELECT '=== New Configuration System Initialization Complete ===' AS info;
SELECT 'Ready for new config system testing and development!' AS status;
SELECT 'Architecture: Global App Settings + Quiz System Configs (completely separated)' AS architecture;
SELECT 'Hooks: useGlobalConfig + useQuizConfig (type-safe React hooks)' AS hooks;
