/**
 * 情绪视图接口
 * 所有视图类型的统一接口
 */

import React from 'react';
import {
  Emotion,
  SkinConfig,
  ContentDisplayMode,
  ViewType,
  ViewConfig,
  RenderEngine
} from '@/types';

/**
 * 情绪视图接口
 * 所有视图类型的统一接口
 */
export interface EmotionView {
  // 获取视图类型
  getType(): ViewType;

  // 获取内容显示模式
  getContentDisplayMode(): ContentDisplayMode;

  // 获取皮肤配置
  getSkinConfig(): SkinConfig;

  // 应用皮肤配置
  applySkinConfig(config: SkinConfig): void;

  // 设置内容显示模式
  setContentDisplayMode(mode: ContentDisplayMode): void;

  // 渲染视图（通用方法）
  render(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode;

  // 设置布局
  setLayout(layout: string): void;

  // 获取布局
  getLayout(): string;

  // 设置动画效果
  setAnimation(animation: any): void;

  // 获取动画效果
  getAnimation(): any;

  // 设置3D效果
  set3DEffects(use3D: boolean, config?: any): void;

  // 获取3D效果状态
  get3DEffects(): { enabled: boolean; config?: any };

  // 获取实现类型（对于轮盘视图等需要不同实现的视图类型）
  getImplementation?(): RenderEngine;

  // 设置实现类型（对于轮盘视图等需要不同实现的视图类型）
  setImplementation?(implementation: RenderEngine): void;
}
