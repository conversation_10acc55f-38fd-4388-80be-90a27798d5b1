/**
 * Cloudflare D1 数据库适配器
 * 实现 DatabaseInterface 接口，提供 Cloudflare D1 数据库的具体实现
 */
export class D1Adapter {
  d1Database;
  transactionStatements = [];
  isInTransaction = false;
  /**
   * 构造函数
   * @param d1Database Cloudflare D1 数据库实例
   */
  constructor(d1Database) {
    this.d1Database = d1Database;
  }
  /**
   * 获取 D1 数据库连接
   */
  getConnection() {
    if (!this.d1Database) {
      throw new Error('[D1Adapter] D1 database instance not provided');
    }
    return this.d1Database;
  }
  /**
   * 执行单个 SQL 查询
   * @param sql SQL 查询或 InStatement 对象
   */
  async executeQuery(sql) {
    const db = this.getConnection();
    let sqlString;
    let args = [];
    if (typeof sql === 'string') {
      sqlString = sql;
    } else {
      sqlString = sql.sql;
      args = sql.args || [];
    }
    try {
      // 如果在事务中，将语句添加到事务队列
      if (this.isInTransaction) {
        this.transactionStatements.push(typeof sql === 'string' ? { sql, args: [] } : sql);
        // 返回一个模拟的结果集，因为我们不能在事务中立即执行
        return {
          columns: [],
          rows: [],
          rowsAffected: 0,
          lastInsertId: undefined,
        };
      }
      // 执行查询
      const result = await db
        .prepare(sqlString)
        .bind(...args)
        .run();
      // 转换 D1 结果为标准 ResultSet
      const standardResult = {
        columns: result.meta?.columns || [],
        rows: result.results || [],
        rowsAffected: result.meta?.changes || 0,
        lastInsertId: result.meta?.last_row_id,
      };
      // 记录查询执行情况
      const truncatedSql = sqlString.length > 100 ? `${sqlString.substring(0, 100)}...` : sqlString;
      console.log(
        `[D1Adapter] Executed query: ${truncatedSql} (${standardResult.rowsAffected} rows affected)`
      );
      return standardResult;
    } catch (error) {
      console.error(`[D1Adapter] Error executing query: ${sqlString}`, error);
      throw error;
    }
  }
  /**
   * 在事务中执行一批 SQL 语句
   * @param statements InStatement 对象数组
   * @param mode 事务模式
   */
  async batchStatements(statements, mode = 'write') {
    if (!statements || statements.length === 0) {
      console.warn('[D1Adapter] No statements provided for batch execution');
      return [];
    }
    const db = this.getConnection();
    try {
      // D1 不支持事务模式，忽略 mode 参数
      // 开始事务
      await db.exec('BEGIN TRANSACTION');
      const results = [];
      // 执行每个语句
      for (const stmt of statements) {
        const result = await db
          .prepare(stmt.sql)
          .bind(...(stmt.args || []))
          .run();
        results.push({
          columns: result.meta?.columns || [],
          rows: result.results || [],
          rowsAffected: result.meta?.changes || 0,
          lastInsertId: result.meta?.last_row_id,
        });
      }
      // 提交事务
      await db.exec('COMMIT');
      console.log(`[D1Adapter] Successfully executed batch of ${statements.length} statements`);
      return results;
    } catch (error) {
      console.error('[D1Adapter] Error in batch transaction:', error);
      // 回滚事务
      try {
        await db.exec('ROLLBACK');
        console.log('[D1Adapter] Transaction rolled back successfully');
      } catch (rollbackError) {
        console.error('[D1Adapter] Failed to rollback transaction:', rollbackError);
      }
      throw error;
    }
  }
  /**
   * 执行多语句 SQL 脚本
   * @param sqlScript 包含多个语句的 SQL 脚本
   */
  async executeScript(sqlScript) {
    if (!sqlScript || sqlScript.trim() === '') {
      console.warn('[D1Adapter] Empty SQL script provided');
      return;
    }
    const db = this.getConnection();
    try {
      // 通过计算分号来估计语句数量
      const statementCount = (sqlScript.match(/;/g) || []).length + 1;
      // D1 支持 exec 方法来执行多语句脚本
      await db.exec(sqlScript);
      console.log(
        `[D1Adapter] Successfully executed SQL script with ~${statementCount} statements`
      );
    } catch (error) {
      console.error('[D1Adapter] Failed to execute SQL script:', error);
      throw error;
    }
  }
  /**
   * 从指定表中获取所有行
   * @param tableName 表名
   * @param limit 可选的行数限制
   */
  async fetchAllFromTable(tableName, limit) {
    // 验证表名以防止 SQL 注入
    if (!/^[a-zA-Z0-9_]+$/.test(tableName)) {
      throw new Error(`Invalid table name: ${tableName}`);
    }
    // 构建带有可选限制的查询
    const query = limit
      ? `SELECT * FROM ${tableName} LIMIT ${limit}`
      : `SELECT * FROM ${tableName}`;
    try {
      const result = await this.executeQuery(query);
      console.log(`[D1Adapter] Fetched ${result.rows.length} rows from table '${tableName}'`);
      return result.rows;
    } catch (error) {
      console.error(`[D1Adapter] Failed to fetch data from table '${tableName}':`, error);
      throw error;
    }
  }
  /**
   * 开始事务
   * @param mode 事务模式
   */
  async transaction(mode = 'write') {
    const db = this.getConnection();
    // 清空事务语句队列
    this.transactionStatements = [];
    this.isInTransaction = true;
    // 执行 BEGIN TRANSACTION
    await db.exec('BEGIN TRANSACTION');
    // 返回一个 Transaction 对象
    return {
      execute: async (sql) => {
        // 将语句添加到事务队列
        if (typeof sql === 'string') {
          this.transactionStatements.push({ sql, args: [] });
        } else {
          this.transactionStatements.push(sql);
        }
        // 返回一个模拟的结果集
        return {
          columns: [],
          rows: [],
          rowsAffected: 0,
          lastInsertId: undefined,
        };
      },
      batch: async (statements) => {
        // 将所有语句添加到事务队列
        this.transactionStatements.push(...statements);
        // 返回模拟的结果集数组
        return statements.map(() => ({
          columns: [],
          rows: [],
          rowsAffected: 0,
          lastInsertId: undefined,
        }));
      },
      commit: async () => {
        try {
          // 执行所有事务语句
          for (const stmt of this.transactionStatements) {
            await db
              .prepare(stmt.sql)
              .bind(...(stmt.args || []))
              .run();
          }
          // 提交事务
          await db.exec('COMMIT');
          this.isInTransaction = false;
          this.transactionStatements = [];
        } catch (error) {
          console.error('[D1Adapter] Error committing transaction:', error);
          // 回滚事务
          await db.exec('ROLLBACK');
          this.isInTransaction = false;
          this.transactionStatements = [];
          throw error;
        }
      },
      rollback: async () => {
        // 回滚事务
        await db.exec('ROLLBACK');
        this.isInTransaction = false;
        this.transactionStatements = [];
      },
    };
  }
  /**
   * 关闭数据库连接
   */
  async close() {
    // D1 不需要显式关闭连接
    console.log('[D1Adapter] Database connection closed (no-op for D1)');
  }
  /**
   * 获取数据库类型
   */
  getDatabaseType() {
    return 'd1';
  }
}
