/**
 * colorUtils 测试
 * 测试颜色工具函数的各种功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  parseRgb,
  calculateContrastRatio,
  rgbToHsl,
  calculateColorSimilarity,
  areColorsTooSimilar,
  isTooSimilarToBackground,
  getColorPool,
  assignColorsToEmotions,
  getBackgroundColor,
  warmLightColors,
  coolLightColors,
  gameLightColors,
  lightModeColors
} from '../colorUtils';

// Mock window and document for testing
const mockWindow = {
  getComputedStyle: vi.fn()
};

const mockDocument = {
  body: {}
};

Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true
});

Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true
});

describe('colorUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockWindow.getComputedStyle.mockReturnValue({
      background_color: '#ffffff'
    });
  });

  describe('parseRgb', () => {
    it('should parse RGB color string correctly', () => {
      const result = parseRgb('rgb(255, 0, 0)');
      expect(result).toEqual({ r: 1, g: 0, b: 0 });
    });

    it('should parse RGBA color string correctly', () => {
      const result = parseRgb('rgba(255, 0, 0, 0.5)');
      expect(result).toEqual({ r: 1, g: 0, b: 0 });
    });

    it('should parse hex color string correctly', () => {
      const result = parseRgb('#ff0000');
      expect(result).toEqual({ r: 1, g: 0, b: 0 });
    });

    it('should parse short hex color string correctly', () => {
      const result = parseRgb('#f00');
      expect(result).toEqual({ r: 1, g: 0, b: 0 });
    });

    it('should return black for invalid color string', () => {
      const result = parseRgb('invalid');
      expect(result).toEqual({ r: 0, g: 0, b: 0 });
    });

    it('should handle hex without # prefix', () => {
      const result = parseRgb('ff0000');
      expect(result).toEqual({ r: 1, g: 0, b: 0 });
    });
  });

  describe('calculateContrastRatio', () => {
    it('should calculate contrast ratio between black and white', () => {
      const ratio = calculateContrastRatio('#000000', '#ffffff');
      expect(ratio).toBeCloseTo(21, 1);
    });

    it('should calculate contrast ratio between same colors', () => {
      const ratio = calculateContrastRatio('#ff0000', '#ff0000');
      expect(ratio).toBeCloseTo(1, 1);
    });

    it('should calculate contrast ratio between red and blue', () => {
      const ratio = calculateContrastRatio('#ff0000', '#0000ff');
      expect(ratio).toBeGreaterThan(1);
      expect(ratio).toBeLessThan(21);
    });

    it('should handle RGB format colors', () => {
      const ratio = calculateContrastRatio('rgb(255, 0, 0)', 'rgb(0, 0, 255)');
      expect(ratio).toBeGreaterThan(1);
    });
  });

  describe('rgbToHsl', () => {
    it('should convert red to HSL correctly', () => {
      const result = rgbToHsl(1, 0, 0);
      expect(result.h).toBeCloseTo(0, 1);
      expect(result.s).toBeCloseTo(1, 1);
      expect(result.l).toBeCloseTo(0.5, 1);
    });

    it('should convert green to HSL correctly', () => {
      const result = rgbToHsl(0, 1, 0);
      expect(result.h).toBeCloseTo(120, 1);
      expect(result.s).toBeCloseTo(1, 1);
      expect(result.l).toBeCloseTo(0.5, 1);
    });

    it('should convert blue to HSL correctly', () => {
      const result = rgbToHsl(0, 0, 1);
      expect(result.h).toBeCloseTo(240, 1);
      expect(result.s).toBeCloseTo(1, 1);
      expect(result.l).toBeCloseTo(0.5, 1);
    });

    it('should convert white to HSL correctly', () => {
      const result = rgbToHsl(1, 1, 1);
      expect(result.h).toBe(0);
      expect(result.s).toBe(0);
      expect(result.l).toBe(1);
    });

    it('should convert black to HSL correctly', () => {
      const result = rgbToHsl(0, 0, 0);
      expect(result.h).toBe(0);
      expect(result.s).toBe(0);
      expect(result.l).toBe(0);
    });
  });

  describe('calculateColorSimilarity', () => {
    it('should return 0 for identical colors', () => {
      const similarity = calculateColorSimilarity('#ff0000', '#ff0000');
      expect(similarity).toBeCloseTo(0, 2);
    });

    it('should return higher value for different colors', () => {
      const similarity = calculateColorSimilarity('#ff0000', '#0000ff');
      expect(similarity).toBeGreaterThan(0.5);
    });

    it('should return maximum value for black and white', () => {
      const similarity = calculateColorSimilarity('#000000', '#ffffff');
      expect(similarity).toBeCloseTo(1, 1);
    });

    it('should handle RGB format colors', () => {
      const similarity = calculateColorSimilarity('rgb(255, 0, 0)', 'rgb(0, 255, 0)');
      expect(similarity).toBeGreaterThan(0);
    });
  });

  describe('areColorsTooSimilar', () => {
    it('should return true for very similar colors', () => {
      const result = areColorsTooSimilar('#ff0000', '#fe0000');
      expect(result).toBe(true);
    });

    it('should return false for different colors', () => {
      const result = areColorsTooSimilar('#ff0000', '#0000ff');
      expect(result).toBe(false);
    });

    it('should consider tier level in similarity check', () => {
      const primaryResult = areColorsTooSimilar('#ff0000', '#ee0000', 'primary');
      const tertiaryResult = areColorsTooSimilar('#ff0000', '#ee0000', 'tertiary');
      
      // Tertiary should be more strict
      expect(tertiaryResult).toBe(true);
    });

    it('should consider color mode in similarity check', () => {
      const mixedResult = areColorsTooSimilar('#ff0000', '#ee0000', 'primary', 'mixed');
      const gameResult = areColorsTooSimilar('#ff0000', '#ee0000', 'primary', 'game');
      
      // Game mode should be more strict
      expect(gameResult).toBe(true);
    });

    it('should handle error gracefully', () => {
      const result = areColorsTooSimilar('invalid', '#ff0000');
      expect(result).toBe(false);
    });
  });

  describe('getBackgroundColor', () => {
    it('should return white as default in Node.js environment', () => {
      // Temporarily remove window
      const originalWindow = global.window;
      delete (global as any).window;
      
      const result = getBackgroundColor();
      expect(result).toBe('#ffffff');
      
      // Restore window
      global.window = originalWindow;
    });

    it('should return computed background color when available', () => {
      mockWindow.getComputedStyle.mockReturnValue({
        background_color: '#f0f0f0'
      });
      
      const result = getBackgroundColor();
      expect(result).toBe('#f0f0f0');
    });

    it('should return default white when computed style is empty', () => {
      mockWindow.getComputedStyle.mockReturnValue({
        background_color: ''
      });
      
      const result = getBackgroundColor();
      expect(result).toBe('#ffffff');
    });
  });

  describe('isTooSimilarToBackground', () => {
    beforeEach(() => {
      mockWindow.getComputedStyle.mockReturnValue({
        background_color: '#ffffff'
      });
    });

    it('should return true for colors too similar to white background', () => {
      const result = isTooSimilarToBackground('#f0f0f0', false);
      expect(result).toBe(true);
    });

    it('should return false for colors different from background', () => {
      // Use a color that should have good contrast with white background
      const result = isTooSimilarToBackground('#000000', false);
      expect(result).toBe(false);
    });

    it('should consider dark mode in similarity check', () => {
      const lightResult = isTooSimilarToBackground('#f0f0f0', false);
      const darkResult = isTooSimilarToBackground('#101010', true);

      expect(lightResult).toBe(true);
      expect(darkResult).toBe(true); // Dark color in dark mode should also be too similar
    });

    it('should handle error gracefully', () => {
      mockWindow.getComputedStyle.mockImplementation(() => {
        throw new Error('Test error');
      });
      
      const result = isTooSimilarToBackground('#ff0000', false);
      expect(result).toBe(false);
    });
  });

  describe('getColorPool', () => {
    it('should return warm light colors for warm mode in light theme', () => {
      const result = getColorPool('warm', false);
      expect(result).toEqual(warmLightColors);
    });

    it('should return cool light colors for cool mode in light theme', () => {
      const result = getColorPool('cool', false);
      expect(result).toEqual(coolLightColors);
    });

    it('should return game light colors for game mode in light theme', () => {
      const result = getColorPool('game', false);
      expect(result).toEqual(gameLightColors);
    });

    it('should return mixed colors for default mode', () => {
      const result = getColorPool('mixed', false);
      expect(result).toEqual(lightModeColors);
    });

    it('should return dark colors for dark mode', () => {
      const result = getColorPool('mixed', true);
      expect(result).not.toEqual(lightModeColors);
    });
  });

  describe('assignColorsToEmotions', () => {
    const mockEmotions = [
      { id: '1', name: 'Happy', color: undefined },
      { id: '2', name: 'Sad', color: undefined },
      { id: '3', name: 'Angry', color: undefined }
    ];

    beforeEach(() => {
      mockWindow.getComputedStyle.mockReturnValue({
        background_color: '#ffffff'
      });
    });

    it('should assign colors to all emotions', () => {
      const result = assignColorsToEmotions(mockEmotions, false);
      
      expect(result).toHaveLength(3);
      result.forEach(emotion => {
        expect(emotion.color).toBeDefined();
        expect(emotion.color).not.toBe('');
      });
    });

    it('should preserve existing colors when valid', () => {
      const emotionsWithColors = [
        { id: '1', name: 'Happy', color: '#000000' }, // Use black which should have good contrast
        { id: '2', name: 'Sad', color: undefined }
      ];

      const result = assignColorsToEmotions(emotionsWithColors, false);

      expect(result[0].color).toBe('#000000');
      expect(result[1].color).toBeDefined();
      expect(result[1].color).not.toBe('#000000');
    });

    it('should filter out placeholder emotions', () => {
      const emotionsWithPlaceholder = [
        { id: '1', name: 'Happy', color: undefined },
        { id: 'placeholder_1', name: 'Placeholder', color: undefined }
      ];
      
      const result = assignColorsToEmotions(emotionsWithPlaceholder, false);
      
      expect(result).toHaveLength(2);
      expect(result.find(e => e.id === 'placeholder_1')?.color).toBe('transparent');
    });

    it('should return empty array for empty input', () => {
      const result = assignColorsToEmotions([], false);
      expect(result).toEqual([]);
    });

    it('should handle only placeholder emotions', () => {
      const placeholderEmotions = [
        { id: 'placeholder_1', name: 'Placeholder', color: undefined }
      ];

      const result = assignColorsToEmotions(placeholderEmotions, false);

      // The function returns empty array when there are no real emotions
      // but adds placeholders at the end, so we should expect 1 item
      expect(result).toHaveLength(1);
      expect(result[0].color).toBe('transparent');
    });

    it('should consider color mode in assignment', () => {
      const gameResult = assignColorsToEmotions(mockEmotions, false, 'primary', 'game');
      const mixedResult = assignColorsToEmotions(mockEmotions, false, 'primary', 'mixed');
      
      expect(gameResult).toHaveLength(3);
      expect(mixedResult).toHaveLength(3);
      
      // Colors should be different between modes
      const gameColors = gameResult.map(e => e.color);
      const mixedColors = mixedResult.map(e => e.color);
      expect(gameColors).not.toEqual(mixedColors);
    });

    it('should handle special Neutral and Happy emotion pair in game mode', () => {
      const specialEmotions = [
        { id: '1', name: 'Neutral', color: undefined },
        { id: '2', name: 'Happy', color: undefined },
        { id: '3', name: 'Sad', color: undefined }
      ];
      
      const result = assignColorsToEmotions(specialEmotions, false, 'primary', 'game');
      
      expect(result).toHaveLength(3);
      
      const neutralEmotion = result.find(e => e.name === 'Neutral');
      const happyEmotion = result.find(e => e.name === 'Happy');
      
      expect(neutralEmotion?.color).toBeDefined();
      expect(happyEmotion?.color).toBeDefined();
      
      // Should have good contrast between Neutral and Happy
      if (neutralEmotion && happyEmotion) {
        const contrast = calculateContrastRatio(neutralEmotion.color, happyEmotion.color);
        expect(contrast).toBeGreaterThan(1.5);
      }
    });
  });
});
