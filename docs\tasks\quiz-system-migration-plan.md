# Quiz系统迁移计划

## 概述

本文档详细说明了从旧Quiz系统架构迁移到新架构的计划，包括需要更新的文件、数据迁移策略和实施步骤。

## 🔍 发现的需要更新的文件

### 1. 服务层文件 (使用旧架构)

#### EmotionDataSetService.ts 和相关文件
- **位置**: `src/services/entities/EmotionDataSetService.ts`
- **问题**: 仍在使用`emotion_data_sets`表结构
- **影响**: 与新的Quiz系统不兼容
- **状态**: ⚠️ 需要保留 (用于向后兼容)

#### QuizEngine.ts
- **位置**: `src/services/quiz/QuizEngine.ts`
- **问题**: 使用旧字段如`emotion_data_set_id`, `tier_id`, `current_tier_index`
- **影响**: 核心Quiz逻辑不兼容新架构
- **状态**: 🔴 需要重写

### 2. 数据库Schema文件 (混合状态)

#### quiz_indexes.sql
- **位置**: `public/seeds/schema/quiz_indexes.sql`
- **问题**: 包含对旧字段的索引引用
- **影响**: 索引可能失效
- **状态**: 🟡 需要更新

### 3. 测试和工具文件

#### check-db.sql
- **位置**: `server/tests/check-db.sql`
- **问题**: 查询旧表结构
- **影响**: 测试失效
- **状态**: 🟡 需要更新

## 📋 迁移策略

### 阶段1: 保持兼容性 (已完成)
- ✅ 更新base.ts中的Schema定义
- ✅ 更新页面组件使用新hooks
- ✅ 创建新的服务架构

### 阶段2: 核心引擎重写 (当前阶段)
- 🔄 重写QuizEngine.ts以支持新架构
- 🔄 更新QuizEngineV2.ts
- 🔄 创建数据迁移工具

### 阶段3: 清理和优化
- ⏳ 更新测试文件
- ⏳ 清理旧的索引定义
- ⏳ 性能优化

### 阶段4: 完全迁移
- ⏳ 评估EmotionDataSet系统是否需要迁移
- ⏳ 统一数据模型
- ⏳ 移除旧代码

## 🛠️ 具体更新计划

### 1. QuizEngine.ts 重写

#### 需要更新的字段映射:
```typescript
// 旧字段 -> 新字段
emotion_data_set_id -> pack_id (通过quiz_packs表)
tier_id -> question_id
current_tier_index -> current_question_index
total_tiers -> total_questions
emotion_id -> selected_option_ids (JSON数组)
tier_level -> question_order
```

#### 需要更新的方法:
- `createPersonalizedSession()` - 使用新的session结构
- `getPersonalizedQuestionPresentation()` - 返回新的问题数据格式
- `submitEmotionAnswer()` - 使用新的答案格式

### 2. 数据库索引更新

#### 需要移除的索引:
```sql
-- 旧索引
idx_quiz_packs_emotion_data_set
idx_quiz_sessions_tier
idx_quiz_answers_emotion_tier
```

#### 需要添加的索引:
```sql
-- 新索引
idx_quiz_questions_pack_order
idx_quiz_answers_session_question
idx_quiz_session_presentation_configs_session
```

### 3. 测试文件更新

#### check-db.sql
- 更新查询以使用新表结构
- 添加新表的验证查询
- 移除对旧表的依赖

## 🔄 数据迁移策略

### 情绪数据集 -> Quiz包 迁移
```sql
-- 示例迁移脚本
INSERT INTO quiz_packs (
  id, name, description, category, quiz_type, 
  difficulty_level, estimated_duration_minutes,
  is_active, created_at, updated_at
)
SELECT 
  id, name, description, 'daily', 'emotion_wheel',
  3, 10, is_active, created_at, updated_at
FROM emotion_data_sets
WHERE is_active = 1;
```

### 层级 -> 问题 迁移
```sql
-- 示例迁移脚本
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_type,
  question_order, tier_level, is_active,
  created_at, updated_at
)
SELECT 
  t.id, t.emotion_data_set_id, 
  '请选择您的' || t.name || '情绪状态',
  'emotion_wheel', t.level, t.level, 1,
  t.created_at, t.updated_at
FROM emotion_data_set_tiers t
JOIN emotion_data_sets eds ON t.emotion_data_set_id = eds.id
WHERE eds.is_active = 1;
```

## ⚠️ 风险和注意事项

### 1. 数据完整性
- 确保迁移过程中不丢失现有数据
- 保持用户会话的连续性
- 验证所有关联关系

### 2. 向后兼容性
- EmotionDataSet系统可能仍被其他功能使用
- 需要评估是否完全迁移或保持双系统并存
- API兼容性考虑

### 3. 性能影响
- 新索引的创建可能影响性能
- 数据迁移期间的系统可用性
- 查询性能优化

## 📅 实施时间表

### 第1周: QuizEngine重写
- [ ] 分析现有QuizEngine功能
- [ ] 设计新的引擎架构
- [ ] 实现核心功能

### 第2周: 数据迁移工具
- [ ] 创建迁移脚本
- [ ] 测试数据完整性
- [ ] 性能优化

### 第3周: 测试和验证
- [ ] 更新测试文件
- [ ] 端到端测试
- [ ] 性能测试

### 第4周: 部署和清理
- [ ] 生产环境部署
- [ ] 监控和调优
- [ ] 文档更新

## 🎯 成功标准

1. **功能完整性**: 所有Quiz功能正常工作
2. **数据完整性**: 无数据丢失或损坏
3. **性能标准**: 响应时间不超过现有系统的120%
4. **兼容性**: 现有API保持兼容
5. **测试覆盖**: 所有核心功能有测试覆盖

## 📚 相关文档

- [Quiz系统架构更新总结](./quiz-system-update-summary.md)
- [数据库Schema文档](../public/seeds/README.md)
- [服务架构文档](../src/services/README.md)
- [类型系统文档](../src/types/README.md)
