/**
 * Quiz会话配置 Repository
 * 管理Quiz会话的最终配置快照
 */

import { BaseRepository } from '../base/BaseRepository';
import type { 
  QuizSessionConfig,
  CreateQuizSessionConfigInput 
} from '../../types/schema';

export class QuizSessionConfigRepository extends BaseRepository<
  QuizSessionConfig,
  CreateQuizSessionConfigInput,
  never // 不支持更新
> {
  protected tableName = 'quiz_session_configs';

  constructor(tableName?: string, db?: any) {
    super(tableName || 'quiz_session_configs', db);
  }

  /**
   * 获取会话配置
   */
  async getSessionConfig(sessionId: string): Promise<QuizSessionConfig | null> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE session_id = ?
        LIMIT 1
      `;
      
      const result = await db.query(query, [sessionId]);
      const config = result.values?.[0];
      
      return config ? this.mapRowToEntity(config) : null;
    } catch (error) {
      console.error('Error getting session config:', error);
      return null;
    }
  }

  /**
   * 获取用户的会话配置列表
   */
  async getUserSessionConfigs(userId: string, limit: number = 50): Promise<QuizSessionConfig[]> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ?
      `;
      
      const result = await db.query(query, [userId, limit]);
      return (result.values || []).map(row => this.mapRowToEntity(row));
    } catch (error) {
      console.error('Error getting user session configs:', error);
      return [];
    }
  }

  /**
   * 获取包的会话配置列表
   */
  async getPackSessionConfigs(packId: string, limit: number = 50): Promise<QuizSessionConfig[]> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE pack_id = ?
        ORDER BY created_at DESC
        LIMIT ?
      `;
      
      const result = await db.query(query, [packId, limit]);
      return (result.values || []).map(row => this.mapRowToEntity(row));
    } catch (error) {
      console.error('Error getting pack session configs:', error);
      return [];
    }
  }

  /**
   * 创建会话配置
   */
  async createSessionConfig(data: CreateQuizSessionConfigInput): Promise<QuizSessionConfig | null> {
    try {
      // 检查是否已存在该会话的配置
      const existingConfig = await this.getSessionConfig(data.session_id);
      if (existingConfig) {
        console.warn(`Session config already exists for session ${data.session_id}`);
        return existingConfig;
      }

      return await this.create(data);
    } catch (error) {
      console.error('Error creating session config:', error);
      return null;
    }
  }

  /**
   * 删除会话配置
   */
  async deleteSessionConfig(sessionId: string): Promise<boolean> {
    try {
      const db = this.getDb();
      const query = `DELETE FROM ${this.tableName} WHERE session_id = ?`;
      
      await db.query(query, [sessionId]);
      return true;
    } catch (error) {
      console.error('Error deleting session config:', error);
      return false;
    }
  }

  /**
   * 获取会话配置统计
   */
  async getSessionConfigStats(userId?: string): Promise<{
    total_sessions: number;
    by_config_version: Record<string, number>;
    by_personalization_level: Record<string, number>;
    avg_personalization_level: number;
  }> {
    try {
      const db = this.getDb();
      
      // 构建基础查询条件
      const whereClause = userId ? 'WHERE user_id = ?' : '';
      const params = userId ? [userId] : [];

      // 总数统计
      const totalQuery = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
      const totalResult = await db.query(totalQuery, params);
      const total = totalResult.values?.[0]?.total || 0;

      // 按配置版本统计
      const versionQuery = `
        SELECT config_version, COUNT(*) as count FROM ${this.tableName} 
        ${whereClause}
        GROUP BY config_version
      `;
      const versionResult = await db.query(versionQuery, params);
      const byVersion: Record<string, number> = {};
      (versionResult.values || []).forEach((row: any) => {
        byVersion[row.config_version] = row.count;
      });

      // 按个性化级别统计（分组）
      const levelQuery = `
        SELECT 
          CASE 
            WHEN personalization_level < 25 THEN 'low'
            WHEN personalization_level < 75 THEN 'medium'
            ELSE 'high'
          END as level_group,
          COUNT(*) as count
        FROM ${this.tableName} 
        ${whereClause}
        GROUP BY level_group
      `;
      const levelResult = await db.query(levelQuery, params);
      const byLevel: Record<string, number> = {};
      (levelResult.values || []).forEach((row: any) => {
        byLevel[row.level_group] = row.count;
      });

      // 平均个性化级别
      const avgQuery = `
        SELECT AVG(personalization_level) as avg_level FROM ${this.tableName} 
        ${whereClause}
      `;
      const avgResult = await db.query(avgQuery, params);
      const avgLevel = avgResult.values?.[0]?.avg_level || 0;

      return {
        total_sessions: total,
        by_config_version: byVersion,
        by_personalization_level: byLevel,
        avg_personalization_level: Math.round(avgLevel)
      };
    } catch (error) {
      console.error('Error getting session config stats:', error);
      return {
        total_sessions: 0,
        by_config_version: {},
        by_personalization_level: {},
        avg_personalization_level: 0
      };
    }
  }

  /**
   * 清理过期的会话配置
   */
  async cleanupExpiredConfigs(daysOld: number = 30): Promise<number> {
    try {
      const db = this.getDb();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const query = `
        DELETE FROM ${this.tableName}
        WHERE created_at < ?
      `;

      const result = await db.run(query, [cutoffDate.toISOString()]);
      return result.changes?.changes || 0;
    } catch (error) {
      console.error('Error cleaning up expired configs:', error);
      return 0;
    }
  }

  /**
   * 生成ID
   */
  protected generateId(): string {
    return `session_config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 提取创建数据中的ID
   */
  protected extractIdFromCreateData(data: CreateQuizSessionConfigInput): string {
    return data.id || this.generateId();
  }

  /**
   * 构建插入查询
   */
  protected buildInsertQuery(data: CreateQuizSessionConfigInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const id = data.id || this.generateId();

    const query = `
      INSERT INTO ${this.tableName} (
        id, session_id, user_id, pack_id, final_presentation_config,
        config_sources, personalization_level, config_version, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      id,
      data.session_id,
      data.user_id,
      data.pack_id,
      data.final_presentation_config,
      data.config_sources,
      data.personalization_level || 50,
      data.config_version || '2.0',
      now
    ];

    return { query, values };
  }

  /**
   * 构建更新查询（会话配置通常不更新，只读）
   */
  protected buildUpdateQuery(id: string, data: any): { query: string; values: any[] } {
    // 会话配置通常是只读的，不支持更新
    throw new Error('Session configs are read-only and cannot be updated');
  }

  /**
   * 映射数据库行到实体
   */
  protected mapRowToEntity(row: any): QuizSessionConfig {
    return {
      id: row.id,
      session_id: row.session_id,
      user_id: row.user_id,
      pack_id: row.pack_id,
      final_presentation_config: row.final_presentation_config,
      config_sources: row.config_sources,
      personalization_level: row.personalization_level,
      config_version: row.config_version,
      created_at: row.created_at
    };
  }

  /**
   * 映射实体到数据库行
   */
  protected mapEntityToRow(entity: QuizSessionConfig): any {
    return {
      id: entity.id,
      session_id: entity.session_id,
      user_id: entity.user_id,
      pack_id: entity.pack_id,
      final_presentation_config: entity.final_presentation_config,
      config_sources: entity.config_sources,
      personalization_level: entity.personalization_level,
      config_version: entity.config_version,
      created_at: entity.created_at
    };
  }

  /**
   * 验证创建数据
   */
  protected async validateCreate(data: CreateQuizSessionConfigInput): Promise<void> {
    if (!data.session_id) {
      throw new Error('Session ID is required');
    }
    if (!data.user_id) {
      throw new Error('User ID is required');
    }
    if (!data.pack_id) {
      throw new Error('Pack ID is required');
    }
    if (!data.final_presentation_config) {
      throw new Error('Final presentation config is required');
    }
    if (!data.config_sources) {
      throw new Error('Config sources are required');
    }
  }

  /**
   * 验证更新数据（不支持更新）
   */
  protected async validateUpdate(data: any): Promise<void> {
    throw new Error('Session configs cannot be updated');
  }
}
