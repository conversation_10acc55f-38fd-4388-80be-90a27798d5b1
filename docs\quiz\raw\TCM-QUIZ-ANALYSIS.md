# 中医体质问卷结构分析与转换方案

## 📊 原始数据结构分析

### 中医证素分类体系
```
脏腑证素 (5个):
├── 心 (4个问题)
├── 肝 (15个问题)
├── 脾 (5个问题)
├── 肺 (0个问题，在胃中体现)
├── 肾 (13个问题)
├── 胆 (3个问题)
├── 胃 (20个问题)
├── 肠 (3个问题)
├── 经络 (9个问题)
└── 神 (4个问题)

病理证素 (9个):
├── 气虚 (9个问题)
├── 血虚 (4个问题)
├── 阴虚 (13个问题)
├── 阳虚 (12个问题)
├── 气滞 (15个问题)
├── 血瘀 (9个问题)
├── 痰 (6个问题)
├── 湿 (6个问题)
├── 热 (17个问题)
└── 寒 (19个问题)
```

### 数据统计
- **总证素类型**: 19个独立的证素
- **总问题数量**: 191个问题
- **评分等级**: 4级 (无、轻、中、重)
- **权重系数**: 0, 0.7, 1.0, 1.5
- **分值范围**: -50 到 65分

### 特殊特征
1. **负分值**: 某些问题有负分值，表示反向指标
2. **性别限制**: 部分问题仅限男性或女性回答
3. **权重计算**: 根据症状程度应用不同权重系数
4. **综合评分**: 每个证素的得分为相关问题的加权求和

## 🎯 转换为Quiz量表结构

### 方案选择: 19个独立量表包

#### 核心理念
```
原始结构: 19个证素 × 各自的问题数量
转换结果: 19个独立的Quiz包
用户体验: 可以选择单个或多个证素进行评估
```

#### Quiz Pack 分类
```typescript
// 脏腑类量表包 (10个)
const organQuizPacks = [
  { id: "tcm_heart", name: "心证素评估", questions: 4 },
  { id: "tcm_liver", name: "肝证素评估", questions: 15 },
  { id: "tcm_spleen", name: "脾证素评估", questions: 5 },
  { id: "tcm_kidney", name: "肾证素评估", questions: 13 },
  { id: "tcm_gallbladder", name: "胆证素评估", questions: 3 },
  { id: "tcm_stomach", name: "胃证素评估", questions: 20 },
  { id: "tcm_intestine", name: "肠证素评估", questions: 3 },
  { id: "tcm_meridian", name: "经络证素评估", questions: 9 },
  { id: "tcm_spirit", name: "神证素评估", questions: 4 }
];

// 病理类量表包 (9个)
const pathologyQuizPacks = [
  { id: "tcm_qi_deficiency", name: "气虚证素评估", questions: 9 },
  { id: "tcm_blood_deficiency", name: "血虚证素评估", questions: 4 },
  { id: "tcm_yin_deficiency", name: "阴虚证素评估", questions: 13 },
  { id: "tcm_yang_deficiency", name: "阳虚证素评估", questions: 12 },
  { id: "tcm_qi_stagnation", name: "气滞证素评估", questions: 15 },
  { id: "tcm_blood_stasis", name: "血瘀证素评估", questions: 9 },
  { id: "tcm_phlegm", name: "痰证素评估", questions: 6 },
  { id: "tcm_dampness", name: "湿证素评估", questions: 6 },
  { id: "tcm_heat", name: "热证素评估", questions: 17 },
  { id: "tcm_cold", name: "寒证素评估", questions: 19 }
];
```

### 统一的问题结构设计

#### 标准化问题格式
```typescript
interface TCMQuestion {
  id: string;
  pack_id: string;
  question_text: string;
  question_text_localized: {
    "zh-CN": string;
    "en-US": string;
  };
  question_type: "likert_scale";
  order_index: number;
  is_required: boolean;
  metadata: {
    syndrome_element: string;
    base_score: number;
    gender_restriction?: "male" | "female";
    weight_coefficients: {
      none: 0;
      mild: 0.7;
      moderate: 1.0;
      severe: 1.5;
    };
  };
}
```

#### 标准化选项结构
```typescript
interface TCMQuestionOption {
  id: string;
  question_id: string;
  option_text: string;
  option_text_localized: {
    "zh-CN": string;
    "en-US": string;
  };
  option_value: "none" | "mild" | "moderate" | "severe";
  order_index: number;
  metadata: {
    weight_coefficient: number;
    score_multiplier: number;
    description: string;
  };
}
```

### 示例: 肝证素量表包

#### Quiz Pack 定义
```typescript
{
  id: "tcm_liver_syndrome",
  name: "Liver Syndrome Assessment",
  name_localized: {
    "zh-CN": "肝证素评估",
    "en-US": "Liver Syndrome Assessment"
  },
  description: "Traditional Chinese Medicine liver syndrome element assessment",
  description_localized: {
    "zh-CN": "中医肝证素评估，通过15个相关症状评估肝脏功能状态",
    "en-US": "TCM liver syndrome assessment through 15 related symptoms"
  },
  quiz_type: "tcm_assessment",
  quiz_style: "likert_scale",
  category: "tcm",
  difficulty_level: 3,
  estimated_duration_minutes: 8,
  tags: ["tcm", "liver", "syndrome", "assessment"],
  metadata: {
    syndrome_element: "liver",
    total_questions: 15,
    scoring_method: "weighted_sum",
    score_range: {
      min: 0,
      max: 735 // 15题 × 最高分49 × 1.5权重
    },
    assessment_levels: {
      normal: { min: 0, max: 100 },
      mild: { min: 101, max: 300 },
      moderate: { min: 301, max: 500 },
      severe: { min: 501, max: 735 }
    }
  }
}
```

#### 问题示例
```typescript
// 问题1: 情绪相关
{
  id: "tcm_liver_q001",
  pack_id: "tcm_liver_syndrome",
  question_text: "您容易急躁或发怒吗？",
  question_text_localized: {
    "zh-CN": "您容易急躁或发怒吗？",
    "en-US": "Do you easily become irritable or angry?"
  },
  question_type: "likert_scale",
  order_index: 1,
  is_required: true,
  metadata: {
    syndrome_element: "liver",
    base_score: 31,
    symptom_category: "emotional",
    weight_coefficients: {
      none: 0,
      mild: 0.7,
      moderate: 1.0,
      severe: 1.5
    }
  }
}

// 问题2: 生理症状
{
  id: "tcm_liver_q002",
  pack_id: "tcm_liver_syndrome",
  question_text: "您感到闷闷不乐，情绪低沉吗？",
  question_text_localized: {
    "zh-CN": "您感到闷闷不乐，情绪低沉吗？",
    "en-US": "Do you feel depressed or in low spirits?"
  },
  question_type: "likert_scale",
  order_index: 2,
  is_required: true,
  metadata: {
    syndrome_element: "liver",
    base_score: 40,
    symptom_category: "emotional",
    weight_coefficients: {
      none: 0,
      mild: 0.7,
      moderate: 1.0,
      severe: 1.5
    }
  }
}

// 性别限制问题示例
{
  id: "tcm_liver_q022",
  pack_id: "tcm_liver_syndrome",
  question_text: "您会出现每次月经相隔时间都不确定，或提前或延后，或长或短吗？",
  question_text_localized: {
    "zh-CN": "您会出现每次月经相隔时间都不确定，或提前或延后，或长或短吗？",
    "en-US": "Do you experience irregular menstrual cycles?"
  },
  question_type: "likert_scale",
  order_index: 22,
  is_required: false,
  condition_logic: {
    show_if: {
      user_profile: "gender",
      value: "female"
    }
  },
  metadata: {
    syndrome_element: "liver",
    base_score: 38,
    symptom_category: "reproductive",
    gender_restriction: "female",
    weight_coefficients: {
      none: 0,
      mild: 0.7,
      moderate: 1.0,
      severe: 1.5
    }
  }
}
```

#### 标准化选项 (所有TCM问题通用)
```typescript
const tcmStandardOptions = [
  {
    option_text: "无",
    option_text_localized: {
      "zh-CN": "无",
      "en-US": "None"
    },
    option_value: "none",
    order_index: 1,
    metadata: {
      weight_coefficient: 0,
      score_multiplier: 0,
      description: "无此症状"
    }
  },
  {
    option_text: "轻",
    option_text_localized: {
      "zh-CN": "轻",
      "en-US": "Mild"
    },
    option_value: "mild",
    order_index: 2,
    metadata: {
      weight_coefficient: 0.7,
      score_multiplier: 0.7,
      description: "症状轻微"
    }
  },
  {
    option_text: "中",
    option_text_localized: {
      "zh-CN": "中",
      "en-US": "Moderate"
    },
    option_value: "moderate",
    order_index: 3,
    metadata: {
      weight_coefficient: 1.0,
      score_multiplier: 1.0,
      description: "症状中等"
    }
  },
  {
    option_text: "重",
    option_text_localized: {
      "zh-CN": "重",
      "en-US": "Severe"
    },
    option_value: "severe",
    order_index: 4,
    metadata: {
      weight_coefficient: 1.5,
      score_multiplier: 1.5,
      description: "症状严重"
    }
  }
];
```

## 🔧 技术实现方案

### 评分计算逻辑
```typescript
interface TCMScoreCalculator {
  calculateSyndromeScore(answers: QuizAnswer[], questions: TCMQuestion[]): number;
  calculateWeightedScore(baseScore: number, severity: string): number;
  generateAssessmentReport(scores: Record<string, number>): TCMAssessmentReport;
}

// 实现示例
function calculateSyndromeScore(answers: QuizAnswer[], questions: TCMQuestion[]): number {
  let totalScore = 0;

  for (const answer of answers) {
    const question = questions.find(q => q.id === answer.question_id);
    if (!question) continue;

    const baseScore = question.metadata.base_score;
    const severity = answer.answer_value;
    const weightCoeff = question.metadata.weight_coefficients[severity];

    const weightedScore = baseScore * weightCoeff;
    totalScore += weightedScore;
  }

  return totalScore;
}
```

### 负分值处理
```typescript
// 处理负分值问题
function handleNegativeScores(baseScore: number, severity: string): number {
  const isNegative = baseScore < 0;
  const absScore = Math.abs(baseScore);
  const weightCoeff = getWeightCoefficient(severity);

  const weightedScore = absScore * weightCoeff;
  return isNegative ? -weightedScore : weightedScore;
}
```

### 性别限制逻辑
```typescript
function filterQuestionsByGender(
  questions: TCMQuestion[],
  userGender: "male" | "female"
): TCMQuestion[] {
  return questions.filter(question => {
    const genderRestriction = question.metadata.gender_restriction;
    if (!genderRestriction) return true;
    return genderRestriction === userGender;
  });
}
```

## 🎨 用户体验设计

### 量表包选择界面
```
中医体质评估
├── 脏腑证素评估
│   ├── 🫀 心证素 (4题, 3分钟)
│   ├── 🫁 肝证素 (15题, 8分钟)
│   ├── 🫘 脾证素 (5题, 3分钟)
│   └── ... 其他脏腑
├── 病理证素评估
│   ├── ⚡ 气虚证素 (9题, 5分钟)
│   ├── 🩸 血虚证素 (4题, 3分钟)
│   ├── ❄️ 阴虚证素 (13题, 7分钟)
│   └── ... 其他病理
└── 综合评估 (选择多个证素)
```

### 问题展示界面
```
肝证素评估 - 问题 1/15

您容易急躁或发怒吗？

○ 无     ○ 轻     ○ 中     ○ 重
  0分    21.7分   31分    46.5分

进度: ████████░░ 53%
```

### 结果报告界面
```
中医体质评估报告

肝证素得分: 245分 (中等程度)
├── 情绪症状: 较明显
├── 生理症状: 轻微
└── 建议: 疏肝理气，调节情志

综合体质类型: 肝郁气滞型
调理建议: ...
```

## 🚀 实施计划与质量保证

### 阶段1: 数据结构准备 (1周)
- [ ] 创建19个证素量表包
- [ ] 导入191个问题和764个选项 (每题4个选项)
- [ ] 设置性别限制条件逻辑
- [ ] 配置负分值处理机制

### 阶段2: 评分系统实现 (1-2周)
```typescript
class TCMScoreCalculator {
  calculateSyndromeScore(answers: QuizAnswer[], questions: TCMQuestion[]): number {
    let totalScore = 0;

    for (const answer of answers) {
      const question = questions.find(q => q.id === answer.question_id);
      if (!question) continue;

      const baseScore = question.metadata.base_score;
      const severity = answer.answer_value;
      const weightCoeff = this.getWeightCoefficient(severity);

      // 处理负分值
      const isNegative = baseScore < 0;
      const absScore = Math.abs(baseScore);
      const weightedScore = absScore * weightCoeff;

      totalScore += isNegative ? -weightedScore : weightedScore;
    }

    return Math.round(totalScore * 100) / 100;
  }
}
```

### 质量保证检查点
- [ ] 所有191个问题正确导入
- [ ] 权重系数计算准确 (0, 0.7, 1.0, 1.5)
- [ ] 负分值处理正确
- [ ] 性别限制逻辑有效
- [ ] 中医专家内容审核

这种转换方案将复杂的中医证素评估系统转换为19个独立但相关的量表包，既保持了中医理论的完整性，又提供了灵活的评估选择和精确的计分系统。
