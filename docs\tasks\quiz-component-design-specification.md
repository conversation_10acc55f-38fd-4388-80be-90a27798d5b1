# Quiz系统基础组件设计规范

## 🎯 设计原则

基于Apple游戏设计原则和iOS移动应用设计指南，结合ViewFactory架构，为Quiz系统的**基础UI组件**制定专业的设计规范。

### 核心设计理念

- **组件化架构**: 构建可复用、可配置的基础UI组件
- **配置驱动**: 所有组件通过后端配置动态生成和定制
- **多样性支持**: 每个组件提供1-2套不同布局或风格选择
- **中医文化融合**: 深度融入中医文化元素的视觉设计
- **响应式设计**: 支持PC、平板、手机等不同屏幕尺寸

### 组件层次结构

```
基础UI组件 (本文档重点)
├── 文本组件 (TextComponent)
├── 按钮组件 (ButtonComponent)
├── 选择器组件 (SelectorComponent)
├── 滑块组件 (SliderComponent)
├── 评分组件 (RatingComponent)
├── 图片选择器组件 (ImageSelectorComponent)
├── 进度指示器组件 (ProgressIndicatorComponent)
└── 媒体组件 (MediaComponent)

特殊视图 (基于基础组件构建)
├── wheel (情绪轮盘视图)
├── card (卡片视图)
├── bubble (气泡视图)
└── galaxy (星系视图)
```

## 📱 移动端优先设计

### 1. 安全区域和布局

```typescript
interface QuizLayoutConstraints {
  // 基于iOS安全区域的布局约束
  safe_area: {
    top: number;      // 状态栏/刘海区域
    bottom: number;   // 底部手势区域 (至少44pt)
    left: number;     // 左侧安全边距
    right: number;    // 右侧安全边距
  };

  // 标准间距系统 (基于4pt网格)
  spacing: {
    xs: 4;    // 紧密间距
    sm: 8;    // 小间距
    md: 16;   // 标准间距
    lg: 24;   // 宽松间距
    xl: 32;   // 分隔间距
  };

  // 触控目标最小尺寸
  touch_target_min: {
    width: 44;   // 最小触控宽度 (pt)
    height: 44;  // 最小触控高度 (pt)
  };
}
```

### 2. 响应式断点

```typescript
interface QuizResponsiveBreakpoints {
  mobile: {
    portrait: { max_width: 428, orientation: 'portrait' };
    landscape: { max_width: 926, orientation: 'landscape' };
  };
  tablet: {
    portrait: { max_width: 834, orientation: 'portrait' };
    landscape: { max_width: 1194, orientation: 'landscape' };
  };
}
```

## 🎨 Quiz专用组件设计

### 1. 情绪轮盘组件 (EmotionWheel)

#### 设计规范

```typescript
interface EmotionWheelDesignSpec {
  // 基础尺寸 (基于容器的百分比)
  dimensions: {
    container_ratio: 0.85;        // 容器宽度的85%
    wheel_radius_ratio: 0.4;      // 容器的40%作为轮盘半径
    inner_radius_ratio: 0.15;     // 内圈半径比例
    sector_gap: 2;                // 扇形间隙 (pt)
  };

  // 触控优化
  touch_optimization: {
    min_sector_size: 44;          // 最小扇形触控区域
    touch_expansion: 8;           // 触控区域扩展
    haptic_feedback: true;        // 启用触觉反馈
  };

  // 视觉层次
  visual_hierarchy: {
    tier_1: { opacity: 1.0, scale: 1.0 };      // 主要情绪
    tier_2: { opacity: 0.85, scale: 0.9 };     // 次要情绪
    tier_3: { opacity: 0.7, scale: 0.8 };      // 细分情绪
  };

  // 动画规范
  animations: {
    sector_hover: {
      duration: 200;              // 悬停动画时长 (ms)
      scale: 1.05;               // 悬停缩放比例
      easing: 'ease-out';        // 缓动函数
    };
    tier_transition: {
      duration: 300;             // 层级切换时长
      type: 'spring';            // 弹性动画
      damping: 0.8;             // 阻尼系数
    };
    selection_feedback: {
      duration: 150;             // 选择反馈时长
      ripple_effect: true;       // 涟漪效果
      color_pulse: true;         // 颜色脉冲
    };
  };
}
```

#### 布局变体

```typescript
interface EmotionWheelLayouts {
  // 布局1: 经典圆形轮盘
  classic_circular: {
    layout_id: 'classic_circular';
    shape: 'circle';
    sector_arrangement: 'radial';
    center_element: 'logo' | 'progress' | 'empty';
  };

  // 布局2: 花瓣式布局
  petal_style: {
    layout_id: 'petal_style';
    shape: 'flower';
    sector_arrangement: 'organic';
    center_element: 'character' | 'mandala';
  };

  // 布局3: 分层环形
  layered_rings: {
    layout_id: 'layered_rings';
    shape: 'concentric_rings';
    sector_arrangement: 'hierarchical';
    center_element: 'navigation_hub';
  };
}
```

### 2. 情绪卡片组件 (EmotionCards)

#### 设计规范

```typescript
interface EmotionCardsDesignSpec {
  // 卡片尺寸系统
  card_sizes: {
    compact: { width: 120, height: 80, padding: 12 };
    standard: { width: 160, height: 120, padding: 16 };
    expanded: { width: 200, height: 160, padding: 20 };
  };

  // 网格布局
  grid_system: {
    mobile_portrait: { columns: 2, gap: 12 };
    mobile_landscape: { columns: 3, gap: 16 };
    tablet_portrait: { columns: 3, gap: 16 };
    tablet_landscape: { columns: 4, gap: 20 };
  };

  // 卡片状态
  card_states: {
    default: {
      elevation: 2;              // 阴影深度
      border_radius: 12;         // 圆角半径
      opacity: 1.0;
    };
    hover: {
      elevation: 4;
      scale: 1.02;
      transition_duration: 200;
    };
    selected: {
      elevation: 6;
      border_width: 2;
      border_color: 'primary';
      glow_effect: true;
    };
    disabled: {
      opacity: 0.5;
      grayscale: true;
    };
  };
}
```

#### 内容布局模式

```typescript
interface EmotionCardContentLayouts {
  // 模式1: 图标+文字
  icon_text: {
    layout_id: 'icon_text';
    structure: {
      icon: { position: 'top', size: 32, margin_bottom: 8 };
      text: { position: 'bottom', alignment: 'center' };
    };
  };

  // 模式2: 背景图+覆盖文字
  background_overlay: {
    layout_id: 'background_overlay';
    structure: {
      background: { type: 'image', overlay_opacity: 0.3 };
      text: { position: 'overlay', color: 'white', shadow: true };
    };
  };

  // 模式3: 分割布局
  split_layout: {
    layout_id: 'split_layout';
    structure: {
      visual: { position: 'left', width_ratio: 0.4 };
      content: { position: 'right', width_ratio: 0.6 };
    };
  };
}
```

### 3. 情绪气泡组件 (EmotionBubbles)

#### 设计规范

```typescript
interface EmotionBubblesDesignSpec {
  // 气泡物理属性
  bubble_physics: {
    size_range: { min: 60, max: 120 };        // 气泡大小范围
    density_factor: 0.8;                      // 密度因子
    collision_detection: true;                // 碰撞检测
    boundary_behavior: 'bounce' | 'wrap';     // 边界行为
  };

  // 动画系统
  animation_system: {
    floating: {
      amplitude: 5;                           // 浮动幅度
      frequency: 0.5;                         // 浮动频率
      phase_offset: 'random';                 // 相位偏移
    };
    interaction: {
      on_touch: 'ripple';                     // 触摸效果
      on_hover: 'glow';                       // 悬停效果
      on_select: 'burst';                     // 选择效果
    };
  };

  // 视觉效果
  visual_effects: {
    transparency: { base: 0.85, hover: 0.95 };
    blur_background: true;                    // 背景模糊
    particle_trail: false;                    // 粒子轨迹
    color_gradient: true;                     // 渐变色彩
  };
}
```

### 4. 情绪星系组件 (EmotionGalaxy)

#### 设计规范 (VIP功能)

```typescript
interface EmotionGalaxyDesignSpec {
  // 3D空间配置
  space_configuration: {
    camera: {
      fov: 75;                                // 视野角度
      near: 0.1;                              // 近裁剪面
      far: 1000;                              // 远裁剪面
      initial_position: [0, 0, 5];           // 初始位置
    };

    lighting: {
      ambient: { intensity: 0.4, color: '#ffffff' };
      directional: { intensity: 0.6, position: [1, 1, 1] };
      point_lights: true;                     // 点光源
    };
  };

  // 星体系统
  celestial_system: {
    emotion_planets: {
      size_mapping: 'intensity';             // 大小映射规则
      orbit_radius: 'category_distance';     // 轨道半径
      rotation_speed: 'user_frequency';      // 旋转速度
    };

    connection_lines: {
      show_relationships: true;              // 显示关联线
      line_opacity: 0.3;                    // 线条透明度
      animated_flow: true;                   // 动画流动效果
    };
  };

  // 交互控制
  interaction_controls: {
    orbit_controls: true;                    // 轨道控制
    zoom_limits: { min: 2, max: 20 };       // 缩放限制
    auto_rotate: false;                      // 自动旋转
    touch_sensitivity: 1.2;                 // 触摸灵敏度
  };
}
```

## 🎮 游戏化交互设计

### 1. 反馈系统

```typescript
interface QuizFeedbackSystem {
  // 视觉反馈
  visual_feedback: {
    selection_highlight: {
      type: 'glow' | 'border' | 'scale' | 'color_shift';
      duration: 300;
      intensity: 'subtle' | 'moderate' | 'strong';
    };

    progress_indication: {
      type: 'progress_bar' | 'step_indicator' | 'completion_ring';
      animation: 'smooth' | 'stepped' | 'elastic';
      color_scheme: 'primary' | 'success' | 'custom';
    };

    state_transitions: {
      enter_animation: 'fade_in' | 'slide_in' | 'scale_in';
      exit_animation: 'fade_out' | 'slide_out' | 'scale_out';
      duration: 400;
    };
  };

  // 触觉反馈
  haptic_feedback: {
    selection: 'light_impact';               // 选择反馈
    navigation: 'selection';                 // 导航反馈
    completion: 'notification_success';      // 完成反馈
    error: 'notification_error';             // 错误反馈
  };

  // 音频反馈
  audio_feedback: {
    selection_sound: 'soft_click';           // 选择音效
    transition_sound: 'whoosh';              // 过渡音效
    completion_sound: 'success_chime';       // 完成音效
    ambient_background: 'nature_sounds';     // 环境音
  };
}
```

### 2. 动画时序

```typescript
interface QuizAnimationTiming {
  // 基础时序 (基于Apple推荐)
  base_timings: {
    micro_interaction: 150;                  // 微交互 (按钮点击等)
    ui_transition: 300;                      // UI过渡
    page_transition: 400;                    // 页面切换
    complex_animation: 600;                  // 复杂动画
  };

  // 缓动函数
  easing_functions: {
    standard: 'cubic-bezier(0.4, 0.0, 0.2, 1)';      // 标准缓动
    decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)';    // 减速
    accelerate: 'cubic-bezier(0.4, 0.0, 1, 1)';      // 加速
    spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'; // 弹性
  };

  // 动画优先级
  animation_priority: {
    critical: 'user_interaction';            // 用户交互优先
    important: 'feedback_animation';         // 反馈动画
    optional: 'decorative_animation';        // 装饰动画
  };
}
```

## 🎨 主题和样式系统

### 1. 色彩系统

```typescript
interface QuizColorSystem {
  // 基础色彩 (支持深色模式)
  base_colors: {
    primary: { light: '#007AFF', dark: '#0A84FF' };
    secondary: { light: '#5856D6', dark: '#5E5CE6' };
    success: { light: '#34C759', dark: '#30D158' };
    warning: { light: '#FF9500', dark: '#FF9F0A' };
    error: { light: '#FF3B30', dark: '#FF453A' };
  };

  // 情绪色彩映射
  emotion_colors: {
    joy: '#FFD60A';          // 喜悦 - 金黄色
    sadness: '#007AFF';      // 悲伤 - 蓝色
    anger: '#FF3B30';        // 愤怒 - 红色
    fear: '#5856D6';         // 恐惧 - 紫色
    surprise: '#FF9500';     // 惊讶 - 橙色
    disgust: '#34C759';      // 厌恶 - 绿色
    neutral: '#8E8E93';      // 中性 - 灰色
  };

  // 语义色彩
  semantic_colors: {
    background: { light: '#FFFFFF', dark: '#000000' };
    surface: { light: '#F2F2F7', dark: '#1C1C1E' };
    text_primary: { light: '#000000', dark: '#FFFFFF' };
    text_secondary: { light: '#3C3C43', dark: '#EBEBF5' };
    border: { light: '#C6C6C8', dark: '#38383A' };
  };
}
```

### 2. 字体系统

```typescript
interface QuizTypographySystem {
  // 字体族
  font_families: {
    primary: 'SF Pro Display';               // 主要字体
    secondary: 'SF Pro Text';                // 次要字体
    monospace: 'SF Mono';                    // 等宽字体
    custom: 'PingFang SC';                   // 中文字体
  };

  // 字体尺寸 (支持动态字体)
  font_sizes: {
    xs: { size: 12, line_height: 16 };
    sm: { size: 14, line_height: 20 };
    base: { size: 17, line_height: 22 };     // iOS标准正文
    lg: { size: 20, line_height: 25 };
    xl: { size: 24, line_height: 32 };
    xxl: { size: 32, line_height: 40 };
  };

  // 字重
  font_weights: {
    light: 300;
    regular: 400;
    medium: 500;
    semibold: 600;
    bold: 700;
  };
}
```

## 📐 组件规格标准

### 1. 组件尺寸规范

```typescript
interface QuizComponentSizes {
  // 按钮尺寸
  buttons: {
    small: { height: 32, padding: '8px 12px', font_size: 14 };
    medium: { height: 44, padding: '12px 16px', font_size: 17 };
    large: { height: 56, padding: '16px 24px', font_size: 20 };
  };

  // 输入框尺寸
  inputs: {
    compact: { height: 36, padding: '8px 12px' };
    standard: { height: 44, padding: '12px 16px' };
    comfortable: { height: 52, padding: '16px 20px' };
  };

  // 卡片尺寸
  cards: {
    small: { min_height: 120, padding: 16 };
    medium: { min_height: 160, padding: 20 };
    large: { min_height: 200, padding: 24 };
  };
}
```

### 2. 间距系统

```typescript
interface QuizSpacingSystem {
  // 基于4pt网格的间距系统
  spacing_scale: {
    0: 0;
    1: 4;     // xs
    2: 8;     // sm
    3: 12;    //
    4: 16;    // md (标准)
    5: 20;    //
    6: 24;    // lg
    8: 32;    // xl
    10: 40;   //
    12: 48;   // xxl
    16: 64;   // xxxl
  };

  // 组件内间距
  component_padding: {
    tight: 8;
    normal: 16;
    loose: 24;
    extra_loose: 32;
  };

  // 组件间间距
  component_margin: {
    tight: 12;
    normal: 16;
    loose: 24;
    section: 32;
  };
}
```

这个设计规范为Quiz系统提供了完整的组件设计标准，确保在不同设备和场景下都能提供一致、优秀的用户体验。
