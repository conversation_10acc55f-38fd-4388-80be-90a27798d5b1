/**
 * 清理废弃的组件和目录
 *
 * 此脚本用于移除已经被新视图系统替代的旧组件和目录。
 * 在运行此脚本之前，请确保已经完成了视图系统的重组工作。
 */

import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// 获取当前模块的目录名
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 要移除的目录
const directoriesToRemove = [
  path.resolve(__dirname, '../src/components/mood'),
  path.resolve(__dirname, '../src/components/wheels'),
  path.resolve(__dirname, '../src/components/display'),
  path.resolve(__dirname, '../src/components/galaxy'),
  path.resolve(__dirname, '../src/views/wheels'),
  path.resolve(__dirname, '../src/views/cards'),
  path.resolve(__dirname, '../src/views/bubbles'),
  path.resolve(__dirname, '../src/views/galaxy'),
];

// 要移除的文件
const filesToRemove = [
  path.resolve(__dirname, '../src/utils/skinSystem.ts'),
  path.resolve(__dirname, '../src/utils/displaySystem.ts'),
  path.resolve(__dirname, '../src/views/BaseEmotionView.tsx'),
];

/**
 * 递归删除目录
 * @param {string} dirPath 目录路径
 */
function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    console.log(`正在删除目录: ${dirPath}`);

    try {
      // 读取目录内容
      const files = fs.readdirSync(dirPath);

      // 遍历目录内容
      for (const file of files) {
        const filePath = path.join(dirPath, file);

        // 检查是否为目录
        if (fs.statSync(filePath).isDirectory()) {
          // 递归删除子目录
          removeDirectory(filePath);
        } else {
          // 删除文件
          fs.unlinkSync(filePath);
          console.log(`  已删除文件: ${filePath}`);
        }
      }

      // 删除空目录
      fs.rmdirSync(dirPath);
      console.log(`  已删除目录: ${dirPath}`);
    } catch (error) {
      console.error(`删除目录 ${dirPath} 时出错:`, error);
    }
  } else {
    console.log(`目录不存在: ${dirPath}`);
  }
}

/**
 * 删除文件
 * @param {string} filePath 文件路径
 */
function removeFile(filePath) {
  if (fs.existsSync(filePath)) {
    console.log(`正在删除文件: ${filePath}`);

    try {
      fs.unlinkSync(filePath);
      console.log(`  已删除文件: ${filePath}`);
    } catch (error) {
      console.error(`删除文件 ${filePath} 时出错:`, error);
    }
  } else {
    console.log(`文件不存在: ${filePath}`);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('开始清理废弃的组件和目录...');

  // 删除目录
  for (const dirPath of directoriesToRemove) {
    removeDirectory(dirPath);
  }

  // 删除文件
  for (const filePath of filesToRemove) {
    removeFile(filePath);
  }

  console.log('清理完成！');
}

// 执行主函数
main();
