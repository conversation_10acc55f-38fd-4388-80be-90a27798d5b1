# 多语言实现文档

## 目录

1. [概述](#概述)
2. [多语言架构](#多语言架构)
3. [多语言数据源](#多语言数据源)
4. [多语言上下文](#多语言上下文)
5. [多语言使用方式](#多语言使用方式)
6. [常见问题与解决方案](#常见问题与解决方案)
7. [最佳实践](#最佳实践)
8. [案例分析](#案例分析)

## 概述

本项目实现了完整的多语言支持，目前支持英文(en)和中文(zh)两种语言。多语言系统采用了React上下文(Context)和SQLite数据库相结合的方式，确保应用程序的所有文本内容都能够根据用户选择的语言进行动态切换。

## 多语言架构

多语言系统的核心架构包括以下几个部分：

1. **LanguageContext**：提供语言上下文，管理当前语言状态和翻译函数
2. **数据库存储**：使用SQLite数据库存储UI标签和翻译
3. **JSON文件备份**：使用JSON文件作为备份翻译源
4. **翻译函数**：提供`t`函数用于获取翻译文本

## 多语言数据源

### 1. 数据库存储

多语言数据主要存储在SQLite数据库的以下表中：

- **ui_labels**：存储所有UI标签的键和默认文本
  ```sql
  CREATE TABLE ui_labels (
    key TEXT PRIMARY KEY,
    default_text TEXT NOT NULL
  );
  ```

- **ui_label_translations**：存储不同语言的翻译
  ```sql
  CREATE TABLE ui_label_translations (
    label_key TEXT,
    language_code TEXT,
    translated_text TEXT NOT NULL,
    PRIMARY KEY (label_key, language_code),
    FOREIGN KEY (label_key) REFERENCES ui_labels(key)
  );
  ```

- **emotion_translations**：存储情绪名称的翻译
  ```sql
  CREATE TABLE emotion_translations (
    emotion_id INTEGER,
    language_code TEXT,
    translated_name TEXT NOT NULL,
    PRIMARY KEY (emotion_id, language_code),
    FOREIGN KEY (emotion_id) REFERENCES emotions(id)
  );
  ```

- **tag_translations**：存储标签名称的翻译
  ```sql
  CREATE TABLE tag_translations (
    tag_id INTEGER,
    language_code TEXT,
    translated_name TEXT NOT NULL,
    PRIMARY KEY (tag_id, language_code),
    FOREIGN KEY (tag_id) REFERENCES tags(id)
  );
  ```

### 2. JSON文件备份

作为备份和初始加载方案，项目还使用了JSON文件存储翻译：

- **src/locales/en.json**：英文翻译
- **src/locales/zh.json**：中文翻译

这些文件在数据库未加载或加载失败时提供翻译支持。

## 多语言上下文

多语言上下文由`LanguageContext.tsx`文件定义，主要包括：

```typescript
// 语言类型定义
export type Language = 'en' | 'zh';

// 语言上下文接口
interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, params?: Record<string, string>) => string;
  uiLabels: Record<string, string>;
  labelsLoading: boolean;
  isLanguageReady: boolean;
}

// 默认语言
const defaultLanguage: Language = 'en';

// 创建上下文
export const LanguageContext = createContext<LanguageContextType>({
  language: defaultLanguage,
  setLanguage: () => {},
  t: (key) => key,
  uiLabels: {},
  labelsLoading: true,
  isLanguageReady: false,
});
```

## 多语言使用方式

### 基本用法

在组件中使用多语言：

```jsx
import { useLanguage } from '@/contexts/LanguageContext';

const MyComponent = () => {
  const { t, language, isLanguageReady } = useLanguage();
  
  return (
    <div>
      {/* 基本翻译 */}
      <h1>{isLanguageReady ? t('app.title') : 'Mindful Mood'}</h1>
      
      {/* 带参数的翻译 */}
      <p>{isLanguageReady ? t('greeting', { name: 'User' }) : 'Hello, User'}</p>
      
      {/* 当前语言 */}
      <p>Current language: {language}</p>
    </div>
  );
};
```

### 切换语言

```jsx
import { useLanguage } from '@/contexts/LanguageContext';

const LanguageSwitcher = () => {
  const { language, setLanguage, isLanguageReady } = useLanguage();
  
  return (
    <div>
      <button onClick={() => setLanguage('en')}>
        {isLanguageReady ? t('settings.language.english') : 'English'}
      </button>
      <button onClick={() => setLanguage('zh')}>
        {isLanguageReady ? t('settings.language.chinese') : '中文'}
      </button>
    </div>
  );
};
```

### 在数据获取中使用多语言

```jsx
import { useLanguage } from '@/contexts/LanguageContext';
import { useSQLiteDB } from '@/lib/useSqLite';

const DataComponent = () => {
  const { language, isLanguageReady } = useLanguage();
  const { dbConnection } = useSQLiteDB();
  const [data, setData] = useState([]);
  
  useEffect(() => {
    const fetchData = async () => {
      if (!isLanguageReady || !dbConnection.current) return;
      
      // 使用当前语言获取数据
      const result = await dbConnection.current.query(`
        SELECT e.id, et.translated_name
        FROM emotions e
        JOIN emotion_translations et ON e.id = et.emotion_id
        WHERE et.language_code = ?
      `, [language]);
      
      setData(result.values || []);
    };
    
    fetchData();
  }, [language, isLanguageReady, dbConnection]);
  
  return (
    // 渲染数据
  );
};
```

## 常见问题与解决方案

### 1. 翻译未加载或未显示

**问题**：组件渲染时翻译未加载完成，导致显示键名而非翻译文本。

**解决方案**：
- 检查`isLanguageReady`状态，只有在语言准备好时才使用翻译
- 提供默认文本作为备份
```jsx
{isLanguageReady ? t('app.title') : 'Mindful Mood'}
```

### 2. 数据库翻译与JSON翻译不一致

**问题**：数据库中的翻译与JSON文件中的翻译不一致，导致界面显示混乱。

**解决方案**：
- 确保数据库和JSON文件中的翻译保持同步
- 使用脚本定期从数据库导出翻译到JSON文件
- 在开发环境中添加翻译一致性检查

### 3. 语言切换后界面未更新

**问题**：切换语言后，某些组件未正确更新显示新语言的文本。

**解决方案**：
- 确保所有使用翻译的组件都依赖于`language`状态
- 在关键组件中添加对`language`和`isLanguageReady`的依赖
```jsx
useEffect(() => {
  // 语言变化时的处理逻辑
  console.log(`Language changed to: ${language}, isReady: ${isLanguageReady}`);
}, [language, isLanguageReady]);
```

## 最佳实践

1. **始终检查语言准备状态**：
   ```jsx
   {isLanguageReady ? t('key') : 'Default text'}
   ```

2. **提供合理的默认值**：
   ```jsx
   const text = isLanguageReady ? t('key') : 'Default text';
   ```

3. **在数据获取中考虑语言因素**：
   ```jsx
   useEffect(() => {
     if (!isLanguageReady) return;
     // 获取数据...
   }, [language, isLanguageReady]);
   ```

4. **使用参数化翻译**：
   ```jsx
   t('welcome', { name: userName })
   // 翻译文本: "Welcome, {{name}}!"
   ```

5. **保持翻译键的一致性**：
   - 使用点表示法组织键名：`section.subsection.key`
   - 例如：`app.title`, `settings.language`, `analytics.no_data`

6. **避免硬编码文本**：
   - 不要在代码中直接使用硬编码的文本
   - 始终使用翻译函数，即使只支持一种语言

## 案例分析

### Analytics页面多语言实现

Analytics页面(`http://localhost:4080/analytics`)是多语言实现的良好示例：

1. **正确使用翻译函数**：
   ```jsx
   <h2 className="text-xl font-semibold mb-4">{t('analytics.title')}</h2>
   ```

2. **数据获取与多语言集成**：
   ```jsx
   // useLocalAnalyticsData.ts
   const { language, isLanguageReady } = useLanguage();
   
   // 只有在语言准备好时才获取数据
   useEffect(() => {
     if (isDatabaseInitialised && dbConnection.current && isLanguageReady) {
       fetchAnalytics(currentPeriod);
     }
   }, [isDatabaseInitialised, dbConnection, language, fetchAnalytics, isLanguageReady]);
   ```

3. **SQL查询中使用语言参数**：
   ```jsx
   // analyticsService.ts
   const emotionDataQuery = `
     SELECT 
       et.translated_name as name,
       COUNT(*) as value,
       e.color
     FROM mood_entries me
     JOIN emotions e ON me.primary_emotion = e.id
     JOIN emotion_translations et ON e.id = et.emotion_id AND et.language_code = ?
     WHERE me.user_id = ?
     GROUP BY et.translated_name, e.color
     ORDER BY value DESC;
   `;
   const emotionDataResult = await db.query(emotionDataQuery, [language, userId]);
   ```

通过这些实践，Analytics页面能够正确地显示多语言内容，并在语言切换时无缝更新。
