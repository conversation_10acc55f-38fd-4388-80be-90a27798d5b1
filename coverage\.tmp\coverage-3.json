{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/compatibility/compatibility.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 82132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 82132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 522, "endOffset": 34298, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 565, "endOffset": 626, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 8241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 733, "endOffset": 3803, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2612, "endOffset": 2827, "count": 2}, {"startOffset": 2800, "endOffset": 2808, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2707, "endOffset": 2730, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2947, "endOffset": 2972, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3506, "endOffset": 3791, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3854, "endOffset": 5930, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5641, "endOffset": 5670, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5727, "endOffset": 5752, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5989, "endOffset": 8233, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7262, "endOffset": 7373, "count": 7}, {"startOffset": 7346, "endOffset": 7354, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7454, "endOffset": 7574, "count": 7}, {"startOffset": 7537, "endOffset": 7555, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7733, "endOffset": 8049, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8293, "endOffset": 17378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8348, "endOffset": 10912, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9792, "endOffset": 10055, "count": 6}, {"startOffset": 9840, "endOffset": 9901, "count": 3}, {"startOffset": 9901, "endOffset": 9930, "count": 0}, {"startOffset": 9930, "endOffset": 9969, "count": 3}, {"startOffset": 9969, "endOffset": 9992, "count": 2}, {"startOffset": 9992, "endOffset": 10054, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10244, "endOffset": 10715, "count": 6}, {"startOffset": 10461, "endOffset": 10701, "count": 3}, {"startOffset": 10604, "endOffset": 10701, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10964, "endOffset": 14789, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12621, "endOffset": 13921, "count": 7}, {"startOffset": 13897, "endOffset": 13902, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14013, "endOffset": 14451, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14839, "endOffset": 17370, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15661, "endOffset": 16618, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16722, "endOffset": 17358, "count": 3}, {"startOffset": 17142, "endOffset": 17344, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17432, "endOffset": 24256, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17487, "endOffset": 21219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18724, "endOffset": 19947, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20280, "endOffset": 20911, "count": 5}, {"startOffset": 20701, "endOffset": 20897, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21272, "endOffset": 24248, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22301, "endOffset": 22695, "count": 5}, {"startOffset": 22491, "endOffset": 22497, "count": 0}, {"startOffset": 22563, "endOffset": 22570, "count": 0}, {"startOffset": 22635, "endOffset": 22643, "count": 2}, {"startOffset": 22644, "endOffset": 22654, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22383, "endOffset": 22405, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23404, "endOffset": 23877, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24311, "endOffset": 30075, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24365, "endOffset": 27403, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26066, "endOffset": 26691, "count": 3}, {"startOffset": 26268, "endOffset": 26307, "count": 1}, {"startOffset": 26443, "endOffset": 26448, "count": 0}, {"startOffset": 26646, "endOffset": 26650, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26168, "endOffset": 26190, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27454, "endOffset": 30067, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28366, "endOffset": 29275, "count": 5}, {"startOffset": 29251, "endOffset": 29256, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29385, "endOffset": 29771, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30127, "endOffset": 34294, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30182, "endOffset": 31967, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30616, "endOffset": 31133, "count": 3}, {"startOffset": 31072, "endOffset": 31080, "count": 1}, {"startOffset": 31081, "endOffset": 31092, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32018, "endOffset": 34286, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32703, "endOffset": 33461, "count": 1}, {"startOffset": 33283, "endOffset": 33460, "count": 0}], "isBlockCoverage": true}], "startOffset": 185}]}