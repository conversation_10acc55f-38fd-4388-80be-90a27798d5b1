-- =============================================
-- Quiz系统个性化配置测试数据 (新配置系统)
-- 对应数据库中的 user_quiz_preferences 表
-- 
-- 6层Quiz个性化配置架构：
-- Layer 0: 数据集展现配置 (Dataset Presentation)
-- Layer 1: 用户选择配置 (User Choice)
-- Layer 2: 渲染策略配置 (Rendering Strategy)
-- Layer 3: 皮肤基础配置 (Skin Base)
-- Layer 4: 视图细节配置 (View Detail)
-- Layer 5: 无障碍增强配置 (Accessibility)
-- =============================================

-- 插入Quiz个性化配置测试数据
INSERT OR IGNORE INTO user_quiz_preferences (
    id,
    user_id,
    config_name,
    presentation_config,
    config_version,
    personalization_level,
    is_active,
    is_default,
    created_at,
    updated_at
) VALUES
    -- 新手用户配置
    (
        'quiz_pref_beginner_user_1',
        'test-user-1',
        'default',
        '{
            "layer0_dataset_presentation": {
                "preferred_pack_categories": ["daily"],
                "default_difficulty_preference": "beginner",
                "session_length_preference": "short",
                "auto_select_recommended": true,
                "restore_progress": true
            },
            "layer1_user_choice": {
                "preferred_view_type": "wheel",
                "active_skin_id": "default-light",
                "color_mode": "warm",
                "user_level": "beginner"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "wheel": "D3",
                    "card": "CSS",
                    "bubble": "CSS",
                    "list": "CSS"
                },
                "performance_mode": "balanced",
                "supported_content_types": {
                    "text": true,
                    "emoji": true,
                    "images": false,
                    "animations": false
                }
            },
            "layer3_skin_base": {
                "selected_skin_id": "default-light",
                "colors": {
                    "primary": "#4F46E5",
                    "secondary": "#7C3AED",
                    "background": "#FFFFFF",
                    "surface": "#F9FAFB",
                    "text": "#111827",
                    "accent": "#F59E0B"
                },
                "animations": {
                    "enable_animations": false,
                    "animation_speed": "normal",
                    "reduce_motion": true
                }
            },
            "layer4_view_detail": {
                "wheel_config": {
                    "container_size": 300,
                    "wheel_radius": 150,
                    "emotion_display_mode": "simple",
                    "show_labels": true,
                    "show_emojis": true
                }
            },
            "layer5_accessibility": {
                "high_contrast": false,
                "large_text": true,
                "reduce_motion": true,
                "keyboard_navigation": true,
                "voice_guidance": false
            }
        }',
        '2.0',
        30, -- personalization_level
        1, -- is_active
        1, -- is_default
        datetime('now', '-30 days'),
        datetime('now', '-1 hour')
    ),
    
    -- 普通用户配置
    (
        'quiz_pref_regular_user_2',
        'test-user-2',
        'default',
        '{
            "layer0_dataset_presentation": {
                "preferred_pack_categories": ["daily", "assessment"],
                "default_difficulty_preference": "regular",
                "session_length_preference": "medium",
                "auto_select_recommended": false,
                "restore_progress": true
            },
            "layer1_user_choice": {
                "preferred_view_type": "card",
                "active_skin_id": "default-dark",
                "color_mode": "cool",
                "user_level": "regular"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "wheel": "SVG",
                    "card": "D3",
                    "bubble": "Canvas",
                    "list": "CSS"
                },
                "performance_mode": "balanced",
                "supported_content_types": {
                    "text": true,
                    "emoji": true,
                    "images": true,
                    "animations": true
                }
            },
            "layer3_skin_base": {
                "selected_skin_id": "default-dark",
                "colors": {
                    "primary": "#3B82F6",
                    "secondary": "#8B5CF6",
                    "background": "#111827",
                    "surface": "#1F2937",
                    "text": "#F9FAFB",
                    "accent": "#10B981"
                },
                "animations": {
                    "enable_animations": true,
                    "animation_speed": "normal",
                    "reduce_motion": false
                }
            },
            "layer4_view_detail": {
                "wheel_config": {
                    "container_size": 400,
                    "wheel_radius": 200,
                    "emotion_display_mode": "detailed",
                    "show_labels": true,
                    "show_emojis": true
                }
            },
            "layer5_accessibility": {
                "high_contrast": true,
                "large_text": false,
                "reduce_motion": false,
                "keyboard_navigation": true,
                "voice_guidance": false
            }
        }',
        '2.0',
        60, -- personalization_level
        1, -- is_active
        1, -- is_default
        datetime('now', '-15 days'),
        datetime('now', '-2 hours')
    ),
    
    -- 高级用户配置
    (
        'quiz_pref_advanced_user_3',
        'test-user-3',
        'default',
        '{
            "layer0_dataset_presentation": {
                "preferred_pack_categories": ["daily", "assessment", "therapy"],
                "default_difficulty_preference": "advanced",
                "session_length_preference": "long",
                "auto_select_recommended": false,
                "restore_progress": true
            },
            "layer1_user_choice": {
                "preferred_view_type": "bubble",
                "active_skin_id": "nature-green",
                "color_mode": "neutral",
                "user_level": "advanced"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "wheel": "Canvas",
                    "card": "Canvas",
                    "bubble": "R3F",
                    "list": "Virtual"
                },
                "performance_mode": "high_quality",
                "supported_content_types": {
                    "text": true,
                    "emoji": true,
                    "images": true,
                    "animations": true
                }
            },
            "layer3_skin_base": {
                "selected_skin_id": "nature-green",
                "colors": {
                    "primary": "#059669",
                    "secondary": "#0D9488",
                    "background": "#F0FDF4",
                    "surface": "#DCFCE7",
                    "text": "#064E3B",
                    "accent": "#F59E0B"
                },
                "animations": {
                    "enable_animations": true,
                    "animation_speed": "fast",
                    "reduce_motion": false
                }
            },
            "layer4_view_detail": {
                "wheel_config": {
                    "container_size": 500,
                    "wheel_radius": 250,
                    "emotion_display_mode": "advanced",
                    "show_labels": true,
                    "show_emojis": true
                }
            },
            "layer5_accessibility": {
                "high_contrast": false,
                "large_text": false,
                "reduce_motion": false,
                "keyboard_navigation": true,
                "voice_guidance": false
            }
        }',
        '2.0',
        85, -- personalization_level
        1, -- is_active
        1, -- is_default
        datetime('now', '-3 days'),
        datetime('now', '-1 day')
    ),
    
    -- VIP用户配置
    (
        'quiz_pref_vip_user_1',
        'test-user-1',
        'vip_premium',
        '{
            "layer0_dataset_presentation": {
                "preferred_pack_categories": ["daily", "assessment", "therapy", "research"],
                "default_difficulty_preference": "expert",
                "session_length_preference": "long",
                "auto_select_recommended": false,
                "restore_progress": true
            },
            "layer1_user_choice": {
                "preferred_view_type": "wheel",
                "active_skin_id": "ocean-blue",
                "color_mode": "cool",
                "user_level": "advanced"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "wheel": "R3F",
                    "card": "R3F",
                    "bubble": "R3F",
                    "list": "Canvas"
                },
                "performance_mode": "high_quality",
                "supported_content_types": {
                    "text": true,
                    "emoji": true,
                    "images": true,
                    "animations": true
                }
            },
            "layer3_skin_base": {
                "selected_skin_id": "ocean-blue",
                "colors": {
                    "primary": "#0EA5E9",
                    "secondary": "#3B82F6",
                    "background": "#F0F9FF",
                    "surface": "#E0F2FE",
                    "text": "#0C4A6E",
                    "accent": "#F59E0B"
                },
                "animations": {
                    "enable_animations": true,
                    "animation_speed": "fast",
                    "reduce_motion": false
                }
            },
            "layer4_view_detail": {
                "wheel_config": {
                    "container_size": 600,
                    "wheel_radius": 300,
                    "emotion_display_mode": "premium",
                    "show_labels": true,
                    "show_emojis": true
                }
            },
            "layer5_accessibility": {
                "high_contrast": false,
                "large_text": false,
                "reduce_motion": false,
                "keyboard_navigation": true,
                "voice_guidance": true
            }
        }',
        '2.0',
        95, -- personalization_level
        0, -- is_active (备用配置)
        0, -- is_default
        datetime('now', '-7 days'),
        datetime('now', '-1 hour')
    ),
    
    -- 无障碍用户配置
    (
        'quiz_pref_accessibility_user_3',
        'test-user-3',
        'accessibility',
        '{
            "layer0_dataset_presentation": {
                "preferred_pack_categories": ["daily"],
                "default_difficulty_preference": "beginner",
                "session_length_preference": "short",
                "auto_select_recommended": true,
                "restore_progress": true
            },
            "layer1_user_choice": {
                "preferred_view_type": "list",
                "active_skin_id": "default-light",
                "color_mode": "neutral",
                "user_level": "beginner"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "wheel": "CSS",
                    "card": "CSS",
                    "bubble": "CSS",
                    "list": "CSS"
                },
                "performance_mode": "performance",
                "supported_content_types": {
                    "text": true,
                    "emoji": false,
                    "images": false,
                    "animations": false
                }
            },
            "layer3_skin_base": {
                "selected_skin_id": "default-light",
                "colors": {
                    "primary": "#000000",
                    "secondary": "#374151",
                    "background": "#FFFFFF",
                    "surface": "#F9FAFB",
                    "text": "#000000",
                    "accent": "#DC2626"
                },
                "animations": {
                    "enable_animations": false,
                    "animation_speed": "slow",
                    "reduce_motion": true
                }
            },
            "layer4_view_detail": {
                "wheel_config": {
                    "container_size": 250,
                    "wheel_radius": 125,
                    "emotion_display_mode": "simple",
                    "show_labels": true,
                    "show_emojis": false
                }
            },
            "layer5_accessibility": {
                "high_contrast": true,
                "large_text": true,
                "reduce_motion": true,
                "keyboard_navigation": true,
                "voice_guidance": true
            }
        }',
        '2.0',
        25, -- personalization_level (简化配置)
        0, -- is_active (备用配置)
        0, -- is_default
        datetime('now', '-5 days'),
        datetime('now', '-30 minutes')
    );

-- 验证插入的数据
SELECT 
    id,
    user_id,
    config_name,
    personalization_level,
    is_active,
    is_default
FROM user_quiz_preferences 
ORDER BY created_at DESC;
