/**
 * tRPC API客户端服务
 * 基于 tRPC 提供云端数据库访问，支持直接 SQL 执行和认证
 */

import {
  ApiResponse,
  ApiError,
  OnlineServiceConfig,
  AuthToken,
  BatchOperationResult
} from './types/OnlineServiceTypes';
import { NetworkStatusService } from './NetworkStatusService';
import { trpc } from '@/lib/trpc'; // 假设 tRPC 客户端在这里

// 导入统一的类型定义
import {
  type SqlQueryConfig,
  type BatchSqlConfig
} from '../../types/schema/api';

// tRPC 请求拦截器
export interface TrpcRequestInterceptor {
  (config: SqlQueryConfig): SqlQueryConfig | Promise<SqlQueryConfig>;
}

// tRPC 响应拦截器
export interface TrpcResponseInterceptor {
  (response: ApiResponse): ApiResponse | Promise<ApiResponse>;
}

// tRPC 错误拦截器
export interface TrpcErrorInterceptor {
  (error: ApiError): ApiError | Promise<ApiError>;
}

export class ApiClientService {
  private static instance: ApiClientService;
  private config: OnlineServiceConfig;
  private networkService: NetworkStatusService;
  private requestInterceptors: TrpcRequestInterceptor[] = [];
  private responseInterceptors: TrpcResponseInterceptor[] = [];
  private errorInterceptors: TrpcErrorInterceptor[] = [];
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private authToken?: AuthToken;

  private constructor(config: OnlineServiceConfig) {
    this.config = config;
    this.networkService = NetworkStatusService.getInstance();
    this.setupDefaultInterceptors();
  }

  static getInstance(config?: OnlineServiceConfig): ApiClientService {
    if (!ApiClientService.instance) {
      if (!config) {
        throw new Error('ApiClientService requires configuration on first initialization');
      }
      ApiClientService.instance = new ApiClientService(config);
    }
    return ApiClientService.instance;
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token: AuthToken): void {
    this.authToken = token;

    // 保存到本地存储
    try {
      localStorage.setItem(this.config.authConfig.tokenStorageKey, JSON.stringify(token));
    } catch (error) {
      console.error('[ApiClientService] Failed to save auth token:', error);
    }
  }

  /**
   * 获取认证令牌
   */
  getAuthToken(): AuthToken | undefined {
    if (this.authToken) {
      return this.authToken;
    }

    // 从本地存储加载
    try {
      const stored = localStorage.getItem(this.config.authConfig.tokenStorageKey);
      if (stored) {
        const token = JSON.parse(stored) as AuthToken;
        // expiresAt 现在是字符串类型，不需要转换
        this.authToken = token;
        return token;
      }
    } catch (error) {
      console.error('[ApiClientService] Failed to load auth token:', error);
    }

    return undefined;
  }

  /**
   * 清除认证令牌
   */
  clearAuthToken(): void {
    this.authToken = undefined;

    try {
      localStorage.removeItem(this.config.authConfig.tokenStorageKey);
    } catch (error) {
      console.error('[ApiClientService] Failed to clear auth token:', error);
    }
  }

  /**
   * 检查令牌是否即将过期
   */
  isTokenExpiringSoon(): boolean {
    const token = this.getAuthToken();
    if (!token) return false;

    const now = new Date();
    const expiresAt = new Date(token.expiresAt);
    const threshold = this.config.authConfig.refreshThreshold * 60 * 1000; // 转换为毫秒

    return (expiresAt.getTime() - now.getTime()) < threshold;
  }

  /**
   * 执行 SQL 查询
   */
  async executeQuery<T = any>(config: SqlQueryConfig): Promise<ApiResponse<T>> {
    // 应用请求拦截器
    let queryConfig = { ...config };
    for (const interceptor of this.requestInterceptors) {
      queryConfig = await interceptor(queryConfig);
    }

    // 检查网络状态
    const networkStatus = this.networkService.getCurrentStatus();
    if (!networkStatus.isOnline) {
      const error: ApiError = {
        message: 'No internet connection',
        code: 'NETWORK_ERROR',
        status: 0
      };
      return this.handleError(error);
    }

    // 检查认证
    if (queryConfig.requireAuth !== false) {
      const token = this.getAuthToken();
      if (!token || this.isTokenExpired(token)) {
        const error: ApiError = {
          message: 'Authentication required',
          code: 'UNAUTHORIZED',
          status: 401
        };
        return this.handleError(error);
      }
    }

    // 检查缓存
    const cacheKey = this.generateQueryCacheKey(queryConfig);
    if (!queryConfig.skipCache) {
      const cachedResponse = this.getFromCache(cacheKey);
      if (cachedResponse) {
        return cachedResponse;
      }
    }

    // 执行 tRPC 查询
    try {
      const result = await this.executeTrpcQuery(queryConfig);

      const apiResponse: ApiResponse<T> = {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };

      // 应用响应拦截器
      let finalResponse = apiResponse;
      for (const interceptor of this.responseInterceptors) {
        finalResponse = await interceptor(finalResponse);
      }

      // 缓存响应
      if (!queryConfig.skipCache && finalResponse.success) {
        this.setCache(cacheKey, finalResponse, 300); // 默认缓存5分钟
      }

      return finalResponse;
    } catch (error) {
      return this.handleTrpcError(error);
    }
  }

  /**
   * 执行批量 SQL 操作
   */
  async executeBatch<T = any>(config: BatchSqlConfig): Promise<ApiResponse<BatchOperationResult<T>>> {
    // 检查网络状态
    const networkStatus = this.networkService.getCurrentStatus();
    if (!networkStatus.isOnline) {
      const error: ApiError = {
        message: 'No internet connection',
        code: 'NETWORK_ERROR',
        status: 0
      };
      return this.handleError(error);
    }

    // 检查认证
    if (config.requireAuth !== false) {
      const token = this.getAuthToken();
      if (!token || this.isTokenExpired(token)) {
        const error: ApiError = {
          message: 'Authentication required',
          code: 'UNAUTHORIZED',
          status: 401
        };
        return this.handleError(error);
      }
    }

    try {
      const result = await this.executeTrpcBatch(config);

      const apiResponse: ApiResponse<BatchOperationResult<T>> = {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };

      return apiResponse;
    } catch (error) {
      return this.handleTrpcError(error);
    }
  }

  /**
   * 便捷方法：执行 SELECT 查询
   */
  async select<T = any>(sql: string, args?: any[], options?: Partial<SqlQueryConfig>): Promise<ApiResponse<T[]>> {
    return this.executeQuery<T[]>({
      sql,
      args,
      ...options
    });
  }

  /**
   * 便捷方法：执行 INSERT 查询
   */
  async insert<T = any>(sql: string, args?: any[], options?: Partial<SqlQueryConfig>): Promise<ApiResponse<T>> {
    return this.executeQuery<T>({
      sql,
      args,
      skipCache: true,
      ...options
    });
  }

  /**
   * 便捷方法：执行 UPDATE 查询
   */
  async update(sql: string, args?: any[], options?: Partial<SqlQueryConfig>): Promise<ApiResponse<{ changes: number }>> {
    return this.executeQuery<{ changes: number }>({
      sql,
      args,
      skipCache: true,
      ...options
    });
  }

  /**
   * 便捷方法：执行 DELETE 查询
   */
  async delete(sql: string, args?: any[], options?: Partial<SqlQueryConfig>): Promise<ApiResponse<{ changes: number }>> {
    return this.executeQuery<{ changes: number }>({
      sql,
      args,
      skipCache: true,
      ...options
    });
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: TrpcRequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: TrpcResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor: TrpcErrorInterceptor): void {
    this.errorInterceptors.push(interceptor);
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 设置默认拦截器
   */
  private setupDefaultInterceptors(): void {
    // 请求拦截器：添加认证信息
    this.addRequestInterceptor((config) => {
      if (config.requireAuth !== false) {
        const token = this.getAuthToken();
        if (token && !this.isTokenExpired(token)) {
          // 在 tRPC 中，认证信息通过上下文传递，这里只是标记
          config.requireAuth = true;
        }
      }
      return config;
    });

    // 响应拦截器：处理认证错误
    this.addResponseInterceptor(async (response) => {
      if (!response.success && response.code === 'UNAUTHORIZED') {
        // 尝试刷新令牌
        if (this.config.authConfig.autoRefresh) {
          const refreshed = await this.refreshToken();
          if (refreshed) {
            // 令牌刷新成功，可以重试原请求
            // 这里需要实现重试逻辑
          } else {
            // 令牌刷新失败，清除认证信息
            this.clearAuthToken();
          }
        }
      }
      return response;
    });
  }

  /**
   * 执行 tRPC 查询
   */
  private async executeTrpcQuery(config: SqlQueryConfig): Promise<any> {
    try {
      // 获取认证令牌
      const token = this.getAuthToken();
      const authHeader = token ? `${token.tokenType} ${token.accessToken}` : undefined;

      // 调用 tRPC 查询
      const result = await trpc.query.query({
        sql: config.sql,
        args: config.args || [],
        auth: authHeader
      });

      if (!result.success) {
        throw new Error(result.error || 'Query failed');
      }

      return result.data;
    } catch (error) {
      console.error('[ApiClientService] tRPC query failed:', error);
      throw error;
    }
  }

  /**
   * 执行 tRPC 批量操作
   */
  private async executeTrpcBatch(config: BatchSqlConfig): Promise<BatchOperationResult<any>> {
    try {
      // 获取认证令牌
      const token = this.getAuthToken();
      const authHeader = token ? `${token.tokenType} ${token.accessToken}` : undefined;

      // 调用 tRPC 批量操作
      const result = await trpc.batch.mutate({
        statements: config.statements,
        transactional: config.transactional || false,
        auth: authHeader
      });

      if (!result.success) {
        throw new Error(result.error || 'Batch operation failed');
      }

      return result.data;
    } catch (error) {
      console.error('[ApiClientService] tRPC batch operation failed:', error);
      throw error;
    }
  }

  /**
   * 处理 tRPC 错误
   */
  private async handleTrpcError(error: any): Promise<ApiResponse> {
    const apiError: ApiError = {
      message: error.message || 'tRPC request failed',
      code: error.code || 'TRPC_ERROR',
      details: error,
      timestamp: new Date().toISOString()
    };

    return this.handleError(apiError);
  }

  /**
   * 生成查询缓存键
   */
  private generateQueryCacheKey(config: SqlQueryConfig): string {
    const args = config.args ? JSON.stringify(config.args) : '';
    return `${config.sql}:${args}`;
  }

  /**
   * 处理错误
   */
  private async handleError(error: ApiError): Promise<ApiResponse> {
    // 应用错误拦截器
    let finalError = error;
    for (const interceptor of this.errorInterceptors) {
      finalError = await interceptor(finalError);
    }

    return {
      success: false,
      error: finalError.message,
      code: finalError.code,
      timestamp: finalError.timestamp
    };
  }



  /**
   * 从缓存获取数据
   */
  private getFromCache(key: string): ApiResponse | undefined {
    const cached = this.cache.get(key);
    if (!cached) return undefined;

    const now = Date.now();
    if (now > cached.timestamp + cached.ttl * 1000) {
      this.cache.delete(key);
      return undefined;
    }

    return cached.data;
  }

  /**
   * 设置缓存
   */
  private setCache(key: string, data: ApiResponse, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 检查令牌是否过期
   */
  private isTokenExpired(token: AuthToken): boolean {
    return new Date() >= new Date(token.expiresAt);
  }

  /**
   * 刷新令牌
   */
  private async refreshToken(): Promise<boolean> {
    const token = this.getAuthToken();
    if (!token || !token.refreshToken) {
      return false;
    }

    try {
      // 使用 tRPC 刷新令牌
      const result = await trpc.auth.refresh.mutate({
        refreshToken: token.refreshToken
      });

      if (result.success && result.data) {
        this.setAuthToken(result.data);
        return true;
      }
    } catch (error) {
      console.error('[ApiClientService] Token refresh failed:', error);
    }

    return false;
  }
}
