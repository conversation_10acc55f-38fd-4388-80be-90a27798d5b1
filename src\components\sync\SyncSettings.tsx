import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { useLanguage } from '@/contexts/LanguageContext';
import { useNetwork } from '@/contexts/NetworkContext';
import { type SyncConfig, useSync } from '@/contexts/SyncContext';
import {
  AlertTriangle,
  Clock,
  Cloud,
  CloudOff,
  RefreshCw,
  Settings,
  Trash2,
  Wifi,
} from 'lucide-react';
import type React from 'react';

interface SyncSettingsProps {
  onClose?: () => void;
}

/**
 * 同步设置组件
 * 用于配置同步选项
 */
const SyncSettings: React.FC<SyncSettingsProps> = ({ onClose }) => {
  const { t } = useLanguage();
  const { isOnline, isInternetReachable, connectionType } = useNetwork();
  const {
    onlineSyncEnabled,
    setOnlineSyncEnabled,
    syncConfig,
    updateSyncConfig,
    syncStatus,
    startSync,
    retryFailedItems,
    clearFailedItems,
    pendingItems,
    failedItems,
  } = useSync();

  // 处理同步间隔变更
  const handleIntervalChange = (value: number[]) => {
    updateSyncConfig({ syncInterval: value[0] });
  };

  // 处理同步类型变更
  const handleSyncTypeChange = (type: keyof SyncConfig['syncTypes'], checked: boolean) => {
    updateSyncConfig({
      syncTypes: {
        ...syncConfig.syncTypes,
        [type]: checked,
      },
    });
  };

  // 获取同步间隔文本
  const getSyncIntervalText = (minutes: number) => {
    if (minutes < 60) {
      return t('sync.interval_minutes', { minutes: String(minutes) });
    }
    const hours = minutes / 60;
    return t('sync.interval_hours', { hours: String(hours) });
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t('sync.settings')}</span>
          <Badge variant={onlineSyncEnabled ? 'default' : 'outline'}>
            {onlineSyncEnabled ? t('sync.enabled') : t('sync.disabled')}
          </Badge>
        </CardTitle>
        <CardDescription>{t('sync.settings_description')}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 网络状态 */}
        <div className="rounded-md bg-muted p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {isOnline ? (
                isInternetReachable ? (
                  <Cloud className="h-5 w-5 text-primary" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-warning" />
                )
              ) : (
                <CloudOff className="h-5 w-5 text-destructive" />
              )}
              <div>
                <p className="text-sm font-medium">
                  {isOnline
                    ? isInternetReachable
                      ? t('sync.network_connected')
                      : t('sync.network_no_internet')
                    : t('sync.network_offline')}
                </p>
                <p className="text-xs text-muted-foreground">
                  {connectionType === 'wifi'
                    ? t('sync.network_wifi')
                    : connectionType === 'cellular'
                      ? t('sync.network_cellular')
                      : t('sync.network_unknown')}
                </p>
              </div>
            </div>

            <Switch
              checked={onlineSyncEnabled}
              onCheckedChange={setOnlineSyncEnabled}
              aria-label={t('sync.toggle_sync')}
            />
          </div>
        </div>

        {/* 同步选项 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">{t('sync.options')}</h3>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="autoSync">{t('sync.auto_sync')}</Label>
              <p className="text-xs text-muted-foreground">{t('sync.auto_sync_description')}</p>
            </div>
            <Switch
              id="autoSync"
              checked={syncConfig.autoSync}
              onCheckedChange={(checked) => updateSyncConfig({ autoSync: checked })}
              disabled={!onlineSyncEnabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="syncOnWifiOnly">{t('sync.wifi_only')}</Label>
              <p className="text-xs text-muted-foreground">{t('sync.wifi_only_description')}</p>
            </div>
            <Switch
              id="syncOnWifiOnly"
              checked={syncConfig.syncOnWifiOnly}
              onCheckedChange={(checked) => updateSyncConfig({ syncOnWifiOnly: checked })}
              disabled={!onlineSyncEnabled || !syncConfig.autoSync}
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="syncInterval">{t('sync.interval')}</Label>
              <span className="text-xs text-muted-foreground">
                {getSyncIntervalText(syncConfig.syncInterval)}
              </span>
            </div>
            <Slider
              id="syncInterval"
              defaultValue={[syncConfig.syncInterval]}
              min={5}
              max={240}
              step={5}
              onValueChange={handleIntervalChange}
              disabled={!onlineSyncEnabled || !syncConfig.autoSync}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>5{t('sync.minutes')}</span>
              <span>1{t('sync.hour')}</span>
              <span>4{t('sync.hours')}</span>
            </div>
          </div>
        </div>

        <Separator />

        {/* 同步内容 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">{t('sync.content')}</h3>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label htmlFor="syncMoodEntries">{t('sync.mood_entries')}</Label>
              </div>
              <Switch
                id="syncMoodEntries"
                checked={syncConfig.syncTypes.mood_entries}
                onCheckedChange={(checked) => handleSyncTypeChange('mood_entries', checked)}
                disabled={!onlineSyncEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label htmlFor="syncUserPreferences">{t('sync.user_preferences')}</Label>
              </div>
              <Switch
                id="syncUserPreferences"
                checked={syncConfig.syncTypes.user_preferences}
                onCheckedChange={(checked) => handleSyncTypeChange('user_preferences', checked)}
                disabled={!onlineSyncEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label htmlFor="syncEmotionData">{t('sync.emotion_data')}</Label>
              </div>
              <Switch
                id="syncEmotionData"
                checked={syncConfig.syncTypes.emotion_data}
                onCheckedChange={(checked) => handleSyncTypeChange('emotion_data', checked)}
                disabled={!onlineSyncEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label htmlFor="syncTags">{t('sync.tags')}</Label>
              </div>
              <Switch
                id="syncTags"
                checked={syncConfig.syncTypes.tags}
                onCheckedChange={(checked) => handleSyncTypeChange('tags', checked)}
                disabled={!onlineSyncEnabled}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* 同步状态 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">{t('sync.status')}</h3>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-xs text-muted-foreground">{t('sync.pending_items')}</p>
              <p className="font-medium flex items-center">
                <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                {pendingItems.length}
              </p>
            </div>

            <div>
              <p className="text-xs text-muted-foreground">{t('sync.failed_items')}</p>
              <p className="font-medium flex items-center">
                <AlertTriangle className="h-4 w-4 mr-1 text-destructive" />
                {failedItems.length}
              </p>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={() => startSync()}
              disabled={
                !onlineSyncEnabled || !isOnline || pendingItems.length === 0 || syncStatus.isSyncing
              }
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('sync.sync_now')}
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={() => retryFailedItems()}
              disabled={
                !onlineSyncEnabled || !isOnline || failedItems.length === 0 || syncStatus.isSyncing
              }
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('sync.retry_failed')}
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={() => clearFailedItems()}
              disabled={failedItems.length === 0 || syncStatus.isSyncing}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {t('sync.clear_failed')}
            </Button>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onClose}>
          {t('common.close')}
        </Button>
        <Button variant="default" onClick={onClose}>
          <Settings className="h-4 w-4 mr-2" />
          {t('common.save')}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SyncSettings;
