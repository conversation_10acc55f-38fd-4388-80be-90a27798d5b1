# Schema 类型定义架构

本目录包含项目的统一类型定义系统，基于 Zod 进行运行时验证，确保类型安全和数据一致性。

## 文件结构

### 📁 `base.ts` - 核心类型定义
**作用**: 项目的主要类型定义文件，包含所有数据库实体的 Zod Schema 定义。

**包含的 Schema**:
- `UserSchema` - 用户信息
- `EmotionSchema` - 情绪数据
- `EmotionDataSetSchema` - 情绪数据集
- `EmotionDataSetTierSchema` - 情绪数据集层级
- `EmojiSetSchema` - 表情集
- `EmojiItemSchema` - 表情项
- `SkinSchema` - 皮肤配置
- `MoodEntrySchema` - 心情记录

**特点**:
- 基于数据库 schema 的统一类型定义
- 使用 Zod 进行运行时验证
- 作为所有类型定义的单一数据源 (Single Source of Truth)
- 导出 TypeScript 类型和 Zod Schema

### 📁 `api.ts` - API 接口类型
**作用**: 定义 tRPC API 的输入输出类型，确保客户端和服务端类型一致。

**包含的 Schema**:
- **认证相关**: `LoginInputSchema`, `RegisterInputSchema`, `AuthResponseSchema`
- **数据库操作**: `SqlQueryInputSchema`, `BatchOperationInputSchema`, `FetchTableInputSchema`
- **支付相关**: `VipPlanSchema`, `PurchaseVipInputSchema`, `PurchaseSkinInputSchema`
- **用户配置**: `UserConfigSchema`, `UpdateUserConfigInputSchema`
- **数据同步**: `SynchronizeDataInputSchema`, `PerformFullSyncInputSchema`
- **通用工具**: `PaginationSchema`, `SortSchema`, `ApiResponseSchema`

**特点**:
- 依赖 `base.ts` 中的基础类型
- 专注于 API 输入输出验证
- 支持分页、排序等通用功能

### 📁 `generator.ts` - Schema 生成工具
**作用**: 提供从 SQL 定义生成 Zod Schema 的工具函数，与手动定义协同工作。

**主要功能**:
- `SQL_TO_ZOD_MAPPING` - SQL 类型到 Zod Schema 的映射
- `generateZodSchemaFromField()` - 从数据库字段生成 Zod Schema
- `generateZodSchemaFromTable()` - 从数据库表生成 Zod Schema
- `validateSchemaConsistency()` - 验证数据库定义与 Zod Schema 的一致性
- `parseCreateTableStatement()` - 从 SQL CREATE TABLE 语句解析表结构

**特点**:
- 工具性质，不包含具体的类型定义
- 可用于验证手动定义的 Schema 与数据库结构的一致性
- 支持从 SQL 自动生成类型定义

### 📁 `translation.ts` - 翻译系统扩展
**作用**: 基于基础 Schema 提供完整的翻译系统功能，替代原有的 `translationTypes.ts`。

**包含的 Schema**:
- **基础翻译**: `TranslationSchema`, `TranslatableEntitySchema`, `TranslationInputSchema`
- **查询过滤**: `TranslationFilterSchema`, `LanguageSupportExtendedSchema`
- **验证导入**: `TranslationValidationSchema`, `TranslationExportSchema`
- **上下文事件**: `TranslationContextSchema`, `TranslationEventSchema`
- **联合类型**: `AnyTranslationSchema`, `EntityTypeSchema`

**特点**:
- 与数据库翻译表完全对齐
- 提供运行时验证和类型安全
- 兼容原有的翻译系统接口
- 支持多实体类型的统一翻译管理

## 使用指南

### 导入基础类型
```typescript
import { UserSchema, EmotionSchema, LanguageCodeSchema, type User, type Emotion, type LanguageCode } from '@/types/schema/base';
```

### 导入 API 类型
```typescript
import { LoginInputSchema, PaginationSchema, GetTranslationInputSchema, type LoginInput } from '@/types/schema/api';
```

### 导入翻译类型
```typescript
import { TranslationSchema, TranslatableEntitySchema, type Translation, type TranslatableEntity } from '@/types/schema/translation';
```

### 使用生成工具
```typescript
import { validateSchemaConsistency, generateZodSchemaFromField } from '@/types/schema/generator';
```

### 数据验证示例
```typescript
import { UserSchema } from '@/types/schema/base';

// 运行时验证
const userData = UserSchema.parse(rawData);

// 类型检查
const user: User = {
  id: 'user-123',
  username: 'testuser',
  email: '<EMAIL>',
  // ... 其他必需字段
};
```

### 翻译系统使用示例
```typescript
import { EmotionTranslationSchema, LanguageCodeSchema } from '@/types/schema/base';
import { TranslationSchema, TranslatableEntitySchema } from '@/types/schema/translation';
import { GetTranslationInputSchema } from '@/types/schema/api';

// 验证翻译数据
const translation = TranslationSchema.parse({
  entityId: 'emotion-123',
  languageCode: 'zh',
  translatedName: '快乐',
  translatedDescription: '一种积极的情绪状态'
});

// API 查询验证
const queryInput = GetTranslationInputSchema.parse({
  entityType: 'emotion',
  entityId: 'emotion-123',
  languageCode: 'zh',
  fallbackLanguage: 'en'
});

// 数据库翻译记录验证
const emotionTranslation = EmotionTranslationSchema.parse({
  emotion_id: 'emotion-123',
  language_code: 'zh',
  translated_name: '快乐'
});
```

## 设计原则

1. **单一数据源**: `base.ts` 作为所有类型定义的权威来源
2. **层次分离**: 基础类型、API 类型、扩展功能、工具函数分别管理
3. **运行时安全**: 使用 Zod 确保运行时类型安全
4. **数据库对齐**: 直接映射数据库表结构，确保数据一致性
5. **一致性验证**: 提供工具验证手动定义与数据库结构的一致性
6. **可扩展性**: 支持通过工具函数自动生成新的类型定义
7. **翻译集成**: 统一管理多语言翻译，支持所有可翻译实体
8. **向后兼容**: 保持与现有翻译系统的接口兼容性

## 修复历史

### 2024年修复记录
**问题**:
- `base.ts` 和 `generator.ts` 中存在重复的类型定义
- `generator.ts` 中的表结构定义不完整且与 `base.ts` 不一致
- 存在未使用的代码和重复导出

**解决方案**:
- 保留 `base.ts` 作为主要类型定义文件
- 重构 `generator.ts` 为纯工具函数库，移除重复定义
- 添加一致性验证工具
- 清理重复导出和未使用代码

**结果**:
- 消除了类型定义的重复和不一致
- 建立了清晰的文件职责分工
- 提供了验证工具确保未来的一致性

## 测试

使用 `validation-test.ts` 进行 Schema 验证测试：

```bash
# 运行验证测试
npx tsx src/types/schema/validation-test.ts
```

测试覆盖：
- 基础 Schema 的有效性
- API Schema 的有效性
- 生成器工具的功能性
