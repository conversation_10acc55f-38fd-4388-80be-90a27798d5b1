# Script to test the fixed server SQL initialization
$logFile = "fixed-server-init-log.txt"
$dbFile = "local.db"

Write-Host "Testing fixed server SQL initialization..."
Write-Host "Log will be saved to: $logFile"

# Check if database file exists and delete it to force fresh initialization
if (Test-Path $dbFile) {
    Write-Host "Removing existing database file to force fresh initialization..."
    Remove-Item $dbFile -Force
    Write-Host "Database file removed."
}

# Start the server with output redirected to log file
Write-Host "Starting server with output redirected to log file..."
Write-Host "Press Ctrl+C after a few seconds to stop the server once initialization is complete."

# Run the server and redirect output to log file
node server\local-server.js > $logFile 2>&1

Write-Host "Server stopped. Analyzing log file..."

# Display the log file content
Get-Content $logFile

# Check if database file was created
if (Test-Path $dbFile) {
    Write-Host "Database file was created successfully."
    
    # Run the verify-database.js script to check the database tables
    Write-Host "Running database verification..."
    node verify-database.js > "verify-db-log.txt" 2>&1
    Get-Content "verify-db-log.txt"
} else {
    Write-Host "ERROR: Database file was not created!"
}

Write-Host "Test complete. Check $logFile for detailed logs."
