# Quiz组件使用示例

## 🎯 快速开始

本文档提供了Quiz组件系统的详细使用示例，帮助开发者快速上手。

## 📝 文本组件使用示例

### 1. 基础文本组件

```typescript
import { QuizComponentFactory, CommonTexts } from '@/components/quiz/presets/ComponentPresets';

// 创建标准问题文本
const questionText = QuizComponentFactory.createText(
  { zh: '请描述您目前的情绪状态', en: 'Please describe your current emotional state' },
  'question'
);

// 创建提示文本
const hintText = QuizComponentFactory.createText(
  CommonTexts.hints.select_one,
  'hint'
);
```

### 2. 中医文化特色文本

```typescript
// 卷轴文本 - 适合古典诗词
const scrollText = QuizComponentFactory.createText(
  { 
    zh: '心静自然凉，情深不知愁。\n古人云：心病还须心药医。',
    en: 'A calm heart naturally feels cool, deep emotions know no sorrow.'
  },
  'scroll_text'
);

// 碑文文本 - 适合重要声明
const inscriptionText = QuizComponentFactory.createText(
  { 
    zh: '中医情绪评估系统\n传承千年智慧，守护心灵健康',
    en: 'TCM Emotion Assessment System\nInheriting millennium wisdom, protecting mental health'
  },
  'inscription_text'
);

// 浮动文本 - 适合轻量提示
const floatingText = QuizComponentFactory.createText(
  { zh: '请耐心完成评估，这将有助于了解您的体质特点', en: 'Please complete the assessment patiently' },
  'floating_text'
);

// 横幅文本 - 适合重要公告
const bannerText = QuizComponentFactory.createText(
  { zh: '🎉 恭喜完成中医体质评估！', en: '🎉 Congratulations on completing the TCM assessment!' },
  'banner_text'
);
```

### 3. 在React组件中使用

```tsx
import React from 'react';
import { TextComponent } from '@/components/quiz/base';

const QuizQuestionPage: React.FC = () => {
  const questionConfig = QuizComponentFactory.createText(
    { zh: '您最近是否经常感到疲劳？', en: 'Do you often feel tired recently?' },
    'question',
    {
      // 自定义覆盖配置
      style: {
        color_scheme: '#2E7D32',
        background_pattern: 'bamboo'
      }
    }
  );

  return (
    <div className="quiz-question-page">
      <TextComponent
        id="question-1"
        config={questionConfig}
        content={questionConfig.content}
        onInteraction={(event) => console.log('Text interaction:', event)}
      />
    </div>
  );
};
```

## 🎚️ 滑块组件使用示例

### 1. 基础滑块

```typescript
// 标准数值滑块
const standardSlider = QuizComponentFactory.createSlider(
  { min: 0, max: 100, step: 1, default_value: 50 },
  { 
    start_label: { zh: '完全不同意', en: 'Strongly Disagree' },
    end_label: { zh: '完全同意', en: 'Strongly Agree' },
    unit: '%'
  },
  'standard_slider'
);
```

### 2. 中医文化特色滑块

```typescript
// 竹节滑块 - 适合体质评估
const bambooSlider = QuizComponentFactory.createSlider(
  { min: 1, max: 10, step: 1, default_value: 5 },
  { 
    start_label: { zh: '虚弱', en: 'Weak' },
    end_label: { zh: '强壮', en: 'Strong' },
    unit: '级'
  },
  'bamboo_slider'
);

// 墨迹滑块 - 适合情绪深度
const inkBrushSlider = QuizComponentFactory.createSlider(
  { min: 0, max: 100, step: 5, default_value: 30 },
  { 
    start_label: { zh: '平静', en: 'Calm' },
    end_label: { zh: '激动', en: 'Excited' },
    unit: '度'
  },
  'ink_brush_slider'
);

// 龙脊滑块 - 适合力量评估
const dragonSpineSlider = QuizComponentFactory.createSlider(
  { min: 1, max: 5, step: 1, default_value: 3 },
  { 
    start_label: { zh: '轻微', en: 'Mild' },
    end_label: { zh: '严重', en: 'Severe' },
    unit: '级'
  },
  'dragon_spine_slider'
);

// 河流滑块 - 适合流动性评估
const riverFlowSlider = QuizComponentFactory.createSlider(
  { min: 0, max: 100, step: 10, default_value: 60 },
  { 
    start_label: { zh: '缓慢', en: 'Slow' },
    end_label: { zh: '急速', en: 'Fast' },
    unit: '流速'
  },
  'river_flow_slider'
);
```

### 3. 在React组件中使用

```tsx
import React, { useState } from 'react';
import { SliderComponent } from '@/components/quiz/base';

const EmotionIntensitySlider: React.FC = () => {
  const [intensity, setIntensity] = useState(50);

  const sliderConfig = QuizComponentFactory.createSlider(
    { min: 0, max: 100, step: 5, default_value: intensity },
    { 
      start_label: { zh: '很轻微', en: 'Very Mild' },
      end_label: { zh: '非常强烈', en: 'Very Intense' },
      unit: '%'
    },
    'bamboo_slider'
  );

  return (
    <div className="emotion-intensity-slider">
      <h3>情绪强度评估</h3>
      <SliderComponent
        config={sliderConfig}
        value={intensity}
        onChange={setIntensity}
        onChangeComplete={(value) => {
          console.log('Final intensity value:', value);
          // 提交到后端或更新状态
        }}
        onInteraction={(event) => console.log('Slider interaction:', event)}
      />
      <p>当前强度: {intensity}%</p>
    </div>
  );
};
```

## 🎯 选择器组件使用示例

### 1. 情绪选择器

```typescript
// 创建情绪选择器
const emotionSelector = QuizComponentFactory.createEmotionSelector([
  { id: 'joy', name: { zh: '喜悦', en: 'Joy' }, icon: 'smile' },
  { id: 'anger', name: { zh: '愤怒', en: 'Anger' }, icon: 'fire' },
  { id: 'sadness', name: { zh: '悲伤', en: 'Sadness' }, icon: 'tear' },
  { id: 'fear', name: { zh: '恐惧', en: 'Fear' }, icon: 'shield' },
  { id: 'surprise', name: { zh: '惊讶', en: 'Surprise' }, icon: 'star' }
]);
```

### 2. 症状多选器

```typescript
const symptomSelector = QuizComponentFactory.createSelector(
  [
    { id: 'headache', value: 'headache', text: { zh: '头痛', en: 'Headache' } },
    { id: 'fatigue', value: 'fatigue', text: { zh: '疲劳', en: 'Fatigue' } },
    { id: 'insomnia', value: 'insomnia', text: { zh: '失眠', en: 'Insomnia' } },
    { id: 'anxiety', value: 'anxiety', text: { zh: '焦虑', en: 'Anxiety' } }
  ],
  'symptom_multi_select',
  {
    validation: {
      required: false,
      min_selections: 0,
      max_selections: 3
    }
  }
);
```

## 🎨 按钮组件使用示例

### 1. 中医风格按钮

```typescript
// 确认按钮 - 印章风格
const confirmButton = QuizComponentFactory.createButton(
  CommonTexts.buttons.confirm,
  'confirm_tcm'
);

// 特殊操作按钮 - 玉佩风格
const specialButton = QuizComponentFactory.createButton(
  { zh: '开始评估', en: 'Start Assessment' },
  'special_action'
);

// 导航按钮
const nextButton = QuizComponentFactory.createButton(
  CommonTexts.buttons.next,
  'navigation',
  {
    content: {
      icon_name: 'arrow-right'
    }
  }
);
```

## 🏗️ 完整页面组合示例

### 中医体质评估页面

```tsx
import React, { useState } from 'react';
import { 
  TextComponent, 
  ButtonComponent, 
  SelectorComponent, 
  SliderComponent 
} from '@/components/quiz/base';
import { QuizComponentFactory, CommonTexts } from '@/components/quiz/presets/ComponentPresets';

const TCMConstitutionAssessment: React.FC = () => {
  const [selectedSymptoms, setSelectedSymptoms] = useState<(string | number)[]>([]);
  const [energyLevel, setEnergyLevel] = useState(50);
  const [sleepQuality, setSleepQuality] = useState(70);

  // 页面组件配置
  const pageComponents = QuizComponentFactory.createQuestionPage({
    title: { zh: '中医体质评估', en: 'TCM Constitution Assessment' },
    question: { zh: '请根据您最近一个月的身体状况，完成以下评估', en: 'Please complete the following assessment based on your physical condition in the past month' },
    options: [
      { id: 'qi_deficiency', value: 'qi_deficiency', text: { zh: '气虚质', en: 'Qi Deficiency' } },
      { id: 'yang_deficiency', value: 'yang_deficiency', text: { zh: '阳虚质', en: 'Yang Deficiency' } },
      { id: 'yin_deficiency', value: 'yin_deficiency', text: { zh: '阴虚质', en: 'Yin Deficiency' } },
      { id: 'phlegm_dampness', value: 'phlegm_dampness', text: { zh: '痰湿质', en: 'Phlegm-Dampness' } }
    ],
    confirmButton: { zh: '提交评估', en: 'Submit Assessment' },
    hint: { zh: '请根据实际情况如实填写，这将有助于为您提供个性化的健康建议', en: 'Please fill in according to the actual situation' }
  });

  // 能量水平滑块
  const energySlider = QuizComponentFactory.createSlider(
    { min: 0, max: 100, step: 5, default_value: energyLevel },
    { 
      start_label: { zh: '精力不足', en: 'Low Energy' },
      end_label: { zh: '精力充沛', en: 'High Energy' },
      unit: '%'
    },
    'bamboo_slider'
  );

  // 睡眠质量滑块
  const sleepSlider = QuizComponentFactory.createSlider(
    { min: 0, max: 100, step: 10, default_value: sleepQuality },
    { 
      start_label: { zh: '睡眠很差', en: 'Poor Sleep' },
      end_label: { zh: '睡眠很好', en: 'Good Sleep' },
      unit: '分'
    },
    'river_flow_slider'
  );

  const handleSubmit = () => {
    const assessmentData = {
      constitution_type: selectedSymptoms,
      energy_level: energyLevel,
      sleep_quality: sleepQuality,
      timestamp: new Date().toISOString()
    };
    
    console.log('Assessment submitted:', assessmentData);
    // 提交到后端API
  };

  return (
    <div className="tcm-constitution-assessment" style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      {/* 页面标题 */}
      <TextComponent
        id="page-title"
        config={pageComponents[0]}
        content={pageComponents[0].content}
      />

      {/* 问题描述 */}
      <TextComponent
        id="question-description"
        config={pageComponents[1]}
        content={pageComponents[1].content}
        style={{ margin: '20px 0' }}
      />

      {/* 体质类型选择 */}
      <div style={{ marginBottom: '30px' }}>
        <TextComponent
          id="constitution-label"
          config={QuizComponentFactory.createText(
            { zh: '请选择最符合您的体质类型：', en: 'Please select the constitution type that best fits you:' },
            'question'
          )}
          content={{ text_localized: { zh: '请选择最符合您的体质类型：', en: 'Please select the constitution type that best fits you:' } }}
        />
        
        <SelectorComponent
          config={pageComponents[2]}
          options={pageComponents[2].options}
          selectedValues={selectedSymptoms}
          onSelectionChange={setSelectedSymptoms}
        />
      </div>

      {/* 能量水平评估 */}
      <div style={{ marginBottom: '30px' }}>
        <TextComponent
          id="energy-label"
          config={QuizComponentFactory.createText(
            { zh: '您的整体精力水平如何？', en: 'How is your overall energy level?' },
            'question'
          )}
          content={{ text_localized: { zh: '您的整体精力水平如何？', en: 'How is your overall energy level?' } }}
        />
        
        <SliderComponent
          config={energySlider}
          value={energyLevel}
          onChange={setEnergyLevel}
        />
      </div>

      {/* 睡眠质量评估 */}
      <div style={{ marginBottom: '30px' }}>
        <TextComponent
          id="sleep-label"
          config={QuizComponentFactory.createText(
            { zh: '您的睡眠质量如何？', en: 'How is your sleep quality?' },
            'question'
          )}
          content={{ text_localized: { zh: '您的睡眠质量如何？', en: 'How is your sleep quality?' } }}
        />
        
        <SliderComponent
          config={sleepSlider}
          value={sleepQuality}
          onChange={setSleepQuality}
        />
      </div>

      {/* 提交按钮 */}
      <div style={{ textAlign: 'center', marginTop: '40px' }}>
        <ButtonComponent
          config={QuizComponentFactory.createButton(
            { zh: '提交评估结果', en: 'Submit Assessment' },
            'confirm_tcm'
          )}
          onClick={handleSubmit}
          disabled={selectedSymptoms.length === 0}
        />
      </div>

      {/* 提示信息 */}
      <TextComponent
        id="hint-text"
        config={QuizComponentFactory.createText(
          { zh: '评估结果将用于为您生成个性化的健康建议', en: 'Assessment results will be used to generate personalized health recommendations for you' },
          'hint'
        )}
        content={{ text_localized: { zh: '评估结果将用于为您生成个性化的健康建议', en: 'Assessment results will be used to generate personalized health recommendations for you' } }}
        style={{ marginTop: '20px' }}
      />
    </div>
  );
};

export default TCMConstitutionAssessment;
```

## 🎯 最佳实践

### 1. 组件选择指南

- **文本组件**: 
  - 使用 `question` 预设用于问题文本
  - 使用 `scroll_text` 用于古典内容
  - 使用 `inscription_text` 用于重要声明
  - 使用 `hint` 用于提示信息

- **滑块组件**:
  - 使用 `bamboo_slider` 用于中医相关评估
  - 使用 `ink_brush_slider` 用于情绪深度
  - 使用 `standard_slider` 用于通用数值

- **按钮组件**:
  - 使用 `confirm_tcm` 用于重要确认操作
  - 使用 `special_action` 用于特殊功能
  - 使用 `navigation` 用于页面导航

### 2. 性能优化建议

- 使用 `React.memo` 包装组件避免不必要的重渲染
- 合理使用 `onChangeComplete` 而不是 `onChange` 来减少API调用
- 对于复杂的滑块交互，考虑使用防抖处理

### 3. 可访问性建议

- 始终提供有意义的 `aria-label`
- 确保键盘导航功能正常
- 为视觉障碍用户提供文本替代方案

这些示例展示了Quiz组件系统的强大功能和灵活性，帮助开发者快速构建专业的中医量表界面！🎉
