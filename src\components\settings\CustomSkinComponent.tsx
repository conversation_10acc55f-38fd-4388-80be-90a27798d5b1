import { SkinPreview } from '@/components/preview/SkinPreview';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useLanguage } from '@/contexts/LanguageContext';
import type { Skin, SkinConfig } from '@/types/skinTypes';
import { CustomWheelManager } from '@/utils/customWheelManager';
import { DataAdapters } from '@/utils/dataAdapters';
import { Services } from '@/services';
import { ArrowUpRight, Brush, Compass, Copy, Download, Plus, Save, Upload } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import { toast } from 'sonner';

interface CustomSkinComponentProps {
  onSkinCreated?: (skinId: string) => void;
}

/**
 * 自定义皮肤组件
 * 用于创建和管理自定义皮肤
 */
const CustomSkinComponent: React.FC<CustomSkinComponentProps> = ({ onSkinCreated }) => {
  const { t } = useLanguage();
  const [customWheelManager] = useState(() => new CustomWheelManager());
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isMigrateDialogOpen, setIsMigrateDialogOpen] = useState(false);
  const [newSkinData, setNewSkinData] = useState({
    name: '',
    description: '',
    basedOnId: '',
  });
  const [importData, setImportData] = useState('');
  const [importError, setImportError] = useState('');
  const [customWheels, setCustomWheels] = useState(customWheelManager.getCustomWheels());
  const [selectedWheelId, setSelectedWheelId] = useState<string | null>(null);

  // 处理创建新皮肤
  const handleCreateSkin = () => {
    if (!newSkinData.name.trim()) {
      toast.error(t('settings.custom_skin.name_required', '请输入皮肤名称'));
      return;
    }

    try {
      let newSkin: Skin;

      if (newSkinData.basedOnId) {
        // 基于现有皮肤创建
        const baseSkin = skinManager.getSkinById(newSkinData.basedOnId);
        if (!baseSkin) {
          toast.error(t('settings.custom_skin.base_skin_not_found', '基础皮肤不存在'));
          return;
        }

        newSkin = skinManager.duplicateSkin(newSkinData.basedOnId, newSkinData.name) as Skin;
        if (newSkinData.description) {
          skinManager.updateSkin(newSkin.id, { description: newSkinData.description });
        }
      } else {
        // 创建全新皮肤
        newSkin = skinManager.createCustomSkin(newSkinData.name, newSkinData.description);
      }

      setIsCreateDialogOpen(false);
      setNewSkinData({
        name: '',
        description: '',
        basedOnId: '',
      });

      toast.success(t('settings.custom_skin.create_success', '创建皮肤成功'));

      if (onSkinCreated) {
        onSkinCreated(newSkin.id);
      }
    } catch (error) {
      console.error('Failed to create custom skin:', error);
      toast.error(t('settings.custom_skin.create_error', '创建皮肤失败'));
    }
  };

  // 处理导入皮肤
  const handleImportSkin = async () => {
    if (!importData.trim()) {
      setImportError(t('settings.custom_skin.import_data_required', '请输入导入数据'));
      return;
    }

    try {
      const parsedData = JSON.parse(importData);

      // 验证数据结构
      if (!parsedData.name || !parsedData.config) {
        setImportError(t('settings.custom_skin.import_invalid_data', '导入数据无效'));
        return;
      }

      // 使用服务创建皮肤
      const skinService = await Services.skin();
      const result = await skinService.create({
        name: parsedData.name,
        description: parsedData.description || '导入的自定义皮肤',
        category: 'custom',
        config: parsedData.config,
        is_unlocked: true,
        author: 'user'
      });

      if (result.success && result.data) {
        setIsImportDialogOpen(false);
        setImportData('');
        setImportError('');

        toast.success(t('settings.custom_skin.import_success', '导入皮肤成功'));

        if (onSkinCreated) {
          onSkinCreated(result.data.id);
        }
      } else {
        setImportError(result.error || '导入失败');
      }
    } catch (error) {
      console.error('Failed to import skin:', error);
      setImportError(t('settings.custom_skin.import_error', '导入失败，请检查数据格式'));
    }
  };

  // 处理从自定义轮盘迁移
  const handleMigrateFromWheel = () => {
    if (!selectedWheelId) {
      toast.error(t('settings.custom_skin.select_wheel', '请选择一个自定义轮盘'));
      return;
    }

    try {
      // 使用 find 方法从 customWheels 中查找指定 ID 的轮盘
      const wheel = customWheels.find((wheel) => wheel.id === selectedWheelId);
      if (!wheel) {
        toast.error(t('settings.custom_skin.wheel_not_found', '自定义轮盘不存在'));
        return;
      }

      // 创建新的皮肤配置
      const skinConfig: SkinConfig = DataAdapters.createDefaultSkinConfig();

      // 如果轮盘有关联的皮肤ID，尝试获取该皮肤的配置
      if (wheel.skinId) {
        const baseSkin = skinManager.getSkinById(wheel.skinId);
        if (baseSkin) {
          skinConfig.colors = { ...baseSkin.config.colors };
          skinConfig.fonts = { ...baseSkin.config.fonts };
          skinConfig.effects = { ...baseSkin.config.effects };
        }
      }

      // 创建新的自定义皮肤
      const newSkin = skinManager.createCustomSkin(
        `${wheel.name} 皮肤`,
        `从自定义轮盘 "${wheel.name}" 迁移的皮肤`
      );

      // 更新皮肤配置
      skinManager.updateSkinConfig(newSkin.id, skinConfig);

      setIsMigrateDialogOpen(false);
      setSelectedWheelId(null);

      toast.success(t('settings.custom_skin.migrate_success', '从自定义轮盘迁移成功'));

      if (onSkinCreated) {
        onSkinCreated(newSkin.id);
      }
    } catch (error) {
      console.error('Failed to migrate from custom wheel:', error);
      toast.error(t('settings.custom_skin.migrate_error', '迁移失败'));
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <p className="text-sm text-muted-foreground">
          {t(
            'settings.custom_skin.description',
            '创建和管理您自己的自定义皮肤，您可以从头开始创建，也可以基于现有皮肤进行修改。'
          )}
        </p>

        <div className="flex flex-wrap gap-2 mt-2">
          <Button variant="default" onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            {t('settings.custom_skin.create', '创建自定义皮肤')}
          </Button>

          <Button variant="outline" onClick={() => setIsImportDialogOpen(true)}>
            <Upload className="h-4 w-4 mr-2" />
            {t('settings.custom_skin.import', '导入皮肤')}
          </Button>

          <Button variant="outline" onClick={() => setIsMigrateDialogOpen(true)}>
            <ArrowUpRight className="h-4 w-4 mr-2" />
            {t('settings.custom_skin.migrate_from_wheel', '从自定义轮盘迁移')}
          </Button>
        </div>
      </div>

      {/* 创建皮肤对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('settings.custom_skin.create_title', '创建自定义皮肤')}</DialogTitle>
            <DialogDescription>
              {t(
                'settings.custom_skin.create_description',
                '创建您自己的自定义皮肤，您可以从头开始创建，也可以基于现有皮肤进行修改。'
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="skin-name">{t('settings.custom_skin.name', '皮肤名称')}</Label>
              <Input
                id="skin-name"
                value={newSkinData.name}
                onChange={(e) => setNewSkinData({ ...newSkinData, name: e.target.value })}
                placeholder={t('settings.custom_skin.name_placeholder', '输入皮肤名称')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="skin-description">
                {t('settings.custom_skin.description_field', '描述')}
              </Label>
              <Textarea
                id="skin-description"
                value={newSkinData.description}
                onChange={(e) => setNewSkinData({ ...newSkinData, description: e.target.value })}
                placeholder={t(
                  'settings.custom_skin.description_placeholder',
                  '输入皮肤描述（可选）'
                )}
              />
            </div>

            {/* 基于现有皮肤选择器 - 实际实现中应该有一个下拉菜单 */}
            {/* 这里简化为一个按钮，表示功能即将推出 */}
            <Button
              variant="outline"
              className="w-full"
              onClick={() =>
                toast.info(t('settings.feature_coming_soon', '此功能即将推出'), { duration: 3000 })
              }
            >
              <Copy className="h-4 w-4 mr-2" />
              {t('settings.custom_skin.based_on', '基于现有皮肤')}
            </Button>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              {t('common.cancel', '取消')}
            </Button>
            <Button onClick={handleCreateSkin}>
              {t('settings.custom_skin.create_button', '创建皮肤')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导入皮肤对话框 */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('settings.custom_skin.import_title', '导入皮肤')}</DialogTitle>
            <DialogDescription>
              {t('settings.custom_skin.import_description', '粘贴皮肤数据以导入皮肤。')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="import-data">
                {t('settings.custom_skin.import_data', '导入数据')}
              </Label>
              <Textarea
                id="import-data"
                value={importData}
                onChange={(e) => {
                  setImportData(e.target.value);
                  setImportError('');
                }}
                placeholder={t(
                  'settings.custom_skin.import_data_placeholder',
                  '粘贴皮肤数据（JSON格式）'
                )}
                rows={10}
              />
              {importError && <p className="text-sm text-red-500">{importError}</p>}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              {t('common.cancel', '取消')}
            </Button>
            <Button onClick={handleImportSkin}>
              {t('settings.custom_skin.import_button', '导入')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 从自定义轮盘迁移对话框 */}
      <Dialog open={isMigrateDialogOpen} onOpenChange={setIsMigrateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('settings.custom_skin.migrate_title', '从自定义轮盘迁移')}</DialogTitle>
            <DialogDescription>
              {t(
                'settings.custom_skin.migrate_description',
                '选择一个自定义轮盘，将其配置迁移到新的自定义皮肤。'
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* 自定义轮盘选择器 - 实际实现中应该有一个下拉菜单 */}
            {/* 这里简化为一个按钮，表示功能即将推出 */}
            <Button
              variant="outline"
              className="w-full"
              onClick={() =>
                toast.info(t('settings.feature_coming_soon', '此功能即将推出'), { duration: 3000 })
              }
            >
              <Compass className="h-4 w-4 mr-2" />
              {t('settings.custom_skin.select_wheel', '选择自定义轮盘')}
            </Button>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsMigrateDialogOpen(false)}>
              {t('common.cancel', '取消')}
            </Button>
            <Button onClick={handleMigrateFromWheel}>
              {t('settings.custom_skin.migrate_button', '迁移')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CustomSkinComponent;
