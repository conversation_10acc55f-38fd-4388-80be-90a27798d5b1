/**
 * 情绪数据编辑器组件
 * 用于创建和编辑情绪数据
 */

import { useLanguage } from '@/contexts/LanguageContext';
import type { EmotionDataSet as EmotionData } from '@/types';
import type { Skin } from '@/types';
import { AlertTriangle, Copy, Download, Plus, Save, Trash2, Upload } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { SkinPreview } from '../preview/SkinPreview';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Textarea } from '../ui/textarea';
import { TierEditor } from './TierEditor';

interface EmotionDataEditorProps {
  emotionData?: EmotionData;
  skin?: Skin;
  onSave?: (data: EmotionData) => void;
  onCancel?: () => void;
  onDelete?: (id: string) => void;
  onDuplicate?: (id: string) => void;
  onImport?: (data: any) => EmotionData | null;
  onExport?: (id: string) => any;
  onActivate?: (id: string) => void;
}

/**
 * 情绪数据编辑器组件
 */
export const EmotionDataEditor: React.FC<EmotionDataEditorProps> = ({
  emotionData,
  skin,
  onSave,
  onCancel,
  onDelete,
  onDuplicate,
  onImport,
  onExport,
  onActivate,
}) => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('general');

  // 处理选项卡切换
  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // 如果切换到 Tiers 选项卡，且是移动端，则重置 TierEditor 组件
    if (value === 'tiers' && isMobile) {
      setTierEditorKey((prev) => prev + 1);
    }
  };
  const [isImporting, setIsImporting] = useState(false);
  const [importData, setImportData] = useState('');
  const [importError, setImportError] = useState('');

  // 检测是否为移动端
  const [isMobile, setIsMobile] = useState(
    typeof window !== 'undefined' && window.innerWidth < 768
  );

  // 用于重置 TierEditor 组件的状态
  const [tierEditorKey, setTierEditorKey] = useState(0);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 编辑状态
  const [editingData, setEditingData] = useState<EmotionData | null>(null);
  const isCreating = !emotionData;

  // 检查是否为系统默认数据（不可编辑）
  const isSystemDefault = emotionData?.isSystemDefault;

  // 初始化编辑数据
  useEffect(() => {
    if (emotionData) {
      setEditingData({ ...emotionData });
    } else {
      // 创建新的情绪数据
      setEditingData({
        id: '',
        name: t('emotion_editor.new_emotion_data', { fallback: '新情绪数据' }),
        description: '',
        tiers: [],
        isActive: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    }
  }, [emotionData, t]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!editingData) return;

    setEditingData({
      ...editingData,
      [e.target.name]: e.target.value,
      updated_at: new Date().toISOString(),
    });
  };

  // 处理保存
  const handleSave = () => {
    if (!editingData) return;

    onSave?.(editingData);
  };

  // 处理删除
  const handleDelete = () => {
    if (!editingData || !editingData.id) return;

    onDelete?.(editingData.id);
  };

  // 处理复制
  const handleDuplicate = () => {
    if (!editingData || !editingData.id) return;

    // 获取当前语言
    const currentLanguage = t.language || 'zh';

    // 调用复制函数，传入当前语言
    onDuplicate?.(editingData.id, currentLanguage);
  };

  // 处理导出
  const handleExport = () => {
    if (!editingData || !editingData.id || !onExport) return;

    const exportData = onExport(editingData.id);
    if (exportData) {
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

      const exportFileName = `${editingData.name.replace(/\s+/g, '_')}_emotions.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileName);
      linkElement.click();
    }
  };

  // 处理激活情绪数据
  const handleActivate = () => {
    if (!editingData || !editingData.id || editingData.is_active) return;

    onActivate?.(editingData.id);
  };

  // 处理导入
  const handleImport = () => {
    if (!onImport) return;

    try {
      const data = JSON.parse(importData);
      const importedData = onImport(data);

      if (importedData) {
        setIsImporting(false);
        setImportData('');
        setImportError('');
      } else {
        setImportError(t('emotion_editor.import_error', { fallback: '导入失败，请检查JSON格式' }));
      }
    } catch (error) {
      setImportError(t('emotion_editor.import_error', { fallback: '导入失败，请检查JSON格式' }));
    }
  };

  // 处理层级变化
  const handleTiersChange = (updatedTiers: any) => {
    if (!editingData) return;

    setEditingData({
      ...editingData,
      tiers: updatedTiers,
      updated_at: new Date().toISOString(),
    });
  };

  // 处理皮肤变化
  const [previewSkin, setPreviewSkin] = useState<Skin | undefined>(skin);

  // 当外部传入的skin变化时，更新previewSkin
  useEffect(() => {
    setPreviewSkin(skin);
  }, [skin]);

  // 处理皮肤变更
  const handleSkinChange = (skinId: string) => {
    // 这里可以根据skinId获取对应的皮肤，然后更新previewSkin
    // 由于我们没有完整的皮肤列表，这里只是一个示例
    console.log('Skin changed to:', skinId);
  };

  if (!editingData) {
    return (
      <Card className="h-full flex items-center justify-center">
        <CardContent className="text-center py-10">
          <p className="text-muted-foreground mb-4">
            {t('emotion_editor.loading', { fallback: '加载中...' })}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="emotion-data-editor">
      <Card>
        <CardHeader>
          <CardTitle>
            {isCreating
              ? t('emotion_editor.create_data', { fallback: '创建情绪数据' })
              : t('emotion_editor.edit_data', { fallback: '编辑情绪数据' })}
          </CardTitle>
          <CardDescription>
            {editingData.name}
            {isCreating && editingData.id === '' && (
              <span className="ml-2 text-xs bg-primary/10 px-2 py-0.5 rounded-full">
                {t('emotion_editor.from_template', { fallback: 'From Template' })}
              </span>
            )}
          </CardDescription>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="mb-4">
              <TabsTrigger value="general">{t('common.general', { fallback: '常规' })}</TabsTrigger>
              <TabsTrigger value="tiers">{t('common.tiers', { fallback: '层级' })}</TabsTrigger>
              <TabsTrigger value="preview">{t('common.preview', { fallback: '预览' })}</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t('common.name', { fallback: '名称' })}</Label>
                <Input
                  id="name"
                  name="name"
                  value={editingData.name}
                  onChange={handleInputChange}
                  placeholder={t('emotion_editor.name_placeholder', {
                    fallback: '输入情绪数据名称',
                  })}
                  disabled={isSystemDefault}
                />
                {isSystemDefault && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('emotion_editor.system_default_name_hint', {
                      fallback: '系统默认情绪数据名称不可修改',
                    })}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('common.description', { fallback: '描述' })}</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={editingData.description || ''}
                  onChange={handleInputChange}
                  placeholder={t('emotion_editor.description_placeholder', {
                    fallback: '输入情绪数据描述',
                  })}
                  disabled={isSystemDefault}
                />
                {isSystemDefault && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('emotion_editor.system_default_description_hint', {
                      fallback: '系统默认情绪数据描述不可修改',
                    })}
                  </p>
                )}
              </div>

              {!isCreating && (
                <div className="pt-4 border-t">
                  <div className="flex flex-col gap-2">
                    <Button
                      variant={editingData.is_active ? 'secondary' : 'default'}
                      className="w-full justify-center"
                      disabled={editingData.is_active}
                      onClick={handleActivate}
                    >
                      {editingData.is_active
                        ? t('settings.emotion_data.current', { fallback: 'Currently in use' })
                        : t('settings.emotion_data.use', { fallback: 'Use this data' })}
                    </Button>

                    <div className="flex gap-2 w-full">
                      <Button variant="outline" className="flex-1" onClick={handleDuplicate}>
                        <Copy className="h-4 w-4 mr-1" />
                        {t('common.duplicate', { fallback: '复制' })}
                      </Button>

                      <Button variant="outline" className="flex-1" onClick={handleExport}>
                        <Download className="h-4 w-4 mr-1" />
                        {t('common.export', { fallback: '导出' })}
                      </Button>
                    </div>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" className="w-full text-destructive">
                          <Trash2 className="h-4 w-4 mr-1" />
                          {t('common.delete', { fallback: '删除' })}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            {t('common.confirm_delete', { fallback: '确认删除' })}
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            {t('emotion_editor.delete_warning', {
                              fallback: '此操作将删除此情绪数据，无法恢复。',
                            })}
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>
                            {t('common.cancel', { fallback: '取消' })}
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleDelete}
                            className="bg-destructive text-destructive-foreground"
                          >
                            {t('common.delete', { fallback: '删除' })}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="tiers">
              {isSystemDefault ? (
                <div className="p-4 border rounded-md bg-muted/20">
                  <div className="flex items-center gap-2 mb-4">
                    <AlertTriangle className="h-5 w-5 text-amber-500" />
                    <h3 className="font-medium">
                      {t('emotion_editor.system_default_tiers_title', {
                        fallback: '系统默认情绪数据',
                      })}
                    </h3>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">
                    {t('emotion_editor.system_default_tiers_message', {
                      fallback:
                        '系统默认情绪数据的层级和情绪不可修改。如果需要自定义情绪数据，请点击下方的"创建副本"按钮创建一个可编辑的副本。',
                    })}
                  </p>
                  <Button variant="outline" onClick={handleDuplicate} className="mt-2">
                    <Copy className="h-4 w-4 mr-2" />
                    {t('emotion_editor.create_copy', { fallback: '创建副本' })}
                  </Button>
                </div>
              ) : (
                <TierEditor
                  key={`tier-editor-${tierEditorKey}`}
                  emotionData={editingData}
                  onChange={handleTiersChange}
                  isMobileView={isMobile}
                />
              )}
            </TabsContent>

            <TabsContent value="preview" className="overflow-x-hidden">
              {previewSkin && (
                <div className="mt-2">
                  <SkinPreview
                    emotionData={editingData}
                    skin={previewSkin}
                    viewType="wheel"
                    contentDisplayMode="textEmoji"
                    RenderEngine="D3"
                    onSkinChange={handleSkinChange}
                  />
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between">
          <div>
            {!isCreating && isSystemDefault && (
              <div className="flex items-center text-sm text-muted-foreground">
                <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
                {t('emotion_editor.system_default_hint', { fallback: '系统默认情绪数据不可修改' })}
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onCancel}>
              {t('common.cancel', { fallback: '取消' })}
            </Button>

            {isSystemDefault ? (
              <Button onClick={handleDuplicate}>
                <Copy className="h-4 w-4 mr-1" />
                {t('emotion_editor.create_copy', { fallback: '创建副本' })}
              </Button>
            ) : (
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-1" />
                {t('common.save', { fallback: '保存' })}
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>

      {/* 导入对话框 */}
      <Dialog open={isImporting} onOpenChange={setIsImporting}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('common.import', { fallback: '导入' })}</DialogTitle>
            <DialogDescription>
              {t('emotion_editor.import_description', { fallback: '粘贴JSON格式的情绪数据' })}
            </DialogDescription>
          </DialogHeader>

          <Textarea
            value={importData}
            onChange={(e) => setImportData(e.target.value)}
            placeholder={t('common.paste_json', { fallback: '粘贴JSON数据' })}
            className="min-h-[200px]"
          />

          {importError && <p className="text-destructive text-sm">{importError}</p>}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImporting(false)}>
              {t('common.cancel', { fallback: '取消' })}
            </Button>
            <Button onClick={handleImport}>{t('common.import', { fallback: '导入' })}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
