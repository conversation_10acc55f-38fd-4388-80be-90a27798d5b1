# 配置系统迁移指南

## 📋 概述

本文档详细说明了从旧配置系统到新配置系统的迁移过程，包括数据迁移、代码更新和测试验证。

## 🔄 迁移概览

### 旧配置系统 → 新配置系统

```
旧系统 (已弃用)
├── user_configs (混合配置)
├── user_presentation_configs (展现配置)
├── pack_presentation_overrides (包覆盖)
├── quiz_session_presentation_configs (会话配置)
└── session_presentation_snapshots (快照)

新系统 (当前)
├── user_configs (全局应用设置)
├── user_quiz_preferences (Quiz系统配置)
├── question_presentation_overrides (问题级emoji映射) **新增**
├── pack_presentation_configs (Quiz包默认配置) **新增**
├── quiz_pack_overrides (包覆盖配置)
└── quiz_session_configs (会话配置)
```

## 🎯 主要变更

### 1. 配置分离架构
- **全局应用设置**: 主题、语言、通知、音效、无障碍
- **Quiz系统配置**: 6层个性化配置，完全独立管理

### 2. 数据表结构变更
```sql
-- 旧表结构 (已弃用)
user_configs: 混合了全局设置和Quiz配置
user_presentation_configs: 复杂的展现配置
pack_presentation_overrides: 包覆盖配置
quiz_session_presentation_configs: 会话配置
session_presentation_snapshots: 配置快照

-- 新表结构 (当前)
user_configs: 仅全局应用设置
user_quiz_preferences: Quiz系统6层配置 (包含emoji映射)
question_presentation_overrides: 问题级emoji映射覆盖 **新增**
pack_presentation_configs: Quiz包默认emoji映射配置 **新增**
quiz_pack_overrides: 简化的包覆盖配置
quiz_session_configs: 最终会话配置
```

### 3. Hook接口变更
```typescript
// 旧接口 (已弃用)
useUserConfig() // 混合配置Hook

// 新接口 (当前)
useGlobalConfig() // 全局应用配置Hook
useQuizConfig()   // Quiz系统配置Hook
```

## 📊 数据迁移

### 1. 清除旧配置数据
```sql
-- 清除旧的配置表数据
DELETE FROM user_configs WHERE id LIKE 'config-%';
DELETE FROM user_presentation_configs;
DELETE FROM pack_presentation_overrides;
DELETE FROM quiz_session_presentation_configs;
DELETE FROM session_presentation_snapshots;
```

### 2. 初始化新配置系统
```sql
-- 运行新配置系统初始化
.read public/seeds/config/new_config_system.sql
.read public/seeds/config/global_app_configs.sql
.read public/seeds/config/quiz_preferences_configs.sql
.read public/seeds/config/quiz_pack_overrides_new.sql
.read public/seeds/config/mood-wheel-quiz-packs.sql  -- **新增**: 包含emoji映射配置
.read public/seeds/test-user-data/test_users_data.sql  -- **新增**: 包含问题级emoji覆盖示例
```

### 3. 数据映射规则

#### 全局应用设置映射
```typescript
// 旧数据 → 新数据
{
  // 从旧 user_configs 提取
  preferred_view_type → 移动到 user_quiz_preferences
  active_skin_id → 移动到 user_quiz_preferences
  dark_mode → 转换为 theme_mode
  language → 保留在 user_configs
  
  // 新增字段
  notifications_enabled → 新字段
  sound_enabled → 新字段
  accessibility → JSON格式的无障碍配置
}
```

#### Quiz配置映射
```typescript
// 旧数据 → 新数据
{
  // 从旧 user_presentation_configs 转换
  layer0_dataset_presentation → 新的6层配置结构
  layer1_user_choice → 包含 preferred_view_type, active_skin_id
  layer2_rendering_strategy → 渲染引擎和性能配置
  layer3_skin_base → 皮肤和颜色配置
  layer4_view_detail → 视图细节配置 + emoji映射配置 **新增**
  layer5_accessibility → 无障碍增强配置
}
```

## 🔧 代码迁移

### 1. Hook使用迁移

#### Settings页面
```typescript
// 旧代码 (已弃用)
import { useUserConfig } from '@/contexts/UserConfigContext';

const { userConfig, updateConfig } = useUserConfig();

// 新代码 (当前)
import { useGlobalConfig } from '@/hooks/useGlobalConfig';

const {
  themeMode,
  language,
  notificationsEnabled,
  soundEnabled,
  accessibilityConfig,
  updateConfig
} = useGlobalConfig();
```

#### QuizSettings页面
```typescript
// 旧代码 (已弃用)
import { useUserConfig } from '@/contexts/UserConfigContext';

const { userConfig, setColorMode } = useUserConfig();

// 新代码 (当前)
import { useQuizConfig } from '@/hooks/useQuizConfig';

const {
  preferredViewType,
  colorMode,
  personalizationLevel,
  updatePresentationConfig
} = useQuizConfig();
```

### 2. 服务层迁移

#### 旧服务 (已弃用)
```typescript
// 不再使用
UserConfigService
UserPresentationConfigService
PackPresentationOverrideService
```

#### 新服务 (当前)
```typescript
// 使用新服务
GlobalAppConfigService
UserQuizPreferencesService
QuizPackOverridesService
QuizConfigMergerService
EmojiMappingService  // **新增**: emoji映射管理服务
```

### 3. tRPC路由迁移

#### 旧路由 (已弃用)
```typescript
// 不再使用
config.getUserConfig
config.updateUserConfig
```

#### 新路由 (当前)
```typescript
// 使用新路由
config.global.getUserConfig
config.global.updateUserConfig
config.quiz.getUserPreferences
config.quiz.updateUserPreferences
config.quiz.generateSessionConfig
config.emoji.getOptionPresentation  // **新增**: emoji映射API
config.emoji.updateUserEmojiMapping  // **新增**: 用户emoji映射API
config.emoji.updateQuestionEmojiOverride  // **新增**: 问题emoji覆盖API
```

## 🧪 测试验证

### 1. 数据完整性验证
```sql
-- 验证新配置系统数据
SELECT 'Global Configs: ' || COUNT(*) FROM user_configs;
SELECT 'Quiz Preferences: ' || COUNT(*) FROM user_quiz_preferences;
SELECT 'Question Overrides: ' || COUNT(*) FROM question_presentation_overrides;  -- **新增**
SELECT 'Pack Configs: ' || COUNT(*) FROM pack_presentation_configs;  -- **新增**
SELECT 'Pack Overrides: ' || COUNT(*) FROM quiz_pack_overrides;
SELECT 'Session Configs: ' || COUNT(*) FROM quiz_session_configs;
```

### 2. 功能测试清单
- [ ] 全局主题切换功能
- [ ] 语言切换功能
- [ ] 通知设置功能
- [ ] 音效设置功能
- [ ] 无障碍配置功能
- [ ] Quiz视图类型切换
- [ ] Quiz颜色模式切换
- [ ] Quiz个性化级别调整
- [ ] Emoji映射配置功能 **新增**
- [ ] 问题级emoji覆盖功能 **新增**
- [ ] Quiz包默认emoji映射 **新增**
- [ ] 配置自动同步功能
- [ ] 离线配置管理

### 3. Hook测试
```typescript
// 测试全局配置Hook
const globalConfig = useGlobalConfig();
expect(globalConfig.themeMode).toBeDefined();
expect(globalConfig.updateConfig).toBeFunction();

// 测试Quiz配置Hook
const quizConfig = useQuizConfig();
expect(quizConfig.preferredViewType).toBeDefined();
expect(quizConfig.updatePresentationConfig).toBeFunction();
```

## 🚨 注意事项

### 1. 破坏性变更
- 旧的Hook接口不再可用
- 旧的配置数据表结构已变更
- 旧的tRPC路由已重构

### 2. 兼容性处理
- 提供迁移脚本自动转换数据
- 保留旧数据备份以防回滚
- 渐进式迁移，避免服务中断

### 3. 性能影响
- 新配置系统采用离线优先架构
- 配置合并逻辑在本地执行
- 网络同步仅在必要时进行

## 📚 相关文档

- [配置系统实现总结](./config-system-implementation.md)
- [配置系统API文档](../api/config-system-api-updated.md)
- [用户个性化配置指南](../quiz/user-personalization-guide.md)
- [类型系统架构文档](../quiz/type-system-architecture.md)

## 🎉 迁移完成检查

迁移完成后，确认以下项目：

- [ ] 所有旧配置数据已清除
- [ ] 新配置系统数据已初始化
- [ ] Hook接口已更新到新版本
- [ ] 页面组件已使用新Hook
- [ ] tRPC路由已更新
- [ ] 测试用例已通过
- [ ] 文档已更新

完成以上检查后，新配置系统迁移即告完成！
