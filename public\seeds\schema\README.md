# Quiz System Database Schema v2.0

This directory contains the updated SQL schema files for the Mindful Mood Mobile quiz system, implementing the new data-presentation separation architecture.

## 🎯 Architecture Overview

The new schema implements a complete separation between data and presentation layers:

- **Data Layer**: Pure quiz content (packs, questions, options) without any presentation logic
- **Presentation Layer**: User personalization configs and session snapshots for UI rendering
- **Session Layer**: Quiz sessions, answers, and results with proper data integrity

## 📁 Files Overview

```
public/seeds/
├── schema/                      # Database schema definitions
│   ├── quiz_system_v2.sql      # Complete database schema (all tables)
│   ├── quiz_indexes_v2.sql     # Optimized indexes for performance
│   ├── quiz_sample_data.sql    # Sample quiz data for development
│   ├── migration_v2.sql        # Migration script from v1 to v2
│   └── README.md               # This documentation
├── config/                     # System configuration data
│   └── system_config_data.sql  # App settings, UI labels, skins, emotions
└── test-user-data/             # Test user data
    └── test_users_data.sql     # Sample users and configurations
```

## 🚀 New Features

- **Universal Quiz Support**: Supports all mainstream and alternative quiz types
- **Data-Presentation Separation**: Complete separation of content and UI configuration
- **6-Layer Personalization**: Comprehensive user customization system
- **Session Management**: Robust quiz session tracking and state management
- **Advanced Analytics**: Built-in emotion pattern analysis and recommendations

## 🚀 Usage

### New Installation
```bash
# 1. Create complete database schema (all tables)
sqlite3 your_database.db < public/seeds/schema/quiz_system_v2.sql

# 2. Add performance indexes
sqlite3 your_database.db < public/seeds/schema/quiz_indexes_v2.sql

# 3. Load system configuration data
sqlite3 your_database.db < public/seeds/config/system_config_data.sql

# 4. Load sample quiz data (optional, for development)
sqlite3 your_database.db < public/seeds/schema/quiz_sample_data.sql

# 5. Load test user data (optional, for development)
sqlite3 your_database.db < public/seeds/test-user-data/test_users_data.sql
```

### Migration from v1
```bash
# Migrate existing database to v2 schema
sqlite3 your_database.db < public/seeds/schema/migration_v2.sql

# Add performance indexes
sqlite3 your_database.db < public/seeds/schema/quiz_indexes_v2.sql

# Load system configuration data
sqlite3 your_database.db < public/seeds/config/system_config_data.sql
```

## 📊 Schema Overview

### 🏗️ Core Infrastructure Tables
- **users** - User management with VIP support and activity tracking
- **app_settings** - Global application configuration settings
- **ui_labels** & **ui_label_translations** - Full internationalization support
- **global_app_configs** - User global preferences (theme, language, notifications)
- **user_streaks** - User engagement and streak tracking

### 🎯 Quiz System Tables
- **quiz_packs** - Quiz definitions and metadata (supports all quiz types)
- **quiz_questions** - Individual questions with type-specific configurations
- **quiz_question_options** - Answer options supporting various input types
- **quiz_sessions** - Session management and progress tracking
- **quiz_answers** - User responses with detailed analytics
- **quiz_results** - Comprehensive result analysis and recommendations

### 🎨 Presentation System Tables (6-Layer Configuration)
- **user_quiz_preferences** - 6-layer Quiz personalization system
- **quiz_pack_overrides** - Quiz-specific presentation customizations
- **quiz_session_presentation_configs** - Session configuration snapshots

### 🎨 Visual Customization Tables
- **skins** - Theme and visual customization system
- **emoji_sets** & **emoji_set_translations** - Emoji collection management
- **emotions** & **emotion_translations** - Core emotion definitions with i18n
- **emoji_items** - Individual emoji mappings to emotions

### 📈 Analytics Tables
- **emotion_pattern_analyses** - Advanced emotion pattern recognition
- **recommendations** - AI-powered personalized recommendations

## 🔧 Key Features

### Data-Presentation Separation
- Pure data storage without UI logic
- Complete presentation customization through configuration
- Session-based configuration snapshots for consistency

### Universal Quiz Support
- **Mainstream**: emotion_wheel, personality_test, iq_test, knowledge_quiz, survey
- **Alternative**: text_input, drawing, ranking, matrix, multimedia_choice
- **Mixed Types**: Support for multiple question types in single quiz
- **Custom Extensions**: Experimental and culture-specific quiz types

### Advanced Personalization
- 6-layer configuration system (User → Device → Context → View → Component → Element)
- Per-quiz presentation overrides
- Accessibility and performance optimizations
- Multi-modal content support (text, emoji, image, animation)

## 🔄 Schema Loading Order

1. **Core Schema** (`quiz_system_v2.sql`) - All table definitions
2. **Performance Indexes** (`quiz_indexes_v2.sql`) - Optimized indexes
3. **Sample Data** (`quiz_sample_data.sql`) - Development and testing data

## 📊 Schema Version

- **Current Version**: 2.0
- **Architecture**: Data-Presentation Separation
- **Migration Path**: Automated migration from v1 via `migration_v2.sql`
- **Backward Compatibility**: Maintains data integrity during migration

## 🚨 Important Notes

- **Breaking Changes**: v2 is a major architectural update
- **Data Migration**: Use `migration_v2.sql` for safe v1 → v2 transition
- **Testing Required**: Thoroughly test after migration
- **Backup Recommended**: Always backup before migration

## 🔄 Maintenance and Updates

When updating the schema:
1. Modify the appropriate SQL files
2. Update indexes if needed (`quiz_indexes_v2.sql`)
3. Test schema integrity and performance
4. Update documentation and migration scripts
5. Consider backward compatibility and data migration strategies

## 📚 Related Documentation

- [Quiz System Architecture](../../../docs/quiz/04-database-design.md)
- [Data-Presentation Separation](../../../docs/quiz/02-data-presentation-separation.md)
- [Configuration Architecture](../../../docs/quiz/05-configuration-architecture-refactor.md)
