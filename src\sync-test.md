
要测试本地与云端的同步功能，您可以按照以下步骤进行：首先，在本地数据库中准备一些新的、尚未同步到云端的数据。然后，执行您在 `src/database/db.ts` 中实现的同步函数（例如 `syncOfflineDataToTurso`）。同步完成后，连接到您的 Turso 云数据库，检查之前准备的本地数据是否已成功写入云端对应的表中，并验证数据的完整性和准确性。同时，也可以检查本地数据的同步状态（如果您的应用有相关标记）是否已更新。


我需要查看 `MoodEntry` 类型的定义，以便创建用于测试同步功能的模拟数据。该类型定义位于 `src/types/mood.ts` 文件中。


toolName: view_files

status: success


filePath: d:\Download\audio-visual\heytcm\mindful-mood-mobile\src\types\mood.ts

我将创建一个新的测试文件 `src/tests/sync.test.ts`，用于编写测试本地到云端同步功能的代码。这个文件将包含准备模拟数据、调用同步函数以及验证数据是否已同步到云数据库的逻辑。


toolName: create_file

status: success


filePath: d:\Download\audio-visual\heytcm\mindful-mood-mobile\src\tests\sync.test.ts

content: // src/tests/sync.test.ts
import { syncOfflineDataToTurso, getClient, generateId, closeClient } from '../database/db';
import type { MoodEntry } from '../types/mood';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

async function runSyncTest() {
  console.log('Starting sync test...');

  // Ensure environment variables are set
  if (!import.meta.env.VITE_TURSO_DATABASE_URL || !import.meta.env.VITE_TURSO_AUTH_TOKEN) {
    console.error('Error: VITE_TURSO_DATABASE_URL and VITE_TURSO_AUTH_TOKEN must be set in your environment variables (e.g., in a .env file).');
    console.log('Please create a .env file in the project root with the following content:');
    console.log('VITE_TURSO_DATABASE_URL="your_turso_db_url"');
    console.log('VITE_TURSO_AUTH_TOKEN="your_turso_auth_token"');
    return;
  }

  const testUserId = `testUser_${generateId()}`;
  const entryId1 = generateId();
  const entryId2 = generateId();
  const tagId1 = `testTag_${generateId()}`;
  const tagId2 = `testTag_${generateId()}`;

  const mockEntries: MoodEntry[] = [
    {
      id: entryId1,
      user_id: testUserId,
      timestamp: new Date().toISOString(),
      emotions: {
        primary: 'happy',
        secondary: 'joyful',
        tertiary: 'ecstatic',
      },
      emotion_data_set_id: 'default',
      emotion_selections: [
        { emotion_id: 'happy', tier_level: 1 },
        { emotion_id: 'joyful', tier_level: 2 },
        { emotion_id: 'ecstatic', tier_level: 3 }
      ],
      intensity: 80,
      reflection: 'Had a great day!',
      tag_ids: `${tagId1},${tagId2}`,
    },
    {
      id: entryId2,
      user_id: testUserId,
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
      emotions: {
        primary: 'sad',
        secondary: 'gloomy',
        tertiary: 'depressed',
      },
      emotion_data_set_id: 'default',
      emotion_selections: [
        { emotion_id: 'sad', tier_level: 1 },
        { emotion_id: 'gloomy', tier_level: 2 },
        { emotion_id: 'depressed', tier_level: 3 }
      ],
      intensity: 30,
      reflection: 'Not feeling the best.',
      tag_ids: tagId1, // Single tag
    },
  ];

  console.log(`Preparing to sync ${mockEntries.length} entries for user ${testUserId}...`);

  try {
    // Step 1: Call the sync function
    await syncOfflineDataToTurso(testUserId, mockEntries);
    console.log('Sync function executed.');

    // Step 2: Verify data in Turso
    const tursoClient = await getClient();
    console.log('Connected to Turso for verification.');

    // Verify user
    const userResult = await tursoClient.execute({
      sql: 'SELECT * FROM users WHERE id = ?',
      args: [testUserId],
    });
    console.log('User verification result:', userResult.rows);
    if (userResult.rows.length === 0) {
      console.error(`FAIL: User ${testUserId} not found in Turso.`);
    } else {
      console.log(`SUCCESS: User ${testUserId} found in Turso.`);
    }

    // Verify mood entries
    console.log('\nVerifying mood entries...');
    for (const entry of mockEntries) {
      if (!entry.id) continue;
      const moodEntryResult = await tursoClient.execute({
        sql: 'SELECT * FROM mood_entries WHERE id = ? AND user_id = ?',
        args: [entry.id, testUserId],
      });
      console.log(`Mood entry ${entry.id} verification result:`, moodEntryResult.rows);
      if (moodEntryResult.rows.length === 0) {
        console.error(`FAIL: Mood entry ${entry.id} not found in Turso.`);
      } else {
        const dbEntry: any = moodEntryResult.rows[0];
        // Basic checks, can be expanded
        if (dbEntry.intensity !== entry.intensity || dbEntry.reflection !== entry.reflection) {
            console.error(`FAIL: Data mismatch for mood entry ${entry.id}.`);
        } else {
            console.log(`SUCCESS: Mood entry ${entry.id} found and basic data matches.`);
        }

        // Verify emotion selections
        if (entry.emotion_selections) {
          console.log(`\nVerifying emotion selections for mood entry ${entry.id}...`);
          for (const selection of entry.emotion_selections) {
            const selectionResult = await tursoClient.execute({
              sql: 'SELECT * FROM emotion_selections WHERE mood_entry_id = ? AND emotion_id = ? AND tier_level = ?',
              args: [entry.id, selection.emotion_id, selection.tier_level],
            });
            console.log(`Emotion selection (${entry.id}, ${selection.emotion_id}, ${selection.tier_level}) verification result:`, selectionResult.rows);
            if (selectionResult.rows.length === 0) {
              console.error(`FAIL: Emotion selection for mood entry ${entry.id}, emotion ${selection.emotion_id}, tier ${selection.tier_level} not found.`);
            } else {
              console.log(`SUCCESS: Emotion selection for mood entry ${entry.id}, emotion ${selection.emotion_id}, tier ${selection.tier_level} found.`);
            }
          }
        }
      }
    }

    // Verify tags and mood_entry_tags
    console.log('\nVerifying tags and associations...');
    const allTagIds = new Set<string>();
    mockEntries.forEach(entry => {
        if (entry.tag_ids) {
            entry.tag_ids.split(',').forEach(tid => allTagIds.add(tid.trim()));
        }
    });

    for (const tagId of Array.from(allTagIds)) {
        const tagResult = await tursoClient.execute({
            sql: 'SELECT * FROM tags WHERE id = ?',
            args: [tagId]
        });
        console.log(`Tag ${tagId} verification result:`, tagResult.rows);
        if (tagResult.rows.length === 0) {
            console.error(`FAIL: Tag ${tagId} not found in Turso.`);
        } else {
            console.log(`SUCCESS: Tag ${tagId} found in Turso.`);
        }
    }

    // Verify mood_entry_tags associations
    for (const entry of mockEntries) {
        if (!entry.id || !entry.tag_ids) continue;
        const entryTagIds = entry.tag_ids.split(',').map(tid => tid.trim());
        for (const tagId of entryTagIds) {
            const moodEntryTagResult = await tursoClient.execute({
                sql: 'SELECT * FROM mood_entry_tags WHERE mood_entry_id = ? AND tag_id = ?',
                args: [entry.id, tagId]
            });
            console.log(`Mood entry tag (${entry.id}, ${tagId}) verification result:`, moodEntryTagResult.rows);
            if (moodEntryTagResult.rows.length === 0) {
                console.error(`FAIL: Association between mood entry ${entry.id} and tag ${tagId} not found.`);
            } else {
                console.log(`SUCCESS: Association for mood entry ${entry.id} and tag ${tagId} found.`);
            }
        }
    }

    console.log('\nSync test verification completed.');

  } catch (error) {
    console.error('Sync test failed:', error);
  } finally {
    await closeClient();
    console.log('Turso client closed.');
    console.log('Test finished.');
  }
}

// Run the test
runSyncTest();

/*
To run this test:
1. Ensure you have ts-node installed (npm install -g ts-node) or your project is configured to run TS files.
   Alternatively, compile it to JS and run with Node.
2. Make sure your .env file is set up with VITE_TURSO_DATABASE_URL and VITE_TURSO_AUTH_TOKEN.
3. Execute from the project root: npx ts-node ./src/tests/sync.test.ts
   (If using Vite's env directly, you might need to run it in an environment where import.meta.env is populated, e.g. via Vitest or a custom script using Vite's APIs)

If 'import.meta.env' causes issues outside Vite's environment for a simple ts-node execution:
- You might need to use a library like 'dotenv' to load .env variables into process.env
- And then access them via process.env.VITE_TURSO_DATABASE_URL.
- For simplicity in this example, we assume an environment where import.meta.env is accessible or will be handled by the user's setup.
- A more robust solution for standalone scripts is to use `dotenv`:
  Install: npm install dotenv
  Then at the top of this file:
  import * as dotenv from 'dotenv';
  dotenv.config(); // Loads .env file into process.env
  // Then replace import.meta.env.VITE_TURSO_DATABASE_URL with process.env.VITE_TURSO_DATABASE_URL
*/


我已经为您创建了一个测试脚本，用于测试本地数据同步到 Turso 云数据库的功能。

**测试脚本位置：** <mcfile name="sync.test.ts" path="src/tests/sync.test.ts"></mcfile>

**脚本功能：**
1.  **准备模拟数据：** 脚本会生成一些模拟的 `MoodEntry` 数据，包括用户信息、情绪条目和标签。
2.  **执行同步：** 调用 <mcsymbol name="syncOfflineDataToTurso" filename="db.ts" path="src/database/db.ts" startline="84" type="function"></mcsymbol> 函数将这些模拟数据同步到您的 Turso 云数据库。
3.  **验证数据：** 连接到 Turso 数据库，并检查用户、情绪条目、标签以及它们之间的关联关系是否已成功写入，并进行基本的数据一致性校验。
4.  **输出结果：** 在控制台输出详细的同步和验证步骤及结果（成功或失败信息）。

**如何运行测试：**

1.  **设置环境变量：**
    确保您的项目根目录下有一个 `.env` 文件，并且其中包含了您的 Turso 数据库 URL 和认证令牌。如果文件不存在，请创建它，并填入以下内容（替换为您的实际凭据）：
    ```env
    VITE_TURSO_DATABASE_URL="your_turso_db_url"
    VITE_TURSO_AUTH_TOKEN="your_turso_auth_token"
    ```
    测试脚本 <mcfile name="sync.test.ts" path="src/tests/sync.test.ts"></mcfile> 会检查这些环境变量。

2.  **安装依赖 (如果尚未安装)：**
    测试脚本可能需要 `ts-node` 来直接运行 TypeScript 文件，以及 `dotenv` 来加载 `.env` 文件中的环境变量。如果您的项目中还没有这些依赖，可以考虑安装它们：
    ```bash
    npm install --save-dev ts-node dotenv
    ```
    (注意：脚本 <mcfile name="sync.test.ts" path="src/tests/sync.test.ts"></mcfile> 内的注释提到了 `dotenv` 的使用方法，如果直接使用 `ts-node` 运行且 `import.meta.env` 未按预期工作时，您可能需要按脚本中的注释修改代码以使用 `dotenv`。)

3.  **执行测试脚本：**
    在您的项目根目录下，打开终端并运行以下命令：
    ```bash
    npx ts-node ./src/tests/sync.test.ts
    ```
    如果您已经将项目编译为 JavaScript，或者有其他运行 TypeScript 脚本的配置（例如通过 Vitest 或自定义的 Vite 脚本），请使用相应的方法执行。

    脚本执行后，您将在控制台看到详细的日志，包括正在同步的数据、连接到 Turso 的状态以及每个数据项的验证结果。

这个测试脚本提供了一种自动化方式来验证 <mcsymbol name="syncOfflineDataToTurso" filename="db.ts" path="src/database/db.ts" startline="84" type="function"></mcsymbol> 函数是否按预期工作，确保您的本地数据能够正确同步到云端。

