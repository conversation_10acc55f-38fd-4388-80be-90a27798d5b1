-- =============================================
-- Quiz包覆盖配置测试数据 (新配置系统)
-- 对应数据库中的 quiz_pack_overrides 表
-- 
-- 包覆盖配置用于针对特定Quiz包的个性化设置
-- 优先级高于用户偏好配置，低于会话配置
-- =============================================

-- 插入Quiz包覆盖配置测试数据
INSERT OR IGNORE INTO quiz_pack_overrides (
    id,
    user_id,
    pack_id,
    presentation_overrides,
    override_reason,
    override_priority,
    is_active,
    created_at,
    updated_at
) VALUES
    -- 情绪轮盘包的特殊配置
    (
        'override_emotion_wheel_user_1',
        'test-user-1',
        'daily-mood-tracker',
        '{
            "layer1_user_choice": {
                "preferred_view_type": "wheel",
                "color_mode": "warm"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "wheel": "D3"
                },
                "performance_mode": "balanced"
            },
            "layer4_view_detail": {
                "wheel_config": {
                    "container_size": 350,
                    "wheel_radius": 175,
                    "emotion_display_mode": "hierarchical",
                    "show_labels": true,
                    "show_emojis": true
                }
            }
        }',
        'pack_requirement', -- 包特定需求
        5, -- override_priority
        1, -- is_active
        datetime('now', '-10 days'),
        datetime('now', '-2 hours')
    ),
    
    -- 中医体质评估包的特殊配置
    (
        'override_tcm_assessment_user_2',
        'test-user-2',
        'tcm-constitution-assessment',
        '{
            "layer1_user_choice": {
                "preferred_view_type": "card",
                "color_mode": "neutral"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "card": "CSS"
                },
                "performance_mode": "performance"
            },
            "layer3_skin_base": {
                "colors": {
                    "primary": "#8B5A2B",
                    "secondary": "#D2691E",
                    "accent": "#CD853F"
                }
            }
        }',
        'pack_requirement', -- 中医主题色彩要求
        6, -- override_priority
        1, -- is_active
        datetime('now', '-8 days'),
        datetime('now', '-1 day')
    ),
    
    -- 心理健康筛查包的无障碍配置
    (
        'override_mental_health_accessibility',
        'test-user-3',
        'mental-health-screening',
        '{
            "layer1_user_choice": {
                "preferred_view_type": "list"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "list": "CSS"
                },
                "performance_mode": "performance",
                "supported_content_types": {
                    "text": true,
                    "emoji": false,
                    "images": false,
                    "animations": false
                }
            },
            "layer5_accessibility": {
                "high_contrast": true,
                "large_text": true,
                "reduce_motion": true,
                "keyboard_navigation": true,
                "voice_guidance": true
            }
        }',
        'accessibility', -- 无障碍需求
        8, -- override_priority (高优先级)
        1, -- is_active
        datetime('now', '-5 days'),
        datetime('now', '-3 hours')
    ),
    
    -- VIP用户的高级Quiz包配置
    (
        'override_advanced_quiz_vip',
        'test-user-1',
        'personality-big-five',
        '{
            "layer1_user_choice": {
                "preferred_view_type": "bubble",
                "color_mode": "cool"
            },
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "bubble": "R3F"
                },
                "performance_mode": "high_quality"
            },
            "layer3_skin_base": {
                "selected_skin_id": "ocean-blue",
                "animations": {
                    "enable_animations": true,
                    "animation_speed": "fast",
                    "reduce_motion": false
                }
            }
        }',
        'user_preference', -- VIP用户偏好
        3, -- override_priority
        1, -- is_active
        datetime('now', '-3 days'),
        datetime('now', '-30 minutes')
    ),
    
    -- 性能优化配置（低端设备）
    (
        'override_performance_optimization',
        'test-user-2',
        'daily-mood-tracker',
        '{
            "layer2_rendering_strategy": {
                "render_engine_preferences": {
                    "wheel": "CSS",
                    "card": "CSS",
                    "bubble": "CSS"
                },
                "performance_mode": "performance",
                "supported_content_types": {
                    "text": true,
                    "emoji": true,
                    "images": false,
                    "animations": false
                }
            },
            "layer3_skin_base": {
                "animations": {
                    "enable_animations": false,
                    "animation_speed": "slow",
                    "reduce_motion": true
                }
            }
        }',
        'performance', -- 性能优化
        7, -- override_priority
        1, -- is_active
        datetime('now', '-2 days'),
        datetime('now', '-1 hour')
    );

-- 验证插入的数据
SELECT 
    id,
    user_id,
    pack_id,
    override_reason,
    override_priority,
    is_active
FROM quiz_pack_overrides 
ORDER BY override_priority DESC, created_at DESC;
