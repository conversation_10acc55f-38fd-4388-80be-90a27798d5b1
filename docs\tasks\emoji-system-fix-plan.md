# Emoji 系统修复计划

## 问题概述

当前的 emoji 表结构与设置页面的情绪数据管理存在不一致，主要体现在以下几个方面：

1. **数据结构不一致**：数据库表结构支持多种表情类型，而前端类型定义只支持简单的字符串映射
2. **服务层不一致**：新的 `emotionService.ts` 与现有的 `emojiService.ts` 使用不同的数据结构
3. **上下文不一致**：`defaultEmotionService.ts` 与 `EmojiContext` 使用不同的数据获取方式
4. **UI 组件不一致**：情绪编辑器只支持 Unicode 表情，不支持其他类型的表情

## 修复计划

### 1. 统一类型定义 ✅

1. ✅ 更新 `src/types/emoji.ts` 以匹配新的数据库结构
   - 添加了 `EmojiItem` 接口，支持 Unicode、图片、SVG 和动画表情
   - 更新了 `EmojiSet` 接口，添加了类型、默认标志等字段
   - 保留了 `mappings` 字段以确保向后兼容性
2. ✅ 确保 `emotionDataTypes.ts` 中的 `Emotion` 接口与这些类型兼容

### 2. 更新服务层 ✅

1. ✅ 更新 `emojiService.ts` 以使用数据库中的表情集和表情项
2. ✅ 添加从数据库获取表情集和表情项的方法
   - 添加了 `getEmojiItemsForSet` 方法
   - 添加了 `getEmojiItemsForEmotion` 方法
3. ✅ 更新 `getEmojiForEmotionId` 方法以支持多种表情类型
4. ✅ 添加 `getEmojiItemForEmotionId` 方法以获取完整的表情项

### 3. 更新上下文 ✅

1. ✅ 更新 `EmojiContext` 以使用新的服务层方法
2. ✅ 添加对多种表情类型的支持
   - 添加了 `getEmojiItem` 方法
   - 更新了 `EmojiProvider` 组件以支持多语言

### 4. 更新 UI 组件 ✅

1. ✅ 更新 `EmotionEditor.tsx` 以支持多种表情类型
2. ✅ 添加表情集选择器
3. ✅ 添加表情预览组件

## 具体修复步骤

### 步骤 1：更新类型定义 ✅

1. ✅ 修改 `src/types/emoji.ts`：
   - 添加了 `EmojiItem` 接口，包含 `id`、`unicode`、`image_url`、`svg_content` 和 `animation_data` 字段
   - 更新了 `EmojiSet` 接口，添加了 `type`、`is_default`、`is_system` 等字段
   - 更新了 `EmotionEmojiMapping` 类型，支持字符串或 `EmojiItem` 对象

2. ✅ 确保 `emotionDataTypes.ts` 中的 `Emotion` 接口兼容新的类型定义
   - 保留了 `emoji` 字段以确保向后兼容性
   - 添加了 `emoji_sets` 字段以支持多表情集

### 步骤 2：更新 emojiService.ts ✅

1. ✅ 添加从数据库获取表情集的方法
   - 实现了 `getAvailableEmojiSets` 方法，从数据库获取表情集
   - 添加了备用的默认表情集，当数据库加载失败时使用

2. ✅ 添加从数据库获取表情项的方法
   - 实现了 `getEmojiItemsForSet` 方法，获取表情集的所有表情项
   - 实现了 `getEmojiItemsForEmotion` 方法，获取情绪对应的表情项

3. ✅ 更新 `getEmojiForEmotionId` 方法以支持多种表情类型
   - 支持字符串或 `EmojiItem` 对象
   - 添加了更多的错误处理和回退机制

4. ✅ 添加 `getEmojiItemForEmotionId` 方法以获取完整的表情项
   - 支持从 `items` 或 `mappings` 字段获取表情项
   - 添加了回退机制，当找不到表情项时返回默认表情

5. ✅ 更新 `getActiveEmojiSet` 和 `setActiveEmojiSet` 方法以使用数据库
   - 添加了语言参数，支持多语言
   - 添加了更多的错误处理和回退机制

### 步骤 3：更新 EmojiContext ✅

1. ✅ 更新 `EmojiProvider` 组件以使用新的服务层方法
   - 添加了语言支持，使用 `useLanguage` 钩子
   - 更新了加载表情集的逻辑，使用新的服务层方法

2. ✅ 更新 `getEmoji` 方法以支持多种表情类型
   - 保持了向后兼容性，仍然返回字符串

3. ✅ 添加 `getEmojiItem` 方法以获取完整的表情项
   - 返回完整的 `EmojiItem` 对象，包含所有表情类型的数据

### 步骤 4：更新 EmotionEditor ✅

1. ✅ 更新组件状态和方法
   - 添加了 `newEmotionEmojiSets` 状态，存储表情集映射
   - 添加了 `activeEmojiSetId` 状态，存储当前活动的表情集 ID
   - 使用 `useEmoji` 钩子获取表情集和表情项

2. ✅ 更新 `handleEmojiSelect` 方法以支持多种表情类型
   - 更新表情集映射，将选择的表情添加到当前活动的表情集中
   - 保持向后兼容性，仍然更新 `newEmotionEmoji` 状态

3. ✅ 添加 `handleEmojiSetChange` 方法，处理表情集选择
   - 更新当前活动的表情集 ID
   - 如果已经有这个表情集的表情，显示它

4. ✅ 更新添加和编辑情绪的方法
   - 在 `handleAddEmotion` 和 `handleUpdateEmotion` 方法中添加表情集支持
   - 添加本地化名称支持

5. ✅ 更新 UI 组件
   - 添加表情集选择器
   - 添加表情预览组件，显示当前表情集的表情
   - 保持向后兼容性，仍然显示单个表情

### 步骤 5：更新 EmotionDataManager ✅

1. ✅ 更新 `updateEmotion` 方法以支持多表情集
2. ✅ 添加 `updateEmotionEmoji` 方法以更新特定表情集的表情
3. ✅ 添加 `getEmotion` 方法以获取情绪对象
4. ✅ 添加 `getEmotionEmojiItem` 方法以获取情绪的表情项
5. ✅ 添加 `getEmotionEmojiItems` 方法以获取情绪的所有表情项
6. ✅ 添加 `deleteEmotionEmoji` 方法以删除情绪的表情项
7. ✅ 添加 `setDefaultEmojiSet` 方法以设置情绪数据的默认表情集

## 测试计划

1. ✅ 测试从数据库加载表情集和表情项
   - 验证 `getAvailableEmojiSets` 方法能够正确加载表情集
   - 验证 `getEmojiItemsForSet` 和 `getEmojiItemsForEmotion` 方法能够正确加载表情项

2. ✅ 测试切换表情集
   - 验证 `setActiveEmojiSet` 方法能够正确切换表情集
   - 验证 UI 中的表情集选择器能够正确显示和选择表情集

3. ✅ 测试选择不同类型的表情
   - 验证 `handleEmojiSelect` 方法能够正确处理表情选择
   - 验证表情预览组件能够正确显示不同类型的表情

4. ⏳ 测试保存和加载带有多表情集的情绪数据
   - 验证 `handleAddEmotion` 和 `handleUpdateEmotion` 方法能够正确保存表情集数据
   - 验证 `EmotionDataManager` 能够正确加载和保存带有多表情集的情绪数据

5. ✅ 测试向后兼容性
   - 验证旧代码仍然可以使用 `emotion.emoji` 字段
   - 验证 `getEmoji` 方法仍然返回字符串

## 注意事项

1. ✅ 保持向后兼容性
   - 保留了 `emoji` 字段以确保向后兼容性
   - 保留了 `mappings` 字段以确保向后兼容性
   - `getEmoji` 方法仍然返回字符串

2. ✅ 系统默认情绪数据保护
   - 系统默认情绪数据的保护机制仍然有效
   - 只有用户创建的情绪数据可以修改

3. ✅ 多语言支持
   - 添加了语言支持，使用 `useLanguage` 钩子
   - 表情集和表情项支持多语言名称和描述

4. ✅ 表情预览
   - 添加了表情预览组件，显示当前表情集的表情
   - 支持不同类型的表情（Unicode、图片、SVG、动画）

## 后续工作

1. ⏳ 完成 `EmotionDataManager` 的更新
   - 更新 `updateEmotion` 方法以支持多表情集
   - 添加 `updateEmotionEmoji` 方法以更新特定表情集的表情

2. ⏳ 添加更多的表情集和表情项
   - 添加更多的 Unicode 表情集
   - 添加图片表情集
   - 添加 SVG 表情集
   - 添加动画表情集

3. ⏳ 实现表情商店功能
   - 添加表情集购买界面
   - 添加表情集解锁功能
   - 添加表情集预览功能

4. ⏳ 添加表情集管理界面
   - 添加表情集创建界面
   - 添加表情集编辑界面
   - 添加表情集删除功能
