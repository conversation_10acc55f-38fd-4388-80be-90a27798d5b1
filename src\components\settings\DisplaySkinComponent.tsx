import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';
import { useSkinManager } from '@/contexts/SkinContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import type { Skin } from '@/types/skinTypes';
import { Check, Lock } from 'lucide-react';
import React from 'react';
import { toast } from 'sonner';

interface SkinCardProps {
  skin: Skin;
  isUnlocked: boolean;
  isActive: boolean;
  onSelect: (skin: Skin) => void;
  onUnlock: (skin: Skin) => void;
}

/**
 * 皮肤卡片组件
 */
const SkinCard: React.FC<SkinCardProps> = ({ skin, isUnlocked, isActive, onSelect, onUnlock }) => {
  const { t } = useLanguage();

  return (
    <Card
      className={`overflow-hidden transition-all duration-300 ${
        isActive ? 'border-primary shadow-lg' : 'border-border'
      } ${isUnlocked ? 'cursor-pointer hover:shadow-md' : 'opacity-80'}`}
    >
      <div className="relative">
        <div className="h-32 bg-muted">
          {skin.preview_image_light ? (
            <img
              src={skin.preview_image_light}
              alt={skin.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <span className="text-muted-foreground">
                {t('settings.skin.no_preview', '无预览')}
              </span>
            </div>
          )}

          {skin.category === 'premium' && (
            <Badge
              variant="secondary"
              className="absolute top-2 right-2 bg-amber-200 text-amber-800"
            >
              {t('settings.skin.premium', '高级')}
            </Badge>
          )}

          {isActive && (
            <div className="absolute top-2 left-2 bg-primary text-primary-foreground rounded-full p-1">
              <Check size={16} />
            </div>
          )}

          {!isUnlocked && (
            <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex flex-col items-center justify-center">
              <Lock className="h-8 w-8 mb-2 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">
                {t('settings.skin.locked', '未解锁')}
              </span>
              {skin.parsedUnlockConditions?.price && (
                <span className="text-sm mt-1 text-muted-foreground">
                  {skin.parsedUnlockConditions.price}{' '}
                  {skin.parsedUnlockConditions.currency || t('settings.skin.points', '积分')}
                </span>
              )}
            </div>
          )}
        </div>

        <CardContent className="p-4">
          <h3 className="font-medium">{skin.name}</h3>
          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">{skin.description}</p>

          <div className="mt-2 flex flex-wrap gap-1">
            {(skin.parsedsupported_view_types || []).map((type) => (
              <Badge key={type} variant="outline" className="text-xs">
                {t(`settings.view_type.${type}`, type)}
              </Badge>
            ))}
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0">
          {isUnlocked ? (
            <Button
              variant={isActive ? 'secondary' : 'default'}
              className="w-full"
              onClick={() => onSelect(skin)}
            >
              {isActive
                ? t('settings.skin.current', '当前使用中')
                : t('settings.skin.use', '使用此皮肤')}
            </Button>
          ) : (
            <Button variant="outline" className="w-full" onClick={() => onUnlock(skin)}>
              {t('settings.skin.unlock', '解锁')}
            </Button>
          )}
        </CardFooter>
      </div>
    </Card>
  );
};

/**
 * 显示皮肤组件
 * 用于在设置页面中选择皮肤，特别是在 DisplayOptions 中选择适用于当前视图类型的皮肤
 */
const DisplaySkinComponent: React.FC<{ filterByViewType?: boolean }> = ({
  filterByViewType = true,
}) => {
  const { t } = useLanguage();
  const { userConfig, updateUserConfig } = useUserConfig();
  const { currentSkin, availableSkins, unlockedSkins, unlockSkin } = useSkinManager();

  // 获取当前显示选项
  const displayOptions = React.useMemo(() => {
    const viewType = userConfig.preferred_view_type || 'wheel';
    const skinId =
      userConfig.view_type_skin_ids?.[viewType] ||
      userConfig.active_skin_id ||
      'default-wheel-skin';
    const renderEngine = userConfig.render_engine_preferences?.[viewType] || 'D3';
    const displayMode = userConfig.content_display_mode_preferences?.[viewType] || 'textEmoji';

    return {
      viewType,
      skinId,
      renderEngine,
      displayMode,
    };
  }, [userConfig]);

  // 获取过滤后的皮肤列表
  const filteredSkins = React.useMemo(() => {
    if (filterByViewType && displayOptions.viewType) {
      // 过滤出支持当前视图类型的皮肤
      return availableSkins.filter((skin) =>
        (skin.parsedsupported_view_types || []).includes(displayOptions.viewType)
      );
    }
    return availableSkins;
  }, [availableSkins, displayOptions.viewType, filterByViewType]);

  // 处理皮肤选择
  const handleSkinSelect = (skin: Skin) => {
    // 检查皮肤是否支持当前的显示选项
    const { displayMode, renderEngine, viewType } = displayOptions;

    // 如果皮肤不支持当前的显示选项，则自动选择该皮肤支持的选项
    const newOptions: any = {
      active_skin_id: skin.id,
      view_type_skin_ids: {
        ...userConfig.view_type_skin_ids,
        [viewType]: skin.id,
      },
    };

    if (!(skin.parsedsupported_content_modes || []).includes(displayMode)) {
      newOptions.content_display_mode_preferences = {
        ...userConfig.content_display_mode_preferences,
        [viewType]: (skin.parsedsupported_content_modes || [])[0] || 'textEmoji',
      };
    }

    if (!(skin.parsedsupported_render_engines || []).includes(renderEngine)) {
      newOptions.render_engine_preferences = {
        ...userConfig.render_engine_preferences,
        [viewType]: (skin.parsedsupported_render_engines || [])[0] || 'D3',
      };
    }

    if (!(skin.parsedsupported_view_types || []).includes(viewType) && !filterByViewType) {
      newOptions.preferred_view_type = (skin.parsedsupported_view_types || [])[0] || 'wheel';
    }

    // 更新用户配置
    updateUserConfig(newOptions);

    toast.success(t('settings.skin_changed', '皮肤已更改'), { duration: 3000 });
  };

  // 处理皮肤解锁
  const handleSkinUnlock = (skin: Skin) => {
    if (window.confirm(t('settings.skin.confirm_unlock', '确定要解锁此皮肤吗？'))) {
      if (unlockSkin(skin.id)) {
        toast.success(t('settings.skin_unlocked', '皮肤已解锁'), { duration: 3000 });
      }
    }
  };

  // 如果没有皮肤可显示，显示提示信息
  if (filteredSkins.length === 0 && filterByViewType) {
    return (
      <div className="p-8 text-center border rounded-md bg-muted/20">
        <p className="text-muted-foreground">
          {t('settings.view_config.no_skins_available', '没有支持当前视图类型的皮肤')}
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => {
            // 跳转到皮肤商店页面
            window.location.href = '/skin-shop';
          }}
        >
          {t('settings.view_config.browse_skins', '浏览所有皮肤')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {filteredSkins
          .filter((skin) => unlockedSkins.some((s) => s.id === skin.id))
          .map((skin) => (
            <SkinCard
              key={skin.id}
              skin={skin}
              isUnlocked={true}
              isActive={currentSkin?.id === skin.id}
              onSelect={handleSkinSelect}
              onUnlock={handleSkinUnlock}
            />
          ))}
      </div>

      {/* 未解锁的皮肤 */}
      {filteredSkins.some((skin) => !unlockedSkins.some((s) => s.id === skin.id)) && (
        <div>
          <h4 className="text-sm font-medium text-muted-foreground mb-3">
            {t('settings.skin.locked', '未解锁的皮肤')}
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {filteredSkins
              .filter((skin) => !unlockedSkins.some((s) => s.id === skin.id))
              .map((skin) => (
                <SkinCard
                  key={skin.id}
                  skin={skin}
                  isUnlocked={false}
                  isActive={false}
                  onSelect={handleSkinSelect}
                  onUnlock={handleSkinUnlock}
                />
              ))}
          </div>
        </div>
      )}

      {/* 查看更多皮肤的链接 */}
      {filterByViewType && (
        <div className="mt-4 text-center">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              // 跳转到皮肤商店页面
              window.location.href = '/skin-shop';
            }}
          >
            {t('settings.view_config.view_more_skins', '查看更多皮肤')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default DisplaySkinComponent;
