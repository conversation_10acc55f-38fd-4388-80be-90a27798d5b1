-- Quiz System Database Schema v2.0
-- Implements data-presentation separation architecture
-- Supports all mainstream and alternative quiz types

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- CORE INFRASTRUCTURE TABLES
-- ============================================================================

-- 1. Users Table - Core user management
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY NOT NULL,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE,
    password_hash TEXT,
    display_name TEXT,
    avatar_url TEXT,

    -- User status and permissions
    is_active BOOLEAN DEFAULT 1,
    is_verified BOOLEAN DEFAULT 0,
    is_banned BOOLEAN DEFAULT 0,
    is_vip BOOLEAN DEFAULT 0,
    vip_tier TEXT, -- 'basic', 'premium', 'enterprise'
    vip_expires_at TIMESTAMP,

    -- Activity tracking
    last_login_at TIMESTAMP,
    login_count INTEGER DEFAULT 0,
    last_active_at TIMESTAMP,

    -- Localization
    timezone TEXT DEFAULT 'UTC',
    locale TEXT DEFAULT 'en',

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Sync fields
    sync_status TEXT DEFAULT 'local', -- 'local', 'synced', 'sync_required', 'sync_failed'
    server_id TEXT,
    last_synced_at TIMESTAMP
);

-- 2. App Settings Table - Global application settings
CREATE TABLE IF NOT EXISTS app_settings (
    key TEXT PRIMARY KEY NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    data_type TEXT DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
    is_system BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. UI Labels Table - Internationalization support
CREATE TABLE IF NOT EXISTS ui_labels (
    key TEXT PRIMARY KEY NOT NULL,
    default_text TEXT NOT NULL,
    description TEXT,
    category TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. UI Label Translations Table - Multi-language support
CREATE TABLE IF NOT EXISTS ui_label_translations (
    id TEXT PRIMARY KEY NOT NULL,
    label_key TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(label_key, language_code),
    FOREIGN KEY (label_key) REFERENCES ui_labels(key) ON DELETE CASCADE
);

-- 5. User Configs Table - User global preferences (non-quiz specific)
CREATE TABLE IF NOT EXISTS user_configs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT,
    config_name TEXT NOT NULL DEFAULT 'default',

    -- Global app preferences
    theme_mode TEXT DEFAULT 'system', -- 'light', 'dark', 'system'
    language TEXT DEFAULT 'en',
    notifications_enabled BOOLEAN DEFAULT 1,
    sound_enabled BOOLEAN DEFAULT 1,
    accessibility TEXT, -- JSON: accessibility settings

    -- Status management
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);

-- 6. User Streaks Table - User engagement tracking
CREATE TABLE IF NOT EXISTS user_streaks (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_entry_date DATE,
    streak_type TEXT DEFAULT 'daily', -- 'daily', 'weekly', 'monthly'

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, streak_type)
);

-- 7. Skins Table - Theme and visual customization
CREATE TABLE IF NOT EXISTS skins (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT, -- 'default', 'nature', 'futuristic', 'minimal', etc.

    -- Compatibility
    supported_view_types TEXT, -- JSON array: supported view types
    supported_render_engines TEXT, -- JSON array: supported render engines

    -- Pricing and access
    is_premium BOOLEAN DEFAULT 0,
    is_unlocked BOOLEAN DEFAULT 1,
    price INTEGER, -- Price in cents/points

    -- Preview assets
    preview_image_light TEXT,
    preview_image_dark TEXT,

    -- Configuration
    config TEXT NOT NULL, -- JSON: complete skin configuration

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- Constraints
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 8. Emoji Sets Table - Emoji collections for emotional expression
CREATE TABLE IF NOT EXISTS emoji_sets (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL DEFAULT 'unicode', -- 'unicode', 'animated', 'custom'

    -- Status and access
    is_default BOOLEAN DEFAULT 0,
    is_system BOOLEAN DEFAULT 1,
    is_unlocked BOOLEAN DEFAULT 1,
    price INTEGER, -- Price in cents/points

    -- Preview
    preview_image TEXT,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,

    -- Constraints
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 9. Emoji Set Translations Table - Multi-language emoji set support
CREATE TABLE IF NOT EXISTS emoji_set_translations (
    emoji_set_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,

    PRIMARY KEY (emoji_set_id, language_code),
    FOREIGN KEY (emoji_set_id) REFERENCES emoji_sets(id) ON DELETE CASCADE
);

-- ============================================================================
-- 注意：以下表已废弃，根据新架构设计原则移除
-- ============================================================================
--
-- 废弃的表：
-- - emotions: 情绪定义表 (废弃)
-- - emotion_translations: 情绪翻译表 (废弃)
-- - emoji_items: 表情项映射表 (废弃)
--
-- 废弃原因：
-- 1. Quiz Pack = Emotion Data Set (统一概念)
-- 2. 数据与展现分离 (emotions属于展现层，不应存储在数据库)
-- 3. 所有内容通过 quiz_questions 和 quiz_question_options 管理
--
-- 新架构中情绪相关内容的管理方式：
-- - quiz_packs: 定义情绪测评包
-- - quiz_questions: 定义情绪相关问题 (如: "您现在的情绪是什么？")
-- - quiz_question_options: 定义情绪选项 (如: option_value="happy", option_text="快乐")
-- - user_presentation_configs: 配置情绪的展现方式 (emoji, color, animation等)
--
-- 表情符号现在通过以下方式管理：
-- - emoji_sets: 表情集定义 (保留，用于皮肤系统)
-- - user_presentation_configs: 在展现配置中定义情绪与表情的映射关系
--
-- 示例：在展现配置中定义情绪展现
-- {
--   "layer4_view_detail": {
--     "emotion_presentation": {
--       "happy": {"emoji": "😊", "color": "#FFD700", "animation": "bounce"},
--       "sad": {"emoji": "😢", "color": "#4169E1", "animation": "fade"}
--     }
--   }
-- }

-- 注意：emotion_data_sets 和 emotion_data_set_tiers 表已废弃
-- 新架构使用 quiz_packs → quiz_questions → quiz_question_options
-- 这些表定义已移除，使用新的Quiz架构替代

-- ============================================================================
-- CORE QUIZ SYSTEM TABLES
-- ============================================================================

-- 1. Quiz Packs Table - Quiz definitions and metadata
CREATE TABLE IF NOT EXISTS quiz_packs (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    version TEXT NOT NULL DEFAULT '1.0.0',

    -- Quiz classification and types (supports mainstream and alternative quiz)
    category TEXT NOT NULL, -- 'daily', 'assessment', 'therapy', 'research', 'entertainment', 'education'
    quiz_type TEXT NOT NULL, -- 'emotion_wheel', 'traditional_scale', 'personality_test', 'iq_test', 'knowledge_quiz', 'survey', 'game_quiz', 'mixed'
    difficulty_level TEXT DEFAULT 'regular', -- 'beginner', 'regular', 'advanced', 'expert'
    quiz_style TEXT, -- 'mainstream', 'alternative', 'experimental', 'cultural_specific'

    -- 注意：emotion_data_set_id 字段已移除，因为emotion_data_sets表已废弃
    -- 新架构中，Quiz包直接包含所有必要的配置信息

    -- Quiz logic configuration (JSON: QuizLogicConfig)
    quiz_logic_config TEXT NOT NULL,

    -- Default presentation hints (JSON: DefaultPresentationHints, optional)
    default_presentation_hints TEXT,

    -- Metadata (JSON: QuizPackMetadata)
    metadata TEXT NOT NULL,

    -- Status management
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,
    sort_order INTEGER DEFAULT 0,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- Foreign key constraints
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
    -- 注意：emotion_data_set_id 外键已移除
);

-- 2. Quiz Questions Table - Independent question management
CREATE TABLE IF NOT EXISTS quiz_questions (
    id TEXT PRIMARY KEY NOT NULL,
    pack_id TEXT NOT NULL,

    -- Question basic information
    question_text TEXT NOT NULL,
    question_text_localized TEXT, -- JSON: multi-language text
    question_type TEXT NOT NULL, -- 'single_choice', 'multiple_choice', 'scale_rating', 'emotion_wheel', 'text_input', 'number_input', 'date_input', 'slider', 'ranking', 'matrix', 'image_choice', 'audio_choice', 'video_choice', 'drawing', 'file_upload'

    -- Question order and grouping
    question_order INTEGER NOT NULL,
    question_group TEXT, -- Optional question grouping (e.g., '脏腑功能', '证素状态')
    tier_level INTEGER DEFAULT 1, -- Question tier level (1, 2, 3...)

    -- Question configuration
    question_config TEXT, -- JSON: question-specific configuration
    validation_rules TEXT, -- JSON: validation rules
    scoring_config TEXT, -- JSON: scoring configuration

    -- Dependency relationships
    parent_question_id TEXT, -- Parent question ID (for hierarchical questions)
    dependency_rules TEXT, -- JSON: dependency rules

    -- Status management
    is_required BOOLEAN DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- Constraints
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_question_id) REFERENCES quiz_questions(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    UNIQUE(pack_id, question_order)
);

-- 3. Quiz Question Options Table - Supports various quiz answer types
CREATE TABLE IF NOT EXISTS quiz_question_options (
    id TEXT PRIMARY KEY NOT NULL,
    question_id TEXT NOT NULL,

    -- Option basic information
    option_text TEXT NOT NULL,
    option_text_localized TEXT, -- JSON: multi-language text
    option_value TEXT NOT NULL, -- Option value (for data processing)

    -- Option type and purpose
    option_type TEXT NOT NULL DEFAULT 'choice', -- 'choice', 'scale_point', 'ranking_item', 'matrix_row', 'matrix_column'

    -- Option order and grouping
    option_order INTEGER NOT NULL,
    option_group TEXT, -- Optional option grouping

    -- Scoring configuration (for scale-type quiz)
    scoring_value REAL, -- Scoring value
    scoring_weight REAL DEFAULT 1.0, -- Scoring weight
    min_value REAL, -- Minimum value (for slider, scale, etc.)
    max_value REAL, -- Maximum value (for slider, scale, etc.)
    step_value REAL, -- Step value (for slider, etc.)

    -- Multimedia content (for image_choice, audio_choice, etc.)
    media_url TEXT, -- Media file URL
    media_type TEXT, -- 'image', 'audio', 'video'
    media_thumbnail_url TEXT, -- Thumbnail URL
    media_alt_text TEXT, -- Alternative text

    -- Matrix configuration (for matrix type)
    matrix_row_id TEXT, -- Matrix row ID
    matrix_column_id TEXT, -- Matrix column ID

    -- Content reuse (optional)
    reference_pack_id TEXT, -- Reference to other quiz pack
    reference_option_id TEXT, -- Reference to other option

    -- Option metadata (pure data)
    metadata TEXT, -- JSON: other metadata
    tags TEXT, -- JSON array: tags

    -- Status management
    is_active BOOLEAN DEFAULT 1,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- Constraints
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
    FOREIGN KEY (reference_pack_id) REFERENCES quiz_packs(id) ON DELETE SET NULL,
    FOREIGN KEY (reference_option_id) REFERENCES quiz_question_options(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    UNIQUE(question_id, option_order)
);

-- ============================================================================
-- PRESENTATION SYSTEM TABLES
-- ============================================================================

-- 4. User Presentation Configs Table - 6-layer personalization configuration (Quiz-specific)
CREATE TABLE IF NOT EXISTS user_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    config_name TEXT NOT NULL DEFAULT 'default',

    -- 6-layer Quiz presentation configuration (JSON: QuizPresentationConfig)
    presentation_config TEXT NOT NULL,

    -- Configuration metadata
    config_version TEXT DEFAULT '2.0',
    personalization_level INTEGER DEFAULT 0, -- 0-100

    -- Status management
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);

-- 5. Pack Presentation Overrides Table - Quiz-specific presentation overrides
CREATE TABLE IF NOT EXISTS pack_presentation_overrides (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,

    -- Presentation override configuration (JSON: PackSpecificPresentationOverride)
    presentation_overrides TEXT,

    -- Tier presentation overrides (JSON: TierPresentationOverrides)
    tier_presentation_overrides TEXT,

    -- Override metadata
    override_reason TEXT, -- 'user_preference', 'accessibility_need', 'performance_optimization'
    override_priority INTEGER DEFAULT 1, -- 1-10

    -- Status management
    is_active BOOLEAN DEFAULT 1,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    UNIQUE(user_id, pack_id)
);

-- ============================================================================
-- SESSION MANAGEMENT TABLES
-- ============================================================================

-- 6. Quiz Sessions Table - Quiz session management
CREATE TABLE IF NOT EXISTS quiz_sessions (
    id TEXT PRIMARY KEY NOT NULL,
    pack_id TEXT NOT NULL,
    user_id TEXT NOT NULL,

    -- Session status
    status TEXT NOT NULL DEFAULT 'INITIATED',
    -- 'INITIATED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABORTED'
    current_question_index INTEGER DEFAULT 0,
    current_question_id TEXT,
    current_tier_level INTEGER,

    -- Time tracking
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_active_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    total_duration_seconds INTEGER,

    -- Progress tracking
    total_questions INTEGER,
    answered_questions INTEGER DEFAULT 0,
    skipped_questions INTEGER DEFAULT 0,
    completion_percentage REAL DEFAULT 0,

    -- Session metadata
    session_type TEXT DEFAULT 'standard', -- 'standard', 'practice', 'assessment'
    device_info TEXT, -- JSON: device information

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (current_question_id) REFERENCES quiz_questions(id) ON DELETE SET NULL
);

-- 7. Quiz Session Presentation Configs Table - Session configuration snapshots
CREATE TABLE IF NOT EXISTS quiz_session_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL,
    user_id TEXT NOT NULL,

    -- Basic configuration snapshot
    config_name TEXT NOT NULL,
    config_version TEXT NOT NULL DEFAULT '2.0',

    -- Complete presentation configuration snapshot (copied from user_presentation_configs)
    presentation_config TEXT NOT NULL, -- JSON: complete 6-layer personalization configuration

    -- Session-specific configuration
    session_overrides TEXT, -- JSON: session-specific configuration overrides

    -- Configuration metadata
    personalization_level INTEGER DEFAULT 50, -- Personalization level 0-100
    config_source TEXT DEFAULT 'user_preference', -- 'user_preference', 'system_default', 'session_custom'

    -- Status management
    is_active BOOLEAN DEFAULT 1,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE(session_id) -- Each session has only one presentation configuration snapshot
);

-- 8. Global App Configs Table - Global application settings (non-Quiz specific)
CREATE TABLE IF NOT EXISTS global_app_configs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    config_name TEXT NOT NULL DEFAULT 'default',

    -- Global app preferences (non-Quiz specific)
    theme_mode TEXT DEFAULT 'system', -- 'light', 'dark', 'system'
    language TEXT DEFAULT 'en', -- 'en', 'zh', etc.
    notifications_enabled BOOLEAN DEFAULT 1,
    sound_enabled BOOLEAN DEFAULT 1,

    -- Accessibility settings (JSON: GlobalAccessibilityConfig)
    accessibility TEXT, -- JSON: global accessibility settings

    -- Status management
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);

-- ============================================================================
-- ANSWER AND RESULT TABLES
-- ============================================================================

-- 9. Quiz Answers Table - Quiz answer records (pure data recording, linked to session presentation config)
CREATE TABLE IF NOT EXISTS quiz_answers (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL,
    question_id TEXT NOT NULL, -- Link to quiz_questions table
    session_presentation_config_id TEXT, -- Link to session presentation config snapshot (optional)

    -- Answer content (pure data)
    selected_option_ids TEXT, -- JSON array: list of selected option IDs (supports multiple choice)
    answer_value TEXT, -- Answer value (can be option value, rating value, etc.)
    answer_text TEXT, -- Answer text (for display)

    -- Scoring related (if scale-type question)
    scale_value REAL, -- Scale rating value
    scale_label TEXT, -- Scale label (e.g., 'None', 'Mild', 'Moderate', 'Severe')

    -- Answer metadata (pure data)
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
    response_time_ms INTEGER, -- Response time (milliseconds)
    is_skipped BOOLEAN DEFAULT 0,
    is_revised BOOLEAN DEFAULT 0, -- Whether answer was revised
    revision_count INTEGER DEFAULT 0,

    -- Timestamps
    answered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE RESTRICT,
    FOREIGN KEY (session_presentation_config_id) REFERENCES quiz_session_presentation_configs(id) ON DELETE SET NULL
);

-- 9. Quiz Results Table - Quiz results
CREATE TABLE IF NOT EXISTS quiz_results (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL UNIQUE,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,

    -- Result status
    status TEXT NOT NULL DEFAULT 'COMPLETED',
    -- 'COMPLETED', 'PARTIALLY_COMPLETED', 'TIMEOUT'
    completion_percentage REAL CHECK (completion_percentage BETWEEN 0 AND 100),

    -- Basic statistics
    total_duration_seconds INTEGER,
    average_response_time_ms INTEGER,
    total_questions INTEGER,
    answered_questions INTEGER,
    skipped_questions INTEGER,

    -- Emotion analysis results (JSON)
    dominant_emotions TEXT, -- JSON array: dominant emotions
    emotion_pattern_analysis TEXT, -- JSON: emotion pattern analysis
    intensity_analysis TEXT, -- JSON: intensity analysis
    category_distribution TEXT, -- JSON: category distribution
    temporal_patterns TEXT, -- JSON: temporal patterns

    -- Scoring and tags
    overall_score REAL,
    emotional_stability_score REAL,
    emotional_complexity_score REAL,
    generated_tags TEXT, -- JSON array: generated tags

    -- Recommendations and summary
    recommendations TEXT, -- JSON: recommendation suggestions
    narrative_summary TEXT, -- Narrative summary
    visual_summary_config TEXT, -- JSON: visual summary configuration

    -- AI analysis status
    ai_analysis_status TEXT DEFAULT 'NOT_REQUESTED',
    -- 'NOT_REQUESTED', 'PENDING', 'COMPLETED', 'FAILED'
    ai_analysis_id TEXT,
    ai_analysis_version TEXT,

    -- Result metadata
    result_version TEXT DEFAULT '2.0',
    analysis_algorithm_version TEXT,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE RESTRICT
);

-- ============================================================================
-- ANALYTICS AND RECOMMENDATION TABLES
-- ============================================================================

-- 10. Emotion Pattern Analyses Table - Emotion pattern analysis
CREATE TABLE IF NOT EXISTS emotion_pattern_analyses (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    result_id TEXT NOT NULL,

    -- Analysis type
    analysis_type TEXT NOT NULL, -- 'single_session', 'trend_analysis', 'comparative'
    analysis_period_start TIMESTAMP,
    analysis_period_end TIMESTAMP,

    -- Pattern data (JSON)
    dominant_emotions TEXT, -- JSON: dominant emotion list
    emotion_transitions TEXT, -- JSON: emotion transition patterns
    intensity_patterns TEXT, -- JSON: intensity patterns
    temporal_patterns TEXT, -- JSON: temporal patterns
    category_distribution TEXT, -- JSON: category distribution

    -- Statistical metrics
    emotional_stability_index REAL,
    emotional_complexity_index REAL,
    emotional_variability_index REAL,
    pattern_consistency_score REAL,

    -- Trend analysis
    trend_direction TEXT, -- 'improving', 'declining', 'stable', 'fluctuating'
    trend_confidence REAL CHECK (trend_confidence BETWEEN 0 AND 1),

    -- Analysis metadata
    analysis_algorithm_version TEXT,
    analysis_confidence REAL,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES quiz_results(id) ON DELETE CASCADE
);

-- 11. Recommendations Table - Recommendation suggestions
CREATE TABLE IF NOT EXISTS recommendations (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    result_id TEXT,
    analysis_id TEXT,

    -- Recommendation type
    recommendation_type TEXT NOT NULL,
    -- 'therapy_technique', 'lifestyle_change', 'quiz_suggestion', 'config_optimization'
    category TEXT, -- 'immediate', 'short_term', 'long_term'
    priority_level INTEGER CHECK (priority_level BETWEEN 1 AND 5),

    -- Recommendation content
    title TEXT NOT NULL,
    description TEXT,
    detailed_explanation TEXT,
    action_steps TEXT, -- JSON array: specific action steps

    -- Recommendation basis
    reasoning TEXT, -- Recommendation reason
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
    evidence_data TEXT, -- JSON: supporting evidence

    -- Resource links
    resources TEXT, -- JSON: related resource links
    external_links TEXT, -- JSON: external links

    -- User feedback
    user_rating INTEGER CHECK (user_rating BETWEEN 1 AND 5),
    user_feedback TEXT,
    is_helpful BOOLEAN,
    is_implemented BOOLEAN DEFAULT 0,
    implementation_date TIMESTAMP,

    -- Recommendation metadata
    recommendation_algorithm_version TEXT,
    personalization_level INTEGER, -- 0-100

    -- Status management
    is_active BOOLEAN DEFAULT 1,
    expires_at TIMESTAMP,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES quiz_results(id) ON DELETE CASCADE,
    FOREIGN KEY (analysis_id) REFERENCES emotion_pattern_analyses(id) ON DELETE CASCADE
);

-- ============================================================================
-- TAGS AND ASSOCIATIONS
-- ============================================================================

-- Tags Table: Stores user-defined or predefined tags for quiz results and mood entries
CREATE TABLE IF NOT EXISTS tags (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL UNIQUE, -- Tag name, e.g., 'work', 'family', 'stress'
    description TEXT, -- Optional description
    category TEXT, -- Optional category, e.g., 'emotion', 'context', 'activity'
    color TEXT, -- Optional color for UI display
    icon TEXT, -- Optional icon identifier

    -- Tag metadata
    is_system BOOLEAN DEFAULT 0, -- System-defined vs user-defined
    is_active BOOLEAN DEFAULT 1,
    usage_count INTEGER DEFAULT 0, -- Track how often this tag is used

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT, -- User who created this tag

    -- Constraints
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Quiz Result Tags Table: Junction table linking quiz results with multiple tags
CREATE TABLE IF NOT EXISTS quiz_result_tags (
    quiz_result_id TEXT NOT NULL,
    tag_id TEXT NOT NULL,

    -- Tag association metadata
    added_by TEXT, -- User who added this tag association
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1), -- For AI-generated tags
    tag_source TEXT DEFAULT 'user', -- 'user', 'ai_generated', 'system_suggested'

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    PRIMARY KEY (quiz_result_id, tag_id),
    FOREIGN KEY (quiz_result_id) REFERENCES quiz_results(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Mood Entry Tags Table: Junction table linking mood entries with multiple tags (for backward compatibility)
CREATE TABLE IF NOT EXISTS mood_entry_tags (
    mood_entry_id TEXT NOT NULL,
    tag_id TEXT NOT NULL,

    -- Tag association metadata
    added_by TEXT, -- User who added this tag association
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1), -- For AI-generated tags
    tag_source TEXT DEFAULT 'user', -- 'user', 'ai_generated', 'system_suggested'

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    PRIMARY KEY (mood_entry_id, tag_id),
    FOREIGN KEY (mood_entry_id) REFERENCES mood_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Tag Translations Table: Stores translations for tag names and descriptions
CREATE TABLE IF NOT EXISTS tag_translations (
    tag_id TEXT NOT NULL,
    language_code TEXT NOT NULL, -- e.g., 'en', 'zh', 'es'
    translated_name TEXT NOT NULL,
    translated_description TEXT,

    -- Translation metadata
    translation_quality REAL CHECK (translation_quality BETWEEN 0 AND 1), -- Quality score
    translator_type TEXT DEFAULT 'human', -- 'human', 'ai', 'community'

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    PRIMARY KEY (tag_id, language_code),
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
) WITHOUT ROWID;

-- ============================================================================
-- EMOJI MAPPING AND PRESENTATION TABLES
-- ============================================================================

-- 12. Question Presentation Overrides Table - 问题级别的emoji映射覆盖
CREATE TABLE IF NOT EXISTS question_presentation_overrides (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    question_id TEXT NOT NULL,

    -- 覆盖配置
    presentation_overrides TEXT NOT NULL, -- JSON: 问题特定的展现覆盖配置
    override_reason TEXT, -- 'user_preference', 'accessibility', 'context_specific', 'theme_change'
    override_priority INTEGER DEFAULT 1, -- 覆盖优先级 (1-10)

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- 约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    -- 唯一约束：每个用户每个问题只能有一个覆盖配置
    UNIQUE(user_id, question_id)
);

-- 13. Pack Presentation Configs Table - Quiz包的默认展现配置
CREATE TABLE IF NOT EXISTS pack_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    pack_id TEXT NOT NULL,
    config_type TEXT NOT NULL, -- 'default_emoji_mapping', 'theme_config', 'layout_config', 'animation_config'

    -- 配置数据
    config_data TEXT NOT NULL, -- JSON: 配置数据
    config_version TEXT DEFAULT '1.0',
    config_description TEXT, -- 配置描述

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- 约束
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    -- 唯一约束：每个包每种配置类型只能有一个活跃配置
    UNIQUE(pack_id, config_type, is_active) WHERE is_active = 1
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Core entity indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_is_vip ON users(is_vip);
CREATE INDEX IF NOT EXISTS idx_users_vip_expires_at ON users(vip_expires_at);

-- Quiz system indexes
CREATE INDEX IF NOT EXISTS idx_quiz_packs_category ON quiz_packs(category);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_quiz_type ON quiz_packs(quiz_type);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_is_active ON quiz_packs(is_active);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_is_default ON quiz_packs(is_default);

CREATE INDEX IF NOT EXISTS idx_quiz_questions_pack_id ON quiz_questions(pack_id);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_question_type ON quiz_questions(question_type);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_order_index ON quiz_questions(order_index);

CREATE INDEX IF NOT EXISTS idx_quiz_question_options_question_id ON quiz_question_options(question_id);
CREATE INDEX IF NOT EXISTS idx_quiz_question_options_option_value ON quiz_question_options(option_value);

CREATE INDEX IF NOT EXISTS idx_quiz_sessions_user_id ON quiz_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_pack_id ON quiz_sessions(pack_id);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_status ON quiz_sessions(status);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_started_at ON quiz_sessions(started_at);

CREATE INDEX IF NOT EXISTS idx_quiz_answers_session_id ON quiz_answers(session_id);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_question_id ON quiz_answers(question_id);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_user_id ON quiz_answers(user_id);

CREATE INDEX IF NOT EXISTS idx_quiz_results_user_id ON quiz_results(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_results_pack_id ON quiz_results(pack_id);
CREATE INDEX IF NOT EXISTS idx_quiz_results_status ON quiz_results(status);
CREATE INDEX IF NOT EXISTS idx_quiz_results_created_at ON quiz_results(created_at);

-- Tags and associations indexes
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(category);
CREATE INDEX IF NOT EXISTS idx_tags_is_active ON tags(is_active);
CREATE INDEX IF NOT EXISTS idx_tags_usage_count ON tags(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_tags_created_by ON tags(created_by);

CREATE INDEX IF NOT EXISTS idx_quiz_result_tags_quiz_result_id ON quiz_result_tags(quiz_result_id);
CREATE INDEX IF NOT EXISTS idx_quiz_result_tags_tag_id ON quiz_result_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_quiz_result_tags_source ON quiz_result_tags(tag_source);
CREATE INDEX IF NOT EXISTS idx_quiz_result_tags_added_by ON quiz_result_tags(added_by);

CREATE INDEX IF NOT EXISTS idx_mood_entry_tags_mood_entry_id ON mood_entry_tags(mood_entry_id);
CREATE INDEX IF NOT EXISTS idx_mood_entry_tags_tag_id ON mood_entry_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_mood_entry_tags_source ON mood_entry_tags(tag_source);

CREATE INDEX IF NOT EXISTS idx_tag_translations_tag_id ON tag_translations(tag_id);
CREATE INDEX IF NOT EXISTS idx_tag_translations_language_code ON tag_translations(language_code);

-- Analytics and recommendation indexes
CREATE INDEX IF NOT EXISTS idx_emotion_pattern_analyses_user_id ON emotion_pattern_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_emotion_pattern_analyses_result_id ON emotion_pattern_analyses(result_id);
CREATE INDEX IF NOT EXISTS idx_emotion_pattern_analyses_analysis_type ON emotion_pattern_analyses(analysis_type);

CREATE INDEX IF NOT EXISTS idx_recommendations_user_id ON recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_result_id ON recommendations(result_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_type ON recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_recommendations_priority ON recommendations(priority_level);
CREATE INDEX IF NOT EXISTS idx_recommendations_is_active ON recommendations(is_active);

-- Configuration indexes
CREATE INDEX IF NOT EXISTS idx_user_configs_user_id ON user_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_configs_is_active ON user_configs(is_active);

CREATE INDEX IF NOT EXISTS idx_user_presentation_configs_user_id ON user_presentation_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_presentation_configs_is_active ON user_presentation_configs(is_active);

CREATE INDEX IF NOT EXISTS idx_pack_presentation_overrides_user_id ON pack_presentation_overrides(user_id);
CREATE INDEX IF NOT EXISTS idx_pack_presentation_overrides_pack_id ON pack_presentation_overrides(pack_id);

CREATE INDEX IF NOT EXISTS idx_quiz_session_presentation_configs_session_id ON quiz_session_presentation_configs(session_id);

CREATE INDEX IF NOT EXISTS idx_question_presentation_overrides_user_id ON question_presentation_overrides(user_id);
CREATE INDEX IF NOT EXISTS idx_question_presentation_overrides_question_id ON question_presentation_overrides(question_id);

CREATE INDEX IF NOT EXISTS idx_pack_presentation_configs_pack_id ON pack_presentation_configs(pack_id);
CREATE INDEX IF NOT EXISTS idx_pack_presentation_configs_config_type ON pack_presentation_configs(config_type);
