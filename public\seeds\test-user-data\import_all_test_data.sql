-- Import All Test Data Script
-- This script imports all test data for development and testing purposes
-- Run this after setting up the main database schema

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Start transaction for atomic import
BEGIN TRANSACTION;

-- ============================================================================
-- IMPORT ORDER (respecting foreign key dependencies)
-- ============================================================================

-- 1. First import basic configuration data (tags, VIP plans, etc.)
.read ../config/insert_tags_data.sql
.read ../config/insert_vip_data.sql

-- 2. Import user unlock data (depends on users and content tables)
.read user_unlocks.sql

-- 3. Import subscription data (depends on users table)
.read user_subscriptions.sql

-- 4. Import quiz result tags (depends on quiz_results and tags tables)
.read quiz_result_tags.sql

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Summary of imported test data
SELECT '=== TEST DATA IMPORT SUMMARY ===' as section;

-- Tags data
SELECT 'Tags:' as table_name, COUNT(*) as record_count FROM tags;
SELECT 'Tag Translations:' as table_name, COUNT(*) as record_count FROM tag_translations;

-- VIP system data
SELECT 'VIP Plans:' as table_name, COUNT(*) as record_count FROM vip_plans;
SELECT 'VIP Features:' as table_name, COUNT(*) as record_count FROM vip_features;

-- User unlock data
SELECT 'User Skin Unlocks:' as table_name, COUNT(*) as record_count FROM user_skin_unlocks;
SELECT 'User Emoji Set Unlocks:' as table_name, COUNT(*) as record_count FROM user_emoji_set_unlocks;

-- Subscription data
SELECT 'User Subscription History:' as table_name, COUNT(*) as record_count FROM user_subscription_history;

-- Quiz result tags
SELECT 'Quiz Result Tags:' as table_name, COUNT(*) as record_count FROM quiz_result_tags;

-- ============================================================================
-- DATA INTEGRITY CHECKS
-- ============================================================================

SELECT '=== DATA INTEGRITY CHECKS ===' as section;

-- Check for orphaned quiz result tags (quiz_results that don't exist)
SELECT 'Orphaned Quiz Result Tags:' as check_name, COUNT(*) as issue_count
FROM quiz_result_tags qrt
LEFT JOIN quiz_results qr ON qrt.quiz_result_id = qr.id
WHERE qr.id IS NULL;

-- Check for orphaned tag references
SELECT 'Orphaned Tag References:' as check_name, COUNT(*) as issue_count
FROM quiz_result_tags qrt
LEFT JOIN tags t ON qrt.tag_id = t.id
WHERE t.id IS NULL;

-- Check for orphaned user references in unlocks
SELECT 'Orphaned User Skin Unlocks:' as check_name, COUNT(*) as issue_count
FROM user_skin_unlocks usu
LEFT JOIN users u ON usu.user_id = u.id
WHERE u.id IS NULL;

SELECT 'Orphaned User Emoji Unlocks:' as check_name, COUNT(*) as issue_count
FROM user_emoji_set_unlocks uesu
LEFT JOIN users u ON uesu.user_id = u.id
WHERE u.id IS NULL;

-- Check for orphaned user references in subscriptions
SELECT 'Orphaned User Subscriptions:' as check_name, COUNT(*) as issue_count
FROM user_subscription_history ush
LEFT JOIN users u ON ush.user_id = u.id
WHERE u.id IS NULL;

-- ============================================================================
-- USAGE STATISTICS
-- ============================================================================

SELECT '=== USAGE STATISTICS ===' as section;

-- Tag usage by category
SELECT 
    COALESCE(t.category, 'uncategorized') as category,
    COUNT(DISTINCT t.id) as total_tags,
    COUNT(qrt.tag_id) as total_usage,
    ROUND(AVG(qrt.confidence_score), 2) as avg_confidence
FROM tags t
LEFT JOIN quiz_result_tags qrt ON t.id = qrt.tag_id
GROUP BY t.category
ORDER BY total_usage DESC;

-- Unlock method distribution
SELECT 
    unlock_method,
    COUNT(*) as skin_unlocks,
    (SELECT COUNT(*) FROM user_emoji_set_unlocks WHERE unlock_method = usu.unlock_method) as emoji_unlocks,
    COUNT(*) + (SELECT COUNT(*) FROM user_emoji_set_unlocks WHERE unlock_method = usu.unlock_method) as total_unlocks
FROM user_skin_unlocks usu
GROUP BY unlock_method
ORDER BY total_unlocks DESC;

-- Subscription status distribution
SELECT 
    status,
    COUNT(*) as subscription_count,
    ROUND(AVG(amount), 2) as avg_amount,
    ROUND(SUM(amount), 2) as total_revenue
FROM user_subscription_history
GROUP BY status
ORDER BY subscription_count DESC;

-- ============================================================================
-- SAMPLE QUERIES FOR TESTING
-- ============================================================================

SELECT '=== SAMPLE TEST QUERIES ===' as section;

-- Get a user's quiz results with tags
SELECT 
    'Sample: User Quiz Results with Tags' as query_description;

-- Get a user's unlocked content
SELECT 
    'Sample: User Unlocked Content' as query_description;

-- Get active VIP subscribers
SELECT 
    'Sample: Active VIP Subscribers' as query_description;

-- Commit the transaction
COMMIT;

-- Final success message
SELECT '=== TEST DATA IMPORT COMPLETED SUCCESSFULLY ===' as status;
SELECT 'All test data has been imported and verified.' as message;
SELECT 'You can now run the sample queries to test the system.' as next_steps;
