{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/error-handling/error-handling.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 55101, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 55101, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 524, "endOffset": 19943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 628, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 681, "endOffset": 3646, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 736, "endOffset": 1709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1760, "endOffset": 2653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2704, "endOffset": 3638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3701, "endOffset": 8368, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3759, "endOffset": 5369, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5423, "endOffset": 6696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6748, "endOffset": 8360, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8422, "endOffset": 11456, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8478, "endOffset": 9372, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9425, "endOffset": 10348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10401, "endOffset": 11448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11511, "endOffset": 15827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11565, "endOffset": 13111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12226, "endOffset": 12577, "count": 3}, {"startOffset": 12539, "endOffset": 12558, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12630, "endOffset": 13099, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13161, "endOffset": 14597, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13301, "endOffset": 14084, "count": 2}, {"startOffset": 13972, "endOffset": 14065, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14649, "endOffset": 15819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15881, "endOffset": 19939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15936, "endOffset": 16823, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16873, "endOffset": 17773, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17826, "endOffset": 18838, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18890, "endOffset": 19931, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}