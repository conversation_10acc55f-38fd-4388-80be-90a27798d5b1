-- Insert Tags and Related Data
-- This script populates the tags system with default tags and translations

-- ============================================================================
-- TAGS DATA
-- ============================================================================

INSERT OR REPLACE INTO tags (id, name, description, category, color, icon, is_system, is_active, usage_count, created_by, created_at, updated_at) VALUES
-- Context tags
('tag_work', '工作', '与工作相关的情绪和体验', 'context', '#4A90E2', 'briefcase', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_family', '家庭', '与家庭成员相关的情绪和体验', 'context', '#F5A623', 'home', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_friends', '朋友', '与朋友社交相关的情绪和体验', 'context', '#7ED321', 'users', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_health', '健康', '与身体健康相关的情绪和体验', 'context', '#50E3C2', 'heart', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Activity tags
('tag_exercise', '运动', '与运动锻炼相关的情绪和体验', 'activity', '#BD10E0', 'activity', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_study', '学习', '与学习教育相关的情绪和体验', 'activity', '#9013FE', 'book', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_rest', '休息', '与休息放松相关的情绪和体验', 'activity', '#8E8E93', 'moon', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_travel', '旅行', '与旅行出游相关的情绪和体验', 'activity', '#FF9500', 'map', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_hobby', '爱好', '与个人爱好相关的情绪和体验', 'activity', '#FF2D92', 'star', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Emotion tags
('tag_stress', '压力', '感受到压力的情况', 'emotion', '#FF3B30', 'alert-triangle', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_anxiety', '焦虑', '感受到焦虑的情况', 'emotion', '#FF9500', 'alert-circle', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_happiness', '快乐', '感受到快乐的情况', 'emotion', '#FFCC02', 'smile', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_sadness', '悲伤', '感受到悲伤的情况', 'emotion', '#007AFF', 'frown', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_anger', '愤怒', '感受到愤怒的情况', 'emotion', '#FF3B30', 'angry', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_calm', '平静', '感受到平静的情况', 'emotion', '#34C759', 'peace', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Time tags
('tag_morning', '早晨', '早晨时间段的情绪记录', 'time', '#FFCC02', 'sunrise', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_afternoon', '下午', '下午时间段的情绪记录', 'time', '#FF9500', 'sun', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_evening', '晚上', '晚上时间段的情绪记录', 'time', '#5856D6', 'sunset', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_night', '深夜', '深夜时间段的情绪记录', 'time', '#1D1D1F', 'moon', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_weekend', '周末', '周末时间的情绪记录', 'time', '#AF52DE', 'calendar', 1, 1, 0, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ============================================================================
-- TAG TRANSLATIONS
-- ============================================================================

INSERT OR REPLACE INTO tag_translations (tag_id, language_code, translated_name, translated_description, translation_quality, translator_type, created_at, updated_at) VALUES
-- English translations
('tag_work', 'en', 'Work', 'Emotions and experiences related to work', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_family', 'en', 'Family', 'Emotions and experiences related to family members', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_friends', 'en', 'Friends', 'Emotions and experiences related to social interactions with friends', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_health', 'en', 'Health', 'Emotions and experiences related to physical health', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_exercise', 'en', 'Exercise', 'Emotions and experiences related to physical exercise', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_study', 'en', 'Study', 'Emotions and experiences related to learning and education', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_rest', 'en', 'Rest', 'Emotions and experiences related to rest and relaxation', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_travel', 'en', 'Travel', 'Emotions and experiences related to travel and trips', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_hobby', 'en', 'Hobby', 'Emotions and experiences related to personal hobbies', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_stress', 'en', 'Stress', 'Situations where stress is felt', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_anxiety', 'en', 'Anxiety', 'Situations where anxiety is felt', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_happiness', 'en', 'Happiness', 'Situations where happiness is felt', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_sadness', 'en', 'Sadness', 'Situations where sadness is felt', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_anger', 'en', 'Anger', 'Situations where anger is felt', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_calm', 'en', 'Calm', 'Situations where calmness is felt', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_morning', 'en', 'Morning', 'Emotional records during morning hours', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_afternoon', 'en', 'Afternoon', 'Emotional records during afternoon hours', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_evening', 'en', 'Evening', 'Emotional records during evening hours', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_night', 'en', 'Night', 'Emotional records during late night hours', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_weekend', 'en', 'Weekend', 'Emotional records during weekend time', 1.0, 'human', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Spanish translations
('tag_work', 'es', 'Trabajo', 'Emociones y experiencias relacionadas con el trabajo', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_family', 'es', 'Familia', 'Emociones y experiencias relacionadas con miembros de la familia', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_friends', 'es', 'Amigos', 'Emociones y experiencias relacionadas con interacciones sociales con amigos', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_health', 'es', 'Salud', 'Emociones y experiencias relacionadas con la salud física', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_exercise', 'es', 'Ejercicio', 'Emociones y experiencias relacionadas con el ejercicio físico', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_study', 'es', 'Estudio', 'Emociones y experiencias relacionadas con el aprendizaje y la educación', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_rest', 'es', 'Descanso', 'Emociones y experiencias relacionadas con el descanso y la relajación', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_travel', 'es', 'Viaje', 'Emociones y experiencias relacionadas con viajes y excursiones', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_hobby', 'es', 'Pasatiempo', 'Emociones y experiencias relacionadas con pasatiempos personales', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_stress', 'es', 'Estrés', 'Situaciones donde se siente estrés', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_anxiety', 'es', 'Ansiedad', 'Situaciones donde se siente ansiedad', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_happiness', 'es', 'Felicidad', 'Situaciones donde se siente felicidad', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_sadness', 'es', 'Tristeza', 'Situaciones donde se siente tristeza', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_anger', 'es', 'Ira', 'Situaciones donde se siente ira', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tag_calm', 'es', 'Calma', 'Situaciones donde se siente calma', 0.9, 'ai', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify tags were inserted
SELECT 'Tags Count:' as info, COUNT(*) as count FROM tags;

-- Verify tag translations were inserted
SELECT 'Tag Translations Count:' as info, COUNT(*) as count FROM tag_translations;

-- Show all tags with their categories
SELECT 
    t.id,
    t.name,
    t.category,
    t.color,
    t.icon,
    t.is_system,
    t.usage_count
FROM tags t
ORDER BY t.category, t.name;

-- Show tag translations summary
SELECT 
    tt.language_code,
    COUNT(*) as translation_count,
    AVG(tt.translation_quality) as avg_quality
FROM tag_translations tt
GROUP BY tt.language_code
ORDER BY tt.language_code;
