import { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Wand2, Bug, Code, Database, TestTube, 
  Monitor, Zap, AlertTriangle, Eye
} from "lucide-react";
import { toast } from "sonner";

const DeveloperSettings: React.FC = () => {
  const { t } = useLanguage();
  
  // 开发者选项状态
  const [debugMode, setDebugMode] = useState(false);
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false);
  const [enableExperimentalFeatures, setEnableExperimentalFeatures] = useState(false);
  const [verboseLogging, setVerboseLogging] = useState(false);

  // 处理调试模式切换
  const handleDebugModeToggle = (enabled: boolean) => {
    setDebugMode(enabled);
    localStorage.setItem('debug-mode', enabled.toString());
    
    if (enabled) {
      console.log('[Developer] Debug mode enabled');
      window.DEBUG = true;
    } else {
      console.log('[Developer] Debug mode disabled');
      window.DEBUG = false;
    }
    
    toast.success(enabled ? 
      t('settings.debug_mode_enabled', '调试模式已启用') : 
      t('settings.debug_mode_disabled', '调试模式已禁用')
    );
  };

  // 处理性能指标显示
  const handlePerformanceMetricsToggle = (enabled: boolean) => {
    setShowPerformanceMetrics(enabled);
    localStorage.setItem('show-performance-metrics', enabled.toString());
    
    if (enabled) {
      // 显示性能指标
      document.body.classList.add('show-performance-metrics');
    } else {
      document.body.classList.remove('show-performance-metrics');
    }
    
    toast.success(enabled ? 
      t('settings.performance_metrics_enabled', '性能指标已启用') : 
      t('settings.performance_metrics_disabled', '性能指标已禁用')
    );
  };

  // 处理实验性功能
  const handleExperimentalFeaturesToggle = (enabled: boolean) => {
    setEnableExperimentalFeatures(enabled);
    localStorage.setItem('experimental-features', enabled.toString());
    
    toast.success(enabled ? 
      t('settings.experimental_features_enabled', '实验性功能已启用') : 
      t('settings.experimental_features_disabled', '实验性功能已禁用')
    );
    
    if (enabled) {
      toast.warning(t('settings.experimental_warning', '实验性功能可能不稳定'));
    }
  };

  // 处理详细日志
  const handleVerboseLoggingToggle = (enabled: boolean) => {
    setVerboseLogging(enabled);
    localStorage.setItem('verbose-logging', enabled.toString());
    
    toast.success(enabled ? 
      t('settings.verbose_logging_enabled', '详细日志已启用') : 
      t('settings.verbose_logging_disabled', '详细日志已禁用')
    );
  };

  // 清除所有缓存
  const handleClearCache = () => {
    try {
      localStorage.clear();
      sessionStorage.clear();
      
      // 清除IndexedDB (如果使用)
      if ('indexedDB' in window) {
        indexedDB.deleteDatabase('mindful-mood-cache');
      }
      
      toast.success(t('settings.cache_cleared', '缓存已清除'));
    } catch (error) {
      toast.error(t('settings.cache_clear_failed', '清除缓存失败'));
    }
  };

  // 重置所有设置
  const handleResetSettings = () => {
    if (confirm(t('settings.reset_confirm', '确定要重置所有设置吗？'))) {
      try {
        // 清除设置相关的localStorage
        const settingsKeys = [
          'theme', 'language', 'user-config', 'debug-mode',
          'show-performance-metrics', 'experimental-features',
          'verbose-logging', 'reduce-motion', 'high-contrast', 'large-text'
        ];
        
        settingsKeys.forEach(key => localStorage.removeItem(key));
        
        toast.success(t('settings.settings_reset', '设置已重置'));
        
        // 刷新页面以应用默认设置
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } catch (error) {
        toast.error(t('settings.reset_failed', '重置失败'));
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-2">
        <Wand2 className="h-6 w-6" />
        <h2 className="text-2xl font-bold">
          {t('settings.developer', '开发者选项')}
        </h2>
        <Badge variant="outline" className="flex items-center space-x-1">
          <Code className="h-3 w-3" />
          <span>{t('settings.advanced', '高级')}</span>
        </Badge>
      </div>

      {/* 警告提示 */}
      <Card className="border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                {t('settings.developer_warning', '开发者选项警告')}
              </p>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">
                {t('settings.developer_warning.desc', '这些选项仅供开发和调试使用，可能影响应用性能和稳定性。')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 调试选项 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bug className="h-5 w-5" />
            <span>{t('settings.debug_options', '调试选项')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 调试模式 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('settings.debug_mode', '调试模式')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.debug_mode.desc', '启用控制台调试信息')}
              </p>
            </div>
            <Switch
              checked={debugMode}
              onCheckedChange={handleDebugModeToggle}
            />
          </div>

          <Separator />

          {/* 详细日志 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('settings.verbose_logging', '详细日志')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.verbose_logging.desc', '记录详细的操作日志')}
              </p>
            </div>
            <Switch
              checked={verboseLogging}
              onCheckedChange={handleVerboseLoggingToggle}
            />
          </div>

          <Separator />

          {/* 性能指标 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('settings.performance_metrics', '性能指标')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.performance_metrics.desc', '显示渲染性能信息')}
              </p>
            </div>
            <Switch
              checked={showPerformanceMetrics}
              onCheckedChange={handlePerformanceMetricsToggle}
            />
          </div>
        </CardContent>
      </Card>

      {/* 实验性功能 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TestTube className="h-5 w-5" />
            <span>{t('settings.experimental_features', '实验性功能')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('settings.enable_experimental', '启用实验性功能')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.experimental.desc', '访问未发布的新功能')}
              </p>
            </div>
            <Switch
              checked={enableExperimentalFeatures}
              onCheckedChange={handleExperimentalFeaturesToggle}
            />
          </div>

          {enableExperimentalFeatures && (
            <>
              <Separator />
              <div className="space-y-2">
                <p className="text-sm font-medium">
                  {t('settings.available_experiments', '可用的实验性功能')}
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  <Button variant="outline" size="sm" disabled>
                    <Zap className="h-4 w-4 mr-2" />
                    {t('settings.webgpu_renderer', 'WebGPU渲染器')}
                  </Button>
                  <Button variant="outline" size="sm" disabled>
                    <Eye className="h-4 w-4 mr-2" />
                    {t('settings.ar_mode', 'AR模式')}
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 测试工具 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Monitor className="h-5 w-5" />
            <span>{t('settings.test_tools', '测试工具')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {t('settings.test_tools.desc', '用于测试和开发的工具')}
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Button variant="outline" onClick={() => window.open('/test/views', '_blank')}>
              <Eye className="h-4 w-4 mr-2" />
              {t('settings.view_tests', '视图测试')}
            </Button>
            
            <Button variant="outline" onClick={() => window.open('/test/database', '_blank')}>
              <Database className="h-4 w-4 mr-2" />
              {t('settings.database_tests', '数据库测试')}
            </Button>
            
            <Button variant="outline" onClick={() => window.open('/test/performance', '_blank')}>
              <Zap className="h-4 w-4 mr-2" />
              {t('settings.performance_tests', '性能测试')}
            </Button>
            
            <Button variant="outline" onClick={() => window.open('/test/components', '_blank')}>
              <Code className="h-4 w-4 mr-2" />
              {t('settings.component_tests', '组件测试')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 系统操作 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>{t('settings.system_operations', '系统操作')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Button 
              variant="outline" 
              onClick={handleClearCache}
              className="h-auto p-4 flex flex-col items-start space-y-2"
            >
              <Database className="h-4 w-4" />
              <div className="text-left">
                <div className="font-medium">{t('settings.clear_cache', '清除缓存')}</div>
                <div className="text-xs text-muted-foreground">
                  {t('settings.clear_cache.desc', '清除所有本地缓存')}
                </div>
              </div>
            </Button>

            <Button 
              variant="destructive" 
              onClick={handleResetSettings}
              className="h-auto p-4 flex flex-col items-start space-y-2"
            >
              <AlertTriangle className="h-4 w-4" />
              <div className="text-left">
                <div className="font-medium">{t('settings.reset_settings', '重置设置')}</div>
                <div className="text-xs text-muted-foreground">
                  {t('settings.reset_settings.desc', '恢复所有默认设置')}
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DeveloperSettings;
