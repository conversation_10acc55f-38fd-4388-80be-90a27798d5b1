/**
 * 配置系统 tRPC 路由
 * 支持全局应用设置和Quiz系统配置的完全分离架构
 *
 * 注意：这是API接口定义阶段，使用模拟数据
 * 服务层将在下一步实现
 */

import { z } from 'zod';
import { router, authenticatedProcedure } from '../router.js';
import { executeQuery, batchStatements } from '../database/index.js';

// 辅助函数：处理错误
const handleError = (error: unknown, operation: string) => {
  console.error(`Error during ${operation}:`, error);
  const errorMessage = error instanceof Error ? error.message : `Unknown error during ${operation}`;
  return {
    success: false,
    data: null,
    error: errorMessage
  };
};

// ==================== 全局应用设置路由 ====================

export const globalAppConfigRouter = router({
  // 获取用户的全局应用设置
  getUserConfig: authenticatedProcedure
    .input(z.object({
      config_name: z.string().optional().default('default')
    }))
    .query(async ({ input, ctx }) => {
      try {
        // 在线服务：从云端数据库获取用户配置
        const query = `
          SELECT * FROM user_configs
          WHERE user_id = ? AND name = ? AND is_active = 1
          ORDER BY last_updated DESC
          LIMIT 1
        `;

        const result = await executeQuery({
          sql: query,
          args: [ctx.user.id, input.config_name]
        });
        const config = result.rows?.[0];

        if (!config) {
          // 如果没有配置，返回默认配置
          const defaultConfig = {
            id: `global_config_${Date.now()}`,
            name: input.config_name,
            user_id: ctx.user.id,
            is_active: true,
            theme_mode: 'system',
            language: 'zh-CN',
            accessibility: JSON.stringify({
              high_contrast: false,
              large_text: false,
              reduce_motion: false,
              screen_reader_support: false
            }),
            notifications_enabled: true,
            sound_enabled: true,
            created_at: new Date().toISOString(),
            last_updated: new Date().toISOString()
          };

          return {
            success: true,
            data: defaultConfig,
            error: null
          };
        }

        // 转换数据库行为配置对象
        const globalConfig = {
          id: config.id,
          name: config.name,
          user_id: config.user_id,
          is_active: Boolean(config.is_active),
          theme_mode: config.theme_mode,
          language: config.language,
          accessibility: config.accessibility,
          notifications_enabled: Boolean(config.notifications_enabled),
          sound_enabled: Boolean(config.sound_enabled),
          created_at: config.created_at,
          last_updated: config.last_updated
        };

        return {
          success: true,
          data: globalConfig,
          error: null
        };
      } catch (error) {
        return handleError(error, 'get global app config');
      }
    }),

  // 更新用户的全局应用设置
  updateUserConfig: authenticatedProcedure
    .input(z.object({
      config_name: z.string().optional().default('default'),
      theme_mode: z.enum(['light', 'dark', 'system']).optional(),
      language: z.enum(['zh-CN', 'en-US']).optional(),
      accessibility: z.string().optional(),
      notifications_enabled: z.boolean().optional(),
      sound_enabled: z.boolean().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // 在线服务：更新云端数据库中的用户配置
        const updateFields: string[] = [];
        const updateValues: any[] = [];

        // 动态构建更新字段
        if (input.theme_mode !== undefined) {
          updateFields.push('theme_mode = ?');
          updateValues.push(input.theme_mode);
        }
        if (input.language !== undefined) {
          updateFields.push('language = ?');
          updateValues.push(input.language);
        }
        if (input.accessibility !== undefined) {
          updateFields.push('accessibility = ?');
          updateValues.push(input.accessibility);
        }
        if (input.notifications_enabled !== undefined) {
          updateFields.push('notifications_enabled = ?');
          updateValues.push(input.notifications_enabled ? 1 : 0);
        }
        if (input.sound_enabled !== undefined) {
          updateFields.push('sound_enabled = ?');
          updateValues.push(input.sound_enabled ? 1 : 0);
        }

        if (updateFields.length === 0) {
          return {
            success: false,
            data: null,
            error: 'No fields to update'
          };
        }

        // 添加last_updated字段
        updateFields.push('last_updated = ?');
        updateValues.push(new Date().toISOString());

        // 添加WHERE条件的参数
        updateValues.push(ctx.user.id);
        updateValues.push(input.config_name);

        const updateQuery = `
          UPDATE user_configs
          SET ${updateFields.join(', ')}
          WHERE user_id = ? AND name = ?
        `;

        await executeQuery({
          sql: updateQuery,
          args: updateValues
        });

        // 获取更新后的配置
        const selectQuery = `
          SELECT * FROM user_configs
          WHERE user_id = ? AND name = ? AND is_active = 1
          ORDER BY last_updated DESC
          LIMIT 1
        `;

        const result = await executeQuery({
          sql: selectQuery,
          args: [ctx.user.id, input.config_name]
        });

        const updatedConfig = result.rows?.[0];

        if (!updatedConfig) {
          return {
            success: false,
            data: null,
            error: 'Failed to retrieve updated config'
          };
        }

        // 转换数据库行为配置对象
        const globalConfig = {
          id: updatedConfig.id,
          name: updatedConfig.name,
          user_id: updatedConfig.user_id,
          is_active: Boolean(updatedConfig.is_active),
          theme_mode: updatedConfig.theme_mode,
          language: updatedConfig.language,
          accessibility: updatedConfig.accessibility,
          notifications_enabled: Boolean(updatedConfig.notifications_enabled),
          sound_enabled: Boolean(updatedConfig.sound_enabled),
          created_at: updatedConfig.created_at,
          last_updated: updatedConfig.last_updated
        };

        return {
          success: true,
          data: globalConfig,
          error: null
        };
      } catch (error) {
        return handleError(error, 'update global app config');
      }
    }),
});

// ==================== Quiz系统配置路由 ====================

export const quizConfigRouter = router({
  // 获取用户的Quiz偏好配置
  getUserPreferences: authenticatedProcedure
    .input(z.object({
      config_name: z.string().optional().default('default')
    }))
    .query(async ({ input, ctx }) => {
      try {
        // 在线服务：从云端数据库获取Quiz偏好配置
        const query = `
          SELECT * FROM user_quiz_preferences
          WHERE user_id = ? AND config_name = ? AND is_active = 1
          ORDER BY updated_at DESC
          LIMIT 1
        `;

        const result = await executeQuery({
          sql: query,
          args: [ctx.user.id, input.config_name]
        });

        const preferences = result.rows?.[0];

        if (!preferences) {
          // 如果没有配置，返回默认配置
          const defaultPreferences = {
            id: `quiz_pref_${Date.now()}`,
            user_id: ctx.user.id,
            config_name: input.config_name,
            presentation_config: JSON.stringify({
              layer0_dataset_presentation: {
                preferred_pack_categories: ['daily', 'assessment'],
                default_difficulty_preference: 'regular',
                session_length_preference: 'medium',
                auto_select_recommended: false,
                restore_progress: true
              },
              layer1_user_choice: {
                preferred_view_type: 'wheel',
                active_skin_id: 'default',
                color_mode: 'warm',
                user_level: 'regular'
              },
              layer2_rendering_strategy: {
                render_engine_preferences: {
                  wheel: 'D3',
                  card: 'SVG',
                  bubble: 'Canvas'
                },
                performance_mode: 'balanced'
              }
            }),
            config_version: '2.0',
            personalization_level: 50,
            is_active: true,
            is_default: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          return {
            success: true,
            data: defaultPreferences,
            error: null
          };
        }

        // 转换数据库行为偏好对象
        const quizPreferences = {
          id: preferences.id,
          user_id: preferences.user_id,
          config_name: preferences.config_name,
          presentation_config: preferences.presentation_config,
          config_version: preferences.config_version,
          personalization_level: preferences.personalization_level,
          is_active: Boolean(preferences.is_active),
          is_default: Boolean(preferences.is_default),
          created_at: preferences.created_at,
          updated_at: preferences.updated_at
        };

        return {
          success: true,
          data: quizPreferences,
          error: null
        };
      } catch (error) {
        return handleError(error, 'get user quiz preferences');
      }
    }),

  // 更新用户的Quiz偏好配置
  updateUserPreferences: authenticatedProcedure
    .input(z.object({
      config_name: z.string().optional().default('default'),
      presentation_config: z.string().optional(),
      personalization_level: z.number().int().min(0).max(100).optional()
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // 在线服务：简单的Quiz偏好更新
        // 注意：这里使用简化的实现，复杂的配置合并逻辑在离线服务中处理
        return {
          success: true,
          data: {
            id: `quiz_pref_${Date.now()}`,
            user_id: ctx.user.id,
            config_name: input.config_name,
            presentation_config: input.presentation_config || JSON.stringify({
              layer1_user_choice: {
                preferred_view_type: 'wheel',
                color_mode: 'warm'
              }
            }),
            config_version: '2.0',
            personalization_level: input.personalization_level || 50,
            is_active: true,
            is_default: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          error: null
        };
      } catch (error) {
        return handleError(error, 'update user quiz preferences');
      }
    }),

  // 生成Quiz会话的最终配置 (简化版)
  generateSessionConfig: authenticatedProcedure
    .input(z.object({
      pack_id: z.string(),
      session_id: z.string()
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // 在线服务：简单的会话配置生成
        // 注意：复杂的配置合并逻辑应该在离线服务中处理
        const sessionConfig = {
          id: `session_config_${Date.now()}`,
          session_id: input.session_id,
          user_id: ctx.user.id,
          pack_id: input.pack_id,
          final_presentation_config: JSON.stringify({
            layer1_user_choice: {
              preferred_view_type: 'wheel',
              color_mode: 'warm'
            },
            layer2_rendering_strategy: {
              render_engine_preferences: { wheel: 'D3' },
              performance_mode: 'balanced'
            }
          }),
          config_sources: JSON.stringify({
            base_config_id: 'default',
            applied_overrides: [],
            applied_defaults: ['system_default'],
            merge_timestamp: new Date().toISOString()
          }),
          personalization_level: 50,
          config_version: '2.0',
          created_at: new Date().toISOString()
        };

        return {
          success: true,
          data: sessionConfig,
          error: null
        };
      } catch (error) {
        return handleError(error, 'generate session config');
      }
    }),
});

// ==================== 主配置路由器 ====================

export const configRouter = router({
  // 全局应用设置
  global: globalAppConfigRouter,

  // Quiz系统配置
  quiz: quizConfigRouter,
});