/**
 * 服务端Quiz引擎服务
 *
 * 职责:
 * - 处理离线Quiz数据同步
 * - 数据冲突检测和解决
 * - Quiz数据验证和完整性检查
 * - 与客户端QuizEngineV3协调
 */

import { executeQuery, batchStatements } from '../database/index.js';
import type {
  QuizPack,
  QuizSession,
  QuizAnswer,
  QuizResult
} from '../../../src/types/schema/base.js';

interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

interface OfflineQuizData {
  sessions: QuizSession[];
  answers: QuizAnswer[];
  results?: QuizResult[];
  metadata: {
    client_timestamp: string;
    sync_version: number;
    device_id: string;
  };
}

interface QuizSyncData {
  client_data: OfflineQuizData;
  last_sync_timestamp?: string;
  conflict_resolution: 'client_wins' | 'server_wins' | 'merge';
}

/**
 * 服务端Quiz引擎服务
 *
 * 功能:
 * - 处理离线Quiz数据同步
 * - 数据冲突检测和解决
 * - Quiz数据验证和完整性检查
 * - 与客户端QuizEngineV3协调
 */
export class QuizEngineService {
  private static instance: QuizEngineService;

  private constructor() {
    // 服务端不需要复杂的初始化，直接使用标准数据库操作
  }

  public static getInstance(): QuizEngineService {
    if (!QuizEngineService.instance) {
      QuizEngineService.instance = new QuizEngineService();
    }
    return QuizEngineService.instance;
  }

  /**
   * 处理离线Quiz数据
   */
  async processOfflineQuizData(data: OfflineQuizData): Promise<ServiceResponse<{
    processed_sessions: number;
    processed_answers: number;
    processed_results: number;
    conflicts: any[];
  }>> {
    try {
      let processedSessions = 0;
      let processedAnswers = 0;
      let processedResults = 0;
      const conflicts: any[] = [];

      // 使用批量操作处理数据（替代事务）
      const statements: any[] = [];

      try {
        // 处理会话数据
        for (const session of data.sessions) {
          const existingSessionResult = await executeQuery({
            sql: 'SELECT * FROM quiz_sessions WHERE id = ?',
            args: [session.id]
          });
          const existingSession = existingSessionResult.rows[0];

          if (existingSession) {
            // 检查冲突
            if (existingSession.updated_at !== session.updated_at) {
              conflicts.push({
                type: 'session_conflict',
                id: session.id,
                server_data: existingSession,
                client_data: session
              });
              continue;
            }

            // 准备更新语句
            statements.push({
              sql: `
                UPDATE quiz_sessions SET
                  status = ?, current_question_index = ?, total_questions = ?,
                  answered_questions = ?, skipped_questions = ?, completion_percentage = ?,
                  last_active_time = ?, end_time = ?, session_metadata = ?, updated_at = ?
                WHERE id = ?
              `,
              args: [
                session.status, session.current_question_index, session.total_questions,
                session.answered_questions, session.skipped_questions, session.completion_percentage,
                session.last_active_time, session.end_time, session.session_metadata, session.updated_at,
                session.id
              ]
            });
          } else {
            // 准备创建语句
            statements.push({
              sql: `
                INSERT INTO quiz_sessions (
                  id, pack_id, user_id, status, current_question_index, total_questions,
                  answered_questions, skipped_questions, completion_percentage,
                  start_time, last_active_time, end_time, session_type, session_metadata,
                  created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `,
              args: [
                session.id, session.pack_id, session.user_id, session.status,
                session.current_question_index, session.total_questions,
                session.answered_questions, session.skipped_questions, session.completion_percentage,
                session.start_time, session.last_active_time, session.end_time,
                session.session_type, session.session_metadata,
                session.created_at, session.updated_at
              ]
            });
          }
          processedSessions++;
        }

        // 处理答案数据
        for (const answer of data.answers) {
          const existingAnswerResult = await executeQuery({
            sql: 'SELECT * FROM quiz_answers WHERE id = ?',
            args: [answer.id]
          });
          const existingAnswer = existingAnswerResult.rows[0];

          if (existingAnswer) {
            // 检查冲突
            if (existingAnswer.updated_at !== answer.updated_at) {
              conflicts.push({
                type: 'answer_conflict',
                id: answer.id,
                server_data: existingAnswer,
                client_data: answer
              });
              continue;
            }

            // 准备更新语句
            statements.push({
              sql: `
                UPDATE quiz_answers SET
                  selected_option_ids = ?, answer_value = ?, answer_text = ?,
                  confidence_score = ?, response_time_ms = ?, answered_at = ?, updated_at = ?
                WHERE id = ?
              `,
              args: [
                answer.selected_option_ids, answer.answer_value, answer.answer_text,
                answer.confidence_score, answer.response_time_ms, answer.answered_at, answer.updated_at,
                answer.id
              ]
            });
          } else {
            // 准备创建语句
            statements.push({
              sql: `
                INSERT INTO quiz_answers (
                  id, session_id, question_id, session_presentation_config_id,
                  selected_option_ids, answer_value, answer_text,
                  confidence_score, response_time_ms, answered_at,
                  created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `,
              args: [
                answer.id, answer.session_id, answer.question_id, answer.session_presentation_config_id,
                answer.selected_option_ids, answer.answer_value, answer.answer_text,
                answer.confidence_score, answer.response_time_ms, answer.answered_at,
                answer.created_at, answer.updated_at
              ]
            });
          }
          processedAnswers++;
        }

        // 处理结果数据
        if (data.results) {
          for (const result of data.results) {
            const existingResultResult = await executeQuery({
              sql: 'SELECT * FROM quiz_results WHERE id = ?',
              args: [result.id]
            });
            const existingResult = existingResultResult.rows[0];

            if (existingResult) {
              // 检查冲突
              if (existingResult.updated_at !== result.updated_at) {
                conflicts.push({
                  type: 'result_conflict',
                  id: result.id,
                  server_data: existingResult,
                  client_data: result
                });
                continue;
              }

              // 准备更新语句
              statements.push({
                sql: `
                  UPDATE quiz_results SET
                    status = ?, completion_percentage = ?, total_time_ms = ?,
                    answered_questions = ?, skipped_questions = ?, result_data = ?, updated_at = ?
                  WHERE id = ?
                `,
                args: [
                  result.status, result.completion_percentage, result.total_time_ms,
                  result.answered_questions, result.skipped_questions,
                  JSON.stringify(result.result_data), result.updated_at,
                  result.id
                ]
              });
            } else {
              // 准备创建语句
              statements.push({
                sql: `
                  INSERT INTO quiz_results (
                    id, session_id, user_id, pack_id, total_questions,
                    answered_questions, completion_percentage, total_time_ms,
                    started_at, completed_at, result_data, created_at, updated_at
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `,
                args: [
                  result.id, result.session_id, result.user_id, result.pack_id,
                  result.total_questions, result.answered_questions, result.completion_percentage,
                  result.total_time_ms, result.started_at, result.completed_at,
                  JSON.stringify(result.result_data), result.created_at, result.updated_at
                ]
              });
            }
            processedResults++;
          }
        }

        // 执行批量操作
        if (statements.length > 0) {
          await batchStatements(statements);
        }

        return {
          success: true,
          data: {
            processed_sessions: processedSessions,
            processed_answers: processedAnswers,
            processed_results: processedResults,
            conflicts
          }
        };

      } catch (error) {
        // 批量操作失败时，错误会自动回滚
        throw error;
      }

    } catch (error) {
      return {
        success: false,
        error: `Failed to process offline quiz data: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * 双向同步Quiz数据
   */
  async syncQuizData(syncData: QuizSyncData): Promise<ServiceResponse<{
    server_changes: any[];
    sync_timestamp: string;
    conflicts_resolved: number;
  }>> {
    try {
      // 处理客户端数据
      const processResult = await this.processOfflineQuizData(syncData.client_data);
      
      if (!processResult.success) {
        return processResult as any;
      }

      // 获取服务端变更
      const serverChanges = await this.getServerChanges(syncData.last_sync_timestamp);

      // 解决冲突
      const conflictsResolved = await this.resolveConflicts(
        processResult.data?.conflicts || [],
        syncData.conflict_resolution
      );

      return {
        success: true,
        data: {
          server_changes: serverChanges,
          sync_timestamp: new Date().toISOString(),
          conflicts_resolved: conflictsResolved
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `Failed to sync quiz data: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * 验证Quiz数据完整性
   */
  async validateQuizData(data: OfflineQuizData): Promise<ServiceResponse<{
    is_valid: boolean;
    validation_errors: string[];
  }>> {
    const validationErrors: string[] = [];

    try {
      // 验证会话数据
      for (const session of data.sessions) {
        if (!session.id || !session.pack_id || !session.user_id) {
          validationErrors.push(`Invalid session data: missing required fields for session ${session.id}`);
        }

        // 验证Quiz包存在
        const packResult = await executeQuery({
          sql: 'SELECT id FROM quiz_packs WHERE id = ?',
          args: [session.pack_id]
        });
        if (packResult.rows.length === 0) {
          validationErrors.push(`Quiz pack not found: ${session.pack_id} for session ${session.id}`);
        }
      }

      // 验证答案数据
      for (const answer of data.answers) {
        if (!answer.id || !answer.session_id || !answer.question_id) {
          validationErrors.push(`Invalid answer data: missing required fields for answer ${answer.id}`);
        }

        // 验证会话存在
        const sessionExistsInData = data.sessions.some(s => s.id === answer.session_id);
        let sessionExistsInDB = false;

        if (!sessionExistsInData) {
          const sessionResult = await executeQuery({
            sql: 'SELECT id FROM quiz_sessions WHERE id = ?',
            args: [answer.session_id]
          });
          sessionExistsInDB = sessionResult.rows.length > 0;
        }

        if (!sessionExistsInData && !sessionExistsInDB) {
          validationErrors.push(`Session not found: ${answer.session_id} for answer ${answer.id}`);
        }

        // 验证问题存在
        const questionResult = await executeQuery({
          sql: 'SELECT id FROM quiz_questions WHERE id = ?',
          args: [answer.question_id]
        });
        if (questionResult.rows.length === 0) {
          validationErrors.push(`Question not found: ${answer.question_id} for answer ${answer.id}`);
        }
      }

      return {
        success: true,
        data: {
          is_valid: validationErrors.length === 0,
          validation_errors: validationErrors
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `Failed to validate quiz data: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // 私有辅助方法

  private async getServerChanges(lastSyncTimestamp?: string): Promise<any[]> {
    const whereClause = lastSyncTimestamp
      ? 'WHERE updated_at > ?'
      : '';
    const params = lastSyncTimestamp ? [lastSyncTimestamp] : [];

    const [sessionsResult, answersResult, resultsResult] = await Promise.all([
      executeQuery({
        sql: `SELECT * FROM quiz_sessions ${whereClause}`,
        args: params
      }),
      executeQuery({
        sql: `SELECT * FROM quiz_answers ${whereClause}`,
        args: params
      }),
      executeQuery({
        sql: `SELECT * FROM quiz_results ${whereClause}`,
        args: params
      })
    ]);

    return [
      ...sessionsResult.rows.map((s: any) => ({ type: 'session', data: s })),
      ...answersResult.rows.map((a: any) => ({ type: 'answer', data: a })),
      ...resultsResult.rows.map((r: any) => ({ type: 'result', data: r }))
    ];
  }

  private async resolveConflicts(conflicts: any[], resolution: string): Promise<number> {
    let resolved = 0;

    for (const conflict of conflicts) {
      try {
        switch (resolution) {
          case 'client_wins':
            // 客户端数据覆盖服务端
            await this.applyClientData(conflict);
            resolved++;
            break;
          case 'server_wins':
            // 保持服务端数据，忽略客户端
            resolved++;
            break;
          case 'merge':
            // 尝试合并数据
            await this.mergeConflictData(conflict);
            resolved++;
            break;
        }
      } catch (error) {
        console.error(`Failed to resolve conflict for ${conflict.type}:`, error);
      }
    }

    return resolved;
  }

  private async applyClientData(conflict: any): Promise<void> {
    const { type, client_data } = conflict;

    switch (type) {
      case 'session_conflict':
        await executeQuery({
          sql: `
            UPDATE quiz_sessions SET
              status = ?, current_question_index = ?, total_questions = ?,
              answered_questions = ?, skipped_questions = ?, completion_percentage = ?,
              last_active_time = ?, end_time = ?, session_metadata = ?, updated_at = ?
            WHERE id = ?
          `,
          args: [
            client_data.status, client_data.current_question_index, client_data.total_questions,
            client_data.answered_questions, client_data.skipped_questions, client_data.completion_percentage,
            client_data.last_active_time, client_data.end_time, client_data.session_metadata, client_data.updated_at,
            client_data.id
          ]
        });
        break;
      case 'answer_conflict':
        await executeQuery({
          sql: `
            UPDATE quiz_answers SET
              selected_option_ids = ?, answer_value = ?, answer_text = ?,
              confidence_score = ?, response_time_ms = ?, answered_at = ?, updated_at = ?
            WHERE id = ?
          `,
          args: [
            client_data.selected_option_ids, client_data.answer_value, client_data.answer_text,
            client_data.confidence_score, client_data.response_time_ms, client_data.answered_at, client_data.updated_at,
            client_data.id
          ]
        });
        break;
      case 'result_conflict':
        await executeQuery({
          sql: `
            UPDATE quiz_results SET
              status = ?, completion_percentage = ?, total_time_ms = ?,
              answered_questions = ?, skipped_questions = ?, result_data = ?, updated_at = ?
            WHERE id = ?
          `,
          args: [
            client_data.status, client_data.completion_percentage, client_data.total_time_ms,
            client_data.answered_questions, client_data.skipped_questions,
            JSON.stringify(client_data.result_data), client_data.updated_at,
            client_data.id
          ]
        });
        break;
    }
  }

  private async mergeConflictData(conflict: any): Promise<void> {
    // 简化的合并逻辑：使用最新的updated_at时间戳
    const { server_data, client_data } = conflict;

    const serverTime = new Date(server_data.updated_at).getTime();
    const clientTime = new Date(client_data.updated_at).getTime();

    if (clientTime > serverTime) {
      await this.applyClientData(conflict);
    }
    // 否则保持服务端数据
  }
}
