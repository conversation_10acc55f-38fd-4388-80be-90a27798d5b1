/**
 * 简化版认证钩子
 * 专注于基本的登录和注册功能，使用tRPC客户端
 */

import { trpc } from '@/lib/trpc';
import { Services } from '@/services';
import { useCallback, useEffect, useState } from 'react';

// 认证相关类型定义
interface AuthUser {
  id: string;
  email: string;
  username?: string;
  displayName?: string;
  isVip: boolean;
  vipExpiresAt?: Date;
  created_at: Date;
  updated_at: Date;
}

interface AuthState {
  isAuthenticated: boolean;
  user?: AuthUser;
  token?: string;
  isLoading: boolean;
  error?: string;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  username?: string;
  displayName?: string;
}

interface UseAuthReturn extends AuthState {
  // 认证操作
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
  register: (data: RegisterData) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<{ success: boolean; error?: string }>;
  clearError: () => void;
}

export function useAuthSimple(): UseAuthReturn {
  // 状态管理
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: undefined,
    token: undefined,
    isLoading: true,
    error: undefined,
  });

  // 保存认证状态到服务层
  const saveAuthToStorage = useCallback(async (state: AuthState) => {
    try {
      const authData = {
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
      };

      // 优先保存到服务层
      try {
        const userConfigService = await Services.userConfig();
        const configResult = await userConfigService.getByUserId('default-user');

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          const userConfig = configResult.data[0];
          await userConfigService.update(userConfig.id, {
            auth_state: JSON.stringify(authData)
          });
          console.log('[useAuthSimple] Saved auth state to service layer');
        }
      } catch (serviceError) {
        console.warn('[useAuthSimple] Failed to save auth to service layer:', serviceError);
        // 回退到 localStorage
        localStorage.setItem('auth_state', JSON.stringify(authData));
        console.log('[useAuthSimple] Saved auth state to localStorage fallback');
      }
    } catch (error) {
      console.error('[useAuthSimple] Error saving auth to storage:', error);
    }
  }, []);

  // 从服务层加载认证状态
  const loadAuthFromStorage = useCallback(async () => {
    try {
      // 优先从服务层加载认证状态
      try {
        const userConfigService = await Services.userConfig();
        const configResult = await userConfigService.getByUserId('default-user');

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          const userConfig = configResult.data[0];
          const authData = userConfig.auth_state ? JSON.parse(userConfig.auth_state) : null;

          if (authData) {
            setAuthState((prev: AuthState) => ({
              ...prev,
              isAuthenticated: authData.isAuthenticated || false,
              user: authData.user,
              token: authData.token,
              isLoading: false,
            }));
            console.log('[useAuthSimple] Loaded auth state from service layer');
            return;
          }
        }
      } catch (serviceError) {
        console.warn('[useAuthSimple] Failed to load auth from service layer:', serviceError);
      }

      // 回退到 localStorage
      const storedAuth = localStorage.getItem('auth_state');
      if (storedAuth) {
        const parsedAuth = JSON.parse(storedAuth);
        setAuthState((prev: AuthState) => ({
          ...prev,
          isAuthenticated: parsedAuth.isAuthenticated || false,
          user: parsedAuth.user,
          token: parsedAuth.token,
          isLoading: false,
        }));
        console.log('[useAuthSimple] Loaded auth state from localStorage fallback');
      } else {
        setAuthState((prev: AuthState) => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('[useAuthSimple] Error loading auth from storage:', error);
      setAuthState((prev: AuthState) => ({ ...prev, isLoading: false }));
    }
  }, []);

  // 登录
  const login = useCallback(
    async (credentials: LoginCredentials): Promise<{ success: boolean; error?: string }> => {
      try {
        setAuthState((prev: AuthState) => ({ ...prev, isLoading: true, error: undefined }));

        console.log('[useAuthSimple] Attempting login...');
        const result = await trpc.login.mutate({
          email: credentials.email,
          password: credentials.password,
        });

        if (result.success && result.data) {
          const newAuthState: AuthState = {
            isAuthenticated: true,
            user: {
              id: result.data.user.id,
              email: result.data.user.email,
              username: result.data.user.username,
              displayName: result.data.user.displayName,
              isVip: result.data.user.isVip || false,
              vipExpiresAt: result.data.user.vipExpiresAt
                ? new Date(result.data.user.vipExpiresAt)
                : undefined,
              created_at: new Date(result.data.user.created_at),
              updated_at: new Date(result.data.user.updated_at),
            },
            token: result.data.token,
            isLoading: false,
            error: undefined,
          };

          setAuthState(newAuthState);
          saveAuthToStorage(newAuthState);

          console.log('[useAuthSimple] Login successful');
          return { success: true };
        }
        throw new Error(result.error || 'Login failed');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Login failed';
        setAuthState((prev: AuthState) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        console.error('[useAuthSimple] Login failed:', errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    [saveAuthToStorage]
  );

  // 注册
  const register = useCallback(
    async (data: RegisterData): Promise<{ success: boolean; error?: string }> => {
      try {
        setAuthState((prev: AuthState) => ({ ...prev, isLoading: true, error: undefined }));

        console.log('[useAuthSimple] Attempting registration...');
        const result = await trpc.register.mutate({
          email: data.email,
          password: data.password,
          username: data.username,
          displayName: data.displayName,
        });

        if (result.success && result.data) {
          const newAuthState: AuthState = {
            isAuthenticated: true,
            user: {
              id: result.data.user.id,
              email: result.data.user.email,
              username: result.data.user.username,
              displayName: result.data.user.displayName,
              isVip: result.data.user.isVip || false,
              vipExpiresAt: result.data.user.vipExpiresAt
                ? new Date(result.data.user.vipExpiresAt)
                : undefined,
              created_at: new Date(result.data.user.created_at),
              updated_at: new Date(result.data.user.updated_at),
            },
            token: result.data.token,
            isLoading: false,
            error: undefined,
          };

          setAuthState(newAuthState);
          saveAuthToStorage(newAuthState);

          console.log('[useAuthSimple] Registration successful');
          return { success: true };
        }
        throw new Error(result.error || 'Registration failed');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Registration failed';
        setAuthState((prev: AuthState) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        console.error('[useAuthSimple] Registration failed:', errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    [saveAuthToStorage]
  );

  // 登出
  const logout = useCallback(async (): Promise<{ success: boolean; error?: string }> => {
    try {
      setAuthState((prev: AuthState) => ({ ...prev, isLoading: true }));

      // 尝试在线登出（如果有实现的话）
      try {
        await trpc.logout.mutate();
      } catch (error) {
        console.warn('[useAuthSimple] Online logout failed:', error);
      }

      // 清除本地状态
      const newAuthState: AuthState = {
        isAuthenticated: false,
        user: undefined,
        token: undefined,
        isLoading: false,
        error: undefined,
      };

      setAuthState(newAuthState);

      // 清除服务层的认证状态
      try {
        const userConfigService = await Services.userConfig();
        const configResult = await userConfigService.getByUserId('default-user');

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          const userConfig = configResult.data[0];
          await userConfigService.update(userConfig.id, { auth_state: null });
          console.log('[useAuthSimple] Cleared auth state from service layer');
        }
      } catch (serviceError) {
        console.warn('[useAuthSimple] Failed to clear auth from service layer:', serviceError);
        // 回退到 localStorage
        localStorage.removeItem('auth_state');
        console.log('[useAuthSimple] Cleared auth state from localStorage fallback');
      }

      console.log('[useAuthSimple] Logout successful');
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Logout failed';
      setAuthState((prev: AuthState) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      console.error('[useAuthSimple] Logout failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }, []);

  // 清除错误
  const clearError = useCallback(() => {
    setAuthState((prev: AuthState) => ({ ...prev, error: undefined }));
  }, []);

  // 初始化
  useEffect(() => {
    loadAuthFromStorage();
  }, [loadAuthFromStorage]);

  return {
    ...authState,
    login,
    register,
    logout,
    clearError,
  };
}
