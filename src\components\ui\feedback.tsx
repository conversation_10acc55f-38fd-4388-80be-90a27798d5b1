import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle2, Info, XCircle } from 'lucide-react';
import { Transition } from './transition';

interface FeedbackProps {
  type?: 'success' | 'error' | 'info' | 'warning';
  message: string;
  className?: string;
  show?: boolean;
  onClose?: () => void;
}

const icons = {
  success: CheckCircle2,
  error: XCircle,
  info: Info,
  warning: AlertCircle,
};

const styles = {
  success: 'bg-green-50 text-green-800 border-green-200',
  error: 'bg-red-50 text-red-800 border-red-200',
  info: 'bg-blue-50 text-blue-800 border-blue-200',
  warning: 'bg-yellow-50 text-yellow-800 border-yellow-200',
};

export const Feedback = ({
  type = 'info',
  message,
  className,
  show = true,
  onClose,
}: FeedbackProps) => {
  const Icon = icons[type];

  return (
    <Transition show={show} type="fade">
      <div className={cn('flex items-center gap-2 p-4 rounded-lg border', styles[type], className)}>
        <Icon className="h-5 w-5 flex-shrink-0" />
        <p className="text-sm font-medium">{message}</p>
        {onClose && (
          <button onClick={onClose} className="ml-auto flex-shrink-0 hover:opacity-70">
            <XCircle className="h-5 w-5" />
          </button>
        )}
      </div>
    </Transition>
  );
};

export const ToastFeedback = ({
  type = 'info',
  message,
  className,
}: Omit<FeedbackProps, 'show' | 'onClose'>) => {
  return (
    <Feedback
      type={type}
      message={message}
      className={cn('fixed bottom-4 left-1/2 -translate-x-1/2 shadow-lg', className)}
    />
  );
};
