-- 测试用户配置数据
-- 为测试用户创建完整的配置数据

-- 1. 默认测试用户的基础配置
INSERT OR IGNORE INTO user_configs (
  id, user_id, config_type, config_key, config_value,
  is_active, created_at, updated_at
) VALUES 
-- 语言配置
('test_user_lang', 'test-user-001', 'language', 'preferred_language', '"zh-CN"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('test_user_fallback', 'test-user-001', 'language', 'fallback_language', '"en-US"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 皮肤/主题配置
('test_user_skin', 'test-user-001', 'appearance', 'selected_skin_id', '"default-skin"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('test_user_theme', 'test-user-001', 'appearance', 'color_mode', '"light"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Quiz偏好配置
('test_user_default_quiz', 'test-user-001', 'quiz', 'default_quiz_pack_id', '"mood_track_quest_main"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('test_user_quiz_style', 'test-user-001', 'quiz', 'preferred_quiz_style', '"multiple_choice"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('test_user_difficulty', 'test-user-001', 'quiz', 'preferred_difficulty', '2', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 个性化设置
('test_user_personalization', 'test-user-001', 'personalization', 'personalization_level', '45', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('test_user_performance', 'test-user-001', 'personalization', 'performance_mode', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 通知设置
('test_user_notifications', 'test-user-001', 'notifications', 'daily_reminder', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('test_user_reminder_time', 'test-user-001', 'notifications', 'reminder_time', '"20:00"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 隐私设置
('test_user_analytics', 'test-user-001', 'privacy', 'analytics_enabled', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('test_user_data_sharing', 'test-user-001', 'privacy', 'data_sharing_enabled', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 2. 高级用户配置（已完成多次Quiz的用户）
INSERT OR IGNORE INTO user_configs (
  id, user_id, config_type, config_key, config_value,
  is_active, created_at, updated_at
) VALUES 
-- 语言配置
('advanced_user_lang', 'test-user-002', 'language', 'preferred_language', '"en-US"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_user_fallback', 'test-user-002', 'language', 'fallback_language', '"zh-CN"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 皮肤/主题配置
('advanced_user_skin', 'test-user-002', 'appearance', 'selected_skin_id', '"dark-theme"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_user_theme', 'test-user-002', 'appearance', 'color_mode', '"dark"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Quiz偏好配置（偏好中医Quiz）
('advanced_user_default_quiz', 'test-user-002', 'quiz', 'default_quiz_pack_id', '"tcm_constitution_assessment"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_user_quiz_style', 'test-user-002', 'quiz', 'preferred_quiz_style', '"likert_scale"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_user_difficulty', 'test-user-002', 'quiz', 'preferred_difficulty', '3', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 个性化设置（高级用户）
('advanced_user_personalization', 'test-user-002', 'personalization', 'personalization_level', '85', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_user_performance', 'test-user-002', 'personalization', 'performance_mode', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 通知设置
('advanced_user_notifications', 'test-user-002', 'notifications', 'daily_reminder', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_user_reminder_time', 'test-user-002', 'notifications', 'reminder_time', '"09:00"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 隐私设置
('advanced_user_analytics', 'test-user-002', 'privacy', 'analytics_enabled', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_user_data_sharing', 'test-user-002', 'privacy', 'data_sharing_enabled', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 3. 新用户配置（刚注册的用户）
INSERT OR IGNORE INTO user_configs (
  id, user_id, config_type, config_key, config_value,
  is_active, created_at, updated_at
) VALUES 
-- 基础配置（使用系统默认值）
('new_user_lang', 'test-user-003', 'language', 'preferred_language', '"zh-CN"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('new_user_skin', 'test-user-003', 'appearance', 'selected_skin_id', '"default-skin"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('new_user_theme', 'test-user-003', 'appearance', 'color_mode', '"auto"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Quiz偏好（使用默认情绪Quiz）
('new_user_default_quiz', 'test-user-003', 'quiz', 'default_quiz_pack_id', '"mood_track_quest_main"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('new_user_difficulty', 'test-user-003', 'quiz', 'preferred_difficulty', '1', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 个性化设置（新用户低级别）
('new_user_personalization', 'test-user-003', 'personalization', 'personalization_level', '15', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('new_user_performance', 'test-user-003', 'personalization', 'performance_mode', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 通知设置（默认关闭）
('new_user_notifications', 'test-user-003', 'notifications', 'daily_reminder', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 隐私设置（保守设置）
('new_user_analytics', 'test-user-003', 'privacy', 'analytics_enabled', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('new_user_data_sharing', 'test-user-003', 'privacy', 'data_sharing_enabled', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 验证数据
SELECT 'User Configs loaded:' as info, COUNT(*) as count FROM user_configs;
SELECT 'Users with configs:' as info, COUNT(DISTINCT user_id) as count FROM user_configs;

-- 按配置类型统计
SELECT config_type, COUNT(*) as count 
FROM user_configs 
GROUP BY config_type 
ORDER BY config_type;
