#!/usr/bin/env node

/**
 * 修复客户端代码中的字段访问问题
 * 只修复真正需要修复的客户端字段访问，不修改数据库字段名
 */

const fs = require('fs');
const path = require('path');

// 需要修复的客户端字段访问映射
const clientFieldAccessMappings = {
  // 只修复客户端代码中访问配置对象的字段
  '.view_configs': '.viewConfigs',  // 访问解析后的配置对象
  '.emoji_size': '.emojiSize',      // 访问配置字段
  '.shadow_color': '.shadowColor',  // 访问配置字段
  '.shadow_blur': '.shadowBlur',    // 访问配置字段
  '.shadow_offset_x': '.shadowOffsetX',
  '.shadow_offset_y': '.shadowOffsetY',
  '.border_radius': '.borderRadius',
  '.animation_duration': '.animationDuration',
  '.animation_easing': '.animationEasing',
  '.bubble_size': '.bubbleSize',
  '.bubble_spacing': '.bubbleSpacing',
  '.card_size': '.cardSize',
  '.card_spacing': '.cardSpacing',
  '.card_height': '.cardHeight',
  '.tier_level': '.tierLevel'
};

// 需要检查的客户端文件
const clientFiles = [
  'src/views/components/bubbles/BubbleView.tsx',
  'src/views/components/cards/CardView.tsx',
  'src/views/components/galaxy/GalaxyComponent.tsx',
  'src/views/components/lists/ListView.tsx',
  'src/views/components/wheels/CanvasWheelComponent.tsx',
  'src/views/components/wheels/D3WheelComponent.tsx',
  'src/views/components/wheels/R3FWheelComponent.tsx',
  'src/views/components/wheels/SVGWheelComponent.tsx',
  'src/views/components/wheels/WebGLWheelComponent.tsx',
  'src/views/components/wheels/WebGPUWheelComponent.tsx',
  'src/utils/wheelConfigExtractor.ts',
  'src/utils/skinPreviewGenerator.ts'
];

/**
 * 修复文件中的客户端字段访问
 */
function fixClientFieldAccess(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️ 文件不存在: ${filePath}`);
      return false;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    let hasChanges = false;
    
    // 修复字段访问
    for (const [wrongAccess, correctAccess] of Object.entries(clientFieldAccessMappings)) {
      // 只修复对象属性访问，不修改字符串或注释
      const regex = new RegExp(`\\${wrongAccess}(?=\\s*[\\[\\]\\(\\)\\.,;:\\s}?])`, 'g');
      if (regex.test(content)) {
        content = content.replace(regex, correctAccess);
        hasChanges = true;
        console.log(`  ✅ 修复 ${filePath}: ${wrongAccess} → ${correctAccess}`);
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(fullPath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 修复文件失败 ${filePath}:`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复客户端字段访问问题...\n');
  
  let modifiedFiles = 0;
  
  for (const filePath of clientFiles) {
    console.log(`📁 检查文件: ${filePath}`);
    if (fixClientFieldAccess(filePath)) {
      modifiedFiles++;
    }
  }
  
  console.log(`\n✨ 修复完成!`);
  console.log(`📊 统计: 检查了 ${clientFiles.length} 个文件，修改了 ${modifiedFiles} 个文件`);
}

// 运行脚本
if (require.main === module) {
  main();
}
