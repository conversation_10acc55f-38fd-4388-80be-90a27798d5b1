-- =============================================
-- 用户偏好设置测试数据
-- 根据键值对结构的用户偏好设置
-- =============================================

-- 插入测试用户偏好设置 (键值对格式)
INSERT OR IGNORE INTO user_preferences (user_id, key, value) VALUES
    -- test-user-1 偏好设置
    ('test-user-1', 'language', 'en'),
    ('test-user-1', 'theme', 'light'),
    ('test-user-1', 'notifications', 'enabled'),
    ('test-user-1', 'privacy_profile_visibility', 'friends'),
    ('test-user-1', 'privacy_data_sharing', 'false'),
    ('test-user-1', 'privacy_analytics_tracking', 'true'),
    ('test-user-1', 'accessibility_high_contrast', 'false'),
    ('test-user-1', 'accessibility_large_text', 'false'),
    ('test-user-1', 'accessibility_reduce_motion', 'false'),
    ('test-user-1', 'reminder_daily_check_in', 'true'),
    ('test-user-1', 'reminder_time', '09:00'),
    ('test-user-1', 'reminder_timezone', 'America/New_York'),
    ('test-user-1', 'backup_auto_backup', 'true'),
    ('test-user-1', 'backup_frequency', 'weekly'),
    ('test-user-1', 'backup_cloud_storage', 'true'),

    -- test-user-2 偏好设置
    ('test-user-2', 'language', 'zh'),
    ('test-user-2', 'theme', 'dark'),
    ('test-user-2', 'notifications', 'disabled'),
    ('test-user-2', 'privacy_profile_visibility', 'private'),
    ('test-user-2', 'privacy_data_sharing', 'false'),
    ('test-user-2', 'privacy_analytics_tracking', 'false'),
    ('test-user-2', 'accessibility_high_contrast', 'true'),
    ('test-user-2', 'accessibility_large_text', 'false'),
    ('test-user-2', 'accessibility_reduce_motion', 'true'),
    ('test-user-2', 'reminder_daily_check_in', 'false'),
    ('test-user-2', 'reminder_time', '20:00'),
    ('test-user-2', 'reminder_timezone', 'Asia/Shanghai'),
    ('test-user-2', 'backup_auto_backup', 'false'),
    ('test-user-2', 'backup_frequency', 'never'),
    ('test-user-2', 'backup_cloud_storage', 'false'),

    -- test-user-3 偏好设置
    ('test-user-3', 'language', 'en'),
    ('test-user-3', 'theme', 'auto'),
    ('test-user-3', 'notifications', 'enabled'),
    ('test-user-3', 'privacy_profile_visibility', 'public'),
    ('test-user-3', 'privacy_data_sharing', 'true'),
    ('test-user-3', 'privacy_analytics_tracking', 'true'),
    ('test-user-3', 'accessibility_high_contrast', 'false'),
    ('test-user-3', 'accessibility_large_text', 'true'),
    ('test-user-3', 'accessibility_reduce_motion', 'false'),
    ('test-user-3', 'reminder_daily_check_in', 'true'),
    ('test-user-3', 'reminder_time', '18:00'),
    ('test-user-3', 'reminder_timezone', 'Europe/London'),
    ('test-user-3', 'backup_auto_backup', 'true'),
    ('test-user-3', 'backup_frequency', 'daily'),
    ('test-user-3', 'backup_cloud_storage', 'true');
