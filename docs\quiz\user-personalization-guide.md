# 用户个性化配置指南

## 🎯 概述

本文档详细描述了用户个性化配置系统的架构、用户旅程和实现细节。该系统采用全新的配置分离架构，通过6层Quiz个性化配置，为不同类型的用户提供从基础到专业级的个性化体验。

> **架构重构**: 本文档已完全更新以反映配置系统重构后的新架构，包括全局应用设置与Quiz系统配置的完全分离、混合架构模式和类型安全的配置管理。

> **相关文档**:
> - 配置系统实现总结请参考 `docs/implementation/config-system-implementation.md`
> - 配置系统API文档请参考 `docs/api/config-system-api-updated.md`
> - 类型系统架构请参考 `docs/quiz/type-system-architecture.md`
> - ViewFactory整合设计请参考 `docs/quiz/viewFactory-design.md`

## 🏗️ 配置系统架构重构

### 配置分离设计
```
配置系统 (完全分离)
├── 全局应用设置 (GlobalAppConfig)
│   ├── 主题模式 (theme_mode)
│   ├── 界面语言 (language)
│   ├── 通知设置 (notifications_enabled)
│   ├── 音效设置 (sound_enabled)
│   └── 无障碍配置 (accessibility)
│
└── Quiz系统配置 (UserQuizPreferences)
    ├── 6层个性化配置 (presentation_config)
    ├── 个性化级别 (personalization_level)
    ├── 配置版本 (config_version)
    └── 配置合并系统
        ├── 用户偏好配置 (user_preferences)
        ├── 包覆盖配置 (pack_overrides)
        └── 最终会话配置 (session_config)
```

### 混合架构模式
```
简单操作: 页面 → tRPC → 云端数据库
复杂业务: 页面 → Hooks → 离线服务 → 本地SQLite → 同步到云端
```

## 📊 Quiz个性化配置架构

### 6层Quiz配置架构 (QuizPresentationConfig)

```
Quiz个性化配置 (6层架构) - 存储在 UserQuizPreferences.presentation_config

Layer 0: 数据集展现配置 (Dataset Presentation)
│   ├── preferred_pack_categories: string[] (偏好的Quiz包分类)
│   │   └── ['daily', 'assessment', 'therapy', 'research', 'entertainment', 'education']
│   ├── default_difficulty_preference: 'beginner' | 'regular' | 'advanced' | 'expert'
│   ├── session_length_preference: 'short' | 'medium' | 'long'
│   ├── auto_select_recommended: boolean (自动选择推荐Quiz包)
│   └── restore_progress: boolean (恢复进度)
│
│   Quiz包架构说明：
│   ├── 每个Quiz包 = 一个完整的量表 (如"日常情绪追踪"、"中医体质评估")
│   ├── 每个问题 = 量表中的一个问题项 (如"主要情绪类别"、"情绪强度")
│   ├── 每个选项 = 问题的一个答案选项 (如"快乐"、"悲伤"、"愤怒")
│   └── 支持多种问题类型 (单选、多选、量表评分、情绪轮盘、文本输入等)
│
│   配置应用策略：
│   ├── 全局配置：Quiz包所有问题应用统一的后续配置
│   ├── 问题配置：单独问题使用独立的配置
│   ├── 选项配置：特定选项使用自定义配置
│   └── 混合配置：组合使用上述策略


Layer 1: 用户选择配置 (User Choice)
│   ├── preferred_view_type: 'wheel' | 'card' | 'bubble' | 'list' (视图类型选择)
│   ├── active_skin_id: string (皮肤主题选择)
│   ├── color_mode: 'warm' | 'cool' | 'neutral' (颜色模式)
│   └── user_level: 'beginner' | 'regular' | 'advanced' (用户级别)
│
Layer 2: 渲染策略配置 (Rendering Strategy)
│   ├── render_engine_preferences: Record<string, string> (渲染引擎偏好)
│   │   ├── wheel: "D3" | "SVG" | "Canvas" | "R3F" (情绪轮盘渲染引擎)
│   │   ├── card: "CSS" | "SVG" | "Canvas" (卡片视图渲染引擎)
│   │   ├── bubble: "Canvas" | "R3F" | "WebGL" (气泡视图渲染引擎)
│   │   └── list: "CSS" | "Virtual" | "Infinite" (列表视图渲染引擎)
│   ├── performance_mode: 'high_quality' | 'balanced' | 'performance' (性能模式)
│   │   ├── high_quality: 最佳视觉效果，高性能要求
│   │   ├── balanced: 平衡性能和视觉效果
│   │   └── performance: 基础渲染，减少动画和效果
│   └── supported_content_types: Record<string, boolean> (支持的内容类型)
│       ├── text: boolean (文本内容)
│       ├── emoji: boolean (表情符号)
│       ├── images: boolean (图片内容)
│       └── animations: boolean (动画效果)
│
Layer 3: 皮肤基础配置 (Skin Base)
│   ├── selected_skin_id: string (选择的皮肤ID)
│   ├── colors: Record<string, string> (颜色配置)
│   │   ├── primary: string (主色调)
│   │   ├── secondary: string (次色调)
│   │   ├── background: string (背景色)
│   │   ├── surface: string (表面色)
│   │   ├── text: string (文本色)
│   │   └── accent: string (强调色)
│   └── animations (动画配置)
│       ├── enable_animations: boolean (启用动画)
│       ├── animation_speed: 'slow' | 'normal' | 'fast' (动画速度)
│       └── reduce_motion: boolean (减少动画)
│
Layer 4: 视图细节配置 (View Detail)
│   ├── wheel_config (轮盘视图配置)
│   │   ├── container_size: number (容器大小: 200-800px)
│   │   ├── wheel_radius: number (轮盘半径: 80-400px)
│   │   ├── emotion_display_mode: string (情绪显示模式)
│   │   ├── show_labels: boolean (显示标签)
│   │   └── show_emojis: boolean (显示表情符号)
│   │
│   └── emotion_presentation (情绪展现配置) - **新增**
│       ├── emoji_mapping: Record<string, EmojiMapping> (emoji映射配置)
│       │   ├── happy: { primary: "😊", alternatives: ["😄", "😃", "🙂"] }
│       │   ├── sad: { primary: "😢", alternatives: ["😭", "😞", "☹️"] }
│       │   └── ... (支持所有情绪选项的emoji映射)
│       ├── color_mapping: Record<string, string> (颜色映射配置)
│       │   ├── happy: "#4CAF50", sad: "#2196F3", angry: "#F44336"
│       │   └── ... (支持所有情绪选项的颜色映射)
│       └── animation_mapping: Record<string, string> (动画映射配置)
│           ├── happy: "bounce", sad: "fade", angry: "shake"
│           └── ... (支持所有情绪选项的动画映射)
│
└── Layer 5: 无障碍增强配置 (Accessibility)
    ├── high_contrast: boolean (高对比度)
    ├── large_text: boolean (大字体)
    ├── reduce_motion: boolean (减少动画)
    ├── keyboard_navigation: boolean (键盘导航)
    └── voice_guidance: boolean (语音指导)
```

### 配置合并系统

```
配置优先级 (从高到低)
1. 问题特定映射 (QuestionPresentationOverrides) - 问题级别的emoji覆盖 **新增**
2. 会话配置快照 (QuizSessionConfig) - 最终生效的配置
3. 包覆盖配置 (QuizPackOverrides) - 针对特定Quiz包的配置
4. 用户偏好配置 (UserQuizPreferences) - 用户个人偏好
5. Quiz包默认配置 (PackPresentationConfigs) - Quiz包的默认emoji映射 **新增**
6. 系统默认配置 (SystemDefaults) - 系统提供的默认值

配置合并流程
用户偏好 + 包覆盖 + 系统默认 → QuizConfigMergerService → 最终会话配置
```
```

## 👥 用户类型与配置策略

### 1. 新手用户 (Beginner Users)
**特点**: 刚开始使用，需要简单易懂的界面
**配置策略**:
- 使用默认配置和免费皮肤
- 简化的视图类型 (wheel, card)
- 基础渲染引擎 (D3, CSS)
- 启用辅助功能
- 个性化级别: 20-40

**示例配置** (QuizPresentationConfig):
```typescript
{
  layer0_dataset_presentation: {
    preferred_pack_categories: ['daily'],
    default_difficulty_preference: 'beginner',
    session_length_preference: 'short',
    auto_select_recommended: true,
    restore_progress: true
  },
  layer1_user_choice: {
    preferred_view_type: 'wheel',
    active_skin_id: 'default-light',
    color_mode: 'warm',
    user_level: 'beginner'
  },
  layer2_rendering_strategy: {
    render_engine_preferences: { wheel: 'D3', card: 'CSS' },
    performance_mode: 'balanced',
    supported_content_types: { text: true, emoji: true, images: false, animations: false }
  },
  layer3_skin_base: {
    selected_skin_id: 'default-light',
    colors: { /* 默认颜色配置 */ },
    animations: { enable_animations: false, animation_speed: 'normal', reduce_motion: true }
  },
  layer4_view_detail: {
    wheel_config: {
      container_size: 300,
      wheel_radius: 150,
      emotion_display_mode: 'simple',
      show_labels: true,
      show_emojis: true
    },
    emotion_presentation: {
      emoji_mapping: {
        happy: { primary: "😊", alternatives: ["😄", "😃", "🙂"] },
        sad: { primary: "😢", alternatives: ["😭", "😞", "☹️"] },
        angry: { primary: "😠", alternatives: ["😡", "🤬", "😤"] }
      },
      color_mapping: {
        happy: "#4CAF50", sad: "#2196F3", angry: "#F44336"
      },
      animation_mapping: {
        happy: "bounce", sad: "fade", angry: "shake"
      }
    }
  },
  layer5_accessibility: {
    high_contrast: false,
    large_text: true,
    reduce_motion: true,
    keyboard_navigation: true,
    voice_guidance: false
  }
}
```

### 2. 普通用户 (Regular Users)
**特点**: 有一定使用经验，希望个性化但不复杂
**配置策略**:
- 混合使用免费和付费皮肤
- 多种视图类型 (wheel, card, bubble)
- 中等性能渲染引擎 (D3, SVG)
- 根据偏好调整显示模式
- 个性化级别: 40-70

### 3. 高级用户 (Advanced Users)
**特点**: 熟练用户，追求个性化和高级功能
**配置策略**:
- 使用付费皮肤和高级主题
- 所有视图类型可用
- 高性能渲染引擎 (Canvas, R3F)
- 精细的视觉效果配置
- 个性化级别: 70-90

### 4. VIP用户 (VIP Users)
**特点**: 付费用户，享受所有功能和最佳体验
**配置策略**:
- 解锁所有皮肤和功能
- 专属视图类型和3D效果
- 最高性能渲染 (R3F, WebGL)
- 完全自定义配置
- 个性化级别: 90-100

### 5. 无障碍用户 (Accessibility Users)
**特点**: 需要特殊辅助功能支持
**配置策略**:
- 高对比度皮肤
- 简化的视图类型
- 稳定的渲染引擎 (CSS)
- 完整的无障碍功能
- 个性化级别: 根据需求调整

## 🔧 配置系统使用示例

### 使用useQuizConfig Hook

```typescript
import { useQuizConfig } from '@/hooks/useQuizConfig';

const QuizSettingsPage = () => {
  const {
    preferences,
    presentationConfig,
    preferredViewType,
    colorMode,
    personalizationLevel,
    updatePresentationConfig,
    generateSessionConfig,
    isLoading,
    error
  } = useQuizConfig();

  // 更新视图类型
  const handleViewTypeChange = async (viewType: 'wheel' | 'card') => {
    await updatePresentationConfig({
      layer1_user_choice: {
        ...presentationConfig?.layer1_user_choice,
        preferred_view_type: viewType
      }
    });
  };

  // 更新颜色模式
  const handleColorModeChange = async (colorMode: 'warm' | 'cool' | 'neutral') => {
    await updatePresentationConfig({
      layer1_user_choice: {
        ...presentationConfig?.layer1_user_choice,
        color_mode: colorMode
      }
    });
  };

  // 生成会话配置
  const startQuizSession = async (packId: string) => {
    const sessionId = `session_${Date.now()}`;
    const sessionConfig = await generateSessionConfig(packId, sessionId);
    if (sessionConfig) {
      // 使用会话配置启动Quiz
      console.log('Session config generated:', sessionConfig);
    }
  };

  return (
    <div>
      <h2>Quiz个性化设置</h2>
      <p>当前视图类型: {preferredViewType}</p>
      <p>当前颜色模式: {colorMode}</p>
      <p>个性化级别: {personalizationLevel}%</p>

      {isLoading && <p>加载中...</p>}
      {error && <p>错误: {error}</p>}
    </div>
  );
};
```

### 配置合并示例

```typescript
import { Services } from '@/services';

const generateFinalConfig = async (userId: string, packId: string, sessionId: string) => {
  // 使用配置合并服务
  const quizConfigMergerService = await Services.quizConfigMerger();

  // 生成最终会话配置
  const result = await quizConfigMergerService.generateSessionConfig(
    userId,
    packId,
    sessionId
  );

  if (result.success && result.data) {
    const sessionConfig = result.data;

    // 解析最终配置
    const finalConfig = JSON.parse(sessionConfig.final_presentation_config);

    // 查看配置来源
    const configSources = JSON.parse(sessionConfig.config_sources);

    console.log('Final config:', finalConfig);
    console.log('Config sources:', configSources);

    return { finalConfig, configSources };
  }

  return null;
};
```

## 🎨 颜色模式系统 (Color Mode System)

### 颜色模式类型

#### 1. **Warm (暖色模式)**
**特点**: 使用暖色调，营造温馨舒适的氛围
**适用场景**: 放松、冥想、晚间使用
**颜色特征**:
```json
{
  "primary_hue_range": [0, 60],      // 红色到黄色
  "saturation_boost": 1.1,           // 饱和度提升10%
  "temperature_shift": "+200K",      // 色温偏暖
  "emotion_mapping": {
    "joy": "#FFD700",                // 金黄色
    "love": "#FF6B6B",               // 暖红色
    "excitement": "#FF8C42",         // 橙色
    "comfort": "#F4A261"             // 暖橙色
  }
}
```

#### 2. **Cool (冷色模式)**
**特点**: 使用冷色调，营造清爽专业的氛围
**适用场景**: 工作、学习、白天使用
**颜色特征**:
```json
{
  "primary_hue_range": [180, 270],   // 青色到紫色
  "saturation_boost": 0.9,           // 饱和度降低10%
  "temperature_shift": "-200K",      // 色温偏冷
  "emotion_mapping": {
    "calm": "#4ECDC4",               // 青绿色
    "focus": "#45B7D1",              // 蓝色
    "peace": "#96CEB4",              // 薄荷绿
    "clarity": "#A8E6CF"             // 浅绿色
  }
}
```

#### 3. **Mixed (混合模式)**
**特点**: 平衡使用暖冷色调，适应不同情绪
**适用场景**: 全天候使用，情绪记录
**颜色特征**:
```json
{
  "primary_hue_range": [0, 360],     // 全色谱
  "saturation_boost": 1.0,           // 标准饱和度
  "temperature_shift": "0K",         // 中性色温
  "emotion_mapping": {
    "balanced": "根据情绪类型动态选择",
    "adaptive": "根据时间和使用场景调整",
    "contextual": "根据内容和用户状态变化"
  }
}
```

#### 4. **Auto (自动模式)**
**特点**: 根据时间、环境、用户行为自动调整
**适用场景**: 智能适配，无需手动调整
**自动调整规则**:
```json
{
  "time_based": {
    "morning": "cool",               // 6:00-12:00 冷色调
    "afternoon": "mixed",            // 12:00-18:00 混合色调
    "evening": "warm",               // 18:00-22:00 暖色调
    "night": "warm_dimmed"           // 22:00-6:00 暖色调+降低亮度
  },
  "context_based": {
    "work_hours": "cool",            // 工作时间偏冷色
    "relaxation": "warm",            // 放松时间偏暖色
    "exercise": "energetic",         // 运动时使用活力色彩
    "meditation": "calm"             // 冥想时使用平静色彩
  },
  "emotion_based": {
    "positive_emotions": "warm_bright",
    "negative_emotions": "cool_soft",
    "neutral_emotions": "balanced"
  }
}
```

### 颜色模式与皮肤的交互

#### 皮肤适配机制
```javascript
const colorModeAdaptation = {
  // 皮肤基础色彩 + 颜色模式调整 = 最终显示色彩
  adaptSkinToColorMode: (skinColors, colorMode) => {
    switch(colorMode) {
      case 'warm':
        return {
          ...skinColors,
          primary: adjustHue(skinColors.primary, 'warmer'),
          accent: increaseSaturation(skinColors.accent, 0.1),
          background: addWarmth(skinColors.background)
        };

      case 'cool':
        return {
          ...skinColors,
          primary: adjustHue(skinColors.primary, 'cooler'),
          accent: decreaseSaturation(skinColors.accent, 0.1),
          background: addCoolness(skinColors.background)
        };

      case 'mixed':
        return skinColors; // 保持原始色彩

      case 'auto':
        return autoAdjustColors(skinColors, getCurrentContext());
    }
  }
};
```

### 颜色模式在不同视图中的应用

#### Wheel 视图中的颜色模式
```javascript
const wheelColorMode = {
  warm: {
    sector_colors: "使用暖色调渐变",
    hover_effect: "暖色发光效果",
    selection_highlight: "金色高亮",
    background_gradient: "暖色径向渐变"
  },
  cool: {
    sector_colors: "使用冷色调渐变",
    hover_effect: "冷色发光效果",
    selection_highlight: "蓝色高亮",
    background_gradient: "冷色径向渐变"
  }
};
```

#### Card 视图中的颜色模式
```javascript
const cardColorMode = {
  warm: {
    card_background: "暖色背景",
    border_color: "暖色边框",
    text_color: "深暖色文字",
    shadow_color: "暖色阴影"
  },
  cool: {
    card_background: "冷色背景",
    border_color: "冷色边框",
    text_color: "深冷色文字",
    shadow_color: "冷色阴影"
  }
};
```

## 🚀 用户旅程详解

### 以 Wheel 视图为例的完整用户旅程

#### T0-T1: 用户选择阶段
```javascript
// 用户操作：点击轮盘视图
const userChoice = {
  viewType: "wheel",
  skinId: "ocean-blue",
  emotionDataSet: "advanced-emotions"
};

// 系统响应：读取用户配置
const userConfig = await getUserConfig(userId);
const displayOptions = {
  viewType: userConfig.preferred_view_type,
  skinId: userConfig.active_skin_id,
  emotionDataId: userConfig.active_emotion_data_id,
  darkMode: userConfig.dark_mode,
  colorMode: userConfig.color_mode
};
```

#### T2-T3: 配置加载与验证阶段
```javascript
// 加载皮肤配置
const skinConfig = await loadSkinConfig(displayOptions.skinId);
const wheelConfig = skinConfig.view_configs.wheel;

// 应用颜色模式调整
const adaptedColors = adaptSkinToColorMode(
  skinConfig.colors,
  displayOptions.colorMode,
  displayOptions.darkMode
);

// 验证兼容性
const isCompatible = validateViewSkinCompatibility(
  displayOptions.viewType,
  displayOptions.skinId
);

// 合并用户偏好
const renderConfig = {
  engine: userConfig.render_engine_preferences.wheel,
  layout: userConfig.layout_preferences.wheel,
  content: userConfig.content_display_mode_preferences.wheel,
  colors: adaptedColors,
  darkMode: displayOptions.darkMode,
  colorMode: displayOptions.colorMode,
  ...wheelConfig
};
```

#### T4-T5: 渲染引擎初始化
```javascript
// 根据用户偏好选择渲染引擎
const engineType = renderConfig.engine; // "R3F"

// 初始化渲染器
const wheelRenderer = new R3FWheelRenderer({
  // 第4层：视图细节配置
  containerSize: wheelConfig.container_size,
  wheelRadius: wheelConfig.wheel_radius,
  innerRadius: wheelConfig.inner_radius,

  // 3D配置 (VIP功能)
  use3DEffects: wheelConfig.use_3d_effects,
  perspective: wheelConfig.perspective,
  depth: wheelConfig.depth,

  // 颜色模式配置
  darkMode: renderConfig.darkMode,
  colorMode: renderConfig.colorMode,

  // 材质配置 (应用颜色模式调整后的颜色)
  materials: createMaterials(renderConfig.colors, wheelConfig, renderConfig.colorMode)
});
```

#### T6-T7: 视觉效果应用
```javascript
// 应用用户自定义的视觉效果
if (wheelConfig.shadow_enabled) {
  wheelRenderer.addShadow({
    color: wheelConfig.shadow_color,
    blur: wheelConfig.shadow_blur,
    offset: [wheelConfig.shadow_offset_x, wheelConfig.shadow_offset_y]
  });
}

// 应用装饰效果 (高级用户功能)
if (wheelConfig.decorations && userConfig.isAdvancedUser) {
  wheelRenderer.addDecorations({
    type: wheelConfig.decoration_type,
    density: wheelConfig.decoration_density,
    color: wheelConfig.decoration_color
  });
}
```

#### T8-T9: 交互系统配置
```javascript
// 配置个性化交互行为
wheelRenderer.configureInteraction({
  hover: {
    effect: wheelConfig.hover_effect,
    scale: wheelConfig.hover_scale,
    color: wheelConfig.highlight_color
  },
  selection: {
    animation: wheelConfig.selection_animation,
    indicator: wheelConfig.selection_indicator
  },
  controls: {
    drag: wheelConfig.drag_enabled,
    zoom: wheelConfig.zoom_enabled,
    rotation: wheelConfig.rotation_enabled
  }
});
```

#### T10-T11: 可访问性增强
```javascript
// 第5层：应用可访问性配置
const accessibility = userConfig.accessibility;

if (accessibility.high_contrast) {
  wheelRenderer.enhanceContrast();
}

if (accessibility.large_text) {
  wheelRenderer.scaleFontSize(1.2);
}

if (accessibility.reduce_motion) {
  wheelRenderer.disableAnimations();
}

if (accessibility.screen_reader_support) {
  wheelRenderer.addAriaLabels();
}
```

## 🎨 ViewConfigOptions 组件架构

### Collapsible 配置界面

ViewConfigOptions 组件使用 collapsible 界面来组织复杂的配置选项：

```typescript
interface ViewConfigSection {
  id: string;
  title: string;
  icon: React.ComponentType;
  badge?: string;
  isOpen: boolean;
  controls: ConfigControl[];
}

interface ConfigControl {
  type: 'slider' | 'switch' | 'select' | 'color';
  key: string;
  label: string;
  description?: string;
  value: any;
  options?: any;
  onChange: (value: any) => void;
}
```

### 配置分组

1. **几何配置** (Geometry Configuration)
   - 容器大小、轮盘半径、扇区间隙
   - 适用于所有用户类型

2. **视觉效果** (Visual Effects)
   - 3D效果、阴影、装饰
   - 高级用户和VIP用户可用

3. **交互行为** (Interaction Behavior)
   - 悬停效果、选择动画、控制选项
   - 根据用户类型提供不同选项

4. **响应式配置** (Responsive Configuration)
   - 断点设置、自适应布局
   - 专业用户功能

## 📱 响应式个性化

### 设备适配策略

```javascript
const deviceConfig = {
  mobile: {
    preferredEngine: "CSS",
    simplifiedUI: true,
    reducedAnimations: true,
    largerTouchTargets: true
  },
  tablet: {
    preferredEngine: "SVG",
    balancedUI: true,
    moderateAnimations: true,
    adaptiveLayout: true
  },
  desktop: {
    preferredEngine: "R3F",
    fullUI: true,
    richAnimations: true,
    multiViewSupport: true
  }
};
```

### 性能自适应

```javascript
const performanceConfig = {
  low: {
    renderEngine: "CSS",
    effects: "minimal",
    animations: "reduced"
  },
  medium: {
    renderEngine: "SVG",
    effects: "standard",
    animations: "normal"
  },
  high: {
    renderEngine: "R3F",
    effects: "enhanced",
    animations: "rich"
  }
};
```

## 🔄 配置同步与持久化

### 本地存储策略
- 用户偏好立即保存到本地存储
- 定期同步到服务器
- 离线时使用本地配置

### 云端同步
- 跨设备配置同步
- 版本控制和冲突解决
- 备份和恢复机制

## 🧪 测试数据说明

测试数据包含7种不同的用户配置：

1. **config-user-1**: 新手用户基础配置
2. **config-user-2**: 普通用户平衡配置
3. **config-user-3**: 年轻用户简化配置
4. **config-default-template**: 系统默认模板
5. **config-vip-user**: VIP用户高级配置
6. **config-accessibility-user**: 无障碍用户专用配置
7. **config-advanced-user**: 高级用户个性化配置
8. **config-power-user**: 专业用户极致配置

每种配置展示了不同用户类型的个性化需求和使用模式。

## 🔧 实现细节

### SkinConfigSchema 详细配置

每个皮肤的 `view_configs` 包含针对不同视图类型的详细配置：

```typescript
interface WheelViewConfig {
  // 几何配置
  container_size: number;           // 容器大小 (200-600px)
  wheel_radius: number;             // 轮盘半径 (100-300px)
  inner_radius: number;             // 内圆半径 (20-120px)
  sector_gap: number;               // 扇区间隙 (0-10px)
  sector_padding: number;           // 扇区内边距 (4-16px)
  sector_border_radius: number;     // 扇区圆角 (0-8px)

  // 视觉效果
  use_3d_effects: boolean;          // 启用3D效果
  perspective: number;              // 透视距离 (800-1200px)
  depth: number;                    // 3D深度 (5-50px)
  shadow_enabled: boolean;          // 启用阴影
  shadow_blur: number;              // 阴影模糊度 (0-30px)
  shadow_offset_y: number;          // 阴影Y偏移 (-20-20px)

  // 装饰效果
  decorations: boolean;             // 启用装饰
  decoration_type: string;          // 装饰类型: glow, sparkles, dots, waves
  decoration_density: number;       // 装饰密度 (0.1-1.0)

  // 交互行为
  hover_effect: string;             // 悬停效果: highlight, scale, glow, lift, pulse
  hover_scale: number;              // 悬停缩放 (1.0-1.5)
  selection_animation: string;      // 选择动画: fade, pulse, spin, bounce, flash
  transition_duration: number;      // 过渡时间 (100-1000ms)

  // 控制功能
  drag_enabled: boolean;            // 启用拖拽
  zoom_enabled: boolean;            // 启用缩放
  rotation_enabled: boolean;        // 启用旋转

  // 响应式配置
  responsive_scaling: boolean;      // 响应式缩放
  min_size: number;                 // 最小尺寸
  max_size: number;                 // 最大尺寸
}
```

### 配置优先级系统

```javascript
const configPriority = {
  1: "用户个人配置",           // 最高优先级
  2: "用户类型默认配置",       // 根据用户等级
  3: "设备适配配置",           // 根据设备性能
  4: "皮肤默认配置",           // 皮肤自带配置
  5: "系统默认配置"            // 最低优先级
};

// 配置合并算法
function mergeConfigs(userConfig, skinConfig, deviceConfig, systemConfig) {
  return {
    ...systemConfig,
    ...deviceConfig,
    ...skinConfig,
    ...userConfig.typeDefaults,
    ...userConfig.personal
  };
}
```

### 性能优化策略

```javascript
const performanceOptimization = {
  // 渲染引擎降级策略
  engineFallback: {
    "R3F": ["Canvas", "SVG", "CSS"],
    "Canvas": ["SVG", "CSS"],
    "SVG": ["CSS"],
    "CSS": []
  },

  // 效果简化策略
  effectSimplification: {
    low: {
      shadows: false,
      decorations: false,
      animations: "minimal"
    },
    medium: {
      shadows: true,
      decorations: false,
      animations: "standard"
    },
    high: {
      shadows: true,
      decorations: true,
      animations: "rich"
    }
  },

  // 自动性能检测
  autoDetection: {
    fps_threshold: 30,
    memory_threshold: "512MB",
    cpu_threshold: "medium"
  }
};
```

## 🎯 用户体验优化

### 渐进式个性化

1. **第一次使用**: 提供简单的预设选择
2. **熟悉阶段**: 逐步开放更多配置选项
3. **高级使用**: 提供完全自定义能力
4. **专家模式**: 开放实验性功能

### 智能推荐系统

```javascript
const recommendationEngine = {
  // 基于使用行为推荐
  behaviorBased: {
    viewTypeUsage: "推荐常用视图类型",
    skinPreference: "推荐相似风格皮肤",
    featureUsage: "推荐相关功能"
  },

  // 基于用户类型推荐
  typeBased: {
    beginner: "推荐简化配置",
    advanced: "推荐高级功能",
    accessibility: "推荐无障碍选项"
  },

  // 基于设备推荐
  deviceBased: {
    mobile: "推荐触屏优化配置",
    desktop: "推荐桌面增强功能",
    tablet: "推荐平板适配配置"
  }
};
```

### 配置预设管理

```javascript
const presetManager = {
  // 系统预设
  systemPresets: [
    "minimal", "standard", "advanced", "accessibility"
  ],

  // 用户自定义预设
  userPresets: [
    "work_mode", "gaming_mode", "presentation_mode"
  ],

  // 社区分享预设
  communityPresets: [
    "popular_configs", "trending_themes", "expert_setups"
  ]
};
```

## 📊 数据分析与优化

### 用户行为追踪

```javascript
const analyticsEvents = {
  configChange: {
    event: "config_changed",
    properties: ["config_type", "old_value", "new_value", "user_type"]
  },

  performanceIssue: {
    event: "performance_issue",
    properties: ["fps", "memory_usage", "render_engine", "config_complexity"]
  },

  featureUsage: {
    event: "feature_used",
    properties: ["feature_name", "frequency", "user_satisfaction"]
  }
};
```

### A/B 测试框架

```javascript
const abTestFramework = {
  tests: {
    "default_skin_selection": {
      variants: ["light_first", "dark_first", "auto_detect"],
      metrics: ["engagement", "customization_rate", "retention"]
    },

    "config_ui_complexity": {
      variants: ["simple", "intermediate", "advanced"],
      metrics: ["completion_rate", "user_satisfaction", "support_requests"]
    }
  }
};
```

## 🔒 安全与隐私

### 配置数据保护

1. **本地加密**: 敏感配置数据本地加密存储
2. **传输安全**: HTTPS + 数据签名验证
3. **访问控制**: 基于用户权限的配置访问
4. **审计日志**: 配置更改的完整审计轨迹

### 隐私保护

```javascript
const privacyProtection = {
  dataMinimization: "只收集必要的配置数据",
  anonymization: "用户行为数据匿名化处理",
  consentManagement: "明确的数据使用同意机制",
  dataRetention: "配置数据保留期限管理"
};
```

## 🚀 未来发展方向

### AI 驱动的个性化

1. **智能配置生成**: 基于用户行为自动生成最优配置
2. **情境感知**: 根据使用场景自动调整配置
3. **预测性优化**: 预测用户需求并提前优化
4. **自然语言配置**: 通过对话方式进行配置

### 高级功能扩展

1. **WebGPU 支持**: 下一代图形渲染技术
2. **VR/AR 适配**: 虚拟现实和增强现实支持
3. **多模态交互**: 语音、手势、眼动控制
4. **协作配置**: 团队共享和协作配置管理

这个个性化配置系统为用户提供了从基础到专业级的完整定制体验，确保每个用户都能获得最适合其需求和偏好的界面配置。

## 📊 数据库映射 (Database Mapping)

### 新架构表结构对应关系

#### 1. Quiz包表 (quiz_packs) - 第0层数据源
```sql
CREATE TABLE quiz_packs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    version TEXT DEFAULT '1.0.0',

    -- Quiz包分类和类型
    category TEXT NOT NULL, -- 'daily', 'assessment', 'therapy', 'research', 'entertainment', 'education'
    quiz_type TEXT NOT NULL, -- 'emotion_wheel', 'traditional_scale', 'personality_test', 'iq_test', 'knowledge_quiz', 'survey', 'game_quiz', 'mixed'
    difficulty_level TEXT DEFAULT 'regular', -- 'beginner', 'regular', 'advanced', 'expert'
    quiz_style TEXT, -- 'mainstream', 'alternative', 'experimental', 'cultural_specific'

    -- Quiz逻辑配置 (JSON)
    quiz_logic_config TEXT NOT NULL,

    -- 元数据 (JSON)
    metadata TEXT NOT NULL,

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 用户展现配置表 (user_presentation_configs) - 6层个性化配置
```sql
CREATE TABLE user_presentation_configs (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    config_name TEXT DEFAULT 'default',

    -- 6层展现配置 (JSON: UserPresentationConfig)
    presentation_config TEXT NOT NULL,

    -- 配置元数据
    config_version TEXT DEFAULT '2.0',
    personalization_level INTEGER DEFAULT 0, -- 0-100

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);
```

#### 3. 会话展现配置快照表 (quiz_session_presentation_configs)
```sql
CREATE TABLE quiz_session_presentation_configs (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    user_id TEXT NOT NULL,

    -- 基础配置快照
    config_name TEXT NOT NULL,
    config_version TEXT DEFAULT '2.0',

    -- 完整的展现配置快照 (从user_presentation_configs复制)
    presentation_config TEXT NOT NULL, -- JSON: 完整的6层个性化配置

    -- 会话特定配置
    session_overrides TEXT, -- JSON: 会话特定的配置覆盖

    -- 配置元数据
    personalization_level INTEGER DEFAULT 50, -- 个性化程度 0-100
    config_source TEXT DEFAULT 'user_preference', -- 'user_preference', 'system_default', 'session_custom'

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 4. 皮肤配置表 (skin_configs) - 第3层皮肤基础
```sql
CREATE TABLE skin_configs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,

    -- 皮肤配置 (JSON)
    config TEXT NOT NULL,

    -- 皮肤元数据
    version TEXT DEFAULT '1.0.0',
    is_premium BOOLEAN DEFAULT 0,
    supported_view_types TEXT, -- JSON array

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6层配置JSON结构映射

#### presentation_config JSON结构
```typescript
interface UserPresentationConfig {
  // 第0层：Quiz包数据集选择
  layer0_quiz_pack_selection: {
    active_quiz_pack_id: string;
    quiz_pack_preferences: {
      preferred_categories: string[];
      preferred_quiz_types: string[];
      difficulty_preference: string;
      style_preference: string;
    };
  };

  // 第1层：基础选择层
  layer1_user_choice: {
    preferred_view_type: string; // 'wheel', 'card', 'bubble', 'galaxy'
    active_skin_id: string;
    dark_mode: boolean;
    color_mode: string; // 'warm', 'cool', 'mixed', 'auto'
  };

  // 第2层：渲染策略层
  layer2_rendering_strategy: {
    render_engine_preferences: {
      wheel: string; // 'D3', 'SVG', 'Canvas', 'R3F'
      card: string;  // 'CSS', 'SVG', 'Canvas'
      bubble: string; // 'Canvas', 'R3F', 'WebGL'
      galaxy: string; // 'R3F', 'WebGL', 'Three.js'
    };
    content_display_mode_preferences: {
      wheel: string; // 'text', 'emoji', 'textEmoji', 'icon'
      card: string;  // 'text', 'emoji', 'image', 'mixed'
      bubble: string; // 'emoji', 'color', 'size', 'mixed'
    };
    layout_preferences: {
      wheel: string; // 'circular_balanced', 'circular_weighted', 'spiral', 'custom'
      card: string;  // 'grid_responsive', 'masonry', 'list', 'carousel'
      bubble: string; // 'organic_flow', 'force_directed', 'clustered', 'random'
    };
    performance_mode: string; // 'low', 'balanced', 'high'
  };

  // 第3层：皮肤基础配置层 (通过active_skin_id关联skin_configs表)
  layer3_skin_base: {
    skin_overrides: {
      colors?: Partial<SkinColors>;
      fonts?: Partial<SkinFonts>;
      animations?: Partial<SkinAnimations>;
      effects?: Partial<SkinEffects>;
    };
  };

  // 第4层：视图细节配置层
  layer4_view_details: {
    wheel_config: WheelViewConfig;
    card_config: CardViewConfig;
    bubble_config: BubbleViewConfig;
    emotion_presentation: EmotionPresentationConfig;
  };

  // 第5层：可访问性增强层
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    screen_reader_support: boolean;
    keyboard_navigation: boolean;
    focus_indicators: boolean;
    color_blind_support: boolean;
    simplified_ui: boolean;
  };
}
```

### 配置字段与数据库的对应关系

| 6层配置字段 | 数据库表 | 字段名 | 数据类型 | 说明 |
|------------|---------|--------|----------|------|
| active_quiz_pack_id | user_presentation_configs | presentation_config.layer0_quiz_pack_selection.active_quiz_pack_id | JSON | 当前选择的Quiz包ID |
| preferred_view_type | user_presentation_configs | presentation_config.layer1_user_choice.preferred_view_type | JSON | 用户偏好的视图类型 |
| active_skin_id | user_presentation_configs | presentation_config.layer1_user_choice.active_skin_id | JSON | 当前选择的皮肤ID |
| render_engine_preferences | user_presentation_configs | presentation_config.layer2_rendering_strategy.render_engine_preferences | JSON | 渲染引擎偏好设置 |
| wheel_config | user_presentation_configs | presentation_config.layer4_view_details.wheel_config | JSON | 轮盘视图详细配置 |
| accessibility | user_presentation_configs | presentation_config.layer5_accessibility | JSON | 可访问性配置 |

### 数据迁移策略

#### 从旧架构(user_configs)到新架构的迁移
```sql
-- 迁移脚本示例
INSERT INTO user_presentation_configs (
    id, user_id, config_name, presentation_config, config_version, is_active
)
SELECT
    id,
    user_id,
    name,
    json_object(
        'layer0_quiz_pack_selection', json_object(
            'active_quiz_pack_id', COALESCE(active_emotion_data_id, 'default-quiz-pack')
        ),
        'layer1_user_choice', json_object(
            'preferred_view_type', preferred_view_type,
            'active_skin_id', active_skin_id,
            'dark_mode', dark_mode,
            'color_mode', COALESCE(color_mode, 'warm')
        ),
        'layer2_rendering_strategy', json_object(
            'render_engine_preferences', render_engine_preferences,
            'content_display_mode_preferences', content_display_mode_preferences,
            'layout_preferences', layout_preferences,
            'performance_mode', 'balanced'
        ),
        'layer3_skin_base', json_object(
            'skin_overrides', json_object()
        ),
        'layer4_view_details', json_object(
            'wheel_config', COALESCE(view_detail_configs, '{}'),
            'card_config', '{}',
            'bubble_config', '{}',
            'emotion_presentation', '{}'
        ),
        'layer5_accessibility', COALESCE(accessibility, '{}')
    ),
    '2.0',
    is_active
FROM user_configs
WHERE user_id IS NOT NULL;
```

## 🔄 Quiz设置迁移架构

### 迁移概述

Quiz设置已从通用Settings页面迁移到专门的QuizSettings页面，实现了功能分离和6层个性化配置的专门管理。

### 迁移前后对比

#### 迁移前：混合配置架构
```typescript
// Settings页面包含所有配置
interface OldSettingsStructure {
  // 通用设置
  language: string;
  theme: string;
  notifications: boolean;

  // Quiz相关设置（混合在一起）
  preferred_view_type: ViewType;
  render_engine_preferences: RenderEnginePreferences;
  content_display_mode: ContentDisplayMode;
  active_skin_id: string;
  dark_mode: boolean;
  // ...
}
```

#### 迁移后：分离配置架构
```typescript
// Settings页面：仅通用设置
interface GeneralSettings {
  language: string;
  theme: string;
  notifications: boolean;
  accessibility: AccessibilitySettings;
  // 不再包含Quiz相关配置
}

// QuizSettings页面：专门的6层Quiz配置
interface QuizSettings {
  layer0_dataset_presentation: Layer0Config;
  layer1_user_choice: Layer1Config;
  layer2_rendering_strategy: Layer2Config;
  layer3_skin_base: Layer3Config;
  layer4_view_details: Layer4Config;
  layer5_accessibility: Layer5Config;
}
```

### 用户体验流程

#### 配置访问新流程
```mermaid
graph TD
    A[Settings页面] --> B[Quiz系统配置入口]
    B --> C[QuizSettings页面]
    C --> D[6层配置选项卡]
    D --> E[Layer 0: 数据集配置]
    D --> F[Layer 1: 用户选择]
    D --> G[Layer 2: 渲染策略]
    D --> H[Layer 3: 皮肤基础]
    D --> I[Layer 4: 视图细节]
    D --> J[Layer 5: 可访问性]

    E --> K[保存配置]
    F --> K
    G --> K
    H --> K
    I --> K
    J --> K

    K --> L[应用到Quiz体验]
```

#### 配置界面层次
```typescript
// QuizSettings页面结构
const QuizSettingsStructure = {
  // 顶层导航
  tabNavigation: [
    { id: 'layer0', label: '数据集展现', icon: 'Database' },
    { id: 'layer1', label: '基础选择', icon: 'Settings' },
    { id: 'layer2', label: '渲染策略', icon: 'Cpu' },
    { id: 'layer3', label: '皮肤基础', icon: 'Palette' },
    { id: 'layer4', label: '视图细节', icon: 'Eye' },
    { id: 'layer5', label: '可访问性', icon: 'Accessibility' }
  ],

  // 每层配置内容
  layerConfigs: {
    layer0: {
      title: '数据集展现配置',
      description: '配置Quiz包选择偏好和展现策略',
      controls: [
        'default_difficulty_preference',
        'session_length_preference',
        'preferred_pack_categories',
        'auto_select_recommended'
      ]
    },
    layer1: {
      title: '基础选择配置',
      description: '配置视图类型、皮肤和基础偏好',
      controls: [
        'preferred_view_type',
        'active_skin_id',
        'dark_mode',
        'color_mode'
      ]
    },
    // ... 其他层级
  }
};
```

### 数据迁移策略

#### 自动迁移工作流
```typescript
class QuizSettingsMigrationWorkflow {
  async performMigration(userId: string): Promise<MigrationResult> {
    // 1. 检查迁移状态
    const migrationStatus = await this.checkMigrationStatus(userId);

    if (migrationStatus.status === 'up_to_date') {
      return { success: true, message: 'No migration needed' };
    }

    // 2. 读取旧配置
    const legacyConfig = await this.getLegacyUserConfig(userId);

    // 3. 映射到新的6层架构
    const newQuizConfig = await this.mapToNewArchitecture(legacyConfig);

    // 4. 验证新配置
    const validation = await this.validateNewConfig(newQuizConfig);
    if (!validation.success) {
      throw new Error(`Migration validation failed: ${validation.errors.join(', ')}`);
    }

    // 5. 保存新配置
    await this.saveQuizConfiguration(userId, newQuizConfig);

    // 6. 清理旧配置
    await this.cleanupLegacyQuizSettings(userId);

    // 7. 记录迁移完成
    await this.recordMigrationCompletion(userId);

    return { success: true, message: 'Migration completed successfully' };
  }

  private async mapToNewArchitecture(legacyConfig: LegacyConfig): Promise<QuizConfiguration> {
    return {
      layer0_dataset_presentation: {
        default_difficulty_preference: this.mapDifficultyLevel(legacyConfig),
        session_length_preference: 'medium',
        preferred_pack_categories: this.inferPackCategories(legacyConfig),
        auto_select_recommended: true
      },
      layer1_user_choice: {
        preferred_view_type: legacyConfig.preferred_view_type || 'wheel',
        active_skin_id: legacyConfig.active_skin_id || 'default',
        dark_mode: legacyConfig.dark_mode || false,
        color_mode: legacyConfig.color_mode || 'warm'
      },
      layer2_rendering_strategy: {
        render_engine_preferences: legacyConfig.render_engine_preferences || this.getDefaultEngines(),
        content_display_mode_preferences: legacyConfig.content_display_mode_preferences || this.getDefaultModes(),
        performance_mode: 'balanced'
      },
      layer3_skin_base: {
        fonts: {
          primary_font: legacyConfig.font_family || 'system',
          size_scale: legacyConfig.font_size_scale || 1.0
        },
        animations: {
          enable_animations: legacyConfig.enable_animations !== false,
          animation_speed: legacyConfig.animation_speed || 'normal',
          reduce_motion: legacyConfig.reduce_motion || false
        }
      },
      layer4_view_details: this.mapViewDetails(legacyConfig),
      layer5_accessibility: this.mapAccessibilitySettings(legacyConfig)
    };
  }
}
```

### 向后兼容性保证

#### API兼容性层
```typescript
class BackwardCompatibilityLayer {
  // 提供旧API的兼容性支持
  async getLegacyViewType(userId: string): Promise<ViewType> {
    const quizConfig = await this.getQuizConfiguration(userId);
    return quizConfig.layer1_user_choice.preferred_view_type;
  }

  async setLegacyViewType(userId: string, viewType: ViewType): Promise<void> {
    await this.updateQuizConfiguration(userId, {
      layer: 'layer1',
      field: 'preferred_view_type',
      value: viewType
    });
  }

  // 提供迁移状态检查
  async checkMigrationStatus(userId: string): Promise<MigrationStatus> {
    const hasLegacyConfig = await this.hasLegacyQuizSettings(userId);
    const hasNewConfig = await this.hasQuizConfiguration(userId);

    if (hasLegacyConfig && !hasNewConfig) {
      return {
        status: 'needs_migration',
        action: 'auto_migrate',
        legacy_config_found: true,
        new_config_exists: false
      };
    }

    if (hasLegacyConfig && hasNewConfig) {
      return {
        status: 'migration_complete',
        action: 'cleanup_legacy',
        legacy_config_found: true,
        new_config_exists: true
      };
    }

    return {
      status: 'up_to_date',
      action: 'none',
      legacy_config_found: false,
      new_config_exists: hasNewConfig
    };
  }
}
```

### 用户引导策略

#### 迁移通知系统
```typescript
class MigrationNotificationSystem {
  async showMigrationNotification(userId: string): Promise<void> {
    const migrationStatus = await this.checkMigrationStatus(userId);

    switch (migrationStatus.status) {
      case 'needs_migration':
        await this.showNotification({
          type: 'info',
          title: 'Quiz设置升级',
          message: '我们已将Quiz设置迁移到专门的配置页面，为您提供更好的个性化体验。',
          actions: [
            { label: '立即体验', action: 'navigate_to_quiz_settings' },
            { label: '稍后提醒', action: 'remind_later' }
          ]
        });
        break;

      case 'migration_complete':
        await this.showNotification({
          type: 'success',
          title: 'Quiz设置已升级',
          message: '您的Quiz配置已成功迁移到新的6层个性化配置系统。',
          actions: [
            { label: '查看新功能', action: 'navigate_to_quiz_settings' },
            { label: '知道了', action: 'dismiss' }
          ]
        });
        break;
    }
  }
}
```

### 测试策略

#### 迁移测试套件
```typescript
describe('Quiz Settings Migration', () => {
  describe('Data Migration', () => {
    test('should migrate legacy settings to 6-layer architecture', async () => {
      const legacyConfig = createLegacyConfig();
      const migration = new QuizSettingsMigrationWorkflow();

      const result = await migration.performMigration('user123');

      expect(result.success).toBe(true);

      const newConfig = await getQuizConfiguration('user123');
      expect(newConfig).toHaveProperty('layer0_dataset_presentation');
      expect(newConfig).toHaveProperty('layer1_user_choice');
      expect(newConfig).toHaveProperty('layer2_rendering_strategy');
      expect(newConfig).toHaveProperty('layer3_skin_base');
      expect(newConfig).toHaveProperty('layer4_view_details');
      expect(newConfig).toHaveProperty('layer5_accessibility');
    });

    test('should preserve user preferences during migration', async () => {
      const legacyConfig = {
        preferred_view_type: 'wheel',
        dark_mode: true,
        color_mode: 'cool',
        active_skin_id: 'ocean-blue'
      };

      await setLegacyConfig('user123', legacyConfig);
      await migration.performMigration('user123');

      const newConfig = await getQuizConfiguration('user123');

      expect(newConfig.layer1_user_choice.preferred_view_type).toBe('wheel');
      expect(newConfig.layer1_user_choice.dark_mode).toBe(true);
      expect(newConfig.layer1_user_choice.color_mode).toBe('cool');
      expect(newConfig.layer1_user_choice.active_skin_id).toBe('ocean-blue');
    });
  });

  describe('User Experience', () => {
    test('should provide seamless navigation from Settings to QuizSettings', async () => {
      const { getByText, getByRole } = render(<Settings />);

      const quizSettingsButton = getByText('进入Quiz设置');
      fireEvent.click(quizSettingsButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/quiz-settings');
      });
    });

    test('should display 6-layer configuration tabs in QuizSettings', async () => {
      const { getByText } = render(<QuizSettings />);

      expect(getByText('数据集展现')).toBeInTheDocument();
      expect(getByText('基础选择')).toBeInTheDocument();
      expect(getByText('渲染策略')).toBeInTheDocument();
      expect(getByText('皮肤基础')).toBeInTheDocument();
      expect(getByText('视图细节')).toBeInTheDocument();
      expect(getByText('可访问性')).toBeInTheDocument();
    });
  });
});
```

## 🎯 总结

通过Quiz设置迁移，我们实现了：

### 主要改进
1. **功能分离**: Quiz配置从通用设置中分离，提供专门的配置体验
2. **专业化配置**: 6层配置架构提供从基础到专业级的个性化选项
3. **用户体验优化**: 清晰的导航和专门的配置界面
4. **数据完整性**: 完整的迁移策略确保用户配置不丢失
5. **向后兼容**: 提供兼容性层确保现有功能正常工作

### 技术优势
1. **类型安全**: 完整的TypeScript类型定义
2. **数据验证**: Zod schema确保配置数据的正确性
3. **测试覆盖**: 全面的测试策略确保迁移质量
4. **文档同步**: 文档与实现保持同步

### 用户价值
1. **个性化体验**: 更细致的个性化配置选项
2. **专业工具**: 专门的Quiz配置管理界面
3. **无缝迁移**: 用户无感知的配置迁移
4. **功能发现**: 通过专门页面发现更多配置选项

这个迁移为Quiz系统的未来发展奠定了坚实的基础，为用户提供了更好的个性化配置体验。
