/**
 * <PERSON><PERSON><PERSON> to create a local SQLite database file for development
 * Run with: node create-local-db.js
 */
const fs = require('node:fs');
const path = require('node:path');
const sqlite3 = require('sqlite3').verbose();

// Path to the local database file
const dbPath = path.join(__dirname, 'local.db');

// Check if the database file already exists
if (fs.existsSync(dbPath)) {
  console.log(`Local database file already exists at: ${dbPath}`);
  console.log('If you want to recreate it, delete the file first.');
  process.exit(0);
}

// Create a new database file
console.log(`Creating new SQLite database file at: ${dbPath}`);
const db = new sqlite3.Database(dbPath);

// Function to run a SQL script from a file
const runSqlScript = (scriptPath) => {
  return new Promise((resolve, reject) => {
    fs.readFile(scriptPath, 'utf8', (err, data) => {
      if (err) {
        console.error(`Error reading SQL script ${scriptPath}:`, err);
        return reject(err);
      }

      // Split the script into individual statements
      const statements = data
        .split(';')
        .map((s) => s.trim())
        .filter((s) => s.length > 0);

      // Execute each statement
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        let hasError = false;
        statements.forEach((statement) => {
          db.run(statement, (err) => {
            if (err) {
              console.error(`Error executing statement: ${statement}`, err);
              hasError = true;
            }
          });
        });

        if (hasError) {
          db.run('ROLLBACK', () => {
            reject(new Error(`Error executing script: ${scriptPath}`));
          });
        } else {
          db.run('COMMIT', () => {
            console.log(`Successfully executed script: ${scriptPath}`);
            resolve();
          });
        }
      });
    });
  });
};

// Initialize the database schema and seed data
const initializeDatabase = async () => {
  try {
    // Create tables
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'schema', 'init.sql'));

    // Load system data
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'config', 'emotions.sql'));
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'config', 'emoji_sets.sql'));
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'config', 'image_emoji_sets.sql'));
    await runSqlScript(
      path.join(__dirname, 'public', 'seeds', 'config', 'additional_emoji_sets.sql')
    );
    await runSqlScript(
      path.join(__dirname, 'public', 'seeds', 'config', 'animated_emoji_sets.sql')
    );
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'config', 'ui_labels_updated.sql'));
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'config', 'tags.sql'));
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'config', 'app_settings.sql'));

    // Load translations
    await runSqlScript(
      path.join(__dirname, 'public', 'seeds', 'config', 'emotion_translations.sql')
    );
    await runSqlScript(
      path.join(__dirname, 'public', 'seeds', 'config', 'ui_label_translations_updated.sql')
    );
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'config', 'tag_translations.sql'));

    // Load version info
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'config', 'version.sql'));

    // Load test user data
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'test', 'users.sql'));
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'test', 'user_preferences.sql'));
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'test', 'user_streaks.sql'));
    // Update emotion selections table structure before loading mood entries
    await runSqlScript(
      path.join(__dirname, 'public', 'seeds', 'schema', 'update_emotion_selections.sql')
    );
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'test', 'mood_entries.sql'));
    await runSqlScript(path.join(__dirname, 'public', 'seeds', 'test', 'mood_entry_tags.sql'));

    console.log('Database initialization completed successfully!');
  } catch (error) {
    console.error('Error initializing database:', error);
  } finally {
    // Close the database connection
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      } else {
        console.log('Database connection closed.');
      }
    });
  }
};

// Run the initialization
initializeDatabase();
