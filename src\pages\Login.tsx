/**
 * 登录页面
 * 支持邮箱密码登录和社交登录
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useAuth';
import { AlertCircle, Eye, EyeOff, Loader2, Lock, LogIn, Mail, Wifi, WifiOff } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

export const Login: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { login, isAuthenticated, isLoading, error, clearError, isOnline } = useAuth();

  // 表单状态
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 如果已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // 清除错误
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // 处理表单输入
  const handleInputChange =
    (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: e.target.value,
      }));

      // 清除错误
      if (error) {
        clearError();
      }
    };

  // 处理记住我选项
  const handleRememberMeChange = (checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      rememberMe: checked,
    }));
  };

  // 表单验证
  const validateForm = (): boolean => {
    if (!formData.email.trim()) {
      toast.error(t('auth.email_required', { fallback: '请输入邮箱地址' }));
      return false;
    }

    if (!formData.email.includes('@')) {
      toast.error(t('auth.email_invalid', { fallback: '请输入有效的邮箱地址' }));
      return false;
    }

    if (!formData.password.trim()) {
      toast.error(t('auth.password_required', { fallback: '请输入密码' }));
      return false;
    }

    if (formData.password.length < 6) {
      toast.error(t('auth.password_too_short', { fallback: '密码至少需要6个字符' }));
      return false;
    }

    return true;
  };

  // 处理登录提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!isOnline) {
      toast.error(t('auth.login_requires_internet', { fallback: '登录需要网络连接' }));
      return;
    }

    try {
      setIsSubmitting(true);
      clearError();

      const result = await login({
        email: formData.email.trim(),
        password: formData.password,
      });

      if (result.success) {
        toast.success(t('auth.login_success', { fallback: '登录成功' }));
        navigate('/', { replace: true });
      } else {
        toast.error(result.error || t('auth.login_failed', { fallback: '登录失败' }));
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error(t('auth.login_error', { fallback: '登录时发生错误' }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理社交登录
  const handleSocialLogin = (provider: 'google' | 'apple') => {
    if (!isOnline) {
      toast.error(t('auth.login_requires_internet', { fallback: '登录需要网络连接' }));
      return;
    }

    // TODO: 实现社交登录
    toast.info(
      t('auth.social_login_coming_soon', {
        fallback: `${provider === 'google' ? 'Google' : 'Apple'} 登录即将推出`,
      })
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold">
              {t('auth.login', { fallback: '登录' })}
            </CardTitle>
            <div className="flex items-center">
              {isOnline ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
            </div>
          </div>
          <CardDescription>
            {t('auth.login_description', { fallback: '登录您的账户以同步数据' })}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* 网络状态提示 */}
          {!isOnline && (
            <Alert variant="destructive">
              <WifiOff className="h-4 w-4" />
              <AlertDescription>
                {t('auth.offline_warning', { fallback: '当前离线，登录需要网络连接' })}
              </AlertDescription>
            </Alert>
          )}

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 登录表单 */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 邮箱输入 */}
            <div className="space-y-2">
              <Label htmlFor="email">{t('auth.email', { fallback: '邮箱地址' })}</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="email"
                  type="email"
                  placeholder={t('auth.email_placeholder', { fallback: '请输入邮箱地址' })}
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  className="pl-10"
                  disabled={isLoading || isSubmitting}
                  autoComplete="email"
                  required
                />
              </div>
            </div>

            {/* 密码输入 */}
            <div className="space-y-2">
              <Label htmlFor="password">{t('auth.password', { fallback: '密码' })}</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder={t('auth.password_placeholder', { fallback: '请输入密码' })}
                  value={formData.password}
                  onChange={handleInputChange('password')}
                  className="pl-10 pr-10"
                  disabled={isLoading || isSubmitting}
                  autoComplete="current-password"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading || isSubmitting}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* 记住我 */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={formData.rememberMe}
                onCheckedChange={handleRememberMeChange}
                disabled={isLoading || isSubmitting}
              />
              <Label htmlFor="remember" className="text-sm">
                {t('auth.remember_me', { fallback: '记住我' })}
              </Label>
            </div>

            {/* 登录按钮 */}
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || isSubmitting || !isOnline}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('auth.logging_in', { fallback: '登录中...' })}
                </>
              ) : (
                <>
                  <LogIn className="mr-2 h-4 w-4" />
                  {t('auth.login', { fallback: '登录' })}
                </>
              )}
            </Button>
          </form>

          {/* 分隔线 */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                {t('auth.or', { fallback: '或' })}
              </span>
            </div>
          </div>

          {/* 社交登录 */}
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="outline"
              onClick={() => handleSocialLogin('google')}
              disabled={isLoading || isSubmitting || !isOnline}
            >
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Google
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSocialLogin('apple')}
              disabled={isLoading || isSubmitting || !isOnline}
            >
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
              </svg>
              Apple
            </Button>
          </div>
        </CardContent>

        <CardFooter className="flex flex-col space-y-2">
          <div className="text-sm text-center text-muted-foreground">
            {t('auth.no_account', { fallback: '还没有账户？' })}
            <Link to="/register" className="ml-1 text-primary hover:underline">
              {t('auth.register_now', { fallback: '立即注册' })}
            </Link>
          </div>
          <div className="text-sm text-center">
            <Link to="/forgot-password" className="text-primary hover:underline">
              {t('auth.forgot_password', { fallback: '忘记密码？' })}
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};
