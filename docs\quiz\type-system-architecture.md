# 泛化类型系统架构文档

本文档详细说明了项目的泛化类型系统架构，包括 Schema 定义、类型生成、验证机制和最佳实践。这是一个可复用的类型系统设计，适用于任何需要类型安全和多语言支持的项目。该系统与6层个性化配置架构完美整合，支持从Settings页面到QuizSettings页面的无缝迁移。

> **架构更新**: 本文档已更新以反映配置系统重构后的新架构，包括全局应用设置与Quiz系统配置的完全分离、6层个性化配置类型定义和专门的配置管理系统。

## 🆕 配置系统类型架构 (新增)

### 配置系统分层设计
```
全局应用设置 (GlobalAppConfig)
├── 主题模式 (theme_mode)
├── 界面语言 (language)
├── 通知设置 (notifications_enabled)
├── 音效设置 (sound_enabled)
└── 无障碍配置 (accessibility)

Quiz系统配置 (UserQuizPreferences)
├── Layer 0: 数据集展现配置 (dataset_presentation)
├── Layer 1: 用户选择配置 (user_choice)
├── Layer 2: 渲染策略配置 (rendering_strategy)
├── Layer 3: 皮肤基础配置 (skin_base)
├── Layer 4: 视图细节配置 (view_detail)
└── Layer 5: 无障碍增强配置 (accessibility)

配置合并系统
├── 系统默认配置 (system_defaults)
├── 用户偏好配置 (user_preferences)
├── 包覆盖配置 (pack_overrides)
└── 最终会话配置 (session_config)
```

### 配置系统类型定义

#### 1. 全局应用配置类型
```typescript
// 全局应用配置 Schema
export const GlobalAppConfigSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  config_name: z.string().min(1).max(50).default('default'),

  // 主题设置
  theme_mode: z.enum(['light', 'dark', 'system']).default('system'),

  // 语言设置
  language: z.enum(['zh-CN', 'en-US']).default('zh-CN'),

  // 通知设置
  notifications_enabled: z.boolean().default(true),

  // 音效设置
  sound_enabled: z.boolean().default(true),

  // 无障碍配置 (JSON字符串)
  accessibility: z.string().default(JSON.stringify({
    high_contrast: false,
    large_text: false,
    reduce_motion: false,
    screen_reader_support: false
  })),

  // 标准字段
  is_active: z.boolean().default(true),
  is_default: z.boolean().default(false),
  created_at: TimestampSchema,
  updated_at: TimestampSchema
});

export type GlobalAppConfig = z.infer<typeof GlobalAppConfigSchema>;
```

#### 2. Quiz系统配置类型
```typescript
// Quiz偏好配置 Schema
export const UserQuizPreferencesSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  config_name: z.string().min(1).max(50).default('default'),

  // 6层个性化配置 (JSON字符串)
  presentation_config: z.string(), // QuizPresentationConfig的JSON序列化

  // 配置元数据
  config_version: z.string().default('2.0'),
  personalization_level: z.number().int().min(0).max(100).default(50),

  // 状态字段
  is_active: z.boolean().default(true),
  is_default: z.boolean().default(false),

  // 标准字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema
});

// Quiz展现配置类型 (TypeScript接口)
export interface QuizPresentationConfig {
  layer0_dataset_presentation: {
    preferred_pack_categories: string[];
    default_difficulty_preference: 'beginner' | 'regular' | 'advanced' | 'expert';
    session_length_preference: 'short' | 'medium' | 'long';
    auto_select_recommended: boolean;
    restore_progress: boolean;
  };
  layer1_user_choice: {
    preferred_view_type: 'wheel' | 'card' | 'bubble' | 'list';
    active_skin_id: string;
    color_mode: 'warm' | 'cool' | 'neutral';
    user_level: 'beginner' | 'regular' | 'advanced';
  };
  layer2_rendering_strategy: {
    render_engine_preferences: Record<string, string>;
    performance_mode: 'high_quality' | 'balanced' | 'performance';
    supported_content_types: Record<string, boolean>;
  };
  layer3_skin_base: {
    selected_skin_id: string;
    colors: Record<string, string>;
    animations: {
      enable_animations: boolean;
      animation_speed: 'slow' | 'normal' | 'fast';
      reduce_motion: boolean;
    };
  };
  layer4_view_detail: {
    wheel_config: {
      container_size: number;
      wheel_radius: number;
      emotion_display_mode: string;
      show_labels: boolean;
      show_emojis: boolean;
    };
  };
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    keyboard_navigation: boolean;
    voice_guidance: boolean;
  };
}

export type UserQuizPreferences = z.infer<typeof UserQuizPreferencesSchema>;
```

#### 3. 配置合并系统类型
```typescript
// Quiz包覆盖配置 Schema
export const QuizPackOverridesSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  pack_id: IdSchema,

  // 覆盖配置 (JSON字符串)
  presentation_overrides: z.string(), // 部分QuizPresentationConfig的JSON序列化

  // 覆盖元数据
  override_reason: z.enum(['user_preference', 'pack_requirement', 'accessibility', 'performance']),
  override_priority: z.number().int().min(1).max(10).default(1),

  // 状态字段
  is_active: z.boolean().default(true),

  // 标准字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema
});

// Quiz会话配置 Schema
export const QuizSessionConfigSchema = z.object({
  id: IdSchema,
  session_id: IdSchema,
  user_id: IdSchema,
  pack_id: IdSchema,

  // 最终合并的配置 (JSON字符串)
  final_presentation_config: z.string(), // 完整QuizPresentationConfig的JSON序列化

  // 配置来源追踪 (JSON字符串)
  config_sources: z.string(), // ConfigSources的JSON序列化

  // 配置元数据
  personalization_level: z.number().int().min(0).max(100),
  config_version: z.string(),

  // 标准字段
  created_at: TimestampSchema
});

// 配置来源追踪类型
export interface ConfigSources {
  base_config_id: string;
  applied_overrides: string[];
  applied_defaults: string[];
  merge_timestamp: string;
}

export type QuizPackOverrides = z.infer<typeof QuizPackOverridesSchema>;
export type QuizSessionConfig = z.infer<typeof QuizSessionConfigSchema>;
```

## 📋 目录

- [配置系统类型架构](#配置系统类型架构)
- [架构概览](#架构概览)
- [Schema 定义系统](#schema-定义系统)
- [类型生成机制](#类型生成机制)
- [验证系统](#验证系统)
- [多语言类型支持](#多语言类型支持)
- [Repository 模式](#repository-模式)
- [Service 模式](#service-模式)
- [最佳实践](#最佳实践)

## 🏗️ 架构概览

### 设计哲学

我们的类型系统基于以下核心原则：

1. **单一数据源** - SQL Schema 作为类型定义的唯一真实来源
2. **分层架构** - 基础类型、API 类型、扩展类型清晰分离
3. **类型安全** - 编译时 + 运行时双重类型检查
4. **自动生成** - 减少手工维护，提高一致性
5. **可扩展性** - 易于添加新的实体和功能

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Pages/Components)                 │
├─────────────────────────────────────────────────────────────┤
│                    Hook 层 (Data Hooks)                     │
├─────────────────────────────────────────────────────────────┤
│                   Service 层 (Business Logic)               │
├─────────────────────────────────────────────────────────────┤
│                 Repository 层 (Data Access)                 │
├─────────────────────────────────────────────────────────────┤
│                   Schema 层 (Type Definitions)              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │   base.ts   │   api.ts    │translation.ts│generator.ts │   │
│  │ (基础类型)   │ (API类型)    │ (翻译扩展)   │ (生成工具)   │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                   数据库层 (SQL Schema)                      │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. Schema 定义层
- **base.ts**: 基础实体类型（直接映射数据库表）
- **api.ts**: API 输入输出类型（服务层需求）
- **translation.ts**: 多语言扩展类型
- **generator.ts**: 类型生成和验证工具

#### 2. 验证层
- **Zod Schema**: 运行时类型验证
- **TypeScript**: 编译时类型检查
- **Custom Validators**: 业务规则验证

#### 3. 应用层
- **Repository**: 数据访问抽象
- **Service**: 业务逻辑封装
- **Hook**: React 数据管理

## 📐 Schema 定义系统

### 基础 Schema 模式

#### 1. 实体 Schema 定义
```typescript
// 基础实体 Schema 模板
export const EntitySchema = z.object({
  // 标准字段
  id: IdSchema,
  created_at: TimestampSchema,
  updated_at: TimestampSchema,

  // 软删除支持
  is_deleted: z.boolean().default(false),
  deleted_at: TimestampSchema.optional(),

  // 审计字段
  created_by: OptionalIdSchema,
  updated_by: OptionalIdSchema,

  // 业务字段
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),

  // 扩展字段（根据需要添加）
  // ...
});

export type Entity = z.infer<typeof EntitySchema>;
```

#### 2. 通用 Schema 组件
```typescript
// 可复用的 Schema 组件
export const IdSchema = z.string().uuid();
export const OptionalIdSchema = z.string().uuid().optional();
export const TimestampSchema = z.string().datetime();
export const LanguageCodeSchema = z.enum([
  'en', 'zh', 'zh-TW', 'ja', 'ko', 'es', 'fr', 'de',
  'it', 'pt', 'ru', 'ar', 'hi', 'th', 'vi'
]);

// 分页 Schema
export const PaginationSchema = z.object({
  limit: z.number().int().min(1).max(1000).default(100),
  offset: z.number().int().min(0).default(0)
});

// 排序 Schema
export const SortingSchema = z.object({
  orderBy: z.string().optional(),
  orderDirection: z.enum(['ASC', 'DESC']).default('ASC')
});

// 过滤器基础 Schema
export const BaseFilterSchema = z.object({
  searchTerm: z.string().optional(),
  is_deleted: z.boolean().optional(),
  created_after: TimestampSchema.optional(),
  created_before: TimestampSchema.optional()
}).merge(PaginationSchema).merge(SortingSchema);
```

### API Schema 模式

#### 1. CRUD 操作 Schema
```typescript
// 创建操作 Schema 模板
export const CreateEntityDataSchema = EntitySchema
  .omit({
    created_at: true,
    updated_at: true,
    is_deleted: true,
    deleted_at: true,
    updated_by: true
  })
  .extend({
    id: IdSchema, // 必需，用于客户端生成
    // 其他创建时的特殊字段
  });

// 更新操作 Schema 模板
export const UpdateEntityDataSchema = EntitySchema
  .omit({
    id: true,
    created_at: true,
    updated_at: true,
    created_by: true
  })
  .partial() // 所有字段都是可选的
  .extend({
    updated_by: OptionalIdSchema
  });

// 过滤器 Schema 模板
export const EntityFilterSchema = BaseFilterSchema.extend({
  // 实体特定的过滤字段
  name: z.string().optional(),
  created_by: OptionalIdSchema,
  // ...
});
```

#### 2. Service 输入 Schema
```typescript
// Service 层输入 Schema 模板
export const CreateEntityInputSchema = z.object({
  // 业务字段（用户友好的格式）
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),

  // 关联数据
  tags: z.array(z.string()).max(10).optional(),

  // 翻译数据
  translations: z.array(TranslationInputSchema).optional(),

  // 其他业务逻辑字段
  // ...
});

export const UpdateEntityInputSchema = CreateEntityInputSchema
  .partial()
  .extend({
    // 更新特定的字段
  });
```

## 🔄 类型生成机制

### 自动类型生成

#### 1. Schema 到 TypeScript 类型
```typescript
// 自动生成 TypeScript 类型
export type Entity = z.infer<typeof EntitySchema>;
export type CreateEntityData = z.infer<typeof CreateEntityDataSchema>;
export type UpdateEntityData = z.infer<typeof UpdateEntityDataSchema>;
export type EntityFilter = z.infer<typeof EntityFilterSchema>;
export type CreateEntityInput = z.infer<typeof CreateEntityInputSchema>;
export type UpdateEntityInput = z.infer<typeof UpdateEntityInputSchema>;
```

#### 2. 类型生成工具
```typescript
// generator.ts - 类型生成辅助工具
export function generateCRUDSchemas<T extends z.ZodObject<any>>(
  baseSchema: T,
  entityName: string,
  options?: {
    excludeFromCreate?: (keyof z.infer<T>)[];
    excludeFromUpdate?: (keyof z.infer<T>)[];
    additionalFilters?: z.ZodObject<any>;
  }
) {
  const createSchema = baseSchema
    .omit({
      created_at: true,
      updated_at: true,
      is_deleted: true,
      deleted_at: true,
      updated_by: true,
      ...(options?.excludeFromCreate?.reduce((acc, key) => ({ ...acc, [key]: true }), {}) || {})
    })
    .extend({
      id: IdSchema
    });

  const updateSchema = baseSchema
    .omit({
      id: true,
      created_at: true,
      updated_at: true,
      created_by: true,
      ...(options?.excludeFromUpdate?.reduce((acc, key) => ({ ...acc, [key]: true }), {}) || {})
    })
    .partial()
    .extend({
      updated_by: OptionalIdSchema
    });

  const filterSchema = BaseFilterSchema.extend({
    name: z.string().optional(),
    created_by: OptionalIdSchema,
    ...(options?.additionalFilters?.shape || {})
  });

  return {
    createSchema,
    updateSchema,
    filterSchema,
    // 生成对应的 TypeScript 类型
    types: {
      Entity: baseSchema,
      created_ata: createSchema,
      updated_ata: updateSchema,
      Filter: filterSchema
    }
  };
}
```

### 使用示例

#### 1. 快速生成实体 Schema
```typescript
// 定义基础实体
const UserSchema = z.object({
  id: IdSchema,
  username: z.string().min(1).max(50),
  email: z.string().email().optional(),
  display_name: z.string().max(100).optional(),
  is_vip: z.boolean().default(false),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: OptionalIdSchema,
  updated_by: OptionalIdSchema,
  is_deleted: z.boolean().default(false),
  deleted_at: TimestampSchema.optional()
});

// 自动生成 CRUD Schema
const userSchemas = generateCRUDSchemas(UserSchema, 'User', {
  excludeFromCreate: ['is_vip'], // VIP 状态不能在创建时设置
  additionalFilters: z.object({
    is_vip: z.boolean().optional(),
    username_contains: z.string().optional()
  })
});

// 导出生成的 Schema 和类型
export const {
  createSchema: CreateUserDataSchema,
  updateSchema: UpdateUserDataSchema,
  filterSchema: UserFilterSchema
} = userSchemas;

export type User = z.infer<typeof UserSchema>;
export type CreateUserData = z.infer<typeof CreateUserDataSchema>;
export type UpdateUserData = z.infer<typeof UpdateUserDataSchema>;
export type UserFilter = z.infer<typeof UserFilterSchema>;
```

## ✅ 验证系统

### 多层验证架构

#### 1. Schema 验证层
```typescript
// 基础验证函数
export function validateSchema<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: string[] } {
  const result = schema.safeParse(data);

  if (result.success) {
    return { success: true, data: result.data };
  }

  return {
    success: false,
    errors: result.error.issues.map(issue =>
      `${issue.path.join('.')}: ${issue.message}`
    )
  };
}

// 异步验证支持
export async function validateSchemaAsync<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  customValidators?: Array<(data: T) => Promise<string | null>>
): Promise<{ success: true; data: T } | { success: false; errors: string[] }> {
  // 基础 Schema 验证
  const baseResult = validateSchema(schema, data);
  if (!baseResult.success) {
    return baseResult;
  }

  // 自定义异步验证
  if (customValidators) {
    const errors: string[] = [];
    for (const validator of customValidators) {
      const error = await validator(baseResult.data);
      if (error) {
        errors.push(error);
      }
    }

    if (errors.length > 0) {
      return { success: false, errors };
    }
  }

  return { success: true, data: baseResult.data };
}
```

#### 2. 业务规则验证
```typescript
// 业务规则验证器
export class EntityValidator<T> {
  private customRules: Array<(data: T) => Promise<string | null>> = [];

  addRule(rule: (data: T) => Promise<string | null>): this {
    this.customRules.push(rule);
    return this;
  }

  async validate(schema: z.ZodSchema<T>, data: unknown): Promise<ValidationResult<T>> {
    return validateSchemaAsync(schema, data, this.customRules);
  }
}

// 使用示例
const userValidator = new EntityValidator<CreateUserInput>()
  .addRule(async (data) => {
    // 检查用户名是否已存在
    const exists = await userRepository.existsByUsername(data.username);
    return exists ? 'Username already exists' : null;
  })
  .addRule(async (data) => {
    // 检查邮箱格式和唯一性
    if (data.email) {
      const exists = await userRepository.existsByEmail(data.email);
      return exists ? 'Email already exists' : null;
    }
    return null;
  });
```

### 验证中间件

#### 1. Repository 层验证
```typescript
export abstract class ValidatedRepository<T, created_ata, updated_ata>
  extends BaseRepository<T, created_ata, updated_ata> {

  protected abstract getCreateSchema(): z.ZodSchema<created_ata>;
  protected abstract getUpdateSchema(): z.ZodSchema<updated_ata>;
  protected abstract getValidator(): EntityValidator<created_ata>;

  async create(context: DatabaseContext, data: unknown): Promise<T> {
    // 验证创建数据
    const validation = await this.getValidator().validate(
      this.getCreateSchema(),
      data
    );

    if (!validation.success) {
      throw new ValidationError(validation.errors);
    }

    return super.create(context, validation.data);
  }

  async update(context: DatabaseContext, id: string, data: unknown): Promise<T> {
    // 验证更新数据
    const validation = validateSchema(this.getUpdateSchema(), data);

    if (!validation.success) {
      throw new ValidationError(validation.errors);
    }

    return super.update(context, id, validation.data);
  }
}
```

#### 2. Service 层验证
```typescript
export abstract class ValidatedService<T, CreateInput, UpdateInput>
  extends BaseService<T, CreateInput, UpdateInput> {

  protected abstract getCreateInputSchema(): z.ZodSchema<CreateInput>;
  protected abstract getUpdateInputSchema(): z.ZodSchema<UpdateInput>;

  async create(input: unknown): Promise<ServiceResult<T>> {
    try {
      // 验证输入数据
      const validation = validateSchema(this.getCreateInputSchema(), input);

      if (!validation.success) {
        return this.createErrorResult('VALIDATION_ERROR', validation.errors.join('; '));
      }

      // 转换为 Repository 数据格式
      const created_ata = await this.transformCreateInput(validation.data);

      // 调用 Repository
      const result = await this.repository.create(
        await this.getDatabaseContext(),
        created_ata
      );

      return this.createSuccessResult(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  protected abstract transformCreateInput(input: CreateInput): Promise<any>;
}
```

## 🌍 多语言类型支持

### 翻译类型系统

#### 1. 基础翻译类型
```typescript
// 通用翻译接口
export const TranslationSchema = z.object({
  entityId: z.string().min(1),
  languageCode: LanguageCodeSchema,
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional()
});

// 可翻译实体基础类型
export const TranslatableEntitySchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1), // 默认语言
  description: z.string().optional(), // 默认语言
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),

  // 运行时翻译字段
  localizedName: z.string().optional(),
  localizedDescription: z.string().optional(),
  translations: z.array(TranslationSchema).optional()
});

// 翻译输入类型
export const TranslationInputSchema = z.object({
  languageCode: LanguageCodeSchema,
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional()
});
```

#### 2. 翻译扩展生成器
```typescript
// 为任何实体添加翻译支持
export function makeTranslatable<T extends z.ZodObject<any>>(
  baseSchema: T,
  entityName: string
) {
  // 扩展基础 Schema 以支持翻译
  const translatableSchema = baseSchema.extend({
    localizedName: z.string().optional(),
    localizedDescription: z.string().optional(),
    translations: z.array(TranslationSchema).optional()
  });

  // 生成翻译表 Schema
  const translationTableSchema = z.object({
    [`${entityName.toLowerCase()}_id`]: IdSchema,
    language_code: LanguageCodeSchema,
    translated_name: z.string().min(1),
    translated_description: z.string().optional()
  });

  // 生成创建输入 Schema（支持翻译）
  const createInputSchema = baseSchema
    .omit({
      id: true,
      created_at: true,
      updated_at: true,
      is_deleted: true,
      deleted_at: true
    })
    .extend({
      translations: z.array(TranslationInputSchema).optional()
    });

  return {
    translatableSchema,
    translationTableSchema,
    createInputSchema,
    types: {
      TranslatableEntity: translatableSchema,
      TranslationTable: translationTableSchema,
      CreateInput: createInputSchema
    }
  };
}
```

### 使用示例

#### 1. 创建可翻译实体
```typescript
// 基础情绪 Schema
const EmotionBaseSchema = z.object({
  id: IdSchema,
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  emoji: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  is_deleted: z.boolean().default(false),
  deleted_at: TimestampSchema.optional()
});

// 添加翻译支持
const emotionSchemas = makeTranslatable(EmotionBaseSchema, 'Emotion');

// 导出类型
export const EmotionSchema = emotionSchemas.translatableSchema;
export const EmotionTranslationSchema = emotionSchemas.translationTableSchema;
export const CreateEmotionInputSchema = emotionSchemas.createInputSchema;

export type Emotion = z.infer<typeof EmotionSchema>;
export type EmotionTranslation = z.infer<typeof EmotionTranslationSchema>;
export type CreateEmotionInput = z.infer<typeof CreateEmotionInputSchema>;
```

## 🎯 Quiz设置迁移类型系统

### 6层个性化配置类型定义

#### 1. Layer 0: 数据集展现配置
```typescript
export const Layer0DatasetPresentationSchema = z.object({
  default_difficulty_preference: z.enum(['beginner', 'regular', 'advanced', 'vip']),
  session_length_preference: z.enum(['short', 'medium', 'long']),
  preferred_pack_categories: z.array(z.string()),
  auto_select_recommended: z.boolean().default(true)
});

export type Layer0DatasetPresentation = z.infer<typeof Layer0DatasetPresentationSchema>;
```

#### 2. Layer 1: 用户选择配置
```typescript
export const Layer1UserChoiceSchema = z.object({
  preferred_view_type: z.enum(['wheel', 'card', 'bubble', 'galaxy', 'tree']),
  active_skin_id: z.string(),
  dark_mode: z.boolean().default(false),
  color_mode: z.enum(['warm', 'cool', 'neutral']).default('warm')
});

export type Layer1UserChoice = z.infer<typeof Layer1UserChoiceSchema>;
```

#### 3. Layer 2: 渲染策略配置
```typescript
export const Layer2RenderingStrategySchema = z.object({
  render_engine_preferences: z.object({
    wheel: z.enum(['D3', 'SVG', 'Canvas', 'R3F']).default('D3'),
    card: z.enum(['CSS', 'SVG', 'Canvas']).default('CSS'),
    bubble: z.enum(['Canvas', 'R3F', 'WebGL']).default('Canvas'),
    galaxy: z.enum(['R3F', 'WebGL', 'Three.js']).default('R3F')
  }),
  content_display_mode_preferences: z.object({
    wheel: z.enum(['text', 'emoji', 'textEmoji', 'icon']).default('textEmoji'),
    card: z.enum(['text', 'emoji', 'image', 'mixed']).default('mixed'),
    bubble: z.enum(['emoji', 'color', 'size', 'mixed']).default('emoji')
  }),
  performance_mode: z.enum(['performance', 'balanced', 'quality']).default('balanced')
});

export type Layer2RenderingStrategy = z.infer<typeof Layer2RenderingStrategySchema>;
```

#### 4. Layer 3: 皮肤基础配置
```typescript
export const Layer3SkinBaseSchema = z.object({
  fonts: z.object({
    primary_font: z.enum(['system', 'serif', 'sans-serif', 'monospace']).default('system'),
    size_scale: z.number().min(0.8).max(1.5).default(1.0)
  }),
  animations: z.object({
    enable_animations: z.boolean().default(true),
    animation_speed: z.enum(['slow', 'normal', 'fast']).default('normal'),
    reduce_motion: z.boolean().default(false)
  })
});

export type Layer3SkinBase = z.infer<typeof Layer3SkinBaseSchema>;
```

#### 5. Layer 4: 视图细节配置
```typescript
export const Layer4ViewDetailsSchema = z.object({
  wheel_config: z.object({
    radius: z.number().min(100).max(400).default(200),
    inner_radius: z.number().min(20).max(100).default(50),
    sector_padding: z.number().min(0).max(10).default(2),
    label_distance: z.number().min(10).max(50).default(20)
  }),
  card_config: z.object({
    card_width: z.number().min(100).max(300).default(150),
    card_height: z.number().min(80).max(200).default(120),
    grid_gap: z.number().min(5).max(30).default(15),
    border_radius: z.number().min(0).max(20).default(8)
  }),
  bubble_config: z.object({
    min_size: z.number().min(20).max(100).default(40),
    max_size: z.number().min(50).max(200).default(80),
    spacing: z.number().min(5).max(50).default(20),
    animation_duration: z.number().min(100).max(2000).default(500)
  })
});

export type Layer4ViewDetails = z.infer<typeof Layer4ViewDetailsSchema>;
```

#### 6. Layer 5: 可访问性配置
```typescript
export const Layer5AccessibilitySchema = z.object({
  high_contrast: z.boolean().default(false),
  large_text: z.boolean().default(false),
  keyboard_navigation: z.boolean().default(true),
  screen_reader_support: z.boolean().default(true),
  focus_indicators: z.boolean().default(true),
  color_blind_support: z.boolean().default(false)
});

export type Layer5Accessibility = z.infer<typeof Layer5AccessibilitySchema>;
```

### 完整的Quiz配置类型
```typescript
export const QuizConfigurationSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  layer0_dataset_presentation: Layer0DatasetPresentationSchema,
  layer1_user_choice: Layer1UserChoiceSchema,
  layer2_rendering_strategy: Layer2RenderingStrategySchema,
  layer3_skin_base: Layer3SkinBaseSchema,
  layer4_view_details: Layer4ViewDetailsSchema,
  layer5_accessibility: Layer5AccessibilitySchema,
  created_at: TimestampSchema,
  updated_at: TimestampSchema
});

export type QuizConfiguration = z.infer<typeof QuizConfigurationSchema>;
```

### 迁移类型定义
```typescript
// 迁移状态类型
export const MigrationStatusSchema = z.object({
  status: z.enum(['needs_migration', 'migration_complete', 'up_to_date']),
  action: z.enum(['auto_migrate', 'cleanup_legacy', 'none']),
  legacy_config_found: z.boolean(),
  new_config_exists: z.boolean(),
  migration_timestamp: TimestampSchema.optional()
});

export type MigrationStatus = z.infer<typeof MigrationStatusSchema>;

// 旧配置映射类型
export const LegacyConfigMappingSchema = z.object({
  preferred_view_type: z.string().optional(),
  render_engine_preferences: z.record(z.string()).optional(),
  content_display_mode_preferences: z.record(z.string()).optional(),
  dark_mode: z.boolean().optional(),
  color_mode: z.string().optional(),
  font_family: z.string().optional(),
  font_size_scale: z.number().optional(),
  enable_animations: z.boolean().optional(),
  animation_speed: z.string().optional(),
  reduce_motion: z.boolean().optional()
});

export type LegacyConfigMapping = z.infer<typeof LegacyConfigMappingSchema>;
```

### QuizSettings页面专用类型
```typescript
// QuizSettings页面状态类型
export const QuizSettingsStateSchema = z.object({
  activeLayer: z.enum(['layer0', 'layer1', 'layer2', 'layer3', 'layer4', 'layer5']).default('layer0'),
  isLoading: z.boolean().default(false),
  hasUnsavedChanges: z.boolean().default(false),
  lastSaved: TimestampSchema.optional(),
  validationErrors: z.record(z.array(z.string())).optional()
});

export type QuizSettingsState = z.infer<typeof QuizSettingsStateSchema>;

// 配置更新输入类型
export const UpdateQuizConfigInputSchema = z.object({
  layer: z.enum(['layer0', 'layer1', 'layer2', 'layer3', 'layer4', 'layer5']),
  field: z.string(),
  value: z.unknown()
});

export type UpdateQuizConfigInput = z.infer<typeof UpdateQuizConfigInputSchema>;
```

### 类型生成工具扩展
```typescript
// Quiz配置专用生成器
export function generateQuizConfigSchemas() {
  const createSchema = QuizConfigurationSchema
    .omit({
      id: true,
      created_at: true,
      updated_at: true
    })
    .extend({
      id: IdSchema
    });

  const updateSchema = QuizConfigurationSchema
    .omit({
      id: true,
      user_id: true,
      created_at: true,
      updated_at: true
    })
    .partial();

  const filterSchema = BaseFilterSchema.extend({
    user_id: OptionalIdSchema,
    layer0_difficulty: z.string().optional(),
    layer1_view_type: z.string().optional()
  });

  return {
    createSchema,
    updateSchema,
    filterSchema,
    types: {
      QuizConfiguration: QuizConfigurationSchema,
      CreateQuizConfigData: createSchema,
      UpdateQuizConfigData: updateSchema,
      QuizConfigFilter: filterSchema
    }
  };
}

// 导出生成的Schema
export const quizConfigSchemas = generateQuizConfigSchemas();
export const {
  createSchema: CreateQuizConfigDataSchema,
  updateSchema: UpdateQuizConfigDataSchema,
  filterSchema: QuizConfigFilterSchema
} = quizConfigSchemas;

// 导出类型
export type CreateQuizConfigData = z.infer<typeof CreateQuizConfigDataSchema>;
export type UpdateQuizConfigData = z.infer<typeof UpdateQuizConfigDataSchema>;
export type QuizConfigFilter = z.infer<typeof QuizConfigFilterSchema>;
```

## 🎯 配置系统最佳实践

### 1. 配置分离原则
```typescript
// ✅ 正确：全局应用设置与Quiz配置完全分离
import { useGlobalConfig } from '@/hooks/useGlobalConfig';
import { useQuizConfig } from '@/hooks/useQuizConfig';

const MyComponent = () => {
  // 全局应用设置
  const { themeMode, language, updateConfig } = useGlobalConfig();

  // Quiz系统配置
  const { preferredViewType, updatePresentationConfig } = useQuizConfig();

  // 分别处理不同类型的配置
};

// ❌ 错误：混合不同类型的配置
const badConfig = {
  theme: 'dark',           // 全局设置
  viewType: 'wheel',       // Quiz配置
  language: 'zh-CN'        // 全局设置
};
```

### 2. 类型安全的配置更新
```typescript
// ✅ 正确：使用类型安全的更新方法
const handleThemeChange = async (theme: 'light' | 'dark' | 'system') => {
  await updateConfig({ theme_mode: theme }); // 类型检查
};

const handleViewTypeChange = async (viewType: 'wheel' | 'card') => {
  await updatePresentationConfig({
    layer1_user_choice: {
      preferred_view_type: viewType // 类型检查
    }
  });
};

// ❌ 错误：绕过类型检查
const badUpdate = async () => {
  await updateConfig({ invalid_field: 'value' }); // 编译错误
};
```

### 3. 配置合并策略
```typescript
// ✅ 正确：使用配置合并服务
const generateFinalConfig = async (packId: string, sessionId: string) => {
  const sessionConfig = await generateSessionConfig(packId, sessionId);
  // 自动合并：用户偏好 + 包覆盖 + 系统默认
  return sessionConfig;
};

// ❌ 错误：手动合并配置
const badMerge = (userConfig: any, packConfig: any) => {
  return { ...userConfig, ...packConfig }; // 丢失优先级逻辑
};
```

### 4. 配置验证模式
```typescript
// ✅ 正确：使用Schema验证
const validateQuizConfig = (config: unknown) => {
  const result = QuizPresentationConfigSchema.safeParse(config);
  if (!result.success) {
    throw new Error(`Invalid config: ${result.error.message}`);
  }
  return result.data;
};

// ❌ 错误：跳过验证
const badValidation = (config: any) => {
  return config; // 没有类型检查
};
```

### 5. Hook使用模式
```typescript
// ✅ 正确：使用专门的配置Hook
const SettingsPage = () => {
  const {
    themeMode,
    language,
    accessibilityConfig,
    updateConfig,
    isOnline,
    lastSyncTime
  } = useGlobalConfig();

  // 处理全局设置...
};

const QuizSettingsPage = () => {
  const {
    preferences,
    presentationConfig,
    personalizationLevel,
    updatePresentationConfig,
    generateSessionConfig
  } = useQuizConfig();

  // 处理Quiz配置...
};

// ❌ 错误：在错误的地方使用配置Hook
const HomePage = () => {
  const { updatePresentationConfig } = useQuizConfig(); // 不应该在Home页面修改Quiz配置
};
```

## 📚 相关文档

- [配置系统实现总结](../implementation/config-system-implementation.md)
- [数据库设计文档](./database-design.md)
- [多语言系统文档](./internationalization.md)
- [类型系统使用指南](../src/types/README.md)
- [Repository 模式文档](./repository-pattern.md)
- [Service 模式文档](./service-pattern.md)
- [Quiz设置迁移总结](../quiz-settings-migration-summary.md)
- [用户个性化指南](./user-personalization-guide.md)

---

通过这个泛化的类型系统架构，我们实现了：
- **类型安全**：编译时和运行时的双重保障
- **代码复用**：可复用的 Schema 组件和生成器
- **自动化**：减少手工维护，提高开发效率
- **可扩展性**：易于添加新的实体和功能
- **多语言支持**：完整的国际化类型基础设施
- **配置分离**：全局应用设置与Quiz系统配置完全分离 ✨
- **6层配置**：专业级的个性化配置类型系统
- **混合架构**：离线优先 + 在线同步的配置管理 ✨
- **迁移支持**：完整的从Settings到QuizSettings的迁移类型定义
