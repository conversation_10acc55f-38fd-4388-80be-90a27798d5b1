/**
 * Vitest 测试设置文件
 * 配置全局测试环境和 Mock
 */

import { vi } from 'vitest';

// 全局 Mock 设置
global.console = {
  ...console,
  // 在测试中静默某些日志
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: console.warn,
  error: console.error,
};

// Mock 环境变量
process.env.NODE_ENV = 'test';
process.env.DB_TYPE = 'sqlite';
process.env.DATABASE_URL = ':memory:';

// 设置测试超时
vi.setConfig({
  testTimeout: 10000, // 10秒
  hookTimeout: 10000, // 10秒
});

// 全局测试钩子
beforeEach(() => {
  // 清理所有 Mock
  vi.clearAllMocks();
});

afterEach(() => {
  // 清理定时器
  vi.clearAllTimers();
});
