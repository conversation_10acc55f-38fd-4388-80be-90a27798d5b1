import { useState } from "react";
import { useLanguage, Language } from "@/contexts/LanguageContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Globe, BellRing, Shield, Accessibility, Crown, 
  Volume2, VolumeX, Eye, EyeOff, Type, Contrast
} from "lucide-react";
import { toast } from "sonner";

interface SystemSettingsProps {
  userLevel: 'beginner' | 'regular' | 'advanced' | 'vip';
}

const SystemSettings: React.FC<SystemSettingsProps> = ({ userLevel }) => {
  const { t, language, setLanguage } = useLanguage();
  
  // 本地状态管理
  const [notifications, setNotifications] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [reduceMotion, setReduceMotion] = useState(false);
  const [highContrast, setHighContrast] = useState(false);
  const [largeText, setLargeText] = useState(false);

  // 处理语言更改
  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang);
    const successMessage = lang === "en" 
      ? "Language changed to English"
      : "语言已更改为中文";
    toast.success(successMessage);
  };

  // 处理通知设置
  const handleNotificationToggle = (enabled: boolean) => {
    setNotifications(enabled);
    if (enabled) {
      // 请求通知权限
      if ('Notification' in window) {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            toast.success(t('settings.notifications_enabled', '通知已启用'));
          } else {
            toast.error(t('settings.notifications_denied', '通知权限被拒绝'));
            setNotifications(false);
          }
        });
      }
    } else {
      toast.success(t('settings.notifications_disabled', '通知已禁用'));
    }
  };

  // 处理音效设置
  const handleSoundToggle = (enabled: boolean) => {
    setSoundEnabled(enabled);
    localStorage.setItem('sound-enabled', enabled.toString());
    toast.success(enabled ? 
      t('settings.sound_enabled', '音效已启用') : 
      t('settings.sound_disabled', '音效已禁用')
    );
  };

  // 处理减少动画设置
  const handleReduceMotionToggle = (enabled: boolean) => {
    setReduceMotion(enabled);
    localStorage.setItem('reduce-motion', enabled.toString());
    
    // 应用CSS类
    if (enabled) {
      document.documentElement.classList.add('reduce-motion');
    } else {
      document.documentElement.classList.remove('reduce-motion');
    }
    
    toast.success(enabled ? 
      t('settings.reduce_motion_enabled', '减少动画已启用') : 
      t('settings.reduce_motion_disabled', '减少动画已禁用')
    );
  };

  // 处理高对比度设置
  const handleHighContrastToggle = (enabled: boolean) => {
    setHighContrast(enabled);
    localStorage.setItem('high-contrast', enabled.toString());
    
    // 应用CSS类
    if (enabled) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
    
    toast.success(enabled ? 
      t('settings.high_contrast_enabled', '高对比度已启用') : 
      t('settings.high_contrast_disabled', '高对比度已禁用')
    );
  };

  // 处理大字体设置
  const handleLargeTextToggle = (enabled: boolean) => {
    setLargeText(enabled);
    localStorage.setItem('large-text', enabled.toString());
    
    // 应用CSS类
    if (enabled) {
      document.documentElement.classList.add('large-text');
    } else {
      document.documentElement.classList.remove('large-text');
    }
    
    toast.success(enabled ? 
      t('settings.large_text_enabled', '大字体已启用') : 
      t('settings.large_text_disabled', '大字体已禁用')
    );
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-2">
        <Globe className="h-6 w-6" />
        <h2 className="text-2xl font-bold">
          {t('settings.system', '系统设置')}
        </h2>
        {userLevel === 'vip' && (
          <Badge variant="destructive" className="flex items-center space-x-1">
            <Crown className="h-3 w-3" />
            <span>VIP</span>
          </Badge>
        )}
      </div>

      {/* 语言设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>{t('settings.language', '语言设置')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              {t('settings.select_language', '选择语言')}
            </Label>
            <Select value={language} onValueChange={handleLanguageChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="zh">中文</SelectItem>
                <SelectItem value="en">English</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 通知设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BellRing className="h-5 w-5" />
            <span>{t('settings.notifications', '通知设置')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('settings.enable_notifications', '启用通知')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.notifications.desc', '接收应用通知和提醒')}
              </p>
            </div>
            <Switch
              checked={notifications}
              onCheckedChange={handleNotificationToggle}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('settings.sound_effects', '音效')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.sound_effects.desc', '播放操作音效')}
              </p>
            </div>
            <Switch
              checked={soundEnabled}
              onCheckedChange={handleSoundToggle}
            />
          </div>
        </CardContent>
      </Card>

      {/* 可访问性设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Accessibility className="h-5 w-5" />
            <span>{t('settings.accessibility', '可访问性')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 减少动画 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium flex items-center space-x-2">
                <EyeOff className="h-4 w-4" />
                <span>{t('settings.reduce_motion', '减少动画')}</span>
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.reduce_motion.desc', '减少界面动画效果')}
              </p>
            </div>
            <Switch
              checked={reduceMotion}
              onCheckedChange={handleReduceMotionToggle}
            />
          </div>

          <Separator />

          {/* 高对比度 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium flex items-center space-x-2">
                <Contrast className="h-4 w-4" />
                <span>{t('settings.high_contrast', '高对比度')}</span>
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.high_contrast.desc', '增强颜色对比度')}
              </p>
            </div>
            <Switch
              checked={highContrast}
              onCheckedChange={handleHighContrastToggle}
            />
          </div>

          <Separator />

          {/* 大字体 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium flex items-center space-x-2">
                <Type className="h-4 w-4" />
                <span>{t('settings.large_text', '大字体')}</span>
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.large_text.desc', '增大文字显示大小')}
              </p>
            </div>
            <Switch
              checked={largeText}
              onCheckedChange={handleLargeTextToggle}
            />
          </div>
        </CardContent>
      </Card>

      {/* 隐私设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>{t('settings.privacy', '隐私设置')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              {t('settings.privacy.desc', '您的数据安全是我们的首要任务')}
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <Button variant="outline" size="sm">
                {t('settings.privacy_policy', '隐私政策')}
              </Button>
              <Button variant="outline" size="sm">
                {t('settings.data_usage', '数据使用')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* VIP用户高级设置 */}
      {userLevel === 'vip' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Crown className="h-5 w-5" />
              <span>{t('settings.vip_system', 'VIP系统设置')}</span>
              <Badge variant="destructive">VIP</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              {t('settings.vip_system.desc', 'VIP用户专享的高级系统设置')}
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <Button variant="outline" size="sm">
                {t('settings.advanced_sync', '高级同步')}
              </Button>
              <Button variant="outline" size="sm">
                {t('settings.priority_support', '优先支持')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SystemSettings;
