// Script to test SQL initialization directly
import * as fs from 'node:fs';
import * as path from 'node:path';
import { executeSqlScript } from './server/lib/localTursoService.js';

// Path to the database file
const dbPath = path.resolve(process.cwd(), 'local.db');

// Function to initialize the database
async function initializeDatabase() {
  console.log('Initializing local SQLite database...');

  // Check if the database file exists
  const dbExists = fs.existsSync(dbPath);

  if (dbExists) {
    console.log('Removing existing database file to force fresh initialization...');
    fs.unlinkSync(dbPath);
    console.log('Database file removed.');
  }

  console.log('Database file does not exist. Creating and initializing...');

  try {
    // Execute the master.sql script to initialize the database
    const masterSqlPath = path.resolve(process.cwd(), 'public/seeds/master.sql');

    if (fs.existsSync(masterSqlPath)) {
      console.log('Found master.sql, executing...');
      const masterSql = fs.readFileSync(masterSqlPath, 'utf8');
      await executeSqlScript(masterSql);
      console.log('Database initialized successfully with master.sql');
    } else {
      console.error('master.sql not found at', masterSqlPath);
      console.log('Attempting to initialize with individual scripts...');

      // Try to initialize with individual scripts
      const scriptsDir = path.resolve(process.cwd(), 'public/seeds');

      // Execute schema scripts first
      const schemaDir = path.join(scriptsDir, 'schema');
      if (fs.existsSync(schemaDir)) {
        const schemaFiles = fs.readdirSync(schemaDir).filter((file) => file.endsWith('.sql'));
        for (const file of schemaFiles) {
          console.log(`Executing schema script: ${file}`);
          const sql = fs.readFileSync(path.join(schemaDir, file), 'utf8');
          await executeSqlScript(sql);
        }
      }

      // Then execute config scripts
      const configDir = path.join(scriptsDir, 'config');
      if (fs.existsSync(configDir)) {
        const configFiles = fs.readdirSync(configDir).filter((file) => file.endsWith('.sql'));

        // Execute create_emotion_data_sets.sql first
        console.log('Executing create_emotion_data_sets.sql...');
        const createEmotionDataSetsPath = path.join(configDir, 'create_emotion_data_sets.sql');
        if (fs.existsSync(createEmotionDataSetsPath)) {
          const sql = fs.readFileSync(createEmotionDataSetsPath, 'utf8');
          await executeSqlScript(sql);
          console.log('Successfully executed create_emotion_data_sets.sql');
        } else {
          console.error('create_emotion_data_sets.sql not found!');
        }

        // Execute populate_emotion_data_set_emotions.sql next
        console.log('Executing populate_emotion_data_set_emotions.sql...');
        const populateEmotionDataSetEmotionsPath = path.join(
          configDir,
          'populate_emotion_data_set_emotions.sql'
        );
        if (fs.existsSync(populateEmotionDataSetEmotionsPath)) {
          const sql = fs.readFileSync(populateEmotionDataSetEmotionsPath, 'utf8');
          await executeSqlScript(sql);
          console.log('Successfully executed populate_emotion_data_set_emotions.sql');
        } else {
          console.error('populate_emotion_data_set_emotions.sql not found!');
        }

        // Execute other config scripts
        for (const file of configFiles) {
          if (
            file !== 'create_emotion_data_sets.sql' &&
            file !== 'populate_emotion_data_set_emotions.sql'
          ) {
            console.log(`Executing config script: ${file}`);
            const sql = fs.readFileSync(path.join(configDir, file), 'utf8');
            await executeSqlScript(sql);
          }
        }
      }

      // Finally execute test data scripts
      const testDir = path.join(scriptsDir, 'test');
      if (fs.existsSync(testDir)) {
        const testFiles = fs.readdirSync(testDir).filter((file) => file.endsWith('.sql'));
        for (const file of testFiles) {
          console.log(`Executing test script: ${file}`);
          const sql = fs.readFileSync(path.join(testDir, file), 'utf8');
          await executeSqlScript(sql);
        }
      }

      console.log('Database initialized successfully with individual scripts');
    }
  } catch (error) {
    console.error('Failed to initialize database:', error);
    process.exit(1);
  }
}

// Run the initialization
initializeDatabase().then(() => {
  console.log('Database initialization complete. Checking if database file was created...');
  if (fs.existsSync(dbPath)) {
    console.log(`Database file was created successfully at ${dbPath}`);
  } else {
    console.error('ERROR: Database file was not created!');
  }
});
