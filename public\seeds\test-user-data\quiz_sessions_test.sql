-- Quiz会话测试数据
-- 包含情绪追踪问卷和中医体质问卷的测试会话

-- 1. 情绪追踪问卷测试会话

-- 完整的情绪路径测试会话 (Happy -> Playful -> Aroused)
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_emotion_001',
  'test_user_001',
  'mood_track_branching_v1',
  'COMPLETED',
  '2024-01-15 09:00:00',
  '2024-01-15 09:02:30',
  3,
  3,
  '{
    "session_type": "emotion_tracking",
    "completion_time_seconds": 150,
    "emotion_path": ["happy", "playful", "aroused"],
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)",
    "completion_rate": 100
  }',
  '2024-01-15 09:00:00',
  '2024-01-15 09:02:30'
);

-- 部分完成的情绪会话 (只完成了主要情绪选择)
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_emotion_002',
  'test_user_002',
  'mood_track_branching_v1',
  'IN_PROGRESS',
  '2024-01-15 10:30:00',
  NULL,
  1,
  3,
  '{
    "session_type": "emotion_tracking",
    "partial_emotion_path": ["sad"],
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    "last_activity": "2024-01-15T10:30:45Z"
  }',
  '2024-01-15 10:30:00',
  '2024-01-15 10:30:45'
);

-- 另一个完整路径 (Angry -> Frustrated -> Mad)
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_emotion_003',
  'test_user_003',
  'mood_track_branching_v1',
  'COMPLETED',
  '2024-01-15 14:15:00',
  '2024-01-15 14:17:20',
  3,
  3,
  '{
    "session_type": "emotion_tracking",
    "completion_time_seconds": 140,
    "emotion_path": ["angry", "frustrated", "mad"],
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
    "completion_rate": 100
  }',
  '2024-01-15 14:15:00',
  '2024-01-15 14:17:20'
);

-- 2. 中医体质问卷测试会话

-- 肝证素评估完整会话
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_tcm_liver_001',
  'test_user_001',
  'tcm_liver_syndrome',
  'COMPLETED',
  '2024-01-15 16:00:00',
  '2024-01-15 16:08:30',
  15,
  15,
  '{
    "session_type": "tcm_assessment",
    "syndrome_element": "liver",
    "completion_time_seconds": 510,
    "total_score": 245.5,
    "severity_level": "moderate",
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)",
    "completion_rate": 100
  }',
  '2024-01-15 16:00:00',
  '2024-01-15 16:08:30'
);

-- 肾证素评估会话
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_tcm_kidney_001',
  'test_user_002',
  'tcm_kidney_syndrome',
  'COMPLETED',
  '2024-01-15 17:30:00',
  '2024-01-15 17:37:45',
  13,
  13,
  '{
    "session_type": "tcm_assessment",
    "syndrome_element": "kidney",
    "completion_time_seconds": 465,
    "total_score": 156.8,
    "severity_level": "mild",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    "completion_rate": 100
  }',
  '2024-01-15 17:30:00',
  '2024-01-15 17:37:45'
);

-- 气虚证素评估会话 (包含负分值)
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_tcm_qi_def_001',
  'test_user_003',
  'tcm_qi_deficiency',
  'COMPLETED',
  '2024-01-15 19:00:00',
  '2024-01-15 19:05:20',
  9,
  9,
  '{
    "session_type": "tcm_assessment",
    "syndrome_element": "qi_deficiency",
    "completion_time_seconds": 320,
    "total_score": 89.3,
    "severity_level": "mild",
    "has_negative_scores": true,
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
    "completion_rate": 100
  }',
  '2024-01-15 19:00:00',
  '2024-01-15 19:05:20'
);

-- 部分完成的中医会话
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_tcm_liver_002',
  'test_user_004',
  'tcm_liver_syndrome',
  'IN_PROGRESS',
  '2024-01-15 20:15:00',
  NULL,
  8,
  15,
  '{
    "session_type": "tcm_assessment",
    "syndrome_element": "liver",
    "partial_score": 124.2,
    "answered_questions": 8,
    "user_agent": "Mozilla/5.0 (Android 14; Mobile)",
    "last_activity": "2024-01-15T20:22:15Z"
  }',
  '2024-01-15 20:15:00',
  '2024-01-15 20:22:15'
);

-- 3. 多证素综合评估会话 (用户选择了多个中医证素)
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_tcm_multi_001',
  'test_user_005',
  'tcm_comprehensive_assessment',
  'COMPLETED',
  '2024-01-16 09:00:00',
  '2024-01-16 09:25:30',
  45,
  45,
  '{
    "session_type": "tcm_comprehensive",
    "selected_syndromes": ["liver", "kidney", "qi_deficiency"],
    "completion_time_seconds": 1530,
    "syndrome_scores": {
      "liver": 245.5,
      "kidney": 156.8,
      "qi_deficiency": 89.3
    },
    "primary_constitution": "liver_qi_stagnation",
    "secondary_patterns": ["kidney_deficiency", "qi_deficiency"],
    "user_agent": "Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X)",
    "completion_rate": 100
  }',
  '2024-01-16 09:00:00',
  '2024-01-16 09:25:30'
);

-- 4. 测试用户的重复评估会话 (追踪变化)
INSERT INTO quiz_sessions (
  id, user_id, pack_id, status, start_time, end_time,
  current_question_index, total_questions, metadata, created_at, updated_at
) VALUES (
  'session_emotion_004',
  'test_user_001',
  'mood_track_branching_v1',
  'COMPLETED',
  '2024-01-16 14:30:00',
  '2024-01-16 14:32:15',
  3,
  3,
  '{
    "session_type": "emotion_tracking",
    "completion_time_seconds": 135,
    "emotion_path": ["happy", "content", "joyful"],
    "previous_session": "session_emotion_001",
    "emotion_change": "same_primary_different_path",
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)",
    "completion_rate": 100
  }',
  '2024-01-16 14:30:00',
  '2024-01-16 14:32:15'
);
