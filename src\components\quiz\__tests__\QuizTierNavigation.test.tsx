/**
 * QuizTierNavigation组件测试
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import QuizTierNavigation from '../QuizTierNavigation';
import { QuizQuestion, QuizQuestionOption } from '@/types/schema/base';

// Mock dependencies
jest.mock('@/contexts/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => key
  })
}));

jest.mock('@/contexts/UserConfigContext', () => ({
  useUserConfig: () => ({
    userConfig: {
      id: 'test-user',
      preferred_view_type: 'wheel'
    }
  })
}));

jest.mock('@capacitor/haptics', () => ({
  Haptics: {
    impact: jest.fn()
  },
  ImpactStyle: {
    Medium: 'medium'
  }
}));

// Mock子组件
jest.mock('../core/QuizComponentRenderer', () => ({
  QuizComponentRenderer: ({ config, onSelect }: any) => (
    <div data-testid="quiz-component-renderer">
      <h3>{config.question_text}</h3>
      {config.options?.map((option: any) => (
        <button
          key={option.id}
          onClick={() => onSelect(option)}
          data-testid={`option-${option.id}`}
        >
          {option.option_text}
        </button>
      ))}
    </div>
  )
}));

jest.mock('../special-views/EmotionWheelView', () => ({
  EmotionWheelView: ({ emotions, onSelect }: any) => (
    <div data-testid="emotion-wheel-view">
      {emotions.map((emotion: any) => (
        <button
          key={emotion.id}
          onClick={() => onSelect(emotion)}
          data-testid={`emotion-${emotion.id}`}
        >
          {emotion.name.zh || emotion.name}
        </button>
      ))}
    </div>
  )
}));

describe('QuizTierNavigation', () => {
  const mockOnSelect = jest.fn();
  const mockOnBack = jest.fn();

  const mockEmotionWheelQuestion: QuizQuestion & { options: QuizQuestionOption[] } = {
    id: 'question-1',
    pack_id: 'pack-1',
    question_text: '请选择您的主要情绪',
    question_text_localized: null,
    question_type: 'emotion_wheel',
    question_order: 1,
    question_group: null,
    tier_level: 1,
    question_config: null,
    validation_rules: null,
    scoring_config: null,
    parent_question_id: null,
    dependency_rules: null,
    is_required: true,
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
    created_by: null,
    updated_by: null,
    options: [
      {
        id: 'option-1',
        question_id: 'question-1',
        option_text: '快乐',
        option_text_localized: null,
        option_value: 'happy',
        option_order: 1,
        metadata: JSON.stringify({ emoji: '😊', color: '#FFD700' }),
        scoring_value: null,
        is_correct: null,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: null,
        updated_by: null
      },
      {
        id: 'option-2',
        question_id: 'question-1',
        option_text: '悲伤',
        option_text_localized: null,
        option_value: 'sad',
        option_order: 2,
        metadata: JSON.stringify({ emoji: '😢', color: '#4169E1' }),
        scoring_value: null,
        is_correct: null,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: null,
        updated_by: null
      }
    ]
  };

  const mockSingleChoiceQuestion: QuizQuestion & { options: QuizQuestionOption[] } = {
    ...mockEmotionWheelQuestion,
    id: 'question-2',
    question_text: '请选择一个选项',
    question_type: 'single_choice',
    tier_level: 1
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render emotion wheel for emotion_wheel question type', () => {
    render(
      <QuizTierNavigation
        currentQuestion={mockEmotionWheelQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByTestId('emotion-wheel-view')).toBeInTheDocument();
    expect(screen.getByText('请选择您的主要情绪')).toBeInTheDocument();
  });

  it('should render quiz component renderer for single_choice question type', () => {
    render(
      <QuizTierNavigation
        currentQuestion={mockSingleChoiceQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByTestId('quiz-component-renderer')).toBeInTheDocument();
    expect(screen.getByText('请选择一个选项')).toBeInTheDocument();
  });

  it('should display progress indicator', () => {
    render(
      <QuizTierNavigation
        currentQuestion={mockEmotionWheelQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByText('quiz.tier_level 1')).toBeInTheDocument();
    expect(screen.getByText('请选择您的主要情绪')).toBeInTheDocument();
  });

  it('should handle option selection for emotion wheel', async () => {
    render(
      <QuizTierNavigation
        currentQuestion={mockEmotionWheelQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
      />
    );

    const happyButton = screen.getByTestId('emotion-option-1');
    fireEvent.click(happyButton);

    await waitFor(() => {
      expect(mockOnSelect).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'option-1',
          option_text: '快乐',
          option_value: 'happy'
        })
      );
    });
  });

  it('should handle option selection for single choice', async () => {
    render(
      <QuizTierNavigation
        currentQuestion={mockSingleChoiceQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
      />
    );

    const optionButton = screen.getByTestId('option-option-1');
    fireEvent.click(optionButton);

    await waitFor(() => {
      expect(mockOnSelect).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'option-1',
          option_text: '快乐',
          option_value: 'happy'
        })
      );
    });
  });

  it('should filter options based on selected path for higher tier levels', () => {
    const secondTierQuestion: QuizQuestion & { options: QuizQuestionOption[] } = {
      ...mockEmotionWheelQuestion,
      id: 'question-2',
      question_text: '请选择具体的快乐情绪',
      tier_level: 2,
      options: [
        {
          ...mockEmotionWheelQuestion.options[0],
          id: 'option-3',
          option_text: '兴奋',
          option_value: 'excited',
          metadata: JSON.stringify({ 
            emoji: '🤩', 
            color: '#FF6347',
            parent_option_id: 'option-1'
          })
        },
        {
          ...mockEmotionWheelQuestion.options[0],
          id: 'option-4',
          option_text: '平静',
          option_value: 'calm',
          metadata: JSON.stringify({ 
            emoji: '😌', 
            color: '#98FB98',
            parent_option_id: 'option-2'
          })
        }
      ]
    };

    const selectedPath = {
      'question-1': mockEmotionWheelQuestion.options[0] // 选择了"快乐"
    };

    render(
      <QuizTierNavigation
        currentQuestion={secondTierQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
        selectedPath={selectedPath}
      />
    );

    // 应该只显示与"快乐"相关的子选项
    expect(screen.getByTestId('emotion-wheel-view')).toBeInTheDocument();
  });

  it('should handle back navigation', () => {
    render(
      <QuizTierNavigation
        currentQuestion={mockEmotionWheelQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
      />
    );

    // 这里需要根据实际的back按钮实现来测试
    // 由于我们mock了子组件，这个测试需要在实际集成测试中验证
  });

  it('should extract emoji from option metadata', () => {
    render(
      <QuizTierNavigation
        currentQuestion={mockEmotionWheelQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
      />
    );

    // 验证emoji是否正确提取和显示
    expect(screen.getByTestId('emotion-wheel-view')).toBeInTheDocument();
  });

  it('should handle invalid metadata gracefully', () => {
    const questionWithInvalidMetadata: QuizQuestion & { options: QuizQuestionOption[] } = {
      ...mockEmotionWheelQuestion,
      options: [
        {
          ...mockEmotionWheelQuestion.options[0],
          metadata: 'invalid-json'
        }
      ]
    };

    expect(() => {
      render(
        <QuizTierNavigation
          currentQuestion={questionWithInvalidMetadata}
          onSelect={mockOnSelect}
          onBack={mockOnBack}
        />
      );
    }).not.toThrow();
  });

  it('should render default question component for unknown question types', () => {
    const unknownTypeQuestion: QuizQuestion & { options: QuizQuestionOption[] } = {
      ...mockEmotionWheelQuestion,
      question_type: 'unknown_type' as any
    };

    render(
      <QuizTierNavigation
        currentQuestion={unknownTypeQuestion}
        onSelect={mockOnSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByText('请选择您的主要情绪')).toBeInTheDocument();
  });
});
