# Dependencies
node_modules
.pnp
.pnp.js

# Build output
dist
build
.wrangler
.dev.vars

# Ignore compiled JavaScript files (TypeScript outputs)
lib/**/*.js
functions/**/*.js
*.js
!jest.config.js
!.eslintrc.js
!local-server.js

# Local database
*.db
*.sqlite
*.sqlite3

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
