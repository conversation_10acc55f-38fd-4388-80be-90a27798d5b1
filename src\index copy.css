/* styles.css or your main CSS file */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* ----------------------------- */
    /* Core Palette & Fonts         */
    /* ----------------------------- */
    --font-primary: "Avenir Next", "Nunito Sans", "Helvetica Neue", sans-serif; /* 简洁亲和 */
    --font-rounded-gothic: "Rounded Gothic", var(--font-primary); /* 备选，如引入 */

    --border-radius-sm: 0.375rem; /* 6px */
    --border-radius-md: 0.75rem; /* 12px */
    --border-radius-lg: 1.25rem; /* 20px */
    --border-radius-full: 9999px;

    /* ----------------------------- */
    /* Light Theme Variables        */
    /* ----------------------------- */
    --bg-app-light: 220 60% 97%; /* 非常浅的、柔和的蓝白背景 */
    --text-primary-light: 220 25% 25%; /* 深灰蓝文字 */
    --text-secondary-light: 220 20% 45%; /* 次要文字 */
    --text-on-accent-light: 0 0% 100%; /* 强调色上的文字 (通常为白色) */

    /* Frosted Glass Base - Light Theme */
    --glass-bg-light: 220 50% 100% / 0.4; /* 白色基底，带透明度和轻微冷调 */
    --glass-border-light: 220 50% 100% / 0.6; /* 玻璃边框，更不透明一些 */
    --glass-shadow-light: 0 10px 30px -10px hsl(220 50% 50% / 0.15);

    /* Accent Colors (can be overridden by emotion) */
    --accent-primary-light: 250 70% 60%; /* 柔和的紫色/蓝色作为默认强调 */
    --accent-secondary-light: 200 70% 65%; /* 柔和的青色 */

    /* Emotion Specific Backgrounds (Light Theme) - 主背景色 */
    --emotion-sad-bg-light: 210 70% 90%;
    --emotion-angry-bg-light: 0 80% 90%;
    --emotion-happy-bg-light: 45 100% 90%;
    --emotion-fear-bg-light: 270 60% 92%;
    --emotion-surprise-bg-light: 300 80% 92%;
    --emotion-disgust-bg-light: 120 40% 90%;
    --emotion-calm-bg-light: 180 50% 95%; /* 平静湖 */
    --emotion-hope-bg-light: 90 70% 93%; /* 希望花园 */

    /* ----------------------------- */
    /* Dark Theme Variables         */
    /* ----------------------------- */
    --bg-app-dark: 220 25% 12%; /* 深邃的蓝黑背景 */
    --text-primary-dark: 220 30% 90%; /* 浅灰白文字 */
    --text-secondary-dark: 220 20% 70%; /* 次要文字 */
    --text-on-accent-dark: 0 0% 100%; /* 强调色上的文字 */

    /* Frosted Glass Base - Dark Theme */
    --glass-bg-dark: 220 20% 25% / 0.35; /* 深色玻璃基底，带透明度 */
    --glass-border-dark: 220 20% 35% / 0.5; /* 玻璃边框 */
    --glass-shadow-dark: 0 10px 30px -10px hsl(220 30% 5% / 0.3);

    /* Accent Colors (can be overridden by emotion) */
    --accent-primary-dark: 250 60% 65%;
    --accent-secondary-dark: 200 60% 70%;

    /* Emotion Specific Backgrounds (Dark Theme) */
    --emotion-sad-bg-dark: 210 40% 20%;
    --emotion-angry-bg-dark: 0 50% 20%;
    --emotion-happy-bg-dark: 45 60% 20%;
    --emotion-fear-bg-dark: 270 30% 22%;
    --emotion-surprise-bg-dark: 300 40% 22%;
    --emotion-disgust-bg-dark: 120 25% 20%;
    --emotion-calm-bg-dark: 180 30% 15%;
    --emotion-hope-bg-dark: 90 40% 18%;

    /* Assign light theme by default */
    --bg-app: var(--bg-app-light);
    --text-primary: var(--text-primary-light);
    --text-secondary: var(--text-secondary-light);
    --text-on-accent: var(--text-on-accent-light);
    --glass-bg: var(--glass-bg-light);
    --glass-border: var(--glass-border-light);
    --glass-shadow: var(--glass-shadow-light);
    --accent-primary: var(--accent-primary-light);
    --accent-secondary: var(--accent-secondary-light);
  }

  .dark {
    --bg-app: var(--bg-app-dark);
    --text-primary: var(--text-primary-dark);
    --text-secondary: var(--text-secondary-dark);
    --text-on-accent: var(--text-on-accent-dark);
    --glass-bg: var(--glass-bg-dark);
    --glass-border: var(--glass-border-dark);
    --glass-shadow: var(--glass-shadow-dark);
    --accent-primary: var(--accent-primary-dark);
    --accent-secondary: var(--accent-secondary-dark);
  }

  /* Emotion theme classes - to be applied to <body> or a main wrapper */
  .theme-sad {
    --bg-app: var(--emotion-sad-bg-light);
  }
  .dark .theme-sad {
    --bg-app: var(--emotion-sad-bg-dark);
  }
  .theme-angry {
    --bg-app: var(--emotion-angry-bg-light);
  }
  .dark .theme-angry {
    --bg-app: var(--emotion-angry-bg-dark);
  }
  .theme-happy {
    --bg-app: var(--emotion-happy-bg-light);
  }
  .dark .theme-happy {
    --bg-app: var(--emotion-happy-bg-dark);
  }
  /* ... add other emotion themes ... */
  .theme-calm {
    --bg-app: var(--emotion-calm-bg-light);
  }
  .dark .theme-calm {
    --bg-app: var(--emotion-calm-bg-dark);
  }

  body {
    @apply bg-app text-primary transition-colors duration-500 ease-in-out;
    font-family: var(--font-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Global style for links or interactive text elements */
  a,
  .interactive-text {
    @apply text-accent-primary hover:opacity-80 transition-opacity;
  }
}

@layer components {
  /* ----------------------------- */
  /* Frosted Glass Pane Component  */
  /* ----------------------------- */
  .glass-pane {
    background-color: hsl(var(--glass-bg));
    border: 1px solid hsl(var(--glass-border));
    box-shadow: var(--glass-shadow);
    backdrop-filter: blur(16px) saturate(180%); /* 毛玻璃核心：模糊 + 轻微饱和度提升 */
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    @apply rounded-lg transition-all duration-300;
  }

  /* Variations */
  .glass-pane-sm-radius {
    @apply rounded-md;
  }
  .glass-pane-lg-radius {
    @apply rounded-xl;
  } /* e.g. for modals */
  .glass-pane-full-radius {
    @apply rounded-full;
  } /* e.g. for circular buttons */

  /* ----------------------------- */
  /* Buttons                       */
  /* ----------------------------- */
  .btn {
    @apply px-6 py-3 rounded-md font-semibold transition-all duration-200 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent;
  }
  .btn-primary {
    @apply bg-accent-primary text-text-on-accent hover:opacity-90 focus:ring-[hsl(var(--accent-primary))];
    /* Breathing light effect for primary CTAs */
  }
  .btn-primary.breathing {
    animation: breathing-glow 2.5s infinite ease-in-out;
  }
  .btn-secondary {
    @apply glass-pane text-primary border-[hsl(var(--accent-primary)/0.5)] hover:bg-[hsl(var(--accent-primary)/0.1)] focus:ring-[hsl(var(--accent-primary))];
  }
  .btn-subtle {
    @apply text-secondary hover:text-primary;
  }
  .btn-icon {
    @apply p-2 glass-pane-sm-radius glass-pane hover:bg-[hsl(var(--glass-bg)/0.5)];
    /* Ensure icon color contrasts */
  }

  /* ----------------------------- */
  /* Cards (Diary, Info etc.)    */
  /* ----------------------------- */
  .content-card {
    @apply glass-pane p-4 md:p-6 flex flex-col gap-3;
  }

  /* ----------------------------- */
  /* Modals                        */
  /* ----------------------------- */
  .modal-overlay {
    @apply fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center p-4 z-40;
    /* backdrop-blur-sm for overlay itself, different from pane */
  }
  .modal-content {
    @apply glass-pane glass-pane-lg-radius p-6 md:p-8 max-w-md w-full relative;
    /* Add custom shadow for modals if needed to pop more */
  }

  /* ----------------------------- */
  /* MoodWheel Specific Components */
  /* ----------------------------- */
  .moodwheel-main-container {
    @apply relative flex items-center justify-center w-[300px] h-[300px] md:w-[400px] md:h-[400px] mx-auto my-8;
    /* Adjust size as needed */
  }

  .moodwheel-layer {
    @apply absolute inset-0 rounded-full transition-transform duration-500 ease-out;
    /* transform-origin: center center; */
  }
  /* Example: a layer could be a glass pane itself if it's the "base" */
  /* .moodwheel-layer.is-glass { @apply glass-pane rounded-full; } */

  .moodwheel-segment {
    @apply absolute cursor-pointer flex items-center justify-center text-center p-2
           transition-all duration-300 ease-in-out;
    /* Clip-path will be used to create扇形. This needs JS to calculate. */
    /* Example hover/active state */
  }
  .moodwheel-segment:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }
  .moodwheel-segment.active {
    /* Style for active segment */
    box-shadow: 0 0 15px 2px hsl(var(--accent-primary) / 0.7);
  }
  .moodwheel-segment-text {
    @apply text-sm md:text-base font-medium text-primary pointer-events-none;
    /* Text color might need to adapt to segment color for contrast */
  }

  /* Center path display */
  .moodwheel-path-display {
    @apply glass-pane glass-pane-sm-radius p-3 text-center text-sm text-secondary
           absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10
           min-w-[150px] max-w-[250px]; /* Example for center "Sad -> Lonely" */
  }

  /* ----------------------------- */
  /* Forms                         */
  /* ----------------------------- */
  .form-input,
  .form-textarea {
    @apply glass-pane w-full p-3 rounded-md border-[hsl(var(--glass-border)/0.7)]
           text-primary placeholder-text-secondary/70
           focus:border-[hsl(var(--accent-primary))] focus:ring-1 focus:ring-[hsl(var(--accent-primary))];
  }
  .form-label {
    @apply block text-sm font-medium text-secondary mb-1;
  }
  .rating-stars {
    /* For 0-10 intensity */
    @apply flex gap-1;
    /* Individual star would be an SVG icon */
  }
  .rating-stars .star.active svg {
    fill: hsl(var(--accent-secondary)); /* Or a gold/yellow color */
  }

  /* ----------------------------- */
  /* Navigation Bar (Bottom)     */
  /* ----------------------------- */
  .bottom-navbar {
    @apply fixed bottom-0 left-0 right-0 glass-pane rounded-t-lg md:rounded-t-xl
           flex justify-around items-center p-3 shadow-[0_-5px_20px_-5px_hsl(var(--glass-shadow)/0.7)] z-30;
    /* Overwrite default rounded-b from glass-pane */
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .nav-item {
    @apply flex flex-col items-center text-xs text-secondary hover:text-primary transition-colors;
    /* Icon + Text */
  }
  .nav-item.active {
    @apply text-accent-primary;
  }
  .nav-item svg {
    @apply w-6 h-6 mb-0.5; /* Adjust icon size */
  }

  /* ----------------------------- */
  /* History/Graph View           */
  /* ----------------------------- */
  .heatmap-cell {
    /* For calendar/heatmap */
    @apply w-4 h-4 rounded-sm transition-colors;
    /* Background color determined by JS based on intensity */
  }
  .heatmap-cell:hover {
    @apply ring-2 ring-accent-primary ring-offset-1 ring-offset-[hsl(var(--bg-app))];
  }

  /* ----------------------------- */
  /* Animations                    */
  /* ----------------------------- */
  @keyframes breathing-glow {
    0%,
    100% {
      box-shadow: 0 0 5px 0px hsl(var(--accent-primary) / 0.5);
    }
    50% {
      box-shadow: 0 0 20px 5px hsl(var(--accent-primary) / 0.3);
    }
  }

  /* Gentle expansion for moodwheel layers (example) */
  .layer-enter {
    opacity: 0;
    transform: scale(0.8);
  }
  .layer-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: opacity 300ms, transform 300ms;
  }
  .layer-exit {
    opacity: 1;
    transform: scale(1);
  }
  .layer-exit-active {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 300ms, transform 300ms;
  }
}
