# 视图系统导入更新计划

本文档列出了需要更新导入语句的文件，以便从旧的视图系统迁移到新的视图系统。

## 更新原则

1. **渐进式更新**：按照依赖关系，从最底层的组件开始更新，逐步向上
2. **保持向后兼容**：在更新过程中，确保不破坏现有功能
3. **完整测试**：每次更新后，运行测试确保系统正常工作

## 需要更新的文件

### 第一阶段：轮盘组件导入更新

| 文件路径 | 旧导入 | 新导入 | 状态 |
|---------|-------|-------|------|
| `src/utils/wheelFactory.ts` | `import { BaseWheel, ... } from '../components/mood/BaseWheel';` | 移除 | 待更新 |
| `src/utils/wheelFactory.ts` | `import { D3Wheel } from '../components/mood/D3Wheel';` | 移除 | 待更新 |
| `src/utils/wheelFactory.ts` | `import { SVGWheel } from '../components/mood/SVGWheel';` | 移除 | 待更新 |
| `src/utils/wheelFactory.ts` | `import { R3FWheel } from '../components/mood/R3FWheel';` | 移除 | 待更新 |
| `src/components/wheels/D3WheelComponent.tsx` | `import { D3Wheel } from '@/components/mood/D3Wheel';` | 移除，使用 D3WheelDirectComponent | 待更新 |
| `src/components/wheels/SVGWheelComponent.tsx` | `import { SVGWheel } from '@/components/mood/SVGWheel';` | 移除，使用 SVGWheelDirectComponent | 待更新 |
| `src/components/wheels/R3FWheelComponent.tsx` | `import { R3FWheel } from '@/components/mood/R3FWheel';` | 移除，使用 R3FWheelDirectComponent | 待更新 |
| `src/components/wheels/WheelComponentTest.tsx` | `import { D3WheelComponent } from './D3WheelComponent';` | `import { D3WheelDirectComponent } from './D3WheelDirectComponent';` | 已完成 |
| `src/components/wheels/WheelComponentTest.tsx` | `import { SVGWheelComponent } from './SVGWheelComponent';` | `import { SVGWheelDirectComponent } from './SVGWheelDirectComponent';` | 已完成 |
| `src/components/wheels/WheelComponentTest.tsx` | `import { R3FWheelComponent } from './R3FWheelComponent';` | `import { R3FWheelDirectComponent } from './R3FWheelDirectComponent';` | 已完成 |

### 第二阶段：适配器组件导入更新

| 文件路径 | 旧导入 | 新导入 | 状态 |
|---------|-------|-------|------|
| `src/components/mood/TierNavigation.tsx` | `import EmotionWheel from './EmotionWheel';` | 已移除 | 已完成 |
| `src/components/mood/TierNavigation.tsx` | `import WheelAdapter from './WheelAdapter';` | 已移除 | 已完成 |

### 第三阶段：视图组件导入更新

| 文件路径 | 旧导入 | 新导入 | 状态 |
|---------|-------|-------|------|
| `src/pages/r3f-wheel-test.tsx` | `import { R3FWheelComponent } from '@/components/wheels/R3FWheelComponent';` | `import { R3FWheelDirectComponent } from '@/components/wheels/R3FWheelDirectComponent';` | 已完成 |

## 更新步骤

### 步骤 1: 更新 WheelComponentTest.tsx

```typescript
// 旧导入
import { D3WheelComponent } from './D3WheelComponent';
import { SVGWheelComponent } from './SVGWheelComponent';
import { R3FWheelComponent } from './R3FWheelComponent';

// 新导入
import { D3WheelDirectComponent } from './D3WheelDirectComponent';
import { SVGWheelDirectComponent } from './SVGWheelDirectComponent';
import { R3FWheelDirectComponent } from './R3FWheelDirectComponent';
```

### 步骤 2: 更新 r3f-wheel-test.tsx

```typescript
// 旧导入
import { R3FWheelComponent } from '@/components/wheels/R3FWheelComponent';

// 新导入
import { R3FWheelDirectComponent } from '@/components/wheels/R3FWheelDirectComponent';
```

### 步骤 3: 移除 wheelFactory.ts 中的旧导入

```typescript
// 移除这些导入
import {
  BaseWheel,
  DefaultContentStrategy,
  TextOnlyStrategy,
  EmojiOnlyStrategy,
  TextEmojiStrategy,
  AnimatedEmojiStrategy
} from '../components/mood/BaseWheel';
import { D3Wheel } from '../components/mood/D3Wheel';
import { SVGWheel } from '../components/mood/SVGWheel';
import { R3FWheel } from '../components/mood/R3FWheel';
```

### 步骤 4: 移除 D3WheelComponent.tsx 中的旧导入

```typescript
// 移除这些导入
import { D3Wheel } from '@/components/mood/D3Wheel';
import {
  TextOnlyStrategy,
  EmojiOnlyStrategy,
  TextEmojiStrategy
} from '@/components/mood/BaseWheel';
```

### 步骤 5: 移除 SVGWheelComponent.tsx 中的旧导入

```typescript
// 移除这些导入
import { SVGWheel } from '@/components/mood/SVGWheel';
import {
  TextOnlyStrategy,
  EmojiOnlyStrategy,
  TextEmojiStrategy
} from '@/components/mood/BaseWheel';
```

### 步骤 6: 移除 R3FWheelComponent.tsx 中的旧导入

```typescript
// 移除这些导入
import { R3FWheel } from '@/components/mood/R3FWheel';
import {
  TextOnlyStrategy,
  EmojiOnlyStrategy,
  TextEmojiStrategy
} from '@/components/mood/BaseWheel';
```

## 测试计划

每次更新导入语句后，需要运行以下测试：

1. **单元测试**：运行 `npm test` 确保所有测试通过
2. **组件测试**：运行 `npm run test:components` 确保组件测试通过
3. **集成测试**：运行 `npm run test:integration` 确保集成测试通过
4. **手动测试**：在开发环境中测试相关功能，确保正常工作

## 风险和缓解措施

1. **依赖关系**
   - 风险：可能存在未知的依赖关系
   - 缓解：使用代码分析工具，识别所有依赖关系

2. **功能回归**
   - 风险：更新导入语句可能导致功能回归
   - 缓解：增加测试覆盖率，确保所有功能正常工作

## 结论

通过执行这个导入更新计划，我们将逐步从旧的视图系统迁移到新的视图系统，确保代码库的一致性和可维护性。更新完成后，我们可以开始移除旧的组件和文件，完成视图系统的迁移。
