import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';
import type { EmotionDataSet } from '@/types/emotionDataTypes';
import { Edit, Plus } from 'lucide-react';
import type React from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

interface EmotionDataCardProps {
  emotionData: EmotionDataSet;
  isActive: boolean;
  onSelect: (emotionData: EmotionDataSet) => void;
  onEdit: (emotionData: EmotionDataSet) => void;
}

/**
 * 情绪数据卡片组件
 */
const EmotionDataCard: React.FC<EmotionDataCardProps> = ({
  emotionData,
  isActive,
  onSelect,
  onEdit,
}) => {
  const { t } = useLanguage();

  // 计算情绪总数 - 确保是数字类型
  const totalEmotions =
    typeof emotionData.emotions_count === 'number'
      ? emotionData.emotions_count
      : emotionData.emotions_count
        ? Number(emotionData.emotions_count)
        : 0;

  return (
    <Card
      className={`overflow-hidden transition-all duration-300 ${
        isActive ? 'border-primary shadow-lg' : 'border-border'
      } hover:shadow-md w-full`}
    >
      <div className="flex justify-between items-center p-4">
        <div className="flex-grow">
          <div className="flex items-center mb-1">
            <h3 className="font-medium text-lg mr-2">{emotionData.name}</h3>
            {isActive && (
              <Badge variant="outline" className="bg-primary/10 text-primary">
                {t('settings.emotion_data.active', { defaultValue: '当前使用' })}
              </Badge>
            )}
          </div>

          <p className="text-sm text-muted-foreground mb-2">
            {emotionData.description ||
              t('settings.emotion_data.no_description', { defaultValue: '无描述' })}
          </p>

          <div className="flex gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">
                {t('settings.emotion_data.tiers', { defaultValue: '层级' })}:
              </span>
              <span className="ml-1 font-medium">
                {Array.isArray(emotionData.tiers) ? emotionData.tiers.length : 0}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">
                {t('settings.emotion_data.emotions', { defaultValue: '情绪' })}:
              </span>
              <span className="ml-1 font-medium">{totalEmotions}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2 ml-4">
          <Button
            variant={isActive ? 'secondary' : 'default'}
            onClick={() => onSelect(emotionData)}
          >
            {isActive
              ? t('settings.emotion_data.current', { defaultValue: '当前' })
              : t('settings.emotion_data.use', { defaultValue: '使用' })}
          </Button>

          <Button variant="outline" size="icon" onClick={() => onEdit(emotionData)}>
            <Edit className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
};

interface EmotionDataSelectionComponentProps {
  emotionDataList: EmotionDataSet[];
  activeEmotionDataId: string;
  onSelect: (emotionDataId: string) => void;
}

/**
 * 情绪数据选择组件
 * 用于在设置页面中选择情绪数据
 */
const EmotionDataSelectionComponent: React.FC<EmotionDataSelectionComponentProps> = ({
  emotionDataList,
  activeEmotionDataId,
  onSelect,
}) => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  // 处理情绪数据选择
  const handleEmotionDataSelect = (emotionData: EmotionDataSet) => {
    onSelect(emotionData.id);
    toast.success(t('settings.emotion_data_changed', { defaultValue: '情绪数据已更改' }), {
      duration: 3000,
    });
  };

  // 处理编辑情绪数据
  const handleEditEmotionData = (emotionData: EmotionDataSet) => {
    // 导航到情绪数据编辑页面
    navigate(`/emotion-data-editor/${emotionData.id}`);
  };

  // 处理创建基于模板的情绪数据
  const handleCreateEmotionData = () => {
    // 获取默认情绪数据作为模板
    const defaultData = emotionDataList.find((data) => data.is_default) || emotionDataList[0];
    if (defaultData) {
      // 导航到情绪数据编辑页面，并传递模板ID
      navigate(`/emotion-data-editor/${defaultData.id}?template=true`);
    } else {
      // 如果没有可用的模板，则从零开始创建
      navigate('/emotion-data-editor');
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-3 mb-4">
        {emotionDataList.map((emotionData) => (
          <EmotionDataCard
            key={emotionData.id}
            emotionData={emotionData}
            isActive={activeEmotionDataId === emotionData.id}
            onSelect={handleEmotionDataSelect}
            onEdit={handleEditEmotionData}
          />
        ))}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
        <Button
          variant="outline"
          className="w-full flex items-center justify-center"
          onClick={handleCreateEmotionData}
        >
          <Plus className="h-4 w-4 mr-2" />
          {t('settings.emotion_data.create', { defaultValue: '基于模板创建' })}
        </Button>

        <Button
          variant="default"
          className="w-full flex items-center justify-center"
          onClick={() => navigate('/emotion-data-editor')}
        >
          <Plus className="h-4 w-4 mr-2" />
          {t('settings.emotion_data.create_from_scratch', { defaultValue: '从零开始创建' })}
        </Button>
      </div>
    </div>
  );
};

export default EmotionDataSelectionComponent;
