使用capacitor开发移动app而不是网站:
使用LanguageContext搭配配置中语言切换，useMockQuery根据页面请求hooks时传输的语言，读取不同语言对应文件夹下的mock数据
# Mood Tracking App PRD

## Overview
A mobile-friendly web application that helps users track, analyze, and understand their emotional wellbeing through an intuitive interface and comprehensive mood logging system.

### Target Users
- Individuals interested in emotional self-awareness
- People practicing mindfulness and mental health tracking
- Users looking for patterns in their emotional states
- Anyone wanting to improve their emotional intelligence

## Core Features

### 1. Mood Logging System

#### 1.1 Three-Tier Emotion Selection
- **Primary Emotions:** 6 base emotions (Happy, Love, Calm, Sad, Fear, Angry)
- **Secondary Emotions:** More specific emotional states for each primary emotion
- **Tertiary Emotions:** Highly specific emotional nuances
- Interactive wheel interface for each tier
- Auto-progression between tiers after selection
- Visual breadcrumb trail showing selected emotions

#### 1.2 Mood Entry Details
- Intensity slider (1-100%)
- Customizable tags
- Optional reflection/notes field
- Timestamp and date tracking
- Tag suggestions based on common contexts

### 2. History Tracking

#### 2.1 Calendar View
- Monthly overview
- Color-coded mood indicators
- Daily entry markers
- Interactive day selection

#### 2.2 Timeline View
- Chronological mood entries
- Grouped by date
- Detailed entry display including:
  - Emotion
  - Intensity
  - Tags
  - Reflections
  - Time stamps

#### 2.3 Entry Filtering
- Filter by emotion
- Time range selection
- Sort options (newest/oldest)
- Tag-based filtering

### 3. Analytics & Insights

#### 3.1 Overview Statistics
- Total entries tracked
- Most common emotions
- Average mood ratings
- Week-over-week changes
- Month-over-month trends

#### 3.2 Pattern Analysis
- Time-of-day patterns
- Day-of-week trends
- Tag correlation analysis
- Mood distribution charts

#### 3.3 Streak Tracking
- Current tracking streak
- All-time best streak
- Daily logging motivation

### 4. Settings & Customization

#### 4.1 Preferences
- Daily reminders
- Dark mode option
- Weekly summary preferences
- Notification settings

#### 4.2 Data Management
- Export functionality
- Backup options
- Data privacy controls
- History clearing option

## Technical Requirements

### UI/UX Requirements
- Mobile-first responsive design
- Touch-friendly interface
- Smooth transitions and animations
- Intuitive navigation
- Accessible color schemes
- Loading states and feedback

### Performance Requirements
- Fast load times (<2s initial load)
- Smooth animations (60fps)
- Offline capabilities
- Efficient data storage
- Responsive interactions

### Browser Support
- Modern browsers (last 2 versions)
- iOS Safari 14+
- Android Chrome 90+
- Mobile-optimized experience

## Data Structure

### Mood Entry
```typescript
interface MoodEntry {
  id: string;
  timestamp: Date;
  emotions: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  intensity: number;
  tags: string[];
  reflection: string;
}

