# 完整服务设计文档

基于客户端-服务端混合架构的完整服务设计规范，定义了离线优先、在线同步的现代应用架构。

## 📋 **架构概览**

### 🎯 **设计原则**

1. **离线优先**: 核心功能完全离线可用
2. **智能同步**: 基于优先级的数据同步策略
3. **职责分离**: 客户端负责业务逻辑，服务端负责数据中心
4. **类型安全**: 端到端的 TypeScript 类型系统
5. **类型统一**: 所有服务使用 `src/types/schema/index.ts` 统一类型定义
6. **渐进增强**: 在线功能作为离线功能的增强

### 📋 **类型系统架构**

#### **统一类型引用规范**
```typescript
// ✅ 正确的类型引用方式
import type {
  // 基础类型
  ServiceResult,
  DatabaseContext,

  // 用户相关
  User,
  UserCreate,
  UserUpdate,
  AuthState,

  // VIP相关
  VipPlan,
  VipPlanCreate,
  VipPlanUpdate,
  VipStatus,
  VipSubscription,

  // Quiz相关
  QuizPack,
  QuizQuestion,
  QuizQuestionOption,
  QuizSession,
  QuizAnswer,
  QuizResult,

  // 解锁相关
  UserUnlock,
  UserUnlockCreate,
  UserUnlockUpdate,

  // 同步相关
  SyncResult,
  SyncStatus,
  SyncConflict,
  ConflictResolution,

  // 支付相关
  PaymentTransaction,
  PurchaseResult,

  // 配置相关
  UserConfig,
  UserPresentationConfig,
  PackPresentationOverride
} from '@/types/schema/index.js';

// ❌ 错误的做法 - 不要自定义重复类型
// interface CustomUser { ... }
// interface MyVipPlan { ... }
```

#### **类型定义层次结构**
```typescript
// src/types/schema/index.ts - 统一导出入口
export * from './api.js';           // API相关类型
export * from './base.js';          // 基础数据类型
export * from './translation.js';   // 多语言类型
export * from './sync.js';          // 同步相关类型
export * from './payment.js';       // 支付相关类型

// 类型验证Schema (基于Zod)
export * from './validation.js';    // 运行时验证Schema
```

#### **服务层类型使用规范**
```typescript
// 客户端服务类型使用
// src/services/entities/QuizEngineV3.ts
import type {
  QuizPack,
  QuizSession,
  QuizQuestion,
  QuizQuestionOption,
  QuizAnswer,
  QuizResult,
  ServiceResult
} from '@/types/schema/index.js';

export class QuizEngineV3 extends BaseService<QuizSession, QuizSessionCreate, QuizSessionUpdate> {
  async createQuizSession(
    packId: string,
    userId: string,
    sessionType: string = 'standard'
  ): Promise<ServiceResult<QuizSession>> {
    // 使用统一类型定义
  }
}

// 服务端服务类型使用
// server/lib/services/QuizEngineService.ts
import type {
  QuizPack,
  QuizSession,
  QuizResult,
  ServiceResult
} from '../../../src/types/schema/index.js';

export class ServerQuizEngineService {
  async validateQuizSession(
    sessionData: QuizSession
  ): Promise<ServiceResult<boolean>> {
    // 使用统一类型定义
  }
}
```

### 🏗️ **整体架构图**

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端 (Offline-First)                    │
├─────────────────────────────────────────────────────────────┤
│  📱 UI Layer                                               │
│  ├── React Components                                      │
│  ├── Hooks (useAuth, useVipStatus, useHomeData)           │
│  └── State Management (Context/Redux)                     │
├─────────────────────────────────────────────────────────────┤
│  🔧 Business Logic Layer                                   │
│  ├── QuizEngineV3 (355行) - 完整Quiz引擎                   │
│  ├── VipPlanService (336行) - VIP计划管理                  │
│  ├── UnlockService (411行) - 解锁系统                      │
│  ├── MoodEntryService - 心情记录                           │
│  └── UserConfigService - 用户配置                          │
├─────────────────────────────────────────────────────────────┤
│  🔄 Sync Layer                                            │
│  ├── SyncCoordinator (458行) - 智能同步协调                │
│  ├── NetworkStatusService - 网络状态监控                   │
│  └── ConflictResolver - 冲突解决策略                       │
├─────────────────────────────────────────────────────────────┤
│  💾 Data Layer                                            │
│  ├── BaseService/BaseRepository - 统一架构                 │
│  ├── DatabaseService - SQLite本地存储                      │
│  └── CacheManager - 本地缓存管理                           │
├─────────────────────────────────────────────────────────────┤
│  🌐 Online Layer (Simplified)                             │
│  ├── ApiClientService - tRPC客户端                         │
│  ├── PaymentService - 支付代理(本地缓存)                   │
│  └── AuthService - 认证状态管理                            │
└─────────────────────────────────────────────────────────────┘
                              │
                         tRPC/HTTP
                              │
┌─────────────────────────────────────────────────────────────┐
│                    服务端 (Data Center)                      │
├─────────────────────────────────────────────────────────────┤
│  🔐 Security Layer                                         │
│  ├── AuthService - 用户认证和授权                          │
│  ├── PermissionService - 权限验证                          │
│  └── SecurityMiddleware - 安全中间件                       │
├─────────────────────────────────────────────────────────────┤
│  💳 Payment Layer                                          │
│  ├── PaymentService - 真实支付处理                         │
│  ├── StripeIntegration - Stripe集成                        │
│  ├── VipSubscriptionService - VIP订阅管理                  │
│  └── TransactionService - 交易记录                         │
├─────────────────────────────────────────────────────────────┤
│  🔄 Sync Layer                                            │
│  ├── SyncService - 数据同步协调                            │
│  ├── ConflictResolver - 服务端冲突解决                     │
│  ├── DataValidator - 数据验证                              │
│  └── BatchProcessor - 批量数据处理                         │
├─────────────────────────────────────────────────────────────┤
│  📊 Analytics Layer                                        │
│  ├── AnalyticsService - 数据分析                           │
│  ├── ReportingService - 报告生成                           │
│  ├── StatisticsService - 统计计算                          │
│  └── RecommendationEngine - 推荐引擎                       │
├─────────────────────────────────────────────────────────────┤
│  🗄️ Data Layer                                            │
│  ├── DatabaseService - 云端数据库                          │
│  ├── CacheService - Redis缓存                              │
│  ├── FileStorageService - 文件存储                         │
│  └── BackupService - 数据备份                              │
├─────────────────────────────────────────────────────────────┤
│  🌐 API Layer                                             │
│  ├── tRPC Router - 类型安全API                             │
│  ├── REST API - 兼容性接口                                 │
│  ├── WebSocket - 实时通信                                  │
│  └── GraphQL - 复杂查询(可选)                              │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 **数据流设计**

### 📥 **数据获取流程**

```mermaid
graph TD
    A[用户请求] --> B[页面Hook]
    B --> C[混合数据Hook]
    C --> D{网络状态}
    D -->|在线| E[在线服务API]
    D -->|离线| F[本地数据库]
    E --> G{API调用}
    G -->|成功| H[返回在线数据]
    G -->|失败| F
    F --> I[返回本地数据]
    H --> J[更新本地缓存]
    I --> K[标记需要同步]
    J --> L[UI状态更新]
    K --> L
```

### 📤 **数据提交流程**

```mermaid
graph TD
    A[用户提交] --> B[业务逻辑层]
    B --> C{操作类型}
    C -->|必须在线| D[在线服务]
    C -->|可离线| E{网络状态}
    E -->|在线| F[在线服务优先]
    E -->|离线| G[本地存储]
    D --> H{调用结果}
    F --> H
    H -->|成功| I[操作完成]
    H -->|失败| J{可回退?}
    J -->|是| G
    J -->|否| K[显示错误]
    G --> L[标记待同步]
    L --> M[网络恢复时同步]
```

### 🔄 **同步机制流程**

```mermaid
graph TD
    A[同步触发] --> B[SyncCoordinator]
    B --> C[收集待同步数据]
    C --> D[按优先级排序]
    D --> E[批量上传]
    E --> F[服务端处理]
    F --> G[冲突检测]
    G --> H{有冲突?}
    H -->|是| I[冲突解决]
    H -->|否| J[应用服务端变更]
    I --> K[解决策略]
    K --> L[更新本地数据]
    J --> L
    L --> M[同步完成]
```

## 📊 **服务分类和职责**

### 🔴 **必须在线服务 (Online-Only)**

#### **认证服务 (AuthService)**
```typescript
// 统一类型引用
import type {
  User,
  UserCreate,
  AuthState,
  LoginCredentials,
  RegisterData,
  AuthResult,
  TokenResult,
  TokenValidation,
  ServiceResult
} from '@/types/schema/index.js';

// 客户端: src/lib/auth/better-auth-client.ts
interface AuthService {
  // 必须在线的操作
  login(credentials: LoginCredentials): Promise<ServiceResult<AuthResult>>;
  register(userData: RegisterData): Promise<ServiceResult<AuthResult>>;
  logout(): Promise<ServiceResult<void>>;
  refreshToken(): Promise<ServiceResult<TokenResult>>;

  // 本地状态管理
  getAuthState(): AuthState;
  saveAuthState(state: AuthState): void;
  clearAuthState(): void;
}

// 服务端: server/lib/services/AuthService.ts
interface ServerAuthService {
  authenticateUser(credentials: LoginCredentials): Promise<ServiceResult<AuthResult>>;
  createUser(userData: UserCreate): Promise<ServiceResult<User>>;
  validateToken(token: string): Promise<ServiceResult<TokenValidation>>;
  refreshUserToken(refreshToken: string): Promise<ServiceResult<TokenResult>>;
  revokeToken(token: string): Promise<ServiceResult<void>>;
}
```

#### **支付服务 (PaymentService)**
```typescript
// 统一类型引用
import type {
  VipPurchaseData,
  SkinPurchaseData,
  PaymentResult,
  PurchaseResult,
  PaymentTransaction,
  PaymentValidation,
  RefundResult,
  ServiceResult
} from '@/types/schema/index.js';

// 客户端: src/services/online/services/PaymentService.ts
interface ClientPaymentService {
  // 必须在线的操作 (通过tRPC调用服务端)
  purchaseVip(data: VipPurchaseData): Promise<ServiceResult<PaymentResult>>;
  purchaseSkin(data: SkinPurchaseData): Promise<ServiceResult<PaymentResult>>;

  // 本地缓存管理
  updateLocalCache(key: string, data: any): void;
  getLocalCache(key: string): any;
}

// 服务端: server/lib/services/PaymentService.ts
interface ServerPaymentService {
  processVipPurchase(data: VipPurchaseData): Promise<ServiceResult<PurchaseResult>>;
  processSkinPurchase(data: SkinPurchaseData): Promise<ServiceResult<PurchaseResult>>;
  validatePayment(transactionId: string): Promise<ServiceResult<PaymentValidation>>;
  handleRefund(transactionId: string): Promise<ServiceResult<RefundResult>>;
  getTransactionHistory(userId: string): Promise<ServiceResult<PaymentTransaction[]>>;
}
```

### 🟡 **在线优先服务 (Online-First)**

#### **内容服务 (ContentService)**
```typescript
// 混合实现策略
interface ContentService {
  // 在线优先，离线回退
  getQuizPacks(): Promise<QuizPack[]>;
  getSkins(): Promise<Skin[]>;
  getEmojiSets(): Promise<EmojiSet[]>;
  
  // 实现逻辑
  async getContent<T>(
    onlineGetter: () => Promise<T>,
    offlineGetter: () => Promise<T>
  ): Promise<T> {
    try {
      if (navigator.onLine) {
        const onlineData = await onlineGetter();
        await this.updateLocalCache(onlineData);
        return onlineData;
      }
    } catch (error) {
      console.warn('Online fetch failed, falling back to offline');
    }
    return await offlineGetter();
  }
}
```

### 🟢 **离线优先服务 (Offline-First)**

#### **Quiz引擎服务 (QuizEngineV3)**
```typescript
// 统一类型引用
import type {
  QuizPack,
  QuizSession,
  QuizSessionCreate,
  QuizSessionUpdate,
  QuizQuestion,
  QuizQuestionOption,
  QuizAnswer,
  QuizResult,
  QuizEngineConfig,
  QuestionPresentationData,
  AnswerSubmissionResult,
  SessionProgress,
  QuizStatistics,
  AnomalyReport,
  ValidationResult,
  ServiceResult
} from '@/types/schema/index.js';

// 客户端: src/services/entities/QuizEngineV3.ts (355行)
interface QuizEngineV3 {
  // 完全离线可用
  createQuizSession(packId: string, userId: string): Promise<ServiceResult<QuizSession>>;
  getCurrentQuestionData(sessionId: string): Promise<ServiceResult<QuestionPresentationData>>;
  submitAnswer(sessionId: string, questionId: string, answer: any): Promise<ServiceResult<AnswerSubmissionResult>>;
  completeSession(sessionId: string): Promise<ServiceResult<QuizResult>>;

  // 配置管理
  updateEngineConfig(config: Partial<QuizEngineConfig>): void;
  getSessionProgress(sessionId: string): Promise<ServiceResult<SessionProgress>>;
}

// 服务端: server/lib/services/QuizEngineService.ts (重构后)
interface ServerQuizEngineService {
  // 数据验证和持久化
  validateQuizSession(sessionData: QuizSession): Promise<ServiceResult<ValidationResult>>;
  persistQuizResults(results: QuizResult[]): Promise<ServiceResult<boolean>>;
  generateQuizStatistics(packId: string): Promise<ServiceResult<QuizStatistics>>;
  detectAnomalousResults(results: QuizResult[]): Promise<ServiceResult<AnomalyReport>>;
}
```

#### **VIP计划服务 (VipPlanService)**
```typescript
// 统一类型引用
import type {
  VipPlan,
  VipPlanCreate,
  VipPlanUpdate,
  VipStatus,
  VipSubscription,
  PlanComparison,
  ValueAnalysis,
  SearchCriteria,
  ExpirationReport,
  ServiceResult
} from '@/types/schema/index.js';

// 客户端: src/services/entities/VipPlanService.ts (336行)
interface ClientVipPlanService {
  // 离线可用的查询和分析
  getAvailablePlans(): Promise<ServiceResult<VipPlan[]>>;
  getPlanDetails(planId: string): Promise<ServiceResult<VipPlan>>;
  comparePlans(planIds: string[]): Promise<ServiceResult<PlanComparison>>;
  getPlanValueAnalysis(planId: string): Promise<ServiceResult<ValueAnalysis>>;
  searchPlans(criteria: SearchCriteria): Promise<ServiceResult<VipPlan[]>>;
}

// 服务端: server/lib/services/VipPlanService.ts (新建)
interface ServerVipPlanService {
  // VIP计划管理
  createVipPlan(planData: VipPlanCreate): Promise<ServiceResult<VipPlan>>;
  updateVipPlan(planId: string, updates: VipPlanUpdate): Promise<ServiceResult<VipPlan>>;
  activateVipPlan(planId: string): Promise<ServiceResult<boolean>>;
  deactivateVipPlan(planId: string): Promise<ServiceResult<boolean>>;

  // VIP状态管理
  getUserVipStatus(userId: string): Promise<ServiceResult<VipStatus>>;
  updateUserVipStatus(userId: string, planId: string): Promise<ServiceResult<VipStatus>>;
  checkVipExpiration(): Promise<ServiceResult<ExpirationReport>>;
}
```

#### **解锁服务 (UnlockService)**
```typescript
// 统一类型引用
import type {
  UserUnlock,
  UserUnlockCreate,
  UserUnlockUpdate,
  UnlockResult,
  UnlockStats,
  UnlockData,
  UnlockRequest,
  UnlockReport,
  BatchResult,
  SyncResult,
  ServiceResult
} from '@/types/schema/index.js';

// 客户端: src/services/entities/UnlockService.ts (411行)
interface ClientUnlockService {
  // 离线解锁状态管理
  unlockContent(userId: string, contentType: string, contentId: string): Promise<ServiceResult<UnlockResult>>;
  isContentUnlocked(userId: string, contentType: string, contentId: string): Promise<ServiceResult<boolean>>;
  getUserUnlockedContent(userId: string, contentType?: string): Promise<ServiceResult<UserUnlock[]>>;
  getUserUnlockStats(userId: string): Promise<ServiceResult<UnlockStats>>;
  batchUnlockContent(userId: string, unlocks: UnlockData[]): Promise<ServiceResult<BatchResult>>;
}

// 服务端: server/lib/services/UnlockService.ts (新建)
interface ServerUnlockService {
  // 解锁验证和处理
  validateUnlockRequest(unlockData: UnlockRequest): Promise<ServiceResult<boolean>>;
  processUnlock(unlockData: UnlockData): Promise<ServiceResult<UnlockResult>>;
  syncUserUnlocks(userId: string, clientUnlocks: UserUnlock[]): Promise<ServiceResult<SyncResult>>;
  generateUnlockReport(userId: string): Promise<ServiceResult<UnlockReport>>;
}
```

### 🔄 **同步协调服务**

#### **同步协调器 (SyncCoordinator)**
```typescript
// 统一类型引用
import type {
  SyncResult,
  SyncStatus,
  SyncConfig,
  SyncConflict,
  ConflictResolution,
  SyncStrategy,
  NetworkStatus,
  FullSyncRequest,
  FullSyncResponse,
  IncrementalSyncRequest,
  IncrementalSyncResponse,
  SyncData,
  BatchUpdate,
  BatchResult,
  SyncReport,
  ValidationResult,
  ServiceResult
} from '@/types/schema/index.js';

// 客户端: src/services/sync/SyncCoordinator.ts (458行)
interface ClientSyncCoordinator {
  // 同步策略管理
  performIncrementalSync(userId: string): Promise<ServiceResult<SyncResult>>;
  performFullSync(userId: string): Promise<ServiceResult<SyncResult>>;
  resolveSyncConflicts(conflicts: SyncConflict[]): Promise<ServiceResult<ConflictResolution[]>>;

  // 状态管理
  getSyncStatus(userId: string): Promise<ServiceResult<SyncStatus>>;
  setSyncConfig(config: Partial<SyncConfig>): void;
  forceSyncEntity(userId: string, entityType: string, entityId: string): Promise<ServiceResult<boolean>>;

  // 网络状态响应
  onNetworkStatusChanged(status: NetworkStatus): void;
  registerSyncStrategy(entityType: string, strategy: SyncStrategy): void;
}

// 服务端: server/lib/services/SyncService.ts (重构后)
interface ServerSyncService {
  // 同步数据处理
  handleFullSync(syncRequest: FullSyncRequest): Promise<ServiceResult<FullSyncResponse>>;
  handleIncrementalSync(syncRequest: IncrementalSyncRequest): Promise<ServiceResult<IncrementalSyncResponse>>;
  resolveDataConflicts(conflicts: SyncConflict[]): Promise<ServiceResult<ConflictResolution[]>>;

  // 数据验证
  validateSyncData(data: SyncData): Promise<ServiceResult<ValidationResult>>;
  detectDataConflicts(clientData: any[], serverData: any[]): Promise<ServiceResult<SyncConflict[]>>;

  // 批量处理
  processBatchUpdates(updates: BatchUpdate[]): Promise<ServiceResult<BatchResult>>;
  generateSyncReport(userId: string): Promise<ServiceResult<SyncReport>>;
}
```

## 🎯 **tRPC API 设计**

### 📡 **API 端点规范**

```typescript
// server/lib/router.ts
// 统一类型引用
import type {
  LoginCredentials,
  RegisterData,
  VipPurchaseData,
  SkinPurchaseData,
  FullSyncRequest,
  IncrementalSyncRequest,
  ServiceResult
} from '../../src/types/schema/index.js';

// Zod验证Schema引用
import {
  LoginCredentialsSchema,
  RegisterDataSchema,
  VipPurchaseDataSchema,
  SkinPurchaseDataSchema,
  FullSyncRequestSchema,
  IncrementalSyncRequestSchema
} from '../../src/types/schema/validation.js';

export const appRouter = router({
  // 认证相关 (必须在线)
  auth: router({
    login: publicProcedure
      .input(LoginCredentialsSchema)
      .mutation(async ({ input }) => {
        return await authService.authenticateUser(input);
      }),
    
    register: publicProcedure
      .input(RegisterDataSchema)
      .mutation(async ({ input }) => {
        return await authService.createUser(input);
      }),
    
    refreshToken: protectedProcedure
      .mutation(async ({ ctx }) => {
        return await authService.refreshUserToken(ctx.refreshToken);
      })
  }),

  // 支付相关 (必须在线)
  payment: router({
    purchaseVip: protectedProcedure
      .input(VipPurchaseDataSchema)
      .mutation(async ({ input, ctx }) => {
        return await paymentService.processVipPurchase(input);
      }),
    
    purchaseSkin: protectedProcedure
      .input(SkinPurchaseDataSchema)
      .mutation(async ({ input, ctx }) => {
        return await paymentService.processSkinPurchase(input);
      })
  }),

  // 同步相关 (客户端主导)
  sync: router({
    performFullSync: protectedProcedure
      .input(FullSyncRequestSchema)
      .mutation(async ({ input, ctx }) => {
        return await syncService.handleFullSync(input);
      }),
    
    getIncrementalUpdates: protectedProcedure
      .input(IncrementalSyncRequestSchema)
      .query(async ({ input, ctx }) => {
        return await syncService.handleIncrementalSync(input);
      })
  }),

  // VIP状态查询 (在线验证)
  vip: router({
    getStatus: protectedProcedure
      .query(async ({ ctx }) => {
        return await vipService.getUserVipStatus(ctx.userId);
      }),
    
    getPlans: publicProcedure
      .query(async () => {
        return await vipService.getAvailablePlans();
      })
  }),

  // 数据查询 (在线优先)
  data: router({
    getQuizPacks: publicProcedure
      .query(async () => {
        return await contentService.getQuizPacks();
      }),
    
    getUserStats: protectedProcedure
      .input(z.object({ userId: z.string() }))
      .query(async ({ input }) => {
        return await analyticsService.getUserStatistics(input.userId);
      })
  })
});
```

## 📈 **性能和扩展性设计**

### ⚡ **性能优化策略**

#### **客户端优化**
```typescript
// 1. 智能缓存策略
interface CacheStrategy {
  // 内存缓存 - 热点数据
  memoryCache: {
    maxSize: 50 * 1024 * 1024; // 50MB
    ttl: 5 * 60 * 1000; // 5分钟
  };
  
  // 本地存储缓存 - 持久数据
  localStorageCache: {
    maxSize: 100 * 1024 * 1024; // 100MB
    ttl: 24 * 60 * 60 * 1000; // 24小时
  };
  
  // SQLite缓存 - 结构化数据
  sqliteCache: {
    enableWAL: true; // Write-Ahead Logging
    cacheSize: 10000; // 页面数
    synchronous: 'NORMAL';
  };
}

// 2. 批量操作优化
interface BatchOperations {
  // 批量同步
  batchSync: {
    maxBatchSize: 100;
    batchTimeout: 5000; // 5秒
    priorityBatching: true;
  };
  
  // 批量数据库操作
  batchDatabase: {
    transactionSize: 50;
    useTransactions: true;
    enableBulkInsert: true;
  };
}

// 3. 网络优化
interface NetworkOptimization {
  // 请求合并
  requestBatching: {
    enabled: true;
    batchWindow: 100; // 100ms
    maxBatchSize: 10;
  };
  
  // 压缩
  compression: {
    enabled: true;
    algorithm: 'gzip';
    minSize: 1024; // 1KB
  };
  
  // 重试策略
  retryStrategy: {
    maxRetries: 3;
    backoffMultiplier: 2;
    initialDelay: 1000;
  };
}
```

#### **服务端优化**
```typescript
// 1. 数据库优化
interface DatabaseOptimization {
  // 连接池
  connectionPool: {
    min: 5;
    max: 20;
    acquireTimeoutMillis: 30000;
    idleTimeoutMillis: 600000;
  };
  
  // 查询优化
  queryOptimization: {
    enableQueryCache: true;
    cacheSize: '256MB';
    enableIndexHints: true;
    usePreparedStatements: true;
  };
  
  // 分页策略
  pagination: {
    defaultPageSize: 50;
    maxPageSize: 1000;
    enableCursorPagination: true;
  };
}

// 2. 缓存策略
interface ServerCacheStrategy {
  // Redis缓存
  redis: {
    host: 'redis-cluster';
    keyPrefix: 'mindful-mood:';
    defaultTTL: 3600; // 1小时
    maxMemory: '2GB';
  };
  
  // 应用级缓存
  applicationCache: {
    vipPlans: { ttl: 24 * 60 * 60 }; // 24小时
    userSessions: { ttl: 30 * 60 }; // 30分钟
    quizPacks: { ttl: 12 * 60 * 60 }; // 12小时
  };
}

// 3. 负载均衡
interface LoadBalancing {
  // 水平扩展
  horizontalScaling: {
    minInstances: 2;
    maxInstances: 10;
    targetCPU: 70; // 70%
    targetMemory: 80; // 80%
  };
  
  // 数据库分片
  databaseSharding: {
    shardKey: 'user_id';
    shardCount: 4;
    enableReadReplicas: true;
  };
}
```

### 📊 **监控和告警**

```typescript
// 监控指标
interface MonitoringMetrics {
  // 性能指标
  performance: {
    responseTime: { p95: 200, p99: 500 }; // ms
    throughput: { target: 1000 }; // requests/second
    errorRate: { threshold: 1 }; // 1%
  };
  
  // 业务指标
  business: {
    syncSuccessRate: { threshold: 99 }; // 99%
    paymentSuccessRate: { threshold: 95 }; // 95%
    userEngagement: { dailyActive: 1000 };
  };
  
  // 系统指标
  system: {
    cpuUsage: { threshold: 80 }; // 80%
    memoryUsage: { threshold: 85 }; // 85%
    diskUsage: { threshold: 90 }; // 90%
  };
}

// 告警配置
interface AlertingConfig {
  // 严重告警
  critical: {
    paymentFailure: { threshold: 5, window: '5m' };
    authServiceDown: { threshold: 1, window: '1m' };
    databaseConnectionLoss: { threshold: 1, window: '30s' };
  };
  
  // 警告告警
  warning: {
    highResponseTime: { threshold: 1000, window: '5m' };
    syncFailureRate: { threshold: 5, window: '10m' };
    lowDiskSpace: { threshold: 85, window: '1h' };
  };
}
```

## 🔒 **安全设计**

### 🛡️ **认证和授权**

```typescript
// JWT Token 策略
interface TokenStrategy {
  accessToken: {
    algorithm: 'RS256';
    expiresIn: '15m';
    issuer: 'mindful-mood-api';
    audience: 'mindful-mood-client';
  };

  refreshToken: {
    expiresIn: '7d';
    rotateOnUse: true;
    revokeOnLogout: true;
    maxConcurrentSessions: 5;
  };
}

// 权限控制
interface PermissionControl {
  roles: {
    user: ['read:own', 'write:own'];
    vip: ['read:own', 'write:own', 'access:premium'];
    admin: ['read:all', 'write:all', 'manage:users'];
  };

  resources: {
    'quiz:*': ['user', 'vip', 'admin'];
    'payment:*': ['user', 'vip'];
    'admin:*': ['admin'];
  };
}
```

### 🔐 **数据加密**

```typescript
// 客户端加密
interface ClientEncryption {
  // 敏感数据加密
  sensitiveData: {
    algorithm: 'AES-256-GCM';
    keyDerivation: 'PBKDF2';
    iterations: 100000;
  };

  // 传输加密
  transport: {
    protocol: 'TLS 1.3';
    certificatePinning: true;
    hsts: true;
  };
}

// 服务端加密
interface ServerEncryption {
  // 数据库加密
  database: {
    encryptionAtRest: true;
    algorithm: 'AES-256';
    keyRotation: '90d';
  };

  // 备份加密
  backup: {
    algorithm: 'AES-256-GCM';
    keyManagement: 'AWS KMS';
    compressionBeforeEncryption: true;
  };
}
```

## 🧪 **测试策略**

### 🔬 **测试金字塔**

```typescript
// 单元测试 (70%)
interface UnitTestStrategy {
  // 客户端单元测试
  client: {
    services: ['QuizEngineV3', 'VipPlanService', 'UnlockService'];
    repositories: ['BaseRepository', 'QuizPackRepository'];
    utilities: ['SyncCoordinator', 'NetworkStatusService'];
    coverage: 90; // 90%
  };

  // 服务端单元测试
  server: {
    services: ['AuthService', 'PaymentService', 'SyncService'];
    controllers: ['tRPC routers', 'middleware'];
    utilities: ['encryption', 'validation'];
    coverage: 95; // 95%
  };
}

// 集成测试 (20%)
interface IntegrationTestStrategy {
  // API集成测试
  api: {
    authentication: ['login', 'register', 'token refresh'];
    payment: ['VIP purchase', 'skin purchase', 'refund'];
    sync: ['full sync', 'incremental sync', 'conflict resolution'];
  };

  // 数据库集成测试
  database: {
    transactions: ['ACID compliance', 'rollback scenarios'];
    performance: ['query optimization', 'index usage'];
    migration: ['schema changes', 'data migration'];
  };
}

// 端到端测试 (10%)
interface E2ETestStrategy {
  // 用户流程测试
  userFlows: {
    registration: ['sign up', 'email verification', 'profile setup'];
    quizFlow: ['start quiz', 'answer questions', 'view results'];
    purchaseFlow: ['select plan', 'payment', 'unlock content'];
    syncFlow: ['offline usage', 'network recovery', 'data sync'];
  };

  // 跨平台测试
  platforms: {
    web: ['Chrome', 'Firefox', 'Safari', 'Edge'];
    mobile: ['iOS Safari', 'Android Chrome'];
    offline: ['airplane mode', 'poor connection'];
  };
}
```

### 🎯 **测试自动化**

```typescript
// CI/CD 测试流水线
interface TestPipeline {
  // 提交时测试
  onCommit: {
    unitTests: { timeout: '5m', parallel: true };
    linting: { eslint: true, prettier: true, typecheck: true };
    security: { dependencyCheck: true, codeAnalysis: true };
  };

  // PR测试
  onPullRequest: {
    integrationTests: { timeout: '15m', database: 'test' };
    e2eTests: { timeout: '30m', browsers: ['chrome', 'firefox'] };
    performanceTests: { lighthouse: true, loadTesting: true };
  };

  // 部署前测试
  preDeployment: {
    smokeTests: { timeout: '5m', criticalPaths: true };
    securityTests: { penetrationTesting: true, vulnerabilityScanning: true };
    compatibilityTests: { backwardCompatibility: true, apiVersioning: true };
  };
}
```

## 📦 **部署和运维**

### 🚀 **部署策略**

```typescript
// 容器化部署
interface ContainerDeployment {
  // Docker配置
  docker: {
    baseImage: 'node:18-alpine';
    multiStage: true;
    healthCheck: {
      interval: '30s';
      timeout: '10s';
      retries: 3;
    };
  };

  // Kubernetes配置
  kubernetes: {
    replicas: { min: 2, max: 10 };
    resources: {
      requests: { cpu: '100m', memory: '256Mi' };
      limits: { cpu: '500m', memory: '512Mi' };
    };
    probes: {
      liveness: '/health/live';
      readiness: '/health/ready';
      startup: '/health/startup';
    };
  };
}

// 蓝绿部署
interface BlueGreenDeployment {
  strategy: {
    type: 'blue-green';
    autoRollback: true;
    healthCheckDuration: '5m';
    trafficSwitchDuration: '2m';
  };

  rollbackTriggers: {
    errorRate: { threshold: 5, window: '2m' };
    responseTime: { threshold: 2000, window: '1m' };
    healthCheck: { failureCount: 3 };
  };
}
```

### 📊 **监控和日志**

```typescript
// 应用监控
interface ApplicationMonitoring {
  // 指标收集
  metrics: {
    business: {
      userRegistrations: 'counter';
      quizCompletions: 'counter';
      vipPurchases: 'counter';
      syncOperations: 'histogram';
    };

    technical: {
      apiResponseTime: 'histogram';
      databaseQueryTime: 'histogram';
      cacheHitRate: 'gauge';
      errorRate: 'counter';
    };
  };

  // 分布式追踪
  tracing: {
    enabled: true;
    samplingRate: 0.1; // 10%
    exporters: ['jaeger', 'zipkin'];
    instrumentations: ['http', 'database', 'redis'];
  };
}

// 日志管理
interface LogManagement {
  // 结构化日志
  structured: {
    format: 'json';
    level: 'info';
    fields: ['timestamp', 'level', 'service', 'traceId', 'userId'];
  };

  // 日志聚合
  aggregation: {
    platform: 'ELK Stack';
    retention: '30d';
    indexing: 'daily';
    alerting: true;
  };

  // 敏感数据处理
  dataProtection: {
    maskPII: true;
    excludePasswords: true;
    hashUserIds: true;
  };
}
```

## 🔄 **数据迁移和版本控制**

### 📊 **数据库迁移**

```typescript
// 迁移策略
interface MigrationStrategy {
  // 版本控制
  versioning: {
    scheme: 'semantic'; // major.minor.patch
    backwardCompatibility: 2; // 支持2个主版本
    migrationPath: 'incremental';
  };

  // 迁移类型
  types: {
    schema: {
      addColumn: 'safe';
      dropColumn: 'breaking';
      renameColumn: 'breaking';
      addIndex: 'safe';
    };

    data: {
      transform: 'batch';
      batchSize: 1000;
      rollbackSupport: true;
    };
  };
}

// 零停机迁移
interface ZeroDowntimeMigration {
  // 分阶段迁移
  phases: {
    phase1: 'schema_preparation';
    phase2: 'dual_write';
    phase3: 'data_migration';
    phase4: 'read_switch';
    phase5: 'cleanup';
  };

  // 回滚策略
  rollback: {
    automaticTriggers: ['data_corruption', 'performance_degradation'];
    manualTriggers: ['business_logic_error'];
    rollbackWindow: '24h';
  };
}
```

### 🔄 **API版本控制**

```typescript
// API版本策略
interface APIVersioning {
  // 版本方案
  scheme: {
    type: 'header'; // X-API-Version
    format: 'v1.2.3';
    defaultVersion: 'v1.0.0';
    supportedVersions: ['v1.0.0', 'v1.1.0', 'v1.2.0'];
  };

  // 兼容性策略
  compatibility: {
    deprecationNotice: '90d'; // 90天通知期
    supportWindow: '1y'; // 1年支持窗口
    breakingChangePolicy: 'major_version_only';
  };

  // 客户端适配
  clientAdaptation: {
    automaticUpgrade: false;
    gracefulDegradation: true;
    featureDetection: true;
  };
}
```

## 📈 **性能基准和SLA**

### ⚡ **性能目标**

```typescript
// 性能SLA
interface PerformanceSLA {
  // 响应时间
  responseTime: {
    api: {
      p50: 100, // 50% < 100ms
      p95: 500, // 95% < 500ms
      p99: 1000 // 99% < 1000ms
    };

    database: {
      simpleQuery: 10, // < 10ms
      complexQuery: 100, // < 100ms
      transaction: 50 // < 50ms
    };
  };

  // 吞吐量
  throughput: {
    api: 1000, // 1000 req/s
    database: 5000, // 5000 queries/s
    sync: 100 // 100 sync operations/s
  };

  // 可用性
  availability: {
    uptime: 99.9, // 99.9%
    mttr: 15, // 15分钟平均恢复时间
    mtbf: 720 // 720小时平均故障间隔
  };
}

// 容量规划
interface CapacityPlanning {
  // 用户增长预测
  userGrowth: {
    current: 10000;
    yearlyGrowthRate: 200; // 200%
    peakConcurrency: 1000;
  };

  // 资源需求
  resourceRequirements: {
    compute: {
      cpu: '4 cores per 1000 users';
      memory: '8GB per 1000 users';
      storage: '100GB per 10000 users';
    };

    network: {
      bandwidth: '1Gbps per 5000 users';
      cdn: 'global distribution';
      latency: '<100ms globally';
    };
  };
}
```

## 📝 **类型统一最佳实践**

### 🎯 **类型定义规范**

#### **1. 统一导入规范**
```typescript
// ✅ 正确的导入方式
import type {
  // 按功能分组导入
  User, UserCreate, UserUpdate,           // 用户相关
  VipPlan, VipStatus, VipSubscription,    // VIP相关
  QuizPack, QuizSession, QuizResult,      // Quiz相关
  ServiceResult, DatabaseContext          // 基础类型
} from '@/types/schema/index.js';

// ❌ 错误的导入方式
import { User } from './types/User.ts';           // 分散导入
import type { CustomUser } from './custom.ts';   // 自定义重复类型
```

#### **2. 类型扩展规范**
```typescript
// ✅ 正确的类型扩展
import type { User, ServiceResult } from '@/types/schema/index.js';

// 扩展基础类型
interface UserWithStats extends User {
  stats: UserStats;
  lastActivity: Date;
}

// 服务特定的结果类型
type UserServiceResult<T> = ServiceResult<T> & {
  cacheHit?: boolean;
  source: 'local' | 'remote';
};

// ❌ 错误的类型重定义
interface MyUser {  // 不要重新定义已有类型
  id: string;
  email: string;
  // ...
}
```

#### **3. 验证Schema使用**
```typescript
// ✅ 正确的验证Schema使用
import { UserCreateSchema, VipPurchaseDataSchema } from '@/types/schema/validation.js';
import type { UserCreate, VipPurchaseData } from '@/types/schema/index.js';

// tRPC端点定义
const createUser = publicProcedure
  .input(UserCreateSchema)  // 使用Zod Schema验证
  .mutation(async ({ input }: { input: UserCreate }) => {
    // input 自动具有正确的类型
    return await userService.createUser(input);
  });

// 客户端调用
const userData: UserCreate = {
  email: '<EMAIL>',
  username: 'user123',
  // TypeScript会确保类型正确
};
```

### 🔧 **类型维护策略**

#### **1. 版本控制**
```typescript
// src/types/schema/version.ts
export const SCHEMA_VERSION = '1.2.0';

// 类型版本兼容性
export interface VersionedType {
  _version: string;
  _deprecated?: string[];
  _added?: string[];
}

// 向后兼容的类型演进
export interface UserV1 {
  id: string;
  email: string;
}

export interface UserV2 extends UserV1 {
  username: string;        // 新增字段
  displayName?: string;    // 可选字段保证兼容性
}
```

#### **2. 类型检查自动化**
```typescript
// scripts/type-check.ts
import { execSync } from 'child_process';

// 检查类型一致性
function checkTypeConsistency() {
  // 检查客户端类型
  execSync('tsc --noEmit --project src/tsconfig.json');

  // 检查服务端类型
  execSync('tsc --noEmit --project server/tsconfig.json');

  // 检查类型导出完整性
  checkTypeExports();
}

function checkTypeExports() {
  // 确保所有类型都从index.ts导出
  // 检查是否有重复类型定义
  // 验证Schema和类型的一致性
}
```

### 📋 **类型检查清单**

#### **开发阶段检查**
- [ ] 所有新类型都在 `src/types/schema/` 中定义
- [ ] 类型通过 `src/types/schema/index.ts` 统一导出
- [ ] 对应的Zod Schema在 `validation.js` 中定义
- [ ] 客户端和服务端使用相同的类型定义
- [ ] 没有重复或冲突的类型定义

#### **代码审查检查**
- [ ] 导入语句使用统一的 `@/types/schema/index.js`
- [ ] 没有自定义重复的基础类型
- [ ] 类型扩展遵循继承原则
- [ ] API端点使用正确的验证Schema
- [ ] 类型变更包含适当的版本控制

#### **部署前检查**
- [ ] TypeScript编译无错误
- [ ] 类型覆盖率达到要求
- [ ] 客户端-服务端类型兼容性测试通过
- [ ] API文档与类型定义一致
- [ ] 向后兼容性验证通过

这个完整的服务设计文档为整个系统提供了清晰的架构指导，特别强调了类型统一的重要性，涵盖了从基础架构到安全、测试、部署和运维的所有方面，确保客户端和服务端的完美协作和类型安全。
