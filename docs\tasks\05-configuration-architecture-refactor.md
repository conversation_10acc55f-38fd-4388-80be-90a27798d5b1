# Quiz配置架构重构方案

## 📋 重构背景

### 问题分析
1. **配置混乱**: user_configs表同时包含全局应用设置和Quiz特定配置
2. **职责不清**: 全局设置和Quiz设置混合在一起，难以维护
3. **表结构重复**: 4个Quiz展现配置表功能重叠，架构过于复杂
4. **数据孤立**: QuizSettings页面使用模拟数据，没有与数据库集成

### 重构目标
1. **职责分离**: 全局应用设置与Quiz系统配置完全分离
2. **架构简化**: 减少重复的配置表，简化数据流
3. **数据集成**: Quiz配置与数据库完全集成
4. **易于维护**: 清晰的配置层次和管理方式

## 🏗️ 新架构设计

### A. 全局应用设置 (user_configs表)

**职责**: 管理应用级别的全局设置
**作用域**: 整个应用
**管理方式**: Settings页面 + useUserConfig hook

```sql
-- 简化后的user_configs表 (只保留全局应用设置)
CREATE TABLE IF NOT EXISTS user_configs (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    user_id TEXT NOT NULL,
    is_active BOOLEAN DEFAULT 1,

    -- 全局应用设置
    theme_mode TEXT DEFAULT 'system', -- 'light', 'dark', 'system'
    language TEXT DEFAULT 'zh-CN', -- 'zh-CN', 'en-US'

    -- 全局无障碍设置
    accessibility TEXT, -- JSON: 全局无障碍配置

    -- 通知和音效设置
    notifications_enabled BOOLEAN DEFAULT 1,
    sound_enabled BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, name)
);
```

**配置内容**:
- 主题模式 (light/dark/system)
- 界面语言
- 全局无障碍设置 (高对比度、大字体、减少动画等)
- 通知设置
- 音效设置

### B. Quiz系统配置 (独立的Quiz配置表)

**职责**: 管理Quiz系统的展现和交互配置
**作用域**: Quiz系统
**管理方式**: QuizSettings页面 + 专门的Quiz配置hooks

#### 1. 用户Quiz偏好配置
```sql
-- 用户Quiz偏好配置表
CREATE TABLE IF NOT EXISTS user_quiz_preferences (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    config_name TEXT NOT NULL DEFAULT 'default',

    -- 6层个性化配置 (JSON: QuizPresentationConfig)
    presentation_config TEXT NOT NULL,

    -- 配置元数据
    config_version TEXT DEFAULT '2.0',
    personalization_level INTEGER DEFAULT 50, -- 0-100

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);
```

#### 2. Quiz包特定覆盖配置
```sql
-- Quiz包特定覆盖配置表 (保留现有结构)
CREATE TABLE IF NOT EXISTS quiz_pack_overrides (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,

    -- 展现覆盖配置 (JSON: PackSpecificOverrides)
    presentation_overrides TEXT,

    -- 覆盖元数据
    override_reason TEXT DEFAULT 'user_preference',
    override_priority INTEGER DEFAULT 1,

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    UNIQUE(user_id, pack_id)
);
```

#### 3. Quiz会话配置快照
```sql
-- Quiz会话配置快照表 (合并原来的两个会话表)
CREATE TABLE IF NOT EXISTS quiz_session_configs (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL UNIQUE,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,

    -- 最终合并的展现配置 (JSON: FinalSessionConfig)
    final_presentation_config TEXT NOT NULL,

    -- 配置来源追踪 (JSON: ConfigSources)
    config_sources TEXT NOT NULL,

    -- 配置元数据
    personalization_level INTEGER DEFAULT 50,
    config_version TEXT DEFAULT '2.0',

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE
);
```

## 🔄 配置层次和合并流程

### 配置优先级 (从高到低)
1. **Quiz包特定覆盖** (quiz_pack_overrides)
2. **用户Quiz偏好** (user_quiz_preferences)
3. **Quiz包默认建议** (quiz_packs.default_presentation_hints)
4. **系统默认配置**

### 配置合并流程
```typescript
class QuizConfigMerger {
  async generateSessionConfig(
    userId: string,
    packId: string,
    sessionId: string
  ): Promise<FinalSessionConfig> {

    // 1. 加载用户Quiz偏好配置
    const userPreferences = await this.loadUserQuizPreferences(userId);

    // 2. 加载包特定覆盖配置
    const packOverrides = await this.loadPackOverrides(userId, packId);

    // 3. 加载包默认建议
    const packDefaults = await this.loadPackDefaults(packId);

    // 4. 合并配置
    const finalConfig = this.mergeConfigurations({
      base: userPreferences,
      overrides: packOverrides,
      defaults: packDefaults
    });

    // 5. 保存会话配置快照
    await this.saveSessionConfig(sessionId, finalConfig);

    return finalConfig;
  }
}
```

## 📊 数据迁移计划

### 第一阶段: 表结构调整
1. 简化user_configs表，移除Quiz相关字段
2. 创建user_quiz_preferences表
3. 重命名pack_presentation_overrides为quiz_pack_overrides
4. 合并会话配置表为quiz_session_configs
5. 删除重复的会话配置表

### 第二阶段: 数据迁移
1. 将user_configs中的Quiz配置迁移到user_quiz_preferences
2. 清理重复的会话配置数据
3. 更新外键关系

### 第三阶段: 代码重构
1. 更新Schema定义
2. 重构Service和Repository
3. 更新Hook和Context
4. 修改页面组件

## 🎯 预期收益

### 1. 架构清晰
- 全局设置与Quiz设置职责明确
- 配置层次简单易懂
- 数据流向清晰

### 2. 易于维护
- 减少重复代码和数据
- 配置管理集中化
- 便于扩展和修改

### 3. 用户体验
- Quiz配置与数据库完全集成
- 支持复杂的个性化需求
- 配置持久化和同步

### 4. 开发效率
- 类型安全的配置管理
- 统一的配置API
- 便于测试和调试

## 🔧 类型定义

### 全局应用设置类型
```typescript
// 全局应用设置
interface GlobalAppConfig {
  id: string;
  name: string;
  user_id: string;
  is_active: boolean;

  // 主题设置
  theme_mode: 'light' | 'dark' | 'system';
  language: 'zh-CN' | 'en-US';

  // 全局无障碍设置
  accessibility: GlobalAccessibilityConfig;

  // 通知和音效
  notifications_enabled: boolean;
  sound_enabled: boolean;

  // 审计字段
  created_at: string;
  last_updated: string;
}

interface GlobalAccessibilityConfig {
  high_contrast: boolean;
  large_text: boolean;
  reduce_motion: boolean;
  screen_reader_support: boolean;
}
```

### Quiz系统配置类型
```typescript
// Quiz用户偏好配置
interface UserQuizPreferences {
  id: string;
  user_id: string;
  config_name: string;

  // 6层个性化配置
  presentation_config: QuizPresentationConfig;

  // 配置元数据
  config_version: string;
  personalization_level: number; // 0-100

  // 状态管理
  is_active: boolean;
  is_default: boolean;

  // 审计字段
  created_at: string;
  updated_at: string;
}

// 6层Quiz展现配置
interface QuizPresentationConfig {
  // Layer 0: 数据集展现配置
  layer0_dataset_presentation: {
    preferred_pack_categories: string[];
    default_difficulty_preference: 'beginner' | 'regular' | 'advanced' | 'expert';
    session_length_preference: 'short' | 'medium' | 'long';
    auto_select_recommended: boolean;
    restore_progress: boolean;
    question_display_fields: QuestionDisplayFields;
    interaction_behavior: InteractionBehavior;
    question_management: QuestionManagement;
  };

  // Layer 1: 用户选择层
  layer1_user_choice: {
    preferred_view_type: ViewType;
    active_skin_id: string;
    color_mode: ColorMode;
    user_level: UserLevel;
  };

  // Layer 2: 渲染策略层
  layer2_rendering_strategy: {
    render_engine_preferences: Record<ViewType, RenderEngine>;
    content_display_mode_preferences: Record<ViewType, ContentDisplayMode[]>;
    layout_preferences: Record<ViewType, LayoutType>;
    performance_mode: 'high_quality' | 'balanced' | 'performance';
    supported_content_types: SupportedContentTypes;
  };

  // Layer 3: 皮肤基础层
  layer3_skin_base: {
    available_skins: AvailableSkin[];
    selected_skin_id: string;
    colors: Record<string, string>;
    fonts: FontConfiguration;
    animations: AnimationConfiguration;
  };

  // Layer 4: 视图细节层
  layer4_view_detail: {
    wheel_config?: WheelConfiguration;
    card_config?: CardConfiguration;
    bubble_config?: BubbleConfiguration;
    list_config?: ListConfiguration;
    emotion_presentation: EmotionPresentationConfig;
  };

  // Layer 5: 可访问性增强层
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    keyboard_navigation: boolean;
    voice_guidance: boolean;
  };
}

// Quiz包特定覆盖配置
interface QuizPackOverrides {
  id: string;
  user_id: string;
  pack_id: string;

  // 展现覆盖配置
  presentation_overrides: Partial<QuizPresentationConfig>;

  // 覆盖元数据
  override_reason: 'user_preference' | 'accessibility_need' | 'performance_optimization';
  override_priority: number; // 1-10

  // 状态管理
  is_active: boolean;

  // 审计字段
  created_at: string;
  updated_at: string;
}

// Quiz会话配置快照
interface QuizSessionConfig {
  id: string;
  session_id: string;
  user_id: string;
  pack_id: string;

  // 最终合并的展现配置
  final_presentation_config: QuizPresentationConfig;

  // 配置来源追踪
  config_sources: ConfigSources;

  // 配置元数据
  personalization_level: number;
  config_version: string;

  // 审计字段
  created_at: string;
}

interface ConfigSources {
  base_config_id: string; // 用户偏好配置ID
  applied_overrides: string[]; // 应用的覆盖配置ID列表
  applied_defaults: string[]; // 应用的默认配置来源
  merge_timestamp: string;
}
```
