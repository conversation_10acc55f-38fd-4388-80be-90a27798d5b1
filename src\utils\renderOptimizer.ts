/**
 * 渲染优化工具
 * 提供用于优化视图渲染性能的工具函数
 */

import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * 使用防抖的窗口尺寸钩子
 * @param debounceTime 防抖时间（毫秒）
 * @returns 窗口尺寸对象 { width, height }
 */
export const useWindowSize = (debounceTime = 200) => {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  const timeoutRef = useRef<number | null>(null);

  useEffect(() => {
    const handleResize = () => {
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = window.setTimeout(() => {
        setWindowSize({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      }, debounceTime);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }
    };
  }, [debounceTime]);

  return windowSize;
};

/**
 * 使用节流的回调函数
 * @param callback 回调函数
 * @param delay 节流延迟（毫秒）
 * @returns 节流后的回调函数
 */
export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay = 200
) => {
  const lastCallTimeRef = useRef(0);

  return useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCallTimeRef.current >= delay) {
        lastCallTimeRef.current = now;
        return callback(...args);
      }
    },
    [callback, delay]
  );
};

/**
 * 使用防抖的回调函数
 * @param callback 回调函数
 * @param delay 防抖延迟（毫秒）
 * @returns 防抖后的回调函数
 */
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay = 200
) => {
  const timeoutRef = useRef<number | null>(null);

  return useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = window.setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  );
};

/**
 * 使用可见性检测钩子
 * 用于检测元素是否在视口中可见
 * @param options IntersectionObserver 选项
 * @returns [ref, isVisible] 元素引用和可见性状态
 */
export const useVisibility = (options = {}) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsVisible(entry.isIntersecting);
    }, options);

    observer.observe(ref.current);

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [options]);

  return [ref, isVisible] as const;
};

/**
 * 使用帧率限制钩子
 * 用于限制动画帧率，减少不必要的渲染
 * @param fps 目标帧率
 * @returns 是否应该渲染当前帧
 */
export const useFpsLimit = (fps = 30) => {
  const [shouldRender, setShouldRender] = useState(true);
  const lastRenderTimeRef = useRef(0);
  const frameInterval = 1000 / fps;

  useEffect(() => {
    const animationFrameId = requestAnimationFrame(function checkFps() {
      const now = performance.now();
      const elapsed = now - lastRenderTimeRef.current;

      if (elapsed >= frameInterval) {
        lastRenderTimeRef.current = now;
        setShouldRender(true);
      } else {
        setShouldRender(false);
      }

      requestAnimationFrame(checkFps);
    });

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [frameInterval]);

  return shouldRender;
};

/**
 * 使用内存缓存钩子
 * 用于缓存计算结果，避免重复计算
 * @param factory 工厂函数，用于创建缓存值
 * @param deps 依赖数组，当依赖变化时重新计算缓存值
 * @returns 缓存的值
 */
export function useMemoCache<T>(factory: () => T, deps: React.DependencyList): T {
  const cache = useRef<{ value: T; deps: React.DependencyList }>({
    value: undefined as unknown as T,
    deps: [],
  });

  const depsChanged = !deps.every((dep, i) => Object.is(dep, cache.current.deps[i]));

  if (cache.current.value === undefined || depsChanged) {
    cache.current.value = factory();
    cache.current.deps = deps;
  }

  return cache.current.value;
}

/**
 * 使用渲染计数钩子
 * 用于跟踪组件渲染次数，帮助调试性能问题
 * @returns 组件渲染次数
 */
export const useRenderCount = () => {
  const renderCount = useRef(0);

  renderCount.current += 1;

  return renderCount.current;
};
