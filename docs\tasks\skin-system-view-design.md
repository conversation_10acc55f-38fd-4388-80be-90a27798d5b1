# 皮肤系统视图设计

## 概述

本文档详细说明皮肤系统的视图设计，包括视图接口、实现类和工厂模式的应用。

## 视图类型概述

系统支持多种视图类型，每种类型都有不同的布局和交互方式，适用于不同的场景：

1. **轮盘视图（Wheel）**：经典的情绪轮盘，以圆形布局展示情绪。
   - 适用场景：情绪选择、情绪导航、情绪关系展示
   - 特点：直观、层级清晰、空间利用率高
   - 实现方式：D3、SVG、R3F、Canvas

2. **卡片视图（Card）**：以卡片形式展示情绪，适合详细信息展示。
   - 适用场景：情绪详情展示、情绪学习、情绪记录
   - 特点：结构化、信息丰富、交互简单
   - 布局方式：网格、列表、瀑布流

3. **气泡视图（Bubble）**：以浮动气泡形式展示情绪，更加生动活泼。
   - 适用场景：情绪探索、放松应用、儿童情绪教育
   - 特点：动态、有趣、自由度高
   - 布局方式：集群、力导向、随机

4. **列表视图（List）**：以列表形式展示情绪，简洁明了。
   - 适用场景：情绪快速选择、情绪搜索、情绪历史记录
   - 特点：简洁、高效、易于导航
   - 布局方式：垂直、水平

5. **网格视图（Grid）**：以网格形式展示情绪，整齐有序。
   - 适用场景：情绪概览、情绪分类、情绪比较
   - 特点：整齐、规范、易于扫描
   - 布局方式：方形、瀑布流、响应式

6. **星系视图（Galaxy）**：以星系形式展示情绪，创造沉浸式体验。
   - 适用场景：情绪探索、冥想应用、创意表达
   - 特点：沉浸式、视觉震撼、互动性强
   - 布局方式：螺旋、轨道、星云

7. **树视图（Tree）**：以树形结构展示情绪，清晰展示层级关系。
   - 适用场景：情绪分类学习、情绪层级关系展示、情绪分析
   - 特点：层级清晰、结构化、易于理解
   - 布局方式：垂直、水平、径向、思维导图

8. **流程图视图（Flow）**：以流程图形式展示情绪，展示情绪转变过程。
   - 适用场景：情绪转变过程、情绪因果关系、情绪管理策略
   - 特点：过程导向、关系清晰、逻辑性强
   - 布局方式：有向图、力导向、分层、径向

9. **标签云视图（TagCloud）**：以标签云形式展示情绪，突出重要情绪。
   - 适用场景：情绪频率展示、情绪重要性展示、情绪关键词
   - 特点：权重可视化、关键词突出、视觉冲击力强
   - 布局方式：圆形、矩形、三角形、星形、自定义

## 视图接口设计

### 1. 基础视图接口

所有视图类型的共同基础接口：

```typescript
interface EmotionView {
  // 获取视图类型
  getType(): ViewType;

  // 获取内容显示模式
  getContentType(): ContentDisplayMode;

  // 获取皮肤配置
  getSkinConfig(): SkinConfig;

  // 应用皮肤配置
  applySkinConfig(config: SkinConfig): void;

  // 设置内容显示模式
  setContentType(mode: ContentDisplayMode): void;

  // 渲染视图（通用方法）
  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type ViewType = 'wheel' | 'card' | 'bubble' | 'list' | 'grid' | 'galaxy' | 'tree' | 'flow' | 'tagCloud';
type ContentDisplayMode = 'text' | 'emoji' | 'textEmoji';
type EmotionTierLevel = 1 | 2 | 3; // 1=主要，2=次要，3=细分
```

### 2. 特定视图接口

各种视图类型的特定接口，扩展基础接口：

```typescript
// 轮盘视图接口
interface WheelView extends EmotionView {
  // 获取轮盘实现类型
  getImplementation(): RenderEngine;

  // 设置轮盘实现类型
  setImplementation(implementation: RenderEngine): void;

  // 渲染轮盘
  renderWheel(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type RenderEngine = 'D3' | 'SVG' | 'R3F' | 'Canvas';

// 卡片视图接口
interface CardView extends EmotionView {
  // 获取卡片布局
  getLayout(): CardLayout;

  // 设置卡片布局
  setLayout(layout: CardLayout): void;

  // 渲染卡片
  renderCards(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type CardLayout = 'grid' | 'list' | 'masonry';

// 气泡视图接口
interface BubbleView extends EmotionView {
  // 获取气泡布局
  getLayout(): BubbleLayout;

  // 设置气泡布局
  setLayout(layout: BubbleLayout): void;

  // 渲染气泡
  renderBubbles(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type BubbleLayout = 'cluster' | 'force' | 'random';

// 星系视图接口
interface GalaxyView extends EmotionView {
  // 获取星系布局
  getLayout(): GalaxyLayout;

  // 设置星系布局
  setLayout(layout: GalaxyLayout): void;

  // 渲染星系
  renderGalaxy(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type GalaxyLayout = 'spiral' | 'orbital' | 'nebula';

// 树视图接口
interface TreeView extends EmotionView {
  // 获取树布局
  getLayout(): TreeLayout;

  // 设置树布局
  setLayout(layout: TreeLayout): void;

  // 渲染树
  renderTree(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type TreeLayout = 'vertical' | 'horizontal' | 'radial' | 'mindmap';

// 流程图视图接口
interface FlowView extends EmotionView {
  // 获取流程图布局
  getLayout(): FlowLayout;

  // 设置流程图布局
  setLayout(layout: FlowLayout): void;

  // 渲染流程图
  renderFlow(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type FlowLayout = 'dagre' | 'force' | 'layered' | 'radial';

// 标签云视图接口
interface TagCloudView extends EmotionView {
  // 获取标签云布局
  getLayout(): TagCloudLayout;

  // 设置标签云布局
  setLayout(layout: TagCloudLayout): void;

  // 渲染标签云
  renderTagCloud(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type TagCloudLayout = 'circle' | 'rectangle' | 'triangle' | 'star' | 'custom';

// 列表视图接口
interface ListView extends EmotionView {
  // 获取列表布局
  getLayout(): ListLayout;

  // 设置列表布局
  setLayout(layout: ListLayout): void;

  // 渲染列表
  renderList(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type ListLayout = 'vertical' | 'horizontal';

// 网格视图接口
interface GridView extends EmotionView {
  // 获取网格布局
  getLayout(): GridLayout;

  // 设置网格布局
  setLayout(layout: GridLayout): void;

  // 渲染网格
  renderGrid(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;
}

type GridLayout = 'square' | 'masonry' | 'responsive';
```

## 视图工厂设计

使用工厂模式创建不同类型的视图：

```typescript
class ViewFactory {
  // 创建视图
  static createView(
    type: ViewType,
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    options?: any
  ): EmotionView {
    switch (type) {
      case 'wheel':
        return this.createWheel(
          options?.implementation || 'D3',
          contentType,
          skinConfig
        );
      case 'card':
        return this.createCard(
          contentType,
          skinConfig,
          options?.layout
        );
      case 'bubble':
        return this.createBubble(
          contentType,
          skinConfig,
          options?.layout
        );
      case 'galaxy':
        return this.createGalaxy(
          contentType,
          skinConfig,
          options?.layout
        );
      case 'tree':
        return this.createTree(
          contentType,
          skinConfig,
          options?.layout
        );
      case 'flow':
        return this.createFlow(
          contentType,
          skinConfig,
          options?.layout
        );
      case 'tagCloud':
        return this.createTagCloud(
          contentType,
          skinConfig,
          options?.layout
        );
      case 'list':
        return this.createList(
          contentType,
          skinConfig,
          options?.layout
        );
      case 'grid':
        return this.createGrid(
          contentType,
          skinConfig,
          options?.layout
        );
      default:
        return this.createWheel('D3', contentType, skinConfig);
    }
  }

  // 创建轮盘视图
  static createWheel(
    implementation: RenderEngine,
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig
  ): WheelView {
    switch (implementation) {
      case 'D3':
        return new D3WheelView(contentType, skinConfig);
      case 'SVG':
        return new SVGWheelView(contentType, skinConfig);
      case 'R3F':
        return new R3FWheelView(contentType, skinConfig);
      case 'Canvas':
        return new CanvasWheelView(contentType, skinConfig);
      default:
        return new D3WheelView(contentType, skinConfig);
    }
  }

  // 创建卡片视图
  static createCard(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: CardLayout = 'grid'
  ): CardView {
    return new CardView(contentType, skinConfig, layout);
  }

  // 创建气泡视图
  static createBubble(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: BubbleLayout = 'cluster'
  ): BubbleView {
    return new BubbleView(contentType, skinConfig, layout);
  }

  // 创建星系视图
  static createGalaxy(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: GalaxyLayout = 'spiral'
  ): GalaxyView {
    return new GalaxyView(contentType, skinConfig, layout);
  }

  // 创建树视图
  static createTree(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: string = 'vertical'
  ): TreeView {
    return new TreeView(contentType, skinConfig, layout);
  }

  // 创建流程图视图
  static createFlow(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: string = 'dagre'
  ): FlowView {
    return new FlowView(contentType, skinConfig, layout);
  }

  // 创建标签云视图
  static createTagCloud(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: string = 'circle'
  ): TagCloudView {
    return new TagCloudView(contentType, skinConfig, layout);
  }

  // 创建列表视图
  static createList(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: string = 'vertical'
  ): ListView {
    return new ListView(contentType, skinConfig, layout);
  }

  // 创建网格视图
  static createGrid(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: string = 'square'
  ): GridView {
    return new GridView(contentType, skinConfig, layout);
  }

  // 注册自定义视图类型
  static registerCustomView(
    type: string,
    creator: (contentType: ContentDisplayMode, skinConfig: SkinConfig, options?: any) => EmotionView
  ): void {
    // 实现自定义视图类型注册...
  }
}
```

## 视图实现类

### 1. 基础视图实现

```typescript
// 抽象基类
abstract class BaseEmotionView implements EmotionView {
  protected type: ViewType;
  protected contentType: ContentDisplayMode;
  protected skinConfig: SkinConfig;

  constructor(type: ViewType, contentType: ContentDisplayMode, skinConfig: SkinConfig) {
    this.type = type;
    this.contentType = contentType;
    this.skinConfig = skinConfig;
  }

  getType(): ViewType {
    return this.type;
  }

  getContentType(): ContentDisplayMode {
    return this.contentType;
  }

  getSkinConfig(): SkinConfig {
    return this.skinConfig;
  }

  applySkinConfig(config: SkinConfig): void {
    this.skinConfig = config;
  }

  setContentType(mode: ContentDisplayMode): void {
    this.contentType = mode;
  }

  abstract render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode;

  // 辅助方法
  protected getEmotionContent(emotion: Emotion): { text: string, emoji: string } {
    const text = emotion.name;
    const emoji = emotion.emoji;

    switch (this.contentType) {
      case 'text':
        return { text, emoji: '' };
      case 'emoji':
        return { text: '', emoji };
      case 'textEmoji':
      default:
        return { text, emoji };
    }
  }

  // 辅助方法
  protected getColorForEmotion(emotion: Emotion, tierType: EmotionTierLevel): string {
    // 如果情绪有自定义颜色，优先使用
    if (emotion.color) {
      return emotion.color;
    }

    // 否则从皮肤配置中获取颜色
    const colors = this.skinConfig.colors;

    // 根据层级类型选择不同的颜色策略
    switch (tierType) {
      case 1:
        return colors.primary;
      case 2:
        return colors.secondary;
      case 3:
        return colors.accent;
      default:
        return colors.primary;
    }
  }
}
```

### 2. 轮盘视图实现

```typescript
// D3 轮盘视图
class D3WheelView extends BaseEmotionView implements WheelView {
  private implementation: RenderEngine = 'D3';

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig) {
    super('wheel', contentType, skinConfig);
  }

  getImplementation(): RenderEngine {
    return this.implementation;
  }

  setImplementation(implementation: RenderEngine): void {
    this.implementation = implementation;
  }

  renderWheel(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现 D3 轮盘渲染...
    return <D3WheelComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderWheel(emotions, tierType, onSelect);
  }
}

// SVG 轮盘视图
class SVGWheelView extends BaseEmotionView implements WheelView {
  // 实现...
}

// R3F 轮盘视图
class R3FWheelView extends BaseEmotionView implements WheelView {
  // 实现...
}
```

### 3. 卡片视图实现

```typescript
class CardView extends BaseEmotionView implements CardView {
  private layout: CardLayout;

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig, layout: CardLayout = 'grid') {
    super('card', contentType, skinConfig);
    this.layout = layout;
  }

  getLayout(): CardLayout {
    return this.layout;
  }

  setLayout(layout: CardLayout): void {
    this.layout = layout;
  }

  renderCards(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现卡片渲染...
    return <CardComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      layout={this.layout}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderCards(emotions, tierType, onSelect);
  }
}
```

### 4. 气泡视图实现

```typescript
class BubbleView extends BaseEmotionView implements BubbleView {
  private layout: BubbleLayout;

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig, layout: BubbleLayout = 'cluster') {
    super('bubble', contentType, skinConfig);
    this.layout = layout;
  }

  getLayout(): BubbleLayout {
    return this.layout;
  }

  setLayout(layout: BubbleLayout): void {
    this.layout = layout;
  }

  renderBubbles(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现气泡渲染...
    return <BubbleComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      layout={this.layout}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderBubbles(emotions, tierType, onSelect);
  }
}
```

### 5. 星系视图实现

```typescript
class GalaxyView extends BaseEmotionView implements GalaxyView {
  private layout: GalaxyLayout;

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig, layout: GalaxyLayout = 'spiral') {
    super('galaxy', contentType, skinConfig);
    this.layout = layout;
  }

  getLayout(): GalaxyLayout {
    return this.layout;
  }

  setLayout(layout: GalaxyLayout): void {
    this.layout = layout;
  }

  renderGalaxy(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现星系渲染...
    return <GalaxyComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      layout={this.layout}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderGalaxy(emotions, tierType, onSelect);
  }
}
```

### 6. 树视图实现

```typescript
class TreeView extends BaseEmotionView implements TreeView {
  private layout: TreeLayout;

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig, layout: TreeLayout = 'vertical') {
    super('tree', contentType, skinConfig);
    this.layout = layout;
  }

  getLayout(): TreeLayout {
    return this.layout;
  }

  setLayout(layout: TreeLayout): void {
    this.layout = layout;
  }

  renderTree(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现树渲染...
    return <TreeComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      layout={this.layout}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderTree(emotions, tierType, onSelect);
  }
}
```

### 7. 流程图视图实现

```typescript
class FlowView extends BaseEmotionView implements FlowView {
  private layout: FlowLayout;

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig, layout: FlowLayout = 'dagre') {
    super('flow', contentType, skinConfig);
    this.layout = layout;
  }

  getLayout(): FlowLayout {
    return this.layout;
  }

  setLayout(layout: FlowLayout): void {
    this.layout = layout;
  }

  renderFlow(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现流程图渲染...
    return <FlowComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      layout={this.layout}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderFlow(emotions, tierType, onSelect);
  }
}
```

### 8. 标签云视图实现

```typescript
class TagCloudView extends BaseEmotionView implements TagCloudView {
  private layout: TagCloudLayout;

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig, layout: TagCloudLayout = 'circle') {
    super('tagCloud', contentType, skinConfig);
    this.layout = layout;
  }

  getLayout(): TagCloudLayout {
    return this.layout;
  }

  setLayout(layout: TagCloudLayout): void {
    this.layout = layout;
  }

  renderTagCloud(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现标签云渲染...
    return <TagCloudComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      layout={this.layout}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderTagCloud(emotions, tierType, onSelect);
  }
}
```

### 9. 列表视图实现

```typescript
class ListView extends BaseEmotionView implements ListView {
  private layout: ListLayout;

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig, layout: ListLayout = 'vertical') {
    super('list', contentType, skinConfig);
    this.layout = layout;
  }

  getLayout(): ListLayout {
    return this.layout;
  }

  setLayout(layout: ListLayout): void {
    this.layout = layout;
  }

  renderList(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现列表渲染...
    return <ListComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      layout={this.layout}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderList(emotions, tierType, onSelect);
  }
}
```

### 10. 网格视图实现

```typescript
class GridView extends BaseEmotionView implements GridView {
  private layout: GridLayout;

  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig, layout: GridLayout = 'square') {
    super('grid', contentType, skinConfig);
    this.layout = layout;
  }

  getLayout(): GridLayout {
    return this.layout;
  }

  setLayout(layout: GridLayout): void {
    this.layout = layout;
  }

  renderGrid(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    // 实现网格渲染...
    return <GridComponent
      emotions={emotions}
      tierType={tierType}
      contentType={this.contentType}
      skinConfig={this.skinConfig}
      layout={this.layout}
      onSelect={onSelect}
    />;
  }

  render(
    emotions: Emotion[],
    tierType: EmotionTierLevel,
    onSelect: (emotion: Emotion) => void
  ): React.ReactNode {
    return this.renderGrid(emotions, tierType, onSelect);
  }
}
```

## 视图组件设计

### 1. 通用预览组件

```typescript
interface SkinPreviewProps {
  emotionData: EmotionData;
  skin: Skin;
  viewType?: ViewType;
  contentDisplayMode?: ContentDisplayMode;
  RenderEngine?: RenderEngine;
  onViewTypeChange?: (type: ViewType) => void;
  onContentDisplayModeChange?: (mode: ContentDisplayMode) => void;
  onRenderEngineChange?: (implementation: RenderEngine) => void;
}

const SkinPreview: React.FC<SkinPreviewProps> = ({
  emotionData,
  skin,
  viewType = 'wheel',
  contentDisplayMode = 'textEmoji',
  RenderEngine = 'D3',
  onViewTypeChange,
  onContentDisplayModeChange,
  onRenderEngineChange
}) => {
  // 实现...
}
```

### 2. 视图选择组件

```typescript
interface ViewTypeSelectorProps {
  viewType: ViewType;
  onChange: (type: ViewType) => void;
}

const ViewTypeSelector: React.FC<ViewTypeSelectorProps> = ({
  viewType,
  onChange
}) => {
  // 实现...
}
```

## 扩展性考虑

1. **新视图类型**：
   - 创建新的视图接口，扩展 EmotionView
   - 实现新的视图类
   - 在 ViewFactory 中添加创建方法
   - 更新 ViewType 类型定义

2. **新的轮盘实现**：
   - 实现新的 WheelView 子类
   - 在 ViewFactory.createWheel 中添加新的 case

3. **自定义视图注册**：
   - 使用 ViewFactory.registerCustomView 注册自定义视图
   - 允许插件和扩展添加新的视图类型

4. **视图配置扩展**：
   - 在 SkinConfig 中添加新的视图特定配置
   - 确保视图实现类能够处理新的配置选项

## 气泡视图皮肤实现指南

气泡视图是一种生动活泼的情绪展示方式，适合用于情绪探索、放松应用和儿童情绪教育等场景。以下是气泡视图皮肤的实现指南：

### 1. 气泡视图的特点

- **动态性**：气泡可以浮动、碰撞、聚集，创造生动的视觉效果
- **自由度**：气泡可以自由分布，不受严格的结构限制
- **趣味性**：适合添加有趣的动画和交互效果
- **适应性**：可以根据情绪的重要性或频率调整气泡大小

### 2. 气泡视图的布局方式

1. **集群布局（Cluster）**：
   - 气泡根据情绪类别聚集在一起
   - 同类情绪的气泡颜色相近或形状相似
   - 适合展示情绪分类和关系

2. **力导向布局（Force）**：
   - 气泡之间有引力和斥力
   - 可以拖动气泡，其他气泡会相应调整位置
   - 适合交互式探索和动态展示

3. **随机布局（Random）**：
   - 气泡随机分布在视图中
   - 可以添加浮动动画增加生动性
   - 适合轻松、放松的应用场景

### 3. 气泡视图的视觉设计

1. **气泡形状**：
   - 圆形：最常见，柔和自然
   - 椭圆形：可以用于区分不同类别
   - 圆角矩形：适合包含更多文本信息
   - 自定义形状：可以根据情绪特点设计特殊形状

2. **气泡颜色**：
   - 使用渐变色增加视觉层次
   - 颜色可以反映情绪的性质（如暖色调表示积极情绪）
   - 透明度可以表示情绪的强度

3. **气泡大小**：
   - 可以根据情绪的重要性或频率调整大小
   - 设置最小和最大尺寸限制，确保可读性
   - 考虑不同设备屏幕大小的适配

### 4. 气泡视图的交互设计

1. **悬停效果**：
   - 放大：突出当前气泡
   - 发光：添加光晕效果
   - 显示详情：显示情绪的详细描述

2. **点击效果**：
   - 选中状态：改变边框或背景颜色
   - 弹出详情：显示情绪的详细信息和建议
   - 动画反馈：添加选中动画增强用户体验

3. **拖拽交互**：
   - 允许用户拖动气泡
   - 可以实现气泡碰撞物理效果
   - 考虑添加吸附效果便于组织

### 5. 气泡视图的动画效果

1. **入场动画**：
   - 淡入：气泡逐渐显现
   - 弹出：气泡从小变大
   - 飘入：气泡从屏幕外飘入

2. **持续动画**：
   - 浮动：气泡轻微上下或左右浮动
   - 脉动：气泡大小轻微变化
   - 旋转：气泡缓慢旋转

3. **交互动画**：
   - 碰撞：气泡之间的物理碰撞效果
   - 合并：相似情绪的气泡可以合并
   - 分裂：复杂情绪可以分裂为更具体的情绪

### 6. 气泡视图的实现技术

1. **Canvas 实现**：
   - 适合大量气泡的高性能渲染
   - 可以实现复杂的物理效果
   - 适合自定义形状和动画

2. **SVG 实现**：
   - 适合需要精确控制的场景
   - 便于实现复杂的交互效果
   - 适合需要高清晰度的场景

3. **CSS 实现**：
   - 适合简单的气泡效果
   - 利用 CSS 动画实现基本动效
   - 适合轻量级应用

### 7. 气泡视图的性能优化

1. **数量控制**：
   - 限制同时显示的气泡数量
   - 可以实现分页或滚动加载

2. **渲染优化**：
   - 使用 requestAnimationFrame 优化动画
   - 考虑使用 Web Workers 处理复杂计算
   - 实现视口裁剪，只渲染可见区域

3. **交互优化**：
   - 添加节流或防抖处理用户输入
   - 优化碰撞检测算法
   - 考虑使用空间分区技术提高性能

### 8. 气泡视图的响应式设计

1. **屏幕适配**：
   - 在小屏幕上减少气泡数量
   - 调整气泡大小适应不同屏幕
   - 考虑不同的布局策略

2. **触摸优化**：
   - 增大触摸目标区域
   - 优化拖拽体验
   - 添加适合触摸的交互反馈

### 9. 气泡视图的辅助功能

1. **无障碍设计**：
   - 确保颜色对比度符合标准
   - 添加适当的 ARIA 标签
   - 支持键盘导航

2. **辅助工具**：
   - 添加搜索或过滤功能
   - 提供分类或分组视图
   - 考虑添加缩放或重置功能

### 10. 气泡视图的实现示例

```typescript
// 气泡组件示例
const BubbleComponent: React.FC<{
  emotions: Emotion[];
  tierType: EmotionTierLevel;
  contentType: ContentDisplayMode;
  skinConfig: SkinConfig;
  layout: BubbleLayout;
  onSelect: (emotion: Emotion) => void;
}> = ({ emotions, tierType, contentType, skinConfig, layout, onSelect }) => {
  // 获取气泡视图配置
  const bubbleConfig = skinConfig.view_configs?.bubble || {};

  // 设置气泡大小
  const bubbleSize = bubbleConfig.bubbleSize || 60;

  // 设置气泡间距
  const bubbleSpacing = bubbleConfig.bubbleSpacing || 10;

  // 设置气泡颜色
  const getBubbleColor = (emotion: Emotion, index: number) => {
    if (emotion.color) return emotion.color;

    const bubbleColors = bubbleConfig.bubbleColors || [
      skinConfig.colors.primary,
      skinConfig.colors.secondary,
      skinConfig.colors.accent
    ];

    return bubbleColors[index % bubbleColors.length];
  };

  // 根据布局类型设置气泡位置
  const getBubblePosition = (index: number, total: number) => {
    switch (layout) {
      case 'cluster':
        // 集群布局实现...
        return { x: /* 计算 x 坐标 */, y: /* 计算 y 坐标 */ };

      case 'force':
        // 力导向布局实现...
        return { x: /* 计算 x 坐标 */, y: /* 计算 y 坐标 */ };

      case 'random':
      default:
        // 随机布局实现...
        return { x: /* 计算 x 坐标 */, y: /* 计算 y 坐标 */ };
    }
  };

  // 渲染气泡
  return (
    <div className="bubble-container">
      {emotions.map((emotion, index) => {
        const { x, y } = getBubblePosition(index, emotions.length);
        const color = getBubbleColor(emotion, index);

        return (
          <div
            key={emotion.id}
            className="bubble"
            style={{
              width: `${bubbleSize}px`,
              height: `${bubbleSize}px`,
              backgroundColor: color,
              borderRadius: '50%',
              position: 'absolute',
              left: `${x}px`,
              top: `${y}px`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'transform 0.3s ease, box-shadow 0.3s ease',
              boxShadow: bubbleConfig.bubbleShadow ? '0 4px 8px rgba(0,0,0,0.2)' : 'none',
              // 添加更多样式...
            }}
            onClick={() => onSelect(emotion)}
          >
            {contentType === 'emoji' || contentType === 'textEmoji' ? (
              <span className="bubble-emoji" style={{ fontSize: `${bubbleSize * 0.5}px` }}>
                {emotion.emoji}
              </span>
            ) : null}

            {contentType === 'text' || contentType === 'textEmoji' ? (
              <span className="bubble-text" style={{
                fontSize: `${bubbleSize * 0.2}px`,
                color: bubbleConfig.textColor || skinConfig.colors.text
              }}>
                {emotion.name}
              </span>
            ) : null}
          </div>
        );
      })}
    </div>
  );
};
```

通过以上指南，开发者可以创建丰富多样的气泡视图皮肤，为用户提供生动有趣的情绪选择体验。
