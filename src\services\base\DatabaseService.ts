/**
 * 数据库服务基类
 * 整合四个核心SQLite服务的最佳实践
 * 提供数据库连接管理、事务支持和完整的初始化流程
 */

import {
  CapacitorSQLite,
  SQLiteConnection,
  type SQLiteDBConnection,
  type capSQLiteUpgradeOptions,
} from '@capacitor-community/sqlite';
import { Capacitor } from '@capacitor/core';
import { BehaviorSubject } from 'rxjs';
import type { DatabaseContext, TransactionManager } from '../types/ServiceTypes';

// 数据库升级语句接口
export interface DatabaseUpgradeStatement {
  toVersion: number;
  statements: string[];
}

// 数据库版本服务接口
export interface IDbVersionService {
  setDbVersion(dbName: string, version: number): void;
  getDbVersion(dbName: string): number | undefined;
  getAllVersions(): Map<string, number>;
  clearVersion(dbName: string): void;
  hasVersion(dbName: string): boolean;
}

// SQLite服务接口
export interface ISQLiteService {
  getPlatform(): string;
  initWebStore(): Promise<void>;
  addUpgradeStatement(options: capSQLiteUpgradeOptions): Promise<void>;
  openDatabase(
    dbName: string,
    loadToVersion: number,
    readOnly: boolean
  ): Promise<SQLiteDBConnection>;
  closeDatabase(dbName: string, readOnly: boolean): Promise<void>;
  saveToStore(dbName: string): Promise<void>;
  saveToLocalDisk(dbName: string): Promise<void>;
  isConnection(dbName: string, readOnly: boolean): Promise<boolean>;
  checkConnectionsConsistency(): Promise<boolean>;
  retrieveConnection(dbName: string, readOnly: boolean): Promise<SQLiteDBConnection>;
}

/**
 * 整合的数据库服务 - 包含四个核心服务的功能
 */
export class DatabaseService implements TransactionManager {
  private static instance: DatabaseService;
  private dbConnection: SQLiteDBConnection | null = null;

  // SQLite核心服务
  private platform = Capacitor.getPlatform();
  private sqlitePlugin = CapacitorSQLite;
  private sqliteConnection = new SQLiteConnection(CapacitorSQLite);
  private dbNameVersionDict: Map<string, number> = new Map();

  // 数据库配置
  private dbName = 'mindful_mood_db';
  private dbVersion = 1;
  private upgradeStatements: DatabaseUpgradeStatement[] = [];

  // 状态管理
  private isInitialized = false;
  private appInit = false;
  public isInitCompleted = new BehaviorSubject<boolean>(false);

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // ==================== SQLite服务功能 ====================

  /**
   * 获取平台信息
   */
  getPlatform(): string {
    return this.platform;
  }

  /**
   * 初始化Web存储
   */
  async initWebStore(): Promise<void> {
    try {
      await this.sqliteConnection.initWebStore();
      console.log('[DatabaseService] Web store initialized successfully');
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.initWebStore: ${msg}`);
    }
  }

  /**
   * 添加升级语句
   */
  async addUpgradeStatement(options: capSQLiteUpgradeOptions): Promise<void> {
    try {
      await this.sqlitePlugin.addUpgradeStatement(options);
      console.log(`[DatabaseService] Upgrade statements added for database: ${options.database}`);
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.addUpgradeStatement: ${msg}`);
    }
  }

  /**
   * 打开数据库连接
   */
  async openDatabase(
    dbName: string,
    loadToVersion: number,
    readOnly: boolean
  ): Promise<SQLiteDBConnection> {
    this.dbNameVersionDict.set(dbName, loadToVersion);
    const encrypted = false;
    const mode = encrypted ? 'secret' : 'no-encryption';

    try {
      let db: SQLiteDBConnection;
      const retCC = await this.checkConnectionsConsistency();
      const isConn = await this.isConnection(dbName, readOnly);

      if (retCC && isConn) {
        console.log(`[DatabaseService] Retrieving existing connection for ${dbName}`);
        db = await this.retrieveConnection(dbName, readOnly);
      } else {
        console.log(`[DatabaseService] Creating new connection for ${dbName}`);
        db = await this.sqliteConnection.createConnection(
          dbName,
          encrypted,
          mode,
          loadToVersion,
          readOnly
        );
      }

      // Web平台特殊处理
      if (this.platform === 'web') {
        const jeepSQLiteEl = document.querySelector('jeep-sqlite');
        if (!jeepSQLiteEl) {
          console.warn('[DatabaseService] jeep-sqlite element not found');
        }
      }

      await db.open();
      const isOpen = await db.isDBOpen();

      if (!isOpen.result) {
        throw new Error('Failed to open database');
      }

      console.log(`[DatabaseService] Database ${dbName} opened successfully`);
      return db;
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.openDatabase: ${msg}`);
    }
  }

  /**
   * 检查连接是否存在
   */
  async isConnection(dbName: string, readOnly: boolean): Promise<boolean> {
    try {
      const result = await this.sqliteConnection.isConnection(dbName, readOnly);
      return result.result !== undefined ? result.result : false;
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.isConnection: ${msg}`);
    }
  }

  /**
   * 检查连接一致性
   */
  async checkConnectionsConsistency(): Promise<boolean> {
    try {
      const result = await this.sqliteConnection.checkConnectionsConsistency();
      return result.result || false;
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.checkConnectionsConsistency: ${msg}`);
    }
  }

  /**
   * 获取现有连接
   */
  async retrieveConnection(dbName: string, readOnly: boolean): Promise<SQLiteDBConnection> {
    try {
      return await this.sqliteConnection.retrieveConnection(dbName, readOnly);
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.retrieveConnection: ${msg}`);
    }
  }

  /**
   * 保存到存储
   */
  async saveToStore(dbName: string): Promise<void> {
    try {
      await this.sqliteConnection.saveToStore(dbName);
      console.log(`[DatabaseService] Database ${dbName} saved to store`);
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.saveToStore: ${msg}`);
    }
  }

  /**
   * 保存到本地磁盘
   */
  async saveToLocalDisk(dbName: string): Promise<void> {
    try {
      await this.sqliteConnection.saveToLocalDisk(dbName);
      console.log(`[DatabaseService] Database ${dbName} saved to local disk`);
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.saveToLocalDisk: ${msg}`);
    }
  }

  /**
   * 关闭数据库连接
   */
  async closeDatabase(dbName: string, readOnly: boolean): Promise<void> {
    try {
      const isConn = await this.isConnection(dbName, readOnly);
      if (isConn) {
        await this.sqliteConnection.closeConnection(dbName, readOnly);
        console.log(`[DatabaseService] Database ${dbName} closed successfully`);
      }
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      throw new Error(`DatabaseService.closeDatabase: ${msg}`);
    }
  }

  // ==================== 版本服务功能 ====================

  /**
   * 设置数据库版本
   */
  setDbVersion(dbName: string, version: number): void {
    this.dbNameVersionDict.set(dbName, version);
    console.log(`[DatabaseService] Set version for ${dbName}: ${version}`);
  }

  /**
   * 获取数据库版本
   */
  getDbVersion(dbName: string): number | undefined {
    const version = this.dbNameVersionDict.get(dbName);
    console.log(`[DatabaseService] Get version for ${dbName}: ${version}`);
    return version;
  }

  /**
   * 获取所有数据库版本
   */
  getAllVersions(): Map<string, number> {
    return new Map(this.dbNameVersionDict);
  }

  /**
   * 清除数据库版本记录
   */
  clearVersion(dbName: string): void {
    const existed = this.dbNameVersionDict.delete(dbName);
    if (existed) {
      console.log(`[DatabaseService] Cleared version for ${dbName}`);
    }
  }

  /**
   * 检查是否有版本记录
   */
  hasVersion(dbName: string): boolean {
    return this.dbNameVersionDict.has(dbName);
  }

  // ==================== 应用初始化功能 ====================

  /**
   * 初始化应用数据库
   */
  async initializeApp(): Promise<boolean> {
    if (this.appInit) {
      console.log('[DatabaseService] App already initialized');
      return this.appInit;
    }

    try {
      console.log('[DatabaseService] Starting app initialization...');

      // 1. Web平台特殊处理
      if (this.platform === 'web') {
        console.log('[DatabaseService] Web platform detected, initializing web store...');

        // 等待 jeep-sqlite 元素加载
        await this.waitForJeepSQLite();

        // 初始化 Web Store
        await this.initWebStore();
      }

      // 2. 初始化数据库
      console.log('[DatabaseService] Initializing database...');
      await this.initializeDatabase();

      // 3. Web平台保存到存储
      if (this.platform === 'web') {
        console.log('[DatabaseService] Saving database to web store...');
        await this.saveToStore(this.dbName);
      }

      this.appInit = true;
      console.log('[DatabaseService] App initialization completed successfully');

      return this.appInit;
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      console.error('[DatabaseService] App initialization failed:', msg);
      this.appInit = false;
      throw new Error(`DatabaseService.initializeApp: ${msg}`);
    }
  }

  /**
   * 等待 jeep-sqlite 元素加载（Web平台）
   */
  private async waitForJeepSQLite(): Promise<void> {
    if (this.platform !== 'web') {
      return;
    }

    try {
      // 检查元素是否已存在
      const existingElement = document.querySelector('jeep-sqlite');
      if (existingElement) {
        console.log('[DatabaseService] jeep-sqlite element already exists');
        return;
      }

      // 等待自定义元素定义
      await customElements.whenDefined('jeep-sqlite');
      console.log('[DatabaseService] jeep-sqlite element loaded successfully');

      // 额外等待一小段时间确保元素完全初始化
      await new Promise((resolve) => setTimeout(resolve, 100));
    } catch (error) {
      console.warn(
        '[DatabaseService] jeep-sqlite element loading failed, continuing anyway:',
        error
      );
      // 不抛出错误，因为某些情况下可能不需要这个元素
    }
  }

  // ==================== 存储服务功能 ====================

  /**
   * 设置升级语句
   */
  setUpgradeStatements(statements: DatabaseUpgradeStatement[]): void {
    this.upgradeStatements = statements;
    this.dbVersion = statements.length > 0 ? statements[statements.length - 1].toVersion : 1;
  }

  /**
   * 初始化数据库存储
   */
  async initializeDatabase(): Promise<void> {
    if (this.isInitialized) {
      console.log('[DatabaseService] Database already initialized');
      return;
    }

    try {
      console.log('[DatabaseService] Starting database initialization...');

      // 1. 添加升级语句
      if (this.upgradeStatements.length > 0) {
        await this.addUpgradeStatement({
          database: this.dbName,
          upgrade: this.upgradeStatements,
        });
      }

      // 2. 打开数据库连接
      this.dbConnection = await this.openDatabase(this.dbName, this.dbVersion, false);

      // 3. 检查数据库是否需要初始化
      const needsInitialization = await this.checkIfDatabaseNeedsInitialization();

      if (needsInitialization) {
        console.log('[DatabaseService] Database needs initialization, loading schema and data...');
        await this.loadDatabaseSchema();
        await this.loadInitialData();
      } else {
        console.log('[DatabaseService] Database already has data, skipping initialization');
      }

      // 4. 设置版本信息
      this.setDbVersion(this.dbName, this.dbVersion);

      // 5. Web平台保存到存储
      if (this.platform === 'web') {
        await this.saveToStore(this.dbName);
      }

      // 6. 通知初始化完成
      this.isInitialized = true;
      this.isInitCompleted.next(true);
      console.log('[DatabaseService] Database initialization completed successfully');
    } catch (error: any) {
      const msg = error.message ? error.message : error;
      console.error('[DatabaseService] Database initialization failed:', msg);
      this.isInitCompleted.next(false);
      throw new Error(`DatabaseService.initializeDatabase: ${msg}`);
    }
  }

  /**
   * 检查数据库是否需要初始化
   */
  private async checkIfDatabaseNeedsInitialization(): Promise<boolean> {
    if (!this.dbConnection) return true;

    try {
      // 检查是否存在关键表和数据
      const tablesResult = await this.dbConnection.query(
        "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name IN ('users', 'emotions', 'emotion_data_sets')"
      );

      const tableCount = tablesResult.values?.[0]?.count || 0;

      // 如果关键表不存在，需要初始化
      if (tableCount < 3) {
        console.log('[DatabaseService] Key tables missing, initialization needed');
        return true;
      }

      // 检查是否有基础数据
      const usersResult = await this.dbConnection.query('SELECT COUNT(*) as count FROM users');
      const emotionsResult = await this.dbConnection.query(
        'SELECT COUNT(*) as count FROM emotions'
      );
      const emotionDataSetsResult = await this.dbConnection.query(
        'SELECT COUNT(*) as count FROM emotion_data_sets'
      );

      const userCount = usersResult.values?.[0]?.count || 0;
      const emotionCount = emotionsResult.values?.[0]?.count || 0;
      const emotionDataSetCount = emotionDataSetsResult.values?.[0]?.count || 0;

      // 如果没有基础数据，需要初始化
      if (userCount === 0 || emotionCount === 0 || emotionDataSetCount === 0) {
        console.log(
          `[DatabaseService] Missing data found - users: ${userCount}, emotions: ${emotionCount}, emotion_data_sets: ${emotionDataSetCount}, initialization needed`
        );
        return true;
      }

      console.log('[DatabaseService] Database has existing data, no initialization needed');
      return false;
    } catch (error) {
      console.log(
        '[DatabaseService] Error checking database state, assuming initialization needed:',
        error
      );
      return true;
    }
  }

  /**
   * 加载数据库架构
   */
  private async loadDatabaseSchema(): Promise<void> {
    if (!this.dbConnection) throw new Error('Database connection not available');

    try {
      console.log('[DatabaseService] Loading database schema from full.sql...');

      // 加载 full.sql 文件
      const schemaResponse = await fetch('/seeds/schema/full.sql');
      if (!schemaResponse.ok) {
        throw new Error(`Failed to load schema file: ${schemaResponse.statusText}`);
      }

      const schemaSQL = await schemaResponse.text();

      // 执行架构创建
      await this.dbConnection.execute(schemaSQL);

      console.log('[DatabaseService] Database schema loaded successfully');
    } catch (error) {
      console.error('[DatabaseService] Failed to load database schema:', error);
      throw error;
    }
  }

  /**
   * 加载初始数据
   */
  private async loadInitialData(): Promise<void> {
    if (!this.dbConnection) throw new Error('Database connection not available');

    try {
      console.log('[DatabaseService] Loading initial data following master.sql order...');

      // 按照 master.sql 中定义的顺序加载数据文件
      const dataFiles = [
        '/seeds/schema/init.sql', // 1. 核心数据
        '/seeds/schema/emotion_data_set_tiers.sql', // 2. 情绪数据集层级
        '/seeds/schema/additional_emoji_init.sql', // 3. 额外表情集
        '/seeds/schema/extended_emoji_items.sql', // 4. 扩展表情项
        '/seeds/schema/ui_labels.sql', // 5. UI标签
        '/seeds/schema/ui_label_translations.sql', // 6. UI标签翻译
      ];

      for (const filePath of dataFiles) {
        try {
          console.log(`[DatabaseService] Loading data from ${filePath}...`);

          const response = await fetch(filePath);
          if (!response.ok) {
            console.warn(`[DatabaseService] Could not load ${filePath}: ${response.statusText}`);
            continue; // 继续加载其他文件
          }

          const sql = await response.text();
          if (sql.trim()) {
            await this.dbConnection.execute(sql);
            console.log(`[DatabaseService] Successfully loaded ${filePath}`);
          }
        } catch (error) {
          console.warn(`[DatabaseService] Error loading ${filePath}:`, error);
          // 继续加载其他文件，不中断整个过程
        }
      }

      console.log('[DatabaseService] Initial data loading completed');
    } catch (error) {
      console.error('[DatabaseService] Failed to load initial data:', error);
      throw error;
    }
  }

  /**
   * 设置数据库连接
   */
  setConnection(connection: SQLiteDBConnection): void {
    this.dbConnection = connection;
  }

  /**
   * 获取数据库连接
   */
  async getConnection(): Promise<SQLiteDBConnection> {
    if (!this.dbConnection) {
      throw new Error('Database connection not available. Please initialize the database first.');
    }
    return this.dbConnection;
  }

  /**
   * 创建数据库上下文
   */
  async createContext(userId?: string, transaction?: boolean): Promise<DatabaseContext> {
    const db = await this.getConnection();
    return {
      db,
      connection: db, // 为了兼容性，同时设置 connection 属性
      userId,
      transaction,
    };
  }

  /**
   * 执行事务
   */
  async execute<T>(
    db: SQLiteDBConnection,
    operation: (context: DatabaseContext) => Promise<T>
  ): Promise<T> {
    const context: DatabaseContext = {
      db,
      connection: db, // 为了兼容性，同时设置 connection 属性
      transaction: true,
    };

    try {
      // 开始事务
      await db.execute('BEGIN TRANSACTION');

      // 执行操作
      const result = await operation(context);

      // 提交事务
      await db.execute('COMMIT');

      return result;
    } catch (error) {
      // 回滚事务
      try {
        await db.execute('ROLLBACK');
      } catch (rollbackError) {
        console.error('Failed to rollback transaction:', rollbackError);
      }
      throw error;
    }
  }

  /**
   * 执行带重试的操作
   */
  async executeWithRetry<T>(operation: () => Promise<T>, maxRetries = 3, delay = 1000): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries) {
          break;
        }

        // 等待后重试
        await this.sleep(delay * attempt);
      }
    }

    throw lastError!;
  }

  /**
   * 检查数据库连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      const db = await this.getConnection();
      await db.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('Database connection check failed:', error);
      return false;
    }
  }

  /**
   * 获取数据库版本
   */
  async getDatabaseVersion(): Promise<number> {
    try {
      const db = await this.getConnection();
      const result = await db.query('PRAGMA user_version');
      return result.values?.[0]?.user_version || 0;
    } catch (error) {
      console.error('Failed to get database version:', error);
      return 0;
    }
  }

  /**
   * 设置数据库版本
   */
  async setDatabaseVersion(version: number): Promise<void> {
    try {
      const db = await this.getConnection();
      await db.execute(`PRAGMA user_version = ${version}`);
    } catch (error) {
      console.error('Failed to set database version:', error);
      throw error;
    }
  }

  /**
   * 执行数据库迁移
   */
  async runMigrations(migrations: Array<{ version: number; sql: string }>): Promise<void> {
    const currentVersion = await this.getDatabaseVersion();
    const db = await this.getConnection();

    // 过滤出需要执行的迁移
    const pendingMigrations = migrations.filter((m) => m.version > currentVersion);

    if (pendingMigrations.length === 0) {
      return;
    }

    // 按版本号排序
    pendingMigrations.sort((a, b) => a.version - b.version);

    try {
      await db.execute('BEGIN TRANSACTION');

      for (const migration of pendingMigrations) {
        console.log(`Running migration version ${migration.version}`);
        await db.execute(migration.sql);
        await this.setDatabaseVersion(migration.version);
      }

      await db.execute('COMMIT');
      console.log(
        `Migrations completed. Database version: ${pendingMigrations[pendingMigrations.length - 1].version}`
      );
    } catch (error) {
      await db.execute('ROLLBACK');
      console.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * 备份数据库
   */
  async backup(backupPath: string): Promise<void> {
    try {
      const db = await this.getConnection();
      // 这里需要根据实际的SQLite插件API来实现备份功能
      // 示例代码，具体实现可能需要调整
      console.log(`Backing up database to ${backupPath}`);
      // await db.backup(backupPath);
    } catch (error) {
      console.error('Database backup failed:', error);
      throw error;
    }
  }

  /**
   * 恢复数据库
   */
  async restore(backupPath: string): Promise<void> {
    try {
      // 这里需要根据实际的SQLite插件API来实现恢复功能
      console.log(`Restoring database from ${backupPath}`);
      // 重新初始化连接
      this.dbConnection = null;
    } catch (error) {
      console.error('Database restore failed:', error);
      throw error;
    }
  }

  /**
   * 清理数据库连接
   */
  async cleanup(): Promise<void> {
    if (this.dbConnection) {
      try {
        // 关闭SQLite连接
        await this.closeDatabase(this.dbName, false);
        this.dbConnection = null;
        this.isInitialized = false;
        this.appInit = false;
        this.isInitCompleted.next(false);
        console.log('[DatabaseService] Database connection cleaned up successfully');
      } catch (error) {
        console.error('[DatabaseService] Failed to cleanup database connection:', error);
      }
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStats(): Promise<{
    size: number;
    tableCount: number;
    recordCount: number;
  }> {
    try {
      const db = await this.getConnection();

      // 获取表数量
      const tablesResult = await db.query(
        "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'"
      );
      const tableCount = tablesResult.values?.[0]?.count || 0;

      // 获取总记录数（这是一个简化的实现）
      let recordCount = 0;
      const tables = await db.query("SELECT name FROM sqlite_master WHERE type='table'");

      if (tables.values) {
        for (const table of tables.values) {
          const countResult = await db.query(`SELECT COUNT(*) as count FROM ${table.name}`);
          recordCount += countResult.values?.[0]?.count || 0;
        }
      }

      return {
        size: 0, // 需要根据实际API获取文件大小
        tableCount,
        recordCount,
      };
    } catch (error) {
      console.error('Failed to get database stats:', error);
      throw error;
    }
  }

  /**
   * 优化数据库
   */
  async optimize(): Promise<void> {
    try {
      const db = await this.getConnection();
      await db.execute('VACUUM');
      await db.execute('ANALYZE');
      console.log('Database optimization completed');
    } catch (error) {
      console.error('Database optimization failed:', error);
      throw error;
    }
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
