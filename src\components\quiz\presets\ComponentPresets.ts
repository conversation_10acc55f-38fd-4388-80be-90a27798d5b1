/**
 * Quiz组件快捷预设
 * 提供常用的组件配置预设，加速开发
 */

import {
  TextComponentConfig,
  ButtonComponentConfig,
  SelectorComponentConfig
} from '@/types/schema/base';

// ==================== 文本组件预设 ====================

export const TextComponentPresets = {
  // 标题文本
  title: {
    layout_id: "standard_text",
    style: {
      font_family: "modern" as const,
      size: "title" as const,
      color_scheme: "#2E7D32",
      alignment: "center" as const,
      line_height: 1.4,
      letter_spacing: 0.02
    }
  },

  // 问题文本
  question: {
    layout_id: "standard_text",
    style: {
      font_family: "traditional" as const,
      size: "large" as const,
      color_scheme: "#1B5E20",
      alignment: "left" as const,
      line_height: 1.6,
      letter_spacing: 0.01
    }
  },

  // NPC对话
  npc_dialogue: {
    layout_id: "dialogue_bubble",
    style: {
      font_family: "traditional" as const,
      size: "medium" as const,
      color_scheme: "#2E7D32",
      alignment: "left" as const,
      line_height: 1.5,
      letter_spacing: 0
    }
  },

  // 提示文本
  hint: {
    layout_id: "standard_text",
    style: {
      font_family: "modern" as const,
      size: "small" as const,
      color_scheme: "#757575",
      alignment: "center" as const,
      line_height: 1.4,
      letter_spacing: 0
    }
  },

  // 描述文本
  description: {
    layout_id: "standard_text",
    style: {
      font_family: "modern" as const,
      size: "medium" as const,
      color_scheme: "#424242",
      alignment: "left" as const,
      line_height: 1.6,
      letter_spacing: 0
    }
  },

  // 古典卷轴文本
  scroll_text: {
    layout_id: "scroll_text",
    style: {
      font_family: "calligraphy" as const,
      size: "medium" as const,
      color_scheme: "#3E2723",
      alignment: "center" as const,
      line_height: 1.8,
      letter_spacing: 0.05,
      background_pattern: "bamboo" as const
    }
  },

  // 碑文文本
  inscription_text: {
    layout_id: "inscription_text",
    style: {
      font_family: "seal_script" as const,
      size: "large" as const,
      color_scheme: "#FFD700",
      alignment: "center" as const,
      line_height: 1.6,
      letter_spacing: 0.1,
      text_shadow: "2px 2px 4px rgba(0, 0, 0, 0.8)"
    }
  },

  // 浮动文本
  floating_text: {
    layout_id: "floating_text",
    style: {
      font_family: "modern" as const,
      size: "medium" as const,
      color_scheme: "#1976D2",
      alignment: "center" as const,
      line_height: 1.5,
      letter_spacing: 0
    }
  },

  // 横幅文本
  banner_text: {
    layout_id: "banner_text",
    style: {
      font_family: "traditional" as const,
      size: "title" as const,
      color_scheme: "#FFFFFF",
      alignment: "center" as const,
      line_height: 1.4,
      letter_spacing: 0.02
    }
  }
} as const;

// ==================== 按钮组件预设 ====================

export const ButtonComponentPresets = {
  // 主要操作按钮
  primary_action: {
    layout_id: "standard_button",
    style: {
      size: "large" as const,
      variant: "primary" as const,
      shape: "rounded" as const,
      icon_position: "none" as const,
      hover_effect: "scale" as const
    },
    feedback: {
      haptic_feedback: true,
      animation: "bounce" as const
    }
  },

  // 次要操作按钮
  secondary_action: {
    layout_id: "standard_button",
    style: {
      size: "medium" as const,
      variant: "secondary" as const,
      shape: "rounded" as const,
      icon_position: "none" as const,
      hover_effect: "scale" as const
    },
    feedback: {
      haptic_feedback: true,
      animation: "bounce" as const
    }
  },

  // 确认按钮 (中医风格)
  confirm_tcm: {
    layout_id: "seal_stamp",
    style: {
      size: "large" as const,
      variant: "primary" as const,
      shape: "rectangle" as const,
      icon_position: "none" as const,
      hover_effect: "shadow" as const
    },
    feedback: {
      haptic_feedback: true,
      sound_effect: "stamp",
      animation: "bounce" as const
    }
  },

  // 特殊操作按钮
  special_action: {
    layout_id: "jade_pendant",
    style: {
      size: "large" as const,
      variant: "primary" as const,
      shape: "custom" as const,
      icon_position: "none" as const,
      hover_effect: "glow" as const
    },
    feedback: {
      haptic_feedback: true,
      sound_effect: "jade_clink",
      animation: "pulse" as const
    }
  },

  // 导航按钮
  navigation: {
    layout_id: "standard_button",
    style: {
      size: "medium" as const,
      variant: "outline" as const,
      shape: "pill" as const,
      icon_position: "left" as const,
      hover_effect: "scale" as const
    },
    feedback: {
      haptic_feedback: false,
      animation: "bounce" as const
    }
  },

  // 取消按钮
  cancel: {
    layout_id: "standard_button",
    style: {
      size: "medium" as const,
      variant: "ghost" as const,
      shape: "rounded" as const,
      icon_position: "none" as const,
      hover_effect: "scale" as const
    },
    feedback: {
      haptic_feedback: false,
      animation: "bounce" as const
    }
  }
} as const;

// ==================== 选择器组件预设 ====================

export const SelectorComponentPresets = {
  // 单选列表
  single_choice_list: {
    layout_id: "vertical_list",
    style: {
      selection_mode: "single" as const,
      marker_style: "circle" as const,
      spacing: 12,
      hover_effect: true,
      animation_style: "scale_in" as const
    },
    validation: {
      required: true,
      min_selections: 1,
      max_selections: 1
    }
  },

  // 多选网格
  multi_choice_grid: {
    layout_id: "grid_layout",
    style: {
      selection_mode: "multiple" as const,
      marker_style: "chinese_marker" as const,
      spacing: 16,
      hover_effect: true,
      animation_style: "bounce" as const
    },
    validation: {
      required: false,
      min_selections: 0,
      max_selections: 5
    }
  },

  // 情绪选择器
  emotion_selector: {
    layout_id: "horizontal_flow",
    style: {
      selection_mode: "single" as const,
      marker_style: "chinese_marker" as const,
      spacing: 8,
      hover_effect: true,
      animation_style: "scale_in" as const
    },
    validation: {
      required: true,
      min_selections: 1,
      max_selections: 1
    }
  },

  // 症状多选
  symptom_multi_select: {
    layout_id: "vertical_list",
    style: {
      selection_mode: "multiple" as const,
      marker_style: "square" as const,
      spacing: 10,
      hover_effect: true,
      animation_style: "fade_in" as const
    },
    validation: {
      required: false,
      min_selections: 0,
      max_selections: 10
    }
  },

  // 卡片式选择
  card_selector: {
    layout_id: "grid_layout",
    style: {
      selection_mode: "single" as const,
      marker_style: "chinese_marker" as const,
      spacing: 20,
      hover_effect: true,
      animation_style: "scale_in" as const
    },
    validation: {
      required: true,
      min_selections: 1,
      max_selections: 1
    }
  }
} as const;

// ==================== 滑块组件预设 ====================

export const SliderComponentPresets = {
  // 标准滑块
  standard_slider: {
    layout_id: "standard_slider",
    style: {
      orientation: "horizontal" as const,
      track_style: "line" as const,
      thumb_style: "circle" as const,
      show_value: true,
      show_labels: true,
      show_ticks: false,
      color_scheme: "#4CAF50",
      gradient_effect: false,
      glow_effect: false
    }
  },

  // 竹节滑块
  bamboo_slider: {
    layout_id: "bamboo_slider",
    style: {
      orientation: "horizontal" as const,
      track_style: "bamboo" as const,
      thumb_style: "jade_bead" as const,
      show_value: true,
      show_labels: true,
      show_ticks: true,
      tick_style: "bamboo_nodes" as const,
      color_scheme: "#8BC34A",
      gradient_effect: true,
      glow_effect: false
    }
  },

  // 墨迹滑块
  ink_brush_slider: {
    layout_id: "ink_brush_slider",
    style: {
      orientation: "horizontal" as const,
      track_style: "ink_brush" as const,
      thumb_style: "yin_yang" as const,
      show_value: true,
      show_labels: true,
      show_ticks: false,
      color_scheme: "#424242",
      gradient_effect: false,
      glow_effect: true
    }
  },

  // 龙脊滑块
  dragon_spine_slider: {
    layout_id: "dragon_spine_slider",
    style: {
      orientation: "horizontal" as const,
      track_style: "dragon_spine" as const,
      thumb_style: "coin" as const,
      show_value: true,
      show_labels: true,
      show_ticks: true,
      tick_style: "dots" as const,
      color_scheme: "#8D6E63",
      gradient_effect: true,
      glow_effect: false
    }
  },

  // 山脊滑块
  mountain_ridge_slider: {
    layout_id: "mountain_ridge_slider",
    style: {
      orientation: "horizontal" as const,
      track_style: "mountain_ridge" as const,
      thumb_style: "lotus_petal" as const,
      show_value: true,
      show_labels: true,
      show_ticks: true,
      tick_style: "lotus_buds" as const,
      color_scheme: "#4CAF50",
      gradient_effect: true,
      glow_effect: false
    }
  },

  // 河流滑块
  river_flow_slider: {
    layout_id: "river_flow_slider",
    style: {
      orientation: "horizontal" as const,
      track_style: "river_flow" as const,
      thumb_style: "pearl" as const,
      show_value: true,
      show_labels: true,
      show_ticks: false,
      color_scheme: "#2196F3",
      gradient_effect: true,
      glow_effect: true
    }
  },

  // 熊猫爪印滑块
  panda_paw_slider: {
    layout_id: "panda_paw_slider",
    style: {
      orientation: "horizontal" as const,
      track_style: "groove" as const,
      thumb_style: "panda_paw" as const,
      show_value: true,
      show_labels: true,
      show_ticks: true,
      tick_style: "dots" as const,
      color_scheme: "#333333",
      gradient_effect: false,
      glow_effect: false
    }
  },

  // 垂直温度计滑块
  vertical_thermometer: {
    layout_id: "vertical_thermometer",
    style: {
      orientation: "vertical" as const,
      track_style: "groove" as const,
      thumb_style: "circle" as const,
      show_value: true,
      show_labels: true,
      show_ticks: true,
      tick_style: "lines" as const,
      color_scheme: "#FF5722",
      gradient_effect: true,
      glow_effect: false
    }
  }
} as const;

// ==================== 评分组件预设 ====================

export const RatingComponentPresets = {
  // 标准星级评分
  star_rating: {
    layout_id: "star_rating",
    style: {
      marker_type: "stars" as const,
      size: "medium" as const,
      spacing: 8,
      hover_effect: "scale" as const,
      fill_animation: "instant" as const,
      orientation: "horizontal" as const,
      allow_half: false,
      show_value: false
    }
  },

  // 莲花评分 (中医风格)
  lotus_rating: {
    layout_id: "lotus_rating",
    style: {
      marker_type: "lotus" as const,
      size: "large" as const,
      spacing: 12,
      hover_effect: "glow" as const,
      fill_animation: "bloom" as const,
      orientation: "horizontal" as const,
      allow_half: false,
      show_value: true
    }
  },

  // 太极评分
  taiji_rating: {
    layout_id: "taiji_rating",
    style: {
      marker_type: "taiji" as const,
      size: "medium" as const,
      spacing: 10,
      hover_effect: "color_change" as const,
      fill_animation: "wave" as const,
      orientation: "horizontal" as const,
      allow_half: false,
      show_value: true
    }
  },

  // 葫芦评分
  gourd_rating: {
    layout_id: "gourd_rating",
    style: {
      marker_type: "gourd" as const,
      size: "medium" as const,
      spacing: 8,
      hover_effect: "bounce" as const,
      fill_animation: "progressive" as const,
      orientation: "horizontal" as const,
      allow_half: false,
      show_value: false
    }
  },

  // 爱心评分
  heart_rating: {
    layout_id: "heart_rating",
    style: {
      marker_type: "hearts" as const,
      size: "medium" as const,
      spacing: 6,
      hover_effect: "scale" as const,
      fill_animation: "instant" as const,
      orientation: "horizontal" as const,
      allow_half: true,
      show_value: false
    }
  },

  // 点状评分
  dot_rating: {
    layout_id: "dot_rating",
    style: {
      marker_type: "dots" as const,
      size: "small" as const,
      spacing: 4,
      hover_effect: "scale" as const,
      fill_animation: "progressive" as const,
      orientation: "horizontal" as const,
      allow_half: false,
      show_value: true
    }
  }
} as const;

// ==================== 下拉选择器组件预设 ====================

export const DropdownComponentPresets = {
  // 标准下拉选择器
  standard_dropdown: {
    layout_id: "standard_dropdown",
    style: {
      arrow_style: "chevron" as const,
      menu_style: "modern" as const,
      max_height: 200,
      placeholder_text: { zh: "请选择...", en: "Please select..." }
    }
  },

  // 传统中医风格下拉选择器
  traditional_dropdown: {
    layout_id: "traditional_dropdown",
    style: {
      arrow_style: "chinese_arrow" as const,
      menu_style: "traditional" as const,
      max_height: 180,
      placeholder_text: { zh: "请选择选项", en: "Select an option" }
    }
  },

  // 浮动风格下拉选择器
  floating_dropdown: {
    layout_id: "floating_dropdown",
    style: {
      arrow_style: "triangle" as const,
      menu_style: "floating" as const,
      max_height: 250,
      placeholder_text: { zh: "选择...", en: "Choose..." }
    }
  },

  // 体质类型选择器
  constitution_dropdown: {
    layout_id: "constitution_dropdown",
    style: {
      arrow_style: "chinese_arrow" as const,
      menu_style: "traditional" as const,
      max_height: 200,
      placeholder_text: { zh: "请选择体质类型", en: "Select constitution type" }
    }
  },

  // 症状选择器
  symptom_dropdown: {
    layout_id: "symptom_dropdown",
    style: {
      arrow_style: "chevron" as const,
      menu_style: "modern" as const,
      max_height: 300,
      placeholder_text: { zh: "请选择症状", en: "Select symptom" }
    }
  }
} as const;

// ==================== 图片组件预设 ====================

export const ImageComponentPresets = {
  // 标准图片
  standard_image: {
    layout_id: "standard_image",
    style: {
      border_style: "simple" as const,
      hover_effect: "none" as const,
      border_radius: 8,
      shadow_effect: false,
      size: {
        width: undefined,
        height: undefined,
        aspect_ratio: undefined
      }
    }
  },

  // 水墨画框
  ink_wash_frame: {
    layout_id: "ink_wash_frame",
    style: {
      border_style: "ink_wash_frame" as const,
      hover_effect: "brightness" as const,
      border_radius: 4,
      shadow_effect: true,
      size: {
        width: undefined,
        height: undefined,
        aspect_ratio: "4:3"
      }
    }
  },

  // 传统画框
  traditional_frame: {
    layout_id: "traditional_frame",
    style: {
      border_style: "traditional" as const,
      hover_effect: "shadow" as const,
      border_radius: 0,
      shadow_effect: true,
      size: {
        width: undefined,
        height: undefined,
        aspect_ratio: "3:4"
      }
    }
  },

  // 交互式图片
  interactive_image: {
    layout_id: "interactive_image",
    style: {
      border_style: "simple" as const,
      hover_effect: "zoom" as const,
      border_radius: 12,
      shadow_effect: false,
      size: {
        width: undefined,
        height: undefined,
        aspect_ratio: "16:9"
      }
    }
  }
} as const;

// ==================== 文本输入组件预设 ====================

export const TextInputComponentPresets = {
  // 标准文本输入
  standard_input: {
    layout_id: "standard_input",
    style: {
      size: "medium" as const,
      border_style: "modern" as const,
      label_position: "top" as const,
      show_counter: false,
      resize: "none" as const
    }
  },

  // 传统中医风格输入
  traditional_input: {
    layout_id: "traditional_input",
    style: {
      size: "medium" as const,
      border_style: "traditional" as const,
      label_position: "top" as const,
      show_counter: true,
      resize: "none" as const
    }
  },

  // 墨迹风格输入
  ink_brush_input: {
    layout_id: "ink_brush_input",
    style: {
      size: "large" as const,
      border_style: "ink_brush" as const,
      label_position: "floating" as const,
      show_counter: false,
      resize: "none" as const
    }
  },

  // 竹节风格输入
  bamboo_input: {
    layout_id: "bamboo_input",
    style: {
      size: "medium" as const,
      border_style: "bamboo" as const,
      label_position: "inside" as const,
      show_counter: true,
      resize: "none" as const
    }
  },

  // 多行文本输入
  textarea_input: {
    layout_id: "textarea_input",
    style: {
      size: "medium" as const,
      border_style: "modern" as const,
      label_position: "top" as const,
      show_counter: true,
      resize: "vertical" as const
    }
  }
} as const;

// ==================== 图片选择器组件预设 ====================

export const ImageSelectorComponentPresets = {
  // 标准图片选择器
  standard_image_selector: {
    layout_id: "standard_image_selector",
    style: {
      columns: 3,
      aspect_ratio: "1:1",
      gap: 16,
      selection_indicator: "checkmark" as const,
      hover_effect: "zoom" as const,
      border_radius: 8
    },
    selection: {
      mode: "single" as const
    }
  },

  // 网格图片选择器
  grid_image_selector: {
    layout_id: "grid_image_selector",
    style: {
      columns: 4,
      aspect_ratio: "4:3",
      gap: 12,
      selection_indicator: "border" as const,
      hover_effect: "shadow" as const,
      border_radius: 12
    },
    selection: {
      mode: "multiple" as const,
      max_selections: 3
    }
  },

  // 情绪图片选择器
  emotion_image_selector: {
    layout_id: "emotion_image_selector",
    style: {
      columns: 2,
      aspect_ratio: "16:9",
      gap: 20,
      selection_indicator: "glow" as const,
      hover_effect: "lift" as const,
      border_radius: 16
    },
    selection: {
      mode: "single" as const
    }
  },

  // 症状图片选择器
  symptom_image_selector: {
    layout_id: "symptom_image_selector",
    style: {
      columns: 3,
      aspect_ratio: "1:1",
      gap: 16,
      selection_indicator: "overlay" as const,
      hover_effect: "brightness" as const,
      border_radius: 8
    },
    selection: {
      mode: "multiple" as const,
      max_selections: 5
    }
  }
} as const;

// ==================== 进度指示器组件预设 ====================

export const ProgressIndicatorComponentPresets = {
  // 标准条形进度条
  standard_progress_bar: {
    layout_id: "standard_progress_bar",
    style: {
      type: "bar" as const,
      size: "medium" as const,
      color_scheme: "#4CAF50",
      background_color: "#E0E0E0",
      border_radius: 4,
      thickness: 8
    },
    labels: {
      show_label: true,
      label_position: "top" as const
    }
  },

  // 圆形进度条
  circle_progress: {
    layout_id: "circle_progress",
    style: {
      type: "circle" as const,
      size: "medium" as const,
      color_scheme: "#4CAF50",
      background_color: "#E0E0E0",
      border_radius: 0,
      thickness: 6
    },
    labels: {
      show_label: false,
      label_position: "center" as const
    }
  },

  // 莲花进度条 (中医风格)
  lotus_progress: {
    layout_id: "lotus_progress",
    style: {
      type: "lotus" as const,
      size: "large" as const,
      color_scheme: "#FF9800",
      background_color: "#FFF3E0",
      border_radius: 0,
      thickness: 0
    },
    labels: {
      show_label: true,
      label_position: "bottom" as const
    }
  },

  // 竹子进度条 (中医风格)
  bamboo_progress: {
    layout_id: "bamboo_progress",
    style: {
      type: "bamboo" as const,
      size: "medium" as const,
      color_scheme: "#8BC34A",
      background_color: "#E8F5E8",
      border_radius: 10,
      thickness: 20
    },
    labels: {
      show_label: true,
      label_position: "top" as const
    }
  },

  // 步骤进度条
  steps_progress: {
    layout_id: "steps_progress",
    style: {
      type: "steps" as const,
      size: "medium" as const,
      color_scheme: "#4CAF50",
      background_color: "#E0E0E0",
      border_radius: 16,
      thickness: 2
    },
    labels: {
      show_label: true,
      label_position: "bottom" as const
    }
  }
} as const;

// ==================== 音频播放器组件预设 ====================

export const AudioPlayerComponentPresets = {
  // 标准音频播放器
  standard_audio_player: {
    layout_id: "standard_audio_player",
    style: {
      player_style: "modern" as const
    },
    controls: {
      show_time: true,
      show_volume: true
    }
  },

  // 传统音频播放器
  traditional_audio_player: {
    layout_id: "traditional_audio_player",
    style: {
      player_style: "traditional" as const
    },
    controls: {
      show_time: true,
      show_volume: true
    }
  },

  // 简约音频播放器
  minimal_audio_player: {
    layout_id: "minimal_audio_player",
    style: {
      player_style: "minimal" as const
    },
    controls: {
      show_time: false,
      show_volume: false
    }
  }
} as const;

// ==================== 视频播放器组件预设 ====================

export const VideoPlayerComponentPresets = {
  // 标准视频播放器
  standard_video_player: {
    layout_id: "standard_video_player",
    style: {
      player_style: "modern" as const,
      size: {
        width: undefined,
        height: undefined,
        aspect_ratio: "16:9"
      }
    },
    controls: {
      show_fullscreen: true
    }
  },

  // 传统视频播放器
  traditional_video_player: {
    layout_id: "traditional_video_player",
    style: {
      player_style: "traditional" as const,
      size: {
        width: undefined,
        height: undefined,
        aspect_ratio: "4:3"
      }
    },
    controls: {
      show_fullscreen: true
    }
  },

  // 简约视频播放器
  minimal_video_player: {
    layout_id: "minimal_video_player",
    style: {
      player_style: "minimal" as const,
      size: {
        width: undefined,
        height: undefined,
        aspect_ratio: "16:9"
      }
    },
    controls: {
      show_fullscreen: false
    }
  }
} as const;

// ==================== 拖拽列表组件预设 ====================

export const DraggableListComponentPresets = {
  // 标准拖拽列表
  standard_draggable_list: {
    layout_id: "standard_draggable_list",
    style: {
      list_style: "modern" as const,
      show_drag_handle: true,
      show_order_numbers: false
    }
  },

  // 传统拖拽列表
  traditional_draggable_list: {
    layout_id: "traditional_draggable_list",
    style: {
      list_style: "traditional" as const,
      show_drag_handle: true,
      show_order_numbers: true
    }
  },

  // 简约拖拽列表
  minimal_draggable_list: {
    layout_id: "minimal_draggable_list",
    style: {
      list_style: "minimal" as const,
      show_drag_handle: false,
      show_order_numbers: true
    }
  }
} as const;

// ==================== NPC角色组件预设 ====================

export const NPCCharacterComponentPresets = {
  // 传统医生
  traditional_doctor: {
    layout_id: "traditional_doctor",
    character: {
      character_style: "traditional_doctor" as const,
      default_emotion: "wise" as const,
      show_name: true,
      animated: true,
      default_avatar: undefined
    }
  },

  // 智慧长者
  wise_elder: {
    layout_id: "wise_elder",
    character: {
      character_style: "wise_elder" as const,
      default_emotion: "neutral" as const,
      show_name: true,
      animated: true,
      default_avatar: undefined
    }
  },

  // 友好向导
  friendly_guide: {
    layout_id: "friendly_guide",
    character: {
      character_style: "friendly_guide" as const,
      default_emotion: "happy" as const,
      show_name: true,
      animated: true,
      default_avatar: undefined
    }
  },

  // 神秘贤者
  mystical_sage: {
    layout_id: "mystical_sage",
    character: {
      character_style: "mystical_sage" as const,
      default_emotion: "wise" as const,
      show_name: true,
      animated: true,
      default_avatar: undefined
    }
  }
} as const;

// ==================== 对话组件预设 ====================

export const DialogueComponentPresets = {
  // 标准对话
  standard_dialogue: {
    layout_id: "standard_dialogue",
    dialogue: {
      dialogue_style: "modern" as const,
      show_avatars: true,
      show_speaker_names: true,
      show_timestamps: false,
      allow_user_input: true,
      animated_typing: true
    }
  },

  // 传统对话
  traditional_dialogue: {
    layout_id: "traditional_dialogue",
    dialogue: {
      dialogue_style: "traditional" as const,
      show_avatars: true,
      show_speaker_names: true,
      show_timestamps: true,
      allow_user_input: true,
      animated_typing: true
    }
  },

  // 简约对话
  minimal_dialogue: {
    layout_id: "minimal_dialogue",
    dialogue: {
      dialogue_style: "minimal" as const,
      show_avatars: false,
      show_speaker_names: false,
      show_timestamps: false,
      allow_user_input: false,
      animated_typing: false
    }
  }
} as const;

// ==================== 快捷创建工具函数 ====================

export class QuizComponentFactory {

  /**
   * 创建标准文本组件
   */
  static createText(
    text: Record<string, string>,
    preset: keyof typeof TextComponentPresets = 'question',
    overrides?: Partial<TextComponentConfig>
  ): TextComponentConfig {
    const baseConfig = TextComponentPresets[preset];

    return {
      id: `text-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'text_component',
      layout_id: baseConfig.layout_id,
      content: {
        text_localized: text,
        animation_effect: 'fade_in'
      },
      style: baseConfig.style,
      ...overrides
    };
  }

  /**
   * 创建标准按钮组件
   */
  static createButton(
    text: Record<string, string>,
    preset: keyof typeof ButtonComponentPresets = 'primary_action',
    overrides?: Partial<ButtonComponentConfig>
  ): ButtonComponentConfig {
    const baseConfig = ButtonComponentPresets[preset];

    return {
      id: `button-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'button_component',
      layout_id: baseConfig.layout_id,
      content: {
        text_localized: text,
        loading_state: false
      },
      style: baseConfig.style,
      feedback: baseConfig.feedback,
      ...overrides
    };
  }

  /**
   * 创建标准选择器组件
   */
  static createSelector(
    options: Array<{
      id: string;
      value: string | number;
      text: Record<string, string>;
      icon?: string;
    }>,
    preset: keyof typeof SelectorComponentPresets = 'single_choice_list',
    overrides?: Partial<SelectorComponentConfig>
  ): SelectorComponentConfig {
    const baseConfig = SelectorComponentPresets[preset];

    return {
      id: `selector-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'selector_component',
      layout_id: baseConfig.layout_id,
      options: options.map(opt => ({
        id: opt.id,
        value: opt.value,
        text_localized: opt.text,
        display_style: 'text_only' as const,
        icon_name: opt.icon,
        disabled: false
      })),
      style: baseConfig.style,
      validation: baseConfig.validation,
      ...overrides
    };
  }

  /**
   * 创建滑块组件
   */
  static createSlider(
    range: { min: number; max: number; step?: number; default_value: number },
    labels?: { start_label?: Record<string, string>; end_label?: Record<string, string>; unit?: string },
    preset: keyof typeof SliderComponentPresets = 'standard_slider',
    overrides?: any
  ): any {
    const baseConfig = SliderComponentPresets[preset];

    return {
      id: `slider-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'slider_component',
      layout_id: baseConfig.layout_id,
      range: {
        min: range.min,
        max: range.max,
        step: range.step || 1,
        default_value: range.default_value
      },
      style: baseConfig.style,
      labels: labels || {},
      ...overrides
    };
  }

  /**
   * 创建评分组件
   */
  static createRating(
    scale: { min_value: number; max_value: number; step?: number; default_value?: number },
    labels?: { start_label?: Record<string, string>; end_label?: Record<string, string>; value_suffix?: string },
    preset: keyof typeof RatingComponentPresets = 'star_rating',
    overrides?: any
  ): any {
    const baseConfig = RatingComponentPresets[preset];

    return {
      id: `rating-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'rating_component',
      layout_id: baseConfig.layout_id,
      scale: {
        min_value: scale.min_value,
        max_value: scale.max_value,
        step: scale.step || 1,
        default_value: scale.default_value
      },
      style: baseConfig.style,
      labels: labels || {},
      ...overrides
    };
  }

  /**
   * 创建下拉选择器组件
   */
  static createDropdown(
    options: Array<{
      id: string;
      value: string | number;
      text: Record<string, string>;
      icon?: string;
      disabled?: boolean;
    }>,
    preset: keyof typeof DropdownComponentPresets = 'standard_dropdown',
    overrides?: any
  ): any {
    const baseConfig = DropdownComponentPresets[preset];

    return {
      id: `dropdown-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'dropdown_component',
      layout_id: baseConfig.layout_id,
      style: baseConfig.style,
      options: options.map(option => ({
        id: option.id,
        value: option.value,
        text_localized: option.text,
        icon_name: option.icon,
        disabled: option.disabled || false
      })),
      ...overrides
    };
  }

  /**
   * 创建图片组件
   */
  static createImage(
    imageUrl: string,
    altText: Record<string, string>,
    caption?: Record<string, string>,
    preset: keyof typeof ImageComponentPresets = 'standard_image',
    overrides?: any
  ): any {
    const baseConfig = ImageComponentPresets[preset];

    return {
      id: `image-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'image_component',
      layout_id: baseConfig.layout_id,
      content: {
        image_url: imageUrl,
        alt_text: altText,
        caption: caption
      },
      style: baseConfig.style,
      ...overrides
    };
  }

  /**
   * 创建文本输入组件
   */
  static createTextInput(
    inputType: 'text' | 'textarea' | 'number' | 'email' | 'password',
    label: Record<string, string>,
    placeholder?: Record<string, string>,
    preset: keyof typeof TextInputComponentPresets = 'standard_input',
    overrides?: any
  ): any {
    const baseConfig = TextInputComponentPresets[preset];

    return {
      id: `text-input-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'text_input_component',
      layout_id: baseConfig.layout_id,
      input: {
        type: inputType,
        placeholder: placeholder,
        required: false
      },
      style: baseConfig.style,
      validation: {
        real_time: true,
        show_errors: true
      },
      label: label,
      ...overrides
    };
  }

  /**
   * 创建图片选择器组件
   */
  static createImageSelector(
    images: Array<{
      id: string;
      url: string;
      alt_text: Record<string, string>;
      title?: Record<string, string>;
      description?: Record<string, string>;
      value: string | number;
    }>,
    preset: keyof typeof ImageSelectorComponentPresets = 'standard_image_selector',
    overrides?: any
  ): any {
    const baseConfig = ImageSelectorComponentPresets[preset];

    return {
      id: `image-selector-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'image_selector_component',
      layout_id: baseConfig.layout_id,
      images: images,
      style: baseConfig.style,
      selection: baseConfig.selection,
      ...overrides
    };
  }

  /**
   * 创建进度指示器组件
   */
  static createProgressIndicator(
    current: number,
    total: number,
    label?: Record<string, string>,
    preset: keyof typeof ProgressIndicatorComponentPresets = 'standard_progress_bar',
    overrides?: any
  ): any {
    const baseConfig = ProgressIndicatorComponentPresets[preset];

    return {
      id: `progress-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'progress_indicator_component',
      layout_id: baseConfig.layout_id,
      progress: {
        current: current,
        total: total,
        show_percentage: true,
        show_fraction: false,
        animated: true
      },
      style: baseConfig.style,
      labels: {
        ...baseConfig.labels,
        label_text: label
      },
      ...overrides
    };
  }

  /**
   * 创建音频播放器组件
   */
  static createAudioPlayer(
    audioUrl: string,
    preset: keyof typeof AudioPlayerComponentPresets = 'standard_audio_player',
    overrides?: any
  ): any {
    const baseConfig = AudioPlayerComponentPresets[preset];

    return {
      id: `audio-player-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'audio_player_component',
      layout_id: baseConfig.layout_id,
      audio_url: audioUrl,
      style: baseConfig.style,
      controls: baseConfig.controls,
      ...overrides
    };
  }

  /**
   * 创建视频播放器组件
   */
  static createVideoPlayer(
    videoUrl: string,
    posterUrl?: string,
    preset: keyof typeof VideoPlayerComponentPresets = 'standard_video_player',
    overrides?: any
  ): any {
    const baseConfig = VideoPlayerComponentPresets[preset];

    return {
      id: `video-player-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'video_player_component',
      layout_id: baseConfig.layout_id,
      video_url: videoUrl,
      poster_url: posterUrl,
      style: baseConfig.style,
      controls: baseConfig.controls,
      ...overrides
    };
  }

  /**
   * 创建拖拽列表组件
   */
  static createDraggableList(
    items: Array<{
      id: string;
      content: Record<string, string>;
      icon?: string;
      value: string | number;
      disabled?: boolean;
    }>,
    preset: keyof typeof DraggableListComponentPresets = 'standard_draggable_list',
    overrides?: any
  ): any {
    const baseConfig = DraggableListComponentPresets[preset];

    return {
      id: `draggable-list-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'draggable_list_component',
      layout_id: baseConfig.layout_id,
      items: items,
      style: baseConfig.style,
      ...overrides
    };
  }

  /**
   * 创建NPC角色组件
   */
  static createNPCCharacter(
    characterName: Record<string, string>,
    characterDescription?: Record<string, string>,
    avatarUrl?: string,
    preset: keyof typeof NPCCharacterComponentPresets = 'traditional_doctor',
    overrides?: any
  ): any {
    const baseConfig = NPCCharacterComponentPresets[preset];

    return {
      id: `npc-character-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'npc_character_component',
      layout_id: baseConfig.layout_id,
      character_name: characterName,
      character_description: characterDescription,
      avatar_url: avatarUrl,
      character: baseConfig.character,
      ...overrides
    };
  }

  /**
   * 创建对话组件
   */
  static createDialogue(
    messages: Array<{
      id: string;
      speaker: 'user' | 'npc' | 'system';
      content: Record<string, string>;
      timestamp?: number;
      avatar?: string;
      emotion?: string;
    }>,
    options?: Array<{
      id: string;
      text: Record<string, string>;
      value: string | number;
      disabled?: boolean;
    }>,
    preset: keyof typeof DialogueComponentPresets = 'standard_dialogue',
    overrides?: any
  ): any {
    const baseConfig = DialogueComponentPresets[preset];

    return {
      id: `dialogue-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      component_type: 'dialogue_component',
      layout_id: baseConfig.layout_id,
      messages: messages,
      options: options,
      dialogue: baseConfig.dialogue,
      ...overrides
    };
  }

  /**
   * 创建情绪选择器 (快捷方法)
   */
  static createEmotionSelector(
    emotions: Array<{
      id: string;
      name: Record<string, string>;
      icon?: string;
    }>
  ): SelectorComponentConfig {
    return this.createSelector(
      emotions.map(emotion => ({
        id: emotion.id,
        value: emotion.id,
        text: emotion.name,
        icon: emotion.icon
      })),
      'emotion_selector',
      {
        style: {
          marker_style: 'chinese_marker',
          animation_style: 'scale_in'
        }
      }
    );
  }

  /**
   * 创建问题页面组件组合
   */
  static createQuestionPage(config: {
    title?: Record<string, string>;
    question: Record<string, string>;
    options: Array<{
      id: string;
      value: string | number;
      text: Record<string, string>;
    }>;
    confirmButton?: Record<string, string>;
    hint?: Record<string, string>;
  }) {
    const components: any[] = [];

    // 标题
    if (config.title) {
      components.push(this.createText(config.title, 'title'));
    }

    // 问题文本
    components.push(this.createText(config.question, 'question'));

    // 选择器
    components.push(this.createSelector(config.options));

    // 确认按钮
    if (config.confirmButton) {
      components.push(this.createButton(config.confirmButton, 'confirm_tcm'));
    }

    // 提示文本
    if (config.hint) {
      components.push(this.createText(config.hint, 'hint'));
    }

    return components;
  }
}

// ==================== 常用文本内容 ====================

export const CommonTexts = {
  // 按钮文本
  buttons: {
    confirm: { zh: '确认', en: 'Confirm' },
    cancel: { zh: '取消', en: 'Cancel' },
    next: { zh: '下一步', en: 'Next' },
    previous: { zh: '上一步', en: 'Previous' },
    submit: { zh: '提交', en: 'Submit' },
    skip: { zh: '跳过', en: 'Skip' },
    retry: { zh: '重试', en: 'Retry' },
    finish: { zh: '完成', en: 'Finish' }
  },

  // 提示文本
  hints: {
    select_one: { zh: '请选择一个选项', en: 'Please select one option' },
    select_multiple: { zh: '可以选择多个选项', en: 'You can select multiple options' },
    required: { zh: '此题为必答题', en: 'This question is required' },
    optional: { zh: '此题为选答题', en: 'This question is optional' }
  },

  // 问候语
  greetings: {
    welcome: { zh: '欢迎使用中医情绪评估系统', en: 'Welcome to TCM Emotion Assessment System' },
    start_quiz: { zh: '开始测评', en: 'Start Assessment' },
    thank_you: { zh: '感谢您的参与', en: 'Thank you for your participation' }
  }
};

export default QuizComponentFactory;
