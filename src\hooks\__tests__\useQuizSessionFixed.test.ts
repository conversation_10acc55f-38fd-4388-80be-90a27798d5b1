/**
 * useQuizSession Hook 测试
 * 测试修复后的Quiz会话管理功能
 */

import { renderHook, waitFor, act } from '@testing-library/react';
import { useQuizSession } from '../useQuizSession';
import { QuizSessionService } from '@/services/entities/QuizSessionService';

// Mock QuizSessionService
jest.mock('@/services/entities/QuizSessionService');

const MockedQuizSessionService = QuizSessionService as jest.MockedClass<typeof QuizSessionService>;

describe('useQuizSession', () => {
  let mockService: jest.Mocked<QuizSessionService>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock service instance
    mockService = {
      createSession: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      startSession: jest.fn(),
      completeSession: jest.fn(),
      pauseSession: jest.fn(),
      resumeSession: jest.fn(),
      updateProgress: jest.fn(),
      getUserSessions: jest.fn(),
      getUserActiveSessions: jest.fn(),
      getUserCompletedSessions: jest.fn(),
      getPackSessions: jest.fn(),
      on: jest.fn(),
      emit: jest.fn(),
    } as any;

    MockedQuizSessionService.mockImplementation(() => mockService);
  });

  it('should create a new quiz session successfully', async () => {
    const mockSession = {
      id: 'session_123',
      pack_id: 'pack_emotion_wheel',
      user_id: 'user_123',
      status: 'INITIATED' as const,
      current_question_index: 0,
      total_questions: 5,
      answered_questions: 0,
      completion_percentage: 0,
      start_time: new Date().toISOString(),
      last_active_time: new Date().toISOString(),
      session_type: 'standard',
      session_metadata: '{}',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    mockService.createSession.mockResolvedValue({
      success: true,
      data: mockSession,
    });

    const { result } = renderHook(() => useQuizSession());

    await act(async () => {
      const sessionResult = await result.current.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        session_type: 'standard',
      });

      expect(sessionResult.success).toBe(true);
      expect(sessionResult.data).toEqual(mockSession);
    });

    expect(mockService.createSession).toHaveBeenCalledWith({
      pack_id: 'pack_emotion_wheel',
      user_id: 'user_123',
      session_type: 'standard',
    });

    // Check that current session is set
    expect(result.current.currentSession).toEqual(mockSession);
  });

  it('should start a quiz session', async () => {
    const mockSession = {
      id: 'session_123',
      status: 'IN_PROGRESS' as const,
      pack_id: 'pack_emotion_wheel',
      user_id: 'user_123',
      current_question_index: 0,
      total_questions: 5,
      answered_questions: 0,
      completion_percentage: 0,
      start_time: new Date().toISOString(),
      last_active_time: new Date().toISOString(),
      session_type: 'standard',
      session_metadata: '{}',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    mockService.startSession.mockResolvedValue({
      success: true,
      data: mockSession,
    });

    const { result } = renderHook(() => useQuizSession());

    await act(async () => {
      const sessionResult = await result.current.startSession('session_123');
      expect(sessionResult.success).toBe(true);
      expect(sessionResult.data?.status).toBe('IN_PROGRESS');
    });

    expect(mockService.startSession).toHaveBeenCalledWith('session_123');
    expect(result.current.currentSession?.status).toBe('IN_PROGRESS');
  });

  it('should update session progress', async () => {
    mockService.updateProgress.mockResolvedValue({
      success: true,
      data: true,
    });

    const { result } = renderHook(() => useQuizSession());

    await act(async () => {
      const progressResult = await result.current.updateProgress('session_123', 2, 5);
      expect(progressResult.success).toBe(true);
    });

    expect(mockService.updateProgress).toHaveBeenCalledWith('session_123', 2, 5);
  });

  it('should complete a quiz session', async () => {
    const mockCompletedSession = {
      id: 'session_123',
      status: 'COMPLETED' as const,
      pack_id: 'pack_emotion_wheel',
      user_id: 'user_123',
      current_question_index: 5,
      total_questions: 5,
      answered_questions: 5,
      completion_percentage: 100,
      start_time: new Date().toISOString(),
      end_time: new Date().toISOString(),
      last_active_time: new Date().toISOString(),
      session_type: 'standard',
      session_metadata: '{}',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    mockService.completeSession.mockResolvedValue({
      success: true,
      data: mockCompletedSession,
    });

    const { result } = renderHook(() => useQuizSession());

    await act(async () => {
      const sessionResult = await result.current.completeSession('session_123');
      expect(sessionResult.success).toBe(true);
      expect(sessionResult.data?.status).toBe('COMPLETED');
      expect(sessionResult.data?.completion_percentage).toBe(100);
    });

    expect(mockService.completeSession).toHaveBeenCalledWith('session_123');
    expect(result.current.currentSession?.status).toBe('COMPLETED');
  });

  it('should pause and resume a session', async () => {
    const mockPausedSession = {
      id: 'session_123',
      status: 'PAUSED' as const,
      pack_id: 'pack_emotion_wheel',
      user_id: 'user_123',
      current_question_index: 2,
      total_questions: 5,
      answered_questions: 2,
      completion_percentage: 40,
      start_time: new Date().toISOString(),
      last_active_time: new Date().toISOString(),
      session_type: 'standard',
      session_metadata: '{}',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const mockResumedSession = {
      ...mockPausedSession,
      status: 'IN_PROGRESS' as const,
    };

    mockService.pauseSession.mockResolvedValue({
      success: true,
      data: mockPausedSession,
    });

    mockService.resumeSession.mockResolvedValue({
      success: true,
      data: mockResumedSession,
    });

    const { result } = renderHook(() => useQuizSession());

    // Test pause
    await act(async () => {
      const pauseResult = await result.current.pauseSession('session_123');
      expect(pauseResult.success).toBe(true);
      expect(pauseResult.data?.status).toBe('PAUSED');
    });

    expect(result.current.currentSession?.status).toBe('PAUSED');

    // Test resume
    await act(async () => {
      const resumeResult = await result.current.resumeSession('session_123');
      expect(resumeResult.success).toBe(true);
      expect(resumeResult.data?.status).toBe('IN_PROGRESS');
    });

    expect(result.current.currentSession?.status).toBe('IN_PROGRESS');
    expect(mockService.pauseSession).toHaveBeenCalledWith('session_123');
    expect(mockService.resumeSession).toHaveBeenCalledWith('session_123');
  });

  it('should get user sessions', async () => {
    const mockSessions = [
      {
        id: 'session_1',
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'COMPLETED' as const,
        current_question_index: 5,
        total_questions: 5,
        answered_questions: 5,
        completion_percentage: 100,
        start_time: new Date().toISOString(),
        end_time: new Date().toISOString(),
        last_active_time: new Date().toISOString(),
        session_type: 'standard',
        session_metadata: '{}',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'session_2',
        pack_id: 'pack_tcm_assessment',
        user_id: 'user_123',
        status: 'IN_PROGRESS' as const,
        current_question_index: 3,
        total_questions: 10,
        answered_questions: 3,
        completion_percentage: 30,
        start_time: new Date().toISOString(),
        last_active_time: new Date().toISOString(),
        session_type: 'standard',
        session_metadata: '{}',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    mockService.getUserSessions.mockResolvedValue({
      success: true,
      data: mockSessions,
    });

    const { result } = renderHook(() => useQuizSession());

    await act(async () => {
      const sessionsResult = await result.current.getUserSessions('user_123');
      expect(sessionsResult.success).toBe(true);
      expect(sessionsResult.data).toHaveLength(2);
      expect(sessionsResult.data?.[0].status).toBe('COMPLETED');
      expect(sessionsResult.data?.[1].status).toBe('IN_PROGRESS');
    });

    expect(mockService.getUserSessions).toHaveBeenCalledWith('user_123', 20);
  });

  it('should handle service errors gracefully', async () => {
    mockService.createSession.mockResolvedValue({
      success: false,
      error: 'Failed to create session',
    });

    const { result } = renderHook(() => useQuizSession());

    await act(async () => {
      const sessionResult = await result.current.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
      });

      expect(sessionResult.success).toBe(false);
      expect(sessionResult.error).toBe('Failed to create session');
    });

    // Check that error state is set
    expect(result.current.error).toBe('Failed to create session');
  });

  it('should manage loading state correctly', async () => {
    // Make service call take some time
    mockService.createSession.mockImplementation(() =>
      new Promise(resolve =>
        setTimeout(() => resolve({ success: true, data: {} as any }), 100)
      )
    );

    const { result } = renderHook(() => useQuizSession());

    expect(result.current.isLoading).toBe(false);

    act(() => {
      result.current.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
      });
    });

    // Should be loading immediately after call
    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
  });

  it('should clear error state', async () => {
    mockService.createSession.mockResolvedValue({
      success: false,
      error: 'Test error',
    });

    const { result } = renderHook(() => useQuizSession());

    await act(async () => {
      await result.current.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
      });
    });

    expect(result.current.error).toBe('Test error');

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBe(null);
  });

  it('should delete a session and clear current session if it matches', async () => {
    const mockSession = {
      id: 'session_123',
      pack_id: 'pack_emotion_wheel',
      user_id: 'user_123',
      status: 'INITIATED' as const,
      current_question_index: 0,
      total_questions: 5,
      answered_questions: 0,
      completion_percentage: 0,
      start_time: new Date().toISOString(),
      last_active_time: new Date().toISOString(),
      session_type: 'standard',
      session_metadata: '{}',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    mockService.delete.mockResolvedValue({
      success: true,
      data: true,
    });

    const { result } = renderHook(() => useQuizSession());

    // Set current session first
    act(() => {
      result.current.setCurrentSession(mockSession);
    });

    expect(result.current.currentSession).toEqual(mockSession);

    await act(async () => {
      const deleteResult = await result.current.deleteSession('session_123');
      expect(deleteResult.success).toBe(true);
    });

    expect(mockService.delete).toHaveBeenCalledWith('session_123');
    expect(result.current.currentSession).toBe(null);
  });
});
