import type { TRPCClientType } from '../types/OnlineServiceTypes';

// 导入统一的类型定义
import {
  type PurchaseResult as PaymentResult,
  type VipPurchaseData,
  type SkinPurchaseData,
  type EmojiSetPurchaseData
} from '../../../types/schema/api';

/**
 * PaymentService - 客户端支付服务代理
 *
 * 轻量级代理服务，专注于：
 * - 调用服务端支付API (server/lib/services/PaymentService.ts)
 * - 管理本地支付状态缓存
 * - 提供用户友好的错误处理
 *
 * 注意：所有支付业务逻辑在服务端实现，这里只是代理和缓存管理
 */
export class PaymentService {
  constructor(private trpc: TRPCClientType) {}

  /**
   * 购买VIP订阅
   * 直接调用服务端API，处理本地状态缓存
   */
  async purchaseVip(data: VipPurchaseData): Promise<PaymentResult> {
    try {
      console.log('[PaymentService] Processing VIP purchase:', data.planId);

      // 直接调用服务端支付API - 所有业务逻辑在服务端处理
      const result = await this.trpc.purchaseVip.mutate({
        planId: data.planId,
        paymentMethodId: data.paymentMethodId,
        userId: data.user_id
      });

      if (result.success) {
        // 更新本地VIP状态缓存
        this.updateLocalCache('vipStatus', result);

        console.log('[PaymentService] VIP purchase successful:', result.transactionId);
        return {
          success: true,
          transactionId: result.transactionId
        };
      } else {
        console.warn('[PaymentService] VIP purchase failed:', result.error);
        return {
          success: false,
          error: result.error || 'VIP purchase failed'
        };
      }
    } catch (error) {
      console.error('[PaymentService] VIP purchase error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'VIP purchase failed'
      };
    }
  }

  /**
   * 购买皮肤
   * 直接调用服务端API
   */
  async purchaseSkin(data: SkinPurchaseData): Promise<PaymentResult> {
    try {
      console.log('[PaymentService] Processing skin purchase:', data.skinId);

      // 直接调用服务端支付API
      const result = await this.trpc.purchaseSkin.mutate({
        skinId: data.skinId,
        paymentMethodId: data.paymentMethodId,
        userId: data.user_id
      });

      if (result.success) {
        // 更新本地皮肤状态缓存
        this.updateLocalCache('unlockedSkins', { skinId: data.skinId, unlocked: true });

        console.log('[PaymentService] Skin purchase successful:', result.transactionId);
        return {
          success: true,
          transactionId: result.transactionId
        };
      } else {
        console.warn('[PaymentService] Skin purchase failed:', result.error);
        return {
          success: false,
          error: result.error || 'Skin purchase failed'
        };
      }
    } catch (error) {
      console.error('[PaymentService] Skin purchase error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Skin purchase failed'
      };
    }
  }

  /**
   * 购买表情集
   * 直接调用服务端API
   */
  async purchaseEmojiSet(data: EmojiSetPurchaseData): Promise<PaymentResult> {
    try {
      console.log('[PaymentService] Processing emoji set purchase:', data.emojiSetId);

      // 使用相同的皮肤购买端点 (服务端会处理表情集逻辑)
      const result = await this.trpc.purchaseSkin.mutate({
        skinId: data.emojiSetId, // 表情集ID
        paymentMethodId: data.paymentMethodId,
        userId: data.user_id
      });

      if (result.success) {
        // 更新本地表情集状态缓存
        this.updateLocalCache('unlockedEmojiSets', { emojiSetId: data.emojiSetId, unlocked: true });

        console.log('[PaymentService] Emoji set purchase successful:', result.transactionId);
        return {
          success: true,
          transactionId: result.transactionId
        };
      } else {
        console.warn('[PaymentService] Emoji set purchase failed:', result.error);
        return {
          success: false,
          error: result.error || 'Emoji set purchase failed'
        };
      }
    } catch (error) {
      console.error('[PaymentService] Emoji set purchase error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Emoji set purchase failed'
      };
    }
  }

  /**
   * 获取购买历史
   * 直接调用服务端API
   */
  async getPurchaseHistory(): Promise<any[]> {
    try {
      const history = await this.trpc.getPurchaseHistory.query();
      return history || [];
    } catch (error) {
      console.error('[PaymentService] Failed to get purchase history:', error);
      return [];
    }
  }

  /**
   * 更新本地缓存
   * 简单的localStorage缓存管理
   * @private
   */
  private updateLocalCache(key: string, data: any): void {
    try {
      if (key === 'vipStatus') {
        localStorage.setItem('vipStatus', JSON.stringify(data));
        window.dispatchEvent(new CustomEvent('vipStatusUpdated', { detail: data }));
      } else if (key === 'unlockedSkins') {
        const unlockedSkins = JSON.parse(localStorage.getItem('unlockedSkins') || '[]');
        if (!unlockedSkins.includes(data.skinId)) {
          unlockedSkins.push(data.skinId);
          localStorage.setItem('unlockedSkins', JSON.stringify(unlockedSkins));
        }
        window.dispatchEvent(new CustomEvent('skinStatusUpdated', { detail: data }));
      } else if (key === 'unlockedEmojiSets') {
        const unlockedEmojiSets = JSON.parse(localStorage.getItem('unlockedEmojiSets') || '[]');
        if (!unlockedEmojiSets.includes(data.emojiSetId)) {
          unlockedEmojiSets.push(data.emojiSetId);
          localStorage.setItem('unlockedEmojiSets', JSON.stringify(unlockedEmojiSets));
        }
        window.dispatchEvent(new CustomEvent('emojiSetStatusUpdated', { detail: data }));
      }
    } catch (error) {
      console.warn('[PaymentService] Failed to update local cache:', error);
    }
  }
}
