/**
 * 基础情绪视图类
 * 实现 EmotionView 接口的抽象基类
 */

import React from 'react';
import {
  Emotion,
  SkinConfig,
  ContentDisplayMode,
  ViewType,
  ViewConfig
} from '@/types';
import { EmotionView } from '@/views/interfaces/EmotionView';

/**
 * 基础情绪视图抽象类
 * 实现 EmotionView 接口的基础功能
 */
export abstract class BaseEmotionView implements EmotionView {
  protected type: ViewType;
  protected contentDisplayMode: ContentDisplayMode;
  protected skinConfig: SkinConfig;
  protected layout?: string;
  protected animation?: any;
  protected use3DEffects: boolean = false;
  protected effects3DConfig?: any;

  /**
   * 构造函数
   * @param type 视图类型
   * @param contentType 内容显示模式
   * @param skinConfig 皮肤配置
   * @param layout 布局（可选）
   */
  constructor(
    type: ViewType,
    contentDisplayMode: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout?: string
  ) {
    this.type = type;
    this.contentDisplayMode = contentDisplayMode;
    this.skinConfig = skinConfig;
    this.layout = layout;
  }

  /**
   * 获取视图类型
   * @returns 视图类型
   */
  getType(): ViewType {
    return this.type;
  }

  /**
   * 获取内容显示模式
   * @returns 内容显示模式
   */
  getContentDisplayMode(): ContentDisplayMode {
    return this.contentDisplayMode;
  }

  /**
   * 获取皮肤配置
   * @returns 皮肤配置
   */
  getSkinConfig(): SkinConfig {
    return this.skinConfig;
  }

  /**
   * 应用皮肤配置
   * @param config 新的皮肤配置
   */
  applySkinConfig(config: SkinConfig): void {
    this.skinConfig = config;
  }

  /**
   * 设置内容显示模式
   * @param mode 新的内容显示模式
   */
  setContentDisplayMode(mode: ContentDisplayMode): void {
    this.contentDisplayMode = mode;
  }

  /**
   * 设置布局
   * @param layout 布局类型
   */
  setLayout(layout: string): void {
    this.layout = layout;
  }

  /**
   * 获取布局
   * @returns 当前布局类型
   */
  getLayout(): string {
    return this.layout || '';
  }

  /**
   * 设置动画效果
   * @param animation 动画效果配置
   */
  setAnimation(animation: any): void {
    this.animation = animation;
  }

  /**
   * 获取动画效果
   * @returns 当前动画效果配置
   */
  getAnimation(): any {
    return this.animation;
  }

  /**
   * 设置3D效果
   * @param use3D 是否使用3D效果
   * @param config 3D效果配置
   */
  set3DEffects(use3D: boolean, config?: any): void {
    this.use3DEffects = use3D;
    this.effects3DConfig = config;
  }

  /**
   * 获取3D效果状态
   * @returns 3D效果状态和配置
   */
  get3DEffects(): { enabled: boolean; config?: any } {
    return {
      enabled: this.use3DEffects,
      config: this.effects3DConfig
    };
  }

  /**
   * 渲染视图
   * 子类必须实现此方法
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  abstract render(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode;
}
