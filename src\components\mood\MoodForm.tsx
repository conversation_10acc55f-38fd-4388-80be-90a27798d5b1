/**
 * MoodForm组件
 * 用于记录情绪详情的表单
 * 支持多层级情绪结构
 */

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLanguage } from '@/contexts/LanguageContext';
import {  EmotionDataSetTier, Emotion } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {TagSuggestions} from '@/components/tags/TagSuggestions';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// 表单数据接口
interface FormData {
  intensity: number;
  tags: string[];
  reflection: string;
}

// 组件属性接口（传统三层结构）
interface TraditionalMoodFormProps {
  selectedPrimary:   Emotion;
  selectedSecondary:  Emotion;
  selectedTertiary:   Emotion;
  onSave: (data: FormData) => void;
  mode?: 'traditional';
}

// 组件属性接口（多层级结构）
interface MultiTierMoodFormProps {
  selectedEmotions: EmotionDataSetTier;
  emotionDataId: string;
  onSave: (data: FormData) => void;
  mode: 'multiTier';
}

// 组合属性接口
type MoodFormProps = TraditionalMoodFormProps | MultiTierMoodFormProps;

/**
 * 情绪记录表单组件
 * 支持传统三层结构和多层级结构
 */
export const MoodForm: React.FC<MoodFormProps> = (props) => {
  const { t } = useLanguage();
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // 初始化表单
  const form = useForm<FormData>({
    defaultValues: {
      intensity: 5,
      tags: [],
      reflection: ''
    }
  });

  // 处理表单提交
  const handleSubmit = (data: FormData) => {
    // 添加选中的标签
    data.tags = selectedTags;

    // 调用保存回调
    props.onSave(data);
  };

  // 处理标签选择
  const handleTagsChange = (tags: string[]) => {
    setSelectedTags(tags);
  };

  // 获取用于标签建议的情绪
  const getEmotionForTagSuggestions = () => {
    if (props.mode === 'multiTier') {
      // 使用多层级结构中的最后一层情绪
      const tiers = Object.keys(props.selectedEmotions).sort((a, b) => {
        const tierA = props.selectedEmotions[a];
        const tierB = props.selectedEmotions[b];
        return (tierB.tier_level || 0) - (tierA.tier_level || 0);
      });

      if (tiers.length > 0) {
        return props.selectedEmotions[tiers[0]];
      }

      return null;
    } else {
      // 使用传统三层结构中的第三层情绪
      return props.selectedTertiary;
    }
  };

  // 渲染选中的情绪摘要
  const renderSelectedEmotionsSummary = () => {
    if (props.mode === 'multiTier') {
      // 渲染多层级结构的情绪摘要
      return (
        <div className="mb-4 p-3 bg-muted rounded-md">
          <h3 className="text-sm font-medium mb-2">{t('mood.form.selected_emotions')}</h3>
          <div className="space-y-1">
            {Object.keys(props.selectedEmotions)
              .sort((a, b) => {
                const tierA = props.selectedEmotions[a];
                const tierB = props.selectedEmotions[b];
                return (tierA.tier_level || 0) - (tierB.tier_level || 0);
              })
              .map(tierId => {
                const emotion = props.selectedEmotions[tierId];
                return (
                  <div key={tierId} className="flex items-center">
                    <span className="text-sm mr-2">
                      {emotion.tier_level && `${t(`mood.tier.level_${emotion.tier_level}`)}: `}
                    </span>
                    <span className="mr-2">{emotion.emoji}</span>
                    <span className="text-sm font-medium">{emotion.name}</span>
                  </div>
                );
              })}
          </div>
        </div>
      );
    } else {
      // 渲染传统三层结构的情绪摘要
      return (
        <div className="mb-4 p-3 bg-muted rounded-md">
          <h3 className="text-sm font-medium mb-2">{t('mood.form.selected_emotions')}</h3>
          <div className="space-y-1">
            <div className="flex items-center">
              <span className="text-sm mr-2">{t('mood.tier.primary')}:</span>
              <span className="mr-2">{props.selectedPrimary.emoji}</span>
              <span className="text-sm font-medium">{props.selectedPrimary.name}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm mr-2">{t('mood.tier.secondary')}:</span>
              <span className="mr-2">{props.selectedSecondary.emoji}</span>
              <span className="text-sm font-medium">{props.selectedSecondary.name}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm mr-2">{t('mood.tier.tertiary')}:</span>
              <span className="mr-2">{props.selectedTertiary.emoji}</span>
              <span className="text-sm font-medium">{props.selectedTertiary.name}</span>
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <Card className="w-full">
      <CardContent className="pt-4">
        {/* 显示选中的情绪摘要 */}
        {renderSelectedEmotionsSummary()}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* 情绪强度滑块 */}
            <FormField
              control={form.control}
              name="intensity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('mood.form.intensity')}</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[field.value]}
                        onValueChange={(value) => field.onChange(value[0])}
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{t('mood.form.intensity_low')}</span>
                        <span>{field.value}</span>
                        <span>{t('mood.form.intensity_high')}</span>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 标签选择 */}
            <div className="space-y-2">
              <Label htmlFor="tags">{t('mood.form.tags')}</Label>
              <TagSuggestions
                selectedTags={selectedTags}
                onChange={handleTagsChange}
                emotion={getEmotionForTagSuggestions()}
              />
            </div>

            {/* 反思文本框 */}
            <FormField
              control={form.control}
              name="reflection"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('mood.form.reflection')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('mood.form.reflection_placeholder')}
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {t('mood.form.reflection_description')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 提交按钮 */}
            <Button type="submit" className="w-full">
              {t('mood.form.save')}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default MoodForm;
