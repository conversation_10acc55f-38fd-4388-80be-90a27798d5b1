/**
 * MobileNavbar 组件测试
 * 测试移动端导航栏组件的各种功能
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import MobileNavbar from '../MobileNavbar';

// Mock dependencies
const mockLanguageContext = {
  t: vi.fn((key: string) => {
    const translations: Record<string, string> = {
      'app.home': '首页',
      'app.history': '历史',
      'app.analytics': '分析',
      'app.settings': '设置'
    };
    return translations[key] || key;
  }),
  language: 'zh-CN',
  isLanguageReady: true,
  setLanguage: vi.fn()
};

vi.mock('@/contexts/LanguageContext', () => ({
  useLanguage: () => mockLanguageContext
}));

// Mock layers
vi.mock('@/lib/layers', () => ({
  LAYERS: {
    NAVIGATION: 1000
  }
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  Home: ({ size }: { size: number }) => <div data-testid="home-icon" data-size={size}>Home Icon</div>,
  Clock: ({ size }: { size: number }) => <div data-testid="clock-icon" data-size={size}>Clock Icon</div>,
  BarChart3: ({ size }: { size: number }) => <div data-testid="chart-icon" data-size={size}>Chart Icon</div>,
  Settings: ({ size }: { size: number }) => <div data-testid="settings-icon" data-size={size}>Settings Icon</div>
}));

// Mock console methods
const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

describe('MobileNavbar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    consoleSpy.mockClear();

    // Reset language context to default state
    mockLanguageContext.isLanguageReady = true;
    mockLanguageContext.language = 'zh-CN';
    mockLanguageContext.t.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'app.home': '首页',
        'app.history': '历史',
        'app.analytics': '分析',
        'app.settings': '设置'
      };
      return translations[key] || key;
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderWithRouter = (initialEntries = ['/']) => {
    return render(
      <MemoryRouter initialEntries={initialEntries}>
        <MobileNavbar />
      </MemoryRouter>
    );
  };

  describe('Basic Rendering', () => {
    it('should render all navigation links', () => {
      renderWithRouter();
      
      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('历史')).toBeInTheDocument();
      expect(screen.getByText('分析')).toBeInTheDocument();
      expect(screen.getByText('设置')).toBeInTheDocument();
    });

    it('should render all icons with correct size', () => {
      renderWithRouter();
      
      expect(screen.getByTestId('home-icon')).toBeInTheDocument();
      expect(screen.getByTestId('clock-icon')).toBeInTheDocument();
      expect(screen.getByTestId('chart-icon')).toBeInTheDocument();
      expect(screen.getByTestId('settings-icon')).toBeInTheDocument();
      
      // Check icon sizes
      expect(screen.getByTestId('home-icon')).toHaveAttribute('data-size', '20');
      expect(screen.getByTestId('clock-icon')).toHaveAttribute('data-size', '20');
      expect(screen.getByTestId('chart-icon')).toHaveAttribute('data-size', '20');
      expect(screen.getByTestId('settings-icon')).toHaveAttribute('data-size', '20');
    });

    it('should have correct navigation structure', () => {
      renderWithRouter();
      
      const nav = screen.getByRole('navigation');
      expect(nav).toBeInTheDocument();
      expect(nav).toHaveClass('fixed', 'bottom-0', 'left-0', 'right-0');
      expect(nav).toHaveStyle({ zIndex: '1000' });
    });

    it('should have correct styling classes', () => {
      renderWithRouter();
      
      const nav = screen.getByRole('navigation');
      expect(nav).toHaveClass(
        'bg-background/80',
        'backdrop-blur-lg',
        'border-t',
        'border-border'
      );
    });
  });

  describe('Navigation Links', () => {
    it('should have correct href attributes', () => {
      renderWithRouter();
      
      const homeLink = screen.getByText('首页').closest('a');
      const historyLink = screen.getByText('历史').closest('a');
      const analyticsLink = screen.getByText('分析').closest('a');
      const settingsLink = screen.getByText('设置').closest('a');
      
      expect(homeLink).toHaveAttribute('href', '/');
      expect(historyLink).toHaveAttribute('href', '/history');
      expect(analyticsLink).toHaveAttribute('href', '/analytics');
      expect(settingsLink).toHaveAttribute('href', '/settings');
    });

    it('should apply active class to current route', () => {
      renderWithRouter(['/history']);
      
      const homeLink = screen.getByText('首页').closest('a');
      const historyLink = screen.getByText('历史').closest('a');
      
      expect(homeLink).toHaveClass('nav-link');
      expect(homeLink).not.toHaveClass('active');
      expect(historyLink).toHaveClass('nav-link', 'active');
    });

    it('should handle home route with end prop', () => {
      renderWithRouter(['/']);
      
      const homeLink = screen.getByText('首页').closest('a');
      expect(homeLink).toHaveClass('nav-link', 'active');
    });

    it('should not apply active class to home when on sub-routes', () => {
      renderWithRouter(['/history']);
      
      const homeLink = screen.getByText('首页').closest('a');
      expect(homeLink).not.toHaveClass('active');
    });
  });

  describe('Language Integration', () => {
    it('should use translations when language is ready', () => {
      mockLanguageContext.isLanguageReady = true;
      renderWithRouter();
      
      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('历史')).toBeInTheDocument();
      expect(screen.getByText('分析')).toBeInTheDocument();
      expect(screen.getByText('设置')).toBeInTheDocument();
      
      expect(mockLanguageContext.t).toHaveBeenCalledWith('app.home');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('app.history');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('app.analytics');
      expect(mockLanguageContext.t).toHaveBeenCalledWith('app.settings');
    });

    it('should use fallback text when language is not ready', () => {
      mockLanguageContext.isLanguageReady = false;
      renderWithRouter();
      
      expect(screen.getByText('Home')).toBeInTheDocument();
      expect(screen.getByText('History')).toBeInTheDocument();
      expect(screen.getByText('Analytics')).toBeInTheDocument();
      expect(screen.getByText('Settings')).toBeInTheDocument();
    });

    it('should log language changes', async () => {
      renderWithRouter();
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('[MobileNavbar] Language changed')
        );
      });
    });

    it('should test translations and log results', async () => {
      renderWithRouter();
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining("[MobileNavbar] Test translation 'app.home': '首页'")
        );
      });
    });

    it('should warn when translation returns key name', async () => {
      mockLanguageContext.t.mockImplementation((key: string) => key);
      renderWithRouter();
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining("[MobileNavbar] Warning: Translation returned key name for 'app.home'")
        );
      });
      
      // Restore mock
      mockLanguageContext.t.mockImplementation((key: string) => {
        const translations: Record<string, string> = {
          'app.home': '首页',
          'app.history': '历史',
          'app.analytics': '分析',
          'app.settings': '设置'
        };
        return translations[key] || key;
      });
    });
  });

  describe('Route Changes', () => {
    it('should update active state when route changes', () => {
      // Test with separate renders for different routes
      const { unmount: unmount1 } = renderWithRouter(['/']);

      let homeLink = screen.getByText('首页').closest('a');
      let historyLink = screen.getByText('历史').closest('a');

      expect(homeLink).toHaveClass('active');
      expect(historyLink).not.toHaveClass('active');

      unmount1();

      // Render with different route
      const { unmount: unmount2 } = renderWithRouter(['/history']);

      homeLink = screen.getByText('首页').closest('a');
      historyLink = screen.getByText('历史').closest('a');

      expect(homeLink).not.toHaveClass('active');
      expect(historyLink).toHaveClass('active');

      unmount2();
    });

    it('should handle all route states correctly', () => {
      // Ensure language is ready for this test
      mockLanguageContext.isLanguageReady = true;

      const routes = [
        { path: '/', expectedActive: '首页' },
        { path: '/history', expectedActive: '历史' },
        { path: '/analytics', expectedActive: '分析' },
        { path: '/settings', expectedActive: '设置' }
      ];

      routes.forEach(({ path, expectedActive }) => {
        const { unmount } = renderWithRouter([path]);

        const activeLink = screen.getByText(expectedActive).closest('a');
        expect(activeLink).toHaveClass('active');

        unmount();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper navigation role', () => {
      renderWithRouter();
      
      const nav = screen.getByRole('navigation');
      expect(nav).toBeInTheDocument();
    });

    it('should have clickable links', () => {
      // Ensure language is ready for this test
      mockLanguageContext.isLanguageReady = true;
      renderWithRouter();

      const homeLink = screen.getByText('首页').closest('a');
      const historyLink = screen.getByText('历史').closest('a');
      const analyticsLink = screen.getByText('分析').closest('a');
      const settingsLink = screen.getByText('设置').closest('a');

      expect(homeLink).toBeInTheDocument();
      expect(historyLink).toBeInTheDocument();
      expect(analyticsLink).toBeInTheDocument();
      expect(settingsLink).toBeInTheDocument();

      // Test that links are focusable
      homeLink?.focus();
      expect(document.activeElement).toBe(homeLink);
    });

    it('should support keyboard navigation', () => {
      // Ensure language is ready for this test
      mockLanguageContext.isLanguageReady = true;
      renderWithRouter();

      const homeLink = screen.getByText('首页').closest('a');
      const historyLink = screen.getByText('历史').closest('a');

      homeLink?.focus();
      expect(document.activeElement).toBe(homeLink);

      // Simulate Tab key to move to next link
      fireEvent.keyDown(homeLink!, { key: 'Tab' });
      historyLink?.focus();
      expect(document.activeElement).toBe(historyLink);
    });
  });

  describe('Responsive Design', () => {
    it('should have mobile-optimized layout', () => {
      renderWithRouter();
      
      const container = screen.getByRole('navigation').firstChild;
      expect(container).toHaveClass(
        'max-w-[550px]',
        'mx-auto',
        'flex',
        'justify-around',
        'items-center',
        'h-16',
        'px-2'
      );
    });

    it('should have safe area padding', () => {
      renderWithRouter();
      
      const nav = screen.getByRole('navigation');
      expect(nav).toHaveStyle({
        paddingBottom: 'env(safe-area-inset-bottom)'
      });
    });

    it('should be fixed at bottom', () => {
      renderWithRouter();
      
      const nav = screen.getByRole('navigation');
      expect(nav).toHaveClass('fixed', 'bottom-0', 'left-0', 'right-0');
    });
  });

  describe('Performance', () => {
    it('should not cause unnecessary re-renders', () => {
      const renderSpy = vi.fn();
      
      const TestWrapper = () => {
        renderSpy();
        return <MobileNavbar />;
      };
      
      const { rerender } = render(
        <MemoryRouter initialEntries={['/']}>
          <TestWrapper />
        </MemoryRouter>
      );
      
      expect(renderSpy).toHaveBeenCalledTimes(1);
      
      // Re-render with same route should not cause unnecessary renders
      rerender(
        <MemoryRouter initialEntries={['/']}>
          <TestWrapper />
        </MemoryRouter>
      );
      
      expect(renderSpy).toHaveBeenCalledTimes(2); // Expected for rerender
    });

    it('should handle rapid route changes', () => {
      const routes = ['/', '/history', '/analytics', '/settings'];
      
      routes.forEach(route => {
        const { unmount } = renderWithRouter([route]);
        
        // Should render without errors
        expect(screen.getByRole('navigation')).toBeInTheDocument();
        
        unmount();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle missing translations gracefully', () => {
      mockLanguageContext.t.mockImplementation(() => {
        throw new Error('Translation error');
      });
      
      expect(() => {
        renderWithRouter();
      }).toThrow('Translation error');
      
      // Restore mock
      mockLanguageContext.t.mockImplementation((key: string) => {
        const translations: Record<string, string> = {
          'app.home': '首页',
          'app.history': '历史',
          'app.analytics': '分析',
          'app.settings': '设置'
        };
        return translations[key] || key;
      });
    });

    it('should handle language context changes', () => {
      // Ensure language is ready for this test
      mockLanguageContext.isLanguageReady = true;
      const { rerender } = renderWithRouter();

      expect(screen.getByText('首页')).toBeInTheDocument();

      // Change language context
      mockLanguageContext.language = 'en-US';
      mockLanguageContext.t.mockImplementation((key: string) => {
        const translations: Record<string, string> = {
          'app.home': 'Home',
          'app.history': 'History',
          'app.analytics': 'Analytics',
          'app.settings': 'Settings'
        };
        return translations[key] || key;
      });

      rerender(
        <MemoryRouter initialEntries={['/']}>
          <MobileNavbar />
        </MemoryRouter>
      );

      expect(screen.getByText('Home')).toBeInTheDocument();
    });
  });
});
