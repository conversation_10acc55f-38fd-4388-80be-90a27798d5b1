/**
 * useNetworkStatus Hook 测试
 * 测试网络状态监控功能
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useNetworkStatus } from '../useNetworkStatus';

// Mock Capacitor
vi.mock('@capacitor/network', () => ({
  Network: {
    getStatus: vi.fn(),
    addListener: vi.fn(),
  }
}));

vi.mock('@capacitor/core', () => ({
  Capacitor: {
    isNativePlatform: vi.fn(),
  }
}));

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true,
});

// Mock navigator.connection
Object.defineProperty(navigator, 'connection', {
  writable: true,
  value: {
    effectiveType: '4g'
  },
});

describe('useNetworkStatus', () => {
  let mockListener: any;
  let mockNetwork: any;
  let mockCapacitor: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    navigator.onLine = true;
    (navigator as any).connection = { effectiveType: '4g' };

    // Get mocked modules
    const { Network } = await import('@capacitor/network');
    const { Capacitor } = await import('@capacitor/core');
    mockNetwork = Network;
    mockCapacitor = Capacitor;

    // Setup mock listener
    mockListener = {
      remove: vi.fn()
    };

    mockNetwork.addListener.mockReturnValue(mockListener);
    mockCapacitor.isNativePlatform.mockReturnValue(false);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Web platform', () => {
    beforeEach(() => {
      mockCapacitor.isNativePlatform.mockReturnValue(false);
    });

    it('should initialize with online status', async () => {
      navigator.onLine = true;

      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isOnline).toBe(true);
        expect(result.current.isConnected).toBe(true);
        expect(result.current.connectionType).toBe('4g');
        expect(result.current.lastOnlineTime).toBe(null);
        expect(result.current.lastOfflineTime).toBe(null);
      });
    });

    it('should initialize with offline status', async () => {
      navigator.onLine = false;

      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isOnline).toBe(false);
        expect(result.current.isConnected).toBe(false);
      });
    });

    it('should handle online/offline events', async () => {
      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isOnline).toBe(true);
      });

      // Simulate going offline
      act(() => {
        navigator.onLine = false;
        window.dispatchEvent(new Event('offline'));
      });

      await waitFor(() => {
        expect(result.current.isOnline).toBe(false);
        expect(result.current.lastOfflineTime).toBeInstanceOf(Date);
      });

      // Simulate going online
      act(() => {
        navigator.onLine = true;
        window.dispatchEvent(new Event('online'));
      });

      await waitFor(() => {
        expect(result.current.isOnline).toBe(true);
        expect(result.current.lastOnlineTime).toBeInstanceOf(Date);
      });
    });

    it('should detect connection type changes', async () => {
      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.connectionType).toBe('4g');
      });

      // Change connection type
      act(() => {
        (navigator as any).connection.effectiveType = '3g';
        window.dispatchEvent(new Event('online'));
      });

      await waitFor(() => {
        expect(result.current.connectionType).toBe('3g');
      });
    });

    it('should handle missing connection API gracefully', async () => {
      // Mock undefined connection
      Object.defineProperty(navigator, 'connection', {
        writable: true,
        value: undefined
      });

      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.connectionType).toBe('unknown');
      });
    });

    it('should clean up event listeners on unmount', async () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener');
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');

      const { unmount } = renderHook(() => useNetworkStatus());

      // Wait for initialization to complete
      await waitFor(() => {
        expect(addEventListenerSpy).toHaveBeenCalledWith('online', expect.any(Function));
        expect(addEventListenerSpy).toHaveBeenCalledWith('offline', expect.any(Function));
      });

      unmount();

      // Wait a bit for cleanup to happen
      await waitFor(() => {
        expect(removeEventListenerSpy).toHaveBeenCalledWith('online', expect.any(Function));
        expect(removeEventListenerSpy).toHaveBeenCalledWith('offline', expect.any(Function));
      });

      addEventListenerSpy.mockRestore();
      removeEventListenerSpy.mockRestore();
    });
  });

  describe('Native platform', () => {
    beforeEach(() => {
      mockCapacitor.isNativePlatform.mockReturnValue(true);
    });

    it('should initialize with network status from Capacitor', async () => {
      const mockStatus = {
        connected: true,
        connectionType: 'wifi'
      };

      mockNetwork.getStatus.mockResolvedValue(mockStatus);

      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isOnline).toBe(true);
        expect(result.current.connectionType).toBe('wifi');
      });

      expect(mockNetwork.getStatus).toHaveBeenCalled();
      expect(mockNetwork.addListener).toHaveBeenCalledWith(
        'networkStatusChange',
        expect.any(Function)
      );
    });

    it('should handle network status changes from Capacitor', async () => {
      const initialStatus = {
        connected: true,
        connectionType: 'wifi'
      };

      mockNetwork.getStatus.mockResolvedValue(initialStatus);

      let statusChangeCallback: (status: any) => void;
      mockNetwork.addListener.mockImplementation((event, callback) => {
        statusChangeCallback = callback;
        return mockListener;
      });

      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isOnline).toBe(true);
        expect(result.current.connectionType).toBe('wifi');
      });

      // Simulate network status change
      act(() => {
        statusChangeCallback!({
          connected: false,
          connectionType: 'none'
        });
      });

      await waitFor(() => {
        expect(result.current.isOnline).toBe(false);
        expect(result.current.connectionType).toBe('none');
        expect(result.current.lastOfflineTime).toBeInstanceOf(Date);
      });
    });

    it('should handle Capacitor API errors gracefully', async () => {
      mockNetwork.getStatus.mockRejectedValue(new Error('Network API error'));

      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        // Should default to online when API fails
        expect(result.current.isOnline).toBe(true);
        expect(result.current.connectionType).toBe('unknown');
      });
    });

    it('should clean up Capacitor listener on unmount', async () => {
      mockNetwork.getStatus.mockResolvedValue({
        connected: true,
        connectionType: 'wifi'
      });

      const { unmount } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(mockNetwork.addListener).toHaveBeenCalled();
      });

      unmount();

      expect(mockListener.remove).toHaveBeenCalled();
    });
  });

  describe('Status change tracking', () => {
    beforeEach(() => {
      mockCapacitor.isNativePlatform.mockReturnValue(false);
    });

    it('should track last online time when going online', async () => {
      navigator.onLine = false;

      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isOnline).toBe(false);
      });

      const beforeOnline = new Date();

      act(() => {
        navigator.onLine = true;
        window.dispatchEvent(new Event('online'));
      });

      await waitFor(() => {
        expect(result.current.isOnline).toBe(true);
        expect(result.current.lastOnlineTime).toBeInstanceOf(Date);
        expect(result.current.lastOnlineTime!.getTime()).toBeGreaterThanOrEqual(beforeOnline.getTime());
      });
    });

    it('should track last offline time when going offline', async () => {
      navigator.onLine = true;

      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isOnline).toBe(true);
      });

      const beforeOffline = new Date();

      act(() => {
        navigator.onLine = false;
        window.dispatchEvent(new Event('offline'));
      });

      await waitFor(() => {
        expect(result.current.isOnline).toBe(false);
        expect(result.current.lastOfflineTime).toBeInstanceOf(Date);
        expect(result.current.lastOfflineTime!.getTime()).toBeGreaterThanOrEqual(beforeOffline.getTime());
      });
    });

    it('should not update timestamps when status does not change', async () => {
      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isOnline).toBe(true);
      });

      // Trigger event without status change
      act(() => {
        window.dispatchEvent(new Event('online'));
      });

      await waitFor(() => {
        expect(result.current.lastOnlineTime).toBe(null);
        expect(result.current.lastOfflineTime).toBe(null);
      });
    });
  });

  describe('Return value consistency', () => {
    it('should ensure isConnected matches isOnline', async () => {
      const { result } = renderHook(() => useNetworkStatus());

      await waitFor(() => {
        expect(result.current.isConnected).toBe(result.current.isOnline);
      });

      act(() => {
        navigator.onLine = false;
        window.dispatchEvent(new Event('offline'));
      });

      await waitFor(() => {
        expect(result.current.isConnected).toBe(result.current.isOnline);
        expect(result.current.isConnected).toBe(false);
      });
    });
  });
});
