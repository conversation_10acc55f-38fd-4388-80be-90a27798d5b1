/* Quiz基础组件样式 */

/* ==================== 全局变量 ==================== */
:root {
  /* 中医文化色彩 */
  --tcm-red: #D32F2F;
  --tcm-gold: #FFD700;
  --tcm-jade: #4CAF50;
  --tcm-bamboo: #8BC34A;
  --tcm-ink: #212121;
  --tcm-paper: #FFF8E1;

  /* 字体系统 */
  --font-modern: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-traditional: 'PingFang SC', 'Source Han Sans', sans-serif;
  --font-calligraphy: 'STKaiti', 'KaiTi', serif;

  /* 尺寸系统 */
  --font-size-small: 14px;
  --font-size-medium: 17px;
  --font-size-large: 20px;
  --font-size-title: 24px;

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 动画时序 */
  --animation-fast: 150ms;
  --animation-normal: 300ms;
  --animation-slow: 600ms;

  /* 阴影系统 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
}

/* ==================== 文本组件样式 ==================== */
.quiz-text-component {
  position: relative;
  word-wrap: break-word;
  transition: all var(--animation-normal) ease-out;
}

/* 字体族样式 */
.font-modern {
  font-family: var(--font-modern);
}

.font-traditional {
  font-family: var(--font-traditional);
}

.font-calligraphy {
  font-family: var(--font-calligraphy);
  font-weight: 400;
}

.font-seal-script {
  font-family: 'STXinwei', 'STKaiti', serif;
  font-weight: bold;
  letter-spacing: 0.1em;
}

.font-clerical-script {
  font-family: 'STLiti', 'STKaiti', serif;
  font-weight: 500;
  letter-spacing: 0.05em;
}

/* 文本尺寸 */
.text-tiny {
  font-size: 12px;
}

.text-small {
  font-size: var(--font-size-small);
}

.text-medium {
  font-size: var(--font-size-medium);
}

.text-large {
  font-size: var(--font-size-large);
}

.text-title {
  font-size: var(--font-size-title);
  font-weight: 600;
}

.text-display {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
}

/* 文本对齐 */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

/* 标准文本布局 */
.quiz-text-standard {
  padding: var(--spacing-sm);
}

/* 对话气泡布局 */
.quiz-text-dialogue-bubble {
  position: relative;
  background: var(--tcm-paper);
  border: 2px solid var(--tcm-gold);
  border-radius: 16px;
  padding: var(--spacing-md);
  margin: var(--spacing-md);
  box-shadow: var(--shadow-md);
}

.dialogue-bubble-arrow {
  position: absolute;
  bottom: -10px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid var(--tcm-gold);
}

.dialogue-bubble-arrow::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid var(--tcm-paper);
}

/* 卷轴文本布局 */
.quiz-text-scroll {
  position: relative;
  background: linear-gradient(45deg, #F5F5DC, #FFF8E1);
  border: 3px solid var(--tcm-gold);
  border-radius: 20px;
  padding: var(--spacing-lg);
  margin: var(--spacing-md);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.quiz-text-scroll::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 10px;
  right: 10px;
  height: 10px;
  background: repeating-linear-gradient(
    90deg,
    var(--tcm-gold),
    var(--tcm-gold) 2px,
    transparent 2px,
    transparent 8px
  );
  border-radius: 5px;
}

.quiz-text-scroll::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 10px;
  right: 10px;
  height: 10px;
  background: repeating-linear-gradient(
    90deg,
    var(--tcm-gold),
    var(--tcm-gold) 2px,
    transparent 2px,
    transparent 8px
  );
  border-radius: 5px;
}

/* 碑文文本布局 */
.quiz-text-inscription {
  position: relative;
  background: linear-gradient(135deg, #424242, #616161);
  color: var(--tcm-gold);
  border: 4px solid #2E2E2E;
  border-radius: 8px;
  padding: var(--spacing-lg);
  margin: var(--spacing-md);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.4);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.quiz-text-inscription::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 4px;
  pointer-events: none;
}

/* 浮动文本布局 */
.quiz-text-floating {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: var(--spacing-md);
  margin: var(--spacing-md);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  animation: quiz-text-float 3s ease-in-out infinite;
}

@keyframes quiz-text-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

/* 横幅文本布局 */
.quiz-text-banner {
  position: relative;
  background: linear-gradient(90deg, var(--tcm-red), var(--tcm-gold), var(--tcm-red));
  color: white;
  text-align: center;
  padding: var(--spacing-md) var(--spacing-xl);
  margin: var(--spacing-md) 0;
  border-radius: 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.quiz-text-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: quiz-banner-shine 3s ease-in-out infinite;
}

@keyframes quiz-banner-shine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

/* 背景图案 */
.bg-pattern-bamboo {
  background-image:
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      rgba(139, 195, 74, 0.1) 10px,
      rgba(139, 195, 74, 0.1) 20px
    );
}

.bg-pattern-cloud {
  background-image:
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.2) 20%, transparent 21%),
    radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.2) 20%, transparent 21%);
}

.bg-pattern-wave {
  background-image:
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 8px,
      rgba(33, 150, 243, 0.1) 8px,
      rgba(33, 150, 243, 0.1) 16px
    );
}

.bg-pattern-mountain {
  background-image:
    linear-gradient(135deg, transparent 40%, rgba(76, 175, 80, 0.1) 40%, rgba(76, 175, 80, 0.1) 60%, transparent 60%);
}

/* 强调文本 */
.text-emphasis {
  color: var(--tcm-red);
  font-weight: 600;
  position: relative;
}

.text-emphasis::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--tcm-red), var(--tcm-gold));
  border-radius: 1px;
}

/* ==================== 按钮组件样式 ==================== */
.quiz-button-component {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  border: none;
  border-radius: 8px;
  font-family: inherit;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.quiz-button-component:focus {
  outline: 2px solid var(--tcm-jade);
  outline-offset: 2px;
}

/* 按钮尺寸 */
.quiz-button-small {
  height: 32px;
  padding: 0 12px;
  font-size: 14px;
  min-width: 64px;
}

.quiz-button-medium {
  height: 44px;
  padding: 0 16px;
  font-size: 17px;
  min-width: 88px;
}

.quiz-button-large {
  height: 56px;
  padding: 0 24px;
  font-size: 20px;
  min-width: 112px;
}

/* 按钮变体 */
.quiz-button-primary {
  background: linear-gradient(135deg, var(--tcm-red), #E57373);
  color: white;
  box-shadow: var(--shadow-md);
}

.quiz-button-primary:hover {
  background: linear-gradient(135deg, #B71C1C, var(--tcm-red));
  box-shadow: var(--shadow-lg);
}

.quiz-button-secondary {
  background: linear-gradient(135deg, var(--tcm-jade), #81C784);
  color: white;
  box-shadow: var(--shadow-md);
}

.quiz-button-outline {
  background: transparent;
  border: 2px solid var(--tcm-red);
  color: var(--tcm-red);
}

.quiz-button-ghost {
  background: transparent;
  color: var(--tcm-ink);
}

.quiz-button-ghost:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* 按钮形状 */
.quiz-button-rectangle {
  border-radius: 4px;
}

.quiz-button-rounded {
  border-radius: 8px;
}

.quiz-button-pill {
  border-radius: 50px;
}

/* 特殊布局 - 玉佩按钮 */
.quiz-button-jade-pendant {
  background: linear-gradient(135deg, #4CAF50, #81C784);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  box-shadow:
    0 4px 8px rgba(76, 175, 80, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
}

.quiz-button-jade-pendant::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

/* 特殊布局 - 印章按钮 */
.quiz-button-seal-stamp {
  background: linear-gradient(135deg, var(--tcm-red), #E57373);
  border-radius: 4px;
  position: relative;
  font-family: var(--font-calligraphy);
  font-weight: bold;
  letter-spacing: 2px;
}

.quiz-button-seal-stamp::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 2px;
}

/* 按钮状态 */
.quiz-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.quiz-button-loading {
  pointer-events: none;
}

.quiz-button-loading .quiz-button-text {
  opacity: 0.7;
}

/* 按钮图标 */
.quiz-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.quiz-button-icon-left {
  order: -1;
}

.quiz-button-icon-right {
  order: 1;
}

/* 加载动画 */
.quiz-button-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: quiz-button-spin 1s linear infinite;
}

@keyframes quiz-button-spin {
  to {
    transform: rotate(360deg);
  }
}

/* 涟漪效果 */
.quiz-button-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  animation: quiz-button-ripple-effect 0.6s ease-out;
}

@keyframes quiz-button-ripple-effect {
  to {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* 脉冲动画 */
@keyframes quiz-button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
  }
}

/* ==================== 选择器组件样式 ==================== */
.quiz-selector-component {
  position: relative;
  width: 100%;
}

/* 垂直列表布局 */
.quiz-selector-vertical-list .quiz-selector-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 水平流式布局 */
.quiz-selector-horizontal-flow .quiz-selector-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

/* 网格布局 */
.quiz-selector-grid-layout .quiz-selector-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

/* 选项样式 */
.quiz-selector-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid transparent;
  border-radius: 8px;
  background: var(--tcm-paper);
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
  user-select: none;
  position: relative;
}

.quiz-selector-option:hover {
  background: #F5F5F5;
  border-color: var(--tcm-jade);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.quiz-selector-option-selected {
  background: linear-gradient(135deg, var(--tcm-jade), #81C784);
  color: white;
  border-color: var(--tcm-jade);
  box-shadow: var(--shadow-md);
}

.quiz-selector-option-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.quiz-selector-option-focused {
  outline: 2px solid var(--tcm-jade);
  outline-offset: 2px;
}

/* 选择标记 */
.quiz-selector-marker {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all var(--animation-fast) ease-out;
}

.quiz-selector-marker-circle {
  border-radius: 50%;
}

.quiz-selector-marker-square {
  border-radius: 4px;
}

.quiz-selector-marker-chinese_marker {
  border-radius: 4px;
  background: linear-gradient(45deg, var(--tcm-gold), #FFE082);
  border-color: var(--tcm-gold);
}

.quiz-selector-option-selected .quiz-selector-marker {
  border-color: white;
  background: white;
}

.quiz-selector-checkmark {
  width: 12px;
  height: 12px;
  background: var(--tcm-jade);
  border-radius: 2px;
  position: relative;
}

.quiz-selector-checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

/* 选项图标 */
.quiz-selector-option-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 选项文本 */
.quiz-selector-option-text {
  flex: 1;
  font-weight: 500;
}

/* 卡片样式选项 */
.quiz-selector-option-card_style {
  flex-direction: column;
  text-align: center;
  padding: var(--spacing-md);
  min-height: 80px;
  justify-content: center;
}

.quiz-selector-option-card_style .quiz-selector-marker {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* 错误状态 */
.quiz-selector-error .quiz-selector-options {
  border: 2px solid var(--tcm-red);
  border-radius: 8px;
  padding: var(--spacing-sm);
}

.quiz-selector-error-message {
  color: var(--tcm-red);
  font-size: var(--font-size-small);
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(211, 47, 47, 0.1);
  border-radius: 4px;
  border-left: 4px solid var(--tcm-red);
}

/* 弹跳动画 */
@keyframes quiz-selector-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 428px) {
  .quiz-selector-grid-layout .quiz-selector-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .quiz-button-large {
    height: 48px;
    font-size: 18px;
  }

  .quiz-text-dialogue-bubble {
    margin: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
}

@media (max-width: 320px) {
  .quiz-selector-grid-layout .quiz-selector-options {
    grid-template-columns: 1fr;
  }
}

/* ==================== 可访问性增强 ==================== */
@media (prefers-reduced-motion: reduce) {
  .quiz-text-component,
  .quiz-button-component,
  .quiz-selector-option {
    transition: none;
    animation: none;
  }
}

@media (prefers-contrast: high) {
  .quiz-text-component,
  .quiz-button-component,
  .quiz-selector-option {
    border-width: 2px;
    border-style: solid;
  }
}

/* 大字体支持 */
.large-text .quiz-text-component {
  font-size: calc(var(--font-size-base) * 1.2);
}

.large-text .quiz-button-component {
  font-size: calc(var(--font-size-base) * 1.2);
  padding: calc(var(--spacing-md) * 1.2);
}

/* ==================== 滑块组件样式 ==================== */
.quiz-slider-component {
  position: relative;
  width: 100%;
  padding: var(--spacing-md);
  user-select: none;
  touch-action: none;
}

.quiz-slider-component:focus {
  outline: 2px solid var(--tcm-jade);
  outline-offset: 2px;
}

/* 水平滑块 */
.quiz-slider-horizontal {
  height: 60px;
}

.quiz-slider-horizontal .quiz-slider-track {
  width: 100%;
  height: 8px;
  margin: 26px 0;
}

.quiz-slider-horizontal .quiz-slider-thumb {
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
}

/* 垂直滑块 */
.quiz-slider-vertical {
  width: 60px;
  height: 200px;
  display: inline-block;
}

.quiz-slider-vertical .quiz-slider-track {
  width: 8px;
  height: 100%;
  margin: 0 26px;
}

.quiz-slider-vertical .quiz-slider-thumb {
  left: 50%;
  transform: translateX(-50%) translateY(50%);
}

/* 滑块轨道样式 */
.quiz-slider-track {
  position: relative;
  background: #E0E0E0;
  border-radius: 4px;
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
}

.quiz-slider-track-line {
  background: linear-gradient(to right, #E0E0E0, #BDBDBD);
  border-radius: 2px;
}

.quiz-slider-track-groove {
  background: #F5F5F5;
  border: 2px inset #E0E0E0;
  border-radius: 6px;
}

.quiz-slider-track-bamboo {
  background:
    linear-gradient(90deg, var(--tcm-bamboo) 0%, #A5D6A7 50%, var(--tcm-bamboo) 100%),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 20px,
      rgba(139, 195, 74, 0.3) 20px,
      rgba(139, 195, 74, 0.3) 22px
    );
  border-radius: 8px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quiz-slider-track-ink_brush {
  background: linear-gradient(90deg, #424242, #616161, #424242);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.quiz-slider-track-ink_brush::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
}

.quiz-slider-track-dragon_spine {
  background:
    linear-gradient(45deg, #8D6E63, #A1887F),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 8px,
      rgba(139, 69, 19, 0.2) 8px,
      rgba(139, 69, 19, 0.2) 12px
    );
  border-radius: 6px;
  box-shadow:
    inset 0 1px 2px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(255, 255, 255, 0.3);
}

.quiz-slider-track-mountain_ridge {
  background: linear-gradient(90deg, #4CAF50, #66BB6A, #4CAF50);
  border-radius: 4px;
  position: relative;
}

.quiz-slider-track-mountain_ridge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 4px,
      rgba(255, 255, 255, 0.1) 4px,
      rgba(255, 255, 255, 0.1) 6px
    );
}

.quiz-slider-track-river_flow {
  background:
    linear-gradient(90deg, #2196F3, #42A5F5, #2196F3),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 3px,
      rgba(255, 255, 255, 0.2) 3px,
      rgba(255, 255, 255, 0.2) 4px
    );
  border-radius: 10px;
  animation: quiz-slider-river-flow 2s ease-in-out infinite;
}

@keyframes quiz-slider-river-flow {
  0%, 100% { box-shadow: 0 0 5px rgba(33, 150, 243, 0.3); }
  50% { box-shadow: 0 0 15px rgba(33, 150, 243, 0.6); }
}

/* 滑块进度 */
.quiz-slider-progress {
  position: absolute;
  top: 0;
  left: 0;
  background: var(--slider-color, var(--tcm-jade));
  border-radius: inherit;
  transition: all var(--animation-fast) ease-out;
  pointer-events: none;
}

.quiz-slider-gradient .quiz-slider-progress {
  background: linear-gradient(90deg, var(--slider-color, var(--tcm-jade)), #81C784);
}

.quiz-slider-glow .quiz-slider-progress {
  box-shadow: 0 0 10px var(--slider-color, var(--tcm-jade));
}

/* 滑块手柄样式 */
.quiz-slider-thumb {
  position: absolute;
  width: 24px;
  height: 24px;
  background: white;
  border: 2px solid var(--slider-color, var(--tcm-jade));
  border-radius: 50%;
  cursor: grab;
  transition: all var(--animation-normal) ease-out;
  box-shadow: var(--shadow-md);
  z-index: 2;
}

.quiz-slider-thumb:hover {
  transform: translateX(-50%) translateY(-50%) scale(1.1);
  box-shadow: var(--shadow-lg);
}

.quiz-slider-dragging .quiz-slider-thumb {
  cursor: grabbing;
  transform: translateX(-50%) translateY(-50%) scale(1.2);
  box-shadow: var(--shadow-xl);
}

.quiz-slider-thumb-square {
  border-radius: 4px;
}

.quiz-slider-thumb-panda_paw {
  background:
    radial-gradient(circle at 30% 30%, #333 20%, transparent 21%),
    radial-gradient(circle at 70% 30%, #333 20%, transparent 21%),
    radial-gradient(circle at 50% 70%, #333 30%, transparent 31%),
    white;
  border-color: #333;
}

.quiz-slider-thumb-jade_bead {
  background: linear-gradient(135deg, var(--tcm-jade), #81C784);
  border-color: #2E7D32;
  box-shadow:
    inset 0 1px 2px rgba(255, 255, 255, 0.3),
    0 2px 8px rgba(76, 175, 80, 0.4);
}

.quiz-slider-thumb-lotus_petal {
  background:
    radial-gradient(ellipse at center, #FFB74D 30%, #FF8A65 70%);
  border-color: #E65100;
  border-radius: 50% 10% 50% 10%;
  transform-origin: center;
}

.quiz-slider-thumb-yin_yang {
  background:
    linear-gradient(90deg, black 50%, white 50%),
    radial-gradient(circle at 25% 50%, white 25%, transparent 26%),
    radial-gradient(circle at 75% 50%, black 25%, transparent 26%);
  border-color: #666;
}

.quiz-slider-thumb-coin {
  background:
    radial-gradient(circle at center, var(--tcm-gold) 60%, #FFB300 80%);
  border-color: #FF8F00;
  position: relative;
}

.quiz-slider-thumb-coin::before {
  content: '¥';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
  color: #8D6E63;
}

.quiz-slider-thumb-pearl {
  background:
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8) 20%, transparent 21%),
    radial-gradient(circle at center, #F8BBD9, #E1BEE7);
  border-color: #AD1457;
  box-shadow:
    inset 0 1px 3px rgba(255, 255, 255, 0.5),
    0 2px 8px rgba(233, 30, 99, 0.3);
}

/* 刻度标记 */
.quiz-slider-ticks {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.quiz-slider-tick {
  position: absolute;
  background: #999;
  pointer-events: none;
}

.quiz-slider-horizontal .quiz-slider-tick-dots {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
}

.quiz-slider-horizontal .quiz-slider-tick-lines {
  width: 2px;
  height: 12px;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
}

.quiz-slider-horizontal .quiz-slider-tick-bamboo_nodes {
  width: 6px;
  height: 6px;
  background: var(--tcm-bamboo);
  border: 1px solid #2E7D32;
  border-radius: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
}

.quiz-slider-horizontal .quiz-slider-tick-lotus_buds {
  width: 8px;
  height: 8px;
  background: #FFB74D;
  border-radius: 50% 10% 50% 10%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
}

/* 值显示 */
.quiz-slider-value-display {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--tcm-ink);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  pointer-events: none;
}

.quiz-slider-value-display::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--tcm-ink);
}

/* 标签 */
.quiz-slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-sm);
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
}

.quiz-slider-vertical .quiz-slider-labels {
  flex-direction: column;
  height: 100%;
  margin-top: 0;
  margin-left: var(--spacing-sm);
}

/* 状态样式 */
.quiz-slider-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.quiz-slider-disabled .quiz-slider-thumb {
  cursor: not-allowed;
}

/* ==================== 评分组件样式 ==================== */
.quiz-rating-component {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  user-select: none;
}

.quiz-rating-component:focus {
  outline: 2px solid var(--tcm-jade);
  outline-offset: 2px;
}

/* 水平布局 */
.quiz-rating-horizontal {
  flex-direction: row;
}

.quiz-rating-horizontal .quiz-rating-markers {
  display: flex;
  flex-direction: row;
}

/* 垂直布局 */
.quiz-rating-vertical {
  flex-direction: column;
}

.quiz-rating-vertical .quiz-rating-markers {
  display: flex;
  flex-direction: column;
}

/* 评分标记容器 */
.quiz-rating-markers {
  display: flex;
  gap: inherit;
}

/* 评分标记 */
.quiz-rating-marker {
  position: relative;
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.quiz-rating-marker:focus {
  outline: 2px solid var(--tcm-jade);
  outline-offset: 2px;
}

/* 尺寸样式 */
.quiz-rating-small .quiz-rating-marker {
  width: 20px;
  height: 20px;
  font-size: 16px;
}

.quiz-rating-medium .quiz-rating-marker {
  width: 28px;
  height: 28px;
  font-size: 24px;
}

.quiz-rating-large .quiz-rating-marker {
  width: 36px;
  height: 36px;
  font-size: 32px;
}

/* 标记类型样式 */
.quiz-rating-marker-stars .quiz-rating-icon {
  color: #E0E0E0;
  transition: color var(--animation-normal) ease-out;
}

.quiz-rating-marker-stars.quiz-rating-marker-active .quiz-rating-icon {
  color: var(--tcm-gold);
}

.quiz-rating-marker-hearts .quiz-rating-icon {
  color: #E0E0E0;
  transition: color var(--animation-normal) ease-out;
}

.quiz-rating-marker-hearts.quiz-rating-marker-active .quiz-rating-icon {
  color: #E91E63;
}

.quiz-rating-marker-diamonds .quiz-rating-icon {
  color: #E0E0E0;
  transition: color var(--animation-normal) ease-out;
}

.quiz-rating-marker-diamonds.quiz-rating-marker-active .quiz-rating-icon {
  color: #9C27B0;
}

.quiz-rating-marker-dots .quiz-rating-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #E0E0E0;
  transition: background var(--animation-normal) ease-out;
}

.quiz-rating-marker-dots.quiz-rating-marker-active .quiz-rating-dot {
  background: var(--tcm-jade);
}

.quiz-rating-marker-lotus .quiz-rating-icon {
  color: #E0E0E0;
  transition: color var(--animation-normal) ease-out;
}

.quiz-rating-marker-lotus.quiz-rating-marker-active .quiz-rating-icon {
  color: #FF9800;
}

.quiz-rating-marker-gourd .quiz-rating-icon {
  color: #E0E0E0;
  transition: color var(--animation-normal) ease-out;
}

.quiz-rating-marker-gourd.quiz-rating-marker-active .quiz-rating-icon {
  color: #FF5722;
}

.quiz-rating-marker-taiji .quiz-rating-icon {
  color: #E0E0E0;
  transition: color var(--animation-normal) ease-out;
}

.quiz-rating-marker-taiji.quiz-rating-marker-active .quiz-rating-icon {
  color: var(--tcm-ink);
}

/* 悬停效果 */
.quiz-rating-marker-hover-scale {
  transform: scale(1.2);
}

.quiz-rating-marker-hover-glow {
  filter: drop-shadow(0 0 8px currentColor);
}

.quiz-rating-marker-hover-color .quiz-rating-icon {
  color: var(--tcm-gold) !important;
}

.quiz-rating-marker-hover-bounce {
  animation: quiz-rating-bounce 0.3s ease-out;
}

@keyframes quiz-rating-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

/* 填充动画 */
.quiz-rating-marker-animate-fill {
  animation: quiz-rating-fill 0.3s ease-out;
}

@keyframes quiz-rating-fill {
  0% { transform: scale(0.8); opacity: 0.5; }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); opacity: 1; }
}

.quiz-rating-marker-bloom {
  animation: quiz-rating-bloom 0.4s ease-out;
}

@keyframes quiz-rating-bloom {
  0% { transform: scale(1); }
  50% { transform: scale(1.3) rotate(10deg); }
  100% { transform: scale(1) rotate(0deg); }
}

.quiz-rating-wave-animation {
  animation: quiz-rating-wave 0.6s ease-out;
}

@keyframes quiz-rating-wave {
  0% { transform: translateX(-10px); }
  25% { transform: translateX(5px); }
  50% { transform: translateX(-2px); }
  75% { transform: translateX(1px); }
  100% { transform: translateX(0); }
}

/* 值显示 */
.quiz-rating-value-display {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary, #212121);
  margin-left: var(--spacing-sm);
}

/* 标签 */
.quiz-rating-labels {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
  margin-top: var(--spacing-xs);
}

.quiz-rating-vertical .quiz-rating-labels {
  flex-direction: column;
  height: 100%;
  margin-top: 0;
  margin-left: var(--spacing-sm);
}

/* 状态样式 */
.quiz-rating-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.quiz-rating-readonly .quiz-rating-marker {
  cursor: default;
}

/* ==================== 下拉选择器组件样式 ==================== */
.quiz-dropdown-component {
  position: relative;
  width: 100%;
  font-family: inherit;
}

/* 触发器 */
.quiz-dropdown-trigger {
  position: relative;
  width: 100%;
  min-height: 44px;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-background, white);
  border: 2px solid var(--color-border, #E0E0E0);
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  text-align: left;
}

.quiz-dropdown-trigger:hover {
  border-color: var(--tcm-jade);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.quiz-dropdown-trigger:focus {
  outline: none;
  border-color: var(--tcm-jade);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.quiz-dropdown-trigger-open {
  border-color: var(--tcm-jade);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.quiz-dropdown-trigger-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 触发器内容 */
.quiz-dropdown-trigger-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  min-width: 0;
}

.quiz-dropdown-placeholder {
  color: var(--color-text-secondary, #757575);
  font-style: italic;
}

/* 箭头样式 */
.quiz-dropdown-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: transform var(--animation-normal) ease-out;
  color: var(--color-text-secondary, #757575);
}

.quiz-dropdown-open .quiz-dropdown-arrow {
  transform: rotate(180deg);
}

.quiz-dropdown-arrow-chevron::before {
  content: '▼';
}

.quiz-dropdown-arrow-triangle::before {
  content: '▲';
  transform: rotate(180deg);
}

.quiz-dropdown-arrow-chinese_arrow::before {
  content: '⌄';
  font-size: 18px;
}

/* 下拉菜单 */
.quiz-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--color-background, white);
  border: 2px solid var(--tcm-jade);
  border-radius: 8px;
  margin-top: 4px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  animation: quiz-dropdown-menu-enter 0.2s ease-out;
}

@keyframes quiz-dropdown-menu-enter {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.quiz-dropdown-menu-modern {
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
}

.quiz-dropdown-menu-traditional {
  border-radius: 4px;
  border-color: var(--tcm-gold);
  background: linear-gradient(135deg, var(--tcm-paper), #FFF8E1);
  box-shadow:
    0 4px 8px rgba(139, 69, 19, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.quiz-dropdown-menu-floating {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
}

/* 选项容器 */
.quiz-dropdown-options {
  max-height: inherit;
  overflow-y: auto;
}

/* 选项 */
.quiz-dropdown-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: all var(--animation-fast) ease-out;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.quiz-dropdown-option:last-child {
  border-bottom: none;
}

.quiz-dropdown-option:hover,
.quiz-dropdown-option-focused {
  background: rgba(76, 175, 80, 0.1);
  color: var(--tcm-jade);
}

.quiz-dropdown-option-selected {
  background: rgba(76, 175, 80, 0.15);
  color: var(--tcm-jade);
  font-weight: 500;
}

.quiz-dropdown-option-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 选项图标 */
.quiz-dropdown-option-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 选项文本 */
.quiz-dropdown-option-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 选中标记 */
.quiz-dropdown-option-checkmark {
  color: var(--tcm-jade);
  font-weight: bold;
  flex-shrink: 0;
}

/* 状态样式 */
.quiz-dropdown-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* ==================== 图片组件样式 ==================== */
.quiz-image-component {
  position: relative;
  display: inline-block;
  max-width: 100%;
}

.quiz-image-container {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quiz-image-element {
  max-width: 100%;
  height: auto;
  display: block;
  transition: all var(--animation-normal) ease-out;
}

/* 交互式图片 */
.quiz-image-interactive {
  cursor: pointer;
}

.quiz-image-interactive:focus {
  outline: 2px solid var(--tcm-jade);
  outline-offset: 2px;
}

/* 边框样式 */
.quiz-image-border-none {
  border: none;
}

.quiz-image-border-simple {
  border: 2px solid var(--color-border, #E0E0E0);
}

.quiz-image-border-ink_wash_frame {
  border: 4px solid var(--tcm-ink);
  position: relative;
}

.quiz-image-border-ink_wash_frame::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  border: 1px solid var(--tcm-gold);
  pointer-events: none;
}

.quiz-image-border-traditional {
  border: 3px solid var(--tcm-gold);
  background: linear-gradient(45deg, var(--tcm-paper), #FFF8E1);
  padding: 8px;
  position: relative;
}

.quiz-image-border-traditional::before {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border: 1px solid rgba(139, 69, 19, 0.3);
  pointer-events: none;
}

/* 悬停效果 */
.quiz-image-hover-zoom:hover .quiz-image-element {
  transform: scale(1.05);
}

.quiz-image-hover-brightness:hover .quiz-image-element {
  filter: brightness(1.1);
}

.quiz-image-hover-shadow:hover {
  box-shadow: var(--shadow-lg);
}

/* 阴影效果 */
.quiz-image-shadow {
  box-shadow: var(--shadow-md);
}

/* 加载状态 */
.quiz-image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  background: #F5F5F5;
  border-radius: 8px;
}

.quiz-image-loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #E0E0E0;
  border-top: 2px solid var(--tcm-jade);
  border-radius: 50%;
  animation: quiz-image-spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes quiz-image-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.quiz-image-loading-text {
  font-size: 14px;
  color: var(--color-text-secondary, #757575);
}

/* 错误状态 */
.quiz-image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  background: #FFEBEE;
  border: 1px solid #FFCDD2;
  border-radius: 8px;
  color: #C62828;
}

.quiz-image-error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.quiz-image-error-text {
  font-size: 14px;
  text-align: center;
}

/* 图片标题 */
.quiz-image-caption {
  margin-top: var(--spacing-sm);
  text-align: center;
  font-size: 14px;
  color: var(--color-text-secondary, #757575);
  font-style: italic;
}

/* ==================== 文本输入组件样式 ==================== */
.quiz-text-input-component {
  position: relative;
  width: 100%;
  font-family: inherit;
}

/* 输入容器 */
.quiz-text-input-container {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.quiz-text-input-wrapper {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 输入元素 */
.quiz-text-input-element {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--color-border, #E0E0E0);
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  background: var(--color-background, white);
  transition: all var(--animation-normal) ease-out;
  resize: none;
}

.quiz-text-input-element:focus {
  outline: none;
  border-color: var(--tcm-jade);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.quiz-text-input-element::placeholder {
  color: var(--color-text-secondary, #757575);
  opacity: 1;
}

/* 尺寸样式 */
.quiz-text-input-small .quiz-text-input-element {
  padding: 6px 12px;
  font-size: 14px;
  min-height: 36px;
}

.quiz-text-input-medium .quiz-text-input-element {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 16px;
  min-height: 44px;
}

.quiz-text-input-large .quiz-text-input-element {
  padding: 12px 16px;
  font-size: 18px;
  min-height: 52px;
}

/* 边框样式 */
.quiz-text-input-border-modern .quiz-text-input-element {
  border-radius: 8px;
  border-width: 2px;
}

.quiz-text-input-border-traditional .quiz-text-input-element {
  border-radius: 4px;
  border-color: var(--tcm-gold);
  background: linear-gradient(135deg, var(--tcm-paper), #FFF8E1);
}

.quiz-text-input-border-ink_brush .quiz-text-input-element {
  border-radius: 12px;
  border-color: var(--tcm-ink);
  background: #FAFAFA;
  position: relative;
}

.quiz-text-input-border-ink_brush .quiz-text-input-element::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border: 1px solid rgba(33, 33, 33, 0.1);
  border-radius: 10px;
  pointer-events: none;
}

.quiz-text-input-border-bamboo .quiz-text-input-element {
  border-radius: 6px;
  border-color: var(--tcm-bamboo);
  background:
    linear-gradient(90deg, #F1F8E9, #E8F5E8),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 20px,
      rgba(139, 195, 74, 0.1) 20px,
      rgba(139, 195, 74, 0.1) 22px
    );
}

/* 标签样式 */
.quiz-text-input-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary, #212121);
  margin-bottom: 4px;
  display: block;
}

.quiz-text-input-label-top {
  margin-bottom: 6px;
}

.quiz-text-input-label-left {
  margin-bottom: 0;
  margin-right: var(--spacing-sm);
  min-width: 100px;
  padding-top: var(--spacing-sm);
}

.quiz-text-input-label-inside {
  position: absolute;
  top: 50%;
  left: var(--spacing-md);
  transform: translateY(-50%);
  background: var(--color-background, white);
  padding: 0 4px;
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
  pointer-events: none;
  transition: all var(--animation-normal) ease-out;
}

.quiz-text-input-focused .quiz-text-input-label-inside,
.quiz-text-input-element:not(:placeholder-shown) + .quiz-text-input-label-inside {
  top: 0;
  font-size: 11px;
  color: var(--tcm-jade);
}

.quiz-text-input-label-floating {
  position: absolute;
  top: 50%;
  left: var(--spacing-md);
  transform: translateY(-50%);
  font-size: 16px;
  color: var(--color-text-secondary, #757575);
  pointer-events: none;
  transition: all var(--animation-normal) ease-out;
  background: var(--color-background, white);
  padding: 0 4px;
}

.quiz-text-input-focused .quiz-text-input-label-floating,
.quiz-text-input-element:not(:placeholder-shown) + .quiz-text-input-label-floating {
  top: 0;
  font-size: 12px;
  color: var(--tcm-jade);
  transform: translateY(-50%);
}

/* 必填标记 */
.quiz-text-input-required {
  color: var(--tcm-red);
  margin-left: 2px;
}

/* 字符计数器 */
.quiz-text-input-counter {
  position: absolute;
  bottom: 4px;
  right: 8px;
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
  background: var(--color-background, white);
  padding: 2px 4px;
  border-radius: 4px;
}

/* 错误状态 */
.quiz-text-input-error .quiz-text-input-element {
  border-color: var(--tcm-red);
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.2);
}

.quiz-text-input-errors {
  margin-top: 4px;
}

.quiz-text-input-error {
  font-size: 12px;
  color: var(--tcm-red);
  margin-bottom: 2px;
}

/* 禁用状态 */
.quiz-text-input-disabled .quiz-text-input-element {
  opacity: 0.5;
  cursor: not-allowed;
  background: #F5F5F5;
}

/* 焦点状态 */
.quiz-text-input-focused .quiz-text-input-element {
  border-color: var(--tcm-jade);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* ==================== 图片选择器组件样式 ==================== */
.quiz-image-selector-component {
  position: relative;
  width: 100%;
}

/* 图片网格 */
.quiz-image-selector-grid {
  display: grid;
  gap: 16px;
}

/* 图片项 */
.quiz-image-selector-item {
  position: relative;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  transition: all var(--animation-normal) ease-out;
  background: var(--color-background, white);
}

.quiz-image-selector-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quiz-image-selector-item:focus {
  outline: none;
  border-color: var(--tcm-jade);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* 选中状态 */
.quiz-image-selector-item-selected {
  border-color: var(--tcm-jade);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
}

/* 禁用状态 */
.quiz-image-selector-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 图片容器 */
.quiz-image-selector-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.quiz-image-selector-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all var(--animation-normal) ease-out;
}

/* 悬停效果 */
.quiz-image-selector-hover-zoom .quiz-image-selector-image {
  transform: scale(1.1);
}

.quiz-image-selector-hover-brightness .quiz-image-selector-image {
  filter: brightness(1.2);
}

.quiz-image-selector-hover-shadow {
  box-shadow: var(--shadow-lg);
}

.quiz-image-selector-hover-lift {
  transform: translateY(-4px);
}

/* 加载状态 */
.quiz-image-selector-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
}

.quiz-image-selector-loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #E0E0E0;
  border-top: 2px solid var(--tcm-jade);
  border-radius: 50%;
  animation: quiz-image-selector-spin 1s linear infinite;
}

@keyframes quiz-image-selector-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.quiz-image-selector-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #FFEBEE;
  color: #C62828;
}

.quiz-image-selector-error-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.quiz-image-selector-error-text {
  font-size: 12px;
  text-align: center;
}

/* 选择指示器 */
.quiz-image-selector-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
  animation: quiz-image-selector-indicator-appear 0.3s ease-out;
}

@keyframes quiz-image-selector-indicator-appear {
  0% { transform: scale(0); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.quiz-image-selector-indicator-border {
  border: 3px solid var(--tcm-jade);
  background: transparent;
}

.quiz-image-selector-indicator-overlay {
  background: rgba(76, 175, 80, 0.8);
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: auto;
  height: auto;
  border-radius: 0;
}

.quiz-image-selector-indicator-checkmark {
  background: var(--tcm-jade);
}

.quiz-image-selector-indicator-glow {
  background: var(--tcm-jade);
  box-shadow: 0 0 12px rgba(76, 175, 80, 0.6);
}

/* 图片信息 */
.quiz-image-selector-info {
  padding: var(--spacing-sm);
  background: var(--color-background, white);
}

.quiz-image-selector-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary, #212121);
  margin-bottom: 2px;
}

.quiz-image-selector-description {
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
  line-height: 1.4;
}

/* 信息栏 */
.quiz-image-selector-info-bar {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: #F5F5F5;
  border-radius: 4px;
  text-align: center;
}

.quiz-image-selector-count {
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
}

/* ==================== 进度指示器组件样式 ==================== */
.quiz-progress-component {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 进度内容 */
.quiz-progress-content {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 条形进度条 */
.quiz-progress-bar .quiz-progress-content {
  width: 100%;
}

.quiz-progress-bar-container {
  width: 100%;
  position: relative;
}

.quiz-progress-bar-track {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.quiz-progress-bar-fill {
  position: relative;
  background: linear-gradient(90deg, currentColor, rgba(255, 255, 255, 0.2));
}

/* 圆形进度条 */
.quiz-progress-circle-container {
  position: relative;
  display: inline-block;
}

.quiz-progress-circle {
  transform: rotate(-90deg);
}

.quiz-progress-circle-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-weight: 500;
}

.quiz-progress-percentage {
  display: block;
  font-size: 16px;
  color: var(--color-text-primary, #212121);
}

.quiz-progress-fraction {
  display: block;
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
  margin-top: 2px;
}

/* 莲花进度条 */
.quiz-progress-lotus-container {
  position: relative;
  display: inline-block;
}

.quiz-progress-lotus {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quiz-progress-lotus-petal {
  position: absolute;
  font-size: 20px;
  opacity: 0.3;
  transition: all var(--animation-normal) ease-out;
  transform-origin: center;
}

.quiz-progress-lotus-petal-active {
  opacity: 1;
  animation: quiz-progress-lotus-bloom 0.6s ease-out;
}

@keyframes quiz-progress-lotus-bloom {
  0% { transform: scale(0.5) rotate(var(--rotation)); opacity: 0; }
  50% { transform: scale(1.2) rotate(var(--rotation)); }
  100% { transform: scale(1) rotate(var(--rotation)); opacity: 1; }
}

.quiz-progress-lotus-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
}

/* 竹子进度条 */
.quiz-progress-bamboo-container {
  width: 100%;
}

.quiz-progress-bamboo {
  display: flex;
  align-items: center;
  gap: 4px;
}

.quiz-progress-bamboo-segment {
  flex: 1;
  height: 20px;
  background: #E0E0E0;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  transition: all var(--animation-normal) ease-out;
}

.quiz-progress-bamboo-segment-active {
  background: linear-gradient(90deg, var(--tcm-bamboo), #A5D6A7);
  animation: quiz-progress-bamboo-grow 0.5s ease-out;
}

@keyframes quiz-progress-bamboo-grow {
  0% { transform: scaleX(0); }
  100% { transform: scaleX(1); }
}

.quiz-progress-bamboo-node {
  position: absolute;
  right: -2px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 12px;
  background: #4CAF50;
  border-radius: 2px;
}

/* 步骤进度条 */
.quiz-progress-steps-container {
  width: 100%;
}

.quiz-progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quiz-progress-step {
  display: flex;
  align-items: center;
  flex: 1;
}

.quiz-progress-step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #E0E0E0;
  color: #757575;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: all var(--animation-normal) ease-out;
}

.quiz-progress-step-completed .quiz-progress-step-circle {
  background: var(--tcm-jade);
  color: white;
}

.quiz-progress-step-current .quiz-progress-step-circle {
  background: var(--tcm-jade);
  color: white;
  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.3);
}

.quiz-progress-step-line {
  flex: 1;
  height: 2px;
  background: #E0E0E0;
  margin: 0 8px;
  transition: all var(--animation-normal) ease-out;
}

.quiz-progress-step-line-completed {
  background: var(--tcm-jade);
}

/* 标签 */
.quiz-progress-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary, #212121);
  text-align: center;
}

.quiz-progress-label-top {
  margin-bottom: var(--spacing-sm);
}

.quiz-progress-label-bottom {
  margin-top: var(--spacing-sm);
}

.quiz-progress-label-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
}

/* 尺寸样式 */
.quiz-progress-small .quiz-progress-bar-track {
  height: 4px;
}

.quiz-progress-medium .quiz-progress-bar-track {
  height: 8px;
}

.quiz-progress-large .quiz-progress-bar-track {
  height: 12px;
}

/* 完成状态 */
.quiz-progress-complete {
  animation: quiz-progress-complete-pulse 1s ease-out;
}

@keyframes quiz-progress-complete-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* ==================== 音频播放器组件样式 ==================== */
.quiz-audio-player-component {
  position: relative;
  width: 100%;
  background: var(--color-background, white);
  border-radius: 8px;
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

/* 音频控件 */
.quiz-audio-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 播放按钮 */
.quiz-audio-play-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: var(--tcm-jade);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
  font-size: 18px;
}

.quiz-audio-play-button:hover {
  background: #45A049;
  transform: scale(1.05);
}

.quiz-audio-play-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.quiz-audio-loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: quiz-audio-spin 1s linear infinite;
}

@keyframes quiz-audio-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度区域 */
.quiz-audio-progress-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.quiz-audio-progress-container {
  position: relative;
  height: 6px;
  background: #E0E0E0;
  border-radius: 3px;
  cursor: pointer;
}

.quiz-audio-progress-track {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 3px;
  overflow: hidden;
}

.quiz-audio-progress-fill {
  height: 100%;
  background: var(--tcm-jade);
  border-radius: 3px;
  transition: width 0.1s ease-out;
}

.quiz-audio-progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: var(--tcm-jade);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: left 0.1s ease-out;
}

/* 时间显示 */
.quiz-audio-time-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
}

.quiz-audio-time-separator {
  margin: 0 2px;
}

/* 音量控制 */
.quiz-audio-volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quiz-audio-mute-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background var(--animation-normal) ease-out;
}

.quiz-audio-mute-button:hover {
  background: rgba(0, 0, 0, 0.1);
}

.quiz-audio-volume-slider {
  width: 60px;
  height: 4px;
  background: #E0E0E0;
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.quiz-audio-volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--tcm-jade);
  border-radius: 50%;
  cursor: pointer;
}

.quiz-audio-volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--tcm-jade);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* 错误状态 */
.quiz-audio-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: var(--spacing-sm);
  background: #FFEBEE;
  border: 1px solid #FFCDD2;
  border-radius: 4px;
  color: #C62828;
}

.quiz-audio-error-icon {
  font-size: 18px;
}

.quiz-audio-error-text {
  font-size: 14px;
}

/* 播放器样式变体 */
.quiz-audio-player-modern {
  border-radius: 12px;
  background: linear-gradient(135deg, #F5F5F5, white);
}

.quiz-audio-player-traditional {
  border-radius: 4px;
  background: linear-gradient(135deg, var(--tcm-paper), #FFF8E1);
  border: 2px solid var(--tcm-gold);
}

.quiz-audio-player-minimal {
  background: transparent;
  box-shadow: none;
  border: 1px solid #E0E0E0;
}

/* 禁用状态 */
.quiz-audio-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* ==================== 视频播放器组件样式 ==================== */
.quiz-video-player-component {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

/* 视频元素 */
.quiz-video-element {
  width: 100%;
  height: auto;
  display: block;
  background: #000;
}

/* 视频控件 */
.quiz-video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: opacity var(--animation-normal) ease-out;
}

.quiz-video-playing:not(:hover) .quiz-video-controls {
  opacity: 0;
  pointer-events: none;
}

.quiz-video-controls-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.quiz-video-controls-center {
  flex: 1;
  margin: 0 var(--spacing-md);
}

.quiz-video-controls-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 播放按钮 */
.quiz-video-play-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
  font-size: 16px;
}

.quiz-video-play-button:hover {
  background: white;
  transform: scale(1.05);
}

.quiz-video-play-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.quiz-video-loading-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(51, 51, 51, 0.3);
  border-top: 2px solid #333;
  border-radius: 50%;
  animation: quiz-video-spin 1s linear infinite;
}

@keyframes quiz-video-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度条 */
.quiz-video-progress-container {
  position: relative;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  cursor: pointer;
}

.quiz-video-progress-track {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 2px;
  overflow: hidden;
}

.quiz-video-progress-fill {
  height: 100%;
  background: var(--tcm-jade);
  border-radius: 2px;
  transition: width 0.1s ease-out;
}

.quiz-video-progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background: var(--tcm-jade);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: left 0.1s ease-out;
}

/* 时间显示 */
.quiz-video-time-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.quiz-video-time-separator {
  margin: 0 2px;
}

/* 全屏按钮 */
.quiz-video-fullscreen-button {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background var(--animation-normal) ease-out;
}

.quiz-video-fullscreen-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 缓冲指示器 */
.quiz-video-buffering {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: var(--spacing-md);
}

.quiz-video-buffering-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: quiz-video-buffering-spin 1s linear infinite;
}

@keyframes quiz-video-buffering-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.quiz-video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: var(--spacing-lg);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  color: white;
  text-align: center;
}

.quiz-video-error-icon {
  font-size: 32px;
}

.quiz-video-error-text {
  font-size: 14px;
}

/* 全屏状态 */
.quiz-video-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
  border-radius: 0 !important;
}

/* 播放器样式变体 */
.quiz-video-player-modern {
  border-radius: 12px;
}

.quiz-video-player-traditional {
  border-radius: 4px;
  border: 3px solid var(--tcm-gold);
}

.quiz-video-player-minimal {
  border-radius: 0;
  box-shadow: none;
}

/* 禁用状态 */
.quiz-video-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* ==================== 拖拽列表组件样式 ==================== */
.quiz-draggable-list-component {
  position: relative;
  width: 100%;
  background: var(--color-background, white);
  border-radius: 8px;
  padding: var(--spacing-sm);
  box-shadow: var(--shadow-sm);
}

/* 列表项 */
.quiz-draggable-list-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-xs);
  background: var(--color-background, white);
  border: 2px solid transparent;
  border-radius: 6px;
  cursor: grab;
  transition: all var(--animation-normal) ease-out;
  user-select: none;
}

.quiz-draggable-list-item:hover {
  background: #F5F5F5;
  border-color: #E0E0E0;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.quiz-draggable-list-item:focus {
  outline: none;
  border-color: var(--tcm-jade);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* 拖拽状态 */
.quiz-draggable-item-dragging {
  opacity: 0.5;
  transform: rotate(2deg);
  cursor: grabbing;
  z-index: 1000;
}

.quiz-draggable-item-drag-over {
  border-color: var(--tcm-jade);
  background: rgba(76, 175, 80, 0.1);
  transform: translateY(-2px);
}

/* 拖拽手柄 */
.quiz-draggable-item-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #757575;
  cursor: grab;
  transition: color var(--animation-normal) ease-out;
}

.quiz-draggable-item-handle:hover {
  color: var(--tcm-jade);
}

.quiz-draggable-handle-icon {
  font-size: 12px;
  line-height: 1;
}

/* 项目图标 */
.quiz-draggable-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 16px;
}

/* 项目内容 */
.quiz-draggable-item-content {
  flex: 1;
  font-size: 14px;
  color: var(--color-text-primary, #212121);
  line-height: 1.4;
}

/* 排序指示器 */
.quiz-draggable-item-order {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #E0E0E0;
  color: #757575;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
}

/* 禁用状态 */
.quiz-draggable-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.quiz-draggable-item-list-disabled {
  cursor: not-allowed;
}

.quiz-draggable-list-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 空状态 */
.quiz-draggable-list-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--color-text-secondary, #757575);
  font-style: italic;
}

/* 列表样式变体 */
.quiz-draggable-list-modern .quiz-draggable-list-item {
  border-radius: 12px;
  padding: var(--spacing-md);
}

.quiz-draggable-list-traditional .quiz-draggable-list-item {
  border-radius: 4px;
  background: linear-gradient(135deg, var(--tcm-paper), #FFF8E1);
  border-color: var(--tcm-gold);
}

.quiz-draggable-list-minimal .quiz-draggable-list-item {
  border-radius: 0;
  border-left: 3px solid transparent;
  border-right: none;
  border-top: none;
  border-bottom: 1px solid #E0E0E0;
}

.quiz-draggable-list-minimal .quiz-draggable-list-item:hover {
  border-left-color: var(--tcm-jade);
}

/* ==================== NPC角色组件样式 ==================== */
.quiz-npc-character-component {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--color-background, white);
  border-radius: 12px;
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
  box-shadow: var(--shadow-sm);
  max-width: 200px;
}

.quiz-npc-character-component:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quiz-npc-character-component:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
}

/* 头像容器 */
.quiz-npc-avatar-container {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background: #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quiz-npc-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.quiz-npc-default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
}

/* 状态指示器 */
.quiz-npc-status-indicators {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.quiz-npc-speaking-indicator,
.quiz-npc-thinking-indicator,
.quiz-npc-idle-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  box-shadow: var(--shadow-sm);
  animation: quiz-npc-indicator-pulse 1s ease-in-out infinite;
}

@keyframes quiz-npc-indicator-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.quiz-npc-idle-indicator {
  animation: quiz-npc-idle-sparkle 2s ease-in-out infinite;
}

@keyframes quiz-npc-idle-sparkle {
  0%, 100% { opacity: 0.5; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* 角色信息 */
.quiz-npc-character-info {
  text-align: center;
}

.quiz-npc-character-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary, #212121);
  margin-bottom: 4px;
}

.quiz-npc-character-description {
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
  line-height: 1.3;
}

/* 交互提示 */
.quiz-npc-interaction-hint {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  animation: quiz-npc-hint-fade-in 0.3s ease-out;
}

@keyframes quiz-npc-hint-fade-in {
  0% { opacity: 0; transform: translateX(-50%) translateY(5px); }
  100% { opacity: 1; transform: translateX(-50%) translateY(0); }
}

/* 角色样式变体 */
.quiz-npc-character-traditional_doctor .quiz-npc-avatar-container {
  background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
  border: 2px solid var(--tcm-jade);
}

.quiz-npc-character-wise_elder .quiz-npc-avatar-container {
  background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
  border: 2px solid var(--tcm-gold);
}

.quiz-npc-character-mystical_sage .quiz-npc-avatar-container {
  background: linear-gradient(135deg, #F3E5F5, #E1BEE7);
  border: 2px solid #9C27B0;
}

/* 情绪状态 */
.quiz-npc-emotion-happy .quiz-npc-avatar-container {
  animation: quiz-npc-happy-bounce 2s ease-in-out infinite;
}

@keyframes quiz-npc-happy-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

.quiz-npc-emotion-concerned .quiz-npc-avatar-container {
  filter: brightness(0.9);
}

.quiz-npc-emotion-wise .quiz-npc-avatar-container {
  box-shadow: 0 0 12px rgba(255, 193, 7, 0.3);
}

/* 禁用状态 */
.quiz-npc-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ==================== 对话组件样式 ==================== */
.quiz-dialogue-component {
  position: relative;
  width: 100%;
  max-width: 600px;
  background: var(--color-background, white);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 400px;
}

/* 消息列表 */
.quiz-dialogue-messages {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  scroll-behavior: smooth;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 消息项 */
.quiz-dialogue-message {
  display: flex;
  gap: var(--spacing-sm);
  max-width: 80%;
  animation: quiz-dialogue-message-appear 0.3s ease-out;
}

@keyframes quiz-dialogue-message-appear {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

.quiz-dialogue-message-user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.quiz-dialogue-message-npc,
.quiz-dialogue-message-system {
  align-self: flex-start;
}

/* 消息头像 */
.quiz-dialogue-message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.quiz-dialogue-message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 消息内容 */
.quiz-dialogue-message-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.quiz-dialogue-message-speaker {
  font-size: 11px;
  color: var(--color-text-secondary, #757575);
  font-weight: 500;
  margin-bottom: 2px;
}

/* 消息气泡 */
.quiz-dialogue-message-bubble {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  position: relative;
  word-wrap: break-word;
}

.quiz-dialogue-message-user .quiz-dialogue-message-bubble {
  background: var(--tcm-jade);
  color: white;
  border-bottom-right-radius: 6px;
}

.quiz-dialogue-message-npc .quiz-dialogue-message-bubble {
  background: #F5F5F5;
  color: var(--color-text-primary, #212121);
  border-bottom-left-radius: 6px;
}

.quiz-dialogue-message-system .quiz-dialogue-message-bubble {
  background: #E3F2FD;
  color: #1976D2;
  border-radius: 12px;
  font-style: italic;
  text-align: center;
}

/* 时间戳 */
.quiz-dialogue-message-timestamp {
  font-size: 10px;
  color: var(--color-text-secondary, #757575);
  margin-top: 2px;
}

.quiz-dialogue-message-user .quiz-dialogue-message-timestamp {
  text-align: right;
}

/* 打字指示器 */
.quiz-dialogue-typing-indicator {
  display: flex;
  gap: 4px;
  padding: 8px 12px;
}

.quiz-dialogue-typing-indicator span {
  width: 6px;
  height: 6px;
  background: #757575;
  border-radius: 50%;
  animation: quiz-dialogue-typing-dot 1.4s ease-in-out infinite;
}

.quiz-dialogue-typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.quiz-dialogue-typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes quiz-dialogue-typing-dot {
  0%, 60%, 100% { transform: translateY(0); opacity: 0.5; }
  30% { transform: translateY(-10px); opacity: 1; }
}

/* 打字状态 */
.quiz-dialogue-typing-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: var(--spacing-sm);
  background: #F5F5F5;
  border-radius: 12px;
  margin: var(--spacing-sm) 0;
}

.quiz-dialogue-typing-text {
  font-size: 12px;
  color: var(--color-text-secondary, #757575);
  font-style: italic;
}

/* 对话选项 */
.quiz-dialogue-options {
  padding: var(--spacing-md);
  border-top: 1px solid #E0E0E0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.quiz-dialogue-option {
  padding: var(--spacing-sm) var(--spacing-md);
  background: #F5F5F5;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--animation-normal) ease-out;
  font-size: 14px;
  text-align: left;
}

.quiz-dialogue-option:hover {
  background: #E0E0E0;
  border-color: var(--tcm-jade);
}

.quiz-dialogue-option:focus {
  outline: none;
  border-color: var(--tcm-jade);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.quiz-dialogue-option-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 输入区域 */
.quiz-dialogue-input-area {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-top: 1px solid #E0E0E0;
  background: #FAFAFA;
}

.quiz-dialogue-input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid #E0E0E0;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  transition: border-color var(--animation-normal) ease-out;
}

.quiz-dialogue-input:focus {
  border-color: var(--tcm-jade);
}

.quiz-dialogue-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quiz-dialogue-send-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: var(--tcm-jade);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all var(--animation-normal) ease-out;
}

.quiz-dialogue-send-button:hover {
  background: #45A049;
  transform: scale(1.05);
}

.quiz-dialogue-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 对话样式变体 */
.quiz-dialogue-modern {
  border-radius: 16px;
}

.quiz-dialogue-traditional {
  border-radius: 8px;
  background: linear-gradient(135deg, var(--tcm-paper), #FFF8E1);
  border: 2px solid var(--tcm-gold);
}

.quiz-dialogue-traditional .quiz-dialogue-message-bubble {
  background: rgba(255, 248, 225, 0.8);
}

.quiz-dialogue-traditional .quiz-dialogue-message-user .quiz-dialogue-message-bubble {
  background: var(--tcm-gold);
  color: #333;
}

.quiz-dialogue-minimal {
  border-radius: 0;
  box-shadow: none;
  border: 1px solid #E0E0E0;
}

/* 禁用状态 */
.quiz-dialogue-disabled {
  opacity: 0.6;
  pointer-events: none;
}
