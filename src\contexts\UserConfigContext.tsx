/**
 * 用户配置上下文
 * 提供用户配置的访问和管理
 * 基于服务层架构实现
 */

import { useDatabaseContext } from '@/contexts/DatabaseContext';
import { Services } from '@/services/ServiceFactoryFixed';
import type { UserConfigService } from '@/services/entities/UserConfigService';
import type {
  BubbleLayout,
  CardLayout,
  ColorMode,
  ContentDisplayMode,
  FlowLayout,
  GalaxyLayout,
  GridLayout,
  ListLayout,
  TagCloudLayout,
  TreeLayout,
  UserConfig,
  ViewType,
  RenderEngine,
} from '@/types';
import type React from 'react';
import { type ReactNode, createContext, useContext, useEffect, useState, useCallback } from 'react';

interface UserConfigContextType {
  userConfig: UserConfig | null;
  isLoading: boolean;
  updateUserConfig: (config: Partial<UserConfig>) => Promise<boolean>;
  setActiveEmotionDataId: (id: string) => Promise<boolean>;
  setActiveSkinId: (id: string) => Promise<boolean>;
  setPreferredViewType: (viewType: ViewType) => Promise<boolean>;
  setContentDisplayMode: (mode: ContentDisplayMode) => Promise<boolean>;
  setRenderEngine: (implementation: RenderEngine) => Promise<boolean>;
  toggleDarkMode: () => Promise<boolean>;
  updateAccessibilitySettings: (settings: Record<string, any>) => Promise<boolean>;
  resetToDefaults: () => Promise<boolean>;
  getUserConfigs: () => UserConfig[];
  setActiveUserConfig: (id: string) => Promise<boolean>;
  createUserConfig: (name: string) => Promise<UserConfig | null>;
  deleteUserConfig: (id: string) => Promise<boolean>;

  // 视图特定设置方法
  setViewContentDisplayMode: (viewType: ViewType, mode: ContentDisplayMode) => Promise<boolean>;
  setViewSkinId: (viewType: ViewType, skinId: string) => Promise<boolean>;
  setViewRenderingEngine: (viewType: ViewType, implementation: string) => Promise<boolean>;
  setWheelViewRenderingEngine: (implementation: RenderEngine) => Promise<boolean>;
  setCardViewLayout: (layout: CardLayout) => Promise<boolean>;
  setBubbleViewLayout: (layout: BubbleLayout) => Promise<boolean>;
  setGalaxyViewLayout: (layout: GalaxyLayout) => Promise<boolean>;
  setListViewLayout: (layout: ListLayout) => Promise<boolean>;
  setGridViewLayout: (layout: GridLayout) => Promise<boolean>;
  setTreeViewLayout: (layout: TreeLayout) => Promise<boolean>;
  setFlowViewLayout: (layout: FlowLayout) => Promise<boolean>;
  setTagCloudViewLayout: (layout: TagCloudLayout) => Promise<boolean>;

  // 视图设置获取方法
  getViewRenderingEngine: (viewType: ViewType) => string;
  getViewLayout: (viewType: ViewType) => string;
  getViewContentDisplayMode: (viewType: ViewType) => ContentDisplayMode;
  getViewSkinId: (viewType: ViewType) => string;

  // 颜色模式方法
  setColorMode: (mode: ColorMode) => Promise<boolean>;
  getColorMode: () => ColorMode;
}

// 创建上下文
const UserConfigContext = createContext<UserConfigContextType | undefined>(undefined);

interface UserConfigProviderProps {
  children: ReactNode;
}

/**
 * 用户配置提供者组件
 * 基于服务层架构实现
 */
export const UserConfigProvider: React.FC<UserConfigProviderProps> = ({ children }) => {
  const { isInitialized } = useDatabaseContext();

  // 服务层状态
  const [userConfigService, setUserConfigService] = useState<UserConfigService | null>(null);
  const [isServiceReady, setIsServiceReady] = useState(false);

  // 用户配置状态
  const [userConfig, setUserConfig] = useState<UserConfig | null>(null);
  const [userConfigs, setUserConfigs] = useState<UserConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 辅助函数：安全解析 JSON 字符串
  const safeParseJSON = useCallback((jsonString: string | undefined, defaultValue: any = {}) => {
    if (!jsonString) return defaultValue;
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('[UserConfigContext] Failed to parse JSON:', error);
      return defaultValue;
    }
  }, []);

  // 初始化服务
  useEffect(() => {
    const initializeService = async () => {
      if (!isInitialized) return;

      try {
        console.log('[UserConfigContext] Initializing UserConfigService...');
        const service = Services.getUserConfigService();
        setUserConfigService(service);
        setIsServiceReady(true);
        console.log('[UserConfigContext] UserConfigService initialized successfully');
      } catch (error) {
        console.error('[UserConfigContext] Failed to initialize UserConfigService:', error);
        setIsServiceReady(false);
      }
    };

    initializeService();
  }, [isInitialized]);

  // 当服务准备好时，加载用户配置
  useEffect(() => {
    const loadUserConfig = async () => {
      if (!isServiceReady || !userConfigService) return;

      try {
        setIsLoading(true);
        console.log('[UserConfigContext] Loading user config from database...');

        const configResult = await userConfigService.getUserConfigs('default-user'); // TODO: 使用实际用户ID

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          // 使用数据库中的配置
          const dbConfig = configResult.data[0];
          setUserConfig(dbConfig);
          setUserConfigs([dbConfig]);
          console.log('[UserConfigContext] Loaded user config from database:', dbConfig.name);
        } else {
          // 如果数据库中没有配置，创建默认配置
          console.log('[UserConfigContext] No user config found, creating default config...');
          await createDefaultConfig();
        }
      } catch (error) {
        console.error('[UserConfigContext] Failed to load user config:', error);
        // 创建默认配置作为回退
        await createDefaultConfig();
      } finally {
        setIsLoading(false);
      }
    };

    loadUserConfig();
  }, [isServiceReady, userConfigService]);

  // 创建默认配置
  const createDefaultConfig = useCallback(async () => {
    if (!userConfigService) return;

    try {
      console.log('[UserConfigContext] Creating default user config...');

      const defaultConfigData = {
        name: '默认配置',
        user_id: 'default-user', // TODO: 使用实际用户ID
        active_emotion_data_id: 'default-emotion-data',
        active_skin_id: 'default-wheel-skin',
        preferred_view_type: 'wheel' as ViewType,
        dark_mode: false,
        color_mode: 'warm' as ColorMode,
        render_engine_preferences: JSON.stringify({
          wheel: 'D3' as RenderEngine,
          card: 'D3' as RenderEngine,
          bubble: 'D3' as RenderEngine,
          galaxy: 'R3F' as RenderEngine,
        }),
        layout_preferences: JSON.stringify({
          card: 'grid' as CardLayout,
          bubble: 'cluster' as BubbleLayout,
          galaxy: 'spiral' as GalaxyLayout,
          list: 'vertical' as ListLayout,
          grid: 'square' as GridLayout,
          tree: 'vertical' as TreeLayout,
          flow: 'dagre' as FlowLayout,
          tagCloud: 'circle' as TagCloudLayout,
        }),
        content_display_mode_preferences: JSON.stringify({
          wheel: 'textEmoji' as ContentDisplayMode,
          card: 'textEmoji' as ContentDisplayMode,
          bubble: 'textEmoji' as ContentDisplayMode,
          galaxy: 'textEmoji' as ContentDisplayMode,
          list: 'textEmoji' as ContentDisplayMode,
          grid: 'textEmoji' as ContentDisplayMode,
          tree: 'textEmoji' as ContentDisplayMode,
          flow: 'textEmoji' as ContentDisplayMode,
          tagCloud: 'textEmoji' as ContentDisplayMode,
        }),
        view_type_skin_ids: JSON.stringify({
          wheel: 'default-wheel-skin',
          card: 'default-card-skin',
          bubble: 'default-bubble-skin',
          galaxy: 'default-galaxy-skin',
          list: 'default-list-skin',
          grid: 'default-grid-skin',
          tree: 'default-tree-skin',
          flow: 'default-flow-skin',
          tagCloud: 'default-tagcloud-skin',
        }),
        accessibility: JSON.stringify({
          high_contrast: false,
          large_text: false,
          reduce_motion: false,
        }),
      };

      const result = await userConfigService.create(defaultConfigData);

      if (result.success && result.data) {
        setUserConfig(result.data);
        setUserConfigs([result.data]);
        console.log('[UserConfigContext] Default user config created successfully');
      } else {
        throw new Error(result.error || 'Failed to create default config');
      }
    } catch (error) {
      console.error('[UserConfigContext] Failed to create default config:', error);
    }
  }, [userConfigService]);

  // 更新用户配置
  const updateUserConfig = useCallback(async (config: Partial<UserConfig>): Promise<boolean> => {
    if (!userConfigService || !userConfig) return false;

    try {
      const result = await userConfigService.update(userConfig.id, config);
      if (result.success && result.data) {
        setUserConfig(result.data);
        console.log('[UserConfigContext] User config updated successfully');
        return true;
      }
      return false;
    } catch (error) {
      console.error('[UserConfigContext] Failed to update user config:', error);
      return false;
    }
  }, [userConfigService, userConfig]);

  // 设置活动情绪数据ID
  const setActiveEmotionDataId = useCallback(async (id: string): Promise<boolean> => {
    return await updateUserConfig({ active_emotion_data_id: id });
  }, [updateUserConfig]);

  // 设置活动皮肤ID
  const setActiveSkinId = useCallback(async (id: string): Promise<boolean> => {
    return await updateUserConfig({ active_skin_id: id });
  }, [updateUserConfig]);

  // 设置首选视图类型
  const setPreferredViewType = useCallback(async (viewType: ViewType): Promise<boolean> => {
    return await updateUserConfig({ preferred_view_type: viewType });
  }, [updateUserConfig]);

  // 设置内容显示模式
  const setContentDisplayMode = useCallback(async (mode: ContentDisplayMode): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.content_display_mode_preferences, {});
    const newPreferences = { ...currentPreferences };

    // 更新所有视图类型的内容显示模式
    const viewTypes: ViewType[] = ['wheel', 'card', 'bubble', 'galaxy', 'list', 'grid', 'tree', 'flow', 'tagCloud'];
    viewTypes.forEach((viewType) => {
      newPreferences[viewType] = mode;
    });

    return await updateUserConfig({ content_display_mode_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置轮盘实现
  const setRenderEngine = useCallback(async (implementation: RenderEngine): Promise<boolean> => {
    if (!userConfig) return false;

    // 安全解析 JSON 字符串
    const currentPreferences = safeParseJSON(userConfig.render_engine_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.wheel = implementation;

    return await updateUserConfig({ render_engine_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 切换深色模式
  const toggleDarkMode = useCallback(async (): Promise<boolean> => {
    if (!userConfig) return false;

    return await updateUserConfig({ dark_mode: !userConfig.dark_mode });
  }, [updateUserConfig, userConfig]);

  // 更新辅助功能设置
  const updateAccessibilitySettings = useCallback(async (settings: Record<string, any>): Promise<boolean> => {
    if (!userConfig) return false;

    // 安全解析 JSON 字符串
    const currentAccessibility = safeParseJSON(userConfig.accessibility, {});
    const newAccessibility = { ...currentAccessibility, ...settings };

    return await updateUserConfig({ accessibility: JSON.stringify(newAccessibility) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 重置配置到默认值
  const resetToDefaults = useCallback(async (): Promise<boolean> => {
    if (!userConfigService || !userConfig) return false;

    try {
      // 删除当前配置并创建新的默认配置
      await userConfigService.delete(userConfig.id);
      await createDefaultConfig();
      return true;
    } catch (error) {
      console.error('[UserConfigContext] Failed to reset to defaults:', error);
      return false;
    }
  }, [userConfigService, userConfig, createDefaultConfig]);

  // 获取所有用户配置
  const getUserConfigs = (): UserConfig[] => {
    return userConfigs;
  };

  // 设置活动用户配置
  const setActiveUserConfig = useCallback(async (id: string): Promise<boolean> => {
    if (!userConfigService) return false;

    try {
      const config = userConfigs.find((c) => c.id === id);
      if (config) {
        // 将当前配置设为非活动
        if (userConfig) {
          await userConfigService.update(userConfig.id, { is_active: false });
        }

        // 将选中配置设为活动
        const result = await userConfigService.update(id, { is_active: true });
        if (result.success && result.data) {
          setUserConfig(result.data);
          // 重新加载所有配置
          const allConfigsResult = await userConfigService.getUserConfigs('default-user');
          if (allConfigsResult.success && allConfigsResult.data) {
            setUserConfigs(allConfigsResult.data);
          }
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('[UserConfigContext] Failed to set active user config:', error);
      return false;
    }
  }, [userConfigService, userConfigs, userConfig]);

  // 创建用户配置
  const createUserConfig = useCallback(async (name: string): Promise<UserConfig | null> => {
    if (!userConfigService || !userConfig) return null;

    try {
      const newConfigData = {
        name,
        user_id: 'default-user',
        active_emotion_data_id: userConfig.active_emotion_data_id,
        active_skin_id: userConfig.active_skin_id,
        preferred_view_type: userConfig.preferred_view_type,
        dark_mode: userConfig.dark_mode,
        color_mode: userConfig.color_mode,
        render_engine_preferences: userConfig.render_engine_preferences,
        layout_preferences: userConfig.layout_preferences,
        content_display_mode_preferences: userConfig.content_display_mode_preferences,
        view_type_skin_ids: userConfig.view_type_skin_ids,
        accessibility: userConfig.accessibility,
        recent_emotion_data_ids: userConfig.recent_emotion_data_ids,
        recent_skin_ids: userConfig.recent_skin_ids,
      };

      const result = await userConfigService.create(newConfigData);
      if (result.success && result.data) {
        setUserConfigs([...userConfigs, result.data]);
        return result.data;
      }
      return null;
    } catch (error) {
      console.error('[UserConfigContext] Failed to create user config:', error);
      return null;
    }
  }, [userConfigService, userConfig, userConfigs]);

  // 删除用户配置
  const deleteUserConfig = useCallback(async (id: string): Promise<boolean> => {
    if (!userConfigService) return false;

    try {
      const result = await userConfigService.delete(id);
      if (result.success) {
        const updatedConfigs = userConfigs.filter((c) => c.id !== id);
        setUserConfigs(updatedConfigs);

        // 如果删除的是当前活动配置，切换到第一个可用配置
        if (userConfig?.id === id && updatedConfigs.length > 0) {
          await setActiveUserConfig(updatedConfigs[0].id);
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('[UserConfigContext] Failed to delete user config:', error);
      return false;
    }
  }, [userConfigService, userConfigs, userConfig, setActiveUserConfig]);

  // 设置视图特定的内容显示模式
  const setViewContentDisplayMode = useCallback(async (viewType: ViewType, mode: ContentDisplayMode): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.content_display_mode_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences[viewType] = mode;

    return await updateUserConfig({ content_display_mode_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置视图特定的皮肤ID
  const setViewSkinId = useCallback(async (viewType: ViewType, skinId: string): Promise<boolean> => {
    if (!userConfig) return false;

    const currentSkinIds = safeParseJSON(userConfig.view_type_skin_ids, {});
    const newSkinIds = { ...currentSkinIds };
    newSkinIds[viewType] = skinId;

    return await updateUserConfig({ view_type_skin_ids: JSON.stringify(newSkinIds) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置轮盘视图的渲染引擎
  const setWheelViewRenderingEngine = useCallback(async (implementation: RenderEngine): Promise<boolean> => {
    if (!userConfig) return false;

    // 解析 JSON 字符串
    let currentPreferences: Record<string, RenderEngine> = {};
    try {
      if (userConfig.render_engine_preferences) {
        currentPreferences = JSON.parse(userConfig.render_engine_preferences);
      }
    } catch (error) {
      console.error('[UserConfigContext] Failed to parse render_engine_preferences:', error);
    }

    const newPreferences = { ...currentPreferences };
    newPreferences.wheel = implementation;

    return await updateUserConfig({ render_engine_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig]);

  // 设置卡片视图的布局
  const setCardViewLayout = useCallback(async (layout: CardLayout): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.layout_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.card = layout;

    return await updateUserConfig({ layout_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置气泡视图的布局
  const setBubbleViewLayout = useCallback(async (layout: BubbleLayout): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.layout_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.bubble = layout;

    return await updateUserConfig({ layout_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置星系视图的布局
  const setGalaxyViewLayout = useCallback(async (layout: GalaxyLayout): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.layout_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.galaxy = layout;

    return await updateUserConfig({ layout_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置视图的渲染引擎
  const setViewRenderingEngine = useCallback(async (viewType: ViewType, implementation: string): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.render_engine_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences[viewType] = implementation as RenderEngine;

    return await updateUserConfig({ render_engine_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置列表视图的布局
  const setListViewLayout = useCallback(async (layout: ListLayout): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.layout_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.list = layout;

    return await updateUserConfig({ layout_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置网格视图的布局
  const setGridViewLayout = useCallback(async (layout: GridLayout): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.layout_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.grid = layout;

    return await updateUserConfig({ layout_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置树状视图的布局
  const setTreeViewLayout = useCallback(async (layout: TreeLayout): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.layout_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.tree = layout;

    return await updateUserConfig({ layout_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置流程图视图的布局
  const setFlowViewLayout = useCallback(async (layout: FlowLayout): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.layout_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.flow = layout;

    return await updateUserConfig({ layout_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 设置标签云视图的布局
  const setTagCloudViewLayout = useCallback(async (layout: TagCloudLayout): Promise<boolean> => {
    if (!userConfig) return false;

    const currentPreferences = safeParseJSON(userConfig.layout_preferences, {});
    const newPreferences = { ...currentPreferences };
    newPreferences.tagCloud = layout;

    return await updateUserConfig({ layout_preferences: JSON.stringify(newPreferences) });
  }, [updateUserConfig, userConfig, safeParseJSON]);

  // 获取视图的渲染引擎
  const getViewRenderingEngine = useCallback((viewType: ViewType): string => {
    if (!userConfig?.render_engine_preferences) return 'D3';
    const preferences = safeParseJSON(userConfig.render_engine_preferences, {});
    return preferences[viewType] || 'D3';
  }, [userConfig, safeParseJSON]);

  // 获取视图的布局
  const getViewLayout = useCallback((viewType: ViewType): string => {
    if (!userConfig?.layout_preferences) return 'default';
    const preferences = safeParseJSON(userConfig.layout_preferences, {});
    return preferences[viewType] || 'default';
  }, [userConfig, safeParseJSON]);

  // 获取视图的内容显示模式
  const getViewContentDisplayMode = useCallback((viewType: ViewType): ContentDisplayMode => {
    if (!userConfig?.content_display_mode_preferences) return 'textEmoji';
    const preferences = safeParseJSON(userConfig.content_display_mode_preferences, {});
    return preferences[viewType] || 'textEmoji';
  }, [userConfig, safeParseJSON]);

  // 获取视图的皮肤ID
  const getViewSkinId = useCallback((viewType: ViewType): string => {
    if (!userConfig?.view_type_skin_ids) return `default-${viewType}-skin`;
    const skinIds = safeParseJSON(userConfig.view_type_skin_ids, {});
    return skinIds[viewType] || `default-${viewType}-skin`;
  }, [userConfig, safeParseJSON]);

  // 设置颜色模式
  const setColorMode = useCallback(async (mode: ColorMode): Promise<boolean> => {
    return await updateUserConfig({ color_mode: mode });
  }, [updateUserConfig]);

  // 获取颜色模式
  const getColorMode = useCallback((): ColorMode => {
    return userConfig?.color_mode || 'warm';
  }, [userConfig]);

  // 提供上下文值
  const contextValue: UserConfigContextType = {
    userConfig,
    isLoading,
    updateUserConfig,
    setActiveEmotionDataId,
    setActiveSkinId,
    setPreferredViewType,
    setContentDisplayMode,
    setRenderEngine,
    toggleDarkMode,
    updateAccessibilitySettings,
    resetToDefaults,
    getUserConfigs,
    setActiveUserConfig,
    createUserConfig,
    deleteUserConfig,

    // 视图特定设置方法
    setViewContentDisplayMode,
    setViewSkinId,
    setViewRenderingEngine,
    setWheelViewRenderingEngine,
    setCardViewLayout,
    setBubbleViewLayout,
    setGalaxyViewLayout,
    setListViewLayout,
    setGridViewLayout,
    setTreeViewLayout,
    setFlowViewLayout,
    setTagCloudViewLayout,

    // 视图设置获取方法
    getViewRenderingEngine,
    getViewLayout,
    getViewContentDisplayMode,
    getViewSkinId,

    // 颜色模式方法
    setColorMode,
    getColorMode,
  };

  return <UserConfigContext.Provider value={contextValue}>{children}</UserConfigContext.Provider>;
};

// 添加 displayName 以支持 Hot Reload
UserConfigProvider.displayName = 'UserConfigProvider';

/**
 * 使用用户配置钩子
 */
export const useUserConfig = (): UserConfigContextType => {
  const context = useContext(UserConfigContext);
  if (context === undefined) {
    throw new Error('useUserConfig must be used within a UserConfigProvider');
  }
  return context;
};
