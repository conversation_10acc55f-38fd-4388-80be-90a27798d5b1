/**
 * 气泡视图设置组件
 * 用于配置气泡视图的布局选项
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import { Check } from 'lucide-react';
import type React from 'react';
import { toast } from 'sonner';
import type { BubbleLayout } from '../../types/userConfigTypes';
import BubbleLayoutPreview from './BubbleLayoutPreview';

interface BubbleViewSettingsProps {
  className?: string;
}

/**
 * 气泡视图设置组件
 * 用于配置气泡视图的布局选项
 */
const BubbleViewSettings: React.FC<BubbleViewSettingsProps> = ({ className }) => {
  const { t } = useLanguage();
  const { userConfig, setBubbleViewLayout } = useUserConfig();

  // 获取当前气泡布局
  const currentLayout = userConfig.layout_preferences?.bubble || 'cluster';

  // 处理布局变更
  const handleLayoutChange = (layout: BubbleLayout) => {
    if (layout === currentLayout) return;

    setBubbleViewLayout(layout);
    toast.success(t('settings.bubble_layout_changed', '气泡布局已更改'), { duration: 3000 });
  };

  // 可用的布局选项
  const layoutOptions: BubbleLayout[] = ['cluster', 'force', 'random', 'circle'];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t('settings.bubble_view_settings', '气泡视图设置')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">{t('settings.bubble_layout', '气泡布局')}</h4>
            <div className="grid grid-cols-2 gap-4">
              {layoutOptions.map((layout) => (
                <Button
                  key={layout}
                  variant="outline"
                  className="p-2 h-auto"
                  onClick={() => handleLayoutChange(layout)}
                >
                  <div className="flex flex-col items-center w-full">
                    <BubbleLayoutPreview
                      layout={layout}
                      size="md"
                      isSelected={currentLayout === layout}
                    />
                    <div className="flex items-center w-full mt-2">
                      <span>{t(`settings.bubble_layout.${layout}`, layout)}</span>
                      {currentLayout === layout && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BubbleViewSettings;
