/**
 * useQuizSession Hook
 * 使用修复后的服务架构管理Quiz会话
 */

import { useState, useCallback } from 'react';
import { Services } from '@/services/ServiceFactoryFixed';
import { QuizSession } from '@/types/schema/base';
import { CreateQuizSessionInput, UpdateQuizSessionInput } from '@/types/schema/api';
import { ServiceResult } from '@/services/types/ServiceTypes';

export interface UseQuizSessionReturn {
  // 状态
  isLoading: boolean;
  currentSession: QuizSession | null;
  error: string | null;

  // 会话操作
  createSession: (input: CreateQuizSessionInput) => Promise<ServiceResult<QuizSession>>;
  getSession: (sessionId: string) => Promise<ServiceResult<QuizSession | null>>;
  updateSession: (sessionId: string, updates: UpdateQuizSessionInput) => Promise<ServiceResult<QuizSession>>;
  startSession: (sessionId: string) => Promise<ServiceResult<QuizSession>>;
  completeSession: (sessionId: string) => Promise<ServiceResult<QuizSession>>;
  pauseSession: (sessionId: string) => Promise<ServiceResult<QuizSession>>;
  resumeSession: (sessionId: string) => Promise<ServiceResult<QuizSession>>;
  deleteSession: (sessionId: string) => Promise<ServiceResult<boolean>>;

  // 进度管理
  updateProgress: (sessionId: string, currentQuestionIndex: number, totalQuestions?: number) => Promise<ServiceResult<boolean>>;

  // 查询操作
  getUserSessions: (userId: string, limit?: number) => Promise<ServiceResult<QuizSession[]>>;
  getUserActiveSessions: (userId: string) => Promise<ServiceResult<QuizSession[]>>;
  getUserCompletedSessions: (userId: string, limit?: number) => Promise<ServiceResult<QuizSession[]>>;
  getPackSessions: (packId: string, limit?: number) => Promise<ServiceResult<QuizSession[]>>;

  // 状态管理
  setCurrentSession: (session: QuizSession | null) => void;
  clearError: () => void;
}

export const useQuizSession = (_db?: any): UseQuizSessionReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentSession, setCurrentSession] = useState<QuizSession | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 获取服务实例
  const service = Services.getQuizSessionService();

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleServiceCall = useCallback(async <T>(
    serviceCall: () => Promise<ServiceResult<T>>
  ): Promise<ServiceResult<T>> => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await serviceCall();

      if (!result.success) {
        setError(result.error || 'Unknown error occurred');
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createSession = useCallback(async (input: CreateQuizSessionInput): Promise<ServiceResult<QuizSession>> => {
    return handleServiceCall(async () => {
      const result = await service.createSession(input);

      if (result.success && result.data) {
        setCurrentSession(result.data);
      }

      return result;
    });
  }, [handleServiceCall, service]);

  const getSession = useCallback(async (sessionId: string): Promise<ServiceResult<QuizSession | null>> => {
    return handleServiceCall(async () => {
      const result = await service.findById(sessionId);

      if (result.success && result.data) {
        setCurrentSession(result.data);
      }

      return result;
    });
  }, [handleServiceCall, service]);

  const updateSession = useCallback(async (sessionId: string, updates: UpdateQuizSessionInput): Promise<ServiceResult<QuizSession>> => {
    return handleServiceCall(async () => {
      const result = await service.update(sessionId, updates);

      if (result.success && result.data) {
        setCurrentSession(result.data);
      }

      return result;
    });
  }, [handleServiceCall, service]);

  const startSession = useCallback(async (sessionId: string): Promise<ServiceResult<QuizSession>> => {
    return handleServiceCall(async () => {
      const result = await service.startSession(sessionId);

      if (result.success && result.data) {
        setCurrentSession(result.data);
      }

      return result;
    });
  }, [handleServiceCall, service]);

  const completeSession = useCallback(async (sessionId: string): Promise<ServiceResult<QuizSession>> => {
    return handleServiceCall(async () => {
      const result = await service.completeSession(sessionId);

      if (result.success && result.data) {
        setCurrentSession(result.data);
      }

      return result;
    });
  }, [handleServiceCall, service]);

  const pauseSession = useCallback(async (sessionId: string): Promise<ServiceResult<QuizSession>> => {
    return handleServiceCall(async () => {
      const result = await service.pauseSession(sessionId);

      if (result.success && result.data) {
        setCurrentSession(result.data);
      }

      return result;
    });
  }, [handleServiceCall, service]);

  const resumeSession = useCallback(async (sessionId: string): Promise<ServiceResult<QuizSession>> => {
    return handleServiceCall(async () => {
      const result = await service.resumeSession(sessionId);

      if (result.success && result.data) {
        setCurrentSession(result.data);
      }

      return result;
    });
  }, [handleServiceCall, service]);

  const deleteSession = useCallback(async (sessionId: string): Promise<ServiceResult<boolean>> => {
    return handleServiceCall(async () => {
      const result = await service.delete(sessionId);

      if (result.success && currentSession?.id === sessionId) {
        setCurrentSession(null);
      }

      return result;
    });
  }, [handleServiceCall, service, currentSession?.id]);

  const updateProgress = useCallback(async (
    sessionId: string,
    currentQuestionIndex: number,
    totalQuestions?: number
  ): Promise<ServiceResult<boolean>> => {
    return handleServiceCall(async () => {
      return await service.updateProgress(sessionId, currentQuestionIndex, totalQuestions);
    });
  }, [handleServiceCall, service]);

  const getUserSessions = useCallback(async (userId: string, limit: number = 20): Promise<ServiceResult<QuizSession[]>> => {
    return handleServiceCall(async () => {
      return await service.getUserSessions(userId, limit);
    });
  }, [handleServiceCall, service]);

  const getUserActiveSessions = useCallback(async (userId: string): Promise<ServiceResult<QuizSession[]>> => {
    return handleServiceCall(async () => {
      return await service.getUserActiveSessions(userId);
    });
  }, [handleServiceCall, service]);

  const getUserCompletedSessions = useCallback(async (userId: string, limit: number = 20): Promise<ServiceResult<QuizSession[]>> => {
    return handleServiceCall(async () => {
      return await service.getUserCompletedSessions(userId, limit);
    });
  }, [handleServiceCall, service]);

  const getPackSessions = useCallback(async (packId: string, limit: number = 50): Promise<ServiceResult<QuizSession[]>> => {
    return handleServiceCall(async () => {
      return await service.getPackSessions(packId, limit);
    });
  }, [handleServiceCall, service]);

  return {
    // 状态
    isLoading,
    currentSession,
    error,

    // 会话操作
    createSession,
    getSession,
    updateSession,
    startSession,
    completeSession,
    pauseSession,
    resumeSession,
    deleteSession,

    // 进度管理
    updateProgress,

    // 查询操作
    getUserSessions,
    getUserActiveSessions,
    getUserCompletedSessions,
    getPackSessions,

    // 状态管理
    setCurrentSession,
    clearError,
  };
};
