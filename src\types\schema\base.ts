/**
 * 基础 Schema 定义
 *
 * 基于数据库 schema 的统一类型定义，使用 zod 进行运行时验证
 * 这是所有类型定义的单一数据源 (Single Source of Truth)
 */

import { z } from 'zod';

// ==================== Enum 类型定义 ====================

export const ViewTypeSchema = z.enum([
  'wheel',
  'card',
  'bubble',
  'galaxy',
  'list',
  'grid',
  'tree',
  'flow',
  'tagCloud',
]);

export const ContentDisplayModeSchema = z.enum([
  'text',
  'emoji',
  'textEmoji',
  'animatedEmoji',
  'image',
  'textImage',
  'mixed',
]);

export const RenderEngineSchema = z.enum(['D3', 'SVG', 'R3F', 'Canvas', 'CSS', 'WebGL', 'WebGPU']);

// 布局类型 Schema
export const CardLayoutSchema = z.enum(['grid', 'list', 'masonry', 'carousel']);
export const BubbleLayoutSchema = z.enum(['cluster', 'force', 'random', 'circle', 'float', 'grid']);
export const GalaxyLayoutSchema = z.enum([
  'spiral',
  'orbital',
  'nebula',
  'circular',
  'random',
  'cluster',
  'force',
]);
export const ListLayoutSchema = z.enum(['vertical', 'horizontal']);
export const GridLayoutSchema = z.enum(['square', 'masonry', 'responsive']);
export const TreeLayoutSchema = z.enum(['vertical', 'horizontal', 'radial', 'mindmap']);
export const FlowLayoutSchema = z.enum(['dagre', 'force', 'layered', 'radial']);
export const TagCloudLayoutSchema = z.enum(['circle', 'rectangle', 'triangle', 'star', 'custom']);

// 用户配置相关类型 Schema
export const ColorModeSchema = z.enum(['warm', 'cool', 'mixed', 'game']);

// 货币类型 Schema
export const CurrencySchema = z.enum(['USD', 'EUR', 'CNY', 'JPY', 'KRW', 'GBP', 'CAD', 'AUD']);

// 解锁条件类型 Schema
export const UnlockConditionTypeSchema = z.enum(['free', 'purchase', 'achievement', 'level', 'time', 'event']);

// VIP 相关类型 Schema
export const VipTierSchema = z.enum(['basic', 'premium', 'enterprise']);
export const SubscriptionStatusSchema = z.enum(['active', 'cancelled', 'expired', 'refunded', 'pending']);
export const BillingCycleSchema = z.enum(['monthly', 'yearly', 'lifetime']);
export const PaymentMethodSchema = z.enum(['stripe', 'paypal', 'apple_pay', 'google_pay', 'wechat_pay', 'alipay']);
export const SubscriptionSourceSchema = z.enum(['web', 'mobile', 'promotion', 'admin']);
export const UnlockMethodSchema = z.enum(['purchase', 'vip', 'achievement', 'free', 'promotion']);
export const VipFeatureTypeSchema = z.enum(['unlock', 'limit', 'access', 'customization']);
export const TranslatorTypeSchema = z.enum(['human', 'ai', 'community']);
export const TagSourceSchema = z.enum(['user', 'ai_generated', 'system_suggested']);
export const TagCategorySchema = z.enum(['emotion', 'context', 'activity', 'time']);

// 皮肤类别 Schema
// 解锁条件子类型 Schema
export const UnlockConditionSubTypeSchema = z.enum([
  'mood_entries_count',
  'consecutive_days',
  'emotion_variety',
  'reflection_length',
  'time_spent',
  'feature_usage',
  'social_sharing',
  'custom_achievement'
]);

// 排序方向 Schema
export const OrderDirectionSchema = z.enum(['ASC', 'DESC']);

// 皮肤类别 Schema
export const SkinCategorySchema = z.enum(['free', 'premium', 'seasonal', 'achievement', 'custom']);

// 情绪相关类型 Schema
export const IntensityLevelSchema = z.union([
  z.literal(1),
  z.literal(2),
  z.literal(3),
  z.literal(4),
  z.literal(5),
]);

export const EmotionCategorySchema = z.enum([
  'primary',
  'secondary',
  'tertiary',
  'complex',
  'custom',
]);

// 表情集类型 Schema
export const EmojiSetTypeSchema = z.enum(['unicode', 'image', 'svg', 'animated']);

// 动画类型 Schema
export const AnimationTypeSchema = z.enum(['lottie', 'gif', 'sprite']);

// 同步状态 Schema
export const SyncStatusSchema = z.enum(['synced', 'sync_required', 'syncing', 'sync_failed']);

// 语言代码 Schema
export const LanguageCodeSchema = z.enum([
  'en',
  'zh',
  'zh-TW',
  'ja',
  'ko',
  'es',
  'fr',
  'de',
  'it',
  'pt',
  'ru',
  'ar',
  'hi',
  'th',
  'vi',
]);

// ==================== 基础类型 ====================



/**
 * 时间戳 Schema
 */
export const TimestampSchema = z.string().datetime();

/**
 * ID Schema
 */
export const IdSchema = z.string().min(1);

/**
 * 可选 ID Schema
 */
export const OptionalIdSchema = z.string().min(1).optional();

// ==================== 用户相关 Schema ====================

/**
 * 用户 Schema（基于 users 表）
 */
export const UserSchema = z.object({
  id: IdSchema,
  username: z.string().min(1),
  email: z.string().email(),
  display_name: z.string().optional(),
  avatar_url: z.string().url().optional(),

  // 账户状态
  is_active: z.boolean().default(true),
  is_verified: z.boolean().default(false),
  is_banned: z.boolean().default(false),
  banned_until: TimestampSchema.optional(),
  ban_reason: z.string().optional(),

  // VIP 状态
  is_vip: z.boolean().default(false),
  vip_tier: z.string().optional(),
  vip_expires_at: TimestampSchema.optional(),
  vip_auto_renew: z.boolean().default(false),

  // 活动追踪
  last_login_at: TimestampSchema.optional(),
  login_count: z.number().int().default(0),
  last_active_at: TimestampSchema.optional(),

  // 元数据
  registration_ip: z.string().optional(),
  registration_source: z.string().optional(),
  timezone: z.string().default('UTC'),
  locale: z.string().default('en'),

  // 同步字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  sync_status: SyncStatusSchema.default('synced'),
  server_updated_at: TimestampSchema,
});

// ==================== 情绪相关 Schema (已迁移到Quiz架构) ====================
// 注意：原emotion相关Schema已迁移到Quiz架构
// 使用 QuizPackSchema, QuizQuestionSchema, QuizQuestionOptionSchema 替代

// ==================== 表情相关 Schema ====================



/**
 * 表情集 Schema（基于 emoji_sets 表）
 */
export const EmojiSetSchema = z.object({
  id: IdSchema,
  name: z.string().min(1),
  description: z.string().optional(),
  type: EmojiSetTypeSchema,
  is_default: z.boolean().default(false),
  is_system: z.boolean().default(false),
  is_unlocked: z.boolean().default(false),
  price: z.number().optional(),
  preview_image: z.string().url().optional(),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: z.string().optional(),

  // 运行时添加的字段（用于客户端处理）
  // 注意：emoji映射现在通过展现配置管理
});



// 注意：EmojiItem已迁移到展现配置中，使用 EmojiMapping 接口替代

/**
 * 问题展现覆盖 Schema（基于 question_presentation_overrides 表）
 */
export const QuestionPresentationOverrideSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  question_id: IdSchema,

  // 覆盖配置
  presentation_overrides: z.string(), // JSON: 问题特定的展现覆盖配置
  override_reason: z.string().optional(), // 'user_preference', 'accessibility', 'context_specific'
  override_priority: z.number().int().default(1), // 覆盖优先级

  // 状态管理
  is_active: z.boolean().default(true),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: z.string().optional(),
  updated_by: z.string().optional(),
});

/**
 * Quiz包展现配置 Schema（基于 pack_presentation_configs 表）
 */
export const PackPresentationConfigSchema = z.object({
  id: IdSchema,
  pack_id: IdSchema,
  config_type: z.string(), // 'default_emoji_mapping', 'theme_config', 'layout_config'

  // 配置数据
  config_data: z.string(), // JSON: 配置数据
  config_version: z.string().default('1.0'),
  config_description: z.string().optional(),

  // 状态管理
  is_active: z.boolean().default(true),
  is_default: z.boolean().default(false),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: z.string().optional(),
  updated_by: z.string().optional(),
});

// ==================== 皮肤相关 Schema ====================

/**
 * 皮肤配置 Schema
 * 基于 ExtendedSkinConfig 接口的完整定义
 */
export const SkinConfigSchema = z.object({
  // 支持的内容显示模式和视图类型
  supported_content_modes: z.array(ContentDisplayModeSchema),
  supported_view_types: z.array(ViewTypeSchema),
  supported_render_engines: z.array(RenderEngineSchema),

  // 颜色配置
  colors: z.object({
    primary: z.string(),
    secondary: z.string(),
    background: z.string(),
    text: z.string(),
    accent: z.string(),
  }),

  // 字体配置
  fonts: z.object({
    family: z.string(),
    size: z.object({
      small: z.number(),
      medium: z.number(),
      large: z.number(),
    }),
    weight: z.object({
      normal: z.number(),
      bold: z.number(),
    }),
  }),

  // 效果配置
  effects: z.object({
    shadows: z.boolean().optional(),
    shadow_color: z.string().optional(),
    shadow_blur: z.number().optional(),
    shadow_offset_x: z.number().optional(),
    shadow_offset_y: z.number().optional(),
    animations: z.boolean().optional(),
    animation_duration: z.number().optional(),
    animation_easing: z.string().optional(),
    border_radius: z.number().optional(),
    border_width: z.number().optional(),
    border_color: z.string().optional(),
    border_style: z.string().optional(),
    opacity: z.number().optional(),
    blur: z.number().optional(),
    grayscale: z.number().optional(),
    brightness: z.number().optional(),
    contrast: z.number().optional(),
    saturation: z.number().optional(),
    hue_rotate: z.number().optional(),
    backdrop_filter: z.string().optional(),
    background_gradient: z.string().optional(),
    text_shadow: z.string().optional(),
  }),

  // 视图特定配置
  view_configs: z
    .object({
      // 通用视图配置
      common: z
        .object({
          background: z
            .object({
              type: z.enum(['solid', 'gradient', 'image', 'pattern']),
              color: z.string().optional(),
              gradient: z.string().optional(),
              image_url: z.string().optional(),
              pattern_type: z.string().optional(),
              opacity: z.number().optional(),
              blur: z.number().optional(),
            })
            .optional(),
          container: z
            .object({
              padding: z.number().optional(),
              margin: z.number().optional(),
              max_width: z.number().optional(),
              max_height: z.number().optional(),
              aspect_ratio: z.number().optional(),
              overflow: z.enum(['visible', 'hidden', 'scroll', 'auto']).optional(),
              position: z.enum(['static', 'relative', 'absolute', 'fixed']).optional(),
            })
            .optional(),
          animation: z
            .object({
              entrance_animation: z.string().optional(),
              exit_animation: z.string().optional(),
              duration: z.number().optional(),
              delay: z.number().optional(),
              easing: z.string().optional(),
              loop: z.boolean().optional(),
            })
            .optional(),
          responsive: z
            .object({
              breakpoints: z
                .object({
                  small: z.number().optional(),
                  medium: z.number().optional(),
                  large: z.number().optional(),
                })
                .optional(),
              adaptive_layout: z.boolean().optional(),
            })
            .optional(),
        })
        .optional(),

      // 轮盘视图配置
      wheel: z
        .object({
          container_size: z.number().optional(),
          wheel_radius: z.number().optional(),
          inner_radius: z.number().optional(),
          sector_gap: z.number().optional(),
          sector_padding: z.number().optional(),
          sector_border_radius: z.number().optional(),
          sector_border_width: z.number().optional(),
          sector_border_color: z.string().optional(),
          sector_stroke_width: z.number().optional(),
          sector_stroke_color: z.string().optional(),
          center_circle_size: z.number().optional(),
          center_circle_color: z.string().optional(),
          emoji_size: z.number().optional(),
          emoji_position: z.enum(['center', 'inner', 'outer']).optional(),
          text_size: z.number().optional(),
          text_position: z.enum(['center', 'inner', 'outer']).optional(),
          text_color: z.string().optional(),
          text_visible: z.enum(['always', 'hover', 'never']).optional(),
          highlight_on_hover: z.boolean().optional(),
          highlight_color: z.string().optional(),
          decorations: z.boolean().optional(),
          decoration_type: z
            .enum(['none', 'glow', 'sparkles', 'dots', 'waves', 'lines', 'custom'])
            .optional(),
          decoration_density: z.number().optional(),
          decoration_color: z.string().optional(),
          use_3d_effects: z.boolean().optional(),
          perspective: z.number().optional(),
          rotate_x: z.number().optional(),
          rotate_y: z.number().optional(),
          rotate_z: z.number().optional(),
          depth: z.number().optional(),
          shadow_enabled: z.boolean().optional(),
          shadow_color: z.string().optional(),
          shadow_blur: z.number().optional(),
          shadow_offset_x: z.number().optional(),
          shadow_offset_y: z.number().optional(),
          hover_effect: z.enum(['highlight', 'scale', 'glow', 'lift', 'pulse', 'none']).optional(),
          hover_scale: z.number().optional(),
          selection_animation: z
            .enum(['fade', 'pulse', 'spin', 'bounce', 'flash', 'none'])
            .optional(),
          selection_indicator: z.enum(['border', 'glow', 'icon', 'none']).optional(),
          transition_duration: z.number().optional(),
          transition_easing: z.string().optional(),
          wheel_background: z.string().optional(),
          wheel_background_opacity: z.number().optional(),
          tier_transition: z.enum(['zoom', 'slide', 'fade', 'flip', 'none']).optional(),
          tier_transition_duration: z.number().optional(),
          wheel_layout: z.enum(['standard', 'concentric', 'spiral', 'flower', 'custom']).optional(),
          drag_enabled: z.boolean().optional(),
          zoom_enabled: z.boolean().optional(),
          zoom_min_scale: z.number().optional(),
          zoom_max_scale: z.number().optional(),
          rotation_enabled: z.boolean().optional(),
          rotation_auto_play: z.boolean().optional(),
          rotation_speed: z.number().optional(),
          rotation_clockwise: z.boolean().optional(),
          responsive_scaling: z.boolean().optional(),
          min_size: z.number().optional(),
          max_size: z.number().optional(),
        })
        .optional(),

      // 卡片视图配置
      card: z
        .object({
          card_size: z.number().optional(),
          card_width: z.number().optional(),
          card_height: z.number().optional(),
          card_spacing: z.number().optional(),
          list_card_height: z.number().optional(),
          card_border_radius: z.number().optional(),
          card_border_width: z.number().optional(),
          card_border_color: z.string().optional(),
          card_border_style: z.enum(['solid', 'dashed', 'dotted', 'double']).optional(),
          card_background: z.string().optional(),
          card_background_opacity: z.number().optional(),
          card_shadow: z.string().optional(),
          card_elevation: z.number().optional(),
          card_hover_effect: z.enum(['lift', 'glow', 'scale', 'flip', 'none']).optional(),
          card_hover_scale: z.number().optional(),
          card_selected_effect: z.enum(['border', 'glow', 'scale', 'none']).optional(),
          card_selected_border_color: z.string().optional(),
          card_selected_glow_color: z.string().optional(),
          card_corner_style: z.enum(['rounded', 'sharp', 'cut']).optional(),
          emoji_size: z.number().optional(),
          emoji_position: z.enum(['top', 'center', 'bottom']).optional(),
          text_size: z.number().optional(),
          text_position: z.enum(['top', 'bottom', 'overlay']).optional(),
          text_color: z.string().optional(),
          text_background: z.string().optional(),
          text_background_opacity: z.number().optional(),
          text_padding: z.number().optional(),
          layout: z.enum(['grid', 'list', 'masonry', 'carousel']).optional(),
          columns: z.number().optional(),
          aspect_ratio: z.number().optional(),
          card_animation: z.enum(['fade', 'slide', 'scale', 'none']).optional(),
          card_animation_duration: z.number().optional(),
          card_animation_delay: z.number().optional(),
        })
        .optional(),

      // 气泡视图配置
      bubble: z
        .object({
          bubble_size: z.number().optional(),
          bubble_min_size: z.number().optional(),
          bubble_max_size: z.number().optional(),
          bubble_spacing: z.number().optional(),
          bubble_shape: z.enum(['circle', 'oval', 'rounded-rect', 'custom']).optional(),
          bubble_border_radius: z.number().optional(),
          bubble_border_width: z.number().optional(),
          bubble_border_color: z.string().optional(),
          bubble_background: z.string().optional(),
          bubble_background_opacity: z.number().optional(),
          bubble_gradient: z.boolean().optional(),
          bubble_gradient_colors: z.array(z.string()).optional(),
          bubble_gradient_direction: z.enum(['radial', 'linear']).optional(),
          bubble_shadow: z.boolean().optional(),
          bubble_shadow_color: z.string().optional(),
          bubble_shadow_blur: z.number().optional(),
          bubble_shadow_offset_x: z.number().optional(),
          bubble_shadow_offset_y: z.number().optional(),
          bubble_hover_effect: z.enum(['scale', 'glow', 'pulse', 'none']).optional(),
          bubble_hover_scale: z.number().optional(),
          bubble_selected_effect: z.enum(['border', 'glow', 'scale', 'none']).optional(),
          bubble_selected_border_color: z.string().optional(),
          bubble_selected_glow_color: z.string().optional(),
          emoji_size: z.number().optional(),
          text_visible: z.enum(['always', 'hover', 'never']).optional(),
          text_size: z.number().optional(),
          text_color: z.string().optional(),
          text_background: z.string().optional(),
          text_background_opacity: z.number().optional(),
          text_position: z.enum(['inside', 'outside', 'tooltip']).optional(),
          layout: z.enum(['circle', 'cluster', 'float', 'grid', 'force', 'random']).optional(),
          floating_animation: z.boolean().optional(),
          floating_speed: z.number().optional(),
          floating_amplitude: z.number().optional(),
          collision_detection: z.boolean().optional(),
          drag_enabled: z.boolean().optional(),
          snap_to_grid: z.boolean().optional(),
          responsive_scaling: z.boolean().optional(),
        })
        .optional(),

      // 星系视图配置
      galaxy: z
        .object({
          container_size: z.number().optional(),
          star_size: z.number().optional(),
          star_min_size: z.number().optional(),
          star_max_size: z.number().optional(),
          orbit_size: z.number().optional(),
          orbit_width: z.number().optional(),
          orbit_color: z.string().optional(),
          orbit_opacity: z.number().optional(),
          orbit_visible: z.boolean().optional(),
          center_size: z.number().optional(),
          center_color: z.string().optional(),
          center_glow: z.boolean().optional(),
          center_glow_color: z.string().optional(),
          center_glow_size: z.number().optional(),
          background_type: z.enum(['stars', 'nebula', 'solid', 'gradient']).optional(),
          background_color: z.string().optional(),
          background_star_density: z.number().optional(),
          background_star_color: z.string().optional(),
          shadow_color: z.string().optional(),
          shadow_blur: z.number().optional(),
          hover_effect: z.enum(['scale', 'glow', 'pulse', 'none']).optional(),
          selection_animation: z.enum(['pulse', 'glow', 'scale', 'none']).optional(),
          transition_duration: z.number().optional(),
          background_nebula_color: z.string().optional(),
          background_nebula_opacity: z.number().optional(),
          star_shape: z.enum(['circle', 'star', 'custom']).optional(),
          star_color: z.string().optional(),
          star_glow: z.boolean().optional(),
          star_glow_color: z.string().optional(),
          star_glow_size: z.number().optional(),
          star_border: z.boolean().optional(),
          star_border_color: z.string().optional(),
          star_border_width: z.number().optional(),
          star_hover_effect: z.enum(['scale', 'glow', 'pulse', 'none']).optional(),
          star_hover_scale: z.number().optional(),
          star_selected_effect: z.enum(['border', 'glow', 'scale', 'none']).optional(),
          star_selected_border_color: z.string().optional(),
          star_selected_glow_color: z.string().optional(),
          emoji_size: z.number().optional(),
          text_visible: z.enum(['always', 'hover', 'never']).optional(),
          text_size: z.number().optional(),
          text_color: z.string().optional(),
          text_background: z.string().optional(),
          text_background_opacity: z.number().optional(),
          text_position: z.enum(['inside', 'outside', 'tooltip']).optional(),
          layout: z
            .enum(['circular', 'spiral', 'random', 'custom', 'cluster', 'force', 'circle'])
            .optional(),
          rotation_enabled: z.boolean().optional(),
          rotation_speed: z.number().optional(),
          rotation_direction: z.enum(['clockwise', 'counterclockwise']).optional(),
          zoom_enabled: z.boolean().optional(),
          zoom_min_scale: z.number().optional(),
          zoom_max_scale: z.number().optional(),
          parallax_effect: z.boolean().optional(),
          parallax_intensity: z.number().optional(),
          perspective_3d: z.boolean().optional(),
          perspective_3d_intensity: z.number().optional(),
        })
        .optional(),

      // 树状视图配置
      tree: z
        .object({
          tree_type: z.enum(['vertical', 'horizontal', 'radial', 'mindmap']).optional(),
          node_size: z.number().optional(),
          node_spacing: z.number().optional(),
          level_spacing: z.number().optional(),
          node_border_radius: z.number().optional(),
          node_border_width: z.number().optional(),
          node_border_color: z.string().optional(),
          node_background: z.string().optional(),
          node_background_opacity: z.number().optional(),
          node_shadow: z.boolean().optional(),
          node_shadow_color: z.string().optional(),
          node_shadow_blur: z.number().optional(),
          node_hover_effect: z.enum(['scale', 'glow', 'pulse', 'none']).optional(),
          node_selected_effect: z.enum(['border', 'glow', 'scale', 'none']).optional(),
          link_style: z.enum(['straight', 'curved', 'orthogonal', 'step']).optional(),
          link_width: z.number().optional(),
          link_color: z.string().optional(),
          link_opacity: z.number().optional(),
          link_animation: z.boolean().optional(),
          link_animation_type: z.enum(['dash', 'flow', 'pulse']).optional(),
          link_animation_speed: z.number().optional(),
          emoji_size: z.number().optional(),
          emoji_position: z.enum(['left', 'right', 'top', 'bottom', 'center']).optional(),
          text_size: z.number().optional(),
          text_color: z.string().optional(),
          text_position: z.enum(['inside', 'outside']).optional(),
          collapsible: z.boolean().optional(),
          initially_expanded: z.boolean().optional(),
          zoom_enabled: z.boolean().optional(),
          pan_enabled: z.boolean().optional(),
          minimap: z.boolean().optional(),
          minimap_size: z.number().optional(),
          minimap_position: z
            .enum(['top-left', 'top-right', 'bottom-left', 'bottom-right'])
            .optional(),
        })
        .optional(),

      // 流程图视图配置
      flow: z
        .object({
          node_size: z.number().optional(),
          node_width: z.number().optional(),
          node_height: z.number().optional(),
          node_spacing: z.number().optional(),
          node_border_radius: z.number().optional(),
          node_border_width: z.number().optional(),
          node_border_color: z.string().optional(),
          node_background: z.string().optional(),
          node_background_opacity: z.number().optional(),
          node_shadow: z.boolean().optional(),
          node_hover_effect: z.enum(['scale', 'glow', 'pulse', 'none']).optional(),
          node_selected_effect: z.enum(['border', 'glow', 'scale', 'none']).optional(),
          edge_style: z.enum(['straight', 'curved', 'orthogonal', 'bezier']).optional(),
          edge_width: z.number().optional(),
          edge_color: z.string().optional(),
          edge_opacity: z.number().optional(),
          edge_animation: z.boolean().optional(),
          edge_animation_type: z.enum(['dash', 'flow', 'pulse']).optional(),
          edge_animation_speed: z.number().optional(),
          arrow_size: z.number().optional(),
          arrow_color: z.string().optional(),
          emoji_size: z.number().optional(),
          emoji_position: z.enum(['left', 'right', 'top', 'bottom', 'center']).optional(),
          text_size: z.number().optional(),
          text_color: z.string().optional(),
          text_position: z.enum(['inside', 'outside']).optional(),
          layout: z.enum(['dagre', 'force', 'layered', 'radial']).optional(),
          layout_direction: z.enum(['LR', 'RL', 'TB', 'BT']).optional(),
          zoom_enabled: z.boolean().optional(),
          pan_enabled: z.boolean().optional(),
          minimap: z.boolean().optional(),
        })
        .optional(),

      // 标签云视图配置
      tag_cloud: z
        .object({
          min_font_size: z.number().optional(),
          max_font_size: z.number().optional(),
          font_family: z.string().optional(),
          font_weight: z.union([z.number(), z.string()]).optional(),
          font_style: z.enum(['normal', 'italic']).optional(),
          text_color: z.string().optional(),
          color_scheme: z.array(z.string()).optional(),
          shape: z.enum(['circle', 'rectangle', 'triangle', 'star', 'custom']).optional(),
          padding: z.number().optional(),
          rotation_range: z.tuple([z.number(), z.number()]).optional(),
          spiral: z.enum(['archimedean', 'rectangular']).optional(),
          random_rotation: z.boolean().optional(),
          random_colors: z.boolean().optional(),
          background_color: z.string().optional(),
          background_opacity: z.number().optional(),
          border: z.boolean().optional(),
          border_color: z.string().optional(),
          border_width: z.number().optional(),
          shadow: z.boolean().optional(),
          shadow_color: z.string().optional(),
          shadow_blur: z.number().optional(),
          hover_effect: z.enum(['scale', 'glow', 'color', 'none']).optional(),
          hover_scale: z.number().optional(),
          hover_color: z.string().optional(),
          selected_effect: z.enum(['scale', 'glow', 'color', 'none']).optional(),
          selected_color: z.string().optional(),
          animation: z.boolean().optional(),
          animation_type: z.enum(['rotate', 'pulse', 'float', 'none']).optional(),
          animation_speed: z.number().optional(),
          emoji_visible: z.boolean().optional(),
          emoji_size: z.number().optional(),
          emoji_position: z.enum(['prefix', 'suffix', 'none']).optional(),
        })
        .optional(),
    })
    .optional(),
});

/**
 * 皮肤 Schema（基于 skins 表）
 */
export const SkinSchema = z.object({
  id: IdSchema,
  name: z.string().min(1),
  description: z.string().optional(),
  category: SkinCategorySchema.optional(),
  version: z.string().optional(),
  tags: z.string().optional(), // JSON array
  preview_image_light: z.string().url().optional(),
  preview_image_dark: z.string().url().optional(),
  is_premium: z.boolean().default(false),
  is_unlocked: z.boolean().default(false),
  unlock_conditions: z.string().optional(), // JSON object
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  author: z.string().optional(),
  supported_content_modes: z.array(ContentDisplayModeSchema), // JSON array
  supported_view_types: z.array(ViewTypeSchema), // JSON array
  supported_render_engines: z.array(RenderEngineSchema), // JSON array
  config: SkinConfigSchema,

  // 运行时添加的字段（用于客户端处理）
  parsedConfig: z.any().optional(), // 解析后的配置对象
  parsedTags: z.array(z.string()).optional(), // 解析后的标签数组
  parsedUnlockConditions: z.any().optional(), // 解析后的解锁条件对象
});

// ==================== 翻译相关 Schema ====================



/**
 * UI 标签 Schema（基于 ui_labels 表）
 */
export const UILabelSchema = z.object({
  key: z.string().min(1), // Primary key
  default_text: z.string().min(1),
  category: z.string().optional(),
  description: z.string().optional(),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * UI 标签翻译 Schema（基于 ui_label_translations 表）
 */
export const UILabelTranslationSchema = z.object({
  id: IdSchema,
  label_key: z.string().min(1),
  language_code: LanguageCodeSchema,
  translated_text: z.string().min(1),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

// 注意：EmotionTranslationSchema已废弃，因为依赖于已废弃的emotions表
// 情绪翻译现在通过Quiz问题和选项的localized字段管理

// 注意：EmotionDataSetTranslationSchema已废弃，因为依赖于已废弃的emotion_data_sets表
// 数据集翻译现在通过Quiz包的localized字段管理

/**
 * 表情集翻译 Schema（基于 emoji_set_translations 表）
 */
export const EmojiSetTranslationSchema = z.object({
  emoji_set_id: IdSchema,
  language_code: LanguageCodeSchema,
  translated_name: z.string().min(1),
  translated_description: z.string().optional(),
});

/**
 * 皮肤翻译 Schema（基于 skin_translations 表）
 */
export const SkinTranslationSchema = z.object({
  skin_id: IdSchema,
  language_code: LanguageCodeSchema,
  translated_name: z.string().min(1),
  translated_description: z.string().optional(),
});



// ==================== 心情记录相关 Schema ====================

/**
 * 心情记录 Schema（基于 mood_entries 表）
 */
export const MoodEntrySchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  timestamp: TimestampSchema,
  emotion_data_set_id: OptionalIdSchema,
  intensity: z.number().int().min(0).max(100).optional(),
  reflection: z.string().optional(),
  tags: z.string().optional(), // JSON array

  // 表情集上下文
  emoji_set_id: OptionalIdSchema,
  emoji_set_version: z.string().optional(),

  // 皮肤配置快照
  skin_id: OptionalIdSchema,
  skin_config_snapshot: z.string().optional(), // JSON

  // 显示配置快照
  view_type_used: ViewTypeSchema.optional(),
  render_engine_used: RenderEngineSchema.optional(),
  display_mode_used: ContentDisplayModeSchema.optional(),

  // 用户配置快照
  user_config_snapshot: z.string().optional(), // JSON

  // 同步字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  sync_status: SyncStatusSchema.default('sync_required'),
  server_id: OptionalIdSchema,
  last_synced_at: TimestampSchema.optional(),
});

/**
 * 情绪选择 Schema（基于 emotion_selections 表）
 */
export const EmotionSelectionSchema = z.object({
  id: IdSchema,
  mood_entry_id: IdSchema,
  emotion_id: IdSchema,
  tier_level: z.number().int().min(1).max(10),
  emotion_data_set_emotion_id: OptionalIdSchema,
  intensity: z.number().int().min(0).max(100).optional(),
  created_at: TimestampSchema,

  // 表情信息
  emoji_item_id: OptionalIdSchema,
  emoji_unicode: z.string().optional(),
  emoji_image_url: z.string().url().optional(),
  emoji_animation_data: z.string().optional(), // JSON

  // 选择上下文
  selection_path: z.string().optional(), // JSON array
  parent_selection_id: OptionalIdSchema,
});



// ==================== 用户配置相关 Schema ====================

// ==================== 全局应用设置 Schema ====================

/**
 * 全局应用配置 Schema（简化版 user_configs 表）
 */
export const GlobalAppConfigSchema = z.object({
  id: IdSchema,
  name: z.string().min(1).default('default'),
  user_id: IdSchema,
  is_active: z.boolean().default(true),

  // 主题设置
  theme_mode: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.enum(['zh-CN', 'en-US']).default('zh-CN'),

  // 全局无障碍设置 (JSON: GlobalAccessibilityConfig)
  accessibility: z.string().default('{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}'),

  // 通知和音效
  notifications_enabled: z.boolean().default(true),
  sound_enabled: z.boolean().default(true),

  // 审计字段
  created_at: TimestampSchema,
  last_updated: TimestampSchema,
});

/**
 * 全局无障碍配置 Schema
 */
export const GlobalAccessibilityConfigSchema = z.object({
  high_contrast: z.boolean(),
  large_text: z.boolean(),
  reduce_motion: z.boolean(),
  screen_reader_support: z.boolean(),
});

// ==================== Quiz系统配置 Schema ====================

// 注意：UserQuizPreferencesSchema 和 QuizPackOverridesSchema 已重命名
// 新的Schema名称在下面定义，这里保留注释作为参考

/**
 * Quiz会话配置快照 Schema
 */
export const QuizSessionConfigSchema = z.object({
  id: IdSchema,
  session_id: IdSchema,
  user_id: IdSchema,
  pack_id: IdSchema,

  // 最终合并的展现配置 (JSON: QuizPresentationConfig)
  final_presentation_config: z.string(),

  // 配置来源追踪 (JSON: ConfigSources)
  config_sources: z.string(),

  // 配置元数据
  personalization_level: z.number().int().min(0).max(100).default(50),
  config_version: z.string().default('2.0'),

  // 审计字段
  created_at: TimestampSchema,
});

// 保留旧的UserConfigSchema作为GlobalAppConfigSchema的别名，用于向后兼容
export const UserConfigSchema = GlobalAppConfigSchema;

// ==================== 标签相关 Schema ====================

/**
 * 标签 Schema（基于 tags 表）
 */
export const TagSchema = z.object({
  id: IdSchema,
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  category: z.string().optional(), // 'emotion', 'context', 'activity', 'time'
  color: z.string().optional(), // Hex color code
  icon: z.string().optional(), // Icon identifier

  // Tag metadata
  is_system: z.boolean().default(false), // System-defined vs user-defined
  is_active: z.boolean().default(true),
  usage_count: z.number().int().min(0).default(0), // Track how often this tag is used

  // Audit fields
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: OptionalIdSchema, // User who created this tag
});

/**
 * 标签翻译 Schema（基于 tag_translations 表）
 */
export const TagTranslationSchema = z.object({
  tag_id: IdSchema,
  language_code: z.string().min(2).max(5), // e.g., 'en', 'zh', 'es'
  translated_name: z.string().min(1),
  translated_description: z.string().optional(),

  // Translation metadata
  translation_quality: z.number().min(0).max(1).optional(), // Quality score
  translator_type: z.enum(['human', 'ai', 'community']).default('human'),

  // Audit fields
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * Quiz结果标签关联 Schema（基于 quiz_result_tags 表）
 */
export const QuizResultTagSchema = z.object({
  quiz_result_id: IdSchema,
  tag_id: IdSchema,

  // Tag association metadata
  added_by: OptionalIdSchema, // User who added this tag association
  confidence_score: z.number().min(0).max(1).optional(), // For AI-generated tags
  tag_source: z.enum(['user', 'ai_generated', 'system_suggested']).default('user'),

  // Audit fields
  created_at: TimestampSchema,
});

/**
 * 心情记录标签关联 Schema（基于 mood_entry_tags 表）- 向后兼容
 */
export const MoodEntryTagSchema = z.object({
  mood_entry_id: IdSchema,
  tag_id: IdSchema,

  // Tag association metadata
  added_by: OptionalIdSchema, // User who added this tag association
  confidence_score: z.number().min(0).max(1).optional(), // For AI-generated tags
  tag_source: z.enum(['user', 'ai_generated', 'system_suggested']).default('user'),

  // Audit fields
  created_at: TimestampSchema,
});

/**
 * 情绪数据集情绪关联 Schema（基于 emotion_data_set_emotions 表）
 */
export const EmotionDataSetEmotionSchema = z.object({
  id: IdSchema,
  emotion_data_set_id: IdSchema,
  tier_id: IdSchema,
  emotion_id: IdSchema,
  position: z.number().int().min(0).optional(),
  created_at: TimestampSchema,
});



// ==================== 皮肤解锁条件 Schema ====================

export const UnlockConditionsSchema = z.object({
  type: UnlockConditionTypeSchema,
  price: z.number().min(0).optional(),
  currency: CurrencySchema.optional(),
  achievement_id: OptionalIdSchema,
  level_required: z.number().int().min(1).optional(),
  start_date: TimestampSchema.optional(),
  end_date: TimestampSchema.optional(),
  event_id: OptionalIdSchema,
  conditions: z
    .array(
      z.object({
        type: UnlockConditionSubTypeSchema,
        value: z.any(),
      })
    )
    .optional(),
});

// ==================== 导出类型 ====================

// 导出 TypeScript 类型
export type User = z.infer<typeof UserSchema>;
// 注意：原emotion相关类型已迁移到Quiz架构，使用 QuizQuestionOption 等替代
export type EmojiSet = z.infer<typeof EmojiSetSchema>;
export type QuestionPresentationOverride = z.infer<typeof QuestionPresentationOverrideSchema>;
export type PackPresentationConfig = z.infer<typeof PackPresentationConfigSchema>;
// 注意：EmojiItem已迁移到展现配置，使用 EmojiMapping 接口替代

// **新增**: Emoji映射相关类型
export interface EmojiMapping {
  primary: string;
  alternatives: string[];
}

export interface EmojiMappingResult {
  emoji: string;
  color: string;
  animation: string;
  source: 'system' | 'pack' | 'user' | 'question';
}

export interface EmotionPresentation {
  emoji_mapping: Record<string, EmojiMapping>;
  color_mapping: Record<string, string>;
  animation_mapping: Record<string, string>;
}
export type Skin = z.infer<typeof SkinSchema>;
export type DatabaseSkin = Skin; // 数据库版本的 Skin 类型别名
export type Tag = z.infer<typeof TagSchema>;
export type QuizResultTag = z.infer<typeof QuizResultTagSchema>;
export type MoodEntry = z.infer<typeof MoodEntrySchema>;
export type EmotionSelection = z.infer<typeof EmotionSelectionSchema>;
export type MoodEntryTag = z.infer<typeof MoodEntryTagSchema>;

// 配置相关类型
export type GlobalAppConfig = z.infer<typeof GlobalAppConfigSchema>;
export type GlobalAccessibilityConfig = z.infer<typeof GlobalAccessibilityConfigSchema>;
export type UserPresentationConfig = z.infer<typeof UserPresentationConfigSchema>;
export type PackPresentationOverride = z.infer<typeof PackPresentationOverrideSchema>;
export type QuizSessionConfig = z.infer<typeof QuizSessionConfigSchema>;
export type UserConfig = z.infer<typeof UserConfigSchema>; // 向后兼容

// 向后兼容的类型别名
export type UserQuizPreferences = UserPresentationConfig;
export type QuizPackOverrides = PackPresentationOverride;

// 翻译相关类型
export type LanguageCode = z.infer<typeof LanguageCodeSchema>;
export type UILabel = z.infer<typeof UILabelSchema>;
export type UILabelTranslation = z.infer<typeof UILabelTranslationSchema>;
// 注意：EmotionTranslation 和 EmotionDataSetTranslation 已废弃
// 翻译现在通过Quiz相关Schema的localized字段管理
export type EmojiSetTranslation = z.infer<typeof EmojiSetTranslationSchema>;
export type SkinTranslation = z.infer<typeof SkinTranslationSchema>;
export type TagTranslation = z.infer<typeof TagTranslationSchema>;

// 视图和布局类型
export type ViewType = z.infer<typeof ViewTypeSchema>;
export type ContentDisplayMode = z.infer<typeof ContentDisplayModeSchema>;
export type RenderEngine = z.infer<typeof RenderEngineSchema>;
export type CardLayout = z.infer<typeof CardLayoutSchema>;
export type BubbleLayout = z.infer<typeof BubbleLayoutSchema>;
export type GalaxyLayout = z.infer<typeof GalaxyLayoutSchema>;
export type ListLayout = z.infer<typeof ListLayoutSchema>;
export type GridLayout = z.infer<typeof GridLayoutSchema>;
export type TreeLayout = z.infer<typeof TreeLayoutSchema>;
export type FlowLayout = z.infer<typeof FlowLayoutSchema>;
export type TagCloudLayout = z.infer<typeof TagCloudLayoutSchema>;

// 表情相关类型
export type EmojiSetType = z.infer<typeof EmojiSetTypeSchema>;
export type AnimationType = z.infer<typeof AnimationTypeSchema>;

// 情绪相关类型
export type IntensityLevel = z.infer<typeof IntensityLevelSchema>;
export type EmotionCategory = z.infer<typeof EmotionCategorySchema>;

// 用户配置相关类型
export type ColorMode = z.infer<typeof ColorModeSchema>;
export type SyncStatus = z.infer<typeof SyncStatusSchema>;

// 皮肤相关类型
export type UnlockConditions = z.infer<typeof UnlockConditionsSchema>;
export type SkinCategory = z.infer<typeof SkinCategorySchema>;

// 解锁条件相关类型
export type UnlockConditionType = z.infer<typeof UnlockConditionTypeSchema>;
export type UnlockConditionSubType = z.infer<typeof UnlockConditionSubTypeSchema>;
export type Currency = z.infer<typeof CurrencySchema>;
export type OrderDirection = z.infer<typeof OrderDirectionSchema>;

// ==================== 类型守卫 ====================

/**
 * 检查是否为有效的心情记录
 */
export function isMoodEntry(obj: any): obj is MoodEntry {
  try {
    MoodEntrySchema.parse(obj);
    return true;
  } catch {
    return false;
  }
}

/**
 * 检查是否为有效的用户
 */
export function isUser(obj: any): obj is User {
  try {
    UserSchema.parse(obj);
    return true;
  } catch {
    return false;
  }
}

// 注意：原emotion验证函数已迁移，使用 QuizQuestionOptionSchema.parse() 替代

// ==================== 常量导出 ====================

/**
 * 所有可用的视图类型
 */
export const allViewTypes: ViewType[] = [
  'wheel',
  'card',
  'bubble',
  'list',
  'grid',
  'galaxy',
  'tree',
  'flow',
  'tagCloud',
];

/**
 * 所有可用的内容显示模式
 */
export const allContentDisplayModes: ContentDisplayMode[] = [
  'text',
  'emoji',
  'textEmoji',
  'animatedEmoji',
  'image',
  'textImage',
  'mixed',
];

/**
 * 所有可用的渲染引擎
 */
export const allRenderEngines: RenderEngine[] = [
  'D3',
  'SVG',
  'R3F',
  'Canvas',
  'CSS',
  'WebGL',
  'WebGPU',
];

// ==================== Quiz系统相关 Schema ====================

/**
 * 用户等级 Schema
 */
export const UserLevelSchema = z.enum(['beginner', 'regular', 'advanced', 'vip']);

/**
 * Quiz会话状态 Schema
 */
export const QuizSessionStatusSchema = z.enum([
  'INITIATED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABORTED'
]);

/**
 * 交互方法 Schema
 */
export const InteractionMethodSchema = z.enum(['click', 'touch', 'keyboard', 'voice']);

/**
 * Quiz包 Schema（基于 quiz_packs 表 - 新架构）
 */
export const QuizPackSchema = z.object({
  id: IdSchema,
  name: z.string().min(1),
  name_localized: z.string().optional(), // JSON: 多语言名称
  description: z.string().optional(),
  description_localized: z.string().optional(), // JSON: 多语言描述
  version: z.string().default('1.0.0'),

  // Quiz分类和类型（支持主流和替代Quiz）
  category: z.string(), // 'daily', 'assessment', 'therapy', 'research', 'entertainment', 'education'
  quiz_type: z.string(), // 'emotion_wheel', 'traditional_scale', 'personality_test', 'iq_test', 'knowledge_quiz', 'survey', 'game_quiz', 'mixed'
  difficulty_level: z.number().int().min(1).max(5).default(1), // 1-5 数值难度等级
  quiz_style: z.string().optional(), // 'mainstream', 'alternative', 'experimental', 'cultural_specific'

  // 时长和标签
  estimated_duration_minutes: z.number().int().min(1).optional(),
  tags: z.string().optional(), // JSON array: 标签列表

  // 注意：原emotion_data_set_id已移除，Quiz包现在直接包含问题和选项

  // Quiz逻辑配置（JSON: QuizLogicConfig）
  quiz_logic_config: z.string().optional(),

  // 默认展现提示（JSON: DefaultPresentationHints，可选）
  default_presentation_hints: z.string().optional(),

  // 元数据（JSON: QuizPackMetadata）
  metadata: z.string().optional(),

  // 状态管理
  is_active: z.boolean().default(true),
  is_default: z.boolean().default(false),
  sort_order: z.number().int().default(0),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: z.string().optional(),
  updated_by: z.string().optional(),
});

/**
 * Quiz问题 Schema（基于 quiz_questions 表 - 新架构）
 */
export const QuizQuestionSchema = z.object({
  id: IdSchema,
  pack_id: IdSchema,

  // 问题基本信息
  question_text: z.string().min(1),
  question_text_localized: z.string().optional(), // JSON: 多语言文本
  question_type: z.string(), // 'single_choice', 'multiple_choice', 'scale_rating', 'emotion_wheel', 'text_input', 'slider', 'ranking', 'matrix'

  // 问题顺序和分组
  question_order: z.number().int(),
  question_group: z.string().optional(), // 问题分组 (如: '脏腑功能', '证素状态')
  tier_level: z.number().int().default(1), // 问题层级 (1, 2, 3...)

  // 问题配置
  question_config: z.string().optional(), // JSON: 问题特定配置
  validation_rules: z.string().optional(), // JSON: 验证规则
  scoring_config: z.string().optional(), // JSON: 评分配置

  // 依赖关系
  parent_question_id: OptionalIdSchema, // 父问题ID (用于层级问题)
  dependency_rules: z.string().optional(), // JSON: 依赖规则

  // 状态管理
  is_required: z.boolean().default(true),
  is_active: z.boolean().default(true),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: z.string().optional(),
  updated_by: z.string().optional(),
});

/**
 * Quiz问题选项 Schema（基于 quiz_question_options 表 - 新架构）
 */
export const QuizQuestionOptionSchema = z.object({
  id: IdSchema,
  question_id: IdSchema,

  // 选项基本信息
  option_text: z.string().min(1),
  option_text_localized: z.string().optional(), // JSON: 多语言文本
  option_value: z.string(),
  option_type: z.enum(['choice', 'scale_point', 'ranking_item', 'matrix_row', 'matrix_column', 'input_constraint']),

  // 选项顺序和评分
  option_order: z.number().int(),
  scoring_value: z.number().optional(),
  scoring_weight: z.number().optional(),

  // 数值范围 (用于slider等)
  min_value: z.number().optional(),
  max_value: z.number().optional(),
  step_value: z.number().optional(),

  // 多媒体内容
  media_url: z.string().optional(),
  media_type: z.enum(['image', 'audio', 'video']).optional(),
  media_thumbnail_url: z.string().optional(),
  media_alt_text: z.string().optional(),

  // 矩阵配置
  matrix_row_id: z.string().optional(),
  matrix_column_id: z.string().optional(),

  // 内容复用 (可选)
  reference_pack_id: z.string().optional(), // 引用其他Quiz包
  reference_option_id: z.string().optional(), // 引用其他选项

  // 选项元数据
  metadata: z.string().optional(), // JSON: 其他元数据 (包含联动关系)
  tags: z.string().optional(), // JSON数组: 标签

  // 状态管理
  is_active: z.boolean().default(true),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: z.string().optional(),
  updated_by: z.string().optional(),
});

/**
 * 用户展现配置 Schema（基于 user_presentation_configs 表）
 */
export const UserPresentationConfigSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  config_name: z.string().default('default'),

  // 6层Quiz展现配置（JSON: QuizPresentationConfig）
  presentation_config: z.string(),

  // 配置元数据
  config_version: z.string().default('2.0'),
  personalization_level: z.number().int().min(0).max(100).default(0),

  // 状态管理
  is_active: z.boolean().default(true),
  is_default: z.boolean().default(false),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * 包展现覆盖 Schema（基于 pack_presentation_overrides 表）
 */
export const PackPresentationOverrideSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  pack_id: IdSchema,

  // 展现覆盖配置（JSON: PackSpecificPresentationOverride）
  presentation_overrides: z.string().optional(),

  // 层级展现覆盖（JSON: TierPresentationOverrides）
  tier_presentation_overrides: z.string().optional(),

  // 覆盖元数据
  override_reason: z.string().optional(), // 'user_preference', 'accessibility_need', 'performance_optimization'
  override_priority: z.number().int().min(1).max(10).default(1),

  // 状态管理
  is_active: z.boolean().default(true),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * Quiz会话 Schema（基于 quiz_sessions 表 - 新架构）
 */
export const QuizSessionSchema = z.object({
  id: IdSchema,
  pack_id: IdSchema,
  user_id: IdSchema,

  // 会话状态
  status: z.enum(['INITIATED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED']).default('INITIATED'),
  current_question_index: z.number().int().default(0),
  total_questions: z.number().int().optional(),

  // 时间跟踪
  start_time: TimestampSchema,
  last_active_time: TimestampSchema,
  end_time: TimestampSchema.optional(),
  total_duration_seconds: z.number().int().optional(),

  // 进度跟踪
  answered_questions: z.number().int().default(0),
  skipped_questions: z.number().int().default(0),
  completion_percentage: z.number().min(0).max(100).default(0),

  // 会话元数据
  session_type: z.string().default('standard'), // 'standard', 'practice', 'assessment'
  session_metadata: z.string().optional(), // JSON: 会话元数据

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * Quiz会话展现配置 Schema（基于 quiz_session_presentation_configs 表 - 新架构）
 */
export const QuizSessionPresentationConfigSchema = z.object({
  id: IdSchema,
  session_id: IdSchema,
  user_id: IdSchema,

  // 配置基本信息
  config_name: z.string().default('session_snapshot'),
  config_version: z.string().default('2.0'),

  // 展现配置 (JSON)
  presentation_config: z.string(), // JSON: 完整的展现配置

  // 个性化级别
  personalization_level: z.number().int().min(0).max(100).default(50),

  // 配置来源
  config_source: z.enum(['user_preference', 'system_default', 'ai_generated', 'admin_override']).default('user_preference'),

  // 状态管理
  is_active: z.boolean().default(true),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * Quiz答案记录 Schema（基于 quiz_answers_clean 表 - 新架构）
 */
export const QuizAnswerSchema = z.object({
  id: IdSchema,
  session_id: IdSchema,
  question_id: IdSchema,
  session_presentation_config_id: OptionalIdSchema,

  // 答案内容
  selected_option_ids: z.string().optional(), // JSON array: 选中的选项ID列表
  answer_value: z.string(), // 答案值
  answer_text: z.string().optional(), // 文本答案 (用于开放式问题)

  // 答案元数据
  confidence_score: z.number().min(0).max(100).optional(),
  response_time_ms: z.number().int().optional(),

  // 时间戳
  answered_at: TimestampSchema,
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * 量表结果 Schema（基于 quiz_results 表）
 */
export const QuizResultSchema = z.object({
  id: IdSchema,
  session_id: IdSchema,
  user_id: IdSchema,
  pack_id: IdSchema,

  // 结果状态
  status: z.enum(['COMPLETED', 'PARTIALLY_COMPLETED', 'TIMEOUT']).default('COMPLETED'),
  completion_percentage: z.number().min(0).max(100),

  // 基础统计
  total_duration_seconds: z.number().int().optional(),
  average_response_time_ms: z.number().int().optional(),
  total_questions: z.number().int().optional(),
  answered_questions: z.number().int().optional(),
  skipped_questions: z.number().int().optional(),

  // 结果数据 (根据Quiz类型而定)
  result_data: z.string().optional(), // JSON: 结果数据 (情绪分析、中医评估等)

  // 评分和标签
  overall_score: z.number().optional(),
  emotional_stability_score: z.number().optional(),
  emotional_complexity_score: z.number().optional(),
  generated_tags: z.string().optional(), // JSON array

  // 建议和摘要
  recommendations: z.string().optional(), // JSON
  narrative_summary: z.string().optional(),
  visual_summary_config: z.string().optional(), // JSON

  // AI分析状态
  ai_analysis_status: z.enum(['NOT_REQUESTED', 'PENDING', 'COMPLETED', 'FAILED']).default('NOT_REQUESTED'),
  ai_analysis_id: OptionalIdSchema,
  ai_analysis_version: z.string().optional(),

  // 结果元数据
  result_version: z.string().default('2.0'),
  analysis_algorithm_version: z.string().optional(),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * 情绪模式分析 Schema（基于 emotion_pattern_analyses 表）
 */
export const EmotionPatternAnalysisSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  result_id: IdSchema,

  // 分析类型
  analysis_type: z.enum(['single_session', 'trend_analysis', 'comparative']),
  analysis_period_start: TimestampSchema.optional(),
  analysis_period_end: TimestampSchema.optional(),

  // 模式数据 (JSON)
  dominant_emotions: z.string().optional(), // JSON
  emotion_transitions: z.string().optional(), // JSON
  intensity_patterns: z.string().optional(), // JSON
  temporal_patterns: z.string().optional(), // JSON
  category_distribution: z.string().optional(), // JSON

  // 统计指标
  emotional_stability_index: z.number().optional(),
  emotional_complexity_index: z.number().optional(),
  emotional_variability_index: z.number().optional(),
  pattern_consistency_score: z.number().optional(),

  // 趋势分析
  trend_direction: z.enum(['improving', 'declining', 'stable', 'fluctuating']).optional(),
  trend_confidence: z.number().min(0).max(1).optional(),

  // 分析元数据
  analysis_algorithm_version: z.string().optional(),
  analysis_confidence: z.number().optional(),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * 推荐建议 Schema（基于 recommendations 表）
 */
export const RecommendationSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  result_id: OptionalIdSchema,
  analysis_id: OptionalIdSchema,

  // 推荐类型
  recommendation_type: z.enum(['therapy_technique', 'lifestyle_change', 'quiz_suggestion', 'config_optimization']),
  category: z.enum(['immediate', 'short_term', 'long_term']).optional(),
  priority_level: z.number().int().min(1).max(5).optional(),

  // 推荐内容
  title: z.string(),
  description: z.string().optional(),
  detailed_explanation: z.string().optional(),
  action_steps: z.string().optional(), // JSON array

  // 推荐依据
  reasoning: z.string().optional(),
  confidence_score: z.number().min(0).max(1).optional(),
  evidence_data: z.string().optional(), // JSON

  // 资源链接
  resources: z.string().optional(), // JSON
  external_links: z.string().optional(), // JSON

  // 用户反馈
  user_rating: z.number().int().min(1).max(5).optional(),
  user_feedback: z.string().optional(),
  is_helpful: z.boolean().optional(),
  is_implemented: z.boolean().default(false),
  implementation_date: TimestampSchema.optional(),

  // 推荐元数据
  recommendation_algorithm_version: z.string().optional(),
  personalization_level: z.number().int().min(0).max(100).optional(),

  // 状态管理
  is_active: z.boolean().default(true),
  expires_at: TimestampSchema.optional(),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

// ==================== Quiz基础组件相关 Schema ====================

/**
 * Quiz组件类型枚举
 */
export const QuizComponentTypeSchema = z.enum([
  // 基础文本和按钮组件
  'text_component',
  'button_component',

  // 选择器组件
  'selector_component',
  'dropdown_component',

  // 输入组件
  'slider_component',
  'rating_component',
  'text_input_component',

  // 媒体组件
  'image_component',
  'image_selector_component',
  'audio_player_component',
  'video_player_component',

  // 交互组件
  'draggable_list_component',
  'progress_indicator_component',

  // 角色和对话组件
  'npc_character_component',
  'dialogue_component'
]);

/**
 * 交互事件类型
 */
export const InteractionTypeSchema = z.enum([
  'select',
  'hover',
  'focus',
  'drag',
  'swipe',
  'long_press',
  'click',
  'change'
]);

/**
 * 动画效果类型
 */
export const AnimationEffectSchema = z.enum([
  'fade_in',
  'fade_out',
  'slide_in',
  'slide_out',
  'scale_in',
  'scale_out',
  'typewriter',
  'brush_stroke',
  'bounce',
  'pulse',
  'shake',
  'glow',
  'ripple'
]);

/**
 * 基础组件配置接口
 */
export const BaseComponentConfigSchema = z.object({
  id: IdSchema,
  component_type: QuizComponentTypeSchema,
  layout_id: z.string(),
  style: z.record(z.any()),
  accessibility: z.object({
    keyboard_navigation: z.boolean().default(true),
    screen_reader_support: z.boolean().default(false),
    high_contrast_mode: z.boolean().default(false),
    large_text: z.boolean().default(false),
  }).optional(),
  animation: z.object({
    effect: AnimationEffectSchema.optional(),
    duration: z.number().optional(),
    easing: z.string().optional(),
  }).optional(),
});

/**
 * 交互事件 Schema
 */
export const InteractionEventSchema = z.object({
  type: InteractionTypeSchema,
  target: z.string(),
  data: z.any(),
  timestamp: z.number(),
  metadata: z.record(z.any()).optional(),
});

/**
 * 组件状态 Schema
 */
export const ComponentStateSchema = z.object({
  is_loading: z.boolean().default(false),
  is_interactive: z.boolean().default(true),
  is_disabled: z.boolean().default(false),
  selected_items: z.array(z.string()).default([]),
  animation_state: z.enum(['idle', 'running', 'paused', 'completed']).default('idle'),
  error_state: z.object({
    has_error: z.boolean(),
    error_message: z.string().optional(),
    recoverable: z.boolean().default(true),
  }).optional(),
});

/**
 * 文本组件配置 Schema
 */
export const TextComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('text_component'),
  content: z.object({
    text_localized: z.record(z.string()),
    emphasis_words: z.array(z.string()).optional(),
    animation_effect: AnimationEffectSchema.optional(),
    scroll_direction: z.enum(['horizontal', 'vertical']).optional(),
    inscription_style: z.enum(['carved', 'painted', 'gold_inlay']).optional(),
  }),
  style: z.object({
    font_family: z.enum(['modern', 'traditional', 'calligraphy', 'seal_script', 'clerical_script']).default('modern'),
    size: z.enum(['tiny', 'small', 'medium', 'large', 'title', 'display']).default('medium'),
    color_scheme: z.string().default('#333333'),
    alignment: z.enum(['left', 'center', 'right', 'justify']).default('left'),
    line_height: z.number().default(1.5),
    letter_spacing: z.number().default(0),
    text_shadow: z.string().optional(),
    background_pattern: z.enum(['none', 'bamboo', 'cloud', 'wave', 'mountain']).optional(),
  }),
});

/**
 * 按钮组件配置 Schema
 */
export const ButtonComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('button_component'),
  content: z.object({
    text_localized: z.record(z.string()),
    icon_name: z.string().optional(),
    loading_state: z.boolean().default(false),
  }),
  style: z.object({
    size: z.enum(['small', 'medium', 'large']).default('medium'),
    variant: z.enum(['primary', 'secondary', 'outline', 'ghost']).default('primary'),
    shape: z.enum(['rectangle', 'rounded', 'pill', 'custom']).default('rounded'),
    icon_position: z.enum(['left', 'right', 'only', 'none']).default('none'),
    hover_effect: z.enum(['scale', 'glow', 'shadow', 'ripple']).default('scale'),
  }),
  feedback: z.object({
    haptic_feedback: z.boolean().default(true),
    sound_effect: z.string().optional(),
    animation: AnimationEffectSchema.default('bounce'),
  }),
});

/**
 * 选择器组件配置 Schema
 */
export const SelectorComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('selector_component'),
  options: z.array(z.object({
    id: z.string(),
    value: z.union([z.string(), z.number()]),
    text_localized: z.record(z.string()),
    display_style: z.enum(['text_only', 'icon_text', 'card_style', 'wheel_sector']).default('text_only'),
    icon_name: z.string().optional(),
    disabled: z.boolean().default(false),
  })),
  style: z.object({
    selection_mode: z.enum(['single', 'multiple']).default('single'),
    marker_style: z.enum(['circle', 'square', 'chinese_marker']).default('circle'),
    spacing: z.number().default(16),
    hover_effect: z.boolean().default(true),
    animation_style: AnimationEffectSchema.default('scale_in'),
  }),
  validation: z.object({
    required: z.boolean().default(false),
    min_selections: z.number().optional(),
    max_selections: z.number().optional(),
  }),
});

/**
 * 下拉选择器组件配置 Schema
 */
export const DropdownComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('dropdown_component'),
  options: z.array(z.object({
    id: z.string(),
    value: z.union([z.string(), z.number()]),
    text_localized: z.record(z.string()),
    icon_name: z.string().optional(),
    disabled: z.boolean().default(false),
  })),
  style: z.object({
    arrow_style: z.enum(['chevron', 'triangle', 'chinese_arrow']).default('chevron'),
    menu_style: z.enum(['modern', 'traditional', 'floating']).default('modern'),
    max_height: z.number().default(200),
    placeholder_text: z.record(z.string()).optional(),
  }),
  validation: z.object({
    required: z.boolean().default(false),
  }),
});

/**
 * 滑块组件配置 Schema
 */
export const SliderComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('slider_component'),
  range: z.object({
    min: z.number(),
    max: z.number(),
    step: z.number().default(1),
    default_value: z.number(),
  }),
  style: z.object({
    orientation: z.enum(['horizontal', 'vertical']).default('horizontal'),
    track_style: z.enum(['line', 'groove', 'bamboo', 'ink_brush', 'dragon_spine', 'mountain_ridge', 'river_flow']).default('line'),
    thumb_style: z.enum(['circle', 'square', 'panda_paw', 'jade_bead', 'lotus_petal', 'yin_yang', 'coin', 'pearl']).default('circle'),
    show_value: z.boolean().default(true),
    show_labels: z.boolean().default(true),
    show_ticks: z.boolean().default(false),
    tick_style: z.enum(['dots', 'lines', 'bamboo_nodes', 'lotus_buds']).optional(),
    color_scheme: z.string().default('#4CAF50'),
    gradient_effect: z.boolean().default(false),
    glow_effect: z.boolean().default(false),
  }),
  labels: z.object({
    start_label: z.record(z.string()).optional(),
    end_label: z.record(z.string()).optional(),
    unit: z.string().optional(),
  }),
});

/**
 * 评分组件配置 Schema
 */
export const RatingComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('rating_component'),
  scale: z.object({
    min_value: z.number().default(1),
    max_value: z.number().default(5),
    step: z.number().default(1),
    default_value: z.number().optional(),
  }),
  style: z.object({
    marker_type: z.enum(['stars', 'dots', 'lotus', 'gourd', 'taiji', 'hearts', 'diamonds']).default('stars'),
    size: z.enum(['small', 'medium', 'large']).default('medium'),
    spacing: z.number().default(8),
    hover_effect: z.enum(['scale', 'glow', 'color_change', 'bounce']).default('scale'),
    fill_animation: z.enum(['instant', 'progressive', 'wave', 'bloom']).default('instant'),
    orientation: z.enum(['horizontal', 'vertical']).default('horizontal'),
    allow_half: z.boolean().default(false),
    show_value: z.boolean().default(false),
  }),
  labels: z.object({
    start_label: z.record(z.string()).optional(),
    end_label: z.record(z.string()).optional(),
    scale_labels: z.array(z.record(z.string())).optional(),
    value_suffix: z.string().optional(),
  }),
});

/**
 * 图片组件配置 Schema
 */
export const ImageComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('image_component'),
  content: z.object({
    image_url: z.string(),
    alt_text: z.record(z.string()),
    caption: z.record(z.string()).optional(),
  }),
  style: z.object({
    size: z.object({
      width: z.number().optional(),
      height: z.number().optional(),
      aspect_ratio: z.string().optional(),
    }),
    border_radius: z.number().default(8),
    border_style: z.enum(['none', 'simple', 'ink_wash_frame', 'traditional']).default('none'),
    shadow_effect: z.boolean().default(false),
    hover_effect: z.enum(['none', 'zoom', 'brightness', 'shadow']).default('none'),
  }),
});

/**
 * 图片选择器组件配置 Schema
 */
export const ImageSelectorComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('image_selector_component'),
  images: z.array(z.object({
    id: z.string(),
    url: z.string(),
    alt_text: z.record(z.string()),
    title: z.record(z.string()).optional(),
    description: z.record(z.string()).optional(),
    value: z.union([z.string(), z.number()]),
  })),
  style: z.object({
    columns: z.number().default(3),
    aspect_ratio: z.string().default('1:1'),
    gap: z.number().default(16),
    selection_indicator: z.enum(['border', 'overlay', 'checkmark', 'glow']).default('border'),
    hover_effect: z.enum(['zoom', 'shadow', 'brightness', 'lift']).default('zoom'),
    border_radius: z.number().default(8),
  }),
  selection: z.object({
    mode: z.enum(['single', 'multiple']).default('single'),
    max_selections: z.number().optional(),
  }),
});

/**
 * 文本输入组件配置 Schema
 */
export const TextInputComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('text_input_component'),
  input: z.object({
    type: z.enum(['text', 'textarea', 'number', 'email', 'password']).default('text'),
    placeholder: z.record(z.string()).optional(),
    max_length: z.number().optional(),
    min_length: z.number().optional(),
    required: z.boolean().default(false),
    pattern: z.string().optional(),
  }),
  style: z.object({
    size: z.enum(['small', 'medium', 'large']).default('medium'),
    border_style: z.enum(['modern', 'traditional', 'ink_brush', 'bamboo']).default('modern'),
    label_position: z.enum(['top', 'left', 'inside', 'floating']).default('top'),
    show_counter: z.boolean().default(false),
    resize: z.enum(['none', 'vertical', 'horizontal', 'both']).default('none'),
  }),
  validation: z.object({
    real_time: z.boolean().default(true),
    show_errors: z.boolean().default(true),
    custom_messages: z.record(z.string()).optional(),
  }),
});

/**
 * 音频播放器组件配置 Schema
 */
export const AudioPlayerComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('audio_player_component'),
  audio: z.object({
    url: z.string(),
    title: z.record(z.string()).optional(),
    artist: z.record(z.string()).optional(),
    duration: z.number().optional(),
    autoplay: z.boolean().default(false),
    loop: z.boolean().default(false),
    preload: z.enum(['none', 'metadata', 'auto']).default('metadata'),
  }),
  style: z.object({
    player_style: z.enum(['minimal', 'full', 'traditional', 'guqin']).default('minimal'),
    size: z.enum(['small', 'medium', 'large']).default('medium'),
    show_progress: z.boolean().default(true),
    show_time: z.boolean().default(true),
    show_volume: z.boolean().default(true),
    color_scheme: z.string().default('#4CAF50'),
  }),
  controls: z.object({
    play_pause: z.boolean().default(true),
    seek: z.boolean().default(true),
    volume: z.boolean().default(true),
    speed: z.boolean().default(false),
    download: z.boolean().default(false),
  }),
});

/**
 * 视频播放器组件配置 Schema
 */
export const VideoPlayerComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('video_player_component'),
  video: z.object({
    url: z.string(),
    poster_url: z.string().optional(),
    title: z.record(z.string()).optional(),
    description: z.record(z.string()).optional(),
    duration: z.number().optional(),
    autoplay: z.boolean().default(false),
    loop: z.boolean().default(false),
    muted: z.boolean().default(false),
    preload: z.enum(['none', 'metadata', 'auto']).default('metadata'),
  }),
  style: z.object({
    player_style: z.enum(['modern', 'traditional', 'minimal', 'theater']).default('modern'),
    aspect_ratio: z.string().default('16:9'),
    border_radius: z.number().default(8),
    show_controls: z.boolean().default(true),
    controls_timeout: z.number().default(3000),
  }),
  controls: z.object({
    play_pause: z.boolean().default(true),
    seek: z.boolean().default(true),
    volume: z.boolean().default(true),
    fullscreen: z.boolean().default(true),
    speed: z.boolean().default(false),
    quality: z.boolean().default(false),
    subtitles: z.boolean().default(false),
  }),
});

/**
 * 拖拽列表组件配置 Schema
 */
export const DraggableListComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('draggable_list_component'),
  items: z.array(z.object({
    id: z.string(),
    content: z.record(z.string()),
    value: z.union([z.string(), z.number()]),
    icon: z.string().optional(),
    disabled: z.boolean().default(false),
    locked: z.boolean().default(false),
  })),
  style: z.object({
    orientation: z.enum(['vertical', 'horizontal']).default('vertical'),
    item_style: z.enum(['card', 'list', 'tile', 'traditional']).default('card'),
    drag_handle: z.enum(['icon', 'whole_item', 'left_edge']).default('icon'),
    spacing: z.number().default(8),
    animation_duration: z.number().default(300),
  }),
  behavior: z.object({
    sortable: z.boolean().default(true),
    removable: z.boolean().default(false),
    addable: z.boolean().default(false),
    multi_select: z.boolean().default(false),
    auto_scroll: z.boolean().default(true),
  }),
});

/**
 * 进度指示器组件配置 Schema
 */
export const ProgressIndicatorComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('progress_indicator_component'),
  progress: z.object({
    current: z.number().min(0),
    total: z.number().min(1),
    show_percentage: z.boolean().default(true),
    show_fraction: z.boolean().default(false),
    animated: z.boolean().default(true),
  }),
  style: z.object({
    type: z.enum(['bar', 'circle', 'lotus', 'bamboo', 'steps']).default('bar'),
    size: z.enum(['small', 'medium', 'large']).default('medium'),
    color_scheme: z.string().default('#4CAF50'),
    background_color: z.string().default('#E0E0E0'),
    border_radius: z.number().default(4),
    thickness: z.number().default(8),
  }),
  labels: z.object({
    show_label: z.boolean().default(true),
    label_text: z.record(z.string()).optional(),
    label_position: z.enum(['top', 'bottom', 'center', 'inside']).default('top'),
  }),
});

/**
 * NPC角色组件配置 Schema
 */
export const NPCCharacterComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('npc_character_component'),
  character: z.object({
    name: z.record(z.string()),
    avatar_url: z.string(),
    role: z.enum(['guide', 'doctor', 'sage', 'companion']).default('guide'),
    personality: z.enum(['wise', 'gentle', 'energetic', 'calm']).default('wise'),
  }),
  dialogue: z.object({
    current_text: z.record(z.string()),
    typing_speed: z.number().default(50),
    auto_advance: z.boolean().default(false),
    show_continue_button: z.boolean().default(true),
  }),
  style: z.object({
    avatar_size: z.enum(['small', 'medium', 'large']).default('medium'),
    avatar_position: z.enum(['left', 'right', 'top', 'center']).default('left'),
    bubble_style: z.enum(['modern', 'traditional', 'scroll', 'cloud']).default('traditional'),
    animation_style: z.enum(['fade', 'slide', 'bounce', 'typewriter']).default('typewriter'),
  }),
  behavior: z.object({
    interactive: z.boolean().default(true),
    expressions: z.boolean().default(true),
    voice_enabled: z.boolean().default(false),
    gesture_hints: z.boolean().default(true),
  }),
});

/**
 * 对话组件配置 Schema
 */
export const DialogueComponentConfigSchema = BaseComponentConfigSchema.extend({
  component_type: z.literal('dialogue_component'),
  conversation: z.object({
    messages: z.array(z.object({
      id: z.string(),
      speaker: z.enum(['user', 'npc', 'system']),
      content: z.record(z.string()),
      timestamp: z.number().optional(),
      options: z.array(z.object({
        id: z.string(),
        text: z.record(z.string()),
        value: z.union([z.string(), z.number()]),
        leads_to: z.string().optional(),
      })).optional(),
    })),
    current_message_index: z.number().default(0),
  }),
  style: z.object({
    layout: z.enum(['chat', 'story', 'interview', 'consultation']).default('chat'),
    bubble_style: z.enum(['modern', 'traditional', 'minimal']).default('modern'),
    spacing: z.number().default(16),
    max_width: z.number().default(600),
  }),
  behavior: z.object({
    auto_scroll: z.boolean().default(true),
    typing_indicator: z.boolean().default(true),
    message_history: z.boolean().default(true),
    branching_enabled: z.boolean().default(false),
  }),
});

// ==================== 工具函数 ====================

/**
 * 验证并转换数据
 */
export function validateAndTransform<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; error: string } {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map((e) => `${e.path.join('.')}: ${e.message}`).join(', '),
      };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown validation error',
    };
  }
}

// ==================== VIP 和 Unlock 系统 Schema ====================

/**
 * 用户皮肤解锁 Schema（基于 user_skin_unlocks 表）
 */
export const UserSkinUnlockSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  skin_id: IdSchema,
  unlock_method: UnlockMethodSchema,
  unlocked_at: TimestampSchema,
  expires_at: TimestampSchema.optional(), // 临时解锁的过期时间
  transaction_id: z.string().optional(), // 支付交易ID
  promotion_code: z.string().optional(), // 促销代码

  // 同步字段
  sync_status: SyncStatusSchema.default('synced'),
  server_updated_at: TimestampSchema,

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * 用户表情集解锁 Schema（基于 user_emoji_set_unlocks 表）
 */
export const UserEmojiSetUnlockSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  emoji_set_id: IdSchema,
  unlock_method: UnlockMethodSchema,
  unlocked_at: TimestampSchema,
  expires_at: TimestampSchema.optional(), // 临时解锁的过期时间
  transaction_id: z.string().optional(), // 支付交易ID
  promotion_code: z.string().optional(), // 促销代码

  // 同步字段
  sync_status: SyncStatusSchema.default('synced'),
  server_updated_at: TimestampSchema,

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * 用户订阅历史 Schema（基于 user_subscription_history 表）
 */
export const UserSubscriptionHistorySchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  subscription_type: z.string(), // 'vip_basic', 'vip_premium', 'vip_enterprise'
  status: SubscriptionStatusSchema,

  // 订阅时间
  started_at: TimestampSchema,
  expires_at: TimestampSchema.optional(),
  cancelled_at: TimestampSchema.optional(),
  auto_renew: z.boolean().default(false),

  // 支付信息
  payment_method: PaymentMethodSchema.optional(),
  transaction_id: z.string().optional(),
  amount: z.number().optional(),
  currency: CurrencySchema.default('USD'),

  // 账单信息
  billing_cycle: BillingCycleSchema,
  next_billing_date: TimestampSchema.optional(),

  // 元数据
  subscription_source: SubscriptionSourceSchema.optional(),
  promotion_code: z.string().optional(),
  notes: z.string().optional(),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * VIP 计划 Schema（基于 vip_plans 表）
 */
export const VipPlanSchema = z.object({
  id: IdSchema,
  name: z.string().min(1),
  description: z.string().optional(),
  tier: VipTierSchema,

  // 定价
  price: z.number().min(0),
  currency: CurrencySchema.default('USD'),
  billing_cycle: BillingCycleSchema,

  // 功能（JSON 数组）
  features: z.string(), // JSON: ['unlimited_skins', 'premium_emoji_sets', 'advanced_analytics']

  // 限制和配额
  skin_unlock_limit: z.number().int().optional(), // NULL 表示无限制
  emoji_set_unlock_limit: z.number().int().optional(), // NULL 表示无限制
  storage_limit_mb: z.number().int().optional(), // NULL 表示无限制

  // 计划状态
  is_active: z.boolean().default(true),
  is_featured: z.boolean().default(false),
  sort_order: z.number().int().default(0),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

/**
 * VIP 功能 Schema（基于 vip_features 表）
 */
export const VipFeatureSchema = z.object({
  id: IdSchema,
  name: z.string().min(1),
  description: z.string().optional(),
  feature_type: VipFeatureTypeSchema,

  // 功能配置（JSON）
  config: z.string().optional(), // JSON: 功能特定配置

  // 状态
  is_active: z.boolean().default(true),

  // 审计字段
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
});

// ==================== Quiz 系统类型导出 ====================

export type QuizPack = z.infer<typeof QuizPackSchema>;
export type QuizQuestion = z.infer<typeof QuizQuestionSchema>;
export type QuizQuestionOption = z.infer<typeof QuizQuestionOptionSchema>;
export type QuizSession = z.infer<typeof QuizSessionSchema>;
export type QuizSessionPresentationConfig = z.infer<typeof QuizSessionPresentationConfigSchema>;
export type QuizAnswer = z.infer<typeof QuizAnswerSchema>;
export type QuizResult = z.infer<typeof QuizResultSchema>;

// ==================== VIP 和 Unlock 系统类型导出 ====================

export type UserSkinUnlock = z.infer<typeof UserSkinUnlockSchema>;
export type UserEmojiSetUnlock = z.infer<typeof UserEmojiSetUnlockSchema>;
export type UserSubscriptionHistory = z.infer<typeof UserSubscriptionHistorySchema>;
export type VipPlan = z.infer<typeof VipPlanSchema>;
export type VipFeature = z.infer<typeof VipFeatureSchema>;

// VIP 相关类型
export type VipTier = z.infer<typeof VipTierSchema>;
export type SubscriptionStatus = z.infer<typeof SubscriptionStatusSchema>;
export type BillingCycle = z.infer<typeof BillingCycleSchema>;
export type PaymentMethod = z.infer<typeof PaymentMethodSchema>;
export type SubscriptionSource = z.infer<typeof SubscriptionSourceSchema>;
export type UnlockMethod = z.infer<typeof UnlockMethodSchema>;
export type VipFeatureType = z.infer<typeof VipFeatureTypeSchema>;
export type TranslatorType = z.infer<typeof TranslatorTypeSchema>;
export type TagSource = z.infer<typeof TagSourceSchema>;
export type TagCategory = z.infer<typeof TagCategorySchema>;