# 同步架构重构方案

## 当前问题分析

### 1. SyncContext.tsx ❌
- **问题**: 复杂的本地队列管理，模拟同步逻辑，与服务端功能重复
- **建议**: 移除，功能由简化的useDataSync替代

### 2. useDataSync.ts ⚠️
- **问题**: 模拟同步逻辑，未调用实际tRPC端点
- **建议**: 重构为直接调用tRPC同步端点

### 3. server/lib/services/SyncService.ts ✅
- **优势**: 完整服务端实现，已集成tRPC，支持双向同步
- **建议**: 保留并充分利用

## 重构方案

### 方案1: 简化架构 (推荐)

#### 1. 移除 SyncContext.tsx
```bash
# 删除文件
rm src/contexts/SyncContext.tsx

# 更新引用该Context的组件
# - 移除 import { useSync } from '@/contexts/SyncContext'
# - 替换为 import { useDataSync } from '@/hooks/useDataSync'
```

#### 2. 重构 useDataSync.ts
```typescript
import { useState, useEffect, useCallback } from 'react';
import { useNetwork } from '@/contexts/NetworkContext';
import { trpc } from '@/lib/trpc';
import { Services } from '@/services';

export interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  syncProgress: number;
  error: string | null;
  pendingChanges: number;
}

export const useDataSync = () => {
  const { isOnline, isInternetReachable } = useNetwork();
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: false,
    isSyncing: false,
    lastSyncTime: null,
    syncProgress: 0,
    error: null,
    pendingChanges: 0
  });

  // 执行数据同步 - 直接调用tRPC
  const performSync = useCallback(async () => {
    if (!isOnline || !isInternetReachable) {
      return { success: false, error: 'No internet connection' };
    }

    setSyncStatus(prev => ({ ...prev, isSyncing: true, error: null }));

    try {
      // 1. 获取本地待同步数据
      const localData = await getLocalPendingData();
      
      // 2. 调用tRPC同步端点
      const result = await trpc.synchronizeData.mutate({
        moodEntriesToUpload: localData.moodEntries,
        emotionSelectionsToUpload: localData.emotionSelections,
        lastSyncTimestamp: getLastSyncTimestamp()
      });

      if (result.success) {
        // 3. 处理服务端返回的数据
        await processServerData(result);
        
        setSyncStatus(prev => ({
          ...prev,
          isSyncing: false,
          lastSyncTime: new Date().toISOString(),
          syncProgress: 100,
          pendingChanges: 0
        }));
        
        return { success: true };
      } else {
        throw new Error(result.error || 'Sync failed');
      }
    } catch (error) {
      setSyncStatus(prev => ({
        ...prev,
        isSyncing: false,
        error: error instanceof Error ? error.message : 'Sync failed'
      }));
      
      return { success: false, error: error instanceof Error ? error.message : 'Sync failed' };
    }
  }, [isOnline, isInternetReachable]);

  // 获取本地待同步数据
  const getLocalPendingData = async () => {
    const moodEntryService = await Services.moodEntry();
    const emotionSelectionService = await Services.emotionSelection();
    
    const moodEntriesResult = await moodEntryService.getUnsynced('current-user');
    const emotionSelectionsResult = await emotionSelectionService.getUnsynced('current-user');
    
    return {
      moodEntries: moodEntriesResult.success ? moodEntriesResult.data : [],
      emotionSelections: emotionSelectionsResult.success ? emotionSelectionsResult.data : []
    };
  };

  // 处理服务端返回的数据
  const processServerData = async (syncResult: any) => {
    // 保存服务端的新数据到本地
    if (syncResult.newMoodEntriesFromServer?.length > 0) {
      const moodEntryService = await Services.moodEntry();
      for (const entry of syncResult.newMoodEntriesFromServer) {
        await moodEntryService.upsert(entry);
      }
    }
    
    if (syncResult.newEmotionSelectionsFromServer?.length > 0) {
      const emotionSelectionService = await Services.emotionSelection();
      for (const selection of syncResult.newEmotionSelectionsFromServer) {
        await emotionSelectionService.upsert(selection);
      }
    }
    
    // 更新最后同步时间
    localStorage.setItem('lastSyncTimestamp', syncResult.serverTimestamp);
  };

  const getLastSyncTimestamp = () => {
    return localStorage.getItem('lastSyncTimestamp') || undefined;
  };

  return {
    syncStatus,
    performSync,
    isOnline: syncStatus.isOnline,
    isSyncing: syncStatus.isSyncing,
    lastSyncTime: syncStatus.lastSyncTime,
    syncProgress: syncStatus.syncProgress,
    syncError: syncStatus.error,
    pendingChanges: syncStatus.pendingChanges
  };
};
```

#### 3. 服务端同步端点使用
服务端已有完整的同步实现：

**可用的tRPC端点**:
- `synchronizeData` - 基础数据同步
- `performFullSync` - 完整数据同步 (需认证)

**使用示例**:
```typescript
// 基础同步
const result = await trpc.synchronizeData.mutate({
  moodEntriesToUpload: localEntries,
  emotionSelectionsToUpload: localSelections,
  lastSyncTimestamp: lastSync
});

// 完整同步 (需要用户认证)
const fullSyncResult = await trpc.performFullSync.mutate({
  userId: 'user123',
  lastSyncTimestamp: lastSync,
  moodEntriesToUpload: localEntries,
  emotionSelectionsToUpload: localSelections,
  userConfigsToUpload: localConfigs,
  tagsToUpload: localTags
});
```

### 方案2: 保留Context但简化 (备选)

如果需要保留Context模式，可以简化SyncContext.tsx：

```typescript
// 简化版SyncContext.tsx
export const SyncProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { performSync, syncStatus } = useDataSync(); // 使用重构后的hook
  
  return (
    <SyncContext.Provider value={{ performSync, syncStatus }}>
      {children}
    </SyncContext.Provider>
  );
};
```

## 实施步骤

### 第1步: 重构useDataSync.ts
1. 移除模拟逻辑
2. 添加tRPC调用
3. 实现本地数据获取和处理

### 第2步: 更新服务层
1. 确保MoodEntryService有getUnsynced方法
2. 确保EmotionSelectionService有getUnsynced方法
3. 添加upsert方法处理服务端数据

### 第3步: 移除SyncContext (可选)
1. 删除SyncContext.tsx文件
2. 更新所有引用该Context的组件
3. 替换为useDataSync hook

### 第4步: 测试
1. 测试离线数据创建
2. 测试在线同步功能
3. 测试网络状态变化时的行为

## 优势

### 简化后的架构优势:
1. **减少复杂性**: 移除重复的队列管理逻辑
2. **直接通信**: 直接使用tRPC与服务端通信
3. **类型安全**: 利用tRPC的端到端类型安全
4. **易于维护**: 更少的抽象层，更清晰的数据流
5. **性能更好**: 减少不必要的状态管理开销

### 与服务端集成优势:
1. **完整功能**: 利用服务端的完整同步实现
2. **冲突处理**: 服务端已实现冲突检测机制
3. **批量操作**: 支持批量上传和下载
4. **增量同步**: 支持基于时间戳的增量同步

## 总结

**推荐使用方案1 (简化架构)**:
- 移除复杂的SyncContext.tsx
- 重构useDataSync.ts为直接调用tRPC
- 充分利用服务端的SyncService.ts实现

这样可以大大简化客户端的同步逻辑，同时保持完整的同步功能。
