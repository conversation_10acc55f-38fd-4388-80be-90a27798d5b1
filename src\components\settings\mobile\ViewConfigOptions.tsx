import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Settings, Palette, Sliders, Layers, Wand2 } from 'lucide-react';
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from '@/components/ui/collapsible';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';

interface ViewConfigOptionsProps {
  userType: 'beginner' | 'regular' | 'advanced' | 'vip' | 'accessibility';
}

const ViewConfigOptions: React.FC<ViewConfigOptionsProps> = ({ userType }) => {
  const { t } = useLanguage();
  const { userConfig, updateUserConfig } = useUserConfig();
  
  // Collapsible 状态管理
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    geometry: false,
    visual: false,
    interaction: false,
    responsive: false
  });

  // 获取当前视图类型
  const currentViewType = userConfig?.preferred_view_type || 'wheel';

  // 切换 collapsible 状态
  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 处理配置更新
  const handleConfigUpdate = async (configPath: string, value: any) => {
    try {
      // 这里应该更新具体的视图配置
      console.log('Updating config:', configPath, value);
      // 实际实现需要根据具体的配置结构来更新
    } catch (error) {
      console.error('Failed to update config:', error);
    }
  };

  // 根据用户类型决定显示哪些配置选项
  const getAvailableSections = () => {
    switch (userType) {
      case 'beginner':
        return ['geometry']; // 只显示基础几何配置
      case 'regular':
        return ['geometry', 'visual']; // 几何 + 视觉效果
      case 'advanced':
        return ['geometry', 'visual', 'interaction']; // 几何 + 视觉 + 交互
      case 'vip':
        return ['geometry', 'visual', 'interaction', 'responsive']; // 全部功能
      case 'accessibility':
        return ['geometry']; // 简化配置，专注可访问性
      default:
        return ['geometry'];
    }
  };

  const availableSections = getAvailableSections();

  // 渲染几何配置
  const renderGeometryConfig = () => (
    <Collapsible 
      open={openSections.geometry} 
      onOpenChange={() => toggleSection('geometry')}
    >
      <CollapsibleTrigger asChild>
        <Button 
          variant="ghost" 
          className="w-full justify-between p-4 h-auto"
        >
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="font-medium">
              {t('view_config.geometry', '基础设置')}
            </span>
            <Badge variant="secondary" className="ml-2">
              {t('view_config.basic', '基础')}
            </Badge>
          </div>
          {openSections.geometry ? 
            <ChevronDown className="h-4 w-4" /> : 
            <ChevronRight className="h-4 w-4" />
          }
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="px-4 pb-4">
        <div className="space-y-4 pt-2">
          {currentViewType === 'wheel' && (
            <>
              {/* 轮盘大小 */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  {t('wheel_config.size', '轮盘大小')}: 中等
                </Label>
                <Slider
                  value={[400]}
                  onValueChange={(value) => handleConfigUpdate('container_size', value[0])}
                  min={300}
                  max={500}
                  step={20}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>小</span>
                  <span>大</span>
                </div>
              </div>

              {userType !== 'beginner' && (
                <>
                  <Separator />
                  {/* 扇区间隙 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      {t('wheel_config.sector_gap', '扇区间隙')}: 标准
                    </Label>
                    <Slider
                      value={[2]}
                      onValueChange={(value) => handleConfigUpdate('sector_gap', value[0])}
                      min={0}
                      max={8}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>紧密</span>
                      <span>宽松</span>
                    </div>
                  </div>
                </>
              )}
            </>
          )}

          {currentViewType === 'card' && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                {t('card_config.columns', '列数')}: 2列
              </Label>
              <Slider
                value={[2]}
                onValueChange={(value) => handleConfigUpdate('columns', value[0])}
                min={1}
                max={4}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>1列</span>
                <span>4列</span>
              </div>
            </div>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  // 渲染视觉效果配置
  const renderVisualConfig = () => (
    <Collapsible 
      open={openSections.visual} 
      onOpenChange={() => toggleSection('visual')}
    >
      <CollapsibleTrigger asChild>
        <Button 
          variant="ghost" 
          className="w-full justify-between p-4 h-auto"
        >
          <div className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <span className="font-medium">
              {t('view_config.visual_effects', '视觉效果')}
            </span>
            <Badge variant="outline" className="ml-2">
              {t('view_config.advanced', '进阶')}
            </Badge>
          </div>
          {openSections.visual ? 
            <ChevronDown className="h-4 w-4" /> : 
            <ChevronRight className="h-4 w-4" />
          }
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="px-4 pb-4">
        <div className="space-y-4 pt-2">
          {/* 阴影效果 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('view_config.shadow', '阴影效果')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('view_config.shadow_desc', '添加立体感')}
              </p>
            </div>
            <Switch
              checked={true}
              onCheckedChange={(value) => handleConfigUpdate('shadow_enabled', value)}
            />
          </div>

          <Separator />

          {/* 动画效果 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('view_config.animations', '动画效果')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('view_config.animations_desc', '流畅的过渡动画')}
              </p>
            </div>
            <Switch
              checked={true}
              onCheckedChange={(value) => handleConfigUpdate('animations_enabled', value)}
            />
          </div>

          {userType === 'vip' && (
            <>
              <Separator />
              {/* VIP专属：粒子效果 */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Wand2 className="h-3 w-3" />
                    {t('view_config.particles', '粒子效果')}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {t('view_config.particles_desc', 'VIP专属动态效果')}
                  </p>
                </div>
                <Switch
                  checked={false}
                  onCheckedChange={(value) => handleConfigUpdate('particles_enabled', value)}
                />
              </div>
            </>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  // 渲染交互配置
  const renderInteractionConfig = () => (
    <Collapsible 
      open={openSections.interaction} 
      onOpenChange={() => toggleSection('interaction')}
    >
      <CollapsibleTrigger asChild>
        <Button 
          variant="ghost" 
          className="w-full justify-between p-4 h-auto"
        >
          <div className="flex items-center gap-2">
            <Sliders className="h-4 w-4" />
            <span className="font-medium">
              {t('view_config.interaction', '交互设置')}
            </span>
            <Badge variant="secondary" className="ml-2">
              {t('view_config.behavior', '行为')}
            </Badge>
          </div>
          {openSections.interaction ? 
            <ChevronDown className="h-4 w-4" /> : 
            <ChevronRight className="h-4 w-4" />
          }
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="px-4 pb-4">
        <div className="space-y-4 pt-2">
          {/* 悬停效果 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              {t('view_config.hover_effect', '悬停效果')}
            </Label>
            <Select
              value="glow"
              onValueChange={(value) => handleConfigUpdate('hover_effect', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">{t('hover.none', '无效果')}</SelectItem>
                <SelectItem value="highlight">{t('hover.highlight', '高亮')}</SelectItem>
                <SelectItem value="glow">{t('hover.glow', '发光')}</SelectItem>
                <SelectItem value="scale">{t('hover.scale', '缩放')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* 触摸反馈 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('view_config.haptic', '触摸反馈')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('view_config.haptic_desc', '点击时的震动反馈')}
              </p>
            </div>
            <Switch
              checked={true}
              onCheckedChange={(value) => handleConfigUpdate('haptic_enabled', value)}
            />
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  return (
    <Card>
      <CardContent className="p-4 space-y-2">
        <div className="flex items-center space-x-3 mb-4">
          <Layers className="h-5 w-5 text-primary" />
          <h3 className="font-medium">
            {t('view_config.title', '视图配置')}
          </h3>
          <Badge variant="outline">
            {currentViewType === 'wheel' ? '轮盘' : 
             currentViewType === 'card' ? '卡片' : 
             currentViewType === 'bubble' ? '气泡' : '视图'}
          </Badge>
        </div>

        <div className="space-y-1">
          {availableSections.includes('geometry') && renderGeometryConfig()}
          {availableSections.includes('visual') && renderVisualConfig()}
          {availableSections.includes('interaction') && renderInteractionConfig()}
        </div>

        {userType === 'beginner' && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <p className="text-xs text-blue-700 dark:text-blue-300">
              💡 更多高级配置选项将在您熟悉应用后逐步开放
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ViewConfigOptions;
