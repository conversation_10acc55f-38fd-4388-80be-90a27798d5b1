/**
 * QuizService 单元测试
 * 验证重构后的服务端专用实现
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuizService } from '../QuizService';

// Mock 数据库操作
vi.mock('../../database/index', () => ({
  executeQuery: vi.fn(),
  batchStatements: vi.fn()
}));

// 导入 Mock 后的模块
import { executeQuery, batchStatements } from '../../database/index';

const mockExecuteQuery = vi.mocked(executeQuery);
const mockBatchStatements = vi.mocked(batchStatements);

describe('QuizService', () => {
  let quizService: QuizService;

  beforeEach(() => {
    quizService = QuizService.getInstance();
    vi.clearAllMocks();
  });

  describe('getAvailableQuizPacks', () => {
    it('should return available quiz packs for free users', async () => {
      const mockQuizPacks = [
        {
          id: 'pack1',
          name: 'Test Pack 1',
          description: 'Test Description',
          quiz_type: 'emotion_wheel',
          is_active: true
        }
      ];

      mockExecuteQuery.mockResolvedValue({
        rows: mockQuizPacks,
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await quizService.getAvailableQuizPacks('user1', 'free');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockQuizPacks);
      expect(mockExecuteQuery).toHaveBeenCalledWith({
        sql: expect.stringContaining('SELECT qp.* FROM quiz_packs qp'),
        args: ['free']
      });
    });

    it('should handle database errors gracefully', async () => {
      mockExecuteQuery.mockRejectedValue(new Error('Database error'));

      const result = await quizService.getAvailableQuizPacks('user1', 'free');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database error');
    });
  });

  describe('validateQuizSession', () => {
    it('should validate a valid quiz session', async () => {
      const sessionData = {
        id: 'session1',
        pack_id: 'pack1',
        user_id: 'user1',
        status: 'IN_PROGRESS' as const,
        current_question_index: 0,
        start_time: new Date().toISOString(),
        last_active_time: new Date().toISOString(),
        answered_questions: 0,
        skipped_questions: 0,
        completion_percentage: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Mock pack exists
      mockExecuteQuery
        .mockResolvedValueOnce({
          rows: [{ id: 'pack1' }],
          changes: 0,
          lastInsertRowid: 0
        })
        // Mock user exists
        .mockResolvedValueOnce({
          rows: [{ id: 'user1' }],
          changes: 0,
          lastInsertRowid: 0
        });

      const result = await quizService.validateQuizSession(sessionData);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
    });

    it('should reject session with non-existent pack', async () => {
      const sessionData = {
        id: 'session1',
        pack_id: 'nonexistent',
        user_id: 'user1',
        status: 'IN_PROGRESS' as const,
        current_question_index: 0,
        start_time: new Date().toISOString(),
        last_active_time: new Date().toISOString(),
        answered_questions: 0,
        skipped_questions: 0,
        completion_percentage: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Mock pack doesn't exist
      mockExecuteQuery.mockResolvedValueOnce({
        rows: [],
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await quizService.validateQuizSession(sessionData);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Quiz pack not found');
    });
  });

  describe('generateQuizStatistics', () => {
    it('should generate statistics for a quiz pack', async () => {
      const packId = 'pack1';
      
      // Mock statistics query
      mockExecuteQuery
        .mockResolvedValueOnce({
          rows: [{
            total_sessions: 10,
            completed_sessions: 8,
            avg_completion_time: 120000
          }],
          changes: 0,
          lastInsertRowid: 0
        })
        // Mock popular questions query
        .mockResolvedValueOnce({
          rows: [
            { question_id: 'q1', answer_count: 5 },
            { question_id: 'q2', answer_count: 3 }
          ],
          changes: 0,
          lastInsertRowid: 0
        });

      const result = await quizService.generateQuizStatistics(packId);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        packId,
        totalSessions: 10,
        completedSessions: 8,
        averageCompletionTime: 120000,
        popularQuestions: [
          { questionId: 'q1', answerCount: 5 },
          { questionId: 'q2', answerCount: 3 }
        ]
      });
    });
  });

  describe('persistQuizResults', () => {
    it('should persist quiz results successfully', async () => {
      const results = [
        {
          id: 'result1',
          session_id: 'session1',
          user_id: 'user1',
          pack_id: 'pack1',
          total_questions: 10,
          answered_questions: 10,
          completion_percentage: 100,
          total_time_ms: 120000,
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
          result_data: { score: 85 },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];

      mockBatchStatements.mockResolvedValue([{
        rows: [],
        changes: 1,
        lastInsertRowid: 1
      }]);

      const result = await quizService.persistQuizResults(results);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
      expect(mockBatchStatements).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            sql: expect.stringContaining('INSERT OR REPLACE INTO quiz_results'),
            args: expect.arrayContaining(['result1', 'session1', 'user1'])
          })
        ])
      );
    });
  });

  describe('detectAnomalousResults', () => {
    it('should detect suspicious timing anomalies', async () => {
      const results = [
        {
          id: 'result1',
          session_id: 'session1',
          user_id: 'user1',
          pack_id: 'pack1',
          total_questions: 10,
          answered_questions: 10,
          completion_percentage: 100,
          total_time_ms: 5000, // 5 seconds - suspicious
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
          result_data: { score: 100 },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];

      const result = await quizService.detectAnomalousResults(results);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].sessionId).toBe('session1');
      expect(result.data![0].anomalies).toHaveLength(2); // Both suspicious timing checks
      expect(result.data![0].anomalies[0].type).toBe('suspicious_timing');
      expect(result.data![0].anomalies[0].severity).toBe('high');
    });

    it('should return empty array for normal results', async () => {
      const results = [
        {
          id: 'result1',
          session_id: 'session1',
          user_id: 'user1',
          pack_id: 'pack1',
          total_questions: 10,
          answered_questions: 10,
          completion_percentage: 100,
          total_time_ms: 300000, // 5 minutes - normal
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
          result_data: { score: 85 },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];

      const result = await quizService.detectAnomalousResults(results);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(0);
    });
  });
});
