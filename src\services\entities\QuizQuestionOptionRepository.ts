/**
 * Quiz问题选项仓储 - 修复版本
 * 纯数据访问层，不包含业务逻辑
 */

import { BaseRepository } from '../base/BaseRepository';
import { QuizQuestionOption } from '../../types/schema/base';
import { CreateQuizQuestionOptionInput, UpdateQuizQuestionOptionInput } from '../../types/schema/api';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class QuizQuestionOptionRepository extends BaseRepository<
  QuizQuestionOption,
  CreateQuizQuestionOptionInput,
  UpdateQuizQuestionOptionInput
> {
  constructor(db?: SQLiteDBConnection) {
    super('quiz_question_options', db);
  }

  /**
   * 根据问题ID获取选项
   */
  async findByQuestionId(questionId: string): Promise<QuizQuestionOption[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE question_id = ? AND is_active = 1
      ORDER BY option_order ASC
    `;

    const result = await db.query(query, [questionId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据选项组获取选项
   */
  async findByOptionGroup(questionId: string, optionGroup: string): Promise<QuizQuestionOption[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE question_id = ? AND option_group = ? AND is_active = 1
      ORDER BY option_order ASC
    `;

    const result = await db.query(query, [questionId, optionGroup]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据选项值获取选项
   */
  async findByOptionValue(questionId: string, optionValue: string): Promise<QuizQuestionOption | null> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE question_id = ? AND option_value = ? AND is_active = 1
      LIMIT 1
    `;

    const result = await db.query(query, [questionId, optionValue]);
    const row = result.values?.[0];
    return row ? this.mapRowToEntity(row) : null;
  }

  /**
   * 获取正确答案选项
   */
  async findCorrectOptions(questionId: string): Promise<QuizQuestionOption[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE question_id = ? AND is_correct = 1 AND is_active = 1
      ORDER BY option_order ASC
    `;

    const result = await db.query(query, [questionId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据内容显示模式获取选项
   */
  async findByContentDisplayMode(questionId: string, displayMode: string): Promise<QuizQuestionOption[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE question_id = ? AND content_display_modes LIKE ? AND is_active = 1
      ORDER BY option_order ASC
    `;

    const displayModePattern = `%"${displayMode}"%`;
    const result = await db.query(query, [questionId, displayModePattern]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 批量插入选项
   */
  async batchInsertOptions(options: CreateQuizQuestionOptionInput[]): Promise<QuizQuestionOption[]> {
    const db = this.getDb();
    const savedOptions: QuizQuestionOption[] = [];

    try {
      await db.execute('BEGIN TRANSACTION');

      for (const optionData of options) {
        const option = await this.create(optionData);
        savedOptions.push(option);
      }

      await db.execute('COMMIT');
      return savedOptions;
    } catch (error) {
      await db.execute('ROLLBACK');
      throw error;
    }
  }

  /**
   * 批量更新选项顺序
   */
  async batchUpdateOptionOrder(updates: Array<{ id: string; option_order: number }>): Promise<boolean> {
    const db = this.getDb();

    try {
      await db.execute('BEGIN TRANSACTION');

      for (const update of updates) {
        const query = `
          UPDATE ${this.tableName}
          SET option_order = ?, updated_at = ?
          WHERE id = ?
        `;
        await db.run(query, [update.option_order, new Date().toISOString(), update.id]);
      }

      await db.execute('COMMIT');
      return true;
    } catch (error) {
      await db.execute('ROLLBACK');
      throw error;
    }
  }

  /**
   * 获取选项的最大顺序号
   */
  async getMaxOptionOrder(questionId: string): Promise<number> {
    const db = this.getDb();
    const query = `
      SELECT COALESCE(MAX(option_order), 0) as max_order
      FROM ${this.tableName}
      WHERE question_id = ?
    `;

    const result = await db.query(query, [questionId]);
    return result.values?.[0]?.max_order || 0;
  }

  /**
   * 删除问题的所有选项
   */
  async deleteByQuestionId(questionId: string): Promise<boolean> {
    const db = this.getDb();
    const query = `DELETE FROM ${this.tableName} WHERE question_id = ?`;
    const result = await db.run(query, [questionId]);
    return (result.changes?.changes ?? 0) > 0;
  }

  /**
   * 获取选项统计信息
   */
  async getOptionStats(questionId: string): Promise<any> {
    const db = this.getDb();
    const query = `
      SELECT
        COUNT(*) as total_options,
        COUNT(CASE WHEN is_correct = 1 THEN 1 END) as correct_options,
        COUNT(DISTINCT option_group) as option_groups,
        COUNT(CASE WHEN image_url IS NOT NULL THEN 1 END) as options_with_images,
        COUNT(CASE WHEN emoji_mappings IS NOT NULL THEN 1 END) as options_with_emojis,
        AVG(scoring_value) as avg_scoring_value
      FROM ${this.tableName}
      WHERE question_id = ? AND is_active = 1
    `;

    const result = await db.query(query, [questionId]);
    const stats = result.values?.[0];

    return stats || {
      total_options: 0,
      correct_options: 0,
      option_groups: 0,
      options_with_images: 0,
      options_with_emojis: 0,
      avg_scoring_value: 0
    };
  }

  protected mapRowToEntity(row: any): QuizQuestionOption {
    return {
      id: row.id,
      question_id: row.question_id,
      option_text: row.option_text,
      option_text_localized: row.option_text_localized,
      option_value: row.option_value,
      option_type: row.option_type || 'choice',
      option_order: row.option_order,
      scoring_value: row.scoring_value,
      scoring_weight: row.scoring_weight,
      min_value: row.min_value,
      max_value: row.max_value,
      step_value: row.step_value,
      media_url: row.media_url,
      media_type: row.media_type,
      media_thumbnail_url: row.media_thumbnail_url,
      media_alt_text: row.media_alt_text,
      matrix_row_id: row.matrix_row_id,
      matrix_column_id: row.matrix_column_id,
      reference_pack_id: row.reference_pack_id,
      reference_option_id: row.reference_option_id,
      metadata: row.metadata,
      tags: row.tags,
      is_active: Boolean(row.is_active),
      created_at: row.created_at,
      updated_at: row.updated_at,
      created_by: row.created_by,
      updated_by: row.updated_by
    };
  }

  protected mapEntityToRow(entity: Partial<QuizQuestionOption>): Record<string, any> {
    return {
      id: entity.id,
      question_id: entity.question_id,
      option_text: entity.option_text,
      option_text_localized: entity.option_text_localized,
      option_value: entity.option_value,
      option_type: entity.option_type,
      option_order: entity.option_order,
      scoring_value: entity.scoring_value,
      scoring_weight: entity.scoring_weight,
      min_value: entity.min_value,
      max_value: entity.max_value,
      step_value: entity.step_value,
      media_url: entity.media_url,
      media_type: entity.media_type,
      media_thumbnail_url: entity.media_thumbnail_url,
      media_alt_text: entity.media_alt_text,
      matrix_row_id: entity.matrix_row_id,
      matrix_column_id: entity.matrix_column_id,
      reference_pack_id: entity.reference_pack_id,
      reference_option_id: entity.reference_option_id,
      metadata: entity.metadata,
      tags: entity.tags,
      is_active: entity.is_active ? 1 : 0,
      created_at: entity.created_at,
      updated_at: entity.updated_at,
      created_by: entity.created_by,
      updated_by: entity.updated_by
    };
  }

  protected buildInsertQuery(data: CreateQuizQuestionOptionInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const optionId = `option_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const query = `
      INSERT INTO ${this.tableName} (
        id, question_id, option_text, option_text_localized, option_value,
        option_type, option_order, scoring_value, scoring_weight,
        min_value, max_value, step_value, media_url, media_type,
        media_thumbnail_url, media_alt_text, matrix_row_id, matrix_column_id,
        reference_pack_id, reference_option_id, metadata, tags,
        is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      optionId,
      data.question_id,
      data.option_text,
      data.option_text_localized || null,
      data.option_value,
      data.option_type || 'choice',
      data.option_order,
      data.scoring_value || null,
      data.scoring_weight || null,
      data.min_value || null,
      data.max_value || null,
      data.step_value || null,
      data.media_url || null,
      data.media_type || null,
      data.media_thumbnail_url || null,
      data.media_alt_text || null,
      data.matrix_row_id || null,
      data.matrix_column_id || null,
      data.reference_pack_id || null,
      data.reference_option_id || null,
      data.metadata || null,
      data.tags || null,
      data.is_active ? 1 : 0,
      now,
      now
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateQuizQuestionOptionInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.option_text !== undefined) {
      fields.push('option_text = ?');
      values.push(data.option_text);
    }

    if (data.option_text_localized !== undefined) {
      fields.push('option_text_localized = ?');
      values.push(this.stringifyJSON(data.option_text_localized));
    }

    if (data.option_value !== undefined) {
      fields.push('option_value = ?');
      values.push(data.option_value);
    }

    if (data.option_type !== undefined) {
      fields.push('option_type = ?');
      values.push(data.option_type);
    }

    if (data.option_order !== undefined) {
      fields.push('option_order = ?');
      values.push(data.option_order);
    }

    if (data.scoring_value !== undefined) {
      fields.push('scoring_value = ?');
      values.push(data.scoring_value);
    }

    if (data.scoring_weight !== undefined) {
      fields.push('scoring_weight = ?');
      values.push(data.scoring_weight);
    }

    if (data.min_value !== undefined) {
      fields.push('min_value = ?');
      values.push(data.min_value);
    }

    if (data.max_value !== undefined) {
      fields.push('max_value = ?');
      values.push(data.max_value);
    }

    if (data.step_value !== undefined) {
      fields.push('step_value = ?');
      values.push(data.step_value);
    }

    if (data.media_url !== undefined) {
      fields.push('media_url = ?');
      values.push(data.media_url);
    }

    if (data.media_type !== undefined) {
      fields.push('media_type = ?');
      values.push(data.media_type);
    }

    if (data.media_thumbnail_url !== undefined) {
      fields.push('media_thumbnail_url = ?');
      values.push(data.media_thumbnail_url);
    }

    if (data.media_alt_text !== undefined) {
      fields.push('media_alt_text = ?');
      values.push(data.media_alt_text);
    }

    if (data.matrix_row_id !== undefined) {
      fields.push('matrix_row_id = ?');
      values.push(data.matrix_row_id);
    }

    if (data.matrix_column_id !== undefined) {
      fields.push('matrix_column_id = ?');
      values.push(data.matrix_column_id);
    }

    if (data.reference_pack_id !== undefined) {
      fields.push('reference_pack_id = ?');
      values.push(data.reference_pack_id);
    }

    if (data.reference_option_id !== undefined) {
      fields.push('reference_option_id = ?');
      values.push(data.reference_option_id);
    }

    if (data.metadata !== undefined) {
      fields.push('metadata = ?');
      values.push(data.metadata);
    }

    if (data.tags !== undefined) {
      fields.push('tags = ?');
      values.push(data.tags);
    }

    if (data.is_active !== undefined) {
      fields.push('is_active = ?');
      values.push(data.is_active ? 1 : 0);
    }

    // Always update updated_at
    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.question_id) {
      conditions.push('question_id = ?');
      values.push(filters.question_id);
    }

    if (filters?.option_group) {
      conditions.push('option_group = ?');
      values.push(filters.option_group);
    }

    if (filters?.is_correct !== undefined) {
      conditions.push('is_correct = ?');
      values.push(filters.is_correct ? 1 : 0);
    }

    if (filters?.is_active !== undefined) {
      conditions.push('is_active = ?');
      values.push(filters.is_active ? 1 : 0);
    } else {
      // Default to active only
      conditions.push('is_active = 1');
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY option_order ASC';

    if (filters?.limit) {
      query += ' LIMIT ?';
      values.push(filters.limit);
    }

    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.question_id) {
      conditions.push('question_id = ?');
      values.push(filters.question_id);
    }

    if (filters?.is_active !== undefined) {
      conditions.push('is_active = ?');
      values.push(filters.is_active ? 1 : 0);
    } else {
      conditions.push('is_active = 1');
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    return { query, values };
  }

  protected extractIdFromCreateData(_data: CreateQuizQuestionOptionInput): string {
    return `option_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }



  private stringifyJSON(obj: any): string | null {
    if (obj === null || obj === undefined) return null;
    try {
      return JSON.stringify(obj);
    } catch {
      return null;
    }
  }
}
