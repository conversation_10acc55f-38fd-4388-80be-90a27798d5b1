/**
 * 轮盘配置提取器
 * 统一从 SkinConfig 中提取轮盘配置的逻辑
 */

import type { SkinConfig } from '../types';

/**
 * 轮盘配置接口
 */
export interface WheelConfig {
  // 基本配置
  container_size: number;
  wheel_radius: number;
  sector_gap: number;
  sector_padding: number;

  // 字体配置
  font_size: number;
  font_family: string;
  text_color: string;

  // 表情配置
  emoji_size: number;
  emoji_position: string;

  // 装饰配置
  decorations: boolean;
  decoration_type: string;
  background_color: string;

  // 3D 效果配置
  use_3d_effects: boolean;
  perspective?: number;
  rotate_x?: number;
  rotate_y?: number;
  rotate_z?: number;
  depth?: number;

  // 阴影配置
  shadow_enabled: boolean;
  shadow_color: string;
  shadow_blur: number;
  shadow_offset_x?: number;
  shadow_offset_y?: number;

  // 交互效果配置
  hover_effect: string;
  hover_scale?: number;
  selection_animation: string;
  selection_indicator?: string;
  transition_duration: number;
  transition_easing?: string;

  // 拖拽和缩放配置
  drag_enabled?: boolean;
  zoom_enabled?: boolean;
  zoom_min_scale?: number;
  zoom_max_scale?: number;

  // 旋转配置
  rotation_enabled?: boolean;
  rotation_auto_play?: boolean;
  rotation_speed?: number;
  rotation_clockwise?: boolean;

  // 响应式配置
  responsive_scaling: boolean;
  min_size: number;
  max_size: number;

  // 层级过渡配置
  tier_transition?: string;
  tier_transition_duration?: number;

  // 布局配置
  wheel_layout?: string;

  // 颜色配置
  colors?: any;
}

/**
 * 从 SkinConfig 中提取轮盘配置
 * @param config 皮肤配置
 * @param options 额外选项
 * @returns 轮盘配置
 */
export function extract_wheel_config(
  config: SkinConfig,
  options: {
    use_3d_effects?: boolean;
    include_advanced_features?: boolean;
  } = {}
): WheelConfig {
  // 确保 config 对象及其必要属性存在
  if (!config) {
    console.warn('extractWheelConfig: config is undefined');
    config = {} as SkinConfig;
  }

  // 确保 fonts 属性存在
  if (!config.fonts) {
    console.warn('extractWheelConfig: config.fonts is undefined');
    config.fonts = {
      family: 'Arial, sans-serif',
      size: { small: 12, medium: 14, large: 18 },
      weight: { normal: 400, bold: 700 },
    };
  }

  // 确保 colors 属性存在
  if (!config.colors) {
    console.warn('extractWheelConfig: config.colors is undefined');
    config.colors = {
      text: '#000000',
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#FFFFFF',
    };
  }

  // 确保 effects 属性存在
  if (!config.effects) {
    console.warn('extractWheelConfig: config.effects is undefined');
    config.effects = {
      shadows: false,
      animations: false,
      border_radius: 8,
      opacity: 1,
    };
  }

  // 基础配置
  const baseConfig: WheelConfig = {
    // 基本配置
    container_size: config.viewConfigs?.wheel?.container_size || 300,
    wheel_radius: config.viewConfigs?.wheel?.wheel_radius || 140,
    sector_gap: config.viewConfigs?.wheel?.sector_gap || 1,
    sector_padding: config.viewConfigs?.wheel?.sector_padding || 2,

    // 字体配置
    font_size: config.fonts?.size?.medium || 14,
    font_family: config.fonts?.family || 'Arial, sans-serif',
    text_color: config.colors?.text || '#000000',

    // 表情配置
    emoji_size: config.viewConfigs?.wheel?.emojiSize || 20,
    emoji_position: config.viewConfigs?.wheel?.emoji_position || 'center',

    // 装饰配置
    decorations: config.viewConfigs?.wheel?.decorations || false,
    decoration_type: config.viewConfigs?.wheel?.decoration_type || 'none',
    background_color: config.colors?.background || '#FFFFFF',

    // 3D 效果配置
    use_3d_effects: options.use_3d_effects ?? config.viewConfigs?.wheel?.use_3d_effects ?? false,

    // 阴影配置
    shadow_enabled: config.effects?.shadows || false,
    shadow_color: config.viewConfigs?.wheel?.shadowColor || 'rgba(0, 0, 0, 0.5)',
    shadow_blur: config.viewConfigs?.wheel?.shadowBlur || 5,

    // 交互效果配置
    hover_effect: config.viewConfigs?.wheel?.hover_effect || 'scale',
    selection_animation: config.viewConfigs?.wheel?.selection_animation || 'pulse',
    transition_duration: config.viewConfigs?.wheel?.transition_duration || 200,

    // 响应式配置
    responsive_scaling: true,
    min_size: 200,
    max_size: 400,
  };

  // 如果需要高级功能，添加额外配置
  if (options.include_advanced_features) {
    const wheelConfig = config.viewConfigs?.wheel as any;
    Object.assign(baseConfig, {
      // 3D 效果高级配置
      perspective: wheelConfig?.perspective || 1000,
      rotate_x: wheelConfig?.rotate_x || 0,
      rotate_y: wheelConfig?.rotate_y || 0,
      rotate_z: wheelConfig?.rotate_z || 0,
      depth: wheelConfig?.depth || 20,

      // 阴影高级配置
      shadow_offset_x: wheelConfig?.shadowOffsetX || 0,
      shadow_offset_y: wheelConfig?.shadowOffsetY || 2,

      // 交互效果高级配置
      hover_scale: wheelConfig?.hover_scale || 1.05,
      selection_indicator: wheelConfig?.selection_indicator || 'none',
      transition_easing: wheelConfig?.transition_easing || 'ease-in-out',

      // 拖拽和缩放配置
      drag_enabled: wheelConfig?.drag_enabled || false,
      zoom_enabled: wheelConfig?.zoom_enabled || false,
      zoom_min_scale: wheelConfig?.zoom_min_scale || 0.5,
      zoom_max_scale: wheelConfig?.zoom_max_scale || 3,

      // 旋转配置
      rotation_enabled: wheelConfig?.rotation_enabled || false,
      rotation_auto_play: wheelConfig?.rotation_auto_play || false,
      rotation_speed: wheelConfig?.rotation_speed || 1,
      rotation_clockwise: wheelConfig?.rotation_clockwise !== false,

      // 响应式高级配置
      min_size: wheelConfig?.min_size || 200,
      max_size: wheelConfig?.max_size || 400,

      // 层级过渡配置
      tier_transition: wheelConfig?.tier_transition || 'fade',
      tier_transition_duration: wheelConfig?.tier_transition_duration || 300,

      // 布局配置
      wheel_layout: wheelConfig?.wheel_layout || 'standard',

      // 颜色配置
      colors: config.colors,
    });
  }

  return baseConfig;
}

/**
 * 为特定渲染引擎提取轮盘配置
 */
export const extract_wheel_config_for_engine = {
  /**
   * Canvas 渲染引擎配置
   */
  canvas: (config: SkinConfig) => extract_wheel_config(config, {
    use_3d_effects: false,
    include_advanced_features: false,
  }),

  /**
   * SVG 渲染引擎配置
   */
  svg: (config: SkinConfig) => extract_wheel_config(config, {
    use_3d_effects: false,
    include_advanced_features: false,
  }),

  /**
   * D3 渲染引擎配置
   */
  d3: (config: SkinConfig) => extract_wheel_config(config, {
    use_3d_effects: false,
    include_advanced_features: true,
  }),

  /**
   * WebGL 渲染引擎配置
   */
  webgl: (config: SkinConfig) => extract_wheel_config(config, {
    use_3d_effects: true,
    include_advanced_features: false,
  }),

  /**
   * WebGPU 渲染引擎配置
   */
  webgpu: (config: SkinConfig) => extract_wheel_config(config, {
    use_3d_effects: true,
    include_advanced_features: false,
  }),

  /**
   * R3F (React Three Fiber) 渲染引擎配置
   */
  r3f: (config: SkinConfig) => extract_wheel_config(config, {
    use_3d_effects: true,
    include_advanced_features: false,
  }),
};
