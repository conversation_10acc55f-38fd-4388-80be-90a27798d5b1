-- User Unlocks Test Data
-- Test data for user_skin_unlocks and user_emoji_set_unlocks tables

-- ============================================================================
-- USER SKIN UNLOCKS
-- ============================================================================

-- Insert user skin unlock records
INSERT OR IGNORE INTO user_skin_unlocks (
    id, user_id, skin_id, unlock_method, unlocked_at, expires_at, 
    transaction_id, promotion_code, sync_status, server_updated_at, 
    created_at, updated_at
) VALUES
-- VIP unlock for premium dark skin
('unlock_skin_001', 'user_test_001', 'skin_premium_dark', 'vip', 
 '2024-01-15T10:00:00Z', NULL, NULL, NULL, 'synced', 
 '2024-01-15T10:00:00Z', '2024-01-15T10:00:00Z', '2024-01-15T10:00:00Z'),

-- Purchase unlock for animated nature skin
('unlock_skin_002', 'user_test_001', 'skin_animated_nature', 'purchase', 
 '2024-01-20T14:30:00Z', NULL, 'txn_skin_purchase_001', NULL, 'synced', 
 '2024-01-20T14:30:00Z', '2024-01-20T14:30:00Z', '2024-01-20T14:30:00Z'),

-- Promotional unlock for holiday winter skin (temporary)
('unlock_skin_003', 'user_test_002', 'skin_holiday_winter', 'promotion', 
 '2024-12-01T00:00:00Z', '2025-01-31T23:59:59Z', NULL, 'WINTER2024', 'synced', 
 '2024-12-01T00:00:00Z', '2024-12-01T00:00:00Z', '2024-12-01T00:00:00Z'),

-- Achievement unlock for streak skin
('unlock_skin_004', 'user_test_003', 'skin_achievement_streak', 'achievement', 
 '2024-02-10T16:45:00Z', NULL, NULL, NULL, 'synced', 
 '2024-02-10T16:45:00Z', '2024-02-10T16:45:00Z', '2024-02-10T16:45:00Z');

-- ============================================================================
-- USER EMOJI SET UNLOCKS
-- ============================================================================

-- Insert user emoji set unlock records
INSERT OR IGNORE INTO user_emoji_set_unlocks (
    id, user_id, emoji_set_id, unlock_method, unlocked_at, expires_at, 
    transaction_id, promotion_code, sync_status, server_updated_at, 
    created_at, updated_at
) VALUES
-- VIP unlock for premium animated emoji set
('unlock_emoji_001', 'user_test_001', 'emoji_set_premium_animated', 'vip', 
 '2024-01-15T10:00:00Z', NULL, NULL, NULL, 'synced', 
 '2024-01-15T10:00:00Z', '2024-01-15T10:00:00Z', '2024-01-15T10:00:00Z'),

-- Purchase unlock for cultural Asian emoji set
('unlock_emoji_002', 'user_test_001', 'emoji_set_cultural_asian', 'purchase', 
 '2024-01-25T11:15:00Z', NULL, 'txn_emoji_purchase_001', NULL, 'synced', 
 '2024-01-25T11:15:00Z', '2024-01-25T11:15:00Z', '2024-01-25T11:15:00Z'),

-- Promotional unlock for holiday pack (temporary)
('unlock_emoji_003', 'user_test_002', 'emoji_set_holiday_pack', 'promotion', 
 '2024-12-01T00:00:00Z', '2025-01-31T23:59:59Z', NULL, 'HOLIDAY2024', 'synced', 
 '2024-12-01T00:00:00Z', '2024-12-01T00:00:00Z', '2024-12-01T00:00:00Z'),

-- Achievement unlock for gold emoji set
('unlock_emoji_004', 'user_test_003', 'emoji_set_achievement_gold', 'achievement', 
 '2024-03-05T09:30:00Z', NULL, NULL, NULL, 'synced', 
 '2024-03-05T09:30:00Z', '2024-03-05T09:30:00Z', '2024-03-05T09:30:00Z'),

-- Free unlock for basic emoji set
('unlock_emoji_005', 'user_test_001', 'emoji_set_free_basic', 'free', 
 '2024-01-01T00:00:00Z', NULL, NULL, NULL, 'synced', 
 '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z');

-- ============================================================================
-- VERIFICATION AND STATISTICS
-- ============================================================================

-- Verify skin unlocks were inserted
SELECT 'User Skin Unlocks Count:' as info, COUNT(*) as count FROM user_skin_unlocks;

-- Verify emoji set unlocks were inserted
SELECT 'User Emoji Set Unlocks Count:' as info, COUNT(*) as count FROM user_emoji_set_unlocks;

-- Show unlock statistics by method for skins
SELECT 
    unlock_method,
    COUNT(*) as unlock_count,
    COUNT(CASE WHEN expires_at IS NOT NULL THEN 1 END) as temporary_unlocks,
    COUNT(CASE WHEN expires_at IS NULL THEN 1 END) as permanent_unlocks
FROM user_skin_unlocks 
GROUP BY unlock_method 
ORDER BY unlock_count DESC;

-- Show unlock statistics by method for emoji sets
SELECT 
    unlock_method,
    COUNT(*) as unlock_count,
    COUNT(CASE WHEN expires_at IS NOT NULL THEN 1 END) as temporary_unlocks,
    COUNT(CASE WHEN expires_at IS NULL THEN 1 END) as permanent_unlocks
FROM user_emoji_set_unlocks 
GROUP BY unlock_method 
ORDER BY unlock_count DESC;

-- Show user unlock summary
SELECT 
    u.user_id,
    COUNT(DISTINCT u.skin_id) as skins_unlocked,
    COUNT(DISTINCT e.emoji_set_id) as emoji_sets_unlocked,
    COUNT(DISTINCT u.skin_id) + COUNT(DISTINCT e.emoji_set_id) as total_unlocks
FROM user_skin_unlocks u
FULL OUTER JOIN user_emoji_set_unlocks e ON u.user_id = e.user_id
GROUP BY u.user_id
ORDER BY total_unlocks DESC;

-- Show expiring unlocks (promotional content)
SELECT 
    'Skin' as content_type,
    user_id,
    skin_id as content_id,
    promotion_code,
    expires_at
FROM user_skin_unlocks 
WHERE expires_at IS NOT NULL AND expires_at > datetime('now')
UNION ALL
SELECT 
    'Emoji Set' as content_type,
    user_id,
    emoji_set_id as content_id,
    promotion_code,
    expires_at
FROM user_emoji_set_unlocks 
WHERE expires_at IS NOT NULL AND expires_at > datetime('now')
ORDER BY expires_at;
