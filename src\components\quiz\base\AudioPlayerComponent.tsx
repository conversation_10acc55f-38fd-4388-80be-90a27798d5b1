/**
 * Quiz音频播放器组件
 * 支持多种中医文化样式的音频播放器组件
 */

import React, { useState, useRef, useEffect } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { AudioPlayerComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type AudioPlayerComponentConfig = z.infer<typeof AudioPlayerComponentConfigSchema>;

export interface AudioPlayerComponentProps extends BaseQuizComponentProps<AudioPlayerComponentConfig> {
  audioUrl: string;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  disabled?: boolean;
}

interface AudioPlayerComponentState extends ComponentState {
  is_playing: boolean;
  is_paused: boolean;
  current_time: number;
  duration: number;
  volume: number;
  is_muted: boolean;
  is_buffering: boolean;
  has_error: boolean;
  error_message: string | null;
}

/**
 * 音频播放器组件类
 */
export class AudioPlayerComponent extends BaseQuizComponent<
  AudioPlayerComponentConfig,
  AudioPlayerComponentProps,
  AudioPlayerComponentState
> {
  private audioRef = React.createRef<HTMLAudioElement>();
  private progressRef = React.createRef<HTMLDivElement>();

  extractConfig(props: AudioPlayerComponentProps): AudioPlayerComponentConfig {
    return props.config;
  }

  getInitialState(): AudioPlayerComponentState {
    return {
      is_loading: true,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      is_playing: false,
      is_paused: false,
      current_time: 0,
      duration: 0,
      volume: 1,
      is_muted: false,
      is_buffering: false,
      has_error: false,
      error_message: null
    };
  }

  componentDidMount(): void {
    const audio = this.audioRef.current;
    if (audio) {
      audio.addEventListener('loadstart', this.handleLoadStart);
      audio.addEventListener('loadedmetadata', this.handleLoadedMetadata);
      audio.addEventListener('canplay', this.handleCanPlay);
      audio.addEventListener('play', this.handlePlay);
      audio.addEventListener('pause', this.handlePause);
      audio.addEventListener('ended', this.handleEnded);
      audio.addEventListener('timeupdate', this.handleTimeUpdate);
      audio.addEventListener('volumechange', this.handleVolumeChange);
      audio.addEventListener('waiting', this.handleWaiting);
      audio.addEventListener('error', this.handleError);
    }
  }

  componentWillUnmount(): void {
    const audio = this.audioRef.current;
    if (audio) {
      audio.removeEventListener('loadstart', this.handleLoadStart);
      audio.removeEventListener('loadedmetadata', this.handleLoadedMetadata);
      audio.removeEventListener('canplay', this.handleCanPlay);
      audio.removeEventListener('play', this.handlePlay);
      audio.removeEventListener('pause', this.handlePause);
      audio.removeEventListener('ended', this.handleEnded);
      audio.removeEventListener('timeupdate', this.handleTimeUpdate);
      audio.removeEventListener('volumechange', this.handleVolumeChange);
      audio.removeEventListener('waiting', this.handleWaiting);
      audio.removeEventListener('error', this.handleError);
    }
  }

  // 音频事件处理
  private handleLoadStart = (): void => {
    this.setState({ is_loading: true, is_buffering: true });
  };

  private handleLoadedMetadata = (): void => {
    const audio = this.audioRef.current;
    if (audio) {
      this.setState({ 
        duration: audio.duration,
        is_loading: false,
        is_buffering: false
      });
    }
  };

  private handleCanPlay = (): void => {
    this.setState({ is_loading: false, is_buffering: false });
  };

  private handlePlay = (): void => {
    this.setState({ is_playing: true, is_paused: false });
    this.props.onPlay?.();
    
    this.emitInteractionEvent('focus', {
      action: 'audio_play',
      current_time: this.state.current_time
    });
  };

  private handlePause = (): void => {
    this.setState({ is_playing: false, is_paused: true });
    this.props.onPause?.();
    
    this.emitInteractionEvent('focus', {
      action: 'audio_pause',
      current_time: this.state.current_time
    });
  };

  private handleEnded = (): void => {
    this.setState({ is_playing: false, is_paused: false, current_time: 0 });
    this.props.onEnded?.();
    
    this.emitInteractionEvent('focus', {
      action: 'audio_ended',
      duration: this.state.duration
    });
  };

  private handleTimeUpdate = (): void => {
    const audio = this.audioRef.current;
    if (audio) {
      this.setState({ current_time: audio.currentTime });
      this.props.onTimeUpdate?.(audio.currentTime, audio.duration);
    }
  };

  private handleVolumeChange = (): void => {
    const audio = this.audioRef.current;
    if (audio) {
      this.setState({ 
        volume: audio.volume,
        is_muted: audio.muted
      });
    }
  };

  private handleWaiting = (): void => {
    this.setState({ is_buffering: true });
  };

  private handleError = (): void => {
    const audio = this.audioRef.current;
    const errorMessage = audio?.error?.message || 'Audio playback error';
    
    this.setState({
      has_error: true,
      error_message: errorMessage,
      is_loading: false,
      is_buffering: false
    });
  };

  // 控制方法
  private togglePlayPause = (): void => {
    if (this.state.is_disabled || this.state.has_error) return;
    
    const audio = this.audioRef.current;
    if (!audio) return;

    if (this.state.is_playing) {
      audio.pause();
    } else {
      audio.play().catch(error => {
        console.error('Audio play failed:', error);
        this.setState({
          has_error: true,
          error_message: 'Failed to play audio'
        });
      });
    }

    this.triggerHapticFeedback('light');
  };

  private handleSeek = (event: React.MouseEvent<HTMLDivElement>): void => {
    if (this.state.is_disabled || this.state.has_error) return;
    
    const audio = this.audioRef.current;
    const progressBar = this.progressRef.current;
    if (!audio || !progressBar) return;

    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * this.state.duration;
    
    audio.currentTime = newTime;
    this.setState({ current_time: newTime });

    this.emitInteractionEvent('click', {
      action: 'audio_seek',
      seek_time: newTime,
      percentage: percentage
    });
  };

  private handleVolumeSliderChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const audio = this.audioRef.current;
    if (!audio) return;

    const newVolume = parseFloat(event.target.value);
    audio.volume = newVolume;
    this.setState({ volume: newVolume, is_muted: newVolume === 0 });
  };

  private toggleMute = (): void => {
    const audio = this.audioRef.current;
    if (!audio) return;

    audio.muted = !audio.muted;
    this.setState({ is_muted: audio.muted });
    this.triggerHapticFeedback('light');
  };

  // 格式化时间
  private formatTime(seconds: number): string {
    if (isNaN(seconds)) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // 获取播放器样式类名
  private getPlayerStyleClassName(): string {
    const style = this.config.style.player_style;
    return `quiz-audio-player-${style}`;
  }

  // 渲染播放/暂停按钮
  private renderPlayButton(): React.ReactNode {
    const isPlaying = this.state.is_playing;
    const isLoading = this.state.is_loading || this.state.is_buffering;
    
    return (
      <button
        className={`quiz-audio-play-button ${isPlaying ? 'playing' : 'paused'}`}
        onClick={this.togglePlayPause}
        disabled={this.state.is_disabled || this.state.has_error}
        aria-label={isPlaying ? 'Pause' : 'Play'}
      >
        {isLoading ? (
          <div className="quiz-audio-loading-spinner" />
        ) : isPlaying ? (
          <span className="quiz-audio-pause-icon">⏸️</span>
        ) : (
          <span className="quiz-audio-play-icon">▶️</span>
        )}
      </button>
    );
  }

  // 渲染进度条
  private renderProgressBar(): React.ReactNode {
    const progress = this.state.duration > 0 ? (this.state.current_time / this.state.duration) * 100 : 0;
    
    return (
      <div 
        ref={this.progressRef}
        className="quiz-audio-progress-container"
        onClick={this.handleSeek}
      >
        <div className="quiz-audio-progress-track">
          <div 
            className="quiz-audio-progress-fill"
            style={{ width: `${progress}%` }}
          />
          <div 
            className="quiz-audio-progress-thumb"
            style={{ left: `${progress}%` }}
          />
        </div>
      </div>
    );
  }

  // 渲染时间显示
  private renderTimeDisplay(): React.ReactNode {
    if (!this.config.controls.show_time) return null;
    
    return (
      <div className="quiz-audio-time-display">
        <span className="quiz-audio-current-time">
          {this.formatTime(this.state.current_time)}
        </span>
        <span className="quiz-audio-time-separator">/</span>
        <span className="quiz-audio-duration">
          {this.formatTime(this.state.duration)}
        </span>
      </div>
    );
  }

  // 渲染音量控制
  private renderVolumeControl(): React.ReactNode {
    if (!this.config.controls.show_volume) return null;
    
    return (
      <div className="quiz-audio-volume-control">
        <button
          className="quiz-audio-mute-button"
          onClick={this.toggleMute}
          aria-label={this.state.is_muted ? 'Unmute' : 'Mute'}
        >
          {this.state.is_muted ? '🔇' : '🔊'}
        </button>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={this.state.is_muted ? 0 : this.state.volume}
          onChange={this.handleVolumeSliderChange}
          className="quiz-audio-volume-slider"
          aria-label="Volume"
        />
      </div>
    );
  }

  // 渲染错误状态
  private renderErrorState(): React.ReactNode {
    if (!this.state.has_error) return null;
    
    return (
      <div className="quiz-audio-error">
        <span className="quiz-audio-error-icon">⚠️</span>
        <span className="quiz-audio-error-text">
          {this.state.error_message || 'Audio playback error'}
        </span>
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-audio-player-component',
      this.getPlayerStyleClassName(),
      this.state.is_playing && 'quiz-audio-playing',
      this.state.is_disabled && 'quiz-audio-disabled',
      this.state.has_error && 'quiz-audio-error-state',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
        role="region"
        aria-label="Audio player"
      >
        {/* 隐藏的音频元素 */}
        <audio
          ref={this.audioRef}
          src={this.props.audioUrl}
          preload="metadata"
        />

        {/* 错误状态 */}
        {this.renderErrorState()}

        {/* 播放器控件 */}
        {!this.state.has_error && (
          <div className="quiz-audio-controls">
            {/* 播放按钮 */}
            {this.renderPlayButton()}

            {/* 进度条和时间 */}
            <div className="quiz-audio-progress-section">
              {this.renderProgressBar()}
              {this.renderTimeDisplay()}
            </div>

            {/* 音量控制 */}
            {this.renderVolumeControl()}
          </div>
        )}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'region';
  }

  protected getAriaLabel(): string {
    return `Audio player: ${this.state.is_playing ? 'Playing' : 'Paused'}`;
  }
}

// 使用Context的函数式组件包装器
const AudioPlayerComponentWrapper: React.FC<AudioPlayerComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <AudioPlayerComponent.contextType.Provider value={{ language }}>
      <AudioPlayerComponent {...props} />
    </AudioPlayerComponent.contextType.Provider>
  );
};

// 设置Context类型
AudioPlayerComponent.contextType = React.createContext({ language: 'zh' });

export default AudioPlayerComponentWrapper;
