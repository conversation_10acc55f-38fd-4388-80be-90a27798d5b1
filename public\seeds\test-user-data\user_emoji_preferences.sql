-- ============================================================================
-- 用户EMOJI个性化配置数据
-- ============================================================================
-- 
-- 此文件包含用户层面的emoji个性化配置
-- 包括：
-- 1. 用户全局emoji偏好设置
-- 2. 用户对特定问题的emoji覆盖配置
-- 3. 用户自定义emoji主题
-- 4. 用户emoji使用历史和偏好分析

-- ============================================================================
-- 用户全局EMOJI偏好配置
-- ============================================================================

-- 用户1: 喜欢可爱风格的emoji
UPDATE user_presentation_configs SET
    presentation_config = JSON_SET(presentation_config,
        '$.layer4_view_detail.emotion_presentation.emoji_mapping',
        JSON('{
            "happy": {
                "primary": "🥰",
                "alternatives": ["😊", "😌", "🙂", "😇", "💕"]
            },
            "sad": {
                "primary": "🥺",
                "alternatives": ["😢", "😔", "😞", "☹️", "💔"]
            },
            "angry": {
                "primary": "😤",
                "alternatives": ["😠", "😡", "🤬", "💢", "🔥"]
            },
            "surprised": {
                "primary": "😮",
                "alternatives": ["😲", "😯", "🤯", "😦", "😧"]
            },
            "playful": {
                "primary": "😜",
                "alternatives": ["😋", "🤪", "😝", "🤭", "😏"]
            },
            "content": {
                "primary": "😌",
                "alternatives": ["😊", "🙂", "😇", "🥰", "💖"]
            }
        }'),
        '$.layer4_view_detail.emotion_presentation.color_mapping',
        JSON('{
            "happy": "#FFB6C1",
            "sad": "#87CEEB", 
            "angry": "#FFB6C1",
            "surprised": "#DDA0DD",
            "playful": "#FF69B4",
            "content": "#98FB98"
        }'),
        '$.layer4_view_detail.emotion_presentation.animation_mapping',
        JSON('{
            "happy": "heart_glow",
            "sad": "gentle_fade",
            "angry": "cute_puff",
            "surprised": "sparkle_pop",
            "playful": "bounce_cute",
            "content": "soft_glow"
        }')
    )
WHERE user_id = 'test-user-1';

-- 用户2: 喜欢简约专业风格的emoji
UPDATE user_presentation_configs SET
    presentation_config = JSON_SET(presentation_config,
        '$.layer4_view_detail.emotion_presentation.emoji_mapping',
        JSON('{
            "happy": {
                "primary": "✅",
                "alternatives": ["👍", "💼", "📈", "🎯", "⭐"]
            },
            "sad": {
                "primary": "📉",
                "alternatives": ["📊", "⚠️", "🔍", "📋", "💭"]
            },
            "angry": {
                "primary": "⚠️",
                "alternatives": ["🚨", "⛔", "🔥", "💢", "⚡"]
            },
            "surprised": {
                "primary": "💡",
                "alternatives": ["🔍", "📊", "📈", "⭐", "🎯"]
            },
            "focused": {
                "primary": "🎯",
                "alternatives": ["💼", "📋", "🔍", "⚡", "💪"]
            },
            "productive": {
                "primary": "📈",
                "alternatives": ["💪", "🚀", "⚡", "🎯", "✅"]
            }
        }'),
        '$.layer4_view_detail.emotion_presentation.color_mapping',
        JSON('{
            "happy": "#2E8B57",
            "sad": "#4682B4",
            "angry": "#FF6347",
            "surprised": "#4169E1",
            "focused": "#2F4F4F",
            "productive": "#228B22"
        }'),
        '$.layer4_view_detail.emotion_presentation.animation_mapping',
        JSON('{
            "happy": "check_mark",
            "sad": "decline_chart",
            "angry": "alert_flash",
            "surprised": "light_bulb",
            "focused": "target_lock",
            "productive": "progress_bar"
        }')
    )
WHERE user_id = 'test-user-2';

-- 用户3: 喜欢自然主题的emoji
UPDATE user_presentation_configs SET
    presentation_config = JSON_SET(presentation_config,
        '$.layer4_view_detail.emotion_presentation.emoji_mapping',
        JSON('{
            "happy": {
                "primary": "🌞",
                "alternatives": ["🌻", "🌺", "🌸", "🌈", "🦋"]
            },
            "sad": {
                "primary": "🌧️",
                "alternatives": ["☁️", "🌊", "🍂", "🌙", "💧"]
            },
            "angry": {
                "primary": "⛈️",
                "alternatives": ["🌪️", "🔥", "🌋", "⚡", "🌩️"]
            },
            "surprised": {
                "primary": "⚡",
                "alternatives": ["🌟", "✨", "💫", "🌠", "🌈"]
            },
            "peaceful": {
                "primary": "🌸",
                "alternatives": ["🍃", "🌿", "🌾", "🕊️", "🦋"]
            },
            "free": {
                "primary": "🦋",
                "alternatives": ["🕊️", "🌈", "☁️", "🌸", "🌿"]
            }
        }'),
        '$.layer4_view_detail.emotion_presentation.color_mapping',
        JSON('{
            "happy": "#FFD700",
            "sad": "#87CEEB",
            "angry": "#DC143C",
            "surprised": "#9370DB",
            "peaceful": "#98FB98",
            "free": "#87CEEB"
        }'),
        '$.layer4_view_detail.emotion_presentation.animation_mapping',
        JSON('{
            "happy": "sunshine",
            "sad": "rain_drops",
            "angry": "thunder_storm",
            "surprised": "lightning_flash",
            "peaceful": "gentle_breeze",
            "free": "butterfly_flight"
        }')
    )
WHERE user_id = 'test-user-3';

-- ============================================================================
-- 用户问题特定EMOJI覆盖配置
-- ============================================================================

-- 用户1在工作压力评估中使用动物emoji来缓解压力
INSERT OR IGNORE INTO question_presentation_overrides (
    id, user_id, question_id, presentation_overrides, override_reason,
    override_priority, is_active, created_at, updated_at, created_by, updated_by
) VALUES 
('qpo_user1_work_stress_animals', 'test-user-1', 'q_work_stress_level', 
 '{
    "emoji_theme": "stress_relief_animals",
    "emoji_mapping": {
        "no_stress": {
            "emoji": "🐨",
            "color": "#4CAF50",
            "animation": "sleep",
            "alternatives": ["🐼", "🐰", "🐱", "🦋", "🕊️"]
        },
        "mild_stress": {
            "emoji": "🐶",
            "color": "#8BC34A",
            "animation": "wag",
            "alternatives": ["🐱", "🐰", "🐼", "🦔", "🐹"]
        },
        "moderate_stress": {
            "emoji": "🐺",
            "color": "#FFC107",
            "animation": "howl",
            "alternatives": ["🦊", "🐯", "🦁", "🐸", "🦉"]
        },
        "high_stress": {
            "emoji": "🦁",
            "color": "#FF9800",
            "animation": "roar",
            "alternatives": ["🐯", "🦈", "🐉", "🦅", "🐺"]
        },
        "extreme_stress": {
            "emoji": "🐉",
            "color": "#F44336",
            "animation": "fire_breath",
            "alternatives": ["🦖", "🦈", "🐍", "🕷️", "🦂"]
        }
    },
    "context": "work_stress_relief",
    "applied_at": "2024-01-15T09:00:00Z"
 }', 'stress_relief', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'test-user-1', 'test-user-1'),

-- 用户2在情绪追踪中使用食物emoji表达心情
('qpo_user2_mood_food', 'test-user-2', 'q001_primary_emotion',
 '{
    "emoji_theme": "food_mood",
    "emoji_mapping": {
        "happy": {
            "emoji": "🍦",
            "color": "#FFB6C1",
            "animation": "sweet_swirl",
            "alternatives": ["🍰", "🎂", "🍭", "🍯", "🧁"]
        },
        "sad": {
            "emoji": "☕",
            "color": "#8B4513",
            "animation": "steam_rise",
            "alternatives": ["🍵", "🥛", "🍞", "🥔", "🍂"]
        },
        "angry": {
            "emoji": "🌶️",
            "color": "#FF4500",
            "animation": "spicy_heat",
            "alternatives": ["🔥", "🌽", "🧄", "🥵", "💥"]
        },
        "surprised": {
            "emoji": "🍿",
            "color": "#FFD700",
            "animation": "pop_corn",
            "alternatives": ["🥨", "🍪", "🧁", "🎉", "✨"]
        }
    },
    "context": "food_comfort",
    "applied_at": "2024-01-16T14:30:00Z"
 }', 'personal_comfort', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'test-user-2', 'test-user-2'),

-- 用户3在TCM体质评估中使用传统符号
('qpo_user3_tcm_traditional', 'test-user-3', 'q_tcm_constitution_type',
 '{
    "emoji_theme": "traditional_symbols",
    "emoji_mapping": {
        "balanced": {
            "emoji": "☯️",
            "color": "#4CAF50",
            "animation": "yin_yang_rotate",
            "alternatives": ["⚖️", "🧘", "🌿", "🍃", "🕊️"]
        },
        "qi_deficiency": {
            "emoji": "🌙",
            "color": "#B0C4DE",
            "animation": "moon_phase",
            "alternatives": ["💤", "☁️", "🍂", "💨", "🌫️"]
        },
        "yang_deficiency": {
            "emoji": "❄️",
            "color": "#87CEEB",
            "animation": "ice_crystal",
            "alternatives": ["🧊", "🌨️", "💙", "🌊", "🔵"]
        },
        "yin_deficiency": {
            "emoji": "☀️",
            "color": "#FF6347",
            "animation": "sun_flare",
            "alternatives": ["🔥", "🌡️", "🌶️", "💥", "⚡"]
        }
    },
    "context": "traditional_medicine",
    "applied_at": "2024-01-17T10:15:00Z"
 }', 'cultural_preference', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'test-user-3', 'test-user-3');

-- ============================================================================
-- 用户自定义EMOJI主题
-- ============================================================================

-- 用户4创建的"游戏风格"emoji主题
INSERT OR IGNORE INTO question_presentation_overrides (
    id, user_id, question_id, presentation_overrides, override_reason,
    override_priority, is_active, created_at, updated_at, created_by, updated_by
) VALUES
('qpo_user4_gaming_theme', 'test-user-4', 'q001_primary_emotion',
 '{
    "emoji_theme": "gaming_style",
    "emoji_mapping": {
        "happy": {
            "emoji": "🎮",
            "color": "#00FF00",
            "animation": "level_up",
            "alternatives": ["🏆", "⭐", "💎", "🎯", "🚀"]
        },
        "sad": {
            "emoji": "💀",
            "color": "#8B0000",
            "animation": "game_over",
            "alternatives": ["😵", "💔", "⚫", "🌫️", "💤"]
        },
        "angry": {
            "emoji": "💥",
            "color": "#FF0000",
            "animation": "explosion",
            "alternatives": ["🔥", "⚡", "💢", "🌪️", "🎆"]
        },
        "surprised": {
            "emoji": "❓",
            "color": "#FFD700",
            "animation": "question_mark",
            "alternatives": ["❗", "💡", "🔍", "👀", "🤔"]
        },
        "focused": {
            "emoji": "🎯",
            "color": "#0000FF",
            "animation": "target_lock",
            "alternatives": ["🔍", "👁️", "⚡", "🧠", "💪"]
        }
    },
    "context": "gaming_mindset",
    "applied_at": "2024-01-18T16:45:00Z"
 }', 'personal_interest', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'test-user-4', 'test-user-4');

-- ============================================================================
-- 用户EMOJI使用历史和偏好分析数据
-- ============================================================================

-- 创建用户emoji使用统计表（如果不存在）
CREATE TABLE IF NOT EXISTS user_emoji_usage_stats (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    option_value TEXT NOT NULL,
    emoji_used TEXT NOT NULL,
    usage_count INTEGER DEFAULT 1,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    context_info TEXT, -- JSON: 使用上下文信息

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, option_value, emoji_used)
);

-- 用户emoji使用历史数据
INSERT OR IGNORE INTO user_emoji_usage_stats (
    id, user_id, option_value, emoji_used, usage_count, last_used_at, context_info,
    created_at, updated_at
) VALUES
-- 用户1的emoji使用统计
('usage_user1_happy_cute', 'test-user-1', 'happy', '🥰', 15, '2024-01-20T10:30:00Z',
 '{"quiz_pack": "mood_track_branching_v1", "session_count": 8, "avg_response_time": 2.3}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('usage_user1_sad_cute', 'test-user-1', 'sad', '🥺', 8, '2024-01-19T14:15:00Z',
 '{"quiz_pack": "mood_track_branching_v1", "session_count": 4, "avg_response_time": 3.1}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('usage_user1_playful_fun', 'test-user-1', 'playful', '😜', 12, '2024-01-20T16:45:00Z',
 '{"quiz_pack": "mood_track_branching_v1", "session_count": 6, "avg_response_time": 1.8}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 用户2的emoji使用统计
('usage_user2_happy_pro', 'test-user-2', 'happy', '✅', 22, '2024-01-20T09:00:00Z',
 '{"quiz_pack": "work_stress_assessment", "session_count": 11, "avg_response_time": 1.5}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('usage_user2_focused_work', 'test-user-2', 'focused', '🎯', 18, '2024-01-20T11:30:00Z',
 '{"quiz_pack": "work_stress_assessment", "session_count": 9, "avg_response_time": 2.0}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('usage_user2_productive_biz', 'test-user-2', 'productive', '📈', 14, '2024-01-19T15:20:00Z',
 '{"quiz_pack": "work_stress_assessment", "session_count": 7, "avg_response_time": 1.7}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 用户3的emoji使用统计
('usage_user3_happy_nature', 'test-user-3', 'happy', '🌞', 19, '2024-01-20T08:15:00Z',
 '{"quiz_pack": "daily_emotion_check", "session_count": 10, "avg_response_time": 2.8}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('usage_user3_peaceful_zen', 'test-user-3', 'peaceful', '🌸', 16, '2024-01-20T12:00:00Z',
 '{"quiz_pack": "daily_emotion_check", "session_count": 8, "avg_response_time": 3.5}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('usage_user3_free_butterfly', 'test-user-3', 'free', '🦋', 11, '2024-01-19T17:30:00Z',
 '{"quiz_pack": "daily_emotion_check", "session_count": 6, "avg_response_time": 4.2}',
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ============================================================================
-- 用户EMOJI偏好学习数据
-- ============================================================================

-- 创建用户emoji偏好学习表（如果不存在）
CREATE TABLE IF NOT EXISTS user_emoji_preferences_learned (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    option_value TEXT NOT NULL,

    -- 学习到的偏好
    preferred_emoji TEXT NOT NULL,
    confidence_score REAL DEFAULT 0.0, -- 0.0-1.0 置信度
    learning_source TEXT, -- 'usage_pattern', 'explicit_choice', 'context_analysis'

    -- 偏好上下文
    context_tags TEXT, -- JSON: 上下文标签
    usage_patterns TEXT, -- JSON: 使用模式分析

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, option_value)
);

-- 用户emoji偏好学习数据
INSERT OR IGNORE INTO user_emoji_preferences_learned (
    id, user_id, option_value, preferred_emoji, confidence_score, learning_source,
    context_tags, usage_patterns, is_active, created_at, updated_at
) VALUES
-- 用户1学习到的偏好
('learned_user1_happy', 'test-user-1', 'happy', '🥰', 0.92, 'usage_pattern',
 '["cute", "affectionate", "warm"]',
 '{"avg_selection_time": 1.8, "consistency_rate": 0.87, "context_preference": "personal_moments"}',
 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('learned_user1_playful', 'test-user-1', 'playful', '😜', 0.85, 'usage_pattern',
 '["fun", "mischievous", "lighthearted"]',
 '{"avg_selection_time": 1.5, "consistency_rate": 0.83, "context_preference": "social_interactions"}',
 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 用户2学习到的偏好
('learned_user2_happy', 'test-user-2', 'happy', '✅', 0.95, 'explicit_choice',
 '["professional", "achievement", "success"]',
 '{"avg_selection_time": 1.2, "consistency_rate": 0.94, "context_preference": "work_environment"}',
 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('learned_user2_focused', 'test-user-2', 'focused', '🎯', 0.88, 'usage_pattern',
 '["goal_oriented", "precise", "determined"]',
 '{"avg_selection_time": 1.6, "consistency_rate": 0.89, "context_preference": "task_completion"}',
 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 用户3学习到的偏好
('learned_user3_happy', 'test-user-3', 'happy', '🌞', 0.90, 'context_analysis',
 '["natural", "warm", "optimistic"]',
 '{"avg_selection_time": 2.1, "consistency_rate": 0.86, "context_preference": "outdoor_activities"}',
 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('learned_user3_peaceful', 'test-user-3', 'peaceful', '🌸', 0.93, 'usage_pattern',
 '["serene", "gentle", "harmonious"]',
 '{"avg_selection_time": 2.8, "consistency_rate": 0.91, "context_preference": "meditation_time"}',
 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 验证用户emoji数据加载
SELECT 'User emoji preferences updated:' as info, COUNT(*) as count FROM user_presentation_configs
WHERE JSON_EXTRACT(presentation_config, '$.layer4_view_detail.emotion_presentation.emoji_mapping') IS NOT NULL;
SELECT 'Question emoji overrides loaded:' as info, COUNT(*) as count FROM question_presentation_overrides;
SELECT 'User emoji usage stats loaded:' as info, COUNT(*) as count FROM user_emoji_usage_stats;
SELECT 'User emoji preferences learned:' as info, COUNT(*) as count FROM user_emoji_preferences_learned;
