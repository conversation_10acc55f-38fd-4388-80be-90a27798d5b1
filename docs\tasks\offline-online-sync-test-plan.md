# 离线存储与数据同步测试计划

本文档概述了应用程序离线存储和数据同步功能的测试计划。该应用允许用户在不联网的情况下使用Capacitor-SQLite实现离线存储和使用核心产品功能，当打开数据同步后，则可以同步数据到云端的数据库，使用tRPC链接云端数据库，实现数据同步、用户登录等核心功能。

## 测试目标

1. 验证离线存储功能的正确性和可靠性
2. 验证数据同步功能的正确性和可靠性
3. 验证用户认证与VIP状态同步功能
4. 验证付费皮肤解锁状态同步功能
5. 验证在各种网络状态下的应用行为

## 测试环境

- 开发环境：使用本地SQLite模拟Turso数据库
- 测试环境：使用Vitest进行单元测试和集成测试
- 模拟环境：使用Mock Service Worker模拟tRPC API响应

## 测试层级

### 1. 单元测试

#### 1.1 离线存储服务测试

- `src/tests/services/offline/dbService.test.ts`
  - 测试数据库初始化
  - 测试表创建
  - 测试CRUD操作
  - 测试事务处理
  - 测试错误处理

- `src/tests/services/offline/emotionService.test.ts`
  - 测试情绪数据的本地存储
  - 测试情绪数据的查询
  - 测试情绪数据的更新
  - 测试情绪数据的删除

- `src/tests/services/offline/historyService.test.ts`
  - 测试历史记录的本地存储
  - 测试历史记录的查询
  - 测试历史记录的更新
  - 测试历史记录的删除

- `src/tests/services/offline/tagService.test.ts`
  - 测试标签的本地存储
  - 测试标签的查询
  - 测试标签的更新
  - 测试标签的删除

#### 1.2 数据同步服务测试

- `src/tests/services/syncService.test.ts`
  - 测试同步服务初始化
  - 测试获取未同步数据
  - 测试上传数据到服务器
  - 测试从服务器下载数据
  - 测试数据冲突解决
  - 测试同步状态更新
  - 测试同步错误处理

- `src/tests/services/apiService.test.ts`
  - 测试API服务初始化
  - 测试API请求构建
  - 测试API响应处理
  - 测试API错误处理
  - 测试网络状态检测

#### 1.3 用户认证与VIP状态测试

- `src/tests/services/authService.test.ts`
  - 测试用户登录
  - 测试用户登出
  - 测试会话管理
  - 测试VIP状态获取
  - 测试VIP状态缓存

#### 1.4 皮肤解锁状态测试

- `src/tests/services/skinService.test.ts`
  - 测试皮肤解锁状态获取
  - 测试皮肤解锁状态缓存
  - 测试皮肤解锁状态更新

### 2. 钩子测试

- `src/tests/hooks/useDataSync.test.ts`
  - 测试同步触发
  - 测试同步状态管理
  - 测试同步错误处理

- `src/tests/hooks/useLocalEmotionsData.test.ts`
  - 测试本地情绪数据加载
  - 测试本地情绪数据更新
  - 测试本地情绪数据与同步状态的交互

- `src/tests/hooks/useLocalHistoryData.test.ts`
  - 测试本地历史记录加载
  - 测试本地历史记录更新
  - 测试本地历史记录与同步状态的交互

- `src/tests/hooks/useLocalTagsData.test.ts`
  - 测试本地标签加载
  - 测试本地标签更新
  - 测试本地标签与同步状态的交互

### 3. 上下文测试

- `src/tests/contexts/SyncContext.test.tsx`
  - 测试同步状态管理
  - 测试同步开关功能
  - 测试同步状态持久化

### 4. 集成测试

- `src/tests/integration/offline-storage.test.tsx`
  - 测试应用在离线状态下的数据存储
  - 测试应用在离线状态下的数据读取
  - 测试应用在离线状态下的UI交互

- `src/tests/integration/online-sync.test.tsx`
  - 测试应用在在线状态下的数据同步
  - 测试应用在在线状态下的数据读取
  - 测试应用在在线状态下的UI交互

- `src/tests/integration/network-transition.test.tsx`
  - 测试应用从离线到在线的转换
  - 测试应用从在线到离线的转换
  - 测试网络状态变化时的数据处理

- `src/tests/integration/auth-vip-sync.test.tsx`
  - 测试用户登录后的VIP状态同步
  - 测试VIP状态变化后的功能解锁
  - 测试用户登出后的VIP状态处理

- `src/tests/integration/skin-unlock-sync.test.tsx`
  - 测试皮肤解锁状态同步
  - 测试皮肤解锁后的UI更新
  - 测试皮肤解锁状态在离线模式下的处理

## 测试场景

### 1. 离线功能测试场景

1. **初始安装场景**
   - 用户首次安装应用，无网络连接
   - 验证应用能够正常初始化本地数据库
   - 验证用户能够使用核心功能

2. **离线数据操作场景**
   - 用户在离线状态下创建情绪记录
   - 用户在离线状态下查看历史记录
   - 用户在离线状态下修改设置

3. **离线状态持久化场景**
   - 用户在离线状态下使用应用
   - 关闭应用后重新打开
   - 验证之前的数据和设置是否保持不变

### 2. 数据同步测试场景

1. **首次同步场景**
   - 用户在离线状态下创建多条记录
   - 用户开启网络连接并启用同步
   - 验证本地数据是否成功上传到云端

2. **增量同步场景**
   - 用户已完成首次同步
   - 用户在本地创建新记录
   - 用户触发同步
   - 验证只有新记录被上传到云端

3. **双向同步场景**
   - 用户在设备A上创建记录并同步
   - 用户在设备B上同步并查看记录
   - 用户在设备B上修改记录并同步
   - 用户在设备A上同步并验证修改是否生效

4. **冲突解决场景**
   - 用户在设备A上修改记录但未同步
   - 用户在设备B上修改同一记录并同步
   - 用户在设备A上同步
   - 验证冲突解决策略是否正确应用

### 3. 网络状态变化测试场景

1. **网络中断场景**
   - 用户在同步过程中网络中断
   - 验证应用是否优雅地处理中断
   - 验证用户是否收到适当的错误提示

2. **网络恢复场景**
   - 用户在离线状态下创建记录
   - 网络恢复后，验证应用是否提示用户同步
   - 验证同步是否成功完成

### 4. 用户认证与VIP状态测试场景

1. **用户登录场景**
   - 用户登录账户
   - 验证VIP状态是否正确获取
   - 验证VIP特定功能是否解锁

2. **VIP状态变化场景**
   - 用户购买VIP
   - 验证VIP状态是否实时更新
   - 验证VIP特定功能是否立即解锁

3. **用户登出场景**
   - 用户登出账户
   - 验证VIP特定功能是否被锁定
   - 验证用户数据是否按预期处理（保留或清除）

### 5. 皮肤解锁测试场景

1. **皮肤购买场景**
   - 用户购买新皮肤
   - 验证皮肤解锁状态是否更新
   - 验证用户是否能够使用新皮肤

2. **多设备皮肤同步场景**
   - 用户在设备A上购买皮肤
   - 用户在设备B上登录
   - 验证已购买的皮肤是否在设备B上可用

3. **离线皮肤使用场景**
   - 用户在在线状态下购买皮肤
   - 用户切换到离线状态
   - 验证用户是否仍能使用已购买的皮肤

## 测试优先级

### 高优先级

1. 离线数据存储基本功能
2. 数据同步基本功能
3. 网络状态变化处理
4. 用户认证基本功能

### 中优先级

1. 增量同步和冲突解决
2. VIP状态同步
3. 皮肤解锁状态同步
4. 多设备数据一致性

### 低优先级

1. 边缘情况处理
2. 性能优化测试
3. 大数据量同步测试

## 测试实施计划

1. **第一阶段**：完成离线存储服务的单元测试
2. **第二阶段**：完成数据同步服务的单元测试
3. **第三阶段**：完成钩子和上下文的单元测试
4. **第四阶段**：完成集成测试
5. **第五阶段**：完成端到端测试

## 测试覆盖率目标

- 离线存储服务：90%+
- 数据同步服务：85%+
- 钩子和上下文：80%+
- 集成测试：70%+
- 整体覆盖率：80%+
