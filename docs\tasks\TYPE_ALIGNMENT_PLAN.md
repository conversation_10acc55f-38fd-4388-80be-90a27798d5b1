# 类型定义对齐计划

## 🎯 目标

根据 `public/seeds/schema/full.sql` 的数据库架构，更新 `src/types` 中的类型定义，确保类型与数据库结构100%对齐。

## 📊 当前问题分析

### ❌ 需要修复的问题

1. **多语言处理不一致**:
   - 现有类型仍使用 `localized_names` JSON 字段
   - 应该移除 JSON 字段，使用独立的翻译接口

2. **字段不匹配**:
   - 数据库字段与类型定义不一致
   - 缺少新增的字段（如 `updated_at`）
   - 字段类型不匹配（如日期字段）

3. **缺失的类型定义**:
   - 缺少翻译相关的接口
   - 缺少新增表的类型定义

## 🔧 修复计划

### 1. 创建统一的翻译接口

```typescript
// 基础翻译接口
export interface Translation {
  entityId: string;
  languageCode: string;
  translatedName: string;
  translatedDescription?: string;
}

// 可翻译实体基接口
export interface TranslatableEntity {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  // 运行时翻译字段
  localizedName?: string;
  localizedDescription?: string;
  translations?: Translation[];
}
```

### 2. 更新现有类型

#### Emotion 类型
```typescript
export interface Emotion extends TranslatableEntity {
  id: string;
  name: string;
  description?: string;
  emoji?: string;
  color?: string;
  tier_level?: number;
  parent_id?: string;
  keywords?: string; // JSON array
  image_url?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  is_deleted: boolean;
  deleted_at?: string;
  // 移除: localized_names, emoji_sets
}
```

#### EmotionDataSet 类型
```typescript
export interface EmotionDataSet extends TranslatableEntity {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  is_default: boolean;
  is_system_default: boolean;
  default_emoji_set_id?: string;
  supported_languages?: string; // JSON array
  created_at: string;
  updated_at: string;
  created_by?: string;
  // 移除: localized_names, localized_descriptions
}
```

#### EmotionDataSetTier 类型
```typescript
export interface EmotionDataSetTier extends TranslatableEntity {
  id: string;
  emotion_data_set_id: string;
  name: string;
  level: number;
  parent_tier_id?: string;
  color?: string;
  description?: string;
  created_at: string;
  updated_at: string;
  // 移除: localized_names
}
```

### 3. 新增类型定义

#### EmojiSet 类型
```typescript
export interface EmojiSet extends TranslatableEntity {
  id: string;
  name: string;
  description?: string;
  type: 'unicode' | 'image' | 'svg' | 'animated';
  is_default: boolean;
  is_system: boolean;
  is_unlocked: boolean;
  price?: number;
  preview_image?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}
```

#### EmojiItem 类型
```typescript
export interface EmojiItem {
  id: string;
  emoji_set_id: string;
  emotion_id: string;
  unicode_char?: string;
  image_url?: string;
  svg_path?: string;
  animation_url?: string;
  alt_text?: string;
  created_at: string;
}
```

#### Skin 类型
```typescript
export interface Skin extends TranslatableEntity {
  id: string;
  name: string;
  description?: string;
  category?: string;
  version?: string;
  tags?: string; // JSON array
  preview_image_light?: string;
  preview_image_dark?: string;
  is_premium: boolean;
  is_unlocked: boolean;
  unlock_conditions?: string; // JSON object
  created_at: string;
  updated_at: string;
  author?: string;
  supported_content_modes?: string; // JSON array
  supported_view_types?: string; // JSON array
  supported_render_engines?: string; // JSON array
  config: string; // JSON object - SkinConfig
}
```

#### Tag 类型
```typescript
export interface Tag extends TranslatableEntity {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}
```

#### UILabel 类型
```typescript
export interface UILabel extends TranslatableEntity {
  id: string;
  key: string;
  name: string;
  description?: string;
  category?: string;
  created_at: string;
  updated_at: string;
}
```

### 4. 更新关联类型

#### MoodEntry 类型
```typescript
export interface MoodEntry {
  id: string;
  user_id: string;
  timestamp: string; // ISO string
  emotion_data_set_id?: string;
  intensity: number;
  reflection?: string;
  tags?: string; // JSON array
  created_at: string;
  updated_at: string;
  sync_status?: string;
  server_id?: string;
  last_synced_at?: string;
}
```

#### EmotionSelection 类型
```typescript
export interface EmotionSelection {
  id: string;
  mood_entry_id: string;
  emotion_id: string;
  emotion_data_set_emotion_id?: string;
  tier_level?: number;
  intensity?: number;
  created_at: string;
}
```

## 📋 执行步骤

### Step 1: 创建基础翻译类型
- 创建 `src/types/translationTypes.ts`
- 定义基础翻译接口

### Step 2: 更新情绪相关类型
- 更新 `src/types/emotionDataTypes.ts`
- 移除 JSON 本地化字段
- 添加翻译支持

### Step 3: 更新表情相关类型
- 更新 `src/types/emojiTypes.ts`
- 对齐数据库字段

### Step 4: 更新皮肤相关类型
- 更新 `src/types/skinTypes.ts`
- 添加翻译支持

### Step 5: 更新心情相关类型
- 更新 `src/types/mood.ts`
- 对齐数据库字段

### Step 6: 创建新的类型文件
- 创建 `src/types/tagTypes.ts`
- 创建 `src/types/uiLabelTypes.ts`

### Step 7: 更新索引文件
- 更新 `src/types/index.ts`
- 导出所有新类型

## ✅ 验证标准

1. **字段对齐**: 所有字段与数据库表结构一致
2. **类型正确**: 字段类型与数据库类型匹配
3. **翻译统一**: 所有可翻译实体使用统一的翻译接口
4. **向后兼容**: 保持现有代码的兼容性
5. **编译通过**: 所有 TypeScript 编译无错误

## 🎯 预期结果

- ✅ 类型定义与数据库架构100%对齐
- ✅ 统一的多语言类型支持
- ✅ 完整的类型覆盖
- ✅ 类型安全的开发体验
- ✅ 自动代码提示和错误检查

让我们开始执行这个计划！🚀
