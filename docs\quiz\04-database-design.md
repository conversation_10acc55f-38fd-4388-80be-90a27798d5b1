# Quiz系统数据库设计

## 🎯 设计原则

### 1. 数据与展现分离
- **数据表**: 存储纯粹的量表逻辑和内容
- **配置表**: 存储用户个性化展现配置
- **快照表**: 存储运行时配置合并结果

### 2. 规范化设计
- 避免数据冗余
- 保持数据一致性
- 支持高效查询

### 3. 扩展性考虑
- 支持未来功能扩展
- 灵活的JSON字段设计
- 版本化支持

## 🗄️ 核心表结构

### 1. 量表包表 (quiz_packs) - 量表定义

```sql
-- 量表包定义表 (量表的基本信息和配置)
CREATE TABLE IF NOT EXISTS quiz_packs (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    version TEXT NOT NULL DEFAULT '1.0.0',

    -- 量表分类和类型 (支持主流和非主流quiz)
    category TEXT NOT NULL, -- 'daily', 'assessment', 'therapy', 'research', 'entertainment', 'education'
    quiz_type TEXT NOT NULL, -- 'emotion_wheel', 'traditional_scale', 'personality_test', 'iq_test', 'knowledge_quiz', 'survey', 'game_quiz', 'mixed'
    difficulty_level TEXT DEFAULT 'regular', -- 'beginner', 'regular', 'advanced', 'expert'
    quiz_style TEXT, -- 'mainstream', 'alternative', 'experimental', 'cultural_specific'

    -- 量表逻辑配置 (JSON: QuizLogicConfig)
    quiz_logic_config TEXT NOT NULL,

    -- 默认展现建议 (JSON: DefaultPresentationHints, 可选)
    default_presentation_hints TEXT,

    -- 元数据 (JSON: QuizPackMetadata)
    metadata TEXT NOT NULL,

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,
    sort_order INTEGER DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_quiz_packs_category_type ON quiz_packs(category, quiz_type);
CREATE INDEX idx_quiz_packs_active ON quiz_packs(is_active, sort_order);
CREATE INDEX idx_quiz_packs_difficulty ON quiz_packs(difficulty_level);
CREATE INDEX idx_quiz_packs_style ON quiz_packs(quiz_style);
CREATE INDEX idx_quiz_packs_type_style ON quiz_packs(quiz_type, quiz_style);
```

### 2. 问题表 (quiz_questions) - 独立的问题管理

```sql
-- 问题定义表 (独立的问题实体)
CREATE TABLE IF NOT EXISTS quiz_questions (
    id TEXT PRIMARY KEY NOT NULL,
    pack_id TEXT NOT NULL,

    -- 问题基本信息
    question_text TEXT NOT NULL,
    question_text_localized TEXT, -- JSON: 多语言文本
    question_type TEXT NOT NULL, -- 'single_choice', 'multiple_choice', 'scale_rating', 'emotion_wheel', 'text_input', 'number_input', 'date_input', 'slider', 'ranking', 'matrix', 'image_choice', 'audio_choice', 'video_choice', 'drawing', 'file_upload'

    -- 问题顺序和分组
    question_order INTEGER NOT NULL,
    question_group TEXT, -- 可选的问题分组 (如: '脏腑功能', '证素状态')
    tier_level INTEGER DEFAULT 1, -- 问题层级 (1, 2, 3...)

    -- 问题配置
    question_config TEXT, -- JSON: 问题特定配置
    validation_rules TEXT, -- JSON: 验证规则
    scoring_config TEXT, -- JSON: 评分配置

    -- 依赖关系
    parent_question_id TEXT, -- 父问题ID (用于层级问题)
    dependency_rules TEXT, -- JSON: 依赖规则

    -- 状态管理
    is_required BOOLEAN DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- 约束
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_question_id) REFERENCES quiz_questions(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    UNIQUE(pack_id, question_order)
);

-- 索引
CREATE INDEX idx_quiz_questions_pack_order ON quiz_questions(pack_id, question_order);
CREATE INDEX idx_quiz_questions_type ON quiz_questions(question_type);
CREATE INDEX idx_quiz_questions_group ON quiz_questions(pack_id, question_group);
CREATE INDEX idx_quiz_questions_tier ON quiz_questions(pack_id, tier_level);
CREATE INDEX idx_quiz_questions_parent ON quiz_questions(parent_question_id);
```

### 3. 问题选项表 (quiz_question_options) - 支持各类Quiz答案类型

```sql
-- 问题选项表 (纯数据，支持各类quiz的答案类型)
CREATE TABLE IF NOT EXISTS quiz_question_options (
    id TEXT PRIMARY KEY NOT NULL,
    question_id TEXT NOT NULL,

    -- 选项基本信息
    option_text TEXT NOT NULL,
    option_text_localized TEXT, -- JSON: 多语言文本
    option_value TEXT NOT NULL, -- 选项值 (用于数据处理)

    -- 选项类型和用途
    option_type TEXT NOT NULL DEFAULT 'choice', -- 'choice', 'scale_point', 'ranking_item', 'matrix_row', 'matrix_column'

    -- 选项顺序和分组
    option_order INTEGER NOT NULL,
    option_group TEXT, -- 可选的选项分组

    -- 评分配置 (用于量表类quiz)
    scoring_value REAL, -- 评分值
    scoring_weight REAL DEFAULT 1.0, -- 评分权重
    min_value REAL, -- 最小值 (用于slider, scale等)
    max_value REAL, -- 最大值 (用于slider, scale等)
    step_value REAL, -- 步长值 (用于slider等)

    -- 多媒体内容 (用于image_choice, audio_choice等)
    media_url TEXT, -- 媒体文件URL
    media_type TEXT, -- 'image', 'audio', 'video'
    media_thumbnail_url TEXT, -- 缩略图URL
    media_alt_text TEXT, -- 替代文本

    -- 矩阵配置 (用于matrix类型)
    matrix_row_id TEXT, -- 矩阵行ID
    matrix_column_id TEXT, -- 矩阵列ID

    -- 内容复用 (可选)
    reference_pack_id TEXT, -- 引用其他Quiz包
    reference_option_id TEXT, -- 引用其他选项

    -- 选项元数据 (纯数据)
    metadata TEXT, -- JSON: 其他元数据
    tags TEXT, -- JSON数组: 标签

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,

    -- 约束
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
    FOREIGN KEY (reference_pack_id) REFERENCES quiz_packs(id) ON DELETE SET NULL,
    FOREIGN KEY (reference_option_id) REFERENCES quiz_question_options(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    UNIQUE(question_id, option_order)
);

-- 索引
CREATE INDEX idx_question_options_question_order ON quiz_question_options(question_id, option_order);
CREATE INDEX idx_question_options_value ON quiz_question_options(option_value);
CREATE INDEX idx_question_options_type ON quiz_question_options(option_type);
CREATE INDEX idx_question_options_matrix ON quiz_question_options(matrix_row_id, matrix_column_id);
```

### 3.1 各类Quiz的选项配置说明

#### 选择类Quiz (需要选项)
```sql
-- 单选题/多选题选项
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, option_order)
VALUES ('q001', 'A选项', 'option_a', 'choice', 1);

-- 情绪轮盘选项
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, option_order, scoring_value)
VALUES ('q002', '快乐', 'happy', 'choice', 1, 5);

-- 图片选择选项
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, media_url, media_type, option_order)
VALUES ('q003', '图片A', 'image_a', 'choice', '/images/option_a.jpg', 'image', 1);
```

#### 评分类Quiz (需要刻度选项)
```sql
-- 量表评分选项 (1-5分)
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, scoring_value, option_order)
VALUES
    ('q004', '完全不同意', 'strongly_disagree', 'scale_point', 1, 1),
    ('q004', '不同意', 'disagree', 'scale_point', 2, 2),
    ('q004', '中立', 'neutral', 'scale_point', 3, 3),
    ('q004', '同意', 'agree', 'scale_point', 4, 4),
    ('q004', '完全同意', 'strongly_agree', 'scale_point', 5, 5);

-- 滑块配置 (0-100)
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, min_value, max_value, step_value, option_order)
VALUES ('q005', '满意度', 'satisfaction_level', 'scale_point', 0, 100, 1, 1);
```

#### 排序类Quiz (需要排序项)
```sql
-- 排序题选项
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, option_order)
VALUES
    ('q006', '家庭', 'family', 'ranking_item', 1),
    ('q006', '事业', 'career', 'ranking_item', 2),
    ('q006', '健康', 'health', 'ranking_item', 3),
    ('q006', '财富', 'wealth', 'ranking_item', 4);
```

#### 矩阵类Quiz (需要行列配置)
```sql
-- 矩阵题 - 行标题
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, matrix_row_id, option_order)
VALUES
    ('q007', '产品质量', 'product_quality', 'matrix_row', 'row_1', 1),
    ('q007', '客户服务', 'customer_service', 'matrix_row', 'row_2', 2);

-- 矩阵题 - 列标题
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, matrix_column_id, scoring_value, option_order)
VALUES
    ('q007', '很差', 'very_poor', 'matrix_column', 'col_1', 1, 1),
    ('q007', '一般', 'average', 'matrix_column', 'col_2', 3, 2),
    ('q007', '很好', 'very_good', 'matrix_column', 'col_3', 5, 3);
```

#### 输入类Quiz (不需要预设选项)
```sql
-- 文本输入题 - 可以定义输入约束
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, metadata, option_order)
VALUES ('q008', '文本输入约束', 'text_constraint', 'input_constraint',
        '{"input_type":"text","max_length":500,"required":true,"placeholder":"请输入您的想法..."}', 1);

-- 数字输入题 - 定义数值范围
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, min_value, max_value, option_order)
VALUES ('q009', '年龄输入', 'age_input', 'input_constraint', 0, 120, 1);

-- 文件上传题 - 定义文件类型约束
INSERT INTO quiz_question_options (question_id, option_text, option_value, option_type, metadata, option_order)
VALUES ('q010', '文件上传约束', 'file_constraint', 'input_constraint',
        '{"allowed_types":["image/jpeg","image/png"],"max_size_mb":10}', 1);
```

### 4. Quiz包就是数据集 - 统一概念

由于 `quiz_pack` 和 `emotion_data_set` 本质上是同一个概念，我们不需要单独的关联表。
Quiz包本身就包含了所有的测评内容：问题、选项、逻辑、展现配置等。

如果需要引用其他Quiz包的内容，可以通过以下方式：

```sql
-- 在quiz_questions表中添加引用字段 (可选)
ALTER TABLE quiz_questions ADD COLUMN reference_pack_id TEXT;
ALTER TABLE quiz_questions ADD COLUMN reference_question_id TEXT;

-- 在quiz_question_options表中添加引用字段 (可选)
ALTER TABLE quiz_question_options ADD COLUMN reference_pack_id TEXT;
ALTER TABLE quiz_question_options ADD COLUMN reference_option_id TEXT;

-- 外键约束
ALTER TABLE quiz_questions ADD CONSTRAINT fk_reference_pack
    FOREIGN KEY (reference_pack_id) REFERENCES quiz_packs(id) ON DELETE SET NULL;
ALTER TABLE quiz_questions ADD CONSTRAINT fk_reference_question
    FOREIGN KEY (reference_question_id) REFERENCES quiz_questions(id) ON DELETE SET NULL;
```

这样可以支持：
- **问题复用**: 一个Quiz包可以引用另一个Quiz包的问题
- **选项复用**: 一个问题可以引用另一个问题的选项
- **内容继承**: 支持基于现有内容创建新的Quiz包

### 2. 用户展现配置表 (user_presentation_configs) - 纯展现

```sql
-- 用户展现配置表 (6层个性化配置)
CREATE TABLE IF NOT EXISTS user_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    config_name TEXT NOT NULL DEFAULT 'default',

    -- 6层展现配置 (JSON: UserPresentationConfig)
    presentation_config TEXT NOT NULL,

    -- 配置元数据
    config_version TEXT DEFAULT '2.0',
    personalization_level INTEGER DEFAULT 0, -- 0-100

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);

-- 索引
CREATE INDEX idx_user_presentation_configs_user ON user_presentation_configs(user_id, is_active);
CREATE INDEX idx_user_presentation_configs_default ON user_presentation_configs(user_id, is_default);
```

### 3. 量表特定展现覆盖表 (pack_presentation_overrides)

```sql
-- 量表特定展现覆盖表
CREATE TABLE IF NOT EXISTS pack_presentation_overrides (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,

    -- 展现覆盖配置 (JSON: PackSpecificPresentationOverride)
    presentation_overrides TEXT,

    -- 层级展现覆盖 (JSON: TierPresentationOverrides)
    tier_presentation_overrides TEXT,

    -- 覆盖元数据
    override_reason TEXT, -- 'user_preference', 'accessibility_need', 'performance_optimization'
    override_priority INTEGER DEFAULT 1, -- 1-10

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    UNIQUE(user_id, pack_id)
);

-- 索引
CREATE INDEX idx_pack_overrides_user_pack ON pack_presentation_overrides(user_id, pack_id);
CREATE INDEX idx_pack_overrides_active ON pack_presentation_overrides(is_active);
```

### 4. 量表会话表 (quiz_sessions)

```sql
-- 量表会话管理表
CREATE TABLE IF NOT EXISTS quiz_sessions (
    id TEXT PRIMARY KEY NOT NULL,
    pack_id TEXT NOT NULL,
    user_id TEXT NOT NULL,

    -- 会话状态
    status TEXT NOT NULL DEFAULT 'INITIATED',
    -- 'INITIATED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABORTED'
    current_question_index INTEGER DEFAULT 0,
    current_question_id TEXT,
    current_tier_level INTEGER,

    -- 时间跟踪
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_active_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    total_duration_seconds INTEGER,

    -- 进度跟踪
    total_questions INTEGER,
    answered_questions INTEGER DEFAULT 0,
    skipped_questions INTEGER DEFAULT 0,
    completion_percentage REAL DEFAULT 0,

    -- 会话元数据
    session_type TEXT DEFAULT 'standard', -- 'standard', 'practice', 'assessment'
    device_info TEXT, -- JSON: 设备信息

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (current_question_id) REFERENCES quiz_questions(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_quiz_sessions_user_status ON quiz_sessions(user_id, status);
CREATE INDEX idx_quiz_sessions_pack_created ON quiz_sessions(pack_id, created_at);
CREATE INDEX idx_quiz_sessions_active ON quiz_sessions(status, last_active_time);
```



### 5. 会话展现配置快照表 (quiz_session_presentation_configs)

```sql
-- 会话展现配置快照表 (整合user_config的功能，因为user_config表后续可能废弃)
CREATE TABLE IF NOT EXISTS quiz_session_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL,
    user_id TEXT NOT NULL,

    -- 基础配置快照
    config_name TEXT NOT NULL,
    config_version TEXT NOT NULL DEFAULT '2.0',

    -- 完整的展现配置快照 (从user_presentation_configs复制)
    presentation_config TEXT NOT NULL, -- JSON: 完整的6层个性化配置

    -- 会话特定配置
    session_overrides TEXT, -- JSON: 会话特定的配置覆盖

    -- 配置元数据
    personalization_level INTEGER DEFAULT 50, -- 个性化程度 0-100
    config_source TEXT DEFAULT 'user_preference', -- 'user_preference', 'system_default', 'session_custom'

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE(session_id) -- 每个会话只有一个展现配置快照
);

-- 索引
CREATE INDEX idx_session_presentation_configs_session ON quiz_session_presentation_configs(session_id);
CREATE INDEX idx_session_presentation_configs_user ON quiz_session_presentation_configs(user_id);
CREATE INDEX idx_session_presentation_configs_source ON quiz_session_presentation_configs(config_source);
```

### 6. 量表答案记录表 (quiz_answers) - 修正版

```sql
-- 量表答案记录表 (纯数据记录，关联会话展现配置)
CREATE TABLE IF NOT EXISTS quiz_answers (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL,
    question_id TEXT NOT NULL, -- 关联到quiz_questions表
    session_presentation_config_id TEXT, -- 关联会话展现配置快照 (可选)

    -- 答案内容 (纯数据)
    selected_option_ids TEXT, -- JSON数组: 选中的选项ID列表 (支持多选)
    answer_value TEXT, -- 答案值 (可以是选项值、评分值等)
    answer_text TEXT, -- 答案文本 (用于显示)

    -- 评分相关 (如果是量表类问题)
    scale_value REAL, -- 量表评分值
    scale_label TEXT, -- 量表标签 (如: '无', '轻', '中', '重')

    -- 答案元数据 (纯数据)
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
    response_time_ms INTEGER, -- 响应时间(毫秒)
    is_skipped BOOLEAN DEFAULT 0,
    is_revised BOOLEAN DEFAULT 0, -- 是否修改过答案
    revision_count INTEGER DEFAULT 0,

    -- 时间戳
    answered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE RESTRICT,
    FOREIGN KEY (session_presentation_config_id) REFERENCES quiz_session_presentation_configs(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_quiz_answers_session_question ON quiz_answers(session_id, question_id);
CREATE INDEX idx_quiz_answers_answered_at ON quiz_answers(answered_at);
CREATE INDEX idx_quiz_answers_scale_value ON quiz_answers(scale_value);
CREATE INDEX idx_quiz_answers_presentation_config ON quiz_answers(session_presentation_config_id);
```

### 7. 量表结果表 (quiz_results)

```sql
-- 量表结果表
CREATE TABLE IF NOT EXISTS quiz_results (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL UNIQUE,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,

    -- 结果状态
    status TEXT NOT NULL DEFAULT 'COMPLETED',
    -- 'COMPLETED', 'PARTIALLY_COMPLETED', 'TIMEOUT'
    completion_percentage REAL CHECK (completion_percentage BETWEEN 0 AND 100),

    -- 基础统计
    total_duration_seconds INTEGER,
    average_response_time_ms INTEGER,
    total_questions INTEGER,
    answered_questions INTEGER,
    skipped_questions INTEGER,

    -- 情绪分析结果 (JSON)
    dominant_emotions TEXT, -- JSON array: 主导情绪
    emotion_pattern_analysis TEXT, -- JSON: 情绪模式分析
    intensity_analysis TEXT, -- JSON: 强度分析
    category_distribution TEXT, -- JSON: 类别分布
    temporal_patterns TEXT, -- JSON: 时间模式

    -- 评分和标签
    overall_score REAL,
    emotional_stability_score REAL,
    emotional_complexity_score REAL,
    generated_tags TEXT, -- JSON array: 生成的标签

    -- 建议和摘要
    recommendations TEXT, -- JSON: 推荐建议
    narrative_summary TEXT, -- 叙述性摘要
    visual_summary_config TEXT, -- JSON: 可视化摘要配置

    -- AI分析状态
    ai_analysis_status TEXT DEFAULT 'NOT_REQUESTED',
    -- 'NOT_REQUESTED', 'PENDING', 'COMPLETED', 'FAILED'
    ai_analysis_id TEXT,
    ai_analysis_version TEXT,

    -- 结果元数据
    result_version TEXT DEFAULT '2.0',
    analysis_algorithm_version TEXT,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE RESTRICT
);

-- 索引
CREATE INDEX idx_quiz_results_user_created ON quiz_results(user_id, created_at);
CREATE INDEX idx_quiz_results_pack ON quiz_results(pack_id);
CREATE INDEX idx_quiz_results_status ON quiz_results(status);
CREATE INDEX idx_quiz_results_ai_analysis ON quiz_results(ai_analysis_status);
```

## 🔧 增强现有表结构

### 1. 情绪数据集层级表增强

```sql
-- 为emotion_data_set_tiers表添加量表相关字段
ALTER TABLE emotion_data_set_tiers ADD COLUMN question_text_key TEXT;
ALTER TABLE emotion_data_set_tiers ADD COLUMN question_type TEXT DEFAULT 'EMOTION_WHEEL_SELECT';
ALTER TABLE emotion_data_set_tiers ADD COLUMN question_config TEXT; -- JSON: 问题特定配置
ALTER TABLE emotion_data_set_tiers ADD COLUMN ui_hints TEXT; -- JSON: UI呈现提示
ALTER TABLE emotion_data_set_tiers ADD COLUMN navigation_config TEXT; -- JSON: 导航配置
ALTER TABLE emotion_data_set_tiers ADD COLUMN validation_rules TEXT; -- JSON: 验证规则

-- 添加索引
CREATE INDEX idx_emotion_tiers_question_type ON emotion_data_set_tiers(question_type);
```

### 2. 用户配置表增强

```sql
-- 为user_configs表添加量表相关配置
ALTER TABLE user_configs ADD COLUMN quiz_preferences TEXT; -- JSON: 量表偏好配置
ALTER TABLE user_configs ADD COLUMN default_quiz_pack_id TEXT;
ALTER TABLE user_configs ADD COLUMN quiz_history_retention_days INTEGER DEFAULT 365;
ALTER TABLE user_configs ADD COLUMN enable_ai_analysis BOOLEAN DEFAULT 0;
ALTER TABLE user_configs ADD COLUMN privacy_settings TEXT; -- JSON: 隐私设置

-- 添加外键约束
ALTER TABLE user_configs ADD CONSTRAINT fk_default_quiz_pack
    FOREIGN KEY (default_quiz_pack_id) REFERENCES quiz_packs(id) ON DELETE SET NULL;
```

## 📊 分析和推荐表结构

### 1. 情绪模式分析表

```sql
-- 情绪模式分析表
CREATE TABLE IF NOT EXISTS emotion_pattern_analyses (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    result_id TEXT NOT NULL,

    -- 分析类型
    analysis_type TEXT NOT NULL, -- 'single_session', 'trend_analysis', 'comparative'
    analysis_period_start TIMESTAMP,
    analysis_period_end TIMESTAMP,

    -- 模式数据 (JSON)
    dominant_emotions TEXT, -- JSON: 主导情绪列表
    emotion_transitions TEXT, -- JSON: 情绪转换模式
    intensity_patterns TEXT, -- JSON: 强度模式
    temporal_patterns TEXT, -- JSON: 时间模式
    category_distribution TEXT, -- JSON: 类别分布

    -- 统计指标
    emotional_stability_index REAL,
    emotional_complexity_index REAL,
    emotional_variability_index REAL,
    pattern_consistency_score REAL,

    -- 趋势分析
    trend_direction TEXT, -- 'improving', 'declining', 'stable', 'fluctuating'
    trend_confidence REAL CHECK (trend_confidence BETWEEN 0 AND 1),

    -- 分析元数据
    analysis_algorithm_version TEXT,
    analysis_confidence REAL,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES quiz_results(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_emotion_analyses_user_type ON emotion_pattern_analyses(user_id, analysis_type);
CREATE INDEX idx_emotion_analyses_period ON emotion_pattern_analyses(analysis_period_start, analysis_period_end);
```

### 2. 推荐建议表

```sql
-- 推荐建议表
CREATE TABLE IF NOT EXISTS recommendations (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    result_id TEXT,
    analysis_id TEXT,

    -- 推荐类型
    recommendation_type TEXT NOT NULL,
    -- 'therapy_technique', 'lifestyle_change', 'quiz_suggestion', 'config_optimization'
    category TEXT, -- 'immediate', 'short_term', 'long_term'
    priority_level INTEGER CHECK (priority_level BETWEEN 1 AND 5),

    -- 推荐内容
    title TEXT NOT NULL,
    description TEXT,
    detailed_explanation TEXT,
    action_steps TEXT, -- JSON array: 具体行动步骤

    -- 推荐依据
    reasoning TEXT, -- 推荐理由
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
    evidence_data TEXT, -- JSON: 支持证据

    -- 资源链接
    resources TEXT, -- JSON: 相关资源链接
    external_links TEXT, -- JSON: 外部链接

    -- 用户反馈
    user_rating INTEGER CHECK (user_rating BETWEEN 1 AND 5),
    user_feedback TEXT,
    is_helpful BOOLEAN,
    is_implemented BOOLEAN DEFAULT 0,
    implementation_date TIMESTAMP,

    -- 推荐元数据
    recommendation_algorithm_version TEXT,
    personalization_level INTEGER, -- 0-100

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    expires_at TIMESTAMP,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES quiz_results(id) ON DELETE CASCADE,
    FOREIGN KEY (analysis_id) REFERENCES emotion_pattern_analyses(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_recommendations_user_active ON recommendations(user_id, is_active);
CREATE INDEX idx_recommendations_type_priority ON recommendations(recommendation_type, priority_level);
CREATE INDEX idx_recommendations_expires ON recommendations(expires_at);
```

## 🔧 数据库维护

### 1. 清理策略

```sql
-- 清理过期会话 (超过30天的非活跃会话)
DELETE FROM quiz_sessions
WHERE status IN ('ABORTED', 'COMPLETED')
  AND updated_at < datetime('now', '-30 days');

-- 清理过期推荐 (已过期的推荐)
DELETE FROM recommendations
WHERE expires_at IS NOT NULL
  AND expires_at < datetime('now');

-- 清理临时配置快照 (超过7天的快照)
DELETE FROM session_presentation_snapshots
WHERE created_at < datetime('now', '-7 days');
```

### 2. 性能优化

```sql
-- 分析表使用情况
ANALYZE;

-- 重建索引 (定期维护)
REINDEX;

-- 清理数据库
VACUUM;
```

## 🏗️ 清理后的架构说明 (数据与展现分离)

### 核心概念澄清

#### 1. Quiz Pack = Emotion Data Set
- **quiz_pack** 和 **emotion_data_set** 本质上是同一个概念
- **emotion_data_set** = 旧叫法，强调数据集合
- **quiz_pack** = 新叫法，强调测评包
- 它们都是**测评内容的完整集合**，包含问题、选项、逻辑等**纯数据**

#### 2. 数据与展现分离原则
- **数据层**: Quiz包、问题、选项只包含纯数据，不包含任何展现配置
- **展现层**: 通过个性化配置系统处理所有UI展现相关内容
- **content_mode**: 由前端根据用户配置动态决定，不存储在数据库中
- **样式配置**: 通过quiz_session_presentation_configs表管理，与数据完全分离

#### 3. 全面的Quiz类型支持
- **主流Quiz**: emotion_wheel, personality_test, iq_test, knowledge_quiz, survey
- **非主流Quiz**: text_input, drawing, ranking, matrix, multimedia_choice
- **混合类型**: 支持在同一个Quiz包中混合多种问题类型
- **quiz_style字段**: 区分mainstream, alternative, experimental, cultural_specific

#### 4. 独立的问题管理系统
- **quiz_questions表**: 独立管理所有问题 (纯数据)
- **quiz_question_options表**: 支持各类Quiz的答案类型 (纯数据)
- **option_type字段**: choice, scale_point, ranking_item, matrix_row, matrix_column, input_constraint
- **支持管理系统**: 可以通过后台管理系统动态添加、编辑问题和选项
- **移除字段**: emotion_id, emotion_tier_level, 所有展现相关配置

#### 5. 会话展现配置快照系统
- **quiz_session_presentation_configs表**: 整合user_config功能，作为会话展现配置快照
- **数据展现分离**: quiz_answers通过session_presentation_config_id关联展现配置，而不是内嵌
- **配置快照**: 每个会话创建时复制用户的展现配置，确保一致性
- **user_config废弃**: 后续可能废弃user_config表，功能迁移到此表

### 管理系统支持

#### Quiz包管理 (支持主流和非主流quiz类型)
```sql
-- 示例1: 情绪追踪器 (主流类型)
INSERT INTO quiz_packs (id, name, category, quiz_type, quiz_style, difficulty_level, quiz_logic_config, metadata)
VALUES ('mood-tracker-clean', '情绪追踪器', 'daily', 'emotion_wheel', 'mainstream', 'regular',
        '{"question_flow":{"type":"hierarchical_progression"}}',
        '{"estimated_duration_minutes":5,"tags":["情绪追踪","日常记录"]}');

-- 示例2: 性格测试 (主流类型)
INSERT INTO quiz_packs (id, name, category, quiz_type, quiz_style, difficulty_level, quiz_logic_config, metadata)
VALUES ('personality-test-big5', '大五人格测试', 'assessment', 'personality_test', 'mainstream', 'advanced',
        '{"question_flow":{"type":"category_based"},"scoring":{"method":"factor_analysis"}}',
        '{"estimated_duration_minutes":20,"tags":["性格","心理测评","科学"]}');

-- 示例3: 知识竞赛 (娱乐类型)
INSERT INTO quiz_packs (id, name, category, quiz_type, quiz_style, difficulty_level, quiz_logic_config, metadata)
VALUES ('trivia-game-history', '历史知识竞赛', 'entertainment', 'knowledge_quiz', 'mainstream', 'regular',
        '{"question_flow":{"type":"random_selection"},"time_limit":30,"lives":3}',
        '{"estimated_duration_minutes":15,"tags":["历史","知识","游戏"]}');

-- 示例4: 创意测试 (非主流类型)
INSERT INTO quiz_packs (id, name, category, quiz_type, quiz_style, difficulty_level, quiz_logic_config, metadata)
VALUES ('creativity-test-alt', '创意思维测试', 'assessment', 'mixed', 'alternative', 'expert',
        '{"question_flow":{"type":"adaptive"},"multimedia_support":true}',
        '{"estimated_duration_minutes":30,"tags":["创意","艺术","非传统"]}');

-- 为不同类型的问题添加示例
-- 情绪轮盘问题 (choice类型选项)
INSERT INTO quiz_questions (id, pack_id, question_text, question_type, question_order)
VALUES ('q001', 'mood-tracker-clean', '您现在的情绪是什么？', 'emotion_wheel', 1);

INSERT INTO quiz_question_options (id, question_id, option_text, option_value, option_type, option_order, scoring_value)
VALUES ('opt001', 'q001', '快乐', 'happy', 'choice', 1, 5);

-- 量表评分问题 (scale_point类型选项)
INSERT INTO quiz_questions (id, pack_id, question_text, question_type, question_order)
VALUES ('q002', 'personality-test-big5', '我经常感到焦虑', 'scale_rating', 1);

INSERT INTO quiz_question_options (id, question_id, option_text, option_value, option_type, option_order, scoring_value)
VALUES ('opt002', 'q002', '完全同意', 'strongly_agree', 'scale_point', 5, 5);

-- 单选题 (choice类型选项)
INSERT INTO quiz_questions (id, pack_id, question_text, question_type, question_order)
VALUES ('q003', 'trivia-game-history', '中国历史上第一个皇帝是谁？', 'single_choice', 1);

INSERT INTO quiz_question_options (id, question_id, option_text, option_value, option_type, option_order, scoring_value)
VALUES ('opt003', 'q003', '秦始皇', 'qin_shihuang', 'choice', 1, 1);

-- 文本输入题 (input_constraint类型)
INSERT INTO quiz_questions (id, pack_id, question_text, question_type, question_order)
VALUES ('q004', 'creativity-test-alt', '请用三个词描述您看到这幅画的感受', 'text_input', 1);

INSERT INTO quiz_question_options (id, question_id, option_text, option_value, option_type, metadata, option_order)
VALUES ('opt004', 'q004', '文本输入约束', 'text_constraint', 'input_constraint',
        '{"max_length":200,"min_words":3,"max_words":3,"required":true}', 1);

-- 排序题 (ranking_item类型选项)
INSERT INTO quiz_questions (id, pack_id, question_text, question_type, question_order)
VALUES ('q005', 'creativity-test-alt', '请按重要性排序以下价值观', 'ranking', 2);

INSERT INTO quiz_question_options (id, question_id, option_text, option_value, option_type, option_order)
VALUES
    ('opt005', 'q005', '家庭', 'family', 'ranking_item', 1),
    ('opt006', 'q005', '事业', 'career', 'ranking_item', 2),
    ('opt007', 'q005', '健康', 'health', 'ranking_item', 3);
```

#### Emoji映射管理机制

基于情绪追踪问卷的三层结构设计，系统采用**4层Emoji映射机制**来管理不同Quiz包和问题的emoji展现：

##### 1. 系统默认映射 (代码层)
```typescript
// 系统内置的默认emoji映射
const SYSTEM_DEFAULT_EMOJI_MAPPING = {
  // 第一层：主要情绪 (7个)
  happy: { primary: "😊", alternatives: ["😄", "😃", "🙂", "😌", "🥰"] },
  surprised: { primary: "😲", alternatives: ["😮", "😯", "🤯", "😦"] },
  bad: { primary: "😞", alternatives: ["😔", "😟", "😕", "🙁"] },
  fearful: { primary: "😨", alternatives: ["😰", "😱", "😧", "😟"] },
  angry: { primary: "😠", alternatives: ["😡", "🤬", "😤", "😒"] },
  disgusted: { primary: "🤢", alternatives: ["🤮", "😖", "😣", "🙄"] },
  sad: { primary: "😢", alternatives: ["😭", "😞", "☹️", "😔"] },

  // 第二层：次要情绪 (41个)
  playful: { primary: "😜", alternatives: ["😋", "🤪", "😝", "🤭"] },
  content: { primary: "😌", alternatives: ["😊", "🙂", "😇", "🥰"] },
  interested: { primary: "🤔", alternatives: ["🧐", "👀", "💭", "🔍"] },

  // 第三层：具体情绪 (82个)
  aroused: { primary: "🤩", alternatives: ["⚡", "🔥", "💥", "✨"] },
  cheeky: { primary: "😏", alternatives: ["😉", "😎", "🤫", "😈"] },
  free: { primary: "🕊️", alternatives: ["🦋", "🌟", "🌈", "☁️"] },
  joyful: { primary: "😆", alternatives: ["🤣", "😂", "😁", "🎉"] }
};
```

##### 2. Quiz包默认映射 (pack_presentation_configs)
```sql
-- Quiz包级别的默认emoji映射配置
INSERT INTO pack_presentation_configs (
    pack_id, config_type, config_data
) VALUES (
    'mood_track_branching_v1',
    'default_emoji_mapping',
    '{
        "layer1_primary_emotions": {
            "happy": {"emoji": "😊", "color": "#4CAF50", "animation": "bounce"},
            "surprised": {"emoji": "😲", "color": "#FF9800", "animation": "pop"},
            "bad": {"emoji": "😞", "color": "#9E9E9E", "animation": "fade"},
            "fearful": {"emoji": "😨", "color": "#9C27B0", "animation": "tremble"},
            "angry": {"emoji": "😠", "color": "#F44336", "animation": "shake"},
            "disgusted": {"emoji": "🤢", "color": "#795548", "animation": "recoil"},
            "sad": {"emoji": "😢", "color": "#2196F3", "animation": "drop"}
        },
        "layer2_secondary_emotions": {
            "playful": {"emoji": "😜", "color": "#4CAF50", "animation": "wiggle"},
            "content": {"emoji": "😌", "color": "#4CAF50", "animation": "glow"},
            "interested": {"emoji": "🤔", "color": "#4CAF50", "animation": "think"}
        },
        "layer3_tertiary_emotions": {
            "aroused": {"emoji": "🤩", "color": "#FF6B35", "animation": "pulse"},
            "cheeky": {"emoji": "😏", "color": "#FF6B35", "animation": "wink"},
            "free": {"emoji": "🕊️", "color": "#87CEEB", "animation": "float"},
            "joyful": {"emoji": "😆", "color": "#FFD700", "animation": "celebrate"}
        }
    }'
);
```

##### 3. 用户全局映射 (user_presentation_configs)
```sql
-- 用户个人的emoji偏好设置
UPDATE user_presentation_configs SET
    presentation_config = JSON_SET(presentation_config,
        '$.layer4_view_detail.emotion_presentation.emoji_mapping',
        '{
            "happy": {
                "primary": "🥰",
                "alternatives": ["😊", "😌", "🙂", "😇"],
                "color": "#FFB6C1",
                "animation": "heart_glow"
            },
            "playful": {
                "primary": "🐶",
                "alternatives": ["🐱", "🐰", "🐼", "🦄"],
                "color": "#FF69B4",
                "animation": "bounce_cute"
            }
        }'
    )
WHERE user_id = 'user_001';
```

##### 4. 问题特定映射 (question_presentation_overrides)
```sql
-- 特定问题的emoji覆盖配置
INSERT INTO question_presentation_overrides (
    user_id, question_id, presentation_overrides
) VALUES (
    'user_001',
    'q001_primary_emotion',
    '{
        "emoji_theme": "professional",
        "emoji_mapping": {
            "happy": {"emoji": "✅", "color": "#2E8B57", "animation": "check"},
            "sad": {"emoji": "📉", "color": "#4682B4", "animation": "decline"}
        }
    }'
);
```

##### Emoji映射优先级机制

系统按以下优先级顺序解析emoji映射：

```
优先级 1: 问题特定映射 (question_presentation_overrides)
    ↓ (如果不存在)
优先级 2: 用户全局映射 (user_presentation_configs)
    ↓ (如果不存在)
优先级 3: Quiz包默认映射 (pack_presentation_configs)
    ↓ (如果不存在)
优先级 4: 系统默认映射 (代码层)
```

##### 实际使用场景示例

**场景1: 情绪追踪问卷的三层选择**
```typescript
// 用户在情绪追踪问卷中的选择流程
Session 1: 日常使用
问题1: "您当前的主要情绪是什么？"
- happy → 显示 😊 (Quiz包默认)
- sad → 显示 😢 (Quiz包默认)

问题2: "您感到快乐，具体是哪种类型的快乐？"
- playful → 显示 😜 (Quiz包默认)
- content → 显示 😌 (Quiz包默认)

问题3: "您感到顽皮，具体是哪种感觉？"
- aroused → 显示 🤩 (Quiz包默认)
- cheeky → 显示 😏 (Quiz包默认)
```

**场景2: 用户个性化emoji偏好**
```typescript
// 用户设置了个人emoji偏好
Session 2: 个性化使用
问题1: "您当前的主要情绪是什么？"
- happy → 显示 🥰 (用户全局映射覆盖)
- sad → 显示 😢 (Quiz包默认，用户未设置)

问题2: "您感到快乐，具体是哪种类型的快乐？"
- playful → 显示 🐶 (用户全局映射覆盖)
- content → 显示 😌 (Quiz包默认，用户未设置)
```

**场景3: 特定问题的专业化展现**
```typescript
// 在工作场景中，用户为第一个问题设置了专业emoji
Session 3: 专业场景
问题1: "您当前的主要情绪是什么？"
- happy → 显示 ✅ (问题特定映射，最高优先级)
- sad → 显示 📉 (问题特定映射，最高优先级)

问题2: "您感到快乐，具体是哪种类型的快乐？"
- playful → 显示 🐶 (用户全局映射，问题特定映射未设置)
- content → 显示 😌 (Quiz包默认，其他映射未设置)
```

##### 动态emoji选择器支持

系统支持用户为每个问题动态选择emoji：

```sql
-- 用户为特定问题选择不同的emoji主题
UPDATE question_presentation_overrides SET
    presentation_overrides = JSON_SET(presentation_overrides,
        '$.emoji_theme', 'animals',
        '$.emoji_mapping.happy.emoji', '🐶',
        '$.emoji_mapping.sad.emoji', '🐱'
    )
WHERE user_id = 'user_001' AND question_id = 'q001_primary_emotion';
```

##### 多主题emoji集合支持

```typescript
// 系统预定义的emoji主题集合
const EMOJI_THEMES = {
  default: {
    happy: "😊", sad: "😢", angry: "😠"
  },
  animals: {
    happy: "🐶", sad: "🐱", angry: "🦁"
  },
  nature: {
    happy: "🌞", sad: "🌧️", angry: "⛈️"
  },
  food: {
    happy: "🍦", sad: "🍋", angry: "🌶️"
  },
  professional: {
    happy: "✅", sad: "📉", angry: "⚠️"
  },
  celebration: {
    happy: "🎉", sad: "📚", angry: "🥊"
  }
};
```

### 数据查询示例

#### 获取带emoji映射的问题选项
```sql
-- 获取情绪追踪问卷的完整结构，包含emoji映射信息
WITH emoji_mappings AS (
  SELECT
    'system' as source,
    'default' as mapping_data
  UNION ALL
  SELECT
    'pack' as source,
    ppc.config_data as mapping_data
  FROM pack_presentation_configs ppc
  WHERE ppc.pack_id = 'mood_track_branching_v1'
    AND ppc.config_type = 'default_emoji_mapping'
  UNION ALL
  SELECT
    'user' as source,
    JSON_EXTRACT(upc.presentation_config, '$.layer4_view_detail.emotion_presentation.emoji_mapping') as mapping_data
  FROM user_presentation_configs upc
  WHERE upc.user_id = 'user_001'
    AND upc.is_active = 1
  UNION ALL
  SELECT
    'question' as source,
    qpo.presentation_overrides as mapping_data
  FROM question_presentation_overrides qpo
  WHERE qpo.user_id = 'user_001'
    AND qpo.question_id = 'q001_primary_emotion'
)
SELECT
    qp.id as pack_id,
    qp.name as pack_name,
    qq.id as question_id,
    qq.question_text,
    qq.metadata as question_metadata,
    qqo.id as option_id,
    qqo.option_text,
    qqo.option_value,
    qqo.option_order,
    em.source as emoji_source,
    em.mapping_data as emoji_config
FROM quiz_packs qp
LEFT JOIN quiz_questions qq ON qp.id = qq.pack_id
LEFT JOIN quiz_question_options qqo ON qq.id = qqo.question_id
LEFT JOIN emoji_mappings em ON 1=1
WHERE qp.id = 'mood_track_branching_v1'
  AND qp.is_active = 1
  AND qq.is_active = 1
  AND qqo.is_active = 1
ORDER BY qq.order_index, qqo.option_order,
         CASE em.source
           WHEN 'question' THEN 1
           WHEN 'user' THEN 2
           WHEN 'pack' THEN 3
           WHEN 'system' THEN 4
         END;
```

#### 获取用户的emoji映射配置
```sql
-- 获取用户在特定Quiz包中的完整emoji映射配置
SELECT
    'question_override' as config_type,
    qpo.question_id,
    qpo.presentation_overrides as config_data,
    qpo.updated_at
FROM question_presentation_overrides qpo
WHERE qpo.user_id = 'user_001'
  AND qpo.question_id IN (
    SELECT qq.id FROM quiz_questions qq
    WHERE qq.pack_id = 'mood_track_branching_v1'
  )

UNION ALL

SELECT
    'user_global' as config_type,
    NULL as question_id,
    JSON_EXTRACT(upc.presentation_config, '$.layer4_view_detail.emotion_presentation') as config_data,
    upc.updated_at
FROM user_presentation_configs upc
WHERE upc.user_id = 'user_001'
  AND upc.is_active = 1

UNION ALL

SELECT
    'pack_default' as config_type,
    NULL as question_id,
    ppc.config_data as config_data,
    ppc.updated_at
FROM pack_presentation_configs ppc
WHERE ppc.pack_id = 'mood_track_branching_v1'
  AND ppc.config_type = 'default_emoji_mapping'

ORDER BY
  CASE config_type
    WHEN 'question_override' THEN 1
    WHEN 'user_global' THEN 2
    WHEN 'pack_default' THEN 3
  END,
  updated_at DESC;
```

#### 获取情绪追踪问卷的分支路径分析
```sql
-- 分析用户在情绪追踪问卷中的完整情绪路径
WITH emotion_path AS (
  SELECT
    qs.id as session_id,
    qs.user_id,
    qa.question_id,
    qq.metadata as question_meta,
    qa.answer_value,
    qqo.metadata as option_meta,
    qa.answered_at,
    ROW_NUMBER() OVER (PARTITION BY qs.id ORDER BY qa.answered_at) as step_order
  FROM quiz_sessions qs
  JOIN quiz_answers qa ON qs.id = qa.session_id
  JOIN quiz_questions qq ON qa.question_id = qq.id
  JOIN quiz_question_options qqo ON qa.selected_option_ids LIKE '%' || qqo.id || '%'
  WHERE qs.pack_id = 'mood_track_branching_v1'
    AND qs.user_id = 'user_001'
    AND qs.status = 'COMPLETED'
)
SELECT
    ep.session_id,
    ep.user_id,
    JSON_EXTRACT(ep.question_meta, '$.emotion_tier') as emotion_tier,
    JSON_EXTRACT(ep.question_meta, '$.layer') as emotion_layer,
    ep.answer_value as selected_emotion,
    JSON_EXTRACT(ep.option_meta, '$.emotion_path') as full_emotion_path,
    ep.answered_at,
    ep.step_order,
    CASE ep.step_order
      WHEN 1 THEN 'Primary Emotion'
      WHEN 2 THEN 'Secondary Emotion'
      WHEN 3 THEN 'Final Emotion'
    END as selection_stage
FROM emotion_path ep
ORDER BY ep.session_id, ep.step_order;
```

#### 获取用户情绪模式分析
```sql
-- 分析用户的情绪选择模式和频率
SELECT
    JSON_EXTRACT(qqo.metadata, '$.emotion_category') as emotion_category,
    qa.answer_value as emotion_selected,
    COUNT(*) as selection_count,
    AVG(qa.response_time_ms) as avg_response_time,
    MIN(qa.answered_at) as first_selected,
    MAX(qa.answered_at) as last_selected,
    COUNT(DISTINCT qs.id) as session_count
FROM quiz_sessions qs
JOIN quiz_answers qa ON qs.id = qa.session_id
JOIN quiz_questions qq ON qa.question_id = qq.id
JOIN quiz_question_options qqo ON qa.selected_option_ids LIKE '%' || qqo.id || '%'
WHERE qs.pack_id = 'mood_track_branching_v1'
  AND qs.user_id = 'user_001'
  AND qs.status = 'COMPLETED'
  AND JSON_EXTRACT(qq.metadata, '$.emotion_tier') = 1  -- 只分析主要情绪
GROUP BY
    JSON_EXTRACT(qqo.metadata, '$.emotion_category'),
    qa.answer_value
ORDER BY selection_count DESC, emotion_category;
```

### 架构优势

#### 1. 全面的Quiz类型支持
- **主流Quiz**: emotion_wheel, personality_test, iq_test, knowledge_quiz, survey
- **非主流Quiz**: text_input, drawing, ranking, matrix, multimedia_choice
- **混合类型**: 支持在同一个Quiz包中混合多种问题类型
- **自定义扩展**: 通过quiz_style字段支持实验性和文化特定的Quiz

#### 2. 强大的个性化展现
- **数据与展现完全分离**: 纯数据存储，展现通过配置系统管理
- **6层个性化配置**: 从用户偏好到具体组件的全方位个性化
- **会话配置快照**: 确保测评过程中展现的一致性
- **多模式支持**: text, emoji, image, animation等多种展现形式

#### 3. 可扩展性
- 支持任意数量的问题和选项
- 支持复杂的问题依赖关系和层级结构
- 支持问题和选项的内容复用
- 支持多种评分和分析算法

#### 4. 管理友好
- 通过管理界面可以轻松添加新Quiz包
- 支持问题和选项的版本管理
- 支持多语言本地化
- 支持A/B测试和实验性功能

#### 5. 数据完整性
- 严格的外键约束和数据验证
- 完整的审计字段追踪变更
- 支持数据迁移和版本升级
- 支持数据备份和恢复

#### 6. 性能优化
- 合理的索引设计支持高效查询
- 分离热数据和冷数据
- 支持缓存和预加载策略
- 支持分布式部署

这个重新设计的数据库架构完全解决了原有的问题，不仅支持传统的情绪和心理测评，还能支持各种主流和非主流的Quiz类型，同时保持了数据与展现分离的核心原则，为个性化展现提供了强大的支持。
