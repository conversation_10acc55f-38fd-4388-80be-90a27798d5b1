/**
 * 列表视图
 * 使用列表布局显示情绪
 *
 * 此组件是新视图系统的一部分，用于替代旧的视图实现
 * 它直接实现了列表的渲染，不依赖旧的视图类
 */

import type { Emotion, ContentDisplayMode, ViewConfig, SkinConfig } from '@/types';
import { BaseEmotionView } from '@/views/base/BaseEmotionView';
// 使用新的组件，不依赖旧的视图实现
import { ListViewComponent } from '@/views/components/lists/ListView';
import type { EmotionView } from '@/views/interfaces/EmotionView';
import type React from 'react';

// 列表布局类型
export type ListLayout = 'vertical' | 'horizontal' | 'accordion' | 'tabs';

/**
 * 列表视图类
 * 使用列表布局显示情绪
 */
export class ListView extends BaseEmotionView implements EmotionView {
  private layout: ListLayout;

  /**
   * 构造函数
   * @param contentDisplayMode 内容显示模式
   * @param skinConfig 皮肤配置
   * @param layout 列表布局
   */
  constructor(
    contentDisplayMode: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: ListLayout = 'vertical'
  ) {
    super('list', contentDisplayMode, skinConfig);
    this.layout = layout;
  }

  /**
   * 设置布局
   * @param layout 列表布局
   */
  setLayout(layout: ListLayout): void {
    this.layout = layout;
  }

  /**
   * 渲染列表
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  renderList(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    // 从配置中获取额外属性
    const onBack = config?.onBack;
    const selectedPath = config?.selectedPath;

    return (
      <ListViewComponent
        emotions={emotions}
        tierLevel={tierLevel}
        contentDisplayMode={this.contentDisplayMode}
        skinConfig={this.skinConfig}
        onSelect={onSelect}
        layout={this.layout}
        onBack={onBack}
        selectedPath={selectedPath}
      />
    );
  }

  /**
   * 渲染视图
   * 实现基类的抽象方法
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  render(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    return this.renderList(emotions, tierLevel, onSelect, config);
  }
}
