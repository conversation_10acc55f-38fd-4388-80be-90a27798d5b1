# Mindful Mood Server

This is the server implementation for the Mindful Mood application, designed to be deployed on Cloudflare Pages. The server provides a comprehensive backend for mood tracking, user management, data synchronization, and analytics.

## Overview

The server provides a tRPC API for accessing the database, supporting multiple database providers (SQLite, Turso, D1). This architecture provides several benefits:

- **Security**: Database credentials are stored server-side only
- **Centralized Data Access**: All database operations go through a single API layer
- **Type Safety**: tRPC provides end-to-end type safety between client and server
- **Authentication**: JWT-based authentication with role-based access control
- **Data Synchronization**: Bi-directional sync between client and server
- **Analytics**: Advanced mood analytics and user activity tracking
- **VIP Management**: Premium features and skin unlock system
- **Reduced Client Bundle Size**: Database client libraries only needed on the server
- **Better Error Handling**: Centralized error handling and logging
- **Cloudflare Pages Integration**: Easy deployment and global distribution

## Project Structure

```
server/
├── functions/                # Serverless functions for cloud deployment
│   ├── _middleware.ts        # Middleware for all requests
│   └── trpc/
│       └── [[route]].ts      # tRPC request handler
├── lib/                      # Core server libraries
│   ├── database/             # Database abstraction layer (supports multiple providers)
│   │   ├── DatabaseInitializer.ts  # Database initializer for the abstraction layer
│   │   ├── DatabaseInterface.ts    # Database interface definitions
│   │   ├── DatabaseService.ts      # Main database service
│   │   ├── SQLiteAdapter.ts        # SQLite database adapter
│   │   ├── TursoAdapter.ts         # Turso database adapter
│   │   ├── D1Adapter.ts           # Cloudflare D1 adapter
│   │   └── index.ts               # Database exports
│   ├── services/             # Business logic services
│   │   ├── AuthService.ts          # User authentication and JWT management
│   │   ├── SyncService.ts          # Data synchronization between client/server
│   │   ├── AnalyticsService.ts     # Mood analytics and statistics
│   │   └── UserManagementService.ts # User profiles and VIP management
│   ├── router.ts             # tRPC router with all endpoints
│   ├── tursoService.ts       # Legacy Turso database service
│   ├── localRouter.js        # tRPC router for local development
│   ├── localTursoService.js  # Local Turso database service
│   └── DatabaseInitializer.ts  # Local database initialization for local-server.js
├── database/                 # Database schema and initialization (uses public/ SQL files)
│   └── schema/                   # (Note: Server uses ../public/seeds/schema/ files directly)
├── tests/                    # Test files and utilities
├── public/
│   └── index.html            # Static landing page
├── local-server.js           # Local development server
├── package.json
├── tsconfig.json
└── .dev.vars                 # Local environment variables
```

## Database Initializers

The server has two separate database initializers:

1. **`lib/DatabaseInitializer.ts`**:
   - Used by `local-server.js` for local development
   - Simpler implementation focused on initializing the local SQLite database
   - Directly executes SQL files from the `public/seeds` directory

2. **`lib/database/DatabaseInitializer.ts`**:
   - Part of the database abstraction layer
   - More complex implementation that supports multiple database providers
   - Used by the `DatabaseService` class
   - Handles `.read` commands in SQL files and supports conditional initialization

## Setup

1. Install dependencies:
   ```bash
   cd server
   npm install
   ```

2. Set up environment variables:
   - Create a `.dev.vars` file in the server directory with the following variables:
     ```
     TURSO_DB_URL=libsql://your-database-url.turso.io
     TURSO_AUTH_TOKEN=your-auth-token
     ENVIRONMENT=development
     ```

3. Run the development server:
   ```bash
   npm run dev
   ```

## API Endpoints

The server exposes the following tRPC procedures:

### Core Database Operations
- `query`: Execute a single SQL query
- `batch`: Execute a batch of SQL statements in a transaction
- `executeScript`: Execute a multi-statement SQL script
- `fetchTable`: Fetch all rows from a specified table
- `fetchTableWithLimit`: Fetch rows from a table with pagination

### Authentication Services (Better-Auth Integration)
- `me`: Get current user information including VIP status
- Better-Auth endpoints:
  - Email/password authentication
  - Social login (Google, Apple)
  - Session management with automatic refresh
  - Multi-device login support
- Legacy endpoints (maintained for compatibility):
  - `login`: User login with email and password
  - `register`: User registration
  - `verifyToken`: JWT token verification
  - `updateVipStatus`: Update user VIP status (admin only)

### Data Synchronization (Updated with New Fields)
- `synchronizeData`: Enhanced sync endpoint supporting new mood entry fields
  - Supports emoji set context (emoji_set_id, emoji_set_version)
  - Supports skin configuration snapshots (skin_id, skin_config_snapshot)
  - Supports display configuration snapshots (view_type_used, render_engine_used, display_mode_used)
  - Supports user configuration snapshots (user_config_snapshot)
  - Enhanced emotion selections with emoji information and selection context
- `performFullSync`: Complete data synchronization with conflict resolution and new field support

### Analytics Services
- `getMoodAnalytics`: Get mood statistics and trends
- `getEmotionUsageStats`: Get emotion usage statistics
- `getUserActivityStats`: Get user activity patterns

### User Management
- `getUserProfile`: Get user profile and preferences
- `updateUserPreferences`: Update user preferences
- `getUserConfig`: Get user configuration settings
- `updateUserConfig`: Update user configuration settings
- `getVipStatus`: Get user VIP status and benefits
- `unlockSkin`: Unlock premium skins
- `getUserUnlockedSkins`: Get list of unlocked skins

### Payment Services (New)
- `getVipPlans`: Get available VIP subscription plans
- `purchaseVip`: Process VIP subscription purchase
- `purchaseSkin`: Process premium skin purchase
- `purchaseEmojiSet`: Process emoji set purchase
- `getPurchaseHistory`: Get user's purchase transaction history
- `getShopItems`: Get all purchasable items (skins and emoji sets)
- `getUserUnlockedSkins`: Get user's unlocked skins
- `getUserUnlockedEmojiSets`: Get user's unlocked emoji sets
- Payment processing with Stripe integration
- Automatic VIP skin unlocking
- Transaction logging and audit trail

### Database Management
- `initializeDatabase`: Initialize database schema and data
- `resetDatabase`: Reset database (development/testing only)

## Database Schema and Initialization

The server uses the same database schema as the client to ensure consistency. Instead of maintaining separate schema files, the server directly reads from the `public/seeds/schema/` directory.

### Schema Loading Order

The DatabaseInitializationService loads SQL files in the following order:

1. **`public/seeds/schema/full.sql`** - Complete database schema with all tables
2. **`public/seeds/schema/init.sql`** - Core data (users, emotions, tags, etc.)
3. **`public/seeds/schema/emotion_data_set_tiers.sql`** - Emotion data set tiers
4. **`public/seeds/schema/additional_emoji_init.sql`** - Additional emoji sets
5. **`public/seeds/schema/extended_emoji_items.sql`** - Extended emoji items
6. **`public/seeds/schema/ui_labels.sql`** - UI labels
7. **`public/seeds/schema/ui_label_translations.sql`** - UI label translations

### Enhanced Schema Features

The server uses an enhanced version of the client schema with the following additions:

#### Enhanced Mood Entries Table
- `emoji_set_id`: Emoji set used when creating the entry
- `emoji_set_version`: Version of emoji set for compatibility
- `skin_id`: Skin used when creating the entry
- `skin_config_snapshot`: JSON snapshot of skin configuration
- `view_type_used`: View type used when creating the entry
- `render_engine_used`: Render engine used when creating the entry
- `display_mode_used`: Display mode used when creating the entry
- `user_config_snapshot`: JSON snapshot of user configuration

#### Enhanced Emotion Selections Table
- `intensity`: Emotion intensity level (0-100)
- `emoji_item_id`: Specific emoji item used for this selection
- `emoji_unicode`: Unicode representation of the emoji
- `emoji_image_url`: Image URL for the emoji
- `emoji_animation_data`: Animation data for animated emojis
- `selection_path`: JSON array representing the selection path
- `parent_selection_id`: Reference to parent selection for hierarchical navigation

#### Better-Auth Integration Tables
- `user_accounts`: OAuth account management
- `user_verifications`: Email verification and password reset
- Enhanced `user_sessions`: Session management with device tracking

#### Payment and VIP Management Tables
- `payment_transactions`: Payment transaction logging
- `user_subscription_history`: VIP subscription tracking
- `user_skin_unlocks`: Premium skin unlock management
- `user_emoji_set_unlocks`: Premium emoji set unlock management

#### Server-Specific User Fields
- `password_hash`: For user authentication (Better-Auth managed)
- `is_vip`: VIP status flag
- `vip_expires_at`: VIP expiration timestamp
- `last_login_at`: Last login tracking
- `is_verified`: Email verification status
- `is_active`: Account status

### Automatic Initialization

The database is automatically initialized when:

1. The server starts and detects an empty database
2. The `initializeDatabase` endpoint is called manually
3. During development when `resetDatabase` is called

## Deployment

To deploy the server to Cloudflare Pages:

1. Log in to Cloudflare:
   ```bash
   npx wrangler login
   ```

2. Deploy to Cloudflare Pages:
   ```bash
   npm run deploy
   ```

3. Set up environment variables in the Cloudflare Pages dashboard:
   - Go to your project in the Cloudflare Pages dashboard
   - Navigate to Settings > Environment variables
   - Add the following variables:
     - `TURSO_DB_URL`
     - `TURSO_AUTH_TOKEN`
     - `ENVIRONMENT` (set to "production")

## Enhanced Data Synchronization

The server provides comprehensive data synchronization capabilities with support for the new mood entry and emotion selection fields:

### Synchronization Features

#### Mood Entry Synchronization
- **Context Preservation**: Maintains emoji set, skin, and display configuration context
- **Configuration Snapshots**: Preserves user configuration at the time of entry creation
- **Version Compatibility**: Handles emoji set version changes gracefully
- **Conflict Resolution**: Intelligent merging of conflicting changes

#### Emotion Selection Synchronization
- **Emoji Information**: Syncs complete emoji data including animations
- **Selection Paths**: Maintains hierarchical selection navigation history
- **Parent-Child Relationships**: Preserves complex emotion selection structures
- **Intensity Tracking**: Syncs emotion intensity levels

#### Synchronization Endpoints

**`synchronizeData`** - Enhanced bidirectional sync:
```typescript
// Request format
{
  moodEntriesToUpload: MoodEntry[],     // With new context fields
  emotionSelectionsToUpload: EmotionSelection[], // With emoji info
  lastSyncTimestamp?: string
}

// Response format
{
  moodEntriesToDownload: MoodEntry[],   // Server changes
  emotionSelectionsToDownload: EmotionSelection[],
  syncedMoodEntries: string[],          // Successfully synced IDs
  syncedEmotionSelections: string[],
  conflicts: ConflictInfo[]             // Conflicts requiring resolution
}
```

**`performFullSync`** - Complete synchronization with conflict resolution:
- Handles large datasets efficiently
- Provides detailed sync progress information
- Supports partial sync recovery
- Maintains data integrity across all related tables

### Data Security and Privacy

#### Client-Server Data Flow
- **Sensitive Data**: Authentication, payment info - server-only
- **User Content**: Mood entries, selections - bidirectional sync
- **Configuration**: User preferences - client-controlled, server-backed
- **VIP Status**: Server-controlled, client read-only

#### Sync Status Management
- `PENDING`: Awaiting synchronization
- `SYNCED`: Successfully synchronized
- `FAILED`: Sync failed, retry required
- `CONFLICT`: Manual conflict resolution needed
- `PARTIAL_SYNC`: Partially synchronized
- `SYNC_IN_PROGRESS`: Currently synchronizing
- `OFFLINE_ONLY`: Local-only data

## Client Integration

The client application uses the enhanced tRPC client with better-auth integration. The client needs to be configured with the server URL in the `.env` file:

```
VITE_API_URL=https://your-pages-url.pages.dev
VITE_BETTER_AUTH_URL=https://your-pages-url.pages.dev
```

### Authentication Integration
```typescript
// Better-Auth client setup
import { createAuthClient } from "@better-auth/react";

export const authClient = createAuthClient({
  baseURL: process.env.VITE_BETTER_AUTH_URL,
  // Integration with tRPC for data operations
});

// tRPC client with auth
const trpcClient = createTRPCClient({
  links: [
    httpBatchLink({
      url: `${process.env.VITE_API_URL}/trpc`,
      headers: async () => {
        const session = await authClient.getSession();
        return session ? {
          'Authorization': `Bearer ${session.token}`
        } : {};
      }
    })
  ]
});
```

## Development Workflow

During development, you can run both the client and server concurrently:

1. Start the server:
   ```bash
   cd server
   npm run dev
   ```

2. In another terminal, start the client:
   ```bash
   npm run dev
   ```

The client will connect to the server running on `http://localhost:8788` by default.

## Enhanced Testing

The server includes comprehensive test coverage for the enhanced mood entry and emotion selection functionality:

### Test Categories

#### Integration Tests
- **Mood Entry Integration**: Tests complete mood entry lifecycle with new fields
- **Emotion Selection Integration**: Tests emotion selection with emoji information
- **Synchronization Tests**: Tests bidirectional sync with conflict resolution
- **Authentication Tests**: Tests Better-Auth integration
- **Payment Tests**: Tests VIP subscription and skin purchase functionality

#### Test Files
- `src/tests/mood-entry-integration.test.tsx`: Mood entry functionality with new fields
- `src/tests/emotion-selection-integration.test.tsx`: Emotion selection with emoji context
- `server/tests/`: Server-side unit and integration tests

#### Running Tests
```bash
# Client-side tests
npm test

# Server-side tests (in server directory)
cd server && npm test

# Specific test files
npm test -- mood-entry-integration.test.tsx
npm test -- emotion-selection-integration.test.tsx

# Watch mode for development
npm test -- --watch
```

#### Test Coverage
- ✅ New mood entry fields (emoji_set_id, skin_id, view_type_used, etc.)
- ✅ Emotion selection enhancements (emoji info, selection paths, intensity)
- ✅ Configuration snapshot functionality
- ✅ Synchronization with new fields and conflict resolution
- ✅ Better-Auth integration and session management
- ✅ Payment service functionality (VIP, skin purchases)
- ✅ Data integrity and validation
- ✅ Offline storage and online sync integration

### Development Database Testing

For testing, the server uses isolated test databases:
- Test database: In-memory SQLite for fast test execution
- Schema: Same as production, loaded from `public/seeds/schema/`
- Data isolation: Each test gets a clean database state
- Mock services: Payment and auth services are mocked for testing
