# 前端核心UI组件与Quiz引擎设计文档

## 1. 概述

本文档详细描述了前端核心UI组件的展现形式、多布局/风格方案，以及与核心Quiz引擎的集成设计。该系统支持丰富的视觉表现、灵活的配置选项，并为Quiz应用提供游戏化的用户体验。该设计与6层个性化配置架构完美整合，实现了从Settings页面到QuizSettings页面的无缝迁移。

> **架构更新**: 本文档已更新以反映Quiz设置迁移后的新架构，包括6层个性化配置和专门的QuizSettings页面。

### 1.1 设计原则

- **数据驱动**: 所有UI组件通过后端配置动态生成
- **6层配置**: 支持从基础到专业级的个性化配置体验
- **多样性**: 每个组件提供1-2套不同布局或风格选择
- **响应式**: 支持PC、平板、手机等不同屏幕尺寸
- **主题化**: 支持全局主题切换（水墨雅致、现代简约、节日喜庆等）
- **可访问性**: 确保色彩对比度、键盘导航等无障碍功能
- **文化特色**: 融入文化元素的视觉设计
- **配置分离**: Quiz配置与通用设置完全分离

## 2. 前端核心UI组件设计

### 2.1 文本组件 (Text Component)

#### 基础展现
一段静态文本，支持丰富的样式配置。

#### 可配置属性
- 字体、大小、颜色、对齐方式
- 粗体/斜体、行间距
- 动画效果、阴影效果

#### 布局/风格方案

##### 布局1: 标准文本块 (Standard Text Block)
```typescript
interface StandardTextConfig {
  layout_id: "standard_text_block";
  style: {
    font_family: string;
    font_size: number;
    color: string;
    alignment: "left" | "center" | "right";
    line_height: number;
  };
}
```
- **展现**: 常规的段落文本
- **适用**: 问题正文、详细描述、提示信息

##### 布局2: 对话气泡 (Dialogue Bubble)
```typescript
interface DialogueBubbleConfig {
  layout_id: "dialogue_bubble";
  style: {
    bubble_color: string;
    border_style: string;
    arrow_direction: "left" | "right" | "top" | "bottom";
    arrow_style: "pointed" | "rounded";
  };
}
```
- **展现**: 文本被包裹在类似漫画对话框的图形中
- **适用**: NPC对话、玩家内心独白

##### 风格变体
- **卷轴式文本 (Scroll Text)**: 文本区域模拟展开的卷轴，背景有卷轴纹理
- **碑文式文本 (Stele Text)**: 文本模拟刻在石碑上的效果，字体使用古朴的隶书或篆书

### 2.2 媒体组件 (Media Component)

#### 图片组件 (Image Component)

##### 布局1: 独立图片 (Standalone Image)
```typescript
interface StandaloneImageConfig {
  layout_id: "standalone_image";
  style: {
    size: { width: number; height: number };
    border_radius: number;
    border_style: string;
    shadow_effect: boolean;
  };
}
```
- **展现**: 图片单独展示，可配置大小、边框、圆角
- **适用**: 问题配图、药材展示

##### 布局2: 图文混排 (Image with Text)
```typescript
interface ImageWithTextConfig {
  layout_id: "image_with_text";
  style: {
    position: "left_image_right_text" | "right_image_left_text" | "top_image_bottom_text";
    spacing: number;
    text_style: TextStyle;
  };
}
```
- **展现**: 图片与关联文本并排或上下排列
- **适用**: 更详细的图文解释

##### 风格变体
- **水墨画框 (Ink Wash Frame)**: 图片带一个水墨风格的画框
- **嵌入式小图 (Inline Thumbnail)**: 小图标或缩略图嵌入在文本段落中

#### 音频播放器 (Audio Player)

##### 布局1: 简约播放条 (Minimalist Player Bar)
```typescript
interface MinimalistPlayerConfig {
  layout_id: "minimalist_player";
  style: {
    show_progress: boolean;
    button_style: "circular" | "square";
    color_scheme: string;
  };
}
```
- **展现**: 仅包含播放/暂停按钮和可选的进度条
- **适用**: 背景引导语、环境音效

##### 布局2: 完整播放器 (Full Player)
```typescript
interface FullPlayerConfig {
  layout_id: "full_player";
  style: {
    show_volume: boolean;
    show_loop: boolean;
    show_timeline: boolean;
    skin_style: "modern" | "vintage" | "chinese";
  };
}
```
- **展现**: 包含播放/暂停、进度条、音量控制、循环按钮
- **适用**: 较长的音乐片段或需要用户控制的音频

### 2.3 选择器组件 (Selector Component)

#### 单选按钮组 (Radio Group) / 复选框组 (Checkbox Group)

##### 布局1: 垂直列表 (Vertical List)
```typescript
interface VerticalListConfig {
  layout_id: "vertical_list";
  style: {
    spacing: number;
    marker_style: "circle" | "square" | "chinese_marker";
    label_position: "right" | "left";
    hover_effect: boolean;
  };
}
```
- **展现**: 每个选项占一行，选择控件在左，标签在右
- **适用**: 大多数情况，清晰易读

##### 布局2: 水平流式 (Horizontal Flow)
```typescript
interface HorizontalFlowConfig {
  layout_id: "horizontal_flow";
  style: {
    wrap_behavior: "wrap" | "scroll";
    item_spacing: number;
    responsive_breakpoint: number;
  };
}
```
- **展现**: 选项水平排列，空间不足时自动换行
- **适用**: 选项文本较短，或希望更紧凑地利用横向空间

##### 布局3: 网格布局 (Grid Layout)
```typescript
interface GridLayoutConfig {
  layout_id: "grid_layout";
  style: {
    columns: number;
    row_gap: number;
    column_gap: number;
    responsive_columns: { [breakpoint: string]: number };
  };
}
```
- **展现**: 选项以网格形式排列（如2列或3列）
- **适用**: 选项是图文结合的小卡片，或选项数量较多时

##### 风格变体
- **按钮式选项 (Button-like Options)**: 每个选项看起来像一个可点击的按钮
- **卡片式选项 (Card-like Options)**: 每个选项是一个小卡片，可包含图片、标题和描述
- **中式标记 (Chinese Style Markers)**: 单选/复选标记替换为毛笔勾选、小印章等

#### 下拉列表 (Dropdown/Select)
```typescript
interface DropdownConfig {
  layout_id: "dropdown_select";
  style: {
    arrow_style: "chevron" | "triangle" | "chinese_arrow";
    menu_style: "modern" | "traditional" | "floating";
    max_height: number;
  };
}
```
- **展现**: 标准的下拉列表控件，点击展开选项
- **风格变体**: 可定制下拉箭头和展开菜单的样式，使其更符合中医主题

### 2.4 滑块组件 (Slider Component)

##### 布局1: 标准水平滑块 (Standard Horizontal Slider)
```typescript
interface HorizontalSliderConfig {
  layout_id: "horizontal_slider";
  style: {
    track_style: "line" | "groove" | "bamboo";
    thumb_style: "circle" | "square" | "custom_shape";
    show_value: boolean;
    show_labels: boolean;
    label_positions: string[];
  };
}
```
- **展现**: 水平轨道，可拖动滑块钮，两端有标签
- **适用**: 大多数数值范围选择

##### 布局2: 垂直滑块 (Vertical Slider)
```typescript
interface VerticalSliderConfig {
  layout_id: "vertical_slider";
  style: {
    height: number;
    orientation: "left_labels" | "right_labels" | "both_labels";
    thermometer_style: boolean;
  };
}
```
- **展现**: 垂直轨道，类似温度计
- **适用**: 当语义上更适合垂直表达时（如水位、能量条）

##### 风格变体
- **竹节滑块 (Bamboo Slider)**: 轨道设计成竹节，滑块钮是小熊猫爪印或竹叶
- **墨迹滑块 (Ink Drop Slider)**: 轨道是淡墨痕迹，滑块钮是浓墨滴
- **分段式滑块 (Stepped Slider)**: 滑块只能停在预设的几个点上，每个点有明确标签

### 2.5 量尺/评分组件 (Scale/Rating Component)

##### 布局1: 水平点状/星级评分 (Horizontal Rating)
```typescript
interface HorizontalRatingConfig {
  layout_id: "horizontal_rating";
  style: {
    marker_type: "dots" | "stars" | "lotus" | "gourd" | "taiji";
    size: "small" | "medium" | "large";
    spacing: number;
    hover_effect: "scale" | "glow" | "color_change";
  };
}
```
- **展现**: 一行小圆点或星星，用户点击选择评分等级
- **适用**: 满意度、疼痛等级等
- **风格变体**: 星星可替换为莲花、小葫芦、太极鱼等

##### 布局2: 水平标签量尺 (Horizontal Labeled Scale)
```typescript
interface LabeledScaleConfig {
  layout_id: "labeled_scale";
  style: {
    label_style: "text" | "wooden_sign" | "scroll";
    scale_type: "likert" | "semantic_differential" | "custom";
    background_style: "line" | "bar" | "traditional";
  };
}
```
- **展现**: 一行可点击的文本标签，代表不同程度
- **适用**: 李克特量表（非常不同意 - 非常同意）
- **风格变体**: 标签可以设计成小木牌或卷轴的样式

### 2.6 其他核心组件

#### 图片选择器组件 (Image Selector Component)

##### 布局1: 网格图片墙 (Grid Image Wall)
```typescript
interface GridImageWallConfig {
  layout_id: "grid_image_wall";
  style: {
    columns: number;
    aspect_ratio: string;
    gap: number;
    selection_indicator: "border" | "overlay" | "checkmark";
    hover_effect: "zoom" | "shadow" | "brightness";
  };
}
```
- **展现**: 多张图片以网格形式排列，可配置每行显示的图片数量
- **适用**: 选择多张图片，或从多张图片中单选

##### 布局2: 水平滚动图片带 (Horizontal Scrolling Strip)
```typescript
interface HorizontalScrollingConfig {
  layout_id: "horizontal_scrolling";
  style: {
    item_width: number;
    scroll_behavior: "smooth" | "auto";
    show_arrows: boolean;
    infinite_scroll: boolean;
  };
}
```
- **展现**: 图片在一行内水平排列，可通过左右箭头或手势滑动浏览
- **适用**: 当图片数量较多，但希望保持纵向空间紧凑时

#### 拖拽排序列表组件 (Draggable List Component)

##### 布局1: 垂直可排序列表 (Vertical Sortable List)
```typescript
interface VerticalSortableConfig {
  layout_id: "vertical_sortable";
  style: {
    drag_handle_style: "dots" | "lines" | "icon";
    item_style: "card" | "row" | "minimal";
    drop_indicator: "line" | "highlight" | "shadow";
  };
}
```
- **展现**: 项目垂直排列，每个项目有拖拽手柄
- **适用**: 对文本或简单图文条目进行排序

##### 布局2: 看板式拖拽 (Kanban-style Drag & Drop)
```typescript
interface KanbanStyleConfig {
  layout_id: "kanban_style";
  style: {
    column_count: number;
    column_headers: string[];
    card_style: "simple" | "detailed" | "chinese_style";
    drop_zones: "highlight" | "outline" | "traditional";
  };
}
```
- **展现**: 多个垂直列（代表不同类别或优先级），项目可以在列之间拖拽
- **适用**: 分类任务、优先级排序
- **中医特色**: 列可以是"五行归类区"，将药材拖入对应的五行区域

#### 按钮组件 (Button Component)

##### 布局1: 标准按钮 (Standard Button)
```typescript
interface StandardButtonConfig {
  layout_id: "standard_button";
  style: {
    shape: "rectangle" | "rounded" | "pill" | "custom";
    size: "small" | "medium" | "large";
    variant: "primary" | "secondary" | "outline" | "ghost";
    icon_position: "left" | "right" | "only" | "none";
  };
}
```

##### 风格变体
- **玉佩按钮 (Jade Pendant Button)**: 按钮形状和材质模拟玉佩
- **印章按钮 (Seal Stamp Button)**: 按钮形状模拟印章，点击时可能有"盖印"的动画效果
- **卷轴展开式按钮 (Scroll Unfurl Button)**: 按钮平时是卷起的小卷轴，点击后展开显示文字

#### 进度指示器组件 (Progress Indicator Component)

##### 布局1: 顶部/底部进度条 (Progress Bar)
```typescript
interface ProgressBarConfig {
  layout_id: "progress_bar";
  style: {
    position: "top" | "bottom" | "inline";
    thickness: number;
    style: "line" | "gradient" | "segmented";
    animation: "smooth" | "stepped" | "pulsing";
  };
}
```

##### 风格变体
- **竹叶生长进度 (Bamboo Leaf Growth)**: 几片空的竹叶轮廓，每完成一步，一片竹叶被填色
- **莲花绽放进度 (Lotus Blooming)**: 一朵莲花从花苞到逐渐绽放的过程
- **墨点连接进度 (Ink Dots Connecting)**: 多个墨点，完成一步则用墨线连接下一个点

#### NPC/角色呈现组件 (NPC/Character Presenter Component)

##### 布局1: 半身像/全身像侧边栏 (Character Sidebar)
```typescript
interface CharacterSidebarConfig {
  layout_id: "character_sidebar";
  style: {
    position: "left" | "right";
    size: "small" | "medium" | "large";
    animation: "idle" | "talking" | "gesturing";
    background: "transparent" | "scenic" | "traditional";
  };
}
```

##### 布局2: 弹出式对话框角色 (Pop-up Dialogue Character)
```typescript
interface PopupDialogueConfig {
  layout_id: "popup_dialogue";
  style: {
    appear_animation: "fade" | "slide" | "bounce";
    character_size: "avatar" | "half_body" | "full_body";
    dialogue_style: "bubble" | "box" | "scroll";
  };
}
```

## 3. 核心Quiz引擎 (Core Quiz Engine) 设计

### 3.1 设计原则

- **数据驱动**: 所有界面配置通过JSON配置文件定义
- **6层配置**: 支持Layer 0-5的个性化配置架构
- **模块化**: 可插拔的组件和策略
- **可扩展性**: 易于添加新的组件类型和交互方式
- **高性能**: 优化的渲染和状态管理
- **易用性**: 简洁的API和清晰的配置结构
- **配置集成**: 与QuizSettings页面的6层配置无缝集成

### 3.2 核心组件架构

```typescript
// 核心量表引擎架构
interface CoreQuizEngine {
  // 配置加载器
  configLoader: ConfigurationLoader;

  // 问题库管理器
  questionBank: QuestionBankManager;

  // 呈现逻辑控制器
  presentationController: PresentationLogicController;

  // 答案处理器
  answerProcessor: AnswerProcessor;

  // 评估引擎
  evaluationEngine: EvaluationEngine;

  // 会话管理器
  sessionManager: SessionManager;

  // 事件分发器
  eventDispatcher: EventDispatcher;
}
```

### 3.3 问题与选项数据结构

#### 问题对象 (Question Object)
```typescript
interface QuestionObject {
  id: string;
  text_localized: { [locale: string]: string };

  // UI配置提示
  preferred_interface_template_id?: string;
  primary_interaction_type: InteractionType;
  ui_hints?: {
    npc_to_display_id?: string;
    background_suggestion_id?: string;
    auxiliary_media_id?: string;
    progress_indicator_style_override?: string;
  };

  // 选项配置
  options: OptionObject[];

  // 逻辑配置
  branching_logic?: BranchingLogic;
  validation_rules?: ValidationRules;
}

enum InteractionType {
  SINGLE_CHOICE_LIST = "SINGLE_CHOICE_LIST",
  MULTIPLE_CHOICE_LIST = "MULTIPLE_CHOICE_LIST",
  SLIDER = "SLIDER",
  IMAGE_GRID_SELECT = "IMAGE_GRID_SELECT",
  NPC_DIALOGUE_CHOICE = "NPC_DIALOGUE_CHOICE",
  TEXT_INPUT = "TEXT_INPUT",
  DRAG_AND_DROP = "DRAG_AND_DROP",
  RATING_SCALE = "RATING_SCALE"
}
```

#### 选项对象 (Option Object)
```typescript
interface OptionObject {
  id: string;
  value: string | number;
  text_localized: { [locale: string]: string };

  // 显示配置
  display_component_type: DisplayComponentType;
  display_component_config: {
    image_url?: string;
    icon_name?: string;
    text_style_override?: string;
    layout_id?: string;
    style_overrides?: Record<string, any>;
  };

  // 即时反馈配置
  feedback_on_select_config?: {
    animation?: string;
    sound?: string;
    visual_effect?: string;
    haptic_feedback?: boolean;
  };

  // 逻辑配置
  scoring_value?: number;
  tags?: string[];
  conditions?: ConditionRule[];
}

enum DisplayComponentType {
  TEXT_ONLY = "TEXT_ONLY",
  IMAGE_WITH_TEXT = "IMAGE_WITH_TEXT",
  ICON_ONLY = "ICON_ONLY",
  BUTTON_STYLE = "BUTTON_STYLE",
  CARD_STYLE = "CARD_STYLE"
}
```

### 3.4 呈现数据对象 (QuestionPresentationData)

```typescript
interface QuestionPresentationData {
  // 基础信息
  question_id: string;
  question_text_localized: string;
  question_text_style_id?: string;

  // 交互类型
  primary_interaction_type: InteractionType;

  // 界面配置
  background_id?: string;
  npc_presenter_config?: NPCPresenterConfig;

  // 选项配置
  options: OptionPresentationData[];

  // 进度信息
  progress_info: {
    current: number;
    total: number;
    style_id?: string;
  };

  // 导航配置
  navigation_buttons_config: NavigationButtonConfig[];

  // 全局样式
  global_styles?: {
    theme_id: string;
    color_scheme: string;
    font_family: string;
  };
}

interface OptionPresentationData {
  option_id: string;
  value: string | number;
  display_config: {
    component_type: DisplayComponentType;
    layout_id: string;
    style_config: Record<string, any>;
    content: {
      text_localized?: string;
      image_url?: string;
      icon_name?: string;
    };
  };
  immediate_feedback_config?: FeedbackConfig;
}
```

### 3.5 后端配置指定布局/风格

#### 配置示例
```json
{
  "id": "mood_assessment_q1",
  "type": "Question",
  "config": {
    "text_localized": {
      "zh": "小友，今天的心情像哪片云彩呀？",
      "en": "Little friend, which cloud represents your mood today?"
    },
    "primary_interaction_type": "IMAGE_GRID_SELECT",
    "preferred_interface_template_id": "chinese_traditional_template",
    "ui_hints": {
      "npc_to_display_id": "wise_panda_doctor",
      "background_suggestion_id": "bamboo_forest_day",
      "progress_indicator_style_override": "bamboo_leaf_growth"
    },
    "options": [
      {
        "id": "happy_cloud",
        "value": "happy",
        "text_localized": {
          "zh": "万里无云，心情舒畅",
          "en": "Clear sky, cheerful mood"
        },
        "display_component_type": "IMAGE_WITH_TEXT",
        "display_component_config": {
          "layout_id": "image_card_vertical",
          "image_url": "/assets/clouds/sunny_cloud.png",
          "style_overrides": {
            "card_background": "#FFF8DC",
            "border_style": "golden_frame",
            "hover_effect": "gentle_glow"
          }
        },
        "feedback_on_select_config": {
          "animation": "gentle_bounce",
          "sound": "soft_chime",
          "visual_effect": "golden_sparkles",
          "haptic_feedback": true
        }
      }
    ]
  }
}
```

### 3.6 工作流程

#### 完整的用户交互流程
```typescript
// 1. 启动会话
const session = await quizEngine.startSession("daily_emotion_assessment");

// 2. 获取问题呈现数据
const questionData = await quizEngine.getNextQuestionPresentation(session.id);

// 3. 前端渲染界面
const renderedUI = UIRenderer.render(questionData);

// 4. 用户交互
user.selectOption("happy_cloud");

// 5. 提交答案
await quizEngine.submitAnswer(session.id, questionData.question_id, {
  selected_value: "happy",
  interaction_metadata: {
    time_spent: 5000,
    hover_count: 2,
    selection_confidence: 0.8
  }
});

// 6. 获取下一个问题或结果
const nextQuestion = await quizEngine.getNextQuestionPresentation(session.id);
if (!nextQuestion) {
  const result = await quizEngine.getSessionResult(session.id);
  UIRenderer.renderResult(result);
}
```

### 3.7 UI配置聚合器 (UI Config Aggregator)

```typescript
class UIConfigAggregator {
  aggregateQuestionConfig(
    question: QuestionObject,
    quizPackGlobalSettings: GlobalUISettings,
    userPreferences: UserUIPreferences,
    contextualFactors: ContextualFactors
  ): QuestionPresentationData {

    // 1. 基础配置合并
    const baseConfig = this.mergeBaseConfig(question, quizPackGlobalSettings);

    // 2. 用户偏好应用
    const userCustomizedConfig = this.applyUserPreferences(baseConfig, userPreferences);

    // 3. 上下文调整
    const contextAdjustedConfig = this.applyContextualAdjustments(
      userCustomizedConfig,
      contextualFactors
    );

    // 4. 响应式适配
    const responsiveConfig = this.applyResponsiveAdjustments(
      contextAdjustedConfig,
      contextualFactors.deviceInfo
    );

    return responsiveConfig;
  }

  private applyContextualAdjustments(
    config: QuestionPresentationData,
    context: ContextualFactors
  ): QuestionPresentationData {
    // 时间相关调整
    if (context.timeOfDay === 'night') {
      config.global_styles.color_scheme = 'dark_warm';
    }

    // 设备性能调整
    if (context.deviceInfo.performance === 'low') {
      config.options.forEach(option => {
        option.display_config.style_config.animations_enabled = false;
      });
    }

    return config;
  }
}
```

## 4. 前端实现挑战与解决方案

### 4.1 强大的组件库

#### 挑战
- 需要构建可复用、可配置、可主题化的UI组件
- 支持多种布局和风格变体
- 保持组件间的一致性

#### 解决方案
```typescript
// 组件工厂模式
class ComponentFactory {
  static createComponent(
    type: ComponentType,
    config: ComponentConfig,
    theme: ThemeConfig
  ): React.ComponentType {

    const BaseComponent = this.getBaseComponent(type);
    const StyledComponent = this.applyTheme(BaseComponent, theme);
    const ConfiguredComponent = this.applyConfig(StyledComponent, config);

    return ConfiguredComponent;
  }

  private static getBaseComponent(type: ComponentType): React.ComponentType {
    const componentMap = {
      [ComponentType.TEXT]: TextComponent,
      [ComponentType.BUTTON]: ButtonComponent,
      [ComponentType.SLIDER]: SliderComponent,
      [ComponentType.IMAGE_SELECTOR]: ImageSelectorComponent,
      // ... 其他组件
    };

    return componentMap[type];
  }
}
```

### 4.2 动态布局系统

#### 挑战
- 根据配置动态组合和排列组件
- 响应式布局适配
- 性能优化

#### 解决方案
```typescript
// 布局引擎
class LayoutEngine {
  renderLayout(
    layoutConfig: LayoutConfig,
    components: ComponentInstance[],
    containerSize: Size
  ): React.ReactElement {

    const layoutStrategy = this.getLayoutStrategy(layoutConfig.type);
    const responsiveConfig = this.calculateResponsiveConfig(layoutConfig, containerSize);

    return layoutStrategy.render(components, responsiveConfig);
  }

  private getLayoutStrategy(type: LayoutType): LayoutStrategy {
    const strategies = {
      [LayoutType.GRID]: new GridLayoutStrategy(),
      [LayoutType.FLEX]: new FlexLayoutStrategy(),
      [LayoutType.ABSOLUTE]: new AbsoluteLayoutStrategy(),
      [LayoutType.FLOW]: new FlowLayoutStrategy(),
    };

    return strategies[type];
  }
}
```

### 4.3 样式管理系统

#### 挑战
- 管理多套风格和主题
- CSS变量和动态样式
- 中医文化元素的视觉实现

#### 解决方案
```typescript
// 主题管理器
class ThemeManager {
  private themes: Map<string, ThemeDefinition> = new Map();

  registerTheme(id: string, theme: ThemeDefinition): void {
    this.themes.set(id, theme);
  }

  applyTheme(themeId: string, element: HTMLElement): void {
    const theme = this.themes.get(themeId);
    if (!theme) return;

    // 应用CSS变量
    Object.entries(theme.cssVariables).forEach(([key, value]) => {
      element.style.setProperty(`--${key}`, value);
    });

    // 应用CSS类
    element.className = `${element.className} ${theme.cssClasses.join(' ')}`;
  }
}

// 中医风格主题定义
const chineseTraditionalTheme: ThemeDefinition = {
  id: "chinese_traditional",
  name: "水墨雅致",
  cssVariables: {
    "primary-color": "#8B4513",
    "secondary-color": "#DEB887",
    "accent-color": "#DAA520",
    "background-gradient": "linear-gradient(135deg, #F5F5DC, #FFFAF0)",
    "border-style": "2px solid #8B4513",
    "font-family": "'KaiTi', '楷体', serif",
    "shadow-effect": "0 4px 8px rgba(139, 69, 19, 0.3)"
  },
  cssClasses: ["chinese-traditional", "ink-wash-style"],
  componentOverrides: {
    button: "jade-pendant-button",
    slider: "bamboo-slider",
    progress: "lotus-blooming"
  }
};
```

## 5. 性能优化策略

### 5.1 组件懒加载
```typescript
// 动态导入组件
const ComponentLoader = {
  async loadComponent(type: ComponentType): Promise<React.ComponentType> {
    switch (type) {
      case ComponentType.COMPLEX_3D_VIEW:
        return (await import('./components/Complex3DView')).default;
      case ComponentType.ADVANCED_CHART:
        return (await import('./components/AdvancedChart')).default;
      default:
        return (await import('./components/BasicComponent')).default;
    }
  }
};
```

### 5.2 渲染优化
```typescript
// 虚拟化长列表
const VirtualizedList = React.memo(({ items, renderItem }: VirtualizedListProps) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });

  const visibleItems = useMemo(() =>
    items.slice(visibleRange.start, visibleRange.end),
    [items, visibleRange]
  );

  return (
    <div className="virtualized-list">
      {visibleItems.map(renderItem)}
    </div>
  );
});
```

## 6. 可访问性支持

### 6.1 键盘导航
```typescript
// 键盘导航管理器
class KeyboardNavigationManager {
  private focusableElements: HTMLElement[] = [];
  private currentIndex = 0;

  registerFocusableElements(elements: HTMLElement[]): void {
    this.focusableElements = elements;
    this.setupKeyboardListeners();
  }

  private setupKeyboardListeners(): void {
    document.addEventListener('keydown', (event) => {
      switch (event.key) {
        case 'Tab':
          event.preventDefault();
          this.moveFocus(event.shiftKey ? -1 : 1);
          break;
        case 'Enter':
        case ' ':
          this.activateCurrentElement();
          break;
      }
    });
  }
}
```

### 6.2 屏幕阅读器支持
```typescript
// ARIA标签管理
class ARIAManager {
  static addARIALabels(element: HTMLElement, config: ARIAConfig): void {
    if (config.label) {
      element.setAttribute('aria-label', config.label);
    }

    if (config.describedBy) {
      element.setAttribute('aria-describedby', config.describedBy);
    }

    if (config.role) {
      element.setAttribute('role', config.role);
    }

    if (config.expanded !== undefined) {
      element.setAttribute('aria-expanded', config.expanded.toString());
    }
  }
}
```

## 7. 实现计划与优先级

### 7.1 第一阶段：核心组件 (4周)
1. **文本组件** - 标准文本块、对话气泡
2. **按钮组件** - 标准按钮、中式风格变体
3. **选择器组件** - 单选、多选、下拉列表
4. **基础布局引擎** - 网格、流式布局

### 7.2 第二阶段：交互组件 (4周)
1. **滑块组件** - 水平、垂直、中式风格
2. **评分组件** - 星级、点状、中式标记
3. **图片选择器** - 网格墙、滚动带
4. **进度指示器** - 进度条、中式风格变体

### 7.3 第三阶段：高级功能 (4周)
1. **拖拽组件** - 排序列表、看板式
2. **媒体组件** - 图片、音频、视频
3. **NPC角色组件** - 侧边栏、弹出式
4. **主题系统** - 多主题支持、动态切换

### 7.4 第四阶段：优化与集成 (4周)
1. **性能优化** - 懒加载、虚拟化、缓存
2. **可访问性** - 键盘导航、屏幕阅读器
3. **测试覆盖** - 单元测试、集成测试
4. **文档完善** - API文档、使用指南

## 8. 总结

本文档详细描述了前端核心UI组件的设计方案和核心量表引擎的架构。通过这个设计，我们可以实现：

### 8.1 核心优势
- **高度可配置**: 后端配置驱动的动态界面生成
- **丰富多样**: 每个组件提供多种布局和风格选择
- **文化特色**: 深度融入中医文化元素的视觉设计
- **响应式**: 完美适配各种设备和屏幕尺寸
- **可扩展**: 易于添加新组件和新功能

### 8.2 技术特点
- **模块化架构**: 清晰的组件分层和职责分离
- **类型安全**: 完整的TypeScript类型定义
- **性能优化**: 懒加载、虚拟化等优化策略
- **可访问性**: 完整的无障碍功能支持

### 8.3 应用价值
- **开发效率**: 配置驱动减少重复开发工作
- **用户体验**: 丰富的交互方式和视觉效果
- **文化传承**: 中医文化的数字化表达
- **商业价值**: 可复用的组件库和引擎架构

这个设计为构建优秀的Quiz游戏体验奠定了坚实的技术基础，同时也为未来的功能扩展和优化提供了清晰的路径。

> **注意**: Wheel ViewType与组件设计的详细整合架构已迁移到 `docs/emotion-dataset-as-quiz-architecture.md` 文档中，该文档提供了更全面的情绪数据集作为量表的架构设计。

## 10. Quiz设置迁移架构

### 10.1 迁移概述

Quiz设置已从通用Settings页面迁移到专门的QuizSettings页面，实现了功能分离和6层个性化配置架构。

#### 迁移前后对比
```typescript
// 迁移前：Settings页面包含Quiz配置
interface OldSettingsStructure {
  // 通用设置
  language: string;
  theme: string;

  // Quiz相关设置（混合在一起）
  preferred_view_type: ViewType;
  render_engine_preferences: RenderEnginePreferences;
  content_display_mode: ContentDisplayMode;
  // ...
}

// 迁移后：分离的配置架构
interface NewArchitecture {
  // Settings页面：仅通用设置
  generalSettings: {
    language: string;
    theme: string;
    notifications: boolean;
    accessibility: AccessibilitySettings;
  };

  // QuizSettings页面：6层Quiz配置
  quizSettings: {
    layer0_dataset_presentation: Layer0Config;
    layer1_user_choice: Layer1Config;
    layer2_rendering_strategy: Layer2Config;
    layer3_skin_base: Layer3Config;
    layer4_view_details: Layer4Config;
    layer5_accessibility: Layer5Config;
  };
}
```

### 10.2 6层配置架构集成

#### Layer 0: 数据集展现配置
```typescript
interface Layer0DatasetPresentation {
  default_difficulty_preference: 'beginner' | 'regular' | 'advanced' | 'vip';
  session_length_preference: 'short' | 'medium' | 'long';
  preferred_pack_categories: string[];
  auto_select_recommended: boolean;
}
```

#### Layer 2: 渲染策略配置
```typescript
interface Layer2RenderingStrategy {
  render_engine_preferences: {
    wheel: 'D3' | 'SVG' | 'Canvas' | 'R3F';
    card: 'CSS' | 'SVG' | 'Canvas';
    bubble: 'Canvas' | 'R3F' | 'WebGL';
    galaxy: 'R3F' | 'WebGL' | 'Three.js';
  };
  content_display_mode_preferences: {
    wheel: 'text' | 'emoji' | 'textEmoji' | 'icon';
    card: 'text' | 'emoji' | 'image' | 'mixed';
    bubble: 'emoji' | 'color' | 'size' | 'mixed';
  };
  performance_mode: 'performance' | 'balanced' | 'quality';
}
```

#### Layer 3: 皮肤基础配置
```typescript
interface Layer3SkinBase {
  fonts: {
    primary_font: string;
    size_scale: number;
  };
  animations: {
    enable_animations: boolean;
    animation_speed: 'slow' | 'normal' | 'fast';
    reduce_motion: boolean;
  };
}
```

### 10.3 用户体验流程

#### 配置访问流程
```mermaid
graph TD
    A[Settings页面] --> B[Quiz系统配置入口]
    B --> C[QuizSettings页面]
    C --> D[6层配置选项卡]
    D --> E[Layer 0: 数据集配置]
    D --> F[Layer 1: 用户选择]
    D --> G[Layer 2: 渲染策略]
    D --> H[Layer 3: 皮肤基础]
    D --> I[Layer 4: 视图细节]
    D --> J[Layer 5: 可访问性]
```

#### 配置应用流程
```typescript
// 配置应用工作流
class QuizConfigurationWorkflow {
  async applyConfiguration(userId: string): Promise<QuizConfiguration> {
    // 1. 加载用户的6层配置
    const userConfig = await this.loadUserQuizConfig(userId);

    // 2. 验证配置兼容性
    const validatedConfig = await this.validateConfiguration(userConfig);

    // 3. 应用配置到ViewFactory
    const viewFactoryConfig = this.transformToViewFactoryConfig(validatedConfig);

    // 4. 初始化Quiz引擎
    const quizEngine = new CoreQuizEngine(viewFactoryConfig);

    return {
      config: validatedConfig,
      engine: quizEngine,
      viewFactory: new ViewFactory(viewFactoryConfig)
    };
  }
}
```

### 10.4 数据迁移策略

#### 自动迁移脚本
```typescript
class QuizSettingsMigration {
  async migrateFromLegacySettings(userId: string): Promise<void> {
    // 1. 读取旧的Settings配置
    const legacyConfig = await this.getLegacyUserConfig(userId);

    // 2. 映射到新的6层架构
    const newQuizConfig = {
      layer0_dataset_presentation: {
        default_difficulty_preference: this.mapDifficultyLevel(legacyConfig),
        session_length_preference: 'medium',
        preferred_pack_categories: this.inferPackCategories(legacyConfig),
        auto_select_recommended: true
      },
      layer1_user_choice: {
        preferred_view_type: legacyConfig.preferred_view_type || 'wheel',
        active_skin_id: legacyConfig.active_skin_id || 'default',
        dark_mode: legacyConfig.dark_mode || false,
        color_mode: legacyConfig.color_mode || 'warm'
      },
      layer2_rendering_strategy: {
        render_engine_preferences: legacyConfig.render_engine_preferences || this.getDefaultEngines(),
        content_display_mode_preferences: legacyConfig.content_display_mode_preferences || this.getDefaultModes(),
        performance_mode: 'balanced'
      },
      // ... 其他层级配置
    };

    // 3. 保存新配置
    await this.saveQuizConfiguration(userId, newQuizConfig);

    // 4. 清理旧配置中的Quiz相关字段
    await this.cleanupLegacyQuizSettings(userId);
  }
}
```

### 10.5 向后兼容性

#### 兼容性保证
```typescript
class BackwardCompatibility {
  // 确保旧的API调用仍然有效
  async getLegacyViewType(userId: string): Promise<ViewType> {
    const quizConfig = await this.getQuizConfiguration(userId);
    return quizConfig.layer1_user_choice.preferred_view_type;
  }

  // 提供迁移提示
  async checkMigrationStatus(userId: string): Promise<MigrationStatus> {
    const hasLegacyConfig = await this.hasLegacyQuizSettings(userId);
    const hasNewConfig = await this.hasQuizConfiguration(userId);

    if (hasLegacyConfig && !hasNewConfig) {
      return { status: 'needs_migration', action: 'auto_migrate' };
    }

    if (hasLegacyConfig && hasNewConfig) {
      return { status: 'migration_complete', action: 'cleanup_legacy' };
    }

    return { status: 'up_to_date', action: 'none' };
  }
}
```

### 10.6 测试策略

#### 迁移测试
```typescript
describe('Quiz Settings Migration', () => {
  test('should migrate legacy settings to 6-layer architecture', async () => {
    const legacyConfig = createLegacyConfig();
    const migration = new QuizSettingsMigration();

    await migration.migrateFromLegacySettings('user123');

    const newConfig = await getQuizConfiguration('user123');
    expect(newConfig).toHaveProperty('layer0_dataset_presentation');
    expect(newConfig).toHaveProperty('layer1_user_choice');
    expect(newConfig).toHaveProperty('layer2_rendering_strategy');
  });

  test('should maintain user preferences during migration', async () => {
    const legacyConfig = {
      preferred_view_type: 'wheel',
      dark_mode: true,
      color_mode: 'cool'
    };

    await migration.migrateFromLegacySettings('user123');
    const newConfig = await getQuizConfiguration('user123');

    expect(newConfig.layer1_user_choice.preferred_view_type).toBe('wheel');
    expect(newConfig.layer1_user_choice.dark_mode).toBe(true);
    expect(newConfig.layer1_user_choice.color_mode).toBe('cool');
  });
});
```

## 9. 原有ViewFactory功能保留

### 9.1 支持的视图类型

ViewFactory 支持以下视图类型：

- **wheel**: 轮盘视图，以圆形方式展示情绪
- **card**: 卡片视图，以卡片方式展示情绪
- **bubble**: 气泡视图，以气泡方式展示情绪
- **galaxy**: 星系视图，以星系方式展示情绪
- **list**: 列表视图（计划中）
- **grid**: 网格视图（计划中）
- **tree**: 树形视图（计划中）
- **flow**: 流程图视图（计划中）
- **tagCloud**: 标签云视图（计划中）

### 9.2 支持的渲染引擎

ViewFactory 支持以下渲染引擎：

- **D3**: 使用 D3.js 实现，适合复杂的数据可视化
- **SVG**: 使用原生 SVG 实现，适合简单的图形
- **R3F**: 使用 React Three Fiber 实现，适合 3D 效果
- **Canvas**: 使用 Canvas API 实现，适合高性能图形
- **WebGL**: 使用 WebGL 实现，适合复杂的 3D 效果
- **WebGPU**: 使用 WebGPU 实现，适合高性能 3D 效果

### 9.3 支持的内容显示模式

ViewFactory 支持以下内容显示模式：

- **text**: 仅显示文本
- **emoji**: 仅显示表情
- **textEmoji**: 同时显示文本和表情
- **animatedEmoji**: 显示动画表情
- **image**: 显示图片（计划中）

### 9.4 ViewFactory 核心方法

ViewFactory 提供了以下方法来创建视图：

- **createViewFromUserConfig**: 根据用户配置创建视图，是创建视图的首选方法
- **createView**: 传统方法，保留向后兼容性
- **createWheel**: 创建轮盘视图
- **createCard**: 创建卡片视图
- **createBubble**: 创建气泡视图
- **createGalaxy**: 创建星系视图

### 9.5 皮肤兼容性功能

ViewFactory 提供了皮肤兼容性检查功能，确保用户选择的视图类型、内容显示模式和渲染引擎与当前皮肤兼容。

#### 9.5.1 视图类型兼容性
检查皮肤是否支持指定的视图类型。如果不支持，会回退到皮肤支持的默认视图类型。

#### 9.5.2 内容显示模式兼容性
检查皮肤是否支持指定的内容显示模式。如果不支持，会回退到皮肤支持的默认内容显示模式。

#### 9.5.3 渲染引擎兼容性
检查皮肤是否支持指定的渲染引擎。如果不支持，会回退到皮肤支持的默认渲染引擎。

### 9.6 轮盘视图特殊配置

ViewFactory 支持轮盘视图的特殊配置，包括：
- 自定义轮盘大小
- 自定义扇区间隙和边框
- 3D 效果
- 动画效果
- 交互行为

### 9.7 类图

```
+------------------+       +------------------+       +------------------+
|   ViewFactory    |------>|   EmotionView    |<------|  BaseEmotionView |
+------------------+       +------------------+       +------------------+
        |                          ^                          ^
        |                          |                          |
        v                          |                          |
+------------------+               |                          |
|   SkinManager    |               |                          |
+------------------+               |                          |
                                   |                          |
                    +---------------------------+    +------------------+
                    |              |            |    |                  |
            +-------+------+ +-----+------+ +---+----------+ +----------+-----+
            | D3WheelView  | | SVGWheelView| | R3FWheelView | | CanvasWheelView|
            +--------------+ +------------+ +--------------+ +----------------+
```

### 9.8 实现状态

#### 9.8.1 已完成功能 ✅
1. ✅ 定义 `EmotionView` 接口，包含所有视图类型共有的方法
2. ✅ 实现 `BaseEmotionView` 抽象类，实现 `EmotionView` 接口的基本功能
3. ✅ 实现 `D3WheelView` 类，使用 D3.js 实现轮盘视图
4. ✅ 实现 `SVGWheelView` 类，使用原生 SVG 实现轮盘视图
5. ✅ 实现 `R3FWheelView` 类，使用 React Three Fiber 实现轮盘视图
6. ✅ 实现 `CardView` 类，实现卡片视图
7. ✅ 实现 `BubbleView` 类，实现气泡视图
8. ✅ 实现 `GalaxyView` 类，实现星系视图
9. ✅ 实现皮肤兼容性检查功能
10. ✅ 实现高级内容显示模式
11. ✅ 实现轮盘视图特殊配置
12. ✅ 实现高级用户功能
13. ✅ 实现设备适配功能
14. ✅ 实现视图配置和交互

#### 9.8.2 计划中功能 ⏳
1. ⏳ 网格视图
2. ⏳ 树形视图
3. ⏳ 流程图视图
4. ⏳ 标签云视图
5. ⏳ 更多的动画效果
6. ⏳ 更多的交互方式
7. ⏳ 更多的布局选项
8. ⏳ 更多的设备适配选项
