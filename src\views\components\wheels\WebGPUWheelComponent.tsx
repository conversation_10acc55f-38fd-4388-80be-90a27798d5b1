/**
 * WebGPU轮盘组件
 * 使用WebGPU实现的轮盘，不依赖旧的轮盘实现
 *
 * 此组件是新视图系统的一部分，用于替代旧的轮盘组件
 * 它直接实现了轮盘的渲染，不依赖旧的轮盘类
 *
 * 注意：WebGPU 是一个新的 API，目前仍在开发中，可能不被所有浏览器支持
 * 此组件会检查浏览器是否支持 WebGPU，如果不支持，会回退到 WebGL 渲染
 */

import { AnimatedEmoji } from '@/components/emoji/AnimatedEmoji';
import { useEmoji } from '@/contexts/EmojiContext';
import type { Emotion, ContentDisplayMode, SkinConfig } from '@/types';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { WebGLWheelComponent } from './WebGLWheelComponent';
import { extract_wheel_config_for_engine, type WheelConfig } from '../../../utils/wheelConfigExtractor';

interface WebGPUWheelComponentProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  onBack?: () => void;
  selectedPath?: any;
}
/**
 * 检查浏览器是否支持 WebGPU
 */
const isWebGPUSupported = (): boolean => {
  return 'gpu' in navigator;
};

/**
 * WebGPU轮盘组件
 */
export const WebGPUWheelComponent: React.FC<WebGPUWheelComponentProps> = (props) => {
  const [isSupported, setIsSupported] = useState<boolean | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [hoveredEmotion, setHoveredEmotion] = useState<string | null>(null);
  const { getEmojiItemForEmotionId } = useEmoji();

  // 提取轮盘配置
  const wheelConfig = extract_wheel_config_for_engine.webgpu(props.skinConfig);

  // 检查浏览器是否支持 WebGPU
  useEffect(() => {
    setIsSupported(isWebGPUSupported());
  }, []);

  // 如果浏览器不支持 WebGPU，回退到 WebGL 渲染
  if (isSupported === false) {
    console.warn('WebGPU is not supported by this browser, falling back to WebGL rendering');
    return <WebGLWheelComponent {...props} />;
  }

  // 如果还在检查是否支持 WebGPU，显示加载中
  if (isSupported === null) {
    return <div>Loading...</div>;
  }

  // 渲染动画表情（如果内容类型为 animatedEmoji）
  const renderAnimatedEmojis = () => {
    if (props.contentDisplayMode !== 'animatedEmoji' || !props.emotions.length) return null;

    return props.emotions.map((emotion, index) => {
      const angle = (index / props.emotions.length) * 2 * Math.PI;
      const radius = wheelConfig.wheel_radius * 0.7; // 调整半径，使表情位于扇区中心
      const x = Math.cos(angle) * radius + wheelConfig.container_size / 2;
      const y = Math.sin(angle) * radius + wheelConfig.container_size / 2;

      const emojiItem = getEmojiItemForEmotionId(emotion.id);

      return (
        <div
          key={emotion.id}
          style={{
            position: 'absolute',
            left: `${x}px`,
            top: `${y}px`,
            transform: 'translate(-50%, -50%)',
            zIndex: hoveredEmotion === emotion.id ? 10 : 1,
            transition: `all ${wheelConfig.transition_duration}ms`,
            cursor: 'pointer',
          }}
          onClick={() => props.onSelect(emotion)}
          onMouseEnter={() => setHoveredEmotion(emotion.id)}
          onMouseLeave={() => setHoveredEmotion(null)}
        >
          {emojiItem ? (
            <AnimatedEmoji
              emojiItem={emojiItem}
              size={
                wheelConfig.emojiSize >= 40
                  ? '4xl'
                  : wheelConfig.emojiSize >= 32
                    ? '3xl'
                    : wheelConfig.emojiSize >= 24
                      ? '2xl'
                      : wheelConfig.emojiSize >= 20
                        ? 'xl'
                        : wheelConfig.emojiSize >= 16
                          ? 'lg'
                          : wheelConfig.emojiSize >= 14
                            ? 'md'
                            : wheelConfig.emojiSize >= 12
                              ? 'sm'
                              : 'xs'
              }
              autoPlay={true}
              loop={true}
            />
          ) : (
            <span style={{ fontSize: `${wheelConfig.emojiSize}px` }}>{emotion.emoji}</span>
          )}
        </div>
      );
    });
  };

  // 渲染返回按钮
  const renderBackButton = () => {
    if (!props.onBack || props.tierLevel === 1) return null;

    return (
      <button
        onClick={props.onBack}
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          boxShadow: wheelConfig.shadow_enabled ? `0 0 5px ${wheelConfig.shadowColor}` : 'none',
        }}
      >
        ←
      </button>
    );
  };

  // 渲染选中路径
  const renderSelectedPath = () => {
    if (!props.selectedPath) return null;

    return (
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 100,
          fontSize: `${wheelConfig.font_size}px`,
          fontFamily: wheelConfig.font_family,
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '5px',
          background: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(5px)',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {props.selectedPath}
      </div>
    );
  };

  // 渲染 WebGPU 轮盘
  // 注意：WebGPU 是一个新的 API，目前仍在开发中，这里只是一个简单的实现
  // 实际的 WebGPU 实现会更加复杂，需要使用 WebGPU API 进行渲染
  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width: `${wheelConfig.container_size}px`,
        height: `${wheelConfig.container_size}px`,
        margin: '0 auto',
        borderRadius: '50%',
        overflow: 'hidden',
        boxShadow: wheelConfig.shadow_enabled
          ? `0 0 ${wheelConfig.shadowBlur}px ${wheelConfig.shadowColor}`
          : 'none',
        backgroundColor: wheelConfig.background_color,
      }}
    >
      {renderBackButton()}
      {renderSelectedPath()}
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          display: props.contentDisplayMode === 'animatedEmoji' ? 'none' : 'block',
        }}
      />
      {props.contentDisplayMode === 'animatedEmoji' && renderAnimatedEmojis()}
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: wheelConfig.text_color,
          fontSize: `${wheelConfig.font_size}px`,
          fontFamily: wheelConfig.font_family,
          textAlign: 'center',
        }}
      >
        WebGPU is supported, but the implementation is not yet complete.
        <br />
        This is a placeholder for the WebGPU wheel component.
      </div>
    </div>
  );
};
