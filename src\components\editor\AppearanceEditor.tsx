/**
 * 外观编辑器组件
 * 用于编辑皮肤的外观设置
 */

import { useLanguage } from '@/contexts/LanguageContext';
import type { EmotionData, ViewType, Skin, SkinConfig } from '@/types';
import type React from 'react';
import { useState } from 'react';
import { HexColorPicker } from 'react-colorful';
import { SkinPreview } from '../preview/SkinPreview';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Slider } from '../ui/slider';
import { Switch } from '../ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';

interface AppearanceEditorProps {
  skin: Skin;
  emotionData: EmotionData;
  onChange: (skin: Skin) => void;
}

/**
 * 外观编辑器组件
 */
export const AppearanceEditor: React.FC<AppearanceEditorProps> = ({
  skin,
  emotionData,
  onChange,
}) => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('colors');
  const [previewViewType, setPreviewViewType] = useState<ViewType>('wheel');

  // 处理颜色变化
  const handleColorChange = (colorKey: string, value: string) => {
    const updatedSkin = {
      ...skin,
      config: {
        ...skin.config,
        colors: {
          ...skin.config.colors,
          [colorKey]: value,
        },
      },
      updated_at: new Date().toISOString(),
    };

    onChange(updatedSkin);
  };

  // 处理字体变化
  const handleFontChange = (fontKey: string, value: string | number) => {
    const updatedSkin = {
      ...skin,
      config: {
        ...skin.config,
        fonts: {
          ...skin.config.fonts,
          [fontKey]: value,
        },
      },
      updated_at: new Date().toISOString(),
    };

    onChange(updatedSkin);
  };

  // 处理字体大小变化
  const handleFontSizeChange = (sizeKey: string, value: number) => {
    const updatedSkin = {
      ...skin,
      config: {
        ...skin.config,
        fonts: {
          ...skin.config.fonts,
          size: {
            ...skin.config.fonts.size,
            [sizeKey]: value,
          },
        },
      },
      updated_at: new Date().toISOString(),
    };

    onChange(updatedSkin);
  };

  // 处理字体粗细变化
  const handleFontWeightChange = (weightKey: string, value: number) => {
    const updatedSkin = {
      ...skin,
      config: {
        ...skin.config,
        fonts: {
          ...skin.config.fonts,
          weight: {
            ...skin.config.fonts.weight,
            [weightKey]: value,
          },
        },
      },
      updated_at: new Date().toISOString(),
    };

    onChange(updatedSkin);
  };

  // 处理效果变化
  const handleEffectChange = (effectKey: string, value: boolean | number) => {
    const updatedSkin = {
      ...skin,
      config: {
        ...skin.config,
        effects: {
          ...skin.config.effects,
          [effectKey]: value,
        },
      },
      updated_at: new Date().toISOString(),
    };

    onChange(updatedSkin);
  };

  // 处理视图配置变化
  const handleViewConfigChange = (viewType: string, configKey: string, value: any) => {
    const view_configs = skin.config.view_configs || {};
    const viewConfig = view_configs[viewType as keyof typeof view_configs] || {};

    const updatedSkin = {
      ...skin,
      config: {
        ...skin.config,
        view_configs: {
          ...view_configs,
          [viewType]: {
            ...viewConfig,
            [configKey]: value,
          },
        },
      },
      updated_at: new Date().toISOString(),
    };

    onChange(updatedSkin);
  };

  // 渲染颜色选择器
  const renderColorPicker = (colorKey: string, label: string, description?: string) => {
    const colorValue = skin.config.colors[colorKey as keyof typeof skin.config.colors] || '#FFFFFF';

    return (
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label htmlFor={`color-${colorKey}`}>{label}</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-10 h-10 p-0 rounded-md"
                style={{ backgroundColor: colorValue }}
              />
            </PopoverTrigger>
            <PopoverContent className="w-auto p-3">
              <HexColorPicker
                color={colorValue}
                onChange={(value) => handleColorChange(colorKey, value)}
              />
              <div className="flex mt-2">
                <Input
                  id={`color-${colorKey}`}
                  value={colorValue}
                  onChange={(e) => handleColorChange(colorKey, e.target.value)}
                  className="h-8"
                />
              </div>
            </PopoverContent>
          </Popover>
        </div>
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
      </div>
    );
  };

  return (
    <div className="appearance-editor">
      <div className="flex flex-col md:flex-row gap-4">
        {/* 设置面板 */}
        <div className="w-full md:w-1/2">
          <Card>
            <CardHeader>
              <CardTitle>{t('appearance_editor.title', { fallback: '外观设置' })}</CardTitle>
              <CardDescription>
                {t('appearance_editor.description', { fallback: '自定义皮肤的外观' })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="colors">
                    {t('appearance_editor.colors', { fallback: '颜色' })}
                  </TabsTrigger>
                  <TabsTrigger value="fonts">
                    {t('appearance_editor.fonts', { fallback: '字体' })}
                  </TabsTrigger>
                  <TabsTrigger value="effects">
                    {t('appearance_editor.effects', { fallback: '效果' })}
                  </TabsTrigger>
                  <TabsTrigger value="wheel">
                    {t('appearance_editor.wheel', { fallback: '轮盘' })}
                  </TabsTrigger>
                  <TabsTrigger value="card">
                    {t('appearance_editor.card', { fallback: '卡片' })}
                  </TabsTrigger>
                  <TabsTrigger value="bubble">
                    {t('appearance_editor.bubble', { fallback: '气泡' })}
                  </TabsTrigger>
                </TabsList>

                {/* 颜色设置 */}
                <TabsContent value="colors" className="space-y-4">
                  {renderColorPicker(
                    'primary',
                    t('appearance_editor.primary_color', { fallback: '主色' })
                  )}
                  {renderColorPicker(
                    'secondary',
                    t('appearance_editor.secondary_color', { fallback: '次色' })
                  )}
                  {renderColorPicker(
                    'background',
                    t('appearance_editor.background_color', { fallback: '背景色' })
                  )}
                  {renderColorPicker(
                    'text',
                    t('appearance_editor.text_color', { fallback: '文本色' })
                  )}
                  {renderColorPicker(
                    'accent',
                    t('appearance_editor.accent_color', { fallback: '强调色' })
                  )}
                </TabsContent>

                {/* 字体设置 */}
                <TabsContent value="fonts" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="font-family">
                      {t('appearance_editor.font_family', { fallback: '字体族' })}
                    </Label>
                    <Select
                      value={skin.config.fonts.family}
                      onValueChange={(value) => handleFontChange('family', value)}
                    >
                      <SelectTrigger id="font-family">
                        <SelectValue
                          placeholder={t('appearance_editor.select_font', { fallback: '选择字体' })}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sans-serif">
                          {t('appearance_editor.sans_serif', { fallback: '无衬线字体' })}
                        </SelectItem>
                        <SelectItem value="serif">
                          {t('appearance_editor.serif', { fallback: '衬线字体' })}
                        </SelectItem>
                        <SelectItem value="monospace">
                          {t('appearance_editor.monospace', { fallback: '等宽字体' })}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="font-size-small">
                      {t('appearance_editor.small_font_size', { fallback: '小字体大小' })}:{' '}
                      {skin.config.fonts.size.small}px
                    </Label>
                    <Slider
                      id="font-size-small"
                      min={8}
                      max={16}
                      step={1}
                      value={[skin.config.fonts.size.small]}
                      onValueChange={(value) => handleFontSizeChange('small', value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="font-size-medium">
                      {t('appearance_editor.medium_font_size', { fallback: '中字体大小' })}:{' '}
                      {skin.config.fonts.size.medium}px
                    </Label>
                    <Slider
                      id="font-size-medium"
                      min={12}
                      max={24}
                      step={1}
                      value={[skin.config.fonts.size.medium]}
                      onValueChange={(value) => handleFontSizeChange('medium', value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="font-size-large">
                      {t('appearance_editor.large_font_size', { fallback: '大字体大小' })}:{' '}
                      {skin.config.fonts.size.large}px
                    </Label>
                    <Slider
                      id="font-size-large"
                      min={16}
                      max={32}
                      step={1}
                      value={[skin.config.fonts.size.large]}
                      onValueChange={(value) => handleFontSizeChange('large', value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="font-weight-normal">
                      {t('appearance_editor.normal_font_weight', { fallback: '普通字重' })}:{' '}
                      {skin.config.fonts.weight.normal}
                    </Label>
                    <Slider
                      id="font-weight-normal"
                      min={300}
                      max={500}
                      step={100}
                      value={[skin.config.fonts.weight.normal]}
                      onValueChange={(value) => handleFontWeightChange('normal', value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="font-weight-bold">
                      {t('appearance_editor.bold_font_weight', { fallback: '粗体字重' })}:{' '}
                      {skin.config.fonts.weight.bold}
                    </Label>
                    <Slider
                      id="font-weight-bold"
                      min={600}
                      max={900}
                      step={100}
                      value={[skin.config.fonts.weight.bold]}
                      onValueChange={(value) => handleFontWeightChange('bold', value[0])}
                    />
                  </div>
                </TabsContent>

                {/* 效果设置 */}
                <TabsContent value="effects" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="shadows">
                      {t('appearance_editor.shadows', { fallback: '阴影' })}
                    </Label>
                    <Switch
                      id="shadows"
                      checked={skin.config.effects.shadows}
                      onCheckedChange={(value) => handleEffectChange('shadows', value)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="animations">
                      {t('appearance_editor.animations', { fallback: '动画' })}
                    </Label>
                    <Switch
                      id="animations"
                      checked={skin.config.effects.animations}
                      onCheckedChange={(value) => handleEffectChange('animations', value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="border-radius">
                      {t('appearance_editor.border_radius', { fallback: '边框圆角' })}:{' '}
                      {skin.config.effects.border_radius}px
                    </Label>
                    <Slider
                      id="border-radius"
                      min={0}
                      max={20}
                      step={1}
                      value={[skin.config.effects.border_radius]}
                      onValueChange={(value) => handleEffectChange('borderRadius', value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="opacity">
                      {t('appearance_editor.opacity', { fallback: '透明度' })}:{' '}
                      {skin.config.effects.opacity * 100}%
                    </Label>
                    <Slider
                      id="opacity"
                      min={0.5}
                      max={1}
                      step={0.05}
                      value={[skin.config.effects.opacity]}
                      onValueChange={(value) => handleEffectChange('opacity', value[0])}
                    />
                  </div>
                </TabsContent>

                {/* 轮盘设置 */}
                <TabsContent value="wheel" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="wheel-container-size">
                      {t('appearance_editor.container_size', { fallback: '容器大小' })}:{' '}
                      {skin.config.view_configs?.wheel?.container_size || 300}px
                    </Label>
                    <Slider
                      id="wheel-container-size"
                      min={200}
                      max={500}
                      step={10}
                      value={[skin.config.view_configs?.wheel?.container_size || 300]}
                      onValueChange={(value) =>
                        handleViewConfigChange('wheel', 'containerSize', value[0])
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="wheel-radius">
                      {t('appearance_editor.wheel_radius', { fallback: '轮盘半径' })}:{' '}
                      {skin.config.view_configs?.wheel?.wheel_radius || 140}px
                    </Label>
                    <Slider
                      id="wheel-radius"
                      min={100}
                      max={250}
                      step={10}
                      value={[skin.config.view_configs?.wheel?.wheel_radius || 140]}
                      onValueChange={(value) =>
                        handleViewConfigChange('wheel', 'wheelRadius', value[0])
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sector-gap">
                      {t('appearance_editor.sector_gap', { fallback: '扇区间隙' })}:{' '}
                      {skin.config.view_configs?.wheel?.sectorGap || 1}px
                    </Label>
                    <Slider
                      id="sector-gap"
                      min={0}
                      max={5}
                      step={0.5}
                      value={[skin.config.view_configs?.wheel?.sectorGap || 1]}
                      onValueChange={(value) =>
                        handleViewConfigChange('wheel', 'sectorGap', value[0])
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="wheel-decorations">
                      {t('appearance_editor.decorations', { fallback: '装饰' })}
                    </Label>
                    <Switch
                      id="wheel-decorations"
                      checked={skin.config.view_configs?.wheel?.decorations || false}
                      onCheckedChange={(value) =>
                        handleViewConfigChange('wheel', 'decorations', value)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="wheel-3d-effects">
                      {t('appearance_editor.3d_effects', { fallback: '3D效果' })}
                    </Label>
                    <Switch
                      id="wheel-3d-effects"
                      checked={skin.config.view_configs?.wheel?.use3DEffects || false}
                      onCheckedChange={(value) =>
                        handleViewConfigChange('wheel', 'use3DEffects', value)
                      }
                    />
                  </div>
                </TabsContent>

                {/* 卡片设置 */}
                <TabsContent value="card" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="card-size">
                      {t('appearance_editor.card_size', { fallback: '卡片大小' })}:{' '}
                      {skin.config.view_configs?.card?.card_size || 100}px
                    </Label>
                    <Slider
                      id="card-size"
                      min={60}
                      max={200}
                      step={10}
                      value={[skin.config.view_configs?.card?.card_size || 100]}
                      onValueChange={(value) =>
                        handleViewConfigChange('card', 'cardSize', value[0])
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="card-spacing">
                      {t('appearance_editor.card_spacing', { fallback: '卡片间距' })}:{' '}
                      {skin.config.view_configs?.card?.card_spacing || 16}px
                    </Label>
                    <Slider
                      id="card-spacing"
                      min={4}
                      max={32}
                      step={4}
                      value={[skin.config.view_configs?.card?.card_spacing || 16]}
                      onValueChange={(value) =>
                        handleViewConfigChange('card', 'cardSpacing', value[0])
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="card-layout">
                      {t('appearance_editor.layout', { fallback: '布局' })}
                    </Label>
                    <Select
                      value={skin.config.view_configs?.card?.layout || 'grid'}
                      onValueChange={(value) => handleViewConfigChange('card', 'layout', value)}
                    >
                      <SelectTrigger id="card-layout">
                        <SelectValue
                          placeholder={t('appearance_editor.select_layout', {
                            fallback: '选择布局',
                          })}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="grid">
                          {t('appearance_editor.grid_layout', { fallback: '网格布局' })}
                        </SelectItem>
                        <SelectItem value="flow">
                          {t('appearance_editor.flow_layout', { fallback: '流式布局' })}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>

                {/* 气泡设置 */}
                <TabsContent value="bubble" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="bubble-size">
                      {t('appearance_editor.bubble_size', { fallback: '气泡大小' })}:{' '}
                      {skin.config.view_configs?.bubble?.bubble_size || 70}px
                    </Label>
                    <Slider
                      id="bubble-size"
                      min={40}
                      max={120}
                      step={5}
                      value={[skin.config.view_configs?.bubble?.bubble_size || 70]}
                      onValueChange={(value) =>
                        handleViewConfigChange('bubble', 'bubbleSize', value[0])
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bubble-spacing">
                      {t('appearance_editor.bubble_spacing', { fallback: '气泡间距' })}:{' '}
                      {skin.config.view_configs?.bubble?.bubble_spacing || 10}px
                    </Label>
                    <Slider
                      id="bubble-spacing"
                      min={0}
                      max={30}
                      step={5}
                      value={[skin.config.view_configs?.bubble?.bubble_spacing || 10]}
                      onValueChange={(value) =>
                        handleViewConfigChange('bubble', 'bubbleSpacing', value[0])
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bubble-layout">
                      {t('appearance_editor.layout', { fallback: '布局' })}
                    </Label>
                    <Select
                      value={skin.config.view_configs?.bubble?.layout || 'circle'}
                      onValueChange={(value) => handleViewConfigChange('bubble', 'layout', value)}
                    >
                      <SelectTrigger id="bubble-layout">
                        <SelectValue
                          placeholder={t('appearance_editor.select_layout', {
                            fallback: '选择布局',
                          })}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="circle">
                          {t('appearance_editor.circle_layout', { fallback: '圆形布局' })}
                        </SelectItem>
                        <SelectItem value="float">
                          {t('appearance_editor.float_layout', { fallback: '浮动布局' })}
                        </SelectItem>
                        <SelectItem value="spiral">
                          {t('appearance_editor.spiral_layout', { fallback: '螺旋布局' })}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* 预览面板 */}
        <div className="w-full md:w-1/2">
          <Card>
            <CardHeader>
              <CardTitle>{t('appearance_editor.preview', { fallback: '预览' })}</CardTitle>
              <div className="flex justify-between items-center">
                <CardDescription>
                  {t('appearance_editor.preview_description', { fallback: '预览皮肤效果' })}
                </CardDescription>

                <Select
                  value={previewViewType}
                  onValueChange={(value) => setPreviewViewType(value as ViewType)}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue
                      placeholder={t('appearance_editor.select_view', { fallback: '选择视图' })}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wheel">
                      {t('appearance_editor.wheel_view', { fallback: '轮盘视图' })}
                    </SelectItem>
                    <SelectItem value="card">
                      {t('appearance_editor.card_view', { fallback: '卡片视图' })}
                    </SelectItem>
                    <SelectItem value="bubble">
                      {t('appearance_editor.bubble_view', { fallback: '气泡视图' })}
                    </SelectItem>
                    <SelectItem value="galaxy">
                      {t('appearance_editor.galaxy_view', { fallback: '星系视图' })}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <SkinPreview
                emotionData={emotionData}
                skin={skin}
                viewType={previewViewType}
                contentDisplayMode="textEmoji"
                RenderEngine="D3"
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
