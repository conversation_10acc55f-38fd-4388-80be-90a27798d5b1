/**
 * 视图容器组件
 * 用于按需渲染视图组件，提高性能
 */

import type { Emotion, ContentDisplayMode, ViewType, SkinConfig } from '@/types';
import { useFpsLimit, useThrottledCallback } from '@/utils/renderOptimizer';
import { ViewFactory } from '@/utils/viewFactory';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import LazyView from './LazyView';

interface ViewContainerProps {
  viewType: ViewType;
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  options?: Record<string, any>;
  lazyLoad?: boolean;
  className?: string;
}

/**
 * 视图容器组件
 * 用于按需渲染视图组件，提高性能
 */
const ViewContainer: React.FC<ViewContainerProps> = ({
  viewType,
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  options = {},
  lazyLoad = false,
  className = '',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isActive, setIsActive] = useState(true);
  const [isVisible, setIsVisible] = useState(true);
  const shouldRender = useFpsLimit(30); // 限制帧率为30fps

  // 使用节流的选择处理函数，避免频繁触发
  const handleSelect = useThrottledCallback(onSelect, 200);

  // 使用 IntersectionObserver 检测组件是否在视口中可见
  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      {
        rootMargin: '100px', // 提前100px开始加载
        threshold: 0.1, // 当10%的组件可见时开始加载
      }
    );

    observer.observe(containerRef.current);

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, []);

  // 使用 Page Visibility API 检测页面是否活跃
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsActive(document.visibilityState === 'visible');
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 如果组件不可见或页面不活跃，不渲染内容
  if (!isVisible || !isActive) {
    return (
      <div
        ref={containerRef}
        className={`view-container ${className}`}
        style={{ minHeight: '100px' }}
      />
    );
  }

  // 如果需要限制帧率且当前帧不应该渲染，使用上一帧的内容
  if (!shouldRender) {
    return <div ref={containerRef} className={`view-container ${className}`} />;
  }

  // 如果启用懒加载，使用 LazyView 组件
  if (lazyLoad) {
    return (
      <div ref={containerRef} className={`view-container ${className}`}>
        <LazyView
          viewType={viewType}
          emotions={emotions}
          tierLevel={tierLevel}
          contentDisplayMode={contentDisplayMode}
          skinConfig={skinConfig}
          onSelect={handleSelect}
          layout={options.layout}
          implementation={options.implementation}
        />
      </div>
    );
  }

  // 否则，直接使用 ViewFactory 创建视图
  const view = ViewFactory.createView(viewType, contentDisplayMode, skinConfig, options);

  return (
    <div ref={containerRef} className={`view-container ${className}`}>
      {view.render(emotions, tierLevel, handleSelect)}
    </div>
  );
};

export default ViewContainer;
