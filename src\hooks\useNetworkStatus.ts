/**
 * 网络状态 Hook
 * 监控网络连接状态，支持Web和移动端
 */

import { useState, useEffect } from 'react';
import { Network } from '@capacitor/network';
import { Capacitor } from '@capacitor/core';

export interface UseNetworkStatusReturn {
  isOnline: boolean;
  connectionType: string;
  isConnected: boolean;
  lastOnlineTime: Date | null;
  lastOfflineTime: Date | null;
}

export const useNetworkStatus = (): UseNetworkStatusReturn => {
  const [isOnline, setIsOnline] = useState(true);
  const [connectionType, setConnectionType] = useState('unknown');
  const [lastOnlineTime, setLastOnlineTime] = useState<Date | null>(null);
  const [lastOfflineTime, setLastOfflineTime] = useState<Date | null>(null);

  useEffect(() => {
    let mounted = true;

    const updateNetworkStatus = (status: { connected: boolean; connectionType: string }) => {
      if (!mounted) return;
      
      const wasOnline = isOnline;
      const nowOnline = status.connected;
      
      setIsOnline(nowOnline);
      setConnectionType(status.connectionType);
      
      // 记录状态变化时间
      if (wasOnline !== nowOnline) {
        if (nowOnline) {
          setLastOnlineTime(new Date());
        } else {
          setLastOfflineTime(new Date());
        }
      }
    };

    const initializeNetworkStatus = async () => {
      try {
        if (Capacitor.isNativePlatform()) {
          // 移动端：使用Capacitor Network API
          const status = await Network.getStatus();
          updateNetworkStatus(status);

          // 监听网络状态变化
          const listener = Network.addListener('networkStatusChange', updateNetworkStatus);
          
          return () => {
            listener.remove();
          };
        } else {
          // Web端：使用浏览器API
          const updateWebStatus = () => {
            updateNetworkStatus({
              connected: navigator.onLine,
              connectionType: (navigator as any).connection?.effectiveType || 'unknown'
            });
          };

          // 初始状态
          updateWebStatus();

          // 监听网络状态变化
          window.addEventListener('online', updateWebStatus);
          window.addEventListener('offline', updateWebStatus);

          return () => {
            window.removeEventListener('online', updateWebStatus);
            window.removeEventListener('offline', updateWebStatus);
          };
        }
      } catch (error) {
        console.error('Error initializing network status:', error);
        // 默认假设在线
        updateNetworkStatus({ connected: true, connectionType: 'unknown' });
      }
    };

    let cleanup: (() => void) | undefined;
    
    initializeNetworkStatus().then((cleanupFn) => {
      cleanup = cleanupFn;
    });

    return () => {
      mounted = false;
      cleanup?.();
    };
  }, [isOnline]);

  return {
    isOnline,
    connectionType,
    isConnected: isOnline,
    lastOnlineTime,
    lastOfflineTime
  };
};
