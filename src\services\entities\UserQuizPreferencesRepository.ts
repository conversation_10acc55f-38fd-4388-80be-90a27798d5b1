/**
 * 用户Quiz偏好配置 Repository
 * 管理用户的Quiz系统个性化配置
 */

import { BaseRepository } from '../base/BaseRepository';
import type { 
  UserQuizPreferences,
  CreateUserQuizPreferencesInput,
  UpdateUserQuizPreferencesInput 
} from '../../types/schema';

export class UserQuizPreferencesRepository extends BaseRepository<
  UserQuizPreferences,
  CreateUserQuizPreferencesInput,
  UpdateUserQuizPreferencesInput
> {
  protected tableName = 'user_presentation_configs';

  /**
   * 根据用户ID查找偏好配置 (别名方法)
   */
  async findByUserId(userId: string, configName: string = 'default'): Promise<UserQuizPreferences | null> {
    return this.getUserPreferences(userId, configName);
  }

  /**
   * 获取用户的Quiz偏好配置
   */
  async getUserPreferences(userId: string, configName: string = 'default'): Promise<UserQuizPreferences | null> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE user_id = ? AND config_name = ? AND is_active = 1
        ORDER BY updated_at DESC
        LIMIT 1
      `;
      
      const result = await db.query(query, [userId, configName]);
      const preferences = result.values?.[0];
      
      return preferences ? this.mapRowToEntity(preferences) : null;
    } catch (error) {
      console.error('Error getting user quiz preferences:', error);
      return null;
    }
  }

  /**
   * 获取用户的默认Quiz偏好配置
   */
  async getDefaultUserPreferences(userId: string): Promise<UserQuizPreferences | null> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE user_id = ? AND is_default = 1 AND is_active = 1
        ORDER BY updated_at DESC
        LIMIT 1
      `;
      
      const result = await db.query(query, [userId]);
      const preferences = result.values?.[0];
      
      return preferences ? this.mapRowToEntity(preferences) : null;
    } catch (error) {
      console.error('Error getting default user quiz preferences:', error);
      return null;
    }
  }

  /**
   * 获取用户的所有Quiz偏好配置
   */
  async getAllUserPreferences(userId: string): Promise<UserQuizPreferences[]> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE user_id = ?
        ORDER BY is_default DESC, is_active DESC, updated_at DESC
      `;
      
      const result = await db.query(query, [userId]);
      return (result.values || []).map(row => this.mapRowToEntity(row));
    } catch (error) {
      console.error('Error getting all user quiz preferences:', error);
      return [];
    }
  }

  /**
   * 更新用户Quiz偏好配置
   */
  async updateUserPreferences(
    userId: string, 
    configName: string, 
    updates: UpdateUserQuizPreferencesInput
  ): Promise<UserQuizPreferences | null> {
    try {
      // 先获取现有配置
      const existingPreferences = await this.getUserPreferences(userId, configName);
      
      if (!existingPreferences) {
        // 如果不存在，创建新配置
        const newPreferencesData: CreateUserQuizPreferencesInput = {
          id: this.generateId(),
          user_id: userId,
          config_name: configName,
          presentation_config: updates.presentation_config || this.getDefaultPresentationConfig(),
          config_version: updates.config_version || '2.0',
          personalization_level: updates.personalization_level || 50,
          is_active: updates.is_active ?? true,
          is_default: updates.is_default ?? false
        };
        
        return await this.create(newPreferencesData);
      }
      
      // 更新现有配置
      const updatedPreferences = await this.update(existingPreferences.id!, updates);
      return updatedPreferences;
    } catch (error) {
      console.error('Error updating user quiz preferences:', error);
      return null;
    }
  }

  /**
   * 创建用户Quiz偏好配置
   */
  async createUserPreferences(data: CreateUserQuizPreferencesInput): Promise<UserQuizPreferences | null> {
    try {
      // 如果创建的是默认配置，先将其他默认配置取消
      if (data.is_default) {
        await this.unsetOtherDefaults(data.user_id);
      }
      
      return await this.create(data);
    } catch (error) {
      console.error('Error creating user quiz preferences:', error);
      return null;
    }
  }

  /**
   * 取消其他默认配置
   */
  private async unsetOtherDefaults(userId: string): Promise<void> {
    try {
      const db = this.getDb();
      const query = `
        UPDATE ${this.tableName} 
        SET is_default = 0, updated_at = ?
        WHERE user_id = ? AND is_default = 1
      `;
      
      await db.query(query, [new Date().toISOString(), userId]);
    } catch (error) {
      console.error('Error unsetting other defaults:', error);
    }
  }

  /**
   * 获取默认展现配置
   */
  private getDefaultPresentationConfig(): string {
    return JSON.stringify({
      layer0_dataset_presentation: {
        preferred_pack_categories: ['daily', 'assessment'],
        default_difficulty_preference: 'regular',
        session_length_preference: 'medium',
        auto_select_recommended: false,
        restore_progress: true,
        question_display_fields: {
          show_question_text: true,
          show_question_description: true,
          show_question_order: true,
          show_progress_indicator: true,
          show_answer_options: true,
          show_option_descriptions: false,
          show_option_icons: true
        },
        interaction_behavior: {
          auto_advance_after_selection: false,
          auto_advance_delay_ms: 1500,
          allow_answer_change: true,
          show_confirmation_dialog: false
        }
      },
      layer1_user_choice: {
        preferred_view_type: 'wheel',
        active_skin_id: 'default',
        color_mode: 'warm',
        user_level: 'regular'
      },
      layer2_rendering_strategy: {
        render_engine_preferences: {
          wheel: 'D3',
          card: 'SVG',
          bubble: 'Canvas'
        },
        content_display_mode_preferences: {
          wheel: ['text', 'emoji'],
          card: ['text', 'image'],
          bubble: ['emoji']
        },
        layout_preferences: {
          wheel: 'circular_balanced',
          card: 'grid_responsive',
          bubble: 'organic_flow'
        },
        performance_mode: 'balanced',
        supported_content_types: {
          text: true,
          emoji: true,
          image: true,
          icon: true,
          audio: false,
          video: false,
          animation: true,
          rich_text: false
        }
      },
      layer3_skin_base: {
        available_skins: [
          { id: 'default', name: '默认皮肤', description: '简洁现代的默认界面风格', category: 'basic' }
        ],
        selected_skin_id: 'default',
        colors: {
          primary: '#4F46E5',
          secondary: '#7C3AED',
          accent: '#F59E0B'
        },
        fonts: {
          primary_font: 'Inter',
          size_scale: 1.0
        },
        animations: {
          enable_animations: true,
          animation_speed: 'normal',
          reduce_motion: false
        }
      },
      layer4_view_detail: {
        wheel_config: {
          container_size: 400,
          wheel_radius: 180,
          emotion_display_mode: 'hierarchical',
          tier_spacing: 60,
          center_radius: 40,
          show_labels: true,
          show_emojis: true
        },
        emotion_presentation: {
          emotion_grouping_style: 'by_category',
          tier_transition_animation: 'fade'
        }
      },
      layer5_accessibility: {
        high_contrast: false,
        large_text: false,
        reduce_motion: false,
        keyboard_navigation: true,
        voice_guidance: false
      }
    });
  }

  /**
   * 生成ID
   */
  protected generateId(): string {
    return `quiz_pref_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 提取创建数据中的ID
   */
  protected extractIdFromCreateData(data: CreateUserQuizPreferencesInput): string {
    return data.id || this.generateId();
  }

  /**
   * 构建插入查询
   */
  protected buildInsertQuery(data: CreateUserQuizPreferencesInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const id = data.id || this.generateId();

    const query = `
      INSERT INTO ${this.tableName} (
        id, user_id, config_name, presentation_config, config_version,
        personalization_level, is_active, is_default, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      id,
      data.user_id,
      data.config_name,
      data.presentation_config,
      data.config_version || '2.0',
      data.personalization_level || 50,
      data.is_active ? 1 : 0,
      data.is_default ? 1 : 0,
      now,
      now
    ];

    return { query, values };
  }

  /**
   * 构建更新查询
   */
  protected buildUpdateQuery(id: string, data: UpdateUserQuizPreferencesInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.config_name !== undefined) {
      fields.push('config_name = ?');
      values.push(data.config_name);
    }

    if (data.presentation_config !== undefined) {
      fields.push('presentation_config = ?');
      values.push(data.presentation_config);
    }

    if (data.config_version !== undefined) {
      fields.push('config_version = ?');
      values.push(data.config_version);
    }

    if (data.personalization_level !== undefined) {
      fields.push('personalization_level = ?');
      values.push(data.personalization_level);
    }

    if (data.is_active !== undefined) {
      fields.push('is_active = ?');
      values.push(data.is_active ? 1 : 0);
    }

    if (data.is_default !== undefined) {
      fields.push('is_default = ?');
      values.push(data.is_default ? 1 : 0);
    }

    // Always update updated_at
    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  /**
   * 映射数据库行到实体
   */
  protected mapRowToEntity(row: any): UserQuizPreferences {
    return {
      id: row.id,
      user_id: row.user_id,
      config_name: row.config_name,
      presentation_config: row.presentation_config,
      config_version: row.config_version,
      personalization_level: row.personalization_level,
      is_active: Boolean(row.is_active),
      is_default: Boolean(row.is_default),
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  /**
   * 映射实体到数据库行
   */
  protected mapEntityToRow(entity: UserQuizPreferences): any {
    return {
      id: entity.id,
      user_id: entity.user_id,
      config_name: entity.config_name,
      presentation_config: entity.presentation_config,
      config_version: entity.config_version,
      personalization_level: entity.personalization_level,
      is_active: entity.is_active ? 1 : 0,
      is_default: entity.is_default ? 1 : 0,
      created_at: entity.created_at,
      updated_at: entity.updated_at
    };
  }

  /**
   * 验证创建数据
   */
  protected async validateCreate(data: CreateUserQuizPreferencesInput): Promise<void> {
    if (!data.user_id) {
      throw new Error('User ID is required');
    }
    if (!data.config_name) {
      throw new Error('Config name is required');
    }
    if (!data.presentation_config) {
      throw new Error('Presentation config is required');
    }
  }

  /**
   * 验证更新数据
   */
  protected async validateUpdate(_data: UpdateUserQuizPreferencesInput): Promise<void> {
    // 基本验证即可
  }
}
