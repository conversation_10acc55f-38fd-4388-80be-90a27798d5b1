/**
 * Quiz选择器组件
 * 支持多种布局和选择模式的通用选择器组件
 */

import React, { useCallback, useMemo } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { SelectorComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type SelectorComponentConfig = z.infer<typeof SelectorComponentConfigSchema>;

export interface SelectorOption {
  id: string;
  value: string | number;
  text_localized: Record<string, string>;
  display_style: 'text_only' | 'icon_text' | 'card_style' | 'wheel_sector';
  icon_name?: string;
  disabled?: boolean;
}

export interface SelectorComponentProps extends BaseQuizComponentProps<SelectorComponentConfig> {
  options: SelectorOption[];
  selectedValues: (string | number)[];
  onSelectionChange: (values: (string | number)[]) => void;
  onValidationError?: (error: string) => void;
}

interface SelectorComponentState extends ComponentState {
  hovered_option: string | null;
  focused_option: string | null;
  validation_error: string | null;
}

/**
 * 选择器组件类
 */
export class SelectorComponent extends BaseQuizComponent<
  SelectorComponentConfig,
  SelectorComponentProps,
  SelectorComponentState
> {
  private containerRef = React.createRef<HTMLDivElement>();

  extractConfig(props: SelectorComponentProps): SelectorComponentConfig {
    return props.config;
  }

  getInitialState(): SelectorComponentState {
    return {
      is_loading: false,
      is_interactive: true,
      is_disabled: false,
      selected_items: this.props.selectedValues.map(String),
      animation_state: 'idle',
      hovered_option: null,
      focused_option: null,
      validation_error: null
    };
  }

  componentDidUpdate(prevProps: SelectorComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.selectedValues !== this.props.selectedValues) {
      this.setState({ 
        selected_items: this.props.selectedValues.map(String)
      });
    }
  }

  /**
   * 处理选项选择
   */
  private handleOptionSelect = (option: SelectorOption): void => {
    if (option.disabled || this.state.is_disabled) {
      return;
    }

    const currentValues = [...this.props.selectedValues];
    const optionValue = option.value;
    const isSelected = currentValues.includes(optionValue);

    let newValues: (string | number)[];

    if (this.config.style.selection_mode === 'single') {
      // 单选模式
      newValues = isSelected ? [] : [optionValue];
    } else {
      // 多选模式
      if (isSelected) {
        newValues = currentValues.filter(v => v !== optionValue);
      } else {
        newValues = [...currentValues, optionValue];
      }
    }

    // 验证选择
    const validationResult = this.validateSelection(newValues);
    if (!validationResult.isValid) {
      this.setState({ validation_error: validationResult.error });
      this.props.onValidationError?.(validationResult.error);
      return;
    }

    // 清除验证错误
    this.setState({ validation_error: null });

    // 触发触觉反馈
    this.triggerHapticFeedback('light');

    // 触发选择动画
    this.triggerSelectionAnimation(option.id);

    // 发送交互事件
    this.emitInteractionEvent('select', {
      option_id: option.id,
      option_value: optionValue,
      selection_mode: this.config.style.selection_mode,
      new_selection: newValues
    });

    // 更新选择
    this.props.onSelectionChange(newValues);
  };

  /**
   * 验证选择
   */
  private validateSelection(values: (string | number)[]): { isValid: boolean; error: string } {
    const validation = this.config.validation;

    if (validation.required && values.length === 0) {
      return { isValid: false, error: '请至少选择一个选项' };
    }

    if (validation.min_selections && values.length < validation.min_selections) {
      return { isValid: false, error: `至少需要选择 ${validation.min_selections} 个选项` };
    }

    if (validation.max_selections && values.length > validation.max_selections) {
      return { isValid: false, error: `最多只能选择 ${validation.max_selections} 个选项` };
    }

    return { isValid: true, error: '' };
  }

  /**
   * 触发选择动画
   */
  private triggerSelectionAnimation(optionId: string): void {
    if (this.personalization.layer5_accessibility?.reduce_motion) {
      return;
    }

    const optionElement = document.querySelector(`[data-option-id="${optionId}"]`);
    if (!optionElement) return;

    const animationStyle = this.config.style.animation_style;
    
    switch (animationStyle) {
      case 'scale_in':
        this.triggerScaleAnimation(optionElement as HTMLElement);
        break;
      case 'fade_in':
        this.triggerFadeAnimation(optionElement as HTMLElement);
        break;
      case 'bounce':
        this.triggerBounceAnimation(optionElement as HTMLElement);
        break;
      default:
        this.triggerScaleAnimation(optionElement as HTMLElement);
    }
  }

  /**
   * 缩放动画
   */
  private triggerScaleAnimation(element: HTMLElement): void {
    element.style.transform = 'scale(1.1)';
    element.style.transition = 'transform 0.2s ease-out';
    
    setTimeout(() => {
      element.style.transform = 'scale(1)';
    }, 200);
  }

  /**
   * 淡入动画
   */
  private triggerFadeAnimation(element: HTMLElement): void {
    element.style.opacity = '0.7';
    element.style.transition = 'opacity 0.3s ease-out';
    
    setTimeout(() => {
      element.style.opacity = '1';
    }, 300);
  }

  /**
   * 弹跳动画
   */
  private triggerBounceAnimation(element: HTMLElement): void {
    element.style.animation = 'quiz-selector-bounce 0.4s ease-out';
    
    setTimeout(() => {
      element.style.animation = '';
    }, 400);
  }

  /**
   * 处理选项悬停
   */
  private handleOptionHover = (optionId: string | null): void => {
    this.setState({ hovered_option: optionId });
    
    if (optionId) {
      this.emitInteractionEvent('hover', { option_id: optionId });
    }
  };

  /**
   * 处理键盘导航
   */
  protected handleKeyNavigation = (event: React.KeyboardEvent): void => {
    if (!this.personalization.layer5_accessibility?.keyboard_navigation) {
      return;
    }

    const options = this.props.options.filter(opt => !opt.disabled);
    const currentFocusIndex = options.findIndex(opt => opt.id === this.state.focused_option);

    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        event.preventDefault();
        const nextIndex = (currentFocusIndex + 1) % options.length;
        this.setState({ focused_option: options[nextIndex].id });
        break;
        
      case 'ArrowUp':
      case 'ArrowLeft':
        event.preventDefault();
        const prevIndex = currentFocusIndex <= 0 ? options.length - 1 : currentFocusIndex - 1;
        this.setState({ focused_option: options[prevIndex].id });
        break;
        
      case 'Enter':
      case ' ':
        event.preventDefault();
        const focusedOption = options.find(opt => opt.id === this.state.focused_option);
        if (focusedOption) {
          this.handleOptionSelect(focusedOption);
        }
        break;
    }
  };

  /**
   * 获取当前语言的选项文本
   */
  private getOptionText(option: SelectorOption): string {
    const { language } = this.context || { language: 'zh' };
    return option.text_localized[language] || option.text_localized['zh'] || option.text_localized['en'] || '';
  }

  /**
   * 获取布局样式类名
   */
  private getLayoutClassName(): string {
    const layoutId = this.config.layout_id;
    
    switch (layoutId) {
      case 'vertical_list':
        return 'quiz-selector-vertical-list';
      case 'horizontal_flow':
        return 'quiz-selector-horizontal-flow';
      case 'grid_layout':
        return 'quiz-selector-grid-layout';
      case 'wheel_layout':
        return 'quiz-selector-wheel-layout';
      default:
        return 'quiz-selector-vertical-list';
    }
  }

  /**
   * 渲染选项
   */
  private renderOption = (option: SelectorOption, index: number): React.ReactNode => {
    const isSelected = this.props.selectedValues.includes(option.value);
    const isHovered = this.state.hovered_option === option.id;
    const isFocused = this.state.focused_option === option.id;
    const optionText = this.getOptionText(option);

    const optionClassName = [
      'quiz-selector-option',
      `quiz-selector-option-${option.display_style}`,
      isSelected && 'quiz-selector-option-selected',
      isHovered && 'quiz-selector-option-hovered',
      isFocused && 'quiz-selector-option-focused',
      option.disabled && 'quiz-selector-option-disabled'
    ].filter(Boolean).join(' ');

    return (
      <div
        key={option.id}
        data-option-id={option.id}
        className={optionClassName}
        onClick={() => this.handleOptionSelect(option)}
        onMouseEnter={() => this.handleOptionHover(option.id)}
        onMouseLeave={() => this.handleOptionHover(null)}
        role="option"
        aria-selected={isSelected}
        aria-disabled={option.disabled}
        tabIndex={isFocused ? 0 : -1}
      >
        {/* 选择标记 */}
        <div className={`quiz-selector-marker quiz-selector-marker-${this.config.style.marker_style}`}>
          {isSelected && <span className="quiz-selector-checkmark" />}
        </div>

        {/* 图标 */}
        {option.icon_name && (
          <span className="quiz-selector-option-icon">
            <i className={`icon-${option.icon_name}`} />
          </span>
        )}

        {/* 文本 */}
        <span className="quiz-selector-option-text">
          {optionText}
        </span>
      </div>
    );
  };

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-selector-component',
      this.getLayoutClassName(),
      this.state.validation_error && 'quiz-selector-error',
      this.props.className
    ].filter(Boolean).join(' ');

    const containerStyles: React.CSSProperties = {
      ...personalizedStyles,
      gap: `${this.config.style.spacing}px`,
      ...this.props.style
    };

    return (
      <div
        ref={this.containerRef}
        className={className}
        style={containerStyles}
        onKeyDown={this.handleKeyNavigation}
        {...accessibilityProps}
        role="listbox"
        aria-multiselectable={this.config.style.selection_mode === 'multiple'}
        aria-label="选择器选项"
      >
        {/* 选项列表 */}
        <div className="quiz-selector-options">
          {this.props.options.map(this.renderOption)}
        </div>

        {/* 验证错误提示 */}
        {this.state.validation_error && (
          <div className="quiz-selector-error-message" role="alert">
            {this.state.validation_error}
          </div>
        )}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'listbox';
  }

  protected getAriaLabel(): string {
    return `Selector with ${this.props.options.length} options`;
  }
}

// 使用Context的函数式组件包装器
const SelectorComponentWrapper: React.FC<SelectorComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <SelectorComponent.contextType.Provider value={{ language }}>
      <SelectorComponent {...props} />
    </SelectorComponent.contextType.Provider>
  );
};

// 设置Context类型
SelectorComponent.contextType = React.createContext({ language: 'zh' });

export default SelectorComponentWrapper;
