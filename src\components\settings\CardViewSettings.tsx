/**
 * 卡片视图设置组件
 * 用于配置卡片视图的布局选项
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import type { CardLayout } from '@/types/layoutTypes';
import { Check } from 'lucide-react';
import type React from 'react';
import { toast } from 'sonner';
import CardLayoutPreview from './CardLayoutPreview';

interface CardViewSettingsProps {
  className?: string;
}

/**
 * 卡片视图设置组件
 * 用于配置卡片视图的布局选项
 */
const CardViewSettings: React.FC<CardViewSettingsProps> = ({ className }) => {
  const { t } = useLanguage();
  const { userConfig, setCardViewLayout } = useUserConfig();

  // 获取当前卡片布局
  const currentLayout = userConfig.layout_preferences?.card || 'grid';

  // 处理布局变更
  const handleLayoutChange = (layout: CardLayout) => {
    if (layout === currentLayout) return;

    setCardViewLayout(layout);
    toast.success(t('settings.card_layout_changed', '卡片布局已更改'), { duration: 3000 });
  };

  // 可用的布局选项
  const layoutOptions: CardLayout[] = ['grid', 'list', 'masonry'];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t('settings.card_view_settings', '卡片视图设置')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">{t('settings.card_layout', '卡片布局')}</h4>
            <div className="grid grid-cols-3 gap-4">
              {layoutOptions.map((layout) => (
                <Button
                  key={layout}
                  variant="outline"
                  className="p-2 h-auto"
                  onClick={() => handleLayoutChange(layout)}
                >
                  <div className="flex flex-col items-center w-full">
                    <CardLayoutPreview
                      layout={layout}
                      size="md"
                      isSelected={currentLayout === layout}
                    />
                    <div className="flex items-center w-full mt-2">
                      <span>{t(`settings.card_layout.${layout}`, layout)}</span>
                      {currentLayout === layout && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CardViewSettings;
