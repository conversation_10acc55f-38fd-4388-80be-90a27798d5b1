/**
 * 特殊视图组件导出文件
 * 
 * 导出所有特殊视图组件，包括情绪轮盘、情绪卡片和情绪气泡视图
 * 这些组件专门用于Quiz系统中的情绪数据集展示
 */

// 情绪轮盘视图
export {
  EmotionWheelView,
  type EmotionWheelViewProps,
  type EmotionItem,
  type WheelConfig
} from './EmotionWheelView';

// 情绪卡片视图
export {
  EmotionCardView,
  type EmotionCardViewProps,
  type EmotionCardData,
  type CardLayoutConfig
} from './EmotionCardView';

// 情绪气泡视图
export {
  EmotionBubbleView,
  type EmotionBubbleViewProps,
  type EmotionBubbleData,
  type BubbleViewConfig
} from './EmotionBubbleView';

// 特殊视图工厂类
export class SpecialViewFactory {
  /**
   * 创建情绪轮盘视图配置
   */
  static createEmotionWheelConfig(
    size: number = 400,
    centerRadius: number = 60,
    tierSpacing: number = 80,
    options: Partial<WheelConfig> = {}
  ): WheelConfig {
    return {
      size,
      center_radius: centerRadius,
      tier_spacing: tierSpacing,
      animation_duration: 300,
      show_labels: true,
      show_emojis: true,
      interactive: true,
      ...options
    };
  }

  /**
   * 创建情绪卡片视图配置
   */
  static createEmotionCardConfig(
    columns: number = 3,
    cardWidth: number = 200,
    cardHeight: number = 150,
    options: Partial<CardLayoutConfig> = {}
  ): CardLayoutConfig {
    return {
      columns,
      card_width: cardWidth,
      card_height: cardHeight,
      spacing: 16,
      show_descriptions: true,
      show_intensity: true,
      show_categories: true,
      animation_enabled: true,
      selection_mode: 'multiple',
      ...options
    };
  }

  /**
   * 创建情绪气泡视图配置
   */
  static createEmotionBubbleConfig(
    width: number = 600,
    height: number = 400,
    minBubbleSize: number = 30,
    maxBubbleSize: number = 80,
    options: Partial<BubbleViewConfig> = {}
  ): BubbleViewConfig {
    return {
      width,
      height,
      min_bubble_size: minBubbleSize,
      max_bubble_size: maxBubbleSize,
      animation_enabled: true,
      physics_enabled: true,
      collision_detection: true,
      gravity_strength: 0.1,
      damping: 0.98,
      show_labels: true,
      show_emojis: true,
      selection_mode: 'multiple',
      ...options
    };
  }

  /**
   * 创建示例情绪数据 - 轮盘格式
   */
  static createSampleWheelEmotions(): EmotionItem[] {
    return [
      // 第一层 - 基础情绪
      {
        id: 'joy',
        name: { zh: '喜悦', en: 'Joy' },
        color: '#FFD700',
        emoji: '😊',
        tier_level: 1,
        children: [
          {
            id: 'happiness',
            name: { zh: '快乐', en: 'Happiness' },
            color: '#FFE55C',
            emoji: '😄',
            tier_level: 2,
            parent_id: 'joy'
          },
          {
            id: 'contentment',
            name: { zh: '满足', en: 'Contentment' },
            color: '#FFF2A1',
            emoji: '😌',
            tier_level: 2,
            parent_id: 'joy'
          }
        ]
      },
      {
        id: 'sadness',
        name: { zh: '悲伤', en: 'Sadness' },
        color: '#4A90E2',
        emoji: '😢',
        tier_level: 1,
        children: [
          {
            id: 'grief',
            name: { zh: '悲痛', en: 'Grief' },
            color: '#2E5BBA',
            emoji: '😭',
            tier_level: 2,
            parent_id: 'sadness'
          },
          {
            id: 'melancholy',
            name: { zh: '忧郁', en: 'Melancholy' },
            color: '#6BB6FF',
            emoji: '😔',
            tier_level: 2,
            parent_id: 'sadness'
          }
        ]
      },
      {
        id: 'anger',
        name: { zh: '愤怒', en: 'Anger' },
        color: '#FF6B6B',
        emoji: '😠',
        tier_level: 1,
        children: [
          {
            id: 'rage',
            name: { zh: '暴怒', en: 'Rage' },
            color: '#FF3333',
            emoji: '😡',
            tier_level: 2,
            parent_id: 'anger'
          },
          {
            id: 'irritation',
            name: { zh: '烦躁', en: 'Irritation' },
            color: '#FF9999',
            emoji: '😤',
            tier_level: 2,
            parent_id: 'anger'
          }
        ]
      },
      {
        id: 'fear',
        name: { zh: '恐惧', en: 'Fear' },
        color: '#9B59B6',
        emoji: '😨',
        tier_level: 1,
        children: [
          {
            id: 'anxiety',
            name: { zh: '焦虑', en: 'Anxiety' },
            color: '#8E44AD',
            emoji: '😰',
            tier_level: 2,
            parent_id: 'fear'
          },
          {
            id: 'worry',
            name: { zh: '担忧', en: 'Worry' },
            color: '#BB8FCE',
            emoji: '😟',
            tier_level: 2,
            parent_id: 'fear'
          }
        ]
      }
    ];
  }

  /**
   * 创建示例情绪数据 - 卡片格式
   */
  static createSampleCardEmotions(): EmotionCardData[] {
    return [
      {
        id: 'joy',
        name: { zh: '喜悦', en: 'Joy' },
        description: { zh: '内心充满快乐和满足的积极情绪', en: 'A positive emotion filled with happiness and satisfaction' },
        emoji: '😊',
        color: '#FFD700',
        intensity: 4,
        category: '积极情绪',
        tags: ['快乐', '满足', '积极']
      },
      {
        id: 'sadness',
        name: { zh: '悲伤', en: 'Sadness' },
        description: { zh: '因失落或不如意而产生的消极情绪', en: 'A negative emotion arising from loss or disappointment' },
        emoji: '😢',
        color: '#4A90E2',
        intensity: 3,
        category: '消极情绪',
        tags: ['失落', '难过', '消极']
      },
      {
        id: 'anger',
        name: { zh: '愤怒', en: 'Anger' },
        description: { zh: '对不公或挫折产生的强烈不满情绪', en: 'Strong dissatisfaction arising from injustice or frustration' },
        emoji: '😠',
        color: '#FF6B6B',
        intensity: 5,
        category: '激烈情绪',
        tags: ['愤怒', '不满', '激烈']
      },
      {
        id: 'fear',
        name: { zh: '恐惧', en: 'Fear' },
        description: { zh: '面对威胁或危险时的自我保护情绪', en: 'A self-protective emotion when facing threats or danger' },
        emoji: '😨',
        color: '#9B59B6',
        intensity: 4,
        category: '防御情绪',
        tags: ['恐惧', '担心', '防御']
      },
      {
        id: 'surprise',
        name: { zh: '惊讶', en: 'Surprise' },
        description: { zh: '遇到意外情况时的短暂情绪反应', en: 'A brief emotional reaction to unexpected situations' },
        emoji: '😲',
        color: '#F39C12',
        intensity: 2,
        category: '中性情绪',
        tags: ['惊讶', '意外', '中性']
      },
      {
        id: 'disgust',
        name: { zh: '厌恶', en: 'Disgust' },
        description: { zh: '对令人反感事物的排斥情绪', en: 'A rejection emotion towards repulsive things' },
        emoji: '🤢',
        color: '#27AE60',
        intensity: 3,
        category: '排斥情绪',
        tags: ['厌恶', '排斥', '反感']
      }
    ];
  }

  /**
   * 创建示例情绪数据 - 气泡格式
   */
  static createSampleBubbleEmotions(): EmotionBubbleData[] {
    return [
      {
        id: 'joy',
        name: { zh: '喜悦', en: 'Joy' },
        emoji: '😊',
        color: '#FFD700',
        intensity: 0.8,
        frequency: 0.7,
        category: '积极情绪'
      },
      {
        id: 'sadness',
        name: { zh: '悲伤', en: 'Sadness' },
        emoji: '😢',
        color: '#4A90E2',
        intensity: 0.6,
        frequency: 0.4,
        category: '消极情绪'
      },
      {
        id: 'anger',
        name: { zh: '愤怒', en: 'Anger' },
        emoji: '😠',
        color: '#FF6B6B',
        intensity: 0.9,
        frequency: 0.3,
        category: '激烈情绪'
      },
      {
        id: 'fear',
        name: { zh: '恐惧', en: 'Fear' },
        emoji: '😨',
        color: '#9B59B6',
        intensity: 0.7,
        frequency: 0.5,
        category: '防御情绪'
      },
      {
        id: 'surprise',
        name: { zh: '惊讶', en: 'Surprise' },
        emoji: '😲',
        color: '#F39C12',
        intensity: 0.4,
        frequency: 0.6,
        category: '中性情绪'
      },
      {
        id: 'disgust',
        name: { zh: '厌恶', en: 'Disgust' },
        emoji: '🤢',
        color: '#27AE60',
        intensity: 0.5,
        frequency: 0.2,
        category: '排斥情绪'
      },
      {
        id: 'love',
        name: { zh: '爱', en: 'Love' },
        emoji: '❤️',
        color: '#E91E63',
        intensity: 0.9,
        frequency: 0.8,
        category: '深层情绪'
      },
      {
        id: 'hope',
        name: { zh: '希望', en: 'Hope' },
        emoji: '🌟',
        color: '#00BCD4',
        intensity: 0.6,
        frequency: 0.7,
        category: '积极情绪'
      }
    ];
  }
}
