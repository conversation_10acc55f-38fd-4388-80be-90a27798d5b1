# 翻译系统迁移指南

本文档说明如何将现有的 `translationTypes.ts` 迁移到新的 schema 架构。

## 迁移概述

新的翻译系统架构将翻译相关的类型定义整合到统一的 schema 系统中，提供以下优势：

1. **运行时验证**: 使用 Zod 进行数据验证
2. **类型安全**: 确保编译时和运行时的类型一致性
3. **数据库对齐**: 直接映射到数据库表结构
4. **API 集成**: 与 tRPC API 无缝集成

## 文件结构变化

### 旧架构
```
src/types/
├── translationTypes.ts     # 所有翻译相关类型
└── uiLabelTypes.ts        # UI 标签类型
```

### 新架构
```
src/types/schema/
├── base.ts                # 基础数据库 Schema（包含翻译表）
├── api.ts                 # API 输入输出 Schema（包含翻译 API）
├── translation.ts         # 扩展翻译功能和工具类型
└── generator.ts           # Schema 生成工具
```

## 类型映射对照表

### 基础翻译类型

| 旧类型 (translationTypes.ts) | 新类型 (schema/) | 文件位置 |
|------------------------------|------------------|----------|
| `Translation` | `Translation` | `translation.ts` |
| `TranslatableEntity` | `TranslatableEntity` | `translation.ts` |
| `TranslationInput` | `TranslationInput` | `translation.ts` |
| `TranslatableCreateInput` | `TranslatableCreateInput` | `translation.ts` |
| `TranslatableUpdateInput` | `TranslatableUpdateInput` | `translation.ts` |
| `LanguageCode` | `LanguageCode` | `base.ts` |

### 数据库相关类型

| 旧类型 | 新类型 | 文件位置 | 说明 |
|--------|--------|----------|------|
| 无 | `UILabelTranslation` | `base.ts` | 直接映射数据库表 |
| 无 | `EmotionTranslation` | `base.ts` | 直接映射数据库表 |
| 无 | `EmojiSetTranslation` | `base.ts` | 直接映射数据库表 |
| 无 | `SkinTranslation` | `base.ts` | 直接映射数据库表 |

### API 相关类型

| 旧类型 | 新类型 | 文件位置 | 说明 |
|--------|--------|----------|------|
| 无 | `GetTranslationInput` | `api.ts` | API 查询输入 |
| 无 | `CreateTranslationInput` | `api.ts` | API 创建输入 |
| 无 | `BatchTranslationInput` | `api.ts` | 批量操作输入 |
| `BatchTranslationResult` | `BatchTranslationResult` | `api.ts` | 批量操作结果 |

### 扩展功能类型

| 旧类型 | 新类型 | 文件位置 | 变化说明 |
|--------|--------|----------|----------|
| `LanguageSupport` | `LanguageSupportExtended` | `translation.ts` | 增加了统计字段 |
| `TranslationFilter` | `TranslationFilter` | `translation.ts` | 使用 Zod 验证 |
| `TranslationExport` | `TranslationExport` | `translation.ts` | 使用 Zod 验证 |

## 迁移步骤

### 1. 更新导入语句

**旧代码:**
```typescript
import { Translation, LanguageCode, TranslatableEntity } from '@/types/translationTypes';
```

**新代码:**
```typescript
// 基础类型
import { LanguageCode } from '@/types/schema/base';
// 扩展功能
import { Translation, TranslatableEntity } from '@/types/schema/translation';
// API 类型
import { GetTranslationInput, CreateTranslationInput } from '@/types/schema/api';
```

### 2. 更新类型使用

**旧代码:**
```typescript
interface MyComponent {
  translations: Translation[];
  currentLanguage: LanguageCode;
}
```

**新代码:**
```typescript
import { Translation, LanguageCode } from '@/types/schema';

interface MyComponent {
  translations: Translation[];
  currentLanguage: LanguageCode;
}
```

### 3. 添加运行时验证

**新功能 - 数据验证:**
```typescript
import { TranslationSchema } from '@/types/schema/translation';

// 验证翻译数据
const validateTranslation = (data: unknown) => {
  try {
    return TranslationSchema.parse(data);
  } catch (error) {
    console.error('Translation validation failed:', error);
    return null;
  }
};
```

### 4. 使用数据库 Schema

**新功能 - 直接数据库映射:**
```typescript
import { EmotionTranslationSchema } from '@/types/schema/base';

// 直接验证数据库查询结果
const emotionTranslations = EmotionTranslationSchema.array().parse(dbResult);
```

## 兼容性说明

### 保持兼容的部分

1. **常量定义**: `LANGUAGE_CODES` 保持不变
2. **基础接口**: 核心翻译接口结构保持兼容
3. **默认语言列表**: `DEFAULT_SUPPORTED_LANGUAGES` 结构兼容

### 需要更新的部分

1. **导入路径**: 需要更新所有导入语句
2. **类型名称**: 部分类型名称有变化（如 `LanguageSupport` → `LanguageSupportExtended`）
3. **验证逻辑**: 建议使用 Zod Schema 进行数据验证

## 实际迁移示例

### 服务层迁移

**旧代码:**
```typescript
// services/TranslationService.ts
import { Translation, LanguageCode } from '@/types/translationTypes';

class TranslationService {
  async getTranslations(entityId: string, language: LanguageCode): Promise<Translation[]> {
    // 实现
  }
}
```

**新代码:**
```typescript
// services/TranslationService.ts
import { LanguageCode } from '@/types/schema/base';
import { Translation } from '@/types/schema/translation';
import { GetTranslationInput } from '@/types/schema/api';

class TranslationService {
  async getTranslations(input: GetTranslationInput): Promise<Translation[]> {
    // 使用 Zod 验证输入
    const validInput = GetTranslationInputSchema.parse(input);
    // 实现
  }
}
```

### 组件层迁移

**旧代码:**
```typescript
// components/TranslationComponent.tsx
import { TranslatableEntity, LanguageCode } from '@/types/translationTypes';

interface Props {
  entity: TranslatableEntity;
  currentLanguage: LanguageCode;
}
```

**新代码:**
```typescript
// components/TranslationComponent.tsx
import { LanguageCode } from '@/types/schema/base';
import { TranslatableEntity } from '@/types/schema/translation';

interface Props {
  entity: TranslatableEntity;
  currentLanguage: LanguageCode;
}
```

## 验证迁移

### 1. 类型检查
```bash
npx tsc --noEmit
```

### 2. 运行测试
```bash
npm test
```

### 3. 验证 Schema
```typescript
import { runAllValidationTests } from '@/types/schema/validation-test';

// 运行 Schema 验证测试
runAllValidationTests();
```

## 注意事项

1. **渐进式迁移**: 可以逐步迁移，新旧类型可以暂时共存
2. **数据验证**: 建议在数据边界（API 输入输出、数据库查询）使用 Zod 验证
3. **性能考虑**: Zod 验证有性能开销，在生产环境中考虑缓存验证结果
4. **错误处理**: 新增的运行时验证需要适当的错误处理逻辑

## 后续步骤

1. 完成类型迁移后，可以考虑删除 `translationTypes.ts`
2. 更新相关文档和注释
3. 考虑将验证逻辑集成到数据访问层
4. 评估是否需要为特定用例创建额外的 Schema 变体
