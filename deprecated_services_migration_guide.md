# 废弃服务清理和迁移指南

## 📋 废弃服务识别和迁移计划

### 🚨 需要立即标记废弃的服务

#### 1. **EmotionDataSetService** → **QuizPackService**
```typescript
// ❌ 废弃服务
import { EmotionDataSetService } from './deprecated/EmotionDataSetService';

// ✅ 新服务
import { QuizPackService } from './entities/QuizPackService';

// 迁移说明：
// - emotion_data_sets 表已废弃
// - 功能已迁移到 quiz_packs 表
// - 使用 quiz_packs.category = 'emotion' 来过滤情绪相关的测验包
```

#### 2. **MoodEntryService** → **MoodTrackingService**
```typescript
// ❌ 废弃服务  
import { MoodEntryService } from './deprecated/MoodEntryService';

// ✅ 新服务
import { MoodTrackingService } from './entities/MoodTrackingService';

// 迁移说明：
// - mood_entries 表结构已更新
// - 新服务支持更丰富的心情追踪功能
// - 集成了Quiz系统的结果记录
```

#### 3. **EmotionService** → **QuizEngineV3**
```typescript
// ❌ 废弃服务
import { EmotionService } from './deprecated/EmotionService';

// ✅ 新服务
import { QuizEngineV3 } from './entities/QuizEngineV3';

// 迁移说明：
// - emotions 表已废弃
// - 情绪数据现在通过 quiz_questions 和 quiz_question_options 管理
// - 使用 QuizEngineV3 来处理情绪相关的测验逻辑
```

### 📁 建议的目录结构重组

```
src/services/
├── entities/           # ✅ 现代化服务
│   ├── QuizEngineV3.ts
│   ├── QuizPackService.ts
│   ├── MoodTrackingService.ts
│   ├── UserQuizPreferencesService.ts
│   └── UnlockService.ts
├── base/              # ✅ 基础架构
│   ├── BaseService.ts
│   └── BaseRepository.ts
├── online/            # ✅ 在线服务
│   └── services/
├── deprecated/        # ⚠️ 废弃服务 (待删除)
│   ├── EmotionDataSetService.ts
│   ├── MoodEntryService.ts
│   └── EmotionService.ts
└── index.ts          # 服务导出入口
```

### 🔄 Services Index 重构

#### 当前状态 (src/services/index.ts)
```typescript
export const Services = {
  // ❌ 需要标记废弃
  emotionDataSet: () => import('./entities/EmotionDataSetService').then(m => new m.EmotionDataSetService()),
  moodEntry: () => import('./entities/MoodEntryService').then(m => new m.MoodEntryService()),
  
  // ✅ 现代化服务
  userQuizPreferences: () => import('./entities/UserQuizPreferencesService').then(m => new m.UserQuizPreferencesService()),
  unlockService: () => import('./entities/UnlockService').then(m => new m.UnlockService()),
};
```

#### 目标状态 (重构后)
```typescript
export const Services = {
  // ✅ 核心Quiz系统服务
  quizEngine: () => import('./entities/QuizEngineV3').then(m => new m.QuizEngineV3()),
  quizPack: () => import('./entities/QuizPackService').then(m => new m.QuizPackService()),
  userQuizPreferences: () => import('./entities/UserQuizPreferencesService').then(m => new m.UserQuizPreferencesService()),
  
  // ✅ 用户数据服务
  moodTracking: () => import('./entities/MoodTrackingService').then(m => new m.MoodTrackingService()),
  userConfig: () => import('./entities/UserConfigService').then(m => new m.UserConfigService()),
  
  // ✅ 内容和解锁服务
  skin: () => import('./entities/SkinService').then(m => new m.SkinService()),
  emojiSet: () => import('./entities/EmojiSetService').then(m => new m.EmojiSetService()),
  unlock: () => import('./entities/UnlockService').then(m => new m.UnlockService()),
  
  // ✅ 配置服务
  globalAppConfig: () => import('./entities/GlobalAppConfigService').then(m => new m.GlobalAppConfigService()),
  quizConfigMerger: () => import('./entities/QuizConfigMergerService').then(m => new m.QuizConfigMergerService()),
};

// ⚠️ 废弃服务 (保留用于向后兼容，但添加废弃警告)
export const DeprecatedServices = {
  /** @deprecated 使用 Services.quizPack 替代，过滤 category='emotion' */
  emotionDataSet: () => {
    console.warn('⚠️ EmotionDataSetService 已废弃，请使用 QuizPackService 替代');
    return import('./deprecated/EmotionDataSetService').then(m => new m.EmotionDataSetService());
  },
  
  /** @deprecated 使用 Services.moodTracking 替代 */
  moodEntry: () => {
    console.warn('⚠️ MoodEntryService 已废弃，请使用 MoodTrackingService 替代');
    return import('./deprecated/MoodEntryService').then(m => new m.MoodEntryService());
  },
};
```

### 🔧 Hooks 迁移指南

#### useHybridData 中的废弃服务调用
```typescript
// ❌ 当前实现 (src/hooks/useHybridData.ts)
export function useHybridMoodEntriesData() {
  return useHybridData(
    'moodEntries',
    async () => {
      // 在线获取逻辑
    },
    async () => {
      const moodEntryService = await Services.moodEntry(); // ❌ 废弃
      const result = await moodEntryService.getAll();
      return result.data;
    }
  );
}

// ✅ 迁移后实现
export function useHybridMoodEntriesData() {
  return useHybridData(
    'moodEntries',
    async () => {
      // 在线获取逻辑
    },
    async () => {
      const moodTrackingService = await Services.moodTracking(); // ✅ 新服务
      const result = await moodTrackingService.getAllEntries();
      return result.data;
    }
  );
}
```

#### useAnalyticsData 中的数据源迁移
```typescript
// ❌ 当前可能的实现
const getEmotionCounts = async (entries) => {
  const emotionDataSetService = await Services.emotionDataSet(); // ❌ 废弃
  // ...
};

// ✅ 迁移后实现
const getEmotionCounts = async (entries) => {
  const quizPackService = await Services.quizPack(); // ✅ 新服务
  const emotionPacks = await quizPackService.getPacksByCategory('emotion');
  // ...
};
```

### 📄 页面级别的迁移

#### WheelTest.tsx
```typescript
// ❌ 当前实现
import { useMockEmotionData } from '@/hooks/useMockEmotionData';

const WheelTest = () => {
  const { emotionData } = useMockEmotionData(); // ❌ 模拟数据
  // ...
};

// ✅ 迁移后实现
import { useQuizData } from '@/hooks/useQuizData';
import { useEmojiMapping } from '@/hooks/useEmojiMapping';

const WheelTest = () => {
  const { quizData } = useQuizData('emotion_wheel_pack_id'); // ✅ 真实数据
  const { emojiMapping } = useEmojiMapping(); // ✅ 个性化映射
  // ...
};
```

### 🗂️ 数据库表迁移对照

| 废弃表 | 新表 | 迁移说明 |
|--------|------|----------|
| `emotions` | `quiz_questions` | 情绪现在作为问题类型存储 |
| `emotion_translations` | `ui_label_translations` | 翻译统一到UI标签系统 |
| `emotion_data_sets` | `quiz_packs` | 情绪数据集迁移到测验包 |
| `emoji_items` | `user_presentation_configs` | 表情符号映射存储在用户配置中 |

### ⏰ 迁移时间表

#### 第1周：标记和警告
- [ ] 添加废弃警告到所有废弃服务
- [ ] 更新 Services index 结构
- [ ] 创建 DeprecatedServices 命名空间

#### 第2周：Hook迁移
- [ ] 更新 useHybridData 中的服务调用
- [ ] 迁移 useAnalyticsData 数据源
- [ ] 更新 useHistoryData 实现

#### 第3周：页面迁移
- [ ] WheelTest.tsx 迁移到真实数据
- [ ] 移除所有模拟数据依赖
- [ ] 更新页面中的直接服务调用

#### 第4周：清理和删除
- [ ] 移除废弃服务文件
- [ ] 清理 DeprecatedServices
- [ ] 更新文档和类型定义

### 🧪 测试策略

#### 回归测试
```typescript
// 确保新服务提供相同的功能
describe('Service Migration', () => {
  it('should provide same data as deprecated service', async () => {
    const oldService = await DeprecatedServices.emotionDataSet();
    const newService = await Services.quizPack();
    
    const oldData = await oldService.getAll();
    const newData = await newService.getPacksByCategory('emotion');
    
    // 验证数据结构兼容性
    expect(transformNewToOldFormat(newData)).toEqual(oldData);
  });
});
```

#### 性能测试
- 确保新服务的性能不低于废弃服务
- 测试在线离线切换的响应时间
- 验证大数据量的处理能力

### 📚 文档更新清单

- [ ] 更新 src/services/README.md
- [ ] 更新 API 文档
- [ ] 更新开发者指南
- [ ] 创建迁移FAQ
- [ ] 更新类型定义文档

---

**重要提醒**: 在删除任何废弃服务之前，确保所有依赖都已迁移，并且通过了完整的回归测试。建议采用渐进式迁移策略，先标记废弃，再逐步迁移，最后删除。
