# 情绪追踪问卷三层结构分析与转换方案

## 📊 原始数据结构分析

### 三层情绪结构
```
Primary Emotions (第一层 - 7个主要情绪)
├── Happy (快乐)
├── Surprised (惊讶)
├── Bad (糟糕)
├── Fearful (恐惧)
├── Angry (愤怒)
├── Disgusted (厌恶)
└── Sad (悲伤)

Secondary Emotions (第二层 - 每个主要情绪下的次要情绪)
├── Happy → 9个次要情绪 (Playful, Content, Interested, etc.)
├── Surprised → 4个次要情绪 (Startled, Confused, Amazed, Excited)
├── Bad → 4个次要情绪 (Bored, Busy, Stressed, Tired)
├── Fearful → 6个次要情绪 (Scared, Anxious, Insecure, etc.)
├── Angry → 8个次要情绪 (Let down, Humiliated, Bitter, etc.)
├── Disgusted → 4个次要情绪 (Disapproving, Disappointed, etc.)
└── Sad → 6个次要情绪 (Hurt, Depressed, Guilty, etc.)

Tertiary Emotions (第三层 - 每个次要情绪下的具体情绪)
└── 每个次要情绪包含2个具体的三级情绪
```

### 数据统计
- **第一层**: 8个主要情绪
- **第二层**: 总计41个次要情绪
- **第三层**: 总计82个具体情绪

## 🎯 转换为Quiz量表结构

### 推荐方案: 拆解为独立关联问题

#### 问题数量分析
```
第一层问题: 1个问题 (主要情绪选择)
├── 8个选项: Happy, Surprised, Bad, Fearful, Angry, Disgusted, Sad

第二层问题: 8个问题 (每个主要情绪对应一个问题)
├── Happy问题: 9个选项 (Playful, Content, Interested, Proud, Accepted, Powerful, Peaceful, Trusting, Optimistic)
├── Surprised问题: 4个选项 (Startled, Confused, Amazed, Excited)
├── Bad问题: 4个选项 (Bored, Busy, Stressed, Tired)
├── Fearful问题: 6个选项 (Scared, Anxious, Insecure, Weak, Rejected, Threatened)
├── Angry问题: 8个选项 (Let down, Humiliated, Bitter, Mad, Aggressive, Frustrated, Distant, Critical)
├── Disgusted问题: 4个选项 (Disapproving, Disappointed, Awful, Repelled)
└── Sad问题: 6个选项 (Hurt, Depressed, Guilty, Despair, Vulnerable, Lonely)

第三层问题: 41个问题 (每个次要情绪对应一个问题)
└── 每个问题包含2个选项 (具体的三级情绪)

总计: 1 + 8 + 41 = 50个独立问题
```

#### Quiz Pack 结构
```typescript
{
  id: "mood_track_quest_v1",
  name: "情绪追踪问卷",
  name_localized: {
    "zh-CN": "情绪追踪问卷",
    "en-US": "Mood Tracking Questionnaire"
  },
  description: "通过50个关联问题，精确识别和记录当前情绪状态",
  quiz_type: "emotion_wheel",
  quiz_style: "conditional_branching",
  category: "emotion",
  difficulty_level: 2,
  estimated_duration_minutes: 3,
  tags: ["emotion", "mood", "tracking", "conditional"],
  metadata: {
    structure_type: "conditional_branching",
    total_questions: 50,
    question_layers: 3,
    branching_logic: true,
    max_questions_per_session: 3
  }
}
```

#### 问题结构设计

##### 第一层: 主要情绪选择 (1个问题)
```typescript
{
  id: "q001_primary_emotion",
  pack_id: "mood_track_quest_v1",
  question_text: "您当前的主要情绪是什么？",
  question_text_localized: {
    "zh-CN": "您当前的主要情绪是什么？",
    "en-US": "What is your primary emotion right now?"
  },
  question_type: "single_choice",
  order_index: 1,
  is_required: true,
  metadata: {
    emotion_tier: 1,
    layer: "primary",
    triggers_questions: ["q002_happy", "q003_surprised", "q004_bad", "q005_fearful", "q006_angry", "q007_disgusted", "q008_sad"]
  }
}
```

##### 第二层: 次要情绪选择 (8个问题，每个主要情绪对应一个)
```typescript
// Happy的次要情绪问题
{
  id: "q002_happy_secondary",
  pack_id: "mood_track_quest_v1",
  question_text: "您感到快乐，具体是哪种类型的快乐？",
  question_text_localized: {
    "zh-CN": "您感到快乐，具体是哪种类型的快乐？",
    "en-US": "You feel happy, what specific type of happiness?"
  },
  question_type: "single_choice",
  order_index: 2,
  is_required: true,
  condition_logic: {
    show_if: {
      question_id: "q001_primary_emotion",
      answer_value: "happy"
    }
  },
  metadata: {
    emotion_tier: 2,
    layer: "secondary",
    parent_emotion: "happy",
    triggers_questions: ["q010_playful", "q011_content", "q012_interested", "q013_proud", "q014_accepted", "q015_powerful", "q016_peaceful", "q017_trusting", "q018_optimistic"]
  }
}

// Surprised的次要情绪问题
{
  id: "q003_surprised_secondary",
  pack_id: "mood_track_quest_v1",
  question_text: "您感到惊讶，具体是哪种类型的惊讶？",
  question_text_localized: {
    "zh-CN": "您感到惊讶，具体是哪种类型的惊讶？",
    "en-US": "You feel surprised, what specific type of surprise?"
  },
  question_type: "single_choice",
  order_index: 3,
  is_required: true,
  condition_logic: {
    show_if: {
      question_id: "q001_primary_emotion",
      answer_value: "surprised"
    }
  },
  metadata: {
    emotion_tier: 2,
    layer: "secondary",
    parent_emotion: "surprised",
    triggers_questions: ["q019_startled", "q020_confused", "q021_amazed", "q022_excited"]
  }
}

// ... 其他6个主要情绪的次要情绪问题类似结构
```

##### 第三层: 具体情绪选择 (41个问题，每个次要情绪对应一个)
```typescript
// Playful的具体情绪问题
{
  id: "q010_playful_tertiary",
  pack_id: "mood_track_quest_v1",
  question_text: "您感到顽皮，具体是哪种感觉？",
  question_text_localized: {
    "zh-CN": "您感到顽皮，具体是哪种感觉？",
    "en-US": "You feel playful, what specific feeling?"
  },
  question_type: "single_choice",
  order_index: 10,
  is_required: true,
  condition_logic: {
    show_if: {
      question_id: "q002_happy_secondary",
      answer_value: "playful"
    }
  },
  metadata: {
    emotion_tier: 3,
    layer: "tertiary",
    parent_emotion: "playful",
    grandparent_emotion: "happy",
    is_final_selection: true
  }
}

// Content的具体情绪问题
{
  id: "q011_content_tertiary",
  pack_id: "mood_track_quest_v1",
  question_text: "您感到满足，具体是哪种感觉？",
  question_text_localized: {
    "zh-CN": "您感到满足，具体是哪种感觉？",
    "en-US": "You feel content, what specific feeling?"
  },
  question_type: "single_choice",
  order_index: 11,
  is_required: true,
  condition_logic: {
    show_if: {
      question_id: "q002_happy_secondary",
      answer_value: "content"
    }
  },
  metadata: {
    emotion_tier: 3,
    layer: "tertiary",
    parent_emotion: "content",
    grandparent_emotion: "happy",
    is_final_selection: true
  }
}

// ... 其他39个次要情绪的具体情绪问题类似结构
```

#### 选项结构设计
```typescript
// 第一层选项 (主要情绪)
const primaryEmotionOptions = [
  {
    id: "happy",
    question_id: "primary_emotion",
    option_text: "Happy (快乐)",
    option_value: "happy",
    order_index: 1,
    metadata: {
      emotion_category: "positive",
      color: "#4CAF50",
      emoji: "😊"
    }
  },
  {
    id: "surprised",
    question_id: "primary_emotion",
    option_text: "Surprised (惊讶)",
    option_value: "surprised",
    order_index: 2,
    metadata: {
      emotion_category: "neutral",
      color: "#FF9800",
      emoji: "😲"
    }
  },
  // ... 其他主要情绪
];

// 第二层选项 (次要情绪 - 动态生成)
const secondaryEmotionOptions = {
  happy: [
    {
      id: "playful",
      question_id: "secondary_emotion",
      option_text: "Playful (顽皮的)",
      option_value: "playful",
      parent_option: "happy",
      metadata: {
        tertiary_emotions: ["aroused", "cheeky"]
      }
    },
    {
      id: "content",
      question_id: "secondary_emotion",
      option_text: "Content (满足的)",
      option_value: "content",
      parent_option: "happy",
      metadata: {
        tertiary_emotions: ["free", "joyful"]
      }
    },
    // ... 其他Happy的次要情绪
  ],
  // ... 其他主要情绪的次要情绪
};

// 第三层选项 (具体情绪 - 动态生成)
const tertiaryEmotionOptions = {
  playful: [
    {
      id: "aroused",
      question_id: "tertiary_emotion",
      option_text: "Aroused (兴奋的)",
      option_value: "aroused",
      parent_option: "playful"
    },
    {
      id: "cheeky",
      question_id: "tertiary_emotion",
      option_text: "Cheeky (厚脸皮的)",
      option_value: "cheeky",
      parent_option: "playful"
    }
  ],
  // ... 其他次要情绪的具体情绪
};
```

### 方案2: 单一问题的轮盘式选择

#### 轮盘视图结构
```typescript
{
  id: "mood_wheel_single",
  name: "情绪轮盘 - 单一选择",
  quiz_type: "emotion_wheel",
  quiz_style: "wheel_hierarchical",
  metadata: {
    wheel_config: {
      center_radius: 60,
      inner_ring_radius: 120,  // 主要情绪
      middle_ring_radius: 180, // 次要情绪
      outer_ring_radius: 240,  // 具体情绪
      selection_mode: "hierarchical_drill_down"
    }
  }
}
```

#### 单一问题结构
```typescript
{
  id: "emotion_wheel_selection",
  pack_id: "mood_wheel_single",
  question_text: "请在情绪轮盘中选择您当前的情绪状态：",
  question_type: "emotion_wheel",
  metadata: {
    wheel_type: "three_tier_hierarchical",
    selection_flow: "primary → secondary → tertiary",
    allow_backtrack: true,
    show_emotion_path: true
  }
}
```

## 🔧 技术实现方案

### 拆解方案的优势

#### 1. **符合现有架构**
- 每个问题都是独立的数据库记录
- 利用现有的条件逻辑系统 (`condition_logic`)
- 无需修改核心数据库结构

#### 2. **灵活的分支逻辑**
```typescript
// 条件逻辑示例
condition_logic: {
  show_if: {
    question_id: "q001_primary_emotion",
    answer_value: "happy"
  }
}
```

#### 3. **精确的问题管理**
- 每个问题可以独立启用/禁用
- 支持个性化问题顺序调整
- 可以针对特定问题进行A/B测试

#### 4. **丰富的元数据支持**
```typescript
metadata: {
  emotion_tier: 2,
  layer: "secondary",
  parent_emotion: "happy",
  triggers_questions: ["q010_playful", "q011_content", ...],
  emotion_path: ["happy", "playful"]
}
```

### 数据库表结构利用

#### 1. 现有字段利用
```sql
-- quiz_questions 表
condition_logic TEXT, -- 存储条件逻辑JSON
metadata TEXT,        -- 存储层级和关联信息
order_index INTEGER,  -- 问题排序

-- quiz_question_options 表
metadata TEXT,        -- 存储情绪路径和颜色信息
option_value TEXT,    -- 存储情绪标识符
```

#### 2. 无需新增表结构
- 利用现有的 `condition_logic` 字段实现分支逻辑
- 利用 `metadata` 字段存储层级关系
- 利用 `order_index` 管理问题顺序

### 前端组件设计

#### 1. 渐进式选择组件
```typescript
interface ProgressiveEmotionSelector {
  currentTier: 1 | 2 | 3;
  selectedPrimary?: string;
  selectedSecondary?: string;
  selectedTertiary?: string;
  availableOptions: EmotionOption[];
  onSelectionChange: (tier: number, value: string) => void;
  allowBacktrack: boolean;
}
```

#### 2. 情绪轮盘组件
```typescript
interface EmotionWheelComponent {
  wheelConfig: {
    centerRadius: number;
    ringRadii: number[];
    emotionData: HierarchicalEmotionData;
  };
  selectionPath: string[];
  onEmotionSelect: (emotionPath: string[]) => void;
  showSelectionPath: boolean;
}
```

### 答案记录结构

#### 渐进式选择答案
```typescript
{
  session_id: "session_123",
  answers: [
    {
      question_id: "primary_emotion",
      selected_option_id: "happy",
      answer_value: "happy",
      metadata: {
        emotion_tier: 1,
        emotion_path: ["happy"]
      }
    },
    {
      question_id: "secondary_emotion",
      selected_option_id: "playful",
      answer_value: "playful",
      metadata: {
        emotion_tier: 2,
        emotion_path: ["happy", "playful"]
      }
    },
    {
      question_id: "tertiary_emotion",
      selected_option_id: "aroused",
      answer_value: "aroused",
      metadata: {
        emotion_tier: 3,
        emotion_path: ["happy", "playful", "aroused"],
        final_emotion: "aroused"
      }
    }
  ]
}
```

#### 轮盘选择答案
```typescript
{
  session_id: "session_124",
  answers: [
    {
      question_id: "emotion_wheel_selection",
      selected_option_id: "aroused",
      answer_value: "aroused",
      metadata: {
        selection_method: "emotion_wheel",
        emotion_path: ["happy", "playful", "aroused"],
        selection_coordinates: { x: 180, y: 220 },
        selection_time_ms: 15000
      }
    }
  ]
}
```

## 🎨 用户体验设计

### 拆解方案的用户流程
```
会话开始: 用户启动情绪追踪问卷

问题1: "您当前的主要情绪是什么？"
选项: [😊快乐] [😲惊讶] [😞糟糕] [😨恐惧] [😠愤怒] [🤢厌恶] [😢悲伤]
用户选择: 😊快乐
     ↓ 系统根据条件逻辑显示下一个问题

问题2: "您感到快乐，具体是哪种类型的快乐？"
选项: [顽皮的] [满足的] [感兴趣的] [自豪的] [被接受的] [强大的] [平静的] [信任的] [乐观的]
用户选择: 顽皮的
     ↓ 系统根据条件逻辑显示最终问题

问题3: "您感到顽皮，具体是哪种感觉？"
选项: [兴奋的] [厚脸皮的]
用户选择: 兴奋的
     ↓ 完成情绪记录

结果: 情绪路径 = ["happy", "playful", "aroused"]
```

### 分支逻辑的优势
```
传统方式: 用户需要浏览所有82个具体情绪
拆解方式: 用户只需回答3个问题，每个问题选项数量合理

第一层: 8个选项 (认知负担低)
第二层: 4-9个选项 (根据第一层选择动态显示)
第三层: 2个选项 (最终精确选择)

总体验: 简单 → 具体 → 精确
```

### 个性化配置支持
```
问题级别配置:
- 可以禁用某些主要情绪问题
- 可以禁用某些次要情绪问题
- 可以调整问题顺序
- 可以自定义问题文本

选项级别配置:
- 可以禁用某些情绪选项
- 可以调整选项顺序
- 可以自定义选项文本和颜色

会话级别配置:
- 可以设置最大问题数量
- 可以启用/禁用某些情绪分支
- 可以根据历史数据推荐路径
```

## 📈 数据分析价值

### 情绪分析维度
1. **情绪分布**: 统计各层级情绪的选择频率
2. **情绪路径**: 分析用户的情绪选择模式
3. **情绪变化**: 追踪情绪状态的时间变化
4. **情绪深度**: 评估用户情绪认知的精细程度

### 个性化推荐
1. **基于历史**: 根据用户历史选择推荐相关情绪
2. **基于模式**: 识别用户的情绪模式和偏好
3. **基于时间**: 分析不同时间段的情绪特征
4. **基于关联**: 发现情绪之间的关联关系

这个三层结构为我们提供了丰富的情绪数据，可以支持多种交互方式和深度的情绪分析。
