/**
 * Quiz答案仓储 - 修复版本
 * 纯数据访问层，不包含业务逻辑
 */

import { BaseRepository } from '../base/BaseRepository';
import { QuizAnswer } from '../../types/schema/base';
import { CreateQuizAnswerInput, UpdateQuizAnswerInput } from '../../types/schema/api';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class QuizAnswerRepository extends BaseRepository<
  QuizAnswer,
  CreateQuizAnswerInput,
  UpdateQuizAnswerInput
> {
  constructor(db?: SQLiteDBConnection) {
    super('quiz_answers_clean', db);
  }

  /**
   * 获取所有答案
   */
  async getAllAnswers(limit: number = 1000): Promise<QuizAnswer[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName} 
      ORDER BY answered_at DESC 
      LIMIT ?
    `;
    
    const result = await db.query(query, [limit]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据会话ID查找答案
   */
  async findBySessionId(sessionId: string): Promise<QuizAnswer[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName} 
      WHERE session_id = ? 
      ORDER BY answered_at ASC
    `;
    
    const result = await db.query(query, [sessionId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据问题ID查找答案
   */
  async findByQuestionId(sessionId: string, questionId: string): Promise<QuizAnswer | null> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName} 
      WHERE session_id = ? AND question_id = ? 
      ORDER BY answered_at DESC 
      LIMIT 1
    `;
    
    const result = await db.query(query, [sessionId, questionId]);
    const row = result.values?.[0];
    return row ? this.mapRowToEntity(row) : null;
  }

  /**
   * 根据用户ID查找答案历史
   */
  async findByUserId(userId: string, packId?: string, limit: number = 100): Promise<QuizAnswer[]> {
    const db = this.getDb();
    let query = `
      SELECT qa.* FROM ${this.tableName} qa
      JOIN quiz_sessions qs ON qa.session_id = qs.id
      WHERE qs.user_id = ?
    `;
    
    const params: any[] = [userId];
    
    if (packId) {
      query += ` AND qs.pack_id = ?`;
      params.push(packId);
    }
    
    query += ` ORDER BY qa.answered_at DESC LIMIT ?`;
    params.push(limit);
    
    const result = await db.query(query, params);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 删除会话的所有答案
   */
  async deleteBySessionId(sessionId: string): Promise<boolean> {
    const db = this.getDb();
    const query = `DELETE FROM ${this.tableName} WHERE session_id = ?`;
    const result = await db.run(query, [sessionId]);
    return (result.changes?.changes ?? 0) > 0;
  }

  /**
   * 批量插入答案
   */
  async batchInsertAnswers(answers: CreateQuizAnswerInput[]): Promise<QuizAnswer[]> {
    const db = this.getDb();
    const savedAnswers: QuizAnswer[] = [];

    try {
      await db.execute('BEGIN TRANSACTION');

      for (const answerData of answers) {
        const answer = await this.create(answerData);
        savedAnswers.push(answer);
      }

      await db.execute('COMMIT');
      return savedAnswers;
    } catch (error) {
      await db.execute('ROLLBACK');
      throw error;
    }
  }

  /**
   * 获取答案统计数据
   */
  async getAnswerStats(sessionId: string): Promise<any> {
    const db = this.getDb();
    const query = `
      SELECT
        COUNT(*) as total_answers,
        AVG(response_time_ms) as avg_response_time,
        AVG(confidence_score) as avg_confidence,
        MIN(answered_at) as first_answer_time,
        MAX(answered_at) as last_answer_time,
        COUNT(DISTINCT question_id) as unique_questions_answered
      FROM ${this.tableName}
      WHERE session_id = ?
    `;
    
    const result = await db.query(query, [sessionId]);
    return result.values?.[0] || {};
  }

  /**
   * 获取选项选择统计
   */
  async getOptionSelectionStats(packId: string): Promise<any[]> {
    const db = this.getDb();
    const query = `
      SELECT
        qa.selected_option_ids,
        qa.answer_value,
        COUNT(*) as selection_count,
        AVG(qa.confidence_score) as avg_confidence
      FROM ${this.tableName} qa
      JOIN quiz_sessions qs ON qa.session_id = qs.id
      WHERE qs.pack_id = ?
      GROUP BY qa.selected_option_ids, qa.answer_value
      ORDER BY selection_count DESC
    `;
    
    const result = await db.query(query, [packId]);
    return result.values || [];
  }

  protected mapRowToEntity(row: any): QuizAnswer {
    return {
      id: row.id,
      session_id: row.session_id,
      question_id: row.question_id,
      session_presentation_config_id: row.session_presentation_config_id,
      selected_option_ids: this.parseJSON(row.selected_option_ids) || [],
      answer_value: row.answer_value,
      answer_text: row.answer_text,
      confidence_score: row.confidence_score || 50,
      response_time_ms: row.response_time_ms || 0,
      answered_at: row.answered_at,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  protected mapEntityToRow(entity: Partial<QuizAnswer>): Record<string, any> {
    return {
      id: entity.id,
      session_id: entity.session_id,
      question_id: entity.question_id,
      session_presentation_config_id: entity.session_presentation_config_id,
      selected_option_ids: this.stringifyJSON(entity.selected_option_ids),
      answer_value: entity.answer_value,
      answer_text: entity.answer_text,
      confidence_score: entity.confidence_score,
      response_time_ms: entity.response_time_ms,
      answered_at: entity.answered_at,
      created_at: entity.created_at,
      updated_at: entity.updated_at
    };
  }

  protected buildInsertQuery(data: CreateQuizAnswerInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const answerId = `answer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const query = `
      INSERT INTO ${this.tableName} (
        id, session_id, question_id, session_presentation_config_id,
        selected_option_ids, answer_value, answer_text, confidence_score,
        response_time_ms, answered_at, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const values = [
      answerId,
      data.session_id,
      data.question_id,
      data.session_presentation_config_id || null,
      this.stringifyJSON(data.selected_option_ids),
      data.answer_value,
      data.answer_text || null,
      data.confidence_score || 50,
      data.response_time_ms || 0,
      now,
      now,
      now
    ];
    
    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateQuizAnswerInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];
    
    if (data.selected_option_ids !== undefined) {
      fields.push('selected_option_ids = ?');
      values.push(this.stringifyJSON(data.selected_option_ids));
    }
    
    if (data.answer_value !== undefined) {
      fields.push('answer_value = ?');
      values.push(data.answer_value);
    }
    
    if (data.answer_text !== undefined) {
      fields.push('answer_text = ?');
      values.push(data.answer_text);
    }
    
    if (data.confidence_score !== undefined) {
      fields.push('confidence_score = ?');
      values.push(data.confidence_score);
    }
    
    if (data.response_time_ms !== undefined) {
      fields.push('response_time_ms = ?');
      values.push(data.response_time_ms);
    }
    
    // Always update updated_at
    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    
    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);
    
    return { query, values };
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];
    
    if (filters?.session_id) {
      conditions.push('session_id = ?');
      values.push(filters.session_id);
    }
    
    if (filters?.question_id) {
      conditions.push('question_id = ?');
      values.push(filters.question_id);
    }
    
    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }
    
    query += ' ORDER BY answered_at DESC';
    
    if (filters?.limit) {
      query += ' LIMIT ?';
      values.push(filters.limit);
    }
    
    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];
    
    if (filters?.session_id) {
      conditions.push('session_id = ?');
      values.push(filters.session_id);
    }
    
    if (filters?.question_id) {
      conditions.push('question_id = ?');
      values.push(filters.question_id);
    }
    
    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }
    
    return { query, values };
  }

  protected extractIdFromCreateData(data: CreateQuizAnswerInput): string {
    return `answer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private parseJSON(jsonString: string | null): any {
    if (!jsonString) return null;
    try {
      return JSON.parse(jsonString);
    } catch {
      return null;
    }
  }

  private stringifyJSON(obj: any): string | null {
    if (obj === null || obj === undefined) return null;
    try {
      return JSON.stringify(obj);
    } catch {
      return null;
    }
  }
}
