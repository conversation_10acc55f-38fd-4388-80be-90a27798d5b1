/**
 * QuizPackService 测试
 * 验证修复后的Quiz包服务架构
 */

import { QuizPackService } from '../QuizPackService';
import { QuizPackRepository } from '../QuizPackRepository';
import { QuizPack } from '../../../types/schema/base';
import { CreateQuizPackInput, UpdateQuizPackInput } from '../../../types/schema/api';

// Mock SQLiteDBConnection
const mockDb = {
  query: jest.fn(),
  run: jest.fn(),
  execute: jest.fn(),
} as any;

// Mock Repository
jest.mock('../QuizPackRepository');

describe('QuizPackService', () => {
  let service: QuizPackService;
  let mockRepository: jest.Mocked<QuizPackRepository>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create service instance
    service = new QuizPackService(mockDb);
    
    // Get the mocked repository instance
    mockRepository = (service as any).repository as jest.Mocked<QuizPackRepository>;
  });

  describe('createQuizPack', () => {
    it('should create a quiz pack successfully', async () => {
      const input: CreateQuizPackInput = {
        name: 'Emotion Wheel Quiz',
        description: 'A comprehensive emotion assessment quiz',
        quiz_type: 'emotion_wheel',
        quiz_style: 'interactive',
        category: 'emotion',
        difficulty_level: 3,
        estimated_duration_minutes: 15,
        tags: ['emotion', 'assessment', 'wheel'],
        metadata: { version: '1.0' },
        is_active: true,
      };

      const mockQuizPack: QuizPack = {
        id: 'pack_123',
        name: 'Emotion Wheel Quiz',
        name_localized: null,
        description: 'A comprehensive emotion assessment quiz',
        description_localized: null,
        quiz_type: 'emotion_wheel',
        quiz_style: 'interactive',
        category: 'emotion',
        difficulty_level: 3,
        estimated_duration_minutes: 15,
        tags: ['emotion', 'assessment', 'wheel'],
        metadata: { version: '1.0' },
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.create.mockResolvedValue(mockQuizPack);

      const result = await service.createQuizPack(input);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockQuizPack);
      expect(mockRepository.create).toHaveBeenCalledWith(input);
    });

    it('should fail validation when name is missing', async () => {
      const input: CreateQuizPackInput = {
        name: '',
        quiz_type: 'emotion_wheel',
      };

      const result = await service.createQuizPack(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Quiz pack name is required');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should fail validation when quiz_type is missing', async () => {
      const input: CreateQuizPackInput = {
        name: 'Test Quiz',
        quiz_type: '',
      };

      const result = await service.createQuizPack(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Quiz type is required');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should fail validation when difficulty_level is out of range', async () => {
      const input: CreateQuizPackInput = {
        name: 'Test Quiz',
        quiz_type: 'emotion_wheel',
        difficulty_level: 6, // Invalid: > 5
      };

      const result = await service.createQuizPack(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Difficulty level must be between 1 and 5');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getActiveQuizPacks', () => {
    it('should get active quiz packs successfully', async () => {
      const mockQuizPacks: QuizPack[] = [
        {
          id: 'pack_1',
          name: 'Emotion Wheel Quiz',
          name_localized: null,
          description: 'Emotion assessment',
          description_localized: null,
          quiz_type: 'emotion_wheel',
          quiz_style: 'interactive',
          category: 'emotion',
          difficulty_level: 3,
          estimated_duration_minutes: 15,
          tags: ['emotion'],
          metadata: {},
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'pack_2',
          name: 'TCM Assessment',
          name_localized: null,
          description: 'Traditional Chinese Medicine assessment',
          description_localized: null,
          quiz_type: 'tcm_assessment',
          quiz_style: 'questionnaire',
          category: 'health',
          difficulty_level: 4,
          estimated_duration_minutes: 25,
          tags: ['tcm', 'health'],
          metadata: {},
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      mockRepository.findActiveQuizPacks.mockResolvedValue(mockQuizPacks);

      const result = await service.getActiveQuizPacks();

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data?.[0].name).toBe('Emotion Wheel Quiz');
      expect(result.data?.[1].name).toBe('TCM Assessment');
      expect(mockRepository.findActiveQuizPacks).toHaveBeenCalled();
    });
  });

  describe('searchQuizPacks', () => {
    it('should search quiz packs successfully', async () => {
      const searchTerm = 'emotion';
      const mockQuizPacks: QuizPack[] = [
        {
          id: 'pack_1',
          name: 'Emotion Wheel Quiz',
          name_localized: null,
          description: 'Emotion assessment',
          description_localized: null,
          quiz_type: 'emotion_wheel',
          quiz_style: 'interactive',
          category: 'emotion',
          difficulty_level: 3,
          estimated_duration_minutes: 15,
          tags: ['emotion'],
          metadata: {},
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      mockRepository.searchQuizPacks.mockResolvedValue(mockQuizPacks);

      const result = await service.searchQuizPacks(searchTerm);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data?.[0].name).toBe('Emotion Wheel Quiz');
      expect(mockRepository.searchQuizPacks).toHaveBeenCalledWith('emotion');
    });

    it('should fail validation when search term is too short', async () => {
      const result = await service.searchQuizPacks('a');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Search term must be at least 2 characters long');
      expect(mockRepository.searchQuizPacks).not.toHaveBeenCalled();
    });
  });

  describe('getQuizPackStats', () => {
    it('should calculate quiz pack stats correctly', async () => {
      const packId = 'pack_123';
      const mockRawStats = {
        total_sessions: 100,
        completed_sessions: 80,
        total_questions: 10,
        avg_completion_time_minutes: 12.5,
      };

      mockRepository.getQuizPackStats.mockResolvedValue(mockRawStats);

      const result = await service.getQuizPackStats(packId);

      expect(result.success).toBe(true);
      expect(result.data?.total_sessions).toBe(100);
      expect(result.data?.completed_sessions).toBe(80);
      expect(result.data?.total_questions).toBe(10);
      expect(result.data?.avg_completion_time_minutes).toBe(13); // Rounded
      expect(result.data?.completion_rate).toBe(80); // 80/100 * 100
      expect(result.data?.popularity_score).toBe(94); // (100 * 0.7) + (80 * 0.3)
    });

    it('should handle empty stats correctly', async () => {
      const packId = 'pack_empty';
      const mockRawStats = {
        total_sessions: 0,
        completed_sessions: 0,
        total_questions: 0,
        avg_completion_time_minutes: 0,
      };

      mockRepository.getQuizPackStats.mockResolvedValue(mockRawStats);

      const result = await service.getQuizPackStats(packId);

      expect(result.success).toBe(true);
      expect(result.data?.total_sessions).toBe(0);
      expect(result.data?.completion_rate).toBe(0);
      expect(result.data?.popularity_score).toBe(0);
    });
  });

  describe('getRecommendedQuizPacks', () => {
    it('should get recommended quiz packs with correct scoring', async () => {
      const mockQuizPacks: QuizPack[] = [
        {
          id: 'pack_popular',
          name: 'Popular Quiz',
          name_localized: null,
          description: 'Very popular quiz',
          description_localized: null,
          quiz_type: 'emotion_wheel',
          quiz_style: 'interactive',
          category: 'emotion',
          difficulty_level: 3,
          estimated_duration_minutes: 15,
          tags: ['emotion'],
          metadata: {},
          is_active: true,
          created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
          updated_at: new Date(),
        },
        {
          id: 'pack_new',
          name: 'New Quiz',
          name_localized: null,
          description: 'Brand new quiz',
          description_localized: null,
          quiz_type: 'tcm_assessment',
          quiz_style: 'questionnaire',
          category: 'health',
          difficulty_level: 2,
          estimated_duration_minutes: 20,
          tags: ['tcm'],
          metadata: {},
          is_active: true,
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
          updated_at: new Date(),
        },
      ];

      mockRepository.findActiveQuizPacks.mockResolvedValue(mockQuizPacks);

      // Mock stats for each pack
      mockRepository.getQuizPackStats
        .mockResolvedValueOnce({
          total_sessions: 100,
          completed_sessions: 90,
          total_questions: 10,
          avg_completion_time_minutes: 15,
        })
        .mockResolvedValueOnce({
          total_sessions: 5,
          completed_sessions: 4,
          total_questions: 8,
          avg_completion_time_minutes: 18,
        });

      const result = await service.getRecommendedQuizPacks(2);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(mockRepository.findActiveQuizPacks).toHaveBeenCalled();
    });
  });

  describe('updateQuizPack', () => {
    it('should update a quiz pack successfully', async () => {
      const packId = 'pack_123';
      const updates: UpdateQuizPackInput = {
        name: 'Updated Quiz Name',
        difficulty_level: 4,
        is_active: false,
      };

      const mockUpdatedPack: QuizPack = {
        id: packId,
        name: 'Updated Quiz Name',
        name_localized: null,
        description: 'Original description',
        description_localized: null,
        quiz_type: 'emotion_wheel',
        quiz_style: 'interactive',
        category: 'emotion',
        difficulty_level: 4,
        estimated_duration_minutes: 15,
        tags: ['emotion'],
        metadata: {},
        is_active: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.update.mockResolvedValue(mockUpdatedPack);

      const result = await service.updateQuizPack(packId, updates);

      expect(result.success).toBe(true);
      expect(result.data?.name).toBe('Updated Quiz Name');
      expect(result.data?.difficulty_level).toBe(4);
      expect(result.data?.is_active).toBe(false);
      expect(mockRepository.update).toHaveBeenCalledWith(packId, updates);
    });
  });

  describe('activateQuizPack and deactivateQuizPack', () => {
    it('should activate a quiz pack successfully', async () => {
      const packId = 'pack_123';
      const mockActivatedPack: QuizPack = {
        id: packId,
        name: 'Test Quiz',
        name_localized: null,
        description: 'Test description',
        description_localized: null,
        quiz_type: 'emotion_wheel',
        quiz_style: 'interactive',
        category: 'emotion',
        difficulty_level: 3,
        estimated_duration_minutes: 15,
        tags: [],
        metadata: {},
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.update.mockResolvedValue(mockActivatedPack);

      const result = await service.activateQuizPack(packId);

      expect(result.success).toBe(true);
      expect(result.data?.is_active).toBe(true);
      expect(mockRepository.update).toHaveBeenCalledWith(packId, { is_active: true });
    });

    it('should deactivate a quiz pack successfully', async () => {
      const packId = 'pack_123';
      const mockDeactivatedPack: QuizPack = {
        id: packId,
        name: 'Test Quiz',
        name_localized: null,
        description: 'Test description',
        description_localized: null,
        quiz_type: 'emotion_wheel',
        quiz_style: 'interactive',
        category: 'emotion',
        difficulty_level: 3,
        estimated_duration_minutes: 15,
        tags: [],
        metadata: {},
        is_active: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.update.mockResolvedValue(mockDeactivatedPack);

      const result = await service.deactivateQuizPack(packId);

      expect(result.success).toBe(true);
      expect(result.data?.is_active).toBe(false);
      expect(mockRepository.update).toHaveBeenCalledWith(packId, { is_active: false });
    });
  });

  describe('getQuizPackCategories and getQuizPackTypes', () => {
    it('should get unique categories from active quiz packs', async () => {
      const mockQuizPacks: QuizPack[] = [
        {
          id: 'pack_1',
          name: 'Quiz 1',
          name_localized: null,
          description: 'Description 1',
          description_localized: null,
          quiz_type: 'emotion_wheel',
          quiz_style: 'interactive',
          category: 'emotion',
          difficulty_level: 3,
          estimated_duration_minutes: 15,
          tags: [],
          metadata: {},
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'pack_2',
          name: 'Quiz 2',
          name_localized: null,
          description: 'Description 2',
          description_localized: null,
          quiz_type: 'tcm_assessment',
          quiz_style: 'questionnaire',
          category: 'health',
          difficulty_level: 4,
          estimated_duration_minutes: 20,
          tags: [],
          metadata: {},
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'pack_3',
          name: 'Quiz 3',
          name_localized: null,
          description: 'Description 3',
          description_localized: null,
          quiz_type: 'emotion_wheel',
          quiz_style: 'interactive',
          category: 'emotion', // Duplicate category
          difficulty_level: 2,
          estimated_duration_minutes: 10,
          tags: [],
          metadata: {},
          is_active: true,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      mockRepository.findActiveQuizPacks.mockResolvedValue(mockQuizPacks);

      const categoriesResult = await service.getQuizPackCategories();
      const typesResult = await service.getQuizPackTypes();

      expect(categoriesResult.success).toBe(true);
      expect(categoriesResult.data).toEqual(['emotion', 'health']);

      expect(typesResult.success).toBe(true);
      expect(typesResult.data).toEqual(['emotion_wheel', 'tcm_assessment']);
    });
  });

  describe('event emission', () => {
    it('should emit events for quiz pack operations', async () => {
      const quizPackCreatedSpy = jest.fn();
      const quizPackUpdatedSpy = jest.fn();
      const quizPackActivatedSpy = jest.fn();

      service.on('quizPackCreated', quizPackCreatedSpy);
      service.on('quizPackUpdated', quizPackUpdatedSpy);
      service.on('quizPackActivated', quizPackActivatedSpy);

      const mockQuizPack: QuizPack = {
        id: 'pack_123',
        name: 'Test Quiz',
        name_localized: null,
        description: 'Test description',
        description_localized: null,
        quiz_type: 'emotion_wheel',
        quiz_style: 'interactive',
        category: 'emotion',
        difficulty_level: 3,
        estimated_duration_minutes: 15,
        tags: [],
        metadata: {},
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.create.mockResolvedValue(mockQuizPack);
      mockRepository.update.mockResolvedValue(mockQuizPack);

      // Test quiz pack creation event
      await service.createQuizPack({
        name: 'Test Quiz',
        quiz_type: 'emotion_wheel',
      });

      expect(quizPackCreatedSpy).toHaveBeenCalledWith(mockQuizPack);

      // Test quiz pack update event
      await service.updateQuizPack('pack_123', { name: 'Updated Quiz' });

      expect(quizPackUpdatedSpy).toHaveBeenCalledWith(mockQuizPack);

      // Test quiz pack activation event
      await service.activateQuizPack('pack_123');

      expect(quizPackActivatedSpy).toHaveBeenCalledWith(mockQuizPack);
    });
  });
});
