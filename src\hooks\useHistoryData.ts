/**
 * History页面数据获取钩子
 * @deprecated 此Hook依赖deprecated的useHybridMoodEntriesData，建议使用Quiz session-based approach
 * 实现离线在线混合模式的历史记录数据获取
 */

import type { MoodEntry } from '@/types';
import { useCallback, useEffect, useState } from 'react';
import { useHybridMoodEntriesData } from './useHybridData';

interface UseHistoryDataReturn {
  // 数据状态
  history: MoodEntry[];

  // 加载状态
  isLoading: boolean;
  error: string | null;

  // 网络状态
  isOnline: boolean;
  lastSyncTime: Date | null;

  // 操作方法
  refresh: () => Promise<void>;
  forceSync: () => Promise<void>;

  // 数据处理方法
  getEntriesByDate: (date: string) => MoodEntry[];
  getDatesWithEntries: () => Set<string>;
}

/**
 * History页面数据钩子
 * 管理心情记录历史数据的获取和处理
 */
export const useHistoryData = (): UseHistoryDataReturn => {
  // 使用混合数据获取钩子获取心情记录
  const {
    data: moodEntries,
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    refresh,
    forceSync,
  } = useHybridMoodEntriesData();

  // 本地状态
  const [history, setHistory] = useState<MoodEntry[]>([]);

  // 当心情记录数据更新时，更新本地状态
  useEffect(() => {
    if (moodEntries && Array.isArray(moodEntries)) {
      // 按时间戳降序排序（最新的在前）
      const sortedEntries = [...moodEntries].sort((a, b) => {
        const timeA = typeof a.timestamp === 'string' ? new Date(a.timestamp) : a.timestamp;
        const timeB = typeof b.timestamp === 'string' ? new Date(b.timestamp) : b.timestamp;
        return timeB.getTime() - timeA.getTime();
      });

      setHistory(sortedEntries);
      console.log('[useHistoryData] Updated history with', sortedEntries.length, 'entries');
    } else {
      setHistory([]);
    }
  }, [moodEntries]);

  // 根据日期获取记录
  const getEntriesByDate = useCallback(
    (date: string): MoodEntry[] => {
      return history.filter((entry) => {
        const timestamp =
          typeof entry.timestamp === 'string' ? new Date(entry.timestamp) : entry.timestamp;
        const entryDate = timestamp.toISOString().split('T')[0];
        return entryDate === date;
      });
    },
    [history]
  );

  // 获取有记录的日期集合
  const getDatesWithEntries = useCallback((): Set<string> => {
    const dates = new Set<string>();
    history.forEach((entry) => {
      const timestamp =
        typeof entry.timestamp === 'string' ? new Date(entry.timestamp) : entry.timestamp;
      if (!Number.isNaN(timestamp.getTime())) {
        const dateStr = timestamp.toISOString().split('T')[0];
        dates.add(dateStr);
      }
    });
    return dates;
  }, [history]);

  return {
    // 数据状态
    history,

    // 加载状态
    isLoading,
    error: error?.message || null,

    // 网络状态
    isOnline,
    lastSyncTime,

    // 操作方法
    refresh,
    forceSync,

    // 数据处理方法
    getEntriesByDate,
    getDatesWithEntries,
  };
};
