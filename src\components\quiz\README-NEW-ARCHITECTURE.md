# Quiz组件新架构设计

本文档介绍基于统一表结构和新组件设计的Quiz系统架构，替代原有的ViewFactory情绪数据渲染系统。

## 架构概述

### 原有架构 vs 新架构

#### 原有架构（基于emotion_data_sets）
```
TierNavigation
├── ViewFactory
│   ├── DisplayAdapter
│   ├── WheelView (D3/SVG/R3F)
│   ├── CardView
│   └── BubbleView
└── emotion_data_sets表结构
    ├── emotion_data_set_tiers
    ├── emotion_data_set_emotions
    └── emotion_data_set_tier_emotions
```

#### 新架构（基于quiz_*表）
```
QuizTierNavigation
├── QuizComponentRenderer
│   ├── SelectorComponent
│   ├── RatingComponent
│   ├── SliderComponent
│   ├── WheelComponent
│   ├── CardComponent
│   └── BubbleComponent
├── EmotionWheelView (特殊视图)
└── 统一表结构
    ├── quiz_packs
    ├── quiz_questions
    ├── quiz_question_options
    ├── quiz_sessions
    └── quiz_answers
```

## 核心组件

### 1. QuizTierNavigation

**功能**: 主要的层级导航组件，替代原有的TierNavigation
**特点**:
- 基于quiz_questions和quiz_question_options
- 支持多种问题类型（emotion_wheel, single_choice, multiple_choice, scale_rating, slider）
- 自动根据问题类型选择合适的渲染组件
- 支持层级联动和选择路径跟踪

**使用方法**:
```typescript
<QuizTierNavigation
  currentQuestion={currentQuestion}
  onSelect={handleEmotionSelect}
  onBack={handleTierBack}
  selectedPath={selectedPath}
  packId={emotionWheelData?.pack.id}
/>
```

### 2. QuizComponentRenderer

**功能**: 动态组件渲染器，根据组件类型渲染不同的Quiz组件
**支持的组件类型**:
- `selector_component`: 选择器组件（单选/多选）
- `rating_component`: 评分组件（Likert量表/星级评分）
- `slider_component`: 滑块组件
- `text_input_component`: 文本输入组件
- `wheel_component`: 轮盘组件
- `card_component`: 卡片组件
- `bubble_component`: 气泡组件

**使用方法**:
```typescript
<QuizComponentRenderer
  componentType="selector_component"
  config={{
    id: currentQuestion.id,
    question_text: currentQuestion.question_text,
    options: availableOptions,
    multiple: false,
    layout: 'grid'
  }}
  onSelect={handleSelect}
  onBack={onBack}
  personalizationConfig={personalizationConfig}
/>
```

### 3. EmotionWheelView

**功能**: 专门的情绪轮盘视图，用于情绪选择场景
**特点**:
- 支持多层级情绪展示
- 支持父子情绪联动
- 可配置的轮盘样式和动画
- 集成触觉反馈

## 数据流架构

### 1. 数据获取流程
```
NewHome.tsx
├── useNewHomeData
│   ├── 获取emotionWheelData
│   ├── 获取recommendedQuizPacks
│   └── 获取userStats
├── useQuizSession
│   ├── 创建Quiz会话
│   ├── 管理答案提交
│   └── 跟踪会话进度
└── QuizTierNavigation
    ├── 渲染当前问题
    ├── 处理选项选择
    └── 管理选择路径
```

### 2. 选择处理流程
```
用户选择选项
├── QuizTierNavigation.handleSelect
├── 触觉反馈
├── 更新selectedPath
├── 更新selectedEmotions
├── 检查是否有下一层级
└── 导航到下一问题或完成
```

### 3. 保存流程
```
用户完成选择
├── NewHome.handleSave
├── 创建Quiz会话
├── 提交所有答案
├── 保存情绪记录
└── 导航到历史页面
```

## 配置系统

### 1. 个性化配置
新架构支持6层个性化配置：
- Layer 1: 全局基础配置
- Layer 2: 用户类型配置
- Layer 3: 皮肤基础配置
- Layer 4: 用户个人配置
- Layer 5: 会话临时配置
- Layer 6: 实时交互配置

### 2. 组件配置
每个Quiz组件都支持丰富的配置选项：

```typescript
// 选择器组件配置
interface SelectorConfig {
  id: string;
  question_text: string;
  options: QuizQuestionOption[];
  multiple?: boolean;
  layout?: 'grid' | 'list' | 'horizontal';
  max_selections?: number;
}

// 评分组件配置
interface RatingConfig {
  id: string;
  question_text: string;
  options: QuizQuestionOption[];
  scale_type?: 'likert' | 'star' | 'numeric';
  min_value?: number;
  max_value?: number;
  step_value?: number;
}
```

## 样式系统

### 1. CSS架构
- 使用CSS自定义属性支持主题切换
- 响应式设计适配移动端
- 毛玻璃效果和现代UI设计
- 流畅的动画和过渡效果

### 2. 主要样式特点
- **毛玻璃效果**: `backdrop-filter: blur(10px)`
- **渐变背景**: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **圆角设计**: `border-radius: 16px`
- **悬停效果**: `transform: translateY(-2px)`
- **阴影效果**: `box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2)`

## 迁移指南

### 1. 从TierNavigation迁移到QuizTierNavigation

**原有代码**:
```typescript
<TierNavigation
  tier={currentTierData?.id || ''}
  emotions={currentEmotions}
  onSelect={handleEmotionSelect}
  onBack={handleTierBack}
/>
```

**新代码**:
```typescript
<QuizTierNavigation
  currentQuestion={currentQuestion}
  onSelect={handleEmotionSelect}
  onBack={handleTierBack}
  selectedPath={selectedPath}
  packId={emotionWheelData?.pack.id}
/>
```

### 2. 数据结构迁移

**原有数据结构**:
```typescript
interface Emotion {
  id: string;
  name: string;
  emoji: string;
  color: string;
}
```

**新数据结构**:
```typescript
interface QuizQuestionOption {
  id: string;
  question_id: string;
  option_text: string;
  option_value: string;
  metadata?: any; // 包含emoji, color等信息
}
```

### 3. 选择处理迁移

**原有处理**:
```typescript
const handleEmotionSelect = (emotion: Emotion, tierIndex: number) => {
  // 基于emotion对象处理
};
```

**新处理**:
```typescript
const handleEmotionSelect = (selectedOption: QuizQuestionOption) => {
  // 基于QuizQuestionOption处理
  // 支持更丰富的元数据
};
```

## 优势对比

### 1. 数据管理
- **原有**: 分散的emotion_data_sets表结构
- **新架构**: 统一的quiz_*表结构，更好的数据一致性

### 2. 组件复用
- **原有**: ViewFactory创建特定视图组件
- **新架构**: QuizComponentRenderer支持多种组件类型，更好的复用性

### 3. 配置灵活性
- **原有**: 基于皮肤的配置系统
- **新架构**: 6层个性化配置，更精细的控制

### 4. 类型安全
- **原有**: 部分类型定义不完整
- **新架构**: 完整的TypeScript类型定义

### 5. 扩展性
- **原有**: 添加新视图类型需要修改ViewFactory
- **新架构**: 添加新组件类型只需扩展QuizComponentRenderer

## 测试策略

### 1. 单元测试
- QuizTierNavigation组件测试
- QuizComponentRenderer组件测试
- 各种Quiz组件类型测试

### 2. 集成测试
- 完整的情绪选择流程测试
- 数据保存和会话管理测试
- 多层级导航测试

### 3. 用户体验测试
- 触觉反馈测试
- 动画和过渡效果测试
- 响应式设计测试

## 性能优化

### 1. 组件优化
- 使用React.memo优化组件渲染
- 使用useMemo缓存计算结果
- 懒加载非关键组件

### 2. 数据优化
- 实现数据缓存机制
- 优化数据库查询
- 减少不必要的API调用

### 3. 样式优化
- 使用CSS变量减少重复计算
- 优化动画性能
- 减少重绘和重排

## 未来规划

### 1. 短期目标
- 完善所有Quiz组件类型
- 优化性能和用户体验
- 完善测试覆盖

### 2. 中期目标
- 支持更多问题类型
- 增强个性化配置
- 集成AI推荐系统

### 3. 长期目标
- 支持自定义组件
- 可视化配置界面
- 多平台适配
