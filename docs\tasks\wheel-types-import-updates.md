# wheelTypes.ts 导入更新计划

本文档列出了所有导入 `wheelTypes.ts` 的文件，以及需要进行的更新。

## 导入更新列表

| 文件路径 | 旧导入 | 新导入 | 状态 |
|---------|-------|-------|------|
| `src/utils/wheelFactory.ts` | `import { WheelConfig, WheelContentStrategy, WheelContentType, WheelType } from '../types/wheelTypes';` | 已废弃，不需要更新 | 已完成 |
| `src/utils/wheelFactory.test.ts` | 无直接导入，但依赖 `wheelFactory.ts` | 已废弃，不需要更新 | 已完成 |
| `src/utils/typeConverters.ts` | `import { WheelContentType, WheelType } from '../types/wheelTypes';` | `import { ContentDisplayMode, RenderEngine } from '../types/previewTypes';` | 已完成 |
| `src/components/mood/WheelAdapter.tsx` | `import { WheelType, WheelContentType } from '../../types/wheelTypes';` | `import { ContentDisplayMode, RenderEngine } from '../../types/previewTypes';` | 已完成 |
| `src/types/compatibilityTypes.ts` | `import { WheelContentType } from './wheelTypes';` | `import { ContentDisplayMode } from './previewTypes';` | 已完成 |
| `src/pages/WheelWithAnimatedEmoji.tsx` | `import { WheelType, WheelContentType } from '@/types/wheelTypes';` | `import { ContentDisplayMode, RenderEngine } from '@/types/previewTypes';` | 已完成 |
| `src/components/mood/BaseWheel.tsx` | `import { WheelConfig, WheelContentStrategy } from '../../types/wheelTypes';` | `import { SkinConfig } from '../../types/skinTypes';` | 已完成 |
| `src/types/previewTypes.ts` | `import { WheelContentType, WheelType } from './wheelTypes';` | 移除导入，直接定义类型 | 已完成 |
| `src/components/mood/R3FWheel.tsx` | `import { WheelConfig, WheelContentStrategy } from '../../types/wheelTypes';` | 已废弃，不需要更新 | 已完成 |
| `src/components/mood/D3Wheel.tsx` | `import { WheelConfig, WheelContentStrategy } from '../../types/wheelTypes';` | `import { SkinConfig } from '../../types/skinTypes';` | 已完成 |
| `src/components/mood/SVGWheel.tsx` | `import { WheelConfig, WheelContentStrategy } from '../../types/wheelTypes';` | `import { SkinConfig } from '../../types/skinTypes';` | 已完成 |

## 更新步骤

### 1. 更新 previewTypes.ts

首先，我们需要确保 `previewTypes.ts` 包含所有必要的类型定义：

```typescript
// 移除对 wheelTypes.ts 的导入
// import { WheelContentType, WheelType } from './wheelTypes';

/**
 * 视图类型
 */
export type ViewType = 'wheel' | 'card' | 'bubble' | 'galaxy';

/**
 * 内容显示模式
 */
export type ContentDisplayMode = 'text' | 'emoji' | 'textEmoji' | 'animatedEmoji';

/**
 * 轮盘实现类型
 */
export type RenderEngine = 'D3' | 'SVG' | 'R3F';
```

### 2. 更新 compatibilityTypes.ts

更新 `compatibilityTypes.ts` 文件，移除对 `wheelTypes.ts` 的依赖：

```typescript
// 移除对 wheelTypes.ts 的导入
// import { WheelContentType } from './wheelTypes';

// 更新 UnifiedContentDisplayMode 类型定义
export type UnifiedContentDisplayMode = ContentDisplayMode;

// 移除这些函数
// export function convertWheelContentTypeToDisplayMode(wheelContentType: WheelContentType): ContentDisplayMode
// export function convertDisplayModeToWheelContentType(contentDisplayMode: ContentDisplayMode): WheelContentType
```

### 3. 更新 typeConverters.ts

更新 `typeConverters.ts` 文件，使用新的类型：

```typescript
import { DisplayMode, RenderEngine, ViewType } from '../types/displayTypes';
import { ContentDisplayMode, RenderEngine } from '../types/previewTypes';

/**
 * 将 DisplayMode 转换为 ContentDisplayMode
 * @param displayMode 显示模式
 * @returns 内容显示模式
 */
export function displayModeToContentDisplayMode(displayMode: DisplayMode): ContentDisplayMode {
  switch (displayMode) {
    case 'text':
      return 'text';
    case 'emoji':
      return 'emoji';
    case 'textEmoji':
      return 'textEmoji';
    default:
      return 'textEmoji';
  }
}

/**
 * 将 RenderEngine 转换为 RenderEngine
 * @param renderEngine 渲染引擎
 * @returns 轮盘实现类型
 */
export function renderEngineToRenderEngine(renderEngine: RenderEngine): RenderEngine {
  switch (renderEngine) {
    case 'D3':
      return 'D3';
    case 'SVG':
      return 'SVG';
    case 'R3F':
      return 'R3F';
    default:
      return 'D3';
  }
}
```

### 4. 更新 WheelAdapter.tsx

更新 `WheelAdapter.tsx` 文件，使用新的类型：
使用setting页面生成的UserConfig来渲染navigation 而不是NavigationStyle，因为NavigationStyle是旧的，UserConfig是新的


```typescript
import { ContentDisplayMode, RenderEngine } from '../../types/previewTypes';

// 将导航样式映射到轮盘类型和内容类型
const mapNavigationStyleToWheelType = (style: NavigationStyle): { wheelType: RenderEngine, contentType: ContentDisplayMode } => {
  switch (style) {
    case 'd3wheel':
      return { wheelType: 'D3', contentType: 'textEmoji' };
    case 'classicwheel':
      return { wheelType: 'D3', contentType: 'text' };
    case 'emojiwheel':
      return { wheelType: 'D3', contentType: 'emoji' };
    case 'precisewheel':
      return { wheelType: 'SVG', contentType: 'textEmoji' };
    case 'r3fwheel':
      return { wheelType: 'R3F', contentType: 'textEmoji' };
    default:
      return { wheelType: 'D3', contentType: 'textEmoji' };
  }
};
```

### 5. 更新 WheelWithAnimatedEmoji.tsx

更新 `WheelWithAnimatedEmoji.tsx` 文件，使用新的类型：

```typescript
import { ContentDisplayMode, RenderEngine } from '@/types/previewTypes';

// 更新状态变量类型
const [selectedWheelType, setSelectedWheelType] = useState<RenderEngine>('R3F');
const [selectedContentType, setSelectedContentType] = useState<ContentDisplayMode>('animatedEmoji');
```

### 6. 更新 BaseWheel.tsx, R3FWheel.tsx, D3Wheel.tsx, SVGWheel.tsx

这些文件都需要更新为使用 `SkinConfig`：

```typescript
import { SkinConfig } from '../../types/skinTypes';

// 创建内容策略接口
export interface ContentStrategy {
  renderContent(
    emotion: any,
    position: { x: number, y: number, z?: number },
    config: SkinConfig
  ): JSX.Element;

  shouldShowEmoji?(): boolean;
  shouldShowText?(): boolean;
}
```

### 7. 更新 wheelFactory.ts

这是最复杂的更新，需要重构 `wheelFactory.ts` 文件，使用新的类型和接口：

```typescript
import { ContentDisplayMode, RenderEngine } from '../types/previewTypes';
import { SkinConfig } from '../types/skinTypes';
// 其他导入...

// 更新 createWheel 方法的签名
createWheel(
  type: RenderEngine = 'D3',
  contentType: ContentDisplayMode = 'textEmoji',
  config: SkinConfig = defaultSkinConfig
): BaseWheel {
  // 实现...
}
```

## 测试计划

每次更新导入语句后，需要运行以下测试：

1. **类型检查**：运行 TypeScript 编译器，确保没有类型错误
2. **单元测试**：运行 `npm test`，确保所有测试通过
3. **手动测试**：在开发环境中测试相关功能，确保一切正常工作
