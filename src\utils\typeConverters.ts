import type { DisplayMode, RenderEngine, ViewType } from '../types/displayTypes';
import type { ContentDisplayMode, RenderEngine } from '../types/previewTypes';

/**
 * 将 DisplayMode 转换为 ContentDisplayMode
 * @param displayMode 显示模式
 * @returns 内容显示模式
 */
export function displayModeToContentDisplayMode(displayMode: DisplayMode): ContentDisplayMode {
  switch (displayMode) {
    case 'text':
      return 'text';
    case 'emoji':
      return 'emoji';
    case 'textEmoji':
      return 'textEmoji';
    default:
      return 'textEmoji';
  }
}

/**
 * @deprecated 使用 displayModeToContentDisplayMode 替代
 */
export function displayModeToWheelContentType(displayMode: DisplayMode): ContentDisplayMode {
  console.warn('displayModeToWheelContentType 已废弃，请使用 displayModeToContentDisplayMode');
  return displayModeToContentDisplayMode(displayMode);
}

/**
 * 将 RenderEngine 转换为 RenderEngine
 * @param renderEngine 渲染引擎
 * @returns 轮盘实现类型
 */
export function renderEngineToRenderEngine(renderEngine: RenderEngine): RenderEngine {
  switch (renderEngine) {
    case 'D3':
      return 'D3';
    case 'SVG':
      return 'SVG';
    case 'R3F':
      return 'R3F';
    default:
      return 'D3';
  }
}

/**
 * @deprecated 使用 renderEngineToRenderEngine 替代
 */
export function renderEngineToWheelType(renderEngine: RenderEngine): RenderEngine {
  console.warn('renderEngineToWheelType 已废弃，请使用 renderEngineToRenderEngine');
  return renderEngineToRenderEngine(renderEngine);
}

/**
 * 将 ViewType 转换为对应的工厂类型
 * @param viewType 视图类型
 * @returns 工厂类型
 */
export function viewTypeToFactoryType(viewType: ViewType): string {
  switch (viewType) {
    case 'wheel':
      return 'WheelFactory';
    case 'card':
      return 'CardFactory';
    case 'bubble':
      return 'BubbleFactory';
    case 'list':
      return 'ListFactory';
    case 'grid':
      return 'GridFactory';
    default:
      return 'WheelFactory';
  }
}
