/**
 * useQuizSession Hook 测试
 * 简化版本，专注于基本功能测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useQuizSession } from '../useQuizSession';

// Mock ServiceFactory completely
const mockService = {
  createSession: vi.fn(),
  findById: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  startSession: vi.fn(),
  completeSession: vi.fn(),
  pauseSession: vi.fn(),
  resumeSession: vi.fn(),
  updateProgress: vi.fn(),
  getUserSessions: vi.fn(),
  getUserActiveSessions: vi.fn(),
  getUserCompletedSessions: vi.fn(),
  getPackSessions: vi.fn(),
  on: vi.fn(),
  emit: vi.fn(),
};

const mockServiceFactory = {
  getQuizSessionService: vi.fn().mockResolvedValue(mockService)
};

// Mock the services module
vi.mock('../../services', () => ({
  ServiceFactory: {
    getInstance: vi.fn(() => mockServiceFactory)
  }
}));

describe('useQuizSession', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize hook with default state', () => {
    const { result } = renderHook(() => useQuizSession());

    // Check initial state
    expect(result.current.isLoading).toBe(false);
    expect(result.current.currentSession).toBe(null);
    expect(result.current.error).toBe(null);
    
    // Check that all methods are defined
    expect(typeof result.current.createSession).toBe('function');
    expect(typeof result.current.getSession).toBe('function');
    expect(typeof result.current.updateSession).toBe('function');
    expect(typeof result.current.startSession).toBe('function');
    expect(typeof result.current.completeSession).toBe('function');
    expect(typeof result.current.pauseSession).toBe('function');
    expect(typeof result.current.resumeSession).toBe('function');
    expect(typeof result.current.deleteSession).toBe('function');
    expect(typeof result.current.updateProgress).toBe('function');
    expect(typeof result.current.getUserSessions).toBe('function');
    expect(typeof result.current.getUserActiveSessions).toBe('function');
    expect(typeof result.current.getUserCompletedSessions).toBe('function');
    expect(typeof result.current.getPackSessions).toBe('function');
    expect(typeof result.current.setCurrentSession).toBe('function');
    expect(typeof result.current.clearError).toBe('function');
  });

  it('should handle service not initialized error', async () => {
    const { result } = renderHook(() => useQuizSession());

    // Try to create session before service is initialized
    const sessionResult = await result.current.createSession({
      pack_id: 'pack_emotion_wheel',
      user_id: 'user_123',
      session_type: 'standard',
    });

    expect(sessionResult.success).toBe(false);
    expect(sessionResult.error).toBe('Service not initialized');
  });

  it('should manage current session state', () => {
    const { result } = renderHook(() => useQuizSession());

    const mockSession = {
      id: 'session_123',
      pack_id: 'pack_emotion_wheel',
      user_id: 'user_123',
      status: 'INITIATED' as const,
      current_question_index: 0,
      total_questions: 5,
      answered_questions: 0,
      completion_percentage: 0,
      start_time: new Date().toISOString(),
      last_active_time: new Date().toISOString(),
      session_type: 'standard',
      session_metadata: '{}',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Initially no session
    expect(result.current.currentSession).toBe(null);

    // Set current session
    act(() => {
      result.current.setCurrentSession(mockSession);
    });

    expect(result.current.currentSession).toEqual(mockSession);

    // Clear current session
    act(() => {
      result.current.setCurrentSession(null);
    });

    expect(result.current.currentSession).toBe(null);
  });

  it('should manage error state', () => {
    const { result } = renderHook(() => useQuizSession());

    // Initially no error
    expect(result.current.error).toBe(null);

    // Clear error (should not throw)
    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBe(null);
  });

  it('should handle loading state', () => {
    const { result } = renderHook(() => useQuizSession());

    // Initially not loading
    expect(result.current.isLoading).toBe(false);
  });
});
