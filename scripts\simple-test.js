/**
 * 简单测试脚本
 * 直接测试 Neutral 和 Happy 之间的对比度
 */

// 模拟颜色工具函数
function parseRgb(color) {
  // 处理十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.substring(1);
    const r = Number.parseInt(hex.substring(0, 2), 16) / 255;
    const g = Number.parseInt(hex.substring(2, 4), 16) / 255;
    const b = Number.parseInt(hex.substring(4, 6), 16) / 255;
    return { r, g, b };
  }

  // 处理 rgb 格式
  const rgbMatch = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
  if (rgbMatch) {
    return {
      r: Number.parseInt(rgbMatch[1], 10) / 255,
      g: Number.parseInt(rgbMatch[2], 10) / 255,
      b: Number.parseInt(rgbMatch[3], 10) / 255,
    };
  }

  // 默认返回黑色
  return { r: 0, g: 0, b: 0 };
}

// 计算对比度
function calculateContrastRatio(color1, color2) {
  const rgb1 = parseRgb(color1);
  const rgb2 = parseRgb(color2);

  // 计算相对亮度
  const getLuminance = (rgb) => {
    const sRGB = {
      r: rgb.r,
      g: rgb.g,
      b: rgb.b,
    };

    // 转换为线性RGB
    const linearRGB = {
      r: sRGB.r <= 0.03928 ? sRGB.r / 12.92 : ((sRGB.r + 0.055) / 1.055) ** 2.4,
      g: sRGB.g <= 0.03928 ? sRGB.g / 12.92 : ((sRGB.g + 0.055) / 1.055) ** 2.4,
      b: sRGB.b <= 0.03928 ? sRGB.b / 12.92 : ((sRGB.b + 0.055) / 1.055) ** 2.4,
    };

    return 0.2126 * linearRGB.r + 0.7152 * linearRGB.g + 0.0722 * linearRGB.b;
  };

  const l1 = getLuminance(rgb1);
  const l2 = getLuminance(rgb2);

  // 计算对比度比率
  return (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
}

// 测试一些颜色对
console.log('测试 Neutral 和 Happy 之间的对比度');
console.log('====================================');

// 游戏风格颜色 - 这些是我们更新后的颜色
const gameStyleColors = {
  // 浅色主题
  light: {
    neutral: '#9C27B0', // 深紫色 (Neutral)
    happy: '#FFEB3B', // 亮黄色 (Happy)
  },
  // 深色主题
  dark: {
    neutral: '#E040FB', // 亮紫色 (Neutral)
    happy: '#FFFF00', // 亮黄色 (Happy)
  },
};

// 测试游戏风格颜色
console.log('\n游戏风格颜色 - 浅色主题:');
const lightContrast = calculateContrastRatio(
  gameStyleColors.light.neutral,
  gameStyleColors.light.happy
);
console.log(`Neutral: ${gameStyleColors.light.neutral}`);
console.log(`Happy: ${gameStyleColors.light.happy}`);
console.log(`对比度: ${lightContrast.to(3)}`);
console.log(lightContrast >= 2.0 ? '✅ 通过' : '❌ 失败');

console.log('\n游戏风格颜色 - 深色主题:');
const darkContrast = calculateContrastRatio(
  gameStyleColors.dark.neutral,
  gameStyleColors.dark.happy
);
console.log(`Neutral: ${gameStyleColors.dark.neutral}`);
console.log(`Happy: ${gameStyleColors.dark.happy}`);
console.log(`对比度: ${darkContrast.to(3)}`);
console.log(darkContrast >= 2.0 ? '✅ 通过' : '❌ 失败');

// 测试原始问题颜色对
console.log('\n原始问题颜色对:');
const originalContrast = calculateContrastRatio('#CCCCCC', '#CDCDCD');
console.log('Neutral: #CCCCCC');
console.log('Happy: #CDCDCD');
console.log(`对比度: ${originalContrast.to(3)}`);
console.log(originalContrast >= 1.5 ? '✅ 通过' : '❌ 失败');

console.log('\n====================================');
console.log('测试完成');
