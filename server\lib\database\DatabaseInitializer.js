/**
 * 数据库初始化服务
 * 用于初始化数据库，包括创建表结构和加载初始数据
 */
import * as fs from 'node:fs';
import * as path from 'node:path';
/**
 * 数据库初始化服务
 */
export class DatabaseInitializer {
  dbService;
  options;
  /**
   * 构造函数
   * @param dbService 数据库服务
   * @param options 初始化选项
   */
  constructor(dbService, options = {}) {
    this.dbService = dbService;
    this.options = {
      force: false,
      loadTestData: false,
      seedsDir: path.resolve(process.cwd(), '../public/seeds'),
      masterSqlFile: 'master.sql',
      verbose: false,
      ...options,
    };
  }
  /**
   * 初始化数据库
   */
  async initialize() {
    const { force, verbose } = this.options;
    // 检查数据库版本
    const version = await this.getDatabaseVersion();
    if (version > 0 && !force) {
      if (verbose) {
        console.log(
          `[DatabaseInitializer] Database already initialized (version ${version}). Skipping initialization.`
        );
      }
      return;
    }
    if (verbose) {
      console.log('[DatabaseInitializer] Initializing database...');
    }
    // 尝试使用主 SQL 文件初始化
    const masterSqlPath = path.resolve(this.options.seedsDir, this.options.masterSqlFile);
    if (fs.existsSync(masterSqlPath)) {
      await this.initializeWithMasterFile(masterSqlPath);
    } else {
      await this.initializeWithIndividualFiles();
    }
    if (verbose) {
      console.log('[DatabaseInitializer] Database initialization completed.');
    }
  }
  /**
   * 获取数据库版本
   */
  async getDatabaseVersion() {
    try {
      const result = await this.dbService.executeQuery('PRAGMA user_version;');
      if (result.rows && result.rows.length > 0) {
        const versionRow = result.rows[0];
        return typeof versionRow === 'object' && versionRow !== null
          ? versionRow.user_version || 0
          : Array.isArray(versionRow)
            ? versionRow[0] || 0
            : 0;
      }
      return 0;
    } catch (error) {
      console.error('[DatabaseInitializer] Error getting database version:', error);
      return 0;
    }
  }
  /**
   * 使用主 SQL 文件初始化数据库
   * @param masterSqlPath 主 SQL 文件路径
   */
  async initializeWithMasterFile(masterSqlPath) {
    const { verbose } = this.options;
    if (verbose) {
      console.log(`[DatabaseInitializer] Initializing database with master file: ${masterSqlPath}`);
    }
    try {
      // 读取主 SQL 文件
      const masterSql = fs.readFileSync(masterSqlPath, 'utf8');
      // 解析主 SQL 文件中的 .read 命令
      const readCommands = this.parseReadCommands(masterSql);
      // 提取主 SQL 文件中的直接 SQL 语句（不包括 .read 命令）
      const directSql = this.extractDirectSql(masterSql);
      // 如果有直接 SQL 语句，先执行它们
      if (directSql.trim()) {
        if (verbose) {
          console.log('[DatabaseInitializer] Executing direct SQL statements from master file');
        }
        await this.dbService.executeScript(directSql);
      }
      // 执行每个 .read 命令引用的 SQL 文件
      for (const readPath of readCommands) {
        const fullPath = path.resolve(this.options.seedsDir, readPath);
        if (fs.existsSync(fullPath)) {
          if (verbose) {
            console.log(`[DatabaseInitializer] Executing SQL file: ${readPath}`);
          }
          // 如果是测试数据且不加载测试数据，则跳过
          if (!this.options.loadTestData && readPath.startsWith('test/')) {
            if (verbose) {
              console.log(`[DatabaseInitializer] Skipping test data: ${readPath}`);
            }
            continue;
          }
          const sql = fs.readFileSync(fullPath, 'utf8');
          await this.dbService.executeScript(sql);
        } else {
          console.warn(`[DatabaseInitializer] SQL file not found: ${fullPath}`);
        }
      }
      // 执行最后的 SQL 语句（如启用外键约束等）
      await this.dbService.executeQuery('PRAGMA ignore_check_constraints=OFF;');
      await this.dbService.executeQuery('PRAGMA foreign_keys=ON;');
      if (verbose) {
        console.log('[DatabaseInitializer] Database initialized successfully with master file.');
      }
    } catch (error) {
      console.error('[DatabaseInitializer] Error initializing database with master file:', error);
      throw error;
    }
  }
  /**
   * 使用单独的 SQL 文件初始化数据库
   */
  async initializeWithIndividualFiles() {
    const { verbose, seedsDir, loadTestData } = this.options;
    if (verbose) {
      console.log(
        `[DatabaseInitializer] Initializing database with individual files from: ${seedsDir}`
      );
    }
    try {
      // 禁用外键约束
      await this.dbService.executeQuery('PRAGMA foreign_keys=OFF;');
      await this.dbService.executeQuery('PRAGMA ignore_check_constraints=ON;');
      // 执行 schema 目录中的脚本
      const schemaDir = path.join(seedsDir, 'schema');
      if (fs.existsSync(schemaDir)) {
        await this.executeScriptsInDirectory(schemaDir);
      }
      // 执行 config 目录中的脚本
      const configDir = path.join(seedsDir, 'config');
      if (fs.existsSync(configDir)) {
        await this.executeScriptsInDirectory(configDir);
      }
      // 执行 test 目录中的脚本（如果需要）
      if (loadTestData) {
        const testDir = path.join(seedsDir, 'test');
        if (fs.existsSync(testDir)) {
          await this.executeScriptsInDirectory(testDir);
        }
      }
      // 启用外键约束
      await this.dbService.executeQuery('PRAGMA ignore_check_constraints=OFF;');
      await this.dbService.executeQuery('PRAGMA foreign_keys=ON;');
      if (verbose) {
        console.log(
          '[DatabaseInitializer] Database initialized successfully with individual files.'
        );
      }
    } catch (error) {
      console.error(
        '[DatabaseInitializer] Error initializing database with individual files:',
        error
      );
      throw error;
    }
  }
  /**
   * 执行目录中的所有 SQL 脚本
   * @param directory 目录路径
   */
  async executeScriptsInDirectory(directory) {
    const { verbose } = this.options;
    if (verbose) {
      console.log(`[DatabaseInitializer] Executing scripts in directory: ${directory}`);
    }
    const files = fs.readdirSync(directory).filter((file) => file.endsWith('.sql'));
    for (const file of files) {
      if (verbose) {
        console.log(`[DatabaseInitializer] Executing script: ${file}`);
      }
      const sql = fs.readFileSync(path.join(directory, file), 'utf8');
      await this.dbService.executeScript(sql);
    }
  }
  /**
   * 解析主 SQL 文件中的 .read 命令
   * @param sql SQL 文本
   * @returns .read 命令引用的文件路径数组
   */
  parseReadCommands(sql) {
    const readCommands = [];
    const lines = sql.split('\n');
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('.read ')) {
        const filePath = trimmedLine.substring(6).trim();
        readCommands.push(filePath);
      }
    }
    return readCommands;
  }
  /**
   * 提取主 SQL 文件中的直接 SQL 语句（不包括 .read 命令）
   * @param sql SQL 文本
   * @returns 直接 SQL 语句
   */
  extractDirectSql(sql) {
    const lines = sql.split('\n');
    const directSqlLines = [];
    for (const line of lines) {
      const trimmedLine = line.trim();
      // 跳过 .read 命令和空行
      if (!trimmedLine.startsWith('.read ') && trimmedLine !== '') {
        directSqlLines.push(line);
      }
    }
    return directSqlLines.join('\n');
  }
}
