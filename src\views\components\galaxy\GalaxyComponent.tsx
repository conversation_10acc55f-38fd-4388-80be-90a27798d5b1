/**
 * 星系组件
 * 使用 Framer Motion 实现的星系视图，不依赖旧的实现
 *
 * 此组件是新视图系统的一部分，用于替代旧的 galaxy 导航样式
 * 它直接实现了星系的渲染，不依赖旧的实现
 */

import AnimatedEmoji from '@/components/emoji/AnimatedEmoji';
import { useEmoji } from '@/contexts/EmojiContext';
import type { Emotion, ContentDisplayMode, SkinConfig } from '@/types';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { motion } from 'framer-motion';
import type React from 'react';
import { useState } from 'react';

interface GalaxyComponentProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  layout?: string;
}

/**
 * 从 SkinConfig 中提取星系配置
 */
const extractGalaxyConfig = (config: SkinConfig) => {
  // 确保 config 对象及其必要属性存在
  if (!config) {
    console.warn('GalaxyComponent: config is undefined');
    config = {} as SkinConfig;
  }

  // 确保 fonts, colors 和 effects 属性存在
  if (!config.fonts) {
    console.warn('GalaxyComponent: config.fonts is undefined');
    config.fonts = {
      family: 'Arial, sans-serif',
      size: { small: 12, medium: 14, large: 18 },
      weight: { normal: 400, bold: 700 },
    };
  }

  if (!config.colors) {
    console.warn('GalaxyComponent: config.colors is undefined');
    config.colors = {
      text: '#FFFFFF',
      primary: '#FF6B6B',
      secondary: '#FFD166',
      accent: '#06D6A0',

      background: 'rgba(0, 0, 0, 0.9)',
    };
  }

  if (!config.effects) {
    console.warn('GalaxyComponent: config.effects is undefined');
    config.effects = {
      shadows: true,
    };
  }

  return {
    // 使用更新后的 SkinConfig 接口中的字段
    containerSize: config.viewConfigs?.galaxy?.container_size || 300,
    fontSize: config.fonts?.size?.medium || 14,
    fontFamily: config.fonts?.family || 'Arial, sans-serif',
    textColor: config.colors?.text || '#FFFFFF',
    emojiSize: config.viewConfigs?.galaxy?.emojiSize || 20,
    backgroundColor: config.colors?.background || 'rgba(0, 0, 0, 0.9)',
    shadowEnabled: config.effects?.shadows || true,
    shadowColor: config.viewConfigs?.galaxy?.shadowColor || 'rgba(255, 255, 255, 0.5)',
    shadowBlur: config.viewConfigs?.galaxy?.shadowBlur || 5,
    hoverEffect: config.viewConfigs?.galaxy?.hover_effect || 'scale',
    selectionAnimation: config.viewConfigs?.galaxy?.selection_animation || 'pulse',
    transitionDuration: config.viewConfigs?.galaxy?.transition_duration || 200,
    layout: config.viewConfigs?.galaxy?.layout || 'spiral',
    responsiveScaling: true,
    minSize: 200,
    maxSize: 400,
  };
};

/**
 * 星系组件
 */
export const GalaxyComponent: React.FC<GalaxyComponentProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  layout,
}) => {
  // 用于跟踪悬停的情绪ID
  const [hoveredEmotion, setHoveredEmotion] = useState<string | null>(null);
  const { getEmojiItem } = useEmoji();

  // 提取星系配置
  const galaxyConfig = extractGalaxyConfig(skinConfig);

  // 检测当前主题是深色还是浅色
  const isDarkMode =
    typeof document !== 'undefined' && document.documentElement.classList.contains('dark');

  // 计算元素位置
  const calculatePositions = () => {
    return emotions.map((emotion, index) => {
      // 特殊处理只有两个情绪的情况
      if (emotions.length === 2) {
        // 为不同层级使用不同的角度，避免重叠
        let angle: number;
        if (tierLevel === 1) {
          angle = index === 0 ? -90 : 90; // 上下分布
        } else if (tierLevel === 2) {
          angle = index === 0 ? 0 : 180; // 左右分布
        } else {
          // tierLevel === 3
          angle = index === 0 ? -45 : 135; // 对角线分布
        }

        // 根据文本长度动态调整距离
        const textFactor = Math.max(1, emotion.name.length / 8); // 文本长度因子
        const distance =
          tierLevel === 1
            ? 120 + textFactor * 10
            : tierLevel === 2
              ? 140 + textFactor * 10
              : 160 + textFactor * 10;

        return {
          x: Math.cos(angle * (Math.PI / 180)) * distance,
          y: Math.sin(angle * (Math.PI / 180)) * distance,
          angle,
        };
      }
      if (emotions.length === 3) {
        // 为三个情绪使用特殊的三角形布局
        let angle: number;
        if (tierLevel === 1) {
          // 第一层级：一个在上，两个在下
          angle = index === 0 ? -90 : index === 1 ? -210 : -330;
        } else if (tierLevel === 2) {
          // 第二层级：一个在右，两个在左
          angle = index === 0 ? 0 : index === 1 ? -120 : 120;
        } else {
          // tierLevel === 3
          // 第三层级：均匀分布
          angle = index * 120;
        }

        // 根据文本长度动态调整距离
        const textFactor = Math.max(1, emotion.name.length / 8); // 文本长度因子
        const distance =
          tierLevel === 1
            ? 130 + textFactor * 10
            : tierLevel === 2
              ? 150 + textFactor * 10
              : 170 + textFactor * 10;

        return {
          x: Math.cos(angle * (Math.PI / 180)) * distance,
          y: Math.sin(angle * (Math.PI / 180)) * distance,
          angle,
        };
      }
      // 使用固定角度分布，确保元素均匀分布
      const angle = (index * 360) / emotions.length;

      // 根据情绪数量、文本长度和层级动态调整距离
      // 增加基础距离，防止重叠
      const textFactor = Math.max(1, emotion.name.length / 8); // 文本长度因子
      const countFactor = Math.max(1, emotions.length / 4); // 情绪数量因子

      // 基础距离随文本长度和情绪数量增加而增加
      const baseDistance =
        tierLevel === 1
          ? (160 + textFactor * 10) * Math.min(1.2, countFactor)
          : tierLevel === 2
            ? (180 + textFactor * 10) * Math.min(1.2, countFactor)
            : // 为 tierLevel === 3 层级增加更多距离，确保均匀分布
              (200 + textFactor * 10) * Math.min(1.2, countFactor);

      // 计算位置，不再添加随机性，确保布局一致且不重叠
      return {
        x: Math.cos(angle * (Math.PI / 180)) * baseDistance,
        y: Math.sin(angle * (Math.PI / 180)) * baseDistance,
        angle,
      };
    });
  };

  // 计算位置
  const positions = calculatePositions();

  // 渲染星系
  return (
    <div
      className="galaxy-container relative w-full h-full flex items-center justify-center"
      data-testid="motion-div"
    >
      <div
        className="galaxy-background absolute inset-0 rounded-full"
        style={{
          backgroundColor: galaxyConfig.background_color,
          boxShadow: galaxyConfig.shadowEnabled
            ? `0 0 ${galaxyConfig.shadowBlur}px ${galaxyConfig.shadowColor}`
            : 'none',
        }}
      />

      {positions.map((position, index) => {
        const emotion = emotions[index];

        // 根据层级和情绪数量调整星球大小
        let planetSize = 'w-14 h-14'; // 默认大小

        // 根据情绪数量和层级调整大小
        if (emotions.length > 6) {
          planetSize = 'w-10 h-10'; // 情绪较多时使用较小的星球
        } else if (emotions.length > 3) {
          planetSize = 'w-12 h-12'; // 中等数量情绪
        } else if (tierLevel === 3) {
          planetSize = 'w-16 h-16'; // 第三层级使用较大的星球
        }

        // 根据内容类型和层级调整样式
        const itemStyle: React.CSSProperties = {
          // 第三层级使用更明显的发光效果
          boxShadow:
            tierLevel === 3
              ? `0 0 15px ${isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.2)'}`
              : 'none',
        };

        // 渲染内容
        let content: React.ReactNode;

        switch (contentDisplayMode) {
          case 'text':
            content = <span>{emotion.name}</span>;
            break;
          case 'emoji':
            content = <span className="text-2xl">{emotion.emoji}</span>;
            break;
          case 'textEmoji':
            content = (
              <>
                <span className="text-xl">{emotion.emoji}</span>
                <span className="mt-1 text-sm">{emotion.name}</span>
              </>
            );
            break;
          case 'animatedEmoji': {
            const emojiItem = getEmojiItem(emotion.id);
            content = emojiItem ? (
              <AnimatedEmoji
                emojiItem={emojiItem}
                size={tierLevel === 3 ? '2xl' : 'xl'}
                autoPlay={true}
                loop={true}
              />
            ) : (
              <span className="text-2xl">{emotion.emoji}</span>
            );
            break;
          }
        }

        return (
          <motion.div
            key={emotion.id}
            className={`absolute ${planetSize} rounded-full flex flex-col items-center justify-center cursor-pointer overflow-hidden`}
            style={{
              backgroundColor: emotion.color || '#cccccc',
              ...itemStyle,
              left: `calc(50% + ${position.x}px)`,
              top: `calc(50% + ${position.y}px)`,
              transform: 'translate(-50%, -50%)',
            }}
            whileHover={{
              scale: galaxyConfig.hoverEffect === 'scale' ? 1.1 : 1,
              boxShadow: '0 0 15px rgba(255, 255, 255, 0.5)',
            }}
            onClick={() => {
              // 触觉反馈
              try {
                Haptics.impact({ style: ImpactStyle.Light });
              } catch (e) {
                // 忽略错误
              }
              onSelect(emotion);
            }}
            onHoverStart={() => setHoveredEmotion(emotion.id)}
            onHoverEnd={() => setHoveredEmotion(null)}
          >
            {content}
          </motion.div>
        );
      })}

      {/* 中心星球 */}
      <div
        className="w-20 h-20 rounded-full bg-opacity-30 flex items-center justify-center"
        style={{
          background: 'radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)',
        }}
      />
    </div>
  );
};
