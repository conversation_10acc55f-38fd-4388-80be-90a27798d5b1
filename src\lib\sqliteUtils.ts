import type { SQLiteConnection, SQLiteDBConnection } from '@capacitor-community/sqlite';

/**
 * SQLite 工具函数
 * 提供数据库操作的辅助功能
 */

/**
 * 执行数据库操作并自动保存到存储
 * @param connection 数据库连接
 * @param sqlite SQLite 连接实例
 * @param dbName 数据库名称
 * @param operation 要执行的操作函数
 * @returns 操作结果
 */
export async function executeWithSave<T>(
  connection: SQLiteDBConnection,
  sqlite: SQLiteConnection,
  dbName: string,
  operation: () => Promise<T>
): Promise<T> {
  try {
    // 执行操作
    const result = await operation();

    // 保存到存储以确保持久化
    try {
      await sqlite.saveToStore(dbName);
      console.log('[SQLiteUtils] Database saved to store after operation');
    } catch (saveError) {
      console.warn('[SQLiteUtils] Warning: Could not save database to store:', saveError);
      // 不抛出错误，因为操作本身成功了
    }

    return result;
  } catch (error) {
    console.error('[SQLiteUtils] Error during database operation:', error);
    throw error;
  }
}

/**
 * 执行 SQL 语句并自动保存
 * @param connection 数据库连接
 * @param sqlite SQLite 连接实例
 * @param dbName 数据库名称
 * @param sql SQL 语句
 * @returns 执行结果
 */
export async function executeSQL(
  connection: SQLiteDBConnection,
  sqlite: SQLiteConnection,
  dbName: string,
  sql: string
) {
  return executeWithSave(connection, sqlite, dbName, async () => {
    return await connection.execute(sql);
  });
}

/**
 * 执行查询（不需要保存，因为是只读操作）
 * @param connection 数据库连接
 * @param sql SQL 查询语句
 * @returns 查询结果
 */
export async function querySQL(connection: SQLiteDBConnection, sql: string) {
  return await connection.query(sql);
}

/**
 * 批量执行 SQL 语句并保存
 * @param connection 数据库连接
 * @param sqlite SQLite 连接实例
 * @param dbName 数据库名称
 * @param statements SQL 语句数组
 * @returns 执行结果数组
 */
export async function executeBatch(
  connection: SQLiteDBConnection,
  sqlite: SQLiteConnection,
  dbName: string,
  statements: string[]
) {
  return executeWithSave(connection, sqlite, dbName, async () => {
    const results = [];
    for (const sql of statements) {
      const result = await connection.execute(sql);
      results.push(result);
    }
    return results;
  });
}

/**
 * 检查数据库是否需要保存到存储
 * @param sqlite SQLite 连接实例
 * @param dbName 数据库名称
 * @returns 是否需要保存
 */
export async function needsSaveToStore(sqlite: SQLiteConnection, dbName: string): Promise<boolean> {
  try {
    // 检查数据库是否存在于存储中
    const dbExists = await sqlite.isDatabase(dbName);
    return !dbExists.result;
  } catch (error) {
    console.warn('[SQLiteUtils] Could not check database existence:', error);
    return true; // 如果无法检查，假设需要保存
  }
}

/**
 * 强制保存数据库到存储
 * @param sqlite SQLite 连接实例
 * @param dbName 数据库名称
 */
export async function forceSaveToStore(sqlite: SQLiteConnection, dbName: string): Promise<void> {
  try {
    await sqlite.saveToStore(dbName);
    console.log(`[SQLiteUtils] Database ${dbName} saved to store successfully`);
  } catch (error) {
    console.error(`[SQLiteUtils] Error saving database ${dbName} to store:`, error);
    throw error;
  }
}
