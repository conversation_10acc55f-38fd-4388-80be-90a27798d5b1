{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "lib": ["ES2022", "DOM"], "types": ["@cloudflare/workers-types", "node"], "skipLibCheck": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "functions/**/*.ts"], "exclude": ["node_modules"]}