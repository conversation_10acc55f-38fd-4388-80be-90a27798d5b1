import { useLanguage } from '@/contexts/LanguageContext';
import { LAYERS } from '@/lib/layers'; // Import layers definition
import { BarChart3, Clock, Home, Settings } from 'lucide-react';
import { useEffect, useRef } from 'react';
import { NavLink } from 'react-router-dom';

const MobileNavbar = () => {
  const { t, language, isLanguageReady } = useLanguage();
  const mountTime = useRef(Date.now());

  // 使用useEffect监听语言变化
  useEffect(() => {
    const timeSinceMount = Date.now() - mountTime.current;

    console.log(
      `[MobileNavbar] Language changed. Language: '${language}', isReady: ${isLanguageReady}, time since mount: ${timeSinceMount}ms`
    );

    // 测试翻译
    const homeTranslation = t('app.home');
    console.log(`[MobileNavbar] Test translation 'app.home': '${homeTranslation}'`);

    // 检查翻译是否返回键名本身
    if (homeTranslation === 'app.home') {
      console.log(`[MobileNavbar] Warning: Translation returned key name for 'app.home'`);
    }
  }, [language, isLanguageReady, t]);

  return (
    <nav
      className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-lg border-t border-border"
      style={{
        paddingBottom: 'env(safe-area-inset-bottom)',
        zIndex: LAYERS.NAVIGATION,
      }}
    >
      <div className="max-w-[550px] mx-auto flex justify-around items-center h-16 px-2">
        <NavLink to="/" className={({ isActive }) => `nav-link ${isActive ? 'active' : ''}`} end>
          <Home size={20} />
          <span>{isLanguageReady ? t('app.home') : 'Home'}</span>
        </NavLink>

        <NavLink to="/history" className={({ isActive }) => `nav-link ${isActive ? 'active' : ''}`}>
          <Clock size={20} />
          <span>{isLanguageReady ? t('app.history') : 'History'}</span>
        </NavLink>

        <NavLink
          to="/analytics"
          className={({ isActive }) => `nav-link ${isActive ? 'active' : ''}`}
        >
          <BarChart3 size={20} />
          <span>{isLanguageReady ? t('app.analytics') : 'Analytics'}</span>
        </NavLink>

        <NavLink
          to="/settings"
          className={({ isActive }) => `nav-link ${isActive ? 'active' : ''}`}
        >
          <Settings size={20} />
          <span>{isLanguageReady ? t('app.settings') : 'Settings'}</span>
        </NavLink>
      </div>
    </nav>
  );
};

export default MobileNavbar;
