/**
 * 命令行测试脚本
 * 用于测试 Neutral 和 Happy 之间的对比度
 *
 * 使用方法：
 * node scripts/test-contrast.js
 */

// 导入必要的模块
const path = require('node:path');
const { execSync } = require('node:child_process');

// 创建临时测试文件
const tempFile = path.join(__dirname, 'temp-test.js');
const fs = require('node:fs');

// 测试代码
const testCode = `
// 导入必要的模块
const { assignColorsToEmotions, calculateContrastRatio } = require('../dist/utils/colorUtils');

// 测试数据：Neutral 和 Happy
const testEmotions = [
  { id: 'emotion1', name: 'Neutral' },
  { id: 'emotion2', name: 'Happy' }
];

// 测试函数
function testNeutralHappyContrast() {
  console.log('测试 Neutral 和 Happy 之间的对比度');
  console.log('====================================');

  // 测试所有颜色模式和主题
  const modes = ['warm', 'cool', 'mixed', 'game'];
  const themes = [false, true]; // false = light, true = dark

  for (const mode of modes) {
    for (const isDark of themes) {
      console.log(\`\\n主题: \${isDark ? '深色' : '浅色'}, 颜色模式: \${mode}\`);
      
      // 分配颜色
      const emotionsWithColors = assignColorsToEmotions(testEmotions, isDark, 'primary', mode);
      
      // 获取颜色
      const neutralColor = emotionsWithColors.find(e => e.name === 'Neutral')?.color;
      const happyColor = emotionsWithColors.find(e => e.name === 'Happy')?.color;
      
      if (neutralColor && happyColor) {
        // 计算对比度
        const contrastRatio = calculateContrastRatio(neutralColor, happyColor);
        
        console.log(\`Neutral 颜色: \${neutralColor}\`);
        console.log(\`Happy 颜色: \${happyColor}\`);
        console.log(\`对比度: \${contrastRatio.to(3)}\`);
        
        // 检查对比度是否足够
        let contrastThreshold = 1.5;
        if (mode === 'game') {
          contrastThreshold = 2.0;
        }
        
        if (contrastRatio < contrastThreshold) {
          console.log(\`❌ 对比度不足 (低于 \${contrastThreshold})\`);
        } else {
          console.log(\`✅ 对比度足够 (高于 \${contrastThreshold})\`);
        }
      } else {
        console.log('❌ 无法获取颜色');
      }
    }
  }
}

// 运行测试
testNeutralHappyContrast();
`;

// 写入临时文件
fs.writeFileSync(tempFile, testCode);

console.log('正在构建项目...');
try {
  // 构建项目
  execSync('npm run build', { stdio: 'inherit' });

  console.log('\n正在运行测试...');
  // 运行测试
  execSync(`node ${tempFile}`, { stdio: 'inherit' });
} catch (error) {
  console.error('测试失败:', error);
} finally {
  // 清理临时文件
  fs.unlinkSync(tempFile);
  console.log('\n测试完成，临时文件已清理');
}
