/**
 * 情绪轮盘视图组件
 * 
 * 专门用于情绪数据集的轮盘式展示，支持多层级情绪选择
 * 这是一个特殊视图组件，用于Quiz系统中的情绪选择场景
 */

import React, { useState, useEffect, useRef } from 'react';
import { PersonalizationConfig, InteractionEvent } from '../../../types/schema/base';

// 情绪项接口
export interface EmotionItem {
  id: string;
  name: Record<string, string>;
  color?: string;
  emoji?: string;
  tier_level: number;
  parent_id?: string;
  children?: EmotionItem[];
  angle?: number;
  radius?: number;
}

// 轮盘配置接口
export interface WheelConfig {
  size: number;
  center_radius: number;
  tier_spacing: number;
  animation_duration: number;
  show_labels: boolean;
  show_emojis: boolean;
  interactive: boolean;
}

// 组件属性接口
export interface EmotionWheelViewProps {
  id: string;
  emotions: EmotionItem[];
  selectedEmotions: string[];
  onEmotionSelect: (emotionId: string, emotion: EmotionItem) => void;
  onTierChange?: (tierLevel: number) => void;
  config?: Partial<WheelConfig>;
  personalization: PersonalizationConfig;
  onInteraction: (event: InteractionEvent) => void;
  language?: string;
  style?: React.CSSProperties;
}

// 默认配置
const DEFAULT_CONFIG: WheelConfig = {
  size: 400,
  center_radius: 60,
  tier_spacing: 80,
  animation_duration: 300,
  show_labels: true,
  show_emojis: true,
  interactive: true
};

export const EmotionWheelView: React.FC<EmotionWheelViewProps> = ({
  id,
  emotions,
  selectedEmotions,
  onEmotionSelect,
  onTierChange,
  config = {},
  personalization,
  onInteraction,
  language = 'zh',
  style
}) => {
  const wheelConfig = { ...DEFAULT_CONFIG, ...config };
  const svgRef = useRef<SVGSVGElement>(null);
  const [currentTier, setCurrentTier] = useState(1);
  const [hoveredEmotion, setHoveredEmotion] = useState<string | null>(null);
  const [animating, setAnimating] = useState(false);

  // 计算情绪在轮盘中的位置
  const calculateEmotionPositions = (emotionList: EmotionItem[], tier: number): EmotionItem[] => {
    const tierEmotions = emotionList.filter(e => e.tier_level === tier);
    const angleStep = (2 * Math.PI) / tierEmotions.length;
    const radius = wheelConfig.center_radius + (tier - 1) * wheelConfig.tier_spacing;

    return tierEmotions.map((emotion, index) => ({
      ...emotion,
      angle: index * angleStep,
      radius: radius
    }));
  };

  // 获取当前层级的情绪
  const getCurrentTierEmotions = (): EmotionItem[] => {
    return calculateEmotionPositions(emotions, currentTier);
  };

  // 处理情绪点击
  const handleEmotionClick = (emotion: EmotionItem) => {
    if (!wheelConfig.interactive) return;

    // 触发交互事件
    onInteraction({
      type: 'select',
      target: emotion.id,
      data: {
        emotion: emotion,
        tier: currentTier,
        action: 'emotion_select'
      },
      timestamp: Date.now()
    });

    // 调用选择回调
    onEmotionSelect(emotion.id, emotion);

    // 如果有子情绪，切换到下一层级
    if (emotion.children && emotion.children.length > 0) {
      setAnimating(true);
      setTimeout(() => {
        setCurrentTier(currentTier + 1);
        onTierChange?.(currentTier + 1);
        setAnimating(false);
      }, wheelConfig.animation_duration);
    }
  };

  // 处理情绪悬停
  const handleEmotionHover = (emotionId: string | null) => {
    setHoveredEmotion(emotionId);
    
    if (emotionId) {
      onInteraction({
        type: 'hover',
        target: emotionId,
        data: { action: 'emotion_hover' },
        timestamp: Date.now()
      });
    }
  };

  // 返回上一层级
  const goBackTier = () => {
    if (currentTier > 1) {
      setAnimating(true);
      setTimeout(() => {
        setCurrentTier(currentTier - 1);
        onTierChange?.(currentTier - 1);
        setAnimating(false);
      }, wheelConfig.animation_duration);
    }
  };

  // 渲染情绪扇形
  const renderEmotionSector = (emotion: EmotionItem, index: number, total: number) => {
    const { angle = 0, radius = 0 } = emotion;
    const angleSpan = (2 * Math.PI) / total;
    const startAngle = angle - angleSpan / 2;
    const endAngle = angle + angleSpan / 2;
    
    const innerRadius = radius - wheelConfig.tier_spacing / 2;
    const outerRadius = radius + wheelConfig.tier_spacing / 2;

    // 计算路径
    const x1 = Math.cos(startAngle) * innerRadius;
    const y1 = Math.sin(startAngle) * innerRadius;
    const x2 = Math.cos(endAngle) * innerRadius;
    const y2 = Math.sin(endAngle) * innerRadius;
    const x3 = Math.cos(endAngle) * outerRadius;
    const y3 = Math.sin(endAngle) * outerRadius;
    const x4 = Math.cos(startAngle) * outerRadius;
    const y4 = Math.sin(startAngle) * outerRadius;

    const largeArcFlag = angleSpan > Math.PI ? 1 : 0;

    const pathData = [
      `M ${x1} ${y1}`,
      `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      `L ${x3} ${y3}`,
      `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
      'Z'
    ].join(' ');

    const isSelected = selectedEmotions.includes(emotion.id);
    const isHovered = hoveredEmotion === emotion.id;
    const emotionColor = emotion.color || '#4CAF50';

    // 文本位置
    const textX = Math.cos(angle) * radius;
    const textY = Math.sin(angle) * radius;

    return (
      <g key={emotion.id}>
        {/* 扇形 */}
        <path
          d={pathData}
          fill={emotionColor}
          fillOpacity={isSelected ? 0.8 : isHovered ? 0.6 : 0.4}
          stroke={isSelected ? '#2E7D32' : '#ffffff'}
          strokeWidth={isSelected ? 3 : 1}
          style={{
            cursor: wheelConfig.interactive ? 'pointer' : 'default',
            transition: `all ${wheelConfig.animation_duration}ms ease`,
            transform: isHovered ? 'scale(1.05)' : 'scale(1)',
            transformOrigin: 'center'
          }}
          onClick={() => handleEmotionClick(emotion)}
          onMouseEnter={() => handleEmotionHover(emotion.id)}
          onMouseLeave={() => handleEmotionHover(null)}
        />

        {/* 表情符号 */}
        {wheelConfig.show_emojis && emotion.emoji && (
          <text
            x={textX}
            y={textY - 10}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize="24"
            style={{ pointerEvents: 'none' }}
          >
            {emotion.emoji}
          </text>
        )}

        {/* 标签文本 */}
        {wheelConfig.show_labels && (
          <text
            x={textX}
            y={textY + (wheelConfig.show_emojis ? 15 : 0)}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize="12"
            fill="#333"
            fontWeight={isSelected ? 'bold' : 'normal'}
            style={{ pointerEvents: 'none' }}
          >
            {emotion.name[language] || emotion.name.zh || emotion.name.en}
          </text>
        )}
      </g>
    );
  };

  const currentEmotions = getCurrentTierEmotions();
  const centerX = wheelConfig.size / 2;
  const centerY = wheelConfig.size / 2;

  return (
    <div
      id={id}
      style={{
        width: wheelConfig.size,
        height: wheelConfig.size,
        position: 'relative',
        ...style
      }}
    >
      <svg
        ref={svgRef}
        width={wheelConfig.size}
        height={wheelConfig.size}
        viewBox={`0 0 ${wheelConfig.size} ${wheelConfig.size}`}
        style={{
          opacity: animating ? 0.7 : 1,
          transition: `opacity ${wheelConfig.animation_duration}ms ease`
        }}
      >
        {/* 背景圆 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={wheelConfig.size / 2 - 10}
          fill="none"
          stroke="#e0e0e0"
          strokeWidth="2"
        />

        {/* 中心圆 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={wheelConfig.center_radius}
          fill="#f5f5f5"
          stroke="#ddd"
          strokeWidth="2"
        />

        {/* 情绪扇形 */}
        <g transform={`translate(${centerX}, ${centerY})`}>
          {currentEmotions.map((emotion, index) =>
            renderEmotionSector(emotion, index, currentEmotions.length)
          )}
        </g>

        {/* 中心文本 */}
        <text
          x={centerX}
          y={centerY - 5}
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="14"
          fontWeight="bold"
          fill="#333"
        >
          第 {currentTier} 层
        </text>
        
        {currentTier > 1 && (
          <text
            x={centerX}
            y={centerY + 15}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize="12"
            fill="#666"
            style={{ cursor: 'pointer' }}
            onClick={goBackTier}
          >
            ← 返回
          </text>
        )}
      </svg>
    </div>
  );
};

export default EmotionWheelView;
