/**
 * 注册页面
 * 支持邮箱密码注册和社交注册
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useAuth';
import {
  AlertCircle,
  Check,
  Eye,
  EyeOff,
  Loader2,
  Lock,
  Mail,
  User,
  UserPlus,
  Wifi,
  WifiOff,
  X,
} from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

export const Register: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { register, isAuthenticated, isLoading, error, clearError, isOnline } = useAuth();

  // 表单状态
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    displayName: '',
    agreeToTerms: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 密码强度检查
  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
  });

  // 如果已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // 清除错误
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // 检查密码强度
  useEffect(() => {
    const password = formData.password;
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    });
  }, [formData.password]);

  // 处理表单输入
  const handleInputChange =
    (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: e.target.value,
      }));

      // 清除错误
      if (error) {
        clearError();
      }
    };

  // 处理同意条款选项
  const handleAgreeToTermsChange = (checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      agreeToTerms: checked,
    }));
  };

  // 表单验证
  const validateForm = (): boolean => {
    if (!formData.email.trim()) {
      toast.error(t('auth.email_required', { fallback: '请输入邮箱地址' }));
      return false;
    }

    if (!formData.email.includes('@')) {
      toast.error(t('auth.email_invalid', { fallback: '请输入有效的邮箱地址' }));
      return false;
    }

    if (!formData.password.trim()) {
      toast.error(t('auth.password_required', { fallback: '请输入密码' }));
      return false;
    }

    if (formData.password.length < 8) {
      toast.error(t('auth.password_too_short', { fallback: '密码至少需要8个字符' }));
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error(t('auth.password_mismatch', { fallback: '两次输入的密码不一致' }));
      return false;
    }

    if (!formData.agreeToTerms) {
      toast.error(t('auth.terms_required', { fallback: '请同意服务条款和隐私政策' }));
      return false;
    }

    return true;
  };

  // 处理注册提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!isOnline) {
      toast.error(t('auth.register_requires_internet', { fallback: '注册需要网络连接' }));
      return;
    }

    try {
      setIsSubmitting(true);
      clearError();

      const result = await register({
        email: formData.email.trim(),
        password: formData.password,
        username: formData.username.trim() || undefined,
        displayName: formData.displayName.trim() || undefined,
      });

      if (result.success) {
        toast.success(t('auth.register_success', { fallback: '注册成功，欢迎使用！' }));
        navigate('/', { replace: true });
      } else {
        toast.error(result.error || t('auth.register_failed', { fallback: '注册失败' }));
      }
    } catch (error) {
      console.error('Register error:', error);
      toast.error(t('auth.register_error', { fallback: '注册时发生错误' }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理社交注册
  const handleSocialRegister = (provider: 'google' | 'apple') => {
    if (!isOnline) {
      toast.error(t('auth.register_requires_internet', { fallback: '注册需要网络连接' }));
      return;
    }

    // TODO: 实现社交注册
    toast.info(
      t('auth.social_register_coming_soon', {
        fallback: `${provider === 'google' ? 'Google' : 'Apple'} 注册即将推出`,
      })
    );
  };

  // 密码强度指示器
  const PasswordStrengthIndicator = () => (
    <div className="space-y-2">
      <div className="text-xs text-muted-foreground">
        {t('auth.password_strength', { fallback: '密码强度要求：' })}
      </div>
      <div className="grid grid-cols-2 gap-1 text-xs">
        <div
          className={`flex items-center ${passwordStrength.length ? 'text-green-600' : 'text-muted-foreground'}`}
        >
          {passwordStrength.length ? (
            <Check className="h-3 w-3 mr-1" />
          ) : (
            <X className="h-3 w-3 mr-1" />
          )}
          {t('auth.password_length', { fallback: '至少8个字符' })}
        </div>
        <div
          className={`flex items-center ${passwordStrength.uppercase ? 'text-green-600' : 'text-muted-foreground'}`}
        >
          {passwordStrength.uppercase ? (
            <Check className="h-3 w-3 mr-1" />
          ) : (
            <X className="h-3 w-3 mr-1" />
          )}
          {t('auth.password_uppercase', { fallback: '包含大写字母' })}
        </div>
        <div
          className={`flex items-center ${passwordStrength.lowercase ? 'text-green-600' : 'text-muted-foreground'}`}
        >
          {passwordStrength.lowercase ? (
            <Check className="h-3 w-3 mr-1" />
          ) : (
            <X className="h-3 w-3 mr-1" />
          )}
          {t('auth.password_lowercase', { fallback: '包含小写字母' })}
        </div>
        <div
          className={`flex items-center ${passwordStrength.number ? 'text-green-600' : 'text-muted-foreground'}`}
        >
          {passwordStrength.number ? (
            <Check className="h-3 w-3 mr-1" />
          ) : (
            <X className="h-3 w-3 mr-1" />
          )}
          {t('auth.password_number', { fallback: '包含数字' })}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold">
              {t('auth.register', { fallback: '注册' })}
            </CardTitle>
            <div className="flex items-center">
              {isOnline ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
            </div>
          </div>
          <CardDescription>
            {t('auth.register_description', { fallback: '创建您的账户以开始使用' })}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* 网络状态提示 */}
          {!isOnline && (
            <Alert variant="destructive">
              <WifiOff className="h-4 w-4" />
              <AlertDescription>
                {t('auth.offline_warning', { fallback: '当前离线，注册需要网络连接' })}
              </AlertDescription>
            </Alert>
          )}

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 注册表单 */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 邮箱输入 */}
            <div className="space-y-2">
              <Label htmlFor="email">{t('auth.email', { fallback: '邮箱地址' })} *</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="email"
                  type="email"
                  placeholder={t('auth.email_placeholder', { fallback: '请输入邮箱地址' })}
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  className="pl-10"
                  disabled={isLoading || isSubmitting}
                  autoComplete="email"
                  required
                />
              </div>
            </div>

            {/* 用户名输入（可选） */}
            <div className="space-y-2">
              <Label htmlFor="username">
                {t('auth.username', { fallback: '用户名' })} (
                {t('auth.optional', { fallback: '可选' })})
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="username"
                  type="text"
                  placeholder={t('auth.username_placeholder', { fallback: '请输入用户名' })}
                  value={formData.username}
                  onChange={handleInputChange('username')}
                  className="pl-10"
                  disabled={isLoading || isSubmitting}
                  autoComplete="username"
                />
              </div>
            </div>

            {/* 显示名称输入（可选） */}
            <div className="space-y-2">
              <Label htmlFor="displayName">
                {t('auth.display_name', { fallback: '显示名称' })} (
                {t('auth.optional', { fallback: '可选' })})
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="displayName"
                  type="text"
                  placeholder={t('auth.display_name_placeholder', { fallback: '请输入显示名称' })}
                  value={formData.displayName}
                  onChange={handleInputChange('displayName')}
                  className="pl-10"
                  disabled={isLoading || isSubmitting}
                  autoComplete="name"
                />
              </div>
            </div>

            {/* 密码输入 */}
            <div className="space-y-2">
              <Label htmlFor="password">{t('auth.password', { fallback: '密码' })} *</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder={t('auth.password_placeholder', { fallback: '请输入密码' })}
                  value={formData.password}
                  onChange={handleInputChange('password')}
                  className="pl-10 pr-10"
                  disabled={isLoading || isSubmitting}
                  autoComplete="new-password"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading || isSubmitting}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              {formData.password && <PasswordStrengthIndicator />}
            </div>

            {/* 确认密码输入 */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">
                {t('auth.confirm_password', { fallback: '确认密码' })} *
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder={t('auth.confirm_password_placeholder', {
                    fallback: '请再次输入密码',
                  })}
                  value={formData.confirmPassword}
                  onChange={handleInputChange('confirmPassword')}
                  className="pl-10 pr-10"
                  disabled={isLoading || isSubmitting}
                  autoComplete="new-password"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isLoading || isSubmitting}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {formData.confirmPassword && formData.password !== formData.confirmPassword && (
                <div className="text-xs text-red-500 flex items-center">
                  <X className="h-3 w-3 mr-1" />
                  {t('auth.password_mismatch', { fallback: '两次输入的密码不一致' })}
                </div>
              )}
            </div>

            {/* 同意条款 */}
            <div className="flex items-start space-x-2">
              <Checkbox
                id="terms"
                checked={formData.agreeToTerms}
                onCheckedChange={handleAgreeToTermsChange}
                disabled={isLoading || isSubmitting}
                className="mt-1"
              />
              <Label htmlFor="terms" className="text-sm leading-5">
                {t('auth.agree_to_terms', { fallback: '我同意' })}
                <Link to="/terms" className="text-primary hover:underline mx-1">
                  {t('auth.terms_of_service', { fallback: '服务条款' })}
                </Link>
                {t('auth.and', { fallback: '和' })}
                <Link to="/privacy" className="text-primary hover:underline mx-1">
                  {t('auth.privacy_policy', { fallback: '隐私政策' })}
                </Link>
              </Label>
            </div>

            {/* 注册按钮 */}
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || isSubmitting || !isOnline}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('auth.registering', { fallback: '注册中...' })}
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  {t('auth.register', { fallback: '注册' })}
                </>
              )}
            </Button>
          </form>

          {/* 分隔线 */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                {t('auth.or', { fallback: '或' })}
              </span>
            </div>
          </div>

          {/* 社交注册 */}
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="outline"
              onClick={() => handleSocialRegister('google')}
              disabled={isLoading || isSubmitting || !isOnline}
            >
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Google
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSocialRegister('apple')}
              disabled={isLoading || isSubmitting || !isOnline}
            >
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
              </svg>
              Apple
            </Button>
          </div>
        </CardContent>

        <CardFooter className="flex flex-col space-y-2">
          <div className="text-sm text-center text-muted-foreground">
            {t('auth.already_have_account', { fallback: '已经有账户？' })}
            <Link to="/login" className="ml-1 text-primary hover:underline">
              {t('auth.login_now', { fallback: '立即登录' })}
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};
