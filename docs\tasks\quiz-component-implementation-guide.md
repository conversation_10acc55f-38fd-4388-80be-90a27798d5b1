# Quiz组件实现指南

## 🎯 实现架构

基于ViewFactory架构和个性化配置系统，实现高度可定制的Quiz组件。

### 核心实现原则

- **配置驱动**: 所有组件行为通过配置控制
- **性能优先**: 60fps流畅体验，优化渲染性能
- **可访问性**: 完整的无障碍功能支持
- **类型安全**: 完整的TypeScript类型定义

## 🏗️ 组件架构设计

### 1. 基础组件接口

```typescript
// 基础Quiz组件接口
interface BaseQuizComponent<TConfig = any, TData = any> {
  // 组件标识
  id: string;
  type: QuizComponentType;
  
  // 配置和数据
  config: TConfig;
  data: TData;
  
  // 个性化配置
  personalization: PersonalizationConfig;
  
  // 状态管理
  state: ComponentState;
  
  // 事件处理
  onInteraction: (event: InteractionEvent) => void;
  onStateChange: (newState: ComponentState) => void;
  
  // 生命周期
  onMount?: () => void;
  onUnmount?: () => void;
  onUpdate?: (prevConfig: TConfig) => void;
}

// Quiz组件类型枚举
enum QuizComponentType {
  EMOTION_WHEEL = 'emotion_wheel',
  EMOTION_CARDS = 'emotion_cards',
  EMOTION_BUBBLES = 'emotion_bubbles',
  EMOTION_GALAXY = 'emotion_galaxy',
  PROGRESS_INDICATOR = 'progress_indicator',
  NAVIGATION_CONTROLLER = 'navigation_controller',
  REALTIME_INSIGHT = 'realtime_insight',
  RESULT_PRESENTER = 'result_presenter'
}

// 组件状态
interface ComponentState {
  isLoading: boolean;
  isInteractive: boolean;
  selectedItems: string[];
  animationState: AnimationState;
  errorState?: ErrorState;
}

// 交互事件
interface InteractionEvent {
  type: InteractionType;
  target: string;
  data: any;
  timestamp: number;
  metadata?: InteractionMetadata;
}

enum InteractionType {
  SELECT = 'select',
  HOVER = 'hover',
  FOCUS = 'focus',
  DRAG = 'drag',
  SWIPE = 'swipe',
  LONG_PRESS = 'long_press'
}
```

### 2. 情绪轮盘组件实现

```typescript
// 情绪轮盘配置接口
interface EmotionWheelConfig {
  // 基础配置
  container_size: number;
  wheel_radius: number;
  inner_radius: number;
  sector_gap: number;
  
  // 视觉配置
  emotion_display_mode: 'hierarchical' | 'clustered' | 'radial';
  tier_transition_animation: 'rotate' | 'zoom' | 'fade';
  
  // 交互配置
  selection_mode: 'single' | 'multiple';
  hover_preview: boolean;
  confidence_rating: boolean;
  
  // 可访问性配置
  keyboard_navigation: boolean;
  screen_reader_support: boolean;
  high_contrast_mode: boolean;
  
  // 性能配置
  render_engine: 'SVG' | 'Canvas' | 'WebGL';
  animation_quality: 'low' | 'medium' | 'high';
}

// 情绪轮盘数据接口
interface EmotionWheelData {
  emotions: EmotionOption[];
  current_tier: number;
  total_tiers: number;
  selected_emotions: string[];
  tier_hierarchy: TierHierarchy;
}

interface EmotionOption {
  id: string;
  name: string;
  category: string;
  tier_level: number;
  intensity: number;
  color: string;
  position: { angle: number; radius: number };
  children?: EmotionOption[];
}

// React组件实现
const EmotionWheel: React.FC<EmotionWheelProps> = ({
  config,
  data,
  personalization,
  onInteraction,
  onStateChange
}) => {
  const [state, setState] = useState<ComponentState>({
    isLoading: false,
    isInteractive: true,
    selectedItems: [],
    animationState: 'idle'
  });
  
  // 应用个性化配置
  const appliedConfig = useMemo(() => 
    applyPersonalizationToWheelConfig(config, personalization),
    [config, personalization]
  );
  
  // 处理情绪选择
  const handleEmotionSelect = useCallback((emotionId: string, metadata?: any) => {
    const event: InteractionEvent = {
      type: InteractionType.SELECT,
      target: emotionId,
      data: { emotion_id: emotionId },
      timestamp: Date.now(),
      metadata
    };
    
    onInteraction(event);
    
    // 更新本地状态
    setState(prev => ({
      ...prev,
      selectedItems: config.selection_mode === 'single' 
        ? [emotionId] 
        : [...prev.selectedItems, emotionId]
    }));
  }, [config.selection_mode, onInteraction]);
  
  // 渲染引擎选择
  const renderWheel = () => {
    switch (appliedConfig.render_engine) {
      case 'SVG':
        return <SVGEmotionWheel {...wheelProps} />;
      case 'Canvas':
        return <CanvasEmotionWheel {...wheelProps} />;
      case 'WebGL':
        return <WebGLEmotionWheel {...wheelProps} />;
      default:
        return <SVGEmotionWheel {...wheelProps} />;
    }
  };
  
  return (
    <div 
      className="emotion-wheel-container"
      style={{ 
        width: appliedConfig.container_size,
        height: appliedConfig.container_size 
      }}
      role="radiogroup"
      aria-label="情绪选择轮盘"
    >
      {renderWheel()}
      
      {/* 可访问性支持 */}
      {appliedConfig.keyboard_navigation && (
        <KeyboardNavigationOverlay
          emotions={data.emotions}
          onSelect={handleEmotionSelect}
        />
      )}
      
      {/* 置信度评级 */}
      {appliedConfig.confidence_rating && state.selectedItems.length > 0 && (
        <ConfidenceRatingModal
          selectedEmotion={state.selectedItems[0]}
          onConfidenceSet={(confidence) => {
            // 处理置信度设置
          }}
        />
      )}
    </div>
  );
};
```

### 3. SVG渲染引擎实现

```typescript
// SVG情绪轮盘实现
const SVGEmotionWheel: React.FC<SVGEmotionWheelProps> = ({
  config,
  data,
  onEmotionSelect,
  selectedEmotions
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  
  // 计算扇形路径
  const calculateSectorPath = useCallback((
    emotion: EmotionOption,
    index: number,
    total: number
  ): string => {
    const angleStep = (2 * Math.PI) / total;
    const startAngle = index * angleStep;
    const endAngle = (index + 1) * angleStep - (config.sector_gap * Math.PI / 180);
    
    const outerRadius = config.wheel_radius;
    const innerRadius = config.inner_radius;
    
    const x1 = Math.cos(startAngle) * innerRadius;
    const y1 = Math.sin(startAngle) * innerRadius;
    const x2 = Math.cos(endAngle) * innerRadius;
    const y2 = Math.sin(endAngle) * innerRadius;
    const x3 = Math.cos(endAngle) * outerRadius;
    const y3 = Math.sin(endAngle) * outerRadius;
    const x4 = Math.cos(startAngle) * outerRadius;
    const y4 = Math.sin(startAngle) * outerRadius;
    
    const largeArcFlag = endAngle - startAngle > Math.PI ? 1 : 0;
    
    return `
      M ${x1} ${y1}
      A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}
      L ${x3} ${y3}
      A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}
      Z
    `;
  }, [config]);
  
  // 处理扇形点击
  const handleSectorClick = useCallback((emotion: EmotionOption, event: React.MouseEvent) => {
    // 触觉反馈
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
    
    onEmotionSelect(emotion.id, {
      click_position: { x: event.clientX, y: event.clientY },
      interaction_method: 'click'
    });
  }, [onEmotionSelect]);
  
  return (
    <svg
      ref={svgRef}
      width={config.container_size}
      height={config.container_size}
      viewBox={`-${config.wheel_radius} -${config.wheel_radius} ${config.wheel_radius * 2} ${config.wheel_radius * 2}`}
      className="emotion-wheel-svg"
    >
      {/* 背景圆环 */}
      <circle
        cx={0}
        cy={0}
        r={config.wheel_radius}
        fill="none"
        stroke="var(--border-color)"
        strokeWidth={1}
        opacity={0.1}
      />
      
      {/* 情绪扇形 */}
      {data.emotions.map((emotion, index) => {
        const isSelected = selectedEmotions.includes(emotion.id);
        const sectorPath = calculateSectorPath(emotion, index, data.emotions.length);
        
        return (
          <g key={emotion.id}>
            {/* 扇形路径 */}
            <path
              d={sectorPath}
              fill={emotion.color}
              stroke="white"
              strokeWidth={2}
              opacity={isSelected ? 1 : 0.8}
              className={`emotion-sector ${isSelected ? 'selected' : ''}`}
              onClick={(e) => handleSectorClick(emotion, e)}
              onMouseEnter={() => {
                // 悬停效果
              }}
              style={{
                cursor: 'pointer',
                transition: 'all 0.2s ease-out',
                transform: isSelected ? 'scale(1.05)' : 'scale(1)',
                transformOrigin: 'center'
              }}
              role="radio"
              aria-checked={isSelected}
              aria-label={emotion.name}
              tabIndex={0}
            />
            
            {/* 情绪标签 */}
            <text
              x={Math.cos((index + 0.5) * (2 * Math.PI) / data.emotions.length) * (config.wheel_radius * 0.7)}
              y={Math.sin((index + 0.5) * (2 * Math.PI) / data.emotions.length) * (config.wheel_radius * 0.7)}
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize={14}
              fill="var(--text-primary)"
              pointerEvents="none"
              className="emotion-label"
            >
              {emotion.name}
            </text>
          </g>
        );
      })}
      
      {/* 中心元素 */}
      <circle
        cx={0}
        cy={0}
        r={config.inner_radius}
        fill="var(--surface-color)"
        stroke="var(--border-color)"
        strokeWidth={2}
      />
    </svg>
  );
};
```

### 4. 性能优化策略

```typescript
// 性能优化Hook
const useQuizComponentOptimization = (config: any) => {
  // 防抖处理
  const debouncedConfig = useDebounce(config, 100);
  
  // 虚拟化支持
  const virtualizedItems = useMemo(() => {
    if (config.items?.length > 100) {
      return useVirtualization(config.items, {
        itemHeight: 60,
        containerHeight: 400,
        overscan: 5
      });
    }
    return config.items;
  }, [config.items]);
  
  // 动画优化
  const animationFrame = useRef<number>();
  const scheduleAnimation = useCallback((callback: () => void) => {
    if (animationFrame.current) {
      cancelAnimationFrame(animationFrame.current);
    }
    animationFrame.current = requestAnimationFrame(callback);
  }, []);
  
  // 内存管理
  useEffect(() => {
    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
    };
  }, []);
  
  return {
    debouncedConfig,
    virtualizedItems,
    scheduleAnimation
  };
};

// 渲染优化组件
const OptimizedQuizComponent = React.memo<QuizComponentProps>(
  ({ config, data, ...props }) => {
    const { debouncedConfig, virtualizedItems } = useQuizComponentOptimization(config);
    
    return (
      <QuizComponent
        config={debouncedConfig}
        data={{ ...data, items: virtualizedItems }}
        {...props}
      />
    );
  },
  (prevProps, nextProps) => {
    // 自定义比较函数，避免不必要的重渲染
    return (
      prevProps.config === nextProps.config &&
      prevProps.data === nextProps.data &&
      prevProps.personalization === nextProps.personalization
    );
  }
);
```

### 5. 可访问性实现

```typescript
// 可访问性Hook
const useQuizAccessibility = (config: AccessibilityConfig) => {
  // 键盘导航
  const handleKeyNavigation = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowUp':
      case 'ArrowDown':
      case 'ArrowLeft':
      case 'ArrowRight':
        // 处理方向键导航
        break;
      case 'Enter':
      case ' ':
        // 处理选择
        break;
      case 'Escape':
        // 处理取消
        break;
    }
  }, []);
  
  // 屏幕阅读器支持
  const announceToScreenReader = useCallback((message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);
  
  // 高对比度模式
  const applyHighContrastMode = useCallback((element: HTMLElement) => {
    if (config.high_contrast) {
      element.classList.add('high-contrast-mode');
    }
  }, [config.high_contrast]);
  
  return {
    handleKeyNavigation,
    announceToScreenReader,
    applyHighContrastMode
  };
};
```

### 6. 测试策略

```typescript
// 组件测试工具
describe('EmotionWheel Component', () => {
  const mockConfig: EmotionWheelConfig = {
    container_size: 400,
    wheel_radius: 180,
    inner_radius: 60,
    sector_gap: 2,
    emotion_display_mode: 'hierarchical',
    tier_transition_animation: 'rotate',
    selection_mode: 'single',
    hover_preview: true,
    confidence_rating: false,
    keyboard_navigation: true,
    screen_reader_support: true,
    high_contrast_mode: false,
    render_engine: 'SVG',
    animation_quality: 'medium'
  };
  
  const mockData: EmotionWheelData = {
    emotions: [
      { id: 'joy', name: '喜悦', category: 'positive', tier_level: 1, intensity: 0.8, color: '#FFD60A', position: { angle: 0, radius: 180 } },
      { id: 'sadness', name: '悲伤', category: 'negative', tier_level: 1, intensity: 0.6, color: '#007AFF', position: { angle: 90, radius: 180 } }
    ],
    current_tier: 1,
    total_tiers: 3,
    selected_emotions: [],
    tier_hierarchy: {}
  };
  
  it('renders correctly with given config and data', () => {
    render(
      <EmotionWheel
        config={mockConfig}
        data={mockData}
        personalization={{}}
        onInteraction={jest.fn()}
        onStateChange={jest.fn()}
      />
    );
    
    expect(screen.getByRole('radiogroup')).toBeInTheDocument();
    expect(screen.getByLabelText('情绪选择轮盘')).toBeInTheDocument();
  });
  
  it('handles emotion selection correctly', async () => {
    const onInteraction = jest.fn();
    
    render(
      <EmotionWheel
        config={mockConfig}
        data={mockData}
        personalization={{}}
        onInteraction={onInteraction}
        onStateChange={jest.fn()}
      />
    );
    
    const joyEmotion = screen.getByLabelText('喜悦');
    fireEvent.click(joyEmotion);
    
    expect(onInteraction).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'select',
        target: 'joy',
        data: { emotion_id: 'joy' }
      })
    );
  });
  
  it('supports keyboard navigation', () => {
    render(
      <EmotionWheel
        config={{ ...mockConfig, keyboard_navigation: true }}
        data={mockData}
        personalization={{}}
        onInteraction={jest.fn()}
        onStateChange={jest.fn()}
      />
    );
    
    const wheelContainer = screen.getByRole('radiogroup');
    fireEvent.keyDown(wheelContainer, { key: 'ArrowRight' });
    
    // 验证焦点移动
    expect(document.activeElement).toBe(screen.getByLabelText('喜悦'));
  });
});
```

这个实现指南提供了完整的Quiz组件开发框架，确保组件具有高性能、可访问性和可维护性。
