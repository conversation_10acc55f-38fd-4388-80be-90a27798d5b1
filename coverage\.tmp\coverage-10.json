{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/architecture/service-alignment.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 54216, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 54216, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 581, "endOffset": 19806, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 624, "endOffset": 685, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 736, "endOffset": 3244, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 806, "endOffset": 1614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1097, "endOffset": 1245, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1346, "endOffset": 1491, "count": 4}, {"startOffset": 1385, "endOffset": 1420, "count": 3}, {"startOffset": 1421, "endOffset": 1453, "count": 2}, {"startOffset": 1454, "endOffset": 1491, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1700, "endOffset": 2402, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1990, "endOffset": 2134, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2226, "endOffset": 2317, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2487, "endOffset": 3236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2773, "endOffset": 2924, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3012, "endOffset": 3156, "count": 4}, {"startOffset": 3112, "endOffset": 3141, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3298, "endOffset": 6295, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3356, "endOffset": 4527, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4059, "endOffset": 4175, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4580, "endOffset": 5511, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5308, "endOffset": 5499, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5563, "endOffset": 6287, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6141, "endOffset": 6275, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6348, "endOffset": 10161, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6403, "endOffset": 7526, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7417, "endOffset": 7514, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7577, "endOffset": 8784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8834, "endOffset": 10153, "count": 1}], "isBlockCoverage": true}, {"functionName": "detectCircularDependency", "ranges": [{"startOffset": 9224, "endOffset": 9978, "count": 1}], "isBlockCoverage": true}, {"functionName": "hasCycle", "ranges": [{"startOffset": 9360, "endOffset": 9886, "count": 4}, {"startOffset": 9426, "endOffset": 9438, "count": 1}, {"startOffset": 9438, "endOffset": 9485, "count": 3}, {"startOffset": 9485, "endOffset": 9498, "count": 0}, {"startOffset": 9498, "endOffset": 9645, "count": 3}, {"startOffset": 9645, "endOffset": 9650, "count": 0}, {"startOffset": 9703, "endOffset": 9782, "count": 3}, {"startOffset": 9782, "endOffset": 9885, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9934, "endOffset": 9962, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10214, "endOffset": 13212, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10269, "endOffset": 11575, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10794, "endOffset": 11039, "count": 4}], "isBlockCoverage": true}, {"functionName": "mockPerformanceTest", "ranges": [{"startOffset": 11104, "endOffset": 11371, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11233, "endOffset": 11267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11627, "endOffset": 12358, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12107, "endOffset": 12346, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12410, "endOffset": 13204, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12944, "endOffset": 13192, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13265, "endOffset": 16556, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13330, "endOffset": 14156, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13603, "endOffset": 13871, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13967, "endOffset": 14066, "count": 4}, {"startOffset": 14006, "endOffset": 14031, "count": 2}, {"startOffset": 14032, "endOffset": 14066, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14211, "endOffset": 15200, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14851, "endOffset": 15188, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15257, "endOffset": 16548, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16179, "endOffset": 16536, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16333, "endOffset": 16520, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16609, "endOffset": 19802, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16664, "endOffset": 17571, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17190, "endOffset": 17364, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17621, "endOffset": 18589, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18157, "endOffset": 18291, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18639, "endOffset": 19794, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}