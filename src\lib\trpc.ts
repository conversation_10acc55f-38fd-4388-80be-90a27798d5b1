import { createTRPCProxyClient, httpBatchLink } from '@trpc/client';
import type { AppRouter } from '../../server/lib/router';

// 导入统一类型定义
import type {
  AuthResponse,
  LoginInput,
  PurchaseEmojiSetInput,
  PurchaseResult,
  PurchaseSkinInput,
  PurchaseVipInput,
  RegisterInput,
  UserConfig,
  VipStatus,
} from '../types/schema/index';

// Get the server URL from environment variables or use a default for development
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // In the browser, use relative path or environment variable
    return import.meta.env.VITE_API_URL || '';
  }
  // In SSR, use absolute URL
  return import.meta.env.VITE_API_URL || 'http://localhost:8788';
};

// Create the tRPC client
export const trpc = createTRPCProxyClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${getBaseUrl()}/trpc`,
      // Optional: configure request headers
      headers() {
        return {
          // You can add authorization headers here if needed
        };
      },
    }),
  ],
});
