/**
 * Quiz系统设置页面
 * 展示6层个性化配置架构和数据与展现分离设计
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Switch } from '../components/ui/switch';
import { Slider } from '../components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Label } from '../components/ui/label';
import {
  Settings,
  Palette,
  Eye,
  Accessibility,
  Cpu,
  Layers,
  Sparkles,
  Brain,
  Target,
  Play,
  Heart,
  Activity,
  GripVertical,
  Plus,
  Trash2,
  RotateCcw,
  ChevronDown,
  ChevronUp,
  Check,
  X,
  ShoppingBag,
  ExternalLink,
  Crown // Import Crown icon for VIP features
} from 'lucide-react';
import {
  useEmotionWheelQuiz,
  useTCMAssessmentQuiz
} from '../hooks/useQuiz';
import { useQuizConfig } from '../hooks/useQuizConfig';
import { useAuth } from '../hooks/useAuth'; // Import useAuth hook

interface PersonalizationConfig {
  layer0_dataset_presentation: {
    preferred_pack_categories: string[];
    default_difficulty_preference: string;
    session_length_preference: string;
    auto_select_recommended: boolean;
    // 新增：进度恢复配置
    restore_progress: boolean;
    // 新增：问题展示字段配置
    question_display_fields: {
      show_question_text: boolean;
      show_question_description: boolean;
      show_question_order: boolean;
      show_progress_indicator: boolean;
      show_answer_options: boolean;
      show_option_descriptions: boolean;
      show_option_icons: boolean;
    };
    // 新增：交互行为配置
    interaction_behavior: {
      auto_advance_after_selection: boolean;
      auto_advance_delay_ms: number;
      allow_answer_change: boolean;
      show_confirmation_dialog: boolean;
    };
    // 新增：问题个性化管理
    question_management: {
      selected_quiz_pack_id: string;
      available_quiz_packs: Array<{
        id: string;
        name: string;
        description: string;
        category: string;
        total_questions: number;
      }>;
      question_customization: {
        [quiz_pack_id: string]: {
          enabled_questions: Array<{
            id: string;
            title: string;
            description: string;
            order: number;
            enabled: boolean;
            required: boolean;
          }>;
          custom_order: string[]; // 问题ID的自定义顺序
        };
      };
    };
  };
  layer1_user_choice: {
    preferred_view_type: string;
    active_skin_id: string;
    dark_mode: boolean;
    color_mode: string;
    user_level: string;
  };
  layer2_rendering_strategy: {
    render_engine_preferences: Record<string, string>;
    content_display_mode_preferences: Record<string, string[]>; // 改为数组支持多种内容类型
    layout_preferences: Record<string, string>;
    performance_mode: string;
    // 新增：支持的内容类型定义
    supported_content_types: {
      text: boolean;           // 文本内容
      emoji: boolean;          // 表情符号
      image: boolean;          // 图片内容
      icon: boolean;           // 图标
      audio: boolean;          // 音频
      video: boolean;          // 视频
      animation: boolean;      // 动画效果
      rich_text: boolean;      // 富文本（HTML/Markdown）
    };
  };
  layer3_skin_base: {
    // 新增：可用皮肤列表和选择
    available_skins: Array<{
      id: string;
      name: string;
      description: string;
      preview_image?: string;
      category: string;
    }>;
    selected_skin_id: string;
    // 皮肤基础配置（只有选择了皮肤后才显示）
    colors: Record<string, string>;
    fonts: {
      primary_font: string;
      size_scale: number;
    };
    animations: {
      enable_animations: boolean;
      animation_speed: string;
      reduce_motion: boolean;
    };
  };
  layer4_view_detail: {
    // 动态视图配置：根据preferred_view_type变化
    wheel_config?: {
      container_size: number;
      wheel_radius: number;
      emotion_display_mode: string;
      tier_spacing: number;
      center_radius: number;
      show_labels: boolean;
      show_emojis: boolean;
    };
    card_config?: {
      grid_columns: number;
      card_size: string;
      card_spacing: number;
      show_descriptions: boolean;
      hover_effects: boolean;
    };
    bubble_config?: {
      container_width: number;
      container_height: number;
      bubble_size_range: [number, number];
      physics_enabled: boolean;
      collision_detection: boolean;
    };
    list_config?: {
      item_height: number;
      show_icons: boolean;
      show_descriptions: boolean;
      grouping_enabled: boolean;
    };
    emotion_presentation: {
      emotion_grouping_style: string;
      tier_transition_animation: string;
    };
  };
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    keyboard_navigation: boolean;
    voice_guidance: boolean;
  };
}

const QuizSettings: React.FC = () => {
  const navigate = useNavigate();
  const [config, setConfig] = useState<PersonalizationConfig | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const { isVip } = useAuth(); // Get VIP status

  // 使用新的配置系统 Hook
  const {
    preferences: quizPreferences,
    presentationConfig,
    isLoading: configLoading,
    error: configError,
    updatePreferences,
    updatePresentationConfig,
    preferredViewType,
    colorMode,
    personalizationLevel,
    performanceMode
  } = useQuizConfig();

  // 本地个性化级别状态（用于实时更新UI）
  const [localPersonalizationLevel, setLocalPersonalizationLevel] = useState(personalizationLevel);

  // 同步personalizationLevel变化到本地状态
  useEffect(() => {
    setLocalPersonalizationLevel(personalizationLevel);
  }, [personalizationLevel]);

  // 使用新的Quiz hooks
  const emotionWheelQuiz = useEmotionWheelQuiz();
  const tcmAssessmentQuiz = useTCMAssessmentQuiz();

  useEffect(() => {
    // 模拟加载用户配置
    setTimeout(() => {
      setConfig({
        layer0_dataset_presentation: {
          preferred_pack_categories: ['daily', 'therapy'],
          default_difficulty_preference: 'regular',
          session_length_preference: 'medium',
          auto_select_recommended: false,
          // 新增配置
          restore_progress: true,
          question_display_fields: {
            show_question_text: true,
            show_question_description: true,
            show_question_order: true,
            show_progress_indicator: true,
            show_answer_options: true,
            show_option_descriptions: false,
            show_option_icons: true,
          },
          interaction_behavior: {
            auto_advance_after_selection: false,
            auto_advance_delay_ms: 1500,
            allow_answer_change: true,
            show_confirmation_dialog: false,
          },
          // 新增：问题个性化管理
          question_management: {
            selected_quiz_pack_id: 'emotion_wheel_basic',
            available_quiz_packs: [
              {
                id: 'emotion_wheel_basic',
                name: '情绪轮盘基础版',
                description: '基础情绪识别和记录',
                category: 'emotion',
                total_questions: 8
              },
              {
                id: 'tcm_assessment_full',
                name: '中医体质评估完整版',
                description: '全面的中医体质分析',
                category: 'tcm',
                total_questions: 15
              },
              {
                id: 'mood_tracking_daily',
                name: '日常心情追踪',
                description: '简化的日常情绪记录',
                category: 'daily',
                total_questions: 5
              },
              {
                id: 'stress_assessment',
                name: '压力评估量表',
                description: '工作和生活压力评估',
                category: 'assessment',
                total_questions: 12
              }
            ],
            question_customization: {
              'emotion_wheel_basic': {
                enabled_questions: [
                  { id: 'q1', title: '当前主要情绪', description: '选择您现在最主要的情绪状态', order: 1, enabled: true, required: true },
                  { id: 'q2', title: '情绪强度', description: '评估当前情绪的强烈程度', order: 2, enabled: true, required: true },
                  { id: 'q3', title: '触发事件', description: '导致这种情绪的主要事件', order: 3, enabled: true, required: false },
                  { id: 'q4', title: '身体感受', description: '情绪在身体上的表现', order: 4, enabled: true, required: false },
                  { id: 'q5', title: '持续时间', description: '这种情绪状态持续了多长时间', order: 5, enabled: true, required: false },
                  { id: 'q6', title: '应对方式', description: '您通常如何处理这种情绪', order: 6, enabled: false, required: false },
                  { id: 'q7', title: '环境因素', description: '周围环境对情绪的影响', order: 7, enabled: false, required: false },
                  { id: 'q8', title: '期望改变', description: '希望情绪状态如何改变', order: 8, enabled: true, required: false }
                ],
                custom_order: ['q1', 'q2', 'q3', 'q4', 'q5', 'q8']
              },
              'tcm_assessment_full': {
                enabled_questions: [
                  { id: 'tcm1', title: '体质类型初判', description: '基础体质类型评估', order: 1, enabled: true, required: true },
                  { id: 'tcm2', title: '气血状况', description: '气血运行状态评估', order: 2, enabled: true, required: true },
                  { id: 'tcm3', title: '脏腑功能', description: '五脏六腑功能评估', order: 3, enabled: true, required: true },
                  { id: 'tcm4', title: '经络状况', description: '经络通畅程度评估', order: 4, enabled: true, required: false },
                  { id: 'tcm5', title: '饮食偏好', description: '饮食习惯和偏好', order: 5, enabled: true, required: false }
                ],
                custom_order: ['tcm1', 'tcm2', 'tcm3', 'tcm4', 'tcm5']
              }
            }
          },
          layer1_user_choice: {
            preferred_view_type: 'wheel',
            active_skin_id: 'default_skin',
            dark_mode: false,
            color_mode: 'warm',
            user_level: 'regular',
          },
          layer2_rendering_strategy: {
            render_engine_preferences: {
              wheel: 'D3',
              card: 'SVG',
              bubble: 'Canvas',
              galaxy: 'WebGL',
            },
            content_display_mode_preferences: {
              wheel: ['text', 'emoji', 'icon'],           // 轮盘支持文本、表情、图标
              card: ['text', 'image', 'icon'],            // 卡片支持文本、图片、图标
              bubble: ['emoji', 'animation'],             // 气泡支持表情、动画
              galaxy: ['text', 'emoji', 'rich_text'],     // 星系支持文本、表情、富文本
            },
            layout_preferences: {
              wheel: 'circular_balanced',
              card: 'grid_responsive',
              bubble: 'organic_flow',
              galaxy: 'spatial_3d',
            },
            performance_mode: 'balanced',
            // 新增：支持的内容类型配置
            supported_content_types: {
              text: true,           // 启用文本内容
              emoji: true,          // 启用表情符号
              image: true,          // 启用图片内容
              icon: true,           // 启用图标
              audio: false,         // 暂不启用音频
              video: false,         // 暂不启用视频
              animation: true,      // 启用动画效果
              rich_text: true,      // 启用富文本
            },
          },
          layer3_skin_base: {
            available_skins: [
              {
                id: 'default',
                name: '默认皮肤',
                description: '简洁现代的默认界面风格',
                category: 'basic'
              },
              {
                id: 'tcm_classic',
                name: '中医经典',
                description: '传统中医风格，温润典雅',
                category: 'tcm'
              },
              {
                id: 'modern_minimal',
                name: '现代简约',
                description: '极简设计，专注内容',
                category: 'modern'
              },
              {
                id: 'nature_zen',
                name: '自然禅意',
                description: '自然色彩，宁静致远',
                category: 'nature'
              }
            ],
            selected_skin_id: 'default',
            colors: {
              primary: '#4F46E5',
              secondary: '#7C3AED',
              accent: '#F59E0B',
            },
            fonts: {
              primary_font: 'Inter',
              size_scale: 1.0,
            },
            animations: {
              enable_animations: true,
              animation_speed: 'normal',
              reduce_motion: false,
            },
          },
          layer4_view_detail: {
            wheel_config: {
              container_size: 400,
              wheel_radius: 180,
              emotion_display_mode: 'hierarchical',
              tier_spacing: 60,
              center_radius: 40,
              show_labels: true,
              show_emojis: true,
            },
            card_config: {
              grid_columns: 3,
              card_size: 'medium',
              card_spacing: 16,
              show_descriptions: true,
              hover_effects: true,
            },
            bubble_config: {
              container_width: 500,
              container_height: 400,
              bubble_size_range: [30, 80],
              physics_enabled: true,
              collision_detection: true,
            },
            list_config: {
              item_height: 60,
              show_icons: true,
              show_descriptions: true,
              grouping_enabled: false,
            },
            emotion_presentation: {
              emotion_grouping_style: 'by_category',
              tier_transition_animation: 'fade',
            },
          },
          layer5_accessibility: {
            high_contrast: false,
            large_text: false,
            reduce_motion: false,
            keyboard_navigation: true,
            voice_guidance: false,
          },
        });
      setLocalPersonalizationLevel(45);
      setIsLoading(false);
    }, 1000);
  }, []);

  const updateConfig = (layer: string, key: string, value: any) => {
    if (!config) return;

    setConfig(prev => ({
      ...prev!,
      [layer]: {
        ...prev![layer as keyof PersonalizationConfig],
        [key]: value,
      },
    }));

    // 重新计算个性化级别
    setLocalPersonalizationLevel(prev => Math.min(prev + 5, 100));
  };

  // 问题管理相关函数
  const toggleQuestionEnabled = (questionId: string) => {
    if (!config) return;

    const selectedPackId = config.layer0_dataset_presentation.question_management.selected_quiz_pack_id;
    const currentCustomization = config.layer0_dataset_presentation.question_management.question_customization[selectedPackId];

    if (!currentCustomization) return;

    const updatedQuestions = currentCustomization.enabled_questions.map(q =>
      q.id === questionId ? { ...q, enabled: !q.enabled } : q
    );

    // 更新自定义顺序，移除被禁用的问题
    const updatedOrder = currentCustomization.custom_order.filter(id => {
      const question = updatedQuestions.find(q => q.id === id);
      return question?.enabled;
    });

    setConfig(prev => ({
      ...prev!,
      layer0_dataset_presentation: {
        ...prev!.layer0_dataset_presentation,
        question_management: {
          ...prev!.layer0_dataset_presentation.question_management,
          question_customization: {
            ...prev!.layer0_dataset_presentation.question_management.question_customization,
            [selectedPackId]: {
              ...currentCustomization,
              enabled_questions: updatedQuestions,
              custom_order: updatedOrder
            }
          }
        }
      }
    }));
  };

  const moveQuestion = (questionId: string, direction: 'up' | 'down') => {
    if (!config) return;

    const selectedPackId = config.layer0_dataset_presentation.question_management.selected_quiz_pack_id;
    const currentCustomization = config.layer0_dataset_presentation.question_management.question_customization[selectedPackId];

    if (!currentCustomization) return;

    const currentOrder = [...currentCustomization.custom_order];
    const currentIndex = currentOrder.indexOf(questionId);

    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    if (newIndex < 0 || newIndex >= currentOrder.length) return;

    // 交换位置
    [currentOrder[currentIndex], currentOrder[newIndex]] = [currentOrder[newIndex], currentOrder[currentIndex]];

    setConfig(prev => ({
      ...prev!,
      layer0_dataset_presentation: {
        ...prev!.layer0_dataset_presentation,
        question_management: {
          ...prev!.layer0_dataset_presentation.question_management,
          question_customization: {
            ...prev!.layer0_dataset_presentation.question_management.question_customization,
            [selectedPackId]: {
              ...currentCustomization,
              custom_order: currentOrder
            }
          }
        }
      }
    }));
  };

  const resetQuestionOrder = () => {
    if (!config) return;

    const selectedPackId = config.layer0_dataset_presentation.question_management.selected_quiz_pack_id;
    const currentCustomization = config.layer0_dataset_presentation.question_management.question_customization[selectedPackId];

    if (!currentCustomization) return;

    // 重置为启用问题的原始顺序
    const originalOrder = currentCustomization.enabled_questions
      .filter(q => q.enabled)
      .sort((a, b) => a.order - b.order)
      .map(q => q.id);

    setConfig(prev => ({
      ...prev!,
      layer0_dataset_presentation: {
        ...prev!.layer0_dataset_presentation,
        question_management: {
          ...prev!.layer0_dataset_presentation.question_management,
          question_customization: {
            ...prev!.layer0_dataset_presentation.question_management.question_customization,
            [selectedPackId]: {
              ...currentCustomization,
              custom_order: originalOrder
            }
          }
        }
      }
    }));
  };

  const switchQuizPack = (packId: string) => {
    if (!config) return;

    setConfig(prev => ({
      ...prev!,
      layer0_dataset_presentation: {
        ...prev!.layer0_dataset_presentation,
        question_management: {
          ...prev!.layer0_dataset_presentation.question_management,
          selected_quiz_pack_id: packId
        }
      }
    }));

    // 重新计算个性化级别
    setLocalPersonalizationLevel(prev => Math.min(prev + 10, 100));
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quiz系统设置</h1>
          <p className="text-muted-foreground">
            基于6层个性化配置架构的深度定制体验
          </p>
        </div>
        <Badge variant="secondary" className="text-sm">
          个性化级别: {localPersonalizationLevel}%
        </Badge>
      </div>

      {/* 架构概览卡片 */}
      <Card className="border-2 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            数据与展现分离架构
          </CardTitle>
          <CardDescription>
            Quiz系统采用创新的6层个性化配置架构，实现数据逻辑与用户界面的完全分离
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Target className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <h3 className="font-semibold">数据层</h3>
              <p className="text-sm text-muted-foreground">纯粹的量表逻辑和内容</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Eye className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <h3 className="font-semibold">展现层</h3>
              <p className="text-sm text-muted-foreground">个性化UI配置和样式</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Sparkles className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <h3 className="font-semibold">会话层</h3>
              <p className="text-sm text-muted-foreground">运行时配置合并</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 5层配置选项卡 - 合并用户选择与视图细节 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="layer0">数据集展现</TabsTrigger>
          <TabsTrigger value="layer1">视图配置</TabsTrigger>
          <TabsTrigger value="layer2">渲染策略</TabsTrigger>
          <TabsTrigger value="layer3">皮肤基础</TabsTrigger>
          <TabsTrigger value="layer4" className="flex items-center gap-1">
            <Sparkles className="h-3 w-3 text-yellow-500" />
            视图细节
          </TabsTrigger>
          <TabsTrigger value="layer5">可访问性</TabsTrigger>
        </TabsList>

        {/* 概览选项卡 */}
        <TabsContent value="overview" className="space-y-4">
          {/* 当前量表选择 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                当前配置量表
              </CardTitle>
              <CardDescription>
                选择要配置的量表，所有配置选项都将应用于此量表
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Select
                  value={config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id}
                  onValueChange={switchQuizPack}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择要配置的量表..." />
                  </SelectTrigger>
                  <SelectContent>
                    {config?.layer0_dataset_presentation.question_management.available_quiz_packs.map((pack) => (
                      <SelectItem key={pack.id} value={pack.id}>
                        <div className="flex items-center gap-2">
                          <span>{pack.name}</span>
                          <Badge variant={pack.category === 'emotion' ? 'default' : 'secondary'} className="text-xs">
                            {pack.category === 'emotion' ? '情绪' :
                             pack.category === 'tcm' ? '中医' :
                             pack.category === 'daily' ? '日常' :
                             pack.category === 'assessment' ? '评估' : pack.category}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* 当前量表信息 */}
                {config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id && (
                  <div className="p-4 bg-muted/50 rounded-lg">
                    {(() => {
                      const selectedPackId = config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id;
                      const selectedPack = config?.layer0_dataset_presentation.question_management.available_quiz_packs.find(p => p.id === selectedPackId);

                      if (!selectedPack) return null;

                      return (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{selectedPack.name}</h4>
                            <Badge variant={selectedPack.category === 'emotion' ? 'default' : 'secondary'}>
                              {selectedPack.category === 'emotion' ? '情绪' :
                               pack.category === 'tcm' ? '中医' :
                               pack.category === 'daily' ? '日常' :
                               pack.category === 'assessment' ? '评估' : selectedPack.category}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {selectedPack.description}
                          </p>
                          <div className="text-xs text-muted-foreground">
                            以下所有配置都将应用于此量表
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 快速启动区域 */}
          <Card className="border-2 border-blue-200 bg-blue-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5 text-blue-600" />
                快速启动当前量表
              </CardTitle>
              <CardDescription>
                使用当前配置快速开始选中的量表测试
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => {
                  // 跳转到NewHome并使用当前选中的量表
                  navigate('/');
                }}
                className="w-full h-16 flex items-center justify-center gap-3"
                size="lg"
                disabled={!config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id}
              >
                <Play className="h-6 w-6" />
                <div className="text-left">
                  <div className="font-semibold">开始测试</div>
                  <div className="text-sm opacity-90">
                    {config?.layer0_dataset_presentation.available_quiz_packs.find(
                      p => p.id === config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id
                    )?.name || '请先选择量表'}
                  </div>
                </div>
              </Button>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => setActiveTab('layer0')}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  Layer 0: 数据集展现
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  量表包选择和问题个性化配置
                </p>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      {config?.layer0_dataset_presentation.default_difficulty_preference}
                    </Badge>
                    <Badge variant="secondary">
                      {config?.layer0_dataset_presentation.question_management.available_quiz_packs.find(
                        p => p.id === config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id
                      )?.name || '未选择'}
                    </Badge>
                  </div>
                  {(() => {
                    const selectedPackId = config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id;
                    const customization = config?.layer0_dataset_presentation.question_management.question_customization[selectedPackId];

                    if (!customization) return null;

                    const enabledCount = customization.enabled_questions.filter(q => q.enabled).length;
                    const totalCount = customization.enabled_questions.length;

                    return (
                      <div className="text-xs text-muted-foreground">
                        已启用问题: {enabledCount}/{totalCount}
                      </div>
                    );
                  })()}
                </div>
              </CardContent>
            </Card>

            <Card
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => setActiveTab('layer1')}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Layer 1: 视图配置
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  视图类型选择和详细配置
                </p>
                <div className="flex gap-2">
                  <Badge variant="outline">
                    {config?.layer1_user_choice.preferred_view_type}
                  </Badge>
                  <Badge variant="secondary">
                    {config?.layer4_view_detail.emotion_presentation.emotion_grouping_style}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Layer 2 Overview Card - Conditionally Rendered */}
            <Card
              className={`cursor-pointer hover:shadow-md transition-shadow ${!isVip && 'opacity-50 pointer-events-none'}`}
              onClick={() => isVip ? setActiveTab('layer2') : null}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Cpu className="h-5 w-5" />
                  Layer 2: 渲染策略
                  {!isVip && <Badge variant="destructive" className="ml-auto"><Crown className="h-3 w-3 mr-1" />VIP</Badge>}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  渲染引擎和性能配置
                </p>
                <Badge variant="outline">
                  {config?.layer2_rendering_strategy.performance_mode}
                </Badge>
                {!isVip && (
                  <p className="text-xs text-red-500 mt-2">升级VIP以解锁此高级设置</p>
                )}
              </CardContent>
            </Card>

            {/* Layer 3 Overview Card */}
            <Card
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => setActiveTab('layer3')}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Layer 3: 皮肤基础
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  颜色、字体和动画效果
                </p>
                <div className="flex gap-2">
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: config?.layer3_skin_base.colors.primary }}
                  />
                  <Badge variant="outline">
                    {config?.layer3_skin_base.fonts.primary_font}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Layer 4 Overview Card - New */}
            <Card
              className={`cursor-pointer hover:shadow-md transition-shadow ${!isVip && 'opacity-50 pointer-events-none'}`}
              onClick={() => isVip ? setActiveTab('layer4') : null}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Sparkles className="h-3 w-3 text-yellow-500" />
                  Layer 4: 视图细节
                  {!isVip && <Badge variant="destructive" className="ml-auto"><Crown className="h-3 w-3 mr-1" />VIP</Badge>}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  自定义情绪轮盘、卡片、气泡和星系视图的详细配置
                </p>
                <div className="flex gap-2">
                  <Badge variant="outline">
                    {config?.layer4_view_detail.emotion_presentation.emotion_grouping_style}
                  </Badge>
                  <Badge variant="secondary">
                    {config?.layer4_view_detail.emotion_presentation.tier_transition_animation}
                  </Badge>
                </div>
                {!isVip && (
                  <p className="text-xs text-red-500 mt-2">升级VIP以解锁此高级设置</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Layer 0: 数据集展现配置 */}
        <TabsContent value="layer0" className="space-y-4">
          {/* 当前量表信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                当前量表配置
              </CardTitle>
              <CardDescription>
                针对当前选中量表的数据展现和交互配置
              </CardDescription>
            </CardHeader>
            <CardContent>
              {config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id ? (
                <div className="p-4 bg-muted/50 rounded-lg">
                  {(() => {
                    const selectedPackId = config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id;
                    const selectedPack = config?.layer0_dataset_presentation.question_management.available_quiz_packs.find(p => p.id === selectedPackId);

                    if (!selectedPack) return null;

                    return (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{selectedPack.name}</h4>
                          <Badge variant={selectedPack.category === 'emotion' ? 'default' : 'secondary'}>
                            {selectedPack.category === 'emotion' ? '情绪' :
                             selectedPack.category === 'tcm' ? '中医' :
                             selectedPack.category === 'daily' ? '日常' :
                             selectedPack.category === 'assessment' ? '评估' : selectedPack.category}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {selectedPack.description}
                        </p>
                        <div className="text-xs text-muted-foreground">
                          以下所有配置都将应用于此量表
                        </div>
                      </div>
                    );
                  })()}
                </div>
              ) : (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    请先在概览页面选择要配置的量表
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 会话配置 */}
          <Card>
            <CardHeader>
              <CardTitle>会话配置</CardTitle>
              <CardDescription>
                配置当前量表的会话行为和进度管理
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 进度恢复配置 */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>恢复进度</Label>
                  <p className="text-sm text-muted-foreground">
                    重新进入时是否恢复上次未完成的Quiz进度
                  </p>
                </div>
                <Switch
                  checked={config?.layer0_dataset_presentation.restore_progress}
                  onCheckedChange={(checked) => updateConfig('layer0_dataset_presentation', 'restore_progress', checked)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="difficulty-preference">难度级别</Label>
                  <Select
                    value={config?.layer0_dataset_presentation.default_difficulty_preference}
                    onValueChange={(value) => updateConfig('layer0_dataset_presentation', 'default_difficulty_preference', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">初学者</SelectItem>
                      <SelectItem value="regular">常规</SelectItem>
                      <SelectItem value="advanced">高级</SelectItem>
                      <SelectItem value="vip">VIP</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    影响问题复杂度和提示信息的显示
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="session-length">预期时长</Label>
                  <Select
                    value={config?.layer0_dataset_presentation.session_length_preference}
                    onValueChange={(value) => updateConfig('layer0_dataset_presentation', 'session_length_preference', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="short">短 (5-10分钟)</SelectItem>
                      <SelectItem value="medium">中 (10-20分钟)</SelectItem>
                      <SelectItem value="long">长 (20分钟以上)</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    影响问题数量和详细程度的建议
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 问题展示字段配置 */}
          <Card>
            <CardHeader>
              <CardTitle>问题展示配置</CardTitle>
              <CardDescription>
                配置在展示问题时显示哪些字段和信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { key: 'show_question_text', label: '问题文本', desc: '显示问题的主要文本内容' },
                { key: 'show_question_description', label: '问题描述', desc: '显示问题的详细描述或说明' },
                { key: 'show_question_order', label: '问题序号', desc: '显示当前问题在Quiz中的序号' },
                { key: 'show_progress_indicator', label: '进度指示器', desc: '显示Quiz完成进度条或百分比' },
                { key: 'show_answer_options', label: '答案选项', desc: '显示可选择的答案选项' },
                { key: 'show_option_descriptions', label: '选项描述', desc: '显示每个选项的详细描述' },
                { key: 'show_option_icons', label: '选项图标', desc: '显示选项的emoji或图标' },
              ].map((field) => (
                <div key={field.key} className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>{field.label}</Label>
                    <p className="text-sm text-muted-foreground">{field.desc}</p>
                  </div>
                  <Switch
                    checked={config?.layer0_dataset_presentation.question_display_fields[field.key as keyof typeof config.layer0_dataset_presentation.question_display_fields]}
                    onCheckedChange={(checked) => updateConfig('layer0_dataset_presentation', 'question_display_fields', {
                      ...config?.layer0_dataset_presentation.question_display_fields,
                      [field.key]: checked
                    })}
                  />
                </div>
              ))}
            </CardContent>
          </Card>

          {/* 交互行为配置 */}
          <Card>
            <CardHeader>
              <CardTitle>交互行为配置</CardTitle>
              <CardDescription>
                配置用户与Quiz的交互方式和行为
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>选择后自动跳转</Label>
                  <p className="text-sm text-muted-foreground">
                    选择答案选项后是否自动跳转到下一个问题
                  </p>
                </div>
                <Switch
                  checked={config?.layer0_dataset_presentation.interaction_behavior.auto_advance_after_selection}
                  onCheckedChange={(checked) => updateConfig('layer0_dataset_presentation', 'interaction_behavior', {
                    ...config?.layer0_dataset_presentation.interaction_behavior,
                    auto_advance_after_selection: checked
                  })}
                />
              </div>

              {config?.layer0_dataset_presentation.interaction_behavior.auto_advance_after_selection && (
                <div className="space-y-2">
                  <Label>自动跳转延迟: {config?.layer0_dataset_presentation.interaction_behavior.auto_advance_delay_ms}ms</Label>
                  <Slider
                    value={[config?.layer0_dataset_presentation.interaction_behavior.auto_advance_delay_ms || 1500]}
                    onValueChange={([value]) => updateConfig('layer0_dataset_presentation', 'interaction_behavior', {
                      ...config?.layer0_dataset_presentation.interaction_behavior,
                      auto_advance_delay_ms: value
                    })}
                    max={5000}
                    min={500}
                    step={250}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    选择答案后等待多长时间自动跳转到下一题
                  </p>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>允许修改答案</Label>
                  <p className="text-sm text-muted-foreground">
                    是否允许用户修改已选择的答案
                  </p>
                </div>
                <Switch
                  checked={config?.layer0_dataset_presentation.interaction_behavior.allow_answer_change}
                  onCheckedChange={(checked) => updateConfig('layer0_dataset_presentation', 'interaction_behavior', {
                    ...config?.layer0_dataset_presentation.interaction_behavior,
                    allow_answer_change: checked
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>显示确认对话框</Label>
                  <p className="text-sm text-muted-foreground">
                    在提交答案或跳转时显示确认对话框
                  </p>
                </div>
                <Switch
                  checked={config?.layer0_dataset_presentation.interaction_behavior.show_confirmation_dialog}
                  onCheckedChange={(checked) => updateConfig('layer0_dataset_presentation', 'interaction_behavior', {
                    ...config?.layer0_dataset_presentation.interaction_behavior,
                    show_confirmation_dialog: checked
                  })}
                />
              </div>
            </CardContent>
          </Card>

          {/* 问题个性化管理 */}
          <Card>
            <CardHeader>
              <CardTitle>问题个性化管理</CardTitle>
              <CardDescription>
                为当前量表定制问题内容和顺序，提供极致的个性化体验
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 当前量表提示 */}
              {config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id ? (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    正在配置: {config?.layer0_dataset_presentation.question_management.available_quiz_packs.find(
                      p => p.id === config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id
                    )?.name}
                  </p>
                </div>
              ) : (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    请先在概览页面选择要配置的量表
                  </p>
                </div>
              )}

              {/* 问题管理 */}
              {config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>问题配置</Label>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={resetQuestionOrder}
                        className="flex items-center gap-1"
                      >
                        <RotateCcw className="h-3 w-3" />
                        重置顺序
                      </Button>
                    </div>
                  </div>

                  {/* 问题列表 */}
                  <div className="space-y-2">
                    {(() => {
                      const selectedPackId = config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id;
                      const customization = config?.layer0_dataset_presentation.question_management.question_customization[selectedPackId];

                      if (!customization) return null;

                      // 按自定义顺序排序已启用的问题
                      const orderedQuestions = customization.custom_order
                        .map(id => customization.enabled_questions.find(q => q.id === id))
                        .filter(Boolean);

                      // 添加未在自定义顺序中的已启用问题
                      const remainingQuestions = customization.enabled_questions
                        .filter(q => q.enabled && !customization.custom_order.includes(q.id));

                      const allQuestions = [...orderedQuestions, ...remainingQuestions];

                      return allQuestions.map((question, index) => (
                        <div
                          key={question.id}
                          className={`p-3 border rounded-lg ${
                            question.enabled ? 'bg-background' : 'bg-muted/50'
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            {/* 拖拽手柄 */}
                            <div className="flex flex-col gap-1">
                              <button
                                onClick={() => moveQuestion(question.id, 'up')}
                                disabled={index === 0}
                                className="p-1 hover:bg-muted rounded disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <ChevronUp className="h-3 w-3" />
                              </button>
                              <GripVertical className="h-4 w-4 text-muted-foreground" />
                              <button
                                onClick={() => moveQuestion(question.id, 'down')}
                                disabled={index === allQuestions.length - 1}
                                className="p-1 hover:bg-muted rounded disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <ChevronDown className="h-3 w-3" />
                              </button>
                            </div>

                            {/* 启用/禁用开关 */}
                            <Switch
                              checked={question.enabled}
                              onCheckedChange={() => toggleQuestionEnabled(question.id)}
                              disabled={question.required}
                            />

                            {/* 问题信息 */}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{question.title}</span>
                                {question.required && (
                                  <Badge variant="destructive" className="text-xs">
                                    必需
                                  </Badge>
                                )}
                                <Badge variant="outline" className="text-xs">
                                  #{index + 1}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground mt-1">
                                {question.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      ));
                    })()}
                  </div>

                  {/* 统计信息 */}
                  {(() => {
                    const selectedPackId = config?.layer0_dataset_presentation.question_management.selected_quiz_pack_id;
                    const customization = config?.layer0_dataset_presentation.question_management.question_customization[selectedPackId];

                    if (!customization) return null;

                    const enabledCount = customization.enabled_questions.filter(q => q.enabled).length;
                    const totalCount = customization.enabled_questions.length;
                    const requiredCount = customization.enabled_questions.filter(q => q.required).length;

                    return (
                      <div className="p-3 bg-muted/50 rounded-lg">
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <div className="text-lg font-semibold text-primary">{enabledCount}</div>
                            <div className="text-xs text-muted-foreground">已启用</div>
                          </div>
                          <div>
                            <div className="text-lg font-semibold text-orange-600">{requiredCount}</div>
                            <div className="text-xs text-muted-foreground">必需项</div>
                          </div>
                          <div>
                            <div className="text-lg font-semibold text-muted-foreground">{totalCount}</div>
                            <div className="text-xs text-muted-foreground">总数</div>
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Layer 1: 视图配置 - 合并用户选择与视图细节 */}
        <TabsContent value="layer1" className="space-y-4">
          {/* 基础视图选择 */}
          <Card>
            <CardHeader>
              <CardTitle>基础视图配置</CardTitle>
              <CardDescription>
                选择您偏好的视图类型和基础界面设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="view-type">首选视图类型</Label>
                  <Select
                    value={config?.layer1_user_choice.preferred_view_type}
                    onValueChange={(value) => updateConfig('layer1_user_choice', 'preferred_view_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="wheel">轮盘视图</SelectItem>
                      <SelectItem value="card">卡片视图</SelectItem>
                      <SelectItem value="bubble">气泡视图</SelectItem>
                      <SelectItem value="galaxy">星系视图</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    选择后下方将显示对应的详细配置选项
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="user-level">用户级别</Label>
                  <Select
                    value={config?.layer1_user_choice.user_level}
                    onValueChange={(value) => updateConfig('layer1_user_choice', 'user_level', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">初学者</SelectItem>
                      <SelectItem value="regular">常规用户</SelectItem>
                      <SelectItem value="advanced">高级用户</SelectItem>
                      <SelectItem value="vip">VIP用户</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>深色模式</Label>
                    <p className="text-sm text-muted-foreground">
                      启用深色主题界面
                    </p>
                  </div>
                  <Switch
                    checked={config?.layer1_user_choice.dark_mode}
                    onCheckedChange={(checked) => updateConfig('layer1_user_choice', 'dark_mode', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color-mode">颜色模式</Label>
                  <Select
                    value={config?.layer1_user_choice.color_mode}
                    onValueChange={(value) => updateConfig('layer1_user_choice', 'color_mode', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="warm">暖色调</SelectItem>
                      <SelectItem value="cool">冷色调</SelectItem>
                      <SelectItem value="mixed">混合色调</SelectItem>
                      <SelectItem value="game">游戏风格</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 视图细节配置 - 根据首选视图类型动态显示 */}
          {config?.layer1_user_choice.preferred_view_type === 'wheel' && (
            <Card>
              <CardHeader>
                <CardTitle>轮盘视图详细配置</CardTitle>
                <CardDescription>
                  自定义情绪轮盘的外观和交互行为
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>容器大小: {config?.layer4_view_detail.wheel_config?.container_size}px</Label>
                    <Slider
                      value={[config?.layer4_view_detail.wheel_config?.container_size || 400]}
                      onValueChange={([value]) => updateConfig('layer4_view_detail', 'wheel_config', {
                        ...config?.layer4_view_detail.wheel_config,
                        container_size: value
                      })}
                      max={800}
                      min={200}
                      step={20}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>轮盘半径: {config?.layer4_view_detail.wheel_config?.wheel_radius}px</Label>
                    <Slider
                      value={[config?.layer4_view_detail.wheel_config?.wheel_radius || 180]}
                      onValueChange={([value]) => updateConfig('layer4_view_detail', 'wheel_config', {
                        ...config?.layer4_view_detail.wheel_config,
                        wheel_radius: value
                      })}
                      max={400}
                      min={80}
                      step={10}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>层级间距: {config?.layer4_view_detail.wheel_config?.tier_spacing}px</Label>
                    <Slider
                      value={[config?.layer4_view_detail.wheel_config?.tier_spacing || 60]}
                      onValueChange={([value]) => updateConfig('layer4_view_detail', 'wheel_config', {
                        ...config?.layer4_view_detail.wheel_config,
                        tier_spacing: value
                      })}
                      max={100}
                      min={20}
                      step={5}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>中心半径: {config?.layer4_view_detail.wheel_config?.center_radius}px</Label>
                    <Slider
                      value={[config?.layer4_view_detail.wheel_config?.center_radius || 40]}
                      onValueChange={([value]) => updateConfig('layer4_view_detail', 'wheel_config', {
                        ...config?.layer4_view_detail.wheel_config,
                        center_radius: value
                      })}
                      max={80}
                      min={20}
                      step={5}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="emotion-display">情绪显示模式</Label>
                    <Select
                      value={config?.layer4_view_detail.wheel_config?.emotion_display_mode}
                      onValueChange={(value) => updateConfig('layer4_view_detail', 'wheel_config', {
                        ...config?.layer4_view_detail.wheel_config,
                        emotion_display_mode: value
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hierarchical">层次化</SelectItem>
                        <SelectItem value="clustered">聚类式</SelectItem>
                        <SelectItem value="radial">径向式</SelectItem>
                        <SelectItem value="spiral">螺旋式</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>显示标签</Label>
                      <p className="text-sm text-muted-foreground">在轮盘上显示情绪文字标签</p>
                    </div>
                    <Switch
                      checked={config?.layer4_view_detail.wheel_config?.show_labels}
                      onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'wheel_config', {
                        ...config?.layer4_view_detail.wheel_config,
                        show_labels: checked
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>显示表情符号</Label>
                      <p className="text-sm text-muted-foreground">在轮盘上显示情绪表情符号</p>
                    </div>
                    <Switch
                      checked={config?.layer4_view_detail.wheel_config?.show_emojis}
                      onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'wheel_config', {
                        ...config?.layer4_view_detail.wheel_config,
                        show_emojis: checked
                      })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {config?.layer1_user_choice.preferred_view_type === 'card' && (
            <Card>
              <CardHeader>
                <CardTitle>卡片视图详细配置</CardTitle>
                <CardDescription>
                  自定义情绪卡片的布局和外观
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>网格列数: {config?.layer4_view_detail.card_config?.grid_columns}</Label>
                    <Slider
                      value={[config?.layer4_view_detail.card_config?.grid_columns || 3]}
                      onValueChange={([value]) => updateConfig('layer4_view_detail', 'card_config', {
                        ...config?.layer4_view_detail.card_config,
                        grid_columns: value
                      })}
                      max={5}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="card-size">卡片大小</Label>
                    <Select
                      value={config?.layer4_view_detail.card_config?.card_size}
                      onValueChange={(value) => updateConfig('layer4_view_detail', 'card_config', {
                        ...config?.layer4_view_detail.card_config,
                        card_size: value
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">小</SelectItem>
                        <SelectItem value="medium">中</SelectItem>
                        <SelectItem value="large">大</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>卡片间距: {config?.layer4_view_detail.card_config?.card_spacing}px</Label>
                    <Slider
                      value={[config?.layer4_view_detail.card_config?.card_spacing || 16]}
                      onValueChange={([value]) => updateConfig('layer4_view_detail', 'card_config', {
                        ...config?.layer4_view_detail.card_config,
                        card_spacing: value
                      })}
                      max={32}
                      min={0}
                      step={4}
                      className="w-full"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>显示描述</Label>
                      <p className="text-sm text-muted-foreground">在卡片上显示额外描述信息</p>
                    </div>
                    <Switch
                      checked={config?.layer4_view_detail.card_config?.show_descriptions}
                      onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'card_config', {
                        ...config?.layer4_view_detail.card_config,
                        show_descriptions: checked
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>悬停效果</Label>
                      <p className="text-sm text-muted-foreground">鼠标悬停时是否显示交互效果</p>
                    </div>
                    <Switch
                      checked={config?.layer4_view_detail.card_config?.hover_effects}
                      onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'card_config', {
                        ...config?.layer4_view_detail.card_config,
                        hover_effects: checked
                      })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {config?.layer1_user_choice.preferred_view_type === 'bubble' && (
            <Card>
              <CardHeader>
                <CardTitle>气泡视图详细配置</CardTitle>
                <CardDescription>
                  自定义情绪气泡图的外观和物理行为
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>容器宽度: {config?.layer4_view_detail.bubble_config?.container_width}px</Label>
                  <Slider
                    value={[config?.layer4_view_detail.bubble_config?.container_width || 500]}
                    onValueChange={([value]) => updateConfig('layer4_view_detail', 'bubble_config', {
                      ...config?.layer4_view_detail.bubble_config,
                      container_width: value
                    })}
                    max={800}
                    min={300}
                    step={20}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label>容器高度: {config?.layer4_view_detail.bubble_config?.container_height}px</Label>
                  <Slider
                    value={[config?.layer4_view_detail.bubble_config?.container_height || 400]}
                    onValueChange={([value]) => updateConfig('layer4_view_detail', 'bubble_config', {
                      ...config?.layer4_view_detail.bubble_config,
                      container_height: value
                    })}
                    max={600}
                    min={200}
                    step={20}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label>气泡大小范围: [{config?.layer4_view_detail.bubble_config?.bubble_size_range?.[0]}px, {config?.layer4_view_detail.bubble_config?.bubble_size_range?.[1]}px]</Label>
                  <Slider
                    value={config?.layer4_view_detail.bubble_config?.bubble_size_range || [30, 80]}
                    onValueChange={(value) => updateConfig('layer4_view_detail', 'bubble_config', {
                      ...config?.layer4_view_detail.bubble_config,
                      bubble_size_range: value as [number, number]
                    })}
                    max={150}
                    min={10}
                    step={5}
                    range
                    className="w-full"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用物理效果</Label>
                    <p className="text-sm text-muted-foreground">气泡是否模拟物理碰撞和重力效果</p>
                  </div>
                  <Switch
                    checked={config?.layer4_view_detail.bubble_config?.physics_enabled}
                    onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'bubble_config', {
                      ...config?.layer4_view_detail.bubble_config,
                      physics_enabled: checked
                    })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用碰撞检测</Label>
                    <p className="text-sm text-muted-foreground">气泡之间是否进行碰撞检测</p>
                  </div>
                  <Switch
                    checked={config?.layer4_view_detail.bubble_config?.collision_detection}
                    onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'bubble_config', {
                      ...config?.layer4_view_detail.bubble_config,
                      collision_detection: checked
                    })}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {config?.layer1_user_choice.preferred_view_type === 'list' && (
            <Card>
              <CardHeader>
                <CardTitle>列表视图详细配置</CardTitle>
                <CardDescription>
                  自定义情绪列表的外观和显示方式
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>列表项高度: {config?.layer4_view_detail.list_config?.item_height}px</Label>
                  <Slider
                    value={[config?.layer4_view_detail.list_config?.item_height || 60]}
                    onValueChange={([value]) => updateConfig('layer4_view_detail', 'list_config', {
                      ...config?.layer4_view_detail.list_config,
                      item_height: value
                    })}
                    max={100}
                    min={40}
                    step={5}
                    className="w-full"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>显示图标</Label>
                    <p className="text-sm text-muted-foreground">在列表项中显示表情符号或图标</p>
                  </div>
                  <Switch
                    checked={config?.layer4_view_detail.list_config?.show_icons}
                    onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'list_config', {
                      ...config?.layer4_view_detail.list_config,
                      show_icons: checked
                    })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>显示描述</Label>
                    <p className="text-sm text-muted-foreground">在列表项中显示详细描述</p>
                  </div>
                  <Switch
                    checked={config?.layer4_view_detail.list_config?.show_descriptions}
                    onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'list_config', {
                      ...config?.layer4_view_detail.list_config,
                      show_descriptions: checked
                    })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用分组</Label>
                    <p className="text-sm text-muted-foreground">按类别或其他属性对列表项进行分组</p>
                  </div>
                  <Switch
                    checked={config?.layer4_view_detail.list_config?.grouping_enabled}
                    onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'list_config', {
                      ...config?.layer4_view_detail.list_config,
                      grouping_enabled: checked
                    })}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {config?.layer1_user_choice.preferred_view_type === 'galaxy' && (
            <Card>
              <CardHeader>
                <CardTitle>星系视图详细配置</CardTitle>
                <CardDescription>
                  自定义情绪星系图的外观和交互行为
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>星系大小: {config?.layer4_view_detail.galaxy_config?.galaxy_size}px</Label>
                  <Slider
                    value={[config?.layer4_view_detail.galaxy_config?.galaxy_size || 500]}
                    onValueChange={([value]) => updateConfig('layer4_view_detail', 'galaxy_config', {
                      ...config?.layer4_view_detail.galaxy_config,
                      galaxy_size: value
                    })}
                    max={1000}
                    min={300}
                    step={50}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label>星星数量: {config?.layer4_view_detail.galaxy_config?.num_stars}</Label>
                  <Slider
                    value={[config?.layer4_view_detail.galaxy_config?.num_stars || 100]}
                    onValueChange={([value]) => updateConfig('layer4_view_detail', 'galaxy_config', {
                      ...config?.layer4_view_detail.galaxy_config,
                      num_stars: value
                    })}
                    max={500}
                    min={50}
                    step={10}
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label>星系旋转速度: {config?.layer4_view_detail.galaxy_config?.rotation_speed}</Label>
                  <Slider
                    value={[config?.layer4_view_detail.galaxy_config?.rotation_speed || 1]}
                    onValueChange={([value]) => updateConfig('layer4_view_detail', 'galaxy_config', {
                      ...config?.layer4_view_detail.galaxy_config,
                      rotation_speed: value
                    })}
                    max={5}
                    min={0.1}
                    step={0.1}
                    className="w-full"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用恒星闪烁</Label>
                    <p className="text-sm text-muted-foreground">星星是否显示闪烁效果</p>
                  </div>
                  <Switch
                    checked={config?.layer4_view_detail.galaxy_config?.enable_twinkle}
                    onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'galaxy_config', {
                      ...config?.layer4_view_detail.galaxy_config,
                      enable_twinkle: checked
                    })}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* 情绪展现 */}
          <Card>
            <CardHeader>
              <CardTitle>情绪展现</CardTitle>
              <CardDescription>
                自定义情绪在Quiz中的分组和过渡动画效果
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="emotion-grouping">情绪分组样式</Label>
                <Select
                  value={config?.layer4_view_detail.emotion_presentation.emotion_grouping_style}
                  onValueChange={(value) => updateConfig('layer4_view_detail', 'emotion_presentation', {
                    ...config?.layer4_view_detail.emotion_presentation,
                    emotion_grouping_style: value
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="by_category">按类别</SelectItem>
                    <SelectItem value="by_intensity">按强度</SelectItem>
                    <SelectItem value="alphabetical">按字母顺序</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="tier-transition">层级过渡动画</Label>
                <Select
                  value={config?.layer4_view_detail.emotion_presentation.tier_transition_animation}
                  onValueChange={(value) => updateConfig('layer4_view_detail', 'emotion_presentation', {
                    ...config?.layer4_view_detail.emotion_presentation,
                    tier_transition_animation: value
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fade">淡入淡出</SelectItem>
                    <SelectItem value="slide">滑动</SelectItem>
                    <SelectItem value="zoom">缩放</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Layer 2: 渲染策略配置 */}
        <TabsContent value="layer2" className="space-y-4">
          {!isVip ? (
            <Card className="border-2 border-red-200 bg-red-50/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <Crown className="h-5 w-5 text-red-600" />
                  VIP专属功能
                </CardTitle>
                <CardDescription className="text-red-600">
                  此为VIP高级功能，升级VIP解锁更强大的渲染策略设置。
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-red-800 mb-4">
                  渲染策略配置允许您优化Quiz界面的性能和显示方式，包括渲染引擎偏好、内容显示模式和布局偏好等。
                </p>
                <Link to="/vip">
                  <Button className="w-full flex items-center gap-2">
                    <Crown className="h-4 w-4" />
                    升级VIP，解锁高级渲染策略
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>渲染引擎偏好</CardTitle>
                  <CardDescription>
                    选择在不同视图下使用的渲染引擎，以优化性能或视觉效果
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 渲染引擎偏好 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="wheel-render-engine">轮盘视图</Label>
                      <Select
                        value={config?.layer2_rendering_strategy.render_engine_preferences.wheel}
                        onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'render_engine_preferences', {
                          ...config?.layer2_rendering_strategy.render_engine_preferences,
                          wheel: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="D3">D3.js (高性能)</SelectItem>
                          <SelectItem value="SVG">SVG (高兼容性)</SelectItem>
                          <SelectItem value="Canvas">Canvas (灵活)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="card-render-engine">卡片视图</Label>
                      <Select
                        value={config?.layer2_rendering_strategy.render_engine_preferences.card}
                        onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'render_engine_preferences', {
                          ...config?.layer2_rendering_strategy.render_engine_preferences,
                          card: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="SVG">SVG (高精度)</SelectItem>
                          <SelectItem value="React">React (组件化)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="bubble-render-engine">气泡视图</Label>
                      <Select
                        value={config?.layer2_rendering_strategy.render_engine_preferences.bubble}
                        onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'render_engine_preferences', {
                          ...config?.layer2_rendering_strategy.render_engine_preferences,
                          bubble: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Canvas">Canvas (性能)</SelectItem>
                          <SelectItem value="WebGL">WebGL (3D)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="galaxy-render-engine">星系视图</Label>
                      <Select
                        value={config?.layer2_rendering_strategy.render_engine_preferences.galaxy}
                        onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'render_engine_preferences', {
                          ...config?.layer2_rendering_strategy.render_engine_preferences,
                          galaxy: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="WebGL">WebGL (高性能3D)</SelectItem>
                          <SelectItem value="PixiJS">PixiJS (2D渲染)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 内容显示模式偏好 */}
              <Card>
                <CardHeader>
                  <CardTitle>内容显示模式</CardTitle>
                  <CardDescription>
                    选择在不同视图下显示哪些类型的内容元素
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {[
                    { view: 'wheel', label: '轮盘视图' },
                    { view: 'card', label: '卡片视图' },
                    { view: 'bubble', label: '气泡视图' },
                    { view: 'galaxy', label: '星系视图' },
                  ].map((viewConfig) => (
                    <div key={viewConfig.view} className="space-y-2">
                      <Label>{viewConfig.label}</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {[ 'text', 'emoji', 'image', 'icon', 'audio', 'video', 'animation', 'rich_text' ].map((contentType) => {
                          const isChecked = config?.layer2_rendering_strategy.content_display_mode_preferences[viewConfig.view]?.includes(contentType) || false;
                          return (
                            <div key={contentType} className="flex items-center space-x-2">
                              <Switch
                                id={`${viewConfig.view}-${contentType}`}
                                checked={isChecked}
                                onCheckedChange={(checked) => {
                                  const currentContentModes = new Set(config?.layer2_rendering_strategy.content_display_mode_preferences[viewConfig.view] || []);
                                  if (checked) {
                                    currentContentModes.add(contentType);
                                  } else {
                                    currentContentModes.delete(contentType);
                                  }
                                  updateConfig('layer2_rendering_strategy', 'content_display_mode_preferences', {
                                    ...config?.layer2_rendering_strategy.content_display_mode_preferences,
                                    [viewConfig.view]: Array.from(currentContentModes)
                                  });
                                }}
                              />
                              <Label htmlFor={`${viewConfig.view}-${contentType}`}>
                                {contentType.charAt(0).toUpperCase() + contentType.slice(1)}
                              </Label>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* 布局偏好 */}
              <Card>
                <CardHeader>
                  <CardTitle>布局偏好</CardTitle>
                  <CardDescription>
                    为不同视图配置特定的布局模式
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="wheel-layout">轮盘视图</Label>
                      <Select
                        value={config?.layer2_rendering_strategy.layout_preferences.wheel}
                        onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'layout_preferences', {
                          ...config?.layer2_rendering_strategy.layout_preferences,
                          wheel: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="circular_balanced">环形平衡</SelectItem>
                          <SelectItem value="spiral">螺旋</SelectItem>
                          <SelectItem value="concentric">同心圆</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="card-layout">卡片视图</Label>
                      <Select
                        value={config?.layer2_rendering_strategy.layout_preferences.card}
                        onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'layout_preferences', {
                          ...config?.layer2_rendering_strategy.layout_preferences,
                          card: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="grid_responsive">响应式网格</SelectItem>
                          <SelectItem value="masonry">瀑布流</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="bubble-layout">气泡视图</Label>
                      <Select
                        value={config?.layer2_rendering_strategy.layout_preferences.bubble}
                        onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'layout_preferences', {
                          ...config?.layer2_rendering_strategy.layout_preferences,
                          bubble: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="organic_flow">有机流动</SelectItem>
                          <SelectItem value="clustered">聚类</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="galaxy-layout">星系视图</Label>
                      <Select
                        value={config?.layer2_rendering_strategy.layout_preferences.galaxy}
                        onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'layout_preferences', {
                          ...config?.layer2_rendering_strategy.layout_preferences,
                          galaxy: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="spatial_3d">空间3D</SelectItem>
                          <SelectItem value="constellation">星座</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 性能模式 */}
              <Card>
                <CardHeader>
                  <CardTitle>性能模式</CardTitle>
                  <CardDescription>
                    选择适合您设备的性能优化级别
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Select
                    value={config?.layer2_rendering_strategy.performance_mode}
                    onValueChange={(value) => updateConfig('layer2_rendering_strategy', 'performance_mode', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">低 (省电)</SelectItem>
                      <SelectItem value="balanced">均衡 (推荐)</SelectItem>
                      <SelectItem value="high">高 (最佳视觉效果)</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    影响动画流畅度、渲染质量和电池消耗
                  </p>
                </CardContent>
              </Card>

              {/* 支持的内容类型 */}
              <Card>
                <CardHeader>
                  <CardTitle>支持的内容类型</CardTitle>
                  <CardDescription>
                    配置Quiz系统支持在界面中渲染的内容类型
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    {[ 'text', 'emoji', 'image', 'icon', 'audio', 'video', 'animation', 'rich_text' ].map((contentType) => (
                      <div key={contentType} className="flex items-center space-x-2">
                        <Switch
                          id={`supported-content-${contentType}`}
                          checked={config?.layer2_rendering_strategy.supported_content_types[contentType as keyof typeof config.layer2_rendering_strategy.supported_content_types]}
                          onCheckedChange={(checked) => updateConfig('layer2_rendering_strategy', 'supported_content_types', {
                            ...config?.layer2_rendering_strategy.supported_content_types,
                            [contentType]: checked
                          })}
                        />
                        <Label htmlFor={`supported-content-${contentType}`}>
                          {contentType.charAt(0).toUpperCase() + contentType.slice(1)}
                        </Label>
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    禁用某些内容类型可以提高性能或限制显示复杂度
                  </p>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* Layer 3: 皮肤基础配置 */}
        <TabsContent value="layer3" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>皮肤选择</CardTitle>
              <CardDescription>
                选择您喜欢的Quiz界面主题皮肤
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Select
                value={config?.layer3_skin_base.selected_skin_id}
                onValueChange={(value) => updateConfig('layer3_skin_base', 'selected_skin_id', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {config?.layer3_skin_base.available_skins.map((skin) => (
                    <SelectItem key={skin.id} value={skin.id}>
                      <div className="flex items-center gap-2">
                        {skin.preview_image && (
                          <img src={skin.preview_image} alt={skin.name} className="w-6 h-6 rounded" />
                        )}
                        <span>{skin.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {skin.category}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {config?.layer3_skin_base.selected_skin_id === 'default' && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    您正在使用默认皮肤。更多高级皮肤可在商店中获取！
                    <Link to="/shop" className="ml-2 text-blue-600 hover:underline flex items-center gap-1 inline-flex">
                      前往商店 <ExternalLink className="h-3 w-3" />
                    </Link>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 皮肤基础配置 */}
          {config?.layer3_skin_base.selected_skin_id && (
            <Card>
              <CardHeader>
                <CardTitle>自定义颜色</CardTitle>
                <CardDescription>
                  调整当前皮肤的主要颜色，创造您的专属风格
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>主色</Label>
                    <input
                      type="color"
                      value={config?.layer3_skin_base.colors.primary}
                      onChange={(e) => updateConfig('layer3_skin_base', 'colors', {
                        ...config?.layer3_skin_base.colors,
                        primary: e.target.value
                      })}
                      className="w-full h-10 p-0 border-none cursor-pointer"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>辅色</Label>
                    <input
                      type="color"
                      value={config?.layer3_skin_base.colors.secondary}
                      onChange={(e) => updateConfig('layer3_skin_base', 'colors', {
                        ...config?.layer3_skin_base.colors,
                        secondary: e.target.value
                      })}
                      className="w-full h-10 p-0 border-none cursor-pointer"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>强调色</Label>
                    <input
                      type="color"
                      value={config?.layer3_skin_base.colors.accent}
                      onChange={(e) => updateConfig('layer3_skin_base', 'colors', {
                        ...config?.layer3_skin_base.colors,
                        accent: e.target.value
                      })}
                      className="w-full h-10 p-0 border-none cursor-pointer"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>字体与大小</CardTitle>
              <CardDescription>
                调整应用内文本的字体样式和整体大小比例
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="primary-font">主字体</Label>
                <Select
                  value={config?.layer3_skin_base.fonts.primary_font}
                  onValueChange={(value) => updateConfig('layer3_skin_base', 'fonts', {
                    ...config?.layer3_skin_base.fonts,
                    primary_font: value
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Inter">Inter</SelectItem>
                    <SelectItem value="Roboto">Roboto</SelectItem>
                    <SelectItem value="Open Sans">Open Sans</SelectItem>
                    <SelectItem value="Noto Sans SC">思源黑体</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>字号缩放: {config?.layer3_skin_base.fonts.size_scale.toFixed(2)}</Label>
                <Slider
                  value={[config?.layer3_skin_base.fonts.size_scale || 1.0]}
                  onValueChange={([value]) => updateConfig('layer3_skin_base', 'fonts', {
                    ...config?.layer3_skin_base.fonts,
                    size_scale: value
                  })}
                  max={1.5}
                  min={0.8}
                  step={0.05}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  调整整体文本的显示大小
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>动画效果</CardTitle>
              <CardDescription>
                控制界面动画的启用状态和速度
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用动画</Label>
                  <p className="text-sm text-muted-foreground">
                    开启或关闭应用内的所有动画效果
                  </p>
                </div>
                <Switch
                  checked={config?.layer3_skin_base.animations.enable_animations}
                  onCheckedChange={(checked) => updateConfig('layer3_skin_base', 'animations', {
                    ...config?.layer3_skin_base.animations,
                    enable_animations: checked
                  })}
                />
              </div>
              {config?.layer3_skin_base.animations.enable_animations && (
                <div className="space-y-2">
                  <Label htmlFor="animation-speed">动画速度</Label>
                  <Select
                    value={config?.layer3_skin_base.animations.animation_speed}
                    onValueChange={(value) => updateConfig('layer3_skin_base', 'animations', {
                      ...config?.layer3_skin_base.animations,
                      animation_speed: value
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="slow">慢</SelectItem>
                      <SelectItem value="normal">正常</SelectItem>
                      <SelectItem value="fast">快</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>减少动态效果</Label>
                  <p className="text-sm text-muted-foreground">
                    为有视觉敏感度的用户减少过度动画和闪烁
                  </p>
                </div>
                <Switch
                  checked={config?.layer3_skin_base.animations.reduce_motion}
                  onCheckedChange={(checked) => updateConfig('layer3_skin_base', 'animations', {
                    ...config?.layer3_skin_base.animations,
                    reduce_motion: checked
                  })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Layer 4: 视图细节配置 - 新增VIP专属 */}
        <TabsContent value="layer4" className="space-y-4">
          {!isVip ? (
            <Card className="border-2 border-red-200 bg-red-50/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <Crown className="h-5 w-5 text-red-600" />
                  VIP专属功能
                </CardTitle>
                <CardDescription className="text-red-600">
                  此为VIP高级功能，升级VIP解锁更精细的视图细节设置。
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-red-800 mb-4">
                  视图细节配置允许您自定义Quiz界面中各种视图（如轮盘、卡片、气泡、列表）的具体展现方式，包括大小、间距、显示模式和情绪展现等。
                </p>
                <Link to="/vip">
                  <Button className="w-full flex items-center gap-2">
                    <Crown className="h-4 w-4" />
                    升级VIP，解锁高级视图细节
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* 视图细节配置 - 根据首选视图类型动态显示 */}
              {config?.layer1_user_choice.preferred_view_type === 'wheel' && (
                <Card>
                  <CardHeader>
                    <CardTitle>轮盘视图详细配置</CardTitle>
                    <CardDescription>
                      自定义情绪轮盘的外观和交互行为
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>容器大小: {config?.layer4_view_detail.wheel_config?.container_size}px</Label>
                        <Slider
                          value={[config?.layer4_view_detail.wheel_config?.container_size || 400]}
                          onValueChange={([value]) => updateConfig('layer4_view_detail', 'wheel_config', {
                            ...config?.layer4_view_detail.wheel_config,
                            container_size: value
                          })}
                          max={800}
                          min={200}
                          step={20}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>轮盘半径: {config?.layer4_view_detail.wheel_config?.wheel_radius}px</Label>
                        <Slider
                          value={[config?.layer4_view_detail.wheel_config?.wheel_radius || 180]}
                          onValueChange={([value]) => updateConfig('layer4_view_detail', 'wheel_config', {
                            ...config?.layer4_view_detail.wheel_config,
                            wheel_radius: value
                          })}
                          max={400}
                          min={80}
                          step={10}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>层级间距: {config?.layer4_view_detail.wheel_config?.tier_spacing}px</Label>
                        <Slider
                          value={[config?.layer4_view_detail.wheel_config?.tier_spacing || 60]}
                          onValueChange={([value]) => updateConfig('layer4_view_detail', 'wheel_config', {
                            ...config?.layer4_view_detail.wheel_config,
                            tier_spacing: value
                          })}
                          max={100}
                          min={20}
                          step={5}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>中心半径: {config?.layer4_view_detail.wheel_config?.center_radius}px</Label>
                        <Slider
                          value={[config?.layer4_view_detail.wheel_config?.center_radius || 40]}
                          onValueChange={([value]) => updateConfig('layer4_view_detail', 'wheel_config', {
                            ...config?.layer4_view_detail.wheel_config,
                            center_radius: value
                          })}
                          max={80}
                          min={20}
                          step={5}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="emotion-display">情绪显示模式</Label>
                        <Select
                          value={config?.layer4_view_detail.wheel_config?.emotion_display_mode}
                          onValueChange={(value) => updateConfig('layer4_view_detail', 'wheel_config', {
                            ...config?.layer4_view_detail.wheel_config,
                            emotion_display_mode: value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="hierarchical">层次化</SelectItem>
                            <SelectItem value="clustered">聚类式</SelectItem>
                            <SelectItem value="radial">径向式</SelectItem>
                            <SelectItem value="spiral">螺旋式</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>显示标签</Label>
                          <p className="text-sm text-muted-foreground">在轮盘上显示情绪文字标签</p>
                        </div>
                        <Switch
                          checked={config?.layer4_view_detail.wheel_config?.show_labels}
                          onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'wheel_config', {
                            ...config?.layer4_view_detail.wheel_config,
                            show_labels: checked
                          })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>显示表情符号</Label>
                          <p className="text-sm text-muted-foreground">在轮盘上显示情绪表情符号</p>
                        </div>
                        <Switch
                          checked={config?.layer4_view_detail.wheel_config?.show_emojis}
                          onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'wheel_config', {
                            ...config?.layer4_view_detail.wheel_config,
                            show_emojis: checked
                          })}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {config?.layer1_user_choice.preferred_view_type === 'card' && (
                <Card>
                  <CardHeader>
                    <CardTitle>卡片视图详细配置</CardTitle>
                    <CardDescription>
                      自定义情绪卡片的外观和交互行为
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label>网格列数: {config?.layer4_view_detail.card_config?.grid_columns}</Label>
                      <Slider
                        value={[config?.layer4_view_detail.card_config?.grid_columns || 3]}
                        onValueChange={([value]) => updateConfig('layer4_view_detail', 'card_config', {
                          ...config?.layer4_view_detail.card_config,
                          grid_columns: value
                        })}
                        max={5}
                        min={1}
                        step={1}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="card-size">卡片大小</Label>
                      <Select
                        value={config?.layer4_view_detail.card_config?.card_size}
                        onValueChange={(value) => updateConfig('layer4_view_detail', 'card_config', {
                          ...config?.layer4_view_detail.card_config,
                          card_size: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">小</SelectItem>
                          <SelectItem value="medium">中</SelectItem>
                          <SelectItem value="large">大</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>卡片间距: {config?.layer4_view_detail.card_config?.card_spacing}px</Label>
                      <Slider
                        value={[config?.layer4_view_detail.card_config?.card_spacing || 16]}
                        onValueChange={([value]) => updateConfig('layer4_view_detail', 'card_config', {
                          ...config?.layer4_view_detail.card_config,
                          card_spacing: value
                        })}
                        max={32}
                        min={0}
                        step={4}
                        className="w-full"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>显示描述</Label>
                        <p className="text-sm text-muted-foreground">在卡片上显示额外描述信息</p>
                      </div>
                      <Switch
                        checked={config?.layer4_view_detail.card_config?.show_descriptions}
                        onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'card_config', {
                          ...config?.layer4_view_detail.card_config,
                          show_descriptions: checked
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>悬停效果</Label>
                        <p className="text-sm text-muted-foreground">鼠标悬停时是否显示交互效果</p>
                      </div>
                      <Switch
                        checked={config?.layer4_view_detail.card_config?.hover_effects}
                        onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'card_config', {
                          ...config?.layer4_view_detail.card_config,
                          hover_effects: checked
                        })}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {config?.layer1_user_choice.preferred_view_type === 'bubble' && (
                <Card>
                  <CardHeader>
                    <CardTitle>气泡视图详细配置</CardTitle>
                    <CardDescription>
                      自定义情绪气泡图的外观和物理行为
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label>容器宽度: {config?.layer4_view_detail.bubble_config?.container_width}px</Label>
                      <Slider
                        value={[config?.layer4_view_detail.bubble_config?.container_width || 500]}
                        onValueChange={([value]) => updateConfig('layer4_view_detail', 'bubble_config', {
                          ...config?.layer4_view_detail.bubble_config,
                          container_width: value
                        })}
                        max={800}
                        min={300}
                        step={20}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>容器高度: {config?.layer4_view_detail.bubble_config?.container_height}px</Label>
                      <Slider
                        value={[config?.layer4_view_detail.bubble_config?.container_height || 400]}
                        onValueChange={([value]) => updateConfig('layer4_view_detail', 'bubble_config', {
                          ...config?.layer4_view_detail.bubble_config,
                          container_height: value
                        })}
                        max={600}
                        min={200}
                        step={20}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>气泡大小范围: [{config?.layer4_view_detail.bubble_config?.bubble_size_range?.[0]}px, {config?.layer4_view_detail.bubble_config?.bubble_size_range?.[1]}px]</Label>
                      <Slider
                        value={config?.layer4_view_detail.bubble_config?.bubble_size_range || [30, 80]}
                        onValueChange={(value) => updateConfig('layer4_view_detail', 'bubble_config', {
                          ...config?.layer4_view_detail.bubble_config,
                          bubble_size_range: value as [number, number]
                        })}
                        max={150}
                        min={10}
                        step={5}
                        range
                        className="w-full"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>启用物理效果</Label>
                        <p className="text-sm text-muted-foreground">气泡是否模拟物理碰撞和重力效果</p>
                      </div>
                      <Switch
                        checked={config?.layer4_view_detail.bubble_config?.physics_enabled}
                        onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'bubble_config', {
                          ...config?.layer4_view_detail.bubble_config,
                          physics_enabled: checked
                        })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>启用碰撞检测</Label>
                        <p className="text-sm text-muted-foreground">气泡之间是否进行碰撞检测</p>
                      </div>
                      <Switch
                        checked={config?.layer4_view_detail.bubble_config?.collision_detection}
                        onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'bubble_config', {
                          ...config?.layer4_view_detail.bubble_config,
                          collision_detection: checked
                        })}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {config?.layer1_user_choice.preferred_view_type === 'list' && (
                <Card>
                  <CardHeader>
                    <CardTitle>列表视图详细配置</CardTitle>
                    <CardDescription>
                      自定义情绪列表的外观和显示方式
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label>列表项高度: {config?.layer4_view_detail.list_config?.item_height}px</Label>
                      <Slider
                        value={[config?.layer4_view_detail.list_config?.item_height || 60]}
                        onValueChange={([value]) => updateConfig('layer4_view_detail', 'list_config', {
                          ...config?.layer4_view_detail.list_config,
                          item_height: value
                        })}
                        max={100}
                        min={40}
                        step={5}
                        className="w-full"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>显示图标</Label>
                        <p className="text-sm text-muted-foreground">在列表项中显示表情符号或图标</p>
                      </div>
                      <Switch
                        checked={config?.layer4_view_detail.list_config?.show_icons}
                        onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'list_config', {
                          ...config?.layer4_view_detail.list_config,
                          show_icons: checked
                        })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>显示描述</Label>
                        <p className="text-sm text-muted-foreground">在列表项中显示详细描述</p>
                      </div>
                      <Switch
                        checked={config?.layer4_view_detail.list_config?.show_descriptions}
                        onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'list_config', {
                          ...config?.layer4_view_detail.list_config,
                          show_descriptions: checked
                        })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>启用分组</Label>
                        <p className="text-sm text-muted-foreground">按类别或其他属性对列表项进行分组</p>
                      </div>
                      <Switch
                        checked={config?.layer4_view_detail.list_config?.grouping_enabled}
                        onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'list_config', {
                          ...config?.layer4_view_detail.list_config,
                          grouping_enabled: checked
                        })}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {config?.layer1_user_choice.preferred_view_type === 'galaxy' && (
                <Card>
                  <CardHeader>
                    <CardTitle>星系视图详细配置</CardTitle>
                    <CardDescription>
                      自定义情绪星系图的外观和交互行为
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label>星系大小: {config?.layer4_view_detail.galaxy_config?.galaxy_size}px</Label>
                      <Slider
                        value={[config?.layer4_view_detail.galaxy_config?.galaxy_size || 500]}
                        onValueChange={([value]) => updateConfig('layer4_view_detail', 'galaxy_config', {
                          ...config?.layer4_view_detail.galaxy_config,
                          galaxy_size: value
                        })}
                        max={1000}
                        min={300}
                        step={50}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>星星数量: {config?.layer4_view_detail.galaxy_config?.num_stars}</Label>
                      <Slider
                        value={[config?.layer4_view_detail.galaxy_config?.num_stars || 100]}
                        onValueChange={([value]) => updateConfig('layer4_view_detail', 'galaxy_config', {
                          ...config?.layer4_view_detail.galaxy_config,
                          num_stars: value
                        })}
                        max={500}
                        min={50}
                        step={10}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>星系旋转速度: {config?.layer4_view_detail.galaxy_config?.rotation_speed}</Label>
                      <Slider
                        value={[config?.layer4_view_detail.galaxy_config?.rotation_speed || 1]}
                        onValueChange={([value]) => updateConfig('layer4_view_detail', 'galaxy_config', {
                          ...config?.layer4_view_detail.galaxy_config,
                          rotation_speed: value
                        })}
                        max={5}
                        min={0.1}
                        step={0.1}
                        className="w-full"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>启用恒星闪烁</Label>
                        <p className="text-sm text-muted-foreground">星星是否显示闪烁效果</p>
                      </div>
                      <Switch
                        checked={config?.layer4_view_detail.galaxy_config?.enable_twinkle}
                        onCheckedChange={(checked) => updateConfig('layer4_view_detail', 'galaxy_config', {
                          ...config?.layer4_view_detail.galaxy_config,
                          enable_twinkle: checked
                        })}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 情绪展现 */}
              <Card>
                <CardHeader>
                  <CardTitle>情绪展现</CardTitle>
                  <CardDescription>
                    自定义情绪在Quiz中的分组和过渡动画效果
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="emotion-grouping">情绪分组样式</Label>
                    <Select
                      value={config?.layer4_view_detail.emotion_presentation.emotion_grouping_style}
                      onValueChange={(value) => updateConfig('layer4_view_detail', 'emotion_presentation', {
                        ...config?.layer4_view_detail.emotion_presentation,
                        emotion_grouping_style: value
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="by_category">按类别</SelectItem>
                        <SelectItem value="by_intensity">按强度</SelectItem>
                        <SelectItem value="alphabetical">按字母顺序</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tier-transition">层级过渡动画</Label>
                    <Select
                      value={config?.layer4_view_detail.emotion_presentation.tier_transition_animation}
                      onValueChange={(value) => updateConfig('layer4_view_detail', 'emotion_presentation', {
                        ...config?.layer4_view_detail.emotion_presentation,
                        tier_transition_animation: value
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fade">淡入淡出</SelectItem>
                        <SelectItem value="slide">滑动</SelectItem>
                        <SelectItem value="zoom">缩放</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* Layer 5: 可访问性 */}
        <TabsContent value="layer5" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>可访问性设置</CardTitle>
              <CardDescription>
                配置辅助功能和界面无障碍选项
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>高对比度</Label>
                  <p className="text-sm text-muted-foreground">
                    增强文本和元素的对比度，提高可读性
                  </p>
                </div>
                <Switch
                  checked={config?.layer5_accessibility.high_contrast}
                  onCheckedChange={(checked) => updateConfig('layer5_accessibility', 'high_contrast', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>大字体</Label>
                  <p className="text-sm text-muted-foreground">
                    增加应用内所有文本的大小
                  </p>
                </div>
                <Switch
                  checked={config?.layer5_accessibility.large_text}
                  onCheckedChange={(checked) => updateConfig('layer5_accessibility', 'large_text', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>减少动态效果</Label>
                  <p className="text-sm text-muted-foreground">
                    禁用或减少界面动画效果，缓解视觉不适
                  </p>
                </div>
                <Switch
                  checked={config?.layer5_accessibility.reduce_motion}
                  onCheckedChange={(checked) => updateConfig('layer5_accessibility', 'reduce_motion', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>键盘导航</Label>
                  <p className="text-sm text-muted-foreground">
                    启用通过键盘进行所有界面操作
                  </p>
                </div>
                <Switch
                  checked={config?.layer5_accessibility.keyboard_navigation}
                  onCheckedChange={(checked) => updateConfig('layer5_accessibility', 'keyboard_navigation', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>语音指导</Label>
                  <p className="text-sm text-muted-foreground">
                    为视觉障碍用户提供语音提示和导航
                  </p>
                </div>
                <Switch
                  checked={config?.layer5_accessibility.voice_guidance}
                  onCheckedChange={(checked) => updateConfig('layer5_accessibility', 'voice_guidance', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 保存按钮 */}
      <div className="flex justify-end gap-4">
        <Button variant="outline">重置为默认</Button>
        <Button>保存配置</Button>
      </div>
    </div>
  );
};

export default QuizSettings;
