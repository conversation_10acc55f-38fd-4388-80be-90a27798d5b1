/**
 * 首页数据Hook - 统一Quiz架构
 * 使用统一的Quiz包架构，从配置中加载默认Quiz
 */

import { useState, useEffect, useCallback } from 'react';
import { Services } from '@/services';
import { QuizPack, QuizQuestion, QuizQuestionOption } from '@/types/schema/base';
import { ServiceResult } from '@/services/types/ServiceTypes';

export interface QuizData {
  pack: QuizPack;
  questions: QuizQuestion[];
  totalQuestions: number;
  estimatedDuration: number;
}

export interface HomeData {
  defaultQuiz: QuizData | null;
  recommendedQuizPacks: QuizPack[];
  recentQuizPacks: QuizPack[];
  userStats: {
    totalSessions: number;
    completedSessions: number;
    favoriteQuizTypes: string[];
  };
  isLoading: boolean;
  error: string | null;
}

export interface UseHomeDataReturn extends HomeData {
  refreshData: () => Promise<void>;
  loadDefaultQuiz: (packId?: string) => Promise<void>;
  loadRecommendedPacks: () => Promise<void>;
  getQuizPacksByCategory: (category: string) => Promise<QuizPack[]>;
  searchQuizPacks: (searchTerm: string) => Promise<QuizPack[]>;
}

export function useNewHomeData(userId?: string): UseHomeDataReturn {
  const [data, setData] = useState<HomeData>({
    defaultQuiz: null,
    recommendedQuizPacks: [],
    recentQuizPacks: [],
    userStats: {
      totalSessions: 0,
      completedSessions: 0,
      favoriteQuizTypes: []
    },
    isLoading: true,
    error: null
  });

  /**
   * 加载默认Quiz数据 - 从配置中获取默认Quiz
   */
  const loadDefaultQuiz = useCallback(async (packId?: string) => {
    try {
      setData(prev => ({ ...prev, isLoading: true, error: null }));

      const quizPackService = await Services.quizPack();
      const quizQuestionService = await Services.quizQuestion();

      // 获取默认Quiz包
      let defaultPack: QuizPack | null = null;

      if (packId) {
        const packResult = await quizPackService.findById(packId);
        if (packResult.success) {
          defaultPack = packResult.data;
        }
      } else {
        // 从配置中获取默认Quiz包 - 优先获取情绪类型的Quiz
        const packsResult = await quizPackService.getQuizPacksByCategory('emotion');
        if (packsResult.success && packsResult.data.length > 0) {
          // 查找默认的或者第一个活跃的Quiz包
          defaultPack = packsResult.data.find(pack => pack.is_default) || packsResult.data[0];
        }
      }

      if (!defaultPack) {
        throw new Error('No default quiz pack found');
      }

      // 获取Quiz问题和选项
      const questionsResult = await quizQuestionService.getQuestionsByType(defaultPack.id, defaultPack.quiz_type || 'emotion_wheel');

      if (!questionsResult.success) {
        throw new Error(questionsResult.error || 'Failed to load quiz questions');
      }

      const questions = questionsResult.data;

      const quizData: QuizData = {
        pack: defaultPack,
        questions,
        totalQuestions: questions.length,
        estimatedDuration: defaultPack.estimated_duration_minutes || 10
      };

      setData(prev => ({
        ...prev,
        defaultQuiz: quizData,
        isLoading: false
      }));

    } catch (error) {
      console.error('Failed to load default quiz data:', error);
      setData(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load default quiz data',
        isLoading: false
      }));
    }
  }, []);

  /**
   * 加载推荐的Quiz包
   */
  const loadRecommendedPacks = useCallback(async () => {
    try {
      const quizPackService = await Services.quizPack();

      // 获取推荐的Quiz包 - 使用getRecommendedQuizPacks方法
      const recommendedResult = await quizPackService.getRecommendedQuizPacks(10);

      if (recommendedResult.success) {
        setData(prev => ({
          ...prev,
          recommendedQuizPacks: recommendedResult.data
        }));
      }

      // 获取活跃的Quiz包作为最近的包 - 使用getActiveQuizPacks替代
      const accessibleResult = await quizPackService.getActiveQuizPacks();

      if (accessibleResult.success) {
        setData(prev => ({
          ...prev,
          recentQuizPacks: accessibleResult.data.slice(0, 5) // 限制为5个
        }));
      }

    } catch (error) {
      console.error('Failed to load recommended packs:', error);
      setData(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load recommended packs'
      }));
    }
  }, [userId]);

  /**
   * 加载用户统计数据
   */
  const loadUserStats = useCallback(async () => {
    try {
      // 这里可以添加用户统计数据的加载逻辑
      // 暂时使用模拟数据
      setData(prev => ({
        ...prev,
        userStats: {
          totalSessions: 0,
          completedSessions: 0,
          favoriteQuizTypes: []
        }
      }));
    } catch (error) {
      console.error('Failed to load user stats:', error);
    }
  }, [userId]);

  /**
   * 根据分类获取Quiz包
   */
  const getQuizPacksByCategory = useCallback(async (category: string): Promise<QuizPack[]> => {
    try {
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.getQuizPacksByCategory(category);

      return result.success ? result.data : [];
    } catch (error) {
      console.error('Failed to get quiz packs by category:', error);
      return [];
    }
  }, []);

  /**
   * 搜索Quiz包
   */
  const searchQuizPacks = useCallback(async (searchTerm: string): Promise<QuizPack[]> => {
    try {
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.searchQuizPacks(searchTerm);

      return result.success ? result.data : [];
    } catch (error) {
      console.error('Failed to search quiz packs:', error);
      return [];
    }
  }, []);

  /**
   * 刷新所有数据
   */
  const refreshData = useCallback(async () => {
    setData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      await Promise.all([
        loadDefaultQuiz(),
        loadRecommendedPacks(),
        loadUserStats()
      ]);
    } catch (error) {
      console.error('Failed to refresh data:', error);
      setData(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to refresh data',
        isLoading: false
      }));
    }
  }, [loadDefaultQuiz, loadRecommendedPacks, loadUserStats]);

  // 初始化数据加载
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  return {
    ...data,
    refreshData,
    loadDefaultQuiz,
    loadRecommendedPacks,
    getQuizPacksByCategory,
    searchQuizPacks
  };
}
