/**
 * Canvas轮盘组件
 * 使用Canvas API实现的轮盘，不依赖旧的轮盘实现
 *
 * 此组件是新视图系统的一部分，用于替代旧的轮盘组件
 * 它直接实现了轮盘的渲染，不依赖旧的轮盘类
 */

import { AnimatedEmoji } from '@/components/emoji/AnimatedEmoji';
import { useEmoji } from '@/contexts/EmojiContext';
import type { Emotion, ContentDisplayMode, SkinConfig } from '@/types';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { extract_wheel_config_for_engine, type WheelConfig } from '../../../utils/wheelConfigExtractor';

interface CanvasWheelComponentProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  onBack?: () => void;
  selectedPath?: any;
}

/**
 * 从 SkinConfig 中提取轮盘配置
 */
/**
 * Canvas轮盘组件
 */
export const CanvasWheelComponent: React.FC<CanvasWheelComponentProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  onBack,
  selectedPath,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [hoveredEmotion, setHoveredEmotion] = useState<string | null>(null);
  const { getEmojiItemForEmotionId } = useEmoji();

  // 提取轮盘配置
  const wheelConfig = extract_wheel_config_for_engine.canvas(skinConfig);

  // 绘制轮盘
  useEffect(() => {
    if (!canvasRef.current || emotions.length === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置画布尺寸
    canvas.width = wheelConfig.container_size;
    canvas.height = wheelConfig.container_size;

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置中心点
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = wheelConfig.wheel_radius;

    // 计算每个扇区的角度
    const anglePerSector = (2 * Math.PI) / emotions.length;

    // 绘制扇区
    emotions.forEach((emotion, index) => {
      const startAngle = index * anglePerSector;
      const endAngle = (index + 1) * anglePerSector;

      // 绘制扇区
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();

      // 设置扇区样式
      ctx.fillStyle = emotion.color || '#cccccc';
      ctx.fill();

      // 绘制扇区边框
      if (wheelConfig.sectorGap > 0) {
        ctx.strokeStyle = wheelConfig.background_color;
        ctx.lineWidth = wheelConfig.sectorGap;
        ctx.stroke();
      }

      // 计算扇区中心点
      const midAngle = startAngle + (endAngle - startAngle) / 2;
      const labelRadius = radius * 0.7; // 调整半径，使文本位于扇区中心
      const labelX = centerX + labelRadius * Math.cos(midAngle);
      const labelY = centerY + labelRadius * Math.sin(midAngle);

      // 根据内容类型显示文本和表情
      if (contentDisplayMode === 'text' || contentDisplayMode === 'textEmoji') {
        // 设置文本样式
        ctx.font = `${wheelConfig.font_size}px ${wheelConfig.font_family}`;
        ctx.fillStyle = wheelConfig.text_color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 绘制文本
        if (contentDisplayMode === 'textEmoji') {
          ctx.fillText(emotion.name, labelX, labelY + wheelConfig.font_size);
        } else {
          ctx.fillText(emotion.name, labelX, labelY);
        }
      }

      if (contentDisplayMode === 'emoji' || contentDisplayMode === 'textEmoji') {
        // 设置表情样式
        ctx.font = `${wheelConfig.emojiSize}px ${wheelConfig.font_family}`;
        ctx.fillStyle = wheelConfig.text_color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 绘制表情
        if (contentDisplayMode === 'textEmoji') {
          ctx.fillText(emotion.emoji, labelX, labelY - wheelConfig.font_size / 2);
        } else {
          ctx.fillText(emotion.emoji, labelX, labelY);
        }
      }
    });

    // 添加装饰效果
    if (wheelConfig.decorations) {
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius + 5, 0, 2 * Math.PI);
      ctx.strokeStyle = wheelConfig.shadowColor;
      ctx.lineWidth = 2;
      ctx.globalAlpha = 0.5;
      ctx.stroke();
      ctx.globalAlpha = 1;
    }

    // 添加阴影效果
    if (wheelConfig.shadow_enabled) {
      ctx.shadowColor = wheelConfig.shadowColor;
      ctx.shadowBlur = wheelConfig.shadowBlur;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
    }

    // 添加点击事件
    const handleCanvasClick = (event: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left - centerX;
      const y = event.clientY - rect.top - centerY;
      const distance = Math.sqrt(x * x + y * y);

      // 检查是否点击在轮盘内
      if (distance <= radius) {
        // 计算点击的角度
        let angle = Math.atan2(y, x);
        if (angle < 0) angle += 2 * Math.PI;

        // 计算点击的扇区索引
        const sectorIndex = Math.floor(angle / anglePerSector);
        if (sectorIndex >= 0 && sectorIndex < emotions.length) {
          onSelect(emotions[sectorIndex]);
        }
      }
    };

    canvas.addEventListener('click', handleCanvasClick);

    // 添加悬停事件
    const handleCanvasMouseMove = (event: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left - centerX;
      const y = event.clientY - rect.top - centerY;
      const distance = Math.sqrt(x * x + y * y);

      // 检查是否悬停在轮盘内
      if (distance <= radius) {
        // 计算悬停的角度
        let angle = Math.atan2(y, x);
        if (angle < 0) angle += 2 * Math.PI;

        // 计算悬停的扇区索引
        const sectorIndex = Math.floor(angle / anglePerSector);
        if (sectorIndex >= 0 && sectorIndex < emotions.length) {
          setHoveredEmotion(emotions[sectorIndex].id);
          canvas.style.cursor = 'pointer';
        } else {
          setHoveredEmotion(null);
          canvas.style.cursor = 'default';
        }
      } else {
        setHoveredEmotion(null);
        canvas.style.cursor = 'default';
      }
    };

    canvas.addEventListener('mousemove', handleCanvasMouseMove);

    // 清理事件监听器
    return () => {
      canvas.removeEventListener('click', handleCanvasClick);
      canvas.removeEventListener('mousemove', handleCanvasMouseMove);
    };
  }, [emotions, tierLevel, contentDisplayMode, skinConfig, wheelConfig, onSelect]);

  // 渲染动画表情（如果内容类型为 animatedEmoji）
  const renderAnimatedEmojis = () => {
    if (contentDisplayMode !== 'animatedEmoji' || !emotions.length) return null;

    return emotions.map((emotion, index) => {
      const angle = (index / emotions.length) * 2 * Math.PI;
      const radius = wheelConfig.wheel_radius * 0.7; // 调整半径，使表情位于扇区中心
      const x = Math.cos(angle) * radius + wheelConfig.container_size / 2;
      const y = Math.sin(angle) * radius + wheelConfig.container_size / 2;

      const emojiItem = getEmojiItemForEmotionId(emotion.id);

      return (
        <div
          key={emotion.id}
          style={{
            position: 'absolute',
            left: `${x}px`,
            top: `${y}px`,
            transform: 'translate(-50%, -50%)',
            zIndex: hoveredEmotion === emotion.id ? 10 : 1,
            transition: `all ${wheelConfig.transition_duration}ms`,
            cursor: 'pointer',
          }}
          onClick={() => onSelect(emotion)}
          onMouseEnter={() => setHoveredEmotion(emotion.id)}
          onMouseLeave={() => setHoveredEmotion(null)}
        >
          {emojiItem ? (
            <AnimatedEmoji
              emojiItem={emojiItem}
              size={
                wheelConfig.emojiSize >= 40
                  ? '4xl'
                  : wheelConfig.emojiSize >= 32
                    ? '3xl'
                    : wheelConfig.emojiSize >= 24
                      ? '2xl'
                      : wheelConfig.emojiSize >= 20
                        ? 'xl'
                        : wheelConfig.emojiSize >= 16
                          ? 'lg'
                          : wheelConfig.emojiSize >= 14
                            ? 'md'
                            : wheelConfig.emojiSize >= 12
                              ? 'sm'
                              : 'xs'
              }
              autoPlay={true}
              loop={true}
            />
          ) : (
            <span style={{ fontSize: `${wheelConfig.emojiSize}px` }}>{emotion.emoji}</span>
          )}
          {contentDisplayMode === 'textEmoji' && (
            <div
              style={{
                textAlign: 'center',
                fontSize: `${wheelConfig.font_size}px`,
                fontFamily: wheelConfig.font_family,
                color: wheelConfig.text_color,
                marginTop: '4px',
              }}
            >
              {emotion.name}
            </div>
          )}
        </div>
      );
    });
  };

  // 渲染返回按钮
  const renderBackButton = () => {
    if (!onBack || tierLevel === 1) return null;

    return (
      <button
        onClick={onBack}
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          boxShadow: wheelConfig.shadow_enabled ? `0 0 5px ${wheelConfig.shadowColor}` : 'none',
        }}
      >
        ←
      </button>
    );
  };

  // 渲染选中路径
  const renderSelectedPath = () => {
    if (!selectedPath) return null;

    return (
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 100,
          fontSize: `${wheelConfig.font_size}px`,
          fontFamily: wheelConfig.font_family,
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '5px',
          background: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(5px)',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {selectedPath}
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width: `${wheelConfig.container_size}px`,
        height: `${wheelConfig.container_size}px`,
        margin: '0 auto',
      }}
    >
      {renderBackButton()}
      {renderSelectedPath()}
      <canvas
        ref={canvasRef}
        style={{
          display: contentDisplayMode === 'animatedEmoji' ? 'none' : 'block',
          borderRadius: '50%',
          boxShadow: wheelConfig.shadow_enabled
            ? `0 0 ${wheelConfig.shadowBlur}px ${wheelConfig.shadowColor}`
            : 'none',
        }}
      />
      {contentDisplayMode === 'animatedEmoji' && (
        <div
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            background: wheelConfig.background_color,
            boxShadow: wheelConfig.shadow_enabled
              ? `0 0 ${wheelConfig.shadowBlur}px ${wheelConfig.shadowColor}`
              : 'none',
            position: 'relative',
          }}
        >
          {/* 渲染扇区背景 */}
          <canvas
            ref={canvasRef}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              borderRadius: '50%',
            }}
          />
          {renderAnimatedEmojis()}
        </div>
      )}
    </div>
  );
};
