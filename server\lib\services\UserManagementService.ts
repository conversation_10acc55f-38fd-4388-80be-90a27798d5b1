/**
 * 用户管理服务
 * 处理用户配置、VIP状态、皮肤解锁等功能
 */

import { executeQuery, batchStatements, InStatement } from '../database/index.js';

// 导入统一的类型定义
import {
  type UserProfile,
  type UserPreferences,
  type SkinUnlock,
  type VipStatus,
  UserProfileSchema,
  UserPreferencesSchema,
  SkinUnlockSchema,
  VipStatusSchema
} from '../../../src/types/schema/api.js';

export class UserManagementService {
  private static instance: UserManagementService;

  private constructor() {}

  static getInstance(): UserManagementService {
    if (!UserManagementService.instance) {
      UserManagementService.instance = new UserManagementService();
    }
    return UserManagementService.instance;
  }

  /**
   * 获取用户配置文件
   */
  async getUserProfile(userId: string): Promise<{ success: boolean; data?: UserProfile; error?: string }> {
    try {
      // 获取用户基本信息
      const userResult = await executeQuery({
        sql: 'SELECT * FROM users WHERE id = ? AND is_active = 1',
        args: [userId]
      });

      if (!userResult.rows || userResult.rows.length === 0) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const user = userResult.rows[0];

      // 获取皮肤解锁信息
      const skinUnlocksResult = await executeQuery({
        sql: 'SELECT skin_id FROM user_skin_unlocks WHERE user_id = ?',
        args: [userId]
      });

      const skinUnlocks = skinUnlocksResult.rows.map((row: any) => row.skin_id);

      // 获取用户偏好设置
      const preferencesResult = await executeQuery({
        sql: 'SELECT * FROM user_preferences WHERE user_id = ?',
        args: [userId]
      });

      const preferences: UserPreferences = preferencesResult.rows.length > 0
        ? {
            theme: preferencesResult.rows[0].theme || 'auto',
            language: preferencesResult.rows[0].language || 'en',
            notifications: Boolean(preferencesResult.rows[0].notifications),
            syncEnabled: Boolean(preferencesResult.rows[0].sync_enabled),
            autoSync: Boolean(preferencesResult.rows[0].auto_sync),
            syncInterval: parseInt(preferencesResult.rows[0].sync_interval) || 30,
            wifiOnly: Boolean(preferencesResult.rows[0].wifi_only)
          }
        : {
            theme: 'auto',
            language: 'en',
            notifications: true,
            syncEnabled: true,
            autoSync: true,
            syncInterval: 30,
            wifiOnly: false
          };

      const profile: UserProfile = {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.display_name,
        avatar: user.avatar_url,
        isVip: Boolean(user.is_vip),
        vipExpiresAt: user.vip_expires_at,
        skinUnlocks,
        preferences,
        created_at: user.created_at,
        updated_at: user.updated_at
      };

      return {
        success: true,
        data: profile
      };
    } catch (error) {
      console.error('[UserManagementService] Get user profile error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get user profile'
      };
    }
  }

  /**
   * 更新用户偏好设置
   */
  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<{ success: boolean; error?: string }> {
    try {
      const statements: InStatement[] = [];

      // 检查用户偏好记录是否存在
      const existingResult = await executeQuery({
        sql: 'SELECT id FROM user_preferences WHERE user_id = ?',
        args: [userId]
      });

      if (existingResult.rows.length === 0) {
        // 创建新的偏好记录
        statements.push({
          sql: `
            INSERT INTO user_preferences (
              user_id, theme, language, notifications, sync_enabled,
              auto_sync, sync_interval, wifi_only, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `,
          args: [
            userId,
            preferences.theme || 'auto',
            preferences.language || 'en',
            preferences.notifications !== undefined ? preferences.notifications : true,
            preferences.syncEnabled !== undefined ? preferences.syncEnabled : true,
            preferences.autoSync !== undefined ? preferences.autoSync : true,
            preferences.syncInterval || 30,
            preferences.wifiOnly !== undefined ? preferences.wifiOnly : false,
            new Date().toISOString(),
            new Date().toISOString()
          ]
        });
      } else {
        // 更新现有偏好记录
        const updateFields: string[] = [];
        const updateArgs: any[] = [];

        if (preferences.theme !== undefined) {
          updateFields.push('theme = ?');
          updateArgs.push(preferences.theme);
        }
        if (preferences.language !== undefined) {
          updateFields.push('language = ?');
          updateArgs.push(preferences.language);
        }
        if (preferences.notifications !== undefined) {
          updateFields.push('notifications = ?');
          updateArgs.push(preferences.notifications);
        }
        if (preferences.syncEnabled !== undefined) {
          updateFields.push('sync_enabled = ?');
          updateArgs.push(preferences.syncEnabled);
        }
        if (preferences.autoSync !== undefined) {
          updateFields.push('auto_sync = ?');
          updateArgs.push(preferences.autoSync);
        }
        if (preferences.syncInterval !== undefined) {
          updateFields.push('sync_interval = ?');
          updateArgs.push(preferences.syncInterval);
        }
        if (preferences.wifiOnly !== undefined) {
          updateFields.push('wifi_only = ?');
          updateArgs.push(preferences.wifiOnly);
        }

        if (updateFields.length > 0) {
          updateFields.push('updated_at = ?');
          updateArgs.push(new Date().toISOString());
          updateArgs.push(userId);

          statements.push({
            sql: `UPDATE user_preferences SET ${updateFields.join(', ')} WHERE user_id = ?`,
            args: updateArgs
          });
        }
      }

      if (statements.length > 0) {
        await batchStatements(statements);
      }

      return { success: true };
    } catch (error) {
      console.error('[UserManagementService] Update user preferences error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update user preferences'
      };
    }
  }

  /**
   * 获取VIP状态
   */
  async getVipStatus(userId: string): Promise<{ success: boolean; data?: VipStatus; error?: string }> {
    try {
      const userResult = await executeQuery({
        sql: 'SELECT is_vip, vip_expires_at FROM users WHERE id = ?',
        args: [userId]
      });

      if (!userResult.rows || userResult.rows.length === 0) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const user = userResult.rows[0];
      const isVip = Boolean(user.is_vip);
      const expiresAt = user.vip_expires_at;

      // 检查VIP是否过期
      const isExpired = expiresAt && new Date(expiresAt) < new Date().toISOString();
      const actualVipStatus = isVip && !isExpired;

      // 如果VIP已过期，更新数据库
      if (isVip && isExpired) {
        await executeQuery({
          sql: 'UPDATE users SET is_vip = 0, vip_expires_at = NULL WHERE id = ?',
          args: [userId]
        });
      }

      // 获取VIP解锁的皮肤
      const vipSkinsResult = await executeQuery({
        sql: `
          SELECT s.id
          FROM skins s
          WHERE s.unlock_condition = 'vip'
          AND (? = 1 OR EXISTS (
            SELECT 1 FROM skin_unlocks su
            WHERE su.user_id = ? AND su.skin_id = s.id
          ))
        `,
        args: [actualVipStatus ? 1 : 0, userId]
      });

      const unlockedSkins = vipSkinsResult.rows.map((row: any) => row.id);

      // VIP功能列表
      const vipFeatures = [
        'unlimited_mood_entries',
        'advanced_analytics',
        'premium_skins',
        'export_data',
        'priority_support',
        'ad_free_experience'
      ];

      const vipStatus: VipStatus = {
        isVip: actualVipStatus,
        expiresAt: actualVipStatus ? expiresAt : undefined,
        features: actualVipStatus ? vipFeatures : [],
        unlockedSkins
      };

      return {
        success: true,
        data: vipStatus
      };
    } catch (error) {
      console.error('[UserManagementService] Get VIP status error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get VIP status'
      };
    }
  }

  /**
   * 解锁皮肤
   */
  async unlockSkin(userId: string, skinId: string, unlockMethod: string, transactionId?: string): Promise<{ success: boolean; error?: string }> {
    try {
      // 检查皮肤是否已解锁
      const existingResult = await executeQuery({
        sql: 'SELECT id FROM user_skin_unlocks WHERE user_id = ? AND skin_id = ?',
        args: [userId, skinId]
      });

      if (existingResult.rows.length > 0) {
        return {
          success: false,
          error: 'Skin already unlocked'
        };
      }

      // 验证皮肤存在
      const skinResult = await executeQuery({
        sql: 'SELECT id, unlock_condition FROM skins WHERE id = ?',
        args: [skinId]
      });

      if (!skinResult.rows || skinResult.rows.length === 0) {
        return {
          success: false,
          error: 'Skin not found'
        };
      }

      const skin = skinResult.rows[0];

      // 验证解锁条件
      if (skin.unlock_condition === 'vip') {
        const vipStatus = await this.getVipStatus(userId);
        if (!vipStatus.success || !vipStatus.data?.isVip) {
          return {
            success: false,
            error: 'VIP status required to unlock this skin'
          };
        }
      }

      // 创建解锁记录
      const unlockId = `unlock_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      await executeQuery({
        sql: `
          INSERT INTO user_skin_unlocks (
            id, user_id, skin_id, unlocked_at, unlock_method, transaction_id, sync_status
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `,
        args: [
          unlockId,
          userId,
          skinId,
          new Date().toISOString(),
          unlockMethod,
          transactionId || null,
          'synced'
        ]
      });

      return { success: true };
    } catch (error) {
      console.error('[UserManagementService] Unlock skin error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to unlock skin'
      };
    }
  }

  /**
   * 获取用户解锁的皮肤列表
   */
  async getUserUnlockedSkins(userId: string): Promise<{ success: boolean; data?: string[]; error?: string }> {
    try {
      const result = await executeQuery({
        sql: `
          SELECT DISTINCT s.id
          FROM skins s
          LEFT JOIN user_skin_unlocks su ON s.id = su.skin_id AND su.user_id = ?
          WHERE s.is_unlocked = 1
          OR su.id IS NOT NULL
          OR (s.is_premium = 1 AND EXISTS (
            SELECT 1 FROM users u
            WHERE u.id = ? AND u.is_vip = 1
            AND (u.vip_expires_at IS NULL OR u.vip_expires_at > datetime('now'))
          ))
        `,
        args: [userId, userId]
      });

      const unlockedSkins = result.rows.map((row: any) => row.id);

      return {
        success: true,
        data: unlockedSkins
      };
    } catch (error) {
      console.error('[UserManagementService] Get unlocked skins error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get unlocked skins'
      };
    }
  }
}
