/**
 * 用户Quiz偏好配置 Service
 * 提供Quiz系统个性化配置的业务逻辑层
 */

import { BaseService } from '../base/BaseService';
import { UserQuizPreferencesRepository } from './UserQuizPreferencesRepository';
import type {
  UserQuizPreferences,
  CreateUserQuizPreferencesInput,
  UpdateUserQuizPreferencesInput
} from '../../types/schema';
import type { ServiceResult } from '../types/ServiceTypes';

export class UserQuizPreferencesService extends BaseService<UserQuizPreferences, CreateUserQuizPreferencesInput, UpdateUserQuizPreferencesInput> {
  constructor(db?: any) {
    const repository = new UserQuizPreferencesRepository('user_presentation_configs', db);
    super(repository);
  }

  // 获取Repository实例的便捷方法
  private get userPrefsRepository(): UserQuizPreferencesRepository {
    return this.repository as UserQuizPreferencesRepository;
  }

  /**
   * 获取用户的Quiz偏好配置
   */
  async getUserPreferences(userId: string, configName: string = 'default'): Promise<ServiceResult<UserQuizPreferences>> {
    try {
      const preferences = await this.userPrefsRepository.getUserPreferences(userId, configName);
      
      if (!preferences) {
        // 如果没有配置，创建默认配置
        const defaultPreferences = await this.createDefaultPreferences(userId, configName);
        if (defaultPreferences) {
          return this.createSuccessResponse(defaultPreferences);
        }
        return this.createErrorResponse('Failed to create default preferences');
      }

      return this.createSuccessResponse(preferences);
    } catch (error) {
      console.error('Error in getUserPreferences:', error);
      return this.createErrorResponse('Failed to get user preferences');
    }
  }

  /**
   * 获取用户的默认Quiz偏好配置
   */
  async getDefaultUserPreferences(userId: string): Promise<ServiceResult<UserQuizPreferences>> {
    try {
      const preferences = await this.userPrefsRepository.getDefaultUserPreferences(userId);
      
      if (!preferences) {
        // 创建默认配置
        const defaultPreferences = await this.createDefaultPreferences(userId, 'default', true);
        if (defaultPreferences) {
          return this.createSuccessResponse(defaultPreferences);
        }
        return this.createErrorResponse('Failed to create default preferences');
      }

      return this.createSuccessResponse(preferences);
    } catch (error) {
      console.error('Error in getDefaultUserPreferences:', error);
      return this.createErrorResponse('Failed to get default user preferences');
    }
  }

  /**
   * 更新用户Quiz偏好配置
   */
  async updateUserPreferences(
    userId: string, 
    configName: string, 
    updates: UpdateUserQuizPreferencesInput
  ): Promise<ServiceResult<UserQuizPreferences>> {
    try {
      // 验证更新数据
      this.validateUpdateData(updates);

      const updatedPreferences = await this.userPrefsRepository.updateUserPreferences(userId, configName, updates);
      
      if (!updatedPreferences) {
        return this.createErrorResponse('Failed to update user preferences');
      }

      return this.createSuccessResponse(updatedPreferences);
    } catch (error) {
      console.error('Error in updateUserPreferences:', error);
      return this.createErrorResponse(error instanceof Error ? error.message : 'Failed to update user preferences');
    }
  }

  /**
   * 创建用户Quiz偏好配置
   */
  async create(data: CreateUserQuizPreferencesInput): Promise<ServiceResult<UserQuizPreferences>> {
    try {
      // 验证创建数据
      this.validateCreateData(data);

      // 设置默认值
      const preferencesData: CreateUserQuizPreferencesInput = {
        ...data,
        id: data.id || this.generateId(),
        config_version: data.config_version || '2.0',
        personalization_level: data.personalization_level ?? 50,
        is_active: data.is_active ?? true,
        is_default: data.is_default ?? false
      };

      const preferences = await this.userPrefsRepository.createUserPreferences(preferencesData);
      
      if (!preferences) {
        return this.createErrorResponse('Failed to create user preferences');
      }

      return this.createSuccessResponse(preferences);
    } catch (error) {
      console.error('Error in create:', error);
      return this.createErrorResponse(error instanceof Error ? error.message : 'Failed to create user preferences');
    }
  }

  /**
   * 创建默认偏好配置
   */
  private async createDefaultPreferences(
    userId: string, 
    configName: string, 
    isDefault: boolean = false
  ): Promise<UserQuizPreferences | null> {
    try {
      const defaultPreferencesData: CreateUserQuizPreferencesInput = {
        id: this.generateId(),
        user_id: userId,
        config_name: configName,
        presentation_config: this.getDefaultPresentationConfig(),
        config_version: '2.0',
        personalization_level: 50,
        is_active: true,
        is_default: isDefault
      };

      return await this.userPrefsRepository.createUserPreferences(defaultPreferencesData);
    } catch (error) {
      console.error('Error creating default preferences:', error);
      return null;
    }
  }

  /**
   * 重置用户偏好为默认值
   */
  async resetToDefault(userId: string, configName: string = 'default'): Promise<ServiceResult<UserQuizPreferences>> {
    try {
      const defaultUpdates: UpdateUserQuizPreferencesInput = {
        presentation_config: this.getDefaultPresentationConfig(),
        personalization_level: 50,
        config_version: '2.0'
      };

      return await this.updateUserPreferences(userId, configName, defaultUpdates);
    } catch (error) {
      console.error('Error in resetToDefault:', error);
      return this.createErrorResponse('Failed to reset preferences to default');
    }
  }

  /**
   * 更新个性化级别
   */
  async updatePersonalizationLevel(
    userId: string, 
    configName: string, 
    level: number
  ): Promise<ServiceResult<UserQuizPreferences>> {
    try {
      if (level < 0 || level > 100) {
        return this.createErrorResponse('Personalization level must be between 0 and 100');
      }

      const updates: UpdateUserQuizPreferencesInput = {
        personalization_level: level
      };

      return await this.updateUserPreferences(userId, configName, updates);
    } catch (error) {
      console.error('Error in updatePersonalizationLevel:', error);
      return this.createErrorResponse('Failed to update personalization level');
    }
  }

  /**
   * 获取默认展现配置
   */
  private getDefaultPresentationConfig(): string {
    return JSON.stringify({
      layer0_dataset_presentation: {
        preferred_pack_categories: ['daily', 'assessment'],
        default_difficulty_preference: 'regular',
        session_length_preference: 'medium',
        auto_select_recommended: false,
        restore_progress: true,
        question_display_fields: {
          show_question_text: true,
          show_question_description: true,
          show_question_order: true,
          show_progress_indicator: true,
          show_answer_options: true,
          show_option_descriptions: false,
          show_option_icons: true
        },
        interaction_behavior: {
          auto_advance_after_selection: false,
          auto_advance_delay_ms: 1500,
          allow_answer_change: true,
          show_confirmation_dialog: false
        }
      },
      layer1_user_choice: {
        preferred_view_type: 'wheel',
        active_skin_id: 'default',
        color_mode: 'warm',
        user_level: 'regular'
      },
      layer2_rendering_strategy: {
        render_engine_preferences: {
          wheel: 'D3',
          card: 'SVG',
          bubble: 'Canvas'
        },
        content_display_mode_preferences: {
          wheel: ['text', 'emoji'],
          card: ['text', 'image'],
          bubble: ['emoji']
        },
        layout_preferences: {
          wheel: 'circular_balanced',
          card: 'grid_responsive',
          bubble: 'organic_flow'
        },
        performance_mode: 'balanced',
        supported_content_types: {
          text: true,
          emoji: true,
          image: true,
          icon: true,
          audio: false,
          video: false,
          animation: true,
          rich_text: false
        }
      },
      layer3_skin_base: {
        available_skins: [
          { id: 'default', name: '默认皮肤', description: '简洁现代的默认界面风格', category: 'basic' }
        ],
        selected_skin_id: 'default',
        colors: {
          primary: '#4F46E5',
          secondary: '#7C3AED',
          accent: '#F59E0B'
        },
        fonts: {
          primary_font: 'Inter',
          size_scale: 1.0
        },
        animations: {
          enable_animations: true,
          animation_speed: 'normal',
          reduce_motion: false
        }
      },
      layer4_view_detail: {
        wheel_config: {
          container_size: 400,
          wheel_radius: 180,
          emotion_display_mode: 'hierarchical',
          tier_spacing: 60,
          center_radius: 40,
          show_labels: true,
          show_emojis: true
        },
        emotion_presentation: {
          emotion_grouping_style: 'by_category',
          tier_transition_animation: 'fade'
        }
      },
      layer5_accessibility: {
        high_contrast: false,
        large_text: false,
        reduce_motion: false,
        keyboard_navigation: true,
        voice_guidance: false
      }
    });
  }

  /**
   * 验证展现配置
   */
  private isValidPresentationConfig(config: string): boolean {
    try {
      const parsed = JSON.parse(config);
      return (
        typeof parsed === 'object' &&
        parsed.layer1_user_choice &&
        typeof parsed.layer1_user_choice.preferred_view_type === 'string'
      );
    } catch {
      return false;
    }
  }

  /**
   * 验证创建数据
   */
  private validateCreateData(data: CreateUserQuizPreferencesInput): void {
    if (!data.user_id) {
      throw new Error('User ID is required');
    }
    if (!data.config_name) {
      throw new Error('Config name is required');
    }
    if (!data.presentation_config) {
      throw new Error('Presentation config is required');
    }
    if (!this.isValidPresentationConfig(data.presentation_config)) {
      throw new Error('Invalid presentation config format');
    }
    if (data.personalization_level !== undefined && (data.personalization_level < 0 || data.personalization_level > 100)) {
      throw new Error('Personalization level must be between 0 and 100');
    }
  }

  /**
   * 验证更新数据
   */
  private validateUpdateData(data: UpdateUserQuizPreferencesInput): void {
    if (data.presentation_config && !this.isValidPresentationConfig(data.presentation_config)) {
      throw new Error('Invalid presentation config format');
    }
    if (data.personalization_level !== undefined && (data.personalization_level < 0 || data.personalization_level > 100)) {
      throw new Error('Personalization level must be between 0 and 100');
    }
  }

  /**
   * 生成ID
   */
  private generateId(): string {
    return `quiz_pref_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 创建成功响应
   */
  private createSuccessResponse<T>(data: T): ServiceResult<T> {
    return {
      success: true,
      data
    };
  }

  /**
   * 创建错误响应
   */
  private createErrorResponse(error: string): ServiceResult<UserQuizPreferences> {
    return {
      success: false,
      error
    };
  }

  /**
   * 获取Repository实例（用于测试）
   */
  getRepository(): UserQuizPreferencesRepository {
    return this.userPrefsRepository;
  }

  /**
   * 设置数据库连接
   */
  setDb(db: any): void {
    this.repository.setDatabase(db);
  }

  /**
   * 验证创建数据 (BaseService抽象方法实现)
   */
  protected async validateCreate(data: CreateUserQuizPreferencesInput): Promise<void> {
    this.validateCreateData(data);
  }

  /**
   * 验证更新数据 (BaseService抽象方法实现)
   */
  protected async validateUpdate(data: UpdateUserQuizPreferencesInput): Promise<void> {
    this.validateUpdateData(data);
  }
}
