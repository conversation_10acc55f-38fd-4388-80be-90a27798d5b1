import { Capacitor } from '@capacitor/core';
import type React from 'react';
import { createContext, useContext, useEffect, useState } from 'react';
import { DatabaseService } from '../services/base/DatabaseService';
import { Services } from '../services';

// 获取平台信息
export const platform = Capacitor.getPlatform();

// 数据库上下文接口
interface DatabaseContextType {
  databaseService: DatabaseService;
  isInitialized: boolean;
  isInitializing: boolean;
  error: string | null;
  platform: string;
}

// 创建数据库上下文
const DatabaseContext = createContext<DatabaseContextType | undefined>(undefined);

// 数据库 Provider 组件
export const DatabaseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [databaseService] = useState(() => DatabaseService.getInstance());

  useEffect(() => {
    console.log('[DatabaseProvider] Initializing database service...');

    const initializeDatabase = async () => {
      try {
        setIsInitializing(true);
        setError(null);

        // 初始化应用
        const success = await databaseService.initializeApp();

        if (success) {
          // Set database connection on ServiceFactory
          const dbConnection = await databaseService.getConnection();
          console.log('[DatabaseProvider] Got database connection:', !!dbConnection);
          Services.setDatabase(dbConnection);
          console.log('[DatabaseProvider] Database connection set on ServiceFactory');

          // Test the connection
          try {
            const healthCheck = await Services.healthCheck();
            console.log('[DatabaseProvider] ServiceFactory health check:', healthCheck);
          } catch (error) {
            console.error('[DatabaseProvider] ServiceFactory health check failed:', error);
          }

          setIsInitialized(true);
          console.log('[DatabaseProvider] Database initialization completed successfully');
        } else {
          throw new Error('Database initialization failed');
        }
      } catch (err: any) {
        console.error('[DatabaseProvider] Database initialization error:', err);
        setError(err.message || 'Database initialization failed');
        setIsInitialized(false);
      } finally {
        setIsInitializing(false);
      }
    };

    // 监听数据库服务的初始化状态
    const subscription = databaseService.isInitCompleted.subscribe((initialized) => {
      console.log('[DatabaseProvider] Database service initialization status:', initialized);
      setIsInitialized(initialized);
      if (initialized) {
        setIsInitializing(false);
        setError(null);
      }
    });

    // 开始初始化
    initializeDatabase();

    // 清理函数
    return () => {
      subscription.unsubscribe();
    };
  }, [databaseService]);

  const contextValue: DatabaseContextType = {
    databaseService,
    isInitialized,
    isInitializing,
    error,
    platform,
  };

  return <DatabaseContext.Provider value={contextValue}>{children}</DatabaseContext.Provider>;
};

// 使用数据库上下文的 Hook
export const useDatabaseContext = (): DatabaseContextType => {
  const context = useContext(DatabaseContext);
  if (context === undefined) {
    throw new Error('useDatabaseContext must be used within a DatabaseProvider');
  }
  return context;
};
