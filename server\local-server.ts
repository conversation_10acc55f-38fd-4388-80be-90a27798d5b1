import * as fs from 'node:fs';
import * as path from 'node:path';
import { createHTTPServer } from '@trpc/server/adapters/standalone';
import { DatabaseInitializer } from './lib/DatabaseInitializer.js';
import { appRouter } from './lib/localRouter.js';

// Port for the local server
const PORT = 8788;

// Get command line arguments
const args = process.argv.slice(2);
const showHelp = args.includes('--help') || args.includes('-h');
const forceReinit = args.includes('--force-reinit') || args.includes('-f');
const verbose = args.includes('--verbose') || args.includes('-v');
const logToFile = args.includes('--log-to-file') || args.includes('-l');

// Show help if requested
if (showHelp) {
  console.log(`
Local Server for Mindful Mood Application

Usage: node server/local-server.js [options]

Options:
  -h, --help           Show this help message
  -f, --force-reinit   Force database reinitialization even if it exists
  -v, --verbose        Show verbose output
  -l, --log-to-file    Log output to server-init.log file
  --force-start        Start server even if database initialization fails

Examples:
  node server/local-server.js                   # Start server normally
  node server/local-server.js --force-reinit    # Reinitialize database and start server
  node server/local-server.js -f -v -l          # Reinitialize with verbose logging to file
  `);
  process.exit(0);
}

// Setup logging
const logFile = 'server-init.log';
let logStream: fs.WriteStream | undefined;

if (logToFile) {
  logStream = fs.createWriteStream(logFile, { flags: 'w' });
  console.log(`Logging to file: ${logFile}`);
}

// Custom logger function
function log(message: string): void {
  console.log(message);
  if (logStream) {
    logStream.write(`${message}\n`);
  }
}

// Function to initialize the database
async function initializeDatabase(): Promise<boolean> {
  log('=== Database Initialization ===');

  // Path to the database file
  const dbPath = path.resolve(process.cwd(), 'local.db');
  log(`Database path: ${dbPath}`);

  // Path to the seeds directory
  const seedsDir = path.resolve(process.cwd(), 'public/seeds');
  log(`Seeds directory: ${seedsDir}`);

  // Check if seeds directory exists
  if (!fs.existsSync(seedsDir)) {
    log(`ERROR: Seeds directory not found at ${seedsDir}`);
    return false;
  }

  // Create a database initializer with options from command line
  log(`Initializing database (force=${forceReinit}, verbose=${verbose})`);
  const initializer = new DatabaseInitializer(dbPath, seedsDir, forceReinit, log);

  try {
    // Initialize the database
    const success = await initializer.initialize();

    if (success) {
      log('Database initialization completed successfully');
    } else {
      log('Database initialization completed with warnings');
    }

    return success;
  } catch (error) {
    log(
      `CRITICAL ERROR during database initialization: ${error instanceof Error ? error.message : String(error)}`
    );
    if (error instanceof Error && error.stack) {
      log(error.stack);
    }
    return false;
  }
}

// Start the server
async function startServer() {
  // Initialize the database first
  const dbInitSuccess = await initializeDatabase();

  if (!dbInitSuccess) {
    log('WARNING: Database initialization had issues. Server may not function correctly.');

    // Ask user if they want to continue
    if (!args.includes('--force-start')) {
      log('Use --force-start flag to start the server despite database initialization issues.');
      log('Exiting...');
      process.exit(1);
    }

    log('Continuing with server startup despite database issues (--force-start)');
  }

  // Create the HTTP server
  const server = createHTTPServer({
    router: appRouter,
    createContext: () => ({}),
  });

  // Start listening
  server.listen(PORT);

  log(`\n🚀 Local tRPC server running at http://localhost:${PORT}/trpc`);
  log('Available routes:');
  log(`- http://localhost:${PORT}/trpc/query`);
  log(`- http://localhost:${PORT}/trpc/batch`);
  log(`- http://localhost:${PORT}/trpc/executeScript`);
  log(`- http://localhost:${PORT}/trpc/fetchTable`);
  log(`- http://localhost:${PORT}/trpc/fetchTableWithLimit`);
  log('\nPress Ctrl+C to stop the server');

  // Close log file stream if it exists
  if (logStream) {
    logStream.end();
  }
}

// Handle errors
process.on('uncaughtException', (error) => {
  log(`UNCAUGHT EXCEPTION: ${error instanceof Error ? error.message : String(error)}`);
  if (error instanceof Error && error.stack) {
    log(error.stack);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  log(
    `UNHANDLED REJECTION at: ${promise}, reason: ${reason instanceof Error ? reason.message : String(reason)}`
  );
  if (reason instanceof Error && reason.stack) {
    log(reason.stack);
  }
});

// Start the server
startServer().catch((error) => {
  log(`FAILED TO START SERVER: ${error instanceof Error ? error.message : String(error)}`);
  if (error instanceof Error && error.stack) {
    log(error.stack);
  }
  process.exit(1);
});
