/**
 * SyncService 单元测试
 * 验证扩展后的同步功能实现
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SyncService } from '../SyncService';

// Mock 数据库操作
vi.mock('../../database/index', () => ({
  executeQuery: vi.fn(),
  batchStatements: vi.fn()
}));

// 导入 Mock 后的模块
import { executeQuery, batchStatements } from '../../database/index';

const mockExecuteQuery = vi.mocked(executeQuery);
const mockBatchStatements = vi.mocked(batchStatements);

describe('SyncService', () => {
  let syncService: SyncService;

  beforeEach(() => {
    syncService = SyncService.getInstance();
    vi.clearAllMocks();
  });

  describe('performFullSync', () => {
    it('should perform full sync successfully', async () => {
      const syncRequest = {
        userId: 'user123',
        lastSyncTimestamp: '2024-01-01T00:00:00Z',
        moodEntriesToUpload: [
          {
            id: 'mood1',
            user_id: 'user123',
            timestamp: '2024-01-01T12:00:00Z',
            emotion_data_set_id: 'emotion_wheel',
            intensity: 7,
            reflection: 'Feeling good today',
            tags: ['happy', 'productive'],
            created_at: '2024-01-01T12:00:00Z',
            updated_at: '2024-01-01T12:00:00Z'
          }
        ],
        quizSessionsToUpload: [
          {
            id: 'session1',
            pack_id: 'pack1',
            user_id: 'user123',
            status: 'COMPLETED' as const,
            current_question_index: 5,
            total_questions: 5,
            answered_questions: 5,
            skipped_questions: 0,
            completion_percentage: 100,
            start_time: '2024-01-01T10:00:00Z',
            last_active_time: '2024-01-01T10:05:00Z',
            end_time: '2024-01-01T10:05:00Z',
            session_type: 'standard',
            session_metadata: '{}',
            created_at: '2024-01-01T10:00:00Z',
            updated_at: '2024-01-01T10:05:00Z'
          }
        ],
        emotionSelectionsToUpload: [],
        userConfigsToUpload: [],
        tagsToUpload: []
      };

      // Mock 批量上传成功
      mockBatchStatements.mockResolvedValue([{
        rows: [],
        changes: 2,
        lastInsertRowid: 0
      }]);

      // Mock 下载数据查询
      mockExecuteQuery
        // 心情记录查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // 情绪选择查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // 用户配置查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // 标签查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // Quiz会话查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // Quiz答案查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // Quiz结果查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // 支付交易查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // 订阅历史查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // 皮肤解锁查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // 表情集解锁查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        })
        // 个性化配置查询
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        });

      const result = await syncService.performFullSync(syncRequest);

      expect(result.success).toBe(true);
      expect(result.uploadedCount).toBe(2); // 1 mood entry + 1 quiz session
      expect(result.downloadedCount).toBe(0);
      expect(result.newMoodEntriesFromServer).toEqual([]);
      expect(result.newQuizSessionsFromServer).toEqual([]);
      expect(result.conflicts).toEqual([]);

      // 验证批量上传被调用
      expect(mockBatchStatements).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            sql: expect.stringContaining('INSERT INTO mood_entries'),
            args: expect.arrayContaining(['mood1', 'user123'])
          }),
          expect.objectContaining({
            sql: expect.stringContaining('INSERT INTO quiz_sessions'),
            args: expect.arrayContaining(['session1', 'pack1', 'user123'])
          })
        ])
      );
    });

    it('should handle sync errors gracefully', async () => {
      const syncRequest = {
        userId: 'user123',
        moodEntriesToUpload: [],
        emotionSelectionsToUpload: [],
        userConfigsToUpload: [],
        tagsToUpload: []
      };

      // Mock 数据库错误
      mockExecuteQuery.mockRejectedValue(new Error('Database connection failed'));

      const result = await syncService.performFullSync(syncRequest);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database connection failed');
      expect(result.uploadedCount).toBe(0);
      expect(result.downloadedCount).toBe(0);
    });
  });

  describe('performIncrementalSync', () => {
    it('should perform incremental sync successfully', async () => {
      const userId = 'user123';
      const lastSyncTimestamp = '2024-01-01T00:00:00Z';

      // Mock 所有下载查询返回空结果
      mockExecuteQuery.mockResolvedValue({
        rows: [],
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await syncService.performIncrementalSync(userId, lastSyncTimestamp);

      expect(result.success).toBe(true);
      expect(result.uploadedCount).toBe(0); // 增量同步不上传
      expect(result.downloadedCount).toBe(0);
      expect(result.conflicts).toEqual([]);

      // 验证查询包含时间过滤
      expect(mockExecuteQuery).toHaveBeenCalledWith({
        sql: expect.stringContaining('WHERE user_id = ? AND updated_at > ?'),
        args: [userId, lastSyncTimestamp]
      });
    });

    it('should download new data in incremental sync', async () => {
      const userId = 'user123';
      const lastSyncTimestamp = '2024-01-01T00:00:00Z';

      // Mock 返回新的Quiz会话数据
      mockExecuteQuery
        .mockResolvedValueOnce({ rows: [], changes: 0, lastInsertRowid: 0 }) // mood_entries
        .mockResolvedValueOnce({ rows: [], changes: 0, lastInsertRowid: 0 }) // user_configs
        .mockResolvedValueOnce({ rows: [], changes: 0, lastInsertRowid: 0 }) // tags
        .mockResolvedValueOnce({ // quiz_sessions
          rows: [{
            id: 'session2',
            pack_id: 'pack1',
            user_id: 'user123',
            status: 'IN_PROGRESS',
            current_question_index: 2,
            total_questions: 5,
            answered_questions: 2,
            skipped_questions: 0,
            completion_percentage: 40,
            start_time: '2024-01-02T10:00:00Z',
            last_active_time: '2024-01-02T10:02:00Z',
            end_time: null,
            session_type: 'standard',
            session_metadata: '{}',
            created_at: '2024-01-02T10:00:00Z',
            updated_at: '2024-01-02T10:02:00Z'
          }],
          changes: 0,
          lastInsertRowid: 0
        })
        .mockResolvedValue({ rows: [], changes: 0, lastInsertRowid: 0 }); // 其他查询

      const result = await syncService.performIncrementalSync(userId, lastSyncTimestamp);

      expect(result.success).toBe(true);
      expect(result.downloadedCount).toBe(1);
      expect(result.newQuizSessionsFromServer).toHaveLength(1);
      expect(result.newQuizSessionsFromServer[0].id).toBe('session2');
    });
  });

  describe('resolveConflicts', () => {
    it('should resolve conflicts with client_wins strategy', async () => {
      const userId = 'user123';
      const conflicts = [
        {
          type: 'mood_entry' as const,
          localData: { id: 'mood1', updated_at: '2024-01-02T00:00:00Z' },
          serverData: { id: 'mood1', updated_at: '2024-01-01T00:00:00Z' },
          conflictReason: 'Different update timestamps'
        }
      ];

      mockBatchStatements.mockResolvedValue([{
        rows: [],
        changes: 1,
        lastInsertRowid: 0
      }]);

      const result = await syncService.resolveConflicts(userId, conflicts, 'client_wins');

      expect(result.success).toBe(true);
      expect(result.resolvedCount).toBe(1);

      // 验证更新语句被调用
      expect(mockBatchStatements).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            sql: expect.stringContaining('UPDATE mood_entries'),
            args: expect.arrayContaining(['mood1'])
          })
        ])
      );
    });

    it('should resolve conflicts with server_wins strategy', async () => {
      const userId = 'user123';
      const conflicts = [
        {
          type: 'quiz_session' as const,
          localData: { id: 'session1', updated_at: '2024-01-01T00:00:00Z' },
          serverData: { id: 'session1', updated_at: '2024-01-02T00:00:00Z' },
          conflictReason: 'Session state conflict'
        }
      ];

      const result = await syncService.resolveConflicts(userId, conflicts, 'server_wins');

      expect(result.success).toBe(true);
      expect(result.resolvedCount).toBe(1);

      // server_wins 策略不需要数据库操作
      expect(mockBatchStatements).not.toHaveBeenCalled();
    });
  });

  describe('getSyncStatistics', () => {
    it('should return sync statistics successfully', async () => {
      const userId = 'user123';

      // Mock 统计查询
      mockExecuteQuery
        .mockResolvedValueOnce({ // mood entries count
          rows: [{ count: 25 }],
          changes: 0,
          lastInsertRowid: 0
        })
        .mockResolvedValueOnce({ // quiz sessions count
          rows: [{ count: 5 }],
          changes: 0,
          lastInsertRowid: 0
        })
        .mockResolvedValueOnce({ // payment transactions count
          rows: [{ count: 2 }],
          changes: 0,
          lastInsertRowid: 0
        })
        .mockResolvedValueOnce({ // last sync timestamp
          rows: [{ last_sync: '2024-01-02T12:00:00Z' }],
          changes: 0,
          lastInsertRowid: 0
        });

      const result = await syncService.getSyncStatistics(userId);

      expect(result.success).toBe(true);
      expect(result.statistics).toEqual({
        totalMoodEntries: 25,
        totalQuizSessions: 5,
        totalPaymentTransactions: 2,
        lastSyncTimestamp: '2024-01-02T12:00:00Z',
        syncFrequency: 1
      });
    });

    it('should handle statistics query errors', async () => {
      const userId = 'user123';

      mockExecuteQuery.mockRejectedValue(new Error('Statistics query failed'));

      const result = await syncService.getSyncStatistics(userId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Statistics query failed');
      expect(result.statistics).toBeUndefined();
    });
  });
});
