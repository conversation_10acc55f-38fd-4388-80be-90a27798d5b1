# Mindful Mood Server Environment Variables

# Database Configuration
DATABASE_URL=./data/mindful_mood.db
DB_TYPE=sqlite

# Stripe Payment Configuration
# Get these from your Stripe Dashboard: https://dashboard.stripe.com/apikeys
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Stripe Webhook Configuration
# Create a webhook endpoint in Stripe Dashboard and copy the signing secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Return URL (for 3D Secure and other redirects)
STRIPE_RETURN_URL=https://your-app-domain.com/payment/return

# VIP Plan Price IDs (create these in Stripe Dashboard)
STRIPE_VIP_MONTHLY_PRICE_ID=price_your_monthly_price_id
STRIPE_VIP_YEARLY_PRICE_ID=price_your_yearly_price_id

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081

# Security
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>

# Analytics (optional)
ANALYTICS_ENABLED=false
ANALYTICS_API_KEY=your_analytics_key

# Feature Flags
ENABLE_VIP_FEATURES=true
ENABLE_SKIN_PURCHASES=true
ENABLE_EMOJI_SET_PURCHASES=true
ENABLE_OFFLINE_SYNC=true

# Development/Testing
MOCK_PAYMENTS=false
SKIP_EMAIL_VERIFICATION=false
DEBUG_MODE=false
