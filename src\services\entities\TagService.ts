/**
 * 标签服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 */

import { BaseService } from '../base/BaseService';
import { BaseRepository } from '../base/BaseRepository';
import { Tag } from '../../types/schema/base';
import { CreateTagInput, UpdateTagInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

// 简化的TagRepository实现
class TagRepository extends BaseRepository<Tag, CreateTagInput, UpdateTagInput> {
  constructor(db?: SQLiteDBConnection) {
    super('tags', db);
  }

  async findByCategory(_category: string): Promise<Tag[]> {
    // 由于数据库中没有category字段，返回所有标签
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} ORDER BY name ASC`;
    const result = await db.query(query);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  async findSystemTags(): Promise<Tag[]> {
    // 由于数据库中没有is_system字段，返回所有标签
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} ORDER BY name ASC`;
    const result = await db.query(query);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  async findUserTags(_userId: string): Promise<Tag[]> {
    // 由于数据库中没有created_by字段，返回所有标签
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} ORDER BY name ASC`;
    const result = await db.query(query);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  async searchTags(searchTerm: string): Promise<Tag[]> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE (name LIKE ? OR description LIKE ?) ORDER BY name ASC`;
    const searchPattern = `%${searchTerm}%`;
    const result = await db.query(query, [searchPattern, searchPattern]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  protected mapRowToEntity(row: any): Tag {
    return {
      id: row.id,
      name: row.name,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  protected mapEntityToRow(entity: Partial<Tag>): Record<string, any> {
    return {
      id: entity.id,
      name: entity.name,
      created_at: entity.created_at,
      updated_at: entity.updated_at
    };
  }

  protected buildInsertQuery(data: CreateTagInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const tagId = `tag_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const query = `
      INSERT INTO ${this.tableName} (
        id, name, description, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?)
    `;

    const values = [
      tagId, data.name, data.description || null, now, now
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateTagInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      fields.push('name = ?');
      values.push(data.name);
    }

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  protected buildSelectQuery(_filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];

    query += ' ORDER BY name ASC';
    return { query, values };
  }

  protected buildCountQuery(_filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];

    return { query, values };
  }

  protected extractIdFromCreateData(_data: CreateTagInput): string {
    return `tag_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

export class TagService extends BaseService<Tag, CreateTagInput, UpdateTagInput> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new TagRepository(db);
    super(repository);
  }

  async createTag(input: CreateTagInput): Promise<ServiceResult<Tag>> {
    try {
      await this.validateCreate(input);
      const tag = await this.repository.create(input);
      this.emit('tagCreated', tag);
      return this.createSuccessResult(tag);
    } catch (error) {
      return this.createErrorResult('Failed to create tag', error);
    }
  }

  async getTagsByCategory(category: string): Promise<ServiceResult<Tag[]>> {
    try {
      const tags = await (this.repository as TagRepository).findByCategory(category);
      return this.createSuccessResult(tags);
    } catch (error) {
      return this.createErrorResult('Failed to get tags by category', error);
    }
  }

  async getSystemTags(): Promise<ServiceResult<Tag[]>> {
    try {
      const tags = await (this.repository as TagRepository).findSystemTags();
      return this.createSuccessResult(tags);
    } catch (error) {
      return this.createErrorResult('Failed to get system tags', error);
    }
  }

  async getUserTags(userId: string): Promise<ServiceResult<Tag[]>> {
    try {
      const tags = await (this.repository as TagRepository).findUserTags(userId);
      return this.createSuccessResult(tags);
    } catch (error) {
      return this.createErrorResult('Failed to get user tags', error);
    }
  }

  async searchTags(searchTerm: string): Promise<ServiceResult<Tag[]>> {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new Error('Search term must be at least 2 characters long');
      }
      const tags = await (this.repository as TagRepository).searchTags(searchTerm.trim());
      return this.createSuccessResult(tags);
    } catch (error) {
      return this.createErrorResult('Failed to search tags', error);
    }
  }

  async updateTag(tagId: string, updates: UpdateTagInput): Promise<ServiceResult<Tag>> {
    try {
      await this.validateUpdate(updates);
      const tag = await this.repository.update(tagId, updates);
      this.emit('tagUpdated', tag);
      return this.createSuccessResult(tag);
    } catch (error) {
      return this.createErrorResult('Failed to update tag', error);
    }
  }

  protected async validateCreate(data: CreateTagInput): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Tag name is required');
    }
    if (data.name.length > 50) {
      throw new Error('Tag name must be less than 50 characters');
    }
  }

  protected async validateUpdate(data: UpdateTagInput): Promise<void> {
    if (data.name !== undefined && (!data.name || data.name.trim().length === 0)) {
      throw new Error('Tag name cannot be empty');
    }
    if (data.name !== undefined && data.name.length > 50) {
      throw new Error('Tag name must be less than 50 characters');
    }
  }
}
