/**
 * 颜色对比度测试工具
 *
 * 这个文件提供了一组函数，用于测试颜色分配算法的对比度效果。
 * 它不是一个自动化测试文件，而是一个可以在应用程序中使用的工具，
 * 用于验证颜色分配的结果是否满足对比度要求。
 */

import {
  areColorsTooSimilar,
  assignColorsToEmotions,
  calculateContrastRatio,
} from './colorUtils';

import   {type ColorMode,
  type Emotion,
} from '@/types';

/**
 * 测试情绪颜色分配的对比度
 * @param emotions 情绪数组
 * @param isDarkMode 是否为深色模式
 * @param tier 情绪层级
 * @param colorMode 颜色模式
 * @returns 测试结果
 */
export function testEmotionColorContrast<T extends { id: string; name: string; color?: string }>(
  emotions: T[],
  isDarkMode: boolean,
  tier: 'primary' | 'secondary' | 'tertiary' = 'primary',
  colorMode: ColorMode = 'mixed'
): {
  success: boolean;
  averageContrast: number;
  minContrast: number;
  failedPairs: { emotion1: string; emotion2: string; contrast: number }[];
  colors: string[];
  tier: 'primary' | 'secondary' | 'tertiary';
} {
  // 分配颜色
  const emotionsWithColors = assignColorsToEmotions(emotions, isDarkMode, tier, colorMode);

  // 获取颜色数组
  const colors = emotionsWithColors.map((emotion) => emotion.color);

  // 检查相邻颜色的对比度
  const contrastResults: { emotion1: string; emotion2: string; contrast: number }[] = [];
  const failedPairs: { emotion1: string; emotion2: string; contrast: number }[] = [];

  for (let i = 0; i < colors.length; i++) {
    const currentColor = colors[i];
    const nextColor = colors[(i + 1) % colors.length]; // 循环到第一个
    const currentName = emotionsWithColors[i].name;
    const nextName = emotionsWithColors[(i + 1) % colors.length].name;

    // 计算对比度
    const contrastRatio = calculateContrastRatio(currentColor, nextColor);

    // 记录结果
    contrastResults.push({
      emotion1: currentName,
      emotion2: nextName,
      contrast: contrastRatio,
    });

    // 如果对比度太低或颜色太相似，记录失败
    // 根据tier调整对比度阈值
    let contrastThreshold = 1.5;
    if (tier === 'tertiary') {
      contrastThreshold = 1.8;
    } else if (tier === 'secondary') {
      contrastThreshold = 1.6;
    } else {
      contrastThreshold = 1.5;
    }

    // 检查对比度和颜色相似度
    if (contrastRatio < contrastThreshold) {
      failedPairs.push({
        emotion1: currentName,
        emotion2: nextName,
        contrast: contrastRatio,
      });
    }
  }

  // 计算平均对比度
  const totalContrast = contrastResults.reduce((sum, result) => sum + result.contrast, 0);
  const averageContrast = totalContrast / contrastResults.length;

  // 计算最小对比度
  const minContrast = Math.min(...contrastResults.map((result) => result.contrast));

  return {
    success: failedPairs.length === 0,
    averageContrast,
    minContrast,
    failedPairs,
    colors,
    tier, // 添加tier属性，用于在UI中显示正确的对比度阈值
  };
}

/**
 * 打印颜色对比度测试结果
 * @param result 测试结果
 */
export function printColorContrastTestResult(
  result: ReturnType<typeof testEmotionColorContrast>
): void {
  console.log('=== 颜色对比度测试结果 ===');
  console.log(`测试结果: ${result.success ? '通过 ✅' : '失败 ❌'}`);
  console.log(`平均对比度: ${result.averageContrast.to(2)}`);
  console.log(`最小对比度: ${result.minContrast.to(2)}`);

  if (result.failedPairs.length > 0) {
    console.log('\n失败的颜色对:');
    result.failedPairs.forEach((pair) => {
      console.log(`- ${pair.emotion1} 和 ${pair.emotion2}: 对比度 ${pair.contrast.to(2)}`);
    });
  }

  console.log('\n颜色列表:');
  result.colors.forEach((color) => {
    console.log(`- ${color}`);
  });
  console.log('========================');
}

/**
 * 运行所有颜色模式和主题的测试
 * @param emotions 情绪数组
 */
export function runAllColorContrastTests<T extends { id: string; name: string; color?: string }>(
  emotions: T[]
): void {
  const modes: ColorMode[] = ['warm', 'cool', 'mixed'];
  const themes = [false, true]; // false = light, true = dark
  const tiers: ('primary' | 'secondary' | 'tertiary')[] = ['primary', 'secondary', 'tertiary'];

  for (const mode of modes) {
    for (const isDark of themes) {
      for (const tier of tiers) {
        console.log(`\n测试: ${isDark ? '深色' : '浅色'}主题, ${mode}颜色模式, ${tier}层级`);
        const result = testEmotionColorContrast(emotions, isDark, tier, mode);
        printColorContrastTestResult(result);
      }
    }
  }
}

// 示例情绪数据，可以用于测试
export const sampleEmotions = [
  { id: 'emotion1', name: 'Happy' },
  { id: 'emotion2', name: 'Sad' },
  { id: 'emotion3', name: 'Angry' },
  { id: 'emotion4', name: 'Surprised' },
  { id: 'emotion5', name: 'Fearful' },
  { id: 'emotion6', name: 'Disgusted' },
  { id: 'emotion7', name: 'Neutral' },
];

// 少量情绪数据，用于测试边缘情况
export const fewEmotions = [
  { id: 'emotion1', name: 'Happy' },
  { id: 'emotion2', name: 'Sad' },
];
