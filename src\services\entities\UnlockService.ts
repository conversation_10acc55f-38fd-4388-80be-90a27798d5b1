/**
 * 内容解锁服务
 * 管理用户内容解锁的CRUD操作和业务逻辑
 * 支持皮肤、表情集、Quiz包等内容的解锁管理
 */

import { BaseService } from '../base/BaseService';
import { BaseRepository } from '../base/BaseRepository';
import type {
  ServiceResult
} from '../types/ServiceTypes';

// 通用解锁类型定义
export interface UserUnlock {
  id: string;
  user_id: string;
  content_type: string;
  content_id: string;
  unlock_method: 'purchase' | 'vip' | 'achievement' | 'gift' | 'promotion';
  unlocked_at: string;
  expires_at?: string;
  transaction_id?: string;
  promotion_code?: string;
  sync_status: 'pending' | 'synced' | 'failed';
  created_at: string;
  updated_at: string;
}

export interface UserUnlockCreate {
  id: string;
  user_id: string;
  content_type: string;
  content_id: string;
  unlock_method: 'purchase' | 'vip' | 'achievement' | 'gift' | 'promotion';
  unlocked_at: string;
  expires_at?: string;
  transaction_id?: string;
  promotion_code?: string;
  sync_status: 'pending' | 'synced' | 'failed';
}

export interface UserUnlockUpdate {
  sync_status?: 'pending' | 'synced' | 'failed';
  expires_at?: string;
  transaction_id?: string;
  promotion_code?: string;
}

export interface UnlockResult {
  success: boolean;
  alreadyUnlocked: boolean;
  unlock?: UserUnlock;
  message: string;
}

/**
 * 用户解锁仓储类
 */
export class UnlockRepository extends BaseRepository<UserUnlock, UserUnlockCreate, UserUnlockUpdate> {
  protected tableName = 'user_unlocks';
  protected primaryKey = 'id';

  constructor(tableName?: string, db?: any) {
    super(tableName || 'user_unlocks', db);
  }

  /**
   * 获取用户的所有解锁记录
   */
  async getUserUnlocks(userId: string): Promise<UserUnlock[]> {
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE user_id = ?
      ORDER BY unlocked_at DESC
    `;

    const result = await this.db.query(query, [userId]);
    return result.values?.map(row => this.mapRowToEntity(row)) || [];
  }

  /**
   * 检查特定内容是否已解锁
   */
  async isContentUnlocked(
    userId: string,
    contentType: string,
    contentId: string
  ): Promise<boolean> {
    const query = `
      SELECT COUNT(*) as count FROM ${this.tableName}
      WHERE user_id = ? AND content_type = ? AND content_id = ?
    `;

    const result = await this.db.query(query, [userId, contentType, contentId]);
    const count = result.values?.[0]?.count || 0;
    return count > 0;
  }

  /**
   * 获取用户已解锁的特定类型内容
   */
  async getUnlockedContentByType(
    userId: string,
    contentType: string
  ): Promise<UserUnlock[]> {
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE user_id = ? AND content_type = ?
      ORDER BY unlocked_at DESC
    `;

    const result = await this.db.query(query, [userId, contentType]);
    return result.values?.map(row => this.mapRowToEntity(row)) || [];
  }

  /**
   * 获取用户解锁统计
   */
  async getUserUnlockStats(userId: string): Promise<{
    totalUnlocks: number;
    skinUnlocks: number;
    emojiSetUnlocks: number;
    quizPackUnlocks: number;
    recentUnlocks: UserUnlock[];
  }> {
    // 总解锁数
    const totalQuery = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE user_id = ?`;
    const totalResult = await this.db.query(totalQuery, [userId]);
    const totalUnlocks = totalResult.values?.[0]?.count || 0;

    // 按类型统计
    const typeStatsQuery = `
      SELECT content_type, COUNT(*) as count
      FROM ${this.tableName}
      WHERE user_id = ?
      GROUP BY content_type
    `;
    const typeStatsResult = await this.db.query(typeStatsQuery, [userId]);
    
    let skinUnlocks = 0;
    let emojiSetUnlocks = 0;
    let quizPackUnlocks = 0;
    
    if (typeStatsResult.values) {
      for (const row of typeStatsResult.values) {
        switch (row.content_type) {
          case 'skin':
            skinUnlocks = row.count;
            break;
          case 'emoji_set':
            emojiSetUnlocks = row.count;
            break;
          case 'quiz_pack':
            quizPackUnlocks = row.count;
            break;
        }
      }
    }

    // 最近解锁
    const recentQuery = `
      SELECT * FROM ${this.tableName}
      WHERE user_id = ?
      ORDER BY unlocked_at DESC
      LIMIT 5
    `;
    const recentResult = await this.db.query(recentQuery, [userId]);
    const recentUnlocks = recentResult.values?.map(row => this.mapRowToEntity(row)) || [];

    return {
      totalUnlocks,
      skinUnlocks,
      emojiSetUnlocks,
      quizPackUnlocks,
      recentUnlocks
    };
  }

  protected mapRowToEntity(row: any): UserUnlock {
    return {
      id: row.id,
      user_id: row.user_id,
      content_type: row.content_type,
      content_id: row.content_id,
      unlock_method: row.unlock_method,
      unlocked_at: row.unlocked_at,
      expires_at: row.expires_at,
      transaction_id: row.transaction_id,
      promotion_code: row.promotion_code,
      sync_status: row.sync_status || 'synced',
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  protected mapEntityToRow(entity: Partial<UserUnlock>): Record<string, any> {
    return {
      id: entity.id,
      user_id: entity.user_id,
      content_type: entity.content_type,
      content_id: entity.content_id,
      unlock_method: entity.unlock_method,
      unlocked_at: entity.unlocked_at,
      expires_at: entity.expires_at,
      transaction_id: entity.transaction_id,
      promotion_code: entity.promotion_code,
      sync_status: entity.sync_status,
      created_at: entity.created_at,
      updated_at: entity.updated_at
    };
  }

  protected buildInsertQuery(data: UserUnlockCreate): { query: string; values: any[] } {
    const now = new Date().toISOString();

    const query = `
      INSERT INTO ${this.tableName} (
        id, user_id, content_type, content_id, unlock_method,
        unlocked_at, expires_at, transaction_id, promotion_code,
        sync_status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      data.id,
      data.user_id,
      data.content_type,
      data.content_id,
      data.unlock_method,
      data.unlocked_at,
      data.expires_at,
      data.transaction_id,
      data.promotion_code,
      data.sync_status,
      now,
      now
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UserUnlockUpdate): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.sync_status !== undefined) {
      fields.push('sync_status = ?');
      values.push(data.sync_status);
    }

    if (data.expires_at !== undefined) {
      fields.push('expires_at = ?');
      values.push(data.expires_at);
    }

    if (data.transaction_id !== undefined) {
      fields.push('transaction_id = ?');
      values.push(data.transaction_id);
    }

    if (data.promotion_code !== undefined) {
      fields.push('promotion_code = ?');
      values.push(data.promotion_code);
    }

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    values.push(id);

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    return { query, values };
  }

  protected extractIdFromCreateData(data: UserUnlockCreate): string {
    return data.id;
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];

    if (filters) {
      const conditions: string[] = [];

      if (filters.user_id) {
        conditions.push('user_id = ?');
        values.push(filters.user_id);
      }

      if (filters.content_type) {
        conditions.push('content_type = ?');
        values.push(filters.content_type);
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }
    }

    query += ' ORDER BY created_at DESC';

    if (filters?.limit) {
      query += ' LIMIT ?';
      values.push(filters.limit);
    }

    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];

    if (filters) {
      const conditions: string[] = [];

      if (filters.user_id) {
        conditions.push('user_id = ?');
        values.push(filters.user_id);
      }

      if (filters.content_type) {
        conditions.push('content_type = ?');
        values.push(filters.content_type);
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }
    }

    return { query, values };
  }
}

/**
 * 内容解锁服务类
 */
export class UnlockService extends BaseService<UserUnlock, UserUnlockCreate, UserUnlockUpdate> {
  constructor(db?: any) {
    const repository = new UnlockRepository('user_unlocks', db);
    super(repository);
  }

  // 获取Repository实例的便捷方法
  private get unlockRepository(): UnlockRepository {
    return this.repository as UnlockRepository;
  }

  /**
   * 解锁内容
   */
  async unlockContent(
    userId: string,
    contentType: string,
    contentId: string,
    unlockMethod: 'purchase' | 'vip' | 'achievement' | 'gift' | 'promotion' = 'purchase',
    transactionId?: string
  ): Promise<ServiceResult<UnlockResult>> {
    try {
      // 检查是否已经解锁
      const isAlreadyUnlocked = await this.unlockRepository.isContentUnlocked(
        userId,
        contentType,
        contentId
      );

      if (isAlreadyUnlocked) {
        return {
          success: true,
          data: {
            success: true,
            alreadyUnlocked: true,
            message: 'Content already unlocked'
          }
        };
      }

      // 验证解锁条件
      const canUnlock = await this.validateUnlockConditions(
        userId, 
        contentType, 
        contentId, 
        unlockMethod
      );

      if (!canUnlock.success || !canUnlock.data) {
        return {
          success: false,
          error: canUnlock.error || 'Unlock conditions not met'
        };
      }

      // 创建解锁记录
      const unlockData: UserUnlockCreate = {
        id: `unlock_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        user_id: userId,
        content_type: contentType,
        content_id: contentId,
        unlock_method: unlockMethod,
        transaction_id: transactionId,
        unlocked_at: new Date().toISOString(),
        sync_status: 'pending'
      };

      const unlock = await this.repository.create(unlockData);

      // 发射解锁事件
      this.emit('contentUnlocked', {
        userId,
        contentType,
        contentId,
        unlockMethod,
        unlock
      });

      return {
        success: true,
        data: {
          success: true,
          alreadyUnlocked: false,
          unlock,
          message: 'Content unlocked successfully'
        }
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to unlock content'
      };
    }
  }

  /**
   * 检查内容是否已解锁
   */
  async isContentUnlocked(
    userId: string,
    contentType: string,
    contentId: string
  ): Promise<ServiceResult<boolean>> {
    try {
      const isUnlocked = await this.unlockRepository.isContentUnlocked(
        userId,
        contentType,
        contentId
      );

      return {
        success: true,
        data: isUnlocked
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to check unlock status'
      };
    }
  }

  /**
   * 获取用户已解锁的内容
   */
  async getUserUnlockedContent(
    userId: string,
    contentType?: string
  ): Promise<ServiceResult<UserUnlock[]>> {
    try {
      let unlocks: UserUnlock[];
      if (contentType) {
        unlocks = await this.unlockRepository.getUnlockedContentByType(userId, contentType);
      } else {
        unlocks = await this.unlockRepository.getUserUnlocks(userId);
      }

      return {
        success: true,
        data: unlocks
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get unlocked content'
      };
    }
  }

  /**
   * 获取用户解锁统计
   */
  async getUserUnlockStats(userId: string): Promise<ServiceResult<{
    totalUnlocks: number;
    skinUnlocks: number;
    emojiSetUnlocks: number;
    quizPackUnlocks: number;
    recentUnlocks: UserUnlock[];
  }>> {
    try {
      const stats = await this.unlockRepository.getUserUnlockStats(userId);

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get unlock statistics'
      };
    }
  }

  /**
   * 批量解锁内容（VIP用户或促销活动）
   */
  async batchUnlockContent(
    userId: string,
    unlocks: Array<{
      contentType: string;
      contentId: string;
      unlockMethod?: string;
    }>,
    transactionId?: string
  ): Promise<ServiceResult<{
    successful: UnlockResult[];
    failed: Array<{ contentType: string; contentId: string; error: string }>;
  }>> {
    try {
      const successful: UnlockResult[] = [];
      const failed: Array<{ contentType: string; contentId: string; error: string }> = [];

      for (const unlock of unlocks) {
        const result = await this.unlockContent(
          userId,
          unlock.contentType,
          unlock.contentId,
          unlock.unlockMethod as any || 'vip',
          transactionId
        );

        if (result.success && result.data) {
          successful.push(result.data);
        } else {
          failed.push({
            contentType: unlock.contentType,
            contentId: unlock.contentId,
            error: result.error || 'Unknown error'
          });
        }
      }

      return this.createSuccessResult({ successful, failed });
    } catch (error) {
      return this.createErrorResult('Failed to batch unlock content', error);
    }
  }

  /**
   * 验证解锁条件
   */
  private async validateUnlockConditions(
    userId: string,
    contentType: string,
    contentId: string,
    unlockMethod: string
  ): Promise<ServiceResult<boolean>> {
    try {
      // 根据内容类型和解锁方法验证条件
      switch (unlockMethod) {
        case 'vip':
          return await this.validateVipUnlock(userId, contentType, contentId);
        case 'purchase':
          return await this.validatePurchaseUnlock(userId, contentType, contentId);
        case 'achievement':
          return await this.validateAchievementUnlock(userId, contentType, contentId);
        case 'gift':
        case 'promotion':
          // 礼品和促销解锁通常不需要额外验证
          return this.createSuccessResult(true);
        default:
          return this.createErrorResult('Invalid unlock method');
      }
    } catch (error) {
      return this.createErrorResult('Failed to validate unlock conditions', error);
    }
  }

  /**
   * 验证VIP解锁条件
   */
  private async validateVipUnlock(
    _userId: string,
    _contentType: string,
    _contentId: string
  ): Promise<ServiceResult<boolean>> {
    // TODO: 集成VIP状态检查
    // const vipService = await Services.vipStatus();
    // const vipStatus = await vipService.getUserVipStatus(userId);
    // return { success: vipStatus.success && vipStatus.data?.isVip, data: vipStatus.success && vipStatus.data?.isVip };

    // 临时实现
    return { success: true, data: true };
  }

  /**
   * 验证购买解锁条件
   */
  private async validatePurchaseUnlock(
    _userId: string,
    _contentType: string,
    _contentId: string
  ): Promise<ServiceResult<boolean>> {
    // TODO: 集成支付验证
    // 检查是否有有效的购买记录
    return { success: true, data: true };
  }

  /**
   * 验证成就解锁条件
   */
  private async validateAchievementUnlock(
    _userId: string,
    _contentType: string,
    _contentId: string
  ): Promise<ServiceResult<boolean>> {
    // TODO: 集成成就系统
    // 检查用户是否满足特定成就条件
    return { success: true, data: true };
  }

  /**
   * 验证创建数据 (BaseService抽象方法实现)
   */
  protected async validateCreate(data: UserUnlockCreate): Promise<void> {
    if (!data.user_id) {
      throw new Error('User ID is required');
    }
    if (!data.content_type) {
      throw new Error('Content type is required');
    }
    if (!data.content_id) {
      throw new Error('Content ID is required');
    }
    if (!data.unlock_method) {
      throw new Error('Unlock method is required');
    }
  }

  /**
   * 验证更新数据 (BaseService抽象方法实现)
   */
  protected async validateUpdate(data: UserUnlockUpdate): Promise<void> {
    // 更新数据验证相对宽松，只要有数据即可
    if (Object.keys(data).length === 0) {
      throw new Error('Update data cannot be empty');
    }
  }
}
