# Repository vs Service 架构分析

## 🤔 为什么需要Repository和Service两层？

### 核心原因：**关注点分离 (Separation of Concerns)**

Repository和Service层各自承担不同的职责，遵循单一职责原则，这是软件架构的最佳实践。

## 📊 架构层次对比

```
┌─────────────────────────────────────────────────────────────┐
│                    UI Layer (Pages/Components)              │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer (Business Logic)           │
├─────────────────────────────────────────────────────────────┤
│                    Repository Layer (Data Access)           │
├─────────────────────────────────────────────────────────────┤
│                    Database Layer (SQLite)                  │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Repository层职责

### **数据访问层 (Data Access Layer)**

#### 主要职责：
1. **纯数据操作** - 只关心如何存取数据
2. **SQL查询构建** - 构建和执行数据库查询
3. **数据映射** - 数据库行与实体对象的转换
4. **基础CRUD操作** - Create, Read, Update, Delete

#### 特点：
- **无业务逻辑** - 不包含任何业务规则
- **数据库相关** - 直接与数据库交互
- **可复用** - 可被多个Service使用
- **简单直接** - 一个方法对应一个数据库操作

#### 示例 (QuizSessionRepository)：
```typescript
// 纯数据查询，无业务逻辑
async getUserSessions(userId: string, limit: number): Promise<ServiceResult<QuizSession[]>> {
  const query = `
    SELECT * FROM quiz_sessions 
    WHERE user_id = ? 
    ORDER BY last_active_time DESC 
    LIMIT ?
  `;
  const rows = await this.db.all(query, [userId, limit]);
  return this.createSuccessResult(rows.map(row => this.mapRowToEntity(row)));
}
```

## 🏢 Service层职责

### **业务逻辑层 (Business Logic Layer)**

#### 主要职责：
1. **业务规则实现** - 实现复杂的业务逻辑
2. **数据验证** - 输入验证和业务规则验证
3. **事务管理** - 跨多个Repository的事务
4. **事件发射** - 业务事件的发布
5. **数据聚合** - 组合多个数据源的数据
6. **缓存管理** - 业务级别的缓存策略

#### 特点：
- **包含业务逻辑** - 实现具体的业务需求
- **数据库无关** - 通过Repository访问数据
- **复杂操作** - 可能涉及多个Repository
- **状态管理** - 管理业务状态和流程

#### 示例 (QuizSessionService)：
```typescript
// 包含业务逻辑：ID生成、状态设置、事件发射
async createSession(input: CreateQuizSessionInput): Promise<ServiceResult<QuizSession>> {
  // 1. 业务验证
  this.validateCreateInput(input);

  // 2. 业务逻辑：生成ID、设置初始状态
  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const session: QuizSession = {
    id: sessionId,
    status: 'INITIATED', // 业务规则：初始状态
    current_question_index: 0,
    completion_percentage: 0,
    // ... 其他业务逻辑
  };

  // 3. 调用Repository进行数据操作
  const result = await this.repository.create(session);

  // 4. 业务事件发射
  if (result.success) {
    this.emit('sessionCreated', result.data);
  }

  return result;
}

// 复杂业务逻辑：统计计算
async getUserQuizStats(userId: string): Promise<ServiceResult<QuizSessionStats>> {
  // 1. 获取数据
  const sessionsResult = await this.repository.getUserSessions(userId, 1000);
  
  // 2. 业务计算
  const sessions = sessionsResult.data;
  const completionRate = totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0;
  
  // 3. 复杂数据聚合
  const averageSessionTime = this.calculateAverageTime(sessions);
  const mostPopularPacks = this.analyzeMostPopular(sessions);
  
  // 4. 返回业务对象
  return this.createSuccessResult({
    total_sessions: totalSessions,
    completion_rate: Math.round(completionRate),
    average_session_time: averageSessionTime,
    most_popular_packs: mostPopularPacks
  });
}
```

## 🔄 实际对比分析

### Repository方法示例：
```typescript
// QuizSessionRepository - 纯数据操作
async completeSession(sessionId: string): Promise<ServiceResult<boolean>> {
  const query = `
    UPDATE quiz_sessions 
    SET status = 'COMPLETED', 
        end_time = ?, 
        completion_percentage = 100
    WHERE id = ?
  `;
  await this.db.run(query, [new Date().toISOString(), sessionId]);
  return this.createSuccessResult(true);
}
```

### Service方法示例：
```typescript
// QuizSessionService - 业务逻辑 + 数据操作
async completeSession(sessionId: string): Promise<ServiceResult<QuizSession>> {
  // 1. 调用Repository更新数据
  const result = await this.repository.completeSession(sessionId);
  
  if (result.success) {
    // 2. 业务逻辑：获取完整数据
    const sessionResult = await this.getSession(sessionId);
    
    if (sessionResult.success && sessionResult.data) {
      // 3. 业务事件：发射完成事件
      this.emit('sessionCompleted', sessionResult.data);
      return sessionResult;
    }
  }
  
  return result;
}
```

## 💡 为什么不能只用Repository？

### 1. **业务逻辑分散**
如果只有Repository，业务逻辑会分散到UI组件中：
```typescript
// ❌ 不好的做法：业务逻辑在组件中
const Analytics = () => {
  const [stats, setStats] = useState();
  
  useEffect(() => {
    const loadStats = async () => {
      // 业务逻辑混在UI中
      const sessions = await repository.getUserSessions(userId, 1000);
      const totalSessions = sessions.length;
      const completedSessions = sessions.filter(s => s.status === 'COMPLETED').length;
      const completionRate = totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0;
      // ... 更多计算逻辑
      setStats({ totalSessions, completionRate });
    };
    loadStats();
  }, []);
};
```

### 2. **代码重复**
多个组件需要相同的业务逻辑时会产生重复代码。

### 3. **难以测试**
业务逻辑与UI耦合，难以进行单元测试。

### 4. **缺乏事务管理**
复杂操作需要跨多个Repository时，事务管理变得困难。

## ✅ 使用Service层的好处

### 1. **业务逻辑集中**
```typescript
// ✅ 好的做法：业务逻辑在Service中
const Analytics = () => {
  const [stats, setStats] = useState();
  
  useEffect(() => {
    const loadStats = async () => {
      const service = await Services.quizSession();
      const result = await service.getUserQuizStats(userId); // 一行搞定
      if (result.success) {
        setStats(result.data);
      }
    };
    loadStats();
  }, []);
};
```

### 2. **易于测试**
```typescript
// 可以独立测试业务逻辑
describe('QuizSessionService', () => {
  it('should calculate completion rate correctly', async () => {
    const service = new QuizSessionService();
    const stats = await service.getUserQuizStats('user123');
    expect(stats.data.completion_rate).toBe(75);
  });
});
```

### 3. **代码复用**
Service方法可以被多个组件和其他Service使用。

### 4. **事件系统**
Service层可以发射业务事件，实现松耦合的组件通信。

## 🎯 总结

| 层次 | Repository | Service |
|------|------------|---------|
| **职责** | 数据访问 | 业务逻辑 |
| **关注点** | 如何存取数据 | 业务规则和流程 |
| **复杂度** | 简单直接 | 复杂多样 |
| **依赖** | 数据库 | Repository |
| **测试** | 数据库测试 | 业务逻辑测试 |
| **复用性** | 高（数据操作） | 高（业务功能） |

### 核心价值：
1. **关注点分离** - 每层专注自己的职责
2. **代码组织** - 清晰的架构层次
3. **可维护性** - 易于修改和扩展
4. **可测试性** - 独立的单元测试
5. **可复用性** - 组件间的代码复用

这种架构虽然增加了一些复杂性，但为大型应用提供了更好的可维护性、可测试性和可扩展性，是现代软件开发的最佳实践。
