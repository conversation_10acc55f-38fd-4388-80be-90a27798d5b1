import { useDisplay } from '@/contexts/DisplayContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import type { ViewType } from '@/types';
import type React from 'react';
import { toast } from 'sonner';
import OptionButton from '../ui/OptionButton';

/**
 * 视图类型选项组件
 * 用于选择视图类型（轮盘、卡片、气泡等）
 */
const ViewTypeOptions: React.FC = () => {
  const { t } = useLanguage();
  const { getUserDisplayOptions, currentSkin, availableSkins } = useDisplay();
  const { userConfig, updateUserConfig } = useUserConfig();

  // 获取当前显示选项
  const displayOptions = getUserDisplayOptions();

  // 处理视图类型变更
  const handleViewTypeChange = (type: ViewType) => {
    // 检查当前皮肤是否支持所选视图类型（不区分大小写）
    const typeLC = type.toLowerCase();

    // 检查 view_configs 中是否有对应的配置
    const hasViewConfig =
      currentSkin.config.view_configs &&
      Object.keys(currentSkin.config.view_configs).some((key) => key.toLowerCase() === typeLC);

    // 检查 supported_view_types 中是否包含该视图类型
    const isTypeSupported = currentSkin.config.supported_view_types?.some(
      (supportedType) => supportedType.toLowerCase() === typeLC
    );

    // 如果 view_configs 中有配置或 supported_view_types 中包含该视图类型，则认为当前皮肤支持该视图类型
    const currentSkinSupportsViewType = hasViewConfig || isTypeSupported;

    if (currentSkinSupportsViewType) {
      // 当前皮肤支持所选视图类型，直接设置
      updateUserConfig({
        preferred_view_type: type,
      });
    } else {
      // 当前皮肤不支持所选视图类型，需要找到支持该视图类型的皮肤
      // 优先选择免费皮肤
      const freeSkins = availableSkins.filter((skin) => {
        // 检查是否已解锁
        if (!skin.is_unlocked) return false;

        // 检查是否为免费皮肤
        if (skin.category !== 'free') return false;

        // 检查 view_configs 中是否有对应的配置
        const hasConfig =
          skin.config.view_configs &&
          Object.keys(skin.config.view_configs).some((key) => key.toLowerCase() === typeLC);

        // 检查 supported_view_types 中是否包含该视图类型
        const supportsType = skin.config.supported_view_types?.some(
          (supportedType) => supportedType.toLowerCase() === typeLC
        );

        // 如果 view_configs 中有配置或 supported_view_types 中包含该视图类型，则认为皮肤支持该视图类型
        return hasConfig || supportsType;
      });

      if (freeSkins.length > 0) {
        // 找到支持所选视图类型的免费皮肤，选择第一个
        updateUserConfig({
          preferred_view_type: type,
          active_skin_id: freeSkins[0].id,
          view_type_skin_ids: JSON.stringify({
            ...(userConfig.view_type_skin_ids ? JSON.parse(userConfig.view_type_skin_ids) : {}),
            [type]: freeSkins[0].id,
          }),
        });

        // 通知用户皮肤已更改
        toast.info(
          t('settings.skin_changed_for_view_type', {
            skinName: freeSkins[0].name,
            viewType: t(`settings.view_type.${type}`),
          }),
          { duration: 3000 }
        );
      } else {
        // 没有找到免费皮肤，查找任何支持该视图类型的已解锁皮肤
        const supportingSkins = availableSkins.filter((skin) => {
          // 检查是否已解锁
          if (!skin.is_unlocked) return false;

          // 检查 view_configs 中是否有对应的配置
          const hasConfig =
            skin.config.view_configs &&
            Object.keys(skin.config.view_configs).some((key) => key.toLowerCase() === typeLC);

          // 检查 supported_view_types 中是否包含该视图类型
          const supportsType = skin.config.supported_view_types?.some(
            (supportedType) => supportedType.toLowerCase() === typeLC
          );

          // 如果 view_configs 中有配置或 supported_view_types 中包含该视图类型，则认为皮肤支持该视图类型
          return hasConfig || supportsType;
        });

        if (supportingSkins.length > 0) {
          // 找到支持所选视图类型的皮肤，选择第一个
          updateUserConfig({
            preferred_view_type: type,
            active_skin_id: supportingSkins[0].id,
            view_type_skin_ids: {
              ...userConfig.view_type_skin_ids,
              [type]: supportingSkins[0].id,
            },
          });

          // 通知用户皮肤已更改
          toast.info(
            t('settings.skin_changed_for_view_type', {
              skinName: supportingSkins[0].name,
              viewType: t(`settings.view_type.${type}`),
            }),
            { duration: 3000 }
          );
        } else {
          // 没有找到支持所选视图类型的皮肤，仍然设置视图类型
          // 但通知用户没有合适的皮肤
          updateUserConfig({
            preferred_view_type: type,
          });

          toast.warning(
            t('settings.no_skin_supports_view_type', {
              viewType: t(`settings.view_type.${type}`),
            }),
            { duration: 3000 }
          );
        }
      }
    }
  };

  // 所有可用的视图类型
  const allViewTypes: ViewType[] = [
    'wheel',
    'card',
    'bubble',
    'list',
    'grid',
    'galaxy',
    'tree',
    'flow',
    'tagCloud',
  ];

  return (
    <div className="space-y-4">
      <p className="text-sm text-muted-foreground">{t('settings.view_type.description')}</p>

      {/* 基础视图类型 */}
      <div>
        <h4 className="text-sm font-medium text-muted-foreground mb-2">
          {t('settings.view_type.basic')}
        </h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {/* 轮盘视图 */}
          {allViewTypes.includes('wheel') && (
            <OptionButton
              label={t('settings.view_type.wheel')}
              tooltip={t('settings.view_type.wheel.description')}
              isSelected={displayOptions.viewType === 'wheel'}
              onClick={() => handleViewTypeChange('wheel')}
            />
          )}

          {/* 卡片视图 */}
          {allViewTypes.includes('card') && (
            <OptionButton
              label={t('settings.view_type.card')}
              tooltip={t('settings.view_type.card.description')}
              isSelected={displayOptions.viewType === 'card'}
              onClick={() => handleViewTypeChange('card')}
            />
          )}

          {/* 气泡视图 */}
          {allViewTypes.includes('bubble') && (
            <OptionButton
              label={t('settings.view_type.bubble')}
              tooltip={t('settings.view_type.bubble.description')}
              isSelected={displayOptions.viewType === 'bubble'}
              onClick={() => handleViewTypeChange('bubble')}
            />
          )}
        </div>
      </div>

      {/* 列表和网格视图 */}
      <div>
        <h4 className="text-sm font-medium text-muted-foreground mb-2">
          {t('settings.view_type.list_grid')}
        </h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {/* 列表视图 */}
          {allViewTypes.includes('list') && (
            <OptionButton
              label={t('settings.view_type.list')}
              tooltip={t('settings.view_type.list.description')}
              isSelected={displayOptions.viewType === 'list'}
              onClick={() => handleViewTypeChange('list')}
            />
          )}

          {/* 网格视图 */}
          {allViewTypes.includes('grid') && (
            <OptionButton
              label={t('settings.view_type.grid')}
              tooltip={t('settings.view_type.grid.description')}
              isSelected={displayOptions.viewType === 'grid'}
              onClick={() => handleViewTypeChange('grid')}
            />
          )}

          {/* 标签云视图 */}
          {allViewTypes.includes('tagCloud') && (
            <OptionButton
              label={t('settings.view_type.tagCloud')}
              tooltip={t('settings.view_type.tagCloud.description')}
              isSelected={displayOptions.viewType === 'tagCloud'}
              onClick={() => handleViewTypeChange('tagCloud')}
            />
          )}
        </div>
      </div>

      {/* 高级视图类型 */}
      <div>
        <h4 className="text-sm font-medium text-muted-foreground mb-2">
          {t('settings.view_type.advanced')}
        </h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {/* 星系视图 */}
          {allViewTypes.includes('galaxy') && (
            <OptionButton
              label={t('settings.view_type.galaxy')}
              tooltip={t('settings.view_type.galaxy.description')}
              isSelected={displayOptions.viewType === 'galaxy'}
              onClick={() => handleViewTypeChange('galaxy')}
            />
          )}

          {/* 树状视图 */}
          {allViewTypes.includes('tree') && (
            <OptionButton
              label={t('settings.view_type.tree')}
              tooltip={t('settings.view_type.tree.description')}
              isSelected={displayOptions.viewType === 'tree'}
              onClick={() => handleViewTypeChange('tree')}
            />
          )}

          {/* 流程图视图 */}
          {allViewTypes.includes('flow') && (
            <OptionButton
              label={t('settings.view_type.flow')}
              tooltip={t('settings.view_type.flow.description')}
              isSelected={displayOptions.viewType === 'flow'}
              onClick={() => handleViewTypeChange('flow')}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewTypeOptions;
