# Biome 迁移指南

## 🎯 为什么选择 Biome？

### 性能优势
- **速度提升**: 比 ESLint + Prettier 快 35-100 倍
- **内存效率**: 更低的内存占用
- **启动时间**: 几乎瞬时启动

### 开发体验
- **统一工具**: 一个工具替代 ESLint + Prettier
- **配置简化**: 单一配置文件 `biome.json`
- **依赖减少**: 减少 node_modules 大小
- **更好的错误信息**: 清晰的错误报告

## 🚀 快速迁移

### 方法 1: 自动迁移（推荐）

```bash
# 运行迁移脚本
node scripts/migrate-to-biome.js
```

### 方法 2: 手动迁移

```bash
# 1. 安装 Biome
npm install --save-dev @biomejs/biome

# 2. 初始化配置
npx @biomejs/biome init

# 3. 迁移现有 ESLint 配置
npx @biomejs/biome migrate eslint --write

# 4. 格式化所有文件
npx biome format --write .

# 5. 检查代码质量
npx biome lint .
```

## 📝 配置说明

### biome.json 主要配置

```json
{
  "formatter": {
    "enabled": true,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineWidth": 100
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true
    }
  },
  "organizeImports": {
    "enabled": true
  }
}
```

### VS Code 集成

安装 Biome 扩展：
```
biomejs.biome
```

VS Code 设置（`.vscode/settings.json`）：
```json
{
  "editor.defaultFormatter": "biomejs.biome",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  }
}
```

## 🛠️ 新的 NPM 脚本

```json
{
  "scripts": {
    "format": "biome format --write .",
    "format:check": "biome format .",
    "lint:biome": "biome lint .",
    "lint:biome:fix": "biome lint --write .",
    "check": "biome check .",
    "check:fix": "biome check --write .",
    "ci:check": "biome ci ."
  }
}
```

### 脚本说明

- `format`: 格式化所有文件
- `format:check`: 检查格式化（不修改文件）
- `lint:biome`: 运行 linting 检查
- `lint:biome:fix`: 运行 linting 并自动修复
- `check`: 运行所有检查（格式化 + linting）
- `check:fix`: 运行所有检查并自动修复
- `ci:check`: CI 环境专用检查

## 🔄 迁移策略

### 策略 1: 完全替换（推荐）

1. 安装 Biome
2. 配置 Biome
3. 移除 ESLint 和 Prettier
4. 更新 CI/CD 流程

### 策略 2: 渐进式迁移

1. 安装 Biome 用于格式化
2. 保留 ESLint 用于 linting
3. 逐步迁移规则到 Biome
4. 最终移除 ESLint

### 策略 3: 并行使用

1. 保留现有工具
2. 添加 Biome 作为补充
3. 在新文件中使用 Biome
4. 逐步迁移旧文件

## 🎨 格式化规则对比

| 规则 | Prettier | Biome |
|------|----------|-------|
| 缩进 | 2 spaces | 2 spaces ✅ |
| 行宽 | 80 | 100 |
| 分号 | true | true ✅ |
| 引号 | single | single ✅ |
| 尾随逗号 | es5 | es5 ✅ |
| 括号间距 | true | true ✅ |

## 🔍 Linting 规则对比

| 类别 | ESLint | Biome |
|------|--------|-------|
| 基础规则 | ✅ | ✅ |
| TypeScript | ✅ | ✅ |
| React | ✅ | ✅ |
| 性能 | 慢 | 快 ⚡ |
| 配置复杂度 | 高 | 低 |

## 🚨 注意事项

### 兼容性
- Node.js 14+ 支持
- 支持 TypeScript、JavaScript、JSON、CSS
- React/JSX 完全支持

### 限制
- 某些 ESLint 插件可能没有对应的 Biome 规则
- 自定义规则需要重新实现
- 社区生态相对较新

### 迁移检查清单

- [ ] 安装 Biome
- [ ] 创建 `biome.json` 配置
- [ ] 更新 package.json 脚本
- [ ] 配置 VS Code 集成
- [ ] 运行格式化和检查
- [ ] 更新 CI/CD 流程
- [ ] 团队培训和文档更新

## 📚 有用的命令

```bash
# 检查特定文件
biome check src/components/Button.tsx

# 格式化特定目录
biome format --write src/components/

# 只检查不修复
biome check --no-write .

# 显示详细输出
biome check --verbose .

# 检查配置文件
biome check --config-path ./custom-biome.json .
```

## 🔧 故障排除

### 常见问题

1. **格式化冲突**
   - 确保禁用其他格式化工具
   - 检查 VS Code 设置

2. **规则不匹配**
   - 查看 Biome 文档了解规则映射
   - 自定义配置以匹配团队标准

3. **性能问题**
   - 检查忽略文件配置
   - 确保不检查 node_modules

### 获取帮助

- [Biome 官方文档](https://biomejs.dev/)
- [GitHub Issues](https://github.com/biomejs/biome/issues)
- [Discord 社区](https://discord.gg/BypW39g6Yc)

## 🎉 迁移完成后的好处

1. **更快的开发体验**: 格式化和检查速度显著提升
2. **简化的工具链**: 减少配置文件和依赖
3. **一致的代码风格**: 统一的格式化和检查规则
4. **更好的 CI 性能**: 更快的构建时间
5. **现代化的工具**: 使用最新的代码质量工具
