# 类型导入修复执行计划

## 🎯 修复目标
将所有分散的类型导入统一为 `@/types` 导入，提高代码一致性和可维护性。

## 📊 修复范围
- **总文件数**: 95 个文件
- **预计工作量**: 4-6 小时
- **风险等级**: 低 (主要是导入语句替换)

## 🚀 执行计划

### 阶段 1: 核心视图组件 (高优先级)
**目标**: 修复视图系统的核心组件
**文件数**: 24 个文件
**预计时间**: 1.5 小时

#### 1.1 Views/implementations (12 个文件)
- bubbles/BubbleView.tsx
- cards/CardView.tsx  
- galaxy/GalaxyView.tsx
- lists/ListView.tsx
- wheels/ (8 个轮盘视图文件)

#### 1.2 Views/components (12 个文件)
- bubbles/BubbleView.tsx
- cards/CardView.tsx
- galaxy/GalaxyComponent.tsx
- lists/ListView.tsx
- wheels/ (8 个轮盘组件文件)

### 阶段 2: 核心功能组件 (高优先级)
**目标**: 修复核心功能相关组件
**文件数**: 15 个文件
**预计时间**: 1 小时

#### 2.1 Components/mood (2 个文件)
- MultiTierEmotionSelector.tsx

#### 2.2 Components/preview (10 个文件)
- BubbleSkinPreview.tsx
- CardSkinPreview.tsx
- GalaxySkinPreview.tsx
- SimpleBubbleView.tsx
- SimpleCardView.tsx
- SkinPreview.tsx
- SkinPreviewTest.tsx

#### 2.3 Hooks (6 个文件)
- useAnalyticsData.ts
- useExportData.ts
- useHistoryData.ts
- useLocalTagsData.ts
- useShop.ts

### 阶段 3: 设置和编辑器 (中优先级)
**目标**: 修复设置界面和编辑器
**文件数**: 19 个文件
**预计时间**: 1.5 小时

#### 3.1 Components/settings (12 个文件)
- display/ (3 个文件)
- BubbleLayoutPreview.tsx
- CardViewSettings.tsx
- ColorModePreview.tsx
- CustomSkinComponent.tsx
- DisplayOptionsComponentVertical.tsx
- DisplaySkinComponent.tsx
- EmotionDataSelectionComponent.tsx
- GalaxyLayoutPreview.tsx
- SkinSelector.tsx

#### 3.2 Components/editor (7 个文件)
- AppearanceEditor.tsx
- CustomSkinEditor.tsx
- EmojiMappingEditor.tsx
- EmotionDataEditor.tsx
- EmotionEditor.tsx
- TierEditor.tsx

### 阶段 4: 工具和服务 (中优先级)
**目标**: 修复工具函数和服务层
**文件数**: 10 个文件
**预计时间**: 1 小时

#### 4.1 Utils (8 个文件)
- colorUtils.ts
- emojiSetManager.ts
- gridFactory.tsx
- listFactory.tsx
- sectorUtils.ts
- skinFixer.ts
- skinPreviewGenerator.ts

#### 4.2 Services (2 个文件)
- entities/EmotionSelectionService.ts
- entities/UserConfigService.ts

### 阶段 5: 辅助组件 (低优先级)
**目标**: 修复辅助功能组件
**文件数**: 10 个文件
**预计时间**: 0.5 小时

#### 5.1 Components/common (3 个文件)
- ViewContainer.tsx

#### 5.2 Components/emoji (2 个文件)
- AnimatedEmoji.tsx
- EmojiDisplay.tsx

#### 5.3 Components/history (1 个文件)
- EmotionMapDisplay.tsx

#### 5.4 Components/tags (2 个文件)
- TagSuggestions.tsx

#### 5.5 Contexts (2 个文件)
- ColorModeContext.tsx
- SkinContext.tsx

#### 5.6 Skins (1 个文件)
- wheelSkins.ts

## ✅ 验证计划

### 每阶段验证
1. **TypeScript 编译检查**: `npx tsc --noEmit`
2. **代码质量检查**: `npm run check`
3. **功能测试**: 启动应用验证核心功能

### 最终验证
1. **全量编译检查**
2. **端到端功能测试**
3. **性能回归测试**

## 🔄 回滚计划
- 每阶段完成后提交 Git
- 如有问题可快速回滚到上一个稳定状态
- 保留修复前的备份文件

## 📈 成功指标
- [ ] 所有文件使用统一的 `@/types` 导入
- [ ] TypeScript 编译 0 错误
- [ ] 应用功能正常运行
- [ ] 代码质量检查通过
