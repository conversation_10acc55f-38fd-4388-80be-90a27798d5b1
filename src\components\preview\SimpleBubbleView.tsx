/**
 * 简化气泡视图组件
 */

import { useTheme } from '@/contexts/ThemeContext';
import type { Emotion } from '@/types/emotionDataTypes';
import type { ContentDisplayMode } from '@/types/previewTypes';
import type { SkinConfig } from '@/types/skinTypes';
import type React from 'react';
import { useState } from 'react';

interface SimpleBubbleViewProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
}

/**
 * 简化气泡视图组件
 * 用于在SkinPreview中预览气泡视图
 */
export const SimpleBubbleView: React.FC<SimpleBubbleViewProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
}) => {
  const { isDarkMode } = useTheme();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // 计算容器大小
  const containerSize = 300;

  // 计算气泡大小
  const bubbleSize = skinConfig.view_configs?.bubble?.bubble_size || 60;

  // 获取情绪内容
  const getEmotionContent = (emotion: Emotion) => {
    const text = emotion.name;
    const emoji = emotion.emoji;

    switch (contentDisplayMode) {
      case 'text':
        return { text, emoji: '' };
      case 'emoji':
        return { text: '', emoji };
      default:
        return { text, emoji };
    }
  };

  // 获取情绪颜色
  const getEmotionColor = (emotion: Emotion, index: number) => {
    // 如果情绪有自定义颜色，优先使用
    if (emotion.color) {
      return emotion.color;
    }

    // 否则根据层级和索引生成颜色
    const hue = (index * 360) / emotions.length;
    const saturation = 70;
    const lightness = isDarkMode ? 50 : 60;

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  // 处理气泡悬停
  const handleBubbleHover = (index: number) => {
    setHoveredIndex(index);
  };

  // 处理气泡离开
  const handleBubbleLeave = () => {
    setHoveredIndex(null);
  };

  // 获取悬停效果
  const getHoverEffect = (index: number) => {
    if (hoveredIndex === index) {
      return 'scale(1.1)';
    }
    return 'scale(1)';
  };

  // 计算气泡位置
  const calculateBubblePosition = (index: number, total: number) => {
    // 使用简单的圆形布局
    const angle = (index / total) * 2 * Math.PI;
    const radius = containerSize / 2 - bubbleSize / 2 - 10;

    const x = containerSize / 2 + radius * Math.cos(angle);
    const y = containerSize / 2 + radius * Math.sin(angle);

    return { x, y };
  };

  return (
    <div
      className="simple-bubble-view"
      style={{
        position: 'relative',
        width: `${containerSize}px`,
        height: `${containerSize}px`,
        margin: '0 auto',
      }}
    >
      {emotions.map((emotion, index) => {
        const { text, emoji } = getEmotionContent(emotion);
        const { x, y } = calculateBubblePosition(index, emotions.length);

        return (
          <div
            key={emotion.id}
            className="bubble"
            style={{
              position: 'absolute',
              left: `${x - bubbleSize / 2}px`,
              top: `${y - bubbleSize / 2}px`,
              width: `${bubbleSize}px`,
              height: `${bubbleSize}px`,
              backgroundColor: getEmotionColor(emotion, index),
              borderRadius: '50%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              boxShadow: skinConfig.effects.shadows ? '0 2px 4px rgba(0, 0, 0, 0.2)' : 'none',
              transform: getHoverEffect(index),
              transition: 'transform 0.2s ease',
              color: skinConfig.colors.text,
              padding: '4px',
            }}
            onClick={() => onSelect(emotion)}
            onMouseEnter={() => handleBubbleHover(index)}
            onMouseLeave={handleBubbleLeave}
          >
            {emoji && (
              <div className="emoji" style={{ fontSize: `${skinConfig.fonts.size.medium}px` }}>
                {emoji}
              </div>
            )}
            {text && (
              <div
                className="text"
                style={{
                  fontSize: `${skinConfig.fonts.size.small}px`,
                  fontWeight: skinConfig.fonts.weight.normal,
                  fontFamily: skinConfig.fonts.family,
                  textAlign: 'center',
                  marginTop: emoji ? '2px' : '0',
                  // 如果文本太长，显示省略号
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  maxWidth: '100%',
                }}
              >
                {text}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
