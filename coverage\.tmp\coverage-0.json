{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/database/three-layer-architecture.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 54787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 54787, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 834, "endOffset": 21341, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 877, "endOffset": 938, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1000, "endOffset": 9954, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1064, "endOffset": 2625, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2690, "endOffset": 4493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4328, "endOffset": 4362, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4432, "endOffset": 4467, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4565, "endOffset": 6223, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6059, "endOffset": 6092, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6162, "endOffset": 6197, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6275, "endOffset": 8405, "count": 1}, {"startOffset": 8267, "endOffset": 8404, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8326, "endOffset": 8365, "count": 39}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8457, "endOffset": 9946, "count": 1}, {"startOffset": 9684, "endOffset": 9936, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10017, "endOffset": 13449, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10075, "endOffset": 11074, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10912, "endOffset": 10943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11013, "endOffset": 11048, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11126, "endOffset": 12039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11970, "endOffset": 12013, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12092, "endOffset": 13441, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13075, "endOffset": 13181, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13515, "endOffset": 16811, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13570, "endOffset": 14512, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14566, "endOffset": 15632, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15452, "endOffset": 15493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15563, "endOffset": 15606, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15685, "endOffset": 16803, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16636, "endOffset": 16679, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16748, "endOffset": 16779, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16864, "endOffset": 19644, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16920, "endOffset": 17780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17301, "endOffset": 17386, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17637, "endOffset": 17768, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17709, "endOffset": 17738, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17834, "endOffset": 18642, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18115, "endOffset": 18254, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18487, "endOffset": 18630, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18561, "endOffset": 18600, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18695, "endOffset": 19636, "count": 1}], "isBlockCoverage": true}, {"functionName": "uniqueConstraintTest", "ranges": [{"startOffset": 18770, "endOffset": 19385, "count": 1}, {"startOffset": 18995, "endOffset": 19259, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19696, "endOffset": 21337, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19754, "endOffset": 20669, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20724, "endOffset": 21329, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}