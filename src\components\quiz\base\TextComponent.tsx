/**
 * Quiz文本组件
 * 支持多语言、动画效果和中医文化样式的文本显示组件
 */

import React, { useEffect, useRef, useState } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { TextComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState,
  AnimationEffect
} from './BaseQuizComponent';

// 类型定义
export type TextComponentConfig = z.infer<typeof TextComponentConfigSchema>;

export interface TextComponentProps extends BaseQuizComponentProps<TextComponentConfig> {
  content: {
    text_localized: Record<string, string>;
    emphasis_words?: string[];
    animation_effect?: AnimationEffect;
  };
}

interface TextComponentState extends ComponentState {
  displayed_text: string;
  animation_progress: number;
}

/**
 * 文本组件类
 */
export class TextComponent extends BaseQuizComponent<
  TextComponentConfig,
  TextComponentProps,
  TextComponentState
> {
  private textRef = React.createRef<HTMLDivElement>();
  private animationFrameId?: number;
  private typewriterTimeoutId?: number;

  extractConfig(props: TextComponentProps): TextComponentConfig {
    return props.config;
  }

  getInitialState(): TextComponentState {
    return {
      is_loading: false,
      is_interactive: false,
      is_disabled: false,
      selected_items: [],
      animation_state: 'idle',
      displayed_text: '',
      animation_progress: 0
    };
  }

  componentDidMount(): void {
    this.startTextAnimation();
  }

  componentWillUnmount(): void {
    this.cleanupAnimations();
  }

  /**
   * 清理动画资源
   */
  private cleanupAnimations(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }
    if (this.typewriterTimeoutId) {
      clearTimeout(this.typewriterTimeoutId);
    }
  }

  /**
   * 开始文本动画
   */
  private startTextAnimation(): void {
    const animationEffect = this.props.content.animation_effect || this.config.content.animation_effect;

    if (!animationEffect || this.personalization.layer5_accessibility?.reduce_motion) {
      // 无动画或减少动画模式，直接显示文本
      this.setState({
        displayed_text: this.getCurrentText(),
        animation_state: 'completed'
      });
      return;
    }

    this.setState({ animation_state: 'running' });

    switch (animationEffect) {
      case 'typewriter':
        this.startTypewriterAnimation();
        break;
      case 'brush_stroke':
        this.startBrushStrokeAnimation();
        break;
      case 'fade_in':
        this.startFadeInAnimation();
        break;
      default:
        this.startFadeInAnimation();
    }
  }

  /**
   * 打字机动画
   */
  private startTypewriterAnimation(): void {
    const fullText = this.getCurrentText();
    let currentIndex = 0;

    const typeNextChar = () => {
      if (currentIndex <= fullText.length) {
        this.setState({
          displayed_text: fullText.substring(0, currentIndex),
          animation_progress: currentIndex / fullText.length
        });
        currentIndex++;

        this.typewriterTimeoutId = setTimeout(typeNextChar, 50) as any;
      } else {
        this.setState({ animation_state: 'completed' });
      }
    };

    typeNextChar();
  }

  /**
   * 毛笔描边动画
   */
  private startBrushStrokeAnimation(): void {
    const element = this.textRef.current;
    if (!element) return;

    const fullText = this.getCurrentText();
    this.setState({ displayed_text: fullText });

    // 应用毛笔描边CSS动画
    element.style.opacity = '0';
    element.style.transform = 'translateY(10px)';
    element.style.filter = 'blur(2px)';
    element.style.transition = 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';

    // 触发动画
    requestAnimationFrame(() => {
      element.style.opacity = '1';
      element.style.transform = 'translateY(0)';
      element.style.filter = 'blur(0)';
    });

    setTimeout(() => {
      this.setState({ animation_state: 'completed' });
    }, 600);
  }

  /**
   * 淡入动画
   */
  private startFadeInAnimation(): void {
    const element = this.textRef.current;
    if (!element) return;

    const fullText = this.getCurrentText();
    this.setState({ displayed_text: fullText });

    element.style.opacity = '0';
    element.style.transition = 'opacity 0.4s ease-out';

    requestAnimationFrame(() => {
      element.style.opacity = '1';
    });

    setTimeout(() => {
      this.setState({ animation_state: 'completed' });
    }, 400);
  }

  /**
   * 获取当前语言的文本
   */
  private getCurrentText(): string {
    const { language } = this.context || { language: 'zh' };
    const textLocalized = this.props.content.text_localized;

    return textLocalized[language] || textLocalized['zh'] || textLocalized['en'] || '';
  }

  /**
   * 处理强调词高亮
   */
  private renderTextWithEmphasis(text: string): React.ReactNode {
    const emphasisWords = this.props.content.emphasis_words;

    if (!emphasisWords || emphasisWords.length === 0) {
      return text;
    }

    let result: React.ReactNode[] = [];
    let lastIndex = 0;

    emphasisWords.forEach((word, index) => {
      const wordIndex = text.indexOf(word, lastIndex);
      if (wordIndex !== -1) {
        // 添加普通文本
        if (wordIndex > lastIndex) {
          result.push(text.substring(lastIndex, wordIndex));
        }

        // 添加强调文本
        result.push(
          <span key={index} className="text-emphasis">
            {word}
          </span>
        );

        lastIndex = wordIndex + word.length;
      }
    });

    // 添加剩余文本
    if (lastIndex < text.length) {
      result.push(text.substring(lastIndex));
    }

    return result;
  }

  /**
   * 获取布局样式类名
   */
  private getLayoutClassName(): string {
    const layoutId = this.config.layout_id;

    switch (layoutId) {
      case 'dialogue_bubble':
        return 'quiz-text-dialogue-bubble';
      case 'scroll_text':
        return 'quiz-text-scroll';
      case 'inscription_text':
        return 'quiz-text-inscription';
      case 'floating_text':
        return 'quiz-text-floating';
      case 'banner_text':
        return 'quiz-text-banner';
      case 'standard_text':
      default:
        return 'quiz-text-standard';
    }
  }

  /**
   * 获取字体样式类名
   */
  private getFontClassName(): string {
    const fontFamily = this.config.style.font_family;

    switch (fontFamily) {
      case 'traditional':
        return 'font-traditional';
      case 'calligraphy':
        return 'font-calligraphy';
      case 'seal_script':
        return 'font-seal-script';
      case 'clerical_script':
        return 'font-clerical-script';
      case 'modern':
      default:
        return 'font-modern';
    }
  }

  /**
   * 获取尺寸样式类名
   */
  private getSizeClassName(): string {
    const size = this.config.style.size;
    return `text-${size}`;
  }

  /**
   * 获取背景图案样式类名
   */
  private getBackgroundPatternClassName(): string {
    const pattern = this.config.style.background_pattern;
    return pattern && pattern !== 'none' ? `bg-pattern-${pattern}` : '';
  }

  /**
   * 获取对齐样式类名
   */
  private getAlignmentClassName(): string {
    const alignment = this.config.style.alignment;
    return `text-${alignment}`;
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();

    const combinedStyles: React.CSSProperties = {
      ...personalizedStyles,
      color: this.config.style.color_scheme,
      lineHeight: this.config.style.line_height,
      letterSpacing: `${this.config.style.letter_spacing}em`,
      ...this.props.style
    };

    const className = [
      'quiz-text-component',
      this.getLayoutClassName(),
      this.getFontClassName(),
      this.getSizeClassName(),
      this.getAlignmentClassName(),
      this.getBackgroundPatternClassName(),
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        ref={this.textRef}
        className={className}
        style={combinedStyles}
        {...accessibilityProps}
        role="text"
        aria-live={this.state.animation_state === 'running' ? 'polite' : 'off'}
      >
        {this.renderTextWithEmphasis(this.state.displayed_text)}

        {/* 对话气泡的箭头 */}
        {this.config.layout_id === 'dialogue_bubble' && (
          <div className="dialogue-bubble-arrow" />
        )}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'text';
  }

  protected getAriaLabel(): string {
    return `Text: ${this.getCurrentText()}`;
  }
}

// 使用Context的函数式组件包装器
const TextComponentWrapper: React.FC<TextComponentProps> = (props) => {
  const { language } = useLanguage();

  return (
    <TextComponent.contextType.Provider value={{ language }}>
      <TextComponent {...props} />
    </TextComponent.contextType.Provider>
  );
};

// 设置Context类型
TextComponent.contextType = React.createContext({ language: 'zh' });

export default TextComponentWrapper;
