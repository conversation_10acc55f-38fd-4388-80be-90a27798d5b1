# 情绪数据与表情系统设计文档

## 1. 系统概述

情绪数据与表情系统是应用程序的核心组件，负责管理用户的情绪数据和表情显示。系统包含两个主要部分：

1. **情绪数据管理**：负责情绪数据的存储、加载、编辑和持久化
2. **表情系统**：负责表情集的管理和情绪-表情映射

## 2. 数据结构

### 2.1 情绪数据结构

```typescript
// 情绪数据
interface EmotionData {
  id: string;                // 唯一标识符
  name: string;              // 数据集名称
  description?: string;      // 描述（可选）
  tiers: EmotionTier[];      // 情绪层级
  isActive: boolean;         // 是否激活
  isDefault?: boolean;       // 是否为默认数据集
  isSystemDefault?: boolean; // 是否为系统默认数据集（从数据库加载，不可修改）
  defaultEmojiSet?: string;  // 默认表情集ID
  supportedLanguages?: string[]; // 支持的语言列表
  localized_names?: Record<string, string>; // 不同语言的名称
  localized_descriptions?: Record<string, string>; // 不同语言的描述
  created_at: Date;           // 创建时间
  updated_at: Date;           // 更新时间
  created_by?: string;        // 创建者（系统预设或用户ID）
}

// 情绪层级
interface EmotionTier {
  id: string;                // 唯一标识符
  name: string;              // 层级名称
  level: number;             // 层级等级（1=主要，2=次要，3=细分）
  parentTierId?: string;     // 父层级ID（可选）
  emotions: Emotion[];       // 该层级包含的情绪
  localized_names?: Record<string, string>; // 不同语言的名称
}

// 情绪
interface Emotion {
  id: string;                // 唯一标识符
  name: string;              // 情绪名称
  emoji: string;             // 默认情绪表情（向后兼容）
  color?: string;            // 情绪颜色（可选）
  keywords?: string[];       // 关键词（可选，用于搜索和分类）
  localized_names?: Record<string, string>; // 不同语言的名称
  emoji_sets?: Record<string, EmojiItem>; // 不同表情集的表情
}
```

### 2.2 表情系统结构

```typescript
// 表情集类型
type EmojiSetType = 'unicode' | 'image' | 'svg' | 'animated';

// 表情项
interface EmojiItem {
  id: string;           // 表情ID，通常与情绪ID相同
  unicode?: string;     // Unicode表情
  image_url?: string;   // 图片URL
  svg_content?: string; // SVG内容
  animation_data?: any; // 动画数据
}

// 表情集
interface EmojiSet {
  id: string;                   // 表情集ID
  name: string;                 // 表情集名称
  description?: string;         // 描述
  type: EmojiSetType;           // 表情类型
  is_default: boolean;          // 是否为默认表情集
  is_system: boolean;           // 是否为系统表情集
  is_unlocked: boolean;         // 是否已解锁
  price?: number;               // 价格（如果是付费表情集）
  preview_image?: string;       // 预览图片
  created_at: string;           // 创建时间
  updated_at: string;           // 更新时间
  created_by?: string;          // 创建者
}
```

## 3. 数据库结构

### 3.1 情绪数据表

```sql
-- 情绪表
CREATE TABLE IF NOT EXISTS emotions (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    tier TEXT NOT NULL CHECK (tier IN ('primary', 'secondary', 'tertiary')),
    parent_id TEXT,
    color TEXT,
    FOREIGN KEY (parent_id) REFERENCES emotions(id) ON DELETE CASCADE
);

-- 情绪翻译表
CREATE TABLE IF NOT EXISTS emotion_translations (
    emotion_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    PRIMARY KEY (emotion_id, language_code),
    FOREIGN KEY (emotion_id) REFERENCES emotions(id) ON DELETE CASCADE
);
```

### 3.2 表情系统表

```sql
-- 表情集表
CREATE TABLE IF NOT EXISTS emoji_sets (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL CHECK (type IN ('unicode', 'image', 'svg', 'animated')),
    is_default BOOLEAN DEFAULT 0,
    is_system BOOLEAN DEFAULT 0,
    is_unlocked BOOLEAN DEFAULT 0,
    price REAL,
    preview_image TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT
);

-- 表情项表
CREATE TABLE IF NOT EXISTS emoji_items (
    id TEXT PRIMARY KEY NOT NULL,
    emoji_set_id TEXT NOT NULL,
    emotion_id TEXT NOT NULL,
    unicode TEXT,
    image_url TEXT,
    svg_content TEXT,
    animation_data TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (emoji_set_id) REFERENCES emoji_sets(id) ON DELETE CASCADE,
    FOREIGN KEY (emotion_id) REFERENCES emotions(id) ON DELETE CASCADE
);

-- 表情集翻译表
CREATE TABLE IF NOT EXISTS emoji_set_translations (
    emoji_set_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (emoji_set_id, language_code),
    FOREIGN KEY (emoji_set_id) REFERENCES emoji_sets(id) ON DELETE CASCADE
);
```

## 4. 服务层

### 4.1 情绪数据服务

- `EmotionDataManager`: 管理情绪数据的加载、保存和编辑
- `defaultEmotionService`: 从数据库加载默认情绪数据
- `emotionService`: 提供情绪数据的CRUD操作

### 4.2 表情系统服务

- `EmojiSetManager`: 管理表情集的加载、保存和切换
- `emojiService`: 提供表情集和表情项的CRUD操作

## 5. 用户界面

### 5.1 情绪数据管理界面

- `EmotionDataEditor`: 编辑情绪数据
- `TierEditor`: 编辑情绪层级
- `EmotionEditor`: 编辑单个情绪
- `EmotionDataSelectionComponent`: 选择活动的情绪数据

### 5.2 表情系统界面

- `EmojiSetSelector`: 选择表情集
- `EmojiPicker`: 选择表情
- `EmojiPreview`: 预览表情

## 6. 工作流程

### 6.1 加载情绪数据

1. 应用启动时，`EmotionDataInitializer` 组件初始化 `EmotionDataManager`
2. `EmotionDataManager` 从本地存储加载情绪数据
3. 如果本地没有数据，`EmotionDataManager` 调用 `defaultEmotionService.loadDefaultEmotionDataFromDB` 从数据库加载默认情绪数据
4. `defaultEmotionService` 调用 `emotionService.getEmotionsData` 获取情绪数据
5. `defaultEmotionService` 调用 `emotionService.getEmojiSets` 和 `emotionService.getEmojiItemsForEmotion` 获取表情集和表情项
6. `defaultEmotionService` 将数据转换为 `EmotionData` 格式并返回
7. `EmotionDataManager` 将数据保存到本地存储

### 6.2 编辑情绪数据

1. 用户在设置页面选择情绪数据进行编辑
2. 系统导航到 `EmotionDataEditor` 页面
3. `EmotionDataEditor` 从 `EmotionDataManager` 获取情绪数据
4. 用户编辑情绪数据（名称、描述等）
5. 用户编辑情绪层级（使用 `TierEditor`）
6. 用户编辑情绪（使用 `EmotionEditor`）
7. 用户保存更改，`EmotionDataEditor` 调用 `EmotionDataManager.updateEmotionData`
8. `EmotionDataManager` 更新数据并保存到本地存储

### 6.3 选择表情

1. 用户在 `EmotionEditor` 中点击"选择表情"按钮
2. 系统显示 `EmojiPicker` 组件
3. 用户选择表情
4. `EmotionEditor` 更新情绪的表情
5. 用户保存更改，`EmotionEditor` 调用 `EmotionDataManager.updateEmotion`
6. `EmotionDataManager` 更新数据并保存到本地存储

## 7. 注意事项

1. **系统默认数据保护**：系统默认情绪数据（`isSystemDefault=true`）不允许修改或删除
2. **多语言支持**：所有用户界面文本和情绪名称都支持多语言
3. **多表情集支持**：每个情绪可以有多个表情集的表情
4. **向后兼容**：保留 `emotion.emoji` 字段以确保向后兼容性
5. **数据持久化**：情绪数据保存在本地存储中，系统默认数据从数据库加载

## 8. 未来扩展

1. **云同步**：将情绪数据同步到云端
2. **自定义表情集**：允许用户创建自定义表情集
3. **表情商店**：提供付费表情集
4. **动画表情**：支持动画表情
5. **情绪分析**：基于用户的情绪数据提供分析和建议
