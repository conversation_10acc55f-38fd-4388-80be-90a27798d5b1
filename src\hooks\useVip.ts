/**
 * VIP状态管理钩子
 * 实现离线在线混合模式的VIP功能管理
 */

import { trpc } from '@/lib/trpc';
import { useCallback, useEffect, useState } from 'react';
import { useAuth } from './useAuth';

// VIP计划类型
export interface VipPlan {
  id: string;
  name: string;
  duration: 'monthly' | 'yearly';
  price: number;
  currency: string;
  features: string[];
  stripePriceId?: string;
  discount?: number;
}

// VIP状态类型
export interface VipStatus {
  isVip: boolean;
  expiresAt?: Date;
  features: string[];
  unlockedSkins: string[];
  plan?: VipPlan;
}

// 购买结果类型
export interface PurchaseResult {
  success: boolean;
  transactionId?: string;
  error?: string;
}

interface UseVipReturn {
  // VIP状态
  vipStatus: VipStatus | null;
  isVip: boolean;
  isExpired: boolean;
  daysRemaining: number;

  // VIP计划
  vipPlans: VipPlan[];

  // 加载状态
  isLoading: boolean;
  error: string | null;

  // 网络状态
  isOnline: boolean;
  lastSyncTime: Date | null;

  // 操作方法
  refresh: () => Promise<void>;
  forceSync: () => Promise<void>;
  purchaseVip: (planId: string, paymentMethodId: string) => Promise<PurchaseResult>;

  // VIP功能检查
  hasFeature: (feature: string) => boolean;
  canAccessPremiumSkins: () => boolean;
  canExportData: () => boolean;
  hasAdvancedAnalytics: () => boolean;
}

/**
 * VIP状态管理钩子
 */
export const useVip = (): UseVipReturn => {
  const { isAuthenticated, user, isOnline } = useAuth();

  const [vipStatus, setVipStatus] = useState<VipStatus | null>(null);
  const [vipPlans, setVipPlans] = useState<VipPlan[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // 加载VIP状态
  const loadVipStatus = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setVipStatus(null);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // 优先从在线服务获取
      if (isOnline) {
        try {
          console.log('[useVip] Loading VIP status from online service...');
          // 使用tRPC获取VIP状态
          const result = await (trpc as any).getVipStatus.query({ userId: user?.id });

          if (result) {
            const status: VipStatus = {
              isVip: result.isVip,
              expiresAt: result.expiresAt ? new Date(result.expiresAt) : undefined,
              features: result.features || [],
              unlockedSkins: result.unlockedSkins || [],
            };

            setVipStatus(status);
            setLastSyncTime(new Date());

            // 缓存到本地存储
            localStorage.setItem('vip_status', JSON.stringify(status));

            console.log('[useVip] VIP status loaded from online service');
            return;
          }
        } catch (onlineError) {
          console.warn('[useVip] Online VIP status loading failed:', onlineError);
        }
      }

      // 回退到本地缓存
      try {
        const cachedStatus = localStorage.getItem('vip_status');
        if (cachedStatus) {
          const parsed = JSON.parse(cachedStatus);
          setVipStatus({
            ...parsed,
            expiresAt: parsed.expiresAt ? new Date(parsed.expiresAt) : undefined,
          });
          console.log('[useVip] VIP status loaded from cache');
        } else {
          // 默认非VIP状态
          setVipStatus({
            isVip: false,
            features: [],
            unlockedSkins: [],
          });
        }
      } catch (cacheError) {
        console.error('[useVip] Error loading VIP status from cache:', cacheError);
        setVipStatus({
          isVip: false,
          features: [],
          unlockedSkins: [],
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load VIP status';
      setError(errorMessage);
      console.error('[useVip] Error loading VIP status:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, isOnline]);

  // 加载VIP计划
  const loadVipPlans = useCallback(async () => {
    try {
      // 优先从在线服务获取
      if (isOnline) {
        try {
          console.log('[useVip] Loading VIP plans from online service...');
          // 使用tRPC获取VIP计划
          const result = await (trpc as any).getVipPlans.query();

          if (result?.success && Array.isArray(result.data)) {
            setVipPlans(result.data);

            // 缓存到本地存储
            localStorage.setItem('vip_plans', JSON.stringify(result.data));

            console.log('[useVip] VIP plans loaded from online service');
            return;
          }
        } catch (onlineError) {
          console.warn('[useVip] Online VIP plans loading failed:', onlineError);
        }
      }

      // 回退到本地缓存
      try {
        const cachedPlans = localStorage.getItem('vip_plans');
        if (cachedPlans) {
          setVipPlans(JSON.parse(cachedPlans));
          console.log('[useVip] VIP plans loaded from cache');
        } else {
          // 默认计划
          const defaultPlans: VipPlan[] = [
            {
              id: 'vip_monthly',
              name: 'VIP Monthly',
              duration: 'monthly',
              price: 9.99,
              currency: 'USD',
              features: [
                'unlimited_mood_entries',
                'advanced_analytics',
                'premium_skins',
                'export_data',
                'priority_support',
                'ad_free_experience',
              ],
            },
            {
              id: 'vip_yearly',
              name: 'VIP Yearly',
              duration: 'yearly',
              price: 99.99,
              currency: 'USD',
              features: [
                'unlimited_mood_entries',
                'advanced_analytics',
                'premium_skins',
                'export_data',
                'priority_support',
                'ad_free_experience',
                'yearly_discount',
              ],
              discount: 17, // 约17%折扣
            },
          ];
          setVipPlans(defaultPlans);
        }
      } catch (cacheError) {
        console.error('[useVip] Error loading VIP plans from cache:', cacheError);
      }
    } catch (error) {
      console.error('[useVip] Error loading VIP plans:', error);
    }
  }, [isOnline]);

  // 购买VIP
  const purchaseVip = useCallback(
    async (planId: string, paymentMethodId: string): Promise<PurchaseResult> => {
      try {
        if (!isAuthenticated) {
          throw new Error('User not authenticated');
        }

        if (!isOnline) {
          throw new Error('VIP purchase requires internet connection');
        }

        console.log('[useVip] Processing VIP purchase...', { planId, paymentMethodId });

        // 使用tRPC购买VIP
        const result = await (trpc as any).purchaseVip.mutate({
          planId,
          paymentMethodId,
        });

        if (result?.success) {
          // 购买成功后刷新VIP状态
          await loadVipStatus();

          console.log('[useVip] VIP purchase successful');
          return {
            success: true,
            transactionId: result.transactionId,
          };
        }
        throw new Error('Purchase failed');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Purchase failed';
        console.error('[useVip] VIP purchase error:', error);
        return {
          success: false,
          error: errorMessage,
        };
      }
    },
    [isAuthenticated, isOnline, loadVipStatus]
  );

  // 刷新数据
  const refresh = useCallback(async () => {
    await Promise.all([loadVipStatus(), loadVipPlans()]);
  }, [loadVipStatus, loadVipPlans]);

  // 强制同步
  const forceSync = useCallback(async () => {
    if (isOnline) {
      await refresh();
    }
  }, [isOnline, refresh]);

  // VIP功能检查方法
  const hasFeature = useCallback(
    (feature: string): boolean => {
      return vipStatus?.features.includes(feature) || false;
    },
    [vipStatus]
  );

  const canAccessPremiumSkins = useCallback((): boolean => {
    return hasFeature('premium_skins');
  }, [hasFeature]);

  const canExportData = useCallback((): boolean => {
    return hasFeature('export_data');
  }, [hasFeature]);

  const hasAdvancedAnalytics = useCallback((): boolean => {
    return hasFeature('advanced_analytics');
  }, [hasFeature]);

  // 计算VIP状态
  const isVip = vipStatus?.isVip || false;
  const isExpired = vipStatus?.expiresAt ? vipStatus.expiresAt < new Date() : false;
  const actualVipStatus = isVip && !isExpired;

  // 计算剩余天数
  const daysRemaining = vipStatus?.expiresAt
    ? Math.max(
        0,
        Math.ceil((vipStatus.expiresAt.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
      )
    : 0;

  // 初始化加载
  useEffect(() => {
    if (isAuthenticated) {
      refresh();
    } else {
      setVipStatus(null);
      setVipPlans([]);
    }
  }, [isAuthenticated, refresh]);

  // 网络恢复时同步
  useEffect(() => {
    if (isOnline && isAuthenticated) {
      forceSync();
    }
  }, [isOnline, isAuthenticated, forceSync]);

  return {
    // VIP状态
    vipStatus,
    isVip: actualVipStatus,
    isExpired,
    daysRemaining,

    // VIP计划
    vipPlans,

    // 加载状态
    isLoading,
    error,

    // 网络状态
    isOnline,
    lastSyncTime,

    // 操作方法
    refresh,
    forceSync,
    purchaseVip,

    // VIP功能检查
    hasFeature,
    canAccessPremiumSkins,
    canExportData,
    hasAdvancedAnalytics,
  };
};
