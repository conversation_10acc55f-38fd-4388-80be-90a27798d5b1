/**
 * 全面测试脚本
 * 测试所有三个层级（primary、secondary、tertiary）中 Neutral 和 Happy 之间的对比度
 */

// 模拟颜色工具函数
function parseRgb(color) {
  // 处理十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.substring(1);
    const r = Number.parseInt(hex.substring(0, 2), 16) / 255;
    const g = Number.parseInt(hex.substring(2, 4), 16) / 255;
    const b = Number.parseInt(hex.substring(4, 6), 16) / 255;
    return { r, g, b };
  }

  // 处理 rgb 格式
  const rgbMatch = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
  if (rgbMatch) {
    return {
      r: Number.parseInt(rgbMatch[1], 10) / 255,
      g: Number.parseInt(rgbMatch[2], 10) / 255,
      b: Number.parseInt(rgbMatch[3], 10) / 255,
    };
  }

  // 默认返回黑色
  return { r: 0, g: 0, b: 0 };
}

// 计算对比度
function calculateContrastRatio(color1, color2) {
  const rgb1 = parseRgb(color1);
  const rgb2 = parseRgb(color2);

  // 计算相对亮度
  const getLuminance = (rgb) => {
    const sRGB = {
      r: rgb.r,
      g: rgb.g,
      b: rgb.b,
    };

    // 转换为线性RGB
    const linearRGB = {
      r: sRGB.r <= 0.03928 ? sRGB.r / 12.92 : ((sRGB.r + 0.055) / 1.055) ** 2.4,
      g: sRGB.g <= 0.03928 ? sRGB.g / 12.92 : ((sRGB.g + 0.055) / 1.055) ** 2.4,
      b: sRGB.b <= 0.03928 ? sRGB.b / 12.92 : ((sRGB.b + 0.055) / 1.055) ** 2.4,
    };

    return 0.2126 * linearRGB.r + 0.7152 * linearRGB.g + 0.0722 * linearRGB.b;
  };

  const l1 = getLuminance(rgb1);
  const l2 = getLuminance(rgb2);

  // 计算对比度比率
  return (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
}

// 计算颜色相似度
function calculateColorSimilarity(color1, color2) {
  const rgb1 = parseRgb(color1);
  const rgb2 = parseRgb(color2);

  // 计算RGB空间中的欧几里得距离
  const rDiff = rgb1.r - rgb2.r;
  const gDiff = rgb1.g - rgb2.g;
  const bDiff = rgb1.b - rgb2.b;

  // 归一化距离值到0-1范围
  return Math.sqrt(rDiff * rDiff + gDiff * gDiff + bDiff * bDiff) / Math.sqrt(3);
}

// 游戏风格颜色 - 这些是我们更新后的颜色
const gameStyleColors = {
  // 浅色主题
  light: {
    primary: {
      neutral: '#D24BDD', // 紫色
      happy: '#4CAF50', // 绿色
    },
    secondary: {
      neutral: '#E066FF', // 亮紫色
      happy: '#66BB6A', // 浅绿色
    },
    tertiary: {
      neutral: '#C038CC', // 深紫色
      happy: '#388E3C', // 深绿色
    },
  },
  // 深色主题
  dark: {
    primary: {
      neutral: '#E066FF', // 亮紫色
      happy: '#66BB6A', // 浅绿色
    },
    secondary: {
      neutral: '#D24BDD', // 紫色
      happy: '#4CAF50', // 绿色
    },
    tertiary: {
      neutral: '#FF33CC', // 亮粉色
      happy: '#33CC33', // 亮绿色
    },
  },
};

// 原始问题颜色对
const originalColors = {
  neutral: '#CCCCCC',
  happy: '#CDCDCD',
};

// 测试函数
function testContrastRatio(color1, color2, name1, name2, tier, threshold) {
  const contrastRatio = calculateContrastRatio(color1, color2);
  const similarity = calculateColorSimilarity(color1, color2);

  console.log(`${name1}: ${color1}`);
  console.log(`${name2}: ${color2}`);
  console.log(`对比度: ${contrastRatio.to(3)}`);
  console.log(`相似度: ${similarity.to(3)}`);

  const passed = contrastRatio >= threshold;
  console.log(passed ? `✅ 通过 (高于 ${threshold})` : `❌ 失败 (低于 ${threshold})`);

  return { contrastRatio, passed };
}

// 运行全面测试
function runComprehensiveTest() {
  console.log('全面测试 Neutral 和 Happy 之间的对比度');
  console.log('===========================================');

  // 设置对比度阈值
  const thresholds = {
    primary: 1.5,
    secondary: 1.6,
    tertiary: 1.8,
    game: 2.0, // 游戏风格的更高要求
  };

  // 测试原始问题颜色对
  console.log('\n原始问题颜色对:');
  const originalResult = testContrastRatio(
    originalColors.neutral,
    originalColors.happy,
    'Neutral',
    'Happy',
    'primary',
    thresholds.primary
  );

  // 测试所有层级和主题
  const themes = ['light', 'dark'];
  const tiers = ['primary', 'secondary', 'tertiary'];

  let allPassed = true;

  themes.forEach((theme) => {
    tiers.forEach((tier) => {
      console.log(`\n游戏风格颜色 - ${theme === 'light' ? '浅色' : '深色'}主题 - ${tier}层级:`);

      const neutralColor = gameStyleColors[theme][tier].neutral;
      const happyColor = gameStyleColors[theme][tier].happy;

      // 使用游戏风格的更高阈值
      const threshold = thresholds.game;

      const result = testContrastRatio(
        neutralColor,
        happyColor,
        'Neutral',
        'Happy',
        tier,
        threshold
      );

      if (!result.passed) {
        allPassed = false;
      }
    });
  });

  console.log('\n===========================================');
  console.log(`测试总结: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);
  console.log('原始问题对比度:', originalResult.contrastRatio.to(3));
  console.log('===========================================');
}

// 运行测试
runComprehensiveTest();
