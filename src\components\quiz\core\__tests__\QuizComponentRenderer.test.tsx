/**
 * QuizComponentRenderer 组件测试
 * 测试Quiz组件渲染器的各种组件类型渲染功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QuizComponentRenderer } from '../QuizComponentRenderer';
import { QuizQuestionOption } from '@/types/schema/base';

// Mock数据
const mockOptions: QuizQuestionOption[] = [
  {
    id: 'option1',
    question_id: 'q1',
    option_text: '开心',
    option_value: 'happy',
    metadata: JSON.stringify({ emoji: '😊' }),
    display_order: 1,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'option2',
    question_id: 'q1',
    option_text: '难过',
    option_value: 'sad',
    metadata: JSON.stringify({ emoji: '😢' }),
    display_order: 2,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'option3',
    question_id: 'q1',
    option_text: '愤怒',
    option_value: 'angry',
    metadata: JSON.stringify({ emoji: '😠' }),
    display_order: 3,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

describe('QuizComponentRenderer', () => {
  let mockOnSelect: ReturnType<typeof vi.fn>;
  let mockOnBack: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockOnSelect = vi.fn();
    mockOnBack = vi.fn();
  });

  describe('Selector Component', () => {
    it('should render selector component with options', () => {
      render(
        <QuizComponentRenderer
          componentType="selector_component"
          config={{
            id: 'test-selector',
            question_text: '你现在的心情如何？',
            options: mockOptions,
            multiple: false,
            layout: 'grid'
          }}
          onSelect={mockOnSelect}
          onBack={mockOnBack}
        />
      );

      expect(screen.getByText('你现在的心情如何？')).toBeInTheDocument();
      expect(screen.getByText('开心')).toBeInTheDocument();
      expect(screen.getByText('难过')).toBeInTheDocument();
      expect(screen.getByText('愤怒')).toBeInTheDocument();
      expect(screen.getByText('返回')).toBeInTheDocument();
    });

    it('should call onSelect when option is clicked', () => {
      render(
        <QuizComponentRenderer
          componentType="selector_component"
          config={{
            id: 'test-selector',
            question_text: '你现在的心情如何？',
            options: mockOptions
          }}
          onSelect={mockOnSelect}
        />
      );

      fireEvent.click(screen.getByText('开心'));
      expect(mockOnSelect).toHaveBeenCalledWith(mockOptions[0]);
    });

    it('should call onBack when back button is clicked', () => {
      render(
        <QuizComponentRenderer
          componentType="selector_component"
          config={{
            id: 'test-selector',
            question_text: '你现在的心情如何？',
            options: mockOptions
          }}
          onSelect={mockOnSelect}
          onBack={mockOnBack}
        />
      );

      fireEvent.click(screen.getByText('返回'));
      expect(mockOnBack).toHaveBeenCalled();
    });

    it('should not render back button when onBack is not provided', () => {
      render(
        <QuizComponentRenderer
          componentType="selector_component"
          config={{
            id: 'test-selector',
            question_text: '你现在的心情如何？',
            options: mockOptions
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.queryByText('返回')).not.toBeInTheDocument();
    });
  });

  describe('Rating Component', () => {
    it('should render rating component with numeric scale', () => {
      render(
        <QuizComponentRenderer
          componentType="rating_component"
          config={{
            id: 'test-rating',
            question_text: '请评分您的满意度',
            options: [],
            scale_type: 'numeric',
            min_value: 1,
            max_value: 5
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('请评分您的满意度')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
    });

    it('should render star rating', () => {
      render(
        <QuizComponentRenderer
          componentType="rating_component"
          config={{
            id: 'test-rating',
            question_text: '请评分您的满意度',
            options: [],
            scale_type: 'star',
            min_value: 1,
            max_value: 5
          }}
          onSelect={mockOnSelect}
        />
      );

      const stars = screen.getAllByText('⭐');
      expect(stars).toHaveLength(5);
    });

    it('should call onSelect with rating value', () => {
      render(
        <QuizComponentRenderer
          componentType="rating_component"
          config={{
            id: 'test-rating',
            question_text: '请评分您的满意度',
            options: [],
            min_value: 1,
            max_value: 5
          }}
          onSelect={mockOnSelect}
        />
      );

      fireEvent.click(screen.getByText('3'));
      expect(mockOnSelect).toHaveBeenCalledWith(3);
    });
  });

  describe('Slider Component', () => {
    it('should render slider component', () => {
      render(
        <QuizComponentRenderer
          componentType="slider_component"
          config={{
            id: 'test-slider',
            question_text: '请选择强度',
            min_value: 0,
            max_value: 100,
            step_value: 10,
            default_value: 50,
            show_labels: true,
            show_value: true
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('请选择强度')).toBeInTheDocument();
      expect(screen.getByText('0')).toBeInTheDocument();
      expect(screen.getByText('100')).toBeInTheDocument();
      expect(screen.getByText('当前值: 50')).toBeInTheDocument();
      expect(screen.getByText('确认')).toBeInTheDocument();
    });

    it('should update slider value and call onSelect', () => {
      render(
        <QuizComponentRenderer
          componentType="slider_component"
          config={{
            id: 'test-slider',
            question_text: '请选择强度',
            min_value: 0,
            max_value: 100,
            default_value: 50
          }}
          onSelect={mockOnSelect}
        />
      );

      const slider = screen.getByRole('slider');
      fireEvent.change(slider, { target: { value: '75' } });
      
      expect(screen.getByText('当前值: 75')).toBeInTheDocument();

      fireEvent.click(screen.getByText('确认'));
      expect(mockOnSelect).toHaveBeenCalledWith(75);
    });
  });

  describe('Text Input Component', () => {
    it('should render text input component', () => {
      render(
        <QuizComponentRenderer
          componentType="text_input_component"
          config={{
            id: 'test-input',
            question_text: '请描述您的感受',
            input_type: 'text',
            placeholder: '请输入...',
            required: true
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('请描述您的感受')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('请输入...')).toBeInTheDocument();
      expect(screen.getByText('提交')).toBeInTheDocument();
    });

    it('should render textarea when input_type is textarea', () => {
      render(
        <QuizComponentRenderer
          componentType="text_input_component"
          config={{
            id: 'test-input',
            question_text: '请详细描述',
            input_type: 'textarea',
            placeholder: '请详细输入...'
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('请详细输入...')).toBeInTheDocument();
    });

    it('should handle text input and submission', () => {
      render(
        <QuizComponentRenderer
          componentType="text_input_component"
          config={{
            id: 'test-input',
            question_text: '请描述您的感受',
            input_type: 'text'
          }}
          onSelect={mockOnSelect}
        />
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '我感觉很好' } });
      fireEvent.click(screen.getByText('提交'));

      expect(mockOnSelect).toHaveBeenCalledWith('我感觉很好');
    });

    it('should show alert for required empty input', () => {
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});

      render(
        <QuizComponentRenderer
          componentType="text_input_component"
          config={{
            id: 'test-input',
            question_text: '请描述您的感受',
            required: true
          }}
          onSelect={mockOnSelect}
        />
      );

      fireEvent.click(screen.getByText('提交'));
      expect(alertSpy).toHaveBeenCalledWith('请输入内容');
      expect(mockOnSelect).not.toHaveBeenCalled();

      alertSpy.mockRestore();
    });
  });

  describe('Card Component', () => {
    it('should render card component', () => {
      render(
        <QuizComponentRenderer
          componentType="card_component"
          config={{
            id: 'test-cards',
            question_text: '选择情绪卡片',
            options: mockOptions,
            card_layout: 'grid',
            columns: 2,
            card_size: 'medium'
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('选择情绪卡片')).toBeInTheDocument();
      expect(screen.getByText('开心')).toBeInTheDocument();
      expect(screen.getByText('难过')).toBeInTheDocument();
      expect(screen.getByText('愤怒')).toBeInTheDocument();
    });

    it('should call onSelect when card is clicked', () => {
      render(
        <QuizComponentRenderer
          componentType="card_component"
          config={{
            id: 'test-cards',
            question_text: '选择情绪卡片',
            options: mockOptions
          }}
          onSelect={mockOnSelect}
        />
      );

      fireEvent.click(screen.getByText('开心'));
      expect(mockOnSelect).toHaveBeenCalledWith(mockOptions[0]);
    });
  });

  describe('Bubble Component', () => {
    it('should render bubble component', () => {
      render(
        <QuizComponentRenderer
          componentType="bubble_component"
          config={{
            id: 'test-bubbles',
            question_text: '选择情绪气泡',
            options: mockOptions,
            bubble_layout: 'cluster',
            animation: true
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('选择情绪气泡')).toBeInTheDocument();
      expect(screen.getByText('开心')).toBeInTheDocument();
      expect(screen.getByText('难过')).toBeInTheDocument();
      expect(screen.getByText('愤怒')).toBeInTheDocument();
    });

    it('should call onSelect when bubble is clicked', () => {
      render(
        <QuizComponentRenderer
          componentType="bubble_component"
          config={{
            id: 'test-bubbles',
            question_text: '选择情绪气泡',
            options: mockOptions
          }}
          onSelect={mockOnSelect}
        />
      );

      fireEvent.click(screen.getByText('开心'));
      expect(mockOnSelect).toHaveBeenCalledWith(mockOptions[0]);
    });
  });

  describe('Wheel Component', () => {
    it('should render wheel component placeholder', () => {
      render(
        <QuizComponentRenderer
          componentType="wheel_component"
          config={{
            id: 'test-wheel',
            question_text: '选择情绪轮盘',
            options: mockOptions,
            size: 300
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('选择情绪轮盘')).toBeInTheDocument();
      expect(screen.getByText('轮盘组件 (待集成)')).toBeInTheDocument();
      expect(screen.getByText('开心')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should render error message for unsupported component type', () => {
      render(
        <QuizComponentRenderer
          componentType={'unsupported_component' as any}
          config={{
            id: 'test-error',
            question_text: '测试错误'
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('不支持的组件类型: unsupported_component')).toBeInTheDocument();
    });

    it('should handle invalid metadata gracefully', () => {
      const optionsWithInvalidMetadata = [
        {
          ...mockOptions[0],
          metadata: 'invalid-json{'
        }
      ];

      render(
        <QuizComponentRenderer
          componentType="selector_component"
          config={{
            id: 'test-selector',
            question_text: '测试无效metadata',
            options: optionsWithInvalidMetadata
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('开心')).toBeInTheDocument();
      expect(screen.getByText('😊')).toBeInTheDocument(); // 默认emoji
    });

    it('should handle missing metadata', () => {
      const optionsWithoutMetadata = [
        {
          ...mockOptions[0],
          metadata: null as any
        }
      ];

      render(
        <QuizComponentRenderer
          componentType="card_component"
          config={{
            id: 'test-cards',
            question_text: '测试无metadata',
            options: optionsWithoutMetadata
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('开心')).toBeInTheDocument();
      expect(screen.getByText('😊')).toBeInTheDocument(); // 默认emoji
    });
  });

  describe('Custom Props', () => {
    it('should apply custom className', () => {
      const { container } = render(
        <QuizComponentRenderer
          componentType="selector_component"
          config={{
            id: 'test-selector',
            question_text: '测试自定义类名',
            options: mockOptions
          }}
          onSelect={mockOnSelect}
          className="custom-quiz-renderer"
        />
      );

      expect(container.firstChild).toHaveClass('quiz-component-renderer', 'custom-quiz-renderer');
    });

    it('should work without personalizationConfig', () => {
      render(
        <QuizComponentRenderer
          componentType="selector_component"
          config={{
            id: 'test-selector',
            question_text: '测试无个性化配置',
            options: mockOptions
          }}
          onSelect={mockOnSelect}
        />
      );

      expect(screen.getByText('测试无个性化配置')).toBeInTheDocument();
    });
  });
});
