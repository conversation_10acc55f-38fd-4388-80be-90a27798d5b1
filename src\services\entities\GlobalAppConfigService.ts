/**
 * 全局应用配置 Service (离线服务)
 * 提供本地SQLite数据库的全局应用设置管理
 */

import { BaseService } from '../base/BaseService';
import { GlobalAppConfigRepository } from './GlobalAppConfigRepository';
import { GlobalAppConfig } from '../../types/schema/base';
import { CreateGlobalAppConfigInput, UpdateGlobalAppConfigInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class GlobalAppConfigService extends BaseService<
  GlobalAppConfig,
  CreateGlobalAppConfigInput,
  UpdateGlobalAppConfigInput
> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new GlobalAppConfigRepository(db);
    super(repository);
  }

  /**
   * 获取用户的活跃全局配置
   */
  async getActiveUserConfig(userId: string, configName: string = 'default'): Promise<ServiceResult<GlobalAppConfig>> {
    try {
      const config = await (this.repository as GlobalAppConfigRepository).getActiveUserConfig(userId, configName);

      if (!config) {
        // 如果没有配置，创建默认配置
        const defaultConfigResult = await this.createDefaultConfig(userId, configName);
        if (defaultConfigResult.success) {
          return defaultConfigResult;
        }
        return this.createErrorResult('Failed to create default config');
      }

      return this.createSuccessResult(config);
    } catch (error) {
      return this.createErrorResult('Failed to get user config', error);
    }
  }

  /**
   * 获取用户的所有全局配置
   */
  async getUserConfigs(userId: string): Promise<ServiceResult<GlobalAppConfig[]>> {
    try {
      const configs = await (this.repository as GlobalAppConfigRepository).getUserConfigs(userId);
      return this.createSuccessResult(configs);
    } catch (error) {
      return this.createErrorResult('Failed to get user configs', error);
    }
  }

  /**
   * 更新用户配置
   */
  async updateUserConfig(userId: string, updates: UpdateGlobalAppConfigInput): Promise<ServiceResult<GlobalAppConfig>> {
    try {
      // 验证更新数据
      await this.validateUpdate(updates);

      const configName = 'default'; // 目前只支持默认配置
      const updatedConfig = await (this.repository as GlobalAppConfigRepository).updateUserConfig(userId, configName, updates);

      if (!updatedConfig) {
        return this.createErrorResult('Failed to update user config');
      }

      // 发射业务事件
      this.emit('globalConfigUpdated', updatedConfig);

      return this.createSuccessResult(updatedConfig);
    } catch (error) {
      return this.createErrorResult('Failed to update user config', error);
    }
  }

  /**
   * 创建用户配置
   */
  async createUserConfig(data: CreateGlobalAppConfigInput): Promise<ServiceResult<GlobalAppConfig>> {
    try {
      // 验证创建数据
      await this.validateCreate(data);

      const config = await this.repository.create(data);

      // 发射业务事件
      this.emit('globalConfigCreated', config);

      return this.createSuccessResult(config);
    } catch (error) {
      return this.createErrorResult('Failed to create user config', error);
    }
  }

  /**
   * 创建默认配置
   */
  private async createDefaultConfig(userId: string, configName: string): Promise<ServiceResult<GlobalAppConfig>> {
    try {
      const defaultConfigData: CreateGlobalAppConfigInput = {
        name: configName,
        user_id: userId,
        is_active: true,
        theme_mode: 'system',
        language: 'zh-CN',
        accessibility: JSON.stringify({
          high_contrast: false,
          large_text: false,
          reduce_motion: false,
          screen_reader_support: false
        }),
        notifications_enabled: true,
        sound_enabled: true
      };

      return await this.createUserConfig(defaultConfigData);
    } catch (error) {
      return this.createErrorResult('Failed to create default config', error);
    }
  }

  /**
   * 重置用户配置为默认值
   */
  async resetToDefault(userId: string, configName: string = 'default'): Promise<ServiceResult<GlobalAppConfig>> {
    try {
      const defaultUpdates: UpdateGlobalAppConfigInput = {
        theme_mode: 'system',
        language: 'zh-CN',
        accessibility: JSON.stringify({
          high_contrast: false,
          large_text: false,
          reduce_motion: false,
          screen_reader_support: false
        }),
        notifications_enabled: true,
        sound_enabled: true
      };

      return await this.updateUserConfig(userId, defaultUpdates);
    } catch (error) {
      return this.createErrorResult('Failed to reset config to default', error);
    }
  }

  // 实现抽象方法
  protected async validateCreate(data: CreateGlobalAppConfigInput): Promise<void> {
    if (!data.user_id) {
      throw new Error('User ID is required');
    }
    if (!data.name) {
      throw new Error('Config name is required');
    }
    if (data.theme_mode && !this.isValidThemeMode(data.theme_mode)) {
      throw new Error('Invalid theme mode');
    }
    if (data.language && !this.isValidLanguage(data.language)) {
      throw new Error('Invalid language');
    }
    if (data.accessibility && !this.isValidAccessibilityConfig(data.accessibility)) {
      throw new Error('Invalid accessibility config');
    }
  }

  protected async validateUpdate(data: UpdateGlobalAppConfigInput): Promise<void> {
    if (data.theme_mode && !this.isValidThemeMode(data.theme_mode)) {
      throw new Error('Invalid theme mode');
    }
    if (data.language && !this.isValidLanguage(data.language)) {
      throw new Error('Invalid language');
    }
    if (data.accessibility && !this.isValidAccessibilityConfig(data.accessibility)) {
      throw new Error('Invalid accessibility config');
    }
  }

  // 私有验证方法
  private isValidThemeMode(mode: string): boolean {
    return ['light', 'dark', 'system'].includes(mode);
  }

  private isValidLanguage(language: string): boolean {
    return ['zh-CN', 'en-US'].includes(language);
  }

  private isValidAccessibilityConfig(config: string): boolean {
    try {
      const parsed = JSON.parse(config);
      return (
        typeof parsed === 'object' &&
        typeof parsed.high_contrast === 'boolean' &&
        typeof parsed.large_text === 'boolean' &&
        typeof parsed.reduce_motion === 'boolean' &&
        typeof parsed.screen_reader_support === 'boolean'
      );
    } catch {
      return false;
    }
  }
}
