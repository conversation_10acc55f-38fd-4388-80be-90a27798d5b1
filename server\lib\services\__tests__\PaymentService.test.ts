/**
 * PaymentService 单元测试
 * 验证真实 Stripe 集成的实现
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { PaymentService } from '../PaymentService';

// Mock Stripe
const mockStripe = {
  customers: {
    create: vi.fn(),
    retrieve: vi.fn()
  },
  paymentMethods: {
    attach: vi.fn()
  },
  paymentIntents: {
    create: vi.fn(),
    confirm: vi.fn()
  },
  subscriptions: {
    create: vi.fn()
  },
  webhooks: {
    constructEvent: vi.fn()
  }
};

vi.mock('stripe', () => {
  return {
    default: vi.fn(() => mockStripe)
  };
});

// Mock 数据库操作
vi.mock('../../database/index', () => ({
  executeQuery: vi.fn(),
  batchStatements: vi.fn()
}));

// 导入 Mock 后的模块
import { executeQuery, batchStatements } from '../../database/index';

const mockExecuteQuery = vi.mocked(executeQuery);
const mockBatchStatements = vi.mocked(batchStatements);

// Mock 环境变量
process.env.STRIPE_SECRET_KEY = 'sk_test_mock_key';
process.env.STRIPE_PUBLISHABLE_KEY = 'pk_test_mock_key';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_mock_secret';

describe('PaymentService', () => {
  let paymentService: PaymentService;

  beforeEach(() => {
    paymentService = PaymentService.getInstance();
    vi.clearAllMocks();
  });

  describe('getVipPlans', () => {
    it('should return VIP plans from database', async () => {
      const mockPlans = [
        {
          id: 'vip_monthly',
          name: 'VIP Monthly',
          description: 'Monthly VIP subscription',
          price: 9.99,
          currency: 'USD',
          duration: 'monthly',
          features: '["unlimited_mood_entries", "advanced_analytics"]',
          stripePriceId: 'price_monthly_test',
          is_active: 1,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ];

      mockExecuteQuery.mockResolvedValue({
        rows: mockPlans,
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await paymentService.getVipPlans();

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].id).toBe('vip_monthly');
      expect(result.data![0].features).toEqual(['unlimited_mood_entries', 'advanced_analytics']);
    });

    it('should handle database errors gracefully', async () => {
      mockExecuteQuery.mockRejectedValue(new Error('Database error'));

      const result = await paymentService.getVipPlans();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database error');
    });
  });

  describe('createVipSubscription', () => {
    it('should create a VIP subscription successfully', async () => {
      const userId = 'user123';
      const planId = 'vip_monthly';
      const paymentMethodId = 'pm_test_123';

      // Mock user exists
      mockExecuteQuery
        .mockResolvedValueOnce({
          rows: [{ id: userId, email: '<EMAIL>', stripe_customer_id: null }],
          changes: 0,
          lastInsertRowid: 0
        })
        // Mock VIP plans
        .mockResolvedValueOnce({
          rows: [{
            id: 'vip_monthly',
            name: 'VIP Monthly',
            price: 9.99,
            currency: 'USD',
            duration: 'monthly',
            features: '[]',
            stripePriceId: 'price_monthly_test',
            is_active: 1
          }],
          changes: 0,
          lastInsertRowid: 0
        })
        // Mock customer update
        .mockResolvedValueOnce({
          rows: [],
          changes: 1,
          lastInsertRowid: 0
        });

      // Mock Stripe operations
      mockStripe.customers.create.mockResolvedValue({
        id: 'cus_test_123',
        email: '<EMAIL>'
      });

      mockStripe.paymentMethods.attach.mockResolvedValue({});

      mockStripe.subscriptions.create.mockResolvedValue({
        id: 'sub_test_123',
        status: 'active',
        latest_invoice: {
          payment_intent: {
            client_secret: 'pi_test_client_secret'
          }
        },
        metadata: {
          userId,
          planId
        }
      });

      const result = await paymentService.createVipSubscription(userId, planId, paymentMethodId);

      expect(result.success).toBe(true);
      expect(result.subscription?.id).toBe('sub_test_123');
      expect(result.clientSecret).toBe('pi_test_client_secret');

      // 验证 Stripe 调用
      expect(mockStripe.customers.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        metadata: {
          userId,
          source: 'mindful_mood_app'
        }
      });

      expect(mockStripe.subscriptions.create).toHaveBeenCalledWith({
        customer: 'cus_test_123',
        items: [{ price: 'price_monthly_test' }],
        default_payment_method: paymentMethodId,
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          userId,
          planId,
          source: 'mindful_mood_app'
        }
      });
    });

    it('should handle invalid plan gracefully', async () => {
      const userId = 'user123';
      const planId = 'invalid_plan';
      const paymentMethodId = 'pm_test_123';

      // Mock user exists
      mockExecuteQuery
        .mockResolvedValueOnce({
          rows: [{ id: userId, email: '<EMAIL>' }],
          changes: 0,
          lastInsertRowid: 0
        })
        // Mock empty plans
        .mockResolvedValueOnce({
          rows: [],
          changes: 0,
          lastInsertRowid: 0
        });

      const result = await paymentService.createVipSubscription(userId, planId, paymentMethodId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid plan');
    });
  });

  describe('processVipPurchase', () => {
    it('should process VIP purchase successfully', async () => {
      const userId = 'user123';
      const planId = 'vip_monthly';
      const paymentMethodId = 'pm_test_123';

      // Mock user exists
      mockExecuteQuery
        .mockResolvedValueOnce({
          rows: [{ id: userId, email: '<EMAIL>' }],
          changes: 0,
          lastInsertRowid: 0
        })
        // Mock transaction creation
        .mockResolvedValueOnce({
          rows: [],
          changes: 1,
          lastInsertRowid: 0
        });

      // Mock VIP plans
      paymentService.getVipPlans = vi.fn().mockResolvedValue({
        success: true,
        data: [{
          id: 'vip_monthly',
          name: 'VIP Monthly',
          price: 9.99,
          currency: 'USD',
          duration: 'monthly'
        }]
      });

      // Mock Stripe PaymentIntent
      mockStripe.paymentIntents.create.mockResolvedValue({
        id: 'pi_test_123',
        status: 'succeeded',
        client_secret: 'pi_test_client_secret'
      });

      // Mock batch operations
      mockBatchStatements.mockResolvedValue([{
        rows: [],
        changes: 1,
        lastInsertRowid: 0
      }]);

      // Mock unlock VIP skins
      paymentService['unlockVipSkins'] = vi.fn().mockResolvedValue(undefined);

      const result = await paymentService.processVipPurchase(userId, planId, paymentMethodId);

      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
      expect(result.paymentIntentId).toBe('pi_test_123');

      // 验证 PaymentIntent 创建
      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith({
        amount: 999, // 9.99 * 100
        currency: 'usd',
        payment_method: paymentMethodId,
        confirmation_method: 'manual',
        confirm: true,
        description: expect.stringContaining('VIP Monthly subscription for user user123'),
        metadata: {
          transactionId: expect.any(String),
          source: 'mindful_mood_app'
        },
        return_url: expect.any(String)
      });
    });
  });

  describe('handleWebhook', () => {
    it('should handle payment_intent.succeeded webhook', async () => {
      const payload = 'webhook_payload';
      const signature = 'webhook_signature';

      const mockEvent = {
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test_123',
            metadata: {
              transactionId: 'txn_test_123'
            }
          }
        }
      };

      mockStripe.webhooks.constructEvent.mockReturnValue(mockEvent);

      mockExecuteQuery.mockResolvedValue({
        rows: [],
        changes: 1,
        lastInsertRowid: 0
      });

      const result = await paymentService.handleWebhook(payload, signature);

      expect(result.success).toBe(true);
      expect(mockStripe.webhooks.constructEvent).toHaveBeenCalledWith(
        payload,
        signature,
        'whsec_mock_secret'
      );

      // 验证交易状态更新
      expect(mockExecuteQuery).toHaveBeenCalledWith({
        sql: expect.stringContaining('UPDATE payment_transactions'),
        args: expect.arrayContaining(['completed', 'pi_test_123', 'txn_test_123'])
      });
    });

    it('should handle webhook signature verification failure', async () => {
      const payload = 'invalid_payload';
      const signature = 'invalid_signature';

      mockStripe.webhooks.constructEvent.mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      const result = await paymentService.handleWebhook(payload, signature);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid signature');
    });
  });

  describe('getPublishableKey', () => {
    it('should return the publishable key', () => {
      const key = paymentService.getPublishableKey();
      expect(key).toBe('pk_test_mock_key');
    });
  });
});
