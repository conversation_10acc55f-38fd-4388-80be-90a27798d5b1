/**
 * Emoji 映射编辑器组件
 * 用于管理情绪与 emoji 的映射关系
 */

import { useLanguage } from '@/contexts/LanguageContext';
import { Services } from '@/services';
import type { EmojiItem, Emotion } from '@/types';
import EmojiPicker from 'emoji-picker-react';
import { Save, Search, Smile } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import EmojiDisplay from '../emoji/EmojiDisplay';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';

interface EmojiMappingEditorProps {
  emotions: Emotion[];
  emojiSetId: string;
  onEmojiChange: (emotionId: string, emojiItem: EmojiItem) => void;
}

interface EmotionEmojiMapping {
  [emotionId: string]: EmojiItem;
}

/**
 * Emoji 映射编辑器组件
 */
export const EmojiMappingEditor: React.FC<EmojiMappingEditorProps> = ({
  emotions,
  emojiSetId,
  onEmojiChange,
}) => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [emojiMappings, setEmojiMappings] = useState<EmotionEmojiMapping>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // 加载当前表情集的 emoji 映射
  useEffect(() => {
    const loadEmojiMappings = async () => {
      if (!emojiSetId || emotions.length === 0) return;

      setIsLoading(true);
      try {
        const emojiItemService = await Services.emojiItem();
        const mappings: EmotionEmojiMapping = {};

        // 为每个情绪获取对应的 emoji 项
        for (const emotion of emotions) {
          const result = await emojiItemService.getByEmojiSetAndEmotion(emojiSetId, emotion.id);
          if (result.success && result.data) {
            mappings[emotion.id] = result.data;
          } else {
            // 如果没有找到，创建一个默认的 emoji 项
            mappings[emotion.id] = {
              id: `${emojiSetId}-${emotion.id}`,
              emoji_set_id: emojiSetId,
              emotion_id: emotion.id,
              unicode: emotion.emoji || '😶', // 向后兼容
              created_at: new Date().toISOString(),
            };
          }
        }

        setEmojiMappings(mappings);
      } catch (error) {
        console.error('Error loading emoji mappings:', error);
        toast.error(t('emoji_mapping.load_error', { fallback: '加载 emoji 映射失败' }));
      } finally {
        setIsLoading(false);
      }
    };

    loadEmojiMappings();
  }, [emojiSetId, emotions, t]);

  // 过滤情绪
  const filteredEmotions = emotions.filter(
    (emotion) =>
      emotion.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emotion.keywords?.includes(searchTerm.toLowerCase())
  );

  // 处理 emoji 选择
  const handleEmojiSelect = (emotionId: string, emojiData: any) => {
    const newEmojiItem: EmojiItem = {
      id: `${emojiSetId}-${emotionId}`,
      emoji_set_id: emojiSetId,
      emotion_id: emotionId,
      unicode: emojiData.emoji, // 向后兼容
      alt_text: emojiData.names?.[0] || emojiData.emoji,
      created_at: new Date().toISOString(),
    };

    // 更新本地状态
    setEmojiMappings((prev) => ({
      ...prev,
      [emotionId]: newEmojiItem,
    }));

    // 通知父组件
    onEmojiChange(emotionId, newEmojiItem);
  };

  // 保存所有映射
  const handleSaveAll = async () => {
    setIsSaving(true);
    try {
      const emojiItemService = await Services.emojiItem();

      for (const [emotionId, emojiItem] of Object.entries(emojiMappings)) {
        // 检查是否已存在
        const existingResult = await emojiItemService.getByEmojiSetAndEmotion(
          emojiSetId,
          emotionId
        );

        if (existingResult.success && existingResult.data) {
          // 更新现有项
          await emojiItemService.update(existingResult.data.id, {
            alt_text: emojiItem.alt_text,
          });
        } else {
          // 创建新项
          await emojiItemService.create({
            emoji_set_id: emojiSetId,
            emotion_id: emotionId,
            unicode_char: emojiItem.unicode_char,
            alt_text: emojiItem.alt_text,
          });
        }
      }

      toast.success(t('emoji_mapping.save_success', { fallback: 'Emoji 映射保存成功' }));
    } catch (error) {
      console.error('Error saving emoji mappings:', error);
      toast.error(t('emoji_mapping.save_error', { fallback: '保存 emoji 映射失败' }));
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2" />
            <p className="text-muted-foreground">
              {t('emoji_mapping.loading', { fallback: '加载 emoji 映射中...' })}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* 搜索和操作栏 */}
      <div className="flex items-center gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('emoji_mapping.search_emotions', { fallback: '搜索情绪...' })}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button onClick={handleSaveAll} disabled={isSaving}>
          <Save className="h-4 w-4 mr-2" />
          {isSaving
            ? t('common.saving', { fallback: '保存中...' })
            : t('common.save_all', { fallback: '保存全部' })}
        </Button>
      </div>

      {/* 情绪列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredEmotions.map((emotion) => {
          const currentEmoji = emojiMappings[emotion.id];

          return (
            <Card key={emotion.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium">{emotion.name}</h4>
                    {emotion.description && (
                      <p className="text-sm text-muted-foreground mt-1">{emotion.description}</p>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {/* 当前 emoji 显示 */}
                    <div className="text-2xl">
                      <EmojiDisplay
                        emojiItem={currentEmoji}
                        fallbackEmoji={emotion.emoji || '😶'}
                        size="lg"
                      />
                    </div>

                    {/* emoji 选择器 */}
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Smile className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0" align="end">
                        <EmojiPicker
                          onEmojiClick={(emojiData) => handleEmojiSelect(emotion.id, emojiData)}
                          width={300}
                          height={400}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredEmotions.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">
              {searchTerm
                ? t('emoji_mapping.no_emotions_found', { fallback: '未找到匹配的情绪' })
                : t('emoji_mapping.no_emotions', { fallback: '没有可用的情绪' })}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
