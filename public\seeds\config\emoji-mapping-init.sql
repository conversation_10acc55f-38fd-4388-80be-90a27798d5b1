-- ============================================================================
-- 量表预置默认EMOJI SET映射配置
-- ============================================================================
--
-- 此文件包含量表(Quiz包)的预置默认emoji映射配置
-- 包括：
-- 1. 各Quiz包的默认emoji映射
-- 2. 系统预定义的emoji主题集合
-- 3. 不同情绪层级的emoji映射规则
--
-- 注意：这些是系统级别的默认配置，用户个性化配置在test-user-data目录中

-- ============================================================================
-- QUIZ包默认EMOJI映射配置
-- ============================================================================

-- 情绪追踪问卷包的默认emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'emoji_config_mood_track_v1',
    'mood_track_branching_v1',
    'default_emoji_mapping',
    '{
        "theme_info": {
            "theme_name": "default_emotions",
            "theme_description": "Default emoji mapping for emotion tracking",
            "supports_alternatives": true,
            "supports_user_customization": true,
            "supports_question_overrides": true
        },
        "layer1_primary_emotions": {
            "happy": {
                "emoji": "😊",
                "color": "#4CAF50",
                "animation": "bounce",
                "alternatives": ["😄", "😃", "🙂", "😌", "🥰"]
            },
            "surprised": {
                "emoji": "😲",
                "color": "#FF9800", 
                "animation": "pop",
                "alternatives": ["😮", "😯", "🤯", "😦", "😧"]
            },
            "bad": {
                "emoji": "😞",
                "color": "#9E9E9E",
                "animation": "fade",
                "alternatives": ["😔", "😟", "😕", "🙁", "😣"]
            },
            "fearful": {
                "emoji": "😨",
                "color": "#9C27B0",
                "animation": "tremble",
                "alternatives": ["😰", "😱", "😧", "😟", "😖"]
            },
            "angry": {
                "emoji": "😠",
                "color": "#F44336",
                "animation": "shake",
                "alternatives": ["😡", "🤬", "😤", "😒", "🙄"]
            },
            "disgusted": {
                "emoji": "🤢",
                "color": "#795548",
                "animation": "recoil",
                "alternatives": ["🤮", "😖", "😣", "🙄", "😤"]
            },
            "sad": {
                "emoji": "😢",
                "color": "#2196F3",
                "animation": "drop",
                "alternatives": ["😭", "😞", "☹️", "😔", "😟"]
            }
        },
        "layer2_secondary_emotions": {
            "playful": {
                "emoji": "😜",
                "color": "#4CAF50",
                "animation": "wiggle",
                "alternatives": ["😋", "🤪", "😝", "🤭", "😏"]
            },
            "content": {
                "emoji": "😌",
                "color": "#4CAF50",
                "animation": "glow",
                "alternatives": ["😊", "🙂", "😇", "🥰", "😍"]
            },
            "interested": {
                "emoji": "🤔",
                "color": "#4CAF50",
                "animation": "think",
                "alternatives": ["🧐", "👀", "💭", "🔍", "📚"]
            },
            "proud": {
                "emoji": "😤",
                "color": "#4CAF50",
                "animation": "puff",
                "alternatives": ["😏", "🤗", "💪", "🏆", "⭐"]
            },
            "accepted": {
                "emoji": "🤗",
                "color": "#4CAF50",
                "animation": "embrace",
                "alternatives": ["😊", "🥰", "💖", "🌟", "✨"]
            },
            "powerful": {
                "emoji": "💪",
                "color": "#4CAF50",
                "animation": "flex",
                "alternatives": ["🦾", "⚡", "🔥", "🚀", "👑"]
            }
        },
        "layer3_tertiary_emotions": {
            "aroused": {
                "emoji": "🤩",
                "color": "#FF6B35",
                "animation": "pulse",
                "alternatives": ["⚡", "🔥", "💥", "✨", "🚀"]
            },
            "cheeky": {
                "emoji": "😏",
                "color": "#FF6B35",
                "animation": "wink",
                "alternatives": ["😉", "😎", "🤫", "😈", "🤭"]
            },
            "free": {
                "emoji": "🕊️",
                "color": "#87CEEB",
                "animation": "float",
                "alternatives": ["🦋", "🌟", "🌈", "☁️", "🌸"]
            },
            "joyful": {
                "emoji": "😆",
                "color": "#FFD700",
                "animation": "celebrate",
                "alternatives": ["🤣", "😂", "😁", "🎉", "🥳"]
            },
            "ecstatic": {
                "emoji": "🤯",
                "color": "#FFD700",
                "animation": "explode",
                "alternatives": ["🎆", "💥", "🌟", "✨", "🎊"]
            },
            "blissful": {
                "emoji": "😇",
                "color": "#FFD700",
                "animation": "halo",
                "alternatives": ["🥰", "😌", "🌟", "✨", "💫"]
            }
        }
    }',
    '1.0',
    'Default emoji mapping configuration for mood tracking quiz pack',
    1,
    1,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 日常情绪检查包的默认emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'emoji_config_daily_check_v1',
    'daily_emotion_check',
    'default_emoji_mapping',
    '{
        "theme_info": {
            "theme_name": "daily_simple",
            "theme_description": "Simple emoji mapping for daily emotion check",
            "supports_alternatives": true,
            "supports_user_customization": true
        },
        "basic_emotions": {
            "great": {
                "emoji": "🤩",
                "color": "#4CAF50",
                "animation": "bounce",
                "alternatives": ["😄", "🥳", "🌟", "✨", "🎉"]
            },
            "good": {
                "emoji": "😊",
                "color": "#8BC34A",
                "animation": "glow",
                "alternatives": ["🙂", "😌", "😇", "🥰", "😍"]
            },
            "okay": {
                "emoji": "😐",
                "color": "#FFC107",
                "animation": "none",
                "alternatives": ["😑", "🤔", "😶", "😏", "🙃"]
            },
            "not_great": {
                "emoji": "😔",
                "color": "#FF9800",
                "animation": "fade",
                "alternatives": ["😞", "😟", "😕", "🙁", "😣"]
            },
            "bad": {
                "emoji": "😢",
                "color": "#F44336",
                "animation": "drop",
                "alternatives": ["😭", "😞", "☹️", "😔", "😖"]
            }
        }
    }',
    '1.0',
    'Simple emoji mapping for daily emotion check',
    1,
    1,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- ============================================================================
-- 系统EMOJI主题配置
-- ============================================================================

-- 动物主题emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'theme_animals_v1',
    'system_themes',
    'emoji_theme',
    '{
        "theme_info": {
            "theme_name": "animals",
            "theme_description": "Animal-based emoji mapping",
            "category": "fun"
        },
        "mappings": {
            "happy": {"emoji": "🐶", "alternatives": ["🐱", "🐰", "🐼", "🦄"]},
            "sad": {"emoji": "🐱", "alternatives": ["🐺", "🐧", "🐨", "🦔"]},
            "angry": {"emoji": "🦁", "alternatives": ["🐯", "🐺", "🦈", "🐉"]},
            "surprised": {"emoji": "🦉", "alternatives": ["🐸", "🦋", "🐙", "🦜"]},
            "playful": {"emoji": "🐵", "alternatives": ["🐶", "🐱", "🐰", "🐼"]},
            "content": {"emoji": "🐨", "alternatives": ["🐼", "🐰", "🐱", "🦋"]}
        }
    }',
    '1.0',
    'Animal theme emoji mapping',
    1,
    0,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 自然主题emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'theme_nature_v1',
    'system_themes',
    'emoji_theme',
    '{
        "theme_info": {
            "theme_name": "nature",
            "theme_description": "Nature-based emoji mapping",
            "category": "peaceful"
        },
        "mappings": {
            "happy": {"emoji": "🌞", "alternatives": ["🌻", "🌺", "🌸", "🌈"]},
            "sad": {"emoji": "🌧️", "alternatives": ["☁️", "🌊", "🍂", "🌙"]},
            "angry": {"emoji": "⛈️", "alternatives": ["🌪️", "🔥", "🌋", "⚡"]},
            "surprised": {"emoji": "⚡", "alternatives": ["🌟", "✨", "💫", "🌠"]},
            "peaceful": {"emoji": "🌸", "alternatives": ["🍃", "🌿", "🌾", "🕊️"]},
            "free": {"emoji": "🦋", "alternatives": ["🕊️", "🌈", "☁️", "🌸"]}
        }
    }',
    '1.0',
    'Nature theme emoji mapping',
    1,
    0,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 专业主题emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'theme_professional_v1',
    'system_themes',
    'emoji_theme',
    '{
        "theme_info": {
            "theme_name": "professional",
            "theme_description": "Professional context emoji mapping",
            "category": "work"
        },
        "mappings": {
            "happy": {"emoji": "✅", "alternatives": ["👍", "💼", "📈", "🎯"]},
            "sad": {"emoji": "📉", "alternatives": ["📊", "⚠️", "🔍", "📋"]},
            "angry": {"emoji": "⚠️", "alternatives": ["🚨", "⛔", "🔥", "💢"]},
            "surprised": {"emoji": "💡", "alternatives": ["🔍", "📊", "📈", "⭐"]},
            "focused": {"emoji": "🎯", "alternatives": ["💼", "📋", "🔍", "⚡"]},
            "productive": {"emoji": "📈", "alternatives": ["💪", "🚀", "⚡", "🎯"]}
        }
    }',
    '1.0',
    'Professional theme emoji mapping',
    1,
    0,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- TCM体质评估包的默认emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'emoji_config_tcm_constitution_v1',
    'tcm_constitution_assessment',
    'default_emoji_mapping',
    '{
        "theme_info": {
            "theme_name": "tcm_constitution",
            "theme_description": "Traditional Chinese Medicine constitution emoji mapping",
            "supports_alternatives": true,
            "supports_user_customization": true
        },
        "constitution_types": {
            "balanced": {
                "emoji": "⚖️",
                "color": "#4CAF50",
                "animation": "balance",
                "alternatives": ["😌", "🧘", "☯️", "🌿", "🍃"]
            },
            "qi_deficiency": {
                "emoji": "😴",
                "color": "#FFC107",
                "animation": "fade",
                "alternatives": ["💤", "🌙", "☁️", "🍂", "💨"]
            },
            "yang_deficiency": {
                "emoji": "🥶",
                "color": "#2196F3",
                "animation": "shiver",
                "alternatives": ["❄️", "🧊", "🌨️", "💙", "🌊"]
            },
            "yin_deficiency": {
                "emoji": "🔥",
                "color": "#FF5722",
                "animation": "flicker",
                "alternatives": ["🌡️", "☀️", "🌶️", "💥", "⚡"]
            },
            "phlegm_dampness": {
                "emoji": "💧",
                "color": "#607D8B",
                "animation": "drip",
                "alternatives": ["🌧️", "☁️", "🌊", "💦", "🌫️"]
            },
            "damp_heat": {
                "emoji": "🌡️",
                "color": "#FF9800",
                "animation": "steam",
                "alternatives": ["💨", "🔥", "☀️", "🌶️", "💥"]
            },
            "blood_stasis": {
                "emoji": "🩸",
                "color": "#9C27B0",
                "animation": "pulse",
                "alternatives": ["💜", "🔴", "⭕", "🌀", "💫"]
            },
            "qi_stagnation": {
                "emoji": "😤",
                "color": "#795548",
                "animation": "block",
                "alternatives": ["🚧", "⛔", "🌪️", "💨", "🔒"]
            },
            "special_constitution": {
                "emoji": "🌟",
                "color": "#E91E63",
                "animation": "sparkle",
                "alternatives": ["✨", "💫", "🎭", "🦄", "🌈"]
            }
        }
    }',
    '1.0',
    'TCM constitution assessment emoji mapping',
    1,
    1,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 工作压力评估包的默认emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'emoji_config_work_stress_v1',
    'work_stress_assessment',
    'default_emoji_mapping',
    '{
        "theme_info": {
            "theme_name": "work_stress",
            "theme_description": "Work stress assessment emoji mapping",
            "supports_alternatives": true,
            "supports_user_customization": true
        },
        "stress_levels": {
            "no_stress": {
                "emoji": "😌",
                "color": "#4CAF50",
                "animation": "calm",
                "alternatives": ["🧘", "☮️", "🌿", "🍃", "💚"]
            },
            "mild_stress": {
                "emoji": "😐",
                "color": "#8BC34A",
                "animation": "neutral",
                "alternatives": ["🤔", "😑", "😶", "🙃", "😏"]
            },
            "moderate_stress": {
                "emoji": "😟",
                "color": "#FFC107",
                "animation": "worry",
                "alternatives": ["😕", "🙁", "😞", "😔", "😣"]
            },
            "high_stress": {
                "emoji": "😰",
                "color": "#FF9800",
                "animation": "sweat",
                "alternatives": ["😨", "😱", "😧", "😖", "💦"]
            },
            "extreme_stress": {
                "emoji": "🤯",
                "color": "#F44336",
                "animation": "explode",
                "alternatives": ["😵", "💥", "🌪️", "⚡", "🔥"]
            }
        },
        "work_emotions": {
            "motivated": {
                "emoji": "💪",
                "color": "#4CAF50",
                "animation": "flex",
                "alternatives": ["🚀", "⚡", "🔥", "🎯", "💼"]
            },
            "overwhelmed": {
                "emoji": "😵‍💫",
                "color": "#FF5722",
                "animation": "dizzy",
                "alternatives": ["🌪️", "💫", "🤯", "😵", "🌀"]
            },
            "focused": {
                "emoji": "🎯",
                "color": "#2196F3",
                "animation": "target",
                "alternatives": ["🔍", "👁️", "💡", "⚡", "🧠"]
            },
            "burned_out": {
                "emoji": "🔥",
                "color": "#795548",
                "animation": "burn",
                "alternatives": ["💨", "😴", "💤", "🌫️", "⚫"]
            }
        }
    }',
    '1.0',
    'Work stress assessment emoji mapping',
    1,
    1,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- ============================================================================
-- 系统预定义EMOJI主题集合
-- ============================================================================

-- 食物主题emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'theme_food_v1',
    'system_themes',
    'emoji_theme',
    '{
        "theme_info": {
            "theme_name": "food",
            "theme_description": "Food-based emoji mapping",
            "category": "fun"
        },
        "mappings": {
            "happy": {"emoji": "🍦", "alternatives": ["🍰", "🎂", "🍭", "🍯"]},
            "sad": {"emoji": "🍋", "alternatives": ["🥔", "🥛", "🍞", "☕"]},
            "angry": {"emoji": "🌶️", "alternatives": ["🔥", "🌽", "🧄", "🥵"]},
            "surprised": {"emoji": "🍿", "alternatives": ["🥨", "🍪", "🧁", "🎉"]},
            "content": {"emoji": "🍯", "alternatives": ["🥐", "🧈", "🥞", "🍵"]},
            "energetic": {"emoji": "☕", "alternatives": ["🥤", "🧃", "⚡", "🔋"]}
        }
    }',
    '1.0',
    'Food theme emoji mapping',
    1,
    0,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 天气主题emoji映射
INSERT OR IGNORE INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'theme_weather_v1',
    'system_themes',
    'emoji_theme',
    '{
        "theme_info": {
            "theme_name": "weather",
            "theme_description": "Weather-based emoji mapping",
            "category": "nature"
        },
        "mappings": {
            "happy": {"emoji": "☀️", "alternatives": ["🌞", "🌈", "🌸", "🌺"]},
            "sad": {"emoji": "🌧️", "alternatives": ["☁️", "🌊", "💧", "🌙"]},
            "angry": {"emoji": "⛈️", "alternatives": ["🌩️", "⚡", "🌪️", "🔥"]},
            "surprised": {"emoji": "⚡", "alternatives": ["🌟", "✨", "💫", "🌠"]},
            "calm": {"emoji": "🌤️", "alternatives": ["☁️", "🌙", "🌊", "🍃"]},
            "energetic": {"emoji": "🌪️", "alternatives": ["💨", "⚡", "🌊", "🔥"]}
        }
    }',
    '1.0',
    'Weather theme emoji mapping',
    1,
    0,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 验证配置加载
SELECT 'Pack Presentation Configs loaded:' as info, COUNT(*) as count FROM pack_presentation_configs;
SELECT 'Quiz pack emoji configs loaded:' as info, COUNT(*) as count FROM pack_presentation_configs WHERE config_type = 'default_emoji_mapping';
SELECT 'System emoji themes loaded:' as info, COUNT(*) as count FROM pack_presentation_configs WHERE config_type = 'emoji_theme';
