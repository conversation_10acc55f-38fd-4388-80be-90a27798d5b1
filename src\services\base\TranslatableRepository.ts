/**
 * 可翻译实体仓储基类
 * 提供统一的多语言处理功能
 */

import { BaseRepository } from './BaseRepository';
import { DatabaseContext } from '../types/ServiceTypes';

// 导入统一的翻译类型定义
import {
  type Translation,
  type TranslatableEntity,
  type TranslationFilter
} from '../../types/schema/translation';
import { type LanguageCode } from '../../types/schema/base';

// 为了向后兼容，重新导出类型
export type { Translation, TranslatableEntity };

// 翻译查询过滤器（扩展统一的 TranslationFilter）
export interface TranslatableFilter extends TranslationFilter {
  // 可以在这里添加 Repository 特有的过滤器字段
}

export abstract class TranslatableRepository<
  T extends TranslatableEntity,
  created_ata,
  updated_ata
> extends BaseRepository<T, created_ata, updated_ata> {

  protected translationTableName: string;
  protected foreignKeyName: string;

  constructor(tableName: string, translationTableName: string, foreignKeyName: string) {
    super(tableName);
    this.translationTableName = translationTableName;
    this.foreignKeyName = foreignKeyName;
  }

  /**
   * 构建包含翻译的查询语句
   */
  protected buildSelectWithTranslationsQuery(filters?: TranslatableFilter): { query: string; values: any[] } {
    let query = `
      SELECT DISTINCT
        t.*,
        tr.translated_name as localized_name,
        tr.translated_description as localized_description
      FROM ${this.tableName} t
      LEFT JOIN ${this.translationTableName} tr ON t.id = tr.${this.foreignKeyName}
    `;

    const values: any[] = [];
    const conditions: string[] = [];

    if (filters) {
      // 语言过滤 - 优先选择指定语言，如果没有则使用默认
      if (filters.languageCode) {
        query = `
          SELECT DISTINCT
            t.*,
            COALESCE(
              (SELECT translated_name FROM ${this.translationTableName} WHERE ${this.foreignKeyName} = t.id AND language_code = ? LIMIT 1),
              t.name
            ) as localized_name,
            COALESCE(
              (SELECT translated_description FROM ${this.translationTableName} WHERE ${this.foreignKeyName} = t.id AND language_code = ? LIMIT 1),
              t.description
            ) as localized_description
          FROM ${this.tableName} t
        `;
        values.push(filters.languageCode, filters.languageCode);
      }

      // 搜索过滤
      if (filters.searchTerm) {
        if (filters.languageCode) {
          conditions.push(`(t.name LIKE ? OR EXISTS (SELECT 1 FROM ${this.translationTableName} WHERE ${this.foreignKeyName} = t.id AND translated_name LIKE ?))`);
        } else {
          conditions.push(`(t.name LIKE ? OR tr.translated_name LIKE ?)`);
        }
        values.push(`%${filters.searchTerm}%`, `%${filters.searchTerm}%`);
      }

      // 添加WHERE子句（只有在有条件且查询还没有WHERE子句时）
      if (conditions.length > 0) {
        const hasWhere = query.toLowerCase().includes(' where ');
        if (hasWhere) {
          query += ` AND ${conditions.join(' AND ')}`;
        } else {
          query += ` WHERE ${conditions.join(' AND ')}`;
        }
      }

      // 排序
      const orderBy = filters.orderBy || 'name';
      const orderDirection = filters.orderDirection || 'ASC';
      if (filters.languageCode) {
        query += ` ORDER BY localized_name ${orderDirection}`;
      } else {
        query += ` ORDER BY COALESCE(tr.translated_name, t.${orderBy}) ${orderDirection}`;
      }

      // 分页
      if (filters.limit) {
        query += ` LIMIT ${filters.limit}`;

        if (filters.offset) {
          query += ` OFFSET ${filters.offset}`;
        }
      }
    } else {
      query += ` ORDER BY t.name ASC`;
    }

    return { query, values };
  }

  /**
   * 获取实体的所有翻译
   */
  async getTranslations(context: DatabaseContext, entityId: string): Promise<Translation[]> {
    const query = `SELECT * FROM ${this.translationTableName} WHERE ${this.foreignKeyName} = ? ORDER BY language_code`;
    const result = await context.db.query(query, [entityId]);

    if (!result.values) {
      return [];
    }

    return result.values.map(row => ({
      entityId: row[this.foreignKeyName],
      languageCode: row.language_code,
      translatedName: row.translated_name,
      translatedDescription: row.translated_description
    }));
  }

  /**
   * 获取特定语言的翻译
   */
  async getTranslation(
    context: DatabaseContext,
    entityId: string,
    languageCode: LanguageCode
  ): Promise<Translation | null> {
    const query = `
      SELECT * FROM ${this.translationTableName}
      WHERE ${this.foreignKeyName} = ? AND language_code = ?
    `;
    const result = await context.db.query(query, [entityId, languageCode]);

    if (!result.values || result.values.length === 0) {
      return null;
    }

    const row = result.values[0];
    return {
      entityId: row[this.foreignKeyName],
      languageCode: row.language_code,
      translatedName: row.translated_name,
      translatedDescription: row.translated_description
    };
  }

  /**
   * 添加或更新翻译
   */
  async upsertTranslation(
    context: DatabaseContext,
    entityId: string,
    languageCode: LanguageCode,
    translatedName: string,
    translatedDescription?: string
  ): Promise<void> {
    const query = `
      INSERT OR REPLACE INTO ${this.translationTableName}
      (${this.foreignKeyName}, language_code, translated_name, translated_description)
      VALUES (?, ?, ?, ?)
    `;

    await context.db.run(query, [entityId, languageCode, translatedName, translatedDescription || null]);
  }

  /**
   * 删除翻译
   */
  async deleteTranslation(
    context: DatabaseContext,
    entityId: string,
    languageCode: LanguageCode
  ): Promise<void> {
    const query = `
      DELETE FROM ${this.translationTableName}
      WHERE ${this.foreignKeyName} = ? AND language_code = ?
    `;
    await context.db.run(query, [entityId, languageCode]);
  }

  /**
   * 批量添加翻译
   */
  async batchUpsertTranslations(
    context: DatabaseContext,
    entityId: string,
    translations: Array<{
      languageCode: LanguageCode;
      translatedName: string;
      translatedDescription?: string;
    }>
  ): Promise<void> {
    for (const translation of translations) {
      await this.upsertTranslation(
        context,
        entityId,
        translation.languageCode,
        translation.translatedName,
        translation.translatedDescription
      );
    }
  }

  /**
   * 删除实体的所有翻译
   */
  async deleteAllTranslations(context: DatabaseContext, entityId: string): Promise<void> {
    const query = `DELETE FROM ${this.translationTableName} WHERE ${this.foreignKeyName} = ?`;
    await context.db.run(query, [entityId]);
  }

  /**
   * 获取带翻译的实体
   */
  async findByIdWithTranslations(
    context: DatabaseContext,
    id: string,
    languageCode?: LanguageCode
  ): Promise<T | null> {
    // 简化的查询，直接构建而不依赖复杂的查询构建器
    let query: string;
    let values: any[];

    if (languageCode) {
      // 如果指定了语言，优先使用该语言的翻译
      query = `
        SELECT DISTINCT
          t.*,
          COALESCE(
            (SELECT translated_name FROM ${this.translationTableName} WHERE ${this.foreignKeyName} = t.id AND language_code = ? LIMIT 1),
            t.name
          ) as localized_name,
          COALESCE(
            (SELECT translated_description FROM ${this.translationTableName} WHERE ${this.foreignKeyName} = t.id AND language_code = ? LIMIT 1),
            t.description
          ) as localized_description
        FROM ${this.tableName} t
        WHERE t.id = ?
      `;
      values = [languageCode, languageCode, id];
    } else {
      // 如果没有指定语言，使用默认查询
      query = `
        SELECT DISTINCT
          t.*,
          tr.translated_name as localized_name,
          tr.translated_description as localized_description
        FROM ${this.tableName} t
        LEFT JOIN ${this.translationTableName} tr ON t.id = tr.${this.foreignKeyName}
        WHERE t.id = ?
      `;
      values = [id];
    }

    const result = await context.db.query(query, values);

    if (!result.values || result.values.length === 0) {
      return null;
    }

    return this.mapRowToEntity(result.values[0]);
  }

  /**
   * 获取带翻译的实体列表
   */
  async findManyWithTranslations(
    context: DatabaseContext,
    filters?: TranslatableFilter
  ): Promise<T[]> {
    const { query, values } = this.buildSelectWithTranslationsQuery(filters);

    const result = await context.connection.query(query, values);

    if (!result.values) {
      return [];
    }

    return result.values.map((row: any) => this.mapRowToEntity(row));
  }

  /**
   * 搜索实体（支持多语言）
   */
  async searchWithTranslations(
    context: DatabaseContext,
    searchTerm: string,
    languageCode?: LanguageCode,
    limit: number = 10
  ): Promise<T[]> {
    const filters: TranslatableFilter = {
      searchTerm,
      languageCode,
      limit,
      orderBy: 'name',
      orderDirection: 'ASC'
    };

    return await this.findManyWithTranslations(context, filters);
  }

  /**
   * 获取支持的语言列表
   */
  async getSupportedLanguages(context: DatabaseContext): Promise<string[]> {
    const query = `
      SELECT DISTINCT language_code
      FROM ${this.translationTableName}
      ORDER BY language_code
    `;
    const result = await context.connection.query(query);

    if (!result.values) {
      return [];
    }

    return result.values.map((row: any) => row.language_code);
  }

  /**
   * 获取翻译完成度统计
   */
  async getTranslationStats(context: DatabaseContext): Promise<Array<{
    languageCode: string;
    totalEntities: number;
    translatedEntities: number;
    completionPercentage: number;
  }>> {
    const query = `
      SELECT
        tr.language_code,
        COUNT(DISTINCT tr.${this.foreignKeyName}) as translated_entities,
        (SELECT COUNT(*) FROM ${this.tableName}) as total_entities,
        ROUND(
          COUNT(DISTINCT tr.${this.foreignKeyName}) * 100.0 /
          (SELECT COUNT(*) FROM ${this.tableName}),
          2
        ) as completion_percentage
      FROM ${this.translationTableName} tr
      GROUP BY tr.language_code
      ORDER BY completion_percentage DESC, tr.language_code
    `;

    const result = await context.connection.query(query);

    if (!result.values) {
      return [];
    }

    return result.values.map((row: any) => ({
      languageCode: row.language_code,
      totalEntities: Number(row.total_entities),
      translatedEntities: Number(row.translated_entities),
      completionPercentage: Number(row.completion_percentage)
    }));
  }

  /**
   * 重写基类的 mapRowToEntity 方法以包含翻译信息
   */
  protected abstract mapRowToEntity(row: any): T;
}
