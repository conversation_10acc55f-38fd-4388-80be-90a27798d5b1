-- Quiz Result Tags Test Data
-- Test data for quiz_result_tags table associations

-- Insert quiz result tag associations
INSERT OR IGNORE INTO quiz_result_tags (quiz_result_id, tag_id, added_by, confidence_score, tag_source, created_at) VALUES
-- Result 001 tags (work stress in morning)
('result_001', 'tag_work', 'user_test_001', 1.0, 'user', '2024-01-15T10:30:00Z'),
('result_001', 'tag_stress', 'user_test_001', 0.9, 'user', '2024-01-15T10:30:00Z'),
('result_001', 'tag_morning', NULL, 0.95, 'ai_generated', '2024-01-15T10:30:00Z'),

-- Result 002 tags (family happiness on weekend)
('result_002', 'tag_family', 'user_test_002', 1.0, 'user', '2024-01-16T14:20:00Z'),
('result_002', 'tag_happiness', 'user_test_002', 0.8, 'user', '2024-01-16T14:20:00Z'),
('result_002', 'tag_weekend', NULL, 0.85, 'ai_generated', '2024-01-16T14:20:00Z'),

-- Result 003 tags (exercise and health bringing calm)
('result_003', 'tag_exercise', 'user_test_003', 1.0, 'user', '2024-01-17T08:45:00Z'),
('result_003', 'tag_health', 'user_test_003', 0.9, 'user', '2024-01-17T08:45:00Z'),
('result_003', 'tag_calm', NULL, 0.75, 'ai_generated', '2024-01-17T08:45:00Z'),

-- Result 004 tags (study anxiety in evening)
('result_004', 'tag_study', 'user_test_001', 1.0, 'user', '2024-01-18T16:15:00Z'),
('result_004', 'tag_anxiety', 'user_test_001', 0.7, 'user', '2024-01-18T16:15:00Z'),
('result_004', 'tag_evening', NULL, 0.9, 'system_suggested', '2024-01-18T16:15:00Z'),

-- Result 005 tags (friends and hobby bringing happiness)
('result_005', 'tag_friends', 'user_test_002', 1.0, 'user', '2024-01-19T19:30:00Z'),
('result_005', 'tag_hobby', 'user_test_002', 0.8, 'user', '2024-01-19T19:30:00Z'),
('result_005', 'tag_happiness', NULL, 0.85, 'ai_generated', '2024-01-19T19:30:00Z'),

-- Result 006 tags (travel and rest in afternoon)
('result_006', 'tag_travel', 'user_test_003', 1.0, 'user', '2024-01-20T12:00:00Z'),
('result_006', 'tag_rest', 'user_test_003', 0.9, 'user', '2024-01-20T12:00:00Z'),
('result_006', 'tag_afternoon', NULL, 0.8, 'ai_generated', '2024-01-20T12:00:00Z'),

-- Result 007 tags (work sadness in morning)
('result_007', 'tag_work', 'user_test_001', 1.0, 'user', '2024-01-21T09:15:00Z'),
('result_007', 'tag_sadness', 'user_test_001', 0.6, 'user', '2024-01-21T09:15:00Z'),
('result_007', 'tag_morning', NULL, 0.95, 'system_suggested', '2024-01-21T09:15:00Z'),

-- Result 008 tags (family anger at night)
('result_008', 'tag_family', 'user_test_002', 1.0, 'user', '2024-01-22T20:45:00Z'),
('result_008', 'tag_anger', 'user_test_002', 0.7, 'user', '2024-01-22T20:45:00Z'),
('result_008', 'tag_night', NULL, 0.9, 'ai_generated', '2024-01-22T20:45:00Z');

-- Verify the data was inserted
SELECT 'Quiz Result Tags Count:' as info, COUNT(*) as count FROM quiz_result_tags;

-- Show tag usage statistics
SELECT 
    t.name as tag_name,
    t.category,
    COUNT(qrt.tag_id) as usage_count,
    AVG(qrt.confidence_score) as avg_confidence,
    qrt.tag_source
FROM tags t
LEFT JOIN quiz_result_tags qrt ON t.id = qrt.tag_id
GROUP BY t.id, qrt.tag_source
ORDER BY usage_count DESC, t.category, t.name;
