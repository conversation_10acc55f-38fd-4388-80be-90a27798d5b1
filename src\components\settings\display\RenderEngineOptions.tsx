import { useLanguage } from '@/contexts/LanguageContext';
import { useSkinManager } from '@/contexts/SkinContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import type { RenderEngine } from '@/types';
import type React from 'react';
import { useMemo } from 'react';
import OptionButton from '../ui/OptionButton';

/**
 * 渲染引擎选项组件
 * 用于选择渲染引擎（D3、SVG、R3F）
 */
const RenderEngineOptions: React.FC = () => {
  const { t } = useLanguage();
  const { activeSkin } = useSkinManager();
  const { userConfig, updateUserConfig } = useUserConfig();

  // 获取当前显示选项
  const displayOptions = useMemo(() => {
    const viewType = userConfig.preferred_view_type || 'wheel';
    const renderEngine = userConfig.render_engine_preferences?.[viewType] || 'D3';
    return { viewType, renderEngine };
  }, [userConfig]);

  // 处理渲染引擎变更
  const handleRenderEngineChange = (engine: RenderEngine) => {
    // 获取当前视图类型
    const viewType = userConfig.preferred_view_type || displayOptions.viewType;

    // 更新用户配置
    updateUserConfig({
      render_engine_preferences: {
        ...userConfig.render_engine_preferences,
        [viewType]: engine,
      },
    });
  };

  // 获取当前皮肤支持的渲染引擎
  const supported_render_engines = activeSkin?.config?.supported_render_engines || ['D3', 'SVG', 'R3F'];

  return (
    <div className="space-y-4">
      <p className="text-sm text-muted-foreground">{t('settings.render_engine.description')}</p>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
        {supported_render_engines.includes('D3') && (
          <OptionButton
            label={t('settings.render_engine.d3')}
            tooltip={t('settings.render_engine.d3.description')}
            isSelected={displayOptions.renderEngine === 'D3'}
            onClick={() => handleRenderEngineChange('D3')}
          />
        )}

        {supported_render_engines.includes('SVG') && (
          <OptionButton
            label={t('settings.render_engine.svg')}
            tooltip={t('settings.render_engine.svg.description')}
            isSelected={displayOptions.renderEngine === 'SVG'}
            onClick={() => handleRenderEngineChange('SVG')}
          />
        )}

        {supported_render_engines.includes('R3F') && (
          <OptionButton
            label={t('settings.render_engine.r3f')}
            tooltip={t('settings.render_engine.r3f.description')}
            isSelected={displayOptions.renderEngine === 'R3F'}
            onClick={() => handleRenderEngineChange('R3F')}
          />
        )}
      </div>
    </div>
  );
};

export default RenderEngineOptions;
