/**
 * 心情追踪业务服务
 * 处理复杂的心情追踪业务逻辑，协调多个实体服务
 */

import { v4 as uuidv4 } from 'uuid';
import type { ServiceResult } from '../types/ServiceTypes';

import type {
  CompleteMoodEntryInput,
  MoodEntryWithSelections,
  MoodTrackingStats,
} from '../../types/schema/api';
// 导入统一的类型定义
import type { EmotionSelection, MoodEntry } from '../../types/schema/base';

// 重新导出类型以供其他模块使用
export type { CompleteMoodEntryInput, MoodEntryWithSelections, MoodTrackingStats };

export class MoodTrackingService {
  private moodEntryService: any;
  private emotionSelectionService: any;
  private userConfigService: any;

  constructor() {
    // 使用动态导入避免循环依赖
    const { MoodEntryService } = require('../entities/MoodEntryService');
    const { EmotionSelectionService } = require('../entities/EmotionSelectionService');
    const { UserConfigService } = require('../entities/UserConfigService');

    this.moodEntryService = new MoodEntryService();
    this.emotionSelectionService = new EmotionSelectionService();
    this.userConfigService = new UserConfigService();
  }

  /**
   * 创建完整的心情记录（包含情绪选择）
   */
  async createCompleteMoodEntry(
    data: CompleteMoodEntryInput
  ): Promise<ServiceResult<MoodEntryWithSelections>> {
    try {
      // 验证输入数据
      const validationResult = this.validateCompleteMoodEntry(data);
      if (!validationResult.isValid) {
        return {
          success: false,
          error: validationResult.errors.join(', '),
          code: 'VALIDATION_ERROR',
        };
      }

      // 获取用户的活动配置以确定默认的情绪数据集
      let emotionDataSetId = data.emotion_data_set_id;
      if (!emotionDataSetId) {
        const activeConfigResult = await this.userConfigService.getActiveConfig(data.user_id);
        if (activeConfigResult.success && activeConfigResult.data) {
          emotionDataSetId = activeConfigResult.data.active_emotion_data_id;
        }
      }

      // 创建心情记录
      const moodEntryResult = await this.moodEntryService.create({
        user_id: data.user_id,
        timestamp: data.timestamp,
        emotion_data_set_id: emotionDataSetId,
        intensity: data.intensity,
        reflection: data.reflection,
        tags: data.tags,
      });

      if (!moodEntryResult.success || !moodEntryResult.data) {
        return {
          success: false,
          error: moodEntryResult.error || 'Failed to create mood entry',
          code: moodEntryResult.code,
        };
      }

      const moodEntry = moodEntryResult.data;

      // 创建情绪选择
      const emotionSelectionsResult = await this.emotionSelectionService.batchCreateForMoodEntry({
        mood_entry_id: moodEntry.id,
        selections: data.emotion_selections,
      });

      if (!emotionSelectionsResult.success || !emotionSelectionsResult.data) {
        // 如果情绪选择创建失败，删除已创建的心情记录
        await this.moodEntryService.delete(moodEntry.id);
        return {
          success: false,
          error: emotionSelectionsResult.error || 'Failed to create emotion selections',
          code: emotionSelectionsResult.code,
        };
      }

      // 更新用户配置中的最近使用记录
      if (emotionDataSetId) {
        await this.userConfigService.addRecentEmotionDataId(data.user_id, emotionDataSetId);
      }

      return {
        success: true,
        data: {
          moodEntry,
          emotionSelections: emotionSelectionsResult.data,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to create complete mood entry: ${error}`,
        code: 'UNKNOWN',
      };
    }
  }

  /**
   * 获取用户的心情记录（包含情绪选择）
   */
  async getUserMoodEntriesWithSelections(
    userId: string,
    options?: {
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      offset?: number;
    }
  ): Promise<ServiceResult<MoodEntryWithSelections[]>> {
    try {
      // 获取心情记录
      const moodEntriesResult = await this.moodEntryService.getUserMoodEntries(userId, {
        startDate: options?.startDate,
        endDate: options?.endDate,
        limit: options?.limit,
        offset: options?.offset,
        orderBy: 'timestamp',
        orderDirection: 'DESC',
      });

      if (!moodEntriesResult.success || !moodEntriesResult.data) {
        return moodEntriesResult as any;
      }

      // 为每个心情记录获取情绪选择
      const entriesWithSelections: MoodEntryWithSelections[] = [];

      for (const moodEntry of moodEntriesResult.data) {
        const selectionsResult = await this.emotionSelectionService.getByMoodEntryId(moodEntry.id);

        entriesWithSelections.push({
          moodEntry,
          emotionSelections:
            selectionsResult.success && selectionsResult.data ? selectionsResult.data : [],
        });
      }

      return {
        success: true,
        data: entriesWithSelections,
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get user mood entries with selections: ${error}`,
        code: 'UNKNOWN',
      };
    }
  }

  /**
   * 更新完整的心情记录
   */
  async updateCompleteMoodEntry(
    entryId: string,
    data: Partial<CompleteMoodEntryInput>
  ): Promise<ServiceResult<MoodEntryWithSelections>> {
    try {
      // 更新心情记录
      const updated_ata: any = {};
      if (data.timestamp !== undefined) updated_ata.timestamp = data.timestamp;
      if (data.emotion_data_set_id !== undefined)
        updated_ata.emotion_data_set_id = data.emotion_data_set_id;
      if (data.intensity !== undefined) updated_ata.intensity = data.intensity;
      if (data.reflection !== undefined) updated_ata.reflection = data.reflection;
      if (data.tags !== undefined) updated_ata.tags = data.tags;

      const moodEntryResult = await this.moodEntryService.update(entryId, updated_ata);

      if (!moodEntryResult.success || !moodEntryResult.data) {
        return moodEntryResult as any;
      }

      // 如果有新的情绪选择，更新它们
      let emotionSelections: EmotionSelection[] = [];
      if (data.emotion_selections) {
        const selectionsResult = await this.emotionSelectionService.batchCreateForMoodEntry({
          mood_entry_id: entryId,
          selections: data.emotion_selections,
        });

        if (selectionsResult.success && selectionsResult.data) {
          emotionSelections = selectionsResult.data;
        }
      } else {
        // 获取现有的情绪选择
        const existingSelectionsResult =
          await this.emotionSelectionService.getByMoodEntryId(entryId);
        if (existingSelectionsResult.success && existingSelectionsResult.data) {
          emotionSelections = existingSelectionsResult.data;
        }
      }

      return {
        success: true,
        data: {
          moodEntry: moodEntryResult.data,
          emotionSelections,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to update complete mood entry: ${error}`,
        code: 'UNKNOWN',
      };
    }
  }

  /**
   * 删除完整的心情记录
   */
  async deleteCompleteMoodEntry(entryId: string): Promise<ServiceResult<boolean>> {
    try {
      // 先删除情绪选择
      await this.emotionSelectionService.deleteByMoodEntryId(entryId);

      // 再删除心情记录
      const deleteResult = await this.moodEntryService.delete(entryId);

      return deleteResult;
    } catch (error) {
      return {
        success: false,
        error: `Failed to delete complete mood entry: ${error}`,
        code: 'UNKNOWN',
      };
    }
  }

  /**
   * 获取用户的心情追踪统计
   */
  async getUserMoodTrackingStats(
    userId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<ServiceResult<MoodTrackingStats>> {
    try {
      // 获取基础统计
      const basicStatsResult = await this.moodEntryService.getUserStats(userId, startDate, endDate);
      if (!basicStatsResult.success || !basicStatsResult.data) {
        return basicStatsResult as any;
      }

      // 获取情绪使用统计
      const emotionStatsResult = await this.emotionSelectionService.getEmotionUsageStats(
        userId,
        startDate,
        endDate
      );
      const mostUsedEmotions =
        emotionStatsResult.success && emotionStatsResult.data
          ? emotionStatsResult.data.slice(0, 10) // 取前10个最常用的情绪
          : [];

      // 获取情绪趋势（按周分组）
      const trendsResult = await this.getEmotionTrends(userId, startDate, endDate, 'week');
      const emotionTrends = trendsResult.success && trendsResult.data ? trendsResult.data : [];

      // 计算连续记录天数
      const streakInfo = await this.calculateStreak(userId);

      const stats: MoodTrackingStats = {
        totalEntries: basicStatsResult.data.totalEntries,
        averageIntensity: basicStatsResult.data.averageIntensity,
        mostUsedEmotions,
        emotionTrends,
        streakInfo,
      };

      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get mood tracking stats: ${error}`,
        code: 'UNKNOWN',
      };
    }
  }

  /**
   * 获取情绪趋势
   */
  private async getEmotionTrends(
    userId: string,
    startDate?: Date,
    endDate?: Date,
    groupBy: 'day' | 'week' | 'month' = 'day'
  ): Promise<
    ServiceResult<
      Array<{
        period: string;
        average_intensity: number;
        entry_count: number;
      }>
    >
  > {
    try {
      // 这里需要实现复杂的趋势分析逻辑
      // 暂时返回空数组
      return {
        success: true,
        data: [],
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get emotion trends: ${error}`,
        code: 'UNKNOWN',
      };
    }
  }

  /**
   * 计算用户的记录连续天数
   */
  private async calculateStreak(userId: string): Promise<{
    currentStreak: number;
    longestStreak: number;
    lastEntryDate: Date | null;
  }> {
    try {
      // 获取最近的记录
      const recentEntriesResult = await this.moodEntryService.getUserMoodEntries(userId, {
        limit: 100,
        orderBy: 'timestamp',
        orderDirection: 'DESC',
      });

      if (
        !recentEntriesResult.success ||
        !recentEntriesResult.data ||
        recentEntriesResult.data.length === 0
      ) {
        return {
          currentStreak: 0,
          longestStreak: 0,
          lastEntryDate: null,
        };
      }

      const entries = recentEntriesResult.data;
      const lastEntryDate = entries[0].timestamp;

      // 计算连续天数的逻辑
      // 这里需要实现复杂的日期计算
      // 暂时返回简单的统计
      return {
        currentStreak: 1,
        longestStreak: 1,
        lastEntryDate,
      };
    } catch (error) {
      return {
        currentStreak: 0,
        longestStreak: 0,
        lastEntryDate: null,
      };
    }
  }

  /**
   * 验证完整心情记录输入
   */
  private validateCompleteMoodEntry(data: CompleteMoodEntryInput): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.user_id) {
      errors.push('User ID is required');
    }

    if (data.intensity < 0 || data.intensity > 100) {
      errors.push('Intensity must be between 0 and 100');
    }

    if (!data.emotion_selections || data.emotion_selections.length === 0) {
      errors.push('At least one emotion selection is required');
    }

    if (data.emotion_selections) {
      data.emotion_selections.forEach((selection, index) => {
        if (!selection.emotion_id) {
          errors.push(`Emotion selection ${index + 1}: emotion_id is required`);
        }
        if (selection.tier_level < 1 || selection.tier_level > 10) {
          errors.push(`Emotion selection ${index + 1}: tier_level must be between 1 and 10`);
        }
      });
    }

    if (data.reflection && data.reflection.length > 1000) {
      errors.push('Reflection must be less than 1000 characters');
    }

    if (data.tags && data.tags.length > 10) {
      errors.push('Maximum 10 tags allowed');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
