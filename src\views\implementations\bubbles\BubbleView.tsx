/**
 * 气泡视图
 * 使用气泡布局显示情绪
 */

import type { Emotion, ContentDisplayMode, ViewConfig, SkinConfig, BubbleLayout } from '@/types';
import { BaseEmotionView } from '@/views/base/BaseEmotionView';
import type React from 'react';
import { BubbleView as BubbleViewComponent } from '@/views/components/bubbles/BubbleView';

/**
 * 气泡视图类
 * 使用气泡布局显示情绪
 */
export class BubbleView extends BaseEmotionView {
  protected layout: BubbleLayout = 'cluster';

  /**
   * 构造函数
   * @param contentType 内容显示模式
   * @param skinConfig 皮肤配置
   * @param layout 气泡布局
   */
  constructor(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: BubbleLayout = 'cluster'
  ) {
    super('bubble', contentType, skinConfig);
    this.layout = layout;
  }

  /**
   * 获取气泡布局
   * @returns 气泡布局
   */
  getLayout(): BubbleLayout {
    return this.layout;
  }

  /**
   * 设置气泡布局
   * @param layout 新的气泡布局
   */
  setLayout(layout: BubbleLayout): void {
    this.layout = layout;
  }

  /**
   * 渲染气泡
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  renderBubbles(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    // 从配置中获取额外属性
    const onBack = config?.onBack;
    const selectedPath = config?.selectedPath;

    return (
      <BubbleViewComponent
        emotions={emotions}
        tierLevel={tierLevel}
        contentDisplayMode={this.contentDisplayMode}
        skinConfig={this.skinConfig}
        onSelect={onSelect}
        layout={this.layout}
        onBack={onBack}
        selectedPath={selectedPath}
      />
    );
  }

  /**
   * 渲染视图
   * 实现基类的抽象方法
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  render(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    return this.renderBubbles(emotions, tierLevel, onSelect, config);
  }
}
