/**
 * 特殊视图组件测试页面
 *
 * 展示新的Quiz组件系统和QuizTierNavigation功能
 * 基于统一表结构的新架构实现
 */

import React, { useState, useEffect } from 'react';
import QuizTierNavigation from './QuizTierNavigation';
import { QuizComponentRenderer } from './core/QuizComponentRenderer';
import { EmotionWheelView } from './special-views/EmotionWheelView';
import { useNewHomeData } from '@/hooks/useNewHomeData';
import { useQuizSession } from '@/hooks/useQuizSession';
import { QuizQuestion, QuizQuestionOption } from '@/types/schema/base';
import { Loading } from '@/components/ui/loading';
import './QuizTierNavigation.css';
import './SpecialViewsTest.css';

const SpecialViewsTest: React.FC = () => {
  // 使用新的数据hooks
  const {
    emotionWheelData,
    isLoading: homeDataLoading,
    error: homeDataError,
    refreshData
  } = useNewHomeData('test-user');

  const {
    startQuizSession,
    submitAnswer,
    currentQuestion,
    session,
    isLoading: sessionLoading
  } = useQuizSession();

  // 状态管理
  const [currentTestMode, setCurrentTestMode] = useState<'tier-navigation' | 'component-renderer' | 'emotion-wheel'>('tier-navigation');
  const [currentTierIndex, setCurrentTierIndex] = useState<number>(0);
  const [selectedEmotions, setSelectedEmotions] = useState<Array<{
    id: string;
    text: string;
    value: string;
    tierLevel: number;
  }>>([]);
  const [selectedPath, setSelectedPath] = useState<{[questionId: string]: QuizQuestionOption}>({});
  const [interactionLog, setInteractionLog] = useState<string[]>([]);

  // 合并加载状态
  const isLoading = homeDataLoading || sessionLoading;
  const error = homeDataError;

  // 获取当前问题
  const getCurrentQuestion = () => {
    if (!emotionWheelData?.questions) return null;

    const currentTier = currentTierIndex + 1;
    return emotionWheelData.questions.find(q => q.tier_level === currentTier) || null;
  };

  // 创建模拟问题数据
  const createMockQuestions = (): (QuizQuestion & { options: QuizQuestionOption[] })[] => {
    return [
      {
        id: 'mock-question-1',
        pack_id: 'mock-pack',
        question_text: '请选择您的主要情绪',
        question_text_localized: null,
        question_type: 'emotion_wheel',
        question_order: 1,
        question_group: null,
        tier_level: 1,
        question_config: null,
        validation_rules: null,
        scoring_config: null,
        parent_question_id: null,
        dependency_rules: null,
        is_required: true,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: null,
        updated_by: null,
        options: [
          {
            id: 'mock-option-1',
            question_id: 'mock-question-1',
            option_text: '快乐',
            option_text_localized: null,
            option_value: 'happy',
            option_order: 1,
            metadata: JSON.stringify({ emoji: '😊', color: '#FFD700' }),
            scoring_value: null,
            is_correct: null,
            is_active: true,
            created_at: new Date(),
            updated_at: new Date(),
            created_by: null,
            updated_by: null
          },
          {
            id: 'mock-option-2',
            question_id: 'mock-question-1',
            option_text: '悲伤',
            option_text_localized: null,
            option_value: 'sad',
            option_order: 2,
            metadata: JSON.stringify({ emoji: '😢', color: '#4169E1' }),
            scoring_value: null,
            is_correct: null,
            is_active: true,
            created_at: new Date(),
            updated_at: new Date(),
            created_by: null,
            updated_by: null
          },
          {
            id: 'mock-option-3',
            question_id: 'mock-question-1',
            option_text: '愤怒',
            option_text_localized: null,
            option_value: 'angry',
            option_order: 3,
            metadata: JSON.stringify({ emoji: '😠', color: '#FF4444' }),
            scoring_value: null,
            is_correct: null,
            is_active: true,
            created_at: new Date(),
            updated_at: new Date(),
            created_by: null,
            updated_by: null
          }
        ]
      },
      {
        id: 'mock-question-2',
        pack_id: 'mock-pack',
        question_text: '请选择一个选项',
        question_text_localized: null,
        question_type: 'single_choice',
        question_order: 2,
        question_group: null,
        tier_level: 1,
        question_config: null,
        validation_rules: null,
        scoring_config: null,
        parent_question_id: null,
        dependency_rules: null,
        is_required: true,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: null,
        updated_by: null,
        options: [
          {
            id: 'mock-choice-1',
            question_id: 'mock-question-2',
            option_text: '选项A',
            option_text_localized: null,
            option_value: 'option_a',
            option_order: 1,
            metadata: null,
            scoring_value: null,
            is_correct: null,
            is_active: true,
            created_at: new Date(),
            updated_at: new Date(),
            created_by: null,
            updated_by: null
          },
          {
            id: 'mock-choice-2',
            question_id: 'mock-question-2',
            option_text: '选项B',
            option_text_localized: null,
            option_value: 'option_b',
            option_order: 2,
            metadata: null,
            scoring_value: null,
            is_correct: null,
            is_active: true,
            created_at: new Date(),
            updated_at: new Date(),
            created_by: null,
            updated_by: null
          }
        ]
      }
    ];
  };

  const mockQuestions = createMockQuestions();

  // 事件处理函数
  const handleEmotionSelect = (selectedOption: QuizQuestionOption) => {
    const newSelection = {
      id: selectedOption.id,
      text: selectedOption.option_text,
      value: selectedOption.option_value,
      tierLevel: currentTierIndex + 1
    };

    const newSelectedEmotions = [...selectedEmotions.slice(0, currentTierIndex), newSelection];
    setSelectedEmotions(newSelectedEmotions);

    // 更新选择路径
    const currentQuestion = getCurrentQuestion() || mockQuestions[0];
    if (currentQuestion) {
      setSelectedPath(prev => ({
        ...prev,
        [currentQuestion.id]: selectedOption
      }));
    }

    // 添加到交互日志
    setInteractionLog(prev => [
      `选择了选项: ${selectedOption.option_text} (${selectedOption.option_value})`,
      ...prev.slice(0, 9)
    ]);

    // 检查是否有下一层级
    const maxTiers = emotionWheelData?.questions.length || mockQuestions.length;
    if (currentTierIndex < maxTiers - 1) {
      setCurrentTierIndex(currentTierIndex + 1);
    }
  };

  const handleTierBack = () => {
    if (currentTierIndex > 0) {
      const newSelectedEmotions = selectedEmotions.slice(0, currentTierIndex - 1);
      setSelectedEmotions(newSelectedEmotions);
      setCurrentTierIndex(currentTierIndex - 1);

      setInteractionLog(prev => [
        `返回到层级 ${currentTierIndex}`,
        ...prev.slice(0, 9)
      ]);
    }
  };

  // 重置所有选择
  const resetAllSelections = () => {
    setSelectedEmotions([]);
    setSelectedPath({});
    setCurrentTierIndex(0);
    setInteractionLog([]);
  };

  // 切换测试模式
  const switchTestMode = (mode: 'tier-navigation' | 'component-renderer' | 'emotion-wheel') => {
    setCurrentTestMode(mode);
    resetAllSelections();
  };

  // 加载状态处理
  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>加载错误</h2>
        <p>{error}</p>
        <button onClick={refreshData}>重试</button>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '40px', color: '#2E7D32' }}>
        Quiz组件系统 - 新架构测试
      </h1>

      {/* 控制面板 */}
      <div style={{
        marginBottom: '40px',
        padding: '20px',
        backgroundColor: '#f5f5f5',
        borderRadius: '8px'
      }}>
        <h2 style={{ marginBottom: '20px' }}>控制面板</h2>

        {/* 测试模式切换 */}
        <div style={{ marginBottom: '20px' }}>
          <h3>测试模式</h3>
          <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            <button
              onClick={() => switchTestMode('tier-navigation')}
              style={{
                padding: '10px 20px',
                backgroundColor: currentTestMode === 'tier-navigation' ? '#4CAF50' : '#ddd',
                color: currentTestMode === 'tier-navigation' ? 'white' : 'black',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              QuizTierNavigation
            </button>
            <button
              onClick={() => switchTestMode('component-renderer')}
              style={{
                padding: '10px 20px',
                backgroundColor: currentTestMode === 'component-renderer' ? '#4CAF50' : '#ddd',
                color: currentTestMode === 'component-renderer' ? 'white' : 'black',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              QuizComponentRenderer
            </button>
            <button
              onClick={() => switchTestMode('emotion-wheel')}
              style={{
                padding: '10px 20px',
                backgroundColor: currentTestMode === 'emotion-wheel' ? '#4CAF50' : '#ddd',
                color: currentTestMode === 'emotion-wheel' ? 'white' : 'black',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              EmotionWheelView
            </button>
          </div>
        </div>

        {/* 操作按钮 */}
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button
            onClick={resetAllSelections}
            style={{
              padding: '10px 20px',
              backgroundColor: '#FF5722',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            重置所有选择
          </button>
          <button
            onClick={refreshData}
            style={{
              padding: '10px 20px',
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            刷新数据
          </button>
        </div>

        {/* 状态显示 */}
        <div style={{ marginTop: '15px', fontSize: '14px', color: '#666' }}>
          当前模式: {currentTestMode} |
          当前层级: {currentTierIndex + 1} |
          已选择: {selectedEmotions.length} 个选项
        </div>
      </div>

      {/* 主要测试内容 */}
      <section style={{ marginBottom: '60px' }}>
        {currentTestMode === 'tier-navigation' && (
          <div>
            <h2 style={{ marginBottom: '20px' }}>QuizTierNavigation 测试</h2>
            <div style={{
              background: 'white',
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              minHeight: '500px'
            }}>
              {(() => {
                const currentQuestion = getCurrentQuestion() || mockQuestions[currentTierIndex] || mockQuestions[0];
                return (
                  <QuizTierNavigation
                    currentQuestion={currentQuestion}
                    onSelect={handleEmotionSelect}
                    onBack={handleTierBack}
                    selectedPath={selectedPath}
                    packId={emotionWheelData?.pack.id || 'mock-pack'}
                  />
                );
              })()}
            </div>
          </div>
        )}

        {currentTestMode === 'component-renderer' && (
          <div>
            <h2 style={{ marginBottom: '20px' }}>QuizComponentRenderer 测试</h2>
            <div style={{ display: 'grid', gap: '30px', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))' }}>

              {/* 选择器组件测试 */}
              <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <h3>选择器组件 (Selector)</h3>
                <QuizComponentRenderer
                  componentType="selector_component"
                  config={{
                    id: 'test-selector',
                    question_text: '请选择您的情绪',
                    options: mockQuestions[0].options,
                    multiple: false,
                    layout: 'grid'
                  }}
                  onSelect={handleEmotionSelect}
                />
              </div>

              {/* 评分组件测试 */}
              <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <h3>评分组件 (Rating)</h3>
                <QuizComponentRenderer
                  componentType="rating_component"
                  config={{
                    id: 'test-rating',
                    question_text: '请评分您的情绪强度',
                    options: [],
                    scale_type: 'numeric',
                    min_value: 1,
                    max_value: 5
                  }}
                  onSelect={(value) => {
                    setInteractionLog(prev => [`评分: ${value}`, ...prev.slice(0, 9)]);
                  }}
                />
              </div>

              {/* 滑块组件测试 */}
              <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <h3>滑块组件 (Slider)</h3>
                <QuizComponentRenderer
                  componentType="slider_component"
                  config={{
                    id: 'test-slider',
                    question_text: '调整您的情绪强度',
                    min_value: 0,
                    max_value: 100,
                    step_value: 5,
                    default_value: 50,
                    show_labels: true,
                    show_value: true
                  }}
                  onSelect={(value) => {
                    setInteractionLog(prev => [`滑块值: ${value}`, ...prev.slice(0, 9)]);
                  }}
                />
              </div>

              {/* 卡片组件测试 */}
              <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <h3>卡片组件 (Card)</h3>
                <QuizComponentRenderer
                  componentType="card_component"
                  config={{
                    id: 'test-cards',
                    question_text: '选择情绪卡片',
                    options: mockQuestions[0].options,
                    card_layout: 'grid',
                    columns: 2,
                    card_size: 'medium'
                  }}
                  onSelect={handleEmotionSelect}
                />
              </div>

            </div>
          </div>
        )}

        {currentTestMode === 'emotion-wheel' && (
          <div>
            <h2 style={{ marginBottom: '20px' }}>EmotionWheelView 测试</h2>
            <div style={{ display: 'flex', gap: '40px', justifyContent: 'center', flexWrap: 'wrap' }}>
              <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <h3>情绪轮盘</h3>
                <EmotionWheelView
                  id="test-emotion-wheel"
                  emotions={mockQuestions[0].options.map(option => {
                    let metadata = {};
                    try {
                      metadata = JSON.parse(option.metadata || '{}');
                    } catch {
                      metadata = {};
                    }
                    return {
                      id: option.id,
                      name: { zh: option.option_text, en: option.option_text },
                      emoji: (metadata as any).emoji || '😊',
                      color: (metadata as any).color || '#3b82f6',
                      tier_level: 1,
                      parent_id: undefined
                    };
                  })}
                  selectedEmotions={selectedEmotions.map(e => e.id)}
                  onEmotionSelect={(emotionId, emotionItem) => {
                    const option = mockQuestions[0].options.find(opt => opt.id === emotionId);
                    if (option) {
                      handleEmotionSelect(option);
                    }
                  }}
                  config={{
                    size: 350,
                    center_radius: 40,
                    tier_spacing: 70,
                    animation_duration: 300,
                    show_labels: true,
                    show_emojis: true,
                    interactive: true
                  }}
                  personalization={{
                    user_type: 'general',
                    language: 'zh',
                    theme: 'modern',
                    accessibility: {
                      high_contrast: false,
                      large_text: false,
                      screen_reader: false,
                      keyboard_navigation: false
                    },
                    cultural_preferences: {
                      tcm_elements: true,
                      traditional_colors: false,
                      simplified_interface: false
                    }
                  }}
                  onInteraction={(event) => {
                    setInteractionLog(prev => [
                      `轮盘交互: ${event.type} - ${event.target}`,
                      ...prev.slice(0, 9)
                    ]);
                  }}
                  language="zh"
                />
              </div>
            </div>
          </div>
        )}
      </section>

      {/* 状态显示 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>选择状态和数据信息</h2>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '20px'
        }}>
          <div style={{
            background: '#f5f5f5',
            padding: '16px',
            borderRadius: '8px'
          }}>
            <h3>当前选择</h3>
            <p><strong>测试模式:</strong> {currentTestMode}</p>
            <p><strong>当前层级:</strong> {currentTierIndex + 1}</p>
            <p><strong>已选择:</strong> {selectedEmotions.length} 个选项</p>
            <div style={{ marginTop: '10px' }}>
              <strong>选择详情:</strong>
              {selectedEmotions.length === 0 ? (
                <span style={{ color: '#666', fontStyle: 'italic' }}> 无</span>
              ) : (
                <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                  {selectedEmotions.map((emotion, index) => (
                    <li key={index} style={{ fontSize: '12px' }}>
                      {emotion.text} ({emotion.value}) - 层级 {emotion.tierLevel}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>

          <div style={{
            background: '#f5f5f5',
            padding: '16px',
            borderRadius: '8px'
          }}>
            <h3>数据源信息</h3>
            <p><strong>情绪轮盘数据:</strong> {emotionWheelData ? '已加载' : '使用模拟数据'}</p>
            <p><strong>Quiz包:</strong> {emotionWheelData?.pack.name || 'Mock Quiz Pack'}</p>
            <p><strong>问题数量:</strong> {emotionWheelData?.questions.length || mockQuestions.length}</p>
            <p><strong>主要情绪:</strong> {emotionWheelData?.primaryEmotions.length || mockQuestions[0].options.length}</p>
            <p><strong>次要情绪:</strong> {emotionWheelData?.secondaryEmotions.length || 0}</p>
          </div>

          <div style={{
            background: '#f5f5f5',
            padding: '16px',
            borderRadius: '8px'
          }}>
            <h3>选择路径</h3>
            <p><strong>路径长度:</strong> {Object.keys(selectedPath).length}</p>
            <div style={{ marginTop: '10px' }}>
              <strong>路径详情:</strong>
              {Object.keys(selectedPath).length === 0 ? (
                <span style={{ color: '#666', fontStyle: 'italic' }}> 无</span>
              ) : (
                <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                  {Object.entries(selectedPath).map(([questionId, option]) => (
                    <li key={questionId} style={{ fontSize: '12px' }}>
                      {questionId}: {option.option_text}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* 交互日志 */}
      <section>
        <h2>交互日志</h2>
        <div style={{
          background: '#f5f5f5',
          padding: '16px',
          borderRadius: '8px',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {interactionLog.length === 0 ? (
            <p style={{ color: '#666', fontStyle: 'italic' }}>暂无交互记录</p>
          ) : (
            interactionLog.map((logEntry, index) => (
              <div key={index} style={{
                marginBottom: '8px',
                padding: '8px',
                background: 'white',
                borderRadius: '4px',
                fontSize: '12px',
                fontFamily: 'monospace'
              }}>
                <div><strong>#{index + 1}:</strong> {logEntry}</div>
                <div style={{ color: '#666', fontSize: '10px' }}>
                  {new Date().toLocaleTimeString()}
                </div>
              </div>
            ))
          )}
        </div>
      </section>

      {/* 架构说明 */}
      <section style={{ marginTop: '40px', padding: '20px', background: '#e8f5e8', borderRadius: '8px' }}>
        <h2>新架构说明</h2>
        <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
          <p><strong>QuizTierNavigation:</strong> 基于统一表结构的层级导航组件，支持多种问题类型和智能组件选择。</p>
          <p><strong>QuizComponentRenderer:</strong> 动态组件渲染器，支持7种组件类型（选择器、评分、滑块、文本输入、轮盘、卡片、气泡）。</p>
          <p><strong>EmotionWheelView:</strong> 专门的情绪轮盘视图，支持多层级情绪展示和父子联动。</p>
          <p><strong>数据架构:</strong> 从emotion_data_sets迁移到quiz_packs → quiz_questions → quiz_question_options的统一表结构。</p>
          <p><strong>个性化配置:</strong> 支持6层个性化配置，从全局基础配置到实时交互配置。</p>
        </div>
      </section>
    </div>
  );
};

export default SpecialViewsTest;
