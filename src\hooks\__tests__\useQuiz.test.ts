/**
 * useQuiz Hook 测试
 * 测试Quiz系统相关功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useQuizPacks, useCreateQuizSession, useSubmitAnswer, useEmotionWheelQuiz } from '../useQuiz';

// Mock Services
vi.mock('../../services', () => ({
  Services: {
    quizPack: vi.fn().mockResolvedValue({
      getAll: vi.fn(),
      getFeaturedQuizPacks: vi.fn(),
      getQuizPacksByType: vi.fn(),
    }),
    quizSession: vi.fn().mockResolvedValue({
      createSession: vi.fn(),
      getCurrentQuestion: vi.fn(),
    }),
    quizAnswer: vi.fn().mockResolvedValue({
      saveAnswer: vi.fn(),
    }),
    quizResult: vi.fn().mockResolvedValue({
      getBySessionId: vi.fn(),
    }),
  }
}));

// Mock tRPC
vi.mock('../../lib/trpc', () => ({
  trpc: {
    getQuizPacks: {
      query: vi.fn()
    },
    getRecommendedQuizPacks: {
      query: vi.fn()
    },
    createQuizSession: {
      mutate: vi.fn()
    },
    submitAnswer: {
      mutate: vi.fn()
    },
    getQuizResult: {
      query: vi.fn()
    },
    getUserSessions: {
      query: vi.fn()
    }
  }
}));

// Mock useHybridData
vi.mock('../useHybridData', () => ({
  useHybridData: vi.fn((key, onlineFn, offlineFn, options) => {
    // Simple mock that returns loading state initially
    return {
      data: null,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
      isOnline: true,
      lastSyncTime: null,
    };
  })
}));

describe('useQuiz', () => {
  let mockServices: any;
  let mockTrpc: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Get mocked modules
    const { Services } = await import('../../services');
    const { trpc } = await import('../../lib/trpc');
    
    mockServices = {
      quizPack: await Services.quizPack(),
      quizSession: await Services.quizSession(),
      quizAnswer: await Services.quizAnswer(),
      quizResult: await Services.quizResult(),
    };
    
    mockTrpc = trpc;
  });

  describe('useQuizPacks', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useQuizPacks());

      expect(result.current.data).toBe(null);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(typeof result.current.refetch).toBe('function');
    });

    it('should accept options parameter', () => {
      const options = {
        category: 'emotion',
        difficultyLevel: 2,
        limit: 10
      };

      const { result } = renderHook(() => useQuizPacks(options));

      expect(result.current).toBeDefined();
    });
  });

  describe('useCreateQuizSession', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useCreateQuizSession());

      expect(result.current.isPending).toBe(false);
      expect(result.current.isSuccess).toBe(true);
      expect(result.current.isError).toBe(false);
      expect(result.current.error).toBe(null);
      expect(result.current.data).toBe(null);
      expect(typeof result.current.mutateAsync).toBe('function');
    });

    it('should handle successful session creation', async () => {
      const mockResponse = {
        success: true,
        data: {
          id: 'session_123',
          pack_id: 'pack_emotion_wheel',
          user_id: 'user_123',
          status: 'INITIATED'
        }
      };

      mockTrpc.createQuizSession.mutate.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useCreateQuizSession());

      await act(async () => {
        const response = await result.current.mutateAsync({
          packId: 'pack_emotion_wheel',
          userId: 'user_123'
        });

        expect(response.success).toBe(true);
        expect(response.data.id).toBe('session_123');
      });

      expect(result.current.isError).toBe(false);
    });

    it('should handle online failure with offline fallback', async () => {
      const mockOfflineResponse = {
        success: true,
        data: {
          id: 'session_offline_123',
          pack_id: 'pack_emotion_wheel',
          user_id: 'user_123',
          status: 'INITIATED'
        }
      };

      mockTrpc.createQuizSession.mutate.mockRejectedValue(new Error('Network error'));
      mockServices.quizSession.createSession.mockResolvedValue(mockOfflineResponse);

      const { result } = renderHook(() => useCreateQuizSession());

      await act(async () => {
        const response = await result.current.mutateAsync({
          packId: 'pack_emotion_wheel',
          userId: 'user_123'
        });

        expect(response.success).toBe(true);
        expect(response.data.id).toBe('session_offline_123');
      });
    });

    it('should handle complete failure', async () => {
      mockTrpc.createQuizSession.mutate.mockRejectedValue(new Error('Network error'));
      mockServices.quizSession.createSession.mockResolvedValue({
        success: false,
        error: 'Offline creation failed'
      });

      const { result } = renderHook(() => useCreateQuizSession());

      await act(async () => {
        try {
          await result.current.mutateAsync({
            packId: 'pack_emotion_wheel',
            userId: 'user_123'
          });
        } catch (error) {
          expect(error).toBeInstanceOf(Error);
        }
      });

      expect(result.current.isError).toBe(true);
      expect(result.current.error).toBeTruthy();
    });
  });

  describe('useSubmitAnswer', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useSubmitAnswer());

      expect(result.current.isPending).toBe(false);
      expect(result.current.isSuccess).toBe(true);
      expect(result.current.isError).toBe(false);
      expect(result.current.error).toBe(null);
      expect(typeof result.current.mutateAsync).toBe('function');
    });

    it('should handle successful answer submission', async () => {
      const mockResponse = {
        success: true,
        data: {
          id: 'answer_123',
          session_id: 'session_123',
          question_id: 'question_123'
        }
      };

      mockTrpc.submitAnswer.mutate.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useSubmitAnswer());

      await act(async () => {
        const response = await result.current.mutateAsync({
          session_id: 'session_123',
          question_id: 'question_123',
          selected_option_ids: ['option_1'],
          answer_value: 'happy',
          confidence_score: 85
        });

        expect(response.success).toBe(true);
        expect(response.data.id).toBe('answer_123');
      });
    });

    it('should handle offline fallback for answer submission', async () => {
      const mockOfflineResponse = {
        success: true,
        data: {
          id: 'answer_offline_123',
          session_id: 'session_123',
          question_id: 'question_123'
        }
      };

      mockTrpc.submitAnswer.mutate.mockRejectedValue(new Error('Network error'));
      mockServices.quizAnswer.saveAnswer.mockResolvedValue(mockOfflineResponse);

      const { result } = renderHook(() => useSubmitAnswer());

      await act(async () => {
        const response = await result.current.mutateAsync({
          session_id: 'session_123',
          question_id: 'question_123',
          selected_option_ids: ['option_1'],
          answer_value: 'happy'
        });

        expect(response.success).toBe(true);
        expect(response.data.id).toBe('answer_offline_123');
      });
    });
  });

  describe('useEmotionWheelQuiz', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useEmotionWheelQuiz());

      expect(result.current.isCreating).toBe(false);
      expect(result.current.error).toBe(null);
      expect(typeof result.current.startEmotionWheelQuiz).toBe('function');
    });

    it('should start emotion wheel quiz with specified pack ID', async () => {
      const mockResponse = {
        success: true,
        data: {
          id: 'session_emotion_123',
          pack_id: 'pack_emotion_wheel',
          user_id: 'user_123'
        }
      };

      mockTrpc.createQuizSession.mutate.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useEmotionWheelQuiz());

      await act(async () => {
        const response = await result.current.startEmotionWheelQuiz(
          'user_123',
          'pack_emotion_wheel'
        );

        expect(response.success).toBe(true);
        expect(response.data.pack_id).toBe('pack_emotion_wheel');
      });
    });

    it('should find emotion wheel pack when no pack ID specified', async () => {
      const mockQuizPacks = [
        {
          id: 'pack_emotion_wheel_1',
          name: 'Emotion Wheel Quiz',
          quiz_type: 'emotion_wheel'
        }
      ];

      const mockSessionResponse = {
        success: true,
        data: {
          id: 'session_auto_123',
          pack_id: 'pack_emotion_wheel_1',
          user_id: 'user_123'
        }
      };

      mockServices.quizPack.getQuizPacksByType.mockResolvedValue({
        success: true,
        data: mockQuizPacks
      });
      mockTrpc.createQuizSession.mutate.mockResolvedValue(mockSessionResponse);

      const { result } = renderHook(() => useEmotionWheelQuiz());

      await act(async () => {
        const response = await result.current.startEmotionWheelQuiz('user_123');

        expect(response.success).toBe(true);
        expect(response.data.pack_id).toBe('pack_emotion_wheel_1');
      });

      expect(mockServices.quizPack.getQuizPacksByType).toHaveBeenCalledWith('emotion_wheel');
    });

    it('should handle error when no emotion wheel packs available', async () => {
      mockServices.quizPack.getQuizPacksByType.mockResolvedValue({
        success: true,
        data: []
      });

      const { result } = renderHook(() => useEmotionWheelQuiz());

      await act(async () => {
        try {
          await result.current.startEmotionWheelQuiz('user_123');
        } catch (error) {
          expect(error).toBeInstanceOf(Error);
          expect((error as Error).message).toBe('No emotion wheel quiz pack available');
        }
      });
    });
  });
});
