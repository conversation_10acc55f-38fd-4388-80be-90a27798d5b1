/**
 * QuizSessionService 测试
 * 验证修复后的服务架构
 */

import { describe, it, expect, beforeEach, mock } from 'bun:test';
import { QuizSessionService } from '../QuizSessionService';
import { QuizSessionRepository } from '../QuizSessionRepository';
import { QuizSession } from '../../../types/schema/base';
import { CreateQuizSessionInput, UpdateQuizSessionInput } from '../../../types/schema/api';

// Mock SQLiteDBConnection
const mockDb = {
  query: mock(() => Promise.resolve({ values: [] })),
  run: mock(() => Promise.resolve({ changes: { changes: 1 } })),
  execute: mock(() => Promise.resolve()),
} as any;

describe('QuizSessionService', () => {
  let service: QuizSessionService;

  beforeEach(() => {
    // Reset all mocks
    mockDb.query.mockClear();
    mockDb.run.mockClear();
    mockDb.execute.mockClear();

    // Create service instance
    service = new QuizSessionService(mockDb);
  });

  describe('createSession', () => {
    it('should create a new quiz session successfully', async () => {
      const input: CreateQuizSessionInput = {
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        session_type: 'standard',
      };

      const mockSession: QuizSession = {
        id: 'session_123',
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'INITIATED',
        current_question_index: 0,
        total_questions: 0,
        answered_questions: 0,
        completion_percentage: 0,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock the database query to return the created session
      mockDb.query.mockResolvedValue({
        values: [mockSession]
      });

      const result = await service.createSession(input);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.pack_id).toBe(input.pack_id);
      expect(result.data?.user_id).toBe(input.user_id);
    });

    it('should fail validation when pack_id is missing', async () => {
      const input: CreateQuizSessionInput = {
        pack_id: '',
        user_id: 'user_123',
      };

      const result = await service.createSession(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Pack ID is required');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should fail validation when user_id is missing', async () => {
      const input: CreateQuizSessionInput = {
        pack_id: 'pack_emotion_wheel',
        user_id: '',
      };

      const result = await service.createSession(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('User ID is required');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('startSession', () => {
    it('should start a session successfully', async () => {
      const sessionId = 'session_123';
      const mockSession: QuizSession = {
        id: sessionId,
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'IN_PROGRESS',
        current_question_index: 0,
        total_questions: 5,
        answered_questions: 0,
        completion_percentage: 0,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.update.mockResolvedValue(mockSession);

      const result = await service.startSession(sessionId);

      expect(result.success).toBe(true);
      expect(result.data?.status).toBe('IN_PROGRESS');
      expect(mockRepository.update).toHaveBeenCalledWith(sessionId, {
        status: 'IN_PROGRESS'
      });
    });
  });

  describe('completeSession', () => {
    it('should complete a session successfully', async () => {
      const sessionId = 'session_123';
      const mockSession: QuizSession = {
        id: sessionId,
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'COMPLETED',
        current_question_index: 5,
        total_questions: 5,
        answered_questions: 5,
        completion_percentage: 100,
        start_time: new Date(),
        end_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.update.mockResolvedValue(mockSession);

      const result = await service.completeSession(sessionId);

      expect(result.success).toBe(true);
      expect(result.data?.status).toBe('COMPLETED');
      expect(result.data?.completion_percentage).toBe(100);
      expect(mockRepository.update).toHaveBeenCalledWith(sessionId, expect.objectContaining({
        status: 'COMPLETED',
        completion_percentage: 100,
      }));
    });
  });

  describe('updateProgress', () => {
    it('should update progress successfully', async () => {
      const sessionId = 'session_123';
      const currentQuestionIndex = 2;
      const totalQuestions = 5;

      const mockSession: QuizSession = {
        id: sessionId,
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'IN_PROGRESS',
        current_question_index: currentQuestionIndex,
        total_questions: totalQuestions,
        answered_questions: 2,
        completion_percentage: 40,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.update.mockResolvedValue(mockSession);

      const result = await service.updateProgress(sessionId, currentQuestionIndex, totalQuestions);

      expect(result.success).toBe(true);
      expect(mockRepository.update).toHaveBeenCalledWith(sessionId, expect.objectContaining({
        current_question_index: currentQuestionIndex,
        completion_percentage: 40,
        total_questions: totalQuestions,
      }));
    });

    it('should auto-complete session when progress reaches 100%', async () => {
      const sessionId = 'session_123';
      const currentQuestionIndex = 5;
      const totalQuestions = 5;

      const mockSession: QuizSession = {
        id: sessionId,
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'COMPLETED',
        current_question_index: currentQuestionIndex,
        total_questions: totalQuestions,
        answered_questions: 5,
        completion_percentage: 100,
        start_time: new Date(),
        end_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.update.mockResolvedValue(mockSession);

      const result = await service.updateProgress(sessionId, currentQuestionIndex, totalQuestions);

      expect(result.success).toBe(true);
      expect(mockRepository.update).toHaveBeenCalledWith(sessionId, expect.objectContaining({
        current_question_index: currentQuestionIndex,
        completion_percentage: 100,
        total_questions: totalQuestions,
        status: 'COMPLETED',
      }));
    });
  });

  describe('getUserSessions', () => {
    it('should get user sessions successfully', async () => {
      const userId = 'user_123';
      const mockSessions: QuizSession[] = [
        {
          id: 'session_1',
          pack_id: 'pack_emotion_wheel',
          user_id: userId,
          status: 'COMPLETED',
          current_question_index: 5,
          total_questions: 5,
          answered_questions: 5,
          completion_percentage: 100,
          start_time: new Date(),
          end_time: new Date(),
          last_active_time: new Date(),
          session_type: 'standard',
          session_metadata: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'session_2',
          pack_id: 'pack_tcm_assessment',
          user_id: userId,
          status: 'IN_PROGRESS',
          current_question_index: 3,
          total_questions: 10,
          answered_questions: 3,
          completion_percentage: 30,
          start_time: new Date(),
          last_active_time: new Date(),
          session_type: 'standard',
          session_metadata: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      mockRepository.findByUserId.mockResolvedValue(mockSessions);

      const result = await service.getUserSessions(userId);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data?.[0].status).toBe('COMPLETED');
      expect(result.data?.[1].status).toBe('IN_PROGRESS');
      expect(mockRepository.findByUserId).toHaveBeenCalledWith(userId, 20);
    });
  });

  describe('getUserQuizStats', () => {
    it('should calculate user quiz stats correctly', async () => {
      const userId = 'user_123';
      const mockSessions: QuizSession[] = [
        {
          id: 'session_1',
          pack_id: 'pack_emotion_wheel',
          user_id: userId,
          status: 'COMPLETED',
          current_question_index: 5,
          total_questions: 5,
          answered_questions: 5,
          completion_percentage: 100,
          start_time: new Date('2024-01-01T10:00:00Z'),
          end_time: new Date('2024-01-01T10:05:00Z'), // 5 minutes
          last_active_time: new Date(),
          session_type: 'standard',
          session_metadata: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'session_2',
          pack_id: 'pack_emotion_wheel',
          user_id: userId,
          status: 'COMPLETED',
          current_question_index: 5,
          total_questions: 5,
          answered_questions: 5,
          completion_percentage: 100,
          start_time: new Date('2024-01-02T10:00:00Z'),
          end_time: new Date('2024-01-02T10:03:00Z'), // 3 minutes
          last_active_time: new Date(),
          session_type: 'standard',
          session_metadata: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'session_3',
          pack_id: 'pack_tcm_assessment',
          user_id: userId,
          status: 'IN_PROGRESS',
          current_question_index: 3,
          total_questions: 10,
          answered_questions: 3,
          completion_percentage: 30,
          start_time: new Date(),
          last_active_time: new Date(),
          session_type: 'standard',
          session_metadata: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      mockRepository.findByUserId.mockResolvedValue(mockSessions);

      const result = await service.getUserQuizStats(userId);

      expect(result.success).toBe(true);
      expect(result.data?.total_sessions).toBe(3);
      expect(result.data?.completed_sessions).toBe(2);
      expect(result.data?.in_progress_sessions).toBe(1);
      expect(result.data?.completion_rate).toBe(67); // 2/3 * 100 rounded
      expect(result.data?.average_session_time).toBe(240); // (300 + 180) / 2 seconds
      expect(result.data?.most_popular_packs).toEqual([
        { pack_id: 'pack_emotion_wheel', session_count: 2 },
        { pack_id: 'pack_tcm_assessment', session_count: 1 },
      ]);
    });
  });

  describe('event emission', () => {
    it('should emit events for session operations', async () => {
      const sessionCreatedSpy = jest.fn();
      const sessionStartedSpy = jest.fn();
      const sessionCompletedSpy = jest.fn();

      service.on('sessionCreated', sessionCreatedSpy);
      service.on('sessionStarted', sessionStartedSpy);
      service.on('sessionCompleted', sessionCompletedSpy);

      const mockSession: QuizSession = {
        id: 'session_123',
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'INITIATED',
        current_question_index: 0,
        total_questions: 0,
        answered_questions: 0,
        completion_percentage: 0,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.create.mockResolvedValue(mockSession);
      mockRepository.update.mockResolvedValue({ ...mockSession, status: 'IN_PROGRESS' });

      // Test session creation event
      await service.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
      });

      expect(sessionCreatedSpy).toHaveBeenCalledWith(mockSession);

      // Test session start event
      await service.startSession('session_123');

      expect(sessionStartedSpy).toHaveBeenCalledWith(expect.objectContaining({
        status: 'IN_PROGRESS'
      }));
    });
  });
});
