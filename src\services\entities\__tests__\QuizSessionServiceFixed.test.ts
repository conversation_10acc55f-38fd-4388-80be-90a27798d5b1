/**
 * QuizSessionService 测试
 * 验证修复后的服务架构
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuizSessionService } from '../QuizSessionService';
import { QuizSession } from '../../../types/schema/base';
import { CreateQuizSessionInput } from '../../../types/schema/api';

// Mock SQLiteDBConnection
const mockDb = {
  query: vi.fn(() => Promise.resolve({ values: [] })),
  run: vi.fn(() => Promise.resolve({ changes: { changes: 1 } })),
  execute: vi.fn(() => Promise.resolve()),
} as any;

describe('QuizSessionService', () => {
  let service: QuizSessionService;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Create service instance
    service = new QuizSessionService(mockDb);
  });

  describe('createSession', () => {
    it('should create a new quiz session successfully', async () => {
      const input: CreateQuizSessionInput = {
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        session_type: 'standard',
      };

      const mockSession: QuizSession = {
        id: 'session_123',
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'INITIATED',
        current_question_index: 0,
        total_questions: 0,
        answered_questions: 0,
        completion_percentage: 0,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock the database query to return the created session
      mockDb.query.mockResolvedValue({
        values: [mockSession]
      });

      const result = await service.createSession(input);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.pack_id).toBe(input.pack_id);
      expect(result.data?.user_id).toBe(input.user_id);
    });

    it('should fail validation when pack_id is missing', async () => {
      const input: CreateQuizSessionInput = {
        pack_id: '',
        user_id: 'user_123',
      };

      const result = await service.createSession(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Pack ID is required');
    });

    it('should fail validation when user_id is missing', async () => {
      const input: CreateQuizSessionInput = {
        pack_id: 'pack_emotion_wheel',
        user_id: '',
      };

      const result = await service.createSession(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('User ID is required');
    });
  });

  describe('startSession', () => {
    it('should start a session successfully', async () => {
      const sessionId = 'session_123';
      const mockSession: QuizSession = {
        id: sessionId,
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'IN_PROGRESS',
        current_question_index: 0,
        total_questions: 5,
        answered_questions: 0,
        completion_percentage: 0,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock database query to return updated session
      mockDb.query.mockResolvedValue({
        values: [mockSession]
      });

      const result = await service.startSession(sessionId);

      expect(result.success).toBe(true);
      expect(result.data?.status).toBe('IN_PROGRESS');
    });
  });

  describe('completeSession', () => {
    it('should complete a session successfully', async () => {
      const sessionId = 'session_123';
      const mockSession: QuizSession = {
        id: sessionId,
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'COMPLETED',
        current_question_index: 5,
        total_questions: 5,
        answered_questions: 5,
        completion_percentage: 100,
        start_time: new Date(),
        end_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock database query to return completed session
      mockDb.query.mockResolvedValue({
        values: [mockSession]
      });

      const result = await service.completeSession(sessionId);

      expect(result.success).toBe(true);
      expect(result.data?.status).toBe('COMPLETED');
      expect(result.data?.completion_percentage).toBe(100);
    });
  });

  describe('updateProgress', () => {
    it('should update progress successfully', async () => {
      const sessionId = 'session_123';
      const currentQuestionIndex = 2;
      const totalQuestions = 5;

      const mockSession: QuizSession = {
        id: sessionId,
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'IN_PROGRESS',
        current_question_index: currentQuestionIndex,
        total_questions: totalQuestions,
        answered_questions: 2,
        completion_percentage: 40,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock database query to return updated session
      mockDb.query.mockResolvedValue({
        values: [mockSession]
      });

      const result = await service.updateProgress(sessionId, currentQuestionIndex, totalQuestions);

      expect(result.success).toBe(true);
      expect(result.data?.completion_percentage).toBe(40);
    });
  });

  describe('getUserSessions', () => {
    it('should get user sessions successfully', async () => {
      const userId = 'user_123';
      const mockSessions: QuizSession[] = [
        {
          id: 'session_1',
          pack_id: 'pack_emotion_wheel',
          user_id: userId,
          status: 'COMPLETED',
          current_question_index: 5,
          total_questions: 5,
          answered_questions: 5,
          completion_percentage: 100,
          start_time: new Date(),
          end_time: new Date(),
          last_active_time: new Date(),
          session_type: 'standard',
          session_metadata: {},
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      // Mock database query to return sessions
      mockDb.query.mockResolvedValue({
        values: mockSessions
      });

      const result = await service.getUserSessions(userId);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data?.[0].status).toBe('COMPLETED');
    });
  });

});
