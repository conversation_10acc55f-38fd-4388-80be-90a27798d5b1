# Translation Management Scripts

This directory contains scripts to help manage translations in the Mindful Mood application.

## Available Scripts

### 1. Translation Key Checker (`check-translations.mjs`)

This script scans the codebase for translation keys used in the UI and compares them with keys defined in database seed files and JSON translation files. It helps identify missing or unused translation keys.

**Usage:**
```
node scripts\check-translations.mjs
```
Or simply run the batch file:
```
scripts\check-translations.bat
```

**Output:**
- Lists keys used in code but missing from seed files
- Lists keys used in code but missing from JSON files
- Lists keys in seed files but not used in code
- Lists keys in JSON files but not used in code
- Lists keys in seed files but missing from JSON files
- Lists keys in JSON files but missing from seed files

### 2. Translation Key Fixer (`fix-translations.mjs`)

This script not only detects missing translation keys but also generates SQL and JSON updates to fix the issues.

**Usage:**
```
node scripts\fix-translations.mjs
```
Or simply run the batch file:
```
scripts\fix-translations.bat
```

**Output:**
- Performs the same analysis as the checker script
- Generates SQL files with INSERT statements for missing keys in seed files
- Generates updated JSON files with missing keys added
- All generated files are placed in the `translation-fixes` directory

## How to Use the Generated Fixes

### SQL Fixes

1. Review the generated SQL files in the `translation-fixes` directory
2. Copy the SQL statements to the appropriate seed files:
   - `missing_ui_labels.sql` → `public/seeds/ui_labels.sql`
   - `missing_ui_label_translations.sql` → `public/seeds/ui_label_translations.sql`

### JSON Fixes

1. Review the generated JSON files in the `translation-fixes` directory
2. Copy the content to the appropriate locale files:
   - `en_updated.json` → `src/locales/en.json`
   - `zh_updated.json` → `src/locales/zh.json`

## Configuration

Both scripts use a configuration object at the top of the file. You can modify this configuration to suit your needs:

- `srcDir`: Directory to scan for code files
- `seedDir`: Directory containing seed files
- `localesDir`: Directory containing JSON translation files
- `uiLabelsSeedFile`: Filename of the UI labels seed file
- `uiLabelTranslationsSeedFile`: Filename of the UI label translations seed file
- `jsonFiles`: Array of JSON translation files
- `fileExtensions`: Array of file extensions to scan
- `excludeDirs`: Array of directories to exclude from scanning
- `translationFunctionNames`: Array of function names used for translations
- `outputDir`: Directory to output generated fixes
- `languages`: Array of language codes

## Notes

- The scripts assume that translation keys are used in the format `t('key')` or `t("key")` in the code.
- The scripts assume that the database seed files use `INSERT OR IGNORE INTO` statements.
- The scripts assume that JSON translation files use a flat structure with dot-notation keys.
