/**
 * 简化卡片视图组件
 */

import { useTheme } from '@/contexts/ThemeContext';
import type { Emotion } from '@/types/emotionDataTypes';
import type { ContentDisplayMode } from '@/types/previewTypes';
import type { SkinConfig } from '@/types/skinTypes';
import type React from 'react';
import { useState } from 'react';

interface SimpleCardViewProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
}

/**
 * 简化卡片视图组件
 * 用于在SkinPreview中预览卡片视图
 */
export const SimpleCardView: React.FC<SimpleCardViewProps> = ({
  emotions,
  tierLevel, // 保留参数以保持接口一致性，但在此组件中未使用
  contentDisplayMode,
  skinConfig,
  onSelect,
}) => {
  const { theme } = useTheme();
  // 计算是否为深色模式
  const isDarkMode =
    theme === 'dark' ||
    (theme === 'system' &&
      typeof window !== 'undefined' &&
      window.matchMedia('(prefers-color-scheme: dark)').matches);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // 计算卡片大小
  const cardSize = skinConfig.view_configs?.card?.card_size || 80;

  // 计算卡片间距
  const cardSpacing = skinConfig.view_configs?.card?.card_spacing || 10;

  // 计算每行卡片数量
  const cardsPerRow = Math.min(emotions.length, 4);

  // 计算行数 (用于将来的布局优化)
  // const rows = Math.ceil(emotions.length / cardsPerRow);

  // 获取情绪内容
  const getEmotionContent = (emotion: Emotion) => {
    const text = emotion.name;
    const emoji = emotion.emoji;

    switch (contentDisplayMode) {
      case 'text':
        return { text, emoji: '' };
      case 'emoji':
        return { text: '', emoji };
      default:
        return { text, emoji };
    }
  };

  // 获取情绪颜色
  const getEmotionColor = (emotion: Emotion, index: number) => {
    // 如果情绪有自定义颜色，优先使用
    if (emotion.color) {
      return emotion.color;
    }

    // 否则根据层级和索引生成颜色
    const hue = (index * 360) / emotions.length;
    const saturation = 70;
    const lightness = isDarkMode ? 50 : 60;

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  // 处理卡片悬停
  const handleCardHover = (index: number) => {
    setHoveredIndex(index);
  };

  // 处理卡片离开
  const handleCardLeave = () => {
    setHoveredIndex(null);
  };

  // 获取悬停效果
  const getHoverEffect = (index: number) => {
    if (hoveredIndex === index) {
      return 'scale(1.05)';
    }
    return 'scale(1)';
  };

  return (
    <div
      className="simple-card-view"
      style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${cardsPerRow}, ${cardSize}px)`,
        gap: `${cardSpacing}px`,
        justifyContent: 'center',
      }}
    >
      {emotions.map((emotion, index) => {
        const { text, emoji } = getEmotionContent(emotion);

        return (
          <div
            key={emotion.id}
            className="card"
            style={{
              width: `${cardSize}px`,
              height: `${cardSize}px`,
              backgroundColor: getEmotionColor(emotion, index),
              borderRadius: `${skinConfig.effects.border_radius}px`,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              boxShadow: skinConfig.effects.shadows ? '0 2px 4px rgba(0, 0, 0, 0.2)' : 'none',
              transform: getHoverEffect(index),
              transition: 'transform 0.2s ease',
              color: skinConfig.colors.text,
              padding: '8px',
            }}
            onClick={() => onSelect(emotion)}
            onMouseEnter={() => handleCardHover(index)}
            onMouseLeave={handleCardLeave}
          >
            {emoji && (
              <div className="emoji" style={{ fontSize: `${skinConfig.fonts.size.large}px` }}>
                {emoji}
              </div>
            )}
            {text && (
              <div
                className="text"
                style={{
                  fontSize: `${skinConfig.fonts.size.small}px`,
                  fontWeight: skinConfig.fonts.weight.normal,
                  fontFamily: skinConfig.fonts.family,
                  textAlign: 'center',
                  marginTop: emoji ? '4px' : '0',
                }}
              >
                {text}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
