// 检查 emotion_data_set_emotions 表
const { executeQuery, initializeDatabaseService, createSQLiteConfig } = require('./dist/database');

// 初始化数据库服务
initializeDatabaseService(createSQLiteConfig('./local.db'));

// 检查 emotion_data_set_emotions 表中的关键记录
const keyEmotions = [
  'default_happy',
  'default_sad',
  'default_angry',
  'default_fearful',
  'default_playful',
  'default_lonely',
  'default_frustrated',
  'default_anxious',
  'default_peaceful',
  'default_aroused',
  'default_isolated',
  'default_annoyed',
  'default_worried',
  'default_thankful',
];

// 构建查询
const query = `SELECT id FROM emotion_data_set_emotions WHERE id IN (${keyEmotions.map(() => '?').join(',')})`;

// 执行查询
executeQuery({
  sql: query,
  args: keyEmotions,
})
  .then((result) => {
    console.log(`Found ${result.rows.length} of ${keyEmotions.length} required emotions`);

    // 显示找到的情绪
    const foundIds = result.rows.map((row) => row.id);
    console.log('Found emotions:', foundIds);

    // 显示缺失的情绪
    const missingIds = keyEmotions.filter((id) => !foundIds.includes(id));
    console.log('Missing emotions:', missingIds);
  })
  .catch((error) => {
    console.error('Error checking emotions:', error);
  });
