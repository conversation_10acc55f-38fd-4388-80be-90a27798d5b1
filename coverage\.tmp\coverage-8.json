{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/security/security.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 84763, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 84763, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 520, "endOffset": 30877, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 563, "endOffset": 624, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 675, "endOffset": 6833, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 728, "endOffset": 3105, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 864, "endOffset": 1238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1328, "endOffset": 1666, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3155, "endOffset": 4929, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3288, "endOffset": 4010, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4979, "endOffset": 6825, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6887, "endOffset": 13655, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6940, "endOffset": 9124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7079, "endOffset": 7589, "count": 2}, {"startOffset": 7471, "endOffset": 7477, "count": 0}, {"startOffset": 7530, "endOffset": 7539, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7893, "endOffset": 8185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9172, "endOffset": 11402, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9947, "endOffset": 10171, "count": 6}, {"startOffset": 10053, "endOffset": 10058, "count": 0}, {"startOffset": 10117, "endOffset": 10152, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10258, "endOffset": 10645, "count": 2}, {"startOffset": 10415, "endOffset": 10644, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11199, "endOffset": 11259, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11317, "endOffset": 11380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11450, "endOffset": 13647, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11989, "endOffset": 12296, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13709, "endOffset": 20102, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13761, "endOffset": 15671, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13893, "endOffset": 14113, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14196, "endOffset": 14614, "count": 4}, {"startOffset": 14492, "endOffset": 14568, "count": 3}, {"startOffset": 14569, "endOffset": 14573, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14869, "endOffset": 15329, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15722, "endOffset": 17923, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15863, "endOffset": 16442, "count": 2}, {"startOffset": 16189, "endOffset": 16213, "count": 1}, {"startOffset": 16323, "endOffset": 16396, "count": 1}, {"startOffset": 16397, "endOffset": 16401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16529, "endOffset": 16731, "count": 1}, {"startOffset": 16679, "endOffset": 16730, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17973, "endOffset": 20094, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18333, "endOffset": 18621, "count": 2}, {"startOffset": 18451, "endOffset": 18493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18702, "endOffset": 18904, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20153, "endOffset": 25778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20207, "endOffset": 22638, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20349, "endOffset": 21317, "count": 1}, {"startOffset": 20988, "endOffset": 20993, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21056, "endOffset": 21090, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22688, "endOffset": 25770, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23216, "endOffset": 23517, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23599, "endOffset": 24118, "count": 2}, {"startOffset": 23941, "endOffset": 23949, "count": 1}, {"startOffset": 24069, "endOffset": 24077, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25832, "endOffset": 30873, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25885, "endOffset": 28268, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26283, "endOffset": 26616, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26699, "endOffset": 27172, "count": 1}, {"startOffset": 27061, "endOffset": 27171, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28316, "endOffset": 30865, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28447, "endOffset": 28926, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}