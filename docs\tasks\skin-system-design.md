# 情绪轮盘皮肤系统设计文档

## 1. 概述

情绪轮盘皮肤系统是一个灵活的、可扩展的显示框架，用于将情绪数据以不同的视觉形式呈现给用户。该系统将数据与展示完全分离，使得同一组情绪数据可以通过不同的皮肤、视图类型和渲染引擎进行展示，而不影响底层数据结构。

### 1.1 设计目标

- **数据与展示分离**：皮肤只负责展示，不影响数据处理
- **灵活性**：支持多种显示模式、渲染引擎和视图类型
- **可扩展性**：易于添加新的皮肤、渲染引擎和视图类型
- **一致性**：所有视图类型都提供一致的用户体验
- **游戏化**：提供类似游戏皮肤的体验，增强用户参与度

### 1.2 核心概念

- **皮肤(Skin)**：定义情绪数据的视觉表现形式，包括颜色、大小、动画等
- **显示模式(DisplayMode)**：定义内容的显示方式（文本、表情或两者兼有）
- **渲染引擎(RenderEngine)**：负责实际渲染视图的技术实现（D3、SVG、R3F等）
- **视图类型(ViewType)**：定义情绪数据的布局方式（轮盘、卡片、气泡等）

## 2. 系统架构

### 2.1 核心组件

```
DisplaySystem
├── DisplayAdapter (适配器)
│   ├── WheelFactory (轮盘工厂)
│   │   ├── BaseWheel (抽象基类)
│   │   │   ├── D3Wheel (D3.js实现)
│   │   │   ├── SVGWheel (SVG实现)
│   │   │   └── R3FWheel (React Three Fiber实现)
│   │   └── WheelContentStrategy (内容策略)
│   │       ├── TextOnlyStrategy (仅显示文本)
│   │       ├── EmojiOnlyStrategy (仅显示emoji)
│   │       └── TextEmojiStrategy (同时显示文本和emoji)
│   │
│   ├── CardFactory (卡片工厂)
│   │   ├── BaseCard (抽象基类)
│   │   │   └── CardView (卡片视图实现)
│   │   └── CardContentStrategy (内容策略)
│   │       ├── TextOnlyCardStrategy (仅显示文本)
│   │       ├── EmojiOnlyCardStrategy (仅显示emoji)
│   │       └── TextEmojiCardStrategy (同时显示文本和emoji)
│   │
│   ├── BubbleFactory (气泡工厂)
│   │   ├── BaseBubble (抽象基类)
│   │   │   └── BubbleView (气泡视图实现)
│   │   └── BubbleContentStrategy (内容策略)
│   │       ├── TextOnlyBubbleStrategy (仅显示文本)
│   │       ├── EmojiOnlyBubbleStrategy (仅显示emoji)
│   │       └── TextEmojiBubbleStrategy (同时显示文本和emoji)
│   │
│   ├── ListFactory (列表工厂)
│   │   └── BaseList (列表基类)
│   │
│   └── GridFactory (网格工厂)
│       └── BaseGrid (网格基类)
│
├── DisplaySystem (显示系统)
│   ├── DisplaySkin (皮肤接口)
│   │   ├── WheelSkins (轮盘皮肤)
│   │   ├── CardSkins (卡片皮肤)
│   │   ├── BubbleSkins (气泡皮肤)
│   │   └── CustomSkins (自定义皮肤)
│   │
│   └── DisplayOptions (显示选项)
│       ├── DisplayMode (显示模式)
│       ├── RenderEngine (渲染引擎)
│       └── ViewType (视图类型)
│
└── DisplayContext (显示上下文)
    └── useDisplay (显示钩子)
```

### 2.2 数据流

1. 用户选择皮肤和显示选项
2. DisplayContext 提供全局显示设置
3. TierNavigation 组件使用 DisplayAdapter 渲染情绪数据
4. DisplayAdapter 根据显示选项选择合适的工厂
5. 工厂创建具体的视图组件和内容策略
6. 视图组件渲染情绪数据并处理用户交互

## 3. 皮肤系统

### 3.1 皮肤接口

```typescript
interface DisplaySkin {
  id: string;                  // 皮肤ID
  name: string;                // 皮肤名称
  description: string;         // 皮肤描述
  previewImage: string;        // 预览图片
  category: string;            // 分类（免费、高级、限时等）
  price?: number;              // 价格（如适用）
  
  // 支持的显示模式
  supportedDisplayModes: DisplayMode[];
  
  // 支持的渲染引擎
  supported_render_engines: RenderEngine[];
  
  // 支持的视图类型
  supported_view_types: ViewType[];
  
  // 显示配置
  config: DisplayConfig;
  
  // 特殊效果和动画
  specialEffects?: {
    idleAnimation?: string;    // 空闲动画
    selectAnimation?: string;  // 选择动画
    backgroundEffect?: string; // 背景效果
  };
}
```

### 3.2 预定义皮肤

#### 3.2.1 轮盘皮肤

- **经典轮盘**：简洁的2D设计，柔和的颜色，文本和emoji并存
- **极简轮盘**：黑白配色，仅显示文本，无装饰和特效
- **表情轮盘**：突出显示emoji，文本仅在悬停时显示，活泼的颜色

#### 3.2.2 气泡皮肤

- **经典气泡**：圆形气泡布局，文本和emoji并存，色彩丰富
- **表情气泡**：只显示emoji的气泡布局，简洁直观

#### 3.2.3 卡片皮肤

- **经典卡片**：矩形卡片布局，文本和emoji并存，整齐有序
- **极简卡片**：简约风格的卡片布局，只显示文本

#### 3.2.4 高级皮肤

- **霓虹轮盘**：霓虹灯效果，发光边缘，暗色背景
- **水晶轮盘**：半透明效果，光泽和反射，精致的边框
- **游戏风格**：3D效果，动态背景，选择时有特效

### 3.3 自定义皮肤

用户可以创建自定义皮肤，包括：

- 自定义颜色和样式
- 自定义动画和特效
- 自定义布局和大小

## 4. 显示模式

### 4.1 文本模式 (text)

- 仅显示情绪文本
- 适合注重内容的用户
- 支持多语言和长文本

### 4.2 表情模式 (emoji)

- 仅显示情绪表情
- 适合视觉导向的用户
- 在悬停时显示文本提示

### 4.3 文本+表情模式 (textEmoji)

- 同时显示文本和表情
- 提供最完整的信息
- 适合大多数用户

## 5. 渲染引擎

### 5.1 D3.js (D3)

- 基于SVG的数据可视化库
- 适合复杂的交互和动画
- 支持平滑的过渡效果

### 5.2 SVG (SVG)

- 原生SVG实现
- 轻量级，性能好
- 适合简单的视图

### 5.3 React Three Fiber (R3F)

- 基于Three.js的React渲染器
- 支持3D效果和WebGL
- 适合高级视觉效果

## 6. 视图类型

### 6.1 轮盘视图 (wheel)

- 圆形布局，扇区分布
- 适合展示层级关系
- 支持多种轮盘变体

### 6.2 卡片视图 (card)

- 网格布局，矩形卡片
- 适合系统化展示
- 支持详细信息

### 6.3 气泡视图 (bubble)

- 圆形气泡，自由分布
- 适合视觉化展示
- 支持动态布局

### 6.4 列表视图 (list)

- 垂直列表，线性排列
- 适合快速浏览
- 支持详细信息

### 6.5 网格视图 (grid)

- 均匀网格，规则排列
- 适合大量数据
- 支持缩略图

## 7. 实现细节

### 7.1 策略模式

使用策略模式分离内容渲染逻辑，使得同一视图类型可以支持不同的显示模式。

### 7.2 工厂模式

使用工厂模式创建不同类型的视图组件，隐藏实现细节。

### 7.3 适配器模式

使用适配器模式将情绪数据与显示系统连接起来，实现数据与展示的分离。

### 7.4 上下文模式

使用React上下文在整个应用中共享显示设置，避免prop drilling。

## 8. 未来扩展

### 8.1 新的视图类型

- 时间线视图：按时间顺序展示情绪变化
- 热图视图：使用热图展示情绪强度
- 3D视图：使用3D空间展示情绪关系

### 8.2 新的渲染引擎

- Canvas：使用Canvas 2D API渲染
- WebGPU：使用WebGPU渲染高性能图形
- CSS：纯CSS实现的轻量级渲染

### 8.3 新的交互方式

- 手势控制：支持滑动、捏合等手势
- 语音控制：支持语音命令
- AR/VR：支持增强现实和虚拟现实

## 9. 结论

情绪轮盘皮肤系统通过将数据与展示分离，提供了一个灵活、可扩展的框架，使得用户可以根据自己的喜好选择不同的显示方式，而不会影响底层数据结构。这种设计使得系统可以无限扩展不同类型的皮肤，同时保持一致的用户体验。
