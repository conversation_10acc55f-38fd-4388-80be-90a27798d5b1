# Emoji映射管理示例

## 概述

本文档展示如何在新架构中管理不同Quiz包的emoji映射，实现同一用户在不同Quiz中使用不同emoji展现的需求。

## 架构设计

### 3层Emoji映射机制

```
Layer 1: 系统默认映射 (代码中定义)
    ↓
Layer 2: 用户全局映射 (user_presentation_configs)
    ↓  
Layer 3: Quiz包特定映射 (pack_presentation_overrides)
```

**优先级**: Pack Override > User Preference > System Default

## 使用示例

### 1. 基础使用 - 获取选项展现

```typescript
import { EmojiMappingService } from '../services/entities/EmojiMappingService';

const emojiService = new EmojiMappingService();

// 获取用户在特定Quiz包中某个选项的展现
const presentation = await emojiService.getOptionPresentation(
  'user_001',           // 用户ID
  'daily-mood-tracker', // Quiz包ID
  'happy'              // 选项值
);

console.log(presentation);
// 输出: {
//   emoji: "😊",
//   color: "#4CAF50", 
//   animation: "bounce",
//   source: "user"
// }
```

### 2. 场景1: 用户全局emoji偏好

```typescript
// 用户喜欢使用更可爱的emoji
await emojiService.updateUserEmojiMapping(
  'user_001',
  'happy',
  { 
    primary: "🥰", 
    alternatives: ["😊", "😌", "🙂", "😇"] 
  },
  "#FFB6C1", // 粉色
  "glow"     // 发光动画
);

// 现在用户在所有Quiz包中看到的"happy"都是🥰
```

### 3. 场景2: Quiz包特定映射

```typescript
// 在"工作压力评估"Quiz中，用户希望使用更专业的emoji
await emojiService.updatePackEmojiOverride(
  'user_001',
  'work-stress-assessment',
  'happy',
  { 
    primary: "✅", 
    alternatives: ["👍", "💼", "📈", "🎯"] 
  },
  "#2E8B57", // 深绿色
  "check"    // 勾选动画
);

// 现在用户在工作压力评估中看到的"happy"是✅
// 但在其他Quiz中仍然是🥰
```

### 4. 场景3: 不同Quiz包的不同主题

```typescript
// 儿童情绪Quiz - 使用动物emoji
await emojiService.updatePackEmojiOverride('user_001', 'kids-emotion-quiz', 'happy', {
  primary: "🐶", alternatives: ["🐱", "🐰", "🐼", "🦄"]
});

// 节日情绪Quiz - 使用庆祝emoji  
await emojiService.updatePackEmojiOverride('user_001', 'holiday-mood-quiz', 'happy', {
  primary: "🎉", alternatives: ["🥳", "🎊", "🎈", "🌟"]
});

// 冥想情绪Quiz - 使用宁静emoji
await emojiService.updatePackEmojiOverride('user_001', 'meditation-quiz', 'happy', {
  primary: "🧘", alternatives: ["☮️", "🕯️", "🌸", "🍃"]
});
```

### 5. 场景4: 获取可用emoji选择

```typescript
// 为用户提供emoji选择器
const availableEmojis = await emojiService.getAvailableEmojis(
  'user_001',
  'daily-mood-tracker', 
  'happy'
);

console.log(availableEmojis);
// 输出: ["🥰", "😊", "😌", "🙂", "😇", "😄", "😃"]
```

## 数据结构示例

### 用户全局配置 (user_presentation_configs)

```json
{
  "layer4_view_detail": {
    "emotion_presentation": {
      "emoji_mapping": {
        "happy": {
          "primary": "🥰",
          "alternatives": ["😊", "😌", "🙂", "😇"]
        },
        "sad": {
          "primary": "😔", 
          "alternatives": ["😢", "😞", "☹️"]
        }
      },
      "color_mapping": {
        "happy": "#FFB6C1",
        "sad": "#87CEEB"
      },
      "animation_mapping": {
        "happy": "glow",
        "sad": "fade"
      }
    }
  }
}
```

### Quiz包特定覆盖 (pack_presentation_overrides)

```json
{
  "layer4_view_detail": {
    "emotion_presentation": {
      "emoji_mapping": {
        "happy": {
          "primary": "🎉",
          "alternatives": ["🥳", "🎊", "🎈", "🌟"]
        }
      },
      "color_mapping": {
        "happy": "#FFD700"
      },
      "animation_mapping": {
        "happy": "celebration"
      }
    }
  }
}
```

## 组件集成示例

### React组件中使用

```tsx
import React, { useEffect, useState } from 'react';
import { EmojiMappingService } from '../services/entities/EmojiMappingService';

interface QuizOptionProps {
  userId: string;
  packId: string;
  option: QuizQuestionOption;
  onSelect: (optionId: string) => void;
}

export const QuizOption: React.FC<QuizOptionProps> = ({ 
  userId, packId, option, onSelect 
}) => {
  const [presentation, setPresentation] = useState({
    emoji: '📝',
    color: '#9E9E9E',
    animation: 'none'
  });

  useEffect(() => {
    const loadPresentation = async () => {
      const emojiService = new EmojiMappingService();
      const result = await emojiService.getOptionPresentation(
        userId, 
        packId, 
        option.option_value
      );
      setPresentation(result);
    };

    loadPresentation();
  }, [userId, packId, option.option_value]);

  return (
    <button
      className={`quiz-option ${presentation.animation}`}
      style={{ 
        backgroundColor: presentation.color,
        '--animation': presentation.animation 
      }}
      onClick={() => onSelect(option.id)}
    >
      <span className="emoji">{presentation.emoji}</span>
      <span className="text">{option.option_text}</span>
    </button>
  );
};
```

### Emoji选择器组件

```tsx
interface EmojiSelectorProps {
  userId: string;
  packId: string;
  optionValue: string;
  onEmojiChange: (emoji: string) => void;
}

export const EmojiSelector: React.FC<EmojiSelectorProps> = ({
  userId, packId, optionValue, onEmojiChange
}) => {
  const [availableEmojis, setAvailableEmojis] = useState<string[]>([]);
  const [currentEmoji, setCurrentEmoji] = useState('📝');

  useEffect(() => {
    const loadEmojis = async () => {
      const emojiService = new EmojiMappingService();
      
      // 获取当前emoji
      const presentation = await emojiService.getOptionPresentation(
        userId, packId, optionValue
      );
      setCurrentEmoji(presentation.emoji);
      
      // 获取可选emoji
      const emojis = await emojiService.getAvailableEmojis(
        userId, packId, optionValue
      );
      setAvailableEmojis(emojis);
    };

    loadEmojis();
  }, [userId, packId, optionValue]);

  const handleEmojiSelect = async (emoji: string) => {
    const emojiService = new EmojiMappingService();
    
    // 更新用户配置
    await emojiService.updateUserEmojiMapping(
      userId,
      optionValue,
      { primary: emoji, alternatives: [] }
    );
    
    setCurrentEmoji(emoji);
    onEmojiChange(emoji);
  };

  return (
    <div className="emoji-selector">
      <div className="current-emoji">{currentEmoji}</div>
      <div className="emoji-grid">
        {availableEmojis.map(emoji => (
          <button
            key={emoji}
            className={`emoji-option ${emoji === currentEmoji ? 'selected' : ''}`}
            onClick={() => handleEmojiSelect(emoji)}
          >
            {emoji}
          </button>
        ))}
      </div>
    </div>
  );
};
```

## 最佳实践

### 1. 性能优化
- 缓存emoji映射结果
- 批量加载多个选项的展现信息
- 使用React.memo优化组件渲染

### 2. 用户体验
- 提供emoji预览功能
- 支持emoji搜索和分类
- 提供重置为默认的选项

### 3. 数据一致性
- 定期清理无效的映射配置
- 验证emoji的有效性
- 提供配置迁移机制

### 4. 扩展性
- 支持自定义emoji集合
- 支持动画效果配置
- 支持主题化emoji映射

这个架构完全解决了不同Quiz包使用不同emoji映射的需求，同时保持了数据与展现分离的核心原则。
