/**
 * 支付系统 tRPC 路由
 * 支持 Stripe 支付和订阅管理
 */

import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../router';
import { TRPCError } from '@trpc/server';
import { PaymentService } from '../services/PaymentService';

// ==================== 输入验证Schema ====================

const CreateVipSubscriptionInputSchema = z.object({
  userId: z.string(),
  planId: z.string(),
  paymentMethodId: z.string(),
  customerEmail: z.string().email().optional(),
});

const ProcessVipPurchaseInputSchema = z.object({
  userId: z.string(),
  planId: z.string(),
  paymentMethodId: z.string(),
});

const ProcessSkinPurchaseInputSchema = z.object({
  userId: z.string(),
  skinId: z.string(),
  paymentMethodId: z.string(),
});

const ProcessEmojiSetPurchaseInputSchema = z.object({
  userId: z.string(),
  emojiSetId: z.string(),
  paymentMethodId: z.string(),
});

const ConfirmPaymentIntentInputSchema = z.object({
  paymentIntentId: z.string(),
});

const HandleWebhookInputSchema = z.object({
  payload: z.string(),
  signature: z.string(),
});

// ==================== 支付路由定义 ====================

export const paymentRouter = router({
  // 获取可用的VIP计划
  getVipPlans: publicProcedure
    .query(async ({ ctx }) => {
      try {
        const paymentService = PaymentService.getInstance();
        const result = await paymentService.getVipPlans();

        if (!result.success) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: result.error || 'Failed to get VIP plans',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch VIP plans',
          cause: error,
        });
      }
    }),

  // 获取 Stripe 公钥
  getStripePublishableKey: publicProcedure
    .query(async ({ ctx }) => {
      try {
        const paymentService = PaymentService.getInstance();
        const publishableKey = paymentService.getPublishableKey();

        return {
          success: true,
          publishableKey
        };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get Stripe publishable key',
          cause: error,
        });
      }
    }),

  // 创建VIP订阅
  createVipSubscription: protectedProcedure
    .input(CreateVipSubscriptionInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const paymentService = PaymentService.getInstance();
        const result = await paymentService.createVipSubscription(
          input.userId,
          input.planId,
          input.paymentMethodId,
          input.customerEmail
        );

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to create VIP subscription',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create VIP subscription',
          cause: error,
        });
      }
    }),

  // 处理VIP购买（一次性支付）
  processVipPurchase: protectedProcedure
    .input(ProcessVipPurchaseInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const paymentService = PaymentService.getInstance();
        const result = await paymentService.processVipPurchase(
          input.userId,
          input.planId,
          input.paymentMethodId
        );

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to process VIP purchase',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process VIP purchase',
          cause: error,
        });
      }
    }),

  // 处理皮肤购买
  processSkinPurchase: protectedProcedure
    .input(ProcessSkinPurchaseInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const paymentService = PaymentService.getInstance();
        const result = await paymentService.processSkinPurchase(
          input.userId,
          input.skinId,
          input.paymentMethodId
        );

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to process skin purchase',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process skin purchase',
          cause: error,
        });
      }
    }),

  // 处理表情集购买
  processEmojiSetPurchase: protectedProcedure
    .input(ProcessEmojiSetPurchaseInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const paymentService = PaymentService.getInstance();
        const result = await paymentService.processEmojiSetPurchase(
          input.userId,
          input.emojiSetId,
          input.paymentMethodId
        );

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to process emoji set purchase',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process emoji set purchase',
          cause: error,
        });
      }
    }),

  // 确认支付意图（用于需要额外认证的支付）
  confirmPaymentIntent: protectedProcedure
    .input(ConfirmPaymentIntentInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const paymentService = PaymentService.getInstance();
        const result = await paymentService.confirmPaymentIntent(input.paymentIntentId);

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to confirm payment intent',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to confirm payment intent',
          cause: error,
        });
      }
    }),

  // 处理 Stripe Webhook（通常由 Stripe 直接调用，不通过 tRPC）
  handleWebhook: publicProcedure
    .input(HandleWebhookInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const paymentService = PaymentService.getInstance();
        const result = await paymentService.handleWebhook(input.payload, input.signature);

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to handle webhook',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to handle webhook',
          cause: error,
        });
      }
    }),
});
