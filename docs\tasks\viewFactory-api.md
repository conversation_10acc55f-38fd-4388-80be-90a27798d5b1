# ViewFactory API 文档

本文档详细描述了 ViewFactory 的 API，包括方法、参数和返回值。

## 1. 创建视图

### 1.1 createViewFromUserConfig

根据用户配置创建视图，是创建视图的首选方法。

```typescript
static createViewFromUserConfig(
  userConfig: UserConfig,
  skinManager: SkinManager,
  viewType?: ViewType
): View
```

**参数：**
- `userConfig: UserConfig` - 用户配置，包含视图类型、内容显示模式、皮肤等信息
- `skinManager: SkinManager` - 皮肤管理器，用于获取皮肤配置
- `viewType?: ViewType` - 视图类型（可选，默认使用用户首选视图类型）

**返回值：**
- `View` - 视图对象

**示例：**
```typescript
const view = ViewFactory.createViewFromUserConfig(userConfig, skinManager);
```

### 1.2 createView

传统方法，保留向后兼容性。

```typescript
static createView(
  viewType: ViewType,
  contentDisplayMode: ContentDisplayMode,
  skinConfig: SkinConfig
): View
```

**参数：**
- `viewType: ViewType` - 视图类型
- `contentDisplayMode: ContentDisplayMode` - 内容显示模式
- `skinConfig: SkinConfig` - 皮肤配置

**返回值：**
- `View` - 视图对象

**示例：**
```typescript
const view = ViewFactory.createView('wheel', 'textEmoji', skinConfig);
```

### 1.3 createWheel

创建轮盘视图。

```typescript
static createWheel(
  implementation: RenderEngine,
  contentDisplayMode: ContentDisplayMode,
  skinConfig: SkinConfig
): View
```

**参数：**
- `implementation: RenderEngine` - 轮盘实现类型（D3、SVG、R3F、Canvas、CSS、WebGL、WebGPU）
- `contentDisplayMode: ContentDisplayMode` - 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
- `skinConfig: SkinConfig` - 皮肤配置

**返回值：**
- `View` - 视图对象

**示例：**
```typescript
const view = ViewFactory.createWheel('D3', 'textEmoji', skinConfig);
```

### 1.4 createCard

创建卡片视图。

```typescript
static createCard(
  contentDisplayMode: ContentDisplayMode,
  skinConfig: SkinConfig,
  layout: CardLayout = 'grid'
): View
```

**参数：**
- `contentDisplayMode: ContentDisplayMode` - 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
- `skinConfig: SkinConfig` - 皮肤配置
- `layout: CardLayout` - 卡片布局（grid、list、masonry）

**返回值：**
- `View` - 视图对象

**示例：**
```typescript
const view = ViewFactory.createCard('textEmoji', skinConfig, 'grid');
```

### 1.5 createBubble

创建气泡视图。

```typescript
static createBubble(
  contentDisplayMode: ContentDisplayMode,
  skinConfig: SkinConfig,
  layout: BubbleLayout = 'cluster'
): View
```

**参数：**
- `contentDisplayMode: ContentDisplayMode` - 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
- `skinConfig: SkinConfig` - 皮肤配置
- `layout: BubbleLayout` - 气泡布局（cluster、force、random、circle）

**返回值：**
- `View` - 视图对象

**示例：**
```typescript
const view = ViewFactory.createBubble('textEmoji', skinConfig, 'cluster');
```

### 1.6 createGalaxy

创建星系视图。

```typescript
static createGalaxy(
  contentDisplayMode: ContentDisplayMode,
  skinConfig: SkinConfig,
  layout: GalaxyLayout = 'spiral'
): View
```

**参数：**
- `contentDisplayMode: ContentDisplayMode` - 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
- `skinConfig: SkinConfig` - 皮肤配置
- `layout: GalaxyLayout` - 星系布局（spiral、orbital、nebula）

**返回值：**
- `View` - 视图对象

**示例：**
```typescript
const view = ViewFactory.createGalaxy('textEmoji', skinConfig, 'spiral');
```

## 2. 皮肤兼容性检查

### 2.1 isSupportedViewType

检查皮肤是否支持指定的视图类型。

```typescript
static isSupportedViewType(
  viewType: ViewType,
  skinConfig: SkinConfig
): boolean
```

**参数：**
- `viewType: ViewType` - 视图类型
- `skinConfig: SkinConfig` - 皮肤配置

**返回值：**
- `boolean` - 是否支持

**示例：**
```typescript
const isSupported = ViewFactory.isSupportedViewType('wheel', skinConfig);
```

### 2.2 getFallbackViewType

获取皮肤支持的回退视图类型。

```typescript
static getFallbackViewType(
  skinConfig: SkinConfig
): ViewType
```

**参数：**
- `skinConfig: SkinConfig` - 皮肤配置

**返回值：**
- `ViewType` - 回退视图类型

**示例：**
```typescript
const fallbackType = ViewFactory.getFallbackViewType(skinConfig);
```

### 2.3 isSupportedContentMode

检查皮肤是否支持指定的内容显示模式。

```typescript
static isSupportedContentMode(
  contentDisplayMode: ContentDisplayMode,
  viewType: ViewType,
  skinConfig: SkinConfig
): boolean
```

**参数：**
- `contentDisplayMode: ContentDisplayMode` - 内容显示模式
- `viewType: ViewType` - 视图类型
- `skinConfig: SkinConfig` - 皮肤配置

**返回值：**
- `boolean` - 是否支持

**示例：**
```typescript
const isSupported = ViewFactory.isSupportedContentMode('emoji', 'wheel', skinConfig);
```

### 2.4 getFallbackContentMode

获取皮肤支持的回退内容显示模式。

```typescript
static getFallbackContentMode(
  viewType: ViewType,
  skinConfig: SkinConfig
): ContentDisplayMode
```

**参数：**
- `viewType: ViewType` - 视图类型
- `skinConfig: SkinConfig` - 皮肤配置

**返回值：**
- `ContentDisplayMode` - 回退内容显示模式

**示例：**
```typescript
const fallbackMode = ViewFactory.getFallbackContentMode('wheel', skinConfig);
```

### 2.5 isSupportedRenderEngine

检查皮肤是否支持指定的渲染引擎。

```typescript
static isSupportedRenderEngine(
  renderEngine: RenderEngine,
  skinConfig: SkinConfig
): boolean
```

**参数：**
- `renderEngine: RenderEngine` - 渲染引擎
- `skinConfig: SkinConfig` - 皮肤配置

**返回值：**
- `boolean` - 是否支持

**示例：**
```typescript
const isSupported = ViewFactory.isSupportedRenderEngine('R3F', skinConfig);
```

### 2.6 getFallbackRenderEngine

获取皮肤支持的回退渲染引擎。

```typescript
static getFallbackRenderEngine(
  skinConfig: SkinConfig
): RenderEngine
```

**参数：**
- `skinConfig: SkinConfig` - 皮肤配置

**返回值：**
- `RenderEngine` - 回退渲染引擎

**示例：**
```typescript
const fallbackEngine = ViewFactory.getFallbackRenderEngine(skinConfig);
```

## 3. 视图对象

### 3.1 View 接口

视图对象的接口。

```typescript
interface View {
  render: (
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ) => React.ReactNode;
}
```

**方法：**
- `render` - 渲染视图
  - `emotions: Emotion[]` - 情绪列表
  - `tierLevel: number` - 层级
  - `onSelect: (emotion: Emotion) => void` - 选择回调
  - `config?: ViewConfig` - 视图配置（可选）
  - 返回 `React.ReactNode` - React 节点

**示例：**
```typescript
const renderedView = view.render(emotions, tierLevel, onSelect, {
  onBack: () => {
    // 返回上一级
    setTierLevel(tierLevel - 1);
  },
  selectedPath: 'Path > To > Emotion'
});
```

### 3.2 ViewConfig 接口

视图配置的接口。

```typescript
interface ViewConfig {
  onBack?: () => void;
  selectedPath?: any;
  [key: string]: any;
}
```

**属性：**
- `onBack?: () => void` - 返回回调（可选）
- `selectedPath?: any` - 选中路径（可选）
- `[key: string]: any` - 其他配置（可选）

**示例：**
```typescript
const config: ViewConfig = {
  onBack: () => {
    // 返回上一级
    setTierLevel(tierLevel - 1);
  },
  selectedPath: 'Path > To > Emotion'
};
```
