/**
 * 架构验证测试
 * 验证修复后的服务架构是否正确工作
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuizSessionService } from '../QuizSessionService';
import { QuizSessionRepository } from '../QuizSessionRepository';

// Mock SQLite DB
const mockDb = {
  query: vi.fn(),
  run: vi.fn(),
  execute: vi.fn(),
} as any;

describe('Architecture Validation', () => {
  describe('Type Safety', () => {
    it('should have correct generic type parameters', () => {
      // 这个测试主要验证TypeScript编译时的类型安全
      const repository = new QuizSessionRepository(mockDb);
      const service = new QuizSessionService(mockDb);

      // 如果类型参数不正确，这些代码将无法编译
      expect(repository).toBeInstanceOf(QuizSessionRepository);
      expect(service).toBeInstanceOf(QuizSessionService);
    });

    it('should accept correct input types', async () => {
      const service = new QuizSessionService(mockDb);
      
      // Mock repository methods
      const mockRepository = (service as any).repository;
      mockRepository.create = vi.fn().mockResolvedValue({
        id: 'session_123',
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'INITIATED',
        current_question_index: 0,
        total_questions: 0,
        answered_questions: 0,
        completion_percentage: 0,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      });

      // 这应该编译通过并正常工作
      const result = await service.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        session_type: 'standard',
      });

      expect(result.success).toBe(true);
    });
  });

  describe('Architecture Separation', () => {
    it('should separate Repository and Service concerns', () => {
      const repository = new QuizSessionRepository(mockDb);
      const service = new QuizSessionService(mockDb);

      // Repository应该只有数据访问方法
      expect(typeof repository.create).toBe('function');
      expect(typeof repository.findById).toBe('function');
      expect(typeof repository.update).toBe('function');
      expect(typeof repository.delete).toBe('function');
      expect(typeof repository.findByUserId).toBe('function');

      // Service应该有业务逻辑方法
      expect(typeof service.createSession).toBe('function');
      expect(typeof service.startSession).toBe('function');
      expect(typeof service.completeSession).toBe('function');
      expect(typeof service.updateProgress).toBe('function');
      expect(typeof service.getUserQuizStats).toBe('function');

      // Service应该继承EventEmitter
      expect(service.on).toBeDefined();
      expect(service.emit).toBeDefined();
    });

    it('should have Repository return raw data and Service return ServiceResult', async () => {
      const repository = new QuizSessionRepository(mockDb);
      
      // Mock database response
      mockDb.query.mockResolvedValue({
        values: [{
          id: 'session_123',
          pack_id: 'pack_emotion_wheel',
          user_id: 'user_123',
          status: 'INITIATED',
          current_question_index: 0,
          total_questions: 0,
          answered_questions: 0,
          completion_percentage: 0,
          start_time: new Date().toISOString(),
          last_active_time: new Date().toISOString(),
          session_type: 'standard',
          session_metadata: '{}',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }]
      });

      // Repository方法应该返回原始数据
      const sessions = await repository.findByUserId('user_123');
      expect(Array.isArray(sessions)).toBe(true);
      expect(sessions[0]).toHaveProperty('id');
      expect(sessions[0]).toHaveProperty('pack_id');

      // Service方法应该返回ServiceResult
      const service = new QuizSessionService(mockDb);
      const result = await service.getUserSessions('user_123');
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('data');
    });
  });

  describe('Error Handling', () => {
    it('should handle Repository errors in Service layer', async () => {
      const service = new QuizSessionService(mockDb);
      
      // Mock repository to throw error
      const mockRepository = (service as any).repository;
      mockRepository.create = vi.fn().mockRejectedValue(new Error('Database error'));

      const result = await service.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to create quiz session');
    });

    it('should validate input in Service layer', async () => {
      const service = new QuizSessionService(mockDb);

      // Test missing pack_id
      const result1 = await service.createSession({
        pack_id: '',
        user_id: 'user_123',
      });

      expect(result1.success).toBe(false);
      expect(result1.error).toContain('Pack ID is required');

      // Test missing user_id
      const result2 = await service.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: '',
      });

      expect(result2.success).toBe(false);
      expect(result2.error).toContain('User ID is required');
    });
  });

  describe('Event System', () => {
    it('should emit events for business operations', async () => {
      const service = new QuizSessionService(mockDb);
      
      // Mock repository
      const mockRepository = (service as any).repository;
      mockRepository.create = vi.fn().mockResolvedValue({
        id: 'session_123',
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'INITIATED',
        current_question_index: 0,
        total_questions: 0,
        answered_questions: 0,
        completion_percentage: 0,
        start_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      });

      const eventSpy = vi.fn();
      service.on('sessionCreated', eventSpy);

      await service.createSession({
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
      });

      expect(eventSpy).toHaveBeenCalled();
    });
  });

  describe('Business Logic', () => {
    it('should implement correct business logic in Service layer', async () => {
      const service = new QuizSessionService(mockDb);
      
      // Mock repository
      const mockRepository = (service as any).repository;
      mockRepository.update = vi.fn().mockResolvedValue({
        id: 'session_123',
        pack_id: 'pack_emotion_wheel',
        user_id: 'user_123',
        status: 'COMPLETED',
        current_question_index: 5,
        total_questions: 5,
        answered_questions: 5,
        completion_percentage: 100,
        start_time: new Date(),
        end_time: new Date(),
        last_active_time: new Date(),
        session_type: 'standard',
        session_metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
      });

      // Test auto-completion logic
      const result = await service.updateProgress('session_123', 5, 5);

      expect(result.success).toBe(true);
      expect(mockRepository.update).toHaveBeenCalledWith('session_123', expect.objectContaining({
        current_question_index: 5,
        completion_percentage: 100,
        total_questions: 5,
        status: 'COMPLETED', // Should auto-complete
        end_time: expect.any(String),
      }));
    });

    it('should calculate statistics correctly', async () => {
      const service = new QuizSessionService(mockDb);
      
      // Mock repository
      const mockRepository = (service as any).repository;
      mockRepository.findByUserId = vi.fn().mockResolvedValue([
        {
          id: 'session_1',
          pack_id: 'pack_emotion_wheel',
          user_id: 'user_123',
          status: 'COMPLETED',
          start_time: new Date('2024-01-01T10:00:00Z'),
          end_time: new Date('2024-01-01T10:05:00Z'), // 5 minutes
        },
        {
          id: 'session_2',
          pack_id: 'pack_emotion_wheel',
          user_id: 'user_123',
          status: 'COMPLETED',
          start_time: new Date('2024-01-02T10:00:00Z'),
          end_time: new Date('2024-01-02T10:03:00Z'), // 3 minutes
        },
        {
          id: 'session_3',
          pack_id: 'pack_tcm_assessment',
          user_id: 'user_123',
          status: 'IN_PROGRESS',
        },
      ]);

      const result = await service.getUserQuizStats('user_123');

      expect(result.success).toBe(true);
      expect(result.data?.total_sessions).toBe(3);
      expect(result.data?.completed_sessions).toBe(2);
      expect(result.data?.completion_rate).toBe(67); // 2/3 * 100 rounded
      expect(result.data?.average_completion_time_minutes).toBe(4); // (300 + 180) / 2 seconds = 240 seconds = 4 minutes
    });
  });
});

// 编译时类型检查测试
describe('Compile-time Type Safety', () => {
  it('should enforce correct types at compile time', () => {
    // 这些测试主要是为了确保TypeScript编译器能够正确检查类型
    
    // 正确的类型应该编译通过
    const repository = new QuizSessionRepository(mockDb);
    const service = new QuizSessionService(mockDb);

    // 这些调用应该有正确的类型推断
    expect(typeof repository.findByUserId).toBe('function');
    expect(typeof service.createSession).toBe('function');
    
    // 如果我们传递错误的类型，TypeScript应该在编译时报错
    // 但在测试中我们无法直接测试编译错误，只能确保正确的类型能工作
  });
});
