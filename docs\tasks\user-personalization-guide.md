# 用户个性化配置指南

## 🎯 概述

本文档详细描述了用户个性化配置系统的架构、用户旅程和实现细节。该系统通过6层配置架构，为不同类型的用户提供从基础到专业级的个性化体验。该系统与情绪数据集作为量表的新架构设计完美整合，实现了配置驱动的个性化体验。

> **相关文档**:
> - 详细的架构设计请参考 `docs/emotion-dataset-as-quiz-architecture.md`
> - ViewFactory整合设计请参考 `docs/viewFactory-design.md`

## 📊 配置层次架构

### 6层配置架构

```
用户个性化配置 (6层架构)

第0层：Quiz包数据集选择 (Quiz Pack Data Set Selection)
│   ├── active_quiz_pack_id (当前选择的Quiz包ID)
│   ├── Quiz包结构说明：
│   │   ├── 每个Quiz包 = 一个完整的量表 (如"日常情绪追踪"、"中医体质评估"、"心理健康筛查")
│   │   ├── 每个问题 = 量表中的一个问题项 (如"主要情绪类别"、"情绪强度"、"脏腑功能")
│   │   ├── 每个选项 = 问题的一个答案选项 (如"快乐"、"悲伤"、"愤怒")
│   │   └── 支持多种问题类型 (单选、多选、量表评分、情绪轮盘、文本输入等)
│   ├── Quiz包分类：
│   │   ├── category: 'daily', 'assessment', 'therapy', 'research', 'entertainment', 'education'
│   │   ├── quiz_type: 'emotion_wheel', 'traditional_scale', 'personality_test', 'iq_test', 'knowledge_quiz', 'survey', 'game_quiz', 'mixed'
│   │   ├── difficulty_level: 'beginner', 'regular', 'advanced', 'expert'
│   │   └── quiz_style: 'mainstream', 'alternative', 'experimental', 'cultural_specific'
│   ├── 配置应用策略：
│   │   ├── 全局配置：Quiz包所有问题应用统一的后续配置
│   │   ├── 问题配置：单独问题使用独立的配置
│   │   ├── 选项配置：特定选项使用自定义配置
│   │   └── 混合配置：组合使用上述策略
│   └── 配置示例：
│       ├── "daily-mood-tracker": 日常情绪追踪，emotion_wheel类型，regular难度
│       ├── "tcm-constitution-assessment": 中医体质评估，traditional_scale类型，advanced难度
│       ├── "personality-big-five": 大五人格测试，personality_test类型，expert难度
│       └── "mental-health-screening": 心理健康筛查，survey类型，regular难度


├── 第1层：基础选择层 (User Choice Layer)
│   ├── preferred_view_type (视图类型选择)
│   ├── active_skin_id (皮肤主题选择)
│   ├── dark_mode (深色/浅色模式)
│   └── color_mode (颜色模式：warm/cool/mixed/auto)
├── 第2层：渲染策略层 (Rendering Strategy Layer)
│   ├── render_engine_preferences (渲染引擎偏好)
│   │   ├── wheel: "D3", "SVG", "Canvas", "R3F" (情绪轮盘渲染引擎)
│   │   ├── card: "CSS", "SVG", "Canvas" (卡片视图渲染引擎)
│   │   ├── bubble: "Canvas", "R3F", "WebGL" (气泡视图渲染引擎)
│   │   └── galaxy: "R3F", "WebGL", "Three.js" (星系视图渲染引擎)
│   ├── content_display_mode_preferences (内容显示模式偏好)
│   │   ├── wheel: "text", "emoji", "textEmoji", "icon" (轮盘内容显示)
│   │   ├── card: "text", "emoji", "image", "mixed" (卡片内容显示)
│   │   └── bubble: "emoji", "color", "size", "mixed" (气泡内容显示)
│   ├── layout_preferences (布局偏好)
│   │   ├── wheel: "circular_balanced", "circular_weighted", "spiral", "custom"
│   │   ├── card: "grid_responsive", "masonry", "list", "carousel"
│   │   └── bubble: "organic_flow", "force_directed", "clustered", "random"
│   └── performance_mode (性能模式)
│       ├── "low": 基础渲染，减少动画和效果
│       ├── "balanced": 平衡性能和视觉效果
│       └── "high": 最佳视觉效果，高性能要求
├── 第3层：皮肤基础配置层 (Skin Base Configuration Layer)
│   ├── colors (颜色配置)
│   │   ├── primary (主色调): 主要UI元素颜色
│   │   ├── secondary (次色调): 辅助UI元素颜色
│   │   ├── background (背景色): 页面背景颜色
│   │   ├── surface (表面色): 卡片、面板背景色
│   │   ├── text (文本色): 主要文本颜色
│   │   └── accent (强调色): 高亮、按钮等强调元素
│   ├── fonts (字体配置)
│   │   ├── primary_font (主字体): 界面主要字体族
│   │   ├── size_scale (字体缩放): 字体大小缩放比例 (0.8-1.5)
│   │   ├── weight_normal (常规字重): 正文字体粗细
│   │   └── weight_bold (粗体字重): 标题字体粗细
│   ├── animations (动画配置)
│   │   ├── enable_animations (启用动画): 是否启用动画效果
│   │   ├── animation_speed (动画速度): "slow", "normal", "fast"
│   │   └── reduce_motion (减少动画): 无障碍模式减少动画
│   └── effects (视觉效果配置)
│       ├── shadows (阴影效果): 是否启用阴影
│       ├── blur_effects (模糊效果): 背景模糊等效果
│       ├── gradients (渐变效果): 渐变背景和边框
│       └── transparency (透明度): UI元素透明度设置
├── 第4层：视图细节配置层 (View Detail Configuration Layer)
│   ├── wheel_config (轮盘视图配置)
│   │   ├── container_size (容器大小): 200-800px
│   │   ├── wheel_radius (轮盘半径): 80-400px
│   │   ├── inner_radius (内圆半径): 20-120px
│   │   ├── sector_gap (扇区间隙): 0-10px
│   │   ├── emotion_display_mode (情绪显示模式): "hierarchical", "clustered", "radial", "spiral"
│   │   └── interaction_config (交互配置): 拖拽、缩放、旋转设置
│   ├── card_config (卡片视图配置)
│   │   ├── card_size (卡片尺寸): "small", "medium", "large"
│   │   ├── grid_columns (网格列数): 2-6列
│   │   ├── card_spacing (卡片间距): 4-24px
│   │   ├── border_radius (圆角半径): 0-16px
│   │   └── hover_effects (悬停效果): "lift", "scale", "glow", "none"
│   ├── bubble_config (气泡视图配置)
│   │   ├── bubble_size_range (气泡大小范围): 最小-最大直径
│   │   ├── animation_speed (动画速度): 气泡移动和变化速度
│   │   ├── collision_detection (碰撞检测): 是否启用物理碰撞
│   │   └── force_strength (力场强度): 气泡间相互作用力
│   └── emotion_presentation (情绪展现配置)
│       ├── emotion_grouping_style (情绪分组样式): "by_category", "by_intensity", "by_frequency"
│       ├── tier_transition_animation (层级切换动画): "fade", "slide", "zoom", "flip"
│       └── selection_feedback (选择反馈): 选中状态的视觉反馈方式
└── 第5层：可访问性增强层 (Accessibility Enhancement Layer)
    ├── high_contrast (高对比度)
    ├── large_text (大字体)
    ├── reduce_motion (减少动画)
    └── screen_reader_support (屏幕阅读器支持)
```

## 👥 用户类型与配置策略

### 1. 新手用户 (Beginner Users)
**特点**: 刚开始使用，需要简单易懂的界面
**配置策略**:
- 使用默认配置和免费皮肤
- 简化的视图类型 (wheel, card)
- 基础渲染引擎 (D3, CSS)
- 文本+表情显示模式
- 启用辅助功能

**示例配置**:
```json
{
  "preferred_view_type": "wheel",
  "active_skin_id": "default-light",
  "active_emotion_data_id": "basic-emotions",
  "dark_mode": false,
  "color_mode": "warm",
  "render_engine_preferences": {"wheel": "D3", "card": "D3"},
  "content_display_mode_preferences": {"wheel": "textEmoji"},
  "accessibility": {
    "large_text": true,
    "simplified_ui": true,
    "reduce_motion": false
  }
}
```

### 2. 普通用户 (Regular Users)
**特点**: 有一定使用经验，希望个性化但不复杂
**配置策略**:
- 混合使用免费和付费皮肤
- 多种视图类型 (wheel, card, bubble)
- 中等性能渲染引擎 (D3, SVG)
- 根据偏好调整显示模式
- 平衡的可访问性设置

### 3. 高级用户 (Advanced Users)
**特点**: 熟练用户，追求个性化和高级功能
**配置策略**:
- 使用付费皮肤和高级主题
- 所有视图类型可用
- 高性能渲染引擎 (Canvas, R3F)
- 精细的视觉效果配置
- 自定义交互行为

### 4. VIP用户 (VIP Users)
**特点**: 付费用户，享受所有功能和最佳体验
**配置策略**:
- 解锁所有皮肤和功能
- 专属视图类型 (galaxy, 3D effects)
- 最高性能渲染 (R3F, WebGPU)
- 完全自定义配置
- 实验性功能访问

### 5. 无障碍用户 (Accessibility Users)
**特点**: 需要特殊辅助功能支持
**配置策略**:
- 高对比度皮肤
- 简化的视图类型
- 稳定的渲染引擎 (CSS)
- 纯文本显示模式
- 完整的无障碍功能

## 🎨 颜色模式系统 (Color Mode System)

### 颜色模式类型

#### 1. **Warm (暖色模式)**
**特点**: 使用暖色调，营造温馨舒适的氛围
**适用场景**: 放松、冥想、晚间使用
**颜色特征**:
```json
{
  "primary_hue_range": [0, 60],      // 红色到黄色
  "saturation_boost": 1.1,           // 饱和度提升10%
  "temperature_shift": "+200K",      // 色温偏暖
  "emotion_mapping": {
    "joy": "#FFD700",                // 金黄色
    "love": "#FF6B6B",               // 暖红色
    "excitement": "#FF8C42",         // 橙色
    "comfort": "#F4A261"             // 暖橙色
  }
}
```

#### 2. **Cool (冷色模式)**
**特点**: 使用冷色调，营造清爽专业的氛围
**适用场景**: 工作、学习、白天使用
**颜色特征**:
```json
{
  "primary_hue_range": [180, 270],   // 青色到紫色
  "saturation_boost": 0.9,           // 饱和度降低10%
  "temperature_shift": "-200K",      // 色温偏冷
  "emotion_mapping": {
    "calm": "#4ECDC4",               // 青绿色
    "focus": "#45B7D1",              // 蓝色
    "peace": "#96CEB4",              // 薄荷绿
    "clarity": "#A8E6CF"             // 浅绿色
  }
}
```

#### 3. **Mixed (混合模式)**
**特点**: 平衡使用暖冷色调，适应不同情绪
**适用场景**: 全天候使用，情绪记录
**颜色特征**:
```json
{
  "primary_hue_range": [0, 360],     // 全色谱
  "saturation_boost": 1.0,           // 标准饱和度
  "temperature_shift": "0K",         // 中性色温
  "emotion_mapping": {
    "balanced": "根据情绪类型动态选择",
    "adaptive": "根据时间和使用场景调整",
    "contextual": "根据内容和用户状态变化"
  }
}
```

#### 4. **Auto (自动模式)**
**特点**: 根据时间、环境、用户行为自动调整
**适用场景**: 智能适配，无需手动调整
**自动调整规则**:
```json
{
  "time_based": {
    "morning": "cool",               // 6:00-12:00 冷色调
    "afternoon": "mixed",            // 12:00-18:00 混合色调
    "evening": "warm",               // 18:00-22:00 暖色调
    "night": "warm_dimmed"           // 22:00-6:00 暖色调+降低亮度
  },
  "context_based": {
    "work_hours": "cool",            // 工作时间偏冷色
    "relaxation": "warm",            // 放松时间偏暖色
    "exercise": "energetic",         // 运动时使用活力色彩
    "meditation": "calm"             // 冥想时使用平静色彩
  },
  "emotion_based": {
    "positive_emotions": "warm_bright",
    "negative_emotions": "cool_soft",
    "neutral_emotions": "balanced"
  }
}
```

### 颜色模式与皮肤的交互

#### 皮肤适配机制
```javascript
const colorModeAdaptation = {
  // 皮肤基础色彩 + 颜色模式调整 = 最终显示色彩
  adaptSkinToColorMode: (skinColors, colorMode) => {
    switch(colorMode) {
      case 'warm':
        return {
          ...skinColors,
          primary: adjustHue(skinColors.primary, 'warmer'),
          accent: increaseSaturation(skinColors.accent, 0.1),
          background: addWarmth(skinColors.background)
        };

      case 'cool':
        return {
          ...skinColors,
          primary: adjustHue(skinColors.primary, 'cooler'),
          accent: decreaseSaturation(skinColors.accent, 0.1),
          background: addCoolness(skinColors.background)
        };

      case 'mixed':
        return skinColors; // 保持原始色彩

      case 'auto':
        return autoAdjustColors(skinColors, getCurrentContext());
    }
  }
};
```

### 颜色模式在不同视图中的应用

#### Wheel 视图中的颜色模式
```javascript
const wheelColorMode = {
  warm: {
    sector_colors: "使用暖色调渐变",
    hover_effect: "暖色发光效果",
    selection_highlight: "金色高亮",
    background_gradient: "暖色径向渐变"
  },
  cool: {
    sector_colors: "使用冷色调渐变",
    hover_effect: "冷色发光效果",
    selection_highlight: "蓝色高亮",
    background_gradient: "冷色径向渐变"
  }
};
```

#### Card 视图中的颜色模式
```javascript
const cardColorMode = {
  warm: {
    card_background: "暖色背景",
    border_color: "暖色边框",
    text_color: "深暖色文字",
    shadow_color: "暖色阴影"
  },
  cool: {
    card_background: "冷色背景",
    border_color: "冷色边框",
    text_color: "深冷色文字",
    shadow_color: "冷色阴影"
  }
};
```

## 🚀 用户旅程详解

### 以 Wheel 视图为例的完整用户旅程

#### T0-T1: 用户选择阶段
```javascript
// 用户操作：点击轮盘视图
const userChoice = {
  viewType: "wheel",
  skinId: "ocean-blue",
  emotionDataSet: "advanced-emotions"
};

// 系统响应：读取用户配置
const userConfig = await getUserConfig(userId);
const displayOptions = {
  viewType: userConfig.preferred_view_type,
  skinId: userConfig.active_skin_id,
  emotionDataId: userConfig.active_emotion_data_id,
  darkMode: userConfig.dark_mode,
  colorMode: userConfig.color_mode
};
```

#### T2-T3: 配置加载与验证阶段
```javascript
// 加载皮肤配置
const skinConfig = await loadSkinConfig(displayOptions.skinId);
const wheelConfig = skinConfig.view_configs.wheel;

// 应用颜色模式调整
const adaptedColors = adaptSkinToColorMode(
  skinConfig.colors,
  displayOptions.colorMode,
  displayOptions.darkMode
);

// 验证兼容性
const isCompatible = validateViewSkinCompatibility(
  displayOptions.viewType,
  displayOptions.skinId
);

// 合并用户偏好
const renderConfig = {
  engine: userConfig.render_engine_preferences.wheel,
  layout: userConfig.layout_preferences.wheel,
  content: userConfig.content_display_mode_preferences.wheel,
  colors: adaptedColors,
  darkMode: displayOptions.darkMode,
  colorMode: displayOptions.colorMode,
  ...wheelConfig
};
```

#### T4-T5: 渲染引擎初始化
```javascript
// 根据用户偏好选择渲染引擎
const engineType = renderConfig.engine; // "R3F"

// 初始化渲染器
const wheelRenderer = new R3FWheelRenderer({
  // 第4层：视图细节配置
  containerSize: wheelConfig.container_size,
  wheelRadius: wheelConfig.wheel_radius,
  innerRadius: wheelConfig.inner_radius,

  // 3D配置 (VIP功能)
  use3DEffects: wheelConfig.use_3d_effects,
  perspective: wheelConfig.perspective,
  depth: wheelConfig.depth,

  // 颜色模式配置
  darkMode: renderConfig.darkMode,
  colorMode: renderConfig.colorMode,

  // 材质配置 (应用颜色模式调整后的颜色)
  materials: createMaterials(renderConfig.colors, wheelConfig, renderConfig.colorMode)
});
```

#### T6-T7: 视觉效果应用
```javascript
// 应用用户自定义的视觉效果
if (wheelConfig.shadow_enabled) {
  wheelRenderer.addShadow({
    color: wheelConfig.shadow_color,
    blur: wheelConfig.shadow_blur,
    offset: [wheelConfig.shadow_offset_x, wheelConfig.shadow_offset_y]
  });
}

// 应用装饰效果 (高级用户功能)
if (wheelConfig.decorations && userConfig.isAdvancedUser) {
  wheelRenderer.addDecorations({
    type: wheelConfig.decoration_type,
    density: wheelConfig.decoration_density,
    color: wheelConfig.decoration_color
  });
}
```

#### T8-T9: 交互系统配置
```javascript
// 配置个性化交互行为
wheelRenderer.configureInteraction({
  hover: {
    effect: wheelConfig.hover_effect,
    scale: wheelConfig.hover_scale,
    color: wheelConfig.highlight_color
  },
  selection: {
    animation: wheelConfig.selection_animation,
    indicator: wheelConfig.selection_indicator
  },
  controls: {
    drag: wheelConfig.drag_enabled,
    zoom: wheelConfig.zoom_enabled,
    rotation: wheelConfig.rotation_enabled
  }
});
```

#### T10-T11: 可访问性增强
```javascript
// 第5层：应用可访问性配置
const accessibility = userConfig.accessibility;

if (accessibility.high_contrast) {
  wheelRenderer.enhanceContrast();
}

if (accessibility.large_text) {
  wheelRenderer.scaleFontSize(1.2);
}

if (accessibility.reduce_motion) {
  wheelRenderer.disableAnimations();
}

if (accessibility.screen_reader_support) {
  wheelRenderer.addAriaLabels();
}
```

## 🎨 ViewConfigOptions 组件架构

### Collapsible 配置界面

ViewConfigOptions 组件使用 collapsible 界面来组织复杂的配置选项：

```typescript
interface ViewConfigSection {
  id: string;
  title: string;
  icon: React.ComponentType;
  badge?: string;
  isOpen: boolean;
  controls: ConfigControl[];
}

interface ConfigControl {
  type: 'slider' | 'switch' | 'select' | 'color';
  key: string;
  label: string;
  description?: string;
  value: any;
  options?: any;
  onChange: (value: any) => void;
}
```

### 配置分组

1. **几何配置** (Geometry Configuration)
   - 容器大小、轮盘半径、扇区间隙
   - 适用于所有用户类型

2. **视觉效果** (Visual Effects)
   - 3D效果、阴影、装饰
   - 高级用户和VIP用户可用

3. **交互行为** (Interaction Behavior)
   - 悬停效果、选择动画、控制选项
   - 根据用户类型提供不同选项

4. **响应式配置** (Responsive Configuration)
   - 断点设置、自适应布局
   - 专业用户功能

## 📱 响应式个性化

### 设备适配策略

```javascript
const deviceConfig = {
  mobile: {
    preferredEngine: "CSS",
    simplifiedUI: true,
    reducedAnimations: true,
    largerTouchTargets: true
  },
  tablet: {
    preferredEngine: "SVG",
    balancedUI: true,
    moderateAnimations: true,
    adaptiveLayout: true
  },
  desktop: {
    preferredEngine: "R3F",
    fullUI: true,
    richAnimations: true,
    multiViewSupport: true
  }
};
```

### 性能自适应

```javascript
const performanceConfig = {
  low: {
    renderEngine: "CSS",
    effects: "minimal",
    animations: "reduced"
  },
  medium: {
    renderEngine: "SVG",
    effects: "standard",
    animations: "normal"
  },
  high: {
    renderEngine: "R3F",
    effects: "enhanced",
    animations: "rich"
  }
};
```

## 🔄 配置同步与持久化

### 本地存储策略
- 用户偏好立即保存到本地存储
- 定期同步到服务器
- 离线时使用本地配置

### 云端同步
- 跨设备配置同步
- 版本控制和冲突解决
- 备份和恢复机制

## 🧪 测试数据说明

测试数据包含7种不同的用户配置：

1. **config-user-1**: 新手用户基础配置
2. **config-user-2**: 普通用户平衡配置
3. **config-user-3**: 年轻用户简化配置
4. **config-default-template**: 系统默认模板
5. **config-vip-user**: VIP用户高级配置
6. **config-accessibility-user**: 无障碍用户专用配置
7. **config-advanced-user**: 高级用户个性化配置
8. **config-power-user**: 专业用户极致配置

每种配置展示了不同用户类型的个性化需求和使用模式。

## 🔧 实现细节

### SkinConfigSchema 详细配置

每个皮肤的 `view_configs` 包含针对不同视图类型的详细配置：

```typescript
interface WheelViewConfig {
  // 几何配置
  container_size: number;           // 容器大小 (200-600px)
  wheel_radius: number;             // 轮盘半径 (100-300px)
  inner_radius: number;             // 内圆半径 (20-120px)
  sector_gap: number;               // 扇区间隙 (0-10px)
  sector_padding: number;           // 扇区内边距 (4-16px)
  sector_border_radius: number;     // 扇区圆角 (0-8px)

  // 视觉效果
  use_3d_effects: boolean;          // 启用3D效果
  perspective: number;              // 透视距离 (800-1200px)
  depth: number;                    // 3D深度 (5-50px)
  shadow_enabled: boolean;          // 启用阴影
  shadow_blur: number;              // 阴影模糊度 (0-30px)
  shadow_offset_y: number;          // 阴影Y偏移 (-20-20px)

  // 装饰效果
  decorations: boolean;             // 启用装饰
  decoration_type: string;          // 装饰类型: glow, sparkles, dots, waves
  decoration_density: number;       // 装饰密度 (0.1-1.0)

  // 交互行为
  hover_effect: string;             // 悬停效果: highlight, scale, glow, lift, pulse
  hover_scale: number;              // 悬停缩放 (1.0-1.5)
  selection_animation: string;      // 选择动画: fade, pulse, spin, bounce, flash
  transition_duration: number;      // 过渡时间 (100-1000ms)

  // 控制功能
  drag_enabled: boolean;            // 启用拖拽
  zoom_enabled: boolean;            // 启用缩放
  rotation_enabled: boolean;        // 启用旋转

  // 响应式配置
  responsive_scaling: boolean;      // 响应式缩放
  min_size: number;                 // 最小尺寸
  max_size: number;                 // 最大尺寸
}
```

### 配置优先级系统

```javascript
const configPriority = {
  1: "用户个人配置",           // 最高优先级
  2: "用户类型默认配置",       // 根据用户等级
  3: "设备适配配置",           // 根据设备性能
  4: "皮肤默认配置",           // 皮肤自带配置
  5: "系统默认配置"            // 最低优先级
};

// 配置合并算法
function mergeConfigs(userConfig, skinConfig, deviceConfig, systemConfig) {
  return {
    ...systemConfig,
    ...deviceConfig,
    ...skinConfig,
    ...userConfig.typeDefaults,
    ...userConfig.personal
  };
}
```

### 性能优化策略

```javascript
const performanceOptimization = {
  // 渲染引擎降级策略
  engineFallback: {
    "R3F": ["Canvas", "SVG", "CSS"],
    "Canvas": ["SVG", "CSS"],
    "SVG": ["CSS"],
    "CSS": []
  },

  // 效果简化策略
  effectSimplification: {
    low: {
      shadows: false,
      decorations: false,
      animations: "minimal"
    },
    medium: {
      shadows: true,
      decorations: false,
      animations: "standard"
    },
    high: {
      shadows: true,
      decorations: true,
      animations: "rich"
    }
  },

  // 自动性能检测
  autoDetection: {
    fps_threshold: 30,
    memory_threshold: "512MB",
    cpu_threshold: "medium"
  }
};
```

## 🎯 用户体验优化

### 渐进式个性化

1. **第一次使用**: 提供简单的预设选择
2. **熟悉阶段**: 逐步开放更多配置选项
3. **高级使用**: 提供完全自定义能力
4. **专家模式**: 开放实验性功能

### 智能推荐系统

```javascript
const recommendationEngine = {
  // 基于使用行为推荐
  behaviorBased: {
    viewTypeUsage: "推荐常用视图类型",
    skinPreference: "推荐相似风格皮肤",
    featureUsage: "推荐相关功能"
  },

  // 基于用户类型推荐
  typeBased: {
    beginner: "推荐简化配置",
    advanced: "推荐高级功能",
    accessibility: "推荐无障碍选项"
  },

  // 基于设备推荐
  deviceBased: {
    mobile: "推荐触屏优化配置",
    desktop: "推荐桌面增强功能",
    tablet: "推荐平板适配配置"
  }
};
```

### 配置预设管理

```javascript
const presetManager = {
  // 系统预设
  systemPresets: [
    "minimal", "standard", "advanced", "accessibility"
  ],

  // 用户自定义预设
  userPresets: [
    "work_mode", "gaming_mode", "presentation_mode"
  ],

  // 社区分享预设
  communityPresets: [
    "popular_configs", "trending_themes", "expert_setups"
  ]
};
```

## 📊 数据分析与优化

### 用户行为追踪

```javascript
const analyticsEvents = {
  configChange: {
    event: "config_changed",
    properties: ["config_type", "old_value", "new_value", "user_type"]
  },

  performanceIssue: {
    event: "performance_issue",
    properties: ["fps", "memory_usage", "render_engine", "config_complexity"]
  },

  featureUsage: {
    event: "feature_used",
    properties: ["feature_name", "frequency", "user_satisfaction"]
  }
};
```

### A/B 测试框架

```javascript
const abTestFramework = {
  tests: {
    "default_skin_selection": {
      variants: ["light_first", "dark_first", "auto_detect"],
      metrics: ["engagement", "customization_rate", "retention"]
    },

    "config_ui_complexity": {
      variants: ["simple", "intermediate", "advanced"],
      metrics: ["completion_rate", "user_satisfaction", "support_requests"]
    }
  }
};
```

## 🔒 安全与隐私

### 配置数据保护

1. **本地加密**: 敏感配置数据本地加密存储
2. **传输安全**: HTTPS + 数据签名验证
3. **访问控制**: 基于用户权限的配置访问
4. **审计日志**: 配置更改的完整审计轨迹

### 隐私保护

```javascript
const privacyProtection = {
  dataMinimization: "只收集必要的配置数据",
  anonymization: "用户行为数据匿名化处理",
  consentManagement: "明确的数据使用同意机制",
  dataRetention: "配置数据保留期限管理"
};
```

## 🚀 未来发展方向

### AI 驱动的个性化

1. **智能配置生成**: 基于用户行为自动生成最优配置
2. **情境感知**: 根据使用场景自动调整配置
3. **预测性优化**: 预测用户需求并提前优化
4. **自然语言配置**: 通过对话方式进行配置

### 高级功能扩展

1. **WebGPU 支持**: 下一代图形渲染技术
2. **VR/AR 适配**: 虚拟现实和增强现实支持
3. **多模态交互**: 语音、手势、眼动控制
4. **协作配置**: 团队共享和协作配置管理

这个个性化配置系统为用户提供了从基础到专业级的完整定制体验，确保每个用户都能获得最适合其需求和偏好的界面配置。

## 📊 数据库映射 (Database Mapping)

### 新架构表结构对应关系

#### 1. Quiz包表 (quiz_packs) - 第0层数据源
```sql
CREATE TABLE quiz_packs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    version TEXT DEFAULT '1.0.0',

    -- Quiz包分类和类型
    category TEXT NOT NULL, -- 'daily', 'assessment', 'therapy', 'research', 'entertainment', 'education'
    quiz_type TEXT NOT NULL, -- 'emotion_wheel', 'traditional_scale', 'personality_test', 'iq_test', 'knowledge_quiz', 'survey', 'game_quiz', 'mixed'
    difficulty_level TEXT DEFAULT 'regular', -- 'beginner', 'regular', 'advanced', 'expert'
    quiz_style TEXT, -- 'mainstream', 'alternative', 'experimental', 'cultural_specific'

    -- Quiz逻辑配置 (JSON)
    quiz_logic_config TEXT NOT NULL,

    -- 元数据 (JSON)
    metadata TEXT NOT NULL,

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 用户展现配置表 (user_presentation_configs) - 6层个性化配置
```sql
CREATE TABLE user_presentation_configs (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    config_name TEXT DEFAULT 'default',

    -- 6层展现配置 (JSON: UserPresentationConfig)
    presentation_config TEXT NOT NULL,

    -- 配置元数据
    config_version TEXT DEFAULT '2.0',
    personalization_level INTEGER DEFAULT 0, -- 0-100

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);
```

#### 3. 会话展现配置快照表 (quiz_session_presentation_configs)
```sql
CREATE TABLE quiz_session_presentation_configs (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    user_id TEXT NOT NULL,

    -- 基础配置快照
    config_name TEXT NOT NULL,
    config_version TEXT DEFAULT '2.0',

    -- 完整的展现配置快照 (从user_presentation_configs复制)
    presentation_config TEXT NOT NULL, -- JSON: 完整的6层个性化配置

    -- 会话特定配置
    session_overrides TEXT, -- JSON: 会话特定的配置覆盖

    -- 配置元数据
    personalization_level INTEGER DEFAULT 50, -- 个性化程度 0-100
    config_source TEXT DEFAULT 'user_preference', -- 'user_preference', 'system_default', 'session_custom'

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 4. 皮肤配置表 (skin_configs) - 第3层皮肤基础
```sql
CREATE TABLE skin_configs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,

    -- 皮肤配置 (JSON)
    config TEXT NOT NULL,

    -- 皮肤元数据
    version TEXT DEFAULT '1.0.0',
    is_premium BOOLEAN DEFAULT 0,
    supported_view_types TEXT, -- JSON array

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6层配置JSON结构映射

#### presentation_config JSON结构
```typescript
interface UserPresentationConfig {
  // 第0层：Quiz包数据集选择
  layer0_quiz_pack_selection: {
    active_quiz_pack_id: string;
    quiz_pack_preferences: {
      preferred_categories: string[];
      preferred_quiz_types: string[];
      difficulty_preference: string;
      style_preference: string;
    };
  };

  // 第1层：基础选择层
  layer1_user_choice: {
    preferred_view_type: string; // 'wheel', 'card', 'bubble', 'galaxy'
    active_skin_id: string;
    dark_mode: boolean;
    color_mode: string; // 'warm', 'cool', 'mixed', 'auto'
  };

  // 第2层：渲染策略层
  layer2_rendering_strategy: {
    render_engine_preferences: {
      wheel: string; // 'D3', 'SVG', 'Canvas', 'R3F'
      card: string;  // 'CSS', 'SVG', 'Canvas'
      bubble: string; // 'Canvas', 'R3F', 'WebGL'
      galaxy: string; // 'R3F', 'WebGL', 'Three.js'
    };
    content_display_mode_preferences: {
      wheel: string; // 'text', 'emoji', 'textEmoji', 'icon'
      card: string;  // 'text', 'emoji', 'image', 'mixed'
      bubble: string; // 'emoji', 'color', 'size', 'mixed'
    };
    layout_preferences: {
      wheel: string; // 'circular_balanced', 'circular_weighted', 'spiral', 'custom'
      card: string;  // 'grid_responsive', 'masonry', 'list', 'carousel'
      bubble: string; // 'organic_flow', 'force_directed', 'clustered', 'random'
    };
    performance_mode: string; // 'low', 'balanced', 'high'
  };

  // 第3层：皮肤基础配置层 (通过active_skin_id关联skin_configs表)
  layer3_skin_base: {
    skin_overrides: {
      colors?: Partial<SkinColors>;
      fonts?: Partial<SkinFonts>;
      animations?: Partial<SkinAnimations>;
      effects?: Partial<SkinEffects>;
    };
  };

  // 第4层：视图细节配置层
  layer4_view_details: {
    wheel_config: WheelViewConfig;
    card_config: CardViewConfig;
    bubble_config: BubbleViewConfig;
    emotion_presentation: EmotionPresentationConfig;
  };

  // 第5层：可访问性增强层
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    screen_reader_support: boolean;
    keyboard_navigation: boolean;
    focus_indicators: boolean;
    color_blind_support: boolean;
    simplified_ui: boolean;
  };
}
```

### 配置字段与数据库的对应关系

| 6层配置字段 | 数据库表 | 字段名 | 数据类型 | 说明 |
|------------|---------|--------|----------|------|
| active_quiz_pack_id | user_presentation_configs | presentation_config.layer0_quiz_pack_selection.active_quiz_pack_id | JSON | 当前选择的Quiz包ID |
| preferred_view_type | user_presentation_configs | presentation_config.layer1_user_choice.preferred_view_type | JSON | 用户偏好的视图类型 |
| active_skin_id | user_presentation_configs | presentation_config.layer1_user_choice.active_skin_id | JSON | 当前选择的皮肤ID |
| render_engine_preferences | user_presentation_configs | presentation_config.layer2_rendering_strategy.render_engine_preferences | JSON | 渲染引擎偏好设置 |
| wheel_config | user_presentation_configs | presentation_config.layer4_view_details.wheel_config | JSON | 轮盘视图详细配置 |
| accessibility | user_presentation_configs | presentation_config.layer5_accessibility | JSON | 可访问性配置 |

### 数据迁移策略

#### 从旧架构(user_configs)到新架构的迁移
```sql
-- 迁移脚本示例
INSERT INTO user_presentation_configs (
    id, user_id, config_name, presentation_config, config_version, is_active
)
SELECT
    id,
    user_id,
    name,
    json_object(
        'layer0_quiz_pack_selection', json_object(
            'active_quiz_pack_id', COALESCE(active_emotion_data_id, 'default-quiz-pack')
        ),
        'layer1_user_choice', json_object(
            'preferred_view_type', preferred_view_type,
            'active_skin_id', active_skin_id,
            'dark_mode', dark_mode,
            'color_mode', COALESCE(color_mode, 'warm')
        ),
        'layer2_rendering_strategy', json_object(
            'render_engine_preferences', render_engine_preferences,
            'content_display_mode_preferences', content_display_mode_preferences,
            'layout_preferences', layout_preferences,
            'performance_mode', 'balanced'
        ),
        'layer3_skin_base', json_object(
            'skin_overrides', json_object()
        ),
        'layer4_view_details', json_object(
            'wheel_config', COALESCE(view_detail_configs, '{}'),
            'card_config', '{}',
            'bubble_config', '{}',
            'emotion_presentation', '{}'
        ),
        'layer5_accessibility', COALESCE(accessibility, '{}')
    ),
    '2.0',
    is_active
FROM user_configs
WHERE user_id IS NOT NULL;
```
