#!/usr/bin/env node

/**
 * 批量修复 D3WheelComponent 中的字段命名脚本
 * 将所有驼峰命名的字段改为下划线命名
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 字段映射：驼峰 -> 下划线
const fieldMappings = {
  'backgroundColor': 'background_color',
  'sectorGap': 'sector_gap',
  'transitionDuration': 'transition_duration',
  'hoverEffect': 'hover_effect',
  'selectionAnimation': 'selection_animation',
  'textColor': 'text_color',
  'fontFamily': 'font_family',
  'fontSize': 'font_size',
  'emojiSize': 'emoji_size',
  'shadowColor': 'shadow_color',
  'shadowEnabled': 'shadow_enabled',
  'shadowBlur': 'shadow_blur',
  'shadowOffsetX': 'shadow_offset_x',
  'shadowOffsetY': 'shadow_offset_y',
  'use3DEffects': 'use_3d_effects',
  'rotateX': 'rotate_x',
  'rotateY': 'rotate_y',
  'dragEnabled': 'drag_enabled',
  'zoomEnabled': 'zoom_enabled',
  'zoomMaxScale': 'zoom_max_scale',
  'zoomMinScale': 'zoom_min_scale',
  'rotationEnabled': 'rotation_enabled',
};

/**
 * 修复 D3WheelComponent 文件
 */
function fixD3WheelComponent() {
  const projectRoot = path.join(path.dirname(__dirname));
  const filePath = path.join(projectRoot, 'src/views/components/wheels/D3WheelComponent.tsx');
  
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    let modifiedContent = content;
    let changeCount = 0;

    console.log(`🔧 修复 D3WheelComponent 字段命名...`);

    // 修复字段访问
    for (const [camelField, snakeField] of Object.entries(fieldMappings)) {
      const fieldRegex = new RegExp(`wheelConfig\\.${camelField}\\b`, 'g');
      const matches = modifiedContent.match(fieldRegex);
      if (matches) {
        modifiedContent = modifiedContent.replace(fieldRegex, `wheelConfig.${snakeField}`);
        changeCount += matches.length;
        console.log(`  ✓ 修复 ${matches.length} 个 ${camelField} -> ${snakeField}`);
      }
    }

    // 如果有修改，写回文件
    if (changeCount > 0) {
      fs.writeFileSync(filePath, modifiedContent, 'utf8');
      console.log(`✅ D3WheelComponent: 修复了 ${changeCount} 个字段命名问题`);
      return true;
    } else {
      console.log(`✅ D3WheelComponent: 无需修复`);
      return false;
    }

  } catch (error) {
    console.error(`❌ 修复 ${filePath} 时出错:`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始修复 D3WheelComponent 字段命名...\n');

  const success = fixD3WheelComponent();

  if (success) {
    console.log('\n✅ D3WheelComponent 字段命名修复完成！');
    console.log('💡 建议运行 TypeScript 检查确保没有引入新的错误:');
    console.log('   npx tsc --noEmit');
  } else {
    console.log('\n✅ D3WheelComponent 字段命名已是最新状态！');
  }
}

main();
