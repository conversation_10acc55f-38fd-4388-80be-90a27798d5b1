/**
 * 全面测试脚本
 * 测试所有配色体系（暖色系、冷色系、混合色系和游戏风格）的三个层级（primary、secondary、tertiary）
 */

// 模拟颜色工具函数
function parseRgb(color) {
  // 处理十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.substring(1);
    const r = Number.parseInt(hex.substring(0, 2), 16) / 255;
    const g = Number.parseInt(hex.substring(2, 4), 16) / 255;
    const b = Number.parseInt(hex.substring(4, 6), 16) / 255;
    return { r, g, b };
  }

  // 处理 rgb 格式
  const rgbMatch = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
  if (rgbMatch) {
    return {
      r: Number.parseInt(rgbMatch[1], 10) / 255,
      g: Number.parseInt(rgbMatch[2], 10) / 255,
      b: Number.parseInt(rgbMatch[3], 10) / 255,
    };
  }

  // 默认返回黑色
  return { r: 0, g: 0, b: 0 };
}

// 计算对比度
function calculateContrastRatio(color1, color2) {
  const rgb1 = parseRgb(color1);
  const rgb2 = parseRgb(color2);

  // 计算相对亮度
  const getLuminance = (rgb) => {
    const sRGB = {
      r: rgb.r,
      g: rgb.g,
      b: rgb.b,
    };

    // 转换为线性RGB
    const linearRGB = {
      r: sRGB.r <= 0.03928 ? sRGB.r / 12.92 : ((sRGB.r + 0.055) / 1.055) ** 2.4,
      g: sRGB.g <= 0.03928 ? sRGB.g / 12.92 : ((sRGB.g + 0.055) / 1.055) ** 2.4,
      b: sRGB.b <= 0.03928 ? sRGB.b / 12.92 : ((sRGB.b + 0.055) / 1.055) ** 2.4,
    };

    return 0.2126 * linearRGB.r + 0.7152 * linearRGB.g + 0.0722 * linearRGB.b;
  };

  const l1 = getLuminance(rgb1);
  const l2 = getLuminance(rgb2);

  // 计算对比度比率
  return (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
}

// 计算颜色相似度
function calculateColorSimilarity(color1, color2) {
  const rgb1 = parseRgb(color1);
  const rgb2 = parseRgb(color2);

  // 计算RGB空间中的欧几里得距离
  const rDiff = rgb1.r - rgb2.r;
  const gDiff = rgb1.g - rgb2.g;
  const bDiff = rgb1.b - rgb2.b;

  // 归一化距离值到0-1范围
  return Math.sqrt(rDiff * rDiff + gDiff * gDiff + bDiff * bDiff) / Math.sqrt(3);
}

// 测试颜色对
const testColors = {
  // 原始问题颜色对
  original: {
    neutral: '#CCCCCC',
    happy: '#CDCDCD',
  },

  // 暖色系
  warm: {
    light: {
      primary: {
        neutral: '#E6B8AF', // 浅粉色
        happy: '#F9CB9C', // 浅橙色
      },
      secondary: {
        neutral: '#D5A6BD', // 浅紫粉
        happy: '#FFD966', // 浅黄色
      },
      tertiary: {
        neutral: '#C27BA0', // 深粉色
        happy: '#F1C232', // 深黄色
      },
    },
    dark: {
      primary: {
        neutral: '#A64D79', // 深紫红
        happy: '#E69138', // 深橙色
      },
      secondary: {
        neutral: '#85200C', // 深红色
        happy: '#B45F06', // 棕色
      },
      tertiary: {
        neutral: '#990000', // 深红色
        happy: '#CC9900', // 金色
      },
    },
  },

  // 冷色系
  cool: {
    light: {
      primary: {
        neutral: '#CFE2F3', // 浅蓝色
        happy: '#D9EAD3', // 浅绿色
      },
      secondary: {
        neutral: '#9FC5E8', // 中蓝色
        happy: '#B6D7A8', // 中绿色
      },
      tertiary: {
        neutral: '#6FA8DC', // 深蓝色
        happy: '#93C47D', // 深绿色
      },
    },
    dark: {
      primary: {
        neutral: '#0B5394', // 深蓝色
        happy: '#38761D', // 深绿色
      },
      secondary: {
        neutral: '#073763', // 深海蓝
        happy: '#274E13', // 深森绿
      },
      tertiary: {
        neutral: '#20124D', // 深紫色
        happy: '#0C343D', // 深青色
      },
    },
  },

  // 混合色系
  mixed: {
    light: {
      primary: {
        neutral: '#D5A6BD', // 浅紫粉
        happy: '#B6D7A8', // 浅绿色
      },
      secondary: {
        neutral: '#9FC5E8', // 浅蓝色
        happy: '#F9CB9C', // 浅橙色
      },
      tertiary: {
        neutral: '#C27BA0', // 深粉色
        happy: '#93C47D', // 深绿色
      },
    },
    dark: {
      primary: {
        neutral: '#A64D79', // 深紫红
        happy: '#38761D', // 深绿色
      },
      secondary: {
        neutral: '#0B5394', // 深蓝色
        happy: '#E69138', // 深橙色
      },
      tertiary: {
        neutral: '#990000', // 深红色
        happy: '#0C343D', // 深青色
      },
    },
  },

  // 游戏风格
  game: {
    light: {
      primary: {
        neutral: '#9C27B0', // 深紫色 (Neutral)
        happy: '#FFEB3B', // 亮黄色 (Happy)
      },
      secondary: {
        neutral: '#6A1B9A', // 更深紫色 (Neutral 变体)
        happy: '#FFC107', // 琥珀色 (Happy 变体)
      },
      tertiary: {
        neutral: '#8E24AA', // 中紫色 (Neutral 变体)
        happy: '#CDDC39', // 酸橙色 (Happy 变体)
      },
    },
    dark: {
      primary: {
        neutral: '#E040FB', // 亮紫色 (Neutral)
        happy: '#FFFF00', // 亮黄色 (Happy)
      },
      secondary: {
        neutral: '#AA00FF', // 深紫色 (Neutral 变体)
        happy: '#FFD600', // 亮金黄色 (Happy 变体)
      },
      tertiary: {
        neutral: '#D500F9', // 亮紫色 (Neutral 变体)
        happy: '#EEFF41', // 亮酸橙色 (Happy 变体)
      },
    },
  },
};

// 设置对比度阈值
const thresholds = {
  primary: 1.5,
  secondary: 1.6,
  tertiary: 1.8,
  game: 2.0, // 游戏风格的更高要求
};

// 测试函数
function testContrastRatio(color1, color2, name1, name2, tier, colorMode, threshold) {
  const contrastRatio = calculateContrastRatio(color1, color2);
  const similarity = calculateColorSimilarity(color1, color2);

  console.log(`${name1}: ${color1}`);
  console.log(`${name2}: ${color2}`);
  console.log(`对比度: ${contrastRatio.to(3)}`);
  console.log(`相似度: ${similarity.to(3)}`);

  const passed = contrastRatio >= threshold;
  console.log(passed ? `✅ 通过 (高于 ${threshold})` : `❌ 失败 (低于 ${threshold})`);

  return { contrastRatio, passed, colorMode, tier };
}

// 运行全面测试
function runAllColorModesTest() {
  console.log('全面测试所有配色体系的三个层级');
  console.log('===========================================');

  // 测试原始问题颜色对
  console.log('\n原始问题颜色对:');
  const originalResult = testContrastRatio(
    testColors.original.neutral,
    testColors.original.happy,
    'Neutral',
    'Happy',
    'primary',
    'original',
    thresholds.primary
  );

  // 测试所有配色体系、层级和主题
  const colorModes = ['warm', 'cool', 'mixed', 'game'];
  const themes = ['light', 'dark'];
  const tiers = ['primary', 'secondary', 'tertiary'];

  const results = [];

  colorModes.forEach((mode) => {
    themes.forEach((theme) => {
      tiers.forEach((tier) => {
        console.log(`\n${mode}颜色 - ${theme === 'light' ? '浅色' : '深色'}主题 - ${tier}层级:`);

        const neutralColor = testColors[mode][theme][tier].neutral;
        const happyColor = testColors[mode][theme][tier].happy;

        // 使用相应的阈值
        const threshold = mode === 'game' ? thresholds.game : thresholds[tier];

        const result = testContrastRatio(
          neutralColor,
          happyColor,
          'Neutral',
          'Happy',
          tier,
          mode,
          threshold
        );

        results.push(result);
      });
    });
  });

  // 统计结果
  const totalTests = results.length;
  const passedTests = results.filter((r) => r.passed).length;
  const failedTests = totalTests - passedTests;

  // 按配色体系统计
  const modeResults = {};
  colorModes.forEach((mode) => {
    const modeTests = results.filter((r) => r.colorMode === mode);
    const modePassed = modeTests.filter((r) => r.passed).length;
    modeResults[mode] = {
      total: modeTests.length,
      passed: modePassed,
      failed: modeTests.length - modePassed,
      passRate: `${((modePassed / modeTests.length) * 100).to(1)}%`,
    };
  });

  console.log('\n===========================================');
  console.log('测试总结:');
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${failedTests}`);
  console.log(`通过率: ${((passedTests / totalTests) * 100).to(1)}%`);

  console.log('\n各配色体系测试结果:');
  Object.keys(modeResults).forEach((mode) => {
    const result = modeResults[mode];
    console.log(`${mode}颜色: ${result.passed}/${result.total} 通过 (${result.passRate})`);
  });

  console.log('\n原始问题对比度:', originalResult.contrastRatio.to(3));
  console.log('===========================================');
}

// 运行测试
runAllColorModesTest();
