# 数据库安全架构设计

## 📋 **概述**

本文档描述了Mindful Mood应用的数据库安全架构，定义了客户端和服务端的数据访问权限，确保敏感数据的安全性。

## 🔐 **数据分类与权限控制**

### **1. 客户端可读写数据 (Client Read-Write)**
这些数据由客户端创建和管理，可以离线操作，需要同步到服务端。

#### **表列表**
- `mood_entries` - 心情记录
- `emotion_selections` - 情绪选择
- `user_configs` - 用户配置
- `user_preferences` - 用户偏好设置

#### **特征**
- ✅ 客户端可以创建、读取、更新、删除
- ✅ 支持离线操作
- ✅ 需要同步到服务端
- ✅ 包含 `sync_status` 字段管理同步状态

### **2. 客户端只读数据 (Client Read-Only)**
这些数据由服务端管理，客户端只能读取，通过同步获取最新数据。

#### **表列表**
- `users` - 用户基本信息（不含敏感认证数据）
- `user_streaks` - 用户连续记录统计
- `user_skin_unlocks` - 皮肤解锁状态
- `user_emoji_set_unlocks` - 表情集解锁状态

#### **特征**
- ❌ 客户端不能修改
- ✅ 客户端可以读取
- ✅ 数据从服务端同步到客户端
- ✅ `sync_status` 始终为 'synced'

### **3. 服务端专用数据 (Server-Only)**
这些数据包含敏感信息，永远不会同步到客户端。

#### **表列表**
- `user_auth` - 用户认证信息（密码、2FA等）
- `user_sessions` - 用户会话管理
- `payment_transactions` - 支付交易记录
- `user_subscription_history` - 订阅历史
- `user_activity_log` - 用户活动日志
- `admin_actions_log` - 管理员操作日志
- `api_keys` - API密钥管理
- `rate_limit_tracking` - 访问频率限制

#### **特征**
- ❌ 永远不同步到客户端
- ❌ 客户端无法访问
- ✅ 仅服务端可以操作
- ✅ 包含敏感的认证和财务数据

### **4. 共享静态数据 (Shared Static)**
这些数据是应用的基础配置，客户端和服务端都可以读取。

#### **表列表**
- `emotions` - 情绪定义
- `tags` - 标签定义
- `skins` - 皮肤配置
- `emoji_sets` - 表情集配置
- `emotion_data_sets` - 情绪数据集
- 所有翻译表 (`*_translations`)
- `ui_labels` - UI标签

#### **特征**
- ✅ 客户端和服务端都可以读取
- ❌ 客户端通常不能修改（除非有特殊权限）
- ✅ 通过应用更新或管理后台更新

## 🔄 **同步策略**

### **客户端到服务端同步**
```typescript
// 客户端创建的数据需要上传到服务端
const clientToServerSync = {
  tables: ['mood_entries', 'emotion_selections', 'user_configs'],
  direction: 'client → server',
  trigger: 'user_action | periodic | app_start',
  conflict_resolution: 'server_wins' // 服务端数据优先
};
```

### **服务端到客户端同步**
```typescript
// 服务端管理的数据需要下载到客户端
const serverToClientSync = {
  tables: ['users', 'user_streaks', 'user_skin_unlocks', 'user_emoji_set_unlocks'],
  direction: 'server → client',
  trigger: 'login | periodic | manual_refresh',
  access: 'read_only' // 客户端只读
};
```

### **不同步数据**
```typescript
// 敏感数据永远不离开服务端
const serverOnlyData = {
  tables: ['user_auth', 'user_sessions', 'payment_transactions', 'user_activity_log'],
  access: 'server_only',
  sync: 'never'
};
```

## 🛡️ **安全措施**

### **1. 数据隔离**
- 敏感认证数据存储在独立的 `user_auth` 表中
- 财务数据存储在独立的 `payment_transactions` 表中
- 客户端永远无法访问这些敏感表

### **2. 字段级安全**
```sql
-- users表中的敏感字段已注释掉，不会出现在客户端schema中
-- password_hash TEXT NOT NULL, -- Excluded from client schema for security
-- salt TEXT, -- Excluded from client schema for security
```

### **3. 权限控制**
- 客户端API只能访问允许的表和字段
- 服务端API有完整的数据库访问权限
- 使用JWT token进行身份验证和授权

### **4. 数据加密**
- 密码使用bcrypt/argon2进行哈希
- 敏感的session token进行哈希存储
- API密钥进行哈希存储

## 📊 **数据流图**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端离线     │    │   客户端在线     │    │     服务端       │
│   SQLite        │    │   tRPC Client   │    │   PostgreSQL    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 读写                   │ 同步                   │ 完全控制
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ • mood_entries  │◄──►│ • mood_entries  │◄──►│ • mood_entries  │
│ • user_configs  │    │ • users (只读)   │    │ • users (完整)   │
│ • emotions      │    │ • user_streaks  │    │ • user_auth     │
│ • skins         │    │ • unlocks       │    │ • payments      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 **实现细节**

### **客户端数据服务**
```typescript
// 客户端只能操作允许的表
class ClientDataService {
  // 可读写
  async createMoodEntry(entry: MoodEntry) { /* 实现 */ }
  async updateUserConfig(config: UserConfig) { /* 实现 */ }
  
  // 只读
  async getUserProfile(): Promise<User> { /* 从同步数据读取 */ }
  async getUserStreaks(): Promise<UserStreaks> { /* 从同步数据读取 */ }
  
  // 禁止访问
  // async getUserAuth() - 此方法不存在
  // async getPaymentHistory() - 此方法不存在
}
```

### **服务端数据服务**
```typescript
// 服务端有完整的数据访问权限
class ServerDataService {
  // 用户管理
  async createUser(userData: CreateUserRequest) { /* 实现 */ }
  async authenticateUser(credentials: LoginRequest) { /* 实现 */ }
  async updateVipStatus(userId: string, vipData: VipUpdate) { /* 实现 */ }
  
  // 敏感操作
  async createPaymentTransaction(transaction: PaymentData) { /* 实现 */ }
  async logUserActivity(activity: ActivityLog) { /* 实现 */ }
  async manageUserSession(session: SessionData) { /* 实现 */ }
}
```

## ✅ **验证清单**

### **客户端安全**
- [ ] 客户端代码中不包含敏感表的schema定义
- [ ] 客户端API调用不能访问敏感端点
- [ ] 本地存储的数据不包含密码或支付信息
- [ ] 同步过程中敏感数据不会下载到客户端

### **服务端安全**
- [ ] 敏感数据表有适当的访问控制
- [ ] API端点有正确的权限验证
- [ ] 密码和敏感数据正确加密存储
- [ ] 审计日志记录所有敏感操作

### **数据同步安全**
- [ ] 同步过程中只传输允许的数据
- [ ] 传输过程使用HTTPS加密
- [ ] 同步冲突解决策略安全合理
- [ ] 同步失败时有适当的错误处理

这种架构设计确保了用户数据的安全性，同时保持了良好的用户体验和离线功能。
