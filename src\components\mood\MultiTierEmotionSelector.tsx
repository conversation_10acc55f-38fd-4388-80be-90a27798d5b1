/**
 * MultiTierEmotionSelector组件
 * 用于多层级情绪选择
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';
import { type Emotion, type EmotionDataSet, EmotionDataSetTier, type MoodEmotionItem, type MoodEmotionTiers } from '@/types';
import { ChevronLeft, X } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import TierNavigation from './TierNavigation';

// 组件属性接口
interface MultiTierEmotionSelectorProps {
  emotionData: EmotionData;
  onComplete: (selectedEmotions: MoodEmotionTiers) => void;
  onCancel?: () => void;
}

/**
 * 多层级情绪选择器组件
 */
const MultiTierEmotionSelector: React.FC<MultiTierEmotionSelectorProps> = ({
  emotionData,
  onComplete,
  onCancel,
}) => {
  const { t } = useLanguage();

  // 当前层级索引
  const [currentTierIndex, setCurrentTierIndex] = useState(0);

  // 选中的情绪
  const [selectedEmotions, setSelectedEmotions] = useState<MoodEmotionTiers>({});

  // 获取当前层级
  const currentTier = emotionData.tiers[currentTierIndex];

  // 获取下一个层级
  const nextTier = emotionData.tiers[currentTierIndex + 1];

  // 是否是最后一个层级
  const isLastTier = currentTierIndex === emotionData.tiers.length - 1;

  // 处理情绪选择
  const handleEmotionSelect = (emotion: Emotion) => {
    // 创建情绪项
    const emotionItem: MoodEmotionItem = {
      id: emotion.id,
      name: emotion.name,
      tier_id: currentTier.id,
      tier_level: currentTier.level,
      emoji: emotion.emoji,
      color: emotion.color,
    };

    // 更新选中的情绪
    setSelectedEmotions((prev) => ({
      ...prev,
      [currentTier.id]: emotionItem,
    }));

    // 如果不是最后一个层级，进入下一个层级
    if (!isLastTier) {
      setCurrentTierIndex(currentTierIndex + 1);
    }
  };

  // 处理返回上一层级
  const handleBack = () => {
    if (currentTierIndex > 0) {
      setCurrentTierIndex(currentTierIndex - 1);
    }
  };

  // 处理完成选择
  const handleComplete = () => {
    onComplete(selectedEmotions);
  };

  // 处理取消选择
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // 渲染层级标题
  const renderTierTitle = () => {
    return (
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          {currentTierIndex > 0 && (
            <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
              <ChevronLeft className="h-5 w-5" />
            </Button>
          )}
          <h2 className="text-lg font-medium">
            {t(`mood.tier.level_${currentTier.level}`, { defaultValue: currentTier.name })}
          </h2>
        </div>
        <Button variant="ghost" size="icon" onClick={handleCancel}>
          <X className="h-5 w-5" />
        </Button>
      </div>
    );
  };

  // 渲染选中的情绪摘要
  const renderSelectedEmotionsSummary = () => {
    if (Object.keys(selectedEmotions).length === 0) {
      return null;
    }

    return (
      <div className="mb-4 p-3 bg-muted rounded-md">
        <h3 className="text-sm font-medium mb-2">{t('mood.selected_emotions')}</h3>
        <div className="space-y-1">
          {Object.keys(selectedEmotions)
            .sort((a, b) => {
              const tierA = selectedEmotions[a];
              const tierB = selectedEmotions[b];
              return (tierA.tier_level || 0) - (tierB.tier_level || 0);
            })
            .map((tierId) => {
              const emotion = selectedEmotions[tierId];
              const tier = emotionData.tiers.find((t) => t.id === tierId);

              return (
                <div key={tierId} className="flex items-center">
                  <span className="text-sm mr-2">
                    {tier ? t(`mood.tier.level_${tier.level}`, { defaultValue: tier.name }) : ''}:
                  </span>
                  <span className="mr-2">{emotion.emoji}</span>
                  <span className="text-sm font-medium">{emotion.name}</span>
                </div>
              );
            })}
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle>{t('mood.select_emotion')}</CardTitle>
      </CardHeader>
      <CardContent>
        {renderTierTitle()}
        {renderSelectedEmotionsSummary()}

        <TierNavigation
          emotions={currentTier.emotions}
          tier={currentTier.level}
          onSelect={handleEmotionSelect}
          onBack={currentTierIndex > 0 ? handleBack : undefined}
          selectedPath={selectedEmotions}
          emotionDataId={emotionData.id}
        />

        {isLastTier && (
          <Button
            className="w-full mt-4"
            onClick={handleComplete}
            disabled={Object.keys(selectedEmotions).length < emotionData.tiers.length}
          >
            {t('mood.continue')}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default MultiTierEmotionSelector;
