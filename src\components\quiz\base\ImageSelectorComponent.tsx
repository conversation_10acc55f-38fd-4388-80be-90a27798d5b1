/**
 * Quiz图片选择器组件
 * 支持多种中医文化样式的图片选择器组件
 */

import React, { useState } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { ImageSelectorComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type ImageSelectorComponentConfig = z.infer<typeof ImageSelectorComponentConfigSchema>;

export interface ImageSelectorOption {
  id: string;
  url: string;
  alt_text: Record<string, string>;
  title?: Record<string, string>;
  description?: Record<string, string>;
  value: string | number;
}

export interface ImageSelectorComponentProps extends BaseQuizComponentProps<ImageSelectorComponentConfig> {
  images: ImageSelectorOption[];
  selectedValues: (string | number)[];
  onSelectionChange: (values: (string | number)[]) => void;
  disabled?: boolean;
}

interface ImageSelectorComponentState extends ComponentState {
  selected_values: (string | number)[];
  loading_images: Set<string>;
  error_images: Set<string>;
  hover_image: string | null;
}

/**
 * 图片选择器组件类
 */
export class ImageSelectorComponent extends BaseQuizComponent<
  ImageSelectorComponentConfig,
  ImageSelectorComponentProps,
  ImageSelectorComponentState
> {
  private containerRef = React.createRef<HTMLDivElement>();

  extractConfig(props: ImageSelectorComponentProps): ImageSelectorComponentConfig {
    return props.config;
  }

  getInitialState(): ImageSelectorComponentState {
    return {
      is_loading: false,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: this.props.selectedValues.map(String),
      animation_state: 'idle',
      selected_values: this.props.selectedValues,
      loading_images: new Set(),
      error_images: new Set(),
      hover_image: null
    };
  }

  componentDidUpdate(prevProps: ImageSelectorComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.selectedValues !== this.props.selectedValues) {
      this.setState({ 
        selected_values: this.props.selectedValues,
        selected_items: this.props.selectedValues.map(String)
      });
    }
    
    if (prevProps.disabled !== this.props.disabled) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled
      });
    }
  }

  /**
   * 处理图片选择
   */
  private handleImageSelect = (image: ImageSelectorOption): void => {
    if (this.state.is_disabled) return;

    const { mode, max_selections } = this.config.selection;
    const currentValues = [...this.state.selected_values];
    const imageValue = image.value;
    const isSelected = currentValues.includes(imageValue);

    let newValues: (string | number)[];

    if (mode === 'single') {
      // 单选模式
      newValues = isSelected ? [] : [imageValue];
    } else {
      // 多选模式
      if (isSelected) {
        // 取消选择
        newValues = currentValues.filter(v => v !== imageValue);
      } else {
        // 添加选择
        if (max_selections && currentValues.length >= max_selections) {
          // 达到最大选择数量，替换第一个
          newValues = [...currentValues.slice(1), imageValue];
        } else {
          newValues = [...currentValues, imageValue];
        }
      }
    }

    this.setState({ 
      selected_values: newValues,
      selected_items: newValues.map(String)
    });

    this.props.onSelectionChange(newValues);

    // 触发触觉反馈
    this.triggerHapticFeedback('light');

    // 发送交互事件
    this.emitInteractionEvent('select', {
      image_id: image.id,
      image_value: imageValue,
      selected_values: newValues,
      selection_mode: mode
    });
  };

  /**
   * 处理图片加载开始
   */
  private handleImageLoadStart = (imageId: string): void => {
    this.setState(prevState => ({
      loading_images: new Set([...prevState.loading_images, imageId])
    }));
  };

  /**
   * 处理图片加载成功
   */
  private handleImageLoad = (imageId: string): void => {
    this.setState(prevState => {
      const newLoadingImages = new Set(prevState.loading_images);
      newLoadingImages.delete(imageId);
      return { loading_images: newLoadingImages };
    });
  };

  /**
   * 处理图片加载失败
   */
  private handleImageError = (imageId: string): void => {
    this.setState(prevState => {
      const newLoadingImages = new Set(prevState.loading_images);
      const newErrorImages = new Set([...prevState.error_images, imageId]);
      newLoadingImages.delete(imageId);
      return { 
        loading_images: newLoadingImages,
        error_images: newErrorImages
      };
    });
  };

  /**
   * 处理图片悬停
   */
  private handleImageHover = (imageId: string | null): void => {
    if (this.state.is_disabled) return;
    
    this.setState({ hover_image: imageId });

    if (imageId) {
      // 触发悬停效果
      this.triggerHoverEffect(imageId);
    }
  };

  /**
   * 触发悬停效果
   */
  private triggerHoverEffect(imageId: string): void {
    if (this.personalization.layer5_accessibility?.reduce_motion) {
      return;
    }

    const hoverEffect = this.config.style.hover_effect;
    const imageElement = this.containerRef.current?.querySelector(`[data-image-id="${imageId}"]`);
    
    if (imageElement) {
      imageElement.classList.add(`quiz-image-selector-hover-${hoverEffect}`);
      
      setTimeout(() => {
        imageElement.classList.remove(`quiz-image-selector-hover-${hoverEffect}`);
      }, 300);
    }
  }

  /**
   * 获取图片文本
   */
  private getImageText(image: ImageSelectorOption, field: 'alt_text' | 'title' | 'description'): string {
    const { language } = this.context || { language: 'zh' };
    const textObj = image[field];
    
    if (!textObj) return '';
    
    return textObj[language] || textObj['zh'] || textObj['en'] || '';
  }

  /**
   * 获取选择指示器样式类名
   */
  private getSelectionIndicatorClassName(): string {
    const indicator = this.config.style.selection_indicator;
    return `quiz-image-selector-indicator-${indicator}`;
  }

  /**
   * 获取网格样式
   */
  private getGridStyles(): React.CSSProperties {
    const { columns, gap } = this.config.style;
    
    return {
      display: 'grid',
      gridTemplateColumns: `repeat(${columns}, 1fr)`,
      gap: `${gap}px`
    };
  }

  /**
   * 渲染图片项
   */
  private renderImageItem = (image: ImageSelectorOption): React.ReactNode => {
    const isSelected = this.state.selected_values.includes(image.value);
    const isLoading = this.state.loading_images.has(image.id);
    const hasError = this.state.error_images.has(image.id);
    const isHovered = this.state.hover_image === image.id;

    const altText = this.getImageText(image, 'alt_text');
    const title = this.getImageText(image, 'title');
    const description = this.getImageText(image, 'description');

    return (
      <div
        key={image.id}
        data-image-id={image.id}
        className={`
          quiz-image-selector-item
          ${isSelected ? 'quiz-image-selector-item-selected' : ''}
          ${isLoading ? 'quiz-image-selector-item-loading' : ''}
          ${hasError ? 'quiz-image-selector-item-error' : ''}
          ${isHovered ? 'quiz-image-selector-item-hovered' : ''}
          ${this.state.is_disabled ? 'quiz-image-selector-item-disabled' : ''}
        `.trim()}
        onClick={() => this.handleImageSelect(image)}
        onMouseEnter={() => this.handleImageHover(image.id)}
        onMouseLeave={() => this.handleImageHover(null)}
        style={{
          aspectRatio: this.config.style.aspect_ratio,
          borderRadius: `${this.config.style.border_radius}px`
        }}
        role="button"
        aria-pressed={isSelected}
        aria-label={`${altText}${isSelected ? ' (selected)' : ''}`}
        tabIndex={this.state.is_disabled ? -1 : 0}
      >
        {/* 图片容器 */}
        <div className="quiz-image-selector-image-container">
          {!hasError ? (
            <img
              src={image.url}
              alt={altText}
              className="quiz-image-selector-image"
              onLoadStart={() => this.handleImageLoadStart(image.id)}
              onLoad={() => this.handleImageLoad(image.id)}
              onError={() => this.handleImageError(image.id)}
            />
          ) : (
            <div className="quiz-image-selector-error">
              <span className="quiz-image-selector-error-icon">⚠️</span>
              <span className="quiz-image-selector-error-text">
                {this.context?.language === 'zh' ? '加载失败' : 'Load failed'}
              </span>
            </div>
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="quiz-image-selector-loading">
              <div className="quiz-image-selector-loading-spinner" />
            </div>
          )}

          {/* 选择指示器 */}
          {isSelected && (
            <div className={`quiz-image-selector-indicator ${this.getSelectionIndicatorClassName()}`}>
              {this.config.style.selection_indicator === 'checkmark' && '✓'}
            </div>
          )}
        </div>

        {/* 图片信息 */}
        {(title || description) && (
          <div className="quiz-image-selector-info">
            {title && (
              <div className="quiz-image-selector-title">{title}</div>
            )}
            {description && (
              <div className="quiz-image-selector-description">{description}</div>
            )}
          </div>
        )}
      </div>
    );
  };

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-image-selector-component',
      this.state.is_disabled && 'quiz-image-selector-disabled',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        ref={this.containerRef}
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
        role="group"
        aria-label="Image selector"
      >
        {/* 图片网格 */}
        <div 
          className="quiz-image-selector-grid"
          style={this.getGridStyles()}
        >
          {this.props.images.map(this.renderImageItem)}
        </div>

        {/* 选择信息 */}
        {this.config.selection.mode === 'multiple' && this.config.selection.max_selections && (
          <div className="quiz-image-selector-info-bar">
            <span className="quiz-image-selector-count">
              {this.state.selected_values.length} / {this.config.selection.max_selections}
            </span>
          </div>
        )}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'group';
  }

  protected getAriaLabel(): string {
    const selectedCount = this.state.selected_values.length;
    const mode = this.config.selection.mode;
    
    return `Image selector (${mode}): ${selectedCount} selected`;
  }
}

// 使用Context的函数式组件包装器
const ImageSelectorComponentWrapper: React.FC<ImageSelectorComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <ImageSelectorComponent.contextType.Provider value={{ language }}>
      <ImageSelectorComponent {...props} />
    </ImageSelectorComponent.contextType.Provider>
  );
};

// 设置Context类型
ImageSelectorComponent.contextType = React.createContext({ language: 'zh' });

export default ImageSelectorComponentWrapper;
