/**
 * CSV导入组件
 * 支持批量导入Quiz包、问题和选项数据
 */

import React, { useState, useRef } from 'react';
import { Services } from '@/services';
import { toast } from 'sonner';
import { useLanguage } from '@/contexts/LanguageContext';

interface CSVImporterProps {
  onImportSuccess: () => void;
}

interface ImportPreview {
  packs: any[];
  questions: any[];
  options: any[];
  errors: string[];
}

const CSVImporter: React.FC<CSVImporterProps> = ({ onImportSuccess }) => {
  const { t } = useLanguage();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isImporting, setIsImporting] = useState(false);
  const [preview, setPreview] = useState<ImportPreview | null>(null);
  const [importType, setImportType] = useState<'complete' | 'packs' | 'questions' | 'options'>('complete');

  /**
   * 处理文件选择
   */
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      toast.error('请选择CSV文件');
      return;
    }

    try {
      const text = await file.text();
      const previewData = parseCSV(text);
      setPreview(previewData);
    } catch (error) {
      toast.error('文件读取失败: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  /**
   * 解析CSV文件
   */
  const parseCSV = (csvText: string): ImportPreview => {
    const lines = csvText.split('\n').filter(line => line.trim());
    const result: ImportPreview = {
      packs: [],
      questions: [],
      options: [],
      errors: []
    };

    if (lines.length === 0) {
      result.errors.push('CSV文件为空');
      return result;
    }

    // 解析标题行
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    
    // 根据标题判断数据类型
    if (headers.includes('pack_name') || headers.includes('quiz_name')) {
      // Quiz包数据
      for (let i = 1; i < lines.length; i++) {
        try {
          const values = parseCSVLine(lines[i]);
          const pack = parsePackData(headers, values);
          if (pack) result.packs.push(pack);
        } catch (error) {
          result.errors.push(`第${i + 1}行解析错误: ${error}`);
        }
      }
    } else if (headers.includes('question_text') || headers.includes('question')) {
      // 问题数据
      for (let i = 1; i < lines.length; i++) {
        try {
          const values = parseCSVLine(lines[i]);
          const question = parseQuestionData(headers, values);
          if (question) result.questions.push(question);
        } catch (error) {
          result.errors.push(`第${i + 1}行解析错误: ${error}`);
        }
      }
    } else if (headers.includes('option_text') || headers.includes('option')) {
      // 选项数据
      for (let i = 1; i < lines.length; i++) {
        try {
          const values = parseCSVLine(lines[i]);
          const option = parseOptionData(headers, values);
          if (option) result.options.push(option);
        } catch (error) {
          result.errors.push(`第${i + 1}行解析错误: ${error}`);
        }
      }
    } else {
      // 尝试解析完整数据格式
      for (let i = 1; i < lines.length; i++) {
        try {
          const values = parseCSVLine(lines[i]);
          const data = parseCompleteData(headers, values);
          if (data.pack) result.packs.push(data.pack);
          if (data.question) result.questions.push(data.question);
          if (data.option) result.options.push(data.option);
        } catch (error) {
          result.errors.push(`第${i + 1}行解析错误: ${error}`);
        }
      }
    }

    return result;
  };

  /**
   * 解析CSV行
   */
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  };

  /**
   * 解析Quiz包数据
   */
  const parsePackData = (headers: string[], values: string[]): any => {
    const pack: any = {};
    
    headers.forEach((header, index) => {
      const value = values[index]?.replace(/"/g, '') || '';
      
      switch (header.toLowerCase()) {
        case 'pack_name':
        case 'quiz_name':
        case 'name':
          pack.name = value;
          break;
        case 'description':
          pack.description = value;
          break;
        case 'quiz_type':
        case 'type':
          pack.quiz_type = value || 'emotion_wheel';
          break;
        case 'category':
          pack.category = value || 'emotion';
          break;
        case 'difficulty_level':
        case 'difficulty':
          pack.difficulty_level = parseInt(value) || 1;
          break;
        case 'estimated_duration_minutes':
        case 'duration':
          pack.estimated_duration_minutes = parseInt(value) || 5;
          break;
        case 'is_active':
        case 'active':
          pack.is_active = value.toLowerCase() === 'true' || value === '1';
          break;
        case 'is_public':
        case 'public':
          pack.is_public = value.toLowerCase() === 'true' || value === '1';
          break;
      }
    });
    
    return pack.name ? pack : null;
  };

  /**
   * 解析问题数据
   */
  const parseQuestionData = (headers: string[], values: string[]): any => {
    const question: any = {};
    
    headers.forEach((header, index) => {
      const value = values[index]?.replace(/"/g, '') || '';
      
      switch (header.toLowerCase()) {
        case 'pack_name':
        case 'quiz_name':
          question.pack_name = value;
          break;
        case 'question_text':
        case 'question':
          question.question_text = value;
          break;
        case 'question_type':
        case 'type':
          question.question_type = value || 'single_choice';
          break;
        case 'question_order':
        case 'order':
          question.question_order = parseInt(value) || 1;
          break;
        case 'question_group':
        case 'group':
          question.question_group = value;
          break;
        case 'tier_level':
        case 'tier':
          question.tier_level = parseInt(value) || 1;
          break;
        case 'is_required':
        case 'required':
          question.is_required = value.toLowerCase() === 'true' || value === '1';
          break;
        case 'is_active':
        case 'active':
          question.is_active = value.toLowerCase() === 'true' || value === '1';
          break;
      }
    });
    
    return question.question_text ? question : null;
  };

  /**
   * 解析选项数据
   */
  const parseOptionData = (headers: string[], values: string[]): any => {
    const option: any = { metadata: {} };
    
    headers.forEach((header, index) => {
      const value = values[index]?.replace(/"/g, '') || '';
      
      switch (header.toLowerCase()) {
        case 'question_text':
        case 'question':
          option.question_text = value;
          break;
        case 'option_text':
        case 'option':
          option.option_text = value;
          break;
        case 'option_value':
        case 'value':
          option.option_value = value;
          break;
        case 'option_order':
        case 'order':
          option.option_order = parseInt(value) || 1;
          break;
        case 'scoring_value':
        case 'score':
          option.scoring_value = value ? parseInt(value) : null;
          break;
        case 'is_correct':
        case 'correct':
          option.is_correct = value.toLowerCase() === 'true' || value === '1' ? true : null;
          break;
        case 'is_active':
        case 'active':
          option.is_active = value.toLowerCase() === 'true' || value === '1';
          break;
        case 'emoji':
          option.metadata.emoji = value;
          break;
        case 'color':
          option.metadata.color = value;
          break;
        case 'description':
          option.metadata.description = value;
          break;
      }
    });
    
    return option.option_text ? option : null;
  };

  /**
   * 解析完整数据格式
   */
  const parseCompleteData = (headers: string[], values: string[]): any => {
    // 这里可以实现更复杂的完整数据解析逻辑
    return {
      pack: parsePackData(headers, values),
      question: parseQuestionData(headers, values),
      option: parseOptionData(headers, values)
    };
  };

  /**
   * 执行导入
   */
  const handleImport = async () => {
    if (!preview) return;

    setIsImporting(true);
    
    try {
      const quizPackService = await Services.quizPack();
      const quizQuestionService = await Services.quizQuestion();
      
      let importedPacks = 0;
      let importedQuestions = 0;
      let importedOptions = 0;

      // 导入Quiz包
      if (preview.packs.length > 0) {
        for (const packData of preview.packs) {
          try {
            const result = await quizPackService.create({
              ...packData,
              created_by: 'csv_import'
            });
            if (result.success) {
              importedPacks++;
            }
          } catch (error) {
            console.error('Failed to import pack:', error);
          }
        }
      }

      // 导入问题
      if (preview.questions.length > 0) {
        for (const questionData of preview.questions) {
          try {
            // 如果有pack_name，需要先找到对应的pack_id
            if (questionData.pack_name) {
              const packsResult = await quizPackService.searchQuizPacks(questionData.pack_name);
              if (packsResult.success && packsResult.data.length > 0) {
                questionData.pack_id = packsResult.data[0].id;
              }
            }
            
            if (questionData.pack_id) {
              const result = await quizQuestionService.create({
                ...questionData,
                created_by: 'csv_import'
              });
              if (result.success) {
                importedQuestions++;
              }
            }
          } catch (error) {
            console.error('Failed to import question:', error);
          }
        }
      }

      // 导入选项
      if (preview.options.length > 0) {
        for (const optionData of preview.options) {
          try {
            // 如果有question_text，需要先找到对应的question_id
            if (optionData.question_text) {
              // 这里需要实现根据问题文本查找问题ID的逻辑
              // 暂时跳过
            }
            
            if (optionData.question_id) {
              const result = await quizQuestionService.createOption({
                ...optionData,
                metadata: JSON.stringify(optionData.metadata),
                created_by: 'csv_import'
              });
              if (result.success) {
                importedOptions++;
              }
            }
          } catch (error) {
            console.error('Failed to import option:', error);
          }
        }
      }

      toast.success(`导入完成: ${importedPacks}个包, ${importedQuestions}个问题, ${importedOptions}个选项`);
      onImportSuccess();
      setPreview(null);
      
    } catch (error) {
      toast.error('导入失败: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsImporting(false);
    }
  };

  /**
   * 下载模板文件
   */
  const downloadTemplate = (type: string) => {
    let csvContent = '';
    
    switch (type) {
      case 'packs':
        csvContent = 'pack_name,description,quiz_type,category,difficulty_level,estimated_duration_minutes,is_active,is_public\n';
        csvContent += '示例Quiz包,这是一个示例Quiz包,emotion_wheel,emotion,1,5,true,false\n';
        break;
      case 'questions':
        csvContent = 'pack_name,question_text,question_type,question_order,question_group,tier_level,is_required,is_active\n';
        csvContent += '示例Quiz包,请选择您的情绪,single_choice,1,主要情绪,1,true,true\n';
        break;
      case 'options':
        csvContent = 'question_text,option_text,option_value,option_order,scoring_value,is_correct,is_active,emoji,color,description\n';
        csvContent += '请选择您的情绪,快乐,happy,1,5,false,true,😊,#FFD700,表示快乐的情绪\n';
        break;
      case 'complete':
        csvContent = 'pack_name,description,quiz_type,question_text,question_type,question_order,option_text,option_value,option_order,emoji,color\n';
        csvContent += '示例Quiz包,完整示例,emotion_wheel,请选择情绪,single_choice,1,快乐,happy,1,😊,#FFD700\n';
        break;
    }
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `quiz_${type}_template.csv`;
    link.click();
  };

  return (
    <div className="csv-importer">
      <div className="importer-header">
        <h2>CSV批量导入</h2>
        <p>支持批量导入Quiz包、问题和选项数据</p>
      </div>

      {/* 模板下载 */}
      <div className="template-section">
        <h3>下载模板文件</h3>
        <div className="template-buttons">
          <button 
            className="template-button"
            onClick={() => downloadTemplate('complete')}
          >
            📄 完整模板
          </button>
          <button 
            className="template-button"
            onClick={() => downloadTemplate('packs')}
          >
            📦 Quiz包模板
          </button>
          <button 
            className="template-button"
            onClick={() => downloadTemplate('questions')}
          >
            ❓ 问题模板
          </button>
          <button 
            className="template-button"
            onClick={() => downloadTemplate('options')}
          >
            📝 选项模板
          </button>
        </div>
      </div>

      {/* 文件上传 */}
      <div className="upload-section">
        <h3>上传CSV文件</h3>
        <div className="upload-area">
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
          />
          <button 
            className="upload-button"
            onClick={() => fileInputRef.current?.click()}
          >
            选择CSV文件
          </button>
          <p>支持的格式: .csv</p>
        </div>
      </div>

      {/* 预览和导入 */}
      {preview && (
        <div className="preview-section">
          <h3>导入预览</h3>
          
          {preview.errors.length > 0 && (
            <div className="preview-errors">
              <h4>错误信息:</h4>
              <ul>
                {preview.errors.map((error, index) => (
                  <li key={index} className="error-item">{error}</li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="preview-stats">
            <div className="stat-item">
              <span className="stat-label">Quiz包:</span>
              <span className="stat-value">{preview.packs.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">问题:</span>
              <span className="stat-value">{preview.questions.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">选项:</span>
              <span className="stat-value">{preview.options.length}</span>
            </div>
          </div>
          
          <div className="preview-actions">
            <button 
              className="action-button secondary"
              onClick={() => setPreview(null)}
            >
              取消
            </button>
            <button 
              className="action-button primary"
              onClick={handleImport}
              disabled={isImporting || preview.errors.length > 0}
            >
              {isImporting ? '导入中...' : '开始导入'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export { CSVImporter };
