/**
 * 皮肤预览组件
 */

import { useLanguage } from '@/contexts/LanguageContext';
import type { Emotion, EmotionData } from '@/types/emotionDataTypes';
import type { ContentDisplayMode, ViewType, RenderEngine } from '@/types/previewTypes';
import type { Skin } from '@/types/skinTypes';
import { ViewFactory } from '@/utils/viewFactory';
import type React from 'react';
import { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

interface SkinPreviewProps {
  emotionData: EmotionData;
  skin: Skin;
  viewType?: ViewType;
  contentDisplayMode?: ContentDisplayMode;
  RenderEngine?: RenderEngine;
  onSelect?: (emotion: Emotion) => void;
  onViewTypeChange?: (viewType: ViewType) => void;
  onContentDisplayModeChange?: (mode: ContentDisplayMode) => void;
  onRenderEngineChange?: (implementation: RenderEngine) => void;
}

/**
 * 皮肤预览组件
 */
export const SkinPreview: React.FC<SkinPreviewProps> = ({
  emotionData,
  skin,
  viewType = 'wheel',
  contentDisplayMode = 'textEmoji',
  RenderEngine = 'D3',
  onSelect,
  onViewTypeChange,
  onContentDisplayModeChange,
  onRenderEngineChange,
}) => {
  const { t } = useLanguage();
  const [selectedTierId, setSelectedTierId] = useState<string | null>(
    emotionData.tiers.length > 0 ? emotionData.tiers[0].id : null
  );

  // 获取选中的层级
  const selectedTier = selectedTierId
    ? emotionData.tiers.find((tier) => tier.id === selectedTierId)
    : null;

  // 处理视图类型变化
  const handleViewTypeChange = (type: ViewType) => {
    onViewTypeChange?.(type);
  };

  // 处理内容显示模式变化
  const handleContentDisplayModeChange = (mode: ContentDisplayMode) => {
    onContentDisplayModeChange?.(mode);
  };

  // 处理轮盘实现类型变化
  const handleRenderEngineChange = (implementation: RenderEngine) => {
    onRenderEngineChange?.(implementation);
  };

  // 处理情绪选择
  const handleEmotionSelect = (emotion: Emotion) => {
    onSelect?.(emotion);
  };

  // 直接使用 onSkinChange 回调，不需要额外的处理函数

  // 创建视图
  const createView = () => {
    if (!selectedTier) return null;

    // 使用 ViewFactory 静态方法创建视图
    const view = ViewFactory.createView(viewType, contentDisplayMode, skin.config, {
      implementation: RenderEngine,
    });

    // 渲染视图
    return view.render(selectedTier.emotions, selectedTier.level, handleEmotionSelect);
  };

  return (
    <div className="skin-preview">
      <div className="flex flex-col gap-4">
        {/* 移动端优化：使用水平滚动的选项卡 */}
        <div className="w-full overflow-x-auto pb-2 md:hidden">
          <div className="flex gap-2 items-center">
            <div className="flex-1">
              <Select value={selectedTierId || ''} onValueChange={setSelectedTierId}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('skin_preview.tier', { fallback: '层级' })} />
                </SelectTrigger>
                <SelectContent>
                  {emotionData.tiers.map((tier) => (
                    <SelectItem key={tier.id} value={tier.id}>
                      {tier.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1">
              <Select
                value={viewType}
                onValueChange={(value) => handleViewTypeChange(value as ViewType)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('skin_preview.view', { fallback: '视图' })} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wheel">
                    {t('skin_preview.wheel_view', { fallback: '轮盘' })}
                  </SelectItem>
                  <SelectItem value="card">
                    {t('skin_preview.card_view', { fallback: '卡片' })}
                  </SelectItem>
                  <SelectItem value="bubble">
                    {t('skin_preview.bubble_view', { fallback: '气泡' })}
                  </SelectItem>
                  <SelectItem value="galaxy">
                    {t('skin_preview.galaxy_view', { fallback: '星系' })}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {viewType === 'wheel' && (
              <div className="flex-1">
                <Select
                  value={RenderEngine}
                  onValueChange={(value) =>
                    handleRenderEngineChange(value as RenderEngine)
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t('skin_preview.impl', { fallback: '实现' })} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="D3">D3</SelectItem>
                    <SelectItem value="SVG">SVG</SelectItem>
                    <SelectItem value="R3F">3D</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div className="flex gap-2 items-center mt-2">
            <div className="flex-1">
              <Select
                value={contentDisplayMode}
                onValueChange={(value) =>
                  handleContentDisplayModeChange(value as ContentDisplayMode)
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('skin_preview.content', { fallback: '内容' })} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">
                    {t('skin_preview.text_only', { fallback: '仅文本' })}
                  </SelectItem>
                  <SelectItem value="emoji">
                    {t('skin_preview.emoji_only', { fallback: '仅表情' })}
                  </SelectItem>
                  <SelectItem value="textEmoji">
                    {t('skin_preview.text_emoji', { fallback: '文本+表情' })}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1">
              <div className="border rounded-md px-3 py-2 text-sm text-center">
                {t('skin_preview.current_skin', { fallback: '当前皮肤' })}: {skin.name}
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          {/* 预览设置 - 桌面端显示 */}
          <div className="hidden md:block w-full md:w-1/3">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  {/* 层级选择 */}
                  <div className="space-y-2">
                    <Label htmlFor="tierSelect">
                      {t('skin_preview.tier', { fallback: '层级' })}
                    </Label>
                    <Select value={selectedTierId || ''} onValueChange={setSelectedTierId}>
                      <SelectTrigger id="tierSelect">
                        <SelectValue
                          placeholder={t('skin_preview.select_tier', { fallback: '选择层级' })}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {emotionData.tiers.map((tier) => (
                          <SelectItem key={tier.id} value={tier.id}>
                            {tier.name} (
                            {t(`tier_editor.level_${tier.level}`, { fallback: `${tier.level}级` })})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 视图类型选择 */}
                  <div className="space-y-2">
                    <Label htmlFor="viewType">
                      {t('skin_preview.view_type', { fallback: '视图类型' })}
                    </Label>
                    <Select
                      value={viewType}
                      onValueChange={(value) => handleViewTypeChange(value as ViewType)}
                    >
                      <SelectTrigger id="viewType">
                        <SelectValue
                          placeholder={t('skin_preview.select_view_type', {
                            fallback: '选择视图类型',
                          })}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="wheel">
                          {t('skin_preview.wheel_view', { fallback: '轮盘视图' })}
                        </SelectItem>
                        <SelectItem value="card">
                          {t('skin_preview.card_view', { fallback: '卡片视图' })}
                        </SelectItem>
                        <SelectItem value="bubble">
                          {t('skin_preview.bubble_view', { fallback: '气泡视图' })}
                        </SelectItem>
                        <SelectItem value="galaxy">
                          {t('skin_preview.galaxy_view', { fallback: '星系视图' })}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 轮盘实现类型选择（仅当视图类型为轮盘时显示） */}
                  {viewType === 'wheel' && (
                    <div className="space-y-2">
                      <Label htmlFor="RenderEngine">
                        {t('skin_preview.wheel_implementation', { fallback: '轮盘实现' })}
                      </Label>
                      <Select
                        value={RenderEngine}
                        onValueChange={(value) =>
                          handleRenderEngineChange(value as RenderEngine)
                        }
                      >
                        <SelectTrigger id="RenderEngine">
                          <SelectValue
                            placeholder={t('skin_preview.select_implementation', {
                              fallback: '选择实现',
                            })}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="D3">
                            {t('skin_preview.d3_wheel', { fallback: 'D3轮盘' })}
                          </SelectItem>
                          <SelectItem value="SVG">
                            {t('skin_preview.svg_wheel', { fallback: 'SVG轮盘' })}
                          </SelectItem>
                          <SelectItem value="R3F">
                            {t('skin_preview.r3f_wheel', { fallback: '3D轮盘' })}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* 内容显示模式选择 */}
                  <div className="space-y-2">
                    <Label htmlFor="contentDisplayMode">
                      {t('skin_preview.content_display_mode', { fallback: '内容显示模式' })}
                    </Label>
                    <Select
                      value={contentDisplayMode}
                      onValueChange={(value) =>
                        handleContentDisplayModeChange(value as ContentDisplayMode)
                      }
                    >
                      <SelectTrigger id="contentDisplayMode">
                        <SelectValue
                          placeholder={t('skin_preview.select_content_mode', {
                            fallback: '选择内容模式',
                          })}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="text">
                          {t('skin_preview.text_only', { fallback: '仅文本' })}
                        </SelectItem>
                        <SelectItem value="emoji">
                          {t('skin_preview.emoji_only', { fallback: '仅表情' })}
                        </SelectItem>
                        <SelectItem value="textEmoji">
                          {t('skin_preview.text_emoji', { fallback: '文本和表情' })}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 皮肤信息 */}
                  <div className="pt-4">
                    <p className="text-sm text-muted-foreground">
                      {t('skin_preview.skin_info', { fallback: '当前皮肤' })}: {skin.name}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 预览区域 */}
          <div className="w-full md:w-2/3">
            <Card>
              <CardContent className="flex items-center justify-center p-2 md:p-6 min-h-[300px] md:min-h-[400px] overflow-hidden">
                {selectedTier ? (
                  selectedTier.emotions.length > 0 ? (
                    <div className="w-full h-full flex items-center justify-center overflow-auto">
                      <div
                        className="max-w-full max-h-full"
                        style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        {createView()}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <p className="text-muted-foreground">
                        {t('skin_preview.no_emotions', {
                          fallback: '此层级没有情绪，请先添加情绪',
                        })}
                      </p>
                    </div>
                  )
                ) : (
                  <div className="text-center">
                    <p className="text-muted-foreground">
                      {emotionData.tiers.length > 0
                        ? t('skin_preview.select_tier', { fallback: '请选择一个层级' })
                        : t('skin_preview.no_tiers', {
                            fallback: '此情绪数据没有层级，请先添加层级',
                          })}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
