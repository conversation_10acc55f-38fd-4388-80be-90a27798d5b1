/**
 * 卡片视图
 * 使用卡片布局显示情绪
 */

import type { Emotion, ContentDisplayMode, ViewConfig, SkinConfig, CardLayout } from '@/types';
import { BaseEmotionView } from '@/views/base/BaseEmotionView';
import type React from 'react';
import { CardView as CardViewComponent } from '@/views/components/cards/CardView';

/**
 * 卡片视图类
 * 使用卡片布局显示情绪
 */
export class CardView extends BaseEmotionView {
  protected layout: CardLayout = 'grid';

  /**
   * 构造函数
   * @param contentType 内容显示模式
   * @param skinConfig 皮肤配置
   * @param layout 卡片布局
   */
  constructor(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: CardLayout = 'grid'
  ) {
    super('card', contentType, skinConfig);
    this.layout = layout;
  }

  /**
   * 获取卡片布局
   * @returns 卡片布局
   */
  getLayout(): CardLayout {
    return this.layout;
  }

  /**
   * 设置卡片布局
   * @param layout 新的卡片布局
   */
  setLayout(layout: CardLayout): void {
    this.layout = layout;
  }

  /**
   * 渲染卡片
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  renderCards(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    // 从配置中获取额外属性
    const onBack = config?.onBack;
    const selectedPath = config?.selectedPath;

    return (
      <CardViewComponent
        emotions={emotions}
        tierLevel={tierLevel}
        contentDisplayMode={this.contentDisplayMode}
        skinConfig={this.skinConfig}
        onSelect={onSelect}
        layout={this.layout}
        onBack={onBack}
        selectedPath={selectedPath}
      />
    );
  }

  /**
   * 渲染视图
   * 实现基类的抽象方法
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  render(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    return this.renderCards(emotions, tierLevel, onSelect, config);
  }
}
