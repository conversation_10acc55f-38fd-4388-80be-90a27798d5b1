import { cn } from '@/lib/utils';
import type React from 'react';

interface NavigationItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

interface SettingsNavigationProps {
  items: NavigationItem[];
  activeItem: string;
  onItemChange: (id: string) => void;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

/**
 * 设置导航组件
 * 用于设置页面中的导航，支持水平和垂直布局
 */
const SettingsNavigation: React.FC<SettingsNavigationProps> = ({
  items,
  activeItem,
  onItemChange,
  className = '',
  orientation = 'horizontal',
}) => {
  const containerClasses = cn(
    'flex gap-1 p-1 rounded-md bg-muted',
    orientation === 'horizontal' ? 'flex-row' : 'flex-col',
    className
  );

  const itemClasses = (isActive: boolean) =>
    cn(
      'flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
      'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
      'disabled:pointer-events-none disabled:opacity-50 cursor-pointer',
      isActive
        ? 'bg-background text-foreground shadow-sm'
        : 'text-muted-foreground hover:bg-background/50 hover:text-foreground'
    );

  return (
    <div className={containerClasses} role="tablist" aria-orientation={orientation}>
      {items.map((item) => (
        <div
          key={item.id}
          className={itemClasses(activeItem === item.id)}
          role="tab"
          aria-selected={activeItem === item.id}
          tabIndex={0}
          onClick={() => onItemChange(item.id)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              onItemChange(item.id);
              e.preventDefault();
            }
          }}
        >
          <div className="flex items-center justify-center">
            {item.icon && <span className="mr-2">{item.icon}</span>}
            {item.label}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SettingsNavigation;
