{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode test", "build": "vite build", "build:dev": "vite build --mode development", "format": "biome format --write .", "format:check": "biome format .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "check": "biome check .", "check:fix": "biome check --write .", "ci:check": "biome ci .", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest watch", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "create-local-db": "node create-local-db.cjs", "test:contrast": "node scripts/test-contrast.js", "test:simple": "node scripts/simple-test.js", "test:comprehensive": "node scripts/comprehensive-test.js", "test:all-modes": "node scripts/all-color-modes-test.js", "cleanup:deprecated": "node scripts/cleanup-deprecated-components.js"}, "dependencies": {"@biomejs/biome": "^1.9.4", "@capacitor-community/sqlite": "^7.0.0", "@capacitor/core": "^7.2.0", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.2.0", "@capacitor/network": "^7.0.1", "@hookform/resolvers": "^3.10.0", "@libsql/client": "^0.15.5", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@tanstack/react-query": "^5.75.7", "@trpc/client": "^11.1.2", "@types/d3": "^7.4.3", "@types/jest": "^29.5.14", "@types/sqlite3": "^5.1.0", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cypress": "^14.3.3", "d3": "^7.9.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "emoji-picker-react": "^4.12.2", "framer-motion": "^10.18.0", "input-otp": "^1.4.2", "jeep-sqlite": "^2.8.0", "jest": "^29.7.0", "libsql": "^0.5.9", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-resizable-panels": "^2.1.9", "react-router-dom": "^6.30.0", "recharts": "^2.15.3", "sonner": "^1.7.4", "sql.js": "1.11.0", "sqlite": "^5.1.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.3.2", "uuid": "^11.1.0", "vaul": "^0.9.9", "zod": "^3.24.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.17", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-v8": "3.1.3", "@vitest/ui": "^3.1.3", "autoprefixer": "^10.4.21", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "lovable-tagger": "^1.1.8", "postcss": "^8.5.3", "sqlite3": "^5.1.7", "tailwindcss": "^3.4.17", "ts-jest": "^29.3.2", "typescript": "^5.8.3", "vite": "^5.4.19", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3"}}