{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 4}, {"startOffset": 872, "endOffset": 895, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/entities/__tests__/QuizPackServiceFixed.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 60912, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 60912, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 879, "endOffset": 22758, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 963, "endOffset": 1193, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1249, "endOffset": 4842, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1330, "endOffset": 3159, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3242, "endOffset": 3693, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3781, "endOffset": 4223, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4323, "endOffset": 4834, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4902, "endOffset": 7149, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4986, "endOffset": 7141, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7206, "endOffset": 9088, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7286, "endOffset": 8621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8713, "endOffset": 9080, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9146, "endOffset": 11080, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9231, "endOffset": 10275, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10350, "endOffset": 11072, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11145, "endOffset": 13723, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11242, "endOffset": 13715, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13779, "endOffset": 15333, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13860, "endOffset": 15325, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15414, "endOffset": 17826, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15497, "endOffset": 16612, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16694, "endOffset": 17818, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17910, "endOffset": 20749, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18004, "endOffset": 20741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20805, "endOffset": 22754, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20891, "endOffset": 22746, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}, {"scriptId": "1325", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/entities/QuizPackService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36345, "count": 1}], "isBlockCoverage": true}, {"functionName": "createQuizPack", "ranges": [{"startOffset": 636, "endOffset": 1097, "count": 5}, {"startOffset": 745, "endOffset": 986, "count": 2}, {"startOffset": 986, "endOffset": 1091, "count": 3}], "isBlockCoverage": true}, {"functionName": "getActiveQuizPacks", "ranges": [{"startOffset": 1128, "endOffset": 1428, "count": 1}, {"startOffset": 1312, "endOffset": 1422, "count": 0}], "isBlockCoverage": true}, {"functionName": "getQuizPacksByType", "ranges": [{"startOffset": 1460, "endOffset": 1772, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuizPacksByCategory", "ranges": [{"startOffset": 1804, "endOffset": 2124, "count": 0}], "isBlockCoverage": false}, {"functionName": "searchQuizPacks", "ranges": [{"startOffset": 2152, "endOffset": 2628, "count": 2}, {"startOffset": 2263, "endOffset": 2622, "count": 1}], "isBlockCoverage": true}, {"functionName": "getQuizPacksByDifficulty", "ranges": [{"startOffset": 2662, "endOffset": 3160, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuizPacksByTag", "ranges": [{"startOffset": 3192, "endOffset": 3615, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuizPackStats", "ranges": [{"startOffset": 3650, "endOffset": 4690, "count": 4}, {"startOffset": 3858, "endOffset": 3931, "count": 3}, {"startOffset": 3932, "endOffset": 3935, "count": 1}, {"startOffset": 4160, "endOffset": 4164, "count": 1}, {"startOffset": 4230, "endOffset": 4234, "count": 1}, {"startOffset": 4294, "endOffset": 4298, "count": 1}, {"startOffset": 4393, "endOffset": 4397, "count": 1}, {"startOffset": 4576, "endOffset": 4684, "count": 0}], "isBlockCoverage": true}, {"functionName": "getRecommendedQuizPacks", "ranges": [{"startOffset": 4721, "endOffset": 6276, "count": 1}, {"startOffset": 6155, "endOffset": 6270, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4974, "endOffset": 5926, "count": 2}, {"startOffset": 5133, "endOffset": 5408, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6015, "endOffset": 6040, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6062, "endOffset": 6079, "count": 2}], "isBlockCoverage": true}, {"functionName": "updateQuizPack", "ranges": [{"startOffset": 6304, "endOffset": 6784, "count": 2}, {"startOffset": 6673, "endOffset": 6778, "count": 0}], "isBlockCoverage": true}, {"functionName": "activateQuizPack", "ranges": [{"startOffset": 6812, "endOffset": 7207, "count": 2}, {"startOffset": 7094, "endOffset": 7201, "count": 0}], "isBlockCoverage": true}, {"functionName": "deactivateQuizPack", "ranges": [{"startOffset": 7235, "endOffset": 7637, "count": 1}, {"startOffset": 7522, "endOffset": 7631, "count": 0}], "isBlockCoverage": true}, {"functionName": "getQuizPackCategories", "ranges": [{"startOffset": 7670, "endOffset": 8104, "count": 1}, {"startOffset": 7985, "endOffset": 8098, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7863, "endOffset": 7884, "count": 3}], "isBlockCoverage": true}, {"functionName": "getQuizPackTypes", "ranges": [{"startOffset": 8137, "endOffset": 8536, "count": 1}, {"startOffset": 8422, "endOffset": 8530, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8320, "endOffset": 8342, "count": 3}], "isBlockCoverage": true}, {"functionName": "validateCreate", "ranges": [{"startOffset": 8555, "endOffset": 9359, "count": 5}, {"startOffset": 8607, "endOffset": 8639, "count": 4}, {"startOffset": 8641, "endOffset": 8711, "count": 1}, {"startOffset": 8711, "endOffset": 8748, "count": 4}, {"startOffset": 8748, "endOffset": 8839, "count": 0}, {"startOffset": 8839, "endOffset": 8868, "count": 4}, {"startOffset": 8868, "endOffset": 8905, "count": 3}, {"startOffset": 8907, "endOffset": 8972, "count": 1}, {"startOffset": 8972, "endOffset": 9021, "count": 3}, {"startOffset": 9021, "endOffset": 9080, "count": 2}, {"startOffset": 9082, "endOffset": 9166, "count": 1}, {"startOffset": 9166, "endOffset": 9225, "count": 2}, {"startOffset": 9225, "endOffset": 9263, "count": 1}, {"startOffset": 9265, "endOffset": 9353, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateUpdate", "ranges": [{"startOffset": 9364, "endOffset": 10266, "count": 2}, {"startOffset": 9479, "endOffset": 9553, "count": 0}, {"startOffset": 9617, "endOffset": 9708, "count": 0}, {"startOffset": 9750, "endOffset": 9808, "count": 0}, {"startOffset": 9810, "endOffset": 9879, "count": 0}, {"startOffset": 9928, "endOffset": 9987, "count": 1}, {"startOffset": 9989, "endOffset": 10073, "count": 0}, {"startOffset": 10132, "endOffset": 10170, "count": 0}, {"startOffset": 10172, "endOffset": 10260, "count": 0}], "isBlockCoverage": true}, {"functionName": "QuizPackService", "ranges": [{"startOffset": 10271, "endOffset": 10397, "count": 15}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 10503, "endOffset": 10534, "count": 15}], "isBlockCoverage": true}], "startOffset": 185}, {"scriptId": "1326", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/base/BaseService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16012, "count": 1}], "isBlockCoverage": true}, {"functionName": "_define_property", "ranges": [{"startOffset": 220, "endOffset": 518, "count": 30}, {"startOffset": 285, "endOffset": 461, "count": 0}], "isBlockCoverage": true}, {"functionName": "on", "ranges": [{"startOffset": 576, "endOffset": 739, "count": 3}], "isBlockCoverage": true}, {"functionName": "off", "ranges": [{"startOffset": 744, "endOffset": 1000, "count": 0}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 1005, "endOffset": 1374, "count": 7}, {"startOffset": 1101, "endOffset": 1368, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1133, "endOffset": 1356, "count": 3}, {"startOffset": 1226, "endOffset": 1342, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeAllListeners", "ranges": [{"startOffset": 1379, "endOffset": 1532, "count": 0}], "isBlockCoverage": false}, {"functionName": "BrowserEventEmitter", "ranges": [{"startOffset": 1537, "endOffset": 1610, "count": 15}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 1686, "endOffset": 2104, "count": 0}], "isBlockCoverage": false}, {"functionName": "findById", "ranges": [{"startOffset": 2133, "endOffset": 2488, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 2513, "endOffset": 3027, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 3052, "endOffset": 3495, "count": 0}], "isBlockCoverage": false}, {"functionName": "findAll", "ranges": [{"startOffset": 3522, "endOffset": 3799, "count": 0}], "isBlockCoverage": false}, {"functionName": "count", "ranges": [{"startOffset": 3826, "endOffset": 4099, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSuccessResult", "ranges": [{"startOffset": 4116, "endOffset": 4221, "count": 16}], "isBlockCoverage": true}, {"functionName": "createErrorResult", "ranges": [{"startOffset": 4226, "endOffset": 4536, "count": 4}, {"startOffset": 4330, "endOffset": 4345, "count": 0}], "isBlockCoverage": true}, {"functionName": "BaseService", "ranges": [{"startOffset": 4541, "endOffset": 4664, "count": 15}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4766, "endOffset": 4793, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}, {"scriptId": "1327", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/entities/QuizPackRepository.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44187, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44187, "count": 1}], "isBlockCoverage": true}, {"functionName": "findActiveQuizPacks", "ranges": [{"startOffset": 506, "endOffset": 821, "count": 0}], "isBlockCoverage": false}, {"functionName": "findByQuizType", "ranges": [{"startOffset": 853, "endOffset": 1223, "count": 0}], "isBlockCoverage": false}, {"functionName": "findByCategory", "ranges": [{"startOffset": 1255, "endOffset": 1624, "count": 0}], "isBlockCoverage": false}, {"functionName": "searchQuizPacks", "ranges": [{"startOffset": 1652, "endOffset": 2128, "count": 0}], "isBlockCoverage": false}, {"functionName": "findByDifficultyLevel", "ranges": [{"startOffset": 2162, "endOffset": 2560, "count": 0}], "isBlockCoverage": false}, {"functionName": "findByTag", "ranges": [{"startOffset": 2592, "endOffset": 2993, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuizPackStats", "ranges": [{"startOffset": 3026, "endOffset": 3987, "count": 0}], "isBlockCoverage": false}, {"functionName": "getQuestionsByPackId", "ranges": [{"startOffset": 4020, "endOffset": 4680, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOptionsByQuestionId", "ranges": [{"startOffset": 4710, "endOffset": 5387, "count": 0}], "isBlockCoverage": false}, {"functionName": "mapRowToEntity", "ranges": [{"startOffset": 5392, "endOffset": 6429, "count": 0}], "isBlockCoverage": false}, {"functionName": "mapEntityToRow", "ranges": [{"startOffset": 6434, "endOffset": 7517, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildInsertQuery", "ranges": [{"startOffset": 7522, "endOffset": 8724, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildUpdateQuery", "ranges": [{"startOffset": 8729, "endOffset": 10946, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildSelectQuery", "ranges": [{"startOffset": 10951, "endOffset": 12105, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildCountQuery", "ranges": [{"startOffset": 12110, "endOffset": 12927, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractIdFromCreateData", "ranges": [{"startOffset": 12932, "endOffset": 13054, "count": 0}], "isBlockCoverage": false}, {"functionName": "stringifyJSON", "ranges": [{"startOffset": 13059, "endOffset": 13253, "count": 0}], "isBlockCoverage": false}, {"functionName": "QuizPackRepository", "ranges": [{"startOffset": 13258, "endOffset": 13313, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 13422, "endOffset": 13456, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}, {"scriptId": "1328", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/base/BaseRepository.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27358, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27358, "count": 1}], "isBlockCoverage": true}, {"functionName": "_define_property", "ranges": [{"startOffset": 216, "endOffset": 514, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDatabase", "ranges": [{"startOffset": 565, "endOffset": 610, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDb", "ranges": [{"startOffset": 638, "endOffset": 771, "count": 0}], "isBlockCoverage": false}, {"functionName": "create", "ranges": [{"startOffset": 797, "endOffset": 1349, "count": 0}], "isBlockCoverage": false}, {"functionName": "findById", "ranges": [{"startOffset": 1378, "endOffset": 2019, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 2044, "endOffset": 2545, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 2570, "endOffset": 2950, "count": 0}], "isBlockCoverage": false}, {"functionName": "find<PERSON>any", "ranges": [{"startOffset": 2977, "endOffset": 3426, "count": 0}], "isBlockCoverage": false}, {"functionName": "count", "ranges": [{"startOffset": 3453, "endOffset": 3913, "count": 0}], "isBlockCoverage": false}, {"functionName": "batchInsert", "ranges": [{"startOffset": 3938, "endOffset": 4594, "count": 0}], "isBlockCoverage": false}, {"functionName": "batchUpdate", "ranges": [{"startOffset": 4619, "endOffset": 5284, "count": 0}], "isBlockCoverage": false}, {"functionName": "executeRawQuery", "ranges": [{"startOffset": 5314, "endOffset": 5609, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildSelectQuery", "ranges": [{"startOffset": 5636, "endOffset": 6608, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildCountQuery", "ranges": [{"startOffset": 6613, "endOffset": 7161, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildWhereClause", "ranges": [{"startOffset": 7191, "endOffset": 7662, "count": 0}], "isBlockCoverage": false}, {"functionName": "escapeIdentifier", "ranges": [{"startOffset": 7691, "endOffset": 7781, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseRepository", "ranges": [{"startOffset": 7786, "endOffset": 7976, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8081, "endOffset": 8111, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}