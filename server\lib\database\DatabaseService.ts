import { type DatabaseConfig, DatabaseFactory, DatabaseType } from './DatabaseFactory.js';
import { type DatabaseInitOptions, DatabaseInitializer } from './DatabaseInitializer.js';
import type {
  DatabaseInterface,
  InStatement,
  ResultSet,
  TransactionMode,
} from './DatabaseInterface.js';

/**
 * 数据库服务类
 * 作为应用程序与数据库适配器之间的接口
 */
export class DatabaseService {
  private static instance: DatabaseService;
  private adapter: DatabaseInterface | null = null;
  private config: DatabaseConfig | null = null;

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * 初始化数据库服务
   * @param config 数据库配置
   */
  public initialize(config: DatabaseConfig): void {
    this.config = config;
    const factory = DatabaseFactory.getInstance();
    this.adapter = factory.createAdapter(config);
    console.log(`[DatabaseService] Initialized with ${config.type} adapter`);
  }

  /**
   * 获取数据库适配器
   */
  public getAdapter(): DatabaseInterface {
    if (!this.adapter) {
      throw new Error(
        '[DatabaseService] Database adapter not initialized. Call initialize() first.'
      );
    }
    return this.adapter;
  }

  /**
   * 执行单个 SQL 查询
   * @param sql SQL 查询或 InStatement 对象
   */
  public async executeQuery(sql: string | InStatement): Promise<ResultSet> {
    return this.getAdapter().executeQuery(sql);
  }

  /**
   * 在事务中执行一批 SQL 语句
   * @param statements InStatement 对象数组
   * @param mode 事务模式
   */
  public async batchStatements(
    statements: InStatement[],
    mode?: TransactionMode
  ): Promise<ResultSet[] | null> {
    return this.getAdapter().batchStatements(statements, mode);
  }

  /**
   * 执行多语句 SQL 脚本
   * @param sqlScript 包含多个语句的 SQL 脚本
   */
  public async executeScript(sqlScript: string): Promise<void> {
    return this.getAdapter().executeScript(sqlScript);
  }

  /**
   * 从指定表中获取所有行
   * @param tableName 表名
   * @param limit 可选的行数限制
   */
  public async fetchAllFromTable(tableName: string, limit?: number): Promise<any[]> {
    return this.getAdapter().fetchAllFromTable(tableName, limit);
  }

  /**
   * 获取数据库类型
   */
  public getDatabaseType(): string {
    return this.getAdapter().getDatabaseType();
  }

  /**
   * 获取当前配置
   */
  public getConfig(): DatabaseConfig | null {
    return this.config;
  }

  /**
   * 初始化数据库
   * @param options 初始化选项
   */
  public async initializeDatabase(options: DatabaseInitOptions = {}): Promise<void> {
    if (!this.adapter) {
      throw new Error(
        '[DatabaseService] Database adapter not initialized. Call initialize() first.'
      );
    }

    const initializer = new DatabaseInitializer(this, options);
    await initializer.initialize();
  }

  /**
   * 关闭数据库连接
   */
  public async close(): Promise<void> {
    if (this.adapter) {
      await this.adapter.close();
      this.adapter = null;
      console.log('[DatabaseService] Database connection closed');
    }
  }
}
