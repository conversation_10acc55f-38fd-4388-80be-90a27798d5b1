/**
 * Quiz系统相关的React Hooks
 * 遵循useHybridData架构模式，基于统一类型定义
 */

import { trpc } from '@/lib/trpc';
import { Services } from '@/services';
import { useCallback, useEffect, useState } from 'react';
import { useHybridData } from './useHybridData';

// 使用统一类型定义
import type {
  QuizPack,
  QuizSession,
  QuizAnswer,
  QuizResult
} from '@/types/schema/base';

interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * 获取Quiz包列表 - 混合数据模式
 */
export function useQuizPacks(options?: {
  category?: string;
  difficultyLevel?: number;
  limit?: number;
  offset?: number;
}) {
  return useHybridData(
    'quizPacks',
    async () => {
      // 在线获取
      const result = await trpc.getQuizPacks.query(options || {});
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch quiz packs from server');
      }
      return result.data;
    },
    async () => {
      // 离线获取
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.getAll();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch quiz packs from local storage');
      }
      return result.data;
    },
    {
      enableOnlineFirst: true,
      enableOfflineFallback: true,
      enableAutoSync: true,
      syncInterval: 10 * 60 * 1000, // 10分钟同步一次
    }
  );
}

/**
 * 获取推荐Quiz包
 */
export function useRecommendedQuizPacks() {
  return useHybridData(
    'recommendedQuizPacks',
    async () => {
      const result = await trpc.getRecommendedQuizPacks.query();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch recommended quiz packs');
      }
      return result.data;
    },
    async () => {
      // 离线回退：获取featured的Quiz包
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.getFeaturedQuizPacks();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch featured quiz packs');
      }
      return result.data;
    }
  );
}

/**
 * 创建Quiz会话
 */
export function useCreateQuizSession() {
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutateAsync = useCallback(async (input: { packId: string; userId: string }) => {
    setIsCreating(true);
    setError(null);

    try {
      // 优先尝试在线创建
      try {
        const result = await trpc.createQuizSession.mutate(input);
        if (result.success) {
          return result;
        }
        throw new Error(result.error || 'Failed to create session online');
      } catch (onlineError) {
        console.warn('Online session creation failed, trying offline:', onlineError);

        // 离线回退
        const quizSessionService = await Services.quizSession();
        const result = await quizSessionService.createSession(input.packId, input.userId);

        if (!result.success) {
          throw new Error(result.error || 'Failed to create session offline');
        }

        return result;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setIsCreating(false);
    }
  }, []);

  return {
    mutateAsync,
    isPending: isCreating,
    isSuccess: !isCreating && !error,
    isError: !!error,
    error,
    data: null // 可以根据需要存储最后的成功结果
  };
}

/**
 * 获取当前问题数据
 * @deprecated 建议使用 useQuizSession 替代
 */
export function useCurrentQuestion(sessionId: string) {
  return useHybridData(
    `currentQuestion_${sessionId}`,
    async () => {
      const result = await trpc.getCurrentQuestion.query({ session_id: sessionId });
      if (!result.success) {
        throw new Error(result.error || 'Failed to get current question');
      }
      return result.data;
    },
    async () => {
      // 离线获取当前问题 - 使用版本服务
      try {
        const quizSessionService = await Services.quizSession();
        const result = await quizSessionService.getCurrentQuestion(sessionId);
        if (!result.success) {
          throw new Error(result.error || 'Failed to get current question offline');
        }
        return result.data;
      } catch (serviceError) {
        console.error('[useCurrentQuestion] QuizSessionService may be deprecated:', serviceError);
        console.warn('[useCurrentQuestion] Consider using useQuizSession instead');
        throw new Error('QuizSessionService is deprecated. Please use useQuizSession.');
      }
    },
    {
      enableOnlineFirst: true,
      enableOfflineFallback: true,
      enableAutoSync: false, // 问题数据不需要自动同步
    }
  );
}

/**
 * 提交答案
 */
export function useSubmitAnswer() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutateAsync = useCallback(async (input: {
    session_id: string;
    question_id: string;
    selected_option_ids: string[];
    answer_value: string;
    answer_text?: string;
    confidence_score?: number;
    response_time_ms?: number;
  }) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // 优先尝试在线提交
      try {
        const result = await trpc.submitAnswer.mutate(input);
        if (result.success) {
          return result;
        }
        throw new Error(result.error || 'Failed to submit answer online');
      } catch (onlineError) {
        console.warn('Online answer submission failed, trying offline:', onlineError);

        // 离线回退
        const quizAnswerService = await Services.quizAnswer();
        const result = await quizAnswerService.saveAnswer({
          session_id: input.session_id,
          question_id: input.question_id,
          session_presentation_config_id: null,
          selected_option_ids: input.selected_option_ids,
          answer_value: input.answer_value,
          answer_text: input.answer_text,
          confidence_score: input.confidence_score,
          response_time_ms: input.response_time_ms,
          answered_at: new Date()
        });

        if (!result.success) {
          throw new Error(result.error || 'Failed to submit answer offline');
        }

        return result;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  return {
    mutateAsync,
    isPending: isSubmitting,
    isSuccess: !isSubmitting && !error,
    isError: !!error,
    error,
    data: null
  };
}

/**
 * 获取Quiz结果
 */
export function useQuizResult(sessionId: string) {
  return useHybridData(
    `quizResult_${sessionId}`,
    async () => {
      const result = await trpc.getQuizResult.query({ session_id: sessionId });
      if (!result.success) {
        throw new Error(result.error || 'Failed to get quiz result');
      }
      return result.data;
    },
    async () => {
      // 离线获取结果
      const quizResultService = await Services.quizResult();
      const result = await quizResultService.getBySessionId(sessionId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to get quiz result offline');
      }
      return result.data;
    }
  );
}

/**
 * 情绪轮盘Quiz专用Hook
 */
export function useEmotionWheelQuiz() {
  const createSession = useCreateQuizSession();

  const startEmotionWheelQuiz = useCallback(async (userId: string, packId?: string) => {
    try {
      // 如果没有指定packId，获取第一个情绪轮盘Quiz包
      let targetPackId = packId;

      if (!targetPackId) {
        const quizPackService = await Services.quizPack();
        const result = await quizPackService.getQuizPacksByType('emotion_wheel');

        if (result.success && result.data && result.data.length > 0) {
          targetPackId = result.data[0].id;
        } else {
          throw new Error('No emotion wheel quiz pack available');
        }
      }

      return await createSession.mutateAsync({
        packId: targetPackId,
        userId
      });
    } catch (error) {
      console.error('Failed to start emotion wheel quiz:', error);
      throw error;
    }
  }, [createSession]);

  return {
    startEmotionWheelQuiz,
    isCreating: createSession.isPending,
    error: createSession.error
  };
}

/**
 * 中医评估Quiz专用Hook
 */
export function useTCMAssessmentQuiz() {
  const createSession = useCreateQuizSession();

  const startTCMAssessment = useCallback(async (userId: string, packId?: string) => {
    try {
      // 如果没有指定packId，获取第一个中医评估Quiz包
      let targetPackId = packId;

      if (!targetPackId) {
        const quizPackService = await Services.quizPack();
        const result = await quizPackService.getQuizPacksByType('tcm_assessment');

        if (result.success && result.data && result.data.length > 0) {
          targetPackId = result.data[0].id;
        } else {
          throw new Error('No TCM assessment quiz pack available');
        }
      }

      return await createSession.mutateAsync({
        packId: targetPackId,
        userId
      });
    } catch (error) {
      console.error('Failed to start TCM assessment:', error);
      throw error;
    }
  }, [createSession]);

  return {
    startTCMAssessment,
    isCreating: createSession.isPending,
    error: createSession.error
  };
}

/**
 * Quiz历史记录Hook
 */
export function useQuizHistory(userId: string, limit: number = 20) {
  return useHybridData(
    `quizHistory_${userId}`,
    async () => {
      const result = await trpc.getUserSessions.query({ user_id: userId, limit });
      if (!result.success) {
        throw new Error(result.error || 'Failed to get user sessions');
      }
      return result.data;
    },
    async () => {
      // 离线获取用户会话
      const quizSessionService = await Services.quizSession();
      const result = await quizSessionService.getUserSessions(userId, limit);
      if (!result.success) {
        throw new Error(result.error || 'Failed to get user sessions offline');
      }
      return result.data;
    },
    {
      enableOnlineFirst: true,
      enableOfflineFallback: true,
      enableAutoSync: true,
      syncInterval: 5 * 60 * 1000, // 5分钟同步一次
    }
  );
}
