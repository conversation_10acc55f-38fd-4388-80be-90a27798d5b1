import { createClient } from '@libsql/client';
// Singleton client instance
let clientInstance = null;
/**
 * Initializes and returns a singleton Turso client instance.
 * @returns {Client} The Turso client.
 * @throws {Error} If Turso DB URL or Auth Token is not configured.
 */
export const getTursoClient = () => {
  if (clientInstance) return clientInstance;
  // Get environment variables from Cloudflare Pages environment
  const TURSO_DB_URL = process.env.TURSO_DB_URL;
  const TURSO_AUTH_TOKEN = process.env.TURSO_AUTH_TOKEN;
  // Validate environment variables
  if (!TURSO_DB_URL) {
    throw new Error(
      'Turso database URL is not configured. Please set TURSO_DB_URL in your environment variables.'
    );
  }
  if (!TURSO_AUTH_TOKEN) {
    throw new Error(
      'Turso auth token is not configured. Please set TURSO_AUTH_TOKEN in your environment variables.'
    );
  }
  try {
    // Create and store the client instance
    clientInstance = createClient({
      url: TURSO_DB_URL,
      authToken: TURSO_AUTH_TOKEN,
    });
    console.log('[TursoService] Turso client initialized successfully.');
    return clientInstance;
  } catch (error) {
    console.error('[TursoService] Failed to initialize Turso client:', error);
    throw error; // Re-throw the error to be handled by the caller
  }
};
/**
 * Executes a single SQL statement against the Turso database.
 * @param {string | InStatement} sql The SQL statement or an InStatement object.
 * @returns {Promise<ResultSet>} A promise that resolves with the query result.
 * @throws {Error} If the client is not initialized or query fails.
 */
export const executeTursoQuery = async (sql) => {
  const client = getTursoClient();
  const sqlString = typeof sql === 'string' ? sql : sql.sql;
  try {
    const result = await client.execute(sql);
    // Log query execution (truncate long queries for better logs)
    const truncatedSql = sqlString.length > 100 ? `${sqlString.substring(0, 100)}...` : sqlString;
    console.log(
      `[TursoService] Executed query: ${truncatedSql} (${result.rowsAffected} rows affected)`
    );
    return result;
  } catch (error) {
    console.error(`[TursoService] Error executing query: ${sqlString}`, error);
    throw error;
  }
};
/**
 * Executes a batch of SQL statements in a transaction against the Turso database.
 * @param {InStatement[]} statements An array of InStatement objects.
 * @param {'deferred' | 'write' | 'read'} [mode='write'] The transaction mode.
 * @returns {Promise<ResultSet[] | null>} A promise that resolves with an array of query results or null if transaction failed.
 * @throws {Error} If the client is not initialized or transaction fails.
 */
export const batchTursoStatements = async (statements, mode = 'write') => {
  if (!statements || statements.length === 0) {
    console.warn('[TursoService] No statements provided for batch execution');
    return [];
  }
  const client = getTursoClient();
  let tx = null;
  try {
    // Start transaction
    tx = await client.transaction(mode);
    // Execute batch
    const results = await tx.batch(statements);
    // Commit transaction
    await tx.commit();
    console.log(`[TursoService] Successfully executed batch of ${statements.length} statements`);
    return results;
  } catch (error) {
    console.error('[TursoService] Error in batch transaction:', error);
    // Rollback transaction if it exists
    if (tx) {
      try {
        await tx.rollback();
        console.log('[TursoService] Transaction rolled back successfully');
      } catch (rollbackError) {
        console.error('[TursoService] Failed to rollback transaction:', rollbackError);
      }
    }
    throw error;
  }
};
/**
 * Executes a multi-statement SQL script against the Turso database.
 * @param {string} sqlScript The SQL script containing multiple statements separated by semicolons.
 * @returns {Promise<void>} A promise that resolves when the script is executed.
 * @throws {Error} If the client is not initialized or script execution fails.
 */
export const executeSqlScript = async (sqlScript) => {
  if (!sqlScript || sqlScript.trim() === '') {
    console.warn('[TursoService] Empty SQL script provided');
    return;
  }
  const client = getTursoClient();
  try {
    // Count approximate number of statements by counting semicolons
    const statementCount = (sqlScript.match(/;/g) || []).length + 1;
    await client.executeMultiple(sqlScript);
    console.log(
      `[TursoService] Successfully executed SQL script with ~${statementCount} statements`
    );
  } catch (error) {
    console.error('[TursoService] Failed to execute SQL script:', error);
    throw error;
  }
};
/**
 * Fetches all rows from a specified table in Turso.
 * @param {string} tableName The name of the table to fetch from.
 * @param {number} [limit] Optional limit on the number of rows to return.
 * @returns {Promise<any[]>} A promise that resolves with an array of rows.
 * @throws {Error} If the table name is invalid or the query fails.
 */
export const fetchAllFromTursoTable = async (tableName, limit) => {
  // Validate table name to prevent SQL injection
  if (!/^[a-zA-Z0-9_]+$/.test(tableName)) {
    throw new Error(`Invalid table name: ${tableName}`);
  }
  // Build query with optional limit
  const query = limit ? `SELECT * FROM ${tableName} LIMIT ${limit}` : `SELECT * FROM ${tableName}`;
  try {
    const result = await executeTursoQuery(query);
    console.log(`[TursoService] Fetched ${result.rows.length} rows from table '${tableName}'`);
    return result.rows;
  } catch (error) {
    console.error(`[TursoService] Failed to fetch data from table '${tableName}':`, error);
    throw error;
  }
};
