/**
 * Quiz包管理组件
 * 用于管理Quiz包的创建、编辑、删除等操作
 */

import React, { useState } from 'react';
import { QuizPack } from '@/types/schema/base';
import { Services } from '@/services';
import { toast } from 'sonner';
import { useLanguage } from '@/contexts/LanguageContext';

interface QuizPackManagerProps {
  quizPacks: QuizPack[];
  onPackSelect: (pack: QuizPack) => void;
  onDataUpdate: () => void;
}

interface QuizPackFormData {
  name: string;
  description: string;
  quiz_type: string;
  category: string;
  difficulty_level: number;
  estimated_duration_minutes: number;
  is_active: boolean;
  is_public: boolean;
}

const QuizPackManager: React.FC<QuizPackManagerProps> = ({
  quizPacks,
  onPackSelect,
  onDataUpdate
}) => {
  const { t } = useLanguage();
  const [isCreating, setIsCreating] = useState(false);
  const [editingPack, setEditingPack] = useState<QuizPack | null>(null);
  const [formData, setFormData] = useState<QuizPackFormData>({
    name: '',
    description: '',
    quiz_type: 'emotion_wheel',
    category: 'emotion',
    difficulty_level: 1,
    estimated_duration_minutes: 5,
    is_active: true,
    is_public: false
  });

  /**
   * 重置表单
   */
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      quiz_type: 'emotion_wheel',
      category: 'emotion',
      difficulty_level: 1,
      estimated_duration_minutes: 5,
      is_active: true,
      is_public: false
    });
    setIsCreating(false);
    setEditingPack(null);
  };

  /**
   * 开始创建新包
   */
  const startCreating = () => {
    resetForm();
    setIsCreating(true);
  };

  /**
   * 开始编辑包
   */
  const startEditing = (pack: QuizPack) => {
    setFormData({
      name: pack.name,
      description: pack.description || '',
      quiz_type: pack.quiz_type,
      category: pack.category || 'emotion',
      difficulty_level: pack.difficulty_level || 1,
      estimated_duration_minutes: pack.estimated_duration_minutes || 5,
      is_active: pack.is_active,
      is_public: pack.is_public || false
    });
    setEditingPack(pack);
    setIsCreating(true);
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const quizPackService = await Services.quizPack();
      
      if (editingPack) {
        // 更新现有包
        const result = await quizPackService.update(editingPack.id, formData);
        if (result.success) {
          toast.success('Quiz包更新成功');
          onDataUpdate();
          resetForm();
        } else {
          throw new Error(result.error || 'Update failed');
        }
      } else {
        // 创建新包
        const result = await quizPackService.create({
          ...formData,
          created_by: 'user' // 可以从用户上下文获取
        });
        if (result.success) {
          toast.success('Quiz包创建成功');
          onDataUpdate();
          resetForm();
        } else {
          throw new Error(result.error || 'Create failed');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Operation failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 删除包
   */
  const handleDelete = async (pack: QuizPack) => {
    if (!confirm(`确定要删除Quiz包"${pack.name}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.delete(pack.id);
      
      if (result.success) {
        toast.success('Quiz包删除成功');
        onDataUpdate();
      } else {
        throw new Error(result.error || 'Delete failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Delete failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 复制包
   */
  const handleDuplicate = async (pack: QuizPack) => {
    try {
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.create({
        name: `${pack.name} (副本)`,
        description: pack.description,
        quiz_type: pack.quiz_type,
        category: pack.category,
        difficulty_level: pack.difficulty_level,
        estimated_duration_minutes: pack.estimated_duration_minutes,
        is_active: false, // 副本默认不激活
        is_public: false,
        created_by: 'user'
      });
      
      if (result.success) {
        toast.success('Quiz包复制成功');
        onDataUpdate();
      } else {
        throw new Error(result.error || 'Duplicate failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Duplicate failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 渲染包列表
   */
  const renderPackList = () => (
    <div className="quiz-pack-list">
      {quizPacks.map(pack => (
        <div key={pack.id} className="quiz-pack-card">
          <div className="pack-header">
            <h3 className="pack-name" onClick={() => onPackSelect(pack)}>
              {pack.name}
            </h3>
            <div className="pack-badges">
              {pack.is_active && <span className="badge active">活跃</span>}
              {pack.is_public && <span className="badge public">公开</span>}
              <span className="badge type">{pack.quiz_type}</span>
            </div>
          </div>
          
          <p className="pack-description">{pack.description}</p>
          
          <div className="pack-meta">
            <span>类别: {pack.category}</span>
            <span>难度: {pack.difficulty_level}/5</span>
            <span>预计时长: {pack.estimated_duration_minutes}分钟</span>
          </div>
          
          <div className="pack-actions">
            <button 
              className="action-button primary"
              onClick={() => onPackSelect(pack)}
            >
              管理问题
            </button>
            <button 
              className="action-button secondary"
              onClick={() => startEditing(pack)}
            >
              编辑
            </button>
            <button 
              className="action-button secondary"
              onClick={() => handleDuplicate(pack)}
            >
              复制
            </button>
            <button 
              className="action-button danger"
              onClick={() => handleDelete(pack)}
            >
              删除
            </button>
          </div>
        </div>
      ))}
    </div>
  );

  /**
   * 渲染创建/编辑表单
   */
  const renderForm = () => (
    <div className="quiz-pack-form-overlay">
      <div className="quiz-pack-form">
        <h3>{editingPack ? '编辑Quiz包' : '创建新Quiz包'}</h3>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label>包名称 *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              required
              placeholder="输入Quiz包名称"
            />
          </div>
          
          <div className="form-group">
            <label>描述</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="输入Quiz包描述"
              rows={3}
            />
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>Quiz类型 *</label>
              <select
                value={formData.quiz_type}
                onChange={(e) => setFormData({...formData, quiz_type: e.target.value})}
                required
              >
                <option value="emotion_wheel">情绪轮盘</option>
                <option value="tcm_assessment">中医评估</option>
                <option value="survey">调查问卷</option>
                <option value="personality">性格测试</option>
                <option value="mood_tracker">情绪追踪</option>
              </select>
            </div>
            
            <div className="form-group">
              <label>类别</label>
              <select
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
              >
                <option value="emotion">情绪</option>
                <option value="health">健康</option>
                <option value="psychology">心理</option>
                <option value="education">教育</option>
                <option value="entertainment">娱乐</option>
              </select>
            </div>
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>难度等级 (1-5)</label>
              <input
                type="number"
                min="1"
                max="5"
                value={formData.difficulty_level}
                onChange={(e) => setFormData({...formData, difficulty_level: parseInt(e.target.value)})}
              />
            </div>
            
            <div className="form-group">
              <label>预计时长 (分钟)</label>
              <input
                type="number"
                min="1"
                value={formData.estimated_duration_minutes}
                onChange={(e) => setFormData({...formData, estimated_duration_minutes: parseInt(e.target.value)})}
              />
            </div>
          </div>
          
          <div className="form-group">
            <div className="checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                />
                激活状态
              </label>
              
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.is_public}
                  onChange={(e) => setFormData({...formData, is_public: e.target.checked})}
                />
                公开可见
              </label>
            </div>
          </div>
          
          <div className="form-actions">
            <button type="button" onClick={resetForm} className="action-button secondary">
              取消
            </button>
            <button type="submit" className="action-button primary">
              {editingPack ? '更新' : '创建'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );

  return (
    <div className="quiz-pack-manager">
      <div className="manager-header">
        <h2>Quiz包管理</h2>
        <button className="action-button primary" onClick={startCreating}>
          创建新Quiz包
        </button>
      </div>
      
      {quizPacks.length === 0 ? (
        <div className="empty-state">
          <p>暂无Quiz包，点击上方按钮创建第一个Quiz包</p>
        </div>
      ) : (
        renderPackList()
      )}
      
      {isCreating && renderForm()}
    </div>
  );
};

export { QuizPackManager };
