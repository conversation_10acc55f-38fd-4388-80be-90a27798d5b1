/**
 * Quiz问题服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 */

import { BaseService } from '../base/BaseService';
import { QuizQuestionRepository } from './QuizQuestionRepository';
import { QuizQuestion } from '../../types/schema/base';
import { CreateQuizQuestionInput, UpdateQuizQuestionInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export interface QuizQuestionStats {
  total_questions: number;
  unique_types: number;
  tier_levels: number;
  question_groups: number;
  conditional_questions: number;
  avg_estimated_time: number;
}

export interface QuestionNavigationResult {
  current_question: QuizQuestion;
  next_question: QuizQuestion | null;
  previous_question: QuizQuestion | null;
  progress: {
    current_index: number;
    total_questions: number;
    completion_percentage: number;
  };
}

export class QuizQuestionService extends BaseService<
  QuizQuestion,
  CreateQuizQuestionInput,
  UpdateQuizQuestionInput
> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new QuizQuestionRepository(db);
    super(repository);
  }

  /**
   * 创建新的Quiz问题
   */
  async createQuestion(input: CreateQuizQuestionInput): Promise<ServiceResult<QuizQuestion>> {
    try {
      // 验证输入
      await this.validateCreate(input);

      // 如果没有指定顺序，自动分配
      if (input.question_order === undefined) {
        const maxOrder = await (this.repository as QuizQuestionRepository).getMaxQuestionOrder(input.pack_id);
        input.question_order = maxOrder + 1;
      }

      // 调用Repository创建问题
      const question = await this.repository.create(input);

      // 发射业务事件
      this.emit('questionCreated', question);

      return this.createSuccessResult(question);
    } catch (error) {
      return this.createErrorResult('Failed to create quiz question', error);
    }
  }

  /**
   * 获取Quiz包的所有问题
   */
  async getPackQuestions(packId: string): Promise<ServiceResult<QuizQuestion[]>> {
    try {
      const questions = await (this.repository as QuizQuestionRepository).findByPackId(packId);
      return this.createSuccessResult(questions);
    } catch (error) {
      return this.createErrorResult('Failed to get pack questions', error);
    }
  }

  /**
   * 根据层级获取问题
   */
  async getQuestionsByTierLevel(packId: string, tierLevel: number): Promise<ServiceResult<QuizQuestion[]>> {
    try {
      if (tierLevel < 1) {
        throw new Error('Tier level must be at least 1');
      }

      const questions = await (this.repository as QuizQuestionRepository).findByTierLevel(packId, tierLevel);
      return this.createSuccessResult(questions);
    } catch (error) {
      return this.createErrorResult('Failed to get questions by tier level', error);
    }
  }

  /**
   * 根据问题组获取问题
   */
  async getQuestionsByGroup(packId: string, questionGroup: string): Promise<ServiceResult<QuizQuestion[]>> {
    try {
      if (!questionGroup || questionGroup.trim().length === 0) {
        throw new Error('Question group cannot be empty');
      }

      const questions = await (this.repository as QuizQuestionRepository).findByGroup(packId, questionGroup.trim());
      return this.createSuccessResult(questions);
    } catch (error) {
      return this.createErrorResult('Failed to get questions by group', error);
    }
  }

  /**
   * 根据问题类型获取问题
   */
  async getQuestionsByType(packId: string, questionType: string): Promise<ServiceResult<QuizQuestion[]>> {
    try {
      if (!questionType || questionType.trim().length === 0) {
        throw new Error('Question type cannot be empty');
      }

      const questions = await (this.repository as QuizQuestionRepository).findByType(packId, questionType.trim());
      return this.createSuccessResult(questions);
    } catch (error) {
      return this.createErrorResult('Failed to get questions by type', error);
    }
  }

  /**
   * 获取问题导航信息
   */
  async getQuestionNavigation(packId: string, currentQuestionId: string): Promise<ServiceResult<QuestionNavigationResult>> {
    try {
      // 获取当前问题
      const currentQuestion = await this.repository.findById(currentQuestionId);
      if (!currentQuestion) {
        throw new Error('Current question not found');
      }

      // 获取所有问题以计算进度
      const allQuestions = await (this.repository as QuizQuestionRepository).findByPackId(packId);
      const currentIndex = allQuestions.findIndex(q => q.id === currentQuestionId);

      if (currentIndex === -1) {
        throw new Error('Current question not found in pack');
      }

      // 获取下一个和上一个问题
      const nextQuestion = await (this.repository as QuizQuestionRepository).findNextQuestion(
        packId,
        currentQuestion.question_order
      );

      const previousQuestion = await (this.repository as QuizQuestionRepository).findPreviousQuestion(
        packId,
        currentQuestion.question_order
      );

      const navigationResult: QuestionNavigationResult = {
        current_question: currentQuestion,
        next_question: nextQuestion,
        previous_question: previousQuestion,
        progress: {
          current_index: currentIndex + 1, // 1-based index
          total_questions: allQuestions.length,
          completion_percentage: Math.round(((currentIndex + 1) / allQuestions.length) * 100)
        }
      };

      return this.createSuccessResult(navigationResult);
    } catch (error) {
      return this.createErrorResult('Failed to get question navigation', error);
    }
  }

  /**
   * 获取条件分支问题
   */
  async getConditionalQuestions(parentQuestionId: string): Promise<ServiceResult<QuizQuestion[]>> {
    try {
      const questions = await (this.repository as QuizQuestionRepository).findByParentQuestionId(parentQuestionId);
      return this.createSuccessResult(questions);
    } catch (error) {
      return this.createErrorResult('Failed to get conditional questions', error);
    }
  }

  /**
   * 获取问题统计信息
   */
  async getQuestionStats(packId: string): Promise<ServiceResult<QuizQuestionStats>> {
    try {
      const rawStats = await (this.repository as QuizQuestionRepository).getQuestionStats(packId);

      const stats: QuizQuestionStats = {
        total_questions: rawStats.total_questions || 0,
        unique_types: rawStats.unique_types || 0,
        tier_levels: rawStats.tier_levels || 0,
        question_groups: rawStats.question_groups || 0,
        conditional_questions: rawStats.conditional_questions || 0,
        avg_estimated_time: Math.round(rawStats.avg_estimated_time || 0)
      };

      return this.createSuccessResult(stats);
    } catch (error) {
      return this.createErrorResult('Failed to get question stats', error);
    }
  }

  /**
   * 重新排序问题
   */
  async reorderQuestions(
    packId: string,
    questionOrders: Array<{ id: string; question_order: number }>
  ): Promise<ServiceResult<boolean>> {
    try {
      // 验证所有问题都属于指定的包
      const packQuestions = await (this.repository as QuizQuestionRepository).findByPackId(packId);
      const packQuestionIds = new Set(packQuestions.map(q => q.id));

      for (const item of questionOrders) {
        if (!packQuestionIds.has(item.id)) {
          throw new Error(`Question ${item.id} does not belong to pack ${packId}`);
        }
        if (item.question_order < 0) {
          throw new Error('Question order must be non-negative');
        }
      }

      // 批量更新顺序
      const success = await (this.repository as QuizQuestionRepository).batchUpdateQuestionOrder(questionOrders);

      if (success) {
        this.emit('questionsReordered', { packId, questionOrders });
      }

      return this.createSuccessResult(success);
    } catch (error) {
      return this.createErrorResult('Failed to reorder questions', error);
    }
  }

  /**
   * 复制问题到另一个包
   */
  async duplicateQuestion(questionId: string, targetPackId: string): Promise<ServiceResult<QuizQuestion>> {
    try {
      // 获取原问题
      const originalQuestion = await this.repository.findById(questionId);
      if (!originalQuestion) {
        throw new Error('Original question not found');
      }

      // 获取目标包的最大顺序号
      const maxOrder = await (this.repository as QuizQuestionRepository).getMaxQuestionOrder(targetPackId);

      // 创建复制的问题数据
      const duplicateData: CreateQuizQuestionInput = {
        pack_id: targetPackId,
        question_text: `${originalQuestion.question_text} (Copy)`,
        question_text_localized: typeof originalQuestion.question_text_localized === 'string'
          ? JSON.parse(originalQuestion.question_text_localized)
          : originalQuestion.question_text_localized,
        question_type: originalQuestion.question_type,
        question_order: maxOrder + 1,
        question_group: originalQuestion.question_group,
        tier_level: originalQuestion.tier_level,
        question_config: typeof originalQuestion.question_config === 'string'
          ? JSON.parse(originalQuestion.question_config)
          : originalQuestion.question_config,
        validation_rules: typeof originalQuestion.validation_rules === 'string'
          ? JSON.parse(originalQuestion.validation_rules)
          : originalQuestion.validation_rules,
        scoring_config: typeof originalQuestion.scoring_config === 'string'
          ? JSON.parse(originalQuestion.scoring_config)
          : originalQuestion.scoring_config,
        parent_question_id: null, // 复制时清除父问题关系
        dependency_rules: typeof originalQuestion.dependency_rules === 'string'
          ? JSON.parse(originalQuestion.dependency_rules)
          : originalQuestion.dependency_rules,
        is_required: originalQuestion.is_required,
        is_active: originalQuestion.is_active,
      };

      // 创建复制的问题
      const duplicatedQuestion = await this.repository.create(duplicateData);

      this.emit('questionDuplicated', { originalQuestion, duplicatedQuestion });

      return this.createSuccessResult(duplicatedQuestion);
    } catch (error) {
      return this.createErrorResult('Failed to duplicate question', error);
    }
  }

  /**
   * 更新问题
   */
  async updateQuestion(questionId: string, updates: UpdateQuizQuestionInput): Promise<ServiceResult<QuizQuestion>> {
    try {
      // 验证更新数据
      await this.validateUpdate(updates);

      // 调用Repository更新
      const question = await this.repository.update(questionId, updates);

      // 发射业务事件
      this.emit('questionUpdated', question);

      return this.createSuccessResult(question);
    } catch (error) {
      return this.createErrorResult('Failed to update quiz question', error);
    }
  }

  /**
   * 激活/停用问题
   */
  async toggleQuestionStatus(questionId: string, isActive: boolean): Promise<ServiceResult<QuizQuestion>> {
    try {
      const question = await this.repository.update(questionId, { is_active: isActive });

      this.emit('questionStatusToggled', { question, isActive });
      return this.createSuccessResult(question);
    } catch (error) {
      return this.createErrorResult('Failed to toggle question status', error);
    }
  }

  // 实现抽象方法
  protected async validateCreate(data: CreateQuizQuestionInput): Promise<void> {
    if (!data.pack_id || data.pack_id.trim().length === 0) {
      throw new Error('Pack ID is required');
    }
    if (!data.question_text || data.question_text.trim().length === 0) {
      throw new Error('Question text is required');
    }
    if (data.question_text.length > 1000) {
      throw new Error('Question text must be less than 1000 characters');
    }
    if (!data.question_type || data.question_type.trim().length === 0) {
      throw new Error('Question type is required');
    }
    if (data.question_order !== undefined && data.question_order < 0) {
      throw new Error('Question order must be non-negative');
    }
    if (data.tier_level !== undefined && data.tier_level < 1) {
      throw new Error('Tier level must be at least 1');
    }
  }

  protected async validateUpdate(data: UpdateQuizQuestionInput): Promise<void> {
    if (data.question_text !== undefined && (!data.question_text || data.question_text.trim().length === 0)) {
      throw new Error('Question text cannot be empty');
    }
    if (data.question_text !== undefined && data.question_text.length > 1000) {
      throw new Error('Question text must be less than 1000 characters');
    }
    if (data.question_type !== undefined && (!data.question_type || data.question_type.trim().length === 0)) {
      throw new Error('Question type cannot be empty');
    }
    if (data.question_order !== undefined && data.question_order < 0) {
      throw new Error('Question order must be non-negative');
    }
    if (data.tier_level !== undefined && data.tier_level < 1) {
      throw new Error('Tier level must be at least 1');
    }
  }
}
