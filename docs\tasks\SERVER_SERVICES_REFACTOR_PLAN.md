# 服务端服务改造计划

基于对 `server/lib/services/` 目录下四个核心服务的分析，制定详细的清理和改造计划。

## 📋 现状分析

### 1. SyncService 重复文件问题
```
server/lib/services/
├── SyncService.ts    ✅ 完整版本 (454行)
└── SyncService.js    ❌ 简化版本 (277行) - 需要删除
```

**问题**:
- 存在功能重复的 TypeScript 和 JavaScript 版本
- JavaScript 版本功能不完整，缺少重要字段
- 维护成本高，容易产生不一致

**解决方案**: 删除 JavaScript 版本，保留并改造 TypeScript 版本

### 2. PaymentService.ts 架构问题
**当前状态**: 基于旧架构，功能相对完整但需要升级

**主要问题**:
- 硬编码 VIP 计划数据，应从数据库读取
- 缺少与新 `vip_plans` 表的集成
- 模拟支付处理，需要真实 Stripe 集成
- 缺少新的解锁系统支持

### 3. QuizEngineService.ts 设计问题
**当前状态**: 基于新架构设计，但实现不完整

**主要问题**:
- 数据库接口不统一 (`DatabaseInterface` vs 标准接口)
- 缺少与客户端 `QuizEngineV3` 的协调
- 冲突解决逻辑过于简化
- 缺少完整的数据验证

### 4. QuizService.ts 架构过时
**当前状态**: 基于旧架构，需要大幅改造

**主要问题**:
- 依赖旧的 `QuizEngineV2`
- 错误的导入路径，引用客户端代码
- 缺少新 Quiz 架构 (`quiz_packs`, `quiz_questions`, `quiz_question_options`) 支持
- 服务实例化方式不统一

## 🚀 改造实施计划

### 📋 **基于在线服务架构的重要更新**

根据 `src/services/online/README.md` 分析，客户端架构已简化：
- ✅ **已移除**: 客户端复杂业务服务 (`AuthService`, `CloudDataService`, `OnlineSyncService`)
- ✅ **保留**: 基础服务 (`ApiClientService`, `NetworkStatusService`)
- ✅ **新模式**: 所有业务逻辑通过 tRPC 直接调用服务端

**影响**: 服务端必须承担所有业务逻辑，客户端只负责 UI 和基础网络管理。

### 阶段 1: 清理重复文件 ✅ (已完成)

#### 任务 1.1: 删除重复的 JavaScript 文件 ✅
```bash
# 已删除的重复文件
✅ server/lib/services/SyncService.js
✅ server/lib/services/AnalyticsService.js
✅ server/lib/services/AuthService.js
✅ server/lib/services/MoodEntryService.js
✅ server/lib/services/UserManagementService.js
✅ server/lib/services/DatabaseInitializationService.js
```

#### 任务 1.2: 类型引用统一验证 ✅
- ✅ 确认服务端使用 `src/types/schema/api.ts` 统一类型
- ✅ 验证 21 个 Schema 已完成统一
- ✅ 确认 tRPC 端到端类型安全

### 阶段 2: SyncService.ts 现代化改造 (4-6小时)

#### 任务 2.1: 架构升级
```typescript
// 目标架构
class SyncService {
  // 支持新的数据表
  - quiz_sessions, quiz_answers, quiz_results
  - vip_subscriptions, user_unlocks
  - user_presentation_configs, pack_presentation_overrides
  
  // 增强的同步策略
  - 增量同步优化
  - 智能冲突解决
  - 批量操作优化
}
```

#### 任务 2.2: 新数据表支持
- 添加 Quiz 系统新表的同步逻辑
- 添加 VIP 和解锁系统的同步支持
- 添加配置系统的同步支持

#### 任务 2.3: 性能优化
- 实现真正的增量同步
- 添加批量操作支持
- 优化数据库查询

### 阶段 3: PaymentService.ts 现代化改造 (6-8小时)

#### 任务 3.1: 数据库集成
```typescript
// 从硬编码改为数据库驱动
async getVipPlans() {
  // 从 vip_plans 表读取
  const result = await executeQuery({
    sql: 'SELECT * FROM vip_plans WHERE is_active = 1 ORDER BY display_order',
    args: []
  });
  return result.rows;
}
```

#### 任务 3.2: 新解锁系统集成
- 集成 `user_unlocks` 表
- 支持多种解锁方式 (purchase, vip, achievement, gift)
- 添加批量解锁功能

#### 任务 3.3: 真实支付集成
- 集成真实的 Stripe API
- 添加支付状态跟踪
- 实现退款处理逻辑

### 阶段 4: QuizEngineService.ts 重构 (8-10小时)

#### 任务 4.1: 数据库接口统一
```typescript
// 统一数据库接口
import { executeQuery, batchStatements } from '../database/index.js';

class QuizEngineService {
  constructor() {
    // 移除自定义 DatabaseInterface
    // 使用标准数据库操作
  }
}
```

#### 任务 4.2: 与客户端协调
- 确保与客户端 `QuizEngineV3` 的数据格式一致
- 实现完整的离线数据处理
- 添加数据验证和完整性检查

#### 任务 4.3: 增强冲突解决
- 实现智能冲突检测
- 添加多种冲突解决策略
- 支持手动冲突解决

### 阶段 5: QuizService.ts 完全重写 (10-12小时)

#### 任务 5.1: 新架构迁移
```typescript
// 新架构服务依赖
import { QuizEngineV3 } from './QuizEngineService.js';  // 服务端版本
import { VipPlanService } from './VipPlanService.js';
import { UnlockService } from './UnlockService.js';

class QuizService {
  // 基于新的 quiz_packs 架构
  // 支持所有 Quiz 类型
  // 集成 VIP 和解锁系统
}
```

#### 任务 5.2: 服务端专用功能
- 实现服务端特有的 Quiz 逻辑
- 添加数据分析和统计功能
- 支持批量 Quiz 操作

#### 任务 5.3: API 端点优化
- 优化 Quiz 相关的 tRPC 端点
- 添加缓存策略
- 实现分页和过滤

### 阶段 6: 新服务创建 (6-8小时)

#### 任务 6.1: VipPlanService.ts (服务端版本)
```typescript
// 服务端 VIP 计划管理
class VipPlanService {
  async getAvailablePlans(): Promise<VipPlan[]>
  async validatePlanAccess(userId: string, planId: string): Promise<boolean>
  async processSubscription(subscriptionData: any): Promise<ServiceResult>
}
```

#### 任务 6.2: UnlockService.ts (服务端版本)
```typescript
// 服务端解锁管理
class UnlockService {
  async processUnlock(unlockData: any): Promise<ServiceResult>
  async validateUnlockAccess(userId: string, contentId: string): Promise<boolean>
  async batchUnlock(userId: string, unlocks: any[]): Promise<ServiceResult>
}
```

#### 任务 6.3: ConfigSyncService.ts
```typescript
// 配置同步专用服务
class ConfigSyncService {
  async syncUserConfigs(userId: string): Promise<ServiceResult>
  async syncPresentationConfigs(userId: string): Promise<ServiceResult>
  async mergeConfigConflicts(conflicts: any[]): Promise<ServiceResult>
}
```

## 📊 改造优先级

### P0 - 立即执行 (第1-2周)
1. **清理重复文件** - 避免维护混乱
2. **SyncService.ts 改造** - 核心同步功能
3. **PaymentService.ts 数据库集成** - VIP 功能基础

### P1 - 第二批 (第3-4周)
4. **QuizEngineService.ts 重构** - Quiz 系统核心
5. **新服务创建** - VIP 和解锁系统

### P2 - 第三批 (第5-6周)
6. **QuizService.ts 重写** - Quiz API 优化
7. **ConfigSyncService.ts 创建** - 配置同步优化

## 🧪 测试策略

### 单元测试
```typescript
// 每个服务的单元测试
describe('SyncService', () => {
  it('should sync new quiz data correctly')
  it('should handle sync conflicts properly')
  it('should support incremental sync')
})

describe('PaymentService', () => {
  it('should process VIP purchase correctly')
  it('should handle payment failures gracefully')
  it('should unlock VIP content properly')
})
```

### 集成测试
- 服务间协作测试
- 数据库操作测试
- tRPC 端点测试

### 端到端测试
- 完整同步流程测试
- 支付流程测试
- Quiz 会话流程测试

## 📈 成功指标

### 技术指标
- [ ] 代码重复率 < 5%
- [ ] 单元测试覆盖率 > 90%
- [ ] 集成测试通过率 100%
- [ ] 性能提升 > 30%

### 业务指标
- [ ] 同步成功率 > 99%
- [ ] 支付成功率 > 95%
- [ ] Quiz 会话完成率提升
- [ ] 系统稳定性 > 99.5%

## 🔄 迁移策略

### 渐进式迁移
1. **并行运行**: 新旧服务并行运行
2. **逐步切换**: 按功能模块逐步切换
3. **回滚准备**: 保留回滚能力
4. **监控验证**: 实时监控切换效果

### 数据迁移
1. **备份现有数据**
2. **执行 schema 迁移**
3. **数据格式转换**
4. **验证数据完整性**

这个改造计划将确保服务端服务与新架构完全对齐，提供更好的性能和可维护性。
