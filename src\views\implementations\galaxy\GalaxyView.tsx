/**
 * 星系视图
 * 使用星系布局显示情绪
 */

import type { Emotion, ContentDisplayMode, ViewConfig, SkinConfig, GalaxyLayout } from '@/types';
import { BaseEmotionView } from '@/views/base/BaseEmotionView';
import type React from 'react';
import { GalaxyComponent } from '@/views/components/galaxy/GalaxyComponent';

/**
 * 星系视图类
 * 使用星系布局显示情绪
 */
export class GalaxyView extends BaseEmotionView {
  protected layout: GalaxyLayout = 'spiral';

  /**
   * 构造函数
   * @param contentType 内容显示模式
   * @param skinConfig 皮肤配置
   * @param layout 星系布局
   */
  constructor(
    contentType: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: GalaxyLayout = 'spiral'
  ) {
    super('galaxy', contentType, skinConfig);
    this.layout = layout;
  }

  /**
   * 获取星系布局
   * @returns 星系布局
   */
  getLayout(): GalaxyLayout {
    return this.layout;
  }

  /**
   * 设置星系布局
   * @param layout 新的星系布局
   */
  setLayout(layout: GalaxyLayout): void {
    this.layout = layout;
  }

  /**
   * 渲染星系
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  renderGalaxy(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    // 从配置中获取额外属性
    const onBack = config?.onBack;
    const selectedPath = config?.selectedPath;

    return (
      <GalaxyComponent
        emotions={emotions}
        tierLevel={tierLevel}
        contentDisplayMode={this.contentDisplayMode}
        skinConfig={this.skinConfig}
        onSelect={onSelect}
        layout={this.layout}
        onBack={onBack}
        selectedPath={selectedPath}
      />
    );
  }

  /**
   * 渲染视图
   * 实现基类的抽象方法
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  render(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    return this.renderGalaxy(emotions, tierLevel, onSelect, config);
  }
}
