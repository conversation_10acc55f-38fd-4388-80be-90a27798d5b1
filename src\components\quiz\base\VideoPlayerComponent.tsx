/**
 * Quiz视频播放器组件
 * 支持多种中医文化样式的视频播放器组件
 */

import React, { useState, useRef, useEffect } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { VideoPlayerComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type VideoPlayerComponentConfig = z.infer<typeof VideoPlayerComponentConfigSchema>;

export interface VideoPlayerComponentProps extends BaseQuizComponentProps<VideoPlayerComponentConfig> {
  videoUrl: string;
  posterUrl?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  disabled?: boolean;
}

interface VideoPlayerComponentState extends ComponentState {
  is_playing: boolean;
  is_paused: boolean;
  current_time: number;
  duration: number;
  volume: number;
  is_muted: boolean;
  is_buffering: boolean;
  is_fullscreen: boolean;
  has_error: boolean;
  error_message: string | null;
  show_controls: boolean;
}

/**
 * 视频播放器组件类
 */
export class VideoPlayerComponent extends BaseQuizComponent<
  VideoPlayerComponentConfig,
  VideoPlayerComponentProps,
  VideoPlayerComponentState
> {
  private videoRef = React.createRef<HTMLVideoElement>();
  private containerRef = React.createRef<HTMLDivElement>();
  private controlsTimeoutId?: number;

  extractConfig(props: VideoPlayerComponentProps): VideoPlayerComponentConfig {
    return props.config;
  }

  getInitialState(): VideoPlayerComponentState {
    return {
      is_loading: true,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      is_playing: false,
      is_paused: false,
      current_time: 0,
      duration: 0,
      volume: 1,
      is_muted: false,
      is_buffering: false,
      is_fullscreen: false,
      has_error: false,
      error_message: null,
      show_controls: true
    };
  }

  componentDidMount(): void {
    const video = this.videoRef.current;
    if (video) {
      video.addEventListener('loadstart', this.handleLoadStart);
      video.addEventListener('loadedmetadata', this.handleLoadedMetadata);
      video.addEventListener('canplay', this.handleCanPlay);
      video.addEventListener('play', this.handlePlay);
      video.addEventListener('pause', this.handlePause);
      video.addEventListener('ended', this.handleEnded);
      video.addEventListener('timeupdate', this.handleTimeUpdate);
      video.addEventListener('volumechange', this.handleVolumeChange);
      video.addEventListener('waiting', this.handleWaiting);
      video.addEventListener('error', this.handleError);
      video.addEventListener('click', this.handleVideoClick);
    }

    // 全屏事件监听
    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);
  }

  componentWillUnmount(): void {
    const video = this.videoRef.current;
    if (video) {
      video.removeEventListener('loadstart', this.handleLoadStart);
      video.removeEventListener('loadedmetadata', this.handleLoadedMetadata);
      video.removeEventListener('canplay', this.handleCanPlay);
      video.removeEventListener('play', this.handlePlay);
      video.removeEventListener('pause', this.handlePause);
      video.removeEventListener('ended', this.handleEnded);
      video.removeEventListener('timeupdate', this.handleTimeUpdate);
      video.removeEventListener('volumechange', this.handleVolumeChange);
      video.removeEventListener('waiting', this.handleWaiting);
      video.removeEventListener('error', this.handleError);
      video.removeEventListener('click', this.handleVideoClick);
    }

    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);

    if (this.controlsTimeoutId) {
      clearTimeout(this.controlsTimeoutId);
    }
  }

  // 视频事件处理
  private handleLoadStart = (): void => {
    this.setState({ is_loading: true, is_buffering: true });
  };

  private handleLoadedMetadata = (): void => {
    const video = this.videoRef.current;
    if (video) {
      this.setState({ 
        duration: video.duration,
        is_loading: false,
        is_buffering: false
      });
    }
  };

  private handleCanPlay = (): void => {
    this.setState({ is_loading: false, is_buffering: false });
  };

  private handlePlay = (): void => {
    this.setState({ is_playing: true, is_paused: false });
    this.props.onPlay?.();
    this.hideControlsAfterDelay();
    
    this.emitInteractionEvent('focus', {
      action: 'video_play',
      current_time: this.state.current_time
    });
  };

  private handlePause = (): void => {
    this.setState({ is_playing: false, is_paused: true, show_controls: true });
    this.props.onPause?.();
    
    if (this.controlsTimeoutId) {
      clearTimeout(this.controlsTimeoutId);
    }
    
    this.emitInteractionEvent('focus', {
      action: 'video_pause',
      current_time: this.state.current_time
    });
  };

  private handleEnded = (): void => {
    this.setState({ is_playing: false, is_paused: false, current_time: 0, show_controls: true });
    this.props.onEnded?.();
    
    this.emitInteractionEvent('focus', {
      action: 'video_ended',
      duration: this.state.duration
    });
  };

  private handleTimeUpdate = (): void => {
    const video = this.videoRef.current;
    if (video) {
      this.setState({ current_time: video.currentTime });
      this.props.onTimeUpdate?.(video.currentTime, video.duration);
    }
  };

  private handleVolumeChange = (): void => {
    const video = this.videoRef.current;
    if (video) {
      this.setState({ 
        volume: video.volume,
        is_muted: video.muted
      });
    }
  };

  private handleWaiting = (): void => {
    this.setState({ is_buffering: true });
  };

  private handleError = (): void => {
    const video = this.videoRef.current;
    const errorMessage = video?.error?.message || 'Video playback error';
    
    this.setState({
      has_error: true,
      error_message: errorMessage,
      is_loading: false,
      is_buffering: false
    });
  };

  private handleVideoClick = (): void => {
    this.togglePlayPause();
    this.showControls();
  };

  private handleFullscreenChange = (): void => {
    const isFullscreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
    
    this.setState({ is_fullscreen: isFullscreen });
  };

  // 控制方法
  private togglePlayPause = (): void => {
    if (this.state.is_disabled || this.state.has_error) return;
    
    const video = this.videoRef.current;
    if (!video) return;

    if (this.state.is_playing) {
      video.pause();
    } else {
      video.play().catch(error => {
        console.error('Video play failed:', error);
        this.setState({
          has_error: true,
          error_message: 'Failed to play video'
        });
      });
    }

    this.triggerHapticFeedback('light');
  };

  private handleSeek = (event: React.MouseEvent<HTMLDivElement>): void => {
    if (this.state.is_disabled || this.state.has_error) return;
    
    const video = this.videoRef.current;
    const progressBar = event.currentTarget;
    if (!video || !progressBar) return;

    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * this.state.duration;
    
    video.currentTime = newTime;
    this.setState({ current_time: newTime });

    this.emitInteractionEvent('click', {
      action: 'video_seek',
      seek_time: newTime,
      percentage: percentage
    });
  };

  private toggleFullscreen = (): void => {
    const container = this.containerRef.current;
    if (!container) return;

    if (!this.state.is_fullscreen) {
      if (container.requestFullscreen) {
        container.requestFullscreen();
      } else if ((container as any).webkitRequestFullscreen) {
        (container as any).webkitRequestFullscreen();
      } else if ((container as any).mozRequestFullScreen) {
        (container as any).mozRequestFullScreen();
      } else if ((container as any).msRequestFullscreen) {
        (container as any).msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen();
      } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
      }
    }

    this.triggerHapticFeedback('light');
  };

  private showControls = (): void => {
    this.setState({ show_controls: true });
    if (this.state.is_playing) {
      this.hideControlsAfterDelay();
    }
  };

  private hideControlsAfterDelay = (): void => {
    if (this.controlsTimeoutId) {
      clearTimeout(this.controlsTimeoutId);
    }
    
    this.controlsTimeoutId = window.setTimeout(() => {
      if (this.state.is_playing) {
        this.setState({ show_controls: false });
      }
    }, 3000);
  };

  // 格式化时间
  private formatTime(seconds: number): string {
    if (isNaN(seconds)) return '0:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // 获取播放器样式类名
  private getPlayerStyleClassName(): string {
    const style = this.config.style.player_style;
    return `quiz-video-player-${style}`;
  }

  // 渲染播放/暂停按钮
  private renderPlayButton(): React.ReactNode {
    const isPlaying = this.state.is_playing;
    const isLoading = this.state.is_loading || this.state.is_buffering;
    
    return (
      <button
        className={`quiz-video-play-button ${isPlaying ? 'playing' : 'paused'}`}
        onClick={this.togglePlayPause}
        disabled={this.state.is_disabled || this.state.has_error}
        aria-label={isPlaying ? 'Pause' : 'Play'}
      >
        {isLoading ? (
          <div className="quiz-video-loading-spinner" />
        ) : isPlaying ? (
          <span className="quiz-video-pause-icon">⏸️</span>
        ) : (
          <span className="quiz-video-play-icon">▶️</span>
        )}
      </button>
    );
  }

  // 渲染进度条
  private renderProgressBar(): React.ReactNode {
    const progress = this.state.duration > 0 ? (this.state.current_time / this.state.duration) * 100 : 0;
    
    return (
      <div 
        className="quiz-video-progress-container"
        onClick={this.handleSeek}
      >
        <div className="quiz-video-progress-track">
          <div 
            className="quiz-video-progress-fill"
            style={{ width: `${progress}%` }}
          />
          <div 
            className="quiz-video-progress-thumb"
            style={{ left: `${progress}%` }}
          />
        </div>
      </div>
    );
  }

  // 渲染控制栏
  private renderControls(): React.ReactNode {
    if (!this.state.show_controls && this.state.is_playing) return null;
    
    return (
      <div className="quiz-video-controls">
        <div className="quiz-video-controls-left">
          {this.renderPlayButton()}
          
          <div className="quiz-video-time-display">
            <span className="quiz-video-current-time">
              {this.formatTime(this.state.current_time)}
            </span>
            <span className="quiz-video-time-separator">/</span>
            <span className="quiz-video-duration">
              {this.formatTime(this.state.duration)}
            </span>
          </div>
        </div>

        <div className="quiz-video-controls-center">
          {this.renderProgressBar()}
        </div>

        <div className="quiz-video-controls-right">
          {this.config.controls.show_fullscreen && (
            <button
              className="quiz-video-fullscreen-button"
              onClick={this.toggleFullscreen}
              aria-label={this.state.is_fullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {this.state.is_fullscreen ? '🗗' : '🗖'}
            </button>
          )}
        </div>
      </div>
    );
  }

  // 渲染错误状态
  private renderErrorState(): React.ReactNode {
    if (!this.state.has_error) return null;
    
    return (
      <div className="quiz-video-error">
        <span className="quiz-video-error-icon">⚠️</span>
        <span className="quiz-video-error-text">
          {this.state.error_message || 'Video playback error'}
        </span>
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-video-player-component',
      this.getPlayerStyleClassName(),
      this.state.is_playing && 'quiz-video-playing',
      this.state.is_fullscreen && 'quiz-video-fullscreen',
      this.state.is_disabled && 'quiz-video-disabled',
      this.state.has_error && 'quiz-video-error-state',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        ref={this.containerRef}
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
        onMouseMove={this.showControls}
        role="region"
        aria-label="Video player"
      >
        {/* 视频元素 */}
        <video
          ref={this.videoRef}
          src={this.props.videoUrl}
          poster={this.props.posterUrl}
          preload="metadata"
          className="quiz-video-element"
          style={{
            width: this.config.style.size.width ? `${this.config.style.size.width}px` : '100%',
            height: this.config.style.size.height ? `${this.config.style.size.height}px` : 'auto',
            aspectRatio: this.config.style.size.aspect_ratio || undefined
          }}
        />

        {/* 错误状态 */}
        {this.renderErrorState()}

        {/* 缓冲指示器 */}
        {this.state.is_buffering && (
          <div className="quiz-video-buffering">
            <div className="quiz-video-buffering-spinner" />
          </div>
        )}

        {/* 控制栏 */}
        {!this.state.has_error && this.renderControls()}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'region';
  }

  protected getAriaLabel(): string {
    return `Video player: ${this.state.is_playing ? 'Playing' : 'Paused'}`;
  }
}

// 使用Context的函数式组件包装器
const VideoPlayerComponentWrapper: React.FC<VideoPlayerComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <VideoPlayerComponent.contextType.Provider value={{ language }}>
      <VideoPlayerComponent {...props} />
    </VideoPlayerComponent.contextType.Provider>
  );
};

// 设置Context类型
VideoPlayerComponent.contextType = React.createContext({ language: 'zh' });

export default VideoPlayerComponentWrapper;
