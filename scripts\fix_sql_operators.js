import fs from 'node:fs';
import path from 'node:path';

// 递归查找所有 SQL 文件
function findSqlFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      findSqlFiles(filePath, fileList);
    } else if (path.extname(file).toLowerCase() === '.sql') {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// 检查文件中是否有 <= 或 >= 运算符
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');

  // 查找形如 "field <= value" 或 "field >= value" 的模式
  const regex = /(\w+)\s*([<>]=)\s*(\d+)/g;
  let match;
  let modified = false;
  let newContent = content;

  while ((match = regex.exec(content)) !== null) {
    const field = match[1];
    const operator = match[2];
    const value = match[3];

    console.log(`Found ${operator} operator in ${filePath}: ${match[0]}`);

    // 替换为 BETWEEN 运算符
    if (operator === '<=') {
      const replacement = `${field} BETWEEN 0 AND ${value}`;
      newContent = newContent.replace(match[0], replacement);
      modified = true;
      console.log(`Replaced with: ${replacement}`);
    } else if (operator === '>=') {
      const replacement = `${field} BETWEEN ${value} AND 100`;
      newContent = newContent.replace(match[0], replacement);
      modified = true;
      console.log(`Replaced with: ${replacement}`);
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, newContent);
    console.log(` ${filePath}`);
  }
}

// 主函数
function main() {
  const sqlFiles = findSqlFiles('.');
  console.log(`Found ${sqlFiles.length} SQL files`);

  sqlFiles.forEach((file) => {
    console.log(`Checking ${file}...`);
    checkFile(file);
  });
}

main();
