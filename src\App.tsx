import { Toaster as Sonner } from '@/components/ui/sonner';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryCache, QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { AppLoadingProvider } from './contexts/AppLoadingContext';
// Core Context Providers
import { DatabaseProvider } from './contexts/DatabaseContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { NetworkProvider } from './contexts/NetworkContext';
import { SyncProvider } from './contexts/SyncContext';

import { ColorModeProvider } from './contexts/ColorModeContext';
// Theme and UI Context Providers
import { ThemeProvider } from './contexts/ThemeContext';
import { UserConfigProvider } from './contexts/UserConfigContext';

// Feature Context Providers
import { EmojiProvider } from './contexts/EmojiContext';
import { SkinProvider } from './contexts/SkinContext';

// Components
import AppContent from './components/AppContent';

const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => {
      // TODO: Consider more sophisticated error handling, like showing a toast.
      console.error('Global query error:', error);
    },
  }),
  defaultOptions: {
    queries: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    {/* Database provider - initialize local database as early as possible */}
    <DatabaseProvider>
      {/* Core infrastructure providers */}
      <ThemeProvider>
        <NetworkProvider>
          <SyncProvider>
            {/* Language and loading providers */}
            <LanguageProvider>
              <AppLoadingProvider>
                {/* User configuration and UI providers */}
                <UserConfigProvider>
                  <ColorModeProvider>
                    {/* Feature providers */}
                    <EmojiProvider>
                      <SkinProvider>
                        {/* UI providers */}
                        <TooltipProvider>
                          <Toaster />
                          <Sonner />
                          {/* Main application content */}
                          <AppContent />
                        </TooltipProvider>
                      </SkinProvider>
                    </EmojiProvider>
                  </ColorModeProvider>
                </UserConfigProvider>
              </AppLoadingProvider>
            </LanguageProvider>
          </SyncProvider>
        </NetworkProvider>
      </ThemeProvider>
    </DatabaseProvider>
  </QueryClientProvider>
);

export default App;
