# Quiz设置问题个性化管理系统

本文档详细说明了Quiz设置系统中新增的问题个性化管理功能，这是一个为用户会话提供极致个性化体验的重大功能增强。

## 🎯 核心功能概述

### 1. **问题开启/关闭勾选**
- ✅ **动态启用/禁用**: 用户可以选择性启用或禁用特定问题
- ✅ **必需问题保护**: 标记为必需的问题无法被禁用
- ✅ **实时统计**: 显示已启用问题数量和总问题数量

### 2. **拖动问题顺序**
- ✅ **上下移动**: 通过上下箭头按钮调整问题顺序
- ✅ **视觉反馈**: 拖拽手柄和禁用状态的清晰视觉指示
- ✅ **边界保护**: 防止超出列表范围的移动操作

### 3. **量表切换功能**
- ✅ **概览页面切换**: 在概览tab中点击量表卡片切换到对应配置
- ✅ **实时配置更新**: 切换量表时立即显示对应的问题配置
- ✅ **状态同步**: 概览页面实时显示当前选中量表和问题统计

## 📋 详细功能实现

### 数据结构设计

#### 问题管理配置
```typescript
question_management: {
  selected_quiz_pack_id: string;
  available_quiz_packs: Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    total_questions: number;
  }>;
  question_customization: {
    [quiz_pack_id: string]: {
      enabled_questions: Array<{
        id: string;
        title: string;
        description: string;
        order: number;
        enabled: boolean;
        required: boolean;
      }>;
      custom_order: string[]; // 问题ID的自定义顺序
    };
  };
}
```

#### 预设量表数据
- **情绪轮盘基础版**: 8个问题，涵盖情绪识别和记录
- **中医体质评估完整版**: 15个问题，全面的中医体质分析
- **日常心情追踪**: 5个问题，简化的日常情绪记录
- **压力评估量表**: 12个问题，工作和生活压力评估

### 核心功能函数

#### 1. **问题启用/禁用切换**
```typescript
const toggleQuestionEnabled = (questionId: string) => {
  // 切换问题的启用状态
  // 自动更新自定义顺序，移除被禁用的问题
  // 保护必需问题不被禁用
};
```

#### 2. **问题顺序调整**
```typescript
const moveQuestion = (questionId: string, direction: 'up' | 'down') => {
  // 在自定义顺序中上下移动问题
  // 边界检查防止越界
  // 实时更新配置状态
};
```

#### 3. **重置问题顺序**
```typescript
const resetQuestionOrder = () => {
  // 重置为启用问题的原始顺序
  // 按问题的order字段排序
  // 只包含已启用的问题
};
```

#### 4. **量表切换**
```typescript
const switchQuizPack = (packId: string) => {
  // 切换当前选中的量表
  // 更新个性化级别
  // 触发界面重新渲染
};
```

## 🎨 用户界面设计

### 量表选择区域
```typescript
// 网格布局展示可用量表
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  {available_quiz_packs.map((pack) => (
    <div className={`p-4 border rounded-lg cursor-pointer transition-all ${
      selected ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50'
    }`}>
      {/* 量表信息：名称、描述、类别、问题数量 */}
      {/* 选中状态指示 */}
    </div>
  ))}
</div>
```

### 问题管理界面
```typescript
// 问题列表，支持启用/禁用和顺序调整
{allQuestions.map((question, index) => (
  <div className={`p-3 border rounded-lg ${
    question.enabled ? 'bg-background' : 'bg-muted/50'
  }`}>
    {/* 拖拽手柄：上下移动按钮 */}
    {/* 启用/禁用开关 */}
    {/* 问题信息：标题、描述、必需标识、序号 */}
  </div>
))}
```

### 统计信息面板
```typescript
// 实时显示问题统计
<div className="grid grid-cols-3 gap-4 text-center">
  <div>
    <div className="text-lg font-semibold text-primary">{enabledCount}</div>
    <div className="text-xs text-muted-foreground">已启用</div>
  </div>
  <div>
    <div className="text-lg font-semibold text-orange-600">{requiredCount}</div>
    <div className="text-xs text-muted-foreground">必需项</div>
  </div>
  <div>
    <div className="text-lg font-semibold text-muted-foreground">{totalCount}</div>
    <div className="text-xs text-muted-foreground">总数</div>
  </div>
</div>
```

## 🔧 技术实现亮点

### 1. **智能问题排序**
```typescript
// 按自定义顺序排序已启用的问题
const orderedQuestions = customization.custom_order
  .map(id => customization.enabled_questions.find(q => q.id === id))
  .filter(Boolean);

// 添加未在自定义顺序中的已启用问题
const remainingQuestions = customization.enabled_questions
  .filter(q => q.enabled && !customization.custom_order.includes(q.id));

const allQuestions = [...orderedQuestions, ...remainingQuestions];
```

### 2. **状态同步机制**
- **配置更新**: 所有操作都通过统一的updateConfig函数
- **实时反馈**: 配置变化立即反映在界面上
- **数据一致性**: 确保自定义顺序与启用状态的一致性

### 3. **用户体验优化**
- **视觉反馈**: 禁用问题的灰色显示
- **操作保护**: 必需问题无法禁用，边界移动被阻止
- **即时更新**: 所有操作都有即时的视觉反馈

## 🚀 概览页面增强

### 量表信息展示
```typescript
// 在Layer 0卡片中显示当前量表信息
<Badge variant="secondary">
  {available_quiz_packs.find(
    p => p.id === selected_quiz_pack_id
  )?.name || '未选择'}
</Badge>

// 显示问题启用统计
<div className="text-xs text-muted-foreground">
  已启用问题: {enabledCount}/{totalCount}
</div>
```

### 点击切换功能
```typescript
// 所有Layer卡片都支持点击切换
<Card 
  className="cursor-pointer hover:shadow-md transition-shadow" 
  onClick={() => setActiveTab('layer0')}
>
  {/* 卡片内容 */}
</Card>
```

## 💡 个性化价值

### 1. **用户会话级别定制**
- **精确控制**: 用户可以精确控制每个会话包含哪些问题
- **顺序自定义**: 根据个人偏好调整问题出现顺序
- **动态适应**: 不同场景下可以快速调整问题配置

### 2. **极致灵活性**
- **问题筛选**: 只启用相关的问题，提高答题效率
- **流程优化**: 将重要问题前置，次要问题后置
- **场景适配**: 不同使用场景可以有不同的问题配置

### 3. **智能化体验**
- **必需保护**: 确保关键问题不会被意外禁用
- **统计反馈**: 实时了解当前配置的问题数量
- **一键重置**: 快速恢复到默认配置

## 🔮 使用场景示例

### 场景1: 快速情绪记录
1. **选择情绪轮盘基础版**
2. **禁用非必需问题**: 只保留"当前主要情绪"和"情绪强度"
3. **调整顺序**: 将最重要的问题前置
4. **结果**: 2分钟内完成核心情绪记录

### 场景2: 深度情绪分析
1. **选择情绪轮盘基础版**
2. **启用所有问题**: 包括"应对方式"和"环境因素"
3. **优化顺序**: 按逻辑关系重新排序
4. **结果**: 全面的情绪状态分析

### 场景3: 中医体质评估
1. **切换到中医评估量表**
2. **根据用户需求**: 启用相关的评估维度
3. **个性化顺序**: 按重要性调整问题顺序
4. **结果**: 针对性的体质评估

## ✅ 功能验证清单

### 基础功能
- [ ] 量表选择界面正常显示
- [ ] 问题启用/禁用开关正常工作
- [ ] 问题顺序上下移动功能正常
- [ ] 重置顺序按钮正常工作

### 数据一致性
- [ ] 禁用问题自动从自定义顺序中移除
- [ ] 必需问题无法被禁用
- [ ] 统计信息实时更新
- [ ] 配置切换时数据正确加载

### 用户体验
- [ ] 概览页面显示当前量表信息
- [ ] 点击概览卡片正确切换到对应配置
- [ ] 视觉反馈清晰（禁用状态、选中状态等）
- [ ] 操作边界保护正常工作

### 性能表现
- [ ] 大量问题时界面响应流畅
- [ ] 配置更新无明显延迟
- [ ] 内存使用合理

这个问题个性化管理系统为Quiz设置提供了前所未有的灵活性，真正实现了"为某个用户的某个session个性化配置"的目标，让每个用户都能根据自己的需求定制最适合的Quiz体验。
