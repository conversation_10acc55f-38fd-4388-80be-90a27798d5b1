/**
 * Emotion Service
 * 情绪相关服务
 */

export interface Emotion {
  id: string;
  name: string;
  category: string;
  intensity: number;
}

export class EmotionService {
  /**
   * 获取所有情绪
   */
  async getAllEmotions(): Promise<Emotion[]> {
    // 模拟数据
    return [
      { id: '1', name: '快乐', category: 'positive', intensity: 5 },
      { id: '2', name: '悲伤', category: 'negative', intensity: 3 },
      { id: '3', name: '愤怒', category: 'negative', intensity: 7 },
      { id: '4', name: '平静', category: 'neutral', intensity: 2 },
    ];
  }

  /**
   * 根据类别获取情绪
   */
  async getEmotionsByCategory(category: string): Promise<Emotion[]> {
    const emotions = await this.getAllEmotions();
    return emotions.filter(emotion => emotion.category === category);
  }

  /**
   * 根据ID获取情绪
   */
  async getEmotionById(id: string): Promise<Emotion | null> {
    const emotions = await this.getAllEmotions();
    return emotions.find(emotion => emotion.id === id) || null;
  }
}

// 导出单例实例
export const emotionService = new EmotionService();
