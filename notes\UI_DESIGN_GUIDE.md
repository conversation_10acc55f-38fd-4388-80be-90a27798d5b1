# MoodWheel UI 设计指南

## 设计风格
- **整体风格**：柔和、现代、亲和力强，符合情绪管理应用的温暖氛围。
- **动效**：轻柔展开、呼吸光效，增强沉浸感和交互体验。
- **布局**：简洁清晰，重点突出情绪轮盘和核心功能模块。

---

## 组件规范
### 1. 情绪轮盘
- **视觉**：三层彩色扇形，动态可缩放/转动。
- **交互**：点击展开子类，中心动态展示当前路径。
- **动效**：模拟「拨开情绪的洋葱」，层层深入。

### 2. 按钮
- **主按钮**：圆角矩形，渐变背景色，悬停时轻微放大。
- **次按钮**：边框样式，背景透明，悬停时背景色轻微变化。
- **图标按钮**：圆形，图标居中，悬停时背景色变化。

### 3. 输入框
- **文本输入**：圆角矩形，边框颜色与主题色一致，聚焦时边框加粗。
- **选择器**：下拉菜单，选项悬停时背景色变化。

### 4. 卡片
- **情绪日记卡片**：圆角矩形，阴影效果，内容分区清晰。
- **情绪图谱卡片**：图表区域与说明文字分区，配色与情绪类型联动。

---

## 颜色系统
- **主色调**：柔和渐变色，与情绪类型联动。
  - 快乐：明亮黄绿 (#FFD700, #98FB98)
  - 悲伤：浅蓝灰 (#87CEEB, #B0C4DE)
  - 愤怒：温暖红橘 (#FF6347, #FFA07A)
  - 平静：浅蓝 (#ADD8E6, #E0FFFF)
- **辅助色**：用于强调和提示。
  - 成功：绿色 (#4CAF50)
  - 警告：橙色 (#FF9800)
  - 错误：红色 (#F44336)
- **中性色**：用于文字和背景。
  - 文字：深灰 (#333333)
  - 背景：浅灰 (#F5F5F5)

---

## 字体
- **主字体**：Rounded Gothic / Avenir / HarmonyOS Sans
- **字号**：
  - 标题：24px / 20px / 18px
  - 正文：16px / 14px
  - 小字：12px
- **字重**：Regular (400) / Medium (500) / Bold (700)

---

## 图标
- **风格**：轻量、拟人化，与情绪主题一致。
- **尺寸**：24px / 20px / 16px
- **颜色**：与文字颜色一致，或使用主题色。

---

## 响应式设计
- **移动端**：单列布局，轮盘可缩放，按钮和输入框适应屏幕宽度。
- **平板/桌面**：多列布局，轮盘固定尺寸，内容区域自适应。

---

## 设计资源
- **Figma 链接**：[MoodWheel UI Kit](https://figma.com/...)
- **图标库**：[Phosphor Icons](https://phosphoricons.com/)
- **字体下载**：[Google Fonts](https://fonts.google.com/)

---

## 注意事项
- 确保所有组件符合无障碍设计标准，支持屏幕阅读器。
- 动效需适度，避免过度干扰用户注意力。
- 设计需与开发团队紧密协作，确保实现效果一致。 