-- 情绪追踪问卷 - 拆解为50个独立关联问题
-- 基于条件分支逻辑，每次会话只回答3个问题

-- 1. 创建Quiz包
INSERT INTO quiz_packs (
  id, name, name_localized, description, description_localized,
  quiz_type, quiz_style, category, difficulty_level, 
  estimated_duration_minutes, tags, metadata, is_active,
  created_at, updated_at
) VALUES (
  'mood_track_branching_v1',
  'Mood Tracking - Branching Logic',
  '{"zh-CN": "情绪追踪问卷 - 分支逻辑", "en-US": "Mood Tracking - Branching Logic"}',
  'A 50-question emotion tracking system with conditional branching logic',
  '{"zh-CN": "50个关联问题的情绪追踪系统，采用条件分支逻辑", "en-US": "A 50-question emotion tracking system with conditional branching logic"}',
  'emotion_wheel',
  'conditional_branching', 
  'emotion',
  2,
  3,
  '["emotion", "mood", "tracking", "conditional", "branching"]',
  '{
    "structure_type": "conditional_branching",
    "total_questions": 50,
    "question_layers": 3,
    "questions_per_session": 3,
    "branching_logic": true,
    "layer_distribution": {
      "primary": 1,
      "secondary": 8, 
      "tertiary": 41
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 2. 第一层问题: 主要情绪选择
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, metadata, created_at, updated_at
) VALUES (
  'q001_primary_emotion',
  'mood_track_branching_v1',
  'What is your primary emotion right now?',
  '{"zh-CN": "您当前的主要情绪是什么？", "en-US": "What is your primary emotion right now?"}',
  'Select the main emotion category that best describes your current state',
  'single_choice',
  1,
  1,
  '{
    "emotion_tier": 1,
    "layer": "primary",
    "triggers_questions": ["q002_happy_secondary", "q003_surprised_secondary", "q004_bad_secondary", "q005_fearful_secondary", "q006_angry_secondary", "q007_disgusted_secondary", "q008_sad_secondary"],
    "display_style": "emotion_grid",
    "show_emoji": true,
    "show_colors": true
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 3. 第二层问题: Happy的次要情绪
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index,
  is_required, condition_logic, metadata, created_at, updated_at
) VALUES (
  'q002_happy_secondary',
  'mood_track_branching_v1',
  'You feel happy, what specific type of happiness?',
  '{"zh-CN": "您感到快乐，具体是哪种类型的快乐？", "en-US": "You feel happy, what specific type of happiness?"}',
  'Choose the secondary emotion that best describes your happiness',
  'single_choice',
  2,
  1,
  '{"show_if": {"question_id": "q001_primary_emotion", "answer_value": "happy"}}',
  '{
    "emotion_tier": 2,
    "layer": "secondary",
    "parent_emotion": "happy",
    "triggers_questions": ["q010_playful_tertiary", "q011_content_tertiary", "q012_interested_tertiary", "q013_proud_tertiary", "q014_accepted_tertiary", "q015_powerful_tertiary", "q016_peaceful_tertiary", "q017_trusting_tertiary", "q018_optimistic_tertiary"]
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 4. 第二层问题: Surprised的次要情绪
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index,
  is_required, condition_logic, metadata, created_at, updated_at
) VALUES (
  'q003_surprised_secondary',
  'mood_track_branching_v1',
  'You feel surprised, what specific type of surprise?',
  '{"zh-CN": "您感到惊讶，具体是哪种类型的惊讶？", "en-US": "You feel surprised, what specific type of surprise?"}',
  'Choose the secondary emotion that best describes your surprise',
  'single_choice',
  3,
  1,
  '{"show_if": {"question_id": "q001_primary_emotion", "answer_value": "surprised"}}',
  '{
    "emotion_tier": 2,
    "layer": "secondary",
    "parent_emotion": "surprised",
    "triggers_questions": ["q019_startled_tertiary", "q020_confused_tertiary", "q021_amazed_tertiary", "q022_excited_tertiary"]
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 5. 第三层问题: Playful的具体情绪
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index,
  is_required, condition_logic, metadata, created_at, updated_at
) VALUES (
  'q010_playful_tertiary',
  'mood_track_branching_v1',
  'You feel playful, what specific feeling?',
  '{"zh-CN": "您感到顽皮，具体是哪种感觉？", "en-US": "You feel playful, what specific feeling?"}',
  'Choose the precise emotion that captures your playful mood',
  'single_choice',
  10,
  1,
  '{"show_if": {"question_id": "q002_happy_secondary", "answer_value": "playful"}}',
  '{
    "emotion_tier": 3,
    "layer": "tertiary",
    "parent_emotion": "playful",
    "grandparent_emotion": "happy",
    "is_final_selection": true,
    "emotion_path": ["happy", "playful"]
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 6. 第三层问题: Content的具体情绪
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index,
  is_required, condition_logic, metadata, created_at, updated_at
) VALUES (
  'q011_content_tertiary',
  'mood_track_branching_v1',
  'You feel content, what specific feeling?',
  '{"zh-CN": "您感到满足，具体是哪种感觉？", "en-US": "You feel content, what specific feeling?"}',
  'Choose the precise emotion that captures your contentment',
  'single_choice',
  11,
  1,
  '{"show_if": {"question_id": "q002_happy_secondary", "answer_value": "content"}}',
  '{
    "emotion_tier": 3,
    "layer": "tertiary",
    "parent_emotion": "content",
    "grandparent_emotion": "happy",
    "is_final_selection": true,
    "emotion_path": ["happy", "content"]
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 7. 第一层选项 (主要情绪)
INSERT INTO quiz_question_options (
  id, question_id, option_text, option_text_localized,
  option_value, order_index, metadata, created_at, updated_at
) VALUES 
-- Happy
('opt_primary_happy', 'q001_primary_emotion', 'Happy', '{"zh-CN": "快乐", "en-US": "Happy"}', 'happy', 1,
 '{"emotion_category": "positive", "secondary_count": 9, "intensity_range": [1, 10]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Surprised
('opt_primary_surprised', 'q001_primary_emotion', 'Surprised', '{"zh-CN": "惊讶", "en-US": "Surprised"}', 'surprised', 2,
 '{"emotion_category": "neutral", "secondary_count": 4, "intensity_range": [1, 8]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Bad
('opt_primary_bad', 'q001_primary_emotion', 'Bad', '{"zh-CN": "糟糕", "en-US": "Bad"}', 'bad', 3,
 '{"emotion_category": "negative", "secondary_count": 4, "intensity_range": [1, 9]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Fearful
('opt_primary_fearful', 'q001_primary_emotion', 'Fearful', '{"zh-CN": "恐惧", "en-US": "Fearful"}', 'fearful', 4,
 '{"emotion_category": "negative", "secondary_count": 6, "intensity_range": [1, 10]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Angry
('opt_primary_angry', 'q001_primary_emotion', 'Angry', '{"zh-CN": "愤怒", "en-US": "Angry"}', 'angry', 5,
 '{"emotion_category": "negative", "secondary_count": 8, "intensity_range": [1, 10]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Disgusted
('opt_primary_disgusted', 'q001_primary_emotion', 'Disgusted', '{"zh-CN": "厌恶", "en-US": "Disgusted"}', 'disgusted', 6,
 '{"emotion_category": "negative", "secondary_count": 4, "intensity_range": [1, 8]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Sad
('opt_primary_sad', 'q001_primary_emotion', 'Sad', '{"zh-CN": "悲伤", "en-US": "Sad"}', 'sad', 7,
 '{"emotion_category": "negative", "secondary_count": 6, "intensity_range": [1, 10]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 8. Happy的次要情绪选项
INSERT INTO quiz_question_options (
  id, question_id, option_text, option_text_localized,
  option_value, order_index, metadata, created_at, updated_at
) VALUES 
-- Happy -> Playful
('opt_happy_playful', 'q002_happy_secondary', 'Playful', '{"zh-CN": "顽皮的", "en-US": "Playful"}', 'playful', 1,
 '{"parent_emotion": "happy", "tertiary_count": 2, "color": "#4CAF50"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Happy -> Content
('opt_happy_content', 'q002_happy_secondary', 'Content', '{"zh-CN": "满足的", "en-US": "Content"}', 'content', 2,
 '{"parent_emotion": "happy", "tertiary_count": 2, "color": "#4CAF50"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Happy -> Interested
('opt_happy_interested', 'q002_happy_secondary', 'Interested', '{"zh-CN": "感兴趣的", "en-US": "Interested"}', 'interested', 3,
 '{"parent_emotion": "happy", "tertiary_count": 2, "color": "#4CAF50"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 9. Playful的具体情绪选项
INSERT INTO quiz_question_options (
  id, question_id, option_text, option_text_localized,
  option_value, order_index, metadata, created_at, updated_at
) VALUES 
-- Playful -> Aroused
('opt_playful_aroused', 'q010_playful_tertiary', 'Aroused', '{"zh-CN": "兴奋的", "en-US": "Aroused"}', 'aroused', 1,
 '{"parent_emotion": "playful", "grandparent_emotion": "happy", "emotion_path": ["happy", "playful", "aroused"]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Playful -> Cheeky  
('opt_playful_cheeky', 'q010_playful_tertiary', 'Cheeky', '{"zh-CN": "厚脸皮的", "en-US": "Cheeky"}', 'cheeky', 2,
 '{"parent_emotion": "playful", "grandparent_emotion": "happy", "emotion_path": ["happy", "playful", "cheeky"]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 10. Content的具体情绪选项
INSERT INTO quiz_question_options (
  id, question_id, option_text, option_text_localized,
  option_value, order_index, metadata, created_at, updated_at
) VALUES 
-- Content -> Free
('opt_content_free', 'q011_content_tertiary', 'Free', '{"zh-CN": "自由的", "en-US": "Free"}', 'free', 1,
 '{"parent_emotion": "content", "grandparent_emotion": "happy", "emotion_path": ["happy", "content", "free"]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Content -> Joyful
('opt_content_joyful', 'q011_content_tertiary', 'Joyful', '{"zh-CN": "欢乐的", "en-US": "Joyful"}', 'joyful', 2,
 '{"parent_emotion": "content", "grandparent_emotion": "happy", "emotion_path": ["happy", "content", "joyful"]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 注意：这里只展示了部分数据结构示例
-- 完整的数据需要包含所有50个问题和对应的选项
-- 可以通过脚本批量生成剩余的数据

-- ============================================================================
-- QUIZ包默认EMOJI映射配置
-- ============================================================================

-- 为情绪追踪问卷包配置默认的emoji映射
INSERT INTO pack_presentation_configs (
    id, pack_id, config_type, config_data, config_version, config_description,
    is_active, is_default, created_at, updated_at
) VALUES (
    'emoji_config_mood_track_v1',
    'mood_track_branching_v1',
    'default_emoji_mapping',
    '{
        "layer1_primary_emotions": {
            "happy": {
                "emoji": "😊",
                "color": "#4CAF50",
                "animation": "bounce",
                "alternatives": ["😄", "😃", "🙂", "😌", "🥰"]
            },
            "surprised": {
                "emoji": "😲",
                "color": "#FF9800",
                "animation": "pop",
                "alternatives": ["😮", "😯", "🤯", "😦", "😧"]
            },
            "bad": {
                "emoji": "😞",
                "color": "#9E9E9E",
                "animation": "fade",
                "alternatives": ["😔", "😟", "😕", "🙁", "😣"]
            },
            "fearful": {
                "emoji": "😨",
                "color": "#9C27B0",
                "animation": "tremble",
                "alternatives": ["😰", "😱", "😧", "😟", "😖"]
            },
            "angry": {
                "emoji": "😠",
                "color": "#F44336",
                "animation": "shake",
                "alternatives": ["😡", "🤬", "😤", "😒", "🙄"]
            },
            "disgusted": {
                "emoji": "🤢",
                "color": "#795548",
                "animation": "recoil",
                "alternatives": ["🤮", "😖", "😣", "🙄", "😤"]
            },
            "sad": {
                "emoji": "😢",
                "color": "#2196F3",
                "animation": "drop",
                "alternatives": ["😭", "😞", "☹️", "😔", "😟"]
            }
        },
        "layer2_secondary_emotions": {
            "playful": {
                "emoji": "😜",
                "color": "#4CAF50",
                "animation": "wiggle",
                "alternatives": ["😋", "🤪", "😝", "🤭", "😏"]
            },
            "content": {
                "emoji": "😌",
                "color": "#4CAF50",
                "animation": "glow",
                "alternatives": ["😊", "🙂", "😇", "🥰", "😍"]
            },
            "interested": {
                "emoji": "🤔",
                "color": "#4CAF50",
                "animation": "think",
                "alternatives": ["🧐", "👀", "💭", "🔍", "📚"]
            },
            "proud": {
                "emoji": "😤",
                "color": "#4CAF50",
                "animation": "puff",
                "alternatives": ["😏", "🤗", "💪", "🏆", "⭐"]
            },
            "accepted": {
                "emoji": "🤗",
                "color": "#4CAF50",
                "animation": "embrace",
                "alternatives": ["😊", "🥰", "💖", "🌟", "✨"]
            },
            "powerful": {
                "emoji": "💪",
                "color": "#4CAF50",
                "animation": "flex",
                "alternatives": ["🦾", "⚡", "🔥", "🚀", "👑"]
            },
            "peaceful": {
                "emoji": "🧘",
                "color": "#4CAF50",
                "animation": "meditate",
                "alternatives": ["☮️", "🕊️", "🌿", "🍃", "🌸"]
            },
            "trusting": {
                "emoji": "🤝",
                "color": "#4CAF50",
                "animation": "handshake",
                "alternatives": ["💖", "🤗", "😇", "🙏", "💕"]
            },
            "optimistic": {
                "emoji": "🌟",
                "color": "#4CAF50",
                "animation": "sparkle",
                "alternatives": ["✨", "🌈", "☀️", "🌞", "💫"]
            },
            "startled": {
                "emoji": "😳",
                "color": "#FF9800",
                "animation": "jump",
                "alternatives": ["😲", "😮", "🤯", "😦", "😧"]
            },
            "confused": {
                "emoji": "😕",
                "color": "#FF9800",
                "animation": "puzzle",
                "alternatives": ["🤔", "😵‍💫", "🤷", "😐", "😑"]
            },
            "amazed": {
                "emoji": "🤩",
                "color": "#FF9800",
                "animation": "star_eyes",
                "alternatives": ["😍", "🤯", "😲", "🌟", "✨"]
            },
            "excited": {
                "emoji": "🎉",
                "color": "#FF9800",
                "animation": "party",
                "alternatives": ["🥳", "🎊", "🚀", "⚡", "🔥"]
            }
        },
        "layer3_tertiary_emotions": {
            "aroused": {
                "emoji": "🤩",
                "color": "#FF6B35",
                "animation": "pulse",
                "alternatives": ["⚡", "🔥", "💥", "✨", "🚀"]
            },
            "cheeky": {
                "emoji": "😏",
                "color": "#FF6B35",
                "animation": "wink",
                "alternatives": ["😉", "😎", "🤫", "😈", "🤭"]
            },
            "free": {
                "emoji": "🕊️",
                "color": "#87CEEB",
                "animation": "float",
                "alternatives": ["🦋", "🌟", "🌈", "☁️", "🌸"]
            },
            "joyful": {
                "emoji": "😆",
                "color": "#FFD700",
                "animation": "celebrate",
                "alternatives": ["🤣", "😂", "😁", "🎉", "🥳"]
            },
            "ecstatic": {
                "emoji": "🤯",
                "color": "#FFD700",
                "animation": "mind_blown",
                "alternatives": ["🎆", "💥", "🌟", "✨", "🎊"]
            },
            "blissful": {
                "emoji": "😇",
                "color": "#FFD700",
                "animation": "halo",
                "alternatives": ["🥰", "😌", "🌟", "✨", "💫"]
            },
            "serene": {
                "emoji": "🧘‍♀️",
                "color": "#87CEEB",
                "animation": "zen",
                "alternatives": ["🧘", "☮️", "🕊️", "🌸", "🍃"]
            },
            "fulfilled": {
                "emoji": "🤗",
                "color": "#87CEEB",
                "animation": "warm_hug",
                "alternatives": ["💖", "🥰", "😌", "🌟", "💕"]
            },
            "vibrant": {
                "emoji": "🌈",
                "color": "#FF6B35",
                "animation": "rainbow",
                "alternatives": ["🎨", "🌟", "✨", "💫", "🎆"]
            },
            "energetic": {
                "emoji": "⚡",
                "color": "#FF6B35",
                "animation": "lightning",
                "alternatives": ["🔥", "💥", "🚀", "💪", "🌟"]
            },
            "liberated": {
                "emoji": "🦋",
                "color": "#87CEEB",
                "animation": "butterfly",
                "alternatives": ["🕊️", "🌈", "☁️", "🌸", "✨"]
            },
            "euphoric": {
                "emoji": "🎊",
                "color": "#FFD700",
                "animation": "confetti",
                "alternatives": ["🎉", "🥳", "🎆", "🌟", "💫"]
            },
            "perplexed": {
                "emoji": "😵‍💫",
                "color": "#FF9800",
                "animation": "dizzy",
                "alternatives": ["🤔", "😕", "🤷", "😐", "🌀"]
            },
            "disillusioned": {
                "emoji": "😔",
                "color": "#9E9E9E",
                "animation": "deflate",
                "alternatives": ["😞", "😟", "😕", "🙁", "💔"]
            },
            "awestruck": {
                "emoji": "😍",
                "color": "#FF9800",
                "animation": "heart_eyes",
                "alternatives": ["🤩", "😲", "🌟", "✨", "💖"]
            },
            "overwhelmed": {
                "emoji": "😵",
                "color": "#FF9800",
                "animation": "overload",
                "alternatives": ["🤯", "😲", "😰", "🌪️", "💫"]
            },
            "despair": {
                "emoji": "😭",
                "color": "#2196F3",
                "animation": "sob",
                "alternatives": ["😢", "💔", "😞", "☹️", "🌧️"]
            },
            "grief": {
                "emoji": "💔",
                "color": "#2196F3",
                "animation": "heartbreak",
                "alternatives": ["😭", "😢", "😞", "🖤", "⚫"]
            },
            "melancholy": {
                "emoji": "🌧️",
                "color": "#2196F3",
                "animation": "rain",
                "alternatives": ["☁️", "😔", "🌙", "💧", "🍂"]
            },
            "lonely": {
                "emoji": "😔",
                "color": "#2196F3",
                "animation": "isolate",
                "alternatives": ["😞", "🌙", "☁️", "💙", "🌊"]
            },
            "rage": {
                "emoji": "🤬",
                "color": "#F44336",
                "animation": "explode",
                "alternatives": ["😡", "💥", "🔥", "⚡", "🌪️"]
            },
            "fury": {
                "emoji": "😡",
                "color": "#F44336",
                "animation": "steam",
                "alternatives": ["🤬", "💢", "🔥", "💥", "⚡"]
            },
            "irritated": {
                "emoji": "😒",
                "color": "#F44336",
                "animation": "eye_roll",
                "alternatives": ["🙄", "😤", "😠", "💢", "⚠️"]
            },
            "resentful": {
                "emoji": "😠",
                "color": "#F44336",
                "animation": "glare",
                "alternatives": ["😡", "🤬", "💢", "🔥", "⚡"]
            },
            "terror": {
                "emoji": "😱",
                "color": "#9C27B0",
                "animation": "scream",
                "alternatives": ["😨", "😰", "💀", "👻", "🌪️"]
            },
            "panic": {
                "emoji": "😰",
                "color": "#9C27B0",
                "animation": "sweat",
                "alternatives": ["😨", "😱", "💦", "🌪️", "⚡"]
            },
            "anxiety": {
                "emoji": "😟",
                "color": "#9C27B0",
                "animation": "worry",
                "alternatives": ["😰", "😨", "💭", "🌀", "💦"]
            },
            "dread": {
                "emoji": "😖",
                "color": "#9C27B0",
                "animation": "cringe",
                "alternatives": ["😰", "😨", "💀", "⚫", "🌫️"]
            },
            "revulsion": {
                "emoji": "🤮",
                "color": "#795548",
                "animation": "sick",
                "alternatives": ["🤢", "😖", "😣", "🙄", "💚"]
            },
            "loathing": {
                "emoji": "😤",
                "color": "#795548",
                "animation": "snort",
                "alternatives": ["🤢", "🙄", "😒", "💢", "⚠️"]
            },
            "repugnance": {
                "emoji": "😣",
                "color": "#795548",
                "animation": "grimace",
                "alternatives": ["🤢", "😖", "🙄", "😤", "💢"]
            },
            "contempt": {
                "emoji": "🙄",
                "color": "#795548",
                "animation": "dismiss",
                "alternatives": ["😒", "😤", "💢", "⚠️", "🚫"]
            }
        },
        "theme_info": {
            "theme_name": "default_emotions",
            "theme_description": "Default emoji mapping for emotion tracking",
            "supports_alternatives": true,
            "supports_user_customization": true,
            "supports_question_overrides": true
        }
    }',
    '1.0',
    'Default emoji mapping configuration for mood tracking quiz pack',
    1,
    1,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);
