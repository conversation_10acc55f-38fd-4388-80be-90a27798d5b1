/**
 * 列表视图组件
 * 使用列表布局显示情绪
 *
 * 此组件是新视图系统的一部分，用于替代旧的视图组件
 * 它直接实现了列表的渲染，不依赖旧的视图类
 */

import { AnimatedEmoji } from '@/components/emoji/AnimatedEmoji';
import { useEmoji } from '@/contexts/EmojiContext';
import { useLanguage } from '@/contexts/LanguageContext';
import type { Emotion, ContentDisplayMode, SkinConfig } from '@/types';
import type { ListLayout } from '@/views/implementations/lists/ListView';
import type React from 'react';
import { useState } from 'react';

interface ListViewProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  layout: ListLayout;
  onBack?: () => void;
  selectedPath?: any;
}

/**
 * 列表视图组件
 */
export const ListViewComponent: React.FC<ListViewProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  layout,
  onBack,
  selectedPath,
}) => {
  const { t } = useLanguage();
  const { getEmojiItemForEmotionId } = useEmoji();
  const [activeTab, setActiveTab] = useState<number>(0);
  const [expandedAccordion, setExpandedAccordion] = useState<string | null>(null);

  // 提取列表配置
  const listConfig = {
    fontSize: skinConfig.fonts?.size?.medium || 14,
    fontFamily: skinConfig.fonts?.family || 'Arial, sans-serif',
    textColor: skinConfig.colors?.text || '#000000',
    backgroundColor: skinConfig.colors?.background || '#FFFFFF',
    primaryColor: skinConfig.colors?.primary || '#4CAF50',
    secondaryColor: skinConfig.colors?.secondary || '#2196F3',
    accentColor: skinConfig.colors?.accent || '#FF4081',
    borderRadius: skinConfig.effects?.borderRadius || 4,
    borderWidth: skinConfig.effects?.border_width || 1,
    borderColor: skinConfig.effects?.border_color || '#CCCCCC',
    borderStyle: skinConfig.effects?.border_style || 'solid',
    shadowEnabled: skinConfig.effects?.shadows || false,
    shadowColor: skinConfig.effects?.shadowColor || 'rgba(0, 0, 0, 0.2)',
    shadowBlur: skinConfig.effects?.shadowBlur || 5,
    shadowOffsetX: skinConfig.effects?.shadowOffsetX || 0,
    shadowOffsetY: skinConfig.effects?.shadowOffsetY || 2,
    animationDuration: skinConfig.effects?.animationDuration || 300,
    animationEasing: skinConfig.effects?.animationEasing || 'ease-in-out',
    emojiSize: skinConfig.viewConfigs?.list?.emojiSize || 24,
    itemSpacing: skinConfig.viewConfigs?.list?.item_spacing || 8,
    itemPadding: skinConfig.viewConfigs?.list?.item_padding || 12,
    hoverEffect: skinConfig.viewConfigs?.list?.hover_effect || 'highlight',
    selectionAnimation: skinConfig.viewConfigs?.list?.selection_animation || 'pulse',
  };

  // 渲染垂直列表
  const renderVerticalList = () => {
    return (
      <div
        className="vertical-list"
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: `${listConfig.itemSpacing}px`,
          width: '100%',
        }}
      >
        {emotions.map((emotion) => (
          <div
            key={emotion.id}
            className="list-item"
            style={{
              padding: `${listConfig.itemPadding}px`,
              borderRadius: `${listConfig.borderRadius}px`,
              border: `${listConfig.borderWidth}px ${listConfig.borderStyle} ${listConfig.borderColor}`,
              backgroundColor: listConfig.background_color,
              boxShadow: listConfig.shadowEnabled
                ? `${listConfig.shadowOffsetX}px ${listConfig.shadowOffsetY}px ${listConfig.shadowBlur}px ${listConfig.shadowColor}`
                : 'none',
              cursor: 'pointer',
              transition: `all ${listConfig.animationDuration}ms ${listConfig.animationEasing}`,
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
            }}
            onClick={() => onSelect(emotion)}
            onMouseEnter={(e) => {
              if (listConfig.hoverEffect === 'highlight') {
                e.currentTarget.style.background_color = `${listConfig.primaryColor}20`; // 20% opacity
              } else if (listConfig.hoverEffect === 'scale') {
                e.currentTarget.style.transform = 'scale(1.02)';
              }
            }}
            onMouseLeave={(e) => {
              if (listConfig.hoverEffect === 'highlight') {
                e.currentTarget.style.background_color = listConfig.background_color;
              } else if (listConfig.hoverEffect === 'scale') {
                e.currentTarget.style.transform = 'scale(1)';
              }
            }}
          >
            {renderEmotionContent(emotion)}
          </div>
        ))}
      </div>
    );
  };

  // 渲染水平列表
  const renderHorizontalList = () => {
    return (
      <div
        className="horizontal-list"
        style={{
          display: 'flex',
          flexDirection: 'row',
          flexWrap: 'wrap',
          gap: `${listConfig.itemSpacing}px`,
          width: '100%',
        }}
      >
        {emotions.map((emotion) => (
          <div
            key={emotion.id}
            className="list-item"
            style={{
              padding: `${listConfig.itemPadding}px`,
              borderRadius: `${listConfig.borderRadius}px`,
              border: `${listConfig.borderWidth}px ${listConfig.borderStyle} ${listConfig.borderColor}`,
              backgroundColor: listConfig.background_color,
              boxShadow: listConfig.shadowEnabled
                ? `${listConfig.shadowOffsetX}px ${listConfig.shadowOffsetY}px ${listConfig.shadowBlur}px ${listConfig.shadowColor}`
                : 'none',
              cursor: 'pointer',
              transition: `all ${listConfig.animationDuration}ms ${listConfig.animationEasing}`,
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              flex: '1 0 auto',
              minWidth: '150px',
              maxWidth: '300px',
            }}
            onClick={() => onSelect(emotion)}
            onMouseEnter={(e) => {
              if (listConfig.hoverEffect === 'highlight') {
                e.currentTarget.style.background_color = `${listConfig.primaryColor}20`; // 20% opacity
              } else if (listConfig.hoverEffect === 'scale') {
                e.currentTarget.style.transform = 'scale(1.02)';
              }
            }}
            onMouseLeave={(e) => {
              if (listConfig.hoverEffect === 'highlight') {
                e.currentTarget.style.background_color = listConfig.background_color;
              } else if (listConfig.hoverEffect === 'scale') {
                e.currentTarget.style.transform = 'scale(1)';
              }
            }}
          >
            {renderEmotionContent(emotion)}
          </div>
        ))}
      </div>
    );
  };

  // 渲染手风琴列表
  const renderAccordionList = () => {
    return (
      <div
        className="accordion-list"
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: `${listConfig.itemSpacing}px`,
          width: '100%',
        }}
      >
        {emotions.map((emotion) => (
          <div
            key={emotion.id}
            className="accordion-item"
            style={{
              borderRadius: `${listConfig.borderRadius}px`,
              border: `${listConfig.borderWidth}px ${listConfig.borderStyle} ${listConfig.borderColor}`,
              backgroundColor: listConfig.background_color,
              boxShadow: listConfig.shadowEnabled
                ? `${listConfig.shadowOffsetX}px ${listConfig.shadowOffsetY}px ${listConfig.shadowBlur}px ${listConfig.shadowColor}`
                : 'none',
              overflow: 'hidden',
              transition: `all ${listConfig.animationDuration}ms ${listConfig.animationEasing}`,
            }}
          >
            <div
              className="accordion-header"
              style={{
                padding: `${listConfig.itemPadding}px`,
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                backgroundColor:
                  expandedAccordion === emotion.id
                    ? `${listConfig.primaryColor}20`
                    : listConfig.background_color,
              }}
              onClick={() =>
                setExpandedAccordion(expandedAccordion === emotion.id ? null : emotion.id)
              }
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                {renderEmotionIcon(emotion)}
                <span
                  style={{
                    fontSize: `${listConfig.font_size}px`,
                    fontFamily: listConfig.font_family,
                    color: listConfig.text_color,
                  }}
                >
                  {emotion.name}
                </span>
              </div>
              <span
                style={{
                  fontSize: '18px',
                  transition: `transform ${listConfig.animationDuration}ms ${listConfig.animationEasing}`,
                  transform: expandedAccordion === emotion.id ? 'rotate(180deg)' : 'rotate(0deg)',
                }}
              >
                ▼
              </span>
            </div>
            {expandedAccordion === emotion.id && (
              <div
                className="accordion-content"
                style={{
                  padding: `${listConfig.itemPadding}px`,
                  borderTop: `${listConfig.borderWidth}px ${listConfig.borderStyle} ${listConfig.borderColor}`,
                }}
              >
                <p
                  style={{
                    fontSize: `${listConfig.font_size}px`,
                    fontFamily: listConfig.font_family,
                    color: listConfig.text_color,
                    margin: '0 0 12px 0',
                  }}
                >
                  {emotion.keywords || t('no_keywords_available')}
                </p>
                <button
                  style={{
                    padding: '8px 16px',
                    backgroundColor: listConfig.primaryColor,
                    color: '#FFFFFF',
                    border: 'none',
                    borderRadius: `${listConfig.borderRadius}px`,
                    cursor: 'pointer',
                    fontSize: `${listConfig.font_size}px`,
                    fontFamily: listConfig.font_family,
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelect(emotion);
                  }}
                >
                  {t('select')}
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // 渲染标签页列表
  const renderTabsList = () => {
    return (
      <div
        className="tabs-list"
        style={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <div
          className="tabs-header"
          style={{
            display: 'flex',
            borderBottom: `${listConfig.borderWidth}px ${listConfig.borderStyle} ${listConfig.borderColor}`,
            marginBottom: `${listConfig.itemSpacing}px`,
            overflowX: 'auto',
            scrollbarWidth: 'thin',
          }}
        >
          {emotions.map((emotion, index) => (
            <div
              key={emotion.id}
              className="tab"
              style={{
                padding: `${listConfig.itemPadding}px`,
                cursor: 'pointer',
                borderBottom:
                  activeTab === index
                    ? `2px solid ${listConfig.primaryColor}`
                    : '2px solid transparent',
                color: activeTab === index ? listConfig.primaryColor : listConfig.text_color,
                fontWeight: activeTab === index ? 'bold' : 'normal',
                fontSize: `${listConfig.font_size}px`,
                fontFamily: listConfig.font_family,
                whiteSpace: 'nowrap',
                transition: `all ${listConfig.animationDuration}ms ${listConfig.animationEasing}`,
              }}
              onClick={() => setActiveTab(index)}
            >
              {emotion.name}
            </div>
          ))}
        </div>
        <div className="tab-content">
          {emotions[activeTab] && (
            <div
              style={{
                padding: `${listConfig.itemPadding}px`,
                borderRadius: `${listConfig.borderRadius}px`,
                border: `${listConfig.borderWidth}px ${listConfig.borderStyle} ${listConfig.borderColor}`,
                backgroundColor: listConfig.background_color,
                boxShadow: listConfig.shadowEnabled
                  ? `${listConfig.shadowOffsetX}px ${listConfig.shadowOffsetY}px ${listConfig.shadowBlur}px ${listConfig.shadowColor}`
                  : 'none',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '16px',
                  marginBottom: '16px',
                }}
              >
                {renderEmotionIcon(emotions[activeTab], 48)}
                <h3
                  style={{
                    fontSize: `${listConfig.font_size * 1.5}px`,
                    fontFamily: listConfig.font_family,
                    color: listConfig.text_color,
                    margin: 0,
                  }}
                >
                  {emotions[activeTab].name}
                </h3>
              </div>
              <p
                style={{
                  fontSize: `${listConfig.font_size}px`,
                  fontFamily: listConfig.font_family,
                  color: listConfig.text_color,
                  marginBottom: '16px',
                }}
              >
                {emotions[activeTab].keywords || t('no_keywords_available')}
              </p>
              <button
                style={{
                  padding: '8px 16px',
                  backgroundColor: listConfig.primaryColor,
                  color: '#FFFFFF',
                  border: 'none',
                  borderRadius: `${listConfig.borderRadius}px`,
                  cursor: 'pointer',
                  fontSize: `${listConfig.font_size}px`,
                  fontFamily: listConfig.font_family,
                }}
                onClick={() => onSelect(emotions[activeTab])}
              >
                {t('select')}
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 渲染情绪内容
  const renderEmotionContent = (emotion: Emotion) => {
    return (
      <>
        {renderEmotionIcon(emotion)}
        <span
          style={{
            fontSize: `${listConfig.font_size}px`,
            fontFamily: listConfig.font_family,
            color: listConfig.text_color,
          }}
        >
          {emotion.name}
        </span>
      </>
    );
  };

  // 渲染情绪图标（表情、图片等）
  const renderEmotionIcon = (emotion: Emotion, size?: number) => {
    const iconSize = size || listConfig.emojiSize;

    if (contentDisplayMode === 'image' && emotion.image_url) {
      return (
        <img
          src={emotion.image_url}
          alt={emotion.name}
          style={{
            width: `${iconSize}px`,
            height: `${iconSize}px`,
            borderRadius: '50%',
            objectFit: 'cover',
          }}
        />
      );
    }
    if (contentDisplayMode === 'animatedEmoji') {
      const emojiItem = getEmojiItemForEmotionId(emotion.id);
      if (emojiItem) {
        return (
          <AnimatedEmoji
            emojiItem={emojiItem}
            size={
              iconSize >= 40
                ? '4xl'
                : iconSize >= 32
                  ? '3xl'
                  : iconSize >= 24
                    ? '2xl'
                    : iconSize >= 20
                      ? 'xl'
                      : iconSize >= 16
                        ? 'lg'
                        : iconSize >= 14
                          ? 'md'
                          : iconSize >= 12
                            ? 'sm'
                            : 'xs'
            }
            autoPlay={true}
            loop={true}
          />
        );
      }
    }

    return (
      <span
        style={{
          fontSize: `${iconSize}px`,
          lineHeight: 1,
        }}
      >
        {emotion.emoji}
      </span>
    );
  };

  // 渲染返回按钮
  const renderBackButton = () => {
    if (!onBack || tierLevel === 1) return null;

    return (
      <button
        onClick={onBack}
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          color: listConfig.text_color,
          padding: '5px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          boxShadow: listConfig.shadowEnabled ? `0 0 5px ${listConfig.shadowColor}` : 'none',
        }}
      >
        ←
      </button>
    );
  };

  // 渲染选中路径
  const renderSelectedPath = () => {
    if (!selectedPath) return null;

    return (
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 100,
          fontSize: `${listConfig.font_size}px`,
          fontFamily: listConfig.font_family,
          color: listConfig.text_color,
          padding: '5px',
          borderRadius: '5px',
          background: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(5px)',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {selectedPath}
      </div>
    );
  };

  return (
    <div
      className="list-view"
      style={{
        width: '100%',
        maxWidth: '800px',
        margin: '0 auto',
        padding: '16px',
        position: 'relative',
      }}
    >
      {renderBackButton()}
      {renderSelectedPath()}

      {/* 标题 */}
      <h3
        className="tier-title"
        style={{
          textAlign: 'center',
          marginBottom: '16px',
          fontSize: `${skinConfig.fonts?.size?.large || 18}px`,
          fontWeight: skinConfig.fonts?.weight?.bold || 700,
          fontFamily: skinConfig.fonts?.family || 'Arial, sans-serif',
          color: skinConfig.colors?.text || '#000000',
        }}
      >
        {t(`tier_level.${tierLevel}`, { fallback: `第${tierLevel}级情绪` })}
      </h3>

      {/* 列表布局 */}
      {layout === 'vertical' && renderVerticalList()}
      {layout === 'horizontal' && renderHorizontalList()}
      {layout === 'accordion' && renderAccordionList()}
      {layout === 'tabs' && renderTabsList()}
    </div>
  );
};
