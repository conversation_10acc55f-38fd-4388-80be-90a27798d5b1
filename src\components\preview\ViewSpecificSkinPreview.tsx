/**
 * 视图特定的皮肤预览组件
 * 根据视图类型选择合适的预览组件
 */

import type { Skin, ViewType } from '@/types';
import type React from 'react';
import BubbleSkinPreview from './BubbleSkinPreview';
import CardSkinPreview from './CardSkinPreview';
import GalaxySkinPreview from './GalaxySkinPreview';
import WheelSkinPreview from './WheelSkinPreview';

interface ViewSpecificSkinPreviewProps {
  skin: Skin;
  viewType: ViewType;
  size?: 'sm' | 'md' | 'lg';
  showControls?: boolean;
  className?: string;
}

/**
 * 视图特定的皮肤预览组件
 */
const ViewSpecificSkinPreview: React.FC<ViewSpecificSkinPreviewProps> = ({
  skin,
  viewType,
  size = 'md',
  showControls = true,
  className = '',
}) => {
  // 检查皮肤是否支持指定的视图类型
  const isSupportedViewType = (type: ViewType): boolean => {
    if (!skin.config.view_configs) return false;
    return !!skin.config.view_configs[type];
  };

  // 渲染特定视图类型的预览
  const renderPreview = () => {
    // 如果皮肤不支持指定的视图类型，显示不支持消息
    if (!isSupportedViewType(viewType)) {
      return (
        <div className="flex items-center justify-center p-4 text-center text-muted-foreground">
          此皮肤不支持
          {viewType === 'wheel'
            ? '轮盘'
            : viewType === 'card'
              ? '卡片'
              : viewType === 'bubble'
                ? '气泡'
                : viewType === 'galaxy'
                  ? '星系'
                  : ''}
          视图
        </div>
      );
    }

    // 根据视图类型选择合适的预览组件
    switch (viewType) {
      case 'wheel':
        return <WheelSkinPreview skin={skin} size={size} showControls={showControls} />;
      case 'card':
        return <CardSkinPreview skin={skin} size={size} showControls={showControls} />;
      case 'bubble':
        return <BubbleSkinPreview skin={skin} size={size} showControls={showControls} />;
      case 'galaxy':
        return <GalaxySkinPreview skin={skin} size={size} showControls={showControls} />;
      default:
        return null;
    }
  };

  return <div className={`view-specific-skin-preview ${className}`}>{renderPreview()}</div>;
};

export default ViewSpecificSkinPreview;
