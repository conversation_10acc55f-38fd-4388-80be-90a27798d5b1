#!/usr/bin/env node

/**
 * 批量修复类型导入脚本
 * 将分散的类型导入统一为 @/types 导入
 */

const fs = require('fs');
const path = require('path');

// 需要替换的导入模式
const importPatterns = [
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/emotionDataTypes';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  },
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/previewTypes';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  },
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/skinTypes';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  },
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/userConfigTypes';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  },
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/emojiTypes';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  },
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/mood';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  },
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/tagTypes';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  },
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/layoutTypes';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  },
  {
    pattern: /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/displayTypes';?/g,
    replacement: (match, imports) => `// REPLACED: ${match.trim()}`
  }
];

// 获取所有需要修复的文件
function getAllTsFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getAllTsFiles(fullPath));
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 提取导入的类型名称
function extractImports(content) {
  const imports = new Set();
  
  // 匹配所有旧的导入语句
  const patterns = [
    /import\s+(?:type\s+)?{([^}]+)}\s+from\s+'@\/types\/\w+';?/g,
    /import\s+(?:type\s+)?([^,\s{]+)\s+from\s+'@\/types\/\w+';?/g
  ];
  
  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const importList = match[1];
      if (importList.includes('{')) {
        // 处理 { type A, B, type C } 格式
        const items = importList.split(',').map(item => {
          return item.trim().replace(/^type\s+/, '').replace(/^\s*type\s+/, '');
        });
        items.forEach(item => {
          if (item.trim()) {
            imports.add(item.trim());
          }
        });
      } else {
        // 处理单个导入
        imports.add(importList.trim().replace(/^type\s+/, ''));
      }
    }
  }
  
  return Array.from(imports);
}

// 修复单个文件
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // 提取所有需要导入的类型
    const allImports = extractImports(content);
    
    if (allImports.length === 0) {
      return false; // 没有需要修复的导入
    }
    
    // 移除所有旧的导入语句
    for (const pattern of importPatterns) {
      content = content.replace(pattern.pattern, pattern.replacement);
    }
    
    // 添加统一的导入语句
    const unifiedImport = `import type { ${allImports.join(', ')} } from '@/types';`;
    
    // 找到第一个 import 语句的位置
    const firstImportMatch = content.match(/^import\s+/m);
    if (firstImportMatch) {
      const insertIndex = content.indexOf(firstImportMatch[0]);
      content = content.slice(0, insertIndex) + unifiedImport + '\n' + content.slice(insertIndex);
    } else {
      // 如果没有找到 import 语句，添加到文件开头
      content = unifiedImport + '\n' + content;
    }
    
    // 清理重复的换行和注释
    content = content.replace(/\/\/ REPLACED:.*\n/g, '');
    content = content.replace(/\n{3,}/g, '\n\n');
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  const files = getAllTsFiles(srcDir);
  
  console.log(`Found ${files.length} TypeScript files`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    if (fixFile(file)) {
      console.log(`: ${file}`);
      fixedCount++;
    }
  }
  
  console.log(`\n ${fixedCount} files`);
}

if (require.main === module) {
  main();
}
