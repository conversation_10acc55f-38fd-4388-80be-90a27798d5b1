# Quiz特殊视图设计文档

## 🎯 设计概述

基于已实现的基础组件，构建4种特殊视图：wheel（情绪轮盘）、card（卡片视图）、bubble（气泡视图）、galaxy（星系视图）。这些视图专门用于Quiz量表问题的展示和交互。

## 🏗️ 架构关系

```
特殊视图层 (Special Views)
├── EmotionWheelView
├── EmotionCardView  
├── EmotionBubbleView
└── EmotionGalaxyView
    ↓ 基于 (Built on)
基础组件层 (Base Components)
├── SelectorComponent
├── ButtonComponent
├── TextComponent
├── ProgressIndicatorComponent
└── MediaComponent
```

## 🎨 特殊视图设计

### 1. 情绪轮盘视图 (EmotionWheelView)

#### 设计理念
- **视觉隐喻**: 传统中医的阴阳八卦轮盘
- **交互方式**: 点击扇形区域选择情绪
- **层次结构**: 支持多层级情绪细分

#### 组件构成
```typescript
interface EmotionWheelViewProps {
  config: WheelViewConfig;
  emotions: EmotionOption[];
  onEmotionSelect: (emotionId: string, metadata?: SelectionMetadata) => void;
  selectedEmotions: string[];
}

// 基于基础组件构建
const EmotionWheelView: React.FC<EmotionWheelViewProps> = ({
  config,
  emotions,
  onEmotionSelect,
  selectedEmotions
}) => {
  return (
    <div className="emotion-wheel-container">
      {/* 中心文本组件 - 显示当前层级标题 */}
      <TextComponent
        config={{
          layout_id: "standard_text",
          style: { size: "large", alignment: "center" },
          content: { text_localized: { zh: "选择您的情绪" } }
        }}
      />
      
      {/* 轮盘选择器组件 - 核心交互区域 */}
      <SelectorComponent
        config={{
          layout_id: "wheel_layout", // 特殊的轮盘布局
          style: {
            selection_mode: config.selection_mode,
            marker_style: "chinese_marker",
            animation_style: "scale"
          }
        }}
        options={emotions.map(emotion => ({
          id: emotion.id,
          value: emotion.id,
          text_localized: { zh: emotion.name },
          display_style: "wheel_sector" // 特殊的扇形显示样式
        }))}
        selectedValues={selectedEmotions}
        onSelectionChange={(values) => {
          values.forEach(value => onEmotionSelect(value as string));
        }}
      />
      
      {/* 进度指示器 - 显示层级进度 */}
      <ProgressIndicatorComponent
        config={{
          layout_id: "lotus_blooming",
          style: { position: "bottom" }
        }}
        current={config.current_tier}
        total={config.total_tiers}
      />
    </div>
  );
};
```

#### 特殊布局实现
```typescript
// 扩展SelectorComponent以支持轮盘布局
const WheelSelectorLayout: React.FC<WheelSelectorProps> = ({
  options,
  selectedValues,
  onSelectionChange,
  config
}) => {
  const wheelRadius = config.wheel_radius || 180;
  const sectorAngle = (2 * Math.PI) / options.length;
  
  return (
    <svg width={wheelRadius * 2} height={wheelRadius * 2} className="wheel-selector">
      {options.map((option, index) => {
        const angle = index * sectorAngle;
        const isSelected = selectedValues.includes(option.value);
        
        return (
          <WheelSector
            key={option.id}
            option={option}
            angle={angle}
            sectorAngle={sectorAngle}
            radius={wheelRadius}
            isSelected={isSelected}
            onClick={() => onSelectionChange([option.value])}
          />
        );
      })}
    </svg>
  );
};
```

### 2. 情绪卡片视图 (EmotionCardView)

#### 设计理念
- **视觉隐喻**: 中医药材标本卡片
- **交互方式**: 点击卡片选择情绪
- **布局特点**: 网格排列，支持图文结合

#### 组件构成
```typescript
const EmotionCardView: React.FC<EmotionCardViewProps> = ({
  config,
  emotions,
  onEmotionSelect,
  selectedEmotions
}) => {
  return (
    <div className="emotion-card-container">
      {/* 标题文本组件 */}
      <TextComponent
        config={{
          layout_id: "dialogue_bubble",
          style: { size: "medium", alignment: "center" },
          content: { text_localized: { zh: "请选择最符合您感受的情绪卡片" } }
        }}
      />
      
      {/* 卡片网格选择器 */}
      <SelectorComponent
        config={{
          layout_id: "grid_layout",
          style: {
            selection_mode: config.selection_mode,
            spacing: 16,
            hover_effect: true,
            animation_style: "scale"
          }
        }}
        options={emotions.map(emotion => ({
          id: emotion.id,
          value: emotion.id,
          text_localized: { zh: emotion.name },
          display_style: "card_style",
          icon_name: emotion.icon
        }))}
        selectedValues={selectedEmotions}
        onSelectionChange={(values) => {
          values.forEach(value => onEmotionSelect(value as string));
        }}
      />
      
      {/* 确认按钮 */}
      {selectedEmotions.length > 0 && (
        <ButtonComponent
          config={{
            layout_id: "jade_pendant",
            style: {
              size: "large",
              variant: "primary"
            },
            content: {
              text_localized: { zh: "确认选择" }
            }
          }}
          onClick={() => {
            // 处理确认逻辑
          }}
        />
      )}
    </div>
  );
};
```

### 3. 情绪气泡视图 (EmotionBubbleView)

#### 设计理念
- **视觉隐喻**: 水墨画中的墨滴气泡
- **交互方式**: 点击浮动气泡选择情绪
- **动画特点**: 气泡浮动、碰撞、融合效果

#### 组件构成
```typescript
const EmotionBubbleView: React.FC<EmotionBubbleViewProps> = ({
  config,
  emotions,
  onEmotionSelect,
  selectedEmotions
}) => {
  const [bubblePositions, setBubblePositions] = useState<BubblePosition[]>([]);
  
  useEffect(() => {
    // 初始化气泡位置
    const positions = generateBubblePositions(emotions, config.container_size);
    setBubblePositions(positions);
    
    // 启动气泡动画
    const animationId = startBubbleAnimation(positions, setBubblePositions);
    
    return () => cancelAnimationFrame(animationId);
  }, [emotions, config.container_size]);
  
  return (
    <div className="emotion-bubble-container">
      {/* 背景文本提示 */}
      <TextComponent
        config={{
          layout_id: "standard_text",
          style: { 
            size: "medium", 
            alignment: "center",
            color_scheme: "rgba(0,0,0,0.6)"
          },
          content: { 
            text_localized: { zh: "轻触气泡，感受情绪的流动" },
            animation_effect: "fade_in"
          }
        }}
      />
      
      {/* 气泡容器 */}
      <div className="bubble-physics-container">
        {emotions.map((emotion, index) => {
          const position = bubblePositions[index];
          const isSelected = selectedEmotions.includes(emotion.id);
          
          return (
            <EmotionBubble
              key={emotion.id}
              emotion={emotion}
              position={position}
              isSelected={isSelected}
              onClick={() => onEmotionSelect(emotion.id)}
              config={config.bubble_config}
            />
          );
        })}
      </div>
      
      {/* 选择反馈 */}
      {selectedEmotions.length > 0 && (
        <div className="selection-feedback">
          <TextComponent
            config={{
              layout_id: "standard_text",
              style: { size: "small", alignment: "center" },
              content: {
                text_localized: { 
                  zh: `已选择 ${selectedEmotions.length} 个情绪` 
                }
              }
            }}
          />
        </div>
      )}
    </div>
  );
};
```

### 4. 情绪星系视图 (EmotionGalaxyView) - VIP功能

#### 设计理念
- **视觉隐喻**: 宇宙星系中的情绪星球
- **交互方式**: 3D空间中的星球点击和轨道导航
- **高级特性**: WebGL渲染、物理引擎、粒子效果

#### 组件构成
```typescript
const EmotionGalaxyView: React.FC<EmotionGalaxyViewProps> = ({
  config,
  emotions,
  onEmotionSelect,
  selectedEmotions
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [galaxyEngine, setGalaxyEngine] = useState<GalaxyEngine | null>(null);
  
  useEffect(() => {
    if (canvasRef.current) {
      const engine = new GalaxyEngine(canvasRef.current, config);
      engine.initializeEmotionPlanets(emotions);
      setGalaxyEngine(engine);
      
      return () => engine.dispose();
    }
  }, [emotions, config]);
  
  return (
    <div className="emotion-galaxy-container">
      {/* 3D画布 */}
      <canvas
        ref={canvasRef}
        className="galaxy-canvas"
        width={config.canvas_width}
        height={config.canvas_height}
      />
      
      {/* 控制面板 */}
      <div className="galaxy-controls">
        <ButtonComponent
          config={{
            layout_id: "standard_button",
            style: { size: "small", variant: "ghost" },
            content: { text_localized: { zh: "重置视角" } }
          }}
          onClick={() => galaxyEngine?.resetCamera()}
        />
        
        <ButtonComponent
          config={{
            layout_id: "standard_button",
            style: { size: "small", variant: "ghost" },
            content: { text_localized: { zh: "自动旋转" } }
          }}
          onClick={() => galaxyEngine?.toggleAutoRotate()}
        />
      </div>
      
      {/* 信息面板 */}
      <div className="galaxy-info-panel">
        <TextComponent
          config={{
            layout_id: "standard_text",
            style: { size: "small", alignment: "left" },
            content: {
              text_localized: { 
                zh: "拖拽旋转视角，点击星球选择情绪" 
              }
            }
          }}
        />
        
        {/* 选中情绪详情 */}
        {selectedEmotions.length > 0 && (
          <div className="selected-emotions-info">
            {selectedEmotions.map(emotionId => {
              const emotion = emotions.find(e => e.id === emotionId);
              return emotion ? (
                <TextComponent
                  key={emotionId}
                  config={{
                    layout_id: "standard_text",
                    style: { size: "small", color_scheme: emotion.color },
                    content: {
                      text_localized: { zh: `✨ ${emotion.name}` }
                    }
                  }}
                />
              ) : null;
            })}
          </div>
        )}
      </div>
    </div>
  );
};
```

## 🔧 技术实现要点

### 1. 视图工厂集成

```typescript
// 扩展ViewFactory以支持特殊视图
class QuizSpecialViewFactory extends QuizEnhancedViewFactory {
  createSpecialView(
    viewType: 'wheel' | 'card' | 'bubble' | 'galaxy',
    questionData: QuestionPresentationData,
    additionalProps?: Record<string, any>
  ): React.ReactElement | null {
    
    const viewConfig = this.extractViewConfig(questionData, viewType);
    const emotions = this.extractEmotionOptions(questionData);
    
    switch (viewType) {
      case 'wheel':
        return React.createElement(EmotionWheelView, {
          config: viewConfig,
          emotions,
          ...additionalProps
        });
        
      case 'card':
        return React.createElement(EmotionCardView, {
          config: viewConfig,
          emotions,
          ...additionalProps
        });
        
      case 'bubble':
        return React.createElement(EmotionBubbleView, {
          config: viewConfig,
          emotions,
          ...additionalProps
        });
        
      case 'galaxy':
        return React.createElement(EmotionGalaxyView, {
          config: viewConfig,
          emotions,
          ...additionalProps
        });
        
      default:
        return null;
    }
  }
}
```

### 2. 配置映射系统

```typescript
// 将问题呈现数据映射为特殊视图配置
class ViewConfigMapper {
  static mapToWheelConfig(
    questionData: QuestionPresentationData
  ): WheelViewConfig {
    const uiConfig = questionData.personalized_ui_config;
    
    return {
      selection_mode: uiConfig.selection_mode || 'single',
      wheel_radius: uiConfig.wheel_config?.wheel_radius || 180,
      container_size: uiConfig.wheel_config?.container_size || 400,
      current_tier: questionData.progress_info.current,
      total_tiers: questionData.progress_info.total,
      animation_style: uiConfig.wheel_config?.tier_transition_animation || 'rotate'
    };
  }
  
  static mapToCardConfig(
    questionData: QuestionPresentationData
  ): CardViewConfig {
    const uiConfig = questionData.personalized_ui_config;
    
    return {
      selection_mode: uiConfig.selection_mode || 'single',
      cards_per_row: uiConfig.card_config?.cards_per_row || 2,
      card_spacing: uiConfig.card_config?.card_spacing || 16,
      show_shadows: uiConfig.card_config?.show_card_shadows ?? true,
      animation_style: 'scale'
    };
  }
}
```

### 3. 性能优化策略

```typescript
// 特殊视图性能优化
class SpecialViewOptimizer {
  // 轮盘视图优化
  static optimizeWheelView(emotions: EmotionOption[]): EmotionOption[] {
    // 限制最大情绪数量，避免扇形过小
    const maxEmotions = 12;
    return emotions.slice(0, maxEmotions);
  }
  
  // 气泡视图优化
  static optimizeBubbleView(emotions: EmotionOption[]): EmotionOption[] {
    // 限制气泡数量，避免性能问题
    const maxBubbles = 20;
    return emotions.slice(0, maxBubbles);
  }
  
  // 星系视图优化（VIP功能）
  static optimizeGalaxyView(emotions: EmotionOption[]): EmotionOption[] {
    // 星系视图支持更多情绪，但需要LOD优化
    return emotions;
  }
}
```

## 🎯 使用示例

```typescript
// Quiz页面中使用特殊视图
const QuizQuestionPage: React.FC<QuizQuestionPageProps> = ({ 
  questionData, 
  onAnswerSubmit 
}) => {
  const viewFactory = new QuizSpecialViewFactory();
  const preferredViewType = questionData.personalized_ui_config.view_type || 'wheel';
  
  const handleEmotionSelect = (emotionId: string, metadata?: any) => {
    onAnswerSubmit({
      emotion_id: emotionId,
      confidence: metadata?.confidence,
      response_time: metadata?.responseTime,
      view_type_used: preferredViewType
    });
  };
  
  const specialView = viewFactory.createSpecialView(
    preferredViewType,
    questionData,
    {
      onEmotionSelect: handleEmotionSelect,
      selectedEmotions: []
    }
  );
  
  return (
    <div className="quiz-question-page">
      {specialView}
    </div>
  );
};
```

这个设计文档展示了如何基于基础组件构建专门的Quiz特殊视图，实现了组件复用和架构清晰的目标，同时为不同的用户偏好提供了丰富的交互方式。
