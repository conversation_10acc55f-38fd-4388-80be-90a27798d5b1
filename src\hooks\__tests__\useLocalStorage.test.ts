/**
 * useLocalStorage Hook 测试
 * 测试本地存储功能
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useLocalStorage } from '../useLocalStorage';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      store = {};
    }),
    get length() {
      return Object.keys(store).length;
    },
    key: vi.fn((index: number) => Object.keys(store)[index] || null)
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true
});

describe('useLocalStorage', () => {
  beforeEach(() => {
    localStorageMock.clear();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should return initial value when localStorage is empty', () => {
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'));

      expect(result.current[0]).toBe('initial-value');
      expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key');
    });

    it('should return stored value when localStorage has data', () => {
      localStorageMock.setItem('test-key', JSON.stringify('stored-value'));

      const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'));

      expect(result.current[0]).toBe('stored-value');
    });

    it('should handle different data types', () => {
      // Test with object
      const testObject = { name: 'test', value: 123 };
      localStorageMock.setItem('object-key', JSON.stringify(testObject));

      const { result: objectResult } = renderHook(() => 
        useLocalStorage('object-key', { name: '', value: 0 })
      );

      expect(objectResult.current[0]).toEqual(testObject);

      // Test with array
      const testArray = [1, 2, 3, 4, 5];
      localStorageMock.setItem('array-key', JSON.stringify(testArray));

      const { result: arrayResult } = renderHook(() => 
        useLocalStorage('array-key', [] as number[])
      );

      expect(arrayResult.current[0]).toEqual(testArray);

      // Test with boolean
      localStorageMock.setItem('boolean-key', JSON.stringify(true));

      const { result: booleanResult } = renderHook(() => 
        useLocalStorage('boolean-key', false)
      );

      expect(booleanResult.current[0]).toBe(true);

      // Test with number
      localStorageMock.setItem('number-key', JSON.stringify(42));

      const { result: numberResult } = renderHook(() => 
        useLocalStorage('number-key', 0)
      );

      expect(numberResult.current[0]).toBe(42);
    });

    it('should handle invalid JSON gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      localStorageMock.setItem('invalid-key', 'invalid-json{');

      const { result } = renderHook(() => useLocalStorage('invalid-key', 'default-value'));

      expect(result.current[0]).toBe('default-value');
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error reading localStorage key "invalid-key":',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });

    it('should handle localStorage access errors', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage access denied');
      });

      const { result } = renderHook(() => useLocalStorage('error-key', 'default-value'));

      expect(result.current[0]).toBe('default-value');
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error reading localStorage key "error-key":',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Setting Values', () => {
    it('should update localStorage when value is set', () => {
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

      act(() => {
        result.current[1]('new-value');
      });

      expect(result.current[0]).toBe('new-value');
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'test-key',
        JSON.stringify('new-value')
      );
    });

    it('should handle function updates', () => {
      const { result } = renderHook(() => useLocalStorage('counter', 0));

      act(() => {
        result.current[1](prev => prev + 1);
      });

      expect(result.current[0]).toBe(1);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'counter',
        JSON.stringify(1)
      );

      act(() => {
        result.current[1](prev => prev * 2);
      });

      expect(result.current[0]).toBe(2);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'counter',
        JSON.stringify(2)
      );
    });

    it('should handle complex object updates', () => {
      const initialUser = { name: 'John', age: 30, preferences: { theme: 'dark' } };
      const { result } = renderHook(() => useLocalStorage('user', initialUser));

      act(() => {
        result.current[1](prev => ({
          ...prev,
          age: 31,
          preferences: { ...prev.preferences, theme: 'light' }
        }));
      });

      expect(result.current[0]).toEqual({
        name: 'John',
        age: 31,
        preferences: { theme: 'light' }
      });
    });

    it('should handle localStorage write errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage quota exceeded');
      });

      const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

      act(() => {
        result.current[1]('new-value');
      });

      // State should still update even if localStorage fails
      expect(result.current[0]).toBe('new-value');
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error setting localStorage key "test-key":',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Storage Event Handling', () => {
    it('should update when storage event is fired', () => {
      const { result } = renderHook(() => useLocalStorage('shared-key', 'initial'));

      expect(result.current[0]).toBe('initial');

      // Simulate storage event from another tab
      act(() => {
        const storageEvent = new StorageEvent('storage', {
          key: 'shared-key',
          newValue: JSON.stringify('updated-from-another-tab'),
          oldValue: JSON.stringify('initial')
        });
        window.dispatchEvent(storageEvent);
      });

      expect(result.current[0]).toBe('updated-from-another-tab');
    });

    it('should ignore storage events for different keys', () => {
      const { result } = renderHook(() => useLocalStorage('my-key', 'my-value'));

      expect(result.current[0]).toBe('my-value');

      // Simulate storage event for different key
      act(() => {
        const storageEvent = new StorageEvent('storage', {
          key: 'other-key',
          newValue: JSON.stringify('other-value'),
          oldValue: null
        });
        window.dispatchEvent(storageEvent);
      });

      // Value should remain unchanged
      expect(result.current[0]).toBe('my-value');
    });

    it('should ignore storage events with null newValue', () => {
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

      expect(result.current[0]).toBe('initial');

      // Simulate storage event with null newValue (item removed)
      act(() => {
        const storageEvent = new StorageEvent('storage', {
          key: 'test-key',
          newValue: null,
          oldValue: JSON.stringify('initial')
        });
        window.dispatchEvent(storageEvent);
      });

      // Value should remain unchanged
      expect(result.current[0]).toBe('initial');
    });

    it('should handle invalid JSON in storage events gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

      expect(result.current[0]).toBe('initial');

      // Simulate storage event with invalid JSON
      act(() => {
        const storageEvent = new StorageEvent('storage', {
          key: 'test-key',
          newValue: 'invalid-json{',
          oldValue: JSON.stringify('initial')
        });
        window.dispatchEvent(storageEvent);
      });

      // Value should remain unchanged
      expect(result.current[0]).toBe('initial');
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error parsing localStorage key "test-key":',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Multiple Instances', () => {
    it('should sync between multiple instances of the same key', () => {
      const { result: result1 } = renderHook(() => useLocalStorage('shared', 'initial'));
      const { result: result2 } = renderHook(() => useLocalStorage('shared', 'initial'));

      expect(result1.current[0]).toBe('initial');
      expect(result2.current[0]).toBe('initial');

      // Update from first instance
      act(() => {
        result1.current[1]('updated');
      });

      expect(result1.current[0]).toBe('updated');

      // Simulate storage event to sync second instance
      act(() => {
        const storageEvent = new StorageEvent('storage', {
          key: 'shared',
          newValue: JSON.stringify('updated'),
          oldValue: JSON.stringify('initial')
        });
        window.dispatchEvent(storageEvent);
      });

      expect(result2.current[0]).toBe('updated');
    });

    it('should handle different keys independently', () => {
      const { result: result1 } = renderHook(() => useLocalStorage('key1', 'value1'));
      const { result: result2 } = renderHook(() => useLocalStorage('key2', 'value2'));

      expect(result1.current[0]).toBe('value1');
      expect(result2.current[0]).toBe('value2');

      // Update first key
      act(() => {
        result1.current[1]('new-value1');
      });

      expect(result1.current[0]).toBe('new-value1');
      expect(result2.current[0]).toBe('value2'); // Should remain unchanged
    });
  });

  describe('Cleanup', () => {
    it('should remove event listener on unmount', () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener');
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');

      const { unmount } = renderHook(() => useLocalStorage('test-key', 'initial'));

      expect(addEventListenerSpy).toHaveBeenCalledWith('storage', expect.any(Function));

      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('storage', expect.any(Function));

      addEventListenerSpy.mockRestore();
      removeEventListenerSpy.mockRestore();
    });
  });

  describe('Type Safety', () => {
    it('should maintain type safety with TypeScript', () => {
      // String type
      const { result: stringResult } = renderHook(() => useLocalStorage('string-key', 'test'));
      expect(typeof stringResult.current[0]).toBe('string');

      // Number type
      const { result: numberResult } = renderHook(() => useLocalStorage('number-key', 42));
      expect(typeof numberResult.current[0]).toBe('number');

      // Boolean type
      const { result: booleanResult } = renderHook(() => useLocalStorage('boolean-key', true));
      expect(typeof booleanResult.current[0]).toBe('boolean');

      // Object type
      interface TestObject {
        name: string;
        count: number;
      }
      const { result: objectResult } = renderHook(() => 
        useLocalStorage<TestObject>('object-key', { name: 'test', count: 0 })
      );
      expect(typeof objectResult.current[0]).toBe('object');
      expect(objectResult.current[0]).toHaveProperty('name');
      expect(objectResult.current[0]).toHaveProperty('count');
    });
  });
});
