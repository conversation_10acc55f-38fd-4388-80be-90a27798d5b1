# 数据库初始化设计

## 概述

本文档描述了应用程序中数据库初始化的设计和实现，包括本地数据库和云端数据库的初始化过程。我们采用了将数据库初始化逻辑与UI组件分离的设计模式，以提高代码的可维护性和可测试性。

## 设计目标

1. **分离关注点**：将数据库初始化逻辑与UI渲染逻辑分离
2. **提高可测试性**：使数据库初始化逻辑更容易进行单元测试
3. **提高可维护性**：集中管理数据库初始化逻辑，避免重复代码
4. **提供灵活配置**：在不同环境（开发、测试、生产）中灵活配置数据库初始化行为
5. **提供进度反馈**：向用户提供数据库初始化进度的反馈

## 架构设计

### 组件结构

```
src/
├── services/
│   ├── DatabaseManager.ts        # 数据库管理器，集中管理数据库初始化逻辑
│   ├── DatabaseService.ts        # 本地数据库服务
│   └── TursoInitializationService.ts  # 云端数据库服务
├── components/
│   ├── DatabaseInitializer.tsx   # 本地数据库初始化UI组件
│   ├── EmotionDataInitializer.tsx # 情绪数据初始化UI组件
│   └── TursoInitializer.tsx      # 云端数据库初始化UI组件
└── App.tsx                       # 应用程序入口
```

### 数据流

1. `App.tsx` 加载 `DatabaseInitializer`、`EmotionDataInitializer` 和 `TursoInitializer` 组件
2. 这些组件使用 `DatabaseManager` 服务来初始化数据库
3. `DatabaseManager` 服务使用 `DatabaseService` 和 `TursoInitializationService` 来实际执行数据库初始化操作
4. 初始化过程中的进度和错误通过回调函数传递给UI组件

## 实现细节

### DatabaseManager

`DatabaseManager` 是一个单例服务，负责管理数据库初始化逻辑。它提供以下主要功能：

- `initializeLocalDatabase`：初始化本地SQLite数据库
- `initializeCloudDatabase`：初始化云端Turso数据库
- `initializeEmotionData`：初始化情绪数据

每个初始化方法都接受回调函数，用于报告进度和错误。

### 数据库初始化组件

- `DatabaseInitializer`：负责初始化本地SQLite数据库，并显示进度
- `EmotionDataInitializer`：负责初始化情绪数据，并显示进度
- `TursoInitializer`：负责初始化云端Turso数据库，并显示进度

这些组件使用 `DatabaseManager` 服务来执行实际的初始化操作，并将进度和错误显示给用户。

## 数据库初始化流程

1. **本地数据库初始化**：
   - 创建数据库架构
   - 加载系统配置数据
   - 加载用户数据
   - 标记初始化完成

2. **情绪数据初始化**：
   - 从数据库加载默认情绪数据
   - 根据当前语言加载本地化数据

3. **云端数据库初始化**：
   - 创建数据库架构
   - 加载系统配置数据
   - 在开发环境中加载测试用户数据
   - 标记初始化完成

## 错误处理

- 每个初始化组件都有重试机制，最多尝试3次
- 在开发和测试环境中，即使初始化失败也会继续运行应用程序
- 在生产环境中，会显示错误消息，但仍然允许用户使用应用程序的基本功能

## 配置

数据库初始化行为可以通过环境变量进行配置：

- `MODE`：环境模式（development、test、production）
- 在开发和测试环境中，初始化失败后会自动重试
- 在生产环境中，初始化失败后会显示错误消息

## 测试

数据库初始化逻辑可以通过以下方式进行测试：

1. **单元测试**：测试 `DatabaseManager` 服务的各个方法
2. **集成测试**：测试数据库初始化组件与 `DatabaseManager` 服务的集成
3. **端到端测试**：测试整个应用程序的数据库初始化流程

## 未来改进

1. 添加更详细的进度指示器，显示初始化的各个阶段
2. 添加更多的配置选项，允许用户自定义数据库初始化行为
3. 添加数据库迁移功能，支持数据库架构的升级
4. 添加数据库备份和恢复功能
