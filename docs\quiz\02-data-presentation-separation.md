# 数据与展现分离架构

## 🎯 核心设计原则

### 完全的数据与展现分离
- **数据层**: 纯粹的量表内容、逻辑、评估规则
- **展现层**: 用户个性化配置、UI样式、交互方式  
- **会话层**: 运行时的个性化展现配置快照

### 设计优势
1. **同一量表，多样展现**: 相同的量表数据可以产生完全不同的用户体验
2. **独立演进**: 数据和展现可以独立迭代和优化
3. **高度个性化**: 支持用户级别的深度定制
4. **易于维护**: 职责清晰，便于团队协作

## 🏗️ 架构分层

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│              (User Personalization Configs)                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ User Base   │ │ Pack Specific│ │ Session Dynamic Rules  │ │
│  │ Config      │ │ Overrides    │ │ & Context Adaptation   │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────┬───────────────────────────────────┘
                          │ Runtime Merge
┌─────────────────────────┴───────────────────────────────────┐
│                    Session Layer                            │
│              (Runtime Configuration Snapshot)              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │         Merged Presentation Configuration              │ │
│  │    (User Config + Overrides + Dynamic Rules)          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────┬───────────────────────────────────┘
                          │ Applied to
┌─────────────────────────┴───────────────────────────────────┐
│                     Data Layer                              │
│                (Pure Quiz Logic & Content)                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Quiz Packs  │ │ Emotion     │ │ Evaluation Strategies  │ │
│  │ (Logic Only)│ │ Datasets    │ │ (Pure Algorithms)      │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📊 数据层设计 (纯数据，无展现)

### 1. 量表包数据结构
```typescript
// 纯数据的量表包 - 不包含任何展现信息
interface EmotionQuizPack {
  pack_id: string;
  version: string;
  title_key: string;
  description_key: string;
  
  // 关联的情绪数据集
  emotion_data_set_id: string;
  
  // 量表逻辑配置 (纯逻辑，无展现)
  quiz_logic_config: {
    question_flow: {
      type: 'tier_sequential' | 'adaptive_branching' | 'user_guided';
      tier_progression_rules?: TierProgressionRule[];
    };
    
    // 层级作为问题的逻辑配置
    tier_question_configs: {
      [tierId: string]: TierQuestionLogicConfig;
    };
    
    // 评估策略 (纯逻辑)
    evaluation_strategy_id: string;
    evaluation_strategy_config: EmotionEvaluationConfig;
    
    // 完成条件 (纯逻辑)
    completion_criteria: {
      min_questions_answered: number;
      allow_skip: boolean;
      max_skip_count?: number;
    };
  };
  
  // 默认展现建议 (可选，用户可完全覆盖)
  default_presentation_hints?: {
    suggested_view_type: ViewType;
    suggested_interaction_style: InteractionStyle;
    content_complexity_level: 'simple' | 'standard' | 'detailed';
  };
  
  // 元数据
  metadata: QuizPackMetadata;
}

// 层级问题逻辑配置 (纯逻辑，无展现)
interface TierQuestionLogicConfig {
  tier_id: string;
  question_text_key: string;
  question_type: 'EMOTION_WHEEL_SELECT' | 'EMOTION_INTENSITY_RATING';
  
  // 逻辑配置
  logic_config: {
    selection_mode: 'single' | 'multiple' | 'rating';
    validation_rules: ValidationRule[];
    branching_logic?: BranchingRule[];
    scoring_rules?: ScoringRule[];
  };
  
  // 情绪选项逻辑配置
  emotion_options_logic: {
    inclusion_strategy: 'all_emotions' | 'filtered_by_category';
    filter_criteria?: EmotionFilterCriteria;
    max_options?: number;
    randomize_order?: boolean;
  };
  
  // 时间和导航逻辑
  navigation_logic: {
    time_limit?: number;
    allow_skip: boolean;
    allow_back: boolean;
    auto_advance_conditions?: AutoAdvanceCondition[];
  };
}
```

### 2. 数据库表结构 (纯数据)
```sql
-- 量表包表 (纯数据，无展现)
CREATE TABLE IF NOT EXISTS quiz_packs (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    version TEXT NOT NULL DEFAULT '1.0.0',
    
    -- 关联的情绪数据集
    emotion_data_set_id TEXT NOT NULL,
    
    -- 量表逻辑配置 (JSON: QuizLogicConfig)
    quiz_logic_config TEXT NOT NULL,
    
    -- 默认展现建议 (JSON: DefaultPresentationHints, 可选)
    default_presentation_hints TEXT,
    
    -- 元数据 (JSON: QuizPackMetadata)
    metadata TEXT NOT NULL,
    
    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (emotion_data_set_id) REFERENCES emotion_data_sets(id)
);
```

## 🎨 展现层设计 (纯展现，无数据逻辑)

### 1. 用户展现配置
```typescript
// 用户的6层展现配置 - 完全独立于量表数据
interface UserPresentationConfig {
  user_id: string;
  
  // 6层个性化配置
  layer0_dataset_presentation: {
    preferred_pack_categories: string[];
    default_difficulty_preference: UserLevel;
    session_length_preference: 'short' | 'medium' | 'long';
  };
  
  layer1_user_choice: {
    preferred_view_type: ViewType;
    active_skin_id: string;
    dark_mode: boolean;
    color_mode: ColorMode;
    user_level: UserLevel;
  };
  
  layer2_rendering_strategy: {
    render_engine_preferences: {
      [viewType in ViewType]: RenderEngine;
    };
    content_display_mode_preferences: {
      [viewType in ViewType]: ContentDisplayMode;
    };
  };
  
  layer3_skin_base: {
    colors: ColorPalette;
    fonts: FontConfiguration;
    effects: VisualEffectsConfig;
    animations: AnimationConfig;
  };
  
  layer4_view_detail: {
    wheel_config?: WheelPresentationConfig;
    card_config?: CardPresentationConfig;
    
    // 情绪展现特定配置 - **更新为4层emoji映射机制**
    emotion_presentation: {
      // 用户全局emoji映射配置
      emoji_mapping: Record<string, EmojiMapping>;
      color_mapping: Record<string, string>;
      animation_mapping: Record<string, string>;

      // 展现样式配置
      emotion_grouping_style: 'by_category' | 'by_intensity';
      tier_transition_animation: 'fade' | 'rotate' | 'zoom';
      emotion_display_style: {
        show_intensity_indicators: boolean;
        use_color_coding: boolean;
        adaptive_sizing: boolean;
      };
    };
    
    // 交互展现配置
    interaction_presentation: {
      hover_effects: HoverEffectConfig;
      selection_feedback: SelectionFeedbackConfig;
      confirmation_style: ConfirmationStyleConfig;
    };
  };
  
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    screen_reader_support: boolean;
    
    // 情绪量表特定可访问性
    emotion_accessibility: {
      audio_descriptions: boolean;
      simplified_labels: boolean;
      color_blind_friendly: boolean;
    };
  };
}

// 量表特定的展现覆盖 (可选)
interface PackSpecificPresentationOverride {
  user_id: string;
  pack_id: string;
  
  // 针对特定量表的展现覆盖
  presentation_overrides: Partial<UserPresentationConfig>;
  
  // 层级特定的展现覆盖
  tier_presentation_overrides: {
    [tierId: string]: TierPresentationOverride;
  };
}
```

### 2. 展现配置数据库表
```sql
-- 用户展现配置表
CREATE TABLE IF NOT EXISTS user_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    config_name TEXT NOT NULL DEFAULT 'default',
    
    -- 6层展现配置 (JSON: UserPresentationConfig)
    presentation_config TEXT NOT NULL,
    
    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);

-- 量表特定展现覆盖表
CREATE TABLE IF NOT EXISTS pack_presentation_overrides (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,

    -- 展现覆盖配置 (JSON: PackSpecificPresentationOverride)
    presentation_overrides TEXT,

    -- 层级展现覆盖 (JSON: TierPresentationOverrides)
    tier_presentation_overrides TEXT,

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    UNIQUE(user_id, pack_id)
);

-- **新增**: 问题级别的emoji映射覆盖表
CREATE TABLE IF NOT EXISTS question_presentation_overrides (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    question_id TEXT NOT NULL,

    -- 问题特定的展现覆盖配置 (JSON: QuestionPresentationOverride)
    presentation_overrides TEXT NOT NULL,
    override_reason TEXT,
    override_priority INTEGER DEFAULT 1,

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
    UNIQUE(user_id, question_id)
);

-- **新增**: Quiz包的默认展现配置表
CREATE TABLE IF NOT EXISTS pack_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    pack_id TEXT NOT NULL,
    config_type TEXT NOT NULL, -- 'default_emoji_mapping', 'theme_config'

    -- 配置数据 (JSON: PackPresentationConfig)
    config_data TEXT NOT NULL,
    config_version TEXT DEFAULT '1.0',

    -- 状态管理
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,

    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    UNIQUE(pack_id, config_type, is_active) WHERE is_active = 1
);
```

## 🔄 会话层设计 (运行时合并)

### 1. 会话展现配置快照
```typescript
// 会话开始时生成的展现配置快照
interface SessionPresentationSnapshot {
  session_id: string;
  user_id: string;
  pack_id: string;
  
  // 合并后的最终展现配置
  final_presentation_config: {
    // 基础展现配置
    base_config: UserPresentationConfig;
    
    // 量表特定覆盖
    pack_overrides?: Partial<UserPresentationConfig>;
    
    // 层级特定覆盖
    tier_overrides: {
      [tierId: string]: TierPresentationOverride;
    };
    
    // 动态调整规则
    dynamic_rules: DynamicPresentationRule[];
  };
  
  // 配置生成元数据
  config_metadata: {
    generated_at: Date;
    config_version: string;
    personalization_level: number; // 0-100
    applied_rules_count: number;
  };
}
```

### 2. 配置合并流程
```typescript
class PresentationConfigMerger {
  async generateSessionPresentationConfig(
    userId: string,
    packId: string,
    sessionContext: SessionContext
  ): Promise<SessionPresentationSnapshot> {
    
    // 1. 加载问题特定展现覆盖 (最高优先级) **新增**
    const questionOverrides = await this.loadQuestionSpecificOverrides(userId, sessionContext.questionIds);

    // 2. 加载用户基础展现配置
    const userBaseConfig = await this.loadUserPresentationConfig(userId);

    // 3. 加载量表特定展现覆盖
    const packOverrides = await this.loadPackSpecificOverrides(userId, packId);

    // 4. 加载Quiz包默认emoji映射 (新增)
    const packDefaultConfigs = await this.loadPackDefaultPresentationConfigs(packId);

    // 5. 加载量表的默认展现建议 (最低优先级，仅作为fallback)
    const packDefaultHints = await this.loadPackDefaultPresentationHints(packId);

    // 6. 应用动态调整规则
    const dynamicRules = await this.loadDynamicPresentationRules(userId, sessionContext);

    // 7. 合并配置 (优先级: 问题覆盖 > 用户配置 > 量表覆盖 > 包默认配置 > 动态规则 > 默认建议)
    const finalConfig = this.mergeConfigurations({
      questionOverrides,
      baseConfig: userBaseConfig,
      packOverrides: packOverrides?.presentation_overrides,
      tierOverrides: packOverrides?.tier_presentation_overrides || {},
      packDefaultConfigs,
      dynamicRules,
      fallbackHints: packDefaultHints
    });
    
    return {
      session_id: sessionContext.sessionId,
      user_id: userId,
      pack_id: packId,
      final_presentation_config: finalConfig,
      config_metadata: {
        generated_at: new Date(),
        config_version: '2.0',
        personalization_level: this.calculatePersonalizationLevel(finalConfig),
        applied_rules_count: dynamicRules.length
      }
    };
  }
}
```

## 🎯 实际应用场景

### 场景1: 同一量表的不同展现
```typescript
// 用户A: 喜欢轮盘视图，暖色调
const userA_presentation = {
  layer1_user_choice: {
    preferred_view_type: 'wheel',
    color_mode: 'warm'
  }
};

// 用户B: 喜欢卡片视图，冷色调
const userB_presentation = {
  layer1_user_choice: {
    preferred_view_type: 'card',
    color_mode: 'cool'
  }
};

// 同一个情绪量表 + 不同用户配置 = 完全不同的体验
```

### 场景2: 量表特定的展现定制
```typescript
// 用户为"日常情绪记录"量表设置特殊展现
const dailyEmotionOverride = {
  pack_id: 'daily-emotion-tracking',
  presentation_overrides: {
    layer4_view_detail: {
      emotion_presentation: {
        emotion_grouping_style: 'by_intensity',
        tier_transition_animation: 'fade'
      }
    }
  }
};
```

### 场景3: 动态展现调整
```typescript
// 基于用户行为的动态调整
const dynamicRule = {
  trigger_conditions: [
    { type: 'response_time', operator: 'greater_than', value: 30000 }
  ],
  ui_modifications: {
    layer4_view_detail: {
      interaction_presentation: {
        confirmation_style: 'enhanced' // 响应慢时增强确认
      }
    }
  }
};
```

## 📈 架构优势

### 1. 高度个性化
- 每个用户都可以有完全不同的量表体验
- 支持量表级别和层级级别的精细化定制
- 动态适应用户行为和偏好

### 2. 易于维护
- 数据团队专注内容和逻辑
- UI团队专注展现和交互
- 两者可以独立迭代和优化

### 3. 强大的扩展性
- 新增量表无需考虑展现问题
- 新增展现方式无需修改量表数据
- 支持A/B测试和渐进式升级

### 4. 优秀的用户体验
- 同一量表可以适应不同用户群体
- 支持可访问性和特殊需求
- 提供一致而个性化的体验

这个数据与展现分离的架构为构建高度个性化的Quiz系统提供了坚实的基础。
