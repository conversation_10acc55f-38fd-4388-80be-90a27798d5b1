import type React from 'react';
import { Loading } from './loading';
import { Logo } from './logo';

interface AppLoadingScreenProps {
  message?: string;
}

export const AppLoadingScreen: React.FC<AppLoadingScreenProps> = ({
  message = 'Loading application...',
}) => {
  return (
    <div className="fixed inset-0 flex flex-col items-center justify-center bg-background z-50">
      <div className="w-24 h-24 mb-8">
        <Logo />
      </div>
      <Loading size="lg" />
      <p className="mt-6 text-muted-foreground animate-pulse">{message}</p>
    </div>
  );
};
