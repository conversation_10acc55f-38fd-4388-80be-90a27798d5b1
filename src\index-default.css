@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 260 70% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 252 83% 74%;
    --primary-foreground: 210 40% 98%;

    --secondary: 260 60% 54%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 260 70% 94%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 252 83% 74%;

    --radius: 0.8rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 260 70% 94%;

    --card: 240 10% 3.9%;
    --card-foreground: 260 70% 94%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 260 70% 94%;

    --primary: 252 83% 74%;
    --primary-foreground: 210 40% 98%;

    --secondary: 260 60% 54%;
    --secondary-foreground: 210 40% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 260 70% 94%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 252 83% 74%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  .mobile-container {
    max-width: 550px;
    margin: 0 auto;
    min-height: 100svh;
    position: relative;
  }

  .emotion-wheel {
    @apply transition-all duration-300 ease-in-out;
  }

  .mood-card {
    @apply bg-white rounded-xl shadow-md p-4 transition-all duration-300 hover:shadow-lg;
  }

  .nav-link {
    @apply flex flex-col items-center justify-center gap-1 text-xs font-medium;
  }

  .nav-link.active {
    @apply text-primary;
  }
}
