# 新的数据库表结构Hooks

本文档介绍为NewHome.tsx页面创建的新hooks，这些hooks使用新的数据库表结构（quiz_packs, quiz_questions, quiz_question_options）来替代原有的emotion_data_sets相关表。

## 概述

### 新的数据库架构
- **quiz_packs**: 替代 emotion_data_sets，存储Quiz包信息
- **quiz_questions**: 存储Quiz问题，支持分层级结构
- **quiz_question_options**: 存储问题选项，支持联动关系
- **quiz_sessions**: 管理Quiz会话状态
- **quiz_answers**: 存储用户答案

### 创建的Hooks

1. **useNewHomeData** - NewHome页面专用数据管理
2. **useEmotionData** - 情绪数据管理（兼容性层）
3. **useQuizSession** - Quiz会话管理

## useNewHomeData

### 功能
- 加载情绪轮盘数据
- 获取推荐Quiz包
- 提供搜索和分类功能
- 管理用户统计数据

### 使用方法

```typescript
import { useNewHomeData } from '@/hooks/useNewHomeData';

const MyComponent = () => {
  const {
    emotionWheelData,
    recommendedQuizPacks,
    recentQuizPacks,
    userStats,
    isLoading,
    error,
    refreshData,
    loadEmotionWheelData,
    searchQuizPacks,
    getQuizPacksByCategory
  } = useNewHomeData(userId);

  // 使用情绪轮盘数据
  if (emotionWheelData) {
    const { pack, questions, primaryEmotions, secondaryEmotions } = emotionWheelData;
    // 渲染情绪轮盘界面
  }

  // 搜索Quiz包
  const handleSearch = async (term: string) => {
    const results = await searchQuizPacks(term);
    // 处理搜索结果
  };

  // 按分类获取Quiz包
  const handleCategoryFilter = async (category: string) => {
    const results = await getQuizPacksByCategory(category);
    // 处理分类结果
  };
};
```

### 数据结构

```typescript
interface EmotionWheelData {
  pack: QuizPack;
  questions: (QuizQuestion & { options: QuizQuestionOption[] })[];
  primaryEmotions: QuizQuestionOption[];
  secondaryEmotions: QuizQuestionOption[];
}

interface NewHomeData {
  emotionWheelData: EmotionWheelData | null;
  recommendedQuizPacks: QuizPack[];
  recentQuizPacks: QuizPack[];
  userStats: {
    totalSessions: number;
    completedSessions: number;
    favoriteEmotions: string[];
  };
  isLoading: boolean;
  error: string | null;
}
```

## useEmotionData

### 功能
- 提供与原有emotion_data_sets兼容的接口
- 将Quiz包数据转换为情绪数据集格式
- 支持层级情绪选择
- 提供搜索和过滤功能

### 使用方法

```typescript
import { useEmotionData } from '@/hooks/useEmotionData';

const MyComponent = () => {
  const {
    emotionDataSets,
    activeDataSet,
    isLoading,
    error,
    setActiveDataSet,
    getEmotionsByTier,
    getChildEmotions,
    searchEmotions
  } = useEmotionData();

  // 设置活跃数据集
  const handleSetActive = async (dataSetId: string) => {
    await setActiveDataSet(dataSetId);
  };

  // 获取指定层级的情绪
  const handleGetTierEmotions = async (dataSetId: string, tierLevel: number) => {
    const emotions = await getEmotionsByTier(dataSetId, tierLevel);
    // 处理情绪列表
  };

  // 获取子情绪
  const handleGetChildEmotions = async (dataSetId: string, parentId: string) => {
    const childEmotions = await getChildEmotions(dataSetId, parentId);
    // 处理子情绪列表
  };
};
```

### 兼容性接口

```typescript
interface EmotionOption {
  id: string;
  text: string;
  value: string;
  emotionId?: string;
  tierLevel: number;
  parentEmotionId?: string;
  metadata?: any;
}

interface EmotionDataSet {
  id: string;
  name: string;
  description: string;
  type: 'emotion_wheel' | 'tcm_assessment' | 'survey';
  tiers: EmotionTier[];
  totalEmotions: number;
  isActive: boolean;
}
```

## useQuizSession

### 功能
- 管理Quiz会话生命周期
- 处理问题导航
- 管理答案提交
- 跟踪会话进度

### 使用方法

```typescript
import { useQuizSession } from '@/hooks/useQuizSession';

const MyComponent = () => {
  const {
    session,
    currentQuestion,
    currentQuestionIndex,
    totalQuestions,
    answers,
    isLoading,
    error,
    isCompleted,
    startQuizSession,
    submitAnswer,
    goToNextQuestion,
    goToPreviousQuestion,
    endQuizSession,
    getSessionProgress
  } = useQuizSession();

  // 开始新会话
  const handleStartSession = async (packId: string, userId: string) => {
    await startQuizSession(packId, userId);
  };

  // 提交答案
  const handleSubmitAnswer = async (selectedOptions: string[]) => {
    await submitAnswer({
      selectedOptionIds: selectedOptions,
      answerValue: selectedOptions.join(','),
      confidenceScore: 80,
      responseTimeMs: Date.now()
    });
  };

  // 导航到下一题
  const handleNext = async () => {
    await goToNextQuestion();
  };

  // 获取进度
  const progress = getSessionProgress(); // 返回百分比
};
```

## NewHome.tsx 更新

### 主要变化

1. **导入新的hooks**
```typescript
import { useNewHomeData } from '@/hooks/useNewHomeData';
import { useQuizSession } from '@/hooks/useQuizSession';
```

2. **使用新的数据结构**
```typescript
const {
  emotionWheelData,
  isLoading: homeDataLoading,
  error: homeDataError
} = useNewHomeData(userConfig?.id);

const {
  startQuizSession,
  submitAnswer,
  isLoading: sessionLoading
} = useQuizSession();
```

3. **更新情绪选择逻辑**
```typescript
const getCurrentTierEmotions = () => {
  if (!emotionWheelData) return [];
  
  const currentTier = currentTierIndex + 1;
  
  if (currentTier === 1) {
    return emotionWheelData.primaryEmotions;
  } else if (currentTier === 2 && selectedEmotions.length > 0) {
    const parentEmotion = selectedEmotions[0];
    return emotionWheelData.secondaryEmotions.filter(
      emotion => {
        const metadata = typeof emotion.metadata === 'string' 
          ? JSON.parse(emotion.metadata) 
          : emotion.metadata;
        return metadata?.parent_emotion_id === parentEmotion.id;
      }
    );
  }
  
  return [];
};
```

4. **更新保存逻辑**
```typescript
const handleSave = async (data: { intensity: number; tags: string[]; reflection: string }) => {
  if (!emotionWheelData || selectedEmotions.length === 0) {
    toast.error('请先选择情绪');
    return;
  }

  if (userConfig?.id && emotionWheelData.pack.id) {
    await startQuizSession(emotionWheelData.pack.id, userConfig.id);
    
    for (const emotion of selectedEmotions) {
      await submitAnswer({
        selectedOptionIds: [emotion.id],
        answerValue: emotion.value,
        answerText: emotion.text,
        confidenceScore: data.intensity,
        responseTimeMs: Date.now()
      });
    }
    
    toast.success('情绪记录已保存');
    navigate('/history');
  }
};
```

## 服务层更新

### 新增的Repository和Service

1. **QuizQuestionRepository** - Quiz问题数据访问
2. **QuizQuestionOptionRepository** - Quiz选项数据访问
3. **QuizQuestionService** - Quiz问题业务逻辑

### Services类更新

```typescript
// 在Services类中添加新的服务访问器
export const Services = {
  // ... 现有服务
  
  async quizQuestion() {
    return await ServiceFactory.getInstance().getQuizQuestionService();
  }
};
```

## 测试

### 单元测试
- `useNewHomeData.test.ts` - 测试NewHome数据hook
- 测试覆盖数据加载、错误处理、搜索功能等

### 集成测试
建议创建集成测试来验证：
- 数据库表之间的关联
- Quiz会话的完整流程
- 情绪选择的联动逻辑

## 迁移指南

### 从旧架构迁移

1. **数据迁移**
   - 将emotion_data_sets转换为quiz_packs
   - 将emotion_data_set_tiers转换为quiz_questions
   - 将emotion_data_set_emotions转换为quiz_question_options

2. **代码迁移**
   - 替换useHomeData为useNewHomeData
   - 更新组件props以适配新的数据结构
   - 更新保存逻辑使用Quiz会话

3. **向后兼容**
   - useEmotionData提供兼容性接口
   - 保持现有组件接口不变
   - 逐步迁移到新架构

## 注意事项

1. **性能优化**
   - 使用React.memo优化组件渲染
   - 实现数据缓存机制
   - 避免不必要的API调用

2. **错误处理**
   - 提供友好的错误提示
   - 实现重试机制
   - 记录错误日志

3. **类型安全**
   - 使用TypeScript严格模式
   - 定义完整的类型接口
   - 避免使用any类型

4. **测试覆盖**
   - 编写全面的单元测试
   - 实现集成测试
   - 进行端到端测试
