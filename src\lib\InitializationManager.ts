/**
 * 全局初始化状态管理器
 * 防止重复初始化数据
 */
class InitializationManager {
  private static instance: InitializationManager;
  private initializationStates: Map<string, boolean> = new Map();
  private initializationPromises: Map<string, Promise<any>> = new Map();

  private constructor() {}

  static getInstance(): InitializationManager {
    if (!InitializationManager.instance) {
      InitializationManager.instance = new InitializationManager();
    }
    return InitializationManager.instance;
  }

  /**
   * 检查某个组件/服务是否已经初始化
   */
  isInitialized(key: string): boolean {
    return this.initializationStates.get(key) === true;
  }

  /**
   * 标记某个组件/服务为已初始化
   */
  markAsInitialized(key: string): void {
    this.initializationStates.set(key, true);
    console.log(`[InitializationManager] Marked '${key}' as initialized`);
  }

  /**
   * 获取或创建初始化 Promise
   * 如果已经有正在进行的初始化，返回现有的 Promise
   * 如果已经初始化完成，返回 resolved Promise
   * 否则执行初始化函数并缓存 Promise
   */
  async getOrCreateInitialization<T>(key: string, initializationFn: () => Promise<T>): Promise<T> {
    // 如果已经初始化完成，直接返回
    if (this.isInitialized(key)) {
      console.log(`[InitializationManager] '${key}' already initialized, skipping`);
      return Promise.resolve(null as T);
    }

    // 如果正在初始化，返回现有的 Promise
    if (this.initializationPromises.has(key)) {
      console.log(`[InitializationManager] '${key}' initialization in progress, waiting...`);
      return this.initializationPromises.get(key)!;
    }

    // 开始新的初始化
    console.log(`[InitializationManager] Starting initialization for '${key}'`);
    const promise = initializationFn()
      .then((result) => {
        this.markAsInitialized(key);
        this.initializationPromises.delete(key);
        return result;
      })
      .catch((error) => {
        console.error(`[InitializationManager] Initialization failed for '${key}':`, error);
        this.initializationPromises.delete(key);
        throw error;
      });

    this.initializationPromises.set(key, promise);
    return promise;
  }

  /**
   * 重置初始化状态（主要用于测试）
   */
  reset(key?: string): void {
    if (key) {
      this.initializationStates.delete(key);
      this.initializationPromises.delete(key);
      console.log(`[InitializationManager] Reset initialization state for '${key}'`);
    } else {
      this.initializationStates.clear();
      this.initializationPromises.clear();
      console.log('[InitializationManager] Reset all initialization states');
    }
  }

  /**
   * 获取所有初始化状态（用于调试）
   */
  getDebugInfo(): { states: Record<string, boolean>; pendingPromises: string[] } {
    const states: Record<string, boolean> = {};
    this.initializationStates.forEach((value, key) => {
      states[key] = value;
    });

    const pendingPromises = Array.from(this.initializationPromises.keys());

    return { states, pendingPromises };
  }
}

export default InitializationManager;

// 常用的初始化键
export const INITIALIZATION_KEYS = {
  DATABASE_SCHEMA: 'database_schema',
  DATABASE_INITIAL_DATA: 'database_initial_data',
  EMOJI_CONTEXT: 'emoji_context',
  EMOTION_DATA_SETS: 'emotion_data_sets',
  USER_CONFIG: 'user_config',
  LANGUAGE_SETUP: 'language_setup',
} as const;

export type InitializationKey = (typeof INITIALIZATION_KEYS)[keyof typeof INITIALIZATION_KEYS];
