-- 检查 emotion_data_sets 表
SELECT id, name FROM emotion_data_sets;

-- 检查 emotion_data_set_tiers 表
SELECT id, emotion_data_set_id, tier_level, name FROM emotion_data_set_tiers;

-- 检查 emotion_data_set_emotions 表中的关键记录
SELECT id, emotion_data_set_id, tier_id, emotion_id FROM emotion_data_set_emotions 
WHERE id IN (
  'default_happy', 'default_sad', 'default_angry', 'default_fearful',
  'default_playful', 'default_lonely', 'default_frustrated', 'default_anxious', 'default_peaceful',
  'default_aroused', 'default_isolated', 'default_annoyed', 'default_worried', 'default_thankful'
);

-- 检查 users 表
SELECT id, username FROM users;

-- 检查 mood_entries 表
SELECT id, user_id, emotion_data_set_id FROM mood_entries LIMIT 5;

-- 检查 emotion_selections 表
SELECT id, mood_entry_id, emotion_id, tier_level, emotion_data_set_emotion_id FROM emotion_selections LIMIT 5;
