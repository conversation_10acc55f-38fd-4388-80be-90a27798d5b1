# Quiz系统基础组件设计规范

## 🎯 设计原则

基于Apple游戏设计原则和iOS移动应用设计指南，为Quiz系统的**配置驱动基础组件**制定专业设计规范。

### 核心设计理念

- **配置驱动**: 所有组件通过后端JSON配置动态生成和定制
- **多样性支持**: 每个组件提供1-2套不同布局或风格选择  
- **中医文化融合**: 深度融入中医文化元素的视觉设计
- **响应式设计**: 支持PC、平板、手机等不同屏幕尺寸
- **即时反馈**: 为所有用户操作提供清晰的视觉和触觉反馈

### 基础组件与常见UI库的区别

| 特性 | 常见UI库 (Ant Design等) | Quiz基础组件 |
|------|------------------------|-------------|
| **配置方式** | 代码属性配置 | 后端JSON配置驱动 |
| **样式定制** | 主题变量覆盖 | 多套预设布局+中医风格变体 |
| **交互反馈** | 标准交互 | 游戏化即时反馈+动画效果 |
| **文化特色** | 通用设计 | 深度中医文化元素融合 |
| **使用场景** | 通用业务系统 | 专门的Quiz量表系统 |

### 组件层次结构

```
基础UI组件 (本文档重点)
├── 文本组件 (TextComponent)
├── 按钮组件 (ButtonComponent)  
├── 选择器组件 (SelectorComponent)
├── 滑块组件 (SliderComponent)
├── 评分组件 (RatingComponent)
├── 图片选择器组件 (ImageSelectorComponent)
├── 进度指示器组件 (ProgressIndicatorComponent)
└── 媒体组件 (MediaComponent)

特殊视图 (基于基础组件构建)
├── wheel (情绪轮盘视图)
├── card (卡片视图)
├── bubble (气泡视图)
└── galaxy (星系视图)
```

## 📱 移动端优先设计

### 1. 安全区域和布局

```typescript
interface QuizLayoutConstraints {
  // 基于iOS安全区域的布局约束
  safe_area: {
    top: number;      // 状态栏/刘海区域
    bottom: number;   // 底部手势区域 (至少44pt)
    left: number;     // 左侧安全边距
    right: number;    // 右侧安全边距
  };
  
  // 标准间距系统 (基于4pt网格)
  spacing: {
    xs: 4;    // 紧密间距
    sm: 8;    // 小间距
    md: 16;   // 标准间距
    lg: 24;   // 宽松间距
    xl: 32;   // 分隔间距
  };
  
  // 触控目标最小尺寸
  touch_target_min: {
    width: 44;   // 最小触控宽度 (pt)
    height: 44;  // 最小触控高度 (pt)
  };
}
```

## 🎨 基础组件设计

### 1. 文本组件 (TextComponent)

#### 配置接口
```typescript
interface TextComponentConfig {
  layout_id: "standard_text" | "dialogue_bubble";
  style: {
    font_family: "modern" | "traditional" | "calligraphy";
    size: "small" | "medium" | "large" | "title";
    color_scheme: string;
    alignment: "left" | "center" | "right";
    line_height: number;
    letter_spacing: number;
  };
  content: {
    text_localized: { [locale: string]: string };
    emphasis_words?: string[];
    animation_effect?: "fade_in" | "typewriter" | "brush_stroke";
  };
}
```

#### 布局变体

**布局1: 标准文本块 (Standard Text)**
- **展现**: 常规的文本段落显示
- **适用**: 问题描述、说明文字
- **中医特色**: 可选择毛笔字体、竹简背景

**布局2: 对话气泡 (Dialogue Bubble)**
- **展现**: 带有指向箭头的对话气泡
- **适用**: NPC对话、提示信息
- **中医特色**: 古典卷轴样式、云朵形状气泡

### 2. 按钮组件 (ButtonComponent)

#### 配置接口
```typescript
interface ButtonComponentConfig {
  layout_id: "standard_button" | "jade_pendant" | "seal_stamp";
  style: {
    size: "small" | "medium" | "large";
    variant: "primary" | "secondary" | "outline" | "ghost";
    shape: "rectangle" | "rounded" | "pill" | "custom";
    icon_position: "left" | "right" | "only" | "none";
    hover_effect: "scale" | "glow" | "shadow" | "ripple";
  };
  content: {
    text_localized: { [locale: string]: string };
    icon_name?: string;
    loading_state?: boolean;
  };
  feedback: {
    haptic_feedback: boolean;
    sound_effect?: string;
    animation: "bounce" | "pulse" | "shake";
  };
}
```

#### 布局变体

**布局1: 标准按钮 (Standard Button)**
- **展现**: 现代化的矩形或圆角按钮
- **适用**: 大多数交互场景
- **状态**: 默认、悬停、按下、禁用

**布局2: 玉佩按钮 (Jade Pendant Button)**
- **展现**: 仿古玉佩形状，带有玉石质感
- **适用**: 重要操作、确认按钮
- **特效**: 点击时有玉石碰撞音效

**布局3: 印章按钮 (Seal Stamp Button)**
- **展现**: 中式印章形状，红色印泥效果
- **适用**: 提交、确认等最终操作
- **特效**: 点击时有盖印动画

### 3. 选择器组件 (SelectorComponent)

#### 配置接口
```typescript
interface SelectorComponentConfig {
  layout_id: "vertical_list" | "horizontal_flow" | "grid_layout";
  style: {
    selection_mode: "single" | "multiple";
    marker_style: "circle" | "square" | "chinese_marker";
    spacing: number;
    hover_effect: boolean;
    animation_style: "fade" | "scale" | "slide";
  };
  options: OptionConfig[];
  validation: {
    required: boolean;
    min_selections?: number;
    max_selections?: number;
  };
}

interface OptionConfig {
  id: string;
  value: string | number;
  text_localized: { [locale: string]: string };
  display_style: "text_only" | "icon_text" | "card_style";
  icon_name?: string;
  disabled?: boolean;
}
```

#### 布局变体

**布局1: 垂直列表 (Vertical List)**
- **展现**: 选项垂直排列，清晰易读
- **适用**: 大多数单选/多选场景
- **中医特色**: 竹简条目样式、毛笔勾选标记

**布局2: 水平流式 (Horizontal Flow)**
- **展现**: 选项水平排列，自动换行
- **适用**: 短文本选项、标签选择
- **中医特色**: 药材标签样式

**布局3: 网格布局 (Grid Layout)**
- **展现**: 选项以网格形式排列
- **适用**: 图文结合的选项
- **中医特色**: 八卦阵列布局

### 4. 滑块组件 (SliderComponent)

#### 配置接口
```typescript
interface SliderComponentConfig {
  layout_id: "horizontal_slider" | "vertical_slider" | "bamboo_slider";
  style: {
    track_style: "line" | "groove" | "bamboo" | "ink_brush";
    thumb_style: "circle" | "square" | "panda_paw" | "jade_bead";
    show_value: boolean;
    show_labels: boolean;
    label_positions: string[];
    color_scheme: string;
  };
  range: {
    min: number;
    max: number;
    step: number;
    default_value: number;
  };
  labels: {
    start_label: string;
    end_label: string;
    unit?: string;
  };
}
```

#### 布局变体

**布局1: 标准水平滑块 (Horizontal Slider)**
- **展现**: 水平轨道，可拖动滑块
- **适用**: 数值范围选择、程度评估
- **中医特色**: 竹节轨道、熊猫爪印滑块

**布局2: 垂直滑块 (Vertical Slider)**
- **展现**: 垂直轨道，类似温度计
- **适用**: 能量水平、疼痛程度
- **中医特色**: 竹筒温度计样式

**布局3: 墨迹滑块 (Ink Brush Slider)**
- **展现**: 墨迹轨道，毛笔滑块
- **适用**: 艺术性评估、情绪强度
- **特效**: 滑动时留下墨迹痕迹

### 5. 评分组件 (RatingComponent)

#### 配置接口
```typescript
interface RatingComponentConfig {
  layout_id: "horizontal_rating" | "labeled_scale" | "lotus_rating";
  style: {
    marker_type: "stars" | "dots" | "lotus" | "gourd" | "taiji";
    size: "small" | "medium" | "large";
    spacing: number;
    hover_effect: "scale" | "glow" | "color_change";
    fill_animation: "instant" | "progressive" | "wave";
  };
  scale: {
    min_value: number;
    max_value: number;
    step: number;
    default_value?: number;
  };
  labels?: {
    start_label: string;
    end_label: string;
    scale_labels?: string[];
  };
}
```

#### 布局变体

**布局1: 水平星级评分 (Horizontal Star Rating)**
- **展现**: 一行星星，点击选择评分
- **适用**: 满意度、质量评估
- **中医特色**: 莲花、小葫芦、太极鱼替代星星

**布局2: 标签量尺 (Labeled Scale)**
- **展现**: 带文字标签的评分尺度
- **适用**: 李克特量表、语义差异量表
- **中医特色**: 木牌标签、卷轴样式

### 6. 图片选择器组件 (ImageSelectorComponent)

#### 配置接口
```typescript
interface ImageSelectorConfig {
  layout_id: "grid_wall" | "horizontal_strip" | "carousel";
  style: {
    columns: number;
    aspect_ratio: string;
    gap: number;
    selection_indicator: "border" | "overlay" | "checkmark" | "glow";
    hover_effect: "zoom" | "shadow" | "brightness" | "lift";
    border_radius: number;
  };
  images: ImageOption[];
  selection: {
    mode: "single" | "multiple";
    max_selections?: number;
  };
}

interface ImageOption {
  id: string;
  url: string;
  alt_text: string;
  title?: string;
  description?: string;
  value: string | number;
}
```

#### 布局变体

**布局1: 网格图片墙 (Grid Wall)**
- **展现**: 图片以网格形式排列
- **适用**: 多图片选择、视觉对比
- **中医特色**: 古典相框边框

**布局2: 水平滚动带 (Horizontal Strip)**
- **展现**: 图片水平排列，可滚动
- **适用**: 图片较多但空间有限
- **中医特色**: 画卷展开效果

### 7. 进度指示器组件 (ProgressIndicatorComponent)

#### 配置接口
```typescript
interface ProgressIndicatorConfig {
  layout_id: "progress_bar" | "lotus_blooming" | "bamboo_growth";
  style: {
    position: "top" | "bottom" | "inline";
    thickness: number;
    style: "line" | "gradient" | "segmented";
    animation: "smooth" | "stepped" | "pulsing";
    color_scheme: string;
  };
  progress: {
    current: number;
    total: number;
    show_percentage: boolean;
    show_text: boolean;
  };
}
```

#### 布局变体

**布局1: 标准进度条 (Progress Bar)**
- **展现**: 水平进度条，填充显示进度
- **适用**: 通用进度显示
- **中医特色**: 竹节分段进度

**布局2: 莲花绽放进度 (Lotus Blooming)**
- **展现**: 莲花从花苞到绽放的过程
- **适用**: 冥想、放松类测评
- **特效**: 花瓣逐渐展开动画

**布局3: 竹叶生长进度 (Bamboo Growth)**
- **展现**: 竹叶逐渐生长填色
- **适用**: 学习、成长类测评
- **特效**: 叶子生长动画

### 8. 媒体组件 (MediaComponent)

#### 配置接口
```typescript
interface MediaComponentConfig {
  layout_id: "audio_player" | "video_player" | "image_viewer";
  style: {
    controls_style: "minimal" | "full" | "chinese";
    size: "small" | "medium" | "large" | "fullscreen";
    border_style: "none" | "simple" | "decorative";
    background_style: "transparent" | "solid" | "gradient";
  };
  media: {
    type: "audio" | "video" | "image";
    url: string;
    poster_url?: string;
    alt_text?: string;
    autoplay?: boolean;
    loop?: boolean;
    controls?: boolean;
  };
}
```

#### 布局变体

**布局1: 简约音频播放器 (Minimal Audio Player)**
- **展现**: 播放/暂停按钮和进度条
- **适用**: 背景音乐、引导语音
- **中医特色**: 古琴样式控制器

**布局2: 完整媒体播放器 (Full Media Player)**
- **展现**: 完整的播放控制界面
- **适用**: 教学视频、详细说明
- **中医特色**: 古典乐器风格界面

## 🎮 游戏化交互设计

### 1. 反馈系统

```typescript
interface FeedbackSystem {
  visual_feedback: {
    selection_highlight: "glow" | "border" | "scale" | "color_shift";
    transition_animation: "fade" | "slide" | "bounce" | "spring";
    success_indicator: "checkmark" | "sparkles" | "glow" | "chinese_seal";
  };
  
  haptic_feedback: {
    selection: "light_impact" | "medium_impact" | "heavy_impact";
    success: "notification_success";
    error: "notification_error";
  };
  
  audio_feedback: {
    selection_sound: "soft_click" | "bamboo_tap" | "jade_clink";
    success_sound: "chime" | "gong" | "bell";
    background_ambient: "nature_sounds" | "guqin_music" | "silence";
  };
}
```

### 2. 动画时序

```typescript
interface AnimationTiming {
  micro_interaction: 150;     // 按钮点击等
  component_transition: 300;  // 组件切换
  page_transition: 400;       // 页面切换
  complex_animation: 600;     // 复杂动画序列
  
  easing_functions: {
    standard: "cubic-bezier(0.4, 0.0, 0.2, 1)";
    spring: "cubic-bezier(0.175, 0.885, 0.32, 1.275)";
    ease_out: "cubic-bezier(0.0, 0.0, 0.2, 1)";
  };
}
```

这个设计规范为Quiz系统的基础组件提供了完整的设计标准，确保组件既具有现代化的用户体验，又融入了深厚的中医文化特色。
