/**
 * Quiz基础组件导出文件
 */

// 基础组件类和接口
export {
  BaseQuizComponent,
  type BaseQuizComponentProps,
  type PersonalizationConfig,
  type ComponentState,
  type InteractionEvent,
  type QuizComponentType,
  type InteractionType,
  type AnimationEffect
} from './BaseQuizComponent';

// 文本组件
export {
  TextComponent,
  type TextComponentConfig,
  type TextComponentProps
} from './TextComponent';

// 按钮组件
export {
  ButtonComponent,
  type ButtonComponentConfig,
  type ButtonComponentProps
} from './ButtonComponent';

// 选择器组件
export {
  SelectorComponent,
  type SelectorComponentConfig,
  type SelectorComponentProps,
  type SelectorOption
} from './SelectorComponent';

// 滑块组件
export {
  SliderComponent,
  type SliderComponentConfig,
  type SliderComponentProps
} from './SliderComponent';

// 评分组件
export {
  RatingComponent,
  type RatingComponentConfig,
  type RatingComponentProps
} from './RatingComponent';

// 下拉选择器组件
export {
  DropdownComponent,
  type DropdownComponentConfig,
  type DropdownComponentProps,
  type DropdownOption
} from './DropdownComponent';

// 图片组件
export {
  ImageComponent,
  type ImageComponentConfig,
  type ImageComponentProps
} from './ImageComponent';

// 文本输入组件
export {
  TextInputComponent,
  type TextInputComponentConfig,
  type TextInputComponentProps
} from './TextInputComponent';

// 图片选择器组件
export {
  ImageSelectorComponent,
  type ImageSelectorComponentConfig,
  type ImageSelectorComponentProps,
  type ImageSelectorOption
} from './ImageSelectorComponent';

// 进度指示器组件
export {
  ProgressIndicatorComponent,
  type ProgressIndicatorComponentConfig,
  type ProgressIndicatorComponentProps
} from './ProgressIndicatorComponent';

// 音频播放器组件
export {
  AudioPlayerComponent,
  type AudioPlayerComponentConfig,
  type AudioPlayerComponentProps
} from './AudioPlayerComponent';

// 视频播放器组件
export {
  VideoPlayerComponent,
  type VideoPlayerComponentConfig,
  type VideoPlayerComponentProps
} from './VideoPlayerComponent';

// 拖拽列表组件
export {
  DraggableListComponent,
  type DraggableListComponentConfig,
  type DraggableListComponentProps,
  type DraggableListItem
} from './DraggableListComponent';

// NPC角色组件
export {
  NPCCharacterComponent,
  type NPCCharacterComponentConfig,
  type NPCCharacterComponentProps
} from './NPCCharacterComponent';

// 对话组件
export {
  DialogueComponent,
  type DialogueComponentConfig,
  type DialogueComponentProps,
  type DialogueMessage,
  type DialogueOption
} from './DialogueComponent';

// 样式文件
import './quiz-components.css';
