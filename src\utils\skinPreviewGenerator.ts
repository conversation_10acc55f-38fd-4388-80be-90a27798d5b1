/**
 * 皮肤预览图像生成器
 * 用于生成皮肤预览图像
 */

import type { ViewType, Skin } from '@/types';

/**
 * 生成皮肤预览图像URL
 * 注意：这是一个模拟实现，实际应用中应该使用真实的预览图像
 * @param skin 皮肤对象
 * @param viewType 视图类型
 * @returns 预览图像URL
 */
export const generatePreviewImageUrl = (skin: Skin, viewType: ViewType): string => {
  // 如果皮肤已经有预览图像，直接返回
  if (skin.preview_image_light) {
    return skin.preview_image_light;
  }

  // 根据视图类型和皮肤ID生成预览图像URL
  const viewTypeMap: Record<ViewType, string> = {
    wheel: 'wheel',
    card: 'card',
    bubble: 'bubble',
    galaxy: 'galaxy',
    tree: 'tree',
  };

  // 根据皮肤分类选择不同的预览图像
  const categoryMap: Record<string, string> = {
    free: 'free',
    paid: 'paid',
    custom: 'custom',
    basic: 'basic',
  };

  // 构建预览图像URL
  const viewTypePath = viewTypeMap[viewType] || 'default';
  const categoryPath = categoryMap[skin.category] || 'default';

  return `/assets/skins/${viewTypePath}-${categoryPath}.png`;
};

/**
 * 生成皮肤预览图像
 * 注意：这是一个模拟实现，实际应用中应该使用Canvas或SVG生成预览图像
 * @param skin 皮肤对象
 * @param viewType 视图类型
 * @returns 预览图像的Base64编码
 */
export const generatePreviewImage = async (skin: Skin, viewType: ViewType): Promise<string> => {
  // 这里应该使用Canvas或SVG生成预览图像
  // 为了简化，我们返回一个占位符
  return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==';
};

/**
 * 为所有皮肤生成预览图像
 * @param skins 皮肤列表
 * @returns 带有预览图像的皮肤列表
 */
export const generatePreviewImagesForSkins = (skins: Skin[]): Skin[] => {
  return skins.map((skin) => {
    // 确定皮肤支持的第一个视图类型
    const viewType = skin.config.supported_view_types?.[0] || 'wheel';

    // 生成预览图像URL
    const previewImage = generatePreviewImageUrl(skin, viewType as ViewType);

    // 返回带有预览图像的皮肤
    return {
      ...skin,
      previewImage,
    };
  });
};

/**
 * 创建默认预览图像
 * @param viewType 视图类型
 * @param category 皮肤分类
 * @returns 默认预览图像URL
 */
export const createDefaultPreviewImage = (viewType: ViewType, category: string): string => {
  return `/assets/skins/${viewType}-${category}-default.png`;
};
