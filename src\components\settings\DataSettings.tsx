import { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useSync } from "@/contexts/SyncContext";
import { useDataSync } from "@/hooks/useDataSync";
import { useExportData } from "@/hooks/useExportData";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { 
  HardDrive, Cloud, Download, Upload, RefreshCw, 
  Crown, Loader2, CheckCircle, AlertCircle, Wifi, WifiOff
} from "lucide-react";
import { toast } from "sonner";

interface DataSettingsProps {
  userLevel: 'beginner' | 'regular' | 'advanced' | 'vip';
}

const DataSettings: React.FC<DataSettingsProps> = ({ userLevel }) => {
  const { t } = useLanguage();
  const { onlineSyncEnabled, toggleOnlineSync } = useSync();
  const { 
    isSyncing, 
    lastSyncResult, 
    syncError, 
    triggerSync, 
    isTursoInitializing 
  } = useDataSync();
  const { 
    exportDataHandler, 
    isExporting: isExportingData, 
    exportError,
    exportDataAsCsvHandler,
    isExportingCsv,
    exportCsvError
  } = useExportData();

  // 本地状态
  const [autoSync, setAutoSync] = useState(true);
  const [syncFrequency, setSyncFrequency] = useState('daily');

  // 处理在线同步切换
  const handleOnlineSyncToggle = (enabled: boolean) => {
    toggleOnlineSync();
    toast.success(enabled ? 
      t('settings.online_sync_enabled', '在线同步已启用') : 
      t('settings.online_sync_disabled', '在线同步已禁用')
    );
  };

  // 处理手动同步
  const handleManualSync = async () => {
    try {
      await triggerSync();
      toast.success(t('settings.sync_completed', '同步完成'));
    } catch (error) {
      toast.error(t('settings.sync_failed', '同步失败'));
    }
  };

  // 处理数据导出
  const handleExportData = async () => {
    try {
      await exportDataHandler();
      toast.success(t('settings.export_completed', '数据导出完成'));
    } catch (error) {
      toast.error(t('settings.export_failed', '数据导出失败'));
    }
  };

  // 处理CSV导出
  const handleExportCsv = async () => {
    try {
      await exportDataAsCsvHandler();
      toast.success(t('settings.csv_export_completed', 'CSV导出完成'));
    } catch (error) {
      toast.error(t('settings.csv_export_failed', 'CSV导出失败'));
    }
  };

  // 获取同步状态显示
  const getSyncStatus = () => {
    if (isTursoInitializing) {
      return {
        icon: <Loader2 className="h-4 w-4 animate-spin" />,
        text: t('settings.sync_initializing', '初始化中...'),
        variant: 'secondary' as const
      };
    }
    
    if (isSyncing) {
      return {
        icon: <RefreshCw className="h-4 w-4 animate-spin" />,
        text: t('settings.syncing', '同步中...'),
        variant: 'secondary' as const
      };
    }
    
    if (syncError) {
      return {
        icon: <AlertCircle className="h-4 w-4" />,
        text: t('settings.sync_error', '同步错误'),
        variant: 'destructive' as const
      };
    }
    
    if (lastSyncResult?.success) {
      return {
        icon: <CheckCircle className="h-4 w-4" />,
        text: t('settings.sync_success', '同步成功'),
        variant: 'default' as const
      };
    }
    
    return {
      icon: onlineSyncEnabled ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />,
      text: onlineSyncEnabled ? t('settings.sync_ready', '准备同步') : t('settings.sync_offline', '离线模式'),
      variant: 'outline' as const
    };
  };

  const syncStatus = getSyncStatus();

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-2">
        <HardDrive className="h-6 w-6" />
        <h2 className="text-2xl font-bold">
          {t('settings.data_management', '数据管理')}
        </h2>
        {userLevel === 'vip' && (
          <Badge variant="destructive" className="flex items-center space-x-1">
            <Crown className="h-3 w-3" />
            <span>VIP</span>
          </Badge>
        )}
      </div>

      {/* 同步设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Cloud className="h-5 w-5" />
            <span>{t('settings.sync_settings', '同步设置')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 在线同步开关 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('settings.online_sync', '在线同步')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('settings.online_sync.desc', '将数据同步到云端')}
              </p>
            </div>
            <Switch
              checked={onlineSyncEnabled}
              onCheckedChange={handleOnlineSyncToggle}
            />
          </div>

          <Separator />

          {/* 同步状态 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">
                {t('settings.sync_status', '同步状态')}
              </Label>
              <Badge variant={syncStatus.variant} className="flex items-center space-x-1">
                {syncStatus.icon}
                <span>{syncStatus.text}</span>
              </Badge>
            </div>

            {/* 最后同步时间 */}
            {lastSyncResult?.timestamp && (
              <p className="text-xs text-muted-foreground">
                {t('settings.last_sync', '最后同步')}: {new Date(lastSyncResult.timestamp).toLocaleString()}
              </p>
            )}

            {/* 手动同步按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleManualSync}
              disabled={isSyncing || isTursoInitializing || !onlineSyncEnabled}
              className="w-full"
            >
              {isSyncing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t('settings.syncing', '同步中...')}
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {t('settings.sync_now', '立即同步')}
                </>
              )}
            </Button>
          </div>

          {/* 高级用户的自动同步设置 */}
          {(userLevel === 'advanced' || userLevel === 'vip') && (
            <>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm font-medium">
                    {t('settings.auto_sync', '自动同步')}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {t('settings.auto_sync.desc', '定期自动同步数据')}
                  </p>
                </div>
                <Switch
                  checked={autoSync}
                  onCheckedChange={setAutoSync}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 数据导出 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Download className="h-5 w-5" />
            <span>{t('settings.data_export', '数据导出')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {t('settings.export.desc', '导出您的数据以备份或迁移')}
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* JSON导出 */}
            <Button
              variant="outline"
              onClick={handleExportData}
              disabled={isExportingData}
              className="h-auto p-4 flex flex-col items-start space-y-2"
            >
              {isExportingData ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              <div className="text-left">
                <div className="font-medium">{t('settings.export_json', '导出JSON')}</div>
                <div className="text-xs text-muted-foreground">
                  {t('settings.export_json.desc', '完整数据格式')}
                </div>
              </div>
            </Button>

            {/* CSV导出 */}
            <Button
              variant="outline"
              onClick={handleExportCsv}
              disabled={isExportingCsv}
              className="h-auto p-4 flex flex-col items-start space-y-2"
            >
              {isExportingCsv ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              <div className="text-left">
                <div className="font-medium">{t('settings.export_csv', '导出CSV')}</div>
                <div className="text-xs text-muted-foreground">
                  {t('settings.export_csv.desc', '表格兼容格式')}
                </div>
              </div>
            </Button>
          </div>

          {/* 导出错误显示 */}
          {(exportError || exportCsvError) && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-sm text-destructive">
                {exportError || exportCsvError}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 存储信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <HardDrive className="h-5 w-5" />
            <span>{t('settings.storage_info', '存储信息')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('settings.local_storage', '本地存储')}</span>
              <span>2.3 MB / 10 MB</span>
            </div>
            <Progress value={23} className="h-2" />
          </div>

          {onlineSyncEnabled && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{t('settings.cloud_storage', '云端存储')}</span>
                <span>1.8 MB / {userLevel === 'vip' ? '1 GB' : '100 MB'}</span>
              </div>
              <Progress value={userLevel === 'vip' ? 0.18 : 1.8} className="h-2" />
            </div>
          )}

          <div className="text-xs text-muted-foreground">
            {t('settings.storage.desc', '数据包括情绪记录、配置和媒体文件')}
          </div>
        </CardContent>
      </Card>

      {/* VIP用户高级数据功能 */}
      {userLevel === 'vip' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Crown className="h-5 w-5" />
              <span>{t('settings.vip_data', 'VIP数据功能')}</span>
              <Badge variant="destructive">VIP</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              {t('settings.vip_data.desc', 'VIP用户专享的高级数据管理功能')}
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                {t('settings.bulk_import', '批量导入')}
              </Button>
              <Button variant="outline" size="sm">
                <Cloud className="h-4 w-4 mr-2" />
                {t('settings.advanced_backup', '高级备份')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DataSettings;
