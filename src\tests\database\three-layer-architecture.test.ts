/**
 * 数据库三层架构验证测试 (P0 最高优先级)
 * 基于 docs/quiz/ 目录的数据库设计
 * 验证 Schema层、Config层、Test-Data层的完整性
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock database connection
const mockDb = {
  query: vi.fn(),
  run: vi.fn(),
  execute: vi.fn(),
  prepare: vi.fn(),
  exec: vi.fn(),
};

describe('数据库三层架构验证测试 (P0)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('1. Schema层测试 - 表结构验证', () => {
    it('应该验证 quiz_packs 表结构正确', async () => {
      const expectedColumns = [
        { name: 'id', type: 'TEXT', pk: 1 },
        { name: 'name', type: 'TEXT', notnull: 1 },
        { name: 'description', type: 'TEXT', notnull: 0 },
        { name: 'category', type: 'TEXT', notnull: 0 },
        { name: 'version', type: 'TEXT', notnull: 0 },
        { name: 'created_at', type: 'TEXT', notnull: 1 },
        { name: 'updated_at', type: 'TEXT', notnull: 1 }
      ];

      mockDb.query.mockResolvedValueOnce(expectedColumns);

      const result = await mockDb.query('PRAGMA table_info(quiz_packs)');
      
      expect(result).toHaveLength(expectedColumns.length);
      expect(result[0].name).toBe('id');
      expect(result[0].pk).toBe(1); // 主键
      expect(result[1].name).toBe('name');
      expect(result[1].notnull).toBe(1); // 非空
    });

    it('应该验证 quiz_questions 表结构正确', async () => {
      const expectedColumns = [
        { name: 'id', type: 'TEXT', pk: 1 },
        { name: 'quiz_pack_id', type: 'TEXT', notnull: 1 },
        { name: 'question_text', type: 'TEXT', notnull: 1 },
        { name: 'question_type', type: 'TEXT', notnull: 1 },
        { name: 'order_index', type: 'INTEGER', notnull: 1 },
        { name: 'is_required', type: 'INTEGER', notnull: 1 },
        { name: 'scoring_config', type: 'TEXT', notnull: 0 },
        { name: 'created_at', type: 'TEXT', notnull: 1 },
        { name: 'updated_at', type: 'TEXT', notnull: 1 }
      ];

      mockDb.query.mockResolvedValueOnce(expectedColumns);

      const result = await mockDb.query('PRAGMA table_info(quiz_questions)');
      
      expect(result).toHaveLength(expectedColumns.length);
      expect(result.find(col => col.name === 'quiz_pack_id')).toBeTruthy();
      expect(result.find(col => col.name === 'question_type')).toBeTruthy();
    });

    it('应该验证 quiz_question_options 表结构正确', async () => {
      const expectedColumns = [
        { name: 'id', type: 'TEXT', pk: 1 },
        { name: 'question_id', type: 'TEXT', notnull: 1 },
        { name: 'option_text', type: 'TEXT', notnull: 1 },
        { name: 'option_value', type: 'TEXT', notnull: 1 },
        { name: 'scoring_value', type: 'REAL', notnull: 0 },
        { name: 'order_index', type: 'INTEGER', notnull: 1 },
        { name: 'created_at', type: 'TEXT', notnull: 1 },
        { name: 'updated_at', type: 'TEXT', notnull: 1 }
      ];

      mockDb.query.mockResolvedValueOnce(expectedColumns);

      const result = await mockDb.query('PRAGMA table_info(quiz_question_options)');
      
      expect(result).toHaveLength(expectedColumns.length);
      expect(result.find(col => col.name === 'question_id')).toBeTruthy();
      expect(result.find(col => col.name === 'scoring_value')).toBeTruthy();
    });

    it('应该验证新配置系统表结构', async () => {
      // 验证 user_quiz_preferences 表
      const userPrefsColumns = [
        { name: 'id', type: 'TEXT', pk: 1 },
        { name: 'user_id', type: 'TEXT', notnull: 1 },
        { name: 'quiz_pack_id', type: 'TEXT', notnull: 1 },
        { name: 'layer_0_config', type: 'TEXT', notnull: 0 },
        { name: 'layer_1_config', type: 'TEXT', notnull: 0 },
        { name: 'layer_2_config', type: 'TEXT', notnull: 0 },
        { name: 'layer_3_config', type: 'TEXT', notnull: 0 },
        { name: 'layer_4_config', type: 'TEXT', notnull: 0 },
        { name: 'layer_5_config', type: 'TEXT', notnull: 0 },
        { name: 'created_at', type: 'TEXT', notnull: 1 },
        { name: 'updated_at', type: 'TEXT', notnull: 1 }
      ];

      mockDb.query.mockResolvedValueOnce(userPrefsColumns);

      const result = await mockDb.query('PRAGMA table_info(user_quiz_preferences)');
      
      expect(result).toHaveLength(userPrefsColumns.length);
      
      // 验证所有6层配置字段都存在
      for (let i = 0; i <= 5; i++) {
        expect(result.find(col => col.name === `layer_${i}_config`)).toBeTruthy();
      }
    });

    it('应该验证外键约束正确设置', async () => {
      const foreignKeys = [
        { table: 'quiz_questions', from: 'quiz_pack_id', to: 'quiz_packs(id)' },
        { table: 'quiz_question_options', from: 'question_id', to: 'quiz_questions(id)' },
        { table: 'user_quiz_preferences', from: 'quiz_pack_id', to: 'quiz_packs(id)' }
      ];

      // 模拟外键信息查询
      mockDb.query
        .mockResolvedValueOnce([{ from: 'quiz_pack_id', table: 'quiz_packs', to: 'id' }])
        .mockResolvedValueOnce([{ from: 'question_id', table: 'quiz_questions', to: 'id' }])
        .mockResolvedValueOnce([{ from: 'quiz_pack_id', table: 'quiz_packs', to: 'id' }]);

      for (const fk of foreignKeys) {
        const result = await mockDb.query(`PRAGMA foreign_key_list(${fk.table})`);
        expect(result).toHaveLength(1);
        expect(result[0].from).toBe(fk.from);
      }
    });
  });

  describe('2. Config层测试 - 系统配置验证', () => {
    it('应该验证默认Quiz包配置存在', async () => {
      const defaultQuizPacks = [
        {
          id: 'tcm-emotions-basic',
          name: 'TCM Basic Emotions',
          category: 'tcm',
          version: '1.0.0'
        },
        {
          id: 'western-emotions-wheel',
          name: 'Western Emotion Wheel',
          category: 'western',
          version: '1.0.0'
        }
      ];

      mockDb.query.mockResolvedValueOnce(defaultQuizPacks);

      const result = await mockDb.query('SELECT * FROM quiz_packs WHERE category IN (?, ?)', ['tcm', 'western']);

      expect(result).toHaveLength(2);
      expect(result.find(pack => pack.category === 'tcm')).toBeTruthy();
      expect(result.find(pack => pack.category === 'western')).toBeTruthy();
    });

    it('应该验证默认展现配置存在', async () => {
      const defaultPresentationConfigs = [
        {
          id: 'config1',
          quiz_pack_id: 'tcm-emotions-basic',
          default_emoji_set: JSON.stringify({
            'joy': '😊',
            'anger': '😠',
            'sadness': '😢',
            'fear': '😨',
            'surprise': '😲'
          })
        }
      ];

      mockDb.query.mockResolvedValueOnce(defaultPresentationConfigs);

      const result = await mockDb.query('SELECT * FROM pack_presentation_configs');

      expect(result).toHaveLength(1);
      expect(result[0].quiz_pack_id).toBe('tcm-emotions-basic');
      expect(() => JSON.parse(result[0].default_emoji_set)).not.toThrow();
    });

    it('应该验证系统默认配置完整性', async () => {
      const systemConfigs = [
        { key: 'app_version', value: '1.0.0' },
        { key: 'database_version', value: '2.0.0' },
        { key: 'supported_languages', value: JSON.stringify(['zh', 'en']) },
        { key: 'default_theme', value: 'light' },
        { key: 'max_quiz_sessions', value: '100' }
      ];

      mockDb.query.mockResolvedValueOnce(systemConfigs);

      const result = await mockDb.query('SELECT * FROM system_config');

      expect(result).toHaveLength(5);

      const configMap = result.reduce((acc, config) => {
        acc[config.key] = config.value;
        return acc;
      }, {});

      expect(configMap.app_version).toBe('1.0.0');
      expect(configMap.database_version).toBe('2.0.0');
      expect(configMap.default_theme).toBe('light');
    });
  });

  describe('3. Test-Data层测试 - 测试数据验证', () => {
    it('应该验证测试用户数据存在', async () => {
      const testUsers = [
        {
          id: 'test-user-1',
          username: 'test_user_1',
          email: '<EMAIL>',
          created_at: new Date().toISOString()
        },
        {
          id: 'test-user-2',
          username: 'test_user_2',
          email: '<EMAIL>',
          created_at: new Date().toISOString()
        }
      ];

      mockDb.query.mockResolvedValueOnce(testUsers);

      const result = await mockDb.query('SELECT * FROM users WHERE username LIKE ?', ['test_%']);

      expect(result).toHaveLength(2);
      expect(result[0].username).toBe('test_user_1');
      expect(result[1].username).toBe('test_user_2');
    });

    it('应该验证测试Quiz会话数据', async () => {
      const testSessions = [
        {
          id: 'session-1',
          user_id: 'test-user-1',
          quiz_pack_id: 'tcm-emotions-basic',
          status: 'completed',
          score: 85
        },
        {
          id: 'session-2',
          user_id: 'test-user-2',
          quiz_pack_id: 'western-emotions-wheel',
          status: 'in_progress',
          score: null
        }
      ];

      mockDb.query.mockResolvedValueOnce(testSessions);

      const result = await mockDb.query('SELECT * FROM quiz_sessions WHERE user_id LIKE ?', ['test-user-%']);

      expect(result).toHaveLength(2);
      expect(result.find(session => session.status === 'completed')).toBeTruthy();
      expect(result.find(session => session.status === 'in_progress')).toBeTruthy();
    });

    it('应该验证测试答案数据完整性', async () => {
      const testAnswers = [
        {
          id: 'answer-1',
          session_id: 'session-1',
          question_id: 'q1',
          selected_option_id: 'opt1',
          answer_value: 'high_energy',
          score: 80
        },
        {
          id: 'answer-2',
          session_id: 'session-1',
          question_id: 'q2',
          selected_option_id: 'opt3',
          answer_value: 'medium_joy',
          score: 90
        }
      ];

      mockDb.query.mockResolvedValueOnce(testAnswers);

      const result = await mockDb.query('SELECT * FROM quiz_answers WHERE session_id = ?', ['session-1']);

      expect(result).toHaveLength(2);
      expect(result.every(answer => answer.session_id === 'session-1')).toBe(true);
      expect(result.every(answer => answer.score !== null)).toBe(true);
    });
  });

  describe('4. 数据库完整性验证', () => {
    it('应该验证数据库索引正确创建', async () => {
      const expectedIndexes = [
        'idx_quiz_questions_pack_id',
        'idx_quiz_question_options_question_id',
        'idx_quiz_sessions_user_id',
        'idx_quiz_answers_session_id',
        'idx_user_quiz_preferences_user_pack'
      ];

      mockDb.query.mockResolvedValueOnce(
        expectedIndexes.map(name => ({ name, unique: 0 }))
      );

      const result = await mockDb.query('SELECT name FROM sqlite_master WHERE type = "index" AND name LIKE "idx_%"');
      
      expect(result).toHaveLength(expectedIndexes.length);
      expectedIndexes.forEach(indexName => {
        expect(result.find(idx => idx.name === indexName)).toBeTruthy();
      });
    });

    it('应该验证数据库触发器正确设置', async () => {
      const expectedTriggers = [
        'update_quiz_packs_timestamp',
        'update_quiz_questions_timestamp',
        'update_quiz_sessions_timestamp'
      ];

      mockDb.query.mockResolvedValueOnce(
        expectedTriggers.map(name => ({ name, tbl_name: name.replace('update_', '').replace('_timestamp', '') }))
      );

      const result = await mockDb.query('SELECT name FROM sqlite_master WHERE type = "trigger"');
      
      expect(result).toHaveLength(expectedTriggers.length);
      expectedTriggers.forEach(triggerName => {
        expect(result.find(trigger => trigger.name === triggerName)).toBeTruthy();
      });
    });

    it('应该验证数据库约束正确执行', async () => {
      // 测试唯一约束
      const uniqueConstraintTest = async () => {
        try {
          await mockDb.run('INSERT INTO quiz_packs (id, name) VALUES (?, ?)', ['duplicate-id', 'Test Pack 1']);
          await mockDb.run('INSERT INTO quiz_packs (id, name) VALUES (?, ?)', ['duplicate-id', 'Test Pack 2']);
          return false; // 如果没有抛出错误，说明约束失效
        } catch (error) {
          return error.message.includes('UNIQUE constraint failed');
        }
      };

      mockDb.run.mockRejectedValueOnce(new Error('UNIQUE constraint failed: quiz_packs.id'));
      
      const constraintWorking = await uniqueConstraintTest();
      expect(constraintWorking).toBe(true);
    });
  });

  describe('5. 数据库性能验证', () => {
    it('应该验证查询性能在可接受范围内', async () => {
      const startTime = Date.now();
      
      // 模拟复杂查询
      mockDb.query.mockResolvedValueOnce([
        { pack_count: 10, question_count: 100, option_count: 400 }
      ]);

      const result = await mockDb.query(`
        SELECT 
          COUNT(DISTINCT qp.id) as pack_count,
          COUNT(DISTINCT qq.id) as question_count,
          COUNT(qo.id) as option_count
        FROM quiz_packs qp
        LEFT JOIN quiz_questions qq ON qp.id = qq.quiz_pack_id
        LEFT JOIN quiz_question_options qo ON qq.id = qo.question_id
      `);

      const endTime = Date.now();
      const queryTime = endTime - startTime;

      expect(result).toHaveLength(1);
      expect(queryTime).toBeLessThan(100); // 查询应在100ms内完成
    });

    it('应该验证数据库大小在合理范围内', async () => {
      const dbStats = {
        page_count: 1000,
        page_size: 4096,
        total_size: 4096000 // ~4MB
      };

      mockDb.query.mockResolvedValueOnce([dbStats]);

      const result = await mockDb.query('PRAGMA page_count; PRAGMA page_size;');
      const totalSize = result[0].page_count * result[0].page_size;

      expect(totalSize).toBeLessThan(10 * 1024 * 1024); // 应小于10MB
      expect(result[0].page_count).toBeGreaterThan(0);
    });
  });
});
