{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/user-experience/user-experience.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 53818, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 53818, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 526, "endOffset": 20424, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 569, "endOffset": 630, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 682, "endOffset": 3720, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 737, "endOffset": 1657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1707, "endOffset": 2327, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2376, "endOffset": 3712, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3228, "endOffset": 3510, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3771, "endOffset": 7946, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3824, "endOffset": 5105, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5156, "endOffset": 6275, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5802, "endOffset": 5967, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6324, "endOffset": 7084, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7136, "endOffset": 7938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7998, "endOffset": 11132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8050, "endOffset": 9297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8789, "endOffset": 8979, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9345, "endOffset": 10282, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10332, "endOffset": 11124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11183, "endOffset": 16558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11238, "endOffset": 13387, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13119, "endOffset": 13375, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13438, "endOffset": 14941, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14457, "endOffset": 14650, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14991, "endOffset": 16550, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16058, "endOffset": 16351, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16609, "endOffset": 20420, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16662, "endOffset": 17895, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17435, "endOffset": 17691, "count": 3}, {"startOffset": 17574, "endOffset": 17677, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17944, "endOffset": 19182, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19234, "endOffset": 20412, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}