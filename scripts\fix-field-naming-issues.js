#!/usr/bin/env node

/**
 * 字段命名问题自动修复脚本
 * 修复驼峰命名 vs 下划线命名的不一致问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 字段映射规则
const fieldMappings = {
  // 皮肤相关
  'isUnlocked': 'is_unlocked',
  'isPremium': 'is_premium',
  'isDefault': 'is_default',
  'isSystem': 'is_system',
  'previewImage': 'preview_image',
  'previewImageLight': 'preview_image_light',
  'previewImageDark': 'preview_image_dark',
  'created_at': 'created_at',
  'updated_at': 'updated_at',
  'created_by': 'created_by',
  'lastUpdated': 'last_updated',

  // 配置相关
  'viewConfigs': 'view_configs',
  'borderRadius': 'border_radius',
  'cardSize': 'card_size',
  'cardWidth': 'card_width',
  'cardHeight': 'card_height',
  'cardSpacing': 'card_spacing',
  'cardBorderRadius': 'card_border_radius',
  'bubbleSize': 'bubble_size',
  'bubbleSpacing': 'bubble_spacing',
  'bubbleBackgroundOpacity': 'bubble_background_opacity',
  'textVisible': 'text_visible',

  // 用户配置相关
  'userId': 'user_id',
  'isActive': 'is_active',
  'activeEmotionDataId': 'active_emotion_data_id',
  'activeSkinId': 'active_skin_id',
  'preferredViewType': 'preferred_view_type',
  'darkMode': 'dark_mode',
  'viewTypeSkinIds': 'view_type_skin_ids',
  'renderEnginePreferences': 'render_engine_preferences',
  'contentDisplayModePreferences': 'content_display_mode_preferences',
  'layoutPreferences': 'layout_preferences',

  // 情绪数据相关
  'emotionDataSetId': 'emotion_data_set_id',
  'parentTierId': 'parent_tier_id',
  'defaultEmojiSetId': 'default_emoji_set_id',
  'tierLevel': 'tier_level',
  'tierId': 'tier_id',

  // 其他
  'emojiSize': 'emoji_size',
  'textSize': 'text_size',
  'shadowColor': 'shadow_color',
  'shadowBlur': 'shadow_blur',
  'shadowOffsetX': 'shadow_offset_x',
  'shadowOffsetY': 'shadow_offset_y',
  'animationDuration': 'animation_duration',
  'animationEasing': 'animation_easing',
  'textShadow': 'text_shadow',
};

// 反向映射
const reverseFieldMappings = {};
for (const [camel, snake] of Object.entries(fieldMappings)) {
  reverseFieldMappings[snake] = camel;
}

// 获取所有 TypeScript 文件
function getAllTsFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...getAllTsFiles(fullPath));
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }

  return files;
}

// 修复文件中的字段命名问题
function fixFieldIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let modifiedContent = content;
    let changeCount = 0;

    // 检测并修复驼峰命名字段的使用
    for (const [camelField, snakeField] of Object.entries(fieldMappings)) {
      // 匹配 .camelField 或 ?.camelField 的模式
      const camelRegex = new RegExp(`(\\w+\\.|\\w+\\?\\.)${camelField}\\b`, 'g');

      if (camelRegex.test(modifiedContent)) {
        // 只在非类型定义的地方进行替换
        const lines = modifiedContent.split('\n');
        const newLines = lines.map(line => {
          // 跳过类型定义、接口定义、Schema定义等
          if (line.includes('interface') ||
              line.includes('type ') ||
              line.includes('Schema') ||
              line.includes('z.object') ||
              line.includes('export type') ||
              line.includes('export interface')) {
            return line;
          }

          const regex = new RegExp(`(\\w+\\.|\\w+\\?\\.)${camelField}\\b`, 'g');
          if (regex.test(line)) {
            changeCount++;
            return line.replace(regex, `$1${snakeField}`);
          }
          return line;
        });
        modifiedContent = newLines.join('\n');
      }
    }

    // 如果有修改，写回文件
    if (changeCount > 0) {
      fs.writeFileSync(filePath, modifiedContent, 'utf8');
      console.log(`✅ 修复 ${filePath}: ${changeCount} 个字段命名问题`);
      return changeCount;
    }

    return 0;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return 0;
  }
}

// 主函数
function main() {
  const rootDir = path.dirname(__dirname);
  const srcDir = path.join(rootDir, 'src');
  const serverDir = path.join(rootDir, 'server');

  // 获取所有需要修复的文件
  const srcFiles = getAllTsFiles(srcDir);
  const serverFiles = getAllTsFiles(serverDir);
  const allFiles = [...srcFiles, ...serverFiles];

  console.log(`🔧 开始修复 ${allFiles.length} 个 TypeScript 文件中的字段命名问题...`);
  console.log(`  - src 目录: ${srcFiles.length} 个文件`);
  console.log(`  - server 目录: ${serverFiles.length} 个文件\n`);

  let totalChanges = 0;
  let modifiedFiles = 0;

  for (const file of allFiles) {
    const changes = fixFieldIssues(file);
    if (changes > 0) {
      totalChanges += changes;
      modifiedFiles++;
    }
  }

  console.log(`\n📊 修复完成统计:`);
  console.log(`修改的文件数: ${modifiedFiles}`);
  console.log(`总修复数量: ${totalChanges}`);

  if (totalChanges > 0) {
    console.log(`\n✅ 字段命名问题修复完成！`);
    console.log(`💡 建议运行 TypeScript 检查确保没有引入新的错误:`);
    console.log(`   npx tsc --noEmit`);
  } else {
    console.log(`\n✅ 没有发现需要修复的字段命名问题！`);
  }
}

main();
