/**
 * Quiz文本输入组件
 * 支持多种中医文化样式的文本输入组件
 */

import React, { useState, useRef } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { TextInputComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type TextInputComponentConfig = z.infer<typeof TextInputComponentConfigSchema>;

export interface TextInputComponentProps extends BaseQuizComponentProps<TextInputComponentConfig> {
  value: string;
  onChange: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  disabled?: boolean;
  label?: Record<string, string>;
}

interface TextInputComponentState extends ComponentState {
  current_value: string;
  is_focused: boolean;
  validation_errors: string[];
  character_count: number;
}

/**
 * 文本输入组件类
 */
export class TextInputComponent extends BaseQuizComponent<
  TextInputComponentConfig,
  TextInputComponentProps,
  TextInputComponentState
> {
  private inputRef = React.createRef<HTMLInputElement | HTMLTextAreaElement>();

  extractConfig(props: TextInputComponentProps): TextInputComponentConfig {
    return props.config;
  }

  getInitialState(): TextInputComponentState {
    return {
      is_loading: false,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      current_value: this.props.value,
      is_focused: false,
      validation_errors: [],
      character_count: this.props.value.length
    };
  }

  componentDidUpdate(prevProps: TextInputComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.value !== this.props.value) {
      this.setState({ 
        current_value: this.props.value,
        character_count: this.props.value.length
      });
      
      if (this.config.validation.real_time) {
        this.validateInput(this.props.value);
      }
    }
    
    if (prevProps.disabled !== this.props.disabled) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled
      });
    }
  }

  /**
   * 验证输入内容
   */
  private validateInput(value: string): string[] {
    const errors: string[] = [];
    const { input, validation } = this.config;
    const { language } = this.context || { language: 'zh' };

    // 必填验证
    if (input.required && !value.trim()) {
      errors.push(language === 'zh' ? '此字段为必填项' : 'This field is required');
    }

    // 长度验证
    if (input.min_length && value.length < input.min_length) {
      errors.push(
        language === 'zh' 
          ? `最少需要${input.min_length}个字符` 
          : `Minimum ${input.min_length} characters required`
      );
    }

    if (input.max_length && value.length > input.max_length) {
      errors.push(
        language === 'zh' 
          ? `最多允许${input.max_length}个字符` 
          : `Maximum ${input.max_length} characters allowed`
      );
    }

    // 格式验证
    if (input.pattern && value && !new RegExp(input.pattern).test(value)) {
      errors.push(language === 'zh' ? '格式不正确' : 'Invalid format');
    }

    // 邮箱验证
    if (input.type === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      errors.push(language === 'zh' ? '请输入有效的邮箱地址' : 'Please enter a valid email address');
    }

    this.setState({ validation_errors: errors });
    return errors;
  }

  /**
   * 处理输入变化
   */
  private handleInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>): void => {
    const newValue = event.target.value;
    
    // 长度限制
    if (this.config.input.max_length && newValue.length > this.config.input.max_length) {
      return;
    }

    this.setState({ 
      current_value: newValue,
      character_count: newValue.length
    });

    this.props.onChange(newValue);

    // 实时验证
    if (this.config.validation.real_time) {
      this.validateInput(newValue);
    }

    // 发送交互事件
    this.emitInteractionEvent('input', {
      action: 'text_changed',
      value: newValue,
      character_count: newValue.length
    });
  };

  /**
   * 处理焦点获得
   */
  private handleFocus = (): void => {
    this.setState({ is_focused: true });
    this.props.onFocus?.();

    // 发送交互事件
    this.emitInteractionEvent('focus', {
      action: 'input_focused'
    });
  };

  /**
   * 处理焦点失去
   */
  private handleBlur = (): void => {
    this.setState({ is_focused: false });
    this.props.onBlur?.();

    // 完整验证
    this.validateInput(this.state.current_value);

    // 发送交互事件
    this.emitInteractionEvent('blur', {
      action: 'input_blurred',
      final_value: this.state.current_value
    });
  };

  /**
   * 获取占位符文本
   */
  private getPlaceholderText(): string {
    const { language } = this.context || { language: 'zh' };
    const placeholder = this.config.input.placeholder;
    
    if (placeholder) {
      return placeholder[language] || placeholder['zh'] || placeholder['en'] || '';
    }
    
    return '';
  }

  /**
   * 获取标签文本
   */
  private getLabelText(): string {
    const { language } = this.context || { language: 'zh' };
    const label = this.props.label;
    
    if (label) {
      return label[language] || label['zh'] || label['en'] || '';
    }
    
    return '';
  }

  /**
   * 获取边框样式类名
   */
  private getBorderStyleClassName(): string {
    const borderStyle = this.config.style.border_style;
    return `quiz-text-input-border-${borderStyle}`;
  }

  /**
   * 获取尺寸样式类名
   */
  private getSizeClassName(): string {
    const size = this.config.style.size;
    return `quiz-text-input-${size}`;
  }

  /**
   * 渲染标签
   */
  private renderLabel(): React.ReactNode {
    const labelText = this.getLabelText();
    if (!labelText) return null;

    const labelPosition = this.config.style.label_position;
    
    return (
      <label 
        className={`quiz-text-input-label quiz-text-input-label-${labelPosition}`}
        htmlFor={this.props.id}
      >
        {labelText}
        {this.config.input.required && (
          <span className="quiz-text-input-required">*</span>
        )}
      </label>
    );
  }

  /**
   * 渲染字符计数器
   */
  private renderCharacterCounter(): React.ReactNode {
    if (!this.config.style.show_counter) return null;

    const { max_length } = this.config.input;
    const currentCount = this.state.character_count;

    return (
      <div className="quiz-text-input-counter">
        {max_length ? `${currentCount}/${max_length}` : currentCount}
      </div>
    );
  }

  /**
   * 渲染错误信息
   */
  private renderErrors(): React.ReactNode {
    if (!this.config.validation.show_errors || this.state.validation_errors.length === 0) {
      return null;
    }

    return (
      <div className="quiz-text-input-errors">
        {this.state.validation_errors.map((error, index) => (
          <div key={index} className="quiz-text-input-error">
            {error}
          </div>
        ))}
      </div>
    );
  }

  /**
   * 渲染输入元素
   */
  private renderInputElement(): React.ReactNode {
    const placeholderText = this.getPlaceholderText();
    const commonProps = {
      id: this.props.id,
      value: this.state.current_value,
      placeholder: placeholderText,
      disabled: this.state.is_disabled,
      onChange: this.handleInputChange,
      onFocus: this.handleFocus,
      onBlur: this.handleBlur,
      className: 'quiz-text-input-element',
      'aria-invalid': this.state.validation_errors.length > 0,
      'aria-describedby': this.state.validation_errors.length > 0 ? `${this.props.id}-errors` : undefined
    };

    if (this.config.input.type === 'textarea') {
      return (
        <textarea
          {...commonProps}
          ref={this.inputRef as React.RefObject<HTMLTextAreaElement>}
          rows={4}
          style={{
            resize: this.config.style.resize
          }}
        />
      );
    }

    return (
      <input
        {...commonProps}
        ref={this.inputRef as React.RefObject<HTMLInputElement>}
        type={this.config.input.type}
        pattern={this.config.input.pattern}
        minLength={this.config.input.min_length}
        maxLength={this.config.input.max_length}
      />
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-text-input-component',
      this.getSizeClassName(),
      this.getBorderStyleClassName(),
      this.state.is_focused && 'quiz-text-input-focused',
      this.state.is_disabled && 'quiz-text-input-disabled',
      this.state.validation_errors.length > 0 && 'quiz-text-input-error',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
      >
        {/* 标签 */}
        {this.config.style.label_position === 'top' && this.renderLabel()}

        {/* 输入容器 */}
        <div className="quiz-text-input-container">
          {this.config.style.label_position === 'left' && this.renderLabel()}
          
          <div className="quiz-text-input-wrapper">
            {this.config.style.label_position === 'inside' && this.renderLabel()}
            {this.renderInputElement()}
            {this.renderCharacterCounter()}
          </div>
        </div>

        {/* 错误信息 */}
        {this.renderErrors()}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'textbox';
  }

  protected getAriaLabel(): string {
    const labelText = this.getLabelText();
    return labelText || 'Text input';
  }
}

// 使用Context的函数式组件包装器
const TextInputComponentWrapper: React.FC<TextInputComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <TextInputComponent.contextType.Provider value={{ language }}>
      <TextInputComponent {...props} />
    </TextInputComponent.contextType.Provider>
  );
};

// 设置Context类型
TextInputComponent.contextType = React.createContext({ language: 'zh' });

export default TextInputComponentWrapper;
