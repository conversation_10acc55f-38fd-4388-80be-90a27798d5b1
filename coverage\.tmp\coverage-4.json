{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/regression/regression.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 86228, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 86228, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 518, "endOffset": 29478, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 561, "endOffset": 622, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 675, "endOffset": 8709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 734, "endOffset": 3389, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3442, "endOffset": 6026, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6079, "endOffset": 8701, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8760, "endOffset": 15848, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8814, "endOffset": 11419, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9292, "endOffset": 10299, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9382, "endOffset": 9944, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10050, "endOffset": 10067, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10197, "endOffset": 10221, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11470, "endOffset": 13531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13583, "endOffset": 15840, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13729, "endOffset": 14160, "count": 2}, {"startOffset": 13883, "endOffset": 13889, "count": 0}, {"startOffset": 13952, "endOffset": 13957, "count": 1}, {"startOffset": 13958, "endOffset": 13973, "count": 1}, {"startOffset": 14034, "endOffset": 14040, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15903, "endOffset": 21329, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15959, "endOffset": 18549, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18600, "endOffset": 21321, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21382, "endOffset": 26648, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21436, "endOffset": 24003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24054, "endOffset": 26640, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26700, "endOffset": 29474, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26755, "endOffset": 29466, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}