/**
 * 商店数据管理钩子
 * 实现离线在线混合模式的商店功能管理
 */

import { trpc } from '@/lib/trpc';
import { Services } from '@/services';
import type { EmojiSet, Skin } from '@/types';
import { useCallback, useEffect, useState } from 'react';
import { useAuth } from './useAuth';
import { useVip } from './useVip';

// 导入统一类型定义
import type {
  ApiResponse,
  PurchaseHistoryItem,
  PurchaseResult as UnifiedPurchaseResult,
} from '@/types/unified';

// 商店商品类型
export interface ShopItem {
  id: string;
  type: 'skin' | 'emoji_set' | 'vip_plan';
  name: string;
  description?: string;
  price: number;
  currency: string;
  isUnlocked: boolean;
  isPurchased: boolean;
  isActive: boolean;
  unlockCondition?: 'purchase' | 'vip' | 'free';
  previewUrl?: string;
  features?: string[];
}

// 购买历史类型
export interface PurchaseHistory {
  id: string;
  itemId: string;
  itemType: 'skin' | 'emoji_set' | 'vip_plan';
  itemName: string;
  price: number;
  currency: string;
  purchaseDate: Date;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  transactionId?: string;
}

// 购买结果类型
export interface PurchaseResult {
  success: boolean;
  transactionId?: string;
  error?: string;
}

interface UseShopReturn {
  // 商品数据
  skins: ShopItem[];
  emojiSets: ShopItem[];
  allItems: ShopItem[];

  // 购买历史
  purchaseHistory: PurchaseHistory[];

  // 加载状态
  isLoading: boolean;
  error: string | null;

  // 网络状态
  isOnline: boolean;
  lastSyncTime: Date | null;

  // 操作方法
  refresh: () => Promise<void>;
  forceSync: () => Promise<void>;

  // 购买操作
  purchaseSkin: (skinId: string, paymentMethodId: string) => Promise<PurchaseResult>;
  purchaseEmojiSet: (emojiSetId: string, paymentMethodId: string) => Promise<PurchaseResult>;

  // 使用操作
  activateSkin: (skinId: string) => Promise<{ success: boolean; error?: string }>;
  activateEmojiSet: (emojiSetId: string) => Promise<{ success: boolean; error?: string }>;

  // 查询方法
  getItemById: (id: string) => ShopItem | undefined;
  getUnlockedItems: () => ShopItem[];
  getPurchasedItems: () => ShopItem[];
  canPurchaseItem: (item: ShopItem) => boolean;
}

/**
 * 商店数据管理钩子
 */
export const useShop = (): UseShopReturn => {
  const { isAuthenticated, user, isOnline } = useAuth();
  const { isVip } = useVip();

  const [skins, setSkins] = useState<ShopItem[]>([]);
  const [emojiSets, setEmojiSets] = useState<ShopItem[]>([]);
  const [purchaseHistory, setPurchaseHistory] = useState<PurchaseHistory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // 转换皮肤数据为商店商品
  const convertSkinsToShopItems = useCallback(
    (skinsData: Skin[]): ShopItem[] => {
      return skinsData.map((skin) => {
        // 解析解锁条件
        let unlockConditions: { type: string; price?: number; currency?: string };
        try {
          unlockConditions = skin.unlock_conditions
            ? JSON.parse(skin.unlock_conditions)
            : { type: 'free' };
        } catch {
          unlockConditions = { type: 'free' };
        }

        return {
          id: skin.id,
          type: 'skin' as const,
          name: skin.name,
          description: skin.description,
          price: unlockConditions.price || 0,
          currency: unlockConditions.currency || 'USD',
          isUnlocked:
            skin.is_unlocked ||
            unlockConditions.type === 'free' ||
            (unlockConditions.type === 'vip' && isVip),
          isPurchased: skin.is_unlocked || false,
          isActive: false, // 需要从用户配置中获取当前激活的皮肤
          unlockCondition: unlockConditions.type as 'purchase' | 'vip' | 'free',
          previewUrl: skin.preview_image_light || skin.preview_image_dark,
          features: [],
        };
      });
    },
    [isVip]
  );

  // 转换表情集数据为商店商品
  const convertEmojiSetsToShopItems = useCallback((emojiSetsData: EmojiSet[]): ShopItem[] => {
    return emojiSetsData.map((emojiSet) => {
      // 表情集的解锁逻辑：基于价格判断是否需要购买
      const unlockCondition = emojiSet.price && emojiSet.price > 0 ? 'purchase' : 'free';

      return {
        id: emojiSet.id,
        type: 'emoji_set' as const,
        name: emojiSet.name,
        description: emojiSet.description,
        price: emojiSet.price || 0,
        currency: 'USD',
        isUnlocked: emojiSet.is_unlocked || unlockCondition === 'free',
        isPurchased: emojiSet.is_unlocked || false,
        isActive: emojiSet.is_default || false, // 使用 is_default 判断是否激活
        unlockCondition: unlockCondition as 'purchase' | 'vip' | 'free',
        previewUrl: emojiSet.preview_image,
        features: [],
      };
    });
  }, []);

  // 加载商店数据
  const loadShopData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 加载皮肤数据
      const skinService = await Services.skin();
      const skinsResult = await skinService.getAll();
      if (skinsResult.success) {
        const skinItems = convertSkinsToShopItems(skinsResult.data);
        setSkins(skinItems);
      }

      // 加载表情集数据
      const emojiSetService = await Services.emojiSet();
      const emojiSetsResult = await emojiSetService.getAll();
      if (emojiSetsResult.success) {
        const emojiSetItems = convertEmojiSetsToShopItems(emojiSetsResult.data);
        setEmojiSets(emojiSetItems);
      }

      setLastSyncTime(new Date());
      console.log('[useShop] Shop data loaded successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load shop data';
      setError(errorMessage);
      console.error('[useShop] Error loading shop data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [convertSkinsToShopItems, convertEmojiSetsToShopItems]);

  // 加载购买历史
  const loadPurchaseHistory = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setPurchaseHistory([]);
      return;
    }

    try {
      // 优先从在线服务获取
      if (isOnline) {
        try {
          console.log('[useShop] Loading purchase history from online service...');
          // 使用tRPC获取购买历史
          const result = await trpc.getPurchaseHistory.query({ limit: 50, offset: 0 });

          if (result?.success && Array.isArray(result.data)) {
            const history: PurchaseHistory[] = result.data.map((item: any) => ({
              id: item.id,
              itemType:
                item.transaction_type === 'subscription'
                  ? ('vip_plan' as const)
                  : item.transaction_type === 'skin_purchase'
                    ? ('skin' as const)
                    : ('emoji_set' as const),
              itemId:
                item.transaction_type === 'subscription'
                  ? 'vip'
                  : item.description?.split(':')[1]?.trim() || item.id,
              itemName: item.description || 'Unknown Item',
              price: item.amount,
              currency: item.currency,
              purchaseDate: new Date(item.created_at),
              status: item.status,
              transactionId: item.id,
            }));
            setPurchaseHistory(history);

            // 缓存到本地存储
            localStorage.setItem('purchase_history', JSON.stringify(history));

            console.log('[useShop] Purchase history loaded from online service');
            return;
          }
        } catch (onlineError) {
          console.warn('[useShop] Online purchase history loading failed:', onlineError);
        }
      }

      // 回退到本地缓存
      try {
        const cachedHistory = localStorage.getItem('purchase_history');
        if (cachedHistory) {
          const parsed = JSON.parse(cachedHistory);
          const history = parsed.map((item: any) => ({
            ...item,
            purchaseDate: new Date(item.purchaseDate),
          }));
          setPurchaseHistory(history);
          console.log('[useShop] Purchase history loaded from cache');
        }
      } catch (cacheError) {
        console.error('[useShop] Error loading purchase history from cache:', cacheError);
      }
    } catch (error) {
      console.error('[useShop] Error loading purchase history:', error);
    }
  }, [isAuthenticated, user, isOnline]);

  // 购买皮肤
  const purchaseSkin = useCallback(
    async (skinId: string, paymentMethodId: string): Promise<PurchaseResult> => {
      try {
        if (!isAuthenticated) {
          throw new Error('User not authenticated');
        }

        if (!isOnline) {
          throw new Error('Purchase requires internet connection');
        }

        console.log('[useShop] Processing skin purchase...', { skinId, paymentMethodId });

        // 使用tRPC购买皮肤
        const result = await (trpc as any).purchaseSkin.mutate({
          skinId,
          paymentMethodId,
        });

        if (result?.success) {
          // 购买成功后刷新商店数据
          await loadShopData();
          await loadPurchaseHistory();

          console.log('[useShop] Skin purchase successful');
          return {
            success: true,
            transactionId: result.transactionId,
          };
        }
        throw new Error('Purchase failed');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Purchase failed';
        console.error('[useShop] Skin purchase error:', error);
        return {
          success: false,
          error: errorMessage,
        };
      }
    },
    [isAuthenticated, isOnline, loadShopData, loadPurchaseHistory]
  );

  // 购买表情集
  const purchaseEmojiSet = useCallback(
    async (emojiSetId: string, paymentMethodId: string): Promise<PurchaseResult> => {
      try {
        if (!isAuthenticated) {
          throw new Error('User not authenticated');
        }

        if (!isOnline) {
          throw new Error('Purchase requires internet connection');
        }

        console.log('[useShop] Processing emoji set purchase...', { emojiSetId, paymentMethodId });

        // 使用tRPC购买表情集
        const result = await (trpc as any).purchaseEmojiSet.mutate({
          emojiSetId,
          paymentMethodId,
        });

        if (result?.success) {
          // 购买成功后刷新商店数据
          await loadShopData();
          await loadPurchaseHistory();

          console.log('[useShop] Emoji set purchase successful');
          return {
            success: true,
            transactionId: result.transactionId || undefined,
          };
        }
        throw new Error('Purchase failed');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Purchase failed';
        console.error('[useShop] Emoji set purchase error:', error);
        return {
          success: false,
          error: errorMessage,
        };
      }
    },
    [isAuthenticated, isOnline, loadShopData, loadPurchaseHistory]
  );

  // 激活皮肤
  const activateSkin = useCallback(
    async (skinId: string): Promise<{ success: boolean; error?: string }> => {
      try {
        const skinService = await Services.skin();
        const result = await skinService.setDefaultSkin(skinId);

        if (result.success) {
          await loadShopData();
          return { success: true };
        }
        throw new Error(result.error);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to activate skin';
        return { success: false, error: errorMessage };
      }
    },
    [loadShopData]
  );

  // 激活表情集
  const activateEmojiSet = useCallback(
    async (emojiSetId: string): Promise<{ success: boolean; error?: string }> => {
      try {
        const emojiSetService = await Services.emojiSet();
        const result = await emojiSetService.setActiveEmojiSet(emojiSetId);

        if (result.success) {
          await loadShopData();
          return { success: true };
        }
        throw new Error(result.error);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to activate emoji set';
        return { success: false, error: errorMessage };
      }
    },
    [loadShopData]
  );

  // 刷新数据
  const refresh = useCallback(async () => {
    await Promise.all([loadShopData(), loadPurchaseHistory()]);
  }, [loadShopData, loadPurchaseHistory]);

  // 强制同步
  const forceSync = useCallback(async () => {
    if (isOnline) {
      await refresh();
    }
  }, [isOnline, refresh]);

  // 查询方法
  const allItems = [...skins, ...emojiSets];

  const getItemById = useCallback(
    (id: string): ShopItem | undefined => {
      return allItems.find((item) => item.id === id);
    },
    [allItems]
  );

  const getUnlockedItems = useCallback((): ShopItem[] => {
    return allItems.filter((item) => item.is_unlocked);
  }, [allItems]);

  const getPurchasedItems = useCallback((): ShopItem[] => {
    return allItems.filter((item) => item.isPurchased);
  }, [allItems]);

  const canPurchaseItem = useCallback(
    (item: ShopItem): boolean => {
      if (item.is_unlocked || item.isPurchased) {
        return false;
      }

      if (item.unlockCondition === 'free') {
        return true;
      }

      if (item.unlockCondition === 'vip') {
        return isVip;
      }

      if (item.unlockCondition === 'purchase') {
        return isAuthenticated && isOnline;
      }

      return false;
    },
    [isVip, isAuthenticated, isOnline]
  );

  // 初始化加载
  useEffect(() => {
    refresh();
  }, [refresh]);

  // 网络恢复时同步
  useEffect(() => {
    if (isOnline && isAuthenticated) {
      forceSync();
    }
  }, [isOnline, isAuthenticated, forceSync]);

  return {
    // 商品数据
    skins,
    emojiSets,
    allItems,

    // 购买历史
    purchaseHistory,

    // 加载状态
    isLoading,
    error,

    // 网络状态
    isOnline,
    lastSyncTime,

    // 操作方法
    refresh,
    forceSync,

    // 购买操作
    purchaseSkin,
    purchaseEmojiSet,

    // 使用操作
    activateSkin,
    activateEmojiSet,

    // 查询方法
    getItemById,
    getUnlockedItems,
    getPurchasedItems,
    canPurchaseItem,
  };
};
