# 离线存储与在线同步实现计划

本文档概述了应用程序离线存储和在线同步功能的实现计划。该应用允许用户在不联网的情况下使用Capacitor-SQLite实现离线存储和使用核心产品功能，当打开数据同步后，则可以同步数据到云端的数据库，使用tRPC链接云端数据库，实现数据同步、用户登录等核心功能。

## 架构概述

### 1. 离线存储层

离线存储层使用Capacitor-SQLite实现，负责在本地设备上存储和管理用户数据。主要组件包括：

- **DatabaseService**: 单例服务，负责SQLite数据库的初始化、连接管理和基本操作。
- **实体服务模块**: 包括MoodEntryService、EmotionSelectionService、UserConfigService等，负责特定数据类型的CRUD操作。
- **业务逻辑服务**: 如MoodTrackingService，处理复杂的业务逻辑。
- **数据模型**: 定义数据结构，包括同步状态字段，用于跟踪数据的同步状态。

### 2. 在线同步层 (基于 tRPC + SQL)

在线同步层使用tRPC + 直接SQL查询实现，负责与云端数据库进行数据同步。主要组件包括：

- **ApiClientService**: 基于tRPC的SQL客户端，支持直接SQL执行、认证、重试、缓存。
- **OnlineSyncService**: 负责协调本地数据和云端数据的同步过程。
- **CloudDataService**: 提供高级的云端数据访问接口，基于SQL查询构建。
- **AuthService**: 用户认证和会话管理。
- **NetworkStatusService**: 网络状态监控和管理。

### 3. 用户界面层

用户界面层提供用户交互界面，允许用户控制同步行为和查看同步状态。主要组件包括：

- **同步设置界面**: 允许用户开启/关闭同步功能。
- **同步状态指示器**: 显示当前同步状态和上次同步时间。
- **同步错误处理**: 显示同步过程中的错误信息并提供重试选项。

## 数据模型

### 1. 核心数据表

所有核心数据表都需要添加同步相关字段：

```sql
-- 同步状态字段（所有核心数据表都需要添加）
sync_status TEXT DEFAULT 'pending' CHECK(sync_status IN ('pending', 'syncing', 'synced', 'error')),
last_synced_at TEXT DEFAULT NULL,
server_id TEXT DEFAULT NULL,
```

### 2. 同步状态定义

- **pending**: 数据尚未同步到云端
- **syncing**: 数据正在同步到云端
- **synced**: 数据已成功同步到云端
- **error**: 数据同步过程中发生错误

## 实现步骤

### 1. 离线存储实现

#### 1.1 数据库初始化

- 使用Capacitor-SQLite创建本地数据库
- 定义数据表结构，包括同步状态字段
- 实现数据库版本管理和迁移机制

#### 1.2 离线服务实现

- 实现emotionService，提供情绪数据的CRUD操作
- 实现historyService，提供历史记录的CRUD操作
- 实现tagService，提供标签的CRUD操作
- 确保所有写操作都正确设置sync_status为'pending'

### 2. 在线同步实现

#### 2.1 tRPC SQL客户端实现

- 实现ApiClientService，基于tRPC提供直接SQL查询能力
- 支持认证、缓存、重试等功能
- 提供便捷的SQL操作方法（select、insert、update、delete）
- 支持批量SQL操作和事务

#### 2.2 云端数据服务实现

- 实现CloudDataService，提供高级的数据访问接口
- 基于SQL查询构建业务友好的API
- 支持分页、过滤、排序等高级查询功能
- 提供数据分析和统计接口

#### 2.3 同步服务实现

- 实现OnlineSyncService，负责协调本地数据和云端数据的同步
- 实现数据冲突检测和解决策略
- 实现增量同步机制，只同步变更的数据
- 支持网络状态感知的智能同步

### 3. 用户界面实现

#### 3.1 同步设置界面

- 实现同步开关控件，允许用户开启/关闭同步功能
- 实现同步频率设置，允许用户选择自动同步的频率
- 实现手动同步按钮，允许用户随时触发同步

#### 3.2 同步状态指示器

- 实现同步状态图标，显示当前同步状态
- 实现上次同步时间显示
- 实现同步进度指示器，显示同步进度

#### 3.3 同步错误处理

- 实现错误提示对话框，显示同步错误信息
- 实现重试按钮，允许用户重试失败的同步
- 实现错误日志记录，方便调试和问题排查

### 4. VIP状态与皮肤解锁实现

#### 4.1 VIP状态同步

- 实现VIP状态的云端验证
- 实现VIP状态的本地缓存
- 实现VIP特定功能的解锁/锁定逻辑

#### 4.2 皮肤解锁同步

- 实现皮肤解锁状态的云端验证
- 实现皮肤解锁状态的本地缓存
- 实现皮肤选择和应用逻辑

## 技术实现细节

### 1. 离线存储技术细节

- 使用Capacitor-SQLite的事务功能确保数据一致性
- 使用预编译SQL语句提高性能和安全性
- 实现数据库连接池管理，优化资源使用

### 2. 在线同步技术细节

- 使用tRPC + 直接SQL查询，提供最大的灵活性和性能
- 支持批量SQL操作和事务，确保数据一致性
- 实现指数退避重试策略，处理网络不稳定情况
- 使用JWT进行API认证，确保数据安全
- 智能缓存机制，减少重复查询

### 3. 性能优化技术细节

- 实现增量同步，只传输变更数据
- 使用压缩算法减少传输数据量
- 实现后台同步，不阻塞用户界面

## 测试计划

详细的测试计划请参考 [离线存储与数据同步测试计划](./offline-online-sync-test-plan.md)。

## 部署计划

### 1. 开发环境部署

- 使用本地SQLite数据库进行开发和测试
- 使用Mock Service Worker模拟tRPC API响应

### 2. 测试环境部署

- 使用测试Turso数据库实例
- 部署测试tRPC服务到Cloudflare Workers
- 配置测试环境的SQL查询权限和认证

### 3. 生产环境部署

- 使用生产Turso数据库实例
- 部署生产tRPC服务到Cloudflare Workers/Pages
- 配置生产环境的安全策略和访问控制

## 时间线

1. **第1周**: 实现离线存储基础设施
2. **第2周**: 实现在线同步基础设施
3. **第3周**: 实现用户界面和交互
4. **第4周**: 实现VIP状态和皮肤解锁同步
5. **第5周**: 测试和修复问题
6. **第6周**: 部署和监控

## 风险与缓解策略

### 1. 网络不稳定风险

- **风险**: 网络不稳定可能导致同步失败或数据不一致
- **缓解**: 实现健壮的重试机制和冲突解决策略

### 2. 数据冲突风险

- **风险**: 多设备同时修改同一数据可能导致冲突
- **缓解**: 实现基于时间戳的冲突检测和解决策略

### 3. 存储空间风险

- **风险**: 本地存储空间不足可能导致数据丢失
- **缓解**: 实现存储空间监控和数据清理机制

## 结论

通过实现离线存储和在线同步功能，我们的应用将能够在各种网络条件下提供一致的用户体验。用户可以在离线状态下使用核心功能，并在网络连接恢复后自动同步数据。同时，VIP状态和付费皮肤解锁等特定功能将通过云端服务进行验证，确保只有授权用户才能访问这些功能。
