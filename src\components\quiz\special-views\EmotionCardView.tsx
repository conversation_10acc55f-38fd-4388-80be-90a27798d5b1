/**
 * 情绪卡片视图组件
 * 
 * 以卡片形式展示情绪数据集，支持网格布局和详细信息展示
 * 这是一个特殊视图组件，用于Quiz系统中的情绪选择场景
 */

import React, { useState } from 'react';
import { PersonalizationConfig, InteractionEvent } from '../../../types/schema/base';

// 情绪卡片数据接口
export interface EmotionCardData {
  id: string;
  name: Record<string, string>;
  description?: Record<string, string>;
  emoji?: string;
  color?: string;
  intensity?: number;
  category?: string;
  tags?: string[];
  image_url?: string;
  tier_level?: number;
}

// 卡片布局配置
export interface CardLayoutConfig {
  columns: number;
  card_width: number;
  card_height: number;
  spacing: number;
  show_descriptions: boolean;
  show_intensity: boolean;
  show_categories: boolean;
  animation_enabled: boolean;
  selection_mode: 'single' | 'multiple';
}

// 组件属性接口
export interface EmotionCardViewProps {
  id: string;
  emotions: EmotionCardData[];
  selectedEmotions: string[];
  onEmotionSelect: (emotionId: string, emotion: EmotionCardData) => void;
  onEmotionDeselect?: (emotionId: string) => void;
  config?: Partial<CardLayoutConfig>;
  personalization: PersonalizationConfig;
  onInteraction: (event: InteractionEvent) => void;
  language?: string;
  style?: React.CSSProperties;
  maxSelections?: number;
}

// 默认配置
const DEFAULT_CONFIG: CardLayoutConfig = {
  columns: 3,
  card_width: 200,
  card_height: 150,
  spacing: 16,
  show_descriptions: true,
  show_intensity: true,
  show_categories: true,
  animation_enabled: true,
  selection_mode: 'multiple'
};

export const EmotionCardView: React.FC<EmotionCardViewProps> = ({
  id,
  emotions,
  selectedEmotions,
  onEmotionSelect,
  onEmotionDeselect,
  config = {},
  personalization,
  onInteraction,
  language = 'zh',
  style,
  maxSelections
}) => {
  const cardConfig = { ...DEFAULT_CONFIG, ...config };
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [expandedCard, setExpandedCard] = useState<string | null>(null);

  // 处理卡片点击
  const handleCardClick = (emotion: EmotionCardData) => {
    const isSelected = selectedEmotions.includes(emotion.id);
    
    // 检查选择限制
    if (!isSelected && maxSelections && selectedEmotions.length >= maxSelections) {
      onInteraction({
        type: 'click',
        target: emotion.id,
        data: {
          action: 'selection_limit_reached',
          maxSelections,
          currentSelections: selectedEmotions.length
        },
        timestamp: Date.now()
      });
      return;
    }

    // 单选模式处理
    if (cardConfig.selection_mode === 'single' && !isSelected) {
      // 先取消所有选择
      selectedEmotions.forEach(selectedId => {
        onEmotionDeselect?.(selectedId);
      });
    }

    // 触发交互事件
    onInteraction({
      type: isSelected ? 'click' : 'select',
      target: emotion.id,
      data: {
        emotion: emotion,
        action: isSelected ? 'deselect' : 'select',
        selectionMode: cardConfig.selection_mode
      },
      timestamp: Date.now()
    });

    // 调用选择/取消选择回调
    if (isSelected) {
      onEmotionDeselect?.(emotion.id);
    } else {
      onEmotionSelect(emotion.id, emotion);
    }
  };

  // 处理卡片悬停
  const handleCardHover = (emotionId: string | null) => {
    setHoveredCard(emotionId);
    
    if (emotionId) {
      onInteraction({
        type: 'hover',
        target: emotionId,
        data: { action: 'card_hover' },
        timestamp: Date.now()
      });
    }
  };

  // 处理卡片展开
  const handleCardExpand = (emotionId: string) => {
    setExpandedCard(expandedCard === emotionId ? null : emotionId);
    
    onInteraction({
      type: 'click',
      target: emotionId,
      data: { action: 'card_expand' },
      timestamp: Date.now()
    });
  };

  // 渲染强度指示器
  const renderIntensityIndicator = (intensity?: number) => {
    if (!intensity || !cardConfig.show_intensity) return null;

    const dots = Array.from({ length: 5 }, (_, i) => (
      <div
        key={i}
        style={{
          width: '8px',
          height: '8px',
          borderRadius: '50%',
          backgroundColor: i < intensity ? '#FF6B6B' : '#E0E0E0',
          margin: '0 2px'
        }}
      />
    ));

    return (
      <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
        <span style={{ fontSize: '12px', color: '#666', marginRight: '8px' }}>强度:</span>
        <div style={{ display: 'flex' }}>{dots}</div>
      </div>
    );
  };

  // 渲染单个情绪卡片
  const renderEmotionCard = (emotion: EmotionCardData) => {
    const isSelected = selectedEmotions.includes(emotion.id);
    const isHovered = hoveredCard === emotion.id;
    const isExpanded = expandedCard === emotion.id;
    
    const cardStyle: React.CSSProperties = {
      width: cardConfig.card_width,
      height: isExpanded ? 'auto' : cardConfig.card_height,
      minHeight: cardConfig.card_height,
      backgroundColor: isSelected ? '#E8F5E8' : '#FFFFFF',
      border: isSelected ? '2px solid #4CAF50' : '1px solid #E0E0E0',
      borderRadius: '12px',
      padding: '16px',
      cursor: 'pointer',
      position: 'relative',
      boxShadow: isHovered ? '0 4px 12px rgba(0,0,0,0.15)' : '0 2px 4px rgba(0,0,0,0.1)',
      transform: cardConfig.animation_enabled && isHovered ? 'translateY(-2px)' : 'translateY(0)',
      transition: cardConfig.animation_enabled ? 'all 0.2s ease' : 'none',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    };

    const emotionName = emotion.name[language] || emotion.name.zh || emotion.name.en || '未知情绪';
    const emotionDescription = emotion.description?.[language] || emotion.description?.zh || emotion.description?.en;

    return (
      <div
        key={emotion.id}
        style={cardStyle}
        onClick={() => handleCardClick(emotion)}
        onMouseEnter={() => handleCardHover(emotion.id)}
        onMouseLeave={() => handleCardHover(null)}
      >
        {/* 选择指示器 */}
        {isSelected && (
          <div
            style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              width: '20px',
              height: '20px',
              borderRadius: '50%',
              backgroundColor: '#4CAF50',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '12px',
              fontWeight: 'bold'
            }}
          >
            ✓
          </div>
        )}

        {/* 情绪图片或表情符号 */}
        <div style={{ textAlign: 'center', marginBottom: '12px' }}>
          {emotion.image_url ? (
            <img
              src={emotion.image_url}
              alt={emotionName}
              style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                objectFit: 'cover'
              }}
            />
          ) : emotion.emoji ? (
            <div style={{ fontSize: '48px', lineHeight: '60px' }}>
              {emotion.emoji}
            </div>
          ) : (
            <div
              style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                backgroundColor: emotion.color || '#4CAF50',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '24px',
                fontWeight: 'bold',
                margin: '0 auto'
              }}
            >
              {emotionName.charAt(0)}
            </div>
          )}
        </div>

        {/* 情绪名称 */}
        <h3
          style={{
            margin: '0 0 8px 0',
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#333',
            textAlign: 'center'
          }}
        >
          {emotionName}
        </h3>

        {/* 分类标签 */}
        {cardConfig.show_categories && emotion.category && (
          <div
            style={{
              fontSize: '12px',
              color: '#666',
              backgroundColor: '#F5F5F5',
              padding: '4px 8px',
              borderRadius: '12px',
              textAlign: 'center',
              marginBottom: '8px'
            }}
          >
            {emotion.category}
          </div>
        )}

        {/* 强度指示器 */}
        {renderIntensityIndicator(emotion.intensity)}

        {/* 描述文本 */}
        {cardConfig.show_descriptions && emotionDescription && (
          <p
            style={{
              margin: '8px 0 0 0',
              fontSize: '14px',
              color: '#666',
              lineHeight: '1.4',
              textAlign: 'center',
              display: isExpanded ? 'block' : '-webkit-box',
              WebkitLineClamp: isExpanded ? 'none' : 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            {emotionDescription}
          </p>
        )}

        {/* 展开按钮 */}
        {emotionDescription && emotionDescription.length > 50 && (
          <button
            style={{
              marginTop: '8px',
              padding: '4px 8px',
              fontSize: '12px',
              color: '#4CAF50',
              backgroundColor: 'transparent',
              border: '1px solid #4CAF50',
              borderRadius: '4px',
              cursor: 'pointer',
              alignSelf: 'center'
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleCardExpand(emotion.id);
            }}
          >
            {isExpanded ? '收起' : '展开'}
          </button>
        )}

        {/* 标签 */}
        {emotion.tags && emotion.tags.length > 0 && (
          <div style={{ marginTop: '8px', display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
            {emotion.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                style={{
                  fontSize: '10px',
                  color: '#888',
                  backgroundColor: '#F0F0F0',
                  padding: '2px 6px',
                  borderRadius: '8px'
                }}
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    );
  };

  // 计算网格样式
  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${cardConfig.columns}, ${cardConfig.card_width}px)`,
    gap: `${cardConfig.spacing}px`,
    justifyContent: 'center',
    padding: `${cardConfig.spacing}px`
  };

  return (
    <div
      id={id}
      style={{
        width: '100%',
        ...style
      }}
    >
      {/* 选择状态提示 */}
      {maxSelections && (
        <div
          style={{
            textAlign: 'center',
            marginBottom: '16px',
            fontSize: '14px',
            color: '#666'
          }}
        >
          已选择 {selectedEmotions.length} / {maxSelections} 个情绪
        </div>
      )}

      {/* 情绪卡片网格 */}
      <div style={gridStyle}>
        {emotions.map(emotion => renderEmotionCard(emotion))}
      </div>
    </div>
  );
};

export default EmotionCardView;
