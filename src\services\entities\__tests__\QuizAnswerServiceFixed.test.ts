/**
 * QuizAnswerService 测试
 * 验证修复后的Quiz答案服务架构
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuizAnswerService } from '../QuizAnswerService';
import { QuizAnswerRepository } from '../QuizAnswerRepository';
import { QuizAnswer } from '../../../types/schema/base';
import { CreateQuizAnswerInput, UpdateQuizAnswerInput } from '../../../types/schema/api';

// Mock SQLiteDBConnection
const mockDb = {
  query: vi.fn(),
  run: vi.fn(),
  execute: vi.fn(),
} as any;

// Mock Repository
vi.mock('../QuizAnswerRepository');

describe('QuizAnswerService', () => {
  let service: QuizAnswerService;
  let mockRepository: any;

  beforeEach(() => {
    vi.clearAllMocks();

    // Create service instance
    service = new QuizAnswerService(mockDb);

    // Get the mocked repository instance
    mockRepository = (service as any).repository;
  });

  describe('saveAnswer', () => {
    it('should save a quiz answer successfully', async () => {
      const input: CreateQuizAnswerInput = {
        session_id: 'session_123',
        question_id: 'question_456',
        selected_option_ids: ['option_1', 'option_2'],
        answer_value: 'happy',
        answer_text: 'I feel happy today',
        confidence_score: 85,
        response_time_ms: 2500,
      };

      const mockAnswer: QuizAnswer = {
        id: 'answer_123',
        session_id: 'session_123',
        question_id: 'question_456',
        session_presentation_config_id: null,
        selected_option_ids: ['option_1', 'option_2'],
        answer_value: 'happy',
        answer_text: 'I feel happy today',
        confidence_score: 85,
        response_time_ms: 2500,
        answered_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.create.mockResolvedValue(mockAnswer);

      const result = await service.saveAnswer(input);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockAnswer);
      expect(mockRepository.create).toHaveBeenCalledWith(input);
    });

    it('should fail validation when session_id is missing', async () => {
      const input: CreateQuizAnswerInput = {
        session_id: '',
        question_id: 'question_456',
        selected_option_ids: ['option_1'],
        answer_value: 'happy',
      };

      const result = await service.saveAnswer(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Session ID is required');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should fail validation when question_id is missing', async () => {
      const input: CreateQuizAnswerInput = {
        session_id: 'session_123',
        question_id: '',
        selected_option_ids: ['option_1'],
        answer_value: 'happy',
      };

      const result = await service.saveAnswer(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Question ID is required');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should fail validation when no options are selected', async () => {
      const input: CreateQuizAnswerInput = {
        session_id: 'session_123',
        question_id: 'question_456',
        selected_option_ids: [],
        answer_value: 'happy',
      };

      const result = await service.saveAnswer(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('At least one selected option is required');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should fail validation when answer_value is missing', async () => {
      const input: CreateQuizAnswerInput = {
        session_id: 'session_123',
        question_id: 'question_456',
        selected_option_ids: ['option_1'],
        answer_value: '',
      };

      const result = await service.saveAnswer(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Answer value is required');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should fail validation when confidence_score is out of range', async () => {
      const input: CreateQuizAnswerInput = {
        session_id: 'session_123',
        question_id: 'question_456',
        selected_option_ids: ['option_1'],
        answer_value: 'happy',
        confidence_score: 150, // Invalid: > 100
      };

      const result = await service.saveAnswer(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Confidence score must be between 0 and 100');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('batchSaveAnswers', () => {
    it('should batch save multiple answers successfully', async () => {
      const answers: CreateQuizAnswerInput[] = [
        {
          session_id: 'session_123',
          question_id: 'question_1',
          selected_option_ids: ['option_1'],
          answer_value: 'happy',
        },
        {
          session_id: 'session_123',
          question_id: 'question_2',
          selected_option_ids: ['option_2'],
          answer_value: 'excited',
        },
      ];

      const mockAnswers: QuizAnswer[] = answers.map((input, index) => ({
        id: `answer_${index + 1}`,
        ...input,
        session_presentation_config_id: null,
        answer_text: null,
        confidence_score: 50,
        response_time_ms: 0,
        answered_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      }));

      mockRepository.batchInsertAnswers.mockResolvedValue(mockAnswers);

      const result = await service.batchSaveAnswers(answers);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(mockRepository.batchInsertAnswers).toHaveBeenCalledWith(answers);
    });

    it('should fail if any answer in batch is invalid', async () => {
      const answers: CreateQuizAnswerInput[] = [
        {
          session_id: 'session_123',
          question_id: 'question_1',
          selected_option_ids: ['option_1'],
          answer_value: 'happy',
        },
        {
          session_id: '', // Invalid
          question_id: 'question_2',
          selected_option_ids: ['option_2'],
          answer_value: 'excited',
        },
      ];

      const result = await service.batchSaveAnswers(answers);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Session ID is required');
      expect(mockRepository.batchInsertAnswers).not.toHaveBeenCalled();
    });
  });

  describe('getSessionAnswers', () => {
    it('should get session answers successfully', async () => {
      const sessionId = 'session_123';
      const mockAnswers: QuizAnswer[] = [
        {
          id: 'answer_1',
          session_id: sessionId,
          question_id: 'question_1',
          session_presentation_config_id: null,
          selected_option_ids: ['option_1'],
          answer_value: 'happy',
          answer_text: null,
          confidence_score: 85,
          response_time_ms: 2500,
          answered_at: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'answer_2',
          session_id: sessionId,
          question_id: 'question_2',
          session_presentation_config_id: null,
          selected_option_ids: ['option_2'],
          answer_value: 'excited',
          answer_text: null,
          confidence_score: 90,
          response_time_ms: 1800,
          answered_at: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      mockRepository.findBySessionId.mockResolvedValue(mockAnswers);

      const result = await service.getSessionAnswers(sessionId);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data?.[0].answer_value).toBe('happy');
      expect(result.data?.[1].answer_value).toBe('excited');
      expect(mockRepository.findBySessionId).toHaveBeenCalledWith(sessionId);
    });
  });

  describe('getUserAnswerAnalysis', () => {
    it('should calculate user answer analysis correctly', async () => {
      const userId = 'user_123';
      const mockAnswers: QuizAnswer[] = [
        {
          id: 'answer_1',
          session_id: 'session_1',
          question_id: 'question_1',
          session_presentation_config_id: null,
          selected_option_ids: ['option_happy'],
          answer_value: 'happy',
          answer_text: null,
          confidence_score: 85,
          response_time_ms: 2500,
          answered_at: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'answer_2',
          session_id: 'session_2',
          question_id: 'question_1',
          session_presentation_config_id: null,
          selected_option_ids: ['option_happy'],
          answer_value: 'happy',
          answer_text: null,
          confidence_score: 90,
          response_time_ms: 1800,
          answered_at: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: 'answer_3',
          session_id: 'session_3',
          question_id: 'question_2',
          session_presentation_config_id: null,
          selected_option_ids: ['option_sad'],
          answer_value: 'sad',
          answer_text: null,
          confidence_score: 75,
          response_time_ms: 3000,
          answered_at: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      mockRepository.findByUserId.mockResolvedValue(mockAnswers);

      const result = await service.getUserAnswerAnalysis(userId);

      expect(result.success).toBe(true);
      expect(result.data?.total_answers).toBe(3);
      expect(result.data?.average_response_time).toBe(2433); // (2500 + 1800 + 3000) / 3
      expect(result.data?.average_confidence).toBe(83); // (85 + 90 + 75) / 3
      expect(result.data?.most_selected_options).toEqual([
        { option_id: 'option_happy', selection_count: 2, percentage: 67 },
        { option_id: 'option_sad', selection_count: 1, percentage: 33 },
      ]);
      expect(result.data?.response_patterns).toEqual([
        { pattern: 'happy', frequency: 2 },
        { pattern: 'sad', frequency: 1 },
      ]);
    });

    it('should return empty stats for user with no answers', async () => {
      const userId = 'user_no_answers';
      mockRepository.findByUserId.mockResolvedValue([]);

      const result = await service.getUserAnswerAnalysis(userId);

      expect(result.success).toBe(true);
      expect(result.data?.total_answers).toBe(0);
      expect(result.data?.average_response_time).toBe(0);
      expect(result.data?.average_confidence).toBe(0);
      expect(result.data?.most_selected_options).toEqual([]);
      expect(result.data?.response_patterns).toEqual([]);
    });
  });

  describe('updateAnswer', () => {
    it('should update an answer successfully', async () => {
      const answerId = 'answer_123';
      const updates: UpdateQuizAnswerInput = {
        answer_value: 'very_happy',
        confidence_score: 95,
        response_time_ms: 2000,
      };

      const mockUpdatedAnswer: QuizAnswer = {
        id: answerId,
        session_id: 'session_123',
        question_id: 'question_456',
        session_presentation_config_id: null,
        selected_option_ids: ['option_1'],
        answer_value: 'very_happy',
        answer_text: null,
        confidence_score: 95,
        response_time_ms: 2000,
        answered_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.update.mockResolvedValue(mockUpdatedAnswer);

      const result = await service.updateAnswer(answerId, updates);

      expect(result.success).toBe(true);
      expect(result.data?.answer_value).toBe('very_happy');
      expect(result.data?.confidence_score).toBe(95);
      expect(mockRepository.update).toHaveBeenCalledWith(answerId, updates);
    });
  });

  describe('deleteSessionAnswers', () => {
    it('should delete session answers successfully', async () => {
      const sessionId = 'session_123';
      mockRepository.deleteBySessionId.mockResolvedValue(true);

      const result = await service.deleteSessionAnswers(sessionId);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
      expect(mockRepository.deleteBySessionId).toHaveBeenCalledWith(sessionId);
    });
  });

  describe('event emission', () => {
    it('should emit events for answer operations', async () => {
      const answerSavedSpy = vi.fn();
      const answerUpdatedSpy = vi.fn();

      service.on('answerSaved', answerSavedSpy);
      service.on('answerUpdated', answerUpdatedSpy);

      const mockAnswer: QuizAnswer = {
        id: 'answer_123',
        session_id: 'session_123',
        question_id: 'question_456',
        session_presentation_config_id: null,
        selected_option_ids: ['option_1'],
        answer_value: 'happy',
        answer_text: null,
        confidence_score: 85,
        response_time_ms: 2500,
        answered_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.create.mockResolvedValue(mockAnswer);
      mockRepository.update.mockResolvedValue(mockAnswer);

      // Test answer save event
      await service.saveAnswer({
        session_id: 'session_123',
        question_id: 'question_456',
        selected_option_ids: ['option_1'],
        answer_value: 'happy',
      });

      expect(answerSavedSpy).toHaveBeenCalledWith(mockAnswer);

      // Test answer update event
      await service.updateAnswer('answer_123', { answer_value: 'very_happy' });

      expect(answerUpdatedSpy).toHaveBeenCalledWith(mockAnswer);
    });
  });
});
