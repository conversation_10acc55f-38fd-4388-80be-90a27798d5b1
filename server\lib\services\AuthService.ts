/**
 * 认证服务
 * 处理用户认证、JWT令牌管理和权限验证
 */

import { executeQuery, batchStatements } from '../database/index.js';
import * as crypto from 'crypto';

// 导入统一的类型定义
import { type User } from '../../../src/types/schema/base.js';
import {
  type AuthToken,
  type LoginCredentials,
  type RegisterData
} from '../../../src/types/schema/api.js';

// AuthService 专用的用户类型，扩展数据库 User 类型
interface AuthUser extends Omit<User, 'display_name' | 'avatar_url'> {
  displayName?: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
}

// 数据库用户行转换为 AuthUser 的适配器函数
function dbUserToAuthUser(dbUser: User): AuthUser {
  return {
    ...dbUser,
    displayName: dbUser.display_name,
    avatar: dbUser.avatar_url,
    roles: ['user'], // 简化角色管理
    permissions: ['read_own_data', 'write_own_data'] // 简化权限管理
  };
}

export class AuthService {
  private static instance: AuthService;

  private constructor() {}

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<{ success: boolean; user?: User; token?: AuthToken; error?: string }> {
    try {
      const { email, password } = credentials;

      // 查找用户和认证信息
      const userResult = await executeQuery({
        sql: `
          SELECT u.*, ua.password_hash, ua.failed_login_attempts, ua.locked_until
          FROM users u
          LEFT JOIN user_auth ua ON u.id = ua.user_id
          WHERE u.email = ? AND u.is_active = 1
        `,
        args: [email]
      });

      if (!userResult.rows || userResult.rows.length === 0) {
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      const userRow = userResult.rows[0];

      // 检查账户是否被锁定
      if (userRow.locked_until && new Date(userRow.locked_until) > new Date()) {
        return {
          success: false,
          error: 'Account is temporarily locked. Please try again later.'
        };
      }

      // 验证密码
      const isValidPassword = await this.verifyPassword(password, userRow.password_hash);
      if (!isValidPassword) {
        // 增加失败登录次数
        await this.incrementFailedLoginAttempts(userRow.id);
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      // 重置失败登录次数并更新最后登录时间
      await executeQuery({
        sql: 'UPDATE users SET last_login_at = ?, login_count = login_count + 1, last_active_at = ? WHERE id = ?',
        args: [new Date().toISOString(), new Date().toISOString(), userRow.id]
      });

      await executeQuery({
        sql: 'UPDATE user_auth SET failed_login_attempts = 0, locked_until = NULL WHERE user_id = ?',
        args: [userRow.id]
      });

      // 获取用户角色和权限
      const user = await this.buildUserObject(userRow);

      // 生成JWT令牌
      const token = await this.generateToken(user);

      return {
        success: true,
        user,
        token
      };
    } catch (error) {
      console.error('[AuthService] Login error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed'
      };
    }
  }

  /**
   * 用户注册
   */
  async register(data: RegisterData): Promise<{ success: boolean; user?: User; token?: AuthToken; error?: string }> {
    try {
      const { email, password, username, displayName } = data;

      // 检查邮箱是否已存在
      const existingUser = await executeQuery({
        sql: 'SELECT id FROM users WHERE email = ?',
        args: [email]
      });

      if (existingUser.rows && existingUser.rows.length > 0) {
        return {
          success: false,
          error: 'Email already exists'
        };
      }

      // 生成用户ID和密码哈希
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      const passwordHash = await this.hashPassword(password);
      const now = new Date().toISOString();

      // 使用事务创建用户和认证信息
      await batchStatements([
        {
          sql: `
            INSERT INTO users (
              id, email, username, display_name,
              is_vip, is_verified, is_active,
              created_at, updated_at, sync_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `,
          args: [
            userId,
            email,
            username || null,
            displayName || null,
            false, // is_vip
            false, // is_verified
            true,  // is_active
            now,
            now,
            'synced' // sync_status
          ]
        },
        {
          sql: `
            INSERT INTO user_auth (
              user_id, password_hash, failed_login_attempts,
              created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?)
          `,
          args: [
            userId,
            passwordHash,
            0, // failed_login_attempts
            now,
            now
          ]
        }
      ]);

      // 获取创建的用户
      const userResult = await executeQuery({
        sql: 'SELECT * FROM users WHERE id = ?',
        args: [userId]
      });

      const user = await this.buildUserObject(userResult.rows[0]);
      const token = await this.generateToken(user);

      return {
        success: true,
        user,
        token
      };
    } catch (error) {
      console.error('[AuthService] Register error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed'
      };
    }
  }

  /**
   * 验证JWT令牌
   */
  async verifyToken(token: string): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      // 这里应该使用真正的JWT库来验证令牌
      // 为了简化，我们使用基本的令牌验证
      const decoded = this.decodeToken(token);

      if (!decoded || !decoded.user_id) {
        return {
          success: false,
          error: 'Invalid token'
        };
      }

      // 检查令牌是否过期
      if (decoded.expiresAt < Date.now()) {
        return {
          success: false,
          error: 'Token expired'
        };
      }

      // 获取用户信息
      const userResult = await executeQuery({
        sql: 'SELECT * FROM users WHERE id = ? AND is_active = 1',
        args: [decoded.user_id]
      });

      if (!userResult.rows || userResult.rows.length === 0) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const user = await this.buildUserObject(userResult.rows[0]);

      return {
        success: true,
        user
      };
    } catch (error) {
      console.error('[AuthService] Token verification error:', error);
      return {
        success: false,
        error: 'Token verification failed'
      };
    }
  }

  /**
   * 检查用户权限
   */
  async hasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const userResult = await executeQuery({
        sql: 'SELECT permissions FROM users WHERE id = ? AND is_active = 1',
        args: [userId]
      });

      if (!userResult.rows || userResult.rows.length === 0) {
        return false;
      }

      const permissions = JSON.parse(userResult.rows[0].permissions || '[]');
      return permissions.includes(permission) || permissions.includes('admin');
    } catch (error) {
      console.error('[AuthService] Permission check error:', error);
      return false;
    }
  }

  /**
   * 更新VIP状态
   */
  async updateVipStatus(userId: string, isVip: boolean, expiresAt?: string): Promise<{ success: boolean; error?: string }> {
    try {
      await executeQuery({
        sql: 'UPDATE users SET is_vip = ?, vip_expires_at = ?, updated_at = ? WHERE id = ?',
        args: [isVip, expiresAt || null, new Date().toISOString(), userId]
      });

      return { success: true };
    } catch (error) {
      console.error('[AuthService] VIP status update error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'VIP status update failed'
      };
    }
  }

  /**
   * 构建用户对象
   */
  private async buildUserObject(userRow: any): Promise<AuthUser> {
    // 首先构建数据库 User 对象
    const dbUser: User = {
      id: userRow.id,
      email: userRow.email,
      username: userRow.username,
      display_name: userRow.display_name,
      avatar_url: userRow.avatar_url,
      is_vip: Boolean(userRow.is_vip),
      vip_expires_at: userRow.vip_expires_at,
      is_verified: Boolean(userRow.is_verified),
      is_active: Boolean(userRow.is_active),
      last_login_at: userRow.last_login_at,
      login_count: userRow.login_count || 0,
      last_active_at: userRow.last_active_at,
      created_at: userRow.created_at,
      updated_at: userRow.updated_at,
      sync_status: userRow.sync_status || 'pending',
      server_updated_at: userRow.server_updated_at || userRow.updated_at,
      // 添加缺失的必需字段
      is_banned: Boolean(userRow.is_banned),
      banned_until: userRow.banned_until,
      ban_reason: userRow.ban_reason,
      vip_tier: userRow.vip_tier,
      vip_auto_renew: Boolean(userRow.vip_auto_renew),
      registration_ip: userRow.registration_ip,
      registration_source: userRow.registration_source,
      timezone: userRow.timezone || 'UTC',
      locale: userRow.locale || 'en'
    };

    // 然后转换为 AuthUser
    return dbUserToAuthUser(dbUser);
  }

  /**
   * 生成JWT令牌
   */
  private async generateToken(user: AuthUser): Promise<AuthToken> {
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时

    // 简化的令牌生成（生产环境应使用真正的JWT库）
    const payload = {
      userId: user.id,
      email: user.email,
      roles: user.roles,
      expiresAt: expiresAt.getTime()
    };

    const token = Buffer.from(JSON.stringify(payload)).toString('base64');

    return {
      accessToken: token,
      expiresAt,
      tokenType: 'Bearer',
      scope: user.permissions
    };
  }

  /**
   * 解码令牌
   */
  private decodeToken(token: string): any {
    try {
      const payload = Buffer.from(token, 'base64').toString('utf-8');
      return JSON.parse(payload);
    } catch {
      return null;
    }
  }

  /**
   * 哈希密码
   */
  private async hashPassword(password: string): Promise<string> {
    // 简化的密码哈希（生产环境应使用bcrypt等库）
    return crypto.createHash('sha256').update(password + 'salt').digest('hex');
  }

  /**
   * 验证密码
   */
  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    const computedHash = await this.hashPassword(password);
    return computedHash === hash;
  }

  /**
   * 增加失败登录次数
   */
  private async incrementFailedLoginAttempts(userId: string): Promise<void> {
    try {
      const result = await executeQuery({
        sql: 'SELECT failed_login_attempts FROM user_auth WHERE user_id = ?',
        args: [userId]
      });

      const currentAttempts = result.rows[0]?.failed_login_attempts || 0;
      const newAttempts = currentAttempts + 1;

      // 如果失败次数达到5次，锁定账户1小时
      const lockUntil = newAttempts >= 5
        ? new Date(Date.now() + 60 * 60 * 1000).toISOString() // 1小时后
        : null;

      await executeQuery({
        sql: 'UPDATE user_auth SET failed_login_attempts = ?, locked_until = ? WHERE user_id = ?',
        args: [newAttempts, lockUntil, userId]
      });
    } catch (error) {
      console.error('[AuthService] Failed to increment login attempts:', error);
    }
  }
}
