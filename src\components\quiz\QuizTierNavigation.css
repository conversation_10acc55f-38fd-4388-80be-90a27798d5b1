/**
 * Quiz层级导航组件样式
 * 基于新的组件设计系统
 */

.quiz-tier-navigation {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  box-sizing: border-box;
}

.tier-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: white;
}

.tier-label {
  font-size: 0.9rem;
  font-weight: 600;
  opacity: 0.8;
}

.question-title {
  font-size: 1.1rem;
  font-weight: 700;
  text-align: center;
  flex: 1;
  margin: 0 1rem;
}

.question-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Quiz选择器组件样式 */
.quiz-selector {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.quiz-selector .question-text {
  color: white;
  text-align: center;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.options-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  padding: 1rem;
}

.options-container.list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  padding: 1rem;
}

.options-container.horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  padding: 1rem;
}

.option-item {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.2rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-height: 80px;
  justify-content: center;
}

.option-item:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.option-item:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.option-emoji {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.option-text {
  font-size: 0.9rem;
  text-align: center;
  line-height: 1.3;
}

/* Quiz评分组件样式 */
.quiz-rating {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.quiz-rating .question-text {
  color: white;
  text-align: center;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.rating-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 2rem 1rem;
}

.rating-item {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.rating-item:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Quiz滑块组件样式 */
.quiz-slider {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.quiz-slider .question-text {
  color: white;
  text-align: center;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.slider-container {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  margin-bottom: 2rem;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  color: white;
  font-weight: 600;
  margin-bottom: 1rem;
}

.slider-input {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
  margin-bottom: 1rem;
}

.slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider-input::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider-value {
  text-align: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
}

.slider-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* 通用按钮样式 */
.back-button,
.confirm-button,
.submit-button {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.8rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.back-button:hover,
.confirm-button:hover,
.submit-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.confirm-button,
.submit-button {
  background: rgba(76, 175, 80, 0.8);
  border-color: rgba(76, 175, 80, 1);
}

.confirm-button:hover,
.submit-button:hover {
  background: rgba(76, 175, 80, 0.9);
  border-color: rgba(76, 175, 80, 1);
}

/* 默认问题样式 */
.quiz-question-default {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.quiz-question-default .question-text {
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.option-button {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.2rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 80px;
}

.option-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quiz-tier-navigation {
    padding: 0.5rem;
  }
  
  .tier-progress {
    padding: 0.8rem;
    margin-bottom: 0.8rem;
  }
  
  .question-title {
    font-size: 1rem;
  }
  
  .options-container.grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.8rem;
    padding: 0.8rem;
  }
  
  .option-item {
    padding: 1rem;
    min-height: 70px;
  }
  
  .option-emoji {
    font-size: 1.5rem;
  }
  
  .option-text {
    font-size: 0.8rem;
  }
}
