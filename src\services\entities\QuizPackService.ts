/**
 * Quiz包服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 */

import { BaseService } from '../base/BaseService';
import { QuizPackRepository } from './QuizPackRepository';
import { QuizPack } from '../../types/schema/base';
import { CreateQuizPackInput, UpdateQuizPackInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export interface QuizPackStats {
  total_sessions: number;
  completed_sessions: number;
  total_questions: number;
  avg_completion_time_minutes: number;
  completion_rate: number;
  popularity_score: number;
}

export interface QuizPackFilter {
  quiz_type?: string;
  category?: string;
  difficulty_level?: number;
  tags?: string[];
  search_term?: string;
  is_active?: boolean;
}

export class QuizPackService extends BaseService<
  QuizPack,
  CreateQuizPackInput,
  UpdateQuizPackInput
> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new QuizPackRepository(db);
    super(repository);
  }

  /**
   * 创建新的Quiz包
   */
  async createQuizPack(input: CreateQuizPackInput): Promise<ServiceResult<QuizPack>> {
    try {
      // 验证输入
      await this.validateCreate(input);

      // 调用Repository创建Quiz包
      const quizPack = await this.repository.create(input);

      // 发射业务事件
      this.emit('quizPackCreated', quizPack);

      return this.createSuccessResult(quizPack);
    } catch (error) {
      return this.createErrorResult('Failed to create quiz pack', error);
    }
  }

  /**
   * 获取活跃的Quiz包
   */
  async getActiveQuizPacks(): Promise<ServiceResult<QuizPack[]>> {
    try {
      const quizPacks = await (this.repository as QuizPackRepository).findActiveQuizPacks();
      return this.createSuccessResult(quizPacks);
    } catch (error) {
      return this.createErrorResult('Failed to get active quiz packs', error);
    }
  }

  /**
   * 根据类型获取Quiz包
   */
  async getQuizPacksByType(quizType: string): Promise<ServiceResult<QuizPack[]>> {
    try {
      const quizPacks = await (this.repository as QuizPackRepository).findByQuizType(quizType);
      return this.createSuccessResult(quizPacks);
    } catch (error) {
      return this.createErrorResult('Failed to get quiz packs by type', error);
    }
  }

  /**
   * 根据分类获取Quiz包
   */
  async getQuizPacksByCategory(category: string): Promise<ServiceResult<QuizPack[]>> {
    try {
      const quizPacks = await (this.repository as QuizPackRepository).findByCategory(category);
      return this.createSuccessResult(quizPacks);
    } catch (error) {
      return this.createErrorResult('Failed to get quiz packs by category', error);
    }
  }

  /**
   * 搜索Quiz包
   */
  async searchQuizPacks(searchTerm: string): Promise<ServiceResult<QuizPack[]>> {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new Error('Search term must be at least 2 characters long');
      }

      const quizPacks = await (this.repository as QuizPackRepository).searchQuizPacks(searchTerm.trim());
      return this.createSuccessResult(quizPacks);
    } catch (error) {
      return this.createErrorResult('Failed to search quiz packs', error);
    }
  }

  /**
   * 根据难度级别获取Quiz包
   */
  async getQuizPacksByDifficulty(difficultyLevel: number): Promise<ServiceResult<QuizPack[]>> {
    try {
      if (difficultyLevel < 1 || difficultyLevel > 5) {
        throw new Error('Difficulty level must be between 1 and 5');
      }

      const quizPacks = await (this.repository as QuizPackRepository).findByDifficultyLevel(difficultyLevel);
      return this.createSuccessResult(quizPacks);
    } catch (error) {
      return this.createErrorResult('Failed to get quiz packs by difficulty', error);
    }
  }

  /**
   * 根据标签获取Quiz包
   */
  async getQuizPacksByTag(tag: string): Promise<ServiceResult<QuizPack[]>> {
    try {
      if (!tag || tag.trim().length === 0) {
        throw new Error('Tag cannot be empty');
      }

      const quizPacks = await (this.repository as QuizPackRepository).findByTag(tag.trim());
      return this.createSuccessResult(quizPacks);
    } catch (error) {
      return this.createErrorResult('Failed to get quiz packs by tag', error);
    }
  }

  /**
   * 获取Quiz包的详细统计信息
   */
  async getQuizPackStats(packId: string): Promise<ServiceResult<QuizPackStats>> {
    try {
      const rawStats = await (this.repository as QuizPackRepository).getQuizPackStats(packId);

      // 计算完成率
      const completionRate = rawStats.total_sessions > 0
        ? Math.round((rawStats.completed_sessions / rawStats.total_sessions) * 100)
        : 0;

      // 计算受欢迎程度分数（基于会话数量和完成率）
      const popularityScore = Math.round(
        (rawStats.total_sessions * 0.7) + (completionRate * 0.3)
      );

      const stats: QuizPackStats = {
        total_sessions: rawStats.total_sessions || 0,
        completed_sessions: rawStats.completed_sessions || 0,
        total_questions: rawStats.total_questions || 0,
        avg_completion_time_minutes: Math.round(rawStats.avg_completion_time_minutes || 0),
        completion_rate: completionRate,
        popularity_score: popularityScore
      };

      return this.createSuccessResult(stats);
    } catch (error) {
      return this.createErrorResult('Failed to get quiz pack stats', error);
    }
  }

  /**
   * 获取推荐的Quiz包
   */
  async getRecommendedQuizPacks(limit: number = 10): Promise<ServiceResult<QuizPack[]>> {
    try {
      // 获取所有活跃的Quiz包
      const allPacks = await (this.repository as QuizPackRepository).findActiveQuizPacks();

      // 为每个包计算推荐分数
      const packsWithScores = await Promise.all(
        allPacks.map(async (pack) => {
          const statsResult = await this.getQuizPackStats(pack.id);
          const stats = statsResult.success ? statsResult.data! : {
            total_sessions: 0,
            completed_sessions: 0,
            total_questions: 0,
            avg_completion_time_minutes: 0,
            completion_rate: 0,
            popularity_score: 0
          };

          // 推荐分数算法：受欢迎程度 + 完成率 + 新鲜度
          const daysSinceCreated = Math.floor(
            (Date.now() - new Date(pack.created_at).getTime()) / (1000 * 60 * 60 * 24)
          );
          const freshnessScore = Math.max(0, 30 - daysSinceCreated); // 新包有额外分数

          const recommendationScore =
            stats.popularity_score * 0.5 +
            stats.completion_rate * 0.3 +
            freshnessScore * 0.2;

          return {
            pack,
            score: recommendationScore
          };
        })
      );

      // 按分数排序并返回前N个
      const recommendedPacks = packsWithScores
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
        .map(item => item.pack);

      return this.createSuccessResult(recommendedPacks);
    } catch (error) {
      return this.createErrorResult('Failed to get recommended quiz packs', error);
    }
  }

  /**
   * 更新Quiz包
   */
  async updateQuizPack(packId: string, updates: UpdateQuizPackInput): Promise<ServiceResult<QuizPack>> {
    try {
      // 验证更新数据
      await this.validateUpdate(updates);

      // 调用Repository更新
      const quizPack = await this.repository.update(packId, updates);

      // 发射业务事件
      this.emit('quizPackUpdated', quizPack);

      return this.createSuccessResult(quizPack);
    } catch (error) {
      return this.createErrorResult('Failed to update quiz pack', error);
    }
  }

  /**
   * 激活Quiz包
   */
  async activateQuizPack(packId: string): Promise<ServiceResult<QuizPack>> {
    try {
      const quizPack = await this.repository.update(packId, { is_active: true });

      this.emit('quizPackActivated', quizPack);
      return this.createSuccessResult(quizPack);
    } catch (error) {
      return this.createErrorResult('Failed to activate quiz pack', error);
    }
  }

  /**
   * 停用Quiz包
   */
  async deactivateQuizPack(packId: string): Promise<ServiceResult<QuizPack>> {
    try {
      const quizPack = await this.repository.update(packId, { is_active: false });

      this.emit('quizPackDeactivated', quizPack);
      return this.createSuccessResult(quizPack);
    } catch (error) {
      return this.createErrorResult('Failed to deactivate quiz pack', error);
    }
  }

  /**
   * 获取Quiz包的分类列表
   */
  async getQuizPackCategories(): Promise<ServiceResult<string[]>> {
    try {
      const allPacks = await (this.repository as QuizPackRepository).findActiveQuizPacks();
      const categories = [...new Set(allPacks.map(pack => pack.category).filter(Boolean))];
      return this.createSuccessResult(categories);
    } catch (error) {
      return this.createErrorResult('Failed to get quiz pack categories', error);
    }
  }

  /**
   * 获取Quiz包的类型列表
   */
  async getQuizPackTypes(): Promise<ServiceResult<string[]>> {
    try {
      const allPacks = await (this.repository as QuizPackRepository).findActiveQuizPacks();
      const types = [...new Set(allPacks.map(pack => pack.quiz_type))];
      return this.createSuccessResult(types);
    } catch (error) {
      return this.createErrorResult('Failed to get quiz pack types', error);
    }
  }

  // 实现抽象方法
  protected async validateCreate(data: CreateQuizPackInput): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Quiz pack name is required');
    }
    if (data.name.length > 100) {
      throw new Error('Quiz pack name must be less than 100 characters');
    }
    if (!data.quiz_type || data.quiz_type.trim().length === 0) {
      throw new Error('Quiz type is required');
    }
    if (data.difficulty_level !== undefined && (data.difficulty_level < 1 || data.difficulty_level > 5)) {
      throw new Error('Difficulty level must be between 1 and 5');
    }
    if (data.estimated_duration_minutes !== undefined && data.estimated_duration_minutes < 1) {
      throw new Error('Estimated duration must be at least 1 minute');
    }
  }

  protected async validateUpdate(data: UpdateQuizPackInput): Promise<void> {
    if (data.name !== undefined && (!data.name || data.name.trim().length === 0)) {
      throw new Error('Quiz pack name cannot be empty');
    }
    if (data.name !== undefined && data.name.length > 100) {
      throw new Error('Quiz pack name must be less than 100 characters');
    }
    if (data.quiz_type !== undefined && (!data.quiz_type || data.quiz_type.trim().length === 0)) {
      throw new Error('Quiz type cannot be empty');
    }
    if (data.difficulty_level !== undefined && (data.difficulty_level < 1 || data.difficulty_level > 5)) {
      throw new Error('Difficulty level must be between 1 and 5');
    }
    if (data.estimated_duration_minutes !== undefined && data.estimated_duration_minutes < 1) {
      throw new Error('Estimated duration must be at least 1 minute');
    }
  }
}
