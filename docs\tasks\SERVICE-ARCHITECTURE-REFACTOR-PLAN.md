# 服务架构重构计划

## 🚨 当前问题分析

### 1. **泛型类型参数问题**
```typescript
// ❌ 错误：缺少类型参数
export class QuizSessionRepository extends BaseRepository<QuizSession> {
  // BaseRepository<T, TCreate, TUpdate> 需要3个类型参数
}

export class QuizSessionService extends BaseService<QuizSession> {
  // BaseService<T, TCreate, TUpdate> 需要3个类型参数
}
```

### 2. **架构不一致问题**
- Repository直接返回`ServiceResult`（应该只返回数据）
- Repository包含业务逻辑（应该只有数据操作）
- Service没有正确使用Repository模式
- 大量内嵌接口定义

### 3. **类型定义分散问题**
- 应该使用`src/types/schema/base.ts`中的统一类型
- 不应该在服务文件中定义接口
- 缺少Create/Update类型定义

## 🎯 解决方案

### 第一步：创建统一的Create/Update类型

在`src/types/schema/api.ts`中添加：

```typescript
// Quiz Session 相关类型
export const CreateQuizSessionInputSchema = z.object({
  pack_id: IdSchema,
  user_id: IdSchema,
  session_type: z.string().optional(),
  session_metadata: z.record(z.any()).optional(),
});

export const UpdateQuizSessionInputSchema = z.object({
  status: z.string().optional(),
  current_question_index: z.number().int().optional(),
  total_questions: z.number().int().optional(),
  answered_questions: z.number().int().optional(),
  completion_percentage: z.number().optional(),
  end_time: TimestampSchema.optional(),
  session_metadata: z.record(z.any()).optional(),
});

export type CreateQuizSessionInput = z.infer<typeof CreateQuizSessionInputSchema>;
export type UpdateQuizSessionInput = z.infer<typeof UpdateQuizSessionInputSchema>;
```

### 第二步：修复BaseRepository

```typescript
// 简化的BaseRepository - 只处理数据访问
export abstract class BaseRepository<T, TCreate, TUpdate> {
  constructor(protected tableName: string, protected db: SQLiteDBConnection) {}

  // 纯数据操作，不返回ServiceResult
  async create(data: TCreate): Promise<T> {
    // 实现创建逻辑
  }

  async findById(id: string): Promise<T | null> {
    // 实现查找逻辑
  }

  async update(id: string, data: TUpdate): Promise<T> {
    // 实现更新逻辑
  }

  async delete(id: string): Promise<boolean> {
    // 实现删除逻辑
  }

  async findMany(filters?: any): Promise<T[]> {
    // 实现批量查找逻辑
  }

  // 抽象方法
  protected abstract mapRowToEntity(row: any): T;
  protected abstract mapEntityToRow(entity: Partial<T>): Record<string, any>;
  protected abstract extractIdFromCreateData(data: TCreate): string;
}
```

### 第三步：修复BaseService

```typescript
// BaseService - 处理业务逻辑和错误处理
export abstract class BaseService<T, TCreate, TUpdate> {
  constructor(protected repository: BaseRepository<T, TCreate, TUpdate>) {}

  async create(data: TCreate): Promise<ServiceResult<T>> {
    try {
      // 1. 验证
      await this.validateCreate(data);
      
      // 2. 调用Repository
      const result = await this.repository.create(data);
      
      // 3. 业务逻辑（事件发射等）
      this.emitEvent('created', result);
      
      return this.createSuccessResult(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // 其他方法...
  
  protected abstract validateCreate(data: TCreate): Promise<void>;
  protected abstract validateUpdate(data: TUpdate): Promise<void>;
}
```

### 第四步：正确的Repository实现

```typescript
// QuizSessionRepository - 纯数据访问
export class QuizSessionRepository extends BaseRepository<
  QuizSession,
  CreateQuizSessionInput,
  UpdateQuizSessionInput
> {
  constructor(db: SQLiteDBConnection) {
    super('quiz_sessions', db);
  }

  // 只有数据查询方法，不包含业务逻辑
  async findByUserId(userId: string, limit: number = 20): Promise<QuizSession[]> {
    const query = `
      SELECT * FROM ${this.tableName} 
      WHERE user_id = ? 
      ORDER BY last_active_time DESC 
      LIMIT ?
    `;
    const rows = await this.db.all(query, [userId, limit]);
    return rows.map(row => this.mapRowToEntity(row));
  }

  async findActiveByUserId(userId: string): Promise<QuizSession[]> {
    const query = `
      SELECT * FROM ${this.tableName} 
      WHERE user_id = ? AND status IN ('INITIATED', 'IN_PROGRESS') 
      ORDER BY last_active_time DESC
    `;
    const rows = await this.db.all(query, [userId]);
    return rows.map(row => this.mapRowToEntity(row));
  }

  protected mapRowToEntity(row: any): QuizSession {
    // 数据映射逻辑
  }

  protected mapEntityToRow(entity: Partial<QuizSession>): Record<string, any> {
    // 实体到行的映射
  }

  protected extractIdFromCreateData(data: CreateQuizSessionInput): string {
    // 从创建数据中提取ID
  }
}
```

### 第五步：正确的Service实现

```typescript
// QuizSessionService - 业务逻辑层
export class QuizSessionService extends BaseService<
  QuizSession,
  CreateQuizSessionInput,
  UpdateQuizSessionInput
> {
  constructor(repository: QuizSessionRepository) {
    super(repository);
  }

  // 业务方法 - 包含业务逻辑
  async createSession(input: CreateQuizSessionInput): Promise<ServiceResult<QuizSession>> {
    try {
      // 1. 业务验证
      await this.validateCreate(input);

      // 2. 业务逻辑：生成ID、设置初始状态
      const sessionData = {
        ...input,
        id: this.generateSessionId(),
        status: 'INITIATED',
        current_question_index: 0,
        completion_percentage: 0,
        start_time: new Date(),
        last_active_time: new Date(),
      };

      // 3. 调用Repository
      const session = await this.repository.create(sessionData);

      // 4. 业务事件
      this.emitEvent('sessionCreated', session);

      return this.createSuccessResult(session);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getUserSessions(userId: string, limit?: number): Promise<ServiceResult<QuizSession[]>> {
    try {
      const sessions = await this.repository.findByUserId(userId, limit);
      return this.createSuccessResult(sessions);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getUserQuizStats(userId: string): Promise<ServiceResult<QuizSessionStats>> {
    try {
      // 1. 获取数据
      const sessions = await this.repository.findByUserId(userId, 1000);
      
      // 2. 业务计算
      const stats = this.calculateStats(sessions);
      
      return this.createSuccessResult(stats);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // 业务逻辑方法
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateStats(sessions: QuizSession[]): QuizSessionStats {
    // 复杂的统计计算逻辑
  }

  protected async validateCreate(data: CreateQuizSessionInput): Promise<void> {
    if (!data.pack_id) throw new Error('Pack ID is required');
    if (!data.user_id) throw new Error('User ID is required');
  }

  protected async validateUpdate(data: UpdateQuizSessionInput): Promise<void> {
    // 更新验证逻辑
  }
}
```

## 📋 重构步骤

### 1. 创建统一类型定义
- [ ] 在`src/types/schema/api.ts`中添加所有Create/Update类型
- [ ] 导出所有需要的类型

### 2. 修复BaseRepository
- [ ] 简化为纯数据访问层
- [ ] 移除ServiceResult返回类型
- [ ] 移除业务逻辑

### 3. 修复BaseService
- [ ] 确保正确使用Repository
- [ ] 添加业务逻辑处理
- [ ] 统一错误处理

### 4. 重构所有Repository
- [ ] 使用正确的泛型类型参数
- [ ] 移除业务逻辑到Service
- [ ] 只保留数据访问方法

### 5. 重构所有Service
- [ ] 使用正确的泛型类型参数
- [ ] 通过Repository访问数据
- [ ] 实现业务逻辑

### 6. 更新导入
- [ ] 使用统一的类型定义
- [ ] 移除内嵌接口定义

## 🎯 预期结果

### 清晰的架构分层
```
UI Components
    ↓
Service Layer (Business Logic)
    ↓
Repository Layer (Data Access)
    ↓
Database Layer
```

### 统一的类型系统
- 所有类型定义在`src/types`中
- 使用Zod进行运行时验证
- 类型安全的泛型实现

### 可维护的代码结构
- 关注点分离
- 易于测试
- 易于扩展
