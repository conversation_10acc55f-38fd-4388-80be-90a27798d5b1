# Quiz设置系统测试指南

本文档提供了测试新增强的Quiz设置系统的详细指南，验证所有新功能是否正常工作。

## 🚀 快速测试步骤

### 1. 访问Quiz设置页面
```bash
# 确保开发服务器运行
npm run dev

# 在浏览器中访问
http://localhost:4080/quiz-settings
```

### 2. 验证VIP访问权限
- 确保在Settings页面中VIP状态为启用
- 如果不是VIP，在Settings页面点击"开启VIP"按钮
- 确认可以看到"进入Quiz系统设置"按钮

## 📋 功能测试清单

### Layer 0: 数据集展现配置

#### 基础配置测试
- [ ] 默认难度偏好下拉选择正常工作
- [ ] 会话长度偏好选择正常工作
- [ ] 自动选择推荐开关正常工作

#### 新增功能测试

##### 进度恢复配置
- [ ] "恢复进度"开关显示正确
- [ ] 开关状态可以正常切换
- [ ] 说明文字清晰易懂

##### 问题展示字段配置
- [ ] 显示7个字段配置选项：
  - [ ] 问题文本
  - [ ] 问题描述
  - [ ] 问题序号
  - [ ] 进度指示器
  - [ ] 答案选项
  - [ ] 选项描述
  - [ ] 选项图标
- [ ] 每个开关都有详细说明
- [ ] 开关状态可以独立控制

##### 交互行为配置
- [ ] "选择后自动跳转"开关正常工作
- [ ] 启用自动跳转后显示延迟设置滑块
- [ ] 延迟滑块范围500-5000ms，步长250ms
- [ ] 滑块实时显示当前数值
- [ ] "允许修改答案"开关正常工作
- [ ] "显示确认对话框"开关正常工作

### Layer 1: 用户选择配置

#### 视图类型选择测试
- [ ] 首选视图类型下拉选择正常
- [ ] 支持的视图类型：wheel, card, bubble, galaxy
- [ ] 选择不同视图类型后，Layer 4内容应该相应变化

#### 其他配置测试
- [ ] 皮肤选择正常工作
- [ ] 深色模式开关正常
- [ ] 颜色模式选择正常
- [ ] 用户级别选择正常

### Layer 2: 渲染策略配置
- [ ] 渲染引擎偏好配置正常
- [ ] 内容显示模式配置正常
- [ ] 布局偏好配置正常
- [ ] 性能模式选择正常

### Layer 3: 皮肤基础配置

#### 皮肤选择功能测试
- [ ] 显示4个预设皮肤选项：
  - [ ] 默认皮肤 (basic)
  - [ ] 中医经典 (tcm)
  - [ ] 现代简约 (modern)
  - [ ] 自然禅意 (nature)
- [ ] 皮肤卡片显示正确信息（名称、描述、类别）
- [ ] 点击皮肤卡片可以选择
- [ ] 当前选中的皮肤有视觉高亮
- [ ] 选中状态显示"✓ 当前选中"

#### 皮肤详细配置测试
- [ ] 只有选择了皮肤后才显示详细配置
- [ ] 字体配置正常（字体选择、大小缩放）
- [ ] 颜色配置正常（主色、次色、强调色）
- [ ] 动画配置正常（启用动画、速度、减少动画）

### Layer 4: 视图细节配置

#### 动态内容测试
这是最重要的测试部分，需要验证内容根据Layer 1的视图类型选择动态变化：

##### 轮盘视图配置 (preferred_view_type = 'wheel')
- [ ] 在Layer 1选择"wheel"视图类型
- [ ] Layer 4应该显示"轮盘视图配置"卡片
- [ ] 配置项包括：
  - [ ] 容器大小滑块 (200-800px)
  - [ ] 轮盘半径滑块 (80-400px)
  - [ ] 层级间距滑块 (20-100px)
  - [ ] 中心半径滑块 (20-80px)
  - [ ] 情绪显示模式下拉选择
  - [ ] 显示标签开关
  - [ ] 显示表情符号开关

##### 卡片视图配置 (preferred_view_type = 'card')
- [ ] 在Layer 1选择"card"视图类型
- [ ] Layer 4应该显示"卡片视图配置"卡片
- [ ] 配置项包括：
  - [ ] 网格列数滑块 (1-6列)
  - [ ] 卡片大小选择 (小/中/大)
  - [ ] 卡片间距滑块 (4-40px)
  - [ ] 显示描述开关
  - [ ] 悬停效果开关

##### 气泡视图配置 (preferred_view_type = 'bubble')
- [ ] 在Layer 1选择"bubble"视图类型
- [ ] Layer 4应该显示"气泡视图配置"卡片
- [ ] 配置项包括：
  - [ ] 容器宽度滑块 (300-800px)
  - [ ] 容器高度滑块 (200-600px)
  - [ ] 气泡大小范围双滑块
  - [ ] 物理引擎开关
  - [ ] 碰撞检测开关

##### 星系视图配置 (preferred_view_type = 'galaxy')
- [ ] 在Layer 1选择"galaxy"视图类型
- [ ] Layer 4应该显示"星系视图配置"卡片
- [ ] 配置项包括：
  - [ ] 列表项高度滑块 (40-100px)
  - [ ] 显示图标开关
  - [ ] 显示描述开关
  - [ ] 启用分组开关

##### 通用情绪展示配置
- [ ] 无论选择哪种视图类型，都显示"情绪展示配置"卡片
- [ ] 情绪分组样式选择正常
- [ ] 层级过渡动画选择正常

### Layer 5: 可访问性配置
- [ ] 高对比度开关正常
- [ ] 大字体开关正常
- [ ] 减少动画开关正常
- [ ] 键盘导航开关正常
- [ ] 语音引导开关正常

## 🔄 交互测试

### 配置联动测试
1. **Layer 1 → Layer 4 联动**
   - [ ] 在Layer 1切换视图类型
   - [ ] Layer 4内容立即更新
   - [ ] 不同视图类型显示不同的配置选项

2. **Layer 3 皮肤选择联动**
   - [ ] 未选择皮肤时不显示详细配置
   - [ ] 选择皮肤后立即显示详细配置
   - [ ] 切换皮肤时配置内容保持

3. **Layer 0 自动跳转联动**
   - [ ] 禁用自动跳转时不显示延迟设置
   - [ ] 启用自动跳转时显示延迟滑块
   - [ ] 延迟设置实时更新

### 数据持久化测试
- [ ] 修改配置后刷新页面，配置保持
- [ ] 在不同标签间切换，配置不丢失
- [ ] 个性化级别正确计算和显示

## 🎨 界面测试

### 响应式设计
- [ ] 桌面端布局正常
- [ ] 平板端适配良好
- [ ] 移动端响应式正常
- [ ] 滑块在小屏幕上可用

### 视觉效果
- [ ] 标签切换动画流畅
- [ ] 卡片悬停效果正常
- [ ] 开关状态变化清晰
- [ ] 滑块拖动体验良好

### 用户体验
- [ ] 配置说明文字清晰
- [ ] 操作反馈及时
- [ ] 错误状态处理良好
- [ ] 加载状态显示正确

## 🔧 技术验证

### 类型安全
- [ ] 无TypeScript编译错误
- [ ] 配置更新函数类型正确
- [ ] 组件Props类型匹配

### 性能表现
- [ ] 页面加载速度 < 2秒
- [ ] 标签切换响应 < 300ms
- [ ] 配置更新响应 < 100ms
- [ ] 滑块拖动流畅无卡顿

### 错误处理
- [ ] 配置加载失败时有降级处理
- [ ] 无效配置值时有默认值
- [ ] 网络错误时有重试机制

## 📊 数据验证

### 配置结构验证
- [ ] 所有6层配置都有正确的数据结构
- [ ] 新增的配置项都有合理的默认值
- [ ] 配置更新函数正确处理嵌套对象

### 业务逻辑验证
- [ ] 进度恢复配置符合业务需求
- [ ] 问题展示字段配置覆盖所有必要字段
- [ ] 交互行为配置满足用户体验需求
- [ ] 视图细节配置支持所有视图类型

## ✅ 验证完成标准

### 基础功能 (必须全部通过)
- [ ] 所有6层配置正常工作
- [ ] 新增的配置项功能完整
- [ ] 动态内容切换正常
- [ ] 皮肤选择系统完整

### 高级功能 (推荐通过)
- [ ] 配置联动关系正确
- [ ] 响应式设计完美
- [ ] 性能表现良好
- [ ] 错误处理完善

### 用户体验 (优秀标准)
- [ ] 界面直观易用
- [ ] 交互反馈及时
- [ ] 配置说明清晰
- [ ] 整体体验流畅

## 🐛 常见问题排查

### 配置不生效
1. 检查updateConfig函数是否正确调用
2. 确认配置对象结构正确
3. 验证React状态更新是否触发

### 动态内容不切换
1. 检查Layer 1的preferred_view_type值
2. 确认条件渲染逻辑正确
3. 验证配置对象是否正确更新

### 皮肤配置不显示
1. 确认selected_skin_id不为空
2. 检查条件渲染的布尔值
3. 验证皮肤选择是否正确更新配置

### 滑块不工作
1. 检查Slider组件导入
2. 确认value和onValueChange正确绑定
3. 验证数值范围和步长设置

---

**注意**: 这个测试指南应该在每次重大更新后执行，确保Quiz设置系统的所有功能都正常工作，特别是新增的动态配置功能。
