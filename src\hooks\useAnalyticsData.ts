/**
 * Analytics页面数据获取钩子
 * 实现离线在线混合模式的分析数据获取和计算
 */

import type { MoodEntry } from '@/types';
import { useCallback, useEffect, useState } from 'react';
import { useHybridMoodEntriesData } from './useHybridData';

// 分析数据类型定义
interface EmotionData {
  name: string;
  value: number;
  color: string;
  percentage?: number;
}

interface WeeklyData {
  day: string;
  intensity: number;
}

interface TagData {
  name: string;
  count: number;
}

interface AnalyticsData {
  totalEntries: number;
  currentStreak: number;
  averageIntensity: number;
  emotionData: EmotionData[];
  weeklyData: WeeklyData[];
  tagData: TagData[];
}

interface UseAnalyticsDataReturn {
  // 数据状态
  analyticsData: AnalyticsData;

  // 加载状态
  isLoading: boolean;
  error: string | null;

  // 网络状态
  isOnline: boolean;
  lastSyncTime: Date | null;

  // 操作方法
  refresh: () => Promise<void>;
  forceSync: () => Promise<void>;
  updatePeriod: (period: 'week' | 'month' | 'year') => void;

  // 当前设置
  currentPeriod: 'week' | 'month' | 'year';
}

/**
 * Analytics页面数据钩子
 * 管理分析数据的获取和计算
 */
export const useAnalyticsData = (): UseAnalyticsDataReturn => {
  // 使用混合数据获取钩子获取心情记录
  const {
    data: moodEntries,
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    refresh,
    forceSync,
  } = useHybridMoodEntriesData();

  // 本地状态
  const [currentPeriod, setCurrentPeriod] = useState<'week' | 'month' | 'year'>('week');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalEntries: 0,
    currentStreak: 0,
    averageIntensity: 0,
    emotionData: [],
    weeklyData: [],
    tagData: [],
  });

  // 计算分析数据
  const calculateAnalytics = useCallback((entries: MoodEntry[], period: string): AnalyticsData => {
    if (!entries || entries.length === 0) {
      return {
        totalEntries: 0,
        currentStreak: 0,
        averageIntensity: 0,
        emotionData: [],
        weeklyData: [],
        tagData: [],
      };
    }

    // 根据时间段过滤数据
    const now = new Date().toISOString();
    const filteredEntries = entries.filter((entry) => {
      const entryDate = new Date(entry.timestamp);
      const diffTime = now.getTime() - entryDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      switch (period) {
        case 'week':
          return diffDays <= 7;
        case 'month':
          return diffDays <= 30;
        case 'year':
          return diffDays <= 365;
        default:
          return true;
      }
    });

    // 计算总记录数
    const totalEntries = filteredEntries.length;

    // 计算平均强度
    const averageIntensity =
      totalEntries > 0
        ? Math.round(
            filteredEntries.reduce((sum, entry) => sum + (entry.intensity || 0), 0) / totalEntries
          )
        : 0;

    // 计算连续天数（简化版）
    const currentStreak = Math.min(entries.length, 7);

    // 计算情绪分布
    const emotionData = calculateEmotionDistribution(filteredEntries);

    // 计算每周数据
    const weeklyData = calculateWeeklyData(filteredEntries);

    // 计算标签数据
    const tagData = calculateTagData(filteredEntries);

    return {
      totalEntries,
      currentStreak,
      averageIntensity,
      emotionData,
      weeklyData,
      tagData,
    };
  }, []);

  // 计算情绪分布
  const calculateEmotionDistribution = useCallback((entries: MoodEntry[]): EmotionData[] => {
    const emotionCounts: { [key: string]: { count: number; color: string } } = {};

    entries.forEach((entry) => {
      if (entry.emotions && Array.isArray(entry.emotions) && entry.emotions.length > 0) {
        const primaryEmotion = entry.emotions[0];
        if (primaryEmotion && typeof primaryEmotion === 'object') {
          const emotionName = primaryEmotion.name || primaryEmotion.emotion_id || 'Unknown';

          if (!emotionCounts[emotionName]) {
            emotionCounts[emotionName] = {
              count: 0,
              color: primaryEmotion.color || `hsl(${Math.random() * 360}, 70%, 50%)`,
            };
          }
          emotionCounts[emotionName].count++;
        }
      } else {
        if (!emotionCounts.Unknown) {
          emotionCounts.Unknown = {
            count: 0,
            color: '#808080',
          };
        }
        emotionCounts.Unknown.count++;
      }
    });

    const total = entries.length;
    return Object.entries(emotionCounts).map(([name, data]) => ({
      name,
      value: data.count,
      color: data.color,
      percentage: total > 0 ? Math.round((data.count / total) * 100) : 0,
    }));
  }, []);

  // 计算每周数据
  const calculateWeeklyData = useCallback((entries: MoodEntry[]): WeeklyData[] => {
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const weeklyIntensity: { [key: string]: { total: number; count: number } } = {};

    entries.forEach((entry) => {
      const timestamp =
        typeof entry.timestamp === 'string' ? new Date(entry.timestamp) : entry.timestamp;
      if (Number.isNaN(timestamp.getTime())) return;

      const dayName = weekDays[timestamp.getDay()];
      if (!weeklyIntensity[dayName]) {
        weeklyIntensity[dayName] = { total: 0, count: 0 };
      }
      weeklyIntensity[dayName].total += entry.intensity || 0;
      weeklyIntensity[dayName].count++;
    });

    return weekDays.map((day) => ({
      day,
      intensity: weeklyIntensity[day]
        ? Math.round(weeklyIntensity[day].total / weeklyIntensity[day].count)
        : 0,
    }));
  }, []);

  // 计算标签数据
  const calculateTagData = useCallback((entries: MoodEntry[]): TagData[] => {
    const tagCounts: { [key: string]: number } = {};

    entries.forEach((entry) => {
      if (entry.tags) {
        try {
          let tags: string[] = [];
          if (typeof entry.tags === 'string') {
            tags = JSON.parse(entry.tags);
          } else if (Array.isArray(entry.tags)) {
            tags = entry.tags;
          }

          if (Array.isArray(tags)) {
            tags.forEach((tag) => {
              if (typeof tag === 'string' && tag.trim()) {
                const cleanTag = tag.trim();
                tagCounts[cleanTag] = (tagCounts[cleanTag] || 0) + 1;
              }
            });
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    });

    return Object.entries(tagCounts)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }, []);

  // 当心情记录数据或时间段更新时，重新计算分析数据
  useEffect(() => {
    if (moodEntries && Array.isArray(moodEntries)) {
      const analytics = calculateAnalytics(moodEntries, currentPeriod);
      setAnalyticsData(analytics);
      console.log(
        '[useAnalyticsData] Updated analytics for period:',
        currentPeriod,
        'entries:',
        moodEntries.length
      );
    }
  }, [moodEntries, currentPeriod, calculateAnalytics]);

  // 更新时间段
  const updatePeriod = useCallback((period: 'week' | 'month' | 'year') => {
    setCurrentPeriod(period);
  }, []);

  return {
    // 数据状态
    analyticsData,

    // 加载状态
    isLoading,
    error: error?.message || null,

    // 网络状态
    isOnline,
    lastSyncTime,

    // 操作方法
    refresh,
    forceSync,
    updatePeriod,

    // 当前设置
    currentPeriod,
  };
};
