# 输入接口更新计划

## 📋 **概述**

根据mood.ts类型定义的扩展，需要更新以下输入接口和验证schema以支持新的字段：

## 🔧 **需要更新的接口**

### **1. 客户端服务接口**

#### **✅ 已更新**
- `CreateMoodEntryInput` - 已扩展支持新字段
- `UpdateMoodEntryInput` - 已扩展支持新字段
- `CreateMoodEntryData` - 已扩展支持新字段
- `UpdateMoodEntryData` - 已扩展支持新字段

#### **🔄 需要更新**
- `CreateEmotionSelectionData` - 需要添加表情信息和选择上下文字段
- `UpdateEmotionSelectionData` - 需要添加表情信息和选择上下文字段
- `EmotionSelectionFilter` - 需要添加新的过滤条件

### **2. 服务端tRPC接口**

#### **🔄 需要更新的Zod Schema**

**synchronizeData路由**:
```typescript
// 当前schema缺少新字段
moodEntriesToUpload: z.array(
  z.object({
    id: z.string(),
    user_id: z.string(),
    timestamp: z.string(),
    emotion_data_set_id: z.string().optional(),
    // ❌ 缺少新字段
    intensity: z.number(),
    reflection: z.string().optional(),
    created_at: z.string(),
    updated_at: z.string(),
    tags: z.array(z.string()).optional()
  })
)

// 需要添加的字段
emoji_set_id: z.string().optional(),
emoji_set_version: z.string().optional(),
skin_id: z.string().optional(),
skin_config_snapshot: z.string().optional(),
view_type_used: z.string().optional(),
render_engine_used: z.string().optional(),
display_mode_used: z.string().optional(),
user_config_snapshot: z.string().optional()
```

**emotionSelectionsToUpload schema**:
```typescript
// 当前schema
emotionSelectionsToUpload: z.array(
  z.object({
    id: z.string().optional(),
    mood_entry_id: z.string(),
    emotion_id: z.string(),
    tier_level: z.number(),
    emotion_data_set_emotion_id: z.string().optional(),
    created_at: z.string().optional()
  })
)

// 需要添加的字段
intensity: z.number().optional(),
emoji_item_id: z.string().optional(),
emoji_unicode: z.string().optional(),
emoji_image_url: z.string().optional(),
emoji_animation_data: z.string().optional(),
selection_path: z.string().optional(),
parent_selection_id: z.string().optional()
```

**performFullSync路由**:
```typescript
// 当前moodEntriesToUpload schema也需要相同的更新
moodEntriesToUpload: z.array(z.object({
  id: z.string(),
  user_id: z.string(),
  timestamp: z.string(),
  emotion_data_set_id: z.string().optional(),
  intensity: z.number(),
  reflection: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string(),
  sync_status: z.string().optional(),
  // 需要添加新字段...
}))
```

### **3. 数据库Schema更新**

#### **🔄 需要更新的表结构**

**mood_entries表**:
```sql
-- 需要添加的列
ALTER TABLE mood_entries ADD COLUMN emoji_set_id TEXT;
ALTER TABLE mood_entries ADD COLUMN emoji_set_version TEXT;
ALTER TABLE mood_entries ADD COLUMN skin_id TEXT;
ALTER TABLE mood_entries ADD COLUMN skin_config_snapshot TEXT;
ALTER TABLE mood_entries ADD COLUMN view_type_used TEXT;
ALTER TABLE mood_entries ADD COLUMN render_engine_used TEXT;
ALTER TABLE mood_entries ADD COLUMN display_mode_used TEXT;
ALTER TABLE mood_entries ADD COLUMN user_config_snapshot TEXT;
```

**emotion_selections表**:
```sql
-- 需要添加的列
ALTER TABLE emotion_selections ADD COLUMN intensity INTEGER;
ALTER TABLE emotion_selections ADD COLUMN emoji_item_id TEXT;
ALTER TABLE emotion_selections ADD COLUMN emoji_unicode TEXT;
ALTER TABLE emotion_selections ADD COLUMN emoji_image_url TEXT;
ALTER TABLE emotion_selections ADD COLUMN emoji_animation_data TEXT;
ALTER TABLE emotion_selections ADD COLUMN selection_path TEXT;
ALTER TABLE emotion_selections ADD COLUMN parent_selection_id TEXT;

-- 添加外键约束
ALTER TABLE emotion_selections ADD CONSTRAINT fk_parent_selection 
  FOREIGN KEY (parent_selection_id) REFERENCES emotion_selections(id);
```

## 🎯 **实施优先级**

### **高优先级 (立即实施)**
1. ✅ 更新客户端CreateMoodEntryInput和UpdateMoodEntryInput接口
2. 🔄 更新数据库schema以支持新字段
3. 🔄 更新MoodEntryRepository的SQL查询
4. 🔄 更新服务端tRPC的Zod验证schema

### **中优先级 (下个版本)**
1. 🔄 更新EmotionSelectionRepository以支持新字段
2. 🔄 更新同步逻辑以处理新字段
3. 🔄 更新服务端SQL查询以包含新字段

### **低优先级 (未来优化)**
1. 🔄 添加新字段的验证逻辑
2. 🔄 优化查询性能
3. 🔄 添加索引以支持新的查询模式

## 📝 **具体更新步骤**

### **步骤1: 更新数据库Schema**
```sql
-- 在full.sql中添加新列定义
-- 创建数据库迁移脚本
-- 更新种子数据以包含新字段的示例值
```

### **步骤2: 更新服务端tRPC Schema**
```typescript
// 创建共享的Zod schema定义
const MoodEntrySchema = z.object({
  id: z.string(),
  user_id: z.string(),
  timestamp: z.string(),
  emotion_data_set_id: z.string().optional(),
  intensity: z.number(),
  reflection: z.string().optional(),
  tags: z.array(z.string()).optional(),
  
  // 新增字段
  emoji_set_id: z.string().optional(),
  emoji_set_version: z.string().optional(),
  skin_id: z.string().optional(),
  skin_config_snapshot: z.string().optional(),
  view_type_used: z.string().optional(),
  render_engine_used: z.string().optional(),
  display_mode_used: z.string().optional(),
  user_config_snapshot: z.string().optional(),
  
  created_at: z.string(),
  updated_at: z.string(),
  sync_status: z.string().optional()
});

const EmotionSelectionSchema = z.object({
  id: z.string().optional(),
  mood_entry_id: z.string(),
  emotion_id: z.string(),
  tier_level: z.number(),
  emotion_data_set_emotion_id: z.string().optional(),
  created_at: z.string().optional(),
  
  // 新增字段
  intensity: z.number().optional(),
  emoji_item_id: z.string().optional(),
  emoji_unicode: z.string().optional(),
  emoji_image_url: z.string().optional(),
  emoji_animation_data: z.string().optional(),
  selection_path: z.string().optional(),
  parent_selection_id: z.string().optional()
});
```

### **步骤3: 更新SQL查询**
```typescript
// 更新INSERT语句以包含新字段
// 更新SELECT语句以返回新字段
// 更新UPDATE语句以支持新字段的修改
```

### **步骤4: 更新客户端同步逻辑**
```typescript
// 确保OnlineServices能够处理新字段
// 更新数据同步逻辑以包含新字段
// 更新本地存储逻辑以保存新字段
```

## 🔍 **验证清单**

### **数据完整性验证**
- [ ] 新字段能够正确保存到数据库
- [ ] 新字段能够正确从数据库读取
- [ ] 新字段能够正确同步到服务端
- [ ] 新字段能够正确从服务端下载

### **类型安全验证**
- [ ] TypeScript编译无错误
- [ ] 所有接口定义一致
- [ ] Zod验证schema正确
- [ ] 运行时类型检查通过

### **功能验证**
- [ ] 心情记录创建包含新字段
- [ ] 心情记录更新支持新字段
- [ ] 情绪选择包含表情信息
- [ ] 配置快照功能正常工作

### **性能验证**
- [ ] 数据库查询性能未受影响
- [ ] 同步性能在可接受范围内
- [ ] 内存使用量在合理范围内

## 🚀 **预期效果**

完成这些更新后，系统将能够：

1. **完整记录用户上下文**: 每个心情记录都包含创建时的完整配置信息
2. **保持视觉一致性**: 历史记录能够按原样显示，不受后续配置变更影响
3. **支持高级功能**: 表情动画、皮肤切换、层级导航等功能完全可用
4. **提供数据分析**: 新字段为用户行为分析提供更丰富的数据
5. **确保类型安全**: 端到端的类型检查确保数据一致性

这个更新计划确保了mood.ts类型定义的扩展能够在整个系统中得到完整的支持和实现。
