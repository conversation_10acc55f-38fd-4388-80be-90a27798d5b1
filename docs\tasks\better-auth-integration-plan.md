# Better-Auth + tRPC + Schema 集成方案

## 📋 **概述**

本文档描述如何将better-auth集成到现有的tRPC + SQLite架构中，实现统一的用户认证、VIP订阅和皮肤购买管理系统。

## 🏗️ **架构设计**

### **1. 认证层架构**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端App      │    │   tRPC Server   │    │   Better-Auth   │
│   (React)       │    │   (Node.js)     │    │   (Auth Core)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ tRPC调用               │ 认证委托               │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ OnlineServices  │◄──►│ AuthMiddleware  │◄──►│ Auth Providers  │
│ (tRPC Client)   │    │ (Session Mgmt)  │    │ (OAuth, Email)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 用户状态同步            │ 数据库操作             │ 会话存储
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ 离线SQLite      │    │ 服务端SQLite    │    │ 会话存储        │
│ (只读用户数据)   │    │ (完整用户数据)   │    │ (Redis/SQLite)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **2. 数据流设计**
```typescript
// 客户端认证流程
App → Better-Auth Client → tRPC Auth Endpoints → Better-Auth Server → Database

// 用户数据同步流程
App → tRPC Sync → Server Services → Database → Client SQLite (Read-Only)

// VIP/购买流程
App → tRPC Purchase → Payment Service → Database → Sync to Client
```

## 🔧 **实现方案**

### **1. Better-Auth 服务端配置**

#### **安装依赖**
```bash
# 服务端
cd server
npm install better-auth @better-auth/drizzle

# 客户端
cd ..
npm install @better-auth/react
```

#### **服务端Auth配置**
```typescript
// server/lib/auth/config.ts
import { betterAuth } from "better-auth";
import { drizzleAdapter } from "@better-auth/drizzle";
import { db } from "../database/index.js";

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "sqlite",
    schema: {
      // 使用现有的users表结构
      user: "users",
      session: "user_sessions", 
      account: "user_accounts", // 新增OAuth账户表
      verification: "user_verifications" // 新增验证表
    }
  }),
  
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    },
    apple: {
      clientId: process.env.APPLE_CLIENT_ID!,
      clientSecret: process.env.APPLE_CLIENT_SECRET!,
    }
  },
  
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  
  user: {
    additionalFields: {
      // 映射到现有schema字段
      displayName: {
        type: "string",
        required: false,
        input: true,
        output: true
      },
      isVip: {
        type: "boolean",
        required: false,
        input: false, // 服务端控制
        output: true
      },
      vipExpiresAt: {
        type: "date",
        required: false,
        input: false,
        output: true
      }
    }
  },
  
  plugins: [
    // 自定义插件处理VIP逻辑
    {
      id: "vip-management",
      hooks: {
        after: [
          {
            matcher: (context) => context.path === "/sign-up",
            handler: async (context) => {
              // 新用户注册后的VIP初始化
              await initializeUserVipStatus(context.user.id);
            }
          }
        ]
      }
    }
  ]
});

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.User;
```

### **2. 数据库Schema扩展**

#### **扩展现有schema**
```sql
-- 扩展现有users表以兼容better-auth
-- 在public/seeds/schema/better-auth-extensions.sql

-- OAuth账户表
CREATE TABLE IF NOT EXISTS user_accounts (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    account_id TEXT NOT NULL,
    provider TEXT NOT NULL,
    provider_account_id TEXT NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    expires_at INTEGER,
    scope TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(provider, provider_account_id)
);

-- 验证表（邮箱验证、密码重置等）
CREATE TABLE IF NOT EXISTS user_verifications (
    id TEXT PRIMARY KEY NOT NULL,
    identifier TEXT NOT NULL, -- email or phone
    value TEXT NOT NULL, -- verification code
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 更新现有user_sessions表以兼容better-auth
ALTER TABLE user_sessions ADD COLUMN IF NOT EXISTS token TEXT;
ALTER TABLE user_sessions ADD COLUMN IF NOT EXISTS user_agent TEXT;
ALTER TABLE user_sessions ADD COLUMN IF NOT EXISTS ip_address TEXT;

-- 索引优化
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id ON user_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_accounts_provider ON user_accounts(provider, provider_account_id);
CREATE INDEX IF NOT EXISTS idx_user_verifications_identifier ON user_verifications(identifier);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(token);
```

### **3. tRPC集成**

#### **认证中间件**
```typescript
// server/lib/middleware/auth.ts
import { auth } from "../auth/config.js";
import { TRPCError } from "@trpc/server";

export const createAuthContext = async (req: Request) => {
  const session = await auth.api.getSession({ 
    headers: req.headers 
  });
  
  return {
    session,
    user: session?.user,
    isAuthenticated: !!session?.user
  };
};

export const requireAuth = t.middleware(async ({ ctx, next }) => {
  if (!ctx.isAuthenticated || !ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required'
    });
  }
  
  return next({
    ctx: {
      ...ctx,
      user: ctx.user,
      userId: ctx.user.id
    }
  });
});
```

#### **更新tRPC路由**
```typescript
// server/lib/router.ts - 认证相关路由
export const authRouter = router({
  // 获取当前用户信息
  me: publicProcedure
    .query(async ({ ctx }) => {
      if (!ctx.user) {
        return { user: null, session: null };
      }
      
      // 从数据库获取完整用户信息（包括VIP状态）
      const userService = UserManagementService.getInstance();
      const profile = await userService.getUserProfile(ctx.user.id);
      
      return {
        user: ctx.user,
        profile: profile.success ? profile.data : null,
        session: ctx.session
      };
    }),
    
  // 更新用户资料
  updateProfile: requireAuth
    .input(z.object({
      displayName: z.string().optional(),
      // 其他可更新字段
    }))
    .mutation(async ({ input, ctx }) => {
      // 通过better-auth更新用户信息
      return await auth.api.updateUser({
        userId: ctx.userId,
        ...input
      });
    }),
});

// VIP和购买相关路由
export const subscriptionRouter = router({
  // 获取VIP状态
  getVipStatus: requireAuth
    .query(async ({ ctx }) => {
      const userService = UserManagementService.getInstance();
      return await userService.getVipStatus(ctx.userId);
    }),
    
  // 购买VIP
  purchaseVip: requireAuth
    .input(z.object({
      plan: z.enum(['monthly', 'yearly']),
      paymentMethodId: z.string()
    }))
    .mutation(async ({ input, ctx }) => {
      const paymentService = PaymentService.getInstance();
      return await paymentService.processVipPurchase(
        ctx.userId, 
        input.plan, 
        input.paymentMethodId
      );
    }),
    
  // 购买皮肤
  purchaseSkin: requireAuth
    .input(z.object({
      skinId: z.string(),
      paymentMethodId: z.string()
    }))
    .mutation(async ({ input, ctx }) => {
      const paymentService = PaymentService.getInstance();
      return await paymentService.processSkinPurchase(
        ctx.userId,
        input.skinId,
        input.paymentMethodId
      );
    })
});

// 合并到主路由
export const appRouter = router({
  auth: authRouter,
  subscription: subscriptionRouter,
  sync: syncRouter,
  analytics: analyticsRouter,
  // ... 其他路由
});
```

### **4. 客户端集成**

#### **Better-Auth客户端配置**
```typescript
// src/lib/auth/client.ts
import { createAuthClient } from "@better-auth/react";

export const authClient = createAuthClient({
  baseURL: process.env.REACT_APP_API_URL || "http://localhost:8788",
  
  // 与tRPC集成
  fetchOptions: {
    onRequest: (context) => {
      // 添加认证头
      const token = localStorage.getItem('auth-token');
      if (token) {
        context.request.headers.set('Authorization', `Bearer ${token}`);
      }
    },
    onResponse: (context) => {
      // 处理认证响应
      const authHeader = context.response.headers.get('set-cookie');
      if (authHeader) {
        // 处理会话cookie
      }
    }
  }
});

export const {
  useSession,
  signIn,
  signUp,
  signOut,
  useUser
} = authClient;
```

#### **tRPC客户端更新**
```typescript
// src/services/online/OnlineServices.ts
import { authClient } from '@/lib/auth/client';

class OnlineServices {
  private trpc: ReturnType<typeof createTRPCClient<AppRouter>>;
  
  constructor() {
    this.trpc = createTRPCClient<AppRouter>({
      links: [
        httpBatchLink({
          url: `${this.baseUrl}/trpc`,
          headers: async () => {
            // 从better-auth获取认证信息
            const session = await authClient.getSession();
            return session ? {
              'Authorization': `Bearer ${session.token}`
            } : {};
          }
        })
      ]
    });
  }
  
  // 认证方法
  async signIn(email: string, password: string) {
    return await authClient.signIn.email({
      email,
      password
    });
  }
  
  async signUp(email: string, password: string, displayName?: string) {
    return await authClient.signUp.email({
      email,
      password,
      name: displayName
    });
  }
  
  async signOut() {
    return await authClient.signOut();
  }
  
  // VIP相关方法
  async getVipStatus() {
    return await this.trpc.subscription.getVipStatus.query();
  }
  
  async purchaseVip(plan: 'monthly' | 'yearly', paymentMethodId: string) {
    return await this.trpc.subscription.purchaseVip.mutate({
      plan,
      paymentMethodId
    });
  }
  
  // 皮肤购买
  async purchaseSkin(skinId: string, paymentMethodId: string) {
    return await this.trpc.subscription.purchaseSkin.mutate({
      skinId,
      paymentMethodId
    });
  }
}
```

### **5. React组件集成**

#### **认证Provider**
```typescript
// src/components/auth/AuthProvider.tsx
import { useSession, useUser } from '@/lib/auth/client';
import { OnlineServices } from '@/services/online';

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { data: session, isLoading } = useSession();
  const { data: user } = useUser();
  
  // 同步用户状态到离线存储
  useEffect(() => {
    if (user) {
      syncUserToOfflineStorage(user);
    }
  }, [user]);
  
  const contextValue = {
    session,
    user,
    isLoading,
    isAuthenticated: !!session,
    signIn: OnlineServices.getInstance().signIn,
    signUp: OnlineServices.getInstance().signUp,
    signOut: OnlineServices.getInstance().signOut
  };
  
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
```

#### **VIP管理组件**
```typescript
// src/components/subscription/VipManager.tsx
export const VipManager: React.FC = () => {
  const { user } = useAuth();
  const [vipStatus, setVipStatus] = useState<VipStatus | null>(null);
  
  const { data: vipData } = OnlineServices.getInstance().trpc.subscription.getVipStatus.useQuery(
    undefined,
    { enabled: !!user }
  );
  
  const purchaseVipMutation = OnlineServices.getInstance().trpc.subscription.purchaseVip.useMutation({
    onSuccess: () => {
      toast.success('VIP购买成功！');
      // 刷新VIP状态
    }
  });
  
  const handlePurchaseVip = async (plan: 'monthly' | 'yearly') => {
    // 集成支付流程
    const paymentMethodId = await showPaymentDialog();
    if (paymentMethodId) {
      purchaseVipMutation.mutate({ plan, paymentMethodId });
    }
  };
  
  return (
    <div className="vip-manager">
      {vipData?.isVip ? (
        <VipStatusCard status={vipData} />
      ) : (
        <VipPurchaseOptions onPurchase={handlePurchaseVip} />
      )}
    </div>
  );
};
```

## 🔄 **数据同步策略**

### **用户数据同步**
```typescript
// 服务端到客户端的用户数据同步
export const syncUserData = async (userId: string) => {
  // 1. 获取服务端用户完整信息
  const serverUser = await getUserFromDatabase(userId);
  
  // 2. 过滤敏感信息，只同步客户端需要的字段
  const clientUserData = {
    id: serverUser.id,
    email: serverUser.email,
    username: serverUser.username,
    displayName: serverUser.displayName,
    avatar: serverUser.avatar,
    isVip: serverUser.isVip,
    vipExpiresAt: serverUser.vipExpiresAt,
    // 不同步密码、认证令牌等敏感信息
  };
  
  // 3. 更新客户端SQLite
  await updateClientUserData(clientUserData);
  
  // 4. 同步皮肤解锁状态
  const unlockedSkins = await getUserUnlockedSkins(userId);
  await updateClientSkinUnlocks(userId, unlockedSkins);
};
```

## 🛡️ **安全考虑**

### **1. 数据隔离**
- 认证数据完全由better-auth管理
- 敏感用户数据（密码、支付信息）永不同步到客户端
- 客户端只存储必要的用户展示信息

### **2. 权限控制**
- 所有VIP和购买操作需要服务端验证
- 客户端皮肤解锁状态为只读
- 支付流程完全在服务端处理

### **3. 会话管理**
- 使用better-auth的安全会话管理
- 支持多设备登录
- 自动令牌刷新

这个集成方案保持了现有架构的完整性，同时添加了现代化的认证和支付功能。
