# 在线服务架构分析与系统性设计

## 当前架构分析

### 🔍 现状问题

#### 1. 在线服务架构混乱
- **OnlineServices.ts**: 简化版，只有基础服务 (ApiClient, NetworkStatus)
- **index.ts**: 有复杂的OnlineServiceFactory，但功能重复
- **Hooks**: 直接调用tRPC，绕过了OnlineServices

#### 2. 交互模式不一致
- **useAuth**: 直接使用 `trpc.login.mutate()`
- **useShop**: 尝试调用 `onlineServices.payment()` (但该服务不存在)
- **useDataSync**: 使用模拟逻辑，未调用实际服务

#### 3. 服务注册缺失
- 支付服务、同步服务、VIP服务等未在OnlineServices中注册
- Hooks无法通过统一接口访问在线服务

## 系统性设计方案

### 🎯 设计原则

1. **统一接口**: 所有在线服务通过OnlineServices统一访问
2. **类型安全**: 利用tRPC的端到端类型安全
3. **离线优先**: 在线服务作为离线服务的补充
4. **错误处理**: 统一的错误处理和重试机制
5. **缓存策略**: 智能缓存和数据同步

### 🏗️ 推荐架构: 混合模式

#### 方案A: 完全通过OnlineServices (推荐)
```typescript
// 页面组件 → Hooks → OnlineServices → tRPC → 服务端
const useAuth = () => {
  const login = async (credentials) => {
    const authService = await onlineServices.auth();
    return await authService.login(credentials);
  };
};
```

#### 方案B: 直接tRPC调用 (当前useAuth模式)
```typescript
// 页面组件 → Hooks → tRPC → 服务端
const useAuth = () => {
  const login = async (credentials) => {
    return await trpc.login.mutate(credentials);
  };
};
```

#### 方案C: 混合模式 (最佳实践)
```typescript
// 简单操作: 直接tRPC
const login = await trpc.login.mutate(credentials);

// 复杂业务: 通过OnlineServices
const paymentService = await onlineServices.payment();
const result = await paymentService.purchaseVip(planId, paymentMethod);
```

### 📋 推荐实施方案

#### 🎯 核心决策: 保持当前tRPC直接调用模式

**理由**:
1. **简洁性**: 减少抽象层，代码更直观
2. **类型安全**: 直接利用tRPC的端到端类型安全
3. **性能**: 减少中间层的性能开销
4. **维护性**: 更少的代码需要维护

#### 🔧 OnlineServices职责重新定义

**新职责**: 只管理基础服务和复杂业务逻辑
```typescript
export class OnlineServices {
  // 基础服务 (保留)
  async apiClient(): Promise<ApiClientService>
  async networkStatus(): Promise<NetworkStatusService>

  // 复杂业务服务 (新增)
  async payment(): Promise<PaymentService>  // 复杂的支付流程
  async analytics(): Promise<AnalyticsService>  // 复杂的数据分析

  // 简单业务 (不需要封装，直接用tRPC)
  // - 认证: trpc.login.mutate()
  // - 同步: trpc.synchronizeData.mutate()
  // - VIP查询: trpc.getVipStatus.query()
}
```

### 🎯 页面-后端交互设计

#### 数据流架构:
```
页面组件 → Hooks → 混合调用 → 服务端
    ↓         ↓         ↓
离线存储 ← Services ← 本地数据库

混合调用:
- 简单操作: 直接tRPC
- 复杂业务: OnlineServices
- 数据查询: 离线Services
```

#### 具体交互模式:

**1. 认证流程** (简单 - 直接tRPC):
```typescript
const useAuth = () => {
  const login = async (credentials) => {
    return await trpc.login.mutate(credentials);
  };

  const register = async (data) => {
    return await trpc.register.mutate(data);
  };
};
```

**2. 支付流程** (复杂 - OnlineServices):
```typescript
const useShop = () => {
  const purchaseSkin = async (skinId, paymentMethodId) => {
    const paymentService = await onlineServices.payment();
    return await paymentService.purchaseSkin(skinId, paymentMethodId);
  };
};
```

**3. 数据同步** (中等 - 直接tRPC):
```typescript
const useDataSync = () => {
  const performSync = async () => {
    const localData = await getLocalPendingData();
    return await trpc.synchronizeData.mutate({
      moodEntriesToUpload: localData.moodEntries,
      lastSyncTimestamp: getLastSyncTimestamp()
    });
  };
};
```

**4. 数据查询** (离线优先):
```typescript
const useHomeData = () => {
  const loadData = async () => {
    // 优先从离线服务获取
    const emotionDataSetService = await Services.emotionDataSet();
    return await emotionDataSetService.getActiveDataSets();
  };
};
```

### 🔄 混合模式设计原则

#### 何时使用OnlineServices:
1. **复杂业务逻辑**: 需要多步骤处理的业务
2. **状态管理**: 需要维护复杂状态的操作
3. **错误处理**: 需要特殊错误处理的场景
4. **缓存策略**: 需要智能缓存的数据

#### 何时直接使用tRPC:
1. **简单CRUD**: 单一的增删改查操作
2. **认证操作**: 登录、注册、登出
3. **状态查询**: 获取用户状态、VIP状态等
4. **数据同步**: 标准的数据同步操作

#### 何时使用离线Services:
1. **数据读取**: 优先从本地数据库读取
2. **离线操作**: 网络不可用时的操作
3. **本地业务**: 纯本地的业务逻辑

## 实施计划

### 🔧 第1步: 重构OnlineServices (只保留复杂业务)

#### 更新OnlineServices.ts:
```typescript
import { trpc } from '@/lib/trpc';
import { ApiClientService } from './ApiClientService';
import { NetworkStatusService } from './NetworkStatusService';

export class OnlineServices {
  private static instance: OnlineServices;
  private services: Map<string, any> = new Map();

  static getInstance(): OnlineServices {
    if (!OnlineServices.instance) {
      OnlineServices.instance = new OnlineServices();
    }
    return OnlineServices.instance;
  }

  // 基础服务 (保留)
  async apiClient(): Promise<ApiClientService> {
    if (!this.services.has('apiClient')) {
      this.services.set('apiClient', new ApiClientService());
    }
    return this.services.get('apiClient');
  }

  async networkStatus(): Promise<NetworkStatusService> {
    if (!this.services.has('networkStatus')) {
      this.services.set('networkStatus', new NetworkStatusService());
    }
    return this.services.get('networkStatus');
  }

  // 复杂业务服务 (新增)
  async payment(): Promise<PaymentService> {
    if (!this.services.has('payment')) {
      const { PaymentService } = await import('./services/PaymentService');
      this.services.set('payment', new PaymentService(trpc));
    }
    return this.services.get('payment');
  }

  async analytics(): Promise<AnalyticsService> {
    if (!this.services.has('analytics')) {
      const { AnalyticsService } = await import('./services/AnalyticsService');
      this.services.set('analytics', new AnalyticsService(trpc));
    }
    return this.services.get('analytics');
  }
}

export const onlineServices = OnlineServices.getInstance();
```

### 🔧 第2步: 创建复杂业务服务

#### PaymentService.ts:
```typescript
export class PaymentService {
  constructor(private trpc: any) {}

  async purchaseVip(planId: string, paymentMethodId: string) {
    try {
      // 1. 验证支付方法
      const paymentMethod = await this.validatePaymentMethod(paymentMethodId);

      // 2. 创建支付意图
      const paymentIntent = await this.trpc.createPaymentIntent.mutate({
        planId,
        paymentMethodId
      });

      // 3. 处理支付
      const result = await this.trpc.purchaseVip.mutate({
        planId,
        paymentIntentId: paymentIntent.id
      });

      // 4. 更新本地VIP状态
      if (result.success) {
        await this.updateLocalVipStatus(result.vipStatus);
      }

      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async purchaseSkin(skinId: string, paymentMethodId: string) {
    // 类似的复杂支付流程
  }

  private async validatePaymentMethod(paymentMethodId: string) {
    // 支付方法验证逻辑
  }

  private async updateLocalVipStatus(vipStatus: any) {
    // 更新本地VIP状态
  }
}
```

### 🔧 第3步: 更新Hooks使用混合模式

#### 更新useAuth.ts (保持直接tRPC):
```typescript
export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const login = async (credentials: LoginCredentials) => {
    try {
      const result = await trpc.login.mutate(credentials);
      if (result.success) {
        setUser(result.user);
        setIsAuthenticated(true);
        localStorage.setItem('authToken', result.token);
      }
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const register = async (data: RegisterData) => {
    return await trpc.register.mutate(data);
  };

  const logout = async () => {
    try {
      await trpc.logout.mutate();
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('authToken');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return { user, isAuthenticated, login, register, logout };
};
```

#### 更新useShop.ts (使用OnlineServices):
```typescript
import { onlineServices } from '@/services/online';

export const useShop = () => {
  const [skins, setSkins] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const purchaseSkin = async (skinId: string, paymentMethodId: string) => {
    setIsLoading(true);
    try {
      const paymentService = await onlineServices.payment();
      const result = await paymentService.purchaseSkin(skinId, paymentMethodId);

      if (result.success) {
        // 更新本地皮肤状态
        await updateLocalSkinStatus(skinId, true);
      }

      return result;
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const purchaseVip = async (planId: string, paymentMethodId: string) => {
    const paymentService = await onlineServices.payment();
    return await paymentService.purchaseVip(planId, paymentMethodId);
  };

  return { skins, purchaseSkin, purchaseVip, isLoading };
};
```

#### 更新useDataSync.ts (直接tRPC):
```typescript
export const useDataSync = () => {
  const [syncStatus, setSyncStatus] = useState({
    isSyncing: false,
    lastSyncTime: null,
    error: null
  });

  const performSync = async () => {
    setSyncStatus(prev => ({ ...prev, isSyncing: true, error: null }));

    try {
      const localData = await getLocalPendingData();

      const result = await trpc.synchronizeData.mutate({
        moodEntriesToUpload: localData.moodEntries,
        emotionSelectionsToUpload: localData.emotionSelections,
        lastSyncTimestamp: getLastSyncTimestamp()
      });

      if (result.success) {
        await processServerData(result);
        setSyncStatus(prev => ({
          ...prev,
          isSyncing: false,
          lastSyncTime: new Date()
        }));
      }

      return result;
    } catch (error) {
      setSyncStatus(prev => ({
        ...prev,
        isSyncing: false,
        error: error.message
      }));
      return { success: false, error: error.message };
    }
  };

  return { syncStatus, performSync };
};
```

### 🔧 第4步: 简化index.ts

#### 更新src/services/online/index.ts:
```typescript
// 导出主要的在线服务类
export { OnlineServices, onlineServices } from './OnlineServices';

// 导出基础服务
export { ApiClientService } from './ApiClientService';
export { NetworkStatusService } from './NetworkStatusService';

// 导出复杂业务服务
export { PaymentService } from './services/PaymentService';
export { AnalyticsService } from './services/AnalyticsService';

// 导出类型
export * from './types/OnlineServiceTypes';

// 移除复杂的Factory模式，简化架构
```

## 优势

### 🚀 混合架构优势:
1. **简洁性**: 简单操作直接使用tRPC，减少不必要的抽象
2. **灵活性**: 复杂业务通过OnlineServices处理
3. **类型安全**: 充分利用tRPC的端到端类型安全
4. **性能**: 减少中间层，提高性能
5. **可维护性**: 清晰的职责分工，易于维护

### 📈 开发效率提升:
1. **学习成本低**: 开发者可以直接使用tRPC，无需学习复杂的服务层
2. **调试友好**: 直接的调用链，更容易调试
3. **扩展性**: 新的简单功能可以直接添加tRPC端点
4. **团队协作**: 明确的使用规则，减少混乱

## 总结与建议

### 🎯 核心建议

**采用混合架构模式**:
1. **简单操作**: 直接使用tRPC (认证、同步、查询)
2. **复杂业务**: 通过OnlineServices (支付、分析)
3. **数据操作**: 优先使用离线Services

### 📋 具体行动计划

#### 立即执行:
1. ✅ **保持useAuth当前模式** - 直接使用tRPC，已经工作良好
2. 🔧 **修复useShop** - 创建PaymentService并注册到OnlineServices
3. 🔧 **重构useDataSync** - 移除模拟逻辑，直接调用tRPC同步端点
4. 🔧 **简化OnlineServices** - 只保留基础服务和复杂业务服务

#### 中期优化:
1. 📝 **创建PaymentService** - 处理复杂的支付流程
2. 📝 **创建AnalyticsService** - 处理复杂的数据分析
3. 🧪 **编写测试** - 为新的服务和hooks编写测试
4. 📚 **更新文档** - 更新使用指南和最佳实践

#### 长期维护:
1. 🔍 **监控性能** - 确保混合架构的性能表现
2. 🔄 **持续优化** - 根据使用情况调整架构
3. 📈 **扩展功能** - 按需添加新的业务服务

### 🚀 预期效果

**短期效果**:
- 修复useShop的支付功能
- 实现真正的数据同步
- 简化在线服务架构

**长期效果**:
- 更清晰的代码结构
- 更好的开发体验
- 更容易的维护和扩展

### ⚠️ 注意事项

1. **向后兼容**: 确保现有功能不受影响
2. **渐进式迁移**: 逐步更新，避免大规模重构
3. **测试覆盖**: 确保所有变更都有测试覆盖
4. **文档更新**: 及时更新相关文档

### 🔗 相关文件

需要修改的文件:
- `src/services/online/OnlineServices.ts` - 重构服务注册
- `src/services/online/index.ts` - 简化导出
- `src/hooks/useShop.ts` - 使用PaymentService
- `src/hooks/useDataSync.ts` - 直接调用tRPC
- `src/hooks/useVip.ts` - 根据需要选择模式

新增文件:
- `src/services/online/services/PaymentService.ts` - 支付服务
- `src/services/online/services/AnalyticsService.ts` - 分析服务

这个混合架构既保持了tRPC的简洁性，又为复杂业务提供了合适的抽象层，是当前项目的最佳选择。
