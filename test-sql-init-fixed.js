// Script to test SQL initialization directly with proper file handling
import * as fs from 'node:fs';
import * as path from 'node:path';
import { executeSqlScript, executeTursoQuery } from './server/lib/localTursoService.js';

// Path to the database file
const dbPath = path.resolve(process.cwd(), 'local.db');
// Path to the seeds directory
const seedsDir = path.resolve(process.cwd(), 'public/seeds');

// Create a log file
const logStream = fs.createWriteStream('sql-init-detailed-log.txt', { flags: 'w' });

// Function to log messages to both console and file
function log(message) {
  console.log(message);
  logStream.write(`${message}\n`);
}

// Function to execute a SQL file
async function executeSqlFile(filePath) {
  try {
    log(`Executing SQL file: ${filePath}`);
    if (!fs.existsSync(filePath)) {
      log(`ERROR: File not found: ${filePath}`);
      return false;
    }

    const sql = fs.readFileSync(filePath, 'utf8');
    await executeSqlScript(sql);
    log(`Successfully executed: ${filePath}`);
    return true;
  } catch (error) {
    log(`ERROR executing ${filePath}: ${error.message}`);
    log(error.stack);
    return false;
  }
}

// Function to initialize the database following master.sql order
async function initializeDatabase() {
  log('Initializing local SQLite database...');

  // Check if the database file exists
  const dbExists = fs.existsSync(dbPath);

  if (dbExists) {
    log('Removing existing database file to force fresh initialization...');
    fs.unlinkSync(dbPath);
    log('Database file removed.');
  }

  log('Database file does not exist. Creating and initializing...');

  try {
    // Disable foreign keys
    await executeTursoQuery('PRAGMA foreign_keys=OFF;');
    await executeTursoQuery('PRAGMA ignore_check_constraints=ON;');
    log('Disabled foreign keys and constraints for initialization');

    // 1. Schema (Table Structure)
    log('\n=== Executing Schema Files ===');
    await executeSqlFile(path.join(seedsDir, 'schema/init.sql'));
    await executeSqlFile(path.join(seedsDir, 'schema/update_emotion_selections.sql'));
    await executeSqlFile(path.join(seedsDir, 'schema/update_mood_entries.sql'));
    await executeSqlFile(path.join(seedsDir, 'schema/update_emoji_items.sql'));

    // 2. Configuration Data
    log('\n=== Executing Configuration Files ===');

    // Load emotions data
    await executeSqlFile(path.join(seedsDir, 'config/emotions.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/update_emotions.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/update_tertiary_emotions.sql'));

    // Load emotion data sets
    await executeSqlFile(path.join(seedsDir, 'config/create_emotion_data_sets.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/populate_emotion_data_set_emotions.sql'));

    // Load emoji sets
    await executeSqlFile(path.join(seedsDir, 'config/emoji_sets.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/additional_emoji_sets.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/image_emoji_sets.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/animated_emoji_sets.sql'));

    // Load translations
    await executeSqlFile(path.join(seedsDir, 'config/emotion_translations.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/ui_labels.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/ui_label_translations.sql'));

    // Load tags
    await executeSqlFile(path.join(seedsDir, 'config/tags.sql'));
    await executeSqlFile(path.join(seedsDir, 'config/tag_translations.sql'));

    // Load app settings
    await executeSqlFile(path.join(seedsDir, 'config/app_settings.sql'));

    // Set database version
    await executeSqlFile(path.join(seedsDir, 'config/version.sql'));

    // 3. Test Data
    log('\n=== Executing Test Data Files ===');

    // Load test user data
    await executeSqlFile(path.join(seedsDir, 'test/users.sql'));
    await executeSqlFile(path.join(seedsDir, 'test/user_preferences.sql'));
    await executeSqlFile(path.join(seedsDir, 'test/user_streaks.sql'));

    // Load test mood entries
    await executeSqlFile(path.join(seedsDir, 'test/mood_entries.sql'));
    await executeSqlFile(path.join(seedsDir, 'test/mood_entry_tags.sql'));

    // Re-enable foreign keys and constraints
    await executeTursoQuery('PRAGMA ignore_check_constraints=OFF;');
    await executeTursoQuery('PRAGMA foreign_keys=ON;');
    log('Re-enabled foreign keys and constraints');

    log('\nDatabase initialization completed successfully');
    return true;
  } catch (error) {
    log(`Failed to initialize database: ${error.message}`);
    log(error.stack);
    return false;
  }
}

// Run the initialization
initializeDatabase().then((success) => {
  log('\nDatabase initialization process finished.');

  if (fs.existsSync(dbPath)) {
    log(`Database file was created at ${dbPath}`);

    if (success) {
      log('SQL initialization appears to be successful.');
    } else {
      log('WARNING: Database file was created but there were errors during initialization.');
    }
  } else {
    log('ERROR: Database file was not created!');
  }

  logStream.end();
});
