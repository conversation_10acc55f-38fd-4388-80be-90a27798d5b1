/**
 * 情绪气泡视图组件
 * 
 * 以动态气泡形式展示情绪数据集，支持气泡大小、颜色和动画效果
 * 这是一个特殊视图组件，用于Quiz系统中的情绪选择场景
 */

import React, { useState, useEffect, useRef } from 'react';
import { PersonalizationConfig, InteractionEvent } from '../../../types/schema/base';

// 情绪气泡数据接口
export interface EmotionBubbleData {
  id: string;
  name: Record<string, string>;
  emoji?: string;
  color?: string;
  intensity?: number;
  frequency?: number;
  category?: string;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
  radius?: number;
}

// 气泡视图配置
export interface BubbleViewConfig {
  width: number;
  height: number;
  min_bubble_size: number;
  max_bubble_size: number;
  animation_enabled: boolean;
  physics_enabled: boolean;
  collision_detection: boolean;
  gravity_strength: number;
  damping: number;
  show_labels: boolean;
  show_emojis: boolean;
  selection_mode: 'single' | 'multiple';
}

// 组件属性接口
export interface EmotionBubbleViewProps {
  id: string;
  emotions: EmotionBubbleData[];
  selectedEmotions: string[];
  onEmotionSelect: (emotionId: string, emotion: EmotionBubbleData) => void;
  onEmotionDeselect?: (emotionId: string) => void;
  config?: Partial<BubbleViewConfig>;
  personalization: PersonalizationConfig;
  onInteraction: (event: InteractionEvent) => void;
  language?: string;
  style?: React.CSSProperties;
}

// 默认配置
const DEFAULT_CONFIG: BubbleViewConfig = {
  width: 600,
  height: 400,
  min_bubble_size: 30,
  max_bubble_size: 80,
  animation_enabled: true,
  physics_enabled: true,
  collision_detection: true,
  gravity_strength: 0.1,
  damping: 0.98,
  show_labels: true,
  show_emojis: true,
  selection_mode: 'multiple'
};

export const EmotionBubbleView: React.FC<EmotionBubbleViewProps> = ({
  id,
  emotions,
  selectedEmotions,
  onEmotionSelect,
  onEmotionDeselect,
  config = {},
  personalization,
  onInteraction,
  language = 'zh',
  style
}) => {
  const bubbleConfig = { ...DEFAULT_CONFIG, ...config };
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [bubbles, setBubbles] = useState<EmotionBubbleData[]>([]);
  const [hoveredBubble, setHoveredBubble] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<string | null>(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

  // 初始化气泡位置和物理属性
  useEffect(() => {
    const initializedBubbles = emotions.map(emotion => {
      const radius = bubbleConfig.min_bubble_size + 
        (emotion.intensity || 0.5) * (bubbleConfig.max_bubble_size - bubbleConfig.min_bubble_size);
      
      return {
        ...emotion,
        x: Math.random() * (bubbleConfig.width - radius * 2) + radius,
        y: Math.random() * (bubbleConfig.height - radius * 2) + radius,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        radius
      };
    });
    
    setBubbles(initializedBubbles);
  }, [emotions, bubbleConfig]);

  // 物理模拟和动画循环
  useEffect(() => {
    if (!bubbleConfig.animation_enabled) return;

    const animate = () => {
      setBubbles(prevBubbles => {
        return prevBubbles.map(bubble => {
          let { x, y, vx, vy, radius } = bubble;

          if (bubbleConfig.physics_enabled && isDragging !== bubble.id) {
            // 重力效果
            vy += bubbleConfig.gravity_strength;

            // 边界碰撞
            if (x + radius > bubbleConfig.width) {
              x = bubbleConfig.width - radius;
              vx *= -bubbleConfig.damping;
            }
            if (x - radius < 0) {
              x = radius;
              vx *= -bubbleConfig.damping;
            }
            if (y + radius > bubbleConfig.height) {
              y = bubbleConfig.height - radius;
              vy *= -bubbleConfig.damping;
            }
            if (y - radius < 0) {
              y = radius;
              vy *= -bubbleConfig.damping;
            }

            // 气泡间碰撞检测
            if (bubbleConfig.collision_detection) {
              prevBubbles.forEach(otherBubble => {
                if (otherBubble.id !== bubble.id) {
                  const dx = (otherBubble.x || 0) - x;
                  const dy = (otherBubble.y || 0) - y;
                  const distance = Math.sqrt(dx * dx + dy * dy);
                  const minDistance = radius + (otherBubble.radius || 0);

                  if (distance < minDistance) {
                    const angle = Math.atan2(dy, dx);
                    const targetX = x + Math.cos(angle) * minDistance;
                    const targetY = y + Math.sin(angle) * minDistance;
                    const ax = (targetX - (otherBubble.x || 0)) * 0.05;
                    const ay = (targetY - (otherBubble.y || 0)) * 0.05;
                    vx -= ax;
                    vy -= ay;
                  }
                }
              });
            }

            // 阻尼
            vx *= bubbleConfig.damping;
            vy *= bubbleConfig.damping;

            // 更新位置
            x += vx;
            y += vy;
          }

          return { ...bubble, x, y, vx, vy };
        });
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [bubbleConfig, isDragging]);

  // 绘制气泡
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, bubbleConfig.width, bubbleConfig.height);

    // 绘制每个气泡
    bubbles.forEach(bubble => {
      const isSelected = selectedEmotions.includes(bubble.id);
      const isHovered = hoveredBubble === bubble.id;
      const bubbleColor = bubble.color || '#4CAF50';
      
      // 气泡阴影
      ctx.save();
      ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
      ctx.shadowBlur = isHovered ? 15 : 8;
      ctx.shadowOffsetX = 2;
      ctx.shadowOffsetY = 2;

      // 绘制气泡圆形
      ctx.beginPath();
      ctx.arc(bubble.x || 0, bubble.y || 0, bubble.radius || 0, 0, 2 * Math.PI);
      ctx.fillStyle = bubbleColor;
      ctx.globalAlpha = isSelected ? 0.9 : isHovered ? 0.7 : 0.6;
      ctx.fill();

      // 选中边框
      if (isSelected) {
        ctx.strokeStyle = '#2E7D32';
        ctx.lineWidth = 3;
        ctx.stroke();
      }

      ctx.restore();

      // 绘制表情符号
      if (bubbleConfig.show_emojis && bubble.emoji) {
        ctx.font = `${(bubble.radius || 0) * 0.6}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#333';
        ctx.fillText(
          bubble.emoji,
          bubble.x || 0,
          (bubble.y || 0) - (bubbleConfig.show_labels ? 8 : 0)
        );
      }

      // 绘制标签
      if (bubbleConfig.show_labels) {
        const emotionName = bubble.name[language] || bubble.name.zh || bubble.name.en || '未知';
        ctx.font = `${Math.max(10, (bubble.radius || 0) * 0.25)}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#333';
        ctx.fillText(
          emotionName,
          bubble.x || 0,
          (bubble.y || 0) + (bubbleConfig.show_emojis ? 12 : 0)
        );
      }

      // 选中指示器
      if (isSelected) {
        ctx.beginPath();
        ctx.arc((bubble.x || 0) + (bubble.radius || 0) * 0.7, (bubble.y || 0) - (bubble.radius || 0) * 0.7, 8, 0, 2 * Math.PI);
        ctx.fillStyle = '#4CAF50';
        ctx.fill();
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // 勾选标记
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo((bubble.x || 0) + (bubble.radius || 0) * 0.7 - 3, (bubble.y || 0) - (bubble.radius || 0) * 0.7);
        ctx.lineTo((bubble.x || 0) + (bubble.radius || 0) * 0.7 - 1, (bubble.y || 0) - (bubble.radius || 0) * 0.7 + 2);
        ctx.lineTo((bubble.x || 0) + (bubble.radius || 0) * 0.7 + 3, (bubble.y || 0) - (bubble.radius || 0) * 0.7 - 2);
        ctx.stroke();
      }
    });
  }, [bubbles, selectedEmotions, hoveredBubble, bubbleConfig, language]);

  // 获取鼠标位置下的气泡
  const getBubbleAtPosition = (x: number, y: number): EmotionBubbleData | null => {
    for (const bubble of bubbles) {
      const dx = x - (bubble.x || 0);
      const dy = y - (bubble.y || 0);
      const distance = Math.sqrt(dx * dx + dy * dy);
      if (distance <= (bubble.radius || 0)) {
        return bubble;
      }
    }
    return null;
  };

  // 处理鼠标事件
  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    setMousePos({ x, y });

    const bubble = getBubbleAtPosition(x, y);
    const newHoveredBubble = bubble?.id || null;
    
    if (newHoveredBubble !== hoveredBubble) {
      setHoveredBubble(newHoveredBubble);
      
      if (newHoveredBubble) {
        onInteraction({
          type: 'hover',
          target: newHoveredBubble,
          data: { action: 'bubble_hover' },
          timestamp: Date.now()
        });
      }
    }

    // 拖拽处理
    if (isDragging && bubble && bubble.id === isDragging) {
      setBubbles(prevBubbles =>
        prevBubbles.map(b =>
          b.id === isDragging ? { ...b, x, y, vx: 0, vy: 0 } : b
        )
      );
    }
  };

  const handleMouseDown = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const bubble = getBubbleAtPosition(x, y);
    if (bubble) {
      setIsDragging(bubble.id);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(null);
  };

  const handleClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const bubble = getBubbleAtPosition(x, y);
    if (bubble) {
      const isSelected = selectedEmotions.includes(bubble.id);

      // 单选模式处理
      if (bubbleConfig.selection_mode === 'single' && !isSelected) {
        selectedEmotions.forEach(selectedId => {
          onEmotionDeselect?.(selectedId);
        });
      }

      // 触发交互事件
      onInteraction({
        type: isSelected ? 'click' : 'select',
        target: bubble.id,
        data: {
          emotion: bubble,
          action: isSelected ? 'deselect' : 'select',
          position: { x, y }
        },
        timestamp: Date.now()
      });

      // 调用选择/取消选择回调
      if (isSelected) {
        onEmotionDeselect?.(bubble.id);
      } else {
        onEmotionSelect(bubble.id, bubble);
      }
    }
  };

  return (
    <div
      id={id}
      style={{
        width: bubbleConfig.width,
        height: bubbleConfig.height,
        position: 'relative',
        border: '1px solid #E0E0E0',
        borderRadius: '8px',
        overflow: 'hidden',
        ...style
      }}
    >
      <canvas
        ref={canvasRef}
        width={bubbleConfig.width}
        height={bubbleConfig.height}
        style={{
          cursor: hoveredBubble ? 'pointer' : 'default',
          display: 'block'
        }}
        onMouseMove={handleMouseMove}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={() => {
          setHoveredBubble(null);
          setIsDragging(null);
        }}
        onClick={handleClick}
      />

      {/* 悬停信息提示 */}
      {hoveredBubble && (
        <div
          style={{
            position: 'absolute',
            left: mousePos.x + 10,
            top: mousePos.y - 30,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            pointerEvents: 'none',
            zIndex: 10
          }}
        >
          {bubbles.find(b => b.id === hoveredBubble)?.name[language] || '未知情绪'}
        </div>
      )}
    </div>
  );
};

export default EmotionBubbleView;
