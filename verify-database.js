// Script to verify database tables after initialization
import { fetchAllFromTursoTable, getTursoClient } from './server/lib/localTursoService.js';

async function verifyDatabase() {
  console.log('Verifying database tables...');

  try {
    // Get list of all tables in the database
    const client = getTursoClient();
    const tablesResult = await client.execute(`
      SELECT name FROM sqlite_master 
      WHERE type='table' 
      ORDER BY name;
    `);

    console.log('\n=== Database Tables ===');
    const tables = tablesResult.rows.map((row) => row.name);
    tables.forEach((table) => console.log(`- ${table}`));

    // Check key tables that should exist after initialization
    const keyTables = [
      'emotion_data_sets',
      'emotion_data_set_emotions',
      'emoji_sets',
      'emoji_items',
      'ui_labels',
      'ui_label_translations',
      'tags',
      'tag_translations',
      'mood_entries',
      'mood_entry_tags',
      'user_preferences',
      'users',
      'user_streaks',
    ];

    console.log('\n=== Checking Key Tables ===');
    for (const table of keyTables) {
      if (tables.includes(table)) {
        // Get row count for this table
        const countResult = await client.execute(`SELECT COUNT(*) as count FROM ${table}`);
        const count = countResult.rows[0].count;
        console.log(`✅ Table '${table}' exists with ${count} rows`);

        // For important tables, show a sample of data
        if (['emotion_data_sets', 'emotion_data_set_emotions', 'emoji_sets'].includes(table)) {
          const sampleData = await fetchAllFromTursoTable(table, 3);
          console.log(
            `   Sample data: ${JSON.stringify(sampleData, null, 2).substring(0, 150)}...`
          );
        }
      } else {
        console.log(`❌ Table '${table}' is MISSING!`);
      }
    }

    console.log('\n=== Database Verification Complete ===');
  } catch (error) {
    console.error('Error verifying database:', error);
  }
}

verifyDatabase();
