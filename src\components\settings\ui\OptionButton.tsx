import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Check, Info } from 'lucide-react';
import type React from 'react';

interface OptionButtonProps {
  label: string;
  tooltip?: string;
  isSelected?: boolean;
  onClick?: () => void;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  icon?: React.ReactNode;
  disabled?: boolean;
}

/**
 * 选项按钮组件
 * 用于设置页面中的选项选择，支持工具提示
 */
const OptionButton: React.FC<OptionButtonProps> = ({
  label,
  tooltip,
  isSelected = false,
  onClick,
  className = '',
  variant = 'outline',
  icon,
  disabled = false,
}) => {
  // 基础按钮样式
  const baseStyles =
    'flex items-center justify-start w-full px-4 py-2 rounded-md text-sm transition-colors';

  // 根据变体和选中状态确定样式
  const variantStyles = {
    default: isSelected
      ? 'bg-primary text-primary-foreground hover:bg-primary/90'
      : 'bg-primary/10 text-foreground hover:bg-primary/20',
    outline: isSelected
      ? 'bg-primary text-primary-foreground hover:bg-primary/90'
      : 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: isSelected
      ? 'bg-accent text-accent-foreground'
      : 'hover:bg-accent hover:text-accent-foreground',
  };

  // 如果有工具提示，则渲染带工具提示的按钮
  if (tooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              type="button"
              className={cn(
                baseStyles,
                variantStyles[variant],
                disabled && 'opacity-50 cursor-not-allowed',
                className
              )}
              onClick={onClick}
              disabled={disabled}
            >
              <div className="flex items-center w-full">
                {icon && <span className="mr-2">{icon}</span>}
                <span>{label}</span>
                {isSelected && <Check className="ml-auto h-4 w-4" />}
              </div>
            </button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // 如果没有工具提示，则渲染普通按钮
  return (
    <button
      type="button"
      className={cn(
        baseStyles,
        variantStyles[variant],
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      onClick={onClick}
      disabled={disabled}
    >
      <div className="flex items-center w-full">
        {icon && <span className="mr-2">{icon}</span>}
        <span>{label}</span>
        {isSelected && <Check className="ml-auto h-4 w-4" />}
      </div>
    </button>
  );
};

export default OptionButton;
