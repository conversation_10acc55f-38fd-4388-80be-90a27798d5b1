# ViewFactory与Quiz系统集成设计

## 🎯 集成目标

将现有的ViewFactory系统与Quiz系统深度集成，实现基于个性化配置的动态UI渲染，支持情绪数据集作为量表的新架构。

## 🏗️ 集成架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Quiz Frontend Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Quiz Session    │  │ Question        │  │ Result          │ │
│  │ Components      │  │ Components      │  │ Components      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────┬───────────────────────────────────┘
                          │ Uses
┌─────────────────────────┴───────────────────────────────────┐
│                Enhanced ViewFactory                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Quiz View       │  │ Emotion View    │  │ Dynamic UI      │ │
│  │ Components      │  │ Components      │  │ Rule Engine     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────┬───────────────────────────────────┘
                          │ Renders based on
┌─────────────────────────┴───────────────────────────────────┐
│              Personalization Configuration                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │         QuestionPresentationData                       │ │
│  │    (Generated by Quiz Engine with 6-Layer Config)     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎨 ViewFactory增强设计

### 1. Quiz专用视图组件

```typescript
// Quiz专用的ViewFactory组件
export interface QuizViewComponents {
  // 问题呈现组件
  QuestionPresenter: React.FC<QuestionPresenterProps>;
  
  // 情绪选择组件
  EmotionSelector: React.FC<EmotionSelectorProps>;
  
  // 进度指示器
  QuizProgressIndicator: React.FC<QuizProgressProps>;
  
  // 导航控制器
  QuizNavigationController: React.FC<QuizNavigationProps>;
  
  // 实时洞察显示器
  RealtimeInsightDisplay: React.FC<RealtimeInsightProps>;
  
  // 结果展示器
  QuizResultPresenter: React.FC<QuizResultProps>;
}

// 问题呈现器属性
interface QuestionPresenterProps {
  questionData: QuestionPresentationData;
  onAnswer: (emotionId: string, metadata?: AnswerMetadata) => void;
  onSkip?: () => void;
  onBack?: () => void;
}

// 情绪选择器属性
interface EmotionSelectorProps {
  emotions: EmotionOption[];
  viewType: ViewType;
  wheelConfig?: PersonalizedWheelConfig;
  cardConfig?: CardPresentationConfig;
  onEmotionSelect: (emotionId: string, confidence?: number) => void;
  selectedEmotionId?: string;
}
```

### 2. 动态组件工厂

```typescript
// 增强的ViewFactory，支持Quiz组件
class EnhancedViewFactory extends ViewFactory {
  // Quiz组件注册表
  private quizComponentRegistry: Map<string, React.ComponentType<any>> = new Map();
  
  constructor() {
    super();
    this.registerQuizComponents();
  }
  
  // 注册Quiz专用组件
  private registerQuizComponents(): void {
    this.quizComponentRegistry.set('QuestionPresenter', QuestionPresenter);
    this.quizComponentRegistry.set('EmotionWheel', EmotionWheel);
    this.quizComponentRegistry.set('EmotionCards', EmotionCards);
    this.quizComponentRegistry.set('EmotionBubbles', EmotionBubbles);
    this.quizComponentRegistry.set('EmotionGalaxy', EmotionGalaxy);
    this.quizComponentRegistry.set('QuizProgressBar', QuizProgressBar);
    this.quizComponentRegistry.set('QuizNavigationBar', QuizNavigationBar);
    this.quizComponentRegistry.set('RealtimeInsightPanel', RealtimeInsightPanel);
    this.quizComponentRegistry.set('QuizResultDashboard', QuizResultDashboard);
  }
  
  // 基于个性化配置创建Quiz组件
  createQuizComponent(
    componentType: string,
    presentationData: QuestionPresentationData,
    additionalProps?: Record<string, any>
  ): React.ReactElement | null {
    const Component = this.quizComponentRegistry.get(componentType);
    if (!Component) {
      console.warn(`Quiz component ${componentType} not found`);
      return null;
    }
    
    // 基于个性化配置生成props
    const enhancedProps = this.generateEnhancedProps(
      componentType,
      presentationData,
      additionalProps
    );
    
    return React.createElement(Component, enhancedProps);
  }
  
  // 生成增强的组件属性
  private generateEnhancedProps(
    componentType: string,
    presentationData: QuestionPresentationData,
    additionalProps?: Record<string, any>
  ): Record<string, any> {
    const baseProps = {
      ...additionalProps,
      presentationData,
    };
    
    // 根据组件类型和个性化配置生成特定属性
    switch (componentType) {
      case 'EmotionWheel':
        return {
          ...baseProps,
          wheelConfig: presentationData.personalized_ui_config.wheel_config,
          emotions: presentationData.emotion_options,
          globalStyles: presentationData.global_styles,
        };
        
      case 'EmotionCards':
        return {
          ...baseProps,
          cardConfig: presentationData.personalized_ui_config.card_config,
          emotions: presentationData.emotion_options,
          layoutConfig: presentationData.personalized_ui_config.layout_config,
        };
        
      case 'QuizProgressBar':
        return {
          ...baseProps,
          progressInfo: presentationData.progress_info,
          styleConfig: presentationData.personalized_ui_config.progress_style,
        };
        
      default:
        return baseProps;
    }
  }
}
```

### 3. 情绪视图组件增强

```typescript
// 增强的情绪轮盘组件
interface EnhancedEmotionWheelProps {
  emotions: EmotionOption[];
  wheelConfig: PersonalizedWheelConfig;
  onEmotionSelect: (emotionId: string, confidence?: number) => void;
  selectedEmotionId?: string;
  
  // Quiz特定属性
  questionContext: {
    questionId: string;
    tierLevel: number;
    isLastQuestion: boolean;
  };
  
  // 个性化配置
  personalizationConfig: {
    layer1_userChoice: UserChoiceConfig;
    layer4_viewDetail: ViewDetailConfig;
    layer5_accessibility: AccessibilityConfig;
  };
  
  // 实时反馈
  realtimeInsights?: RealtimeInsight[];
}

const EnhancedEmotionWheel: React.FC<EnhancedEmotionWheelProps> = ({
  emotions,
  wheelConfig,
  onEmotionSelect,
  selectedEmotionId,
  questionContext,
  personalizationConfig,
  realtimeInsights
}) => {
  // 基于个性化配置调整渲染
  const adjustedWheelConfig = useMemo(() => {
    return applyPersonalizationToWheelConfig(wheelConfig, personalizationConfig);
  }, [wheelConfig, personalizationConfig]);
  
  // 处理情绪选择
  const handleEmotionSelect = useCallback((emotionId: string) => {
    // 如果启用了置信度评级
    if (personalizationConfig.layer4_viewDetail.interaction_enhancements.confidence_rating) {
      // 显示置信度选择器
      showConfidenceSelector(emotionId, onEmotionSelect);
    } else {
      onEmotionSelect(emotionId);
    }
  }, [onEmotionSelect, personalizationConfig]);
  
  // 渲染增强的轮盘
  return (
    <div className="enhanced-emotion-wheel">
      {/* 实时洞察显示 */}
      {realtimeInsights && realtimeInsights.length > 0 && (
        <RealtimeInsightOverlay insights={realtimeInsights} />
      )}
      
      {/* 核心轮盘组件 */}
      <EmotionWheel
        emotions={emotions}
        config={adjustedWheelConfig}
        onEmotionSelect={handleEmotionSelect}
        selectedEmotionId={selectedEmotionId}
        renderEngine={personalizationConfig.layer2_rendering.render_engine_preferences.wheel}
        accessibilityConfig={personalizationConfig.layer5_accessibility}
      />
      
      {/* 问题上下文信息 */}
      {questionContext.isLastQuestion && (
        <LastQuestionIndicator />
      )}
    </div>
  );
};
```

### 4. 动态UI规则集成

```typescript
// ViewFactory与动态UI规则的集成
class DynamicUIViewFactory extends EnhancedViewFactory {
  constructor(
    private dynamicUIRuleEngine: DynamicUIRuleEngine
  ) {
    super();
  }
  
  // 应用动态UI规则后创建组件
  async createDynamicQuizComponent(
    componentType: string,
    presentationData: QuestionPresentationData,
    userContext: UserContext,
    sessionContext: SessionContext,
    additionalProps?: Record<string, any>
  ): Promise<React.ReactElement | null> {
    
    // 1. 应用动态UI规则
    const adjustedPresentationData = await this.dynamicUIRuleEngine.evaluateUIRules(
      presentationData.personalized_ui_config,
      userContext,
      sessionContext
    );
    
    // 2. 更新呈现数据
    const enhancedPresentationData = {
      ...presentationData,
      personalized_ui_config: adjustedPresentationData
    };
    
    // 3. 创建组件
    return this.createQuizComponent(
      componentType,
      enhancedPresentationData,
      additionalProps
    );
  }
}
```

## 🔄 组件生命周期集成

### 1. Quiz会话组件

```typescript
// Quiz会话的主要组件
const QuizSessionComponent: React.FC<QuizSessionProps> = ({
  sessionId,
  onSessionComplete
}) => {
  const [currentQuestion, setCurrentQuestion] = useState<QuestionPresentationData | null>(null);
  const [realtimeInsights, setRealtimeInsights] = useState<RealtimeInsight[]>([]);
  const viewFactory = useViewFactory();
  
  // 获取当前问题
  const { data: questionData, isLoading } = trpc.quizSessions.getCurrentQuestion.useQuery({
    session_id: sessionId
  });
  
  // 提交答案
  const submitAnswerMutation = trpc.quizSessions.submitEmotionAnswer.useMutation({
    onSuccess: (result) => {
      if (result.realtime_insights) {
        setRealtimeInsights(result.realtime_insights);
      }
      
      if (result.next_action_hint === 'QUIZ_COMPLETED') {
        onSessionComplete();
      } else {
        // 获取下一个问题
        refetch();
      }
    }
  });
  
  // 处理情绪选择
  const handleEmotionSelect = useCallback((emotionId: string, metadata?: AnswerMetadata) => {
    if (!currentQuestion) return;
    
    submitAnswerMutation.mutate({
      session_id: sessionId,
      tier_id: currentQuestion.question_id,
      emotion_id: emotionId,
      confidence: metadata?.confidence,
      response_time_ms: metadata?.responseTime,
      interaction_method: metadata?.interactionMethod,
    });
  }, [currentQuestion, sessionId, submitAnswerMutation]);
  
  // 渲染当前问题
  const renderCurrentQuestion = useCallback(() => {
    if (!questionData || !viewFactory) return null;
    
    // 确定使用的视图类型
    const viewType = questionData.personalized_ui_config.view_type || 'wheel';
    
    // 基于视图类型创建对应的组件
    switch (viewType) {
      case 'wheel':
        return viewFactory.createQuizComponent('EmotionWheel', questionData, {
          onEmotionSelect: handleEmotionSelect,
          realtimeInsights
        });
        
      case 'card':
        return viewFactory.createQuizComponent('EmotionCards', questionData, {
          onEmotionSelect: handleEmotionSelect,
          realtimeInsights
        });
        
      case 'bubble':
        return viewFactory.createQuizComponent('EmotionBubbles', questionData, {
          onEmotionSelect: handleEmotionSelect,
          realtimeInsights
        });
        
      case 'galaxy':
        return viewFactory.createQuizComponent('EmotionGalaxy', questionData, {
          onEmotionSelect: handleEmotionSelect,
          realtimeInsights
        });
        
      default:
        return viewFactory.createQuizComponent('EmotionWheel', questionData, {
          onEmotionSelect: handleEmotionSelect,
          realtimeInsights
        });
    }
  }, [questionData, viewFactory, handleEmotionSelect, realtimeInsights]);
  
  if (isLoading) {
    return <QuizLoadingSpinner />;
  }
  
  if (!questionData) {
    return <QuizCompletedMessage onComplete={onSessionComplete} />;
  }
  
  return (
    <div className="quiz-session-container">
      {/* 进度指示器 */}
      {viewFactory.createQuizComponent('QuizProgressBar', questionData)}
      
      {/* 问题呈现区域 */}
      <div className="question-presentation-area">
        {renderCurrentQuestion()}
      </div>
      
      {/* 导航控制 */}
      {viewFactory.createQuizComponent('QuizNavigationBar', questionData, {
        onSkip: () => handleSkip(),
        onBack: () => handleBack(),
      })}
      
      {/* 实时洞察面板 */}
      {realtimeInsights.length > 0 && (
        <RealtimeInsightPanel insights={realtimeInsights} />
      )}
    </div>
  );
};
```

### 2. 个性化配置应用

```typescript
// 个性化配置应用工具
export const PersonalizationApplier = {
  // 应用轮盘配置
  applyWheelPersonalization: (
    baseConfig: WheelConfig,
    personalizationConfig: PersonalizationConfig
  ): PersonalizedWheelConfig => {
    const { layer1_userChoice, layer3_skinBase, layer4_viewDetail, layer5_accessibility } = personalizationConfig;
    
    return {
      ...baseConfig,
      
      // 应用用户选择
      container_size: layer4_viewDetail.wheel_config?.container_size || baseConfig.container_size,
      wheel_radius: layer4_viewDetail.wheel_config?.wheel_radius || baseConfig.wheel_radius,
      
      // 应用皮肤配置
      colors: layer3_skinBase.colors,
      fonts: layer3_skinBase.fonts,
      animations: layer3_skinBase.animations,
      
      // 应用视图细节
      emotion_display_mode: layer4_viewDetail.emotion_presentation.emotion_grouping_style === 'by_category' ? 'hierarchical' : 'flat',
      tier_transition_animation: layer4_viewDetail.emotion_presentation.tier_transition_animation,
      
      // 应用可访问性
      high_contrast_mode: layer5_accessibility.high_contrast,
      large_text_mode: layer5_accessibility.large_text,
      reduce_motion: layer5_accessibility.reduce_motion,
      audio_descriptions: layer5_accessibility.emotion_accessibility.audio_descriptions,
      
      // 应用交互增强
      hover_preview: layer4_viewDetail.interaction_presentation.hover_effects.enable_hover,
      selection_confirmation: layer4_viewDetail.interaction_presentation.confirmation_style.confirmation_method !== 'auto',
      confidence_rating: layer4_viewDetail.interaction_presentation.selection_feedback.show_selection_indicator,
    };
  },
  
  // 应用卡片配置
  applyCardPersonalization: (
    baseConfig: CardConfig,
    personalizationConfig: PersonalizationConfig
  ): PersonalizedCardConfig => {
    // 类似的个性化应用逻辑
    return {
      ...baseConfig,
      // 应用个性化配置
    };
  },
};
```

## 🎯 集成优势

### 1. 统一的组件体系
- ViewFactory提供统一的组件创建和管理机制
- Quiz组件与现有组件无缝集成
- 一致的API和使用模式

### 2. 高度的个性化能力
- 基于6层配置的深度个性化
- 动态UI规则的实时应用
- 用户行为驱动的界面调整

### 3. 优秀的可维护性
- 清晰的组件职责分离
- 可插拔的组件架构
- 易于扩展和修改

### 4. 强大的扩展性
- 支持新视图类型的快速添加
- 灵活的配置系统
- 模块化的组件设计

这个集成设计确保了ViewFactory与Quiz系统的深度融合，为用户提供高度个性化的量表体验。
