import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loading } from '@/components/ui/loading';
import { useLanguage } from '@/contexts/LanguageContext';
import { Services } from '@/services';
import { useEffect, useState } from 'react';
import {
  Bar,
  BarChart,
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import { toast } from 'sonner';

// Quiz分析数据类型定义
interface EmotionData {
  name: string;
  value: number;
  color: string;
  percentage?: number;
}

interface WeeklyData {
  day: string;
  sessions: number;
  completionRate: number;
}

interface QuizTypeData {
  name: string;
  count: number;
  color: string;
}

interface TCMSyndromeData {
  syndrome: string;
  averageScore: number;
  assessmentCount: number;
  severityLevel: string;
}

interface AnalyticsData {
  totalSessions: number;
  completedSessions: number;
  completionRate: number;
  averageSessionTime: number;
  emotionData: EmotionData[];
  weeklyData: WeeklyData[];
  quizTypeData: QuizTypeData[];
  tcmSyndromeData: TCMSyndromeData[];
}

const Analytics = () => {
  const { t } = useLanguage();
  const [period, setPeriod] = useState<'week' | 'month' | 'year'>('week');

  // 新的状态管理 - 使用Quiz服务层
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalSessions: 0,
    completedSessions: 0,
    completionRate: 0,
    averageSessionTime: 0,
    emotionData: [],
    weeklyData: [],
    quizTypeData: [],
    tcmSyndromeData: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  // 数据加载 Effect
  useEffect(() => {
    const loadAnalytics = async () => {
      try {
        setIsLoading(true);

        // 使用真实的Quiz服务
        const quizSessionService = await Services.quizSession();
        const quizAnswerService = await Services.quizAnswer();

        // 获取Quiz会话数据
        const sessionsResult = await quizSessionService.getAllSessions();
        if (!sessionsResult.success) {
          throw new Error(sessionsResult.error);
        }

        // 获取Quiz答案数据
        const answersResult = await quizAnswerService.getAllAnswers();
        if (!answersResult.success) {
          throw new Error(answersResult.error);
        }

        const sessions = sessionsResult.data || [];
        const answers = answersResult.data || [];

        // 计算分析数据
        const analytics = calculateQuizAnalytics(sessions, answers, period);
        setAnalyticsData(analytics);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load analytics';
        toast.error(`${t('errors.failed_to_load_analytics')}: ${errorMessage}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadAnalytics();
  }, [period, t]);

  // Handle period change
  const handlePeriodChange = (newPeriod: 'week' | 'month' | 'year') => {
    setPeriod(newPeriod);
  };

  // 计算Quiz分析数据的辅助函数
  const calculateQuizAnalytics = (sessions: any[], answers: any[], period: string): AnalyticsData => {
    if (!sessions || sessions.length === 0) {
      return {
        totalSessions: 0,
        completedSessions: 0,
        completionRate: 0,
        averageSessionTime: 0,
        emotionData: [],
        weeklyData: [],
        quizTypeData: [],
        tcmSyndromeData: [],
      };
    }

    // 根据时间段过滤数据
    const now = new Date();
    const filteredSessions = sessions.filter((session) => {
      const sessionDate = new Date(session.start_time);
      const diffTime = now.getTime() - sessionDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      switch (period) {
        case 'week':
          return diffDays <= 7;
        case 'month':
          return diffDays <= 30;
        case 'year':
          return diffDays <= 365;
        default:
          return true;
      }
    });

    const totalSessions = filteredSessions.length;
    const completedSessions = filteredSessions.filter(s => s.status === 'COMPLETED').length;
    const completionRate = totalSessions > 0 ? Math.round((completedSessions / totalSessions) * 100) : 0;
    const averageSessionTime = calculateAverageSessionTime(filteredSessions);
    const emotionData = calculateEmotionDataFromQuiz(filteredSessions, answers);
    const weeklyData = calculateWeeklyQuizData(filteredSessions);
    const quizTypeData = calculateQuizTypeData(filteredSessions);
    const tcmSyndromeData = calculateTCMSyndromeData(filteredSessions, answers);

    return {
      totalSessions,
      completedSessions,
      completionRate,
      averageSessionTime,
      emotionData,
      weeklyData,
      quizTypeData,
      tcmSyndromeData,
    };
  };

  // 计算平均会话时间
  const calculateAverageSessionTime = (sessions: any[]): number => {
    const completedSessions = sessions.filter(s => s.status === 'COMPLETED' && s.metadata?.completion_time_seconds);
    if (completedSessions.length === 0) return 0;

    const totalTime = completedSessions.reduce((sum, session) => sum + (session.metadata?.completion_time_seconds || 0), 0);
    return Math.round(totalTime / completedSessions.length);
  };

  // 从Quiz数据计算情绪分布
  const calculateEmotionDataFromQuiz = (sessions: any[], answers: any[]): EmotionData[] => {
    const emotionCounts: { [key: string]: { count: number; color: string } } = {};

    sessions.forEach((session) => {
      if (session.pack_id === 'mood_track_branching_v1' && session.metadata?.emotion_path) {
        const finalEmotion = session.metadata.emotion_path[session.metadata.emotion_path.length - 1];
        if (finalEmotion) {
          if (!emotionCounts[finalEmotion]) {
            emotionCounts[finalEmotion] = {
              count: 0,
              color: getEmotionColor(finalEmotion),
            };
          }
          emotionCounts[finalEmotion].count++;
        }
      }
    });

    const total = Object.values(emotionCounts).reduce((sum, data) => sum + data.count, 0);
    return Object.entries(emotionCounts).map(([name, data]) => ({
      name,
      value: data.count,
      color: data.color,
      percentage: total > 0 ? Math.round((data.count / total) * 100) : 0,
    }));
  };

  // 计算每周Quiz数据
  const calculateWeeklyQuizData = (sessions: any[]): WeeklyData[] => {
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const weeklyStats: { [key: string]: { sessions: number; completed: number } } = {};

    sessions.forEach((session) => {
      const sessionDate = new Date(session.start_time);
      const dayName = weekDays[sessionDate.getDay()];

      if (!weeklyStats[dayName]) {
        weeklyStats[dayName] = { sessions: 0, completed: 0 };
      }
      weeklyStats[dayName].sessions++;
      if (session.status === 'COMPLETED') {
        weeklyStats[dayName].completed++;
      }
    });

    return weekDays.map((day) => ({
      day,
      sessions: weeklyStats[day]?.sessions || 0,
      completionRate: weeklyStats[day]
        ? Math.round((weeklyStats[day].completed / weeklyStats[day].sessions) * 100)
        : 0,
    }));
  };

  // 计算Quiz类型分布
  const calculateQuizTypeData = (sessions: any[]): QuizTypeData[] => {
    const typeCounts: { [key: string]: number } = {};

    sessions.forEach((session) => {
      const type = session.pack_id.includes('tcm') ? 'TCM Assessment' :
                   session.pack_id.includes('mood') ? 'Emotion Tracking' : 'Other';
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });

    return Object.entries(typeCounts).map(([name, count]) => ({
      name,
      count,
      color: name === 'TCM Assessment' ? '#DC2626' : name === 'Emotion Tracking' ? '#3B82F6' : '#6B7280',
    }));
  };

  // 计算中医证素数据
  const calculateTCMSyndromeData = (sessions: any[], answers: any[]): TCMSyndromeData[] => {
    const syndromeStats: { [key: string]: { scores: number[]; count: number } } = {};

    sessions.forEach((session) => {
      if (session.pack_id.includes('tcm') && session.metadata?.total_score) {
        const syndrome = session.pack_id.replace('tcm_', '').replace('_syndrome', '');
        if (!syndromeStats[syndrome]) {
          syndromeStats[syndrome] = { scores: [], count: 0 };
        }
        syndromeStats[syndrome].scores.push(session.metadata.total_score);
        syndromeStats[syndrome].count++;
      }
    });

    return Object.entries(syndromeStats).map(([syndrome, stats]) => ({
      syndrome,
      averageScore: Math.round(stats.scores.reduce((sum, score) => sum + score, 0) / stats.scores.length),
      assessmentCount: stats.count,
      severityLevel: getSeverityLevel(stats.scores.reduce((sum, score) => sum + score, 0) / stats.scores.length),
    }));
  };

  // 获取情绪颜色
  const getEmotionColor = (emotion: string): string => {
    const colors: { [key: string]: string } = {
      happy: '#4CAF50',
      sad: '#2196F3',
      angry: '#F44336',
      fearful: '#9C27B0',
      surprised: '#FF9800',
      disgusted: '#795548',
      aroused: '#E91E63',
      content: '#8BC34A',
      joyful: '#FFEB3B',
    };
    return colors[emotion] || '#9E9E9E';
  };

  // 获取严重程度级别
  const getSeverityLevel = (score: number): string => {
    if (score < 100) return 'normal';
    if (score < 300) return 'mild';
    if (score < 500) return 'moderate';
    return 'severe';
  };





  // 使用分析数据
  const emotionPercentages = analyticsData.emotionData;

  // 获取顶部情绪
  const topEmotion =
    analyticsData.emotionData.length > 0
      ? [...analyticsData.emotionData].sort((a, b) => b.value - a.value)[0]
      : null;

  if (isLoading && analyticsData.totalSessions === 0) {
    return <Loading size="lg" text={t('app.loading')} />;
  }

  return (
    <div className="py-4 space-y-4 animate-fade-in">
      <h2 className="text-xl font-semibold mb-4">{t('analytics.title')}</h2>

      {analyticsData.totalSessions > 0 ? (
        <>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm text-muted-foreground">
                  Total Sessions
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <p className="text-2xl font-bold">{analyticsData.totalSessions}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm text-muted-foreground">
                  Completion Rate
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <p className="text-2xl font-bold">
                  {analyticsData.completionRate}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm text-muted-foreground">
                  {t('analytics.top_emotion')}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                {topEmotion && (
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: topEmotion.color }}
                    />
                    <p className="text-lg font-semibold">{topEmotion.name}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm text-muted-foreground">
                  Avg Session Time
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <p className="text-2xl font-bold">{analyticsData.averageSessionTime}s</p>
              </CardContent>
            </Card>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">{t('analytics.emotions_distribution')}</h3>
              <div className="flex space-x-2">
                <Button
                  variant={period === 'week' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePeriodChange('week')}
                >
                  {t('analytics.week')}
                </Button>
                <Button
                  variant={period === 'month' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePeriodChange('month')}
                >
                  {t('analytics.month')}
                </Button>
                <Button
                  variant={period === 'year' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePeriodChange('year')}
                >
                  {t('analytics.year')}
                </Button>
              </div>
            </div>

            <Card className="p-4">
              <div className="flex flex-col md:flex-row">
                <div className="w-full md:w-1/2 h-[200px] flex justify-center">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={emotionPercentages}
                        cx="50%"
                        cy="50%"
                        innerRadius={50}
                        outerRadius={70}
                        fill="#8884d8"
                        dataKey="value"
                        labelLine={false}
                      >
                        {emotionPercentages.map((entry: { color: string }, index: number) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="w-full md:w-1/2 mt-4 md:mt-0">
                  <div className="space-y-2">
                    {emotionPercentages.map(
                      (emotion: { name: string; color: string; percentage?: number }) => (
                        <div key={emotion.name} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: emotion.color }}
                            />
                            <span>{emotion.name}</span>
                          </div>
                          <span className="font-medium">{emotion.percentage || 0}%</span>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-4">Weekly Quiz Activity</h3>
            <Card className="p-4">
              <div className="h-[200px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={analyticsData.weeklyData}>
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="sessions" fill="#3B82F6" name="Sessions" />
                    <Bar dataKey="completionRate" fill="#10B981" name="Completion Rate %" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-4">Quiz Types</h3>
            <div className="flex flex-wrap gap-2">
              {analyticsData.quizTypeData.map((type) => (
                <Badge key={type.name} className="text-sm py-1 px-3" style={{ backgroundColor: type.color }}>
                  {type.name} <span className="ml-1 text-xs opacity-70">({type.count})</span>
                </Badge>
              ))}
            </div>
          </div>

          {analyticsData.tcmSyndromeData.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-4">TCM Syndrome Assessment</h3>
              <div className="space-y-2">
                {analyticsData.tcmSyndromeData.map((syndrome) => (
                  <Card key={syndrome.syndrome} className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium capitalize">{syndrome.syndrome}</h4>
                        <p className="text-sm text-muted-foreground">
                          {syndrome.assessmentCount} assessments
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold">{syndrome.averageScore}</p>
                        <p className="text-sm text-muted-foreground capitalize">{syndrome.severityLevel}</p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <div className="text-5xl mb-4">📊</div>
          <h2 className="text-xl font-medium mb-2">{t('analytics.no_data')}</h2>
          <p className="text-muted-foreground text-sm">{t('analytics.empty_state')}</p>
        </div>
      )}
    </div>
  );
};

export default Analytics;
