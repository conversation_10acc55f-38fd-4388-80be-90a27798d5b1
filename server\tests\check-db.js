// 检查数据库表和外键关系
const { executeQuery, initializeDatabaseService, createSQLiteConfig } = require('./dist/database');

async function checkDatabase() {
  try {
    console.log('Initializing database service...');
    initializeDatabaseService(createSQLiteConfig('./local.db'));

    // 1. 检查所有表
    console.log('\n--- 检查所有表 ---');
    const tables = await executeQuery(
      "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"
    );
    console.log('Tables in database:');
    tables.rows.forEach((table) => console.log(`- ${table.name}`));

    // 2. 检查 emotion_data_sets 表
    console.log('\n--- 检查 emotion_data_sets 表 ---');
    const emotionDataSets = await executeQuery('SELECT id, name FROM emotion_data_sets;');
    console.log(`Found ${emotionDataSets.rows.length} emotion data sets:`);
    emotionDataSets.rows.forEach((set) => console.log(`- ${set.id}: ${set.name}`));

    // 3. 检查 emotion_data_set_tiers 表
    console.log('\n--- 检查 emotion_data_set_tiers 表 ---');
    const tiers = await executeQuery(
      'SELECT id, emotion_data_set_id, tier_level, name FROM emotion_data_set_tiers;'
    );
    console.log(`Found ${tiers.rows.length} emotion data set tiers:`);
    tiers.rows.forEach((tier) =>
      console.log(
        `- ${tier.id}: Level ${tier.tier_level} in ${tier.emotion_data_set_id} (${tier.name})`
      )
    );

    // 4. 检查 emotion_data_set_emotions 表中的关键记录
    console.log('\n--- 检查 emotion_data_set_emotions 表中的关键记录 ---');
    const keyEmotions = [
      'default_happy',
      'default_sad',
      'default_angry',
      'default_fearful',
      'default_playful',
      'default_lonely',
      'default_frustrated',
      'default_anxious',
      'default_peaceful',
      'default_aroused',
      'default_isolated',
      'default_annoyed',
      'default_worried',
      'default_thankful',
    ];

    const placeholders = keyEmotions.map(() => '?').join(',');
    const emotionQuery = `SELECT id, emotion_data_set_id, tier_id, emotion_id FROM emotion_data_set_emotions WHERE id IN (${placeholders});`;

    const emotions = await executeQuery({
      sql: emotionQuery,
      args: keyEmotions,
    });

    console.log(`Found ${emotions.rows.length} of ${keyEmotions.length} required emotions:`);

    // 显示找到的情绪
    emotions.rows.forEach((emotion) => {
      console.log(
        `- ${emotion.id}: ${emotion.emotion_id} in ${emotion.emotion_data_set_id} (tier: ${emotion.tier_id})`
      );
    });

    // 显示缺失的情绪
    const foundIds = emotions.rows.map((e) => e.id);
    const missingIds = keyEmotions.filter((id) => !foundIds.includes(id));

    if (missingIds.length > 0) {
      console.log('\nMissing emotions:');
      missingIds.forEach((id) => console.log(`- ${id}`));
    }

    // 5. 检查 users 表
    console.log('\n--- 检查 users 表 ---');
    const users = await executeQuery('SELECT id, username FROM users;');
    console.log(`Found ${users.rows.length} users:`);
    users.rows.forEach((user) => console.log(`- ${user.id}: ${user.username}`));

    console.log('\nDatabase check completed.');
  } catch (error) {
    console.error('Error checking database:', error);
  }
}

checkDatabase();
