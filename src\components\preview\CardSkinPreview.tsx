/**
 * 卡片皮肤预览组件
 * 用于在设置页面中展示卡片视图的皮肤预览
 */

import { useLanguage } from '@/contexts/LanguageContext';
import type { ContentDisplayMode, Skin, CardLayout } from '@/types';
import { ViewFactory } from '@/utils/viewFactory';
import type React from 'react';
import { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

interface CardSkinPreviewProps {
  skin: Skin;
  size?: 'sm' | 'md' | 'lg';
  showControls?: boolean;
  className?: string;
}

/**
 * 卡片皮肤预览组件
 */
const CardSkinPreview: React.FC<CardSkinPreviewProps> = ({
  skin,
  size = 'md',
  showControls = true,
  className = '',
}) => {
  const { t } = useLanguage();
  const [contentDisplayMode, setContentDisplayMode] = useState<ContentDisplayMode>('textEmoji');
  const [layout, setLayout] = useState<CardLayout>('grid');

  // 创建示例情绪数据用于预览
  const sampleEmotions = [
    { id: '1', name: t('emotions.happy', { fallback: 'Happy' }), emoji: '😊', color: '#FFD700' },
    { id: '2', name: t('emotions.sad', { fallback: 'Sad' }), emoji: '😢', color: '#4169E1' },
    { id: '3', name: t('emotions.angry', { fallback: 'Angry' }), emoji: '😠', color: '#FF4500' },
    { id: '4', name: t('emotions.fear', { fallback: 'Fear' }), emoji: '😨', color: '#800080' },
    {
      id: '5',
      name: t('emotions.surprise', { fallback: 'Surprise' }),
      emoji: '😲',
      color: '#FF69B4',
    },
    {
      id: '6',
      name: t('emotions.disgust', { fallback: 'Disgust' }),
      emoji: '🤢',
      color: '#32CD32',
    },
  ];

  // 获取容器尺寸
  const getContainerSize = () => {
    switch (size) {
      case 'sm':
        return { width: 200, height: 200 };
      case 'lg':
        return { width: 400, height: 400 };
      default:
        return { width: 300, height: 300 };
    }
  };

  const containerSize = getContainerSize();

  // 创建卡片视图
  const createCardView = () => {
    // 更新皮肤配置以使用选定的布局
    const updatedSkinConfig = {
      ...skin.config,
      view_configs: {
        ...skin.config.view_configs,
        card: {
          ...skin.config.view_configs?.card,
          layout,
        },
      },
    };

    // 使用 ViewFactory 静态方法创建视图
    const view = ViewFactory.createView('card', contentDisplayMode, updatedSkinConfig, { layout });

    // 渲染视图
    return view.render(
      sampleEmotions,
      1,
      () => {} // 空函数，因为这只是预览
    );
  };

  return (
    <div className={`card-skin-preview ${className}`}>
      <Card className="border shadow-sm">
        {showControls && (
          <div className="p-3 border-b">
            <div className="flex flex-col gap-2">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="cardLayout" className="text-xs mb-1 block">
                    {t('skin_preview.card_layout', { fallback: '卡片布局' })}
                  </Label>
                  <Select value={layout} onValueChange={(value) => setLayout(value as CardLayout)}>
                    <SelectTrigger id="cardLayout" className="h-8 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="grid">
                        {t('skin_preview.grid_layout', { fallback: '网格布局' })}
                      </SelectItem>
                      <SelectItem value="list">
                        {t('skin_preview.list_layout', { fallback: '列表布局' })}
                      </SelectItem>
                      <SelectItem value="masonry">
                        {t('skin_preview.masonry_layout', { fallback: '瀑布流布局' })}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <Label htmlFor="contentDisplayMode" className="text-xs mb-1 block">
                    {t('skin_preview.content_display_mode', { fallback: '内容显示' })}
                  </Label>
                  <Select
                    value={contentDisplayMode}
                    onValueChange={(value) => setContentDisplayMode(value as ContentDisplayMode)}
                  >
                    <SelectTrigger id="contentDisplayMode" className="h-8 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">
                        {t('skin_preview.text_only', { fallback: '仅文本' })}
                      </SelectItem>
                      <SelectItem value="emoji">
                        {t('skin_preview.emoji_only', { fallback: '仅表情' })}
                      </SelectItem>
                      <SelectItem value="textEmoji">
                        {t('skin_preview.text_emoji', { fallback: '文本+表情' })}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        )}
        <CardContent className="p-2 flex items-center justify-center">
          <div
            className="card-preview-container"
            style={{
              width: `${containerSize.width}px`,
              height: `${containerSize.height}px`,
              overflow: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {createCardView()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CardSkinPreview;
