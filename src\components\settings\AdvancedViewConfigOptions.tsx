import { useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUserConfig } from "@/contexts/UserConfigContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Separator } from "@/components/ui/separator";
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from "@/components/ui/collapsible";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Settings, ChevronDown, ChevronRight, Sliders, 
  Palette, Eye, Sparkles, Crown, Zap
} from "lucide-react";
import { toast } from "sonner";

interface AdvancedViewConfigOptionsProps {
  userLevel: 'beginner' | 'regular' | 'advanced' | 'vip';
  viewType: string;
  isOpen: boolean;
  onToggle: () => void;
}

const AdvancedViewConfigOptions: React.FC<AdvancedViewConfigOptionsProps> = ({
  userLevel,
  viewType,
  isOpen,
  onToggle
}) => {
  const { t } = useLanguage();
  const { userConfig, updateUserConfig } = useUserConfig();
  
  // Collapsible 状态管理
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    geometry: false,
    visual: false,
    interaction: false,
    animation: false,
    responsive: false
  });

  // 切换 collapsible 状态
  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 获取当前视图配置
  const getCurrentViewConfig = () => {
    try {
      const skinConfigs = userConfig?.view_type_skin_ids ? 
        JSON.parse(userConfig.view_type_skin_ids) : {};
      return skinConfigs[viewType] || {};
    } catch {
      return {};
    }
  };

  const currentConfig = getCurrentViewConfig();

  // 处理配置更新
  const handleConfigUpdate = (configPath: string, value: any) => {
    try {
      const updatedConfig = {
        ...currentConfig,
        [configPath]: value
      };
      
      const updatedSkinConfigs = {
        ...JSON.parse(userConfig?.view_type_skin_ids || '{}'),
        [viewType]: updatedConfig
      };
      
      updateUserConfig({
        view_type_skin_ids: JSON.stringify(updatedSkinConfigs)
      });
      
      toast.success(t('settings.config_updated', '配置已更新'));
    } catch (error) {
      console.error('Failed to update config:', error);
      toast.error(t('settings.config_update_failed', '配置更新失败'));
    }
  };

  // 渲染几何配置
  const renderGeometryConfig = () => (
    <Collapsible 
      open={openSections.geometry} 
      onOpenChange={() => toggleSection('geometry')}
    >
      <CollapsibleTrigger asChild>
        <Button 
          variant="ghost" 
          className="w-full justify-between p-4 h-auto"
        >
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="font-medium">
              {t('view_config.geometry', '几何配置')}
            </span>
            <Badge variant="secondary" className="ml-2">
              {t('view_config.basic', '基础')}
            </Badge>
          </div>
          {openSections.geometry ? 
            <ChevronDown className="h-4 w-4" /> : 
            <ChevronRight className="h-4 w-4" />
          }
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="px-4 pb-4">
        <div className="space-y-4 pt-2">
          {/* 容器大小 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              {t('wheel_config.container_size', '容器大小')}: {currentConfig.container_size || 400}px
            </Label>
            <Slider
              value={[currentConfig.container_size || 400]}
              onValueChange={(value) => handleConfigUpdate('container_size', value[0])}
              min={200}
              max={600}
              step={20}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>200px</span>
              <span>600px</span>
            </div>
          </div>

          {/* 视图特定配置 */}
          {viewType === 'wheel' && (
            <>
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  {t('wheel_config.wheel_radius', '轮盘半径')}: {currentConfig.wheel_radius || 180}px
                </Label>
                <Slider
                  value={[currentConfig.wheel_radius || 180]}
                  onValueChange={(value) => handleConfigUpdate('wheel_radius', value[0])}
                  min={100}
                  max={300}
                  step={10}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  {t('wheel_config.inner_radius', '内圆半径')}: {currentConfig.inner_radius || 60}px
                </Label>
                <Slider
                  value={[currentConfig.inner_radius || 60]}
                  onValueChange={(value) => handleConfigUpdate('inner_radius', value[0])}
                  min={20}
                  max={120}
                  step={10}
                  className="w-full"
                />
              </div>
            </>
          )}

          {viewType === 'card' && (
            <>
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  {t('card_config.card_width', '卡片宽度')}: {currentConfig.card_width || 200}px
                </Label>
                <Slider
                  value={[currentConfig.card_width || 200]}
                  onValueChange={(value) => handleConfigUpdate('card_width', value[0])}
                  min={150}
                  max={300}
                  step={10}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  {t('card_config.card_height', '卡片高度')}: {currentConfig.card_height || 120}px
                </Label>
                <Slider
                  value={[currentConfig.card_height || 120]}
                  onValueChange={(value) => handleConfigUpdate('card_height', value[0])}
                  min={80}
                  max={200}
                  step={10}
                  className="w-full"
                />
              </div>
            </>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  // 渲染视觉效果配置
  const renderVisualConfig = () => (
    <Collapsible 
      open={openSections.visual} 
      onOpenChange={() => toggleSection('visual')}
    >
      <CollapsibleTrigger asChild>
        <Button 
          variant="ghost" 
          className="w-full justify-between p-4 h-auto"
        >
          <div className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <span className="font-medium">
              {t('view_config.visual_effects', '视觉效果')}
            </span>
            <Badge variant="outline" className="ml-2">
              {t('view_config.advanced', '高级')}
            </Badge>
          </div>
          {openSections.visual ? 
            <ChevronDown className="h-4 w-4" /> : 
            <ChevronRight className="h-4 w-4" />
          }
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="px-4 pb-4">
        <div className="space-y-4 pt-2">
          {/* 3D 效果 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('view_config.use_3d_effects', '3D 效果')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('view_config.use_3d_effects_desc', '启用立体视觉效果')}
              </p>
            </div>
            <Switch
              checked={currentConfig.use_3d_effects || false}
              onCheckedChange={(value) => handleConfigUpdate('use_3d_effects', value)}
            />
          </div>

          {/* 阴影效果 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">
                {t('view_config.shadow_enabled', '阴影效果')}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t('view_config.shadow_enabled_desc', '添加投影效果')}
              </p>
            </div>
            <Switch
              checked={currentConfig.shadow_enabled || false}
              onCheckedChange={(value) => handleConfigUpdate('shadow_enabled', value)}
            />
          </div>

          {/* 装饰效果 (VIP功能) */}
          {userLevel === 'vip' && (
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-sm font-medium flex items-center space-x-1">
                  <span>{t('view_config.decorations', '装饰效果')}</span>
                  <Crown className="h-3 w-3" />
                </Label>
                <p className="text-xs text-muted-foreground">
                  {t('view_config.decorations_desc', '添加视觉装饰元素')}
                </p>
              </div>
              <Switch
                checked={currentConfig.decorations || false}
                onCheckedChange={(value) => handleConfigUpdate('decorations', value)}
              />
            </div>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  // 渲染交互配置
  const renderInteractionConfig = () => (
    <Collapsible 
      open={openSections.interaction} 
      onOpenChange={() => toggleSection('interaction')}
    >
      <CollapsibleTrigger asChild>
        <Button 
          variant="ghost" 
          className="w-full justify-between p-4 h-auto"
        >
          <div className="flex items-center gap-2">
            <Sliders className="h-4 w-4" />
            <span className="font-medium">
              {t('view_config.interaction', '交互行为')}
            </span>
            <Badge variant="secondary" className="ml-2">
              {t('view_config.behavior', '行为')}
            </Badge>
          </div>
          {openSections.interaction ? 
            <ChevronDown className="h-4 w-4" /> : 
            <ChevronRight className="h-4 w-4" />
          }
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="px-4 pb-4">
        <div className="space-y-4 pt-2">
          {/* 悬停效果 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              {t('view_config.hover_effect', '悬停效果')}
            </Label>
            <Select
              value={currentConfig.hover_effect || 'glow'}
              onValueChange={(value) => handleConfigUpdate('hover_effect', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">{t('hover.none', '无效果')}</SelectItem>
                <SelectItem value="highlight">{t('hover.highlight', '高亮')}</SelectItem>
                <SelectItem value="scale">{t('hover.scale', '缩放')}</SelectItem>
                <SelectItem value="glow">{t('hover.glow', '发光')}</SelectItem>
                <SelectItem value="lift">{t('hover.lift', '抬升')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 过渡时间 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              {t('view_config.transition_duration', '过渡时间')}: {currentConfig.transition_duration || 300}ms
            </Label>
            <Slider
              value={[currentConfig.transition_duration || 300]}
              onValueChange={(value) => handleConfigUpdate('transition_duration', value[0])}
              min={100}
              max={1000}
              step={50}
              className="w-full"
            />
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>{t('settings.advanced_view_config', '高级视图配置')}</span>
                {userLevel === 'vip' && (
                  <Badge variant="destructive" className="flex items-center space-x-1">
                    <Crown className="h-3 w-3" />
                    <span>VIP</span>
                  </Badge>
                )}
              </CardTitle>
              {isOpen ? 
                <ChevronDown className="h-4 w-4" /> : 
                <ChevronRight className="h-4 w-4" />
              }
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="space-y-4">
            {/* 配置说明 */}
            <div className="p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                {t('view_config.explanation', '详细配置当前视图的几何、视觉效果和交互行为。')}
              </p>
            </div>

            {/* 几何配置 */}
            {renderGeometryConfig()}
            
            <Separator />
            
            {/* 视觉效果配置 */}
            {renderVisualConfig()}
            
            <Separator />
            
            {/* 交互配置 */}
            {renderInteractionConfig()}

            {/* VIP功能提示 */}
            {userLevel !== 'vip' && (
              <>
                <Separator />
                <div className="p-3 bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <Crown className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
                        {t('view_config.vip_features', 'VIP专属功能')}
                      </p>
                      <p className="text-xs text-amber-700 dark:text-amber-300">
                        {t('view_config.vip_desc', '升级到VIP以解锁装饰效果、高级动画和更多自定义选项')}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default AdvancedViewConfigOptions;
