/**
 * Quiz引擎 V3 - 基于新架构
 *
 * 遵循 src/services/README.md 架构规范
 * 位置: src/services/entities/QuizEngineV3.ts
 *
 * 完全基于新的数据库架构设计，支持:
 * - quiz_packs, quiz_questions, quiz_question_options (新架构)
 * - quiz_sessions, quiz_answers, quiz_results (新架构)
 * - quiz_session_presentation_configs (新架构)
 *
 * 替代旧的 emotion_data_set tier 管理方式
 */

import { BaseService } from '../base/BaseService';
import { QuizPackRepository } from './QuizPackRepository';
import { QuizSessionRepository } from './QuizSessionRepository';
import { QuizAnswerRepository } from './QuizAnswerRepository';
import type { ServiceResult } from '../types/ServiceTypes';

// 使用统一类型定义
import type {
  QuizSession,
} from '../../types/schema/base';

// Quiz引擎配置
export interface QuizEngineConfig {
  enableRealtimeInsights: boolean;
  enableAutoSave: boolean;
  autoSaveInterval: number;
  maxSessionDuration: number;
  enableAnalytics: boolean;
}

// 问题呈现数据 (新架构)
export interface QuestionPresentationData {
  session_id: string;
  question_id: string;
  question_text: string;
  question_text_localized?: string;
  question_type: string;
  question_order: number;
  tier_level: number;

  // 问题选项 (基于quiz_question_options表)
  question_options: Array<{
    option_id: string;
    option_text: string;
    option_text_localized?: string;
    option_value: string;
    option_type: 'choice' | 'scale_point' | 'ranking_item' | 'matrix_row' | 'matrix_column' | 'input_constraint';
    option_order: number;
    scoring_value?: number;
    scoring_weight?: number;

    // 数值范围 (用于slider等)
    min_value?: number;
    max_value?: number;
    step_value?: number;

    // 多媒体内容
    media_url?: string;
    media_type?: 'image' | 'audio' | 'video';
    media_thumbnail_url?: string;
    media_alt_text?: string;

    // 矩阵题支持
    matrix_row_id?: string;
    matrix_column_id?: string;

    // 扩展数据
    metadata?: Record<string, any>;
    tags?: string[];
  }>;

  // 进度信息
  progress_info: {
    current_question: number;
    total_questions: number;
    completion_percentage: number;
    estimated_remaining_time: string;
  };

  // 导航配置
  navigation_config: {
    show_back_button: boolean;
    show_skip_button: boolean;
    show_progress_indicator: boolean;
    allow_revision: boolean;
  };

  // 问题配置
  question_config?: Record<string, any>;
  validation_rules?: Record<string, any>;
  scoring_config?: Record<string, any>;
}

// 实时洞察
export interface RealtimeInsight {
  type: string;
  message: string;
  confidence: number;
  metadata?: Record<string, any>;
}

// 答案提交结果
export interface AnswerSubmissionResult {
  session_id: string;
  question_id: string;
  is_valid: boolean;
  validation_error_key?: string;
  next_action_hint?: string;
  realtime_insights?: RealtimeInsight[];
  progress_update?: {
    current_question: number;
    total_questions: number;
    completion_percentage: number;
  };
  next_question_data?: QuestionPresentationData;
}

/**
 * Quiz引擎 V3 - 新架构实现
 *
 * 特性:
 * - 完全基于新数据库架构
 * - 支持多种问题类型和选项类型
 * - 实时进度跟踪
 * - 灵活的答案验证
 * - 自动结果生成
 * - 遵循BaseService架构模式
 */
export class QuizEngineV3 extends BaseService<QuizSession, any, any> {
  private _engineConfig: QuizEngineConfig;

  constructor(
    private quizPackRepository: QuizPackRepository,
    private quizSessionRepository: QuizSessionRepository,
    private _quizAnswerRepository: QuizAnswerRepository,
    config: Partial<QuizEngineConfig> = {}
  ) {
    super(quizSessionRepository);
    this._engineConfig = {
      enableRealtimeInsights: true,
      enableAutoSave: true,
      autoSaveInterval: 30000, // 30秒
      maxSessionDuration: 3600000, // 1小时
      enableAnalytics: true,
      ...config
    };
  }

  /**
   * 创建Quiz会话 (新架构)
   */
  async createQuizSession(
    packId: string,
    userId: string,
    sessionType: string = 'standard'
  ): Promise<ServiceResult<QuizSession>> {
    try {
      // 验证Quiz包存在
      const pack = await this.quizPackRepository.findById(packId);
      if (!pack) {
        return {
          success: false,
          error: 'Quiz pack not found'
        };
      }

      // 获取问题总数
      const questionsResult = await this.quizPackRepository.getQuestionsByPackId(packId);
      const totalQuestions = questionsResult.success ? (questionsResult.data?.length || 0) : 0;

      // 生成会话ID
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // 创建会话记录
      const sessionData = {
        id: sessionId,
        pack_id: packId,
        user_id: userId,
        status: 'INITIATED' as const,
        current_question_index: 0,
        total_questions: totalQuestions,
        answered_questions: 0,
        skipped_questions: 0,
        completion_percentage: 0,
        start_time: new Date().toISOString(),
        last_active_time: new Date().toISOString(),
        session_type: sessionType,
        session_metadata: {
          pack_name: pack.name,
          pack_type: pack.quiz_type,
          estimated_duration: pack.estimated_duration_minutes
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const session = await this.quizSessionRepository.create(sessionData);
      return {
        success: true,
        data: session
      };

    } catch (error) {
      return {
        success: false,
        error: `Failed to create quiz session: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * 获取当前问题数据 (新架构)
   */
  async getCurrentQuestionData(sessionId: string): Promise<ServiceResult<QuestionPresentationData>> {
    try {
      // 获取会话信息
      const session = await this.quizSessionRepository.findById(sessionId);
      if (!session) {
        return {
          success: false,
          error: 'Session not found'
        };
      }
      if (session.status === 'COMPLETED') {
        return {
          success: false,
          error: 'Session already completed'
        };
      }

      // 获取当前问题
      const questionsResult = await this.quizPackRepository.getQuestionsByPackId(session.pack_id);
      if (!questionsResult.success || !questionsResult.data) {
        return {
          success: false,
          error: 'No questions found for this pack'
        };
      }

      const questions = questionsResult.data;
      const currentQuestion = questions[session.current_question_index];

      if (!currentQuestion) {
        return {
          success: false,
          error: 'Current question not found'
        };
      }

      // 获取问题选项
      const optionsResult = await this.quizPackRepository.getOptionsByQuestionId(currentQuestion.id);
      const options = optionsResult.success ? (optionsResult.data || []) : [];

      // 构建问题呈现数据
      const questionData: QuestionPresentationData = {
        session_id: sessionId,
        question_id: currentQuestion.id,
        question_text: currentQuestion.question_text,
        question_text_localized: currentQuestion.question_text_localized,
        question_type: currentQuestion.question_type,
        question_order: currentQuestion.question_order,
        tier_level: currentQuestion.tier_level || 1,

        question_options: options.map((option: any) => ({
          option_id: option.id,
          option_text: option.option_text,
          option_text_localized: option.option_text_localized,
          option_value: option.option_value,
          option_type: option.option_type,
          option_order: option.option_order,
          scoring_value: option.scoring_value,
          scoring_weight: option.scoring_weight,
          min_value: option.min_value,
          max_value: option.max_value,
          step_value: option.step_value,
          media_url: option.media_url,
          media_type: option.media_type,
          media_thumbnail_url: option.media_thumbnail_url,
          media_alt_text: option.media_alt_text,
          matrix_row_id: option.matrix_row_id,
          matrix_column_id: option.matrix_column_id,
          metadata: option.metadata ? JSON.parse(option.metadata) : undefined,
          tags: option.tags ? JSON.parse(option.tags) : undefined
        })),

        progress_info: {
          current_question: session.current_question_index + 1,
          total_questions: session.total_questions || 0,
          completion_percentage: session.completion_percentage || 0,
          estimated_remaining_time: this.calculateEstimatedRemainingTime(session)
        },

        navigation_config: {
          show_back_button: session.current_question_index > 0,
          show_skip_button: this.isSkipAllowed(currentQuestion),
          show_progress_indicator: true,
          allow_revision: true
        },

        question_config: currentQuestion.question_config ? JSON.parse(currentQuestion.question_config) : undefined,
        validation_rules: currentQuestion.validation_rules ? JSON.parse(currentQuestion.validation_rules) : undefined,
        scoring_config: currentQuestion.scoring_config ? JSON.parse(currentQuestion.scoring_config) : undefined
      };

      return {
        success: true,
        data: questionData
      };

    } catch (error) {
      return {
        success: false,
        error: `Failed to get current question: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // 私有辅助方法
  private calculateEstimatedRemainingTime(session: QuizSession): string {
    const remainingQuestions = (session.total_questions || 0) - session.current_question_index;
    const avgTimePerQuestion = 30; // 假设每题30秒
    const remainingSeconds = remainingQuestions * avgTimePerQuestion;

    if (remainingSeconds < 60) {
      return `${remainingSeconds}秒`;
    } else {
      const minutes = Math.ceil(remainingSeconds / 60);
      return `${minutes}分钟`;
    }
  }

  private isSkipAllowed(question: any): boolean {
    return !question.is_required;
  }

  protected async validateCreate(data: any): Promise<void> {
    // QuizSession创建验证
    if (!data.pack_id) {
      throw new Error('Pack ID is required');
    }
    if (!data.user_id) {
      throw new Error('User ID is required');
    }
  }

  protected async validateUpdate(_data: any): Promise<void> {
    // QuizSession更新验证
    // 基本验证即可
  }
}
