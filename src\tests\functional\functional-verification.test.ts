/**
 * 功能验证测试 (P3 中低优先级)
 * 验证系统各功能模块的正确性和完整性
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('功能验证测试 (P3)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('1. Quiz核心功能验证', () => {
    it('应该验证Quiz包加载功能', async () => {
      const mockQuizPackLoader = {
        loadQuizPack: vi.fn().mockResolvedValue({
          id: 'tcm-emotions',
          name: 'TCM情绪评估',
          description: '基于中医理论的情绪状态评估',
          version: '2.0.0',
          questions: [
            {
              id: 'q1',
              text: '您最近的情绪状态如何？',
              type: 'single_choice',
              options: [
                { id: 'opt1', text: '非常愉悦', value: 'very_happy' },
                { id: 'opt2', text: '比较平静', value: 'calm' },
                { id: 'opt3', text: '略有烦躁', value: 'irritated' }
              ]
            }
          ],
          metadata: {
            estimatedTime: '5-8分钟',
            difficulty: 'easy',
            category: 'emotion_assessment'
          }
        }),
        validateQuizPack: vi.fn().mockReturnValue({
          valid: true,
          errors: [],
          warnings: []
        }),
        getQuizPackList: vi.fn().mockResolvedValue([
          { id: 'tcm-emotions', name: 'TCM情绪评估', available: true },
          { id: 'western-psychology', name: '西方心理学评估', available: true },
          { id: 'mindfulness', name: '正念状态评估', available: false }
        ])
      };

      // 测试Quiz包加载
      const quizPack = await mockQuizPackLoader.loadQuizPack('tcm-emotions');
      expect(quizPack.id).toBe('tcm-emotions');
      expect(quizPack.questions).toHaveLength(1);
      expect(quizPack.questions[0].options).toHaveLength(3);

      // 测试Quiz包验证
      const validation = mockQuizPackLoader.validateQuizPack(quizPack);
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);

      // 测试Quiz包列表
      const quizList = await mockQuizPackLoader.getQuizPackList();
      expect(quizList).toHaveLength(3);
      expect(quizList.filter(q => q.available)).toHaveLength(2);
    });

    it('应该验证Quiz会话管理功能', async () => {
      const mockQuizSession = {
        createSession: vi.fn().mockResolvedValue({
          sessionId: 'session_123',
          userId: 'user123',
          quizPackId: 'tcm-emotions',
          status: 'created',
          startTime: new Date(),
          currentQuestionIndex: 0,
          answers: [],
          progress: 0
        }),
        startSession: vi.fn().mockResolvedValue({
          sessionId: 'session_123',
          status: 'in_progress',
          currentQuestion: {
            id: 'q1',
            text: '您最近的情绪状态如何？',
            options: [
              { id: 'opt1', text: '非常愉悦', value: 'very_happy' },
              { id: 'opt2', text: '比较平静', value: 'calm' }
            ]
          }
        }),
        submitAnswer: vi.fn().mockResolvedValue({
          sessionId: 'session_123',
          questionId: 'q1',
          answer: 'very_happy',
          score: 85,
          nextQuestion: {
            id: 'q2',
            text: '您的睡眠质量如何？'
          },
          progress: 0.1
        }),
        completeSession: vi.fn().mockResolvedValue({
          sessionId: 'session_123',
          status: 'completed',
          finalScore: 82,
          completionTime: 420000, // 7分钟
          results: {
            dominantEmotion: 'joy',
            emotionalBalance: 'good',
            recommendations: ['保持积极心态', '适当运动']
          }
        })
      };

      // 测试会话创建
      const session = await mockQuizSession.createSession('user123', 'tcm-emotions');
      expect(session.sessionId).toBe('session_123');
      expect(session.status).toBe('created');
      expect(session.progress).toBe(0);

      // 测试会话开始
      const startedSession = await mockQuizSession.startSession(session.sessionId);
      expect(startedSession.status).toBe('in_progress');
      expect(startedSession.currentQuestion).toBeDefined();

      // 测试答案提交
      const answerResult = await mockQuizSession.submitAnswer(session.sessionId, 'q1', 'very_happy');
      expect(answerResult.score).toBe(85);
      expect(answerResult.progress).toBe(0.1);
      expect(answerResult.nextQuestion).toBeDefined();

      // 测试会话完成
      const completion = await mockQuizSession.completeSession(session.sessionId);
      expect(completion.status).toBe('completed');
      expect(completion.finalScore).toBe(82);
      expect(completion.results.dominantEmotion).toBe('joy');
    });

    it('应该验证Quiz评分算法功能', async () => {
      const mockScoringEngine = {
        calculateQuestionScore: vi.fn().mockImplementation((question, answer) => {
          const scoringMap = {
            'very_happy': 90,
            'happy': 80,
            'calm': 70,
            'neutral': 60,
            'sad': 40,
            'very_sad': 20
          };
          return {
            questionId: question.id,
            answer: answer,
            rawScore: scoringMap[answer] || 50,
            weightedScore: (scoringMap[answer] || 50) * (question.weight || 1),
            explanation: `基于${answer}的情绪状态评分`
          };
        }),
        calculateTotalScore: vi.fn().mockImplementation((questionScores) => {
          const totalRaw = questionScores.reduce((sum, score) => sum + score.rawScore, 0);
          const totalWeighted = questionScores.reduce((sum, score) => sum + score.weightedScore, 0);
          const averageScore = totalRaw / questionScores.length;

          return {
            totalRawScore: totalRaw,
            totalWeightedScore: totalWeighted,
            averageScore: averageScore,
            normalizedScore: Math.round(averageScore),
            grade: averageScore >= 80 ? 'excellent' : averageScore >= 60 ? 'good' : 'needs_improvement'
          };
        }),
        generateEmotionalProfile: vi.fn().mockImplementation((scores) => {
          const dominantEmotion = scores.length > 0 && scores[0].rawScore >= 70 ? 'positive' : 'neutral';
          return {
            dominantEmotion: dominantEmotion,
            emotionalStability: scores.every(s => s.rawScore >= 50) ? 'stable' : 'unstable',
            balanceScore: scores.reduce((sum, s) => sum + s.rawScore, 0) / scores.length,
            recommendations: dominantEmotion === 'positive' ?
              ['保持当前状态', '继续积极生活'] :
              ['关注情绪调节', '寻求专业建议']
          };
        })
      };

      // 测试单题评分
      const question = { id: 'q1', weight: 1.2 };
      const questionScore = mockScoringEngine.calculateQuestionScore(question, 'very_happy');
      expect(questionScore.rawScore).toBe(90);
      expect(questionScore.weightedScore).toBe(108); // 90 * 1.2

      // 测试总分计算
      const questionScores = [
        { rawScore: 90, weightedScore: 108 },
        { rawScore: 80, weightedScore: 80 },
        { rawScore: 70, weightedScore: 70 }
      ];
      const totalScore = mockScoringEngine.calculateTotalScore(questionScores);
      expect(totalScore.averageScore).toBe(80); // (90+80+70)/3
      expect(totalScore.grade).toBe('excellent');

      // 测试情绪画像生成
      const profile = mockScoringEngine.generateEmotionalProfile(questionScores);
      expect(profile.dominantEmotion).toBe('positive');
      expect(profile.emotionalStability).toBe('stable');
      expect(profile.recommendations).toContain('保持当前状态');
    });
  });

  describe('2. 配置管理功能验证', () => {
    it('应该验证用户配置CRUD功能', async () => {
      const mockConfigManager = {
        createUserConfig: vi.fn().mockResolvedValue({
          configId: 'config_123',
          userId: 'user123',
          name: '我的个性化配置',
          layers: {
            layer_0: { theme: 'traditional' },
            layer_1: { emojiSet: 'tcm_style' },
            layer_2: { language: 'zh' },
            layer_3: { accessibility: true },
            layer_4: { animations: 'reduced' },
            layer_5: { notifications: 'enabled' }
          },
          created: true
        }),
        readUserConfig: vi.fn().mockResolvedValue({
          configId: 'config_123',
          userId: 'user123',
          name: '我的个性化配置',
          isActive: true,
          lastModified: new Date(),
          layers: {
            layer_0: { theme: 'traditional' },
            layer_1: { emojiSet: 'tcm_style' }
          }
        }),
        updateUserConfig: vi.fn().mockResolvedValue({
          configId: 'config_123',
          updated: true,
          changes: ['layer_1.emojiSet'],
          newValues: { 'layer_1.emojiSet': 'modern' },
          version: 2
        }),
        deleteUserConfig: vi.fn().mockResolvedValue({
          configId: 'config_123',
          deleted: true,
          backupCreated: true,
          backupId: 'backup_123'
        })
      };

      // 测试配置创建
      const createResult = await mockConfigManager.createUserConfig('user123', {
        name: '我的个性化配置',
        theme: 'traditional',
        emojiSet: 'tcm_style'
      });
      expect(createResult.created).toBe(true);
      expect(createResult.configId).toBe('config_123');
      expect(createResult.layers.layer_0.theme).toBe('traditional');

      // 测试配置读取
      const config = await mockConfigManager.readUserConfig('config_123');
      expect(config.userId).toBe('user123');
      expect(config.isActive).toBe(true);
      expect(config.layers.layer_1.emojiSet).toBe('tcm_style');

      // 测试配置更新
      const updateResult = await mockConfigManager.updateUserConfig('config_123', {
        'layer_1.emojiSet': 'modern'
      });
      expect(updateResult.updated).toBe(true);
      expect(updateResult.changes).toContain('layer_1.emojiSet');
      expect(updateResult.version).toBe(2);

      // 测试配置删除
      const deleteResult = await mockConfigManager.deleteUserConfig('config_123');
      expect(deleteResult.deleted).toBe(true);
      expect(deleteResult.backupCreated).toBe(true);
    });

    it('应该验证配置层级合并功能', async () => {
      const mockConfigMerger = {
        mergeConfigLayers: vi.fn().mockImplementation((baseLayers, userLayers, contextLayers) => {
          // 模拟配置层级合并逻辑
          const merged = { ...baseLayers };

          // 应用用户配置
          Object.keys(userLayers).forEach(layer => {
            merged[layer] = { ...merged[layer], ...userLayers[layer] };
          });

          // 应用上下文配置
          Object.keys(contextLayers).forEach(layer => {
            merged[layer] = { ...merged[layer], ...contextLayers[layer] };
          });

          return {
            mergedConfig: merged,
            appliedLayers: Object.keys({ ...userLayers, ...contextLayers }),
            conflicts: [],
            mergeStrategy: 'layer_priority'
          };
        }),
        validateConfigIntegrity: vi.fn().mockReturnValue({
          valid: true,
          missingLayers: [],
          invalidValues: [],
          warnings: []
        }),
        applyConfigToSession: vi.fn().mockResolvedValue({
          applied: true,
          sessionId: 'session_123',
          configSnapshot: {
            theme: 'traditional',
            emojiSet: 'tcm_style',
            language: 'zh'
          },
          effectiveTime: new Date()
        })
      };

      // 测试配置合并
      const baseLayers = {
        layer_0: { theme: 'default', emojiSet: 'default' },
        layer_1: { language: 'en' }
      };
      const userLayers = {
        layer_0: { theme: 'traditional' },
        layer_1: { language: 'zh' }
      };
      const contextLayers = {
        layer_0: { emojiSet: 'tcm_style' }
      };

      const mergeResult = mockConfigMerger.mergeConfigLayers(baseLayers, userLayers, contextLayers);
      expect(mergeResult.mergedConfig.layer_0.theme).toBe('traditional'); // 用户配置覆盖
      expect(mergeResult.mergedConfig.layer_0.emojiSet).toBe('tcm_style'); // 上下文配置覆盖
      expect(mergeResult.mergedConfig.layer_1.language).toBe('zh'); // 用户配置覆盖
      expect(mergeResult.appliedLayers).toContain('layer_0');

      // 测试配置完整性验证
      const validation = mockConfigMerger.validateConfigIntegrity(mergeResult.mergedConfig);
      expect(validation.valid).toBe(true);
      expect(validation.missingLayers).toHaveLength(0);

      // 测试配置应用到会话
      const application = await mockConfigMerger.applyConfigToSession('session_123', mergeResult.mergedConfig);
      expect(application.applied).toBe(true);
      expect(application.configSnapshot.theme).toBe('traditional');
    });

    it('应该验证配置导入导出功能', async () => {
      const mockConfigPortability = {
        exportConfig: vi.fn().mockResolvedValue({
          exportId: 'export_123',
          format: 'json',
          version: '2.0',
          data: {
            metadata: {
              exportDate: new Date().toISOString(),
              version: '2.0',
              userId: 'user123',
              configName: '我的配置'
            },
            layers: {
              layer_0: { theme: 'traditional', emojiSet: 'tcm_style' },
              layer_1: { language: 'zh', accessibility: true }
            },
            customizations: {
              emojiMappings: { 'joy': '😊', 'peace': '☯️' },
              colorScheme: { primary: '#8B4513', secondary: '#DAA520' }
            }
          },
          size: 2048, // bytes
          checksum: 'abc123def456'
        }),
        importConfig: vi.fn().mockImplementation((importData) => {
          return {
            importId: 'import_123',
            success: true,
            configId: 'config_imported_123',
            warnings: [],
            conflicts: [],
            migrationRequired: false,
            appliedChanges: [
              'theme updated to traditional',
              'emojiSet updated to tcm_style',
              'language updated to zh'
            ]
          };
        }),
        validateImportData: vi.fn().mockReturnValue({
          valid: true,
          version: '2.0',
          compatible: true,
          requiredMigrations: [],
          warnings: [],
          estimatedImportTime: 500 // ms
        })
      };

      // 测试配置导出
      const exportResult = await mockConfigPortability.exportConfig('user123', 'config_123');
      expect(exportResult.format).toBe('json');
      expect(exportResult.data.metadata.userId).toBe('user123');
      expect(exportResult.data.layers.layer_0.theme).toBe('traditional');
      expect(exportResult.checksum).toBeTruthy();

      // 测试导入数据验证
      const validation = mockConfigPortability.validateImportData(exportResult.data);
      expect(validation.valid).toBe(true);
      expect(validation.compatible).toBe(true);
      expect(validation.estimatedImportTime).toBeLessThan(1000);

      // 测试配置导入
      const importResult = mockConfigPortability.importConfig(exportResult.data);
      expect(importResult.success).toBe(true);
      expect(importResult.configId).toBe('config_imported_123');
      expect(importResult.appliedChanges).toHaveLength(3);
      expect(importResult.conflicts).toHaveLength(0);
    });
  });

  describe('3. 数据存储功能验证', () => {
    it('应该验证本地存储功能', async () => {
      const mockLocalStorage = {
        saveData: vi.fn().mockImplementation((key, data) => {
          return {
            saved: true,
            key: key,
            size: JSON.stringify(data).length,
            timestamp: new Date(),
            compressed: JSON.stringify(data).length > 1024
          };
        }),
        loadData: vi.fn().mockImplementation((key) => {
          const mockData = {
            'user_config': { theme: 'traditional', emojiSet: 'tcm_style' },
            'quiz_progress': { sessionId: 'session_123', currentQuestion: 5 },
            'offline_results': [{ quizId: 'quiz1', score: 85 }]
          };
          return {
            found: key in mockData,
            data: mockData[key] || null,
            lastModified: new Date(),
            size: mockData[key] ? JSON.stringify(mockData[key]).length : 0
          };
        }),
        deleteData: vi.fn().mockReturnValue({
          deleted: true,
          key: 'test_key',
          freedSpace: 1024
        }),
        clearStorage: vi.fn().mockReturnValue({
          cleared: true,
          itemsRemoved: 15,
          spaceFreed: 15360 // bytes
        })
      };

      // 测试数据保存
      const saveResult = mockLocalStorage.saveData('user_config', { theme: 'traditional' });
      expect(saveResult.saved).toBe(true);
      expect(saveResult.size).toBeGreaterThan(0);

      // 测试数据加载
      const loadResult = mockLocalStorage.loadData('user_config');
      expect(loadResult.found).toBe(true);
      expect(loadResult.data.theme).toBe('traditional');

      // 测试数据删除
      const deleteResult = mockLocalStorage.deleteData('test_key');
      expect(deleteResult.deleted).toBe(true);
      expect(deleteResult.freedSpace).toBe(1024);

      // 测试存储清理
      const clearResult = mockLocalStorage.clearStorage();
      expect(clearResult.cleared).toBe(true);
      expect(clearResult.itemsRemoved).toBe(15);
    });

    it('应该验证数据同步功能', async () => {
      const mockDataSync = {
        syncToCloud: vi.fn().mockResolvedValue({
          synced: true,
          syncId: 'sync_123',
          itemsSynced: 8,
          conflicts: 0,
          syncTime: 1500, // ms
          lastSyncTimestamp: new Date()
        }),
        syncFromCloud: vi.fn().mockResolvedValue({
          synced: true,
          itemsReceived: 5,
          itemsUpdated: 3,
          conflicts: 1,
          conflictResolution: 'server_wins',
          downloadTime: 800
        }),
        resolveConflicts: vi.fn().mockResolvedValue({
          resolved: true,
          conflictsCount: 1,
          resolutionStrategy: 'merge',
          resolvedItems: [
            {
              itemId: 'config_123',
              conflictType: 'concurrent_modification',
              resolution: 'merged_changes',
              finalValue: { theme: 'traditional', emojiSet: 'modern' }
            }
          ]
        }),
        getLastSyncStatus: vi.fn().mockReturnValue({
          lastSync: new Date(Date.now() - 3600000), // 1小时前
          status: 'success',
          itemsInSync: 12,
          pendingItems: 2,
          nextScheduledSync: new Date(Date.now() + 3600000) // 1小时后
        })
      };

      // 测试云端同步
      const cloudSync = await mockDataSync.syncToCloud();
      expect(cloudSync.synced).toBe(true);
      expect(cloudSync.itemsSynced).toBe(8);
      expect(cloudSync.conflicts).toBe(0);
      expect(cloudSync.syncTime).toBeLessThan(3000);

      // 测试从云端同步
      const fromCloudSync = await mockDataSync.syncFromCloud();
      expect(fromCloudSync.synced).toBe(true);
      expect(fromCloudSync.itemsReceived).toBe(5);
      expect(fromCloudSync.conflicts).toBe(1);

      // 测试冲突解决
      const conflictResolution = await mockDataSync.resolveConflicts();
      expect(conflictResolution.resolved).toBe(true);
      expect(conflictResolution.resolvedItems).toHaveLength(1);
      expect(conflictResolution.resolvedItems[0].resolution).toBe('merged_changes');

      // 测试同步状态查询
      const syncStatus = mockDataSync.getLastSyncStatus();
      expect(syncStatus.status).toBe('success');
      expect(syncStatus.itemsInSync).toBe(12);
      expect(syncStatus.pendingItems).toBe(2);
    });
  });

  describe('4. 用户界面功能验证', () => {
    it('应该验证主题切换功能', async () => {
      const mockThemeManager = {
        getAvailableThemes: vi.fn().mockReturnValue([
          { id: 'light', name: '浅色主题', preview: '#ffffff' },
          { id: 'dark', name: '深色主题', preview: '#1a1a1a' },
          { id: 'traditional', name: '传统主题', preview: '#8B4513' },
          { id: 'modern', name: '现代主题', preview: '#2196F3' }
        ]),
        applyTheme: vi.fn().mockImplementation((themeId) => {
          const themes = {
            'light': { background: '#ffffff', text: '#000000', primary: '#2196F3' },
            'dark': { background: '#1a1a1a', text: '#ffffff', primary: '#bb86fc' },
            'traditional': { background: '#f5f5dc', text: '#8B4513', primary: '#DAA520' },
            'modern': { background: '#fafafa', text: '#212121', primary: '#2196F3' }
          };
          return {
            applied: true,
            themeId: themeId,
            colors: themes[themeId],
            transitionTime: 300,
            cssVariablesUpdated: true
          };
        }),
        previewTheme: vi.fn().mockReturnValue({
          previewing: true,
          previewId: 'preview_123',
          duration: 5000, // 5秒预览
          revertable: true
        }),
        revertTheme: vi.fn().mockReturnValue({
          reverted: true,
          previousTheme: 'light',
          currentTheme: 'light'
        })
      };

      // 测试获取可用主题
      const themes = mockThemeManager.getAvailableThemes();
      expect(themes).toHaveLength(4);
      expect(themes.find(t => t.id === 'traditional')).toBeDefined();

      // 测试主题应用
      const applyResult = mockThemeManager.applyTheme('traditional');
      expect(applyResult.applied).toBe(true);
      expect(applyResult.colors.background).toBe('#f5f5dc');
      expect(applyResult.transitionTime).toBe(300);

      // 测试主题预览
      const previewResult = mockThemeManager.previewTheme('dark');
      expect(previewResult.previewing).toBe(true);
      expect(previewResult.revertable).toBe(true);

      // 测试主题恢复
      const revertResult = mockThemeManager.revertTheme();
      expect(revertResult.reverted).toBe(true);
      expect(revertResult.currentTheme).toBe('light');
    });

    it('应该验证Emoji映射功能', async () => {
      const mockEmojiMapper = {
        getEmojiSets: vi.fn().mockReturnValue([
          {
            id: 'default',
            name: '默认表情',
            mappings: { 'joy': '😊', 'sad': '😢', 'anger': '😠' }
          },
          {
            id: 'tcm_style',
            name: '中医风格',
            mappings: { 'joy': '🎉', 'peace': '☯️', 'energy': '⚡' }
          },
          {
            id: 'minimal',
            name: '简约风格',
            mappings: { 'positive': '●', 'neutral': '○', 'negative': '◐' }
          }
        ]),
        applyEmojiSet: vi.fn().mockImplementation((setId) => {
          return {
            applied: true,
            setId: setId,
            mappingsCount: setId === 'tcm_style' ? 3 : 3,
            customMappings: 0,
            renderingUpdated: true
          };
        }),
        customizeEmoji: vi.fn().mockImplementation((emotion, emoji) => {
          return {
            customized: true,
            emotion: emotion,
            oldEmoji: '😊',
            newEmoji: emoji,
            saved: true,
            previewAvailable: true
          };
        }),
        validateEmojiMapping: vi.fn().mockReturnValue({
          valid: true,
          supportedEmotions: ['joy', 'sad', 'anger', 'peace', 'energy'],
          missingMappings: [],
          invalidEmojis: []
        })
      };

      // 测试获取表情集
      const emojiSets = mockEmojiMapper.getEmojiSets();
      expect(emojiSets).toHaveLength(3);
      expect(emojiSets.find(s => s.id === 'tcm_style')).toBeDefined();

      // 测试应用表情集
      const applyResult = mockEmojiMapper.applyEmojiSet('tcm_style');
      expect(applyResult.applied).toBe(true);
      expect(applyResult.mappingsCount).toBe(3);
      expect(applyResult.renderingUpdated).toBe(true);

      // 测试自定义表情
      const customResult = mockEmojiMapper.customizeEmoji('joy', '🎊');
      expect(customResult.customized).toBe(true);
      expect(customResult.newEmoji).toBe('🎊');
      expect(customResult.saved).toBe(true);

      // 测试表情映射验证
      const validation = mockEmojiMapper.validateEmojiMapping();
      expect(validation.valid).toBe(true);
      expect(validation.missingMappings).toHaveLength(0);
    });

    it('应该验证多语言功能', async () => {
      const mockI18n = {
        getSupportedLanguages: vi.fn().mockReturnValue([
          { code: 'zh', name: '中文', nativeName: '中文', coverage: 100 },
          { code: 'en', name: 'English', nativeName: 'English', coverage: 100 },
          { code: 'ja', name: 'Japanese', nativeName: '日本語', coverage: 85 },
          { code: 'ko', name: 'Korean', nativeName: '한국어', coverage: 80 }
        ]),
        switchLanguage: vi.fn().mockImplementation((langCode) => {
          return {
            switched: true,
            language: langCode,
            loadTime: 200,
            stringsLoaded: langCode === 'zh' ? 1250 : 1180,
            fallbacksUsed: langCode === 'ja' ? 15 : 0
          };
        }),
        translateText: vi.fn().mockImplementation((key, lang) => {
          const translations = {
            'zh': { 'quiz.start': '开始测验', 'quiz.submit': '提交答案' },
            'en': { 'quiz.start': 'Start Quiz', 'quiz.submit': 'Submit Answer' },
            'ja': { 'quiz.start': 'クイズを開始', 'quiz.submit': '答えを提出' }
          };
          return {
            translated: true,
            key: key,
            text: translations[lang]?.[key] || key,
            fallback: !translations[lang]?.[key]
          };
        }),
        detectLanguage: vi.fn().mockReturnValue({
          detected: 'zh',
          confidence: 0.95,
          alternatives: ['en', 'ja'],
          source: 'browser_locale'
        })
      };

      // 测试获取支持的语言
      const languages = mockI18n.getSupportedLanguages();
      expect(languages).toHaveLength(4);
      expect(languages.find(l => l.code === 'zh').coverage).toBe(100);

      // 测试语言切换
      const switchResult = mockI18n.switchLanguage('zh');
      expect(switchResult.switched).toBe(true);
      expect(switchResult.stringsLoaded).toBe(1250);
      expect(switchResult.fallbacksUsed).toBe(0);

      // 测试文本翻译
      const translation = mockI18n.translateText('quiz.start', 'zh');
      expect(translation.translated).toBe(true);
      expect(translation.text).toBe('开始测验');
      expect(translation.fallback).toBe(false);

      // 测试语言检测
      const detection = mockI18n.detectLanguage();
      expect(detection.detected).toBe('zh');
      expect(detection.confidence).toBeGreaterThan(0.9);
    });
  });

  describe('5. 报告生成功能验证', () => {
    it('应该验证Quiz结果报告生成', async () => {
      const mockReportGenerator = {
        generateBasicReport: vi.fn().mockResolvedValue({
          reportId: 'report_123',
          type: 'basic',
          userId: 'user123',
          quizSessionId: 'session_123',
          generatedAt: new Date(),
          content: {
            summary: {
              totalScore: 85,
              completionTime: '7分钟',
              dominantEmotion: 'joy',
              emotionalBalance: 'good'
            },
            details: {
              questionScores: [
                { questionId: 'q1', score: 90, emotion: 'joy' },
                { questionId: 'q2', score: 80, emotion: 'peace' }
              ],
              emotionalProfile: {
                strengths: ['积极心态', '情绪稳定'],
                improvements: ['压力管理'],
                recommendations: ['保持运动', '规律作息']
              }
            }
          },
          format: 'json',
          size: 2048
        }),
        generateDetailedReport: vi.fn().mockResolvedValue({
          reportId: 'report_detailed_123',
          type: 'detailed',
          content: {
            tcmAnalysis: {
              constitution: '气郁质倾向',
              emotionalPattern: '肝气郁结，心神不宁',
              recommendations: ['疏肝理气', '养心安神'],
              acupoints: ['太冲', '神门', '百会']
            },
            trendAnalysis: {
              historicalComparison: '较上次提升15%',
              progressTrend: 'improving',
              consistencyScore: 0.85
            },
            actionPlan: {
              shortTerm: ['每日冥想10分钟', '增加户外活动'],
              longTerm: ['建立情绪日记习惯', '学习压力管理技巧'],
              followUp: '建议2周后重新评估'
            }
          }
        }),
        exportReport: vi.fn().mockImplementation((reportId, format) => {
          return {
            exported: true,
            reportId: reportId,
            format: format,
            downloadUrl: `https://reports.example.com/${reportId}.${format}`,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时
            fileSize: format === 'pdf' ? 1024000 : 512000 // bytes
          };
        }),
        shareReport: vi.fn().mockReturnValue({
          shared: true,
          shareId: 'share_123',
          shareUrl: 'https://share.example.com/report/share_123',
          permissions: ['view'],
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天
          accessCode: 'ABC123'
        })
      };

      // 测试基础报告生成
      const basicReport = await mockReportGenerator.generateBasicReport('session_123');
      expect(basicReport.type).toBe('basic');
      expect(basicReport.content.summary.totalScore).toBe(85);
      expect(basicReport.content.details.questionScores).toHaveLength(2);

      // 测试详细报告生成
      const detailedReport = await mockReportGenerator.generateDetailedReport('session_123');
      expect(detailedReport.type).toBe('detailed');
      expect(detailedReport.content.tcmAnalysis.constitution).toBe('气郁质倾向');
      expect(detailedReport.content.actionPlan.shortTerm).toHaveLength(2);

      // 测试报告导出
      const exportResult = mockReportGenerator.exportReport('report_123', 'pdf');
      expect(exportResult.exported).toBe(true);
      expect(exportResult.format).toBe('pdf');
      expect(exportResult.downloadUrl).toContain('.pdf');

      // 测试报告分享
      const shareResult = mockReportGenerator.shareReport('report_123');
      expect(shareResult.shared).toBe(true);
      expect(shareResult.shareUrl).toBeTruthy();
      expect(shareResult.accessCode).toBe('ABC123');
    });
  });
});