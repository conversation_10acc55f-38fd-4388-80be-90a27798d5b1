/**
 * Quiz对话组件
 * 支持多种中医文化样式的对话组件
 */

import React, { useState, useEffect, useRef } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { DialogueComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type DialogueComponentConfig = z.infer<typeof DialogueComponentConfigSchema>;

export interface DialogueMessage {
  id: string;
  speaker: 'user' | 'npc' | 'system';
  content: Record<string, string>;
  timestamp?: number;
  avatar?: string;
  emotion?: string;
}

export interface DialogueOption {
  id: string;
  text: Record<string, string>;
  value: string | number;
  disabled?: boolean;
}

export interface DialogueComponentProps extends BaseQuizComponentProps<DialogueComponentConfig> {
  messages: DialogueMessage[];
  options?: DialogueOption[];
  onOptionSelect?: (option: DialogueOption) => void;
  onMessageSend?: (message: string) => void;
  isTyping?: boolean;
  disabled?: boolean;
}

interface DialogueComponentState extends ComponentState {
  displayed_messages: DialogueMessage[];
  current_input: string;
  is_typing_animation: boolean;
  typing_message_id: string | null;
  auto_scroll: boolean;
}

/**
 * 对话组件类
 */
export class DialogueComponent extends BaseQuizComponent<
  DialogueComponentConfig,
  DialogueComponentProps,
  DialogueComponentState
> {
  private messagesRef = React.createRef<HTMLDivElement>();
  private inputRef = React.createRef<HTMLInputElement>();
  private typingTimeoutId?: number;

  extractConfig(props: DialogueComponentProps): DialogueComponentConfig {
    return props.config;
  }

  getInitialState(): DialogueComponentState {
    return {
      is_loading: false,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      displayed_messages: [...this.props.messages],
      current_input: '',
      is_typing_animation: false,
      typing_message_id: null,
      auto_scroll: true
    };
  }

  componentDidMount(): void {
    this.scrollToBottom();
  }

  componentDidUpdate(prevProps: DialogueComponentProps, prevState: DialogueComponentState): void {
    super.componentDidUpdate(prevProps);
    
    // 处理新消息
    if (prevProps.messages !== this.props.messages) {
      const newMessages = this.props.messages.slice(prevProps.messages.length);
      
      if (newMessages.length > 0 && this.config.dialogue.animated_typing) {
        this.animateNewMessages(newMessages);
      } else {
        this.setState({ displayed_messages: [...this.props.messages] });
      }
    }
    
    // 自动滚动到底部
    if (prevState.displayed_messages.length !== this.state.displayed_messages.length && this.state.auto_scroll) {
      this.scrollToBottom();
    }
    
    if (prevProps.disabled !== this.props.disabled) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled
      });
    }
  }

  componentWillUnmount(): void {
    if (this.typingTimeoutId) {
      clearTimeout(this.typingTimeoutId);
    }
  }

  /**
   * 动画显示新消息
   */
  private animateNewMessages(newMessages: DialogueMessage[]): void {
    if (this.personalization.layer5_accessibility?.reduce_motion) {
      this.setState({ displayed_messages: [...this.props.messages] });
      return;
    }

    let messageIndex = 0;
    
    const animateNextMessage = () => {
      if (messageIndex >= newMessages.length) return;
      
      const message = newMessages[messageIndex];
      const messageText = this.getMessageText(message);
      
      // 开始打字动画
      this.setState({
        is_typing_animation: true,
        typing_message_id: message.id
      });
      
      // 模拟打字速度
      const typingDelay = Math.min(messageText.length * 50, 2000);
      
      this.typingTimeoutId = window.setTimeout(() => {
        this.setState(prevState => ({
          displayed_messages: [...prevState.displayed_messages, message],
          is_typing_animation: false,
          typing_message_id: null
        }));
        
        messageIndex++;
        
        // 继续下一条消息
        if (messageIndex < newMessages.length) {
          setTimeout(animateNextMessage, 500);
        }
      }, typingDelay);
    };
    
    animateNextMessage();
  }

  /**
   * 滚动到底部
   */
  private scrollToBottom(): void {
    const messagesContainer = this.messagesRef.current;
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  }

  /**
   * 处理选项选择
   */
  private handleOptionSelect = (option: DialogueOption): void => {
    if (this.state.is_disabled || option.disabled) return;

    this.props.onOptionSelect?.(option);

    // 触发触觉反馈
    this.triggerHapticFeedback('light');

    // 发送交互事件
    this.emitInteractionEvent('click', {
      action: 'option_selected',
      option_id: option.id,
      option_value: option.value
    });
  };

  /**
   * 处理输入变化
   */
  private handleInputChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    this.setState({ current_input: event.target.value });
  };

  /**
   * 处理消息发送
   */
  private handleMessageSend = (): void => {
    const message = this.state.current_input.trim();
    if (!message || this.state.is_disabled) return;

    this.props.onMessageSend?.(message);
    this.setState({ current_input: '' });

    // 触发触觉反馈
    this.triggerHapticFeedback('medium');

    // 发送交互事件
    this.emitInteractionEvent('click', {
      action: 'message_sent',
      message_content: message
    });
  };

  /**
   * 处理键盘事件
   */
  private handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>): void => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.handleMessageSend();
    }
  };

  /**
   * 获取消息文本
   */
  private getMessageText(message: DialogueMessage): string {
    const { language } = this.context || { language: 'zh' };
    return message.content[language] || message.content['zh'] || message.content['en'] || '';
  }

  /**
   * 获取选项文本
   */
  private getOptionText(option: DialogueOption): string {
    const { language } = this.context || { language: 'zh' };
    return option.text[language] || option.text['zh'] || option.text['en'] || '';
  }

  /**
   * 获取对话样式类名
   */
  private getDialogueStyleClassName(): string {
    const style = this.config.dialogue.dialogue_style;
    return `quiz-dialogue-${style}`;
  }

  /**
   * 格式化时间戳
   */
  private formatTimestamp(timestamp?: number): string {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  /**
   * 渲染消息
   */
  private renderMessage = (message: DialogueMessage): React.ReactNode => {
    const messageText = this.getMessageText(message);
    const isTyping = this.state.typing_message_id === message.id;
    
    return (
      <div
        key={message.id}
        className={`
          quiz-dialogue-message
          quiz-dialogue-message-${message.speaker}
          ${isTyping ? 'quiz-dialogue-message-typing' : ''}
        `.trim()}
      >
        {/* 头像 */}
        {this.config.dialogue.show_avatars && message.avatar && (
          <div className="quiz-dialogue-message-avatar">
            <img src={message.avatar} alt={`${message.speaker} avatar`} />
          </div>
        )}

        {/* 消息内容 */}
        <div className="quiz-dialogue-message-content">
          {/* 说话者名称 */}
          {this.config.dialogue.show_speaker_names && message.speaker !== 'user' && (
            <div className="quiz-dialogue-message-speaker">
              {message.speaker === 'system' ? 'System' : 'NPC'}
            </div>
          )}

          {/* 消息气泡 */}
          <div className="quiz-dialogue-message-bubble">
            {isTyping ? (
              <div className="quiz-dialogue-typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            ) : (
              messageText
            )}
          </div>

          {/* 时间戳 */}
          {this.config.dialogue.show_timestamps && message.timestamp && (
            <div className="quiz-dialogue-message-timestamp">
              {this.formatTimestamp(message.timestamp)}
            </div>
          )}
        </div>
      </div>
    );
  };

  /**
   * 渲染对话选项
   */
  private renderOptions(): React.ReactNode {
    if (!this.props.options || this.props.options.length === 0) return null;

    return (
      <div className="quiz-dialogue-options">
        {this.props.options.map(option => (
          <button
            key={option.id}
            className={`
              quiz-dialogue-option
              ${option.disabled ? 'quiz-dialogue-option-disabled' : ''}
            `.trim()}
            onClick={() => this.handleOptionSelect(option)}
            disabled={this.state.is_disabled || option.disabled}
          >
            {this.getOptionText(option)}
          </button>
        ))}
      </div>
    );
  }

  /**
   * 渲染输入区域
   */
  private renderInputArea(): React.ReactNode {
    if (!this.config.dialogue.allow_user_input) return null;

    return (
      <div className="quiz-dialogue-input-area">
        <input
          ref={this.inputRef}
          type="text"
          value={this.state.current_input}
          onChange={this.handleInputChange}
          onKeyPress={this.handleKeyPress}
          placeholder={
            this.context?.language === 'zh' 
              ? '输入您的回复...' 
              : 'Type your reply...'
          }
          disabled={this.state.is_disabled || this.props.isTyping}
          className="quiz-dialogue-input"
        />
        
        <button
          onClick={this.handleMessageSend}
          disabled={
            this.state.is_disabled || 
            this.props.isTyping || 
            !this.state.current_input.trim()
          }
          className="quiz-dialogue-send-button"
          aria-label="Send message"
        >
          📤
        </button>
      </div>
    );
  }

  /**
   * 渲染打字指示器
   */
  private renderTypingIndicator(): React.ReactNode {
    if (!this.props.isTyping) return null;

    return (
      <div className="quiz-dialogue-typing-status">
        <div className="quiz-dialogue-typing-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span className="quiz-dialogue-typing-text">
          {this.context?.language === 'zh' ? '正在输入...' : 'Typing...'}
        </span>
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-dialogue-component',
      this.getDialogueStyleClassName(),
      this.state.is_disabled && 'quiz-dialogue-disabled',
      this.state.is_typing_animation && 'quiz-dialogue-animating',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
        role="log"
        aria-label="Dialogue conversation"
        aria-live="polite"
      >
        {/* 消息列表 */}
        <div 
          ref={this.messagesRef}
          className="quiz-dialogue-messages"
          onScroll={(e) => {
            const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
            const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
            this.setState({ auto_scroll: isAtBottom });
          }}
        >
          {this.state.displayed_messages.map(this.renderMessage)}
          
          {/* 打字指示器 */}
          {this.renderTypingIndicator()}
        </div>

        {/* 对话选项 */}
        {this.renderOptions()}

        {/* 输入区域 */}
        {this.renderInputArea()}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'log';
  }

  protected getAriaLabel(): string {
    return `Dialogue with ${this.state.displayed_messages.length} messages`;
  }
}

// 使用Context的函数式组件包装器
const DialogueComponentWrapper: React.FC<DialogueComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <DialogueComponent.contextType.Provider value={{ language }}>
      <DialogueComponent {...props} />
    </DialogueComponent.contextType.Provider>
  );
};

// 设置Context类型
DialogueComponent.contextType = React.createContext({ language: 'zh' });

export default DialogueComponentWrapper;
