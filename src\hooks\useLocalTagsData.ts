import type { Tag } from '@/types';
import { useEffect, useState } from 'react';
import { useHybridTagsData } from './useHybridData';

interface UseLocalTagsDataReturn {
  commonTags: Tag[];
  suggestedTags: Tag[];
  allTags: Tag[];
  isLoading: boolean;
  error: Error | null;
  isOnline: boolean;
  lastSyncTime: Date | null;
  refreshTags: () => Promise<void>;
  forceSync: () => Promise<void>;
}

/**
 * 标签数据钩子
 * 根据离线在线实现计划，优先从服务端获取，在线不可用时使用离线数据
 */
export const useLocalTagsData = (): UseLocalTagsDataReturn => {
  const [commonTags, setCommonTags] = useState<Tag[]>([]);
  const [suggestedTags, setSuggestedTags] = useState<Tag[]>([]);

  // 使用混合数据获取钩子
  const {
    data: allTags,
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    refresh,
    forceSync,
  } = useHybridTagsData();

  // 当标签数据更新时，重新分类
  useEffect(() => {
    if (allTags && Array.isArray(allTags)) {
      // 分离常用标签和建议标签
      const common = allTags.filter((tag: Tag) => tag.usage_count && tag.usage_count > 5);
      const suggested = allTags.filter((tag: Tag) => !tag.usage_count || tag.usage_count <= 5);

      setCommonTags(common);
      setSuggestedTags(suggested);
    } else {
      setCommonTags([]);
      setSuggestedTags([]);
    }
  }, [allTags]);

  return {
    commonTags,
    suggestedTags,
    allTags: allTags || [],
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    refreshTags: refresh,
    forceSync,
  };
};
