import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import { useNavigate } from 'react-router-dom';

const NotFound = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 text-center">
      <div className="text-6xl mb-4">😵</div>
      <h1 className="text-3xl font-bold mb-2">404</h1>
      <p className="text-xl text-muted-foreground mb-8">{t('error.not_found')}</p>
      <Button onClick={() => navigate('/')}>{t('error.go_home')}</Button>
    </div>
  );
};

export default NotFound;
