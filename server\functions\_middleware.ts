// 使用泛型接口替代 Context
interface Context<T = any> {
  env: T;
  request: Request;
  next: () => Promise<Response>;
}

export interface Env {
  TURSO_DB_URL: string;
  TURSO_AUTH_TOKEN: string;
  ENVIRONMENT: string;
}

/**
 * Get CORS headers based on environment
 * @param env The environment variables
 * @returns CORS headers appropriate for the environment
 */
const getCorsHeaders = (env: Env) => {
  // In production, restrict to specific origins
  const isProd = env.ENVIRONMENT === 'production';

  return {
    'Access-Control-Allow-Origin': isProd
      ? 'https://mindful-mood-mobile.pages.dev' // Replace with your actual production domain
      : '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, trpc-batch-mode',
    'Access-Control-Max-Age': '86400', // 24 hours
    'Access-Control-Allow-Credentials': 'true',
  };
};

export const onRequest = async (context: Context<Env>) => {
  // Get CORS headers for the current environment
  const corsHeaders = getCorsHeaders(context.env);

  // Handle preflight OPTIONS requests
  if (context.request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    });
  }

  // Set environment variables for services
  process.env.TURSO_DB_URL = context.env.TURSO_DB_URL;
  process.env.TURSO_AUTH_TOKEN = context.env.TURSO_AUTH_TOKEN;
  process.env.ENVIRONMENT = context.env.ENVIRONMENT;

  try {
    // Process the request
    const response = await context.next();

    // Add CORS headers to the response
    const newHeaders = new Headers(response.headers);
    Object.entries(corsHeaders).forEach(([key, value]) => {
      newHeaders.set(key, value);
    });

    return new Response(response.body, {
      status: response.status,
      headers: newHeaders,
    });
  } catch (error) {
    // Handle any errors that occur during request processing
    console.error('Error in middleware:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }
};
