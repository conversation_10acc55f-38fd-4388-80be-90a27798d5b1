-- Test User Data
-- Sample users with different profiles and configurations

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- TEST USERS
-- ============================================================================

-- Test User 1: Premium VIP User (<PERSON>)
INSERT OR IGNORE INTO users (
    id, username, email, display_name, avatar_url, is_active, is_verified, is_vip, vip_tier,
    vip_expires_at, last_login_at, login_count, last_active_at, timezone, locale, created_at, updated_at, sync_status
) VALUES
    (
        'test-user-1',
        'john_doe',
        '<EMAIL>',
        '<PERSON>',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
        1, 1, 1, 'premium',
        datetime('now', '+1 year'),
        datetime('now', '-1 hour'),
        25,
        datetime('now', '-30 minutes'),
        'America/New_York',
        'en',
        datetime('now', '-30 days'),
        datetime('now', '-1 hour'),
        'synced'
    );

-- Test User 2: Regular User (Jane Smith)
INSERT OR IGNORE INTO users (
    id, username, email, display_name, avatar_url, is_active, is_verified, is_vip, vip_tier,
    vip_expires_at, last_login_at, login_count, last_active_at, timezone, locale, created_at, updated_at, sync_status
) VALUES
    (
        'test-user-2',
        'jane_smith',
        '<EMAIL>',
        'Jane Smith',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=jane',
        1, 1, 0, NULL,
        NULL,
        datetime('now', '-2 hours'),
        15,
        datetime('now', '-1 hour'),
        'Asia/Shanghai',
        'zh',
        datetime('now', '-15 days'),
        datetime('now', '-2 hours'),
        'synced'
    );

-- Test User 3: New User with Accessibility Needs (Alex Chen)
INSERT OR IGNORE INTO users (
    id, username, email, display_name, avatar_url, is_active, is_verified, is_vip, vip_tier,
    vip_expires_at, last_login_at, login_count, last_active_at, timezone, locale, created_at, updated_at, sync_status
) VALUES
    (
        'test-user-3',
        'alex_chen',
        '<EMAIL>',
        'Alex Chen',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=alex',
        1, 0, 0, NULL,
        NULL,
        datetime('now', '-1 day'),
        3,
        datetime('now', '-6 hours'),
        'Europe/London',
        'en',
        datetime('now', '-3 days'),
        datetime('now', '-1 day'),
        'synced'
    );

-- Test User 4: Advanced User (Maria Garcia)
INSERT OR IGNORE INTO users (
    id, username, email, display_name, avatar_url, is_active, is_verified, is_vip, vip_tier,
    vip_expires_at, last_login_at, login_count, last_active_at, timezone, locale, created_at, updated_at, sync_status
) VALUES
    (
        'test-user-4',
        'maria_garcia',
        '<EMAIL>',
        'Maria Garcia',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=maria',
        1, 1, 1, 'basic',
        datetime('now', '+6 months'),
        datetime('now', '-3 hours'),
        42,
        datetime('now', '-2 hours'),
        'Europe/Madrid',
        'es',
        datetime('now', '-60 days'),
        datetime('now', '-3 hours'),
        'synced'
    );

-- Test User 5: Power User (David Kim)
INSERT OR IGNORE INTO users (
    id, username, email, display_name, avatar_url, is_active, is_verified, is_vip, vip_tier,
    vip_expires_at, last_login_at, login_count, last_active_at, timezone, locale, created_at, updated_at, sync_status
) VALUES
    (
        'test-user-5',
        'david_kim',
        '<EMAIL>',
        'David Kim',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=david',
        1, 1, 1, 'enterprise',
        datetime('now', '+2 years'),
        datetime('now', '-30 minutes'),
        156,
        datetime('now', '-15 minutes'),
        'Asia/Seoul',
        'ko',
        datetime('now', '-120 days'),
        datetime('now', '-30 minutes'),
        'synced'
    );

-- ============================================================================
-- USER STREAKS
-- ============================================================================

INSERT OR IGNORE INTO user_streaks (id, user_id, current_streak, longest_streak, last_entry_date, streak_type) VALUES
    ('streak-user-1', 'test-user-1', 7, 12, date('now'), 'daily'),
    ('streak-user-2', 'test-user-2', 3, 5, date('now'), 'daily'),
    ('streak-user-3', 'test-user-3', 0, 1, date('now', '-2 days'), 'daily'),
    ('streak-user-4', 'test-user-4', 14, 21, date('now'), 'daily'),
    ('streak-user-5', 'test-user-5', 28, 45, date('now'), 'daily');

-- ============================================================================
-- GLOBAL APP CONFIGS
-- ============================================================================

-- User 1: Premium user with system theme
INSERT OR IGNORE INTO global_app_configs (
    id, user_id, config_name, theme_mode, language, notifications_enabled, sound_enabled,
    accessibility, is_active, is_default, created_at, updated_at
) VALUES
    (
        'global-config-user-1',
        'test-user-1',
        'default',
        'system',
        'en',
        1, 1,
        '{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}',
        1, 1,
        datetime('now', '-30 days'),
        datetime('now', '-1 hour')
    );

-- User 2: Regular user with dark theme and Chinese language
INSERT OR IGNORE INTO global_app_configs (
    id, user_id, config_name, theme_mode, language, notifications_enabled, sound_enabled,
    accessibility, is_active, is_default, created_at, updated_at
) VALUES
    (
        'global-config-user-2',
        'test-user-2',
        'default',
        'dark',
        'zh',
        1, 0,
        '{"high_contrast": true, "large_text": false, "reduce_motion": true, "screen_reader_support": false}',
        1, 1,
        datetime('now', '-15 days'),
        datetime('now', '-2 hours')
    );

-- User 3: Accessibility user with high contrast and large text
INSERT OR IGNORE INTO global_app_configs (
    id, user_id, config_name, theme_mode, language, notifications_enabled, sound_enabled,
    accessibility, is_active, is_default, created_at, updated_at
) VALUES
    (
        'global-config-user-3',
        'test-user-3',
        'default',
        'light',
        'en',
        0, 1,
        '{"high_contrast": true, "large_text": true, "reduce_motion": true, "screen_reader_support": true}',
        1, 1,
        datetime('now', '-3 days'),
        datetime('now', '-1 day')
    );

-- User 4: Advanced user with custom preferences
INSERT OR IGNORE INTO global_app_configs (
    id, user_id, config_name, theme_mode, language, notifications_enabled, sound_enabled,
    accessibility, is_active, is_default, created_at, updated_at
) VALUES
    (
        'global-config-user-4',
        'test-user-4',
        'default',
        'dark',
        'es',
        1, 1,
        '{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}',
        1, 1,
        datetime('now', '-60 days'),
        datetime('now', '-3 hours')
    );

-- User 5: Power user with enterprise settings
INSERT OR IGNORE INTO global_app_configs (
    id, user_id, config_name, theme_mode, language, notifications_enabled, sound_enabled,
    accessibility, is_active, is_default, created_at, updated_at
) VALUES
    (
        'global-config-user-5',
        'test-user-5',
        'default',
        'system',
        'ko',
        1, 1,
        '{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}',
        1, 1,
        datetime('now', '-120 days'),
        datetime('now', '-30 minutes')
    );

-- ============================================================================
-- USER PRESENTATION CONFIGS - 6-Layer Configuration Examples
-- ============================================================================

-- User 1: Premium VIP User - Advanced Configuration
INSERT OR IGNORE INTO user_presentation_configs (
    id, user_id, config_name, presentation_config, config_version, personalization_level,
    is_active, is_default, created_at, updated_at
) VALUES
    (
        'quiz-pref-user-1',
        'test-user-1',
        'default',
        '{"layer0_dataset_presentation":{"preferred_pack_categories":["daily","assessment","therapy"],"default_difficulty_preference":"advanced","session_length_preference":"long","auto_select_recommended":false,"restore_progress":true},"layer1_user_choice":{"preferred_view_type":"wheel","active_skin_id":"ocean-blue","color_mode":"cool","user_level":"advanced"},"layer2_rendering_strategy":{"render_engine_preferences":{"wheel":"R3F","card":"Canvas","bubble":"WebGL","galaxy":"R3F"},"performance_mode":"high_quality","supported_content_types":{"text":true,"emoji":true,"images":true,"animations":true}},"layer3_skin_base":{"selected_skin_id":"ocean-blue","colors":{"primary":"#0EA5E9","secondary":"#64748B","background":"#F0F9FF","surface":"#E0F2FE","text":"#0C4A6E","accent":"#06B6D4"},"animations":{"enable_animations":true,"animation_speed":"fast","reduce_motion":false}},"layer4_view_detail":{"wheel_config":{"container_size":500,"wheel_radius":250,"emotion_display_mode":"advanced","show_labels":true,"show_emojis":true},"emotion_presentation":{"emoji_mapping":{"happy":{"primary":"😊","alternatives":["😄","😃","🙂","😌","🥰"]},"sad":{"primary":"😢","alternatives":["😭","😞","☹️","😔"]},"angry":{"primary":"😠","alternatives":["😡","🤬","😤","😒"]},"fearful":{"primary":"😨","alternatives":["😰","😱","😧","😟"]},"surprised":{"primary":"😲","alternatives":["😮","😯","🤯","😦"]},"disgusted":{"primary":"🤢","alternatives":["🤮","😖","😣","🙄"]},"neutral":{"primary":"😐","alternatives":["😑","😶","🤔","😏"]}},"color_mapping":{"happy":"#4CAF50","sad":"#2196F3","angry":"#F44336","fearful":"#9C27B0","surprised":"#FF9800","disgusted":"#795548","neutral":"#9E9E9E"},"animation_mapping":{"happy":"bounce","sad":"fade","angry":"shake","fearful":"tremble","surprised":"pop","disgusted":"recoil","neutral":"none"}}},"layer5_accessibility":{"high_contrast":false,"large_text":false,"reduce_motion":false,"keyboard_navigation":true,"voice_guidance":false}}',
        '2.0',
        85,
        1, 1,
        datetime('now', '-30 days'),
        datetime('now', '-1 hour')
    );

-- User 2: Regular User - Balanced Configuration
INSERT OR IGNORE INTO user_presentation_configs (
    id, user_id, config_name, presentation_config, config_version, personalization_level,
    is_active, is_default, created_at, updated_at
) VALUES
    (
        'quiz-pref-user-2',
        'test-user-2',
        'default',
        '{"layer0_dataset_presentation":{"preferred_pack_categories":["daily","entertainment"],"default_difficulty_preference":"regular","session_length_preference":"medium","auto_select_recommended":true,"restore_progress":true},"layer1_user_choice":{"preferred_view_type":"card","active_skin_id":"default-dark","color_mode":"warm","user_level":"regular"},"layer2_rendering_strategy":{"render_engine_preferences":{"wheel":"D3","card":"SVG","bubble":"Canvas","galaxy":"SVG"},"performance_mode":"balanced","supported_content_types":{"text":true,"emoji":true,"images":false,"animations":true}},"layer3_skin_base":{"selected_skin_id":"default-dark","colors":{"primary":"#60A5FA","secondary":"#94A3B8","background":"#0F172A","surface":"#1E293B","text":"#F1F5F9","accent":"#34D399"},"animations":{"enable_animations":true,"animation_speed":"normal","reduce_motion":true}},"layer4_view_detail":{"wheel_config":{"container_size":350,"wheel_radius":175,"emotion_display_mode":"standard","show_labels":true,"show_emojis":true}},"layer5_accessibility":{"high_contrast":true,"large_text":false,"reduce_motion":true,"keyboard_navigation":true,"voice_guidance":false}}',
        '2.0',
        60,
        1, 1,
        datetime('now', '-15 days'),
        datetime('now', '-2 hours')
    );

-- User 3: Accessibility User - Simplified Configuration
INSERT OR IGNORE INTO user_presentation_configs (
    id, user_id, config_name, presentation_config, config_version, personalization_level,
    is_active, is_default, created_at, updated_at
) VALUES
    (
        'quiz-pref-user-3',
        'test-user-3',
        'default',
        '{"layer0_dataset_presentation":{"preferred_pack_categories":["daily"],"default_difficulty_preference":"beginner","session_length_preference":"short","auto_select_recommended":true,"restore_progress":true},"layer1_user_choice":{"preferred_view_type":"card","active_skin_id":"default-light","color_mode":"neutral","user_level":"beginner"},"layer2_rendering_strategy":{"render_engine_preferences":{"wheel":"CSS","card":"CSS","bubble":"CSS","galaxy":"CSS"},"performance_mode":"performance","supported_content_types":{"text":true,"emoji":false,"images":false,"animations":false}},"layer3_skin_base":{"selected_skin_id":"default-light","colors":{"primary":"#3B82F6","secondary":"#64748B","background":"#FFFFFF","surface":"#F8FAFC","text":"#1E293B","accent":"#10B981"},"animations":{"enable_animations":false,"animation_speed":"slow","reduce_motion":true}},"layer4_view_detail":{"wheel_config":{"container_size":300,"wheel_radius":150,"emotion_display_mode":"simple","show_labels":true,"show_emojis":false}},"layer5_accessibility":{"high_contrast":true,"large_text":true,"reduce_motion":true,"keyboard_navigation":true,"voice_guidance":true}}',
        '2.0',
        30,
        1, 1,
        datetime('now', '-3 days'),
        datetime('now', '-1 day')
    );

-- User 4: Advanced User - Custom Configuration
INSERT OR IGNORE INTO user_presentation_configs (
    id, user_id, config_name, presentation_config, config_version, personalization_level,
    is_active, is_default, created_at, updated_at
) VALUES
    (
        'quiz-pref-user-4',
        'test-user-4',
        'default',
        '{"layer0_dataset_presentation":{"preferred_pack_categories":["assessment","therapy","research"],"default_difficulty_preference":"advanced","session_length_preference":"long","auto_select_recommended":false,"restore_progress":true},"layer1_user_choice":{"preferred_view_type":"bubble","active_skin_id":"nature-green","color_mode":"warm","user_level":"advanced"},"layer2_rendering_strategy":{"render_engine_preferences":{"wheel":"Canvas","card":"SVG","bubble":"Canvas","galaxy":"R3F"},"performance_mode":"high_quality","supported_content_types":{"text":true,"emoji":true,"images":true,"animations":true}},"layer3_skin_base":{"selected_skin_id":"nature-green","colors":{"primary":"#059669","secondary":"#6B7280","background":"#F0FDF4","surface":"#ECFDF5","text":"#064E3B","accent":"#10B981"},"animations":{"enable_animations":true,"animation_speed":"normal","reduce_motion":false}},"layer4_view_detail":{"wheel_config":{"container_size":400,"wheel_radius":200,"emotion_display_mode":"advanced","show_labels":true,"show_emojis":true}},"layer5_accessibility":{"high_contrast":false,"large_text":false,"reduce_motion":false,"keyboard_navigation":true,"voice_guidance":false}}',
        '2.0',
        75,
        1, 1,
        datetime('now', '-60 days'),
        datetime('now', '-3 hours')
    );

-- User 5: Power User - Enterprise Configuration
INSERT OR IGNORE INTO user_presentation_configs (
    id, user_id, config_name, presentation_config, config_version, personalization_level,
    is_active, is_default, created_at, updated_at
) VALUES
    (
        'quiz-pref-user-5',
        'test-user-5',
        'default',
        '{"layer0_dataset_presentation":{"preferred_pack_categories":["daily","assessment","therapy","research","education"],"default_difficulty_preference":"expert","session_length_preference":"long","auto_select_recommended":false,"restore_progress":true},"layer1_user_choice":{"preferred_view_type":"galaxy","active_skin_id":"neon-glow","color_mode":"auto","user_level":"advanced"},"layer2_rendering_strategy":{"render_engine_preferences":{"wheel":"R3F","card":"Canvas","bubble":"WebGL","galaxy":"WebGL"},"performance_mode":"high_quality","supported_content_types":{"text":true,"emoji":true,"images":true,"animations":true}},"layer3_skin_base":{"selected_skin_id":"neon-glow","colors":{"primary":"#8B5CF6","secondary":"#6B7280","background":"#0F0F23","surface":"#1A1A2E","text":"#E5E7EB","accent":"#F59E0B"},"animations":{"enable_animations":true,"animation_speed":"fast","reduce_motion":false}},"layer4_view_detail":{"wheel_config":{"container_size":600,"wheel_radius":300,"emotion_display_mode":"expert","show_labels":true,"show_emojis":true}},"layer5_accessibility":{"high_contrast":false,"large_text":false,"reduce_motion":false,"keyboard_navigation":true,"voice_guidance":false}}',
        '2.0',
        95,
        1, 1,
        datetime('now', '-120 days'),
        datetime('now', '-30 minutes')
    );

-- ============================================================================
-- QUESTION PRESENTATION OVERRIDES - 问题级别的emoji映射覆盖
-- ============================================================================

-- 用户为特定问题设置个性化emoji映射
INSERT OR IGNORE INTO question_presentation_overrides (
    id, user_id, question_id, presentation_overrides, override_reason,
    override_priority, is_active, created_at, updated_at, created_by, updated_by
) VALUES
-- 用户1在工作场景中为主要情绪问题设置专业emoji
('qpo_user001_q001_professional', 'test-user-1', 'q001_primary_emotion',
 '{
    "emoji_theme": "professional",
    "emoji_mapping": {
        "happy": {
            "emoji": "✅",
            "color": "#2E8B57",
            "animation": "check",
            "alternatives": ["👍", "💼", "📈", "🎯", "⭐"]
        },
        "sad": {
            "emoji": "📉",
            "color": "#4682B4",
            "animation": "decline",
            "alternatives": ["📊", "⚠️", "🔍", "📋", "💭"]
        },
        "angry": {
            "emoji": "⚠️",
            "color": "#FF6347",
            "animation": "alert",
            "alternatives": ["🚨", "⛔", "🔥", "💢", "⚡"]
        }
    },
    "context": "work_environment",
    "applied_at": "2024-01-15T09:00:00Z"
 }', 'context_specific', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'test-user-1', 'test-user-1'),

-- 用户2为playful问题设置动物主题emoji
('qpo_user002_q010_animals', 'test-user-2', 'q010_playful_tertiary',
 '{
    "emoji_theme": "animals",
    "emoji_mapping": {
        "aroused": {
            "emoji": "🐶",
            "color": "#FF69B4",
            "animation": "wag",
            "alternatives": ["🐱", "🐰", "🐼", "🦄", "🐵"]
        },
        "cheeky": {
            "emoji": "🐱",
            "color": "#FF69B4",
            "animation": "pounce",
            "alternatives": ["🦊", "🐺", "🐯", "🦁", "🐸"]
        }
    },
    "context": "playful_mood",
    "applied_at": "2024-01-15T14:30:00Z"
 }', 'user_preference', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'test-user-2', 'test-user-2'),

-- 用户3为content问题设置自然主题emoji
('qpo_user003_q011_nature', 'test-user-3', 'q011_content_tertiary',
 '{
    "emoji_theme": "nature",
    "emoji_mapping": {
        "free": {
            "emoji": "🌸",
            "color": "#FFB6C1",
            "animation": "bloom",
            "alternatives": ["🌺", "🌻", "🌷", "🌹", "🌼"]
        },
        "joyful": {
            "emoji": "🌞",
            "color": "#FFD700",
            "animation": "shine",
            "alternatives": ["🌈", "⭐", "✨", "🌟", "💫"]
        }
    },
    "context": "nature_connection",
    "applied_at": "2024-01-16T08:15:00Z"
 }', 'user_preference', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'test-user-3', 'test-user-3');

-- ============================================================================
-- PACK PRESENTATION OVERRIDES - User-specific pack customizations
-- ============================================================================

-- User 1: Override for emotion wheel pack
INSERT OR IGNORE INTO pack_presentation_overrides (
    id, user_id, pack_id, presentation_overrides, tier_presentation_overrides,
    override_reason, override_priority, is_active, created_at, updated_at
) VALUES
    (
        'override-user-1-emotion-wheel',
        'test-user-1',
        'emotion-wheel-tracker',
        '{"layer4_view_detail":{"wheel_config":{"container_size":600,"wheel_radius":300,"use_3d_effects":true,"perspective":1200,"depth":30},"emotion_presentation":{"emoji_mapping":{"happy":{"primary":"🎉","alternatives":["🥳","🎊","🎈","🌟"]},"excited":{"primary":"🚀","alternatives":["⚡","💫","🔥","✨"]},"grateful":{"primary":"🙏","alternatives":["💖","🌺","🌸","💝"]}},"color_mapping":{"happy":"#FFD700","excited":"#FF6B35","grateful":"#FF69B4"},"animation_mapping":{"happy":"celebration","excited":"rocket","grateful":"glow"}}}}',
        '{"tier_1":{"enhanced_visuals":true},"tier_2":{"advanced_animations":true}}',
        'user_preference',
        1,
        1,
        datetime('now', '-20 days'),
        datetime('now', '-5 days')
    );

-- User 2: Override for personality test
INSERT OR IGNORE INTO pack_presentation_overrides (
    id, user_id, pack_id, presentation_overrides, tier_presentation_overrides,
    override_reason, override_priority, is_active, created_at, updated_at
) VALUES
    (
        'override-user-2-personality',
        'test-user-2',
        'big5-personality-test',
        '{"layer1_user_choice":{"preferred_view_type":"card"},"layer2_rendering_strategy":{"performance_mode":"balanced"}}',
        '{"tier_1":{"simplified_ui":true}}',
        'performance_optimization',
        2,
        1,
        datetime('now', '-10 days'),
        datetime('now', '-2 days')
    );

-- User 3: Accessibility override for all packs
INSERT OR IGNORE INTO pack_presentation_overrides (
    id, user_id, pack_id, presentation_overrides, tier_presentation_overrides,
    override_reason, override_priority, is_active, created_at, updated_at
) VALUES
    (
        'override-user-3-accessibility',
        'test-user-3',
        'emotion-wheel-tracker',
        '{"layer5_accessibility":{"high_contrast":true,"large_text":true,"reduce_motion":true,"voice_guidance":true}}',
        '{"tier_1":{"accessibility_enhanced":true,"simplified_navigation":true}}',
        'accessibility_need',
        3,
        1,
        datetime('now', '-2 days'),
        datetime('now', '-1 day')
    );
