/**
 * Better-Auth + tRPC 集成示例组件
 * 展示认证、VIP购买、皮肤购买的完整流程
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useAuthState, useVipStatus } from '@/lib/auth/better-auth-client';
import { OnlineServices } from '@/services/online';
import { CreditCard, Crown, LogIn, LogOut, Palette, User } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export const AuthIntegrationExample: React.FC = () => {
  const { user, isAuthenticated, isLoading, authService } = useAuthState();
  const { isVip, vipStatus, refetch: refetchVipStatus } = useVipStatus();
  const [vipPlans, setVipPlans] = useState<any[]>([]);
  const [purchaseHistory, setPurchaseHistory] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // 获取VIP计划
  useEffect(() => {
    const fetchVipPlans = async () => {
      try {
        const onlineServices = OnlineServices.getInstance();
        const result = await onlineServices.api.getVipPlans.query();
        if (result.success) {
          setVipPlans(result.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch VIP plans:', error);
      }
    };

    fetchVipPlans();
  }, []);

  // 获取购买历史
  useEffect(() => {
    if (isAuthenticated) {
      const fetchPurchaseHistory = async () => {
        try {
          const onlineServices = OnlineServices.getInstance();
          const result = await onlineServices.api.getPurchaseHistory.query({ limit: 10 });
          if (result.success) {
            setPurchaseHistory(result.data || []);
          }
        } catch (error) {
          console.error('Failed to fetch purchase history:', error);
        }
      };

      fetchPurchaseHistory();
    }
  }, [isAuthenticated]);

  // 登录处理
  const handleSignIn = async () => {
    try {
      setIsProcessing(true);
      const result = await authService.signInWithEmail('<EMAIL>', 'password123');
      if (result.data?.user) {
        toast.success('登录成功！');
      }
    } catch (error) {
      toast.error('登录失败');
      console.error('Sign in error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // 注册处理
  const handleSignUp = async () => {
    try {
      setIsProcessing(true);
      const result = await authService.signUpWithEmail(
        '<EMAIL>',
        'password123',
        'New User'
      );
      if (result.data?.user) {
        toast.success('注册成功！');
      }
    } catch (error) {
      toast.error('注册失败');
      console.error('Sign up error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // 登出处理
  const handleSignOut = async () => {
    try {
      setIsProcessing(true);
      await authService.signOut();
      toast.success('已登出');
    } catch (error) {
      toast.error('登出失败');
      console.error('Sign out error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // VIP购买处理
  const handlePurchaseVip = async (planId: string) => {
    try {
      setIsProcessing(true);

      // 模拟支付方式选择
      const paymentMethodId = 'pm_test_card';

      const onlineServices = OnlineServices.getInstance();
      const result = await onlineServices.api.purchaseVip.mutate({
        planId,
        paymentMethodId,
      });

      if (result.success) {
        toast.success('VIP购买成功！');
        await refetchVipStatus();
      } else {
        toast.error(`VIP购买失败: ${result.error}`);
      }
    } catch (error) {
      toast.error('VIP购买失败');
      console.error('VIP purchase error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // 皮肤购买处理
  const handlePurchaseSkin = async (skinId: string) => {
    try {
      setIsProcessing(true);

      const paymentMethodId = 'pm_test_card';

      const onlineServices = OnlineServices.getInstance();
      const result = await onlineServices.api.purchaseSkin.mutate({
        skinId,
        paymentMethodId,
      });

      if (result.success) {
        toast.success('皮肤购买成功！');
      } else {
        toast.error(`皮肤购买失败: ${result.error}`);
      }
    } catch (error) {
      toast.error('皮肤购买失败');
      console.error('Skin purchase error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Better-Auth + tRPC 集成示例
          </CardTitle>
          <CardDescription>展示认证、VIP订阅和皮肤购买的完整流程</CardDescription>
        </CardHeader>
        <CardContent>
          {!isAuthenticated ? (
            <div className="space-y-4">
              <p className="text-muted-foreground">请先登录以体验完整功能</p>
              <div className="flex gap-2">
                <Button
                  onClick={handleSignIn}
                  disabled={isProcessing}
                  className="flex items-center gap-2"
                >
                  <LogIn className="h-4 w-4" />
                  登录 (测试账户)
                </Button>
                <Button variant="outline" onClick={handleSignUp} disabled={isProcessing}>
                  注册新账户
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* 用户信息 */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">
                    欢迎, {user?.displayName || user?.email}
                  </h3>
                  <div className="flex items-center gap-2 mt-1">
                    {isVip ? (
                      <Badge variant="default" className="flex items-center gap-1">
                        <Crown className="h-3 w-3" />
                        VIP用户
                      </Badge>
                    ) : (
                      <Badge variant="secondary">普通用户</Badge>
                    )}
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={handleSignOut}
                  disabled={isProcessing}
                  className="flex items-center gap-2"
                >
                  <LogOut className="h-4 w-4" />
                  登出
                </Button>
              </div>

              <Separator />

              {/* VIP状态和购买 */}
              <div>
                <h4 className="text-md font-semibold mb-4 flex items-center gap-2">
                  <Crown className="h-4 w-4" />
                  VIP订阅
                </h4>

                {isVip ? (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <Crown className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                        <p className="font-semibold">您是VIP用户！</p>
                        <p className="text-sm text-muted-foreground">
                          到期时间:{' '}
                          {vipStatus?.expiresAt
                            ? new Date(vipStatus.expiresAt).toLocaleDateString()
                            : '永久'}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {vipPlans.map((plan) => (
                      <Card key={plan.id}>
                        <CardHeader>
                          <CardTitle className="text-lg">{plan.name}</CardTitle>
                          <CardDescription>
                            ${plan.price} / {plan.duration === 'monthly' ? '月' : '年'}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <ul className="text-sm space-y-1 mb-4">
                            {plan.features.slice(0, 3).map((feature: string) => (
                              <li key={feature}>• {feature.replace('_', ' ')}</li>
                            ))}
                          </ul>
                          <Button
                            onClick={() => handlePurchaseVip(plan.id)}
                            disabled={isProcessing}
                            className="w-full flex items-center gap-2"
                          >
                            <CreditCard className="h-4 w-4" />
                            购买VIP
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>

              <Separator />

              {/* 皮肤购买示例 */}
              <div>
                <h4 className="text-md font-semibold mb-4 flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  皮肤购买
                </h4>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold">高级主题皮肤</p>
                        <p className="text-sm text-muted-foreground">$4.99</p>
                      </div>
                      <Button
                        onClick={() => handlePurchaseSkin('premium-theme-001')}
                        disabled={isProcessing}
                        className="flex items-center gap-2"
                      >
                        <CreditCard className="h-4 w-4" />
                        购买皮肤
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 购买历史 */}
              {purchaseHistory.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <h4 className="text-md font-semibold mb-4">购买历史</h4>
                    <div className="space-y-2">
                      {purchaseHistory.slice(0, 3).map((transaction) => (
                        <Card key={transaction.id}>
                          <CardContent className="pt-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">{transaction.description}</p>
                                <p className="text-sm text-muted-foreground">
                                  {new Date(transaction.created_at).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-semibold">${transaction.amount}</p>
                                <Badge
                                  variant={
                                    transaction.status === 'completed' ? 'default' : 'secondary'
                                  }
                                >
                                  {transaction.status}
                                </Badge>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
