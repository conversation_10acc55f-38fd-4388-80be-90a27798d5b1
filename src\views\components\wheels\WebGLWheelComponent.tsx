/**
 * WebGL轮盘组件
 * 使用WebGL实现的轮盘，不依赖旧的轮盘实现
 *
 * 此组件是新视图系统的一部分，用于替代旧的轮盘组件
 * 它直接实现了轮盘的渲染，不依赖旧的轮盘类
 */

import { AnimatedEmoji } from '@/components/emoji/AnimatedEmoji';
import { useEmoji } from '@/contexts/EmojiContext';
import type { Emotion, ContentDisplayMode, SkinConfig } from '@/types';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { extract_wheel_config_for_engine, type WheelConfig } from '../../../utils/wheelConfigExtractor';

interface WebGLWheelComponentProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  onBack?: () => void;
  selectedPath?: any;
}
/*
 * WebGL轮盘组件
 */
export const WebGLWheelComponent: React.FC<WebGLWheelComponentProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  onBack,
  selectedPath,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [hoveredEmotion, setHoveredEmotion] = useState<string | null>(null);
  const { getEmojiItemForEmotionId } = useEmoji();
  const [renderer, setRenderer] = useState<THREE.WebGLRenderer | null>(null);
  const [scene, setScene] = useState<THREE.Scene | null>(null);
  const [camera, setCamera] = useState<THREE.PerspectiveCamera | null>(null);
  const [emotionMeshes, setEmotionMeshes] = useState<THREE.Mesh[]>([]);

  // 提取轮盘配置
  // const wheelConfig = extractWheelConfig(skinConfig);
  const wheelConfig = extract_wheel_config_for_engine.webgl(skinConfig);

  // 初始化 WebGL 渲染器
  useEffect(() => {
    if (!containerRef.current) return;

    // 创建渲染器
    const newRenderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
    });
    newRenderer.setSize(wheelConfig.container_size, wheelConfig.container_size);
    newRenderer.setClearColor(0x000000, 0); // 透明背景

    // 创建场景
    const newScene = new THREE.Scene();

    // 创建相机
    const newCamera = new THREE.PerspectiveCamera(
      45,
      1, // 宽高比为 1（正方形）
      0.1,
      1000
    );
    newCamera.position.z = 5;

    // 添加环境光和方向光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    newScene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(10, 10, 10);
    newScene.add(directionalLight);

    // 将渲染器的 DOM 元素添加到容器中
    containerRef.current.appendChild(newRenderer.domElement);

    // 保存渲染器、场景和相机
    setRenderer(newRenderer);
    setScene(newScene);
    setCamera(newCamera);

    // 清理函数
    return () => {
      if (containerRef.current && newRenderer.domElement) {
        containerRef.current.removeChild(newRenderer.domElement);
      }
      newRenderer.dispose();
    };
  }, []);

  // 创建轮盘
  useEffect(() => {
    if (!scene || !camera || !renderer || emotions.length === 0) return;

    // 清除现有的轮盘
    emotionMeshes.forEach((mesh) => {
      scene.remove(mesh);
    });

    // 创建新的轮盘
    const newEmotionMeshes: THREE.Mesh[] = [];
    const radius = 2; // 轮盘半径（在 Three.js 中使用较小的单位）
    const anglePerSector = (2 * Math.PI) / emotions.length;

    // 创建扇区
    emotions.forEach((emotion, index) => {
      const startAngle = index * anglePerSector;
      const endAngle = (index + 1) * anglePerSector;

      // 创建扇区形状
      const shape = new THREE.Shape();
      shape.moveTo(0, 0);
      shape.lineTo(radius * Math.cos(startAngle), radius * Math.sin(startAngle));

      // 添加弧形
      const arcCurve = new THREE.EllipseCurve(0, 0, radius, radius, startAngle, endAngle, false, 0);

      const arcPoints = arcCurve.getPoints(16);
      arcPoints.forEach((point) => {
        shape.lineTo(point.x, point.y);
      });

      shape.lineTo(0, 0);

      // 创建扇区几何体
      const geometry = new THREE.ShapeGeometry(shape);

      // 创建扇区材质
      const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(emotion.color || '#cccccc'),
        side: THREE.DoubleSide,
        roughness: 0.7,
        metalness: 0.2,
      });

      // 创建扇区网格
      const mesh = new THREE.Mesh(geometry, material);
      mesh.userData = { emotion };

      // 添加到场景
      scene.add(mesh);
      newEmotionMeshes.push(mesh);
    });

    // 保存扇区网格
    setEmotionMeshes(newEmotionMeshes);

    // 渲染场景
    const animate = () => {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    };
    animate();

    // 添加鼠标事件
    const handleMouseMove = (event: MouseEvent) => {
      if (!containerRef.current) return;

      // 计算鼠标位置
      const rect = containerRef.current.getBoundingClientRect();
      const x = ((event.clientX - rect.left) / wheelConfig.container_size) * 2 - 1;
      const y = -((event.clientY - rect.top) / wheelConfig.container_size) * 2 + 1;

      // 创建射线
      const raycaster = new THREE.Raycaster();
      raycaster.setFromCamera(new THREE.Vector2(x, y), camera);

      // 检测射线与扇区的交点
      const intersects = raycaster.intersectObjects(newEmotionMeshes);

      // 处理悬停效果
      if (intersects.length > 0) {
        const intersectedMesh = intersects[0].object as THREE.Mesh;
        const emotion = intersectedMesh.userData.emotion as Emotion;
        setHoveredEmotion(emotion.id);
        containerRef.current.style.cursor = 'pointer';

        // 应用悬停效果
        if (wheelConfig.hover_effect === 'scale') {
          intersectedMesh.scale.set(1.05, 1.05, 1.05);
        }
      } else {
        setHoveredEmotion(null);
        containerRef.current.style.cursor = 'default';

        // 重置所有扇区的缩放
        newEmotionMeshes.forEach((mesh) => {
          mesh.scale.set(1, 1, 1);
        });
      }
    };

    // 添加点击事件
    const handleClick = (event: MouseEvent) => {
      if (!containerRef.current) return;

      // 计算鼠标位置
      const rect = containerRef.current.getBoundingClientRect();
      const x = ((event.clientX - rect.left) / wheelConfig.container_size) * 2 - 1;
      const y = -((event.clientY - rect.top) / wheelConfig.container_size) * 2 + 1;

      // 创建射线
      const raycaster = new THREE.Raycaster();
      raycaster.setFromCamera(new THREE.Vector2(x, y), camera);

      // 检测射线与扇区的交点
      const intersects = raycaster.intersectObjects(newEmotionMeshes);

      // 处理点击事件
      if (intersects.length > 0) {
        const intersectedMesh = intersects[0].object as THREE.Mesh;
        const emotion = intersectedMesh.userData.emotion as Emotion;
        onSelect(emotion);
      }
    };

    // 添加事件监听器
    if (containerRef.current) {
      containerRef.current.addEventListener('mousemove', handleMouseMove);
      containerRef.current.addEventListener('click', handleClick);
    }

    // 清理函数
    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('mousemove', handleMouseMove);
        containerRef.current.removeEventListener('click', handleClick);
      }
    };
  }, [emotions, scene, camera, renderer, wheelConfig, onSelect]);

  // 渲染动画表情（如果内容类型为 animatedEmoji）
  const renderAnimatedEmojis = () => {
    if (contentDisplayMode !== 'animatedEmoji' || !emotions.length) return null;

    return emotions.map((emotion, index) => {
      const angle = (index / emotions.length) * 2 * Math.PI;
      const radius = wheelConfig.wheel_radius * 0.7; // 调整半径，使表情位于扇区中心
      const x = Math.cos(angle) * radius + wheelConfig.container_size / 2;
      const y = Math.sin(angle) * radius + wheelConfig.container_size / 2;

      const emojiItem = getEmojiItemForEmotionId(emotion.id);

      return (
        <div
          key={emotion.id}
          style={{
            position: 'absolute',
            left: `${x}px`,
            top: `${y}px`,
            transform: 'translate(-50%, -50%)',
            zIndex: hoveredEmotion === emotion.id ? 10 : 1,
            transition: `all ${wheelConfig.transition_duration}ms`,
            cursor: 'pointer',
          }}
          onClick={() => onSelect(emotion)}
          onMouseEnter={() => setHoveredEmotion(emotion.id)}
          onMouseLeave={() => setHoveredEmotion(null)}
        >
          {emojiItem ? (
            <AnimatedEmoji
              emojiItem={emojiItem}
              size={
                wheelConfig.emojiSize >= 40
                  ? '4xl'
                  : wheelConfig.emojiSize >= 32
                    ? '3xl'
                    : wheelConfig.emojiSize >= 24
                      ? '2xl'
                      : wheelConfig.emojiSize >= 20
                        ? 'xl'
                        : wheelConfig.emojiSize >= 16
                          ? 'lg'
                          : wheelConfig.emojiSize >= 14
                            ? 'md'
                            : wheelConfig.emojiSize >= 12
                              ? 'sm'
                              : 'xs'
              }
              autoPlay={true}
              loop={true}
            />
          ) : (
            <span style={{ fontSize: `${wheelConfig.emojiSize}px` }}>{emotion.emoji}</span>
          )}
        </div>
      );
    });
  };

  // 渲染返回按钮
  const renderBackButton = () => {
    if (!onBack || tierLevel === 1) return null;

    return (
      <button
        onClick={onBack}
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          boxShadow: wheelConfig.shadow_enabled ? `0 0 5px ${wheelConfig.shadowColor}` : 'none',
        }}
      >
        ←
      </button>
    );
  };

  // 渲染选中路径
  const renderSelectedPath = () => {
    if (!selectedPath) return null;

    return (
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 100,
          fontSize: `${wheelConfig.font_size}px`,
          fontFamily: wheelConfig.font_family,
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '5px',
          background: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(5px)',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {selectedPath}
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width: `${wheelConfig.container_size}px`,
        height: `${wheelConfig.container_size}px`,
        margin: '0 auto',
        borderRadius: '50%',
        overflow: 'hidden',
        boxShadow: wheelConfig.shadow_enabled
          ? `0 0 ${wheelConfig.shadowBlur}px ${wheelConfig.shadowColor}`
          : 'none',
      }}
    >
      {renderBackButton()}
      {renderSelectedPath()}
      {contentDisplayMode === 'animatedEmoji' && renderAnimatedEmojis()}
    </div>
  );
};
