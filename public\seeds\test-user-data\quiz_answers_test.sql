-- Quiz答案测试数据
-- 包含情绪追踪问卷和中医体质问卷的详细答案记录

-- 1. 情绪追踪问卷答案 (Happy -> Playful -> Aroused 路径)

-- 第一层: 主要情绪选择
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_emotion_001_q1',
  'session_emotion_001',
  'q001_primary_emotion',
  'opt_primary_happy',
  'happy',
  'Happy',
  3500,
  '{
    "emotion_tier": 1,
    "layer": "primary",
    "emotion_path": ["happy"],
    "selection_confidence": 0.9,
    "user_hesitation_time": 2.1
  }',
  '2024-01-15 09:00:15',
  '2024-01-15 09:00:15'
);

-- 第二层: 次要情绪选择
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_emotion_001_q2',
  'session_emotion_001',
  'q002_happy_secondary',
  'opt_happy_playful',
  'playful',
  'Playful',
  4200,
  '{
    "emotion_tier": 2,
    "layer": "secondary",
    "parent_emotion": "happy",
    "emotion_path": ["happy", "playful"],
    "selection_confidence": 0.85,
    "available_options": 9
  }',
  '2024-01-15 09:01:00',
  '2024-01-15 09:01:00'
);

-- 第三层: 具体情绪选择
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_emotion_001_q3',
  'session_emotion_001',
  'q010_playful_tertiary',
  'opt_playful_aroused',
  'aroused',
  'Aroused',
  2800,
  '{
    "emotion_tier": 3,
    "layer": "tertiary",
    "parent_emotion": "playful",
    "grandparent_emotion": "happy",
    "emotion_path": ["happy", "playful", "aroused"],
    "is_final_selection": true,
    "selection_confidence": 0.95
  }',
  '2024-01-15 09:02:15',
  '2024-01-15 09:02:15'
);

-- 2. 另一个情绪路径 (Angry -> Frustrated -> Mad)

INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES 
-- 主要情绪: Angry
('answer_emotion_003_q1', 'session_emotion_003', 'q001_primary_emotion', 'opt_primary_angry', 'angry', 'Angry', 2900,
 '{"emotion_tier": 1, "layer": "primary", "emotion_path": ["angry"], "selection_confidence": 0.92}', 
 '2024-01-15 14:15:20', '2024-01-15 14:15:20'),

-- 次要情绪: Frustrated  
('answer_emotion_003_q2', 'session_emotion_003', 'q006_angry_secondary', 'opt_angry_frustrated', 'frustrated', 'Frustrated', 3800,
 '{"emotion_tier": 2, "layer": "secondary", "parent_emotion": "angry", "emotion_path": ["angry", "frustrated"]}',
 '2024-01-15 14:16:10', '2024-01-15 14:16:10'),

-- 具体情绪: Mad
('answer_emotion_003_q3', 'session_emotion_003', 'q025_frustrated_tertiary', 'opt_frustrated_mad', 'mad', 'Mad', 2200,
 '{"emotion_tier": 3, "layer": "tertiary", "parent_emotion": "frustrated", "emotion_path": ["angry", "frustrated", "mad"], "is_final_selection": true}',
 '2024-01-15 14:17:05', '2024-01-15 14:17:05');

-- 3. 中医肝证素评估答案

-- 问题1: 容易急躁或发怒 (中等程度)
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_tcm_liver_001_q1',
  'session_tcm_liver_001',
  'tcm_liver_q001',
  'tcm_liver_q001_moderate',
  'moderate',
  'Moderate',
  5200,
  '{
    "syndrome_element": "liver",
    "base_score": 31,
    "weight_coefficient": 1.0,
    "calculated_score": 31.0,
    "symptom_category": "emotional",
    "severity_level": "moderate"
  }',
  '2024-01-15 16:00:30',
  '2024-01-15 16:00:30'
);

-- 问题2: 情绪低沉 (轻微程度)
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_tcm_liver_001_q2',
  'session_tcm_liver_001',
  'tcm_liver_q002',
  'tcm_liver_q002_mild',
  'mild',
  'Mild',
  4800,
  '{
    "syndrome_element": "liver",
    "base_score": 40,
    "weight_coefficient": 0.7,
    "calculated_score": 28.0,
    "symptom_category": "emotional",
    "severity_level": "mild"
  }',
  '2024-01-15 16:01:15',
  '2024-01-15 16:01:15'
);

-- 问题3: 经常叹气 (严重程度)
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_tcm_liver_001_q3',
  'session_tcm_liver_001',
  'tcm_liver_q003',
  'tcm_liver_q003_severe',
  'severe',
  'Severe',
  3600,
  '{
    "syndrome_element": "liver",
    "base_score": 40,
    "weight_coefficient": 1.5,
    "calculated_score": 60.0,
    "symptom_category": "emotional",
    "severity_level": "severe"
  }',
  '2024-01-15 16:02:00',
  '2024-01-15 16:02:00'
);

-- 性别限制问题: 月经不调 (仅女性，中等程度)
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_tcm_liver_001_q15',
  'session_tcm_liver_001',
  'tcm_liver_q015',
  'tcm_liver_q015_moderate',
  'moderate',
  'Moderate',
  6200,
  '{
    "syndrome_element": "liver",
    "base_score": 38,
    "weight_coefficient": 1.0,
    "calculated_score": 38.0,
    "symptom_category": "reproductive",
    "gender_restriction": "female",
    "severity_level": "moderate"
  }',
  '2024-01-15 16:07:30',
  '2024-01-15 16:07:30'
);

-- 4. 中医气虚证素评估答案 (包含负分值示例)

-- 问题1: 怕风 (轻微程度)
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_tcm_qi_def_001_q1',
  'session_tcm_qi_def_001',
  'tcm_qi_def_q001',
  'tcm_qi_def_q001_mild',
  'mild',
  'Mild',
  4500,
  '{
    "syndrome_element": "qi_deficiency",
    "base_score": 31,
    "weight_coefficient": 0.7,
    "calculated_score": 21.7,
    "symptom_category": "environmental_sensitivity",
    "severity_level": "mild"
  }',
  '2024-01-15 19:00:45',
  '2024-01-15 19:00:45'
);

-- 负分值问题: 气喘或气短 (无症状，负分值处理)
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_tcm_qi_def_001_q8',
  'session_tcm_qi_def_001',
  'tcm_qi_def_q008',
  'tcm_qi_def_q008_none',
  'none',
  'None',
  3200,
  '{
    "syndrome_element": "qi_deficiency",
    "base_score": -25,
    "weight_coefficient": 0,
    "calculated_score": 0,
    "symptom_category": "respiratory",
    "is_negative_score": true,
    "severity_level": "none"
  }',
  '2024-01-15 19:04:20',
  '2024-01-15 19:04:20'
);

-- 5. 多证素综合评估的部分答案

-- 肝证素部分
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_tcm_multi_001_liver_q1',
  'session_tcm_multi_001',
  'tcm_liver_q001',
  'tcm_liver_q001_moderate',
  'moderate',
  'Moderate',
  4800,
  '{
    "syndrome_element": "liver",
    "assessment_type": "comprehensive",
    "base_score": 31,
    "weight_coefficient": 1.0,
    "calculated_score": 31.0,
    "part_of_multi_syndrome": true
  }',
  '2024-01-16 09:02:30',
  '2024-01-16 09:02:30'
);

-- 肾证素部分
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES (
  'answer_tcm_multi_001_kidney_q1',
  'session_tcm_multi_001',
  'tcm_kidney_q001',
  'tcm_kidney_q001_mild',
  'mild',
  'Mild',
  5100,
  '{
    "syndrome_element": "kidney",
    "assessment_type": "comprehensive",
    "base_score": 25,
    "weight_coefficient": 0.7,
    "calculated_score": 17.5,
    "part_of_multi_syndrome": true
  }',
  '2024-01-16 09:15:45',
  '2024-01-16 09:15:45'
);

-- 6. 重复评估的答案 (追踪情绪变化)

-- 同一用户的第二次情绪评估 (Happy -> Content -> Joyful)
INSERT INTO quiz_answers (
  id, session_id, question_id, selected_option_id, answer_value,
  answer_text, response_time_ms, metadata, created_at, updated_at
) VALUES 
-- 主要情绪: Happy (相同)
('answer_emotion_004_q1', 'session_emotion_004', 'q001_primary_emotion', 'opt_primary_happy', 'happy', 'Happy', 2800,
 '{"emotion_tier": 1, "emotion_path": ["happy"], "repeat_assessment": true, "previous_session": "session_emotion_001"}',
 '2024-01-16 14:30:20', '2024-01-16 14:30:20'),

-- 次要情绪: Content (不同路径)
('answer_emotion_004_q2', 'session_emotion_004', 'q002_happy_secondary', 'opt_happy_content', 'content', 'Content', 3500,
 '{"emotion_tier": 2, "emotion_path": ["happy", "content"], "path_change": "different_secondary"}',
 '2024-01-16 14:31:10', '2024-01-16 14:31:10'),

-- 具体情绪: Joyful (最终不同结果)
('answer_emotion_004_q3', 'session_emotion_004', 'q011_content_tertiary', 'opt_content_joyful', 'joyful', 'Joyful', 2400,
 '{"emotion_tier": 3, "emotion_path": ["happy", "content", "joyful"], "is_final_selection": true, "emotion_evolution": "playful_to_content"}',
 '2024-01-16 14:32:00', '2024-01-16 14:32:00');
