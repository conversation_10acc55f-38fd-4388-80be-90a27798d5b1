# Quiz基础组件实现计划

## 🎯 实现目标

基于ViewFactory架构，实现配置驱动的Quiz基础组件系统，为构建wheel、card、bubble、galaxy等特殊视图提供坚实基础。

## 📋 实现优先级

### 第一阶段：核心基础组件 (2-3周)

#### 1. 文本组件 (TextComponent) - 优先级：🔥🔥🔥
**实现文件**: `src/components/quiz/base/TextComponent.tsx`

```typescript
interface TextComponentProps {
  config: TextComponentConfig;
  content: TextContent;
  onInteraction?: (event: InteractionEvent) => void;
}

// 实现要点：
// - 支持多语言文本渲染
// - 动画效果（打字机、毛笔描边等）
// - 响应式字体大小
// - 可访问性支持
```

#### 2. 按钮组件 (ButtonComponent) - 优先级：🔥🔥🔥
**实现文件**: `src/components/quiz/base/ButtonComponent.tsx`

```typescript
interface ButtonComponentProps {
  config: ButtonComponentConfig;
  onClick: (event: ButtonClickEvent) => void;
  disabled?: boolean;
  loading?: boolean;
}

// 实现要点：
// - 多种视觉风格（标准、玉佩、印章）
// - 触觉反馈集成
// - 加载状态动画
// - 键盘导航支持
```

#### 3. 选择器组件 (SelectorComponent) - 优先级：🔥🔥
**实现文件**: `src/components/quiz/base/SelectorComponent.tsx`

```typescript
interface SelectorComponentProps {
  config: SelectorComponentConfig;
  options: OptionConfig[];
  selectedValues: (string | number)[];
  onSelectionChange: (values: (string | number)[]) => void;
}

// 实现要点：
// - 单选/多选模式
// - 三种布局变体
// - 选择状态动画
// - 验证规则支持
```

### 第二阶段：交互组件 (2-3周)

#### 4. 滑块组件 (SliderComponent) - 优先级：🔥🔥
**实现文件**: `src/components/quiz/base/SliderComponent.tsx`

```typescript
interface SliderComponentProps {
  config: SliderComponentConfig;
  value: number;
  onChange: (value: number) => void;
  onChangeComplete?: (value: number) => void;
}

// 实现要点：
// - 水平/垂直方向支持
// - 中医风格轨道和滑块
// - 实时值显示
// - 触摸手势优化
```

#### 5. 评分组件 (RatingComponent) - 优先级：🔥🔥
**实现文件**: `src/components/quiz/base/RatingComponent.tsx`

```typescript
interface RatingComponentProps {
  config: RatingComponentConfig;
  value: number;
  onChange: (value: number) => void;
  readonly?: boolean;
}

// 实现要点：
// - 多种评分标记（星星、莲花、太极等）
// - 悬停预览效果
// - 填充动画
// - 半分支持
```

### 第三阶段：媒体和进度组件 (1-2周)

#### 6. 图片选择器组件 (ImageSelectorComponent) - 优先级：🔥
**实现文件**: `src/components/quiz/base/ImageSelectorComponent.tsx`

```typescript
interface ImageSelectorProps {
  config: ImageSelectorConfig;
  images: ImageOption[];
  selectedIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
}

// 实现要点：
// - 网格/滚动布局
// - 图片懒加载
// - 选择状态指示
// - 图片预览功能
```

#### 7. 进度指示器组件 (ProgressIndicatorComponent) - 优先级：🔥
**实现文件**: `src/components/quiz/base/ProgressIndicatorComponent.tsx`

```typescript
interface ProgressIndicatorProps {
  config: ProgressIndicatorConfig;
  current: number;
  total: number;
  animated?: boolean;
}

// 实现要点：
// - 多种进度样式
// - 中医文化动画（莲花绽放、竹叶生长）
// - 平滑过渡动画
// - 百分比显示
```

#### 8. 媒体组件 (MediaComponent) - 优先级：🔥
**实现文件**: `src/components/quiz/base/MediaComponent.tsx`

```typescript
interface MediaComponentProps {
  config: MediaComponentConfig;
  onPlayStateChange?: (isPlaying: boolean) => void;
  onProgress?: (progress: number) => void;
}

// 实现要点：
// - 音频/视频播放器
// - 自定义控制界面
// - 中医风格皮肤
// - 播放状态管理
```

## 🏗️ 技术实现架构

### 1. 组件基类设计

```typescript
// 基础Quiz组件抽象类
abstract class BaseQuizComponent<TConfig, TProps> extends React.Component<TProps> {
  protected config: TConfig;
  protected personalization: PersonalizationConfig;
  
  constructor(props: TProps) {
    super(props);
    this.config = this.extractConfig(props);
    this.personalization = this.extractPersonalization(props);
  }
  
  // 抽象方法：子类必须实现
  abstract render(): React.ReactNode;
  abstract extractConfig(props: TProps): TConfig;
  
  // 通用方法
  protected extractPersonalization(props: TProps): PersonalizationConfig {
    return (props as any).personalization || {};
  }
  
  protected applyPersonalization(baseConfig: TConfig): TConfig {
    return PersonalizationApplier.apply(baseConfig, this.personalization);
  }
  
  protected emitInteractionEvent(type: InteractionType, data: any): void {
    const event: InteractionEvent = {
      type,
      target: this.constructor.name,
      data,
      timestamp: Date.now(),
      metadata: {
        component_id: (this.props as any).id,
        user_agent: navigator.userAgent
      }
    };
    
    (this.props as any).onInteraction?.(event);
  }
}
```

### 2. 配置验证系统

```typescript
// 配置验证器
class ComponentConfigValidator {
  static validateTextConfig(config: TextComponentConfig): ValidationResult {
    const errors: string[] = [];
    
    if (!config.layout_id) {
      errors.push('layout_id is required');
    }
    
    if (!config.content?.text_localized) {
      errors.push('text_localized is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  static validateButtonConfig(config: ButtonComponentConfig): ValidationResult {
    // 按钮配置验证逻辑
    return { isValid: true, errors: [] };
  }
  
  // 其他组件配置验证方法...
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}
```

### 3. 样式系统集成

```typescript
// 样式应用器
class QuizComponentStyleApplier {
  static applyTextStyles(
    config: TextComponentConfig,
    personalization: PersonalizationConfig
  ): React.CSSProperties {
    const baseStyles: React.CSSProperties = {
      fontFamily: this.getFontFamily(config.style.font_family),
      fontSize: this.getFontSize(config.style.size),
      color: config.style.color_scheme,
      textAlign: config.style.alignment,
      lineHeight: config.style.line_height,
      letterSpacing: config.style.letter_spacing
    };
    
    // 应用个性化样式
    if (personalization.layer3_skin_base?.fonts) {
      baseStyles.fontSize = `${baseStyles.fontSize}px * ${personalization.layer3_skin_base.fonts.size_scale}`;
    }
    
    if (personalization.layer5_accessibility?.large_text) {
      baseStyles.fontSize = `${parseFloat(baseStyles.fontSize as string) * 1.2}px`;
    }
    
    return baseStyles;
  }
  
  private static getFontFamily(family: string): string {
    const fontMap = {
      'modern': 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
      'traditional': 'PingFang SC, Source Han Sans, sans-serif',
      'calligraphy': 'STKaiti, KaiTi, serif'
    };
    
    return fontMap[family] || fontMap.modern;
  }
  
  private static getFontSize(size: string): number {
    const sizeMap = {
      'small': 14,
      'medium': 17,
      'large': 20,
      'title': 24
    };
    
    return sizeMap[size] || sizeMap.medium;
  }
}
```

### 4. 动画系统

```typescript
// 动画管理器
class QuizAnimationManager {
  static createTextAnimation(
    type: string,
    element: HTMLElement,
    config: any
  ): Animation {
    switch (type) {
      case 'typewriter':
        return this.createTypewriterAnimation(element, config);
      case 'brush_stroke':
        return this.createBrushStrokeAnimation(element, config);
      case 'fade_in':
        return this.createFadeInAnimation(element, config);
      default:
        return this.createFadeInAnimation(element, config);
    }
  }
  
  private static createTypewriterAnimation(
    element: HTMLElement,
    config: any
  ): Animation {
    const text = element.textContent || '';
    element.textContent = '';
    
    return element.animate([
      { opacity: 1 }
    ], {
      duration: text.length * 50,
      easing: 'linear',
      fill: 'forwards'
    });
  }
  
  private static createBrushStrokeAnimation(
    element: HTMLElement,
    config: any
  ): Animation {
    return element.animate([
      { 
        opacity: 0,
        transform: 'translateY(10px)',
        filter: 'blur(2px)'
      },
      { 
        opacity: 1,
        transform: 'translateY(0)',
        filter: 'blur(0px)'
      }
    ], {
      duration: 600,
      easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
      fill: 'forwards'
    });
  }
}
```

## 🧪 测试策略

### 1. 单元测试

```typescript
// 组件测试示例
describe('TextComponent', () => {
  const mockConfig: TextComponentConfig = {
    layout_id: 'standard_text',
    style: {
      font_family: 'modern',
      size: 'medium',
      color_scheme: '#333333',
      alignment: 'left',
      line_height: 1.5,
      letter_spacing: 0
    },
    content: {
      text_localized: {
        'zh': '测试文本',
        'en': 'Test text'
      }
    }
  };
  
  it('renders text correctly', () => {
    render(
      <TextComponent
        config={mockConfig}
        content={mockConfig.content}
      />
    );
    
    expect(screen.getByText('测试文本')).toBeInTheDocument();
  });
  
  it('applies personalization correctly', () => {
    const personalization: PersonalizationConfig = {
      layer5_accessibility: {
        large_text: true
      }
    };
    
    render(
      <TextComponent
        config={mockConfig}
        content={mockConfig.content}
        personalization={personalization}
      />
    );
    
    const textElement = screen.getByText('测试文本');
    expect(textElement).toHaveStyle({ fontSize: '20.4px' }); // 17 * 1.2
  });
});
```

### 2. 集成测试

```typescript
// ViewFactory集成测试
describe('Quiz Components ViewFactory Integration', () => {
  let viewFactory: QuizEnhancedViewFactory;
  
  beforeEach(() => {
    viewFactory = new QuizEnhancedViewFactory();
  });
  
  it('creates text component from config', () => {
    const questionData: QuestionPresentationData = {
      question_id: 'test_q1',
      question_text_localized: '测试问题',
      primary_interaction_type: 'TEXT_DISPLAY',
      personalized_ui_config: {},
      // ... 其他必需字段
    };
    
    const component = viewFactory.createQuizComponent(
      'TextComponent',
      questionData
    );
    
    expect(component).toBeTruthy();
    expect(component.type).toBe(TextComponent);
  });
});
```

## 📦 交付计划

### 里程碑1: 核心组件 (第3周)
- ✅ TextComponent 完成
- ✅ ButtonComponent 完成  
- ✅ SelectorComponent 完成
- ✅ 基础测试覆盖率 >80%

### 里程碑2: 交互组件 (第6周)
- ✅ SliderComponent 完成
- ✅ RatingComponent 完成
- ✅ 组件集成测试完成
- ✅ 性能基准测试完成

### 里程碑3: 完整系统 (第8周)
- ✅ 所有基础组件完成
- ✅ ViewFactory完全集成
- ✅ 文档和示例完成
- ✅ 生产环境部署就绪

## 🎯 成功指标

- **功能完整性**: 8个基础组件全部实现
- **配置驱动**: 100%通过后端JSON配置控制
- **性能指标**: 组件渲染时间 <16ms (60fps)
- **可访问性**: WCAG 2.1 AA级别合规
- **测试覆盖率**: 单元测试 >85%，集成测试 >70%
- **文档完整性**: 每个组件都有完整的API文档和使用示例

这个实现计划为Quiz基础组件的开发提供了清晰的路线图和技术指导，确保组件系统的高质量交付。
