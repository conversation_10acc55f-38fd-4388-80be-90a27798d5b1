import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';

interface TransitionProps {
  children: React.ReactNode;
  className?: string;
  show?: boolean;
  type?: 'fade' | 'slide' | 'scale';
}

const variants = {
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  slide: {
    initial: { x: -20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 20, opacity: 0 },
  },
  scale: {
    initial: { scale: 0.95, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.95, opacity: 0 },
  },
};

export const Transition = ({
  children,
  className,
  show = true,
  type = 'fade',
}: TransitionProps) => {
  return (
    <AnimatePresence mode="wait">
      {show && (
        <motion.div
          className={cn(className)}
          initial="initial"
          animate="animate"
          exit="exit"
          variants={variants[type]}
          transition={{ duration: 0.2 }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export const PageTransition = ({ children }: { children: React.ReactNode }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  );
};
