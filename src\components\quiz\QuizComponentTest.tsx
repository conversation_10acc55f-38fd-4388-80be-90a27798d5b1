/**
 * Quiz组件测试页面
 * 用于测试和展示Quiz基础组件
 */

import React, { useState, useEffect } from 'react';
import {
  TextComponent,
  ButtonComponent,
  SelectorComponent,
  SliderComponent,
  RatingComponent,
  DropdownComponent,
  ImageComponent,
  TextInputComponent,
  ImageSelectorComponent,
  ProgressIndicatorComponent,
  AudioPlayerComponent,
  VideoPlayerComponent,
  DraggableListComponent,
  NPCCharacterComponent,
  DialogueComponent,
  type TextComponentConfig,
  type ButtonComponentConfig,
  type SelectorComponentConfig,
  type SliderComponentConfig,
  type RatingComponentConfig,
  type DropdownComponentConfig,
  type ImageComponentConfig,
  type TextInputComponentConfig,
  type ImageSelectorComponentConfig,
  type ProgressIndicatorComponentConfig,
  type AudioPlayerComponentConfig,
  type VideoPlayerComponentConfig,
  type DraggableListComponentConfig,
  type NPCCharacterComponentConfig,
  type DialogueComponentConfig,
  type SelectorOption,
  type DropdownOption,
  type ImageSelectorOption,
  type DraggableListItem,
  type DialogueMessage,
  type InteractionEvent,
  type PersonalizationConfig
} from './base';
import {
  QuizComponentFactory,
  TextComponentPresets,
  ButtonComponentPresets,
  SelectorComponentPresets,
  SliderComponentPresets,
  RatingComponentPresets,
  DropdownComponentPresets,
  ImageComponentPresets,
  TextInputComponentPresets,
  ImageSelectorComponentPresets,
  ProgressIndicatorComponentPresets,
  AudioPlayerComponentPresets,
  VideoPlayerComponentPresets,
  DraggableListComponentPresets,
  NPCCharacterComponentPresets,
  DialogueComponentPresets,
  CommonTexts
} from './presets/ComponentPresets';
import {
  ThemeManager,
  ModernTheme,
  TraditionalTCMTheme,
  GamifiedTheme,
  DarkTheme,
  type ThemeConfig
} from './themes/ThemeSystem';

const QuizComponentTest: React.FC = () => {
  const [selectedValues, setSelectedValues] = useState<(string | number)[]>([]);
  const [interactionLog, setInteractionLog] = useState<InteractionEvent[]>([]);
  const [currentTheme, setCurrentTheme] = useState<string>('modern');
  const [showPresets, setShowPresets] = useState<boolean>(false);
  const [sliderValues, setSliderValues] = useState<Record<string, number>>({
    standard: 50,
    bamboo: 30,
    ink_brush: 70,
    dragon_spine: 25,
    mountain_ridge: 80,
    river_flow: 60,
    panda_paw: 40
  });
  const [ratingValues, setRatingValues] = useState<Record<string, number>>({
    star: 3,
    lotus: 4,
    taiji: 2,
    gourd: 5,
    heart: 3,
    dot: 4
  });
  const [dropdownValues, setDropdownValues] = useState<Record<string, string | number | null>>({
    constitution: null,
    symptom: null,
    traditional: null,
    floating: null
  });
  const [textInputValues, setTextInputValues] = useState<Record<string, string>>({
    standard: '',
    traditional: '',
    ink_brush: '',
    bamboo: '',
    textarea: ''
  });
  const [imageSelectorValues, setImageSelectorValues] = useState<Record<string, (string | number)[]>>({
    emotion: [],
    symptom: [],
    grid: [],
    standard: []
  });
  const [progressValues, setProgressValues] = useState<Record<string, { current: number; total: number }>>({
    bar: { current: 3, total: 10 },
    circle: { current: 7, total: 10 },
    lotus: { current: 5, total: 8 },
    bamboo: { current: 4, total: 6 },
    steps: { current: 2, total: 5 }
  });
  const [audioPlayerStates, setAudioPlayerStates] = useState<Record<string, { isPlaying: boolean; currentTime: number; duration: number }>>({
    standard: { isPlaying: false, currentTime: 0, duration: 0 },
    traditional: { isPlaying: false, currentTime: 0, duration: 0 },
    minimal: { isPlaying: false, currentTime: 0, duration: 0 }
  });
  const [videoPlayerStates, setVideoPlayerStates] = useState<Record<string, { isPlaying: boolean; currentTime: number; duration: number }>>({
    standard: { isPlaying: false, currentTime: 0, duration: 0 },
    traditional: { isPlaying: false, currentTime: 0, duration: 0 },
    minimal: { isPlaying: false, currentTime: 0, duration: 0 }
  });
  const [draggableListItems, setDraggableListItems] = useState<Record<string, DraggableListItem[]>>({
    standard: [
      { id: '1', content: { zh: '第一项', en: 'First item' }, value: 1, order: 0 },
      { id: '2', content: { zh: '第二项', en: 'Second item' }, value: 2, order: 1 },
      { id: '3', content: { zh: '第三项', en: 'Third item' }, value: 3, order: 2 },
      { id: '4', content: { zh: '第四项', en: 'Fourth item' }, value: 4, order: 3 }
    ],
    traditional: [
      { id: '1', content: { zh: '气虚质', en: 'Qi Deficiency' }, value: 'qi_deficiency', order: 0 },
      { id: '2', content: { zh: '阳虚质', en: 'Yang Deficiency' }, value: 'yang_deficiency', order: 1 },
      { id: '3', content: { zh: '阴虚质', en: 'Yin Deficiency' }, value: 'yin_deficiency', order: 2 }
    ],
    minimal: [
      { id: '1', content: { zh: '重要', en: 'Important' }, value: 'high', order: 0 },
      { id: '2', content: { zh: '一般', en: 'Normal' }, value: 'medium', order: 1 },
      { id: '3', content: { zh: '较低', en: 'Low' }, value: 'low', order: 2 }
    ]
  });
  const [npcCharacterStates, setNpcCharacterStates] = useState<Record<string, { emotion: string; isAnimating: boolean }>>({
    traditional_doctor: { emotion: 'wise', isAnimating: false },
    wise_elder: { emotion: 'neutral', isAnimating: false },
    friendly_guide: { emotion: 'happy', isAnimating: false },
    mystical_sage: { emotion: 'wise', isAnimating: false }
  });
  const [dialogueStates, setDialogueStates] = useState<Record<string, { messages: DialogueMessage[]; currentMessageIndex: number }>>({
    standard: {
      messages: [
        { id: '1', speaker: 'npc', content: { zh: '您好，欢迎来到中医情绪评估系统。', en: 'Hello, welcome to the TCM Emotion Assessment System.' }, timestamp: Date.now() - 10000 },
        { id: '2', speaker: 'user', content: { zh: '您好，我想了解一下我的情绪状态。', en: 'Hello, I would like to understand my emotional state.' }, timestamp: Date.now() - 5000 },
        { id: '3', speaker: 'npc', content: { zh: '很好，我将为您进行专业的评估。请回答以下问题。', en: 'Great, I will provide you with a professional assessment. Please answer the following questions.' }, timestamp: Date.now() }
      ],
      currentMessageIndex: 2
    },
    traditional: {
      messages: [
        { id: '1', speaker: 'npc', content: { zh: '古人云："知己知彼，百战不殆。"了解自己的情绪状态，是养生的第一步。', en: 'As the ancients said: "Know yourself and know your enemy, and you will never be defeated." Understanding your emotional state is the first step to wellness.' }, timestamp: Date.now() - 8000 },
        { id: '2', speaker: 'user', content: { zh: '请指导我如何开始。', en: 'Please guide me on how to start.' }, timestamp: Date.now() - 3000 }
      ],
      currentMessageIndex: 1
    },
    minimal: {
      messages: [
        { id: '1', speaker: 'system', content: { zh: '系统已准备就绪。', en: 'System ready.' }, timestamp: Date.now() }
      ],
      currentMessageIndex: 0
    }
  });

  // 初始化主题系统
  useEffect(() => {
    ThemeManager.initialize('modern');
  }, []);

  // 处理主题切换
  const handleThemeChange = (themeName: string) => {
    if (ThemeManager.setCurrentTheme(themeName)) {
      setCurrentTheme(themeName);
    }
  };

  // 个性化配置示例
  const personalizationConfig: PersonalizationConfig = {
    layer3_skin_base: {
      fonts: {
        size_scale: 1.1,
        primary_font: 'traditional'
      },
      colors: {
        primary: '#D32F2F'
      },
      animations: {
        enable_animations: true,
        animation_speed: 'normal',
        reduce_motion: false
      }
    },
    layer5_accessibility: {
      large_text: false,
      high_contrast: false,
      reduce_motion: false,
      keyboard_navigation: true,
      screen_reader_support: true
    }
  };

  // 文本组件配置
  const textConfig: TextComponentConfig = {
    id: 'test-text',
    component_type: 'text_component',
    layout_id: 'dialogue_bubble',
    content: {
      text_localized: {
        zh: '欢迎使用Quiz组件系统！这是一个支持中医文化元素的文本组件。',
        en: 'Welcome to the Quiz Component System! This is a text component with TCM cultural elements.'
      },
      emphasis_words: ['Quiz组件系统', 'TCM cultural elements'],
      animation_effect: 'brush_stroke'
    },
    style: {
      font_family: 'traditional',
      size: 'large',
      color_scheme: '#2E7D32',
      alignment: 'center',
      line_height: 1.6,
      letter_spacing: 0.02
    }
  };

  // 按钮组件配置
  const buttonConfigs: ButtonComponentConfig[] = [
    {
      id: 'standard-button',
      component_type: 'button_component',
      layout_id: 'standard_button',
      content: {
        text_localized: {
          zh: '标准按钮',
          en: 'Standard Button'
        },
        icon_name: 'check',
        loading_state: false
      },
      style: {
        size: 'medium',
        variant: 'primary',
        shape: 'rounded',
        icon_position: 'left',
        hover_effect: 'scale'
      },
      feedback: {
        haptic_feedback: true,
        sound_effect: 'click',
        animation: 'bounce'
      }
    },
    {
      id: 'jade-button',
      component_type: 'button_component',
      layout_id: 'jade_pendant',
      content: {
        text_localized: {
          zh: '玉佩',
          en: 'Jade'
        },
        loading_state: false
      },
      style: {
        size: 'large',
        variant: 'primary',
        shape: 'custom',
        icon_position: 'none',
        hover_effect: 'glow'
      },
      feedback: {
        haptic_feedback: true,
        sound_effect: 'jade_clink',
        animation: 'pulse'
      }
    },
    {
      id: 'seal-button',
      component_type: 'button_component',
      layout_id: 'seal_stamp',
      content: {
        text_localized: {
          zh: '确认',
          en: 'Confirm'
        },
        loading_state: false
      },
      style: {
        size: 'medium',
        variant: 'primary',
        shape: 'rectangle',
        icon_position: 'none',
        hover_effect: 'shadow'
      },
      feedback: {
        haptic_feedback: true,
        sound_effect: 'stamp',
        animation: 'bounce'
      }
    }
  ];

  // 选择器组件配置
  const selectorOptions: SelectorOption[] = [
    {
      id: 'joy',
      value: 'joy',
      text_localized: {
        zh: '喜悦',
        en: 'Joy'
      },
      display_style: 'icon_text',
      icon_name: 'smile'
    },
    {
      id: 'calm',
      value: 'calm',
      text_localized: {
        zh: '平静',
        en: 'Calm'
      },
      display_style: 'icon_text',
      icon_name: 'leaf'
    },
    {
      id: 'excited',
      value: 'excited',
      text_localized: {
        zh: '兴奋',
        en: 'Excited'
      },
      display_style: 'icon_text',
      icon_name: 'star'
    },
    {
      id: 'thoughtful',
      value: 'thoughtful',
      text_localized: {
        zh: '沉思',
        en: 'Thoughtful'
      },
      display_style: 'icon_text',
      icon_name: 'brain'
    }
  ];

  const selectorConfigs: SelectorComponentConfig[] = [
    {
      id: 'vertical-selector',
      component_type: 'selector_component',
      layout_id: 'vertical_list',
      options: selectorOptions,
      style: {
        selection_mode: 'single',
        marker_style: 'chinese_marker',
        spacing: 12,
        hover_effect: true,
        animation_style: 'scale_in'
      },
      validation: {
        required: true,
        min_selections: 1,
        max_selections: 1
      }
    },
    {
      id: 'grid-selector',
      component_type: 'selector_component',
      layout_id: 'grid_layout',
      options: selectorOptions,
      style: {
        selection_mode: 'multiple',
        marker_style: 'circle',
        spacing: 16,
        hover_effect: true,
        animation_style: 'bounce'
      },
      validation: {
        required: false,
        min_selections: 0,
        max_selections: 3
      }
    }
  ];

  // 处理交互事件
  const handleInteraction = (event: InteractionEvent) => {
    console.log('Interaction Event:', event);
    setInteractionLog(prev => [event, ...prev.slice(0, 9)]); // 保留最近10条记录
  };

  // 处理按钮点击
  const handleButtonClick = (buttonId: string) => {
    console.log(`Button clicked: ${buttonId}`);
  };

  // 处理选择器变化
  const handleSelectionChange = (values: (string | number)[]) => {
    setSelectedValues(values);
    console.log('Selection changed:', values);
  };

  // 处理滑块变化
  const handleSliderChange = (sliderId: string, value: number) => {
    setSliderValues(prev => ({ ...prev, [sliderId]: value }));
    console.log(`Slider ${sliderId} changed:`, value);
  };

  // 处理评分变化
  const handleRatingChange = (ratingId: string, value: number) => {
    setRatingValues(prev => ({ ...prev, [ratingId]: value }));
    console.log(`Rating ${ratingId} changed:`, value);
  };

  // 处理下拉选择器变化
  const handleDropdownChange = (dropdownId: string, value: string | number | null) => {
    setDropdownValues(prev => ({ ...prev, [dropdownId]: value }));
    console.log(`Dropdown ${dropdownId} changed:`, value);
  };

  // 处理文本输入变化
  const handleTextInputChange = (inputId: string, value: string) => {
    setTextInputValues(prev => ({ ...prev, [inputId]: value }));
    console.log(`Text input ${inputId} changed:`, value);
  };

  // 处理图片选择器变化
  const handleImageSelectorChange = (selectorId: string, values: (string | number)[]) => {
    setImageSelectorValues(prev => ({ ...prev, [selectorId]: values }));
    console.log(`Image selector ${selectorId} changed:`, values);
  };

  // 处理进度变化 (模拟进度更新)
  const handleProgressUpdate = (progressId: string) => {
    setProgressValues(prev => {
      const current = prev[progressId];
      const newCurrent = current.current < current.total ? current.current + 1 : 0;
      return {
        ...prev,
        [progressId]: { ...current, current: newCurrent }
      };
    });
  };

  // 处理音频播放器事件
  const handleAudioPlayerEvent = (componentId: string, event: InteractionEvent) => {
    if (event.type === 'click' && event.data?.action) {
      const action = event.data.action;
      if (action === 'play' || action === 'pause') {
        setAudioPlayerStates(prev => ({
          ...prev,
          [componentId]: {
            ...prev[componentId],
            isPlaying: action === 'play'
          }
        }));
      }
    }
  };

  // 处理视频播放器事件
  const handleVideoPlayerEvent = (componentId: string, event: InteractionEvent) => {
    if (event.type === 'click' && event.data?.action) {
      const action = event.data.action;
      if (action === 'play' || action === 'pause') {
        setVideoPlayerStates(prev => ({
          ...prev,
          [componentId]: {
            ...prev[componentId],
            isPlaying: action === 'play'
          }
        }));
      }
    }
  };

  // 处理拖拽列表事件
  const handleDraggableListEvent = (componentId: string, event: InteractionEvent) => {
    if (event.type === 'drag' && event.data?.action === 'reorder') {
      setDraggableListItems(prev => ({
        ...prev,
        [componentId]: event.data.items as DraggableListItem[]
      }));
    }
  };

  // 处理NPC角色事件
  const handleNPCCharacterEvent = (componentId: string, event: InteractionEvent) => {
    if (event.type === 'click' && event.data?.action === 'emotion_change') {
      setNpcCharacterStates(prev => ({
        ...prev,
        [componentId]: {
          ...prev[componentId],
          emotion: event.data.emotion as string
        }
      }));
    }
  };

  // 处理对话事件
  const handleDialogueEvent = (componentId: string, event: InteractionEvent) => {
    if (event.type === 'select' && event.data?.action === 'option_select') {
      // 添加用户选择的消息
      const userMessage: DialogueMessage = {
        id: `user-${Date.now()}`,
        speaker: 'user',
        content: { zh: event.data.text as string, en: event.data.text as string },
        timestamp: Date.now()
      };

      setDialogueStates(prev => ({
        ...prev,
        [componentId]: {
          ...prev[componentId],
          messages: [...prev[componentId].messages, userMessage],
          currentMessageIndex: prev[componentId].messages.length
        }
      }));
    }
  };

  return (
    <div className="quiz-component-test" style={{ padding: '20px', maxWidth: '1000px', margin: '0 auto' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '40px', color: 'var(--color-primary, #2E7D32)' }}>
        Quiz组件测试页面
      </h1>

      {/* 主题切换器 */}
      <section style={{ marginBottom: '40px', padding: '20px', background: 'var(--color-surface, #f5f5f5)', borderRadius: '8px' }}>
        <h2 style={{ marginBottom: '20px', color: 'var(--color-text-primary, #212121)' }}>主题切换</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          {ThemeManager.getAllThemes().map((theme) => (
            <button
              key={theme.name}
              onClick={() => handleThemeChange(theme.name)}
              style={{
                padding: '8px 16px',
                border: currentTheme === theme.name ? '2px solid var(--color-primary)' : '1px solid var(--color-border)',
                borderRadius: '6px',
                background: currentTheme === theme.name ? 'var(--color-primary)' : 'var(--color-background)',
                color: currentTheme === theme.name ? 'white' : 'var(--color-text-primary)',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: currentTheme === theme.name ? 'bold' : 'normal'
              }}
            >
              {theme.display_name.zh}
            </button>
          ))}
        </div>
        <p style={{ marginTop: '10px', fontSize: '14px', color: 'var(--color-text-secondary)' }}>
          当前主题: {ThemeManager.getCurrentTheme().display_name.zh} - {ThemeManager.getCurrentTheme().description.zh}
        </p>
      </section>

      {/* 预设演示 */}
      <section style={{ marginBottom: '40px', padding: '20px', background: 'var(--color-surface, #f5f5f5)', borderRadius: '8px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h2 style={{ color: 'var(--color-text-primary, #212121)' }}>组件预设演示</h2>
          <button
            onClick={() => setShowPresets(!showPresets)}
            style={{
              padding: '6px 12px',
              border: '1px solid var(--color-border)',
              borderRadius: '4px',
              background: 'var(--color-background)',
              color: 'var(--color-text-primary)',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            {showPresets ? '隐藏预设' : '显示预设'}
          </button>
        </div>

        {showPresets && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {/* 文本预设 */}
            <div>
              <h3 style={{ marginBottom: '15px', color: 'var(--color-text-primary)' }}>文本组件预设</h3>
              {Object.entries(TextComponentPresets).map(([presetName, preset]) => (
                <div key={presetName} style={{ marginBottom: '10px' }}>
                  <TextComponent
                    id={`preset-text-${presetName}`}
                    config={QuizComponentFactory.createText(
                      { zh: `${presetName} 预设示例`, en: `${presetName} preset example` },
                      presetName as any
                    )}
                    content={{ text_localized: { zh: `${presetName} 预设示例`, en: `${presetName} preset example` } }}
                    personalization={personalizationConfig}
                    onInteraction={handleInteraction}
                  />
                </div>
              ))}
            </div>

            {/* 按钮预设 */}
            <div>
              <h3 style={{ marginBottom: '15px', color: 'var(--color-text-primary)' }}>按钮组件预设</h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                {Object.entries(ButtonComponentPresets).map(([presetName, preset]) => (
                  <ButtonComponent
                    key={presetName}
                    config={QuizComponentFactory.createButton(
                      { zh: `${presetName}`, en: presetName },
                      presetName as any
                    )}
                    personalization={personalizationConfig}
                    onInteraction={handleInteraction}
                    onClick={() => console.log(`${presetName} clicked`)}
                  />
                ))}
              </div>
            </div>
          </div>
        )}
      </section>

      {/* 文本组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>文本组件 (TextComponent)</h2>
        <TextComponent
          id="test-text-1"
          config={textConfig}
          content={textConfig.content}
          personalization={personalizationConfig}
          onInteraction={handleInteraction}
        />
      </section>

      {/* 按钮组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>按钮组件 (ButtonComponent)</h2>
        <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap', justifyContent: 'center' }}>
          {buttonConfigs.map((config) => (
            <ButtonComponent
              key={config.id}
              config={config}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
              onClick={() => handleButtonClick(config.id)}
            />
          ))}
        </div>
      </section>

      {/* 选择器组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>选择器组件 (SelectorComponent)</h2>

        <h3>垂直列表布局 (单选)</h3>
        <SelectorComponent
          config={selectorConfigs[0]}
          options={selectorOptions}
          selectedValues={selectedValues}
          onSelectionChange={handleSelectionChange}
          personalization={personalizationConfig}
          onInteraction={handleInteraction}
          style={{ marginBottom: '20px' }}
        />

        <h3>网格布局 (多选)</h3>
        <SelectorComponent
          config={selectorConfigs[1]}
          options={selectorOptions}
          selectedValues={selectedValues}
          onSelectionChange={handleSelectionChange}
          personalization={personalizationConfig}
          onInteraction={handleInteraction}
        />
      </section>

      {/* 滑块组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>滑块组件 (SliderComponent)</h2>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '30px' }}>
          {/* 标准滑块 */}
          <div>
            <h3>标准滑块</h3>
            <SliderComponent
              config={QuizComponentFactory.createSlider(
                { min: 0, max: 100, step: 1, default_value: sliderValues.standard },
                { start_label: { zh: '最小', en: 'Min' }, end_label: { zh: '最大', en: 'Max' }, unit: '%' },
                'standard_slider'
              )}
              value={sliderValues.standard}
              onChange={(value) => handleSliderChange('standard', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 竹节滑块 */}
          <div>
            <h3>竹节滑块 (中医风格)</h3>
            <SliderComponent
              config={QuizComponentFactory.createSlider(
                { min: 0, max: 100, step: 5, default_value: sliderValues.bamboo },
                { start_label: { zh: '虚', en: 'Weak' }, end_label: { zh: '实', en: 'Strong' }, unit: '分' },
                'bamboo_slider'
              )}
              value={sliderValues.bamboo}
              onChange={(value) => handleSliderChange('bamboo', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 墨迹滑块 */}
          <div>
            <h3>墨迹滑块 (书法风格)</h3>
            <SliderComponent
              config={QuizComponentFactory.createSlider(
                { min: 0, max: 100, step: 1, default_value: sliderValues.ink_brush },
                { start_label: { zh: '淡', en: 'Light' }, end_label: { zh: '浓', en: 'Dark' } },
                'ink_brush_slider'
              )}
              value={sliderValues.ink_brush}
              onChange={(value) => handleSliderChange('ink_brush', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 龙脊滑块 */}
          <div>
            <h3>龙脊滑块 (古典风格)</h3>
            <SliderComponent
              config={QuizComponentFactory.createSlider(
                { min: 1, max: 10, step: 1, default_value: sliderValues.dragon_spine },
                { start_label: { zh: '轻', en: 'Light' }, end_label: { zh: '重', en: 'Heavy' }, unit: '级' },
                'dragon_spine_slider'
              )}
              value={sliderValues.dragon_spine}
              onChange={(value) => handleSliderChange('dragon_spine', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 山脊滑块 */}
          <div>
            <h3>山脊滑块 (自然风格)</h3>
            <SliderComponent
              config={QuizComponentFactory.createSlider(
                { min: 0, max: 100, step: 10, default_value: sliderValues.mountain_ridge },
                { start_label: { zh: '低', en: 'Low' }, end_label: { zh: '高', en: 'High' }, unit: '米' },
                'mountain_ridge_slider'
              )}
              value={sliderValues.mountain_ridge}
              onChange={(value) => handleSliderChange('mountain_ridge', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 河流滑块 */}
          <div>
            <h3>河流滑块 (流动风格)</h3>
            <SliderComponent
              config={QuizComponentFactory.createSlider(
                { min: 0, max: 100, step: 5, default_value: sliderValues.river_flow },
                { start_label: { zh: '缓', en: 'Slow' }, end_label: { zh: '急', en: 'Fast' }, unit: '流速' },
                'river_flow_slider'
              )}
              value={sliderValues.river_flow}
              onChange={(value) => handleSliderChange('river_flow', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 熊猫爪印滑块 */}
          <div>
            <h3>熊猫爪印滑块 (可爱风格)</h3>
            <SliderComponent
              config={QuizComponentFactory.createSlider(
                { min: 0, max: 100, step: 1, default_value: sliderValues.panda_paw },
                { start_label: { zh: '少', en: 'Few' }, end_label: { zh: '多', en: 'Many' }, unit: '个' },
                'panda_paw_slider'
              )}
              value={sliderValues.panda_paw}
              onChange={(value) => handleSliderChange('panda_paw', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>
        </div>
      </section>

      {/* 评分组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>评分组件 (RatingComponent)</h2>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '30px' }}>
          {/* 星级评分 */}
          <div>
            <h3>星级评分</h3>
            <RatingComponent
              config={QuizComponentFactory.createRating(
                { min_value: 1, max_value: 5, default_value: ratingValues.star },
                { start_label: { zh: '很差', en: 'Poor' }, end_label: { zh: '很好', en: 'Excellent' } },
                'star_rating'
              )}
              value={ratingValues.star}
              onChange={(value) => handleRatingChange('star', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 莲花评分 */}
          <div>
            <h3>莲花评分 (中医风格)</h3>
            <RatingComponent
              config={QuizComponentFactory.createRating(
                { min_value: 1, max_value: 5, default_value: ratingValues.lotus },
                { start_label: { zh: '虚弱', en: 'Weak' }, end_label: { zh: '强壮', en: 'Strong' }, value_suffix: '朵' },
                'lotus_rating'
              )}
              value={ratingValues.lotus}
              onChange={(value) => handleRatingChange('lotus', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 太极评分 */}
          <div>
            <h3>太极评分 (阴阳平衡)</h3>
            <RatingComponent
              config={QuizComponentFactory.createRating(
                { min_value: 1, max_value: 5, default_value: ratingValues.taiji },
                { start_label: { zh: '失衡', en: 'Imbalanced' }, end_label: { zh: '平衡', en: 'Balanced' } },
                'taiji_rating'
              )}
              value={ratingValues.taiji}
              onChange={(value) => handleRatingChange('taiji', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 葫芦评分 */}
          <div>
            <h3>葫芦评分 (药材品质)</h3>
            <RatingComponent
              config={QuizComponentFactory.createRating(
                { min_value: 1, max_value: 5, default_value: ratingValues.gourd },
                { start_label: { zh: '劣质', en: 'Poor Quality' }, end_label: { zh: '上品', en: 'Premium' } },
                'gourd_rating'
              )}
              value={ratingValues.gourd}
              onChange={(value) => handleRatingChange('gourd', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 爱心评分 */}
          <div>
            <h3>爱心评分 (情感强度)</h3>
            <RatingComponent
              config={QuizComponentFactory.createRating(
                { min_value: 1, max_value: 5, default_value: ratingValues.heart },
                { start_label: { zh: '冷淡', en: 'Cold' }, end_label: { zh: '热情', en: 'Passionate' } },
                'heart_rating'
              )}
              value={ratingValues.heart}
              onChange={(value) => handleRatingChange('heart', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 点状评分 */}
          <div>
            <h3>点状评分 (简约风格)</h3>
            <RatingComponent
              config={QuizComponentFactory.createRating(
                { min_value: 1, max_value: 5, default_value: ratingValues.dot },
                { start_label: { zh: '最低', en: 'Lowest' }, end_label: { zh: '最高', en: 'Highest' }, value_suffix: '分' },
                'dot_rating'
              )}
              value={ratingValues.dot}
              onChange={(value) => handleRatingChange('dot', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>
        </div>
      </section>

      {/* 下拉选择器组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>下拉选择器组件 (DropdownComponent)</h2>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '30px' }}>
          {/* 体质类型选择器 */}
          <div>
            <h3>体质类型选择器</h3>
            <DropdownComponent
              config={QuizComponentFactory.createDropdown([
                { id: 'qi_deficiency', value: 'qi_deficiency', text: { zh: '气虚质', en: 'Qi Deficiency' } },
                { id: 'yang_deficiency', value: 'yang_deficiency', text: { zh: '阳虚质', en: 'Yang Deficiency' } },
                { id: 'yin_deficiency', value: 'yin_deficiency', text: { zh: '阴虚质', en: 'Yin Deficiency' } },
                { id: 'phlegm_dampness', value: 'phlegm_dampness', text: { zh: '痰湿质', en: 'Phlegm-Dampness' } },
                { id: 'damp_heat', value: 'damp_heat', text: { zh: '湿热质', en: 'Damp-Heat' } }
              ], 'constitution_dropdown')}
              options={[
                { id: 'qi_deficiency', value: 'qi_deficiency', text_localized: { zh: '气虚质', en: 'Qi Deficiency' } },
                { id: 'yang_deficiency', value: 'yang_deficiency', text_localized: { zh: '阳虚质', en: 'Yang Deficiency' } },
                { id: 'yin_deficiency', value: 'yin_deficiency', text_localized: { zh: '阴虚质', en: 'Yin Deficiency' } },
                { id: 'phlegm_dampness', value: 'phlegm_dampness', text_localized: { zh: '痰湿质', en: 'Phlegm-Dampness' } },
                { id: 'damp_heat', value: 'damp_heat', text_localized: { zh: '湿热质', en: 'Damp-Heat' } }
              ]}
              value={dropdownValues.constitution}
              onChange={(value) => handleDropdownChange('constitution', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 症状选择器 */}
          <div>
            <h3>症状选择器</h3>
            <DropdownComponent
              config={QuizComponentFactory.createDropdown([
                { id: 'headache', value: 'headache', text: { zh: '头痛', en: 'Headache' }, icon: 'head' },
                { id: 'fatigue', value: 'fatigue', text: { zh: '疲劳', en: 'Fatigue' }, icon: 'tired' },
                { id: 'insomnia', value: 'insomnia', text: { zh: '失眠', en: 'Insomnia' }, icon: 'sleep' },
                { id: 'anxiety', value: 'anxiety', text: { zh: '焦虑', en: 'Anxiety' }, icon: 'worry' },
                { id: 'depression', value: 'depression', text: { zh: '抑郁', en: 'Depression' }, icon: 'sad' }
              ], 'symptom_dropdown')}
              options={[
                { id: 'headache', value: 'headache', text_localized: { zh: '头痛', en: 'Headache' }, icon_name: 'head' },
                { id: 'fatigue', value: 'fatigue', text_localized: { zh: '疲劳', en: 'Fatigue' }, icon_name: 'tired' },
                { id: 'insomnia', value: 'insomnia', text_localized: { zh: '失眠', en: 'Insomnia' }, icon_name: 'sleep' },
                { id: 'anxiety', value: 'anxiety', text_localized: { zh: '焦虑', en: 'Anxiety' }, icon_name: 'worry' },
                { id: 'depression', value: 'depression', text_localized: { zh: '抑郁', en: 'Depression' }, icon_name: 'sad' }
              ]}
              value={dropdownValues.symptom}
              onChange={(value) => handleDropdownChange('symptom', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 传统风格下拉选择器 */}
          <div>
            <h3>传统风格下拉选择器</h3>
            <DropdownComponent
              config={QuizComponentFactory.createDropdown([
                { id: 'spring', value: 'spring', text: { zh: '春季', en: 'Spring' } },
                { id: 'summer', value: 'summer', text: { zh: '夏季', en: 'Summer' } },
                { id: 'autumn', value: 'autumn', text: { zh: '秋季', en: 'Autumn' } },
                { id: 'winter', value: 'winter', text: { zh: '冬季', en: 'Winter' } }
              ], 'traditional_dropdown')}
              options={[
                { id: 'spring', value: 'spring', text_localized: { zh: '春季', en: 'Spring' } },
                { id: 'summer', value: 'summer', text_localized: { zh: '夏季', en: 'Summer' } },
                { id: 'autumn', value: 'autumn', text_localized: { zh: '秋季', en: 'Autumn' } },
                { id: 'winter', value: 'winter', text_localized: { zh: '冬季', en: 'Winter' } }
              ]}
              value={dropdownValues.traditional}
              onChange={(value) => handleDropdownChange('traditional', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 浮动风格下拉选择器 */}
          <div>
            <h3>浮动风格下拉选择器</h3>
            <DropdownComponent
              config={QuizComponentFactory.createDropdown([
                { id: 'mild', value: 1, text: { zh: '轻微', en: 'Mild' } },
                { id: 'moderate', value: 2, text: { zh: '中等', en: 'Moderate' } },
                { id: 'severe', value: 3, text: { zh: '严重', en: 'Severe' } },
                { id: 'critical', value: 4, text: { zh: '危重', en: 'Critical' }, disabled: true }
              ], 'floating_dropdown')}
              options={[
                { id: 'mild', value: 1, text_localized: { zh: '轻微', en: 'Mild' } },
                { id: 'moderate', value: 2, text_localized: { zh: '中等', en: 'Moderate' } },
                { id: 'severe', value: 3, text_localized: { zh: '严重', en: 'Severe' } },
                { id: 'critical', value: 4, text_localized: { zh: '危重', en: 'Critical' }, disabled: true }
              ]}
              value={dropdownValues.floating}
              onChange={(value) => handleDropdownChange('floating', value)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>
        </div>
      </section>

      {/* 图片组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>图片组件 (ImageComponent)</h2>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '30px' }}>
          {/* 标准图片 */}
          <div>
            <h3>标准图片</h3>
            <ImageComponent
              config={QuizComponentFactory.createImage(
                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
                { zh: '美丽的山景', en: 'Beautiful mountain landscape' },
                { zh: '宁静的自然风光', en: 'Peaceful natural scenery' },
                'standard_image'
              )}
              onLoad={() => console.log('Standard image loaded')}
              onError={(error) => console.log('Standard image error:', error)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 水墨画框 */}
          <div>
            <h3>水墨画框 (中医风格)</h3>
            <ImageComponent
              config={QuizComponentFactory.createImage(
                'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
                { zh: '中医草药', en: 'Traditional Chinese herbs' },
                { zh: '传统中医药材', en: 'Traditional Chinese medicine herbs' },
                'ink_wash_frame'
              )}
              onLoad={() => console.log('Ink wash frame image loaded')}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 传统画框 */}
          <div>
            <h3>传统画框 (古典风格)</h3>
            <ImageComponent
              config={QuizComponentFactory.createImage(
                'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
                { zh: '竹林', en: 'Bamboo forest' },
                { zh: '幽静的竹林小径', en: 'Quiet bamboo forest path' },
                'traditional_frame'
              )}
              onLoad={() => console.log('Traditional frame image loaded')}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 交互式图片 */}
          <div>
            <h3>交互式图片 (可点击)</h3>
            <ImageComponent
              config={QuizComponentFactory.createImage(
                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=225&fit=crop',
                { zh: '点击查看详情', en: 'Click for details' },
                { zh: '点击图片进行交互', en: 'Click image to interact' },
                'interactive_image'
              )}
              onClick={() => alert('图片被点击了！')}
              onLoad={() => console.log('Interactive image loaded')}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>
        </div>
      </section>

      {/* 文本输入组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>文本输入组件 (TextInputComponent)</h2>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '30px' }}>
          {/* 标准文本输入 */}
          <div>
            <h3>标准文本输入</h3>
            <TextInputComponent
              config={QuizComponentFactory.createTextInput(
                'text',
                { zh: '姓名', en: 'Name' },
                { zh: '请输入您的姓名', en: 'Please enter your name' },
                'standard_input'
              )}
              value={textInputValues.standard}
              onChange={(value) => handleTextInputChange('standard', value)}
              label={{ zh: '姓名', en: 'Name' }}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 传统中医风格输入 */}
          <div>
            <h3>传统中医风格输入</h3>
            <TextInputComponent
              config={QuizComponentFactory.createTextInput(
                'text',
                { zh: '体质描述', en: 'Constitution Description' },
                { zh: '请描述您的体质特点', en: 'Please describe your constitution' },
                'traditional_input',
                {
                  input: {
                    max_length: 100,
                    required: true
                  }
                }
              )}
              value={textInputValues.traditional}
              onChange={(value) => handleTextInputChange('traditional', value)}
              label={{ zh: '体质描述', en: 'Constitution Description' }}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 墨迹风格输入 */}
          <div>
            <h3>墨迹风格输入 (浮动标签)</h3>
            <TextInputComponent
              config={QuizComponentFactory.createTextInput(
                'email',
                { zh: '电子邮箱', en: 'Email Address' },
                { zh: '请输入邮箱地址', en: 'Please enter email address' },
                'ink_brush_input'
              )}
              value={textInputValues.ink_brush}
              onChange={(value) => handleTextInputChange('ink_brush', value)}
              label={{ zh: '电子邮箱', en: 'Email Address' }}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 竹节风格输入 */}
          <div>
            <h3>竹节风格输入 (内嵌标签)</h3>
            <TextInputComponent
              config={QuizComponentFactory.createTextInput(
                'number',
                { zh: '年龄', en: 'Age' },
                { zh: '请输入年龄', en: 'Please enter age' },
                'bamboo_input',
                {
                  input: {
                    min_length: 1,
                    max_length: 3
                  }
                }
              )}
              value={textInputValues.bamboo}
              onChange={(value) => handleTextInputChange('bamboo', value)}
              label={{ zh: '年龄', en: 'Age' }}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 多行文本输入 */}
          <div style={{ gridColumn: 'span 2' }}>
            <h3>多行文本输入 (症状描述)</h3>
            <TextInputComponent
              config={QuizComponentFactory.createTextInput(
                'textarea',
                { zh: '症状详细描述', en: 'Detailed Symptom Description' },
                { zh: '请详细描述您的症状...', en: 'Please describe your symptoms in detail...' },
                'textarea_input',
                {
                  input: {
                    max_length: 500,
                    required: false
                  }
                }
              )}
              value={textInputValues.textarea}
              onChange={(value) => handleTextInputChange('textarea', value)}
              label={{ zh: '症状详细描述', en: 'Detailed Symptom Description' }}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>
        </div>
      </section>

      {/* 图片选择器组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>图片选择器组件 (ImageSelectorComponent)</h2>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '40px' }}>
          {/* 情绪图片选择器 */}
          <div>
            <h3>情绪图片选择器 (单选)</h3>
            <ImageSelectorComponent
              config={QuizComponentFactory.createImageSelector([
                {
                  id: 'happy',
                  url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop',
                  alt_text: { zh: '快乐', en: 'Happy' },
                  title: { zh: '快乐', en: 'Happy' },
                  description: { zh: '感到愉悦和满足', en: 'Feeling joyful and content' },
                  value: 'happy'
                },
                {
                  id: 'sad',
                  url: 'https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=200&h=200&fit=crop',
                  alt_text: { zh: '悲伤', en: 'Sad' },
                  title: { zh: '悲伤', en: 'Sad' },
                  description: { zh: '感到沮丧和失落', en: 'Feeling down and disappointed' },
                  value: 'sad'
                },
                {
                  id: 'angry',
                  url: 'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=200&h=200&fit=crop',
                  alt_text: { zh: '愤怒', en: 'Angry' },
                  title: { zh: '愤怒', en: 'Angry' },
                  description: { zh: '感到生气和烦躁', en: 'Feeling mad and irritated' },
                  value: 'angry'
                },
                {
                  id: 'calm',
                  url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop',
                  alt_text: { zh: '平静', en: 'Calm' },
                  title: { zh: '平静', en: 'Calm' },
                  description: { zh: '感到宁静和放松', en: 'Feeling peaceful and relaxed' },
                  value: 'calm'
                }
              ], 'emotion_image_selector')}
              images={[
                {
                  id: 'happy',
                  url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop',
                  alt_text: { zh: '快乐', en: 'Happy' },
                  title: { zh: '快乐', en: 'Happy' },
                  description: { zh: '感到愉悦和满足', en: 'Feeling joyful and content' },
                  value: 'happy'
                },
                {
                  id: 'sad',
                  url: 'https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=200&h=200&fit=crop',
                  alt_text: { zh: '悲伤', en: 'Sad' },
                  title: { zh: '悲伤', en: 'Sad' },
                  description: { zh: '感到沮丧和失落', en: 'Feeling down and disappointed' },
                  value: 'sad'
                },
                {
                  id: 'angry',
                  url: 'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=200&h=200&fit=crop',
                  alt_text: { zh: '愤怒', en: 'Angry' },
                  title: { zh: '愤怒', en: 'Angry' },
                  description: { zh: '感到生气和烦躁', en: 'Feeling mad and irritated' },
                  value: 'angry'
                },
                {
                  id: 'calm',
                  url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop',
                  alt_text: { zh: '平静', en: 'Calm' },
                  title: { zh: '平静', en: 'Calm' },
                  description: { zh: '感到宁静和放松', en: 'Feeling peaceful and relaxed' },
                  value: 'calm'
                }
              ]}
              selectedValues={imageSelectorValues.emotion}
              onSelectionChange={(values) => handleImageSelectorChange('emotion', values)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>

          {/* 症状图片选择器 */}
          <div>
            <h3>症状图片选择器 (多选，最多3个)</h3>
            <ImageSelectorComponent
              config={QuizComponentFactory.createImageSelector([
                {
                  id: 'headache',
                  url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=200&h=200&fit=crop',
                  alt_text: { zh: '头痛', en: 'Headache' },
                  title: { zh: '头痛', en: 'Headache' },
                  value: 'headache'
                },
                {
                  id: 'fatigue',
                  url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop',
                  alt_text: { zh: '疲劳', en: 'Fatigue' },
                  title: { zh: '疲劳', en: 'Fatigue' },
                  value: 'fatigue'
                },
                {
                  id: 'insomnia',
                  url: 'https://images.unsplash.com/photo-1520637836862-4d197d17c93a?w=200&h=200&fit=crop',
                  alt_text: { zh: '失眠', en: 'Insomnia' },
                  title: { zh: '失眠', en: 'Insomnia' },
                  value: 'insomnia'
                },
                {
                  id: 'anxiety',
                  url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=200&fit=crop',
                  alt_text: { zh: '焦虑', en: 'Anxiety' },
                  title: { zh: '焦虑', en: 'Anxiety' },
                  value: 'anxiety'
                },
                {
                  id: 'depression',
                  url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop',
                  alt_text: { zh: '抑郁', en: 'Depression' },
                  title: { zh: '抑郁', en: 'Depression' },
                  value: 'depression'
                },
                {
                  id: 'stress',
                  url: 'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=200&h=200&fit=crop',
                  alt_text: { zh: '压力', en: 'Stress' },
                  title: { zh: '压力', en: 'Stress' },
                  value: 'stress'
                }
              ], 'symptom_image_selector')}
              images={[
                {
                  id: 'headache',
                  url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=200&h=200&fit=crop',
                  alt_text: { zh: '头痛', en: 'Headache' },
                  title: { zh: '头痛', en: 'Headache' },
                  value: 'headache'
                },
                {
                  id: 'fatigue',
                  url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200&h=200&fit=crop',
                  alt_text: { zh: '疲劳', en: 'Fatigue' },
                  title: { zh: '疲劳', en: 'Fatigue' },
                  value: 'fatigue'
                },
                {
                  id: 'insomnia',
                  url: 'https://images.unsplash.com/photo-1520637836862-4d197d17c93a?w=200&h=200&fit=crop',
                  alt_text: { zh: '失眠', en: 'Insomnia' },
                  title: { zh: '失眠', en: 'Insomnia' },
                  value: 'insomnia'
                },
                {
                  id: 'anxiety',
                  url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=200&fit=crop',
                  alt_text: { zh: '焦虑', en: 'Anxiety' },
                  title: { zh: '焦虑', en: 'Anxiety' },
                  value: 'anxiety'
                },
                {
                  id: 'depression',
                  url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop',
                  alt_text: { zh: '抑郁', en: 'Depression' },
                  title: { zh: '抑郁', en: 'Depression' },
                  value: 'depression'
                },
                {
                  id: 'stress',
                  url: 'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=200&h=200&fit=crop',
                  alt_text: { zh: '压力', en: 'Stress' },
                  title: { zh: '压力', en: 'Stress' },
                  value: 'stress'
                }
              ]}
              selectedValues={imageSelectorValues.symptom}
              onSelectionChange={(values) => handleImageSelectorChange('symptom', values)}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
          </div>
        </div>
      </section>

      {/* 进度指示器组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>进度指示器组件 (ProgressIndicatorComponent)</h2>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '30px' }}>
          {/* 标准条形进度条 */}
          <div>
            <h3>标准条形进度条</h3>
            <ProgressIndicatorComponent
              config={QuizComponentFactory.createProgressIndicator(
                progressValues.bar.current,
                progressValues.bar.total,
                { zh: '问卷完成进度', en: 'Quiz Progress' },
                'standard_progress_bar'
              )}
              current={progressValues.bar.current}
              total={progressValues.bar.total}
              onComplete={() => console.log('Bar progress completed!')}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
            <button
              onClick={() => handleProgressUpdate('bar')}
              style={{ marginTop: '10px', padding: '8px 16px', borderRadius: '4px', border: 'none', background: '#4CAF50', color: 'white', cursor: 'pointer' }}
            >
              更新进度
            </button>
          </div>

          {/* 圆形进度条 */}
          <div>
            <h3>圆形进度条</h3>
            <ProgressIndicatorComponent
              config={QuizComponentFactory.createProgressIndicator(
                progressValues.circle.current,
                progressValues.circle.total,
                { zh: '评估进度', en: 'Assessment Progress' },
                'circle_progress'
              )}
              current={progressValues.circle.current}
              total={progressValues.circle.total}
              onComplete={() => console.log('Circle progress completed!')}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
            <button
              onClick={() => handleProgressUpdate('circle')}
              style={{ marginTop: '10px', padding: '8px 16px', borderRadius: '4px', border: 'none', background: '#4CAF50', color: 'white', cursor: 'pointer' }}
            >
              更新进度
            </button>
          </div>

          {/* 莲花进度条 (中医风格) */}
          <div>
            <h3>莲花进度条 (中医风格)</h3>
            <ProgressIndicatorComponent
              config={QuizComponentFactory.createProgressIndicator(
                progressValues.lotus.current,
                progressValues.lotus.total,
                { zh: '莲花绽放进度', en: 'Lotus Bloom Progress' },
                'lotus_progress'
              )}
              current={progressValues.lotus.current}
              total={progressValues.lotus.total}
              onComplete={() => console.log('Lotus progress completed!')}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
            <button
              onClick={() => handleProgressUpdate('lotus')}
              style={{ marginTop: '10px', padding: '8px 16px', borderRadius: '4px', border: 'none', background: '#FF9800', color: 'white', cursor: 'pointer' }}
            >
              莲花绽放
            </button>
          </div>

          {/* 竹子进度条 (中医风格) */}
          <div>
            <h3>竹子进度条 (中医风格)</h3>
            <ProgressIndicatorComponent
              config={QuizComponentFactory.createProgressIndicator(
                progressValues.bamboo.current,
                progressValues.bamboo.total,
                { zh: '竹子生长进度', en: 'Bamboo Growth Progress' },
                'bamboo_progress'
              )}
              current={progressValues.bamboo.current}
              total={progressValues.bamboo.total}
              onComplete={() => console.log('Bamboo progress completed!')}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
            <button
              onClick={() => handleProgressUpdate('bamboo')}
              style={{ marginTop: '10px', padding: '8px 16px', borderRadius: '4px', border: 'none', background: '#8BC34A', color: 'white', cursor: 'pointer' }}
            >
              竹子生长
            </button>
          </div>

          {/* 步骤进度条 */}
          <div style={{ gridColumn: 'span 2' }}>
            <h3>步骤进度条</h3>
            <ProgressIndicatorComponent
              config={QuizComponentFactory.createProgressIndicator(
                progressValues.steps.current,
                progressValues.steps.total,
                { zh: '问卷步骤进度', en: 'Quiz Steps Progress' },
                'steps_progress'
              )}
              current={progressValues.steps.current}
              total={progressValues.steps.total}
              onComplete={() => console.log('Steps progress completed!')}
              personalization={personalizationConfig}
              onInteraction={handleInteraction}
            />
            <button
              onClick={() => handleProgressUpdate('steps')}
              style={{ marginTop: '10px', padding: '8px 16px', borderRadius: '4px', border: 'none', background: '#4CAF50', color: 'white', cursor: 'pointer' }}
            >
              下一步
            </button>
          </div>
        </div>
      </section>

      {/* 音频播放器组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>音频播放器组件 (AudioPlayerComponent)</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '30px' }}>
          {/* 标准音频播放器 */}
          <div>
            <h3>标准音频播放器</h3>
            <AudioPlayerComponent
              id="standard-audio-player"
              config={QuizComponentFactory.createAudioPlayer(
                'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
                'standard_audio_player'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleAudioPlayerEvent('standard', event)}
            />
          </div>

          {/* 传统音频播放器 */}
          <div>
            <h3>传统音频播放器 (中医风格)</h3>
            <AudioPlayerComponent
              id="traditional-audio-player"
              config={QuizComponentFactory.createAudioPlayer(
                'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
                'traditional_audio_player'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleAudioPlayerEvent('traditional', event)}
            />
          </div>

          {/* 简约音频播放器 */}
          <div>
            <h3>简约音频播放器</h3>
            <AudioPlayerComponent
              id="minimal-audio-player"
              config={QuizComponentFactory.createAudioPlayer(
                'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
                'minimal_audio_player'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleAudioPlayerEvent('minimal', event)}
            />
          </div>
        </div>
      </section>

      {/* 视频播放器组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>视频播放器组件 (VideoPlayerComponent)</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '30px' }}>
          {/* 标准视频播放器 */}
          <div>
            <h3>标准视频播放器</h3>
            <VideoPlayerComponent
              id="standard-video-player"
              config={QuizComponentFactory.createVideoPlayer(
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
                'standard_video_player'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleVideoPlayerEvent('standard', event)}
            />
          </div>

          {/* 传统视频播放器 */}
          <div>
            <h3>传统视频播放器 (中医风格)</h3>
            <VideoPlayerComponent
              id="traditional-video-player"
              config={QuizComponentFactory.createVideoPlayer(
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
                'traditional_video_player'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleVideoPlayerEvent('traditional', event)}
            />
          </div>

          {/* 简约视频播放器 */}
          <div>
            <h3>简约视频播放器</h3>
            <VideoPlayerComponent
              id="minimal-video-player"
              config={QuizComponentFactory.createVideoPlayer(
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
                'minimal_video_player'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleVideoPlayerEvent('minimal', event)}
            />
          </div>
        </div>
      </section>

      {/* 拖拽列表组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>拖拽列表组件 (DraggableListComponent)</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '30px' }}>
          {/* 标准拖拽列表 */}
          <div>
            <h3>标准拖拽列表</h3>
            <DraggableListComponent
              id="standard-draggable-list"
              config={QuizComponentFactory.createDraggableList(
                draggableListItems.standard,
                'standard_draggable_list'
              )}
              items={draggableListItems.standard}
              onReorder={(items) => setDraggableListItems(prev => ({ ...prev, standard: items }))}
              personalization={personalizationConfig}
              onInteraction={(event) => handleDraggableListEvent('standard', event)}
            />
          </div>

          {/* 传统拖拽列表 */}
          <div>
            <h3>传统拖拽列表 (中医风格)</h3>
            <DraggableListComponent
              id="traditional-draggable-list"
              config={QuizComponentFactory.createDraggableList(
                draggableListItems.traditional,
                'traditional_draggable_list'
              )}
              items={draggableListItems.traditional}
              onReorder={(items) => setDraggableListItems(prev => ({ ...prev, traditional: items }))}
              personalization={personalizationConfig}
              onInteraction={(event) => handleDraggableListEvent('traditional', event)}
            />
          </div>

          {/* 简约拖拽列表 */}
          <div>
            <h3>简约拖拽列表</h3>
            <DraggableListComponent
              id="minimal-draggable-list"
              config={QuizComponentFactory.createDraggableList(
                draggableListItems.minimal,
                'minimal_draggable_list'
              )}
              items={draggableListItems.minimal}
              onReorder={(items) => setDraggableListItems(prev => ({ ...prev, minimal: items }))}
              personalization={personalizationConfig}
              onInteraction={(event) => handleDraggableListEvent('minimal', event)}
            />
          </div>
        </div>
      </section>

      {/* NPC角色组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>NPC角色组件 (NPCCharacterComponent)</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '30px' }}>
          {/* 传统医生 */}
          <div>
            <h3>传统医生</h3>
            <NPCCharacterComponent
              id="traditional-doctor-npc"
              config={QuizComponentFactory.createNPCCharacter(
                { zh: '李医师', en: 'Dr. Li' },
                { zh: '经验丰富的中医师', en: 'Experienced TCM practitioner' },
                undefined,
                'traditional_doctor'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleNPCCharacterEvent('traditional_doctor', event)}
            />
          </div>

          {/* 智慧长者 */}
          <div>
            <h3>智慧长者</h3>
            <NPCCharacterComponent
              id="wise-elder-npc"
              config={QuizComponentFactory.createNPCCharacter(
                { zh: '张长者', en: 'Elder Zhang' },
                { zh: '智慧的长者，了解古老的智慧', en: 'Wise elder with ancient wisdom' },
                undefined,
                'wise_elder'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleNPCCharacterEvent('wise_elder', event)}
            />
          </div>

          {/* 友好向导 */}
          <div>
            <h3>友好向导</h3>
            <NPCCharacterComponent
              id="friendly-guide-npc"
              config={QuizComponentFactory.createNPCCharacter(
                { zh: '小助手', en: 'Little Helper' },
                { zh: '友好的向导，帮助您了解情绪', en: 'Friendly guide to help you understand emotions' },
                undefined,
                'friendly_guide'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleNPCCharacterEvent('friendly_guide', event)}
            />
          </div>

          {/* 神秘贤者 */}
          <div>
            <h3>神秘贤者</h3>
            <NPCCharacterComponent
              id="mystical-sage-npc"
              config={QuizComponentFactory.createNPCCharacter(
                { zh: '玄机子', en: 'Mystic Sage' },
                { zh: '神秘的贤者，掌握深奥的知识', en: 'Mysterious sage with profound knowledge' },
                undefined,
                'mystical_sage'
              )}
              personalization={personalizationConfig}
              onInteraction={(event) => handleNPCCharacterEvent('mystical_sage', event)}
            />
          </div>
        </div>
      </section>

      {/* 对话组件测试 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>对话组件 (DialogueComponent)</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '30px' }}>
          {/* 标准对话 */}
          <div>
            <h3>标准对话</h3>
            <DialogueComponent
              id="standard-dialogue"
              config={QuizComponentFactory.createDialogue(
                dialogueStates.standard.messages,
                [
                  { id: 'option1', text: { zh: '我想了解更多', en: 'I want to learn more' }, value: 'learn_more' },
                  { id: 'option2', text: { zh: '开始评估', en: 'Start assessment' }, value: 'start_assessment' }
                ],
                'standard_dialogue'
              )}
              messages={dialogueStates.standard.messages}
              onOptionSelect={(option) => console.log('Standard dialogue option selected:', option)}
              personalization={personalizationConfig}
              onInteraction={(event) => handleDialogueEvent('standard', event)}
            />
          </div>

          {/* 传统对话 */}
          <div>
            <h3>传统对话 (中医风格)</h3>
            <DialogueComponent
              id="traditional-dialogue"
              config={QuizComponentFactory.createDialogue(
                dialogueStates.traditional.messages,
                [
                  { id: 'option1', text: { zh: '请指教', en: 'Please guide me' }, value: 'guide_me' },
                  { id: 'option2', text: { zh: '我明白了', en: 'I understand' }, value: 'understand' }
                ],
                'traditional_dialogue'
              )}
              messages={dialogueStates.traditional.messages}
              onOptionSelect={(option) => console.log('Traditional dialogue option selected:', option)}
              personalization={personalizationConfig}
              onInteraction={(event) => handleDialogueEvent('traditional', event)}
            />
          </div>

          {/* 简约对话 */}
          <div>
            <h3>简约对话</h3>
            <DialogueComponent
              id="minimal-dialogue"
              config={QuizComponentFactory.createDialogue(
                dialogueStates.minimal.messages,
                [
                  { id: 'option1', text: { zh: '确定', en: 'OK' }, value: 'ok' }
                ],
                'minimal_dialogue'
              )}
              messages={dialogueStates.minimal.messages}
              onOptionSelect={(option) => console.log('Minimal dialogue option selected:', option)}
              personalization={personalizationConfig}
              onInteraction={(event) => handleDialogueEvent('minimal', event)}
            />
          </div>
        </div>
      </section>

      {/* 状态显示 */}
      <section style={{ marginBottom: '40px' }}>
        <h2>组件状态</h2>
        <div style={{
          background: '#f5f5f5',
          padding: '16px',
          borderRadius: '8px',
          fontFamily: 'monospace',
          fontSize: '14px'
        }}>
          <p><strong>选中值:</strong> {JSON.stringify(selectedValues)}</p>
          <p><strong>滑块值:</strong> {JSON.stringify(sliderValues)}</p>
          <p><strong>评分值:</strong> {JSON.stringify(ratingValues)}</p>
          <p><strong>下拉选择值:</strong> {JSON.stringify(dropdownValues)}</p>
          <p><strong>文本输入值:</strong> {JSON.stringify(textInputValues)}</p>
          <p><strong>图片选择值:</strong> {JSON.stringify(imageSelectorValues)}</p>
          <p><strong>进度值:</strong> {JSON.stringify(progressValues)}</p>
        </div>
      </section>

      {/* 交互日志 */}
      <section>
        <h2>交互日志</h2>
        <div style={{
          background: '#f5f5f5',
          padding: '16px',
          borderRadius: '8px',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {interactionLog.length === 0 ? (
            <p style={{ color: '#666', fontStyle: 'italic' }}>暂无交互记录</p>
          ) : (
            interactionLog.map((event, index) => (
              <div key={index} style={{
                marginBottom: '8px',
                padding: '8px',
                background: 'white',
                borderRadius: '4px',
                fontSize: '12px',
                fontFamily: 'monospace'
              }}>
                <div><strong>类型:</strong> {event.type}</div>
                <div><strong>目标:</strong> {event.target}</div>
                <div><strong>数据:</strong> {JSON.stringify(event.data)}</div>
                <div><strong>时间:</strong> {new Date(event.timestamp).toLocaleTimeString()}</div>
              </div>
            ))
          )}
        </div>
      </section>
    </div>
  );
};

export default QuizComponentTest;
