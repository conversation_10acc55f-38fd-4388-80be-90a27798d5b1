/**
 * 用户配置服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 * 基于user_configs表结构
 */

import { BaseService } from '../base/BaseService';
import { BaseRepository } from '../base/BaseRepository';
import { UserConfig } from '../../types/schema/base';
import { CreateUserConfigInput, UpdateUserConfigInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

// UserConfigRepository实现
class UserConfigRepository extends BaseRepository<UserConfig, CreateUserConfigInput, UpdateUserConfigInput> {
  constructor(db?: SQLiteDBConnection) {
    super('user_configs', db);
  }

  async findByUserId(userId: string): Promise<UserConfig[]> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE user_id = ? ORDER BY name ASC`;
    const result = await db.query(query, [userId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  async findActiveByUserId(userId: string): Promise<UserConfig | null> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE user_id = ? AND is_active = 1 LIMIT 1`;
    const result = await db.query(query, [userId]);
    const row = result.values?.[0];
    return row ? this.mapRowToEntity(row) : null;
  }

  async findByName(userId: string, name: string): Promise<UserConfig | null> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE user_id = ? AND name = ? LIMIT 1`;
    const result = await db.query(query, [userId, name]);
    const row = result.values?.[0];
    return row ? this.mapRowToEntity(row) : null;
  }

  async setActiveConfig(userId: string, configId: string): Promise<boolean> {
    const db = this.getDb();

    // 先将所有配置设为非活动
    await db.run(`UPDATE ${this.tableName} SET is_active = 0 WHERE user_id = ?`, [userId]);

    // 设置指定配置为活动
    const result = await db.run(`UPDATE ${this.tableName} SET is_active = 1 WHERE id = ? AND user_id = ?`, [configId, userId]);
    return (result.changes?.changes ?? 0) > 0;
  }

  protected mapRowToEntity(row: any): UserConfig {
    return {
      id: row.id,
      name: row.name,
      user_id: row.user_id,
      is_active: Boolean(row.is_active),
      theme_mode: row.dark_mode ? 'dark' : 'light',
      language: row.language || 'zh-CN',
      accessibility: row.accessibility,
      notifications_enabled: Boolean(row.notifications_enabled),
      sound_enabled: Boolean(row.sound_enabled),
      created_at: row.created_at,
      last_updated: row.last_updated
    } as UserConfig;
  }

  protected mapEntityToRow(entity: Partial<UserConfig>): Record<string, any> {
    return {
      id: entity.id,
      name: entity.name,
      user_id: entity.user_id,
      is_active: entity.is_active ? 1 : 0,
      dark_mode: entity.theme_mode === 'dark' ? 1 : 0,
      language: entity.language,
      accessibility: entity.accessibility,
      notifications_enabled: entity.notifications_enabled ? 1 : 0,
      sound_enabled: entity.sound_enabled ? 1 : 0,
      created_at: entity.created_at,
      last_updated: entity.last_updated
    };
  }

  protected buildInsertQuery(data: CreateUserConfigInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const configId = `config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const query = `
      INSERT INTO ${this.tableName} (
        id, name, user_id, is_active, active_emotion_data_id, active_skin_id,
        preferred_view_type, dark_mode, color_mode, render_engine_preferences,
        layout_preferences, content_display_mode_preferences, view_type_skin_ids,
        accessibility, recent_emotion_data_ids, recent_skin_ids, created_at, last_updated
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      configId, data.name, data.user_id, data.is_active ? 1 : 0,
      data.active_emotion_data_id, data.active_skin_id, data.preferred_view_type,
      data.dark_mode ? 1 : 0, data.color_mode, data.render_engine_preferences,
      data.layout_preferences, data.content_display_mode_preferences,
      data.view_type_skin_ids, data.accessibility, data.recent_emotion_data_ids,
      data.recent_skin_ids, now, now
    ];

    return { query, values };
  }

  protected extractIdFromCreateData(_data: CreateUserConfigInput): string {
    return `config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  protected buildUpdateQuery(id: string, data: UpdateUserConfigInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      fields.push('name = ?');
      values.push(data.name);
    }

    if (data.is_active !== undefined) {
      fields.push('is_active = ?');
      values.push(data.is_active ? 1 : 0);
    }

    if (data.active_emotion_data_id !== undefined) {
      fields.push('active_emotion_data_id = ?');
      values.push(data.active_emotion_data_id);
    }

    if (data.active_skin_id !== undefined) {
      fields.push('active_skin_id = ?');
      values.push(data.active_skin_id);
    }

    if (data.preferred_view_type !== undefined) {
      fields.push('preferred_view_type = ?');
      values.push(data.preferred_view_type);
    }

    if (data.dark_mode !== undefined) {
      fields.push('dark_mode = ?');
      values.push(data.dark_mode ? 1 : 0);
    }

    if (data.color_mode !== undefined) {
      fields.push('color_mode = ?');
      values.push(data.color_mode);
    }

    if (data.render_engine_preferences !== undefined) {
      fields.push('render_engine_preferences = ?');
      values.push(data.render_engine_preferences);
    }

    if (data.layout_preferences !== undefined) {
      fields.push('layout_preferences = ?');
      values.push(data.layout_preferences);
    }

    if (data.content_display_mode_preferences !== undefined) {
      fields.push('content_display_mode_preferences = ?');
      values.push(data.content_display_mode_preferences);
    }

    if (data.view_type_skin_ids !== undefined) {
      fields.push('view_type_skin_ids = ?');
      values.push(data.view_type_skin_ids);
    }

    if (data.accessibility !== undefined) {
      fields.push('accessibility = ?');
      values.push(data.accessibility);
    }

    if (data.recent_emotion_data_ids !== undefined) {
      fields.push('recent_emotion_data_ids = ?');
      values.push(data.recent_emotion_data_ids);
    }

    if (data.recent_skin_ids !== undefined) {
      fields.push('recent_skin_ids = ?');
      values.push(data.recent_skin_ids);
    }

    // Always update last_updated
    fields.push('last_updated = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }
}

// UserConfigService实现
export class UserConfigService extends BaseService<UserConfig, CreateUserConfigInput, UpdateUserConfigInput> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new UserConfigRepository(db);
    super(repository);
  }

  async getActiveUserConfig(userId: string): Promise<ServiceResult<UserConfig | null>> {
    try {
      const config = await (this.repository as UserConfigRepository).findActiveByUserId(userId);
      return this.createSuccessResult(config);
    } catch (error) {
      return this.createErrorResult('Failed to get active user config', error);
    }
  }

  async getUserConfigs(userId: string): Promise<ServiceResult<UserConfig[]>> {
    try {
      const configs = await (this.repository as UserConfigRepository).findByUserId(userId);
      return this.createSuccessResult(configs);
    } catch (error) {
      return this.createErrorResult('Failed to get user configs', error);
    }
  }

  async setActiveConfig(userId: string, configId: string): Promise<ServiceResult<boolean>> {
    try {
      const result = await (this.repository as UserConfigRepository).setActiveConfig(userId, configId);

      if (result) {
        this.emit('activeConfigChanged', { userId, configId });
      }

      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to set active config', error);
    }
  }

  async createUserConfig(input: CreateUserConfigInput): Promise<ServiceResult<UserConfig>> {
    try {
      await this.validateCreate(input);

      // 检查是否已存在同名配置
      const existing = await (this.repository as UserConfigRepository).findByName(
        input.user_id || '',
        input.name
      );

      if (existing) {
        return this.createErrorResult('Config with this name already exists');
      }

      const config = await this.repository.create(input);
      this.emit('configCreated', config);

      return this.createSuccessResult(config);
    } catch (error) {
      return this.createErrorResult('Failed to create user config', error);
    }
  }

  protected async validateCreate(data: CreateUserConfigInput): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Config name is required');
    }
    if (data.name.length > 100) {
      throw new Error('Config name must be less than 100 characters');
    }
    if (!data.active_emotion_data_id) {
      throw new Error('Active emotion data ID is required');
    }
    if (!data.active_skin_id) {
      throw new Error('Active skin ID is required');
    }
    if (!data.preferred_view_type) {
      throw new Error('Preferred view type is required');
    }
  }

  protected async validateUpdate(data: UpdateUserConfigInput): Promise<void> {
    if (data.name !== undefined && (!data.name || data.name.trim().length === 0)) {
      throw new Error('Config name cannot be empty');
    }
    if (data.name !== undefined && data.name.length > 100) {
      throw new Error('Config name must be less than 100 characters');
    }
  }
}
