# NewHome量表切换功能实现

本文档详细说明了在NewHome页面添加量表切换功能的实现，以及修复数据库服务错误的解决方案。

## 🎯 核心功能实现

### 1. **量表切换器**
- ✅ **下拉选择器**: 用户可以从可用量表中选择
- ✅ **分类标签**: 不同类型量表有不同的颜色标识
- ✅ **实时切换**: 选择量表后立即加载对应的问题数据
- ✅ **状态重置**: 切换量表时重置当前答题状态

### 2. **设置页面集成**
- ✅ **配置按钮**: 直接跳转到Quiz设置页面
- ✅ **个性化配置**: 在设置页面配置的问题管理会影响NewHome的渲染
- ✅ **无缝体验**: 从NewHome到设置页面的流畅导航

### 3. **数据库服务修复**
- ✅ **修复getDatabase错误**: 将`databaseService.getDatabase()`改为直接传递`databaseService`
- ✅ **服务初始化**: 确保所有Quiz相关服务正确初始化
- ✅ **错误处理**: 改进错误处理和日志记录

## 📋 技术实现详情

### 数据库服务修复

#### 问题原因
```typescript
// 错误的调用方式
new QuizPackService(databaseService.getDatabase())
new QuizQuestionService(databaseService.getDatabase())
new QuizEngineV2(databaseService.getDatabase())
```

DatabaseService类没有`getDatabase()`方法，而是使用`getConnection()`方法。

#### 解决方案
```typescript
// 正确的调用方式
new QuizPackService(databaseService)
new QuizQuestionService(databaseService)
new QuizEngineV2(databaseService)
```

直接传递DatabaseService实例，让各个服务内部调用`getConnection()`方法。

### 量表切换功能

#### 状态管理
```typescript
// 量表选择状态
const [selectedQuizPackId, setSelectedQuizPackId] = useState<string>('');
const [availableQuizPacks, setAvailableQuizPacks] = useState<QuizPack[]>([]);

// 当前答题状态
const [currentTierIndex, setCurrentTierIndex] = useState<number>(0);
const [selectedEmotions, setSelectedEmotions] = useState<Array<{...}>>([]);
const [selectedPath, setSelectedPath] = useState<{[questionId: string]: QuizQuestionOption}>({});
```

#### 数据初始化
```typescript
useEffect(() => {
  const initializeQuizPacks = async () => {
    // 获取所有分类的量表
    const emotionPacks = await getQuizPacksByCategory('emotion');
    const tcmPacks = await getQuizPacksByCategory('tcm');
    const dailyPacks = await getQuizPacksByCategory('daily');
    const assessmentPacks = await getQuizPacksByCategory('assessment');
    
    // 合并并去重
    const allPacks = [...emotionPacks, ...tcmPacks, ...dailyPacks, ...assessmentPacks, ...recommendedQuizPacks];
    const uniquePacks = allPacks.filter((pack, index, self) => 
      index === self.findIndex(p => p.id === pack.id)
    );
    
    setAvailableQuizPacks(uniquePacks);
    
    // 设置默认选择
    if (!selectedQuizPackId && uniquePacks.length > 0) {
      const defaultPack = uniquePacks.find(pack => pack.category === 'emotion') || uniquePacks[0];
      setSelectedQuizPackId(defaultPack.id);
    }
  };

  if (recommendedQuizPacks.length > 0) {
    initializeQuizPacks();
  }
}, [recommendedQuizPacks, getQuizPacksByCategory, selectedQuizPackId]);
```

#### 量表切换逻辑
```typescript
useEffect(() => {
  if (selectedQuizPackId) {
    loadEmotionWheelData(selectedQuizPackId);
    // 重置当前状态
    setCurrentTierIndex(0);
    setSelectedEmotions([]);
    setSelectedPath({});
  }
}, [selectedQuizPackId, loadEmotionWheelData]);

const handleQuizPackChange = (packId: string) => {
  setSelectedQuizPackId(packId);
};
```

## 🎨 用户界面设计

### 量表选择器卡片
```typescript
<Card>
  <CardHeader className="pb-3">
    <CardTitle className="flex items-center justify-between">
      <span className="flex items-center gap-2">
        <Play className="h-5 w-5" />
        当前量表
      </span>
      <Button variant="outline" size="sm" onClick={handleGoToSettings}>
        <Settings className="h-4 w-4" />
        配置
      </Button>
    </CardTitle>
    <CardDescription>
      选择您要进行的量表测试，配置页面可以个性化问题和显示方式
    </CardDescription>
  </CardHeader>
  <CardContent>
    {/* 下拉选择器和量表信息 */}
  </CardContent>
</Card>
```

### 下拉选择器
```typescript
<Select value={selectedQuizPackId} onValueChange={handleQuizPackChange}>
  <SelectTrigger>
    <SelectValue placeholder="选择量表..." />
  </SelectTrigger>
  <SelectContent>
    {availableQuizPacks.map((pack) => (
      <SelectItem key={pack.id} value={pack.id}>
        <div className="flex items-center justify-between w-full">
          <span>{pack.name}</span>
          <Badge variant={pack.category === 'emotion' ? 'default' : 'secondary'}>
            {pack.category === 'emotion' ? '情绪' :
             pack.category === 'tcm' ? '中医' :
             pack.category === 'daily' ? '日常' :
             pack.category === 'assessment' ? '评估' : pack.category}
          </Badge>
        </div>
      </SelectItem>
    ))}
  </SelectContent>
</Select>
```

### 当前量表信息展示
```typescript
{selectedQuizPackId && emotionWheelData && (
  <div className="p-3 bg-muted/50 rounded-lg">
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">{emotionWheelData.pack.name}</h4>
        <Badge variant="outline">
          {emotionWheelData.questions.length} 个问题
        </Badge>
      </div>
      <p className="text-sm text-muted-foreground">
        {emotionWheelData.pack.description}
      </p>
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        <span>难度: {emotionWheelData.pack.difficulty_level}</span>
        <span>•</span>
        <span>预计时间: {emotionWheelData.pack.estimated_duration_minutes} 分钟</span>
      </div>
    </div>
  </div>
)}
```

## 🔧 与设置页面的集成

### 配置按钮
```typescript
<Button
  variant="outline"
  size="sm"
  onClick={handleGoToSettings}
  className="flex items-center gap-2"
>
  <Settings className="h-4 w-4" />
  配置
</Button>

const handleGoToSettings = () => {
  navigate('/quiz-settings');
};
```

### 配置数据流向
```
NewHome选择量表 → Quiz设置页面配置问题 → NewHome渲染配置后的问题
     ↓                    ↓                        ↓
1. 选择量表ID        2. 个性化问题管理           3. 按配置渲染
2. 跳转设置页面      3. 启用/禁用问题           4. 应用内容显示模式
3. 返回NewHome      4. 调整问题顺序           5. 使用选择的皮肤
```

## 💡 用户体验改进

### 1. **无缝切换体验**
- **即时响应**: 选择量表后立即加载数据
- **状态重置**: 切换时清除之前的答题状态
- **视觉反馈**: 加载状态和错误提示

### 2. **个性化配置集成**
- **直接导航**: 从NewHome直接跳转到配置页面
- **配置应用**: 设置页面的配置立即影响NewHome的渲染
- **一致性**: 保持配置和渲染的一致性

### 3. **信息展示优化**
- **量表信息**: 显示名称、描述、问题数量、难度、预计时间
- **分类标识**: 不同类型量表的颜色区分
- **状态指示**: 当前选中状态的清晰标识

## 🚀 使用场景示例

### 场景1: 快速情绪记录
1. **打开NewHome**: 默认选择情绪轮盘量表
2. **开始答题**: 直接进行情绪记录
3. **快速完成**: 2-3分钟完成基础情绪记录

### 场景2: 切换到中医评估
1. **选择量表**: 从下拉菜单选择"中医体质评估"
2. **查看信息**: 了解评估内容和预计时间
3. **开始评估**: 进行详细的中医体质分析

### 场景3: 个性化配置
1. **点击配置按钮**: 跳转到Quiz设置页面
2. **个性化问题**: 启用/禁用特定问题，调整顺序
3. **返回测试**: 回到NewHome查看配置效果

### 场景4: 日常追踪
1. **选择日常追踪**: 选择简化的日常情绪记录量表
2. **快速记录**: 只回答核心问题
3. **形成习惯**: 每日快速记录情绪状态

## ✅ 功能验证清单

### 基础功能
- [ ] 量表下拉选择器正常工作
- [ ] 量表切换时数据正确加载
- [ ] 状态重置功能正常
- [ ] 配置按钮跳转正确

### 数据库修复
- [ ] QuizPackService正常初始化
- [ ] QuizQuestionService正常初始化
- [ ] QuizEngineV2正常初始化
- [ ] 数据加载无错误

### 用户体验
- [ ] 界面响应流畅
- [ ] 量表信息显示正确
- [ ] 分类标签颜色正确
- [ ] 加载状态提示清晰

### 集成功能
- [ ] 设置页面导航正常
- [ ] 配置数据正确应用
- [ ] NewHome和设置页面数据同步
- [ ] 个性化配置生效

## 🔮 未来扩展

### 1. **量表推荐算法**
- 根据用户历史记录推荐合适的量表
- 基于时间和场景的智能推荐
- 个性化量表排序

### 2. **快速启动模式**
- 一键启动最常用的量表
- 收藏夹功能
- 最近使用记录

### 3. **量表预览功能**
- 在选择前预览量表内容
- 显示示例问题
- 估算完成时间

这个量表切换功能为NewHome页面提供了强大的灵活性，用户可以根据当前需求快速切换不同类型的量表，同时通过与设置页面的集成，实现了真正的个性化测试体验。
