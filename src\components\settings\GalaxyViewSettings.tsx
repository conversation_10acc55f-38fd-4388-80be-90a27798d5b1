/**
 * 星系视图设置组件
 * 用于配置星系视图的布局选项
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import { Check } from 'lucide-react';
import type React from 'react';
import { toast } from 'sonner';
import type { GalaxyLayout } from '../../types/userConfigTypes';
import GalaxyLayoutPreview from './GalaxyLayoutPreview';

interface GalaxyViewSettingsProps {
  className?: string;
}

/**
 * 星系视图设置组件
 * 用于配置星系视图的布局选项
 */
const GalaxyViewSettings: React.FC<GalaxyViewSettingsProps> = ({ className }) => {
  const { t } = useLanguage();
  const { userConfig, setGalaxyViewLayout } = useUserConfig();

  // 获取当前星系布局
  const currentLayout = userConfig.layout_preferences?.galaxy || 'spiral';

  // 处理布局变更
  const handleLayoutChange = (layout: GalaxyLayout) => {
    if (layout === currentLayout) return;

    setGalaxyViewLayout(layout);
    toast.success(t('settings.galaxy_layout_changed', '星系布局已更改'), { duration: 3000 });
  };

  // 可用的布局选项
  const layoutOptions: GalaxyLayout[] = ['orbital', 'spiral', 'nebula'];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t('settings.galaxy_view_settings', '星系视图设置')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">{t('settings.galaxy_layout', '星系布局')}</h4>
            <div className="grid grid-cols-3 gap-4">
              {layoutOptions.map((layout) => (
                <Button
                  key={layout}
                  variant="outline"
                  className="p-2 h-auto"
                  onClick={() => handleLayoutChange(layout)}
                >
                  <div className="flex flex-col items-center w-full">
                    <GalaxyLayoutPreview
                      layout={layout}
                      size="md"
                      isSelected={currentLayout === layout}
                    />
                    <div className="flex items-center w-full mt-2">
                      <span>{t(`settings.galaxy_layout.${layout}`, layout)}</span>
                      {currentLayout === layout && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GalaxyViewSettings;
