/**
 * 配置系统迁移验证测试 (P0 最高优先级)
 * 基于 docs/implementation/config-system-migration-guide.md
 * 验证新旧配置系统的正确切换
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock database connection
const mockDb = {
  query: vi.fn(),
  run: vi.fn(),
  execute: vi.fn(),
  prepare: vi.fn(),
};

describe('配置系统迁移验证测试 (P0)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('1. 旧配置清理验证', () => {
    it('应该验证 user_configs 表已清理', async () => {
      // 模拟查询旧配置表
      mockDb.query.mockResolvedValueOnce([]);
      
      const result = await mockDb.query('SELECT COUNT(*) as count FROM user_configs');
      expect(result).toEqual([]);
      expect(mockDb.query).toHaveBeenCalledWith('SELECT COUNT(*) as count FROM user_configs');
    });

    it('应该验证 user_presentation_configs 表已清理', async () => {
      mockDb.query.mockResolvedValueOnce([]);
      
      const result = await mockDb.query('SELECT COUNT(*) as count FROM user_presentation_configs');
      expect(result).toEqual([]);
    });

    it('应该验证 pack_presentation_overrides 表已清理', async () => {
      mockDb.query.mockResolvedValueOnce([]);
      
      const result = await mockDb.query('SELECT COUNT(*) as count FROM pack_presentation_overrides');
      expect(result).toEqual([]);
    });

    it('应该验证 session_presentation_snapshots 表已移除', async () => {
      // 模拟表不存在的情况
      mockDb.query.mockRejectedValueOnce(new Error('Table does not exist'));
      
      try {
        await mockDb.query('SELECT COUNT(*) as count FROM session_presentation_snapshots');
        expect.fail('表应该已被移除');
      } catch (error) {
        expect(error.message).toContain('Table does not exist');
      }
    });
  });

  describe('2. 新配置系统初始化验证', () => {
    it('应该验证 user_quiz_preferences 表存在且结构正确', async () => {
      const expectedColumns = [
        'id', 'user_id', 'quiz_pack_id', 'layer_0_config', 
        'layer_1_config', 'layer_2_config', 'layer_3_config',
        'layer_4_config', 'layer_5_config', 'created_at', 'updated_at'
      ];
      
      mockDb.query.mockResolvedValueOnce(
        expectedColumns.map(name => ({ name, type: 'TEXT' }))
      );
      
      const result = await mockDb.query('PRAGMA table_info(user_quiz_preferences)');
      expect(result).toHaveLength(expectedColumns.length);
    });

    it('应该验证 question_presentation_overrides 表存在', async () => {
      const expectedColumns = [
        'id', 'user_id', 'question_id', 'emoji_mapping', 
        'custom_config', 'created_at', 'updated_at'
      ];
      
      mockDb.query.mockResolvedValueOnce(
        expectedColumns.map(name => ({ name, type: 'TEXT' }))
      );
      
      const result = await mockDb.query('PRAGMA table_info(question_presentation_overrides)');
      expect(result).toHaveLength(expectedColumns.length);
    });

    it('应该验证 pack_presentation_configs 表存在', async () => {
      const expectedColumns = [
        'id', 'quiz_pack_id', 'default_emoji_set', 
        'presentation_config', 'created_at', 'updated_at'
      ];
      
      mockDb.query.mockResolvedValueOnce(
        expectedColumns.map(name => ({ name, type: 'TEXT' }))
      );
      
      const result = await mockDb.query('PRAGMA table_info(pack_presentation_configs)');
      expect(result).toHaveLength(expectedColumns.length);
    });
  });

  describe('3. 6层配置架构验证', () => {
    it('应该验证 Layer 0: 数据集展现配置', async () => {
      const layer0Config = {
        datasetId: 'tcm-emotions',
        displayMode: 'grid',
        itemsPerRow: 3
      };
      
      mockDb.query.mockResolvedValueOnce([{ layer_0_config: JSON.stringify(layer0Config) }]);
      
      const result = await mockDb.query(
        'SELECT layer_0_config FROM user_quiz_preferences WHERE user_id = ? AND quiz_pack_id = ?',
        ['user123', 'pack456']
      );
      
      const config = JSON.parse(result[0].layer_0_config);
      expect(config).toEqual(layer0Config);
    });

    it('应该验证 Layer 1: 用户选择配置', async () => {
      const layer1Config = {
        selectedEmojiSet: 'traditional',
        preferredLanguage: 'zh',
        accessibility: true
      };
      
      mockDb.query.mockResolvedValueOnce([{ layer_1_config: JSON.stringify(layer1Config) }]);
      
      const result = await mockDb.query(
        'SELECT layer_1_config FROM user_quiz_preferences WHERE user_id = ?',
        ['user123']
      );
      
      const config = JSON.parse(result[0].layer_1_config);
      expect(config).toEqual(layer1Config);
    });

    it('应该验证所有6层配置的完整性', async () => {
      const fullConfig = {
        layer_0_config: '{"datasetId":"tcm-emotions"}',
        layer_1_config: '{"selectedEmojiSet":"traditional"}',
        layer_2_config: '{"renderStrategy":"optimized"}',
        layer_3_config: '{"skinTheme":"light"}',
        layer_4_config: '{"viewDetails":"expanded","emotionDisplay":"icons"}',
        layer_5_config: '{"accessibility":"enhanced"}'
      };
      
      mockDb.query.mockResolvedValueOnce([fullConfig]);
      
      const result = await mockDb.query(
        'SELECT * FROM user_quiz_preferences WHERE user_id = ?',
        ['user123']
      );
      
      expect(result[0]).toMatchObject(fullConfig);
      
      // 验证每层配置都是有效的JSON
      Object.keys(fullConfig).forEach(key => {
        if (key.includes('layer_')) {
          expect(() => JSON.parse(fullConfig[key])).not.toThrow();
        }
      });
    });
  });

  describe('4. Emoji映射系统测试', () => {
    it('应该验证默认emoji映射存在', async () => {
      const defaultMapping = {
        'joy': '😊',
        'anger': '😠',
        'sadness': '😢',
        'fear': '😨',
        'surprise': '😲'
      };
      
      mockDb.query.mockResolvedValueOnce([{ 
        default_emoji_set: JSON.stringify(defaultMapping) 
      }]);
      
      const result = await mockDb.query(
        'SELECT default_emoji_set FROM pack_presentation_configs WHERE quiz_pack_id = ?',
        ['tcm-emotions-pack']
      );
      
      const mapping = JSON.parse(result[0].default_emoji_set);
      expect(mapping).toEqual(defaultMapping);
    });

    it('应该验证用户自定义emoji映射', async () => {
      const customMapping = {
        'joy': '🎉',
        'anger': '🔥'
      };
      
      mockDb.query.mockResolvedValueOnce([{ 
        emoji_mapping: JSON.stringify(customMapping) 
      }]);
      
      const result = await mockDb.query(
        'SELECT emoji_mapping FROM question_presentation_overrides WHERE user_id = ? AND question_id = ?',
        ['user123', 'q1']
      );
      
      const mapping = JSON.parse(result[0].emoji_mapping);
      expect(mapping).toEqual(customMapping);
    });
  });

  describe('5. 数据迁移完整性验证', () => {
    it('应该验证数据迁移无丢失', async () => {
      // 模拟迁移前后的数据统计
      const beforeCount = 100;
      const afterCount = 100;
      
      mockDb.query
        .mockResolvedValueOnce([{ count: beforeCount }])  // 旧系统数据
        .mockResolvedValueOnce([{ count: afterCount }]);  // 新系统数据
      
      const oldData = await mockDb.query('SELECT COUNT(*) as count FROM old_config_table');
      const newData = await mockDb.query('SELECT COUNT(*) as count FROM user_quiz_preferences');
      
      expect(newData[0].count).toBeGreaterThanOrEqual(oldData[0].count);
    });

    it('应该验证配置数据格式正确性', async () => {
      const sampleConfig = {
        user_id: 'user123',
        quiz_pack_id: 'pack456',
        layer_0_config: '{"valid":"json"}',
        layer_1_config: '{"valid":"json"}',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      mockDb.query.mockResolvedValueOnce([sampleConfig]);
      
      const result = await mockDb.query(
        'SELECT * FROM user_quiz_preferences LIMIT 1'
      );
      
      const config = result[0];
      expect(config.user_id).toBeTruthy();
      expect(config.quiz_pack_id).toBeTruthy();
      expect(() => JSON.parse(config.layer_0_config)).not.toThrow();
      expect(() => JSON.parse(config.layer_1_config)).not.toThrow();
      expect(new Date(config.created_at)).toBeInstanceOf(Date);
      expect(new Date(config.updated_at)).toBeInstanceOf(Date);
    });
  });
});
