# 全面清理计划

## 概述

本文档提供了一个全面的清理计划，用于移除所有与旧视图系统相关的废弃组件、类型和工具类。这将使代码库更加整洁，减少冗余，并提高代码的可维护性。

## 已完成的清理工作

我们已经移除了以下废弃的文件：

- `src/types/wheelTypes.ts`：旧的轮盘类型定义
- `src/utils/wheelFactory.ts`：旧的轮盘工厂类
- `src/utils/wheelFactory.test.ts`：旧的轮盘工厂测试
- `src/utils/bubbleFactory.ts`：旧的气泡工厂类
- `src/utils/cardFactory.ts`：旧的卡片工厂类
- `src/components/mood/WheelAdapter.tsx`：旧的轮盘适配器
- `src/components/mood/D3Wheel.tsx`：旧的D3轮盘实现
- `src/components/mood/SVGWheel.tsx`：旧的SVG轮盘实现
- `src/components/mood/R3FWheel.tsx`：旧的R3F轮盘实现
- `src/components/mood/BaseWheel.tsx`：旧的基础轮盘类
- `src/components/wheels/D3WheelComponent.tsx`：旧的D3轮盘组件
- `src/components/wheels/SVGWheelComponent.tsx`：旧的SVG轮盘组件
- `src/components/wheels/R3FWheelComponent.tsx`：旧的R3F轮盘组件
- `src/components/mood/EmotionWheel.tsx`：旧的情绪轮盘组件

## 待清理的组件和文件

### 1. 废弃的组件

| 文件路径 | 替代文件 | 状态 |
|---------|---------|------|
| `src/components/mood/EmotionWheel.tsx` | `src/components/display/DisplayAdapter.tsx` | 已移除 |
| `src/components/views/GalaxyView.tsx` | `src/components/galaxy/GalaxyDirectComponent.tsx` | 待移除 |
| `src/components/display/BaseBubble.tsx` | `src/components/views/BubbleView.tsx` | 待移除 |
| `src/components/display/BaseCard.tsx` | `src/components/views/CardView.tsx` | 待移除 |
| `src/components/display/BubbleView.tsx` | `src/components/views/BubbleView.tsx` | 待移除 |
| `src/components/display/CardView.tsx` | `src/components/views/CardView.tsx` | 待移除 |

### 2. 废弃的类型定义

| 文件路径 | 替代文件 | 状态 |
|---------|---------|------|
| `src/types/mood.ts` (部分) | `src/types/emotionDataTypes.ts` | 待清理 |
| `src/types/emotionTypes.ts` (部分) | `src/types/emotionDataTypes.ts` | 待清理 |
| `src/types/displayTypes.ts` | `src/types/previewTypes.ts` 和 `src/types/userConfigTypes.ts` | 待清理 |

### 3. 废弃的工具类

| 文件路径 | 替代文件 | 状态 |
|---------|---------|------|
| `src/utils/skinSystem.ts` | `src/utils/skinManager.ts` | 待移除 |
| `src/utils/displaySystem.ts` | `src/utils/viewFactory.tsx` | 待移除 |

## 清理步骤

### 步骤 1: 更新导入语句

在移除文件之前，需要更新所有导入这些文件的地方，改为导入新的替代文件。

1. 搜索所有导入旧组件的地方
2. 更新导入语句，使用新组件
3. 运行测试，确保系统正常工作

### 步骤 2: 移除 EmotionWheel.tsx

1. 搜索所有导入 `EmotionWheel` 的地方
2. 更新导入语句，使用 `DisplayAdapter`
3. 更新组件使用方式，适应 `DisplayAdapter` 的接口
4. 移除 `EmotionWheel.tsx` 文件

### 步骤 3: 移除 GalaxyView.tsx

1. 搜索所有导入 `GalaxyView` 的地方
2. 更新导入语句，使用 `GalaxyDirectComponent`
3. 更新组件使用方式，适应 `GalaxyDirectComponent` 的接口
4. 移除 `GalaxyView.tsx` 文件

### 步骤 4: 移除 BaseBubble.tsx 和 BubbleView.tsx

1. 搜索所有导入 `BaseBubble` 和 `BubbleView` 的地方
2. 更新导入语句，使用 `src/views/bubbles/BubbleView.tsx`
3. 更新组件使用方式，适应新的接口
4. 移除 `BaseBubble.tsx` 和 `BubbleView.tsx` 文件

### 步骤 5: 移除 BaseCard.tsx 和 CardView.tsx

1. 搜索所有导入 `BaseCard` 和 `CardView` 的地方
2. 更新导入语句，使用 `src/views/cards/CardView.tsx`
3. 更新组件使用方式，适应新的接口
4. 移除 `BaseCard.tsx` 和 `CardView.tsx` 文件

### 步骤 6: 清理 mood.ts 和 emotionTypes.ts

1. 分析 `mood.ts` 和 `emotionTypes.ts` 中的类型定义
2. 确定哪些类型已经被 `emotionDataTypes.ts` 替代
3. 更新所有使用这些类型的地方，使用 `emotionDataTypes.ts` 中的类型
4. 从 `mood.ts` 和 `emotionTypes.ts` 中移除已替代的类型

### 步骤 7: 清理 displayTypes.ts

1. 分析 `displayTypes.ts` 中的类型定义
2. 确定哪些类型已经被 `previewTypes.ts` 和 `userConfigTypes.ts` 替代
3. 更新所有使用这些类型的地方，使用新的类型
4. 从 `displayTypes.ts` 中移除已替代的类型

### 步骤 8: 移除 skinSystem.ts 和 displaySystem.ts

1. 搜索所有导入 `skinSystem.ts` 和 `displaySystem.ts` 的地方
2. 更新导入语句，使用 `skinManager.ts` 和 `viewFactory.tsx`
3. 更新方法调用，适应新的接口
4. 移除 `skinSystem.ts` 和 `displaySystem.ts` 文件

## 测试计划

每次清理后，需要运行以下测试：

1. **类型检查**：运行 TypeScript 编译器，确保没有类型错误
2. **单元测试**：运行 `npm test`，确保所有测试通过
3. **手动测试**：在开发环境中测试相关功能，确保一切正常工作

## 风险和缓解措施

1. **功能回归**
   - 风险：清理过程中可能导致现有功能失效
   - 缓解：增加测试覆盖率，采用渐进式清理，保持向后兼容性

2. **依赖关系**
   - 风险：可能有一些代码依赖于废弃的组件和类型
   - 缓解：在更新之前进行全面的代码分析，确保所有依赖都被正确处理

## 时间安排

| 阶段 | 预计时间 | 开始日期 | 结束日期 |
|------|---------|---------|---------|
| 更新导入语句 | 1天 | TBD | TBD |
| 移除 EmotionWheel.tsx | 0.5天 | TBD | TBD |
| 移除 GalaxyView.tsx | 0.5天 | TBD | TBD |
| 移除 BaseBubble.tsx 和 BubbleView.tsx | 0.5天 | TBD | TBD |
| 移除 BaseCard.tsx 和 CardView.tsx | 0.5天 | TBD | TBD |
| 清理 mood.ts 和 emotionTypes.ts | 1天 | TBD | TBD |
| 清理 displayTypes.ts | 0.5天 | TBD | TBD |
| 移除 skinSystem.ts 和 displaySystem.ts | 0.5天 | TBD | TBD |
| 测试和验证 | 1天 | TBD | TBD |

## 结论

通过执行这个全面的清理计划，我们将移除所有与旧视图系统相关的废弃组件、类型和工具类。这将使代码库更加整洁，减少冗余，并提高代码的可维护性。同时，我们将确保系统的正常运行，不会因为清理而导致功能回归。
