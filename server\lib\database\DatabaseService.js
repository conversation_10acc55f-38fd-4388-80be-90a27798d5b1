import { DatabaseFactory } from './DatabaseFactory.js';
import { DatabaseInitializer } from './DatabaseInitializer.js';
/**
 * 数据库服务类
 * 作为应用程序与数据库适配器之间的接口
 */
export class DatabaseService {
  static instance;
  adapter = null;
  config = null;
  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }
  /**
   * 初始化数据库服务
   * @param config 数据库配置
   */
  initialize(config) {
    this.config = config;
    const factory = DatabaseFactory.getInstance();
    this.adapter = factory.createAdapter(config);
    console.log(`[DatabaseService] Initialized with ${config.type} adapter`);
  }
  /**
   * 获取数据库适配器
   */
  getAdapter() {
    if (!this.adapter) {
      throw new Error(
        '[DatabaseService] Database adapter not initialized. Call initialize() first.'
      );
    }
    return this.adapter;
  }
  /**
   * 执行单个 SQL 查询
   * @param sql SQL 查询或 InStatement 对象
   */
  async executeQuery(sql) {
    return this.getAdapter().executeQuery(sql);
  }
  /**
   * 在事务中执行一批 SQL 语句
   * @param statements InStatement 对象数组
   * @param mode 事务模式
   */
  async batchStatements(statements, mode) {
    return this.getAdapter().batchStatements(statements, mode);
  }
  /**
   * 执行多语句 SQL 脚本
   * @param sqlScript 包含多个语句的 SQL 脚本
   */
  async executeScript(sqlScript) {
    return this.getAdapter().executeScript(sqlScript);
  }
  /**
   * 从指定表中获取所有行
   * @param tableName 表名
   * @param limit 可选的行数限制
   */
  async fetchAllFromTable(tableName, limit) {
    return this.getAdapter().fetchAllFromTable(tableName, limit);
  }
  /**
   * 获取数据库类型
   */
  getDatabaseType() {
    return this.getAdapter().getDatabaseType();
  }
  /**
   * 获取当前配置
   */
  getConfig() {
    return this.config;
  }
  /**
   * 初始化数据库
   * @param options 初始化选项
   */
  async initializeDatabase(options = {}) {
    if (!this.adapter) {
      throw new Error(
        '[DatabaseService] Database adapter not initialized. Call initialize() first.'
      );
    }
    const initializer = new DatabaseInitializer(this, options);
    await initializer.initialize();
  }
  /**
   * 关闭数据库连接
   */
  async close() {
    if (this.adapter) {
      await this.adapter.close();
      this.adapter = null;
      console.log('[DatabaseService] Database connection closed');
    }
  }
}
