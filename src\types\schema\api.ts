/**
 * API Schema 定义
 *
 * 定义 tRPC API 的输入输出类型，确保客户端和服务端类型一致
 */

import { z } from 'zod';
import {
  // @deprecated EmojiItemSchema 已废弃
  // EmojiItemSchema,
  EmojiSetSchema,
  // @deprecated EmotionDataSetSchema 已废弃
  // EmotionDataSetSchema,
  // @deprecated EmotionSchema 已废弃
  // EmotionSchema,
  EmotionSelectionSchema,
  IdSchema,
  LanguageCodeSchema,
  MoodEntrySchema,
  OptionalIdSchema,
  SkinSchema,
  SkinConfigSchema,
  SyncStatusSchema,
  TimestampSchema,
  UserSchema,
  // 导入 enum schemas
  ViewTypeSchema,
  LanguageCode,
  ContentDisplayModeSchema,
  RenderEngineSchema,
  ColorModeSchema,
  EmojiSetTypeSchema,
  SkinCategorySchema,
  OrderDirectionSchema,
  QuizPackSchema,
  QuizSessionSchema,
  QuizQuestionSchema,
  QuizQuestionOptionSchema,
  QuizAnswerSchema,
  // **新增**: emoji映射相关Schema
  QuestionPresentationOverrideSchema,
  PackPresentationConfigSchema,
  // **新增**: emoji映射相关类型 (作为类型导入)
  type EmojiMapping,
  type EmojiMappingResult,
  type EmotionPresentation,
} from './base';

// ==================== 通用 API Schema ====================

/**
 * 分页参数 Schema
 */
export const PaginationSchema = z.object({
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
});

/**
 * 排序参数 Schema
 */
export const SortSchema = z.object({
  field: z.string(),
  direction: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * 通用响应 Schema
 */
export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: z.string().optional(),
    message: z.string().optional(),
  });

/**
 * 分页响应 Schema
 */
export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    success: z.boolean(),
    data: z.array(itemSchema).optional(),
    pagination: z
      .object({
        total: z.number().int(),
        limit: z.number().int(),
        offset: z.number().int(),
        hasMore: z.boolean(),
      })
      .optional(),
    error: z.string().optional(),
  });

// ==================== 认证相关 Schema ====================

/**
 * 登录输入 Schema
 */
export const LoginInputSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

/**
 * 注册输入 Schema
 */
export const RegisterInputSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(6),
  display_name: z.string().optional(),
});

/**
 * 认证响应 Schema
 */
export const AuthResponseSchema = z.object({
  success: z.boolean(),
  data: z
    .object({
      user: z.lazy(() => UserSchema),
      token: z.string(),
      refreshToken: z.string().optional(),
    })
    .optional(),
  error: z.string().optional(),
});

// ==================== 数据库操作 Schema ====================

/**
 * 批量操作输入 Schema
 */
export const BatchOperationInputSchema = z.object({
  operations: z.array(
    z.object({
      type: z.enum(['insert', 'update', 'delete']),
      table: z.string(),
      data: z.record(z.any()).optional(),
      where: z.record(z.any()).optional(),
    })
  ),
});

/**
 * 获取表数据输入 Schema
 */
export const FetchTableInputSchema = z.object({
  tableName: z.string(),
  limit: z.number().int().min(1).max(1000).default(100),
  offset: z.number().int().min(0).default(0),
  orderBy: z.string().optional(),
  orderDirection: z.enum(['ASC', 'DESC']).default('ASC'),
  where: z.record(z.any()).optional(),
});

// ==================== 支付相关 Schema ====================

/**
 * 购买 VIP 输入 Schema
 */
export const PurchaseVipInputSchema = z.object({
  planId: z.lazy(() => IdSchema),
  paymentMethodId: z.string(),
});

/**
 * 购买皮肤输入 Schema
 */
export const PurchaseSkinInputSchema = z.object({
  skinId: z.lazy(() => IdSchema),
  paymentMethodId: z.string(),
});

/**
 * 购买表情集输入 Schema
 */
export const PurchaseEmojiSetInputSchema = z.object({
  emojiSetId: z.lazy(() => IdSchema),
  paymentMethodId: z.string(),
});

/**
 * 获取购买历史输入 Schema
 */
export const GetPurchaseHistoryInputSchema = z.object({
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
  type: z.enum(['all', 'vip', 'skin', 'emoji_set']).default('all'),
});

/**
 * 购买历史项 Schema
 */
export const PurchaseHistoryItemSchema = z.object({
  id: z.lazy(() => IdSchema),
  type: z.enum(['vip', 'skin', 'emoji_set']),
  itemId: z.lazy(() => IdSchema),
  itemName: z.string(),
  amount: z.number(),
  currency: z.string(),
  status: z.enum(['pending', 'completed', 'failed', 'refunded']),
  purchasedAt: z.lazy(() => TimestampSchema),
  expiresAt: z.lazy(() => TimestampSchema).optional(),
});

// ==================== 用户配置相关 Schema ====================

// ==================== 全局应用设置 Schema ====================

/**
 * 全局应用配置 Schema（简化版 user_configs 表）
 */
export const GlobalAppConfigSchema = z.object({
  id: z.lazy(() => IdSchema),
  name: z.string().default('default'),
  user_id: z.lazy(() => IdSchema),
  is_active: z.boolean().default(true),

  // 主题设置
  theme_mode: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.enum(['zh-CN', 'en-US']).default('zh-CN'),

  // 全局无障碍设置 (JSON: GlobalAccessibilityConfig)
  accessibility: z.string().default('{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}'),

  // 通知和音效
  notifications_enabled: z.boolean().default(true),
  sound_enabled: z.boolean().default(true),

  // 审计字段
  created_at: z.lazy(() => TimestampSchema),
  last_updated: z.lazy(() => TimestampSchema),
});

// ==================== Quiz系统配置 Schema ====================

/**
 * 用户展现配置 Schema（对应 user_presentation_configs 表）
 */
export const UserPresentationConfigsSchema = z.object({
  id: z.lazy(() => IdSchema),
  user_id: z.lazy(() => IdSchema),
  config_name: z.string().default('default'),

  // 6层个性化配置 (JSON: QuizPresentationConfig)
  presentation_config: z.string(),

  // 配置元数据
  config_version: z.string().default('2.0'),
  personalization_level: z.number().int().min(0).max(100).default(50),

  // 状态管理
  is_active: z.boolean().default(true),
  is_default: z.boolean().default(false),

  // 审计字段
  created_at: z.lazy(() => TimestampSchema),
  updated_at: z.lazy(() => TimestampSchema),
});

/**
 * 包展现覆盖配置 Schema（对应 pack_presentation_overrides 表）
 */
export const PackPresentationOverridesSchema = z.object({
  id: z.lazy(() => IdSchema),
  user_id: z.lazy(() => IdSchema),
  pack_id: z.lazy(() => IdSchema),

  // 展现覆盖配置 (JSON: PackSpecificPresentationOverride)
  presentation_overrides: z.string().optional(),

  // 层级展现覆盖 (JSON: TierPresentationOverrides)
  tier_presentation_overrides: z.string().optional(),

  // 覆盖元数据
  override_reason: z.enum(['user_preference', 'accessibility_need', 'performance_optimization']).default('user_preference'),
  override_priority: z.number().int().min(1).max(10).default(1),

  // 状态管理
  is_active: z.boolean().default(true),

  // 审计字段
  created_at: z.lazy(() => TimestampSchema),
  updated_at: z.lazy(() => TimestampSchema),
});

/**
 * Quiz会话配置快照 Schema
 */
export const QuizSessionConfigSchema = z.object({
  id: z.lazy(() => IdSchema),
  session_id: z.lazy(() => IdSchema),
  user_id: z.lazy(() => IdSchema),
  pack_id: z.lazy(() => IdSchema),

  // 最终合并的展现配置 (JSON: QuizPresentationConfig)
  final_presentation_config: z.string(),

  // 配置来源追踪 (JSON: ConfigSources)
  config_sources: z.string(),

  // 配置元数据
  personalization_level: z.number().int().min(0).max(100).default(50),
  config_version: z.string().default('2.0'),

  // 审计字段
  created_at: z.lazy(() => TimestampSchema),
});

// 保留旧的Schema名称作为别名，用于向后兼容
export const UserConfigSchema = GlobalAppConfigSchema;
export const UserQuizPreferencesSchema = UserPresentationConfigsSchema;
export const QuizPackOverridesSchema = PackPresentationOverridesSchema;

// ==================== Emoji映射配置 Schema **新增** ====================

/**
 * 获取选项展现配置输入 Schema
 */
export const GetOptionPresentationInputSchema = z.object({
  pack_id: z.lazy(() => IdSchema),
  option_value: z.string().min(1),
  question_id: z.lazy(() => IdSchema).optional(), // 可选，用于问题特定映射
});

/**
 * 选项展现配置响应 Schema
 */
export const OptionPresentationResponseSchema = z.object({
  emoji: z.string(),
  color: z.string(),
  animation: z.string(),
  source: z.enum(['system', 'pack', 'user', 'question']),
});

/**
 * 更新用户emoji映射输入 Schema
 */
export const UpdateUserEmojiMappingInputSchema = z.object({
  option_value: z.string().min(1),
  emoji_mapping: z.object({
    primary: z.string(),
    alternatives: z.array(z.string()),
  }),
  color: z.string().optional(),
  animation: z.string().optional(),
});

/**
 * 更新问题emoji覆盖输入 Schema
 */
export const UpdateQuestionEmojiOverrideInputSchema = z.object({
  question_id: z.lazy(() => IdSchema),
  option_value: z.string().min(1),
  emoji_mapping: z.object({
    primary: z.string(),
    alternatives: z.array(z.string()),
  }),
  color: z.string().optional(),
  animation: z.string().optional(),
  override_reason: z.string().optional(),
});

/**
 * 获取可用emoji输入 Schema
 */
export const GetAvailableEmojisInputSchema = z.object({
  pack_id: z.lazy(() => IdSchema),
  option_value: z.string().min(1),
  question_id: z.lazy(() => IdSchema).optional(),
});

/**
 * 可用emoji响应 Schema
 */
export const AvailableEmojisResponseSchema = z.object({
  emojis: z.array(z.string()),
  current_emoji: z.string(),
  source: z.enum(['system', 'pack', 'user', 'question']),
});

/**
 * 更新用户配置输入 Schema
 */
export const UpdateUserConfigInputSchema = z.object({
  userId: z.lazy(() => IdSchema),
  config: z.object({
    active_emotion_data_id: z.lazy(() => IdSchema).optional(),
    active_skin_id: z.lazy(() => IdSchema).optional(),
    preferred_view_type: z.string().optional(),
    dark_mode: z.boolean().optional(),
    color_mode: z.string().optional(),
    render_engine_preferences: z.string().optional(),
    layout_preferences: z.string().optional(),
    content_display_mode_preferences: z.string().optional(),
    view_type_skin_ids: z.string().optional(),
    accessibility: z.string().optional(),
    recent_emotion_data_ids: z.string().optional(),
    recent_skin_ids: z.string().optional(),
  }),
});

// ==================== VIP 和 Unlock 系统相关 Schema ====================

/**
 * VIP 状态查询输入 Schema
 */
export const GetVipStatusInputSchema = z.object({
  userId: z.lazy(() => IdSchema),
});

/**
 * VIP 状态响应 Schema
 */
export const VipStatusSchema = z.object({
  isVip: z.boolean(),
  tier: z.string().optional(),
  expiresAt: z.lazy(() => TimestampSchema).optional(),
  features: z.array(z.string()),
  unlockedSkins: z.array(z.lazy(() => IdSchema)),
  unlockedEmojiSets: z.array(z.lazy(() => IdSchema)),
});

/**
 * 获取用户解锁内容输入 Schema
 */
export const GetUserUnlocksInputSchema = z.object({
  userId: z.lazy(() => IdSchema),
  contentType: z.enum(['skin', 'emoji_set', 'all']).default('all'),
  includeExpired: z.boolean().default(false),
});

/**
 * 用户解锁内容响应 Schema
 */
export const UserUnlocksResponseSchema = z.object({
  skins: z.array(z.object({
    id: z.lazy(() => IdSchema),
    skin_id: z.lazy(() => IdSchema),
    unlock_method: z.enum(['purchase', 'vip', 'achievement', 'free', 'promotion']),
    unlocked_at: z.lazy(() => TimestampSchema),
    expires_at: z.lazy(() => TimestampSchema).optional(),
  })),
  emojiSets: z.array(z.object({
    id: z.lazy(() => IdSchema),
    emoji_set_id: z.lazy(() => IdSchema),
    unlock_method: z.enum(['purchase', 'vip', 'achievement', 'free', 'promotion']),
    unlocked_at: z.lazy(() => TimestampSchema),
    expires_at: z.lazy(() => TimestampSchema).optional(),
  })),
});

/**
 * 解锁内容输入 Schema
 */
export const UnlockContentInputSchema = z.object({
  userId: z.lazy(() => IdSchema),
  contentType: z.enum(['skin', 'emoji_set']),
  contentId: z.lazy(() => IdSchema),
  unlockMethod: z.enum(['purchase', 'vip', 'achievement', 'free', 'promotion']),
  transactionId: z.string().optional(),
  promotionCode: z.string().optional(),
  expiresAt: z.lazy(() => TimestampSchema).optional(),
});

/**
 * 获取订阅历史输入 Schema
 */
export const GetSubscriptionHistoryInputSchema = z.object({
  userId: z.lazy(() => IdSchema),
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
  status: z.enum(['active', 'cancelled', 'expired', 'refunded', 'pending', 'all']).default('all'),
});

/**
 * 订阅历史响应 Schema
 */
export const SubscriptionHistoryResponseSchema = z.object({
  subscriptions: z.array(z.object({
    id: z.lazy(() => IdSchema),
    subscription_type: z.string(),
    status: z.enum(['active', 'cancelled', 'expired', 'refunded', 'pending']),
    started_at: z.lazy(() => TimestampSchema),
    expires_at: z.lazy(() => TimestampSchema).optional(),
    amount: z.number().optional(),
    currency: z.string().optional(),
    billing_cycle: z.enum(['monthly', 'yearly', 'lifetime']),
  })),
  pagination: z.object({
    total: z.number().int(),
    limit: z.number().int(),
    offset: z.number().int(),
    hasMore: z.boolean(),
  }),
});

// ==================== 数据同步相关 Schema ====================

/**
 * 同步数据输入 Schema
 */
export const SynchronizeDataInputSchema = z.object({
  tables: z.array(z.string()).optional(),
  lastSyncTime: z.lazy(() => TimestampSchema).optional(),
  forceFullSync: z.boolean().default(false),
});

/**
 * 完整同步输入 Schema
 */
export const PerformFullSyncInputSchema = z.object({
  userId: z.lazy(() => IdSchema),
  deviceId: z.string().optional(),
});

// ==================== 翻译相关 Schema ====================

/**
 * 获取翻译输入 Schema
 */
export const GetTranslationInputSchema = z.object({
  entityType: z.enum(['quiz_pack', 'quiz_question', 'emoji_set', 'skin', 'tag', 'ui_label']),
  entityId: z.lazy(() => IdSchema).optional(),
  languageCode: z.lazy(() => LanguageCodeSchema).optional(),
  fallbackLanguage: z.lazy(() => LanguageCodeSchema).default('en'),
});

/**
 * 创建翻译输入 Schema
 */
export const CreateTranslationInputSchema = z.object({
  entityType: z.enum(['quiz_pack', 'quiz_question', 'emoji_set', 'skin', 'tag']),
  entityId: z.lazy(() => IdSchema),
  languageCode: z.lazy(() => LanguageCodeSchema),
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional(),
});

/**
 * 更新翻译输入 Schema
 */
export const UpdateTranslationInputSchema = z.object({
  entityType: z.enum(['quiz_pack', 'quiz_question', 'emoji_set', 'skin', 'tag']),
  entityId: z.lazy(() => IdSchema),
  languageCode: z.lazy(() => LanguageCodeSchema),
  translatedName: z.string().min(1).optional(),
  translatedDescription: z.string().optional(),
});

/**
 * 批量翻译输入 Schema
 */
export const BatchTranslationInputSchema = z.object({
  entityType: z.enum(['quiz_pack', 'quiz_question', 'emoji_set', 'skin', 'tag']),
  translations: z.array(
    z.object({
      entityId: z.lazy(() => IdSchema),
      languageCode: z.lazy(() => LanguageCodeSchema),
      translatedName: z.string().min(1),
      translatedDescription: z.string().optional(),
    })
  ),
});

/**
 * 获取 UI 标签输入 Schema
 */
export const GetUILabelsInputSchema = z.object({
  languageCode: z.lazy(() => LanguageCodeSchema).optional(),
  category: z.string().optional(),
  keys: z.array(z.string()).optional(),
  searchTerm: z.string().optional(),
  limit: z.number().int().min(1).max(1000).default(100),
  offset: z.number().int().min(0).default(0),
});

/**
 * 翻译统计输入 Schema
 */
export const GetTranslationStatsInputSchema = z.object({
  entityType: z
    .enum(['quiz_pack', 'quiz_question', 'emoji_set', 'skin', 'tag', 'ui_label'])
    .optional(),
  languageCode: z.lazy(() => LanguageCodeSchema).optional(),
});

/**
 * 语言支持信息 Schema
 */
export const LanguageSupportSchema = z.object({
  code: z.lazy(() => LanguageCodeSchema),
  name: z.string(),
  nativeName: z.string(),
  region: z.string().optional(),
  isRTL: z.boolean().default(false),
  isSupported: z.boolean().default(true),
  completionPercentage: z.number().min(0).max(100).default(0),
});

/**
 * 翻译统计响应 Schema
 */
export const TranslationStatsSchema = z.object({
  languageCode: z.lazy(() => LanguageCodeSchema),
  totalEntities: z.number().int().min(0),
  translatedEntities: z.number().int().min(0),
  completionPercentage: z.number().min(0).max(100),
  lastUpdated: z.lazy(() => TimestampSchema).optional(),
});

/**
 * 批量翻译结果 Schema
 */
export const BatchTranslationResultSchema = z.object({
  success: z.boolean(),
  processed: z.number().int().min(0),
  failed: z.number().int().min(0),
  errors: z.array(z.string()),
  results: z.array(
    z.object({
      entityId: z.lazy(() => IdSchema),
      languageCode: z.lazy(() => LanguageCodeSchema),
      success: z.boolean(),
      error: z.string().optional(),
    })
  ),
});

// ==================== 数据库操作 Schema (从 server/lib/router.ts 统一) ====================

/**
 * SQL 查询输入 Schema (统一自 server)
 */
export const SqlQueryInputSchema = z.union([
  z.string(),
  z.object({
    sql: z.string(),
    args: z.array(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
  }),
]);

/**
 * 批量 SQL 语句输入 Schema (统一自 server)
 */
export const BatchStatementsInputSchema = z.object({
  statements: z.array(
    z.object({
      sql: z.string(),
      args: z.array(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
    })
  ),
  mode: z.enum(['deferred', 'write', 'read']).optional(),
});

/**
 * SQL 脚本输入 Schema (统一自 server)
 */
export const SqlScriptInputSchema = z.object({
  script: z.string(),
});

/**
 * 表查询输入 Schema (统一自 server)
 */
export const TableQueryInputSchema = z.object({
  tableName: z.string().regex(/^[a-zA-Z0-9_]+$/),
});

/**
 * 带限制的表查询输入 Schema (统一自 server)
 */
export const TableQueryWithLimitInputSchema = z.object({
  tableName: z.string().regex(/^[a-zA-Z0-9_]+$/),
  limit: z.number().int().positive().optional(),
});

// ==================== 认证相关 Schema (从 server/lib/services/AuthService.ts 统一) ====================

/**
 * 认证令牌 Schema (统一自 server 和 AuthService)
 */
export const AuthTokenSchema = z.object({
  accessToken: z.string(),
  refreshToken: z.string().optional(),
  expiresAt: z.string().datetime(), // 统一使用 string 类型
  tokenType: z.literal('Bearer'),
  scope: z.array(z.string()).optional(),
});

/**
 * 令牌验证输入 Schema (统一自 server)
 */
export const VerifyTokenInputSchema = z.object({
  token: z.string(),
});

/**
 * 更新 VIP 状态输入 Schema (统一自 server)
 */
export const UpdateVipStatusInputSchema = z.object({
  userId: z.lazy(() => IdSchema),
  isVip: z.boolean(),
  expiresAt: z.string().optional(),
});

// ==================== 支付相关 Schema (从 server/lib/services/PaymentService.ts 统一) ====================

/**
 * 支付方式 Schema (统一自 server)
 */
export const PaymentMethodSchema = z.object({
  id: z.string(),
  type: z.enum(['stripe', 'paypal', 'apple_pay', 'google_pay']),
  last4: z.string().optional(),
  brand: z.string().optional(),
  expiryMonth: z.number().int().min(1).max(12).optional(),
  expiryYear: z.number().int().optional(),
});

/**
 * VIP 计划 Schema (统一自 server)
 */
export const VipPlanSchema = z.object({
  id: z.string(),
  name: z.string(),
  duration: z.enum(['monthly', 'yearly']),
  price: z.number().min(0),
  currency: z.string().length(3),
  features: z.array(z.string()),
  stripePriceId: z.string().optional(),
});

/**
 * 购买结果 Schema (统一自 server)
 */
export const PurchaseResultSchema = z.object({
  success: z.boolean(),
  transactionId: z.string().optional(),
  error: z.string().optional(),
  requiresAction: z.boolean().optional(),
  clientSecret: z.string().optional(),
});

/**
 * 皮肤购买信息 Schema (统一自 server)
 */
export const SkinPurchaseSchema = z.object({
  skinId: z.lazy(() => IdSchema),
  price: z.number().min(0),
  currency: z.string().length(3),
});

/**
 * 表情集购买信息 Schema (统一自 server)
 */
export const EmojiSetPurchaseSchema = z.object({
  emojiSetId: z.lazy(() => IdSchema),
  price: z.number().min(0),
  currency: z.string().length(3),
});

// 注意：购买相关的 Schema 已在文件前面定义

// ==================== 数据同步相关 Schema (从 server/lib/router.ts 统一) ====================

/**
 * 心情记录同步输入 Schema (统一自 server)
 */
export const MoodEntryUploadSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  timestamp: z.string(),
  emotion_data_set_id: z.string().optional(),
  intensity: z.number(),
  reflection: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string(),
  tags: z.array(z.string()).optional(),

  // 表情集关联
  emoji_set_id: z.string().optional(),
  emoji_set_version: z.string().optional(),

  // 皮肤配置快照
  skin_id: z.string().optional(),
  skin_config_snapshot: z.string().optional(),

  // 显示配置快照
  view_type_used: z.string().optional(),
  render_engine_used: z.string().optional(),
  display_mode_used: z.string().optional(),

  // 用户配置快照
  user_config_snapshot: z.string().optional(),
});

/**
 * 情绪选择同步输入 Schema (统一自 server)
 */
export const EmotionSelectionUploadSchema = z.object({
  id: z.string().optional(),
  mood_entry_id: z.string(),
  emotion_id: z.string(),
  tier_level: z.number(),
  emotion_data_set_emotion_id: z.string().optional(),
  created_at: z.string().optional(),

  // 情绪强度
  intensity: z.number().optional(),

  // 表情信息
  emoji_item_id: z.string().optional(),
  emoji_unicode: z.string().optional(),
  emoji_image_url: z.string().optional(),
  emoji_animation_data: z.string().optional(),

  // 选择上下文
  selection_path: z.string().optional(),
  parent_selection_id: z.string().optional(),
});

/**
 * 数据同步输入 Schema (统一自 server)
 */
export const DataSynchronizeInputSchema = z.object({
  moodEntriesToUpload: z.array(MoodEntryUploadSchema).optional(),
  emotionSelectionsToUpload: z.array(EmotionSelectionUploadSchema).optional(),
  lastSyncTimestamp: z.string().optional(),
  userId: z.string(),
});

/**
 * 完整同步输入 Schema (统一自 server)
 */
export const FullSyncInputSchema = z.object({
  userId: z.string(),
  lastSyncTimestamp: z.string().optional(),
  moodEntriesToUpload: z.array(MoodEntryUploadSchema).optional(),
  emotionSelectionsToUpload: z.array(EmotionSelectionUploadSchema).optional(),
  userConfigsToUpload: z
    .array(
      z.object({
        id: z.string(),
        user_id: z.string(),
        name: z.string(),
        config_data: z.string(),
        is_active: z.boolean(),
        created_at: z.string(),
        last_updated: z.string(),
      })
    )
    .optional(),
  tagsToUpload: z
    .array(
      z.object({
        id: z.string(),
        user_id: z.string(),
        name: z.string(),
        color: z.string().optional(),
        created_at: z.string(),
        updated_at: z.string(),
      })
    )
    .optional(),
});

// ==================== 分析服务相关 Schema (从 server/lib/services/AnalyticsService.ts 统一) ====================

/**
 * 心情分析输入 Schema (统一自 server)
 */
export const GetMoodAnalyticsInputSchema = z.object({
  userId: z.string(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  groupBy: z.enum(['day', 'week', 'month', 'year']).optional(),
  metrics: z.array(z.string()).optional(),
});

/**
 * 情绪使用统计输入 Schema (统一自 server)
 */
export const GetEmotionUsageStatsInputSchema = z.object({
  userId: z.string(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

/**
 * 用户活动统计输入 Schema (统一自 server)
 */
export const GetUserActivityStatsInputSchema = z.object({
  userId: z.string(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

// ==================== 用户管理相关 Schema (从 server/lib/services/UserManagementService.ts 统一) ====================

/**
 * 用户偏好设置输入 Schema (统一自 server)
 */
export const UpdateUserPreferencesInputSchema = z.object({
  userId: z.string(),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'auto']).optional(),
    language: z.string().optional(),
    notifications: z.boolean().optional(),
    syncEnabled: z.boolean().optional(),
    autoSync: z.boolean().optional(),
    syncInterval: z.number().optional(),
    wifiOnly: z.boolean().optional(),
  }),
});

/**
 * 解锁皮肤输入 Schema (统一自 server)
 */
export const UnlockSkinInputSchema = z.object({
  userId: z.string(),
  skinId: z.string(),
  unlockMethod: z.string(),
  transactionId: z.string().optional(),
});

/**
 * 获取用户配置文件输入 Schema (统一自 server)
 */
export const GetUserProfileInputSchema = z.object({
  userId: z.string(),
});

/**
 * 获取用户配置输入 Schema (统一自 server)
 */
export const GetUserConfigInputSchema = z.object({
  userId: z.string(),
});

/**
 * 获取用户解锁皮肤输入 Schema (统一自 server)
 */
export const GetUserUnlockedSkinsInputSchema = z.object({
  userId: z.string(),
});

/**
 * 获取用户解锁表情集输入 Schema (统一自 server)
 */
export const GetUserUnlockedEmojiSetsInputSchema = z.object({
  userId: z.string(),
});

// ==================== 服务层接口 Schema (统一自 server) ====================

// 注意：AuthTokenSchema 已在文件前面定义

/**
 * 登录凭据 Schema (统一自 AuthService)
 */
export const LoginCredentialsSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

/**
 * 注册数据 Schema (统一自 AuthService)
 */
export const RegisterDataSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  username: z.string().optional(),
  displayName: z.string().optional(),
});

/**
 * 用户配置文件 Schema (统一自 UserManagementService)
 */
export const UserProfileSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  username: z.string().optional(),
  displayName: z.string().optional(),
  avatar: z.string().optional(),
  isVip: z.boolean(),
  vipExpiresAt: z.string().optional(),
  skinUnlocks: z.array(z.string()),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'auto']),
    language: z.string(),
    notifications: z.boolean(),
    syncEnabled: z.boolean(),
    autoSync: z.boolean(),
    syncInterval: z.number(),
    wifiOnly: z.boolean(),
  }),
  created_at: z.string(),
  updated_at: z.string(),
});

/**
 * 用户偏好设置 Schema (统一自 UserManagementService)
 */
export const UserPreferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'auto']),
  language: z.string(),
  notifications: z.boolean(),
  syncEnabled: z.boolean(),
  autoSync: z.boolean(),
  syncInterval: z.number(),
  wifiOnly: z.boolean(),
});

/**
 * 皮肤解锁信息 Schema (统一自 UserManagementService)
 */
export const SkinUnlockSchema = z.object({
  id: z.string(),
  userId: z.string(),
  skinId: z.string(),
  unlockedAt: z.string(),
  unlockMethod: z.enum(['purchase', 'vip', 'achievement', 'free']),
  transactionId: z.string().optional(),
});

// ==================== 分析服务相关 Schema (统一自 AnalyticsService) ====================

/**
 * 分析查询输入 Schema (统一自 AnalyticsService)
 */
export const AnalyticsQuerySchema = z.object({
  userId: z.string(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  groupBy: z.enum(['day', 'week', 'month', 'year']).optional(),
  metrics: z.array(z.string()).optional(),
});

/**
 * 心情分析数据 Schema (统一自 AnalyticsService)
 */
export const MoodAnalyticsSchema = z.object({
  totalEntries: z.number().int(),
  averageIntensity: z.number(),
  minIntensity: z.number(),
  maxIntensity: z.number(),
  moodTrend: z.enum(['improving', 'declining', 'stable']),
  periodData: z
    .array(
      z.object({
        period: z.string(),
        entryCount: z.number().int(),
        averageIntensity: z.number(),
        minIntensity: z.number(),
        maxIntensity: z.number(),
      })
    )
    .optional(),
});

/**
 * 情绪使用统计 Schema (统一自 AnalyticsService)
 */
export const EmotionUsageStatsSchema = z.object({
  totalSelections: z.number().int(),
  topEmotions: z.array(
    z.object({
      emotionId: z.string(),
      emotionName: z.string(),
      count: z.number().int(),
      percentage: z.number(),
      averageIntensity: z.number(),
    })
  ),
  emotionDistribution: z.array(
    z.object({
      emotionId: z.string(),
      emotionName: z.string(),
      tierLevel: z.number().int(),
      count: z.number().int(),
    })
  ),
  tierAnalysis: z.array(
    z.object({
      tierLevel: z.number().int(),
      count: z.number().int(),
      percentage: z.number(),
      topEmotions: z.array(z.string()),
    })
  ),
});

/**
 * 用户活动统计 Schema (统一自 AnalyticsService)
 */
export const UserActivityStatsSchema = z.object({
  totalDays: z.number().int(),
  activeDays: z.number().int(),
  streakCurrent: z.number().int(),
  streakLongest: z.number().int(),
  averageEntriesPerDay: z.number(),
  activityPattern: z.array(
    z.object({
      dayOfWeek: z.number().int().min(0).max(6),
      dayName: z.string(),
      entryCount: z.number().int(),
      averageIntensity: z.number(),
    })
  ),
});

// ==================== 数据库管理相关 Schema (统一自 server) ====================

/**
 * 数据库结果集 Schema (统一自 DatabaseInterface)
 */
export const ResultSetSchema = z.object({
  columns: z.array(z.string()),
  rows: z.array(z.any()),
  rowsAffected: z.number().int(),
  lastInsertId: z.union([z.string(), z.number()]).optional(),
});

/**
 * SQL 语句 Schema (统一自 DatabaseInterface)
 */
export const InStatementSchema = z.object({
  sql: z.string(),
  args: z.array(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
});

/**
 * 事务模式 Schema (统一自 DatabaseInterface)
 */
export const TransactionModeSchema = z.enum(['deferred', 'write', 'read']);

/**
 * 重置数据库输入 Schema (统一自 server)
 */
export const ResetDatabaseInputSchema = z.object({
  confirm: z.literal('RESET_DATABASE_CONFIRM'),
});

// ==================== 导出类型 ====================

// API 输入类型
export type LoginInput = z.infer<typeof LoginInputSchema>;
export type RegisterInput = z.infer<typeof RegisterInputSchema>;
export type SqlQueryInput = z.infer<typeof SqlQueryInputSchema>;
export type BatchOperationInput = z.infer<typeof BatchOperationInputSchema>;
export type FetchTableInput = z.infer<typeof FetchTableInputSchema>;
export type PurchaseVipInput = z.infer<typeof PurchaseVipInputSchema>;
export type PurchaseSkinInput = z.infer<typeof PurchaseSkinInputSchema>;
export type PurchaseEmojiSetInput = z.infer<typeof PurchaseEmojiSetInputSchema>;
export type GetPurchaseHistoryInput = z.infer<typeof GetPurchaseHistoryInputSchema>;
// export type UpdateUserConfigInput = z.infer<typeof UpdateUserConfigInputSchema>; // 重复定义，已注释
export type GetVipStatusInput = z.infer<typeof GetVipStatusInputSchema>;
export type GetUserUnlocksInput = z.infer<typeof GetUserUnlocksInputSchema>;
export type UserUnlocksResponse = z.infer<typeof UserUnlocksResponseSchema>;
export type UnlockContentInput = z.infer<typeof UnlockContentInputSchema>;
export type GetSubscriptionHistoryInput = z.infer<typeof GetSubscriptionHistoryInputSchema>;
export type SubscriptionHistoryResponse = z.infer<typeof SubscriptionHistoryResponseSchema>;

// **新增**: Emoji映射相关类型
export type GetOptionPresentationInput = z.infer<typeof GetOptionPresentationInputSchema>;
export type OptionPresentationResponse = z.infer<typeof OptionPresentationResponseSchema>;
export type UpdateUserEmojiMappingInput = z.infer<typeof UpdateUserEmojiMappingInputSchema>;
export type UpdateQuestionEmojiOverrideInput = z.infer<typeof UpdateQuestionEmojiOverrideInputSchema>;
export type GetAvailableEmojisInput = z.infer<typeof GetAvailableEmojisInputSchema>;
export type AvailableEmojisResponse = z.infer<typeof AvailableEmojisResponseSchema>;
export type SynchronizeDataInput = z.infer<typeof SynchronizeDataInputSchema>;
export type PerformFullSyncInput = z.infer<typeof PerformFullSyncInputSchema>;
export type GetTranslationInput = z.infer<typeof GetTranslationInputSchema>;
export type CreateTranslationInput = z.infer<typeof CreateTranslationInputSchema>;
export type UpdateTranslationInput = z.infer<typeof UpdateTranslationInputSchema>;
export type BatchTranslationInput = z.infer<typeof BatchTranslationInputSchema>;
export type GetUILabelsInput = z.infer<typeof GetUILabelsInputSchema>;
export type GetTranslationStatsInput = z.infer<typeof GetTranslationStatsInputSchema>;

// 数据库操作类型 (统一自 server)
export type BatchStatementsInput = z.infer<typeof BatchStatementsInputSchema>;
export type SqlScriptInput = z.infer<typeof SqlScriptInputSchema>;
export type TableQueryInput = z.infer<typeof TableQueryInputSchema>;
export type TableQueryWithLimitInput = z.infer<typeof TableQueryWithLimitInputSchema>;

// 认证相关类型 (统一自 server)
export type AuthToken = z.infer<typeof AuthTokenSchema>;
export type VerifyTokenInput = z.infer<typeof VerifyTokenInputSchema>;
export type UpdateVipStatusInput = z.infer<typeof UpdateVipStatusInputSchema>;

// 支付相关类型 (统一自 server)
export type PaymentMethod = z.infer<typeof PaymentMethodSchema>;
export type SkinPurchase = z.infer<typeof SkinPurchaseSchema>;
export type EmojiSetPurchase = z.infer<typeof EmojiSetPurchaseSchema>;

// 数据同步相关类型 (统一自 server)
export type MoodEntryUpload = z.infer<typeof MoodEntryUploadSchema>;
export type EmotionSelectionUpload = z.infer<typeof EmotionSelectionUploadSchema>;
export type DataSynchronizeInput = z.infer<typeof DataSynchronizeInputSchema>;
export type FullSyncInput = z.infer<typeof FullSyncInputSchema>;

// 分析服务相关类型 (统一自 server)
export type GetMoodAnalyticsInput = z.infer<typeof GetMoodAnalyticsInputSchema>;
export type GetEmotionUsageStatsInput = z.infer<typeof GetEmotionUsageStatsInputSchema>;
export type GetUserActivityStatsInput = z.infer<typeof GetUserActivityStatsInputSchema>;

// 用户管理相关类型 (统一自 server)
export type UpdateUserPreferencesInput = z.infer<typeof UpdateUserPreferencesInputSchema>;
export type UnlockSkinInput = z.infer<typeof UnlockSkinInputSchema>;
export type GetUserProfileInput = z.infer<typeof GetUserProfileInputSchema>;
export type GetUserConfigInput = z.infer<typeof GetUserConfigInputSchema>;
export type GetUserUnlockedSkinsInput = z.infer<typeof GetUserUnlockedSkinsInputSchema>;
export type GetUserUnlockedEmojiSetsInput = z.infer<typeof GetUserUnlockedEmojiSetsInputSchema>;

// 服务层接口类型 (统一自 server)
export type LoginCredentials = z.infer<typeof LoginCredentialsSchema>;
export type RegisterData = z.infer<typeof RegisterDataSchema>;
export type UserProfile = z.infer<typeof UserProfileSchema>;
export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
export type SkinUnlock = z.infer<typeof SkinUnlockSchema>;

// 分析服务相关类型 (统一自 AnalyticsService)
export type AnalyticsQuery = z.infer<typeof AnalyticsQuerySchema>;
export type MoodAnalytics = z.infer<typeof MoodAnalyticsSchema>;
export type EmotionUsageStats = z.infer<typeof EmotionUsageStatsSchema>;
export type UserActivityStats = z.infer<typeof UserActivityStatsSchema>;

// 客户端服务层类型 (统一自 src/services)
export type CreateMoodEntryInput = z.infer<typeof CreateMoodEntryInputSchema>;
export type UpdateMoodEntryInput = z.infer<typeof UpdateMoodEntryInputSchema>;
export type CompleteMoodEntryInput = z.infer<typeof CompleteMoodEntryInputSchema>;
export type MoodEntryWithSelections = z.infer<typeof MoodEntryWithSelectionsSchema>;
export type MoodTrackingStats = z.infer<typeof MoodTrackingStatsSchema>;

// 在线服务相关类型 (统一自 src/services/online)
export type VipPurchaseData = z.infer<typeof VipPurchaseDataSchema>;
export type SkinPurchaseData = z.infer<typeof SkinPurchaseDataSchema>;
export type EmojiSetPurchaseData = z.infer<typeof EmojiSetPurchaseDataSchema>;
export type SqlQueryConfig = z.infer<typeof SqlQueryConfigSchema>;
export type BatchSqlConfig = z.infer<typeof BatchSqlConfigSchema>;

// Repository 层数据类型 (统一自 src/services/entities)
export type CreateMoodEntryData = z.infer<typeof CreateMoodEntryDataSchema>;
export type UpdateMoodEntryData = z.infer<typeof UpdateMoodEntryDataSchema>;
export type MoodEntryFilter = z.infer<typeof MoodEntryFilterSchema>;

// 情绪选择 Repository 类型 (统一自 EmotionSelectionRepository)
export type CreateEmotionSelectionData = z.infer<typeof CreateEmotionSelectionDataSchema>;
export type UpdateEmotionSelectionData = z.infer<typeof UpdateEmotionSelectionDataSchema>;
export type EmotionSelectionFilter = z.infer<typeof EmotionSelectionFilterSchema>;

// 情绪 Repository 类型 (统一自 EmotionRepository)
export type CreateEmotionData = z.infer<typeof CreateEmotionDataSchema>;
export type UpdateEmotionData = z.infer<typeof UpdateEmotionDataSchema>;
export type EmotionFilter = z.infer<typeof EmotionFilterSchema>;

// 皮肤 Repository 类型 (统一自 SkinRepository)
export type CreateSkinData = z.infer<typeof CreateSkinDataSchema>;
export type UpdateSkinData = z.infer<typeof UpdateSkinDataSchema>;

// 表情集 Repository 类型 (统一自 EmojiSetRepository)
export type CreateEmojiSetData = z.infer<typeof CreateEmojiSetDataSchema>;
export type UpdateEmojiSetData = z.infer<typeof UpdateEmojiSetDataSchema>;

// 表情项 Repository 类型 (统一自 EmojiItemRepository)
export type CreateEmojiItemData = z.infer<typeof CreateEmojiItemDataSchema>;
export type UpdateEmojiItemData = z.infer<typeof UpdateEmojiItemDataSchema>;

// 全局应用设置 Repository 类型
export type CreateGlobalAppConfigInput = z.infer<typeof CreateGlobalAppConfigInputSchema>;
export type UpdateGlobalAppConfigInput = z.infer<typeof UpdateGlobalAppConfigInputSchema>;

// Quiz系统配置 Repository 类型
export type CreateUserQuizPreferencesInput = z.infer<typeof CreateUserQuizPreferencesInputSchema>;
export type UpdateUserQuizPreferencesInput = z.infer<typeof UpdateUserQuizPreferencesInputSchema>;
export type CreateQuizPackOverridesInput = z.infer<typeof CreateQuizPackOverridesInputSchema>;
export type UpdateQuizPackOverridesInput = z.infer<typeof UpdateQuizPackOverridesInputSchema>;
export type CreateQuizSessionConfigInput = z.infer<typeof CreateQuizSessionConfigInputSchema>;

// 配置系统 tRPC 类型
export type GetGlobalAppConfigInput = z.infer<typeof GetGlobalAppConfigInputSchema>;
export type UpdateGlobalAppConfigTRPCInput = z.infer<typeof UpdateGlobalAppConfigTRPCInputSchema>;
export type CreateGlobalAppConfigTRPCInput = z.infer<typeof CreateGlobalAppConfigTRPCInputSchema>;
export type GetUserQuizPreferencesInput = z.infer<typeof GetUserQuizPreferencesInputSchema>;
export type UpdateUserQuizPreferencesTRPCInput = z.infer<typeof UpdateUserQuizPreferencesTRPCInputSchema>;
export type CreateUserQuizPreferencesTRPCInput = z.infer<typeof CreateUserQuizPreferencesTRPCInputSchema>;
export type GetQuizPackOverridesInput = z.infer<typeof GetQuizPackOverridesInputSchema>;
export type UpdateQuizPackOverridesTRPCInput = z.infer<typeof UpdateQuizPackOverridesTRPCInputSchema>;
export type CreateQuizPackOverridesTRPCInput = z.infer<typeof CreateQuizPackOverridesTRPCInputSchema>;
export type GenerateQuizSessionConfigInput = z.infer<typeof GenerateQuizSessionConfigInputSchema>;
export type GetQuizSessionConfigInput = z.infer<typeof GetQuizSessionConfigInputSchema>;

// 配置系统 tRPC Response 类型
export type ConfigResponse = z.infer<typeof ConfigResponseSchema>;
export type GlobalAppConfigResponse = z.infer<typeof GlobalAppConfigResponseSchema>;
export type GlobalAppConfigListResponse = z.infer<typeof GlobalAppConfigListResponseSchema>;
export type UserPresentationConfigsResponse = z.infer<typeof UserPresentationConfigsResponseSchema>;
export type PackPresentationOverridesResponse = z.infer<typeof PackPresentationOverridesResponseSchema>;
export type QuizSessionConfigResponse = z.infer<typeof QuizSessionConfigResponseSchema>;

// 用户配置 Repository 类型 (统一自 UserConfigRepository) - 向后兼容
export type CreateUserConfigData = z.infer<typeof CreateUserConfigDataSchema>;
export type UpdateUserConfigData = z.infer<typeof UpdateUserConfigDataSchema>;
export type UserConfigFilter = z.infer<typeof UserConfigFilterSchema>;

// 数据库管理相关类型 (统一自 server)
export type ResultSet = z.infer<typeof ResultSetSchema>;
export type InStatement = z.infer<typeof InStatementSchema>;
export type TransactionMode = z.infer<typeof TransactionModeSchema>;
export type ResetDatabaseInput = z.infer<typeof ResetDatabaseInputSchema>;

// ==================== 客户端服务层 Schema (统一自 src/services) ====================

/**
 * 创建心情记录输入 Schema (统一自 MoodEntryService)
 */
export const CreateMoodEntryInputSchema = z.object({
  // MoodEntrySchema
  id: z.string(),
  user_id: z.string(),
  timestamp: z.string().datetime().optional(),
  emotion_data_set_id: z.string().optional(),
  intensity: z.number().min(0).max(100),
  reflection: z.string().max(1000).optional(),
  tags: z.array(z.string()).max(10).optional(),

  // 表情集关联
  emoji_set_id: z.string().optional(),
  emoji_set_version: z.string().optional(),

  // 皮肤配置快照
  skin_id: z.string().optional(),
  skin_config_snapshot: z.string().optional(),

  // 显示配置快照
  view_type_used: z.string().optional(),
  render_engine_used: z.string().optional(),
  display_mode_used: z.string().optional(),

  // 用户配置快照
  user_config_snapshot: z.string().optional(),
});

/**
 * 更新心情记录输入 Schema (统一自 MoodEntryService)
 */
export const UpdateMoodEntryInputSchema = z.object({
  timestamp: z.string().datetime().optional(),
  emotion_data_set_id: z.string().optional(),
  intensity: z.number().min(0).max(100).optional(),
  reflection: z.string().max(1000).optional(),
  tags: z.array(z.string()).max(10).optional(),

  // 表情集关联
  emoji_set_id: z.string().optional(),
  emoji_set_version: z.string().optional(),

  // 皮肤配置快照
  skin_id: z.string().optional(),
  skin_config_snapshot: z.string().optional(),

  // 显示配置快照
  view_type_used: z.string().optional(),
  render_engine_used: z.string().optional(),
  display_mode_used: z.string().optional(),

  // 用户配置快照
  user_config_snapshot: z.string().optional(),
});

/**
 * 完整心情记录输入 Schema (统一自 MoodTrackingService)
 */
export const CompleteMoodEntryInputSchema = z.object({
  user_id: z.string(),
  timestamp: z.date().optional(),
  emotion_data_set_id: z.string().optional(),
  emotion_selections: z
    .array(
      z.object({
        emotion_id: z.string(),
        tier_level: z.number().min(1).max(10),
        emotion_data_set_emotion_id: z.string().optional(),
      })
    )
    .min(1),
  intensity: z.number().min(0).max(100),
  reflection: z.string().max(1000).optional(),
  tags: z.array(z.string()).max(10).optional(),
});

/**
 * 心情记录与情绪选择组合 Schema (统一自 MoodTrackingService)
 */
export const MoodEntryWithSelectionsSchema = z.object({
  moodEntry: z.lazy(() => MoodEntrySchema),
  emotionSelections: z.array(z.lazy(() => EmotionSelectionSchema)),
});

/**
 * 心情追踪统计 Schema (统一自 MoodTrackingService)
 */
export const MoodTrackingStatsSchema = z.object({
  totalEntries: z.number().int(),
  averageIntensity: z.number(),
  mostUsedEmotions: z.array(
    z.object({
      emotion_id: z.string(),
      emotion_name: z.string(),
      tier_level: z.number().int(),
      usage_count: z.number().int(),
      percentage: z.number(),
    })
  ),
  emotionTrends: z.array(
    z.object({
      period: z.string(),
      average_intensity: z.number(),
      entry_count: z.number().int(),
    })
  ),
  streakInfo: z.object({
    currentStreak: z.number().int(),
    longestStreak: z.number().int(),
    lastEntryDate: z.date().nullable(),
  }),
});

// ==================== 在线服务相关 Schema (统一自 src/services/online) ====================

/**
 * VIP 购买数据 Schema (统一自 PaymentService)
 */
export const VipPurchaseDataSchema = z.object({
  planId: z.string(),
  paymentMethodId: z.string(),
  userId: z.string().optional(),
});

/**
 * 皮肤购买数据 Schema (统一自 PaymentService)
 */
export const SkinPurchaseDataSchema = z.object({
  skinId: z.string(),
  paymentMethodId: z.string(),
  userId: z.string().optional(),
});

/**
 * 表情集购买数据 Schema (统一自 PaymentService)
 */
export const EmojiSetPurchaseDataSchema = z.object({
  emojiSetId: z.string(),
  paymentMethodId: z.string(),
  userId: z.string().optional(),
});

/**
 * SQL 查询配置 Schema (统一自 ApiClientService)
 */
export const SqlQueryConfigSchema = z.object({
  sql: z.string(),
  args: z.array(z.any()).optional(),
  timeout: z.number().optional(),
  retries: z.number().optional(),
  requireAuth: z.boolean().optional(),
  skipCache: z.boolean().optional(),
});

/**
 * 批量 SQL 配置 Schema (统一自 ApiClientService)
 */
export const BatchSqlConfigSchema = z.object({
  statements: z.array(
    z.object({
      sql: z.string(),
      args: z.array(z.any()).optional(),
    })
  ),
  transactional: z.boolean().optional(),
  timeout: z.number().optional(),
  requireAuth: z.boolean().optional(),
});

// ==================== Repository 层数据类型 Schema (统一自 src/services/entities) ====================

/**
 * 创建心情记录数据 Schema (统一自 MoodEntryRepository)
 */
export const CreateMoodEntryDataSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  timestamp: z.string().datetime(),
  emotion_data_set_id: z.string().optional(),
  intensity: z.number().min(0).max(100),
  reflection: z.string().max(1000).optional(),
  tags: z.array(z.string()).max(10).optional(),

  // 表情集关联
  emoji_set_id: z.string().optional(),
  emoji_set_version: z.string().optional(),

  // 皮肤配置快照
  skin_id: z.string().optional(),
  skin_config_snapshot: z.string().optional(),

  // 显示配置快照
  view_type_used: z.string().optional(),
  render_engine_used: z.string().optional(),
  display_mode_used: z.string().optional(),

  // 用户配置快照
  user_config_snapshot: z.string().optional(),
});

/**
 * 更新心情记录数据 Schema (统一自 MoodEntryRepository)
 */
export const UpdateMoodEntryDataSchema = z.object({
  timestamp: z.string().datetime().optional(),
  emotion_data_set_id: z.string().optional(),
  intensity: z.number().min(0).max(100).optional(),
  reflection: z.string().max(1000).optional(),
  tags: z.array(z.string()).max(10).optional(),
  sync_status: z.string().optional(),
  server_id: z.string().optional(),
  last_synced_at: z.string().datetime().optional(),

  // 表情集关联
  emoji_set_id: z.string().optional(),
  emoji_set_version: z.string().optional(),

  // 皮肤配置快照
  skin_id: z.string().optional(),
  skin_config_snapshot: z.string().optional(),

  // 显示配置快照
  view_type_used: z.lazy(() => ViewTypeSchema).optional(),
  render_engine_used: z.lazy(() => RenderEngineSchema).optional(),
  display_mode_used: z.lazy(() => ContentDisplayModeSchema).optional(),

  // 用户配置快照
  user_config_snapshot: z.string().optional(),
});

/**
 * 心情记录过滤器 Schema (统一自 MoodEntryRepository)
 */
export const MoodEntryFilterSchema = z.object({
  userId: z.string().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  emotionDataSetId: z.string().optional(),
  minIntensity: z.number().min(0).max(100).optional(),
  maxIntensity: z.number().min(0).max(100).optional(),
  tags: z.array(z.string()).optional(),
  syncStatus: z.string().optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['timestamp', 'intensity', 'created_at']).optional(),
  orderDirection: z.enum(['ASC', 'DESC']).optional(),
});

// ==================== 情绪选择 Repository 类型 Schema ====================

/**
 * 创建情绪选择数据 Schema (统一自 EmotionSelectionRepository)
 */
export const CreateEmotionSelectionDataSchema = z.object({
  id: z.string(),
  mood_entry_id: z.string(),
  emotion_id: z.string(),
  tier_level: z.number().int().min(1).max(10),
  emotion_data_set_emotion_id: z.string().optional(),
  intensity: z.number().int().min(0).max(100).optional(),
  created_at: z.string(),

  // 表情信息
  emoji_item_id: z.string().optional(),
  emoji_unicode: z.string().optional(),
  emoji_image_url: z.string().url().optional(),
  emoji_animation_data: z.string().optional(),

  // 选择上下文
  selection_path: z.string().optional(),
  parent_selection_id: z.string().optional(),
});

/**
 * 更新情绪选择数据 Schema (统一自 EmotionSelectionRepository)
 */
export const UpdateEmotionSelectionDataSchema = z.object({
  emotion_id: z.string().optional(),
  tier_level: z.number().int().min(1).max(10).optional(),
  emotion_data_set_emotion_id: z.string().optional(),
});

/**
 * 情绪选择过滤器 Schema (统一自 EmotionSelectionRepository)
 */
export const EmotionSelectionFilterSchema = z.object({
  mood_entry_id: z.string().optional(),
  emotion_id: z.string().optional(),
  tier_level: z.number().int().min(1).max(10).optional(),
  user_id: z.string().optional(),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['tier_level', 'created_at']).optional(),
  orderDirection: z.enum(['ASC', 'DESC']).optional(),
  include_emotion: z.boolean().optional(),
  include_mood_entry: z.boolean().optional(),
});

// ==================== 情绪 Repository 类型 Schema ====================

/**
 * 创建情绪数据 Schema (统一自 EmotionRepository)
 */
export const CreateEmotionDataSchema = z.object({
  id: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  emoji: z.string().optional(),
  color: z.string().optional(),
  tier_level: z.number().int().min(1).max(10).optional(),
  parent_id: z.string().optional(),
  keywords: z.string().optional(), // JSON array of keywords
  image_url: z.string().url().optional(),
  created_by: z.string().optional(),
});

/**
 * 更新情绪数据 Schema (统一自 EmotionRepository)
 */
export const UpdateEmotionDataSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  emoji: z.string().optional(),
  color: z.string().optional(),
  tier_level: z.number().int().min(1).max(10).optional(),
  parent_id: z.string().optional(),
  keywords: z.string().optional(),
  image_url: z.string().url().optional(),
  updated_by: z.string().optional(),
});

/**
 * 情绪过滤器 Schema (统一自 EmotionRepository)
 */
export const EmotionFilterSchema = z.object({
  languageCode: z.string().optional(),
  searchTerm: z.string().optional(),
  tier_level: z.number().int().min(1).max(10).optional(),
  parent_id: z.string().optional(),
  color: z.string().optional(),
  has_parent: z.boolean().optional(),
  is_deleted: z.boolean().optional(),
  created_by: z.string().optional(),
  keywords: z.array(z.string()).optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['name', 'tier_level', 'created_at', 'updated_at']).optional(),
  orderDirection: z.enum(['ASC', 'DESC']).optional(),
  include_children: z.boolean().optional(),
  include_parent: z.boolean().optional(),
});

// ==================== 皮肤 Repository 类型 Schema ====================

/**
 * 创建皮肤数据 Schema (统一自 SkinRepository)
 */
export const CreateSkinDataSchema = z.object({
  id: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  category: z.string().optional(),
  version: z.string().optional(),
  tags: z.string().optional(), // JSON array
  preview_image_light: z.string().url().optional(),
  preview_image_dark: z.string().url().optional(),
  is_premium: z.boolean(),
  is_unlocked: z.boolean(),
  unlock_conditions: z.string().optional(), // JSON object
  author: z.string().optional(),
  supported_content_modes: z.string().optional(), // JSON array
  supported_view_types: z.string().optional(), // JSON array
  supported_render_engines: z.string().optional(), // JSON array
  config: z.string(), // JSON object - SkinConfig
});

/**
 * 更新皮肤数据 Schema (统一自 SkinRepository)
 */
export const UpdateSkinDataSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  category: z.string().optional(),
  version: z.string().optional(),
  tags: z.string().optional(),
  preview_image_light: z.string().url().optional(),
  preview_image_dark: z.string().url().optional(),
  is_premium: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  unlock_conditions: z.string().optional(),
  author: z.string().optional(),
  supported_content_modes: z.string().optional(),
  supported_view_types: z.string().optional(),
  supported_render_engines: z.string().optional(),
  config: z.string().optional(),
});

// ==================== 表情集 Repository 类型 Schema ====================

/**
 * 创建表情集数据 Schema (统一自 EmojiSetRepository)
 */
export const CreateEmojiSetDataSchema = z.object({
  id: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  type: z.enum(['unicode', 'image', 'svg', 'animated']),
  is_default: z.boolean(),
  is_system: z.boolean(),
  is_unlocked: z.boolean(),
  price: z.number().min(0).optional(),
  preview_image: z.string().url().optional(),
  created_by: z.string().optional(),
});

/**
 * 更新表情集数据 Schema (统一自 EmojiSetRepository)
 */
export const UpdateEmojiSetDataSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  type: z.enum(['unicode', 'image', 'svg', 'animated']).optional(),
  is_default: z.boolean().optional(),
  is_system: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  price: z.number().min(0).optional(),
  preview_image: z.string().url().optional(),
});

// ==================== 表情项 Repository 类型 Schema ====================

/**
 * 创建表情项数据 Schema (统一自 EmojiItemRepository)
 */
export const CreateEmojiItemDataSchema = z.object({
  id: z.string(),
  emoji_set_id: z.string(),
  emotion_id: z.string(),
  unicode_char: z.string().optional(),
  image_url: z.string().url().optional(),
  svg_path: z.string().optional(),
  animation_url: z.string().url().optional(),
  alt_text: z.string().optional(),
});

/**
 * 更新表情项数据 Schema (统一自 EmojiItemRepository)
 */
export const UpdateEmojiItemDataSchema = z.object({
  emoji_set_id: z.string().optional(),
  emotion_id: z.string().optional(),
  unicode_char: z.string().optional(),
  image_url: z.string().url().optional(),
  svg_path: z.string().optional(),
  animation_url: z.string().url().optional(),
  alt_text: z.string().optional(),
});

// ==================== 用户配置 Repository 类型 Schema ====================

// ==================== 全局应用设置 Input Schema ====================

/**
 * 创建全局应用配置数据 Schema
 */
export const CreateGlobalAppConfigInputSchema = z.object({
  id: z.string(),
  name: z.string().min(1).default('default'),
  user_id: z.string(),
  is_active: z.boolean().optional(),
  theme_mode: z.enum(['light', 'dark', 'system']).optional(),
  language: z.enum(['zh-CN', 'en-US']).optional(),
  accessibility: z.string().optional(), // JSON string
  notifications_enabled: z.boolean().optional(),
  sound_enabled: z.boolean().optional(),
});

/**
 * 更新全局应用配置数据 Schema
 */
export const UpdateGlobalAppConfigInputSchema = z.object({
  name: z.string().min(1).optional(),
  is_active: z.boolean().optional(),
  theme_mode: z.enum(['light', 'dark', 'system']).optional(),
  language: z.enum(['zh-CN', 'en-US']).optional(),
  accessibility: z.string().optional(),
  notifications_enabled: z.boolean().optional(),
  sound_enabled: z.boolean().optional(),
});

// ==================== Quiz系统配置 Input Schema ====================

/**
 * 创建Quiz用户偏好配置数据 Schema
 */
export const CreateUserQuizPreferencesInputSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  config_name: z.string().min(1).default('default'),
  presentation_config: z.string(), // JSON string
  config_version: z.string().optional(),
  personalization_level: z.number().int().min(0).max(100).optional(),
  is_active: z.boolean().optional(),
  is_default: z.boolean().optional(),
});

/**
 * 更新Quiz用户偏好配置数据 Schema
 */
export const UpdateUserQuizPreferencesInputSchema = z.object({
  config_name: z.string().min(1).optional(),
  presentation_config: z.string().optional(),
  config_version: z.string().optional(),
  personalization_level: z.number().int().min(0).max(100).optional(),
  is_active: z.boolean().optional(),
  is_default: z.boolean().optional(),
});

/**
 * 创建Quiz包覆盖配置数据 Schema
 */
export const CreateQuizPackOverridesInputSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  pack_id: z.string(),
  presentation_overrides: z.string().optional(), // JSON string
  override_reason: z.enum(['user_preference', 'accessibility_need', 'performance_optimization']).optional(),
  override_priority: z.number().int().min(1).max(10).optional(),
  is_active: z.boolean().optional(),
});

/**
 * 更新Quiz包覆盖配置数据 Schema
 */
export const UpdateQuizPackOverridesInputSchema = z.object({
  presentation_overrides: z.string().optional(),
  override_reason: z.enum(['user_preference', 'accessibility_need', 'performance_optimization']).optional(),
  override_priority: z.number().int().min(1).max(10).optional(),
  is_active: z.boolean().optional(),
});

/**
 * 创建Quiz会话配置数据 Schema
 */
export const CreateQuizSessionConfigInputSchema = z.object({
  id: z.string(),
  session_id: z.string(),
  user_id: z.string(),
  pack_id: z.string(),
  final_presentation_config: z.string(), // JSON string
  config_sources: z.string(), // JSON string
  personalization_level: z.number().int().min(0).max(100).optional(),
  config_version: z.string().optional(),
});

// ==================== 配置系统 tRPC Input Schema ====================

/**
 * 全局应用设置 tRPC Input Schema
 */
export const GetGlobalAppConfigInputSchema = z.object({
  config_name: z.string().optional().default('default')
});

export const UpdateGlobalAppConfigTRPCInputSchema = UpdateGlobalAppConfigInputSchema;

export const CreateGlobalAppConfigTRPCInputSchema = CreateGlobalAppConfigInputSchema.omit({ user_id: true });

/**
 * Quiz系统配置 tRPC Input Schema
 */
export const GetUserQuizPreferencesInputSchema = z.object({
  config_name: z.string().optional().default('default')
});

export const UpdateUserQuizPreferencesTRPCInputSchema = z.object({
  config_name: z.string().optional().default('default'),
  updates: UpdateUserQuizPreferencesInputSchema
});

export const CreateUserQuizPreferencesTRPCInputSchema = CreateUserQuizPreferencesInputSchema.omit({ user_id: true });

export const GetQuizPackOverridesInputSchema = z.object({
  pack_id: z.string()
});

export const UpdateQuizPackOverridesTRPCInputSchema = z.object({
  pack_id: z.string(),
  updates: UpdateQuizPackOverridesInputSchema
});

export const CreateQuizPackOverridesTRPCInputSchema = CreateQuizPackOverridesInputSchema.omit({ user_id: true });

export const GenerateQuizSessionConfigInputSchema = z.object({
  pack_id: z.string(),
  session_id: z.string()
});

export const GetQuizSessionConfigInputSchema = z.object({
  session_id: z.string()
});

/**
 * 配置系统 tRPC Response Schema
 */
export const ConfigResponseSchema = z.object({
  success: z.boolean(),
  error: z.string().optional()
});

export const GlobalAppConfigResponseSchema = ConfigResponseSchema.extend({
  data: GlobalAppConfigSchema.optional()
});

export const GlobalAppConfigListResponseSchema = ConfigResponseSchema.extend({
  data: z.array(GlobalAppConfigSchema).optional()
});

export const UserPresentationConfigsResponseSchema = ConfigResponseSchema.extend({
  data: UserPresentationConfigsSchema.optional()
});

export const PackPresentationOverridesResponseSchema = ConfigResponseSchema.extend({
  data: PackPresentationOverridesSchema.optional()
});

export const QuizSessionConfigResponseSchema = ConfigResponseSchema.extend({
  data: QuizSessionConfigSchema.optional()
});

// 保留旧的Schema作为别名，用于向后兼容
export const CreateUserConfigDataSchema = CreateGlobalAppConfigInputSchema;
export const UpdateUserConfigDataSchema = UpdateGlobalAppConfigInputSchema;

/**
 * 用户配置过滤器 Schema (统一自 UserConfigRepository)
 */
export const UserConfigFilterSchema = z.object({
  userId: z.string().optional(),
  isActive: z.boolean().optional(),
  name: z.string().optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['name', 'created_at', 'last_updated']).optional(),
  orderDirection: z.enum(['ASC', 'DESC']).optional(),
});

// API 响应类型
export type AuthResponse = z.infer<typeof AuthResponseSchema>;
export type PurchaseResult = z.infer<typeof PurchaseResultSchema>;
export type PurchaseHistoryItem = z.infer<typeof PurchaseHistoryItemSchema>;
export type VipPlan = z.infer<typeof VipPlanSchema>;
export type VipStatus = z.infer<typeof VipStatusSchema>;
// 配置相关类型
export type GlobalAppConfig = z.infer<typeof GlobalAppConfigSchema>;
export type UserPresentationConfigs = z.infer<typeof UserPresentationConfigsSchema>;
export type PackPresentationOverrides = z.infer<typeof PackPresentationOverridesSchema>;
export type QuizSessionConfig = z.infer<typeof QuizSessionConfigSchema>;
export type UserConfig = z.infer<typeof UserConfigSchema>; // 向后兼容

// 向后兼容的类型别名
export type UserQuizPreferences = UserPresentationConfigs;
export type QuizPackOverrides = PackPresentationOverrides;
export type LanguageSupport = z.infer<typeof LanguageSupportSchema>;
export type TranslationStats = z.infer<typeof TranslationStatsSchema>;
export type BatchTranslationResult = z.infer<typeof BatchTranslationResultSchema>;

// ==================== Tag 相关 Schema ====================

/**
 * 创建标签数据 Schema (统一自 TagRepository)
 */
export const CreateTagDataSchema = z.object({
  id: z.lazy(() => IdSchema),
  name: z.string().min(1).max(100),
});

/**
 * 更新标签数据 Schema (统一自 TagRepository)
 */
export const UpdateTagDataSchema = z.object({
  name: z.string().min(1).max(100).optional(),
});

// ==================== 情绪数据集相关 Schema ====================

/**
 * 创建情绪数据集数据 Schema (统一自 EmotionDataSetRepository)
 */
export const CreateEmotionDataSetDataSchema = z.object({
  id: z.lazy(() => IdSchema),
  name: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  is_active: z.boolean().default(true),
  is_default: z.boolean().default(false),
  is_system_default: z.boolean().default(false),
  default_emoji_set_id: z.lazy(() => OptionalIdSchema),
  supported_languages: z.array(z.lazy(() => LanguageCodeSchema)).optional(), // JSON array
  created_by: z.lazy(() => OptionalIdSchema),
});

/**
 * 更新情绪数据集数据 Schema (统一自 EmotionDataSetRepository)
 */
export const UpdateEmotionDataSetDataSchema = z.object({
  name: z.string().min(1).max(200).optional(),
  description: z.string().max(1000).optional(),
  is_active: z.boolean().optional(),
  is_default: z.boolean().optional(),
  is_system_default: z.boolean().optional(),
  default_emoji_set_id: z.lazy(() => OptionalIdSchema),
  supported_languages: z.array(z.lazy(() => LanguageCodeSchema)).optional(), // JSON array
});

/**
 * 情绪数据集过滤器 Schema (统一自 EmotionDataSetRepository)
 */
export const EmotionDataSetFilterSchema = z.object({
  languageCode: z.string().optional(),
  searchTerm: z.string().optional(),
  is_active: z.boolean().optional(),
  is_default: z.boolean().optional(),
  is_system_default: z.boolean().optional(),
  created_by: z.lazy(() => OptionalIdSchema),
  supported_language: z.string().optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['name', 'created_at', 'updated_at']).optional(),
  orderDirection: z.enum(['ASC', 'DESC']).optional(),
  include_tiers: z.boolean().optional(),
  include_emotions_count: z.boolean().optional(),
});

// ==================== 情绪数据集层级相关 Schema ====================

/**
 * 创建情绪数据集层级数据 Schema (统一自 EmotionDataSetTierRepository)
 */
export const CreateEmotionDataSetTierDataSchema = z.object({
  id: z.lazy(() => IdSchema),
  emotion_data_set_id: z.lazy(() => IdSchema),
  name: z.string().min(1).max(200),
  level: z.number().int().min(1).max(10),
  parent_tier_id: z.lazy(() => OptionalIdSchema),
  color: z.string().optional(),
  description: z.string().max(1000).optional(),
});

/**
 * 更新情绪数据集层级数据 Schema (统一自 EmotionDataSetTierRepository)
 */
export const UpdateEmotionDataSetTierDataSchema = z.object({
  name: z.string().min(1).max(200).optional(),
  level: z.number().int().min(1).max(10).optional(),
  parent_tier_id: z.lazy(() => OptionalIdSchema),
  color: z.string().optional(),
  description: z.string().max(1000).optional(),
});

/**
 * 情绪数据集层级过滤器 Schema (统一自 EmotionDataSetTierRepository)
 */
export const EmotionDataSetTierFilterSchema = z.object({
  languageCode: z.string().optional(),
  searchTerm: z.string().optional(),
  emotion_data_set_id: z.lazy(() => OptionalIdSchema),
  level: z.number().int().min(1).max(10).optional(),
  parent_tier_id: z.lazy(() => OptionalIdSchema),
  color: z.string().optional(),
  has_parent: z.boolean().optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['name', 'level', 'created_at', 'updated_at']).optional(),
  orderDirection: z.enum(['ASC', 'DESC']).optional(),
  include_children: z.boolean().optional(),
  include_emotions_count: z.boolean().optional(),
});

// ==================== 心情记录标签关联相关 Schema ====================

/**
 * 创建心情记录标签关联数据 Schema (统一自 MoodEntryTagRepository)
 */
export const CreateMoodEntryTagDataSchema = z.object({
  mood_entry_id: IdSchema,
  tag_id: IdSchema,
});

/**
 * 更新心情记录标签关联数据 Schema (统一自 MoodEntryTagRepository)
 */
export const UpdateMoodEntryTagDataSchema = z.object({
  // 关联表通常只有创建和删除操作，无更新字段
});

/**
 * 心情记录标签关联过滤器 Schema (统一自 MoodEntryTagRepository)
 */
export const MoodEntryTagFilterSchema = z.object({
  mood_entry_id: OptionalIdSchema,
  tag_id: OptionalIdSchema,
  user_id: OptionalIdSchema,
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['mood_entry_id', 'tag_id']).optional(),
  orderDirection: z.enum(['ASC', 'DESC']).optional(),
  include_mood_entry: z.boolean().optional(),
  include_tag: z.boolean().optional(),
});

// ==================== Service 输入类型 Schema ====================

/**
 * 创建情绪输入 Schema (统一自 EmotionService)
 */
export const CreateEmotionInputSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  emoji: z.string().optional(),
  color: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional(),
  tier_level: z.number().int().min(1).max(5).optional(),
  parent_id: OptionalIdSchema,
  keywords: z.array(z.string().max(50)).max(20).optional(),
  image_url: z.string().url().optional(),
  created_by: OptionalIdSchema,
  translations: z
    .array(
      z.object({
        languageCode: LanguageCodeSchema,
        translatedName: z.string().optional(),
        translatedDescription: z.string().optional(),
      })
    )
    .optional(),
});

/**
 * 更新情绪输入 Schema (统一自 EmotionService)
 */
export const UpdateEmotionInputSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  emoji: z.string().optional(),
  color: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional(),
  tier_level: z.number().int().min(1).max(5).optional(),
  parent_id: OptionalIdSchema,
  keywords: z.array(z.string().max(50)).max(20).optional(),
  image_url: z.string().url().optional(),
  updated_by: OptionalIdSchema,
  translations: z
    .array(
      z.object({
        languageCode: LanguageCodeSchema,
        translatedName: z.string().optional(),
        translatedDescription: z.string().optional(),
      })
    )
    .optional(),
});

/**
 * 创建表情集输入 Schema (统一自 EmojiSetService)
 */
export const CreateEmojiSetInputSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  type: EmojiSetTypeSchema,
  is_default: z.boolean().optional(),
  is_system: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  price: z.number().min(0).optional(),
  preview_image: z.string().url().optional(),
  created_by: OptionalIdSchema,
});

/**
 * 更新表情集输入 Schema (统一自 EmojiSetService)
 */
export const UpdateEmojiSetInputSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  type: EmojiSetTypeSchema.optional(),
  is_default: z.boolean().optional(),
  is_system: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  price: z.number().min(0).optional(),
  preview_image: z.string().url().optional(),
});

/**
 * 创建皮肤输入 Schema (统一自 SkinService)
 */
export const CreateSkinInputSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  category: z.string().optional(),
  version: z.string().optional(),
  tags: z.array(z.string()).optional(),
  preview_image_light: z.string().url().optional(),
  preview_image_dark: z.string().url().optional(),
  is_premium: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  unlock_conditions: z.any().optional(), // UnlockConditions object
  author: z.string().optional(),
  supported_content_modes: z.array(z.string()).optional(),
  supported_view_types: z.array(z.string()).optional(),
  supported_render_engines: z.array(z.string()).optional(),
  config: z.any(), // SkinConfig object
  translations: z
    .array(
      z.object({
        languageCode: LanguageCodeSchema,
        translatedName: z.string().optional(),
        translatedDescription: z.string().optional(),
      })
    )
    .optional(),
});

/**
 * 更新皮肤输入 Schema (统一自 SkinService)
 */
export const UpdateSkinInputSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  category: z.string().optional(),
  version: z.string().optional(),
  tags: z.array(z.string()).optional(),
  preview_image_light: z.string().url().optional(),
  preview_image_dark: z.string().url().optional(),
  is_premium: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  unlock_conditions: z.any().optional(), // UnlockConditions object
  author: z.string().optional(),
  supported_content_modes: z.array(z.string()).optional(),
  supported_view_types: z.array(z.string()).optional(),
  supported_render_engines: z.array(z.string()).optional(),
  config: z.any().optional(), // SkinConfig object
  translations: z
    .array(
      z.object({
        languageCode: LanguageCodeSchema,
        translatedName: z.string().optional(),
        translatedDescription: z.string().optional(),
      })
    )
    .optional(),
});

/**
 * @deprecated CreateEmojiItemInputSchema 已废弃
 * 原因：依赖于已废弃的emotions表，emoji映射现在通过展现配置管理
 * 替代：使用 EmojiMappingConfig 接口和相关API
 */
// export const CreateEmojiItemInputSchema = z.object({
//   emoji_set_id: z.string(),
//   emotion_id: z.string(),
//   unicode_char: z.string().optional(),
//   image_url: z.string().url().optional(),
//   svg_path: z.string().optional(),
//   animation_url: z.string().url().optional(),
//   alt_text: z.string().optional(),
// });

/**
 * @deprecated UpdateEmojiItemInputSchema 已废弃
 * 原因：依赖于已废弃的emotions表，emoji映射现在通过展现配置管理
 * 替代：使用 EmojiMappingConfig 接口和相关API
 */
// export const UpdateEmojiItemInputSchema = z.object({
//   emoji_set_id: z.string().optional(),
//   emotion_id: z.string().optional(),
//   unicode_char: z.string().optional(),
//   image_url: z.string().url().optional(),
//   svg_path: z.string().optional(),
//   animation_url: z.string().url().optional(),
//   alt_text: z.string().optional(),
// });

/**
 * 创建用户配置输入 Schema (统一自 UserConfigService)
 */
export const CreateUserConfigInputSchema = z.object({
  name: z.string().min(1).max(100),
  user_id: z.string(),
  active_emotion_data_id: z.string(),
  active_skin_id: z.string(),
  preferred_view_type: z.string(),
  dark_mode: z.boolean().optional(),
  color_mode: z.string().optional(),
  render_engine_preferences: z.string().optional(),
  layout_preferences: z.string().optional(),
  content_display_mode_preferences: z.string().optional(),
  view_type_skin_ids: z.string().optional(),
  accessibility: z.string().optional(),
  recent_emotion_data_ids: z.string().optional(),
  recent_skin_ids: z.string().optional(),
});

/**
 * 更新用户配置输入 Schema (服务层扁平结构，统一自 UserConfigService)
 */
export const UpdateUserConfigServiceInputSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  is_active: z.boolean().optional(),
  active_emotion_data_id: z.string().optional(),
  active_skin_id: z.string().optional(),
  preferred_view_type: z.string().optional(),
  dark_mode: z.boolean().optional(),
  color_mode: z.string().optional(),
  render_engine_preferences: z.string().optional(),
  layout_preferences: z.string().optional(),
  content_display_mode_preferences: z.string().optional(),
  view_type_skin_ids: z.string().optional(),
  accessibility: z.string().optional(),
  recent_emotion_data_ids: z.string().optional(),
  recent_skin_ids: z.string().optional(),
});

// ==================== UILabel Repository 类型 Schema ====================

/**
 * 创建UI标签数据 Schema (统一自 UILabelRepository)
 */
export const CreateUILabelDataSchema = z.object({
  id: IdSchema,
  key: z.string().min(1).max(200),
  name: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  category: z.string().max(100).optional(),
});

/**
 * 更新UI标签数据 Schema (统一自 UILabelRepository)
 */
export const UpdateUILabelDataSchema = z.object({
  key: z.string().min(1).max(200).optional(),
  name: z.string().min(1).max(200).optional(),
  description: z.string().max(1000).optional(),
  category: z.string().max(100).optional(),
});

// ==================== 新增：预览和布局配置类型 Schema ====================

/**
 * 预览配置 Schema
 */
export const Preview_configschema = z.object({
  viewType: z.string(),
  renderEngine: z.string(),
  contentDisplayMode: z.string(),
  layout: z.string().optional(),
  skinId: OptionalIdSchema,
  emotionDataSetId: OptionalIdSchema,
  showLabels: z.boolean().default(true),
  showIntensity: z.boolean().default(true),
  animationEnabled: z.boolean().default(true),
  interactionEnabled: z.boolean().default(true),
});

/**
 * 布局配置 Schema
 */
export const LayoutConfigSchema = z.object({
  // 通用布局配置
  spacing: z.number().min(0).optional(),
  padding: z.number().min(0).optional(),
  margin: z.number().min(0).optional(),

  // 网格布局配置
  columns: z.number().int().min(1).optional(),
  rows: z.number().int().min(1).optional(),

  // 圆形布局配置
  radius: z.number().min(0).optional(),
  startAngle: z.number().optional(),
  endAngle: z.number().optional(),

  // 力导向布局配置
  strength: z.number().optional(),
  distance: z.number().min(0).optional(),
  iterations: z.number().int().min(1).optional(),

  // 层级布局配置
  levelHeight: z.number().min(0).optional(),
  nodeSpacing: z.number().min(0).optional(),

  // 自定义配置
  customConfig: z.record(z.any()).optional(),
});



// 注意：CreateUserConfigInputSchema, UpdateUserConfigInputSchema, CreateMoodEntryInputSchema, UpdateMoodEntryInputSchema
// 已在文件前面定义，这里不重复定义

// 通用类型
export type Pagination = z.infer<typeof PaginationSchema>;
export type Sort = z.infer<typeof SortSchema>;

// 新增的类型导出
export type CreateTagData = z.infer<typeof CreateTagDataSchema>;
export type UpdateTagData = z.infer<typeof UpdateTagDataSchema>;
export type CreateEmotionDataSetData = z.infer<typeof CreateEmotionDataSetDataSchema>;
export type UpdateEmotionDataSetData = z.infer<typeof UpdateEmotionDataSetDataSchema>;
export type EmotionDataSetFilter = z.infer<typeof EmotionDataSetFilterSchema>;
export type CreateEmotionDataSetTierData = z.infer<typeof CreateEmotionDataSetTierDataSchema>;
export type UpdateEmotionDataSetTierData = z.infer<typeof UpdateEmotionDataSetTierDataSchema>;
export type EmotionDataSetTierFilter = z.infer<typeof EmotionDataSetTierFilterSchema>;
export type CreateMoodEntryTagData = z.infer<typeof CreateMoodEntryTagDataSchema>;
export type UpdateMoodEntryTagData = z.infer<typeof UpdateMoodEntryTagDataSchema>;
export type MoodEntryTagFilter = z.infer<typeof MoodEntryTagFilterSchema>;
// 注意：CreateEmotionInput 和 UpdateEmotionInput 已在下面的 Service 输入类型部分定义
export type CreateUILabelData = z.infer<typeof CreateUILabelDataSchema>;
export type UpdateUILabelData = z.infer<typeof UpdateUILabelDataSchema>;

// 新增配置类型
export type PreviewConfig = z.infer<typeof Preview_configschema>;
export type LayoutConfig = z.infer<typeof LayoutConfigSchema>;
export type DatabaseSkinConfig = z.infer<typeof SkinConfigSchema>; // 数据库版本的 SkinConfig

// ViewConfig 接口定义 - 用于视图渲染时的配置
export interface ViewConfig {
  onBack?: () => void;
  selectedPath?: any;
  [key: string]: any;
}

// ==================== 新增：查询过滤器类型 Schema ====================

/**
 * 表情集查询过滤器 Schema
 */
export const EmojiSetFilterSchema = z.object({
  languageCode: z.string().optional(),
  searchTerm: z.string().optional(),
  type: EmojiSetTypeSchema.optional(),
  is_default: z.boolean().optional(),
  is_system: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  created_by: z.string().optional(),
  limit: z.number().int().min(1).max(100).optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['name', 'type', 'created_at', 'updated_at']).optional(),
  orderDirection: OrderDirectionSchema.optional(),
  include_items: z.boolean().optional(),
  include_items_count: z.boolean().optional(),
});

/**
 * 表情项查询过滤器 Schema
 */
export const EmojiItemFilterSchema = z.object({
  emoji_set_id: z.string().optional(),
  emotion_id: z.string().optional(),
  has_unicode: z.boolean().optional(),
  has_image: z.boolean().optional(),
  has_svg: z.boolean().optional(),
  has_animation: z.boolean().optional(),
  limit: z.number().int().min(1).max(100).optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['emotion_id', 'created_at']).optional(),
  orderDirection: OrderDirectionSchema.optional(),
  include_emoji_set: z.boolean().optional(),
  include_emotion: z.boolean().optional(),
});

// 注意：EmotionFilterSchema, EmotionDataSetFilterSchema, MoodEntryFilterSchema 已在文件前面定义，这里不重复定义

/**
 * 皮肤查询过滤器 Schema
 */
export const SkinFilterSchema = z.object({
  languageCode: z.string().optional(),
  searchTerm: z.string().optional(),
  category: SkinCategorySchema.optional(),
  is_premium: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  author: z.string().optional(),
  tags: z.array(z.string()).optional(),
  supported_content_mode: ContentDisplayModeSchema.optional(),
  supported_view_type: ViewTypeSchema.optional(),
  supported_render_engine: RenderEngineSchema.optional(),
  limit: z.number().int().min(1).max(100).optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['name', 'category', 'created_at', 'updated_at']).optional(),
  orderDirection: OrderDirectionSchema.optional(),
});

// ==================== 新增：统计信息类型 Schema ====================

/**
 * 表情集统计信息 Schema
 */
export const EmojiSetStatsSchema = z.object({
  emojiSet: z.any(), // EmojiSet reference
  items_count: z.number().int().min(0),
  emotions_covered: z.number().int().min(0),
  usage_count: z.number().int().min(0),
  last_used: z.string().datetime().optional(),
});

/**
 * 表情使用统计 Schema
 */
export const EmojiUsageStatsSchema = z.object({
  emojiItem: z.any(), // EmojiItem reference
  usage_count: z.number().int().min(0),
  last_used: z.string().datetime().optional(),
  users_count: z.number().int().min(0),
});

/**
 * 心情统计信息 Schema
 */
export const MoodStatsSchema = z.object({
  total_entries: z.number().int().min(0),
  avg_intensity: z.number().min(0).max(100),
  most_common_emotion: z.string().optional(),
  most_common_tags: z.array(z.string()),
  entries_this_week: z.number().int().min(0),
  entries_this_month: z.number().int().min(0),
  streak_days: z.number().int().min(0),
  last_entry_date: z.string().datetime().optional(),
});

/**
 * 心情趋势 Schema
 */
export const MoodTrendSchema = z.object({
  date: z.string().datetime(),
  avg_intensity: z.number().min(0).max(100),
  entry_count: z.number().int().min(0),
  dominant_emotion: z.string().optional(),
  dominant_tags: z.array(z.string()),
});

// ==================== 新增：业务逻辑类型 Schema ====================

/**
 * 服务结果 Schema
 */
export const ServiceResultSchema = z.union([
  z.object({
    success: z.literal(true),
    data: z.any(),
    error: z.never().optional(),
  }),
  z.object({
    success: z.literal(false),
    data: z.never().optional(),
    error: z.string(),
  }),
]);

/**
 * 分页结果 Schema
 */
export const PaginatedResultSchema = z.object({
  items: z.array(z.any()),
  total: z.number().int().min(0),
  page: z.number().int().min(1),
  pageSize: z.number().int().min(1),
  totalPages: z.number().int().min(0),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

/**
 * 布局偏好设置 Schema
 */
export const LayoutPreferencesSchema = z.object({
  card: z.string().optional(), // CardLayout
  bubble: z.string().optional(), // BubbleLayout
  galaxy: z.string().optional(), // GalaxyLayout
  list: z.string().optional(), // ListLayout
  grid: z.string().optional(), // GridLayout
  tree: z.string().optional(), // TreeLayout
  flow: z.string().optional(), // FlowLayout
  tag_cloud: z.string().optional(), // TagCloudLayout
});

/**
 * 渲染引擎偏好设置 Schema
 */
export const RenderEnginePreferencesSchema = z.object({
  wheel: z.string().optional(), // RenderEngine
  card: z.string().optional(),
  bubble: z.string().optional(),
  galaxy: z.string().optional(),
  list: z.string().optional(),
  grid: z.string().optional(),
  tree: z.string().optional(),
  flow: z.string().optional(),
  tagCloud: z.string().optional(),
});

/**
 * 在线服务类型定义 (简化版)
 * 只保留基础类型，业务逻辑类型通过tRPC定义
 */

// API 响应基础类型 (保留用于基础服务)
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  timestamp?: string;
}

// API 错误类型
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
  timestamp?: string;
}

// 网络状态类型
export interface NetworkStatus {
  isOnline: boolean;
  connectionType?: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  isSlowConnection?: boolean;
  lastOnlineTime?: Date;
  lastOfflineTime?: Date;
}

// API 请求配置
export interface ApiRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  requireAuth?: boolean;
  skipErrorHandling?: boolean;
}

// 上传进度信息
export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number; // bytes per second
  estimatedTime?: number; // seconds
}

// 下载进度信息
export interface DownloadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number; // bytes per second
  estimatedTime?: number; // seconds
}

// 服务器信息类型
export interface ServerInfo {
  version: string;
  environment: 'development' | 'staging' | 'production';
  features: string[];
  maintenance?: {
    scheduled: boolean;
    startTime?: Date;
    endTime?: Date;
    message?: string;
  };
  limits?: {
    maxRequestSize: number;
    maxBatchSize: number;
    rateLimit: number;
  };
}

// 健康检查响应
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  services: {
    database: 'up' | 'down' | 'degraded';
    cache: 'up' | 'down' | 'degraded';
    storage: 'up' | 'down' | 'degraded';
  };
  responseTime: number; // milliseconds
  version: string;
}

// 在线服务事件类型 (简化版)
export type OnlineServiceEvent =
  | { type: 'network_status_changed'; data: NetworkStatus }
  | { type: 'server_error'; data: ApiError }
  | { type: 'connection_lost'; data: { timestamp: Date } }
  | { type: 'connection_restored'; data: { timestamp: Date } };

// 缓存策略类型
export interface CacheStrategy {
  type: 'memory' | 'localStorage' | 'indexedDB' | 'none';
  ttl?: number; // seconds
  maxSize?: number; // bytes
  compression?: boolean;
}

// API 端点配置
export interface ApiEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  requireAuth: boolean;
  cache?: CacheStrategy;
  timeout?: number;
  retries?: number;
  rateLimit?: {
    requests: number;
    window: number; // seconds
  };
}

// WebSocket 连接状态
export interface WebSocketStatus {
  connected: boolean;
  connecting: boolean;
  lastConnectedAt?: Date;
  lastDisconnectedAt?: Date;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  reconnectDelay: number;
}

// 服务质量指标
export interface ServiceQualityMetrics {
  averageResponseTime: number; // milliseconds
  successRate: number; // percentage
  errorRate: number; // percentage
  uptime: number; // percentage
  lastMeasuredAt: Date;
  sampleSize: number;
}

// 数据使用统计
export interface DataUsageStats {
  uploadedBytes: number;
  downloadedBytes: number;
  requestCount: number;
  cacheHitRate: number; // percentage
  compressionRatio: number; // percentage
  period: {
    start: Date;
    end: Date;
  };
}

// tRPC 客户端类型 (用于业务服务)
export interface TRPCClientType {
  // 认证相关
  login: { mutate: (data: any) => Promise<any> };
  register: { mutate: (data: any) => Promise<any> };
  logout: { mutate: () => Promise<any> };

  // VIP和支付相关
  getVipPlans: { query: () => Promise<any> };
  getVipStatus: { query: () => Promise<any> };
  purchaseVip: { mutate: (data: any) => Promise<any> };
  purchaseSkin: { mutate: (data: any) => Promise<any> };
  getPurchaseHistory: { query: () => Promise<any> };

  // 数据同步相关
  synchronizeData: { mutate: (data: any) => Promise<any> };
  performFullSync: { mutate: (data: any) => Promise<any> };

  // 数据库查询相关
  query: { query: (data: any) => Promise<any> };
  batch: { mutate: (data: any) => Promise<any> };

  // 分析相关
  getMoodAnalytics: { query: (data: any) => Promise<any> };
  getEmotionUsageStats: { query: (data: any) => Promise<any> };
  getUserActivityStats: { query: (data: any) => Promise<any> };
}

// 注意：AuthToken 类型已在上面的 Schema 部分定义

// 批量操作结果类型 (保留用于API客户端)
export interface BatchOperationResult<T = any> {
  results: T[];
  errors: Array<{ index: number; error: string }>;
  totalProcessed: number;
  totalSuccessful: number;
  totalFailed: number;
}

// 在线服务配置类型 (扩展版)
export interface OnlineServiceConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  authConfig: {
    tokenStorageKey: string;
    autoRefresh: boolean;
    refreshThreshold: number; // minutes before expiry
  };
}

// 注意：以下类型已移除，现在通过tRPC定义：
// - AuthUser, AuthState, LoginCredentials, RegisterData
// - MoodEntry, UserProfile, PaymentPlan, PaymentResult
// - SyncStatus, SyncConfig, SyncItem, SyncBatch
// - ConflictResolution, RealtimeUpdate
//
// 这些类型现在在以下位置定义：
// - 认证相关：src/hooks/useAuth.ts 或 src/lib/trpc.ts
// - 业务数据：server/lib/router.ts (tRPC路由定义)
// - 同步相关：src/hooks/useDataSync.ts

// ==================== 导出新增类型 ====================

// 查询过滤器类型（新增的，避免重复）
export type EmojiSetFilter = z.infer<typeof EmojiSetFilterSchema>;
export type EmojiItemFilter = z.infer<typeof EmojiItemFilterSchema>;
export type SkinFilter = z.infer<typeof SkinFilterSchema>;
// 注意：EmotionFilter, EmotionDataSetFilter, MoodEntryFilter 已在文件前面定义

// 统计信息类型
export type EmojiSetStats = z.infer<typeof EmojiSetStatsSchema>;
export type EmojiUsageStats = z.infer<typeof EmojiUsageStatsSchema>;
export type MoodStats = z.infer<typeof MoodStatsSchema>;
export type MoodTrend = z.infer<typeof MoodTrendSchema>;

// Service 输入类型
export type CreateEmotionInput = z.infer<typeof CreateEmotionInputSchema>;
export type UpdateEmotionInput = z.infer<typeof UpdateEmotionInputSchema>;
export type CreateEmojiSetInput = z.infer<typeof CreateEmojiSetInputSchema>;
export type UpdateEmojiSetInput = z.infer<typeof UpdateEmojiSetInputSchema>;
export type CreateSkinInput = z.infer<typeof CreateSkinInputSchema>;
export type UpdateSkinInput = z.infer<typeof UpdateSkinInputSchema>;
// @deprecated CreateEmojiItemInput 和 UpdateEmojiItemInput 已废弃
// export type CreateEmojiItemInput = z.infer<typeof CreateEmojiItemInputSchema>;
// export type UpdateEmojiItemInput = z.infer<typeof UpdateEmojiItemInputSchema>;
export type CreateUserConfigInput = z.infer<typeof CreateUserConfigInputSchema>;
export type UpdateUserConfigServiceInput = z.infer<typeof UpdateUserConfigServiceInputSchema>;

// 业务逻辑类型
export type ServiceResult<T> =
  | {
      success: true;
      data: T;
      error?: never;
    }
  | {
      success: false;
      data?: never;
      error: string;
    };

export type PaginatedResult<T> = {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
};

export type LayoutPreferences = z.infer<typeof LayoutPreferencesSchema>;
export type RenderEnginePreferences = z.infer<typeof RenderEnginePreferencesSchema>;

// ==================== 新增：业务逻辑类型 Schema ====================

/**
 * 表情项映射 Schema（向后兼容）
 */
export const EmotionEmojiMappingSchema = z.record(z.union([z.string(), z.any()]));

/**
 * @deprecated EmojiMappingSchema 已废弃（向后兼容）
 * 原因：依赖于已废弃的emotions表
 * 替代：使用 EmojiMappingConfig 接口
 */
// export const EmojiMappingSchema = z.object({
//   emotion_id: z.string(),
//   emoji_set_id: z.string(),
//   emoji_id: z.string(),
// });

/**
 * 表情集配置 Schema
 */
export const EmojiSetConfigSchema = z.object({
  default_set_id: z.string(),
  fallback_unicode: z.boolean(),
  cache_enabled: z.boolean(),
  cache_duration: z.number().int().min(0),
});

/**
 * 情绪层级树节点 Schema
 */
export const EmotionTreeNodeSchema = z.object({
  emotion: z.any(), // Emotion reference
  children: z.array(z.lazy(() => EmotionTreeNodeSchema)),
  level: z.number().int().min(1),
  path: z.array(z.string()),
});

/**
 * 关联数据同步状态 Schema
 */
export const RelatedDataSyncStatusSchema = z.object({
  moodEntry: SyncStatusSchema,
  emotionSelections: SyncStatusSchema,
  emojiItems: SyncStatusSchema,
  userConfig: SyncStatusSchema,
  lastSyncAttempt: z.string().datetime().optional(),
  syncErrors: z.array(z.string()).optional(),
  partialSyncData: z
    .object({
      syncedFields: z.array(z.string()),
      failedFields: z.array(z.string()),
    })
    .optional(),
});

/**
 * 计算属性和派生数据 Schema
 */
export const ComputedMoodDataSchema = z.object({
  dominantEmotion: z.string().optional(),
  emotionIntensityAverage: z.number().min(0).max(100),
  emotionDiversity: z.number().min(0),
  selectionComplexity: z.number().min(0),
  visualRepresentation: z.object({
    primaryColor: z.string(),
    secondaryColors: z.array(z.string()),
    emojiSequence: z.array(z.string()),
  }),
});

/**
 * 显示偏好 Schema
 */
export const DisplayPreferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'auto']).optional(),
  colorScheme: z.string().optional(),
  animationsEnabled: z.boolean().optional(),
  compactMode: z.boolean().optional(),
  showLabels: z.boolean().optional(),
  showTooltips: z.boolean().optional(),
});

/**
 * 心情记录显示数据 Schema
 */
export const MoodEntryDisplayDataSchema = z.object({
  entry: z.any(), // MoodEntry reference
  computedData: ComputedMoodDataSchema,
  displayConfig: DisplayPreferencesSchema,
  renderingHints: z.object({
    preferredLayout: z.string(),
    animationSettings: z.any(),
    colorScheme: z.string(),
  }),
});

/**
 * 情绪可视化数据 Schema
 */
export const EmotionVisualizationDataSchema = z.object({
  emotionId: z.string(),
  visualElements: z.object({
    emoji: z.string(),
    color: z.string(),
    position: z
      .object({
        x: z.number(),
        y: z.number(),
      })
      .optional(),
    size: z.number().optional(),
    opacity: z.number().min(0).max(1).optional(),
  }),
  animationData: z
    .object({
      type: z.enum(['static', 'pulse', 'bounce', 'fade']),
      duration: z.number().min(0),
      delay: z.number().min(0).optional(),
    })
    .optional(),
  interactionData: z
    .object({
      clickable: z.boolean(),
      hoverEffect: z.boolean(),
      tooltip: z.string().optional(),
    })
    .optional(),
});

/**
 * 皮肤使用记录 Schema
 */
export const SkinUsageSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  skin_id: z.string(),
  started_at: z.string().datetime(),
  ended_at: z.string().datetime().optional(),
  duration: z.number().int().min(0).optional(),
  created_at: z.string(),
});

/**
 * UI标签类别 Schema
 */
export const UILabelCategorySchema = z.enum([
  'navigation',
  'action',
  'status',
  'form',
  'content',
  'system',
  'error',
  'success',
  'warning',
  'info',
]);

/**
 * UI标签使用统计 Schema
 */
export const UILabelUsageStatsSchema = z.object({
  label_key: z.string(),
  usage_count: z.number().int().min(0),
  last_used: z.string().datetime().optional(),
  contexts: z.array(z.string()),
  languages_available: z.array(z.string()),
  translation_completeness: z.number().min(0).max(1),
});

/**
 * UI标签翻译覆盖率 Schema
 */
export const UILabelTranslationCoverageSchema = z.object({
  total_labels: z.number().int().min(0),
  translated_labels: z.number().int().min(0),
  coverage_percentage: z.number().min(0).max(100),
  missing_translations: z.array(
    z.object({
      label_key: z.string(),
      missing_languages: z.array(z.string()),
    })
  ),
  last_updated: z.string().datetime(),
});

/**
 * 用户配置扩展 Schema
 */
export const UserConfigExtendedSchema = z.object({
  // 基础配置字段
  id: z.string().optional(),
  name: z.string().optional(),
  user_id: z.string().optional(),
  is_active: z.boolean().optional(),
  active_emotion_data_id: z.string().optional(),
  active_skin_id: z.string().optional(),
  preferred_view_type: z.string().optional(),
  dark_mode: z.boolean().optional(),
  color_mode: z.string().optional(),
  render_engine_preferences: z.string().optional(),
  layout_preferences: z.string().optional(),
  content_display_mode_preferences: z.string().optional(),
  view_type_skin_ids: z.string().optional(),
  accessibility: z.string().optional(),
  created_at: z.string().optional(),
  last_updated: z.string().datetime().optional(),

  // 扩展字段（解析后的类型）
  recent_emotion_data_ids: z.array(z.string()).optional(),
  recent_skin_ids: z.array(z.string()).optional(),

  // 使用统计
  usage_stats: z
    .object({
      total_sessions: z.number().int().min(0),
      total_mood_entries: z.number().int().min(0),
      favorite_view_types: z.array(z.string()),
      last_active: z.string().datetime(),
    })
    .optional(),
});

// ==================== 导出新增业务逻辑类型 ====================

export type EmotionEmojiMapping = z.infer<typeof EmotionEmojiMappingSchema>;
export type EmojiMapping = z.infer<typeof EmojiMappingSchema>;
export type EmojiSetConfig = z.infer<typeof EmojiSetConfigSchema>;
export type EmotionTreeNode = z.infer<typeof EmotionTreeNodeSchema>;
export type RelatedDataSyncStatus = z.infer<typeof RelatedDataSyncStatusSchema>;
export type ComputedMoodData = z.infer<typeof ComputedMoodDataSchema>;
export type DisplayPreferences = z.infer<typeof DisplayPreferencesSchema>;
export type MoodEntryDisplayData = z.infer<typeof MoodEntryDisplayDataSchema>;
export type EmotionVisualizationData = z.infer<typeof EmotionVisualizationDataSchema>;
export type SkinUsage = z.infer<typeof SkinUsageSchema>;
export type UILabelCategory = z.infer<typeof UILabelCategorySchema>;
export type UILabelUsageStats = z.infer<typeof UILabelUsageStatsSchema>;
export type UILabelTranslationCoverage = z.infer<typeof UILabelTranslationCoverageSchema>;
export type UserConfigExtended = z.infer<typeof UserConfigExtendedSchema>;

// ==================== 业务逻辑版本的 Skin 类型 ====================

/**
 * 业务逻辑版本的皮肤配置 Schema（使用驼峰命名）
 */
export const BusinessSkinConfigSchema = z.object({
  // 支持的内容显示模式和视图类型
  supported_content_modes: z.array(z.string()).optional(),
  supported_view_types: z.array(z.string()).optional(),
  supported_render_engines: z.array(z.string()).optional(),

  // 颜色配置
  colors: z.object({
    primary: z.string(),
    secondary: z.string(),
    background: z.string(),
    text: z.string(),
    accent: z.string(),
  }),

  // 字体配置
  fonts: z.object({
    family: z.string(),
    size: z.object({
      small: z.number(),
      medium: z.number(),
      large: z.number(),
    }),
    weight: z.object({
      normal: z.number(),
      bold: z.number(),
    }),
  }),

  // 效果配置（使用下划线命名）
  effects: z
    .object({
      shadows: z.boolean().optional(),
      shadow_color: z.string().optional(),
      shadow_blur: z.number().optional(),
      shadow_offset_x: z.number().optional(),
      shadow_offset_y: z.number().optional(),
      animations: z.boolean().optional(),
      animation_duration: z.number().optional(),
      animation_easing: z.string().optional(),
      border_radius: z.number().optional(),
      border_width: z.number().optional(),
      border_color: z.string().optional(),
      border_style: z.string().optional(),
      opacity: z.number().optional(),
      blur: z.number().optional(),
      grayscale: z.number().optional(),
      brightness: z.number().optional(),
      contrast: z.number().optional(),
      saturation: z.number().optional(),
      hue_rotate: z.number().optional(),
      backdrop_filter: z.string().optional(),
      text_shadow: z.string().optional(),
    })
    .optional(),

  // 视图配置（使用下划线命名）
  view_configs: z
    .object({
      // 通用配置
      common: z
        .object({
          background: z
            .object({
              type: z.enum(['solid', 'gradient', 'image']).optional(),
              color: z.string().optional(),
              gradient: z.string().optional(),
              image: z.string().optional(),
              opacity: z.number().optional(),
            })
            .optional(),
        })
        .optional(),

      // 轮盘视图配置
      wheel: z
        .object({
          container_size: z.number().optional(),
          wheel_radius: z.number().optional(),
          sector_gap: z.number().optional(),
          decoration_type: z.enum(['none', 'glow', 'sparkles', 'dots', 'waves', 'lines']).optional(),
          sector_padding: z.number().optional(),
          emoji_size: z.number().optional(),
          emoji_position: z.enum(['center', 'inner', 'outer']).optional(),
          text_size: z.number().optional(),
          text_position: z.enum(['center', 'inner', 'outer', 'tooltip']).optional(),
          text_visible: z.enum(['always', 'hover', 'never']).optional(),
          decorations: z.boolean().optional(),
          use_3d_effects: z.boolean().optional(),
          shadow_enabled: z.boolean().optional(),
          shadow_color: z.string().optional(),
          shadow_blur: z.number().optional(),
          hover_effect: z.enum(['none', 'scale', 'glow', 'pulse']).optional(),
          selection_animation: z.enum(['none', 'scale', 'glow', 'pulse']).optional(),
          transition_duration: z.number().optional(),
        })
        .optional(),

      // 星系视图配置（使用下划线命名）
      galaxy: z
        .object({
          container_size: z.number().optional(),
          star_size: z.number().optional(),
          star_min_size: z.number().optional(),
          star_max_size: z.number().optional(),
          orbit_size: z.number().optional(),
          orbit_width: z.number().optional(),
          orbit_color: z.string().optional(),
          orbit_opacity: z.number().optional(),
          orbit_visible: z.boolean().optional(),
          center_size: z.number().optional(),
          center_color: z.string().optional(),
          center_glow: z.boolean().optional(),
          center_glow_color: z.string().optional(),
          center_glow_size: z.number().optional(),
          background_type: z.enum(['stars', 'nebula', 'solid', 'gradient']).optional(),
          background_color: z.string().optional(),
          background_star_density: z.number().optional(),
          background_star_color: z.string().optional(),
          background_nebula_color: z.string().optional(),
          background_nebula_opacity: z.number().optional(),
          star_shape: z.enum(['circle', 'square', 'diamond', 'star']).optional(),
          star_color: z.string().optional(),
          star_border_width: z.number().optional(),
          star_border_color: z.string().optional(),
          star_glow: z.boolean().optional(),
          star_glow_color: z.string().optional(),
          star_glow_size: z.number().optional(),
          star_hover_effect: z.enum(['none', 'scale', 'glow', 'pulse']).optional(),
          star_hover_scale: z.number().optional(),
          star_selected_effect: z.enum(['none', 'scale', 'glow', 'pulse']).optional(),
          shadow_color: z.string().optional(),
          shadow_blur: z.number().optional(),
          hover_effect: z.enum(['none', 'scale', 'glow', 'pulse']).optional(),
          selection_animation: z.enum(['none', 'scale', 'glow', 'pulse']).optional(),
          transition_duration: z.number().optional(),

          star_selected_glow_color: z.string().optional(),
          emoji_size: z.number().optional(),
          text_visible: z.enum(['always', 'hover', 'never']).optional(),
          text_size: z.number().optional(),
          text_color: z.string().optional(),
          text_background: z.string().optional(),
          text_background_opacity: z.number().optional(),
          text_position: z.enum(['inside', 'outside', 'tooltip']).optional(),
          layout: z.enum(['circular', 'spiral', 'random', 'grid']).optional(),
          rotation_enabled: z.boolean().optional(),
          rotation_speed: z.number().optional(),
          rotation_direction: z.enum(['clockwise', 'counterclockwise']).optional(),
          zoom_enabled: z.boolean().optional(),
          zoom_min_scale: z.number().optional(),
          zoom_max_scale: z.number().optional(),
          parallax_effect: z.boolean().optional(),
          parallax_intensity: z.number().optional(),
          perspective_3d: z.boolean().optional(),
          perspective_3d_intensity: z.number().optional(),
        })
        .optional(),

      // 卡片视图配置
      card: z
        .object({
          card_size: z.number().optional(),
          card_width: z.number().optional(),
          card_height: z.number().optional(),
          card_spacing: z.number().optional(),
          card_border_radius: z.number().optional(),
          card_hover_effect: z.enum(['scale', 'glow', 'lift', 'none']).optional(),
          card_hover_scale: z.number().optional(),
          columns:z.number().optional(),
          emoji_size: z.number().optional(),
          aspect_ratio: z.number().optional(),
          text_size: z.number().optional(),
          layout: z.enum(['grid', 'list', 'masonry', 'carousel']).optional(),
        })
        .optional(),

      // 气泡视图配置
      bubble: z
        .object({
          bubble_size: z.number().optional(),
          bubble_spacing: z.number().optional(),
          bubble_shape: z.enum(['circle', 'oval', 'rounded-rect', 'custom']).optional(),
          bubble_border_radius: z.number().optional(),
          bubble_hover_effect: z.enum(['scale', 'glow', 'lift', 'none']).optional(),
          bubble_hover_scale: z.number().optional(),
          bubble_background_opacity: z.number().optional(),
          floating_animation: z.boolean().optional(),
          emoji_size: z.number().optional(),
          text_visible: z.enum(['always', 'hover', 'never']).optional(),
          layout: z.enum(['cluster', 'force', 'random', 'circle', 'float', 'grid']).optional(),
        })
        .optional(),
        list: z
        .object({
          item_spacing: z.number().optional(),
          item_spacing_horizontal: z.number().optional(),
          item_padding: z.number().optional(),
          hover_effect: z.enum(['highlight', 'scale', 'glow', 'lift', 'pulse', 'none']).optional(),
          selection_animation: z.enum(['highlight', 'scale', 'glow', 'lift', 'pulse', 'none']).optional(),
          emoji_size: z.number().optional(),
          text_size: z.number().optional(),
          layout: z.enum(['vertical', 'horizontal']).optional(),
        })
        .optional(),
    })
    .optional(),
});

/**
 * 业务逻辑版本的皮肤 Schema（使用下划线命名）
 */
export const BusinessSkinSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  category: z.string().optional(),
  version: z.string().optional(),
  tags: z.array(z.string()).optional(), // 业务逻辑中使用数组
  preview_image: z.string().optional(),
  preview_image_light: z.string().optional(),
  preview_image_dark: z.string().optional(),
  is_premium: z.boolean().optional(),
  is_unlocked: z.boolean().optional(),
  is_default: z.boolean().optional(),
  unlock_conditions: z.any().optional(), // 业务逻辑中使用对象
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  created_by: z.string().optional(),
  author: z.string().optional(),
  config: BusinessSkinConfigSchema, // 业务逻辑中使用对象
});

// 导出业务逻辑类型
export type BusinessSkinConfig = z.infer<typeof BusinessSkinConfigSchema>;
export type BusinessSkin = z.infer<typeof BusinessSkinSchema>;

// 为了向后兼容，创建类型别名
export type SkinConfig = BusinessSkinConfig;
export type Skin = BusinessSkin;

// ==================== Quiz 系统相关 Schema ====================

/**
 * 获取Quiz包列表输入 Schema
 */
export const GetQuizPacksInputSchema = z.object({
  userId: z.lazy(() => IdSchema).optional(),
  userType: z.enum(['free', 'premium']).default('free'),
  category: z.string().optional(),
  quizType: z.string().optional(),
  difficultyLevel: z.number().int().min(1).max(5).optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
});

/**
 * 搜索Quiz包输入 Schema
 */
export const SearchQuizPacksInputSchema = z.object({
  searchTerm: z.string().min(1),
  filters: z.object({
    category: z.string().optional(),
    quizType: z.string().optional(),
    difficultyLevel: z.number().int().min(1).max(5).optional(),
    tags: z.array(z.string()).optional(),
  }).optional(),
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
});

/**
 * 获取Quiz包详情输入 Schema
 */
export const GetQuizPackDetailsInputSchema = z.object({
  packId: z.lazy(() => IdSchema),
});

/**
 * 创建Quiz会话输入 Schema
 */
export const CreateQuizSessionInputSchema = z.object({
  pack_id: z.lazy(() => IdSchema),
  user_id: z.lazy(() => IdSchema),
  session_type: z.string().optional(),
  session_metadata: z.record(z.any()).optional(),
});

/**
 * 更新Quiz会话输入 Schema
 */
export const UpdateQuizSessionInputSchema = z.object({
  status: z.string().optional(),
  current_question_index: z.number().int().optional(),
  total_questions: z.number().int().optional(),
  answered_questions: z.number().int().optional(),
  completion_percentage: z.number().optional(),
  end_time: z.lazy(() => TimestampSchema).optional(),
  session_metadata: z.record(z.any()).optional(),
});

/**
 * 获取当前问题数据输入 Schema
 */
export const GetCurrentQuestionInputSchema = z.object({
  sessionId: z.lazy(() => IdSchema),
});

/**
 * 提交答案输入 Schema
 */
export const SubmitAnswerInputSchema = z.object({
  session_id: z.lazy(() => IdSchema),
  question_id: z.lazy(() => IdSchema),
  selected_option_ids: z.array(z.lazy(() => IdSchema)),
  answer_value: z.string(),
  answer_text: z.string().optional(),
  confidence_score: z.number().min(0).max(100).optional(),
  response_time_ms: z.number().int().min(0).optional(),
});

/**
 * 创建Quiz答案输入 Schema
 */
export const CreateQuizAnswerInputSchema = z.object({
  session_id: z.lazy(() => IdSchema),
  question_id: z.lazy(() => IdSchema),
  session_presentation_config_id: z.lazy(() => OptionalIdSchema),
  selected_option_ids: z.array(z.lazy(() => IdSchema)),
  answer_value: z.string(),
  answer_text: z.string().optional(),
  confidence_score: z.number().min(0).max(100).optional(),
  response_time_ms: z.number().int().min(0).optional(),
});

/**
 * 更新Quiz答案输入 Schema
 */
export const UpdateQuizAnswerInputSchema = z.object({
  selected_option_ids: z.array(z.lazy(() => IdSchema)).optional(),
  answer_value: z.string().optional(),
  answer_text: z.string().optional(),
  confidence_score: z.number().min(0).max(100).optional(),
  response_time_ms: z.number().int().min(0).optional(),
});

/**
 * 获取用户会话输入 Schema
 */
export const GetUserSessionsInputSchema = z.object({
  userId: z.lazy(() => IdSchema),
  limit: z.number().int().min(1).max(100).default(20),
  status: z.enum(['INITIATED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED']).optional(),
});

/**
 * 获取会话答案输入 Schema
 */
export const GetSessionAnswersInputSchema = z.object({
  sessionId: z.lazy(() => IdSchema),
});

/**
 * 会话操作输入 Schema
 */
export const SessionOperationInputSchema = z.object({
  sessionId: z.lazy(() => IdSchema),
});

/**
 * 问题呈现数据 Schema
 */
export const QuestionPresentationDataSchema = z.object({
  session_id: z.string(),
  question_id: z.string(),
  question_text: z.string(),
  question_text_localized: z.string(),
  question_type: z.string(),
  question_order: z.number(),

  question_options: z.array(z.object({
    option_id: z.string(),
    option_text: z.string(),
    option_text_localized: z.string(),
    option_value: z.string(),
    option_type: z.enum(['choice', 'scale_point', 'ranking_item', 'matrix_row', 'matrix_column', 'input_constraint']),
    option_order: z.number(),
    scoring_value: z.number().optional(),

    // 多媒体内容
    media_url: z.string().optional(),
    media_type: z.enum(['image', 'audio', 'video']).optional(),
    media_thumbnail_url: z.string().optional(),

    // 矩阵配置
    matrix_row_id: z.string().optional(),
    matrix_column_id: z.string().optional(),

    // 数值范围
    min_value: z.number().optional(),
    max_value: z.number().optional(),
    step_value: z.number().optional(),

    // 约束配置
    constraints: z.record(z.any()).optional(),

    // 联动关系
    parent_emotion: z.string().optional(),
    grandparent_emotion: z.string().optional(),
  })),

  progress_info: z.object({
    current_question: z.number(),
    total_questions: z.number(),
    completion_percentage: z.number(),
    estimated_remaining_time: z.string().optional(),
  }),

  navigation_config: z.object({
    show_back_button: z.boolean(),
    show_skip_button: z.boolean(),
    show_progress_indicator: z.boolean(),
    allow_revision: z.boolean(),
  }),

  question_config: z.record(z.any()).optional(),
  validation_rules: z.record(z.any()).optional(),
});

/**
 * 答案提交结果 Schema
 */
export const AnswerSubmissionResultSchema = z.object({
  session_id: z.string(),
  question_id: z.string(),
  is_valid: z.boolean(),
  validation_error_key: z.string().optional(),
  next_action_hint: z.string().optional(),
  progress_update: z.object({
    current_question: z.number(),
    total_questions: z.number(),
    completion_percentage: z.number(),
  }).optional(),
  next_question_data: QuestionPresentationDataSchema.optional(),
});

// 导出Quiz相关类型
export type GetQuizPacksInput = z.infer<typeof GetQuizPacksInputSchema>;
export type SearchQuizPacksInput = z.infer<typeof SearchQuizPacksInputSchema>;
export type GetQuizPackDetailsInput = z.infer<typeof GetQuizPackDetailsInputSchema>;
export type CreateQuizSessionInput = z.infer<typeof CreateQuizSessionInputSchema>;
export type UpdateQuizSessionInput = z.infer<typeof UpdateQuizSessionInputSchema>;
export type GetCurrentQuestionInput = z.infer<typeof GetCurrentQuestionInputSchema>;
export type SubmitAnswerInput = z.infer<typeof SubmitAnswerInputSchema>;
export type CreateQuizAnswerInput = z.infer<typeof CreateQuizAnswerInputSchema>;
export type UpdateQuizAnswerInput = z.infer<typeof UpdateQuizAnswerInputSchema>;

// ==================== Quiz Pack 类型 ====================

/**
 * 创建Quiz包输入 Schema
 */
export const CreateQuizPackInputSchema = z.object({
  name: z.string().min(1),
  name_localized: z.record(z.string()).optional(),
  description: z.string().optional(),
  description_localized: z.record(z.string()).optional(),
  quiz_type: z.string(),
  quiz_style: z.string().optional(),
  category: z.string().optional(),
  difficulty_level: z.number().int().min(1).max(5).default(1),
  estimated_duration_minutes: z.number().int().min(1).optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
  is_active: z.boolean().default(true),
});

/**
 * 更新Quiz包输入 Schema
 */
export const UpdateQuizPackInputSchema = z.object({
  name: z.string().min(1).optional(),
  name_localized: z.record(z.string()).optional(),
  description: z.string().optional(),
  description_localized: z.record(z.string()).optional(),
  quiz_type: z.string().optional(),
  quiz_style: z.string().optional(),
  category: z.string().optional(),
  difficulty_level: z.number().int().min(1).max(5).optional(),
  estimated_duration_minutes: z.number().int().min(1).optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
  is_active: z.boolean().optional(),
});

export type CreateQuizPackInput = z.infer<typeof CreateQuizPackInputSchema>;
export type UpdateQuizPackInput = z.infer<typeof UpdateQuizPackInputSchema>;

// ==================== Quiz Question 类型 ====================

/**
 * 创建Quiz问题输入 Schema
 */
export const CreateQuizQuestionInputSchema = z.object({
  pack_id: z.lazy(() => IdSchema),
  question_text: z.string().min(1),
  question_text_localized: z.record(z.string()).optional(),
  question_type: z.string(),
  question_order: z.number().int().min(0),
  question_group: z.string().optional(),
  tier_level: z.number().int().min(1).optional(),
  question_config: z.record(z.any()).optional(),
  validation_rules: z.record(z.any()).optional(),
  scoring_config: z.record(z.any()).optional(),
  parent_question_id: z.lazy(() => OptionalIdSchema),
  dependency_rules: z.record(z.any()).optional(),
  is_required: z.boolean().default(true),
  is_active: z.boolean().default(true),
  created_by: z.string().optional(),
});

/**
 * 更新Quiz问题输入 Schema
 */
export const UpdateQuizQuestionInputSchema = z.object({
  question_text: z.string().min(1).optional(),
  question_text_localized: z.record(z.string()).optional(),
  question_type: z.string().optional(),
  question_order: z.number().int().min(0).optional(),
  question_group: z.string().optional(),
  tier_level: z.number().int().min(1).optional(),
  question_config: z.record(z.any()).optional(),
  validation_rules: z.record(z.any()).optional(),
  scoring_config: z.record(z.any()).optional(),
  parent_question_id: z.lazy(() => OptionalIdSchema),
  dependency_rules: z.record(z.any()).optional(),
  is_required: z.boolean().optional(),
  is_active: z.boolean().optional(),
  updated_by: z.string().optional(),
});

export type CreateQuizQuestionInput = z.infer<typeof CreateQuizQuestionInputSchema>;
export type UpdateQuizQuestionInput = z.infer<typeof UpdateQuizQuestionInputSchema>;

// ==================== Quiz Question Option 类型 ====================

/**
 * 创建Quiz问题选项输入 Schema
 */
export const CreateQuizQuestionOptionInputSchema = z.object({
  question_id: z.lazy(() => IdSchema),
  option_text: z.string().min(1),
  option_text_localized: z.string().optional(), // JSON string
  option_value: z.string(),
  option_type: z.enum(['choice', 'scale_point', 'ranking_item', 'matrix_row', 'matrix_column', 'input_constraint']).default('choice'),
  option_order: z.number().int().min(0),
  scoring_value: z.number().optional(),
  scoring_weight: z.number().optional(),
  min_value: z.number().optional(),
  max_value: z.number().optional(),
  step_value: z.number().optional(),
  media_url: z.string().optional(),
  media_type: z.enum(['image', 'audio', 'video']).optional(),
  media_thumbnail_url: z.string().optional(),
  media_alt_text: z.string().optional(),
  matrix_row_id: z.string().optional(),
  matrix_column_id: z.string().optional(),
  reference_pack_id: z.string().optional(),
  reference_option_id: z.string().optional(),
  metadata: z.string().optional(), // JSON string
  tags: z.string().optional(), // JSON array string
  is_active: z.boolean().default(true),
});

/**
 * 更新Quiz问题选项输入 Schema
 */
export const UpdateQuizQuestionOptionInputSchema = z.object({
  option_text: z.string().min(1).optional(),
  option_text_localized: z.string().optional(),
  option_value: z.string().optional(),
  option_type: z.enum(['choice', 'scale_point', 'ranking_item', 'matrix_row', 'matrix_column', 'input_constraint']).optional(),
  option_order: z.number().int().min(0).optional(),
  scoring_value: z.number().optional(),
  scoring_weight: z.number().optional(),
  min_value: z.number().optional(),
  max_value: z.number().optional(),
  step_value: z.number().optional(),
  media_url: z.string().optional(),
  media_type: z.enum(['image', 'audio', 'video']).optional(),
  media_thumbnail_url: z.string().optional(),
  media_alt_text: z.string().optional(),
  matrix_row_id: z.string().optional(),
  matrix_column_id: z.string().optional(),
  reference_pack_id: z.string().optional(),
  reference_option_id: z.string().optional(),
  metadata: z.string().optional(),
  tags: z.string().optional(),
  is_active: z.boolean().optional(),
});

export type CreateQuizQuestionOptionInput = z.infer<typeof CreateQuizQuestionOptionInputSchema>;
export type UpdateQuizQuestionOptionInput = z.infer<typeof UpdateQuizQuestionOptionInputSchema>;

// ==================== Skin 类型 ====================

// CreateSkinInputSchema 和 UpdateSkinInputSchema 已在第2222行和第2252行定义
// 这里不再重复定义，避免重复声明错误

// ==================== Tag 类型 ====================

/**
 * 创建标签输入 Schema
 */
export const CreateTagInputSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  category: z.string().optional(),
  color: z.string().optional(),
  icon: z.string().optional(),
  is_system: z.boolean().default(false),
  is_active: z.boolean().default(true),
  created_by: z.string().optional(),
});

/**
 * 更新标签输入 Schema
 */
export const UpdateTagInputSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  category: z.string().optional(),
  color: z.string().optional(),
  icon: z.string().optional(),
  is_system: z.boolean().optional(),
  is_active: z.boolean().optional(),
  updated_by: z.string().optional(),
});

export type CreateTagInput = z.infer<typeof CreateTagInputSchema>;
export type UpdateTagInput = z.infer<typeof UpdateTagInputSchema>;

// ==================== UILabel 类型 ====================

/**
 * 创建UI标签输入 Schema
 */
export const CreateUILabelInputSchema = z.object({
  key: z.string().min(1),
  default_text: z.string().min(1),
  category: z.string().optional(),
  context: z.string().optional(),
  description: z.string().optional(),
  is_system: z.boolean().default(false),
  is_active: z.boolean().default(true),
});

/**
 * 更新UI标签输入 Schema
 */
export const UpdateUILabelInputSchema = z.object({
  key: z.string().min(1).optional(),
  default_text: z.string().min(1).optional(),
  category: z.string().optional(),
  context: z.string().optional(),
  description: z.string().optional(),
  is_system: z.boolean().optional(),
  is_active: z.boolean().optional(),
});

export type CreateUILabelInput = z.infer<typeof CreateUILabelInputSchema>;
export type UpdateUILabelInput = z.infer<typeof UpdateUILabelInputSchema>;

// ==================== UserConfig 类型 ====================

// CreateUserConfigInputSchema 和 UpdateUserConfigInputSchema 已在第2312行和第2332行定义
// 这里不再重复定义，避免重复声明错误

// ==================== 业务接口类型 ====================

/**
 * Quiz问题选项统计信息
 */
export interface QuizQuestionOptionStats {
  total_options: number;
  correct_options: number;
  option_groups: number;
  options_with_images: number;
  options_with_emojis: number;
  avg_scoring_value: number;
}

/**
 * 选项组结果
 */
export interface OptionGroupResult {
  group_name: string;
  options: any[]; // 使用any避免循环依赖，实际使用时会是QuizQuestionOption[]
  total_count: number;
}
export type GetUserSessionsInput = z.infer<typeof GetUserSessionsInputSchema>;
export type GetSessionAnswersInput = z.infer<typeof GetSessionAnswersInputSchema>;
export type SessionOperationInput = z.infer<typeof SessionOperationInputSchema>;
export type QuestionPresentationData = z.infer<typeof QuestionPresentationDataSchema>;
export type AnswerSubmissionResult = z.infer<typeof AnswerSubmissionResultSchema>;
