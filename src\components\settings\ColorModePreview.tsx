import { useTheme } from '@/contexts/ThemeContext';
import type { ColorMode } from '@/types/userConfigTypes';
import { getColorPool } from '@/utils/colorUtils';
import type React from 'react';

interface ColorModePreviewProps {
  colorMode: ColorMode;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  onClick?: () => void;
}

/**
 * 颜色模式预览组件
 * 展示不同颜色模式的颜色样本
 */
const ColorModePreview: React.FC<ColorModePreviewProps> = ({
  colorMode,
  size = 'md',
  showLabel = false,
  onClick,
}) => {
  const { theme } = useTheme();
  const isDarkMode =
    theme === 'dark' ||
    (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // 获取颜色池
  const colorPool = getColorPool(colorMode, isDarkMode);

  // 选择一部分颜色进行展示
  const displayColors = colorPool.slice(0, 5);

  // 根据尺寸设置样式
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-8 h-8';
      default:
        return 'w-6 h-6';
    }
  };

  // 获取颜色模式的标签
  const getColorModeLabel = () => {
    switch (colorMode) {
      case 'warm':
        return '暖色系';
      case 'cool':
        return '冷色系';
      case 'mixed':
        return '混合色系';
      case 'game':
        return '游戏风格';
      default:
        return colorMode;
    }
  };

  return (
    <div
      className={`flex flex-col items-center ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      <div className="flex gap-1 mb-1">
        {displayColors.map((color, index) => (
          <div
            key={index}
            className={`${getSizeClass()} rounded-full`}
            style={{ backgroundColor: color }}
          />
        ))}
      </div>
      {showLabel && <span className="text-xs text-muted-foreground">{getColorModeLabel()}</span>}
    </div>
  );
};

export default ColorModePreview;
