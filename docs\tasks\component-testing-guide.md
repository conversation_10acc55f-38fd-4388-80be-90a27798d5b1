# 组件测试指南

本文档提供了在项目中编写和维护组件测试的指南和最佳实践。

## 目录

1. [测试环境设置](#测试环境设置)
2. [测试组件的基本步骤](#测试组件的基本步骤)
3. [模拟依赖](#模拟依赖)
4. [测试渲染和交互](#测试渲染和交互)
5. [处理路径别名](#处理路径别名)
6. [常见问题和解决方案](#常见问题和解决方案)
7. [测试示例](#测试示例)

## 测试环境设置

我们的项目使用以下工具进行测试：

- **Vitest**: 测试运行器
- **@testing-library/react**: React 组件测试库
- **jsdom**: 浏览器环境模拟

测试文件应放在 `src/tests` 目录下，并遵循以下命名约定：

- 组件测试: `src/tests/components/[ComponentName].test.tsx`
- 工具函数测试: `src/tests/utils/[utilName].test.ts`

## 测试组件的基本步骤

1. **导入必要的依赖**

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ComponentToTest } from '@/path/to/component';
```

2. **创建测试套件**

```typescript
describe('组件名称测试', () => {
  // 测试用例将在这里
});
```

3. **编写测试用例**

```typescript
it('应该正确渲染组件', () => {
  render(<ComponentToTest />);
  // 断言
});
```

## 模拟依赖

### 模拟 Context

如果组件依赖于 React Context，需要模拟 Context 提供者：

```typescript
// 模拟 Context
vi.mock('@/contexts/SomeContext', () => ({
  useSomeContext: vi.fn().mockReturnValue({
    someValue: 'mockValue',
    someFunction: vi.fn()
  })
}));

// 导入模拟后的 hook
import { useSomeContext } from '@/contexts/SomeContext';
```

### 模拟工具函数

```typescript
// 模拟工具函数
vi.mock('@/utils/someUtil', () => ({
  someFunction: vi.fn().mockImplementation(() => 'mockResult')
}));

// 导入模拟后的函数
import * as someUtil from '@/utils/someUtil';
```

## 测试渲染和交互

### 测试组件渲染

```typescript
it('应该正确渲染组件', () => {
  render(<ComponentToTest />);
  
  // 检查元素是否存在
  expect(screen.getByText('预期文本')).toBeDefined();
  
  // 或者使用 query 方法检查元素是否不存在
  expect(screen.queryByText('不应该存在的文本')).toBeNull();
});
```

### 测试用户交互

```typescript
it('应该响应用户点击', () => {
  const handleClick = vi.fn();
  render(<Button onClick={handleClick}>点击我</Button>);
  
  // 模拟点击
  fireEvent.click(screen.getByText('点击我'));
  
  // 验证点击处理函数被调用
  expect(handleClick).toHaveBeenCalledTimes(1);
});
```

## 处理路径别名

在测试文件中使用 `@/` 路径别名需要正确配置 Vitest。我们的项目已经在 `vitest.config.ts` 中配置了路径别名：

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import tsconfigPaths from 'vite-tsconfig-paths';
import path from 'path';

export default defineConfig({
  plugins: [tsconfigPaths()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  test: {
    // 测试配置
  },
});
```

## 常见问题和解决方案

### 1. 模拟 Context 时的问题

如果在模拟 Context 时遇到问题，可以尝试以下方法：

```typescript
// 直接模拟 hook 函数
const mockUseColorMode = vi.fn().mockReturnValue({
  colorMode: 'warm',
  setColorMode: vi.fn()
});

vi.mock('@/contexts/ColorModeContext', () => ({
  useColorMode: () => mockUseColorMode()
}));
```

### 2. 处理 HSL 颜色格式

如果组件返回 HSL 格式的颜色而不是十六进制格式，可以调整测试断言：

```typescript
expect(
  color.match(/^#[0-9a-f]{6}$/i) || 
  color.match(/^hsl\(\s*\d+(\.\d+)?\s*,\s*\d+(\.\d+)?%\s*,\s*\d+(\.\d+)?%\s*\)$/i)
).toBeTruthy();
```

### 3. 测试失败时的调试

如果测试失败，可以使用 `screen.debug()` 查看渲染的 DOM：

```typescript
render(<ComponentToTest />);
screen.debug(); // 打印当前渲染的 DOM
```

## 测试示例

### D3WheelComponent 测试示例

```typescript
/**
 * D3WheelComponent 组件测试
 * 测试 D3WheelComponent 是否正确使用颜色模式
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/react';

// 模拟 D3WheelComponent 组件
vi.mock('@/views/components/wheels/D3WheelComponent', () => ({
  D3WheelComponent: vi.fn((props) => {
    // 返回一个简单的 div，并将 props 作为数据属性
    return <div data-testid="d3-wheel-component" data-props={JSON.stringify(props)}></div>;
  })
}));

// 模拟 ColorModeContext 和 ThemeContext
vi.mock('@/contexts/ColorModeContext', () => ({
  useColorMode: vi.fn().mockReturnValue({
    colorMode: 'warm',
    setColorMode: vi.fn()
  })
}));

vi.mock('@/contexts/ThemeContext', () => ({
  useTheme: vi.fn().mockReturnValue({
    theme: 'light',
    setTheme: vi.fn()
  })
}));

// 导入模拟后的组件和 hooks
import { D3WheelComponent } from '@/views/components/wheels/D3WheelComponent';
import { useColorMode } from '@/contexts/ColorModeContext';
import { useTheme } from '@/contexts/ThemeContext';

// 测试数据
const mockEmotions = [
  { id: 'happy', name: '快乐', emoji: '😊' },
  { id: 'sad', name: '悲伤', emoji: '😢' },
  { id: 'angry', name: '愤怒', emoji: '😠' },
  { id: 'surprised', name: '惊讶', emoji: '😲' }
];

const mockSkinConfig = {
  id: 'test-skin',
  name: 'Test Skin',
  // ... 其他配置
};

describe('D3WheelComponent 组件测试', () => {
  beforeEach(() => {
    // 清除模拟
    vi.clearAllMocks();
  });

  it('应该根据暖色系颜色模式渲染轮盘', () => {
    // 模拟 useColorMode 返回暖色系
    vi.mocked(useColorMode).mockReturnValue({
      colorMode: 'warm',
      setColorMode: vi.fn()
    });
    
    // 渲染组件
    render(
      <D3WheelComponent
        emotions={mockEmotions}
        tierLevel={1}
        contentDisplayMode="text"
        skinConfig={mockSkinConfig}
        onSelect={vi.fn()}
      />
    );
    
    // 验证 D3WheelComponent 是否被调用
    expect(D3WheelComponent).toHaveBeenCalled();
    
    // 检查调用参数
    const calls = vi.mocked(D3WheelComponent).mock.calls;
    const lastCall = calls[calls.length - 1];
    expect(lastCall[0].tierLevel).toBe(1); // 检查层级是否为 1
  });
});
```

### 工具函数测试示例

```typescript
/**
 * 颜色工具测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { calculateContrastRatio } from '@/utils/colorUtils';

describe('颜色工具测试', () => {
  it('calculateContrastRatio 应该计算两个颜色之间的对比度', () => {
    // 黑色和白色的对比度应该很高
    const blackWhiteContrast = calculateContrastRatio('#000000', '#ffffff');
    expect(blackWhiteContrast).toBeGreaterThan(10);
    
    // 相似颜色的对比度应该较低
    const similarColorsContrast = calculateContrastRatio('#ff0000', '#ff0001');
    expect(similarColorsContrast).toBeLessThan(1.5);
  });
});
```

---

通过遵循本指南中的最佳实践，您可以编写出可靠、可维护的组件测试，确保应用程序的质量和稳定性。
