# 分支式Quiz架构设计

## 🎯 核心理念

### 从层级结构到分支逻辑
将复杂的层级数据结构转换为独立但关联的问题序列，通过条件分支逻辑实现智能导航。

```
原始结构: 三层嵌套的情绪树
转换结果: 50个独立问题 + 条件分支逻辑
用户体验: 每次只回答3个问题，获得精确结果
```

## 🏗️ 架构优势

### 1. **完全兼容现有系统**
- ✅ 利用现有的 `quiz_questions` 和 `quiz_question_options` 表
- ✅ 利用现有的 `condition_logic` 字段实现分支
- ✅ 利用现有的 `metadata` 字段存储关系
- ✅ 无需修改核心数据库结构

### 2. **极致的个性化支持**
```typescript
// 问题级别个性化
{
  enabled_questions: [
    { id: "q001_primary_emotion", enabled: true, required: true },
    { id: "q002_happy_secondary", enabled: true, required: false },
    { id: "q005_fearful_secondary", enabled: false, required: false }
  ]
}

// 选项级别个性化  
{
  question_customization: {
    "q001_primary_emotion": {
      disabled_options: ["disgusted", "fearful"],
      custom_order: ["happy", "sad", "angry", "surprised"]
    }
  }
}
```

### 3. **智能分支导航**
```typescript
// 条件逻辑示例
{
  show_if: {
    question_id: "q001_primary_emotion",
    answer_value: "happy"
  }
}

// 复杂条件逻辑
{
  show_if: {
    and: [
      { question_id: "q001_primary_emotion", answer_value: "happy" },
      { user_preference: "detailed_emotions", value: true }
    ]
  }
}
```

## 📊 数据结构设计

### Quiz Pack 元数据
```json
{
  "structure_type": "conditional_branching",
  "total_questions": 50,
  "question_layers": 3,
  "questions_per_session": 3,
  "branching_logic": true,
  "layer_distribution": {
    "primary": 1,
    "secondary": 8,
    "tertiary": 41
  },
  "emotion_coverage": {
    "primary_emotions": 8,
    "secondary_emotions": 41,
    "tertiary_emotions": 82
  }
}
```

### 问题关联关系
```json
{
  "emotion_tier": 2,
  "layer": "secondary",
  "parent_emotion": "happy",
  "triggers_questions": [
    "q010_playful_tertiary",
    "q011_content_tertiary",
    "q012_interested_tertiary"
  ],
  "condition_logic": {
    "show_if": {
      "question_id": "q001_primary_emotion",
      "answer_value": "happy"
    }
  }
}
```

### 情绪路径追踪
```json
{
  "emotion_path": ["happy", "playful", "aroused"],
  "parent_emotion": "playful",
  "grandparent_emotion": "happy",
  "is_final_selection": true,
  "path_depth": 3
}
```

## 🎮 用户体验流程

### 标准流程 (3步完成)
```
Step 1: 主要情绪选择
├── 问题: "您当前的主要情绪是什么？"
├── 选项: 8个主要情绪 (Happy, Sad, Angry, etc.)
└── 用户选择: Happy

Step 2: 次要情绪选择  
├── 问题: "您感到快乐，具体是哪种类型的快乐？"
├── 选项: 9个Happy的次要情绪 (Playful, Content, etc.)
└── 用户选择: Playful

Step 3: 具体情绪选择
├── 问题: "您感到顽皮，具体是哪种感觉？"
├── 选项: 2个Playful的具体情绪 (Aroused, Cheeky)
└── 用户选择: Aroused

结果: 完整情绪路径 ["happy", "playful", "aroused"]
```

### 个性化流程 (可配置)
```
配置1: 只要主要情绪
├── 禁用所有次要和具体情绪问题
└── 1步完成: Happy

配置2: 跳过某些情绪分支
├── 禁用Fearful和Disgusted分支
└── 减少选择复杂度

配置3: 自定义问题顺序
├── 调整情绪选项的显示顺序
└── 根据用户偏好排序
```

## 🔧 技术实现要点

### 1. **条件逻辑引擎**
```typescript
interface ConditionLogic {
  show_if?: {
    question_id: string;
    answer_value: string | string[];
    operator?: 'equals' | 'in' | 'not_equals';
  };
  and?: ConditionLogic[];
  or?: ConditionLogic[];
}

// 使用示例
const shouldShowQuestion = evaluateCondition(
  question.condition_logic,
  currentAnswers
);
```

### 2. **动态问题生成**
```typescript
function getNextQuestions(
  currentAnswers: QuizAnswer[],
  allQuestions: QuizQuestion[]
): QuizQuestion[] {
  return allQuestions.filter(question => {
    if (!question.condition_logic) return true;
    return evaluateCondition(question.condition_logic, currentAnswers);
  });
}
```

### 3. **情绪路径构建**
```typescript
function buildEmotionPath(answers: QuizAnswer[]): string[] {
  const path: string[] = [];
  
  // 按层级顺序构建路径
  const primaryAnswer = answers.find(a => a.metadata?.emotion_tier === 1);
  if (primaryAnswer) path.push(primaryAnswer.answer_value);
  
  const secondaryAnswer = answers.find(a => a.metadata?.emotion_tier === 2);
  if (secondaryAnswer) path.push(secondaryAnswer.answer_value);
  
  const tertiaryAnswer = answers.find(a => a.metadata?.emotion_tier === 3);
  if (tertiaryAnswer) path.push(tertiaryAnswer.answer_value);
  
  return path;
}
```

## 📈 业务价值

### 1. **用户体验优化**
- **认知负担降低**: 每次只需选择少量选项
- **选择精确度提高**: 通过层级引导获得精确结果
- **完成率提升**: 简化流程提高用户完成率

### 2. **数据质量提升**
- **路径完整性**: 记录完整的情绪选择路径
- **选择一致性**: 层级引导确保逻辑一致性
- **数据丰富度**: 每个层级都提供有价值的信息

### 3. **系统扩展性**
- **新情绪添加**: 可以轻松添加新的情绪分支
- **逻辑调整**: 可以修改分支逻辑而不影响数据结构
- **个性化增强**: 支持用户级别的深度个性化

### 4. **分析能力增强**
```typescript
// 情绪分布分析
const emotionDistribution = {
  primary: countByValue(answers, 'emotion_tier', 1),
  secondary: countByValue(answers, 'emotion_tier', 2),
  tertiary: countByValue(answers, 'emotion_tier', 3)
};

// 路径模式分析
const commonPaths = findCommonEmotionPaths(allSessions);

// 个性化推荐
const recommendations = generateRecommendations(
  userHistory,
  emotionPatterns
);
```

## 🚀 实施计划

### 阶段1: 核心架构 (1-2周)
- [ ] 实现条件逻辑引擎
- [ ] 创建分支式Quiz组件
- [ ] 完成基础数据结构

### 阶段2: 数据导入 (1周)
- [ ] 转换mood-track-quest数据
- [ ] 创建50个问题和对应选项
- [ ] 设置条件逻辑关系

### 阶段3: 用户界面 (1-2周)
- [ ] 设计情绪选择界面
- [ ] 实现动态问题显示
- [ ] 添加进度指示和路径显示

### 阶段4: 个性化配置 (1周)
- [ ] 集成问题启用/禁用功能
- [ ] 实现选项自定义排序
- [ ] 添加个性化推荐逻辑

### 阶段5: 测试和优化 (1周)
- [ ] 用户体验测试
- [ ] 性能优化
- [ ] 数据分析验证

## ✅ 成功指标

### 用户体验指标
- [ ] 问卷完成率 > 90%
- [ ] 平均完成时间 < 2分钟
- [ ] 用户满意度 > 4.5/5

### 数据质量指标
- [ ] 情绪路径完整率 > 95%
- [ ] 数据一致性检查通过
- [ ] 异常选择模式 < 5%

### 系统性能指标
- [ ] 问题加载时间 < 200ms
- [ ] 条件逻辑计算时间 < 50ms
- [ ] 数据库查询优化完成

这种分支式架构不仅完美解决了复杂层级结构的转换问题，还为系统带来了前所未有的灵活性和个性化能力。通过将82个情绪选项转换为50个独立问题，我们实现了用户体验的简化和系统架构的优化。
