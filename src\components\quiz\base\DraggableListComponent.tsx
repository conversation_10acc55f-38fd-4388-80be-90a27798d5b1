/**
 * Quiz拖拽列表组件
 * 支持多种中医文化样式的拖拽列表组件
 */

import React, { useState, useRef } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { DraggableListComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type DraggableListComponentConfig = z.infer<typeof DraggableListComponentConfigSchema>;

export interface DraggableListItem {
  id: string;
  content: Record<string, string>;
  icon?: string;
  value: string | number;
  disabled?: boolean;
}

export interface DraggableListComponentProps extends BaseQuizComponentProps<DraggableListComponentConfig> {
  items: DraggableListItem[];
  onOrderChange: (newOrder: DraggableListItem[]) => void;
  disabled?: boolean;
}

interface DraggableListComponentState extends ComponentState {
  ordered_items: DraggableListItem[];
  dragging_item: string | null;
  drag_over_item: string | null;
  drag_start_index: number | null;
  drag_offset: { x: number; y: number };
}

/**
 * 拖拽列表组件类
 */
export class DraggableListComponent extends BaseQuizComponent<
  DraggableListComponentConfig,
  DraggableListComponentProps,
  DraggableListComponentState
> {
  private containerRef = React.createRef<HTMLDivElement>();
  private dragItemRef = React.createRef<HTMLDivElement>();

  extractConfig(props: DraggableListComponentProps): DraggableListComponentConfig {
    return props.config;
  }

  getInitialState(): DraggableListComponentState {
    return {
      is_loading: false,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      ordered_items: [...this.props.items],
      dragging_item: null,
      drag_over_item: null,
      drag_start_index: null,
      drag_offset: { x: 0, y: 0 }
    };
  }

  componentDidUpdate(prevProps: DraggableListComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.items !== this.props.items) {
      this.setState({ ordered_items: [...this.props.items] });
    }
    
    if (prevProps.disabled !== this.props.disabled) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled
      });
    }
  }

  /**
   * 处理拖拽开始
   */
  private handleDragStart = (event: React.DragEvent<HTMLDivElement>, item: DraggableListItem, index: number): void => {
    if (this.state.is_disabled || item.disabled) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const offsetX = event.clientX - rect.left;
    const offsetY = event.clientY - rect.top;

    this.setState({
      dragging_item: item.id,
      drag_start_index: index,
      drag_offset: { x: offsetX, y: offsetY }
    });

    // 设置拖拽数据
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', item.id);

    // 触发触觉反馈
    this.triggerHapticFeedback('medium');

    // 发送交互事件
    this.emitInteractionEvent('focus', {
      action: 'drag_start',
      item_id: item.id,
      start_index: index
    });
  };

  /**
   * 处理拖拽结束
   */
  private handleDragEnd = (event: React.DragEvent<HTMLDivElement>): void => {
    this.setState({
      dragging_item: null,
      drag_over_item: null,
      drag_start_index: null,
      drag_offset: { x: 0, y: 0 }
    });

    // 发送交互事件
    this.emitInteractionEvent('focus', {
      action: 'drag_end'
    });
  };

  /**
   * 处理拖拽悬停
   */
  private handleDragOver = (event: React.DragEvent<HTMLDivElement>, targetItem: DraggableListItem): void => {
    event.preventDefault();
    
    if (this.state.is_disabled || targetItem.disabled) return;
    if (this.state.dragging_item === targetItem.id) return;

    this.setState({ drag_over_item: targetItem.id });
  };

  /**
   * 处理拖拽离开
   */
  private handleDragLeave = (event: React.DragEvent<HTMLDivElement>): void => {
    // 只有当离开整个容器时才清除悬停状态
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      this.setState({ drag_over_item: null });
    }
  };

  /**
   * 处理拖拽放置
   */
  private handleDrop = (event: React.DragEvent<HTMLDivElement>, targetItem: DraggableListItem, targetIndex: number): void => {
    event.preventDefault();
    
    if (this.state.is_disabled || targetItem.disabled) return;
    
    const draggedItemId = event.dataTransfer.getData('text/plain');
    const draggedItem = this.state.ordered_items.find(item => item.id === draggedItemId);
    const dragStartIndex = this.state.drag_start_index;
    
    if (!draggedItem || dragStartIndex === null || dragStartIndex === targetIndex) {
      this.setState({
        dragging_item: null,
        drag_over_item: null,
        drag_start_index: null
      });
      return;
    }

    // 重新排序项目
    const newItems = [...this.state.ordered_items];
    newItems.splice(dragStartIndex, 1);
    newItems.splice(targetIndex, 0, draggedItem);

    this.setState({
      ordered_items: newItems,
      dragging_item: null,
      drag_over_item: null,
      drag_start_index: null
    });

    // 通知父组件
    this.props.onOrderChange(newItems);

    // 触发触觉反馈
    this.triggerHapticFeedback('light');

    // 发送交互事件
    this.emitInteractionEvent('click', {
      action: 'item_reordered',
      item_id: draggedItem.id,
      from_index: dragStartIndex,
      to_index: targetIndex,
      new_order: newItems.map(item => item.id)
    });
  };

  /**
   * 处理键盘导航
   */
  private handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>, item: DraggableListItem, index: number): void => {
    if (this.state.is_disabled || item.disabled) return;

    const { key } = event;
    let newIndex = index;

    switch (key) {
      case 'ArrowUp':
        event.preventDefault();
        newIndex = Math.max(0, index - 1);
        break;
      case 'ArrowDown':
        event.preventDefault();
        newIndex = Math.min(this.state.ordered_items.length - 1, index + 1);
        break;
      case ' ':
      case 'Enter':
        event.preventDefault();
        // 空格键或回车键可以用于选择/取消选择项目
        this.handleItemClick(item);
        return;
      default:
        return;
    }

    if (newIndex !== index) {
      // 移动项目
      const newItems = [...this.state.ordered_items];
      const [movedItem] = newItems.splice(index, 1);
      newItems.splice(newIndex, 0, movedItem);

      this.setState({ ordered_items: newItems });
      this.props.onOrderChange(newItems);

      // 聚焦到新位置
      setTimeout(() => {
        const newElement = this.containerRef.current?.children[newIndex] as HTMLElement;
        newElement?.focus();
      }, 0);

      // 触发触觉反馈
      this.triggerHapticFeedback('light');
    }
  };

  /**
   * 处理项目点击
   */
  private handleItemClick = (item: DraggableListItem): void => {
    if (this.state.is_disabled || item.disabled) return;

    // 发送交互事件
    this.emitInteractionEvent('click', {
      action: 'item_clicked',
      item_id: item.id,
      item_value: item.value
    });
  };

  /**
   * 获取项目文本
   */
  private getItemText(item: DraggableListItem): string {
    const { language } = this.context || { language: 'zh' };
    return item.content[language] || item.content['zh'] || item.content['en'] || '';
  }

  /**
   * 获取列表样式类名
   */
  private getListStyleClassName(): string {
    const style = this.config.style.list_style;
    return `quiz-draggable-list-${style}`;
  }

  /**
   * 渲染拖拽手柄
   */
  private renderDragHandle(): React.ReactNode {
    if (!this.config.style.show_drag_handle) return null;

    return (
      <div className="quiz-draggable-item-handle">
        <span className="quiz-draggable-handle-icon">⋮⋮</span>
      </div>
    );
  }

  /**
   * 渲染列表项
   */
  private renderListItem = (item: DraggableListItem, index: number): React.ReactNode => {
    const isDragging = this.state.dragging_item === item.id;
    const isDragOver = this.state.drag_over_item === item.id;
    const itemText = this.getItemText(item);

    return (
      <div
        key={item.id}
        className={`
          quiz-draggable-list-item
          ${isDragging ? 'quiz-draggable-item-dragging' : ''}
          ${isDragOver ? 'quiz-draggable-item-drag-over' : ''}
          ${item.disabled ? 'quiz-draggable-item-disabled' : ''}
          ${this.state.is_disabled ? 'quiz-draggable-item-list-disabled' : ''}
        `.trim()}
        draggable={!this.state.is_disabled && !item.disabled}
        onDragStart={(e) => this.handleDragStart(e, item, index)}
        onDragEnd={this.handleDragEnd}
        onDragOver={(e) => this.handleDragOver(e, item)}
        onDragLeave={this.handleDragLeave}
        onDrop={(e) => this.handleDrop(e, item, index)}
        onClick={() => this.handleItemClick(item)}
        onKeyDown={(e) => this.handleKeyDown(e, item, index)}
        tabIndex={this.state.is_disabled || item.disabled ? -1 : 0}
        role="button"
        aria-label={`${itemText} (position ${index + 1} of ${this.state.ordered_items.length})`}
        aria-grabbed={isDragging}
        aria-dropeffect={isDragOver ? 'move' : 'none'}
      >
        {/* 拖拽手柄 */}
        {this.renderDragHandle()}

        {/* 项目图标 */}
        {item.icon && (
          <div className="quiz-draggable-item-icon">
            {item.icon}
          </div>
        )}

        {/* 项目内容 */}
        <div className="quiz-draggable-item-content">
          {itemText}
        </div>

        {/* 排序指示器 */}
        {this.config.style.show_order_numbers && (
          <div className="quiz-draggable-item-order">
            {index + 1}
          </div>
        )}
      </div>
    );
  };

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-draggable-list-component',
      this.getListStyleClassName(),
      this.state.is_disabled && 'quiz-draggable-list-disabled',
      this.state.dragging_item && 'quiz-draggable-list-dragging',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        ref={this.containerRef}
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
        role="list"
        aria-label="Draggable list"
        aria-live="polite"
      >
        {/* 列表项 */}
        {this.state.ordered_items.map(this.renderListItem)}

        {/* 空状态 */}
        {this.state.ordered_items.length === 0 && (
          <div className="quiz-draggable-list-empty">
            <span className="quiz-draggable-list-empty-text">
              {this.context?.language === 'zh' ? '暂无项目' : 'No items'}
            </span>
          </div>
        )}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'list';
  }

  protected getAriaLabel(): string {
    return `Draggable list with ${this.state.ordered_items.length} items`;
  }
}

// 使用Context的函数式组件包装器
const DraggableListComponentWrapper: React.FC<DraggableListComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <DraggableListComponent.contextType.Provider value={{ language }}>
      <DraggableListComponent {...props} />
    </DraggableListComponent.contextType.Provider>
  );
};

// 设置Context类型
DraggableListComponent.contextType = React.createContext({ language: 'zh' });

export default DraggableListComponentWrapper;
