# 服务层架构文档

## 概述

本项目采用混合架构模式，结合离线优先的本地服务和基于tRPC的在线服务，为用户提供无缝的心情追踪体验。服务层设计确保了代码的可维护性、可测试性和类型安全。

## 架构概览

### 离线优先架构
- **本地数据库**: 使用SQLite作为主要数据存储
- **离线服务**: 完整的CRUD操作和业务逻辑
- **数据同步**: 智能的双向同步机制
- **网络容错**: 在网络不可用时保持完整功能

### 在线服务架构
- **tRPC通信**: 类型安全的端到端通信
- **服务端实现**: 基于Cloudflare Pages的无服务器架构
- **数据库支持**: 支持SQLite、Turso、D1多种数据库
- **认证系统**: 基于Better-Auth的现代认证方案

## 服务层结构

### 1. 离线服务 (Offline Services)

#### 核心实体服务 (Core Entity Services)
负责单个实体的CRUD操作，直接对应数据库表：

**Quiz系统核心服务** ✨ 新架构:
- `QuizPackService` - Quiz包服务 (替代废弃的EmotionDataSetService)
- `QuizQuestionService` - Quiz问题服务 (替代废弃的EmotionDataSetTierService)
- `QuizQuestionOptionService` - Quiz问题选项服务 (替代废弃的EmotionDataSetEmotionService)
- `QuizSessionService` - Quiz会话服务
- `QuizAnswerService` - Quiz答案服务

**传统数据服务**:
- `MoodEntryService` - 心情记录服务 (保留，用于向后兼容)
- `EmotionSelectionService` - 情绪选择服务 (保留，用于向后兼容)

**系统支持服务**:
- `TagService` - 标签服务
- `EmojiSetService` - 表情集服务 (保留，用于皮肤系统)
- `SkinService` - 皮肤服务
- `UILabelService` - UI标签服务
- `UILabelTranslationService` - UI标签翻译服务

**已废弃服务** ⚠️:
- `EmotionDataSetService` - 已废弃，使用QuizPackService替代
- `EmotionDataSetTierService` - 已废弃，使用QuizQuestionService替代
- `EmotionDataSetEmotionService` - 已废弃，使用QuizQuestionOptionService替代
- `EmojiItemService` - 已废弃，emoji映射现在通过展现配置管理
- `UserConfigService` - 已废弃，使用新配置系统替代

#### 配置系统服务 (Configuration System Services) ✨ 新架构
负责全局应用设置和Quiz系统配置的完全分离管理：

**全局应用配置** (对应 user_configs 表):
- `UserConfigService` - 用户全局配置服务 (主题、语言、通知、无障碍等)

**Quiz展现配置** (对应新的展现配置表):
- `UserPresentationConfigService` - 用户展现配置服务 (6层个性化配置)
- `PackPresentationOverrideService` - Quiz包展现覆盖服务 (包特定配置)
- `QuizSessionPresentationConfigService` - Quiz会话展现配置服务 (会话配置快照)

**配置管理服务**:
- `QuizConfigMergerService` - 配置合并服务 (复杂配置合并逻辑)
- `AppSettingsService` - 应用设置服务 (对应 app_settings 表)

#### 业务逻辑服务 (Business Logic Services)
处理复杂的业务逻辑，协调多个实体服务：

**Quiz系统业务逻辑** ✨ 新架构:
- `QuizEngineV3Service` - Quiz引擎服务 (新架构，支持所有Quiz类型)
- `QuizSessionManagementService` - Quiz会话管理服务
- `QuizResultAnalysisService` - Quiz结果分析服务

**传统业务逻辑** (保留兼容):
- `MoodTrackingService` - 心情追踪业务逻辑 (保留，用于向后兼容)

**分析和推荐服务**:
- `AnalyticsService` - 数据分析服务
- `RecommendationService` - 推荐服务
- `UserEngagementService` - 用户参与度分析服务

#### 数据访问层 (Data Access Layer)
提供统一的数据访问接口：

**基础数据访问**:
- `DatabaseService` - 数据库连接和基础操作
- `BaseRepository` - 基础仓储模式实现
- `BaseService` - 基础服务类，提供通用功能

**多语言支持**:
- `TranslatableRepository` - 多语言支持的仓储基类
- `TranslatableService` - 多语言支持的服务基类
- `UILabelTranslationRepository` - UI标签翻译数据访问层

**Quiz系统数据访问** ✨ 新架构:
- `QuizPackRepository` - Quiz包数据访问层
- `QuizQuestionRepository` - Quiz问题数据访问层
- `QuizQuestionOptionRepository` - Quiz问题选项数据访问层
- `QuizSessionRepository` - Quiz会话数据访问层
- `QuizAnswerRepository` - Quiz答案数据访问层

**配置系统数据访问**:
- `UserConfigRepository` - 用户配置数据访问层
- `UserPresentationConfigRepository` - 用户展现配置数据访问层
- `PackPresentationOverrideRepository` - 包展现覆盖数据访问层
- `AppSettingsRepository` - 应用设置数据访问层

### 2. 在线服务 (Online Services)

#### 基础在线服务
- `OnlineServices` - 在线服务管理器 (简化版)
- `ApiClientService` - API客户端服务
- `NetworkStatusService` - 网络状态监控

#### tRPC端点 (服务端实现)
基于 `server/lib/router.ts` 的tRPC路由：

**核心数据库操作**:
- `query` - 执行单个SQL查询
- `batch` - 执行批量SQL操作
- `executeScript` - 执行多语句SQL脚本
- `fetchTable` - 获取表数据
- `fetchTableWithLimit` - 分页获取表数据

**认证服务** (Better-Auth集成):
- `me` - 获取当前用户信息
- `login` - 用户登录 (兼容性端点)
- `register` - 用户注册 (兼容性端点)
- `verifyToken` - JWT令牌验证
- `updateVipStatus` - 更新VIP状态

**数据同步服务**:
- `synchronizeData` - 增强的双向数据同步
- `performFullSync` - 完整数据同步

**分析服务**:
- `getMoodAnalytics` - 心情分析统计
- `getEmotionUsageStats` - 情绪使用统计
- `getUserActivityStats` - 用户活动统计

**用户管理**:
- `getUserProfile` - 获取用户资料
- `updateUserPreferences` - 更新用户偏好
- `getVipStatus` - 获取VIP状态
- `unlockSkin` - 解锁皮肤
- `getUserUnlockedSkins` - 获取已解锁皮肤

**支付服务**:
- `getVipPlans` - 获取VIP计划
- `purchaseVip` - 购买VIP订阅
- `purchaseSkin` - 购买皮肤
- `getPurchaseHistory` - 获取购买历史

**配置系统服务** ✨ 新架构:
- `config.global.getUserConfig` - 获取用户全局配置 (user_configs表)
- `config.global.updateUserConfig` - 更新用户全局配置
- `config.presentation.getUserPresentationConfig` - 获取用户展现配置 (user_presentation_configs表)
- `config.presentation.updateUserPresentationConfig` - 更新用户展现配置
- `config.presentation.getPackOverrides` - 获取包展现覆盖配置 (pack_presentation_overrides表)
- `config.presentation.updatePackOverrides` - 更新包展现覆盖配置
- `config.session.generateSessionConfig` - 生成Quiz会话配置快照
- `config.app.getAppSettings` - 获取应用设置 (app_settings表)
- `config.app.updateAppSettings` - 更新应用设置

**数据库管理**:
- `initializeDatabase` - 初始化数据库
- `resetDatabase` - 重置数据库 (开发/测试)

## Hooks层 (数据访问层)

### 认证相关Hooks
- `useAuth` - 完整的认证管理，支持离线/在线混合模式
- `useAuthSimple` - 简化版认证hook，专注基本功能

### 数据管理Hooks
- `useDataSync` - 数据同步管理，处理离线/在线数据同步
- `useShop` - 商店功能，皮肤和表情集购买管理
- `useVip` - VIP功能管理，订阅和权限控制

### 业务数据Hooks
- `useHomeData` - 首页数据管理
- `useHistoryData` - 历史记录数据管理
- `useSettingsData` - 设置页面数据管理
- `useAnalyticsData` - 分析数据管理
- `useExportData` - 数据导出功能

### 配置系统Hooks ✨ 新架构
- `useGlobalConfig` - 全局应用配置管理 (对应user_configs表: 主题、语言、通知、无障碍等)
- `usePresentationConfig` - Quiz展现配置管理 (对应user_presentation_configs表: 6层个性化配置)
- `usePackOverrides` - Quiz包覆盖配置管理 (对应pack_presentation_overrides表)
- `useSessionConfig` - Quiz会话配置管理 (会话配置快照生成和管理)
- `useAppSettings` - 应用设置管理 (对应app_settings表)
- `useNetworkStatus` - 网络状态监控 (支持Web和移动端)

### 本地数据Hooks
**Quiz系统数据Hooks** ✨ 新架构:
- `useQuizPacksData` - Quiz包数据管理 (替代useLocalEmotionsData)
- `useQuizSessionsData` - Quiz会话数据管理
- `useQuizAnswersData` - Quiz答案数据管理

**传统数据Hooks** (保留兼容):
- `useLocalHistoryData` - 本地历史数据管理 (心情记录等)
- `useLocalTagsData` - 本地标签数据管理

**混合数据访问**:
- `useHybridData` - 混合数据访问 (离线优先，在线同步)
- `useEmojiSetsData` - 表情集数据管理 (用于皮肤系统)
- `useSkinsData` - 皮肤数据管理

## 页面层调用模式

### 1. Home页面 (`src/pages/Home.tsx`) ✨ 已更新为新架构
```typescript
import { Services } from '@/services';

// 使用新的Quiz架构服务
const quizPackService = await Services.quizPack();
const activePacksResult = await quizPackService.getActivePacks();

// 创建Quiz会话 (替代直接创建心情记录)
const quizSessionService = await Services.quizSession();
const sessionResult = await quizSessionService.createSession({
  pack_id: 'mood_track_branching_v1',
  user_id: 'user123',
  session_type: 'standard'
});

// 向后兼容：仍支持传统心情记录
const moodEntryService = await Services.moodEntry();
const moodEntryResult = await moodEntryService.create(moodEntryInput);
```

### 2. Settings页面 (`src/pages/Settings.tsx`) ✨ 已更新为新架构
```typescript
import { useGlobalConfig } from '@/hooks/useGlobalConfig';
import { usePresentationConfig } from '@/hooks/usePresentationConfig';
import { useAppSettings } from '@/hooks/useAppSettings';
import { useDataSync } from '@/hooks/useDataSync';

// 使用全局配置Hook (对应user_configs表)
const {
  themeMode,
  language,
  notificationsEnabled,
  soundEnabled,
  accessibilityConfig,
  updateConfig,
  isOnline,
  lastSyncTime
} = useGlobalConfig();

// 使用Quiz展现配置Hook (对应user_presentation_configs表)
const {
  presentationConfig,
  personalizationLevel,
  updatePresentationConfig
} = usePresentationConfig();

// 使用应用设置Hook (对应app_settings表)
const {
  appSettings,
  updateAppSetting
} = useAppSettings();

const { performSync, syncStatus } = useDataSync();
```

### 3. Shop页面 (`src/pages/Shop.tsx`)
```typescript
import { useShop } from '@/hooks/useShop';
import { useVip } from '@/hooks/useVip';

// 通过专门的Hooks管理商店功能
const { skins, purchaseSkin, activateSkin } = useShop();
const { isVip, canAccessPremiumSkins } = useVip();
```

### 4. QuizSettings页面 (`src/pages/QuizSettings.tsx`) ✨ 新架构
```typescript
import { usePresentationConfig } from '@/hooks/usePresentationConfig';
import { usePackOverrides } from '@/hooks/usePackOverrides';
import { useSessionConfig } from '@/hooks/useSessionConfig';

// 使用Quiz展现配置Hook (对应user_presentation_configs表)
const {
  presentationConfig,
  personalizationLevel,
  updatePresentationConfig,
  configVersion
} = usePresentationConfig();

// 使用包覆盖配置Hook (对应pack_presentation_overrides表)
const {
  packOverrides,
  updatePackOverride,
  getPackOverride
} = usePackOverrides();

// 使用会话配置Hook (对应quiz_session_presentation_configs表)
const {
  generateSessionConfig,
  getSessionConfig
} = useSessionConfig();

// 生成会话配置快照
const startQuizSession = async (packId: string) => {
  const sessionId = `session_${Date.now()}`;
  const sessionConfig = await generateSessionConfig(packId, sessionId);
  // 使用会话配置启动Quiz
};
```

### 5. History页面 (`src/pages/History.tsx`)
```typescript
import { useHistoryData } from '@/hooks/useHistoryData';

// 通过Hook获取历史数据
const { entries, isLoading, loadMore } = useHistoryData();
```

## 数据流架构

### 离线优先模式
```
页面组件 → Hooks → 离线服务 → 本地SQLite数据库
    ↓
网络可用时 → 数据同步 → tRPC → 服务端 → 云端数据库
```

### 认证和支付流程
```
页面组件 → useAuth/useShop → tRPC直接调用 → 服务端 → 云端数据库
```

### 配置系统数据流 ✨ 新架构
```
全局应用设置 (user_configs表):
用户操作 → useGlobalConfig → UserConfigService → 本地SQLite (user_configs)
                                                    ↓
云端同步 ← tRPC config.global ← 网络可用时自动同步

Quiz展现配置系统:
用户展现配置 (user_presentation_configs) + 包覆盖 (pack_presentation_overrides) + 系统默认
                                    ↓
                        QuizConfigMergerService → 会话配置快照 (quiz_session_presentation_configs)
                                    ↓
                Quiz组件使用 ← usePresentationConfig + useSessionConfig Hooks

应用设置 (app_settings表):
系统管理 → useAppSettings → AppSettingsService → 本地SQLite (app_settings)
```

### 数据同步流程
```
本地数据变更 → 标记为待同步 → useDataSync → tRPC同步端点 → 服务端处理 → 云端数据库
云端数据变更 → 服务端推送 → tRPC同步端点 → useDataSync → 本地数据库更新
```

## 实现状态

### 1. 离线服务基础架构 ✅
- **ServiceTypes.ts**: 完整的服务层类型系统
- **BaseService.ts**: 通用服务基类，提供错误处理、验证、事件发射
- **BaseRepository.ts**: 数据访问层基类，提供通用CRUD操作
- **DatabaseService.ts**: 数据库连接管理和事务支持
- **TranslatableRepository/Service**: 多语言支持的基类

### 2. 离线实体服务 ✅ 已更新为新架构
**Quiz系统核心服务** ✨:
- **QuizPackService**: Quiz包CRUD和管理 (替代EmotionDataSetService)
- **QuizQuestionService**: Quiz问题CRUD和管理 (替代EmotionDataSetTierService)
- **QuizQuestionOptionService**: Quiz问题选项CRUD和管理 (替代EmotionDataSetEmotionService)
- **QuizSessionService**: Quiz会话管理和状态跟踪
- **QuizAnswerService**: Quiz答案记录和分析

**传统数据服务** (保留兼容):
- **MoodEntryService**: 心情记录CRUD和同步支持
- **EmotionSelectionService**: 情绪选择管理和统计分析

**系统支持服务**:
- **UserConfigService**: 用户全局配置和偏好设置 (对应user_configs表)
- **TagService**: 标签管理
- **EmojiSetService**: 表情集管理 (用于皮肤系统)
- **SkinService**: 皮肤管理
- **UILabelService**: UI标签管理
- **UILabelTranslationService**: UI标签翻译管理

**已废弃服务** ⚠️:
- **EmotionDataSetService**: 已废弃，使用QuizPackService替代
- **EmotionDataSetTierService**: 已废弃，使用QuizQuestionService替代
- **EmotionDataSetEmotionService**: 已废弃，使用QuizQuestionOptionService替代
- **EmojiItemService**: 已废弃，emoji映射现在通过展现配置管理

### 3. 离线业务逻辑服务 ✅
- **MoodTrackingService**: 复杂心情追踪业务逻辑

### 4. 配置系统服务 ✅ ✨ 新架构
**全局应用配置** (对应user_configs表):
- **UserConfigService**: 用户全局配置管理 (主题、语言、通知、无障碍)
- **UserConfigRepository**: 全局配置数据访问层

**Quiz展现配置** (对应新的展现配置表):
- **UserPresentationConfigService**: 用户展现配置管理 (6层个性化配置，对应user_presentation_configs表)
- **UserPresentationConfigRepository**: 用户展现配置数据访问层
- **PackPresentationOverrideService**: Quiz包展现覆盖配置管理 (对应pack_presentation_overrides表)
- **PackPresentationOverrideRepository**: 包展现覆盖配置数据访问层
- **QuizSessionPresentationConfigService**: Quiz会话展现配置管理 (对应quiz_session_presentation_configs表)
- **QuizSessionPresentationConfigRepository**: 会话展现配置数据访问层

**配置管理服务**:
- **QuizConfigMergerService**: 配置合并服务 (复杂配置合并逻辑)
- **AppSettingsService**: 应用设置管理 (对应app_settings表)
- **AppSettingsRepository**: 应用设置数据访问层

### 4. 在线服务架构 ✅ (基于tRPC)
- **OnlineServices**: 简化版在线服务管理器
- **ApiClientService**: 基础API客户端
- **NetworkStatusService**: 网络状态监控
- **tRPC端点**: 完整的服务端API (详见server/README.md)

### 5. Hooks层 ✅
- **useAuth**: 完整认证管理 (已更新为tRPC)
- **useAuthSimple**: 简化版认证hook
- **useDataSync**: 数据同步管理 (需要更新为tRPC)
- **useShop**: 商店功能管理 (需要更新为tRPC)
- **useVip**: VIP功能管理 (需要更新为tRPC)
- **其他业务Hooks**: 各种专门的数据管理hooks

### 6. 配置系统Hooks ✅ ✨ 新架构
- **useGlobalConfig**: 全局应用配置Hook (对应user_configs表，离线优先 + 在线同步)
- **usePresentationConfig**: Quiz展现配置Hook (对应user_presentation_configs表，6层个性化配置管理)
- **usePackOverrides**: Quiz包覆盖配置Hook (对应pack_presentation_overrides表)
- **useSessionConfig**: Quiz会话配置Hook (对应quiz_session_presentation_configs表)
- **useAppSettings**: 应用设置Hook (对应app_settings表)
- **useNetworkStatus**: 网络状态监控Hook (支持Web和移动端)

### 6. 服务工厂和统一导出 ✅
- **Services**: 离线服务访问器
- **onlineServices**: 在线服务访问器 (简化版)
- **index.ts**: 统一导出接口

## 使用方法

### 1. 离线服务使用 (主要数据操作) ✨ 已更新为新架构
```typescript
import { Services } from '@/services';

// 使用新的Quiz架构创建Quiz会话
const quizSessionService = await Services.quizSession();
const sessionResult = await quizSessionService.createSession({
  pack_id: 'mood_track_branching_v1',
  user_id: 'user123',
  session_type: 'standard'
});

// 获取Quiz包 (替代情绪数据集)
const quizPackService = await Services.quizPack();
const packsResult = await quizPackService.getActivePacks();

// 获取Quiz问题
const quizQuestionService = await Services.quizQuestion();
const questionsResult = await quizQuestionService.getByPackId('mood_track_branching_v1');

// 记录Quiz答案
const quizAnswerService = await Services.quizAnswer();
const answerResult = await quizAnswerService.recordAnswer({
  session_id: sessionResult.data.id,
  question_id: 'q001_primary_emotion',
  selected_option_ids: ['opt_happy'],
  answer_value: 'happy',
  scale_value: 80
});

// 获取用户全局配置 (对应user_configs表)
const userConfigService = await Services.userConfig();
const configResult = await userConfigService.getActiveConfig('user123');

// 获取用户展现配置 (对应user_presentation_configs表)
const presentationConfigService = await Services.userPresentationConfig();
const presentationResult = await presentationConfigService.getUserConfig('user123');

// 向后兼容：仍支持传统心情记录
const moodEntryService = await Services.moodEntry();
const entriesResult = await moodEntryService.getByUserId('user123', { limit: 20 });
```

### 2. 在线服务使用 (tRPC调用)
```typescript
import { trpc } from '@/lib/trpc';

// 用户认证
const loginResult = await trpc.login.mutate({
  email: '<EMAIL>',
  password: 'password123'
});

// 数据同步
const syncResult = await trpc.synchronizeData.mutate({
  moodEntriesToUpload: localEntries,
  emotionSelectionsToUpload: localSelections,
  lastSyncTimestamp: lastSync
});

// 获取分析数据
const analyticsResult = await trpc.getMoodAnalytics.query({
  userId: 'user123',
  timeRange: '30d'
});

// VIP功能
const vipPlans = await trpc.getVipPlans.query();
const purchaseResult = await trpc.purchaseVip.mutate({
  planId: 'monthly',
  paymentMethodId: 'pm_123'
});

// 直接数据库查询 (高级用法)
const customQuery = await trpc.query.query({
  sql: 'SELECT * FROM mood_entries WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?',
  args: ['user123', 20]
});
```

### 3. 配置系统使用 ✨ 新架构
```typescript
import { useGlobalConfig } from '@/hooks/useGlobalConfig';
import { usePresentationConfig } from '@/hooks/usePresentationConfig';
import { usePackOverrides } from '@/hooks/usePackOverrides';
import { useSessionConfig } from '@/hooks/useSessionConfig';
import { useAppSettings } from '@/hooks/useAppSettings';

// 全局应用配置 (对应user_configs表)
const {
  themeMode,
  language,
  notificationsEnabled,
  soundEnabled,
  accessibilityConfig,
  updateConfig,
  isOnline,
  lastSyncTime
} = useGlobalConfig();

// Quiz展现配置 (对应user_presentation_configs表)
const {
  presentationConfig,
  personalizationLevel,
  updatePresentationConfig,
  configVersion
} = usePresentationConfig();

// Quiz包覆盖配置 (对应pack_presentation_overrides表)
const {
  packOverrides,
  updatePackOverride,
  getPackOverride
} = usePackOverrides();

// Quiz会话配置 (对应quiz_session_presentation_configs表)
const {
  generateSessionConfig,
  getSessionConfig
} = useSessionConfig();

// 应用设置 (对应app_settings表)
const {
  appSettings,
  updateAppSetting
} = useAppSettings();

// 使用示例
const handleThemeChange = async (theme: 'light' | 'dark' | 'system') => {
  await updateConfig({ theme_mode: theme });
};

const startQuizSession = async (packId: string) => {
  const sessionId = `session_${Date.now()}`;
  const sessionConfig = await generateSessionConfig(packId, sessionId);
  // 使用会话配置启动Quiz
};

const updateEmojiMapping = async (optionValue: string, emoji: string) => {
  await updatePresentationConfig({
    layer4_view_detail: {
      emotion_presentation: {
        [optionValue]: { emoji, color: '#FFD700', animation: 'bounce' }
      }
    }
  });
};
```

### 4. Hooks使用 (推荐的页面层调用方式)
```typescript
import { useAuth } from '@/hooks/useAuth';
import { useDataSync } from '@/hooks/useDataSync';
import { useShop } from '@/hooks/useShop';

// 认证管理
const { isAuthenticated, user, login, logout } = useAuth();

// 数据同步
const { syncStatus, performSync, isOnline } = useDataSync();

// 商店功能
const { skins, purchaseSkin, activateSkin } = useShop();

// 使用示例
const handleLogin = async () => {
  const result = await login({ email, password });
  if (result.success) {
    await performSync(); // 登录后自动同步
  }
};
```

### 4. React组件中的实际使用模式

#### Home页面模式 (直接服务调用) ✨ 已更新为新架构
```typescript
import React, { useState, useEffect } from 'react';
import { Services } from '@/services';
import { QuizPack, QuizSession } from '@/types';

const Home: React.FC = () => {
  const [activePack, setActivePack] = useState<QuizPack | null>(null);
  const [currentSession, setCurrentSession] = useState<QuizSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        // 获取活动的Quiz包 (替代情绪数据集)
        const quizPackService = await Services.quizPack();
        const packsResult = await quizPackService.getActivePacks();

        if (packsResult.success && packsResult.data.length > 0) {
          setActivePack(packsResult.data[0]);
        }
      } catch (error) {
        console.error('Failed to load data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, []);

  const handleStartQuiz = async (packId: string) => {
    const quizSessionService = await Services.quizSession();
    const sessionResult = await quizSessionService.createSession({
      pack_id: packId,
      user_id: 'user123',
      session_type: 'standard'
    });

    if (sessionResult.success) {
      setCurrentSession(sessionResult.data);
    }
  };

  const handleAnswerQuestion = async (questionId: string, selectedOptions: string[]) => {
    if (!currentSession) return;

    const quizAnswerService = await Services.quizAnswer();
    const result = await quizAnswerService.recordAnswer({
      session_id: currentSession.id,
      question_id: questionId,
      selected_option_ids: selectedOptions,
      answer_value: selectedOptions[0] // 简化示例
    });
    // 处理结果...
  };

  // 渲染组件...
};
```

#### Settings页面模式 (配置系统Hooks) ✨ 已更新为新架构
```typescript
import React from 'react';
import { useGlobalConfig } from '@/hooks/useGlobalConfig';
import { usePresentationConfig } from '@/hooks/usePresentationConfig';
import { useAppSettings } from '@/hooks/useAppSettings';
import { useDataSync } from '@/hooks/useDataSync';

const Settings: React.FC = () => {
  // 全局配置 (对应user_configs表)
  const {
    themeMode,
    language,
    notificationsEnabled,
    soundEnabled,
    accessibilityConfig,
    updateConfig,
    isOnline,
    lastSyncTime
  } = useGlobalConfig();

  // Quiz展现配置 (对应user_presentation_configs表)
  const {
    presentationConfig,
    personalizationLevel,
    updatePresentationConfig
  } = usePresentationConfig();

  // 应用设置 (对应app_settings表)
  const {
    appSettings,
    updateAppSetting
  } = useAppSettings();

  const { syncStatus, performSync } = useDataSync();

  const handleThemeChange = async (theme: 'light' | 'dark' | 'system') => {
    await updateConfig({ theme_mode: theme });
    // 自动同步到云端 (Hook内部处理)
  };

  const handleAccessibilityChange = async (setting: string, value: boolean) => {
    const updatedAccessibility = { ...accessibilityConfig, [setting]: value };
    await updateConfig({
      accessibility: JSON.stringify(updatedAccessibility)
    });
  };

  const handlePersonalizationChange = async (level: number) => {
    await updatePresentationConfig({
      personalization_level: level
    });
  };

  // 渲染设置界面...
};
```

#### Shop页面模式 (专门Hooks)
```typescript
import React from 'react';
import { useShop } from '@/hooks/useShop';
import { useAuth } from '@/hooks/useAuth';

const Shop: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const { skins, purchaseSkin, isLoading } = useShop();

  const handlePurchase = async (skinId: string) => {
    if (!isAuthenticated) {
      // 跳转到登录页面
      return;
    }

    const result = await purchaseSkin(skinId, 'payment_method_id');
    // 处理购买结果...
  };

  // 渲染商店界面...
};
```

### 5. 服务初始化和生命周期
```typescript
// App.tsx 或 main.tsx
import { initializeServices, cleanupServices } from '@/services';

// 应用启动时初始化离线服务
await initializeServices();

// 应用关闭时清理资源
window.addEventListener('beforeunload', () => {
  cleanupServices();
});
```

## 文件组织

```
src/services/
├── index.ts                    # 统一导出
├── ServiceTypes.ts             # 类型定义
├── ServiceFactory.ts           # 离线服务工厂
├── base/
│   ├── BaseService.ts          # 基础服务类
│   ├── BaseRepository.ts       # 基础仓储类
│   ├── TranslatableRepository.ts # 多语言仓储基类
│   ├── TranslatableService.ts  # 多语言服务基类
│   └── DatabaseService.ts      # 数据库服务
├── entities/                   # 实体服务 (离线)
│   ├── # Quiz系统核心服务 ✨ 新架构
│   ├── QuizPackService.ts           # Quiz包服务 (替代EmotionDataSetService)
│   ├── QuizPackRepository.ts        # Quiz包数据访问层
│   ├── QuizQuestionService.ts       # Quiz问题服务 (替代EmotionDataSetTierService)
│   ├── QuizQuestionRepository.ts    # Quiz问题数据访问层
│   ├── QuizQuestionOptionService.ts # Quiz问题选项服务 (替代EmotionDataSetEmotionService)
│   ├── QuizQuestionOptionRepository.ts # Quiz问题选项数据访问层
│   ├── QuizSessionService.ts        # Quiz会话服务
│   ├── QuizSessionRepository.ts     # Quiz会话数据访问层
│   ├── QuizAnswerService.ts         # Quiz答案服务
│   ├── QuizAnswerRepository.ts      # Quiz答案数据访问层
│   ├── QuizEngineV3.ts              # 新Quiz引擎 (支持所有Quiz类型)
│   ├── # 传统数据服务 (保留兼容)
│   ├── MoodEntryService.ts          # 心情记录服务
│   ├── EmotionSelectionService.ts   # 情绪选择服务
│   ├── # 系统支持服务
│   ├── TagService.ts
│   ├── EmojiSetService.ts           # 表情集服务 (用于皮肤系统)
│   ├── SkinService.ts
│   ├── UILabelService.ts
│   ├── UILabelTranslationService.ts # UI标签翻译服务
│   ├── UILabelTranslationRepository.ts # UI标签翻译数据访问层
│   ├── # 配置系统服务 ✨ 新架构
│   ├── UserConfigService.ts         # 用户全局配置服务 (对应user_configs表)
│   ├── UserConfigRepository.ts      # 用户全局配置数据访问层
│   ├── UserPresentationConfigService.ts # 用户展现配置服务 (对应user_presentation_configs表)
│   ├── UserPresentationConfigRepository.ts # 用户展现配置数据访问层
│   ├── PackPresentationOverrideService.ts # 包展现覆盖服务 (对应pack_presentation_overrides表)
│   ├── PackPresentationOverrideRepository.ts # 包展现覆盖数据访问层
│   ├── QuizSessionPresentationConfigService.ts # 会话展现配置服务 (对应quiz_session_presentation_configs表)
│   ├── QuizSessionPresentationConfigRepository.ts # 会话展现配置数据访问层
│   ├── AppSettingsService.ts        # 应用设置服务 (对应app_settings表)
│   ├── AppSettingsRepository.ts     # 应用设置数据访问层
│   └── QuizConfigMergerService.ts   # 配置合并服务
│   ├── # 已废弃服务 ⚠️
│   ├── # EmotionDataSetService.ts      # @deprecated 使用QuizPackService替代
│   ├── # EmotionDataSetTierService.ts  # @deprecated 使用QuizQuestionService替代
│   ├── # EmotionDataSetEmotionService.ts # @deprecated 使用QuizQuestionOptionService替代
│   └── # EmojiItemService.ts           # @deprecated emoji映射现在通过展现配置管理
├── business/                   # 业务逻辑服务 (离线)
│   ├── QuizEngineV3Service.ts       # Quiz引擎业务逻辑
│   ├── QuizSessionManagementService.ts # Quiz会话管理
│   ├── QuizResultAnalysisService.ts # Quiz结果分析
│   ├── MoodTrackingService.ts       # 心情追踪 (保留兼容)
│   ├── AnalyticsService.ts          # 数据分析服务
│   └── RecommendationService.ts     # 推荐服务
└── online/                     # 在线服务 (简化版)
    ├── index.ts
    ├── OnlineServices.ts       # 简化版在线服务管理器
    ├── NetworkStatusService.ts # 网络状态监控
    ├── ApiClientService.ts     # 基础API客户端
    └── types/
        └── OnlineServiceTypes.ts # 简化版类型定义

src/hooks/                      # 数据访问Hooks
├── useAuth.ts                  # 认证管理 (tRPC)
├── useAuthSimple.ts            # 简化版认证
├── useDataSync.ts              # 数据同步管理
├── useShop.ts                  # 商店功能
├── useVip.ts                   # VIP功能
├── # 配置系统Hooks ✨ 新架构
├── useGlobalConfig.ts          # 全局应用配置Hook (对应user_configs表)
├── usePresentationConfig.ts    # Quiz展现配置Hook (对应user_presentation_configs表)
├── usePackOverrides.ts         # Quiz包覆盖配置Hook (对应pack_presentation_overrides表)
├── useSessionConfig.ts         # Quiz会话配置Hook (对应quiz_session_presentation_configs表)
├── useAppSettings.ts           # 应用设置Hook (对应app_settings表)
├── useNetworkStatus.ts         # 网络状态监控Hook
├── # Quiz系统数据Hooks ✨ 新架构
├── useQuizPacksData.ts         # Quiz包数据Hook
├── useQuizSessionsData.ts      # Quiz会话数据Hook
├── useQuizAnswersData.ts       # Quiz答案数据Hook
└── ...                         # 其他业务Hooks

src/lib/
└── trpc.ts                     # tRPC客户端配置

server/lib/
├── router.ts                   # tRPC路由定义 (服务端API)
├── auth.ts                     # Better-Auth配置
├── database.ts                 # 数据库连接管理
└── ...                         # 其他服务端逻辑
```

## 在线服务架构重构计划

### 🎯 核心决策: 采用混合架构模式

基于架构分析，我们采用以下混合模式：
- **简单操作**: 直接使用tRPC (认证、同步、查询)
- **复杂业务**: 通过OnlineServices (支付、分析)
- **数据操作**: 优先使用离线Services

### 📋 改造计划

#### 阶段1: 修复现有问题 (优先级: 高)

**1.1 修复useShop支付功能** 🔧
- **问题**: 调用不存在的 `onlineServices.payment()`
- **解决**: 创建PaymentService并注册到OnlineServices
- **文件**:
  - 新建 `src/services/online/services/PaymentService.ts`
  - 更新 `src/services/online/OnlineServices.ts`
  - 更新 `src/hooks/useShop.ts`

**1.2 重构useDataSync** 🔧
- **问题**: 使用模拟逻辑，未调用实际tRPC端点
- **解决**: 直接调用 `trpc.synchronizeData.mutate()`
- **文件**: `src/hooks/useDataSync.ts`

**1.3 简化OnlineServices架构** 🔧
- **问题**: 复杂的Factory模式，功能重复
- **解决**: 只保留基础服务和复杂业务服务
- **文件**:
  - 更新 `src/services/online/OnlineServices.ts`
  - 简化 `src/services/online/index.ts`

#### 阶段2: 完善业务服务 (优先级: 中)

**2.1 创建复杂业务服务**
- **PaymentService**: 处理复杂支付流程
- **AnalyticsService**: 处理复杂数据分析
- **文件**:
  - `src/services/online/services/PaymentService.ts`
  - `src/services/online/services/AnalyticsService.ts`

**2.2 更新相关Hooks**
- **useVip**: 根据复杂度选择直接tRPC或PaymentService
- **useAnalytics**: 使用AnalyticsService处理复杂分析
- **文件**: `src/hooks/useVip.ts`, `src/hooks/useAnalytics.ts`

#### 阶段3: 测试和优化 (优先级: 中)

**3.1 编写测试**
- 单元测试: PaymentService, AnalyticsService
- 集成测试: useShop, useDataSync, useVip
- 端到端测试: 支付流程, 数据同步流程

**3.2 性能优化**
- 智能缓存策略
- 网络状态感知的重试机制
- 错误处理优化

#### 阶段4: 扩展功能 (优先级: 低)

**4.1 实现新的业务Hooks**
- **useHomeData**: 首页数据管理
- **useHistoryData**: 历史记录数据管理
- **useSettingsData**: 设置页面数据管理
- **useExportData**: 数据导出功能

**4.2 服务端完善**
- 完善Better-Auth集成
- 实现完整的数据同步逻辑
- 添加支付处理逻辑
- 实现数据分析端点

### 🧪 测试策略

#### 单元测试
```typescript
// PaymentService.test.ts
describe('PaymentService', () => {
  it('should handle VIP purchase flow', async () => {
    const paymentService = new PaymentService(mockTrpc);
    const result = await paymentService.purchaseVip('monthly', 'pm_123');
    expect(result.success).toBe(true);
  });
});

// useShop.test.ts
describe('useShop', () => {
  it('should purchase skin successfully', async () => {
    const { result } = renderHook(() => useShop());
    const purchaseResult = await result.current.purchaseSkin('skin_1', 'pm_123');
    expect(purchaseResult.success).toBe(true);
  });
});
```

#### 集成测试
```typescript
// DataSync.integration.test.ts
describe('Data Sync Integration', () => {
  it('should sync local data to server', async () => {
    // 1. 创建本地数据
    // 2. 调用同步
    // 3. 验证服务端数据
    // 4. 验证本地同步状态
  });
});
```

#### 端到端测试
```typescript
// Payment.e2e.test.ts
describe('Payment Flow E2E', () => {
  it('should complete VIP purchase', async () => {
    // 1. 登录用户
    // 2. 选择VIP计划
    // 3. 输入支付信息
    // 4. 完成支付
    // 5. 验证VIP状态
  });
});
```

## Quiz系统架构迁移

### 🔄 新旧架构对比

| 方面 | 旧架构 (emotion_data_set tier) | 新架构 (quiz_packs) |
|------|--------------------------------|---------------------|
| 概念模型 | emotion_data_set → tier → emotion | quiz_pack → question → option |
| 数据管理 | 层级式tier管理 | 灵活的问题管理 |
| 问题类型 | 仅支持情绪选择 | 支持多种Quiz类型 (emotion_wheel, personality_test, iq_test, knowledge_quiz, survey, game_quiz, mixed) |
| 展现配置 | 混合在数据中 | 完全分离 (user_presentation_configs, pack_presentation_overrides, quiz_session_presentation_configs) |
| 扩展性 | 受限于emotion概念 | 支持任意Quiz类型 (mainstream, alternative, experimental, cultural_specific) |
| 引擎实现 | QuizEngine.ts (废弃) | QuizEngineV3.ts (推荐) |
| 数据库表 | emotion_data_sets, emotion_data_set_tiers, emotion_data_set_emotions (已废弃) | quiz_packs, quiz_questions, quiz_question_options |
| 配置管理 | user_configs (混合配置) | 分离配置: user_configs (全局) + user_presentation_configs (Quiz展现) |
| Emoji映射 | emoji_items表 (已废弃) | 展现配置中的emoji_mapping (JSON) |

### 📋 迁移状态

**新架构服务** ✅:
- **QuizEngineV3.ts**: 新引擎已实现，位于 `src/services/entities/`
- **QuizPackService.ts**: Quiz包服务，对应 `quiz_packs` 表
- **QuizQuestionService.ts**: Quiz问题服务，对应 `quiz_questions` 表
- **QuizQuestionOptionService.ts**: Quiz问题选项服务，对应 `quiz_question_options` 表
- **QuizSessionService.ts**: Quiz会话服务，对应 `quiz_sessions` 表
- **QuizAnswerService.ts**: Quiz答案服务，对应 `quiz_answers` 表

**配置系统服务** ✅:
- **UserConfigService.ts**: 用户全局配置，对应 `user_configs` 表
- **UserPresentationConfigService.ts**: 用户展现配置，对应 `user_presentation_configs` 表
- **PackPresentationOverrideService.ts**: 包展现覆盖，对应 `pack_presentation_overrides` 表
- **QuizSessionPresentationConfigService.ts**: 会话展现配置，对应 `quiz_session_presentation_configs` 表
- **AppSettingsService.ts**: 应用设置，对应 `app_settings` 表

**已废弃服务** ⚠️:
- **QuizEngine.ts**: 标记为废弃，基于旧的emotion_data_set tier架构
- **EmotionDataSetService.ts**: 标记为废弃，使用QuizPackService替代
- **EmotionDataSetTierService.ts**: 标记为废弃，使用QuizQuestionService替代
- **EmotionDataSetEmotionService.ts**: 标记为废弃，使用QuizQuestionOptionService替代
- **EmojiItemService.ts**: 标记为废弃，emoji映射现在通过展现配置管理

**数据库表迁移状态**:
- ❌ **emotions, emotion_translations, emoji_items**: 已从schema中移除
- ❌ **emotion_data_sets, emotion_data_set_tiers, emotion_data_set_emotions**: 已从schema中移除
- ✅ **quiz_packs, quiz_questions, quiz_question_options**: 新架构表已创建
- ✅ **user_presentation_configs, pack_presentation_overrides, quiz_session_presentation_configs**: 展现配置表已创建

### 🎯 推荐使用

```typescript
// ❌ 旧方式 (废弃)
import { QuizEngine } from '../quiz/QuizEngine';
import { EmotionDataSetService } from './EmotionDataSetService';
import { EmotionDataSetTierService } from './EmotionDataSetTierService';
import { EmojiItemService } from './EmojiItemService';

// ✅ 新方式 (推荐)
import { QuizEngineV3 } from './QuizEngineV3';
import { QuizPackService } from './QuizPackService';
import { QuizQuestionService } from './QuizQuestionService';
import { QuizQuestionOptionService } from './QuizQuestionOptionService';
import { QuizSessionService } from './QuizSessionService';
import { QuizAnswerService } from './QuizAnswerService';

// ✅ 配置系统 (新架构)
import { UserConfigService } from './UserConfigService'; // 全局配置
import { UserPresentationConfigService } from './UserPresentationConfigService'; // Quiz展现配置
import { PackPresentationOverrideService } from './PackPresentationOverrideService'; // 包覆盖配置
import { AppSettingsService } from './AppSettingsService'; // 应用设置

// ✅ Hooks使用 (新架构)
import { useGlobalConfig } from '@/hooks/useGlobalConfig'; // 对应user_configs表
import { usePresentationConfig } from '@/hooks/usePresentationConfig'; // 对应user_presentation_configs表
import { usePackOverrides } from '@/hooks/usePackOverrides'; // 对应pack_presentation_overrides表
import { useSessionConfig } from '@/hooks/useSessionConfig'; // 对应quiz_session_presentation_configs表
import { useAppSettings } from '@/hooks/useAppSettings'; // 对应app_settings表
```

## 类型系统架构要求 ✨ 重要

### 🎯 核心原则：审计字段自动管理

为避免类型不匹配错误，严格遵循以下原则：

#### 1. 审计字段分离原则
```typescript
// ✅ 正确：完整实体类型包含所有字段
export const UserPresentationConfigSchema = z.object({
  id: IdSchema,
  user_id: IdSchema,
  // ... 业务字段
  created_at: TimestampSchema,  // 审计字段
  updated_at: TimestampSchema,  // 审计字段
});

// ✅ 正确：创建输入类型不包含审计字段
export const CreateUserPresentationConfigInputSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  // ... 业务字段
  // ❌ 不包含 created_at, updated_at
});

// ✅ 正确：更新输入类型不包含审计字段
export const UpdateUserPresentationConfigInputSchema = z.object({
  // ... 可选的业务字段
  // ❌ 不包含 created_at, updated_at
}).partial();
```

#### 2. Repository层责任分工
```typescript
// ✅ Repository层自动管理审计字段
protected buildInsertQuery(data: CreateInput): { query: string; values: any[] } {
  const now = new Date().toISOString();
  // 自动添加 created_at, updated_at
  const values = [...businessFields, now, now];
  return { query, values };
}

protected buildUpdateQuery(id: string, data: UpdateInput): { query: string; values: any[] } {
  // 自动添加 updated_at
  fields.push('updated_at = ?');
  values.push(new Date().toISOString());
  return { query, values };
}
```

#### 3. 服务层调用规范
```typescript
// ✅ 正确：不传递审计字段
await this.userPrefsRepo.update(userPrefs.id, {
  presentation_config: JSON.stringify(config)
  // ❌ 不传递 updated_at
});

// ✅ 正确：不传递审计字段
await this.packOverridesRepo.create({
  id: generateId(),
  user_id: userId,
  pack_id: packId,
  presentation_overrides: JSON.stringify(config)
  // ❌ 不传递 created_at, updated_at
});
```

#### 4. 数据库操作方法选择
```typescript
// ✅ 正确：修改操作使用 db.run()
const result = await db.run(deleteQuery, [id]);
return result.changes?.changes || 0;

// ✅ 正确：查询操作使用 db.query()
const result = await db.query(selectQuery, [userId]);
return result.values || [];
```

### 🔍 常见错误模式及修复

#### 错误1：在更新/创建时传递审计字段
```typescript
// ❌ 错误
await repository.update(id, {
  business_field: value,
  updated_at: new Date().toISOString()  // 不应该传递
});

// ✅ 修复
await repository.update(id, {
  business_field: value  // Repository会自动添加updated_at
});
```

#### 错误2：使用错误的数据库方法
```typescript
// ❌ 错误：DELETE操作使用query()
const result = await db.query('DELETE FROM table WHERE id = ?', [id]);
return result.changes || 0;  // changes属性不存在

// ✅ 修复：DELETE操作使用run()
const result = await db.run('DELETE FROM table WHERE id = ?', [id]);
return result.changes?.changes || 0;  // 正确的属性访问
```

#### 错误3：类型定义与数据库表不一致
```typescript
// ❌ 错误：输入类型包含审计字段
export const UpdateConfigInputSchema = z.object({
  config_field: z.string().optional(),
  updated_at: TimestampSchema  // 不应该包含
});

// ✅ 修复：输入类型只包含业务字段
export const UpdateConfigInputSchema = z.object({
  config_field: z.string().optional()
});
```

### 📋 类型系统检查清单

在创建新的Repository和Service时，确保：

- [ ] 完整实体Schema包含所有数据库字段（包括审计字段）
- [ ] 创建输入Schema不包含审计字段（id除外）
- [ ] 更新输入Schema不包含审计字段和id
- [ ] Repository的buildInsertQuery自动添加created_at和updated_at
- [ ] Repository的buildUpdateQuery自动添加updated_at
- [ ] 修改操作使用db.run()，查询操作使用db.query()
- [ ] 服务层调用Repository时不传递审计字段
- [ ] 类型导出在src/types/schema/index.ts中统一管理

### 🛠️ 开发工具支持

使用TypeScript严格模式和ESLint规则来预防类型错误：

```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### 🗄️ 数据库操作最佳实践

#### 1. 方法选择规范
```typescript
// ✅ 查询操作：使用 db.query()
const result = await db.query('SELECT * FROM table WHERE id = ?', [id]);
// 返回: { values: any[], columns: string[] }

// ✅ 修改操作：使用 db.run()
const result = await db.run('INSERT INTO table (...) VALUES (...)', values);
const result = await db.run('UPDATE table SET ... WHERE id = ?', values);
const result = await db.run('DELETE FROM table WHERE id = ?', [id]);
// 返回: { changes: { changes: number } }
```

#### 2. 结果处理规范
```typescript
// ✅ 查询结果处理
const queryResult = await db.query(sql, params);
const rows = queryResult.values || [];
const firstRow = queryResult.values?.[0];

// ✅ 修改结果处理
const runResult = await db.run(sql, params);
const affectedRows = runResult.changes?.changes || 0;
const wasSuccessful = affectedRows > 0;
```

#### 3. 错误处理模式
```typescript
// ✅ 标准错误处理
async deleteRecord(id: string): Promise<boolean> {
  try {
    const db = this.getDb();
    const result = await db.run('DELETE FROM table WHERE id = ?', [id]);
    return (result.changes?.changes || 0) > 0;
  } catch (error) {
    console.error('Error deleting record:', error);
    return false;
  }
}
```

## 注意事项

1. **新架构优先**: 优先使用新的Quiz架构 (quiz_packs, quiz_questions, quiz_question_options)，旧架构仅保留向后兼容
2. **数据与展现分离**: 严格遵循数据与展现分离原则，所有展现配置通过配置系统管理
3. **表对应关系**: 确保服务层与数据库表的一一对应关系
   - `QuizPackService` ↔ `quiz_packs` 表
   - `UserConfigService` ↔ `user_configs` 表
   - `UserPresentationConfigService` ↔ `user_presentation_configs` 表
   - `AppSettingsService` ↔ `app_settings` 表
4. **类型系统一致性**: 严格遵循上述类型系统架构要求，确保审计字段由Repository层自动管理
5. **离线优先**: 主要数据操作通过离线服务，确保应用在无网络时正常工作
6. **tRPC通信**: 认证、同步、支付等功能直接使用tRPC调用服务端
7. **错误处理**: 所有服务方法都返回 `ServiceResult<T>` 类型
8. **事务支持**: 复杂操作使用数据库事务确保数据一致性
9. **类型安全**: 全面的TypeScript类型定义，tRPC提供端到端类型安全，类型定义与数据库schema保持一致
10. **可测试性**: 服务层与数据层分离，便于单元测试
11. **同步机制**: 本地优先，网络可用时智能同步到云端
12. **网络容错**: 在网络不可用时保持完整的离线功能
13. **配置系统**: 使用分离的配置系统，全局配置与Quiz展现配置完全独立管理
14. **废弃服务**: 避免使用已标记为废弃的服务，及时迁移到新架构

## 总结

本项目的服务层架构采用了现代化的混合模式，完全对齐当前的数据库schema和类型定义：

### 🏗️ 核心架构特点
- **新Quiz架构**: 基于 `quiz_packs` → `quiz_questions` → `quiz_question_options` 的灵活数据模型
- **数据与展现分离**: 严格分离数据存储与展现配置，支持高度个性化
- **分层配置系统**: 全局配置 (`user_configs`) + Quiz展现配置 (`user_presentation_configs`) + 包覆盖 (`pack_presentation_overrides`) + 会话快照 (`quiz_session_presentation_configs`)
- **类型安全**: TypeScript类型定义与数据库schema完全一致，确保端到端类型安全
- **审计字段自动管理**: Repository层自动处理 `created_at` 和 `updated_at` 字段，避免类型不匹配错误

### 🔄 服务层对应关系
- **离线服务**提供完整的本地数据管理和业务逻辑，每个服务对应特定数据库表
- **tRPC在线服务**提供类型安全的云端通信，支持数据同步和认证
- **Hooks层**为页面组件提供便捷的数据访问接口，封装复杂的配置合并逻辑
- **智能同步**确保离线和在线数据的一致性

### 🎯 架构优势
- **支持多种Quiz类型**: emotion_wheel, personality_test, iq_test, knowledge_quiz, survey, game_quiz等
- **高度可扩展**: 支持mainstream, alternative, experimental, cultural_specific等不同风格
- **完整的多语言支持**: UI标签翻译系统 (`ui_labels`, `ui_label_translations`)
- **强大的个性化**: 6层展现配置系统，支持用户、包、问题级别的个性化
- **向后兼容**: 保留传统心情追踪功能，平滑迁移到新架构
- **类型系统健壮性**: 严格的类型定义和审计字段自动管理，预防常见的类型错误
- **数据库操作规范**: 明确的数据库方法使用规范，确保操作的正确性和一致性

这种架构既保证了应用的离线可用性，又提供了现代化的在线体验，完美支持各种类型的Quiz和测评需求，是PWA应用的理想选择。
