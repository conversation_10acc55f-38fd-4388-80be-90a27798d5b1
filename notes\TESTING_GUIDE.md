# MoodWheel 测试指南

## 测试类型
### 1. 单元测试
- **工具**：Vitest / Jest
- **目标**：测试核心功能模块（如情绪轮盘逻辑、数据处理函数等）。
- **示例**：
  ```javascript
  // 测试情绪轮盘数据解析
  test('parseEmotionData should return correct structure', () => {
    const data = parseEmotionData(mockData);
    expect(data.emotions).toHaveLength(8);
    expect(data.emotions[0].name).toBe('Happy');
  });
  ```

### 2. 集成测试
- **工具**：Vitest / Jest
- **目标**：测试模块间交互（如情绪日记提交、历史数据获取等）。
- **示例**：
  ```javascript
  // 测试情绪日记提交流程
  test('submitMoodDiary should save data correctly', async () => {
    const result = await submitMoodDiary(mockDiaryData);
    expect(result.status).toBe('success');
    expect(result.data.id).toBeDefined();
  });
  ```

### 3. 端到端测试
- **工具**：Cypress
- **目标**：模拟用户操作流程，测试完整功能。
- **示例**：
  ```javascript
  // 测试情绪轮盘交互流程
  it('should navigate through emotion wheel', () => {
    cy.visit('/');
    cy.get('[data-testid="emotion-wheel"]').click();
    cy.get('[data-testid="emotion-path"]').should('contain', 'Happy');
  });
  ```

### 4. 性能测试
- **工具**：Lighthouse / WebPageTest
- **目标**：测试页面加载速度、交互响应时间、资源占用等。
- **指标**：
  - 首次内容绘制 (FCP) < 1.5s
  - 交互响应时间 < 100ms
  - 内存占用 < 50MB

---

## 测试流程
1. **编写测试**：根据功能模块编写单元测试和集成测试。
2. **运行测试**：执行 `pnpm test` 运行所有测试。
3. **端到端测试**：执行 `pnpm cypress:run` 运行 Cypress 测试。
4. **性能测试**：使用 Lighthouse 或 WebPageTest 进行性能分析。

---

## 测试覆盖率
- **目标**：核心模块测试覆盖率不低于 80%。
- **工具**：使用 Vitest/Jest 的覆盖率报告功能。
- **命令**：`pnpm test:coverage`

---

## 常见问题
- **测试失败**：检查测试数据和模拟函数是否正确。
- **性能问题**：优化资源加载和代码执行效率。
- **环境问题**：确保测试环境与开发环境一致。

---

## 注意事项
- 测试需覆盖正常和异常场景，确保功能健壮性。
- 定期更新测试用例，适应功能变化。
- 测试结果需与团队共享，及时修复问题。 