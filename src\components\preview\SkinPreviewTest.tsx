/**
 * 皮肤预览测试组件
 */

import type { EmotionData } from '@/types/emotionDataTypes';
import type { ContentDisplayMode, ViewType, RenderEngine } from '@/types/previewTypes';
import type { Skin } from '@/types/skinTypes';
import { DataAdapters } from '@/utils/dataAdapters';
import { Services } from '@/services';
import type React from 'react';
import { useEffect, useState } from 'react';
import { SkinPreview } from './SkinPreview';

/**
 * 皮肤预览测试组件
 */
export const SkinPreviewTest: React.FC = () => {
  const [emotionData, setEmotionData] = useState<EmotionData | null>(null);
  const [skin, setSkin] = useState<Skin | null>(null);
  const [viewType, setViewType] = useState<ViewType>('wheel');
  const [contentDisplayMode, setContentDisplayMode] = useState<ContentDisplayMode>('textEmoji');
  const [RenderEngine, setRenderEngine] = useState<RenderEngine>('D3');

  useEffect(() => {
    // 加载数据
    const defaultEmotionData = DataAdapters.createDefaultEmotionData();
    setEmotionData(defaultEmotionData);

    // 获取默认皮肤
    const defaultSkin = skinManager.getDefaultSkin();
    if (defaultSkin) {
      setSkin(defaultSkin);
    } else {
      // 如果没有默认皮肤，创建一个临时皮肤
      const tempSkin = DataAdapters.createDefaultSkin();
      setSkin(tempSkin);
    }
  }, [skinManager]);

  if (!emotionData || !skin) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">SkinPreview Test</h2>
      <SkinPreview
        emotionData={emotionData}
        skin={skin}
        viewType={viewType}
        contentDisplayMode={contentDisplayMode}
        RenderEngine={RenderEngine}
        onViewTypeChange={setViewType}
        onContentDisplayModeChange={setContentDisplayMode}
        onRenderEngineChange={setRenderEngine}
      />
    </div>
  );
};
