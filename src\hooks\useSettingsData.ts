/**
 * Settings页面数据获取钩子
 * 实现离线在线混合模式的设置数据获取和更新
 */

import { useState, useEffect, useCallback } from 'react';
import { Services } from '@/services';
import {
  useHybridEmotionDataSetsData,
  useHybridEmojiSetsData,
  useHybridSkinsData
} from './useHybridData';
import {
  EmotionDataSet,
  EmojiSet,
  Skin
} from '@/types';

interface UseSettingsDataReturn {
  // 数据状态
  emotionDataSets: EmotionDataSet[];
  activeEmotionDataSet: EmotionDataSet | null;
  emojiSets: EmojiSet[];
  activeEmojiSet: EmojiSet | null;
  skins: Skin[];
  activeSkin: Skin | null;

  // 加载状态
  isLoadingEmotionData: boolean;
  isLoadingEmojiSets: boolean;
  isLoadingSkins: boolean;
  error: string | null;

  // 网络状态
  isOnline: boolean;
  lastSyncTime: Date | null;

  // 操作方法
  refresh: () => Promise<void>;
  forceSync: () => Promise<void>;

  // 设置更新方法
  updateEmotionDataSet: (dataSetId: string) => Promise<{ success: boolean; error?: string }>;
  updateEmojiSet: (setId: string) => Promise<{ success: boolean; error?: string }>;
  updateSkin: (skinId: string) => Promise<{ success: boolean; error?: string }>;
  unlockSkin: (skinId: string) => Promise<{ success: boolean; error?: string }>;
}

/**
 * Settings页面数据钩子
 * 管理设置相关数据的获取和更新
 */
export const useSettingsData = (): UseSettingsDataReturn => {
  // 使用混合数据获取钩子
  const {
    data: emotionDataSets,
    isLoading: isLoadingEmotionData,
    error: emotionDataError,
    isOnline: emotionDataOnline,
    lastSyncTime: emotionDataSyncTime,
    refresh: refreshEmotionData,
    forceSync: forceSyncEmotionData
  } = useHybridEmotionDataSetsData();

  const {
    data: emojiSets,
    isLoading: isLoadingEmojiSets,
    error: emojiSetsError,
    isOnline: emojiSetsOnline,
    lastSyncTime: emojiSetsSyncTime,
    refresh: refreshEmojiSets,
    forceSync: forceSyncEmojiSets
  } = useHybridEmojiSetsData();

  const {
    data: skins,
    isLoading: isLoadingSkins,
    error: skinsError,
    isOnline: skinsOnline,
    lastSyncTime: skinsSyncTime,
    refresh: refreshSkins,
    forceSync: forceSyncSkins
  } = useHybridSkinsData();

  // 本地状态
  const [activeEmotionDataSet, setActiveEmotionDataSet] = useState<EmotionDataSet | null>(null);
  const [activeEmojiSet, setActiveEmojiSet] = useState<EmojiSet | null>(null);
  const [activeSkin, setActiveSkin] = useState<Skin | null>(null);

  // 加载活动设置
  const loadActiveSettings = useCallback(async () => {
    try {
      // 获取活动的情绪数据集
      if (emotionDataSets && Array.isArray(emotionDataSets)) {
        const activeDataSet = emotionDataSets.find(ds => ds.is_active) || emotionDataSets[0];
        if (activeDataSet) {
          setActiveEmotionDataSet(activeDataSet);
        }
      }

      // 获取活动的表情集
      if (emojiSets && Array.isArray(emojiSets)) {
        const activeSet = emojiSets.find(set => set.is_active) || emojiSets[0];
        if (activeSet) {
          setActiveEmojiSet(activeSet);
        }
      }

      // 获取活动的皮肤
      if (skins && Array.isArray(skins)) {
        const activeSkinItem = skins.find(skin => skin.is_default) || skins[0];
        if (activeSkinItem) {
          setActiveSkin(activeSkinItem);
        }
      }
    } catch (error) {
      console.error('[useSettingsData] Error loading active settings:', error);
    }
  }, [emotionDataSets, emojiSets, skins]);

  // 当数据加载完成时，设置活动项
  useEffect(() => {
    loadActiveSettings();
  }, [loadActiveSettings]);

  // 更新情绪数据集 (现在使用Quiz包)
  const updateEmotionDataSet = useCallback(async (dataSetId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('[useSettingsData] Updating emotion data set (quiz pack):', dataSetId);

      // 优先尝试在线更新
      if (emotionDataOnline) {
        try {
          // TODO: 实现在线更新 - 调用tRPC端点
          console.log('[useSettingsData] Online update not implemented, falling back to offline');
        } catch (onlineError) {
          console.warn('[useSettingsData] Online update failed, falling back to offline:', onlineError);
        }
      }

      // ✅ 使用新的 QuizPackService 离线更新
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.setAsDefault(dataSetId);

      if (result.success) {
        // 更新本地状态
        const selectedDataSet = emotionDataSets?.find(ds => ds.id === dataSetId);
        if (selectedDataSet) {
          setActiveEmotionDataSet(selectedDataSet);
        }
        console.log('[useSettingsData] Emotion data set (quiz pack) updated successfully');
        return { success: true };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update emotion data set';
      console.error('[useSettingsData] Error updating emotion data set:', error);
      return { success: false, error: errorMessage };
    }
  }, [emotionDataOnline, emotionDataSets]);

  // 更新表情集
  const updateEmojiSet = useCallback(async (setId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('[useSettingsData] Updating emoji set:', setId);

      // 优先尝试在线更新
      if (emojiSetsOnline) {
        try {
          // TODO: 实现在线更新 - 调用tRPC端点
          console.log('[useSettingsData] Online update not implemented, falling back to offline');
        } catch (onlineError) {
          console.warn('[useSettingsData] Online update failed, falling back to offline:', onlineError);
        }
      }

      // ✅ 使用新的 EmojiMappingService 离线更新
      const emojiMappingService = await Services.emojiMapping();
      const result = await emojiMappingService.setActiveEmojiSet(setId);

      if (result.success) {
        // 更新本地状态
        const selectedSet = emojiSets?.find(set => set.id === setId);
        if (selectedSet) {
          setActiveEmojiSet(selectedSet);
        }
        console.log('[useSettingsData] Emoji set updated successfully');
        return { success: true };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update emoji set';
      console.error('[useSettingsData] Error updating emoji set:', error);
      return { success: false, error: errorMessage };
    }
  }, [emojiSetsOnline, emojiSets]);

  // 更新皮肤
  const updateSkin = useCallback(async (skinId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('[useSettingsData] Updating skin:', skinId);

      // 优先尝试在线更新
      if (skinsOnline) {
        try {
          // TODO: 实现在线更新
          console.log('[useSettingsData] Online update not implemented, falling back to offline');
        } catch (onlineError) {
          console.warn('[useSettingsData] Online update failed, falling back to offline:', onlineError);
        }
      }

      // 离线更新
      const skinService = await Services.skin();
      const result = await skinService.setDefaultSkin(skinId);

      if (result.success) {
        // 更新本地状态
        const selectedSkin = skins?.find(skin => skin.id === skinId);
        if (selectedSkin) {
          setActiveSkin(selectedSkin);
        }
        console.log('[useSettingsData] Skin updated successfully');
        return { success: true };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update skin';
      console.error('[useSettingsData] Error updating skin:', error);
      return { success: false, error: errorMessage };
    }
  }, [skinsOnline, skins]);

  // 解锁皮肤
  const unlockSkin = useCallback(async (skinId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('[useSettingsData] Unlocking skin:', skinId);

      // 优先尝试在线解锁
      if (skinsOnline) {
        try {
          // TODO: 实现在线解锁
          console.log('[useSettingsData] Online unlock not implemented, falling back to offline');
        } catch (onlineError) {
          console.warn('[useSettingsData] Online unlock failed, falling back to offline:', onlineError);
        }
      }

      // 离线解锁
      const skinService = await Services.skin();
      const result = await skinService.unlockSkin(skinId);

      if (result.success) {
        // 刷新皮肤列表
        await refreshSkins();
        console.log('[useSettingsData] Skin unlocked successfully');
        return { success: true };
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unlock skin';
      console.error('[useSettingsData] Error unlocking skin:', error);
      return { success: false, error: errorMessage };
    }
  }, [skinsOnline, refreshSkins]);

  // 刷新所有数据
  const refresh = useCallback(async () => {
    await Promise.all([
      refreshEmotionData(),
      refreshEmojiSets(),
      refreshSkins()
    ]);
  }, [refreshEmotionData, refreshEmojiSets, refreshSkins]);

  // 强制同步所有数据
  const forceSync = useCallback(async () => {
    await Promise.all([
      forceSyncEmotionData(),
      forceSyncEmojiSets(),
      forceSyncSkins()
    ]);
  }, [forceSyncEmotionData, forceSyncEmojiSets, forceSyncSkins]);

  // 计算总体状态
  const isOnline = emotionDataOnline && emojiSetsOnline && skinsOnline;
  const lastSyncTime = [emotionDataSyncTime, emojiSetsSyncTime, skinsSyncTime]
    .filter(Boolean)
    .sort((a, b) => (b?.getTime() || 0) - (a?.getTime() || 0))[0] || null;

  const error = emotionDataError?.message || emojiSetsError?.message || skinsError?.message || null;

  return {
    // 数据状态
    emotionDataSets: emotionDataSets || [],
    activeEmotionDataSet,
    emojiSets: emojiSets || [],
    activeEmojiSet,
    skins: skins || [],
    activeSkin,

    // 加载状态
    isLoadingEmotionData,
    isLoadingEmojiSets,
    isLoadingSkins,
    error,

    // 网络状态
    isOnline,
    lastSyncTime,

    // 操作方法
    refresh,
    forceSync,

    // 设置更新方法
    updateEmotionDataSet,
    updateEmojiSet,
    updateSkin,
    unlockSkin
  };
};
