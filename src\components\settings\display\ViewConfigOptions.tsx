import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useLanguage } from '@/contexts/LanguageContext';
import { useSkinManager } from '@/contexts/SkinContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import { Check } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';

/**
 * 简化的视图配置选项组件
 * 用于配置视图的基本参数和皮肤选择
 */
const ViewConfigOptions: React.FC = () => {
  const { t } = useLanguage();
  const { activeSkin, skins } = useSkinManager();
  const { userConfig, updateUserConfig } = useUserConfig();

  // 获取当前显示选项
  const displayOptions = {
    viewType: userConfig.preferred_view_type || 'wheel',
    renderEngine:
      userConfig.render_engine_preferences?.[userConfig.preferred_view_type || 'wheel'] || 'D3',
    displayMode:
      userConfig.content_display_mode_preferences?.[userConfig.preferred_view_type || 'wheel'] ||
      'textEmoji',
    skinId: userConfig.active_skin_id || 'default',
  };

  const currentSkin = activeSkin;
  const [isLoading, setIsLoading] = useState(false);

  // 处理皮肤选择
  const handleSkinSelect = async (skinId: string) => {
    try {
      setIsLoading(true);
      updateUserConfig({
        active_skin_id: skinId,
        view_type_skin_ids: {
          ...userConfig.view_type_skin_ids,
          [displayOptions.viewType]: skinId,
        },
      });
    } catch (error) {
      console.error('Failed to update skin:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 过滤可用皮肤 - SkinConfig 没有 is_unlocked 属性，所以显示所有皮肤
  const availableSkins = skins;

  return (
    <div className="space-y-6">
      {/* 皮肤选择区域 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">{t('settings.skin_selection')}</h3>
          <Badge variant="outline">
            {availableSkins.length} {t('settings.available')}
          </Badge>
        </div>

        {availableSkins.length === 0 ? (
          <Card className="p-6 text-center">
            <p className="text-muted-foreground">{t('settings.no_skins_available')}</p>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableSkins.map((skin, index) => (
              <Card
                key={index}
                className={`overflow-hidden transition-all duration-300 cursor-pointer hover:shadow-md ${
                  currentSkin === skin ? 'border-primary shadow-lg' : 'border-border'
                }`}
                onClick={() => handleSkinSelect(`skin-${index}`)}
              >
                <div className="relative">
                  <div className="h-20 bg-muted relative">
                    {/* 简化的预览区域 */}
                    <div className="w-full h-full flex items-center justify-center text-2xl">
                      🎨
                    </div>

                    {/* 选中标识 */}
                    {currentSkin === skin && (
                      <div className="absolute top-1 left-1 bg-primary text-primary-foreground rounded-full p-1">
                        <Check size={12} />
                      </div>
                    )}
                  </div>

                  <div className="p-3">
                    <h4 className="font-medium text-sm mb-1">{skin.name || `Skin ${index + 1}`}</h4>
                    <div className="flex items-center justify-between">
                      <Badge variant="secondary" className="text-xs">
                        {t('settings.available')}
                      </Badge>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* 当前配置信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">{t('settings.current_configuration')}</h3>
        <Card className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">{t('settings.view_type')}</Label>
              <p className="text-sm text-muted-foreground mt-1">{displayOptions.viewType}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">{t('settings.render_engine')}</Label>
              <p className="text-sm text-muted-foreground mt-1">{displayOptions.renderEngine}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">{t('settings.display_mode')}</Label>
              <p className="text-sm text-muted-foreground mt-1">{displayOptions.displayMode}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">{t('settings.active_skin')}</Label>
              <p className="text-sm text-muted-foreground mt-1">
                {currentSkin?.name || 'Default Skin'}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button variant="outline" onClick={() => window.location.reload()} disabled={isLoading}>
          {t('settings.refresh')}
        </Button>
      </div>
    </div>
  );
};

export default ViewConfigOptions;
