import * as path from 'node:path';
import { D1Adapter } from './D1Adapter.js';
import type { DatabaseInterface } from './DatabaseInterface.js';
import { SQLiteAdapter } from './SQLiteAdapter.js';
import { TursoAdapter } from './TursoAdapter.js';

/**
 * 数据库类型枚举
 */
export enum DatabaseType {
  TURSO = 'turso',
  SQLITE = 'sqlite',
  D1 = 'd1',
}

/**
 * 数据库配置接口
 */
export interface DatabaseConfig {
  type: DatabaseType;
  url?: string;
  authToken?: string;
  dbPath?: string;
  d1Instance?: any;
}

/**
 * 数据库工厂类
 * 用于创建不同类型的数据库适配器
 */
export class DatabaseFactory {
  private static instance: DatabaseFactory;
  private adapters: Map<string, DatabaseInterface> = new Map();

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): DatabaseFactory {
    if (!DatabaseFactory.instance) {
      DatabaseFactory.instance = new DatabaseFactory();
    }
    return DatabaseFactory.instance;
  }

  /**
   * 创建数据库适配器
   * @param config 数据库配置
   * @param instanceId 可选的实例 ID，用于创建多个同类型的数据库连接
   */
  public createAdapter(config: DatabaseConfig, instanceId = 'default'): DatabaseInterface {
    const key = `${config.type}_${instanceId}`;

    // 如果适配器已经存在，直接返回
    if (this.adapters.has(key)) {
      return this.adapters.get(key)!;
    }

    let adapter: DatabaseInterface;

    switch (config.type) {
      case DatabaseType.TURSO:
        if (!config.url) {
          throw new Error('Turso database URL is required');
        }
        adapter = new TursoAdapter(config.url, config.authToken);
        break;

      case DatabaseType.SQLITE: {
        let dbPath = config.dbPath;
        if (!dbPath) {
          // 默认使用项目根目录下的 local.db
          dbPath = path.resolve(process.cwd(), 'local.db');
          console.log(`[DatabaseFactory] Using default SQLite database path: ${dbPath}`);
        }
        adapter = new SQLiteAdapter(dbPath);
        break;
      }

      case DatabaseType.D1:
        if (!config.d1Instance) {
          throw new Error('D1 database instance is required');
        }
        adapter = new D1Adapter(config.d1Instance);
        break;

      default:
        throw new Error(`Unsupported database type: ${config.type}`);
    }

    // 存储适配器实例
    this.adapters.set(key, adapter);
    return adapter;
  }

  /**
   * 获取数据库适配器
   * @param type 数据库类型
   * @param instanceId 可选的实例 ID
   */
  public getAdapter(type: DatabaseType, instanceId = 'default'): DatabaseInterface | undefined {
    return this.adapters.get(`${type}_${instanceId}`);
  }

  /**
   * 关闭所有数据库连接
   */
  public async closeAll(): Promise<void> {
    for (const [key, adapter] of this.adapters.entries()) {
      try {
        await adapter.close();
        console.log(`[DatabaseFactory] Closed database connection: ${key}`);
      } catch (error) {
        console.error(`[DatabaseFactory] Error closing database connection ${key}:`, error);
      }
    }
    this.adapters.clear();
  }
}
