import { useLanguage } from '@/contexts/LanguageContext';
import { useSkinManager } from '@/contexts/SkinContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import type { ContentDisplayMode } from '@/types';
import type React from 'react';
import { useMemo } from 'react';
import OptionButton from '../ui/OptionButton';

/**
 * 显示模式选项组件
 * 用于选择显示模式（文本、表情符号、文本+表情符号）
 */
const DisplayModeOptions: React.FC = () => {
  const { t } = useLanguage();
  const { userConfig, updateUserConfig } = useUserConfig();
  const { currentSkin } = useSkinManager();

  // 获取当前显示选项
  const displayOptions = useMemo(() => {
    const viewType = userConfig.preferred_view_type || 'wheel';
    const skinId =
      userConfig.view_type_skin_ids?.[viewType] ||
      userConfig.active_skin_id ||
      'default-wheel-skin';
    const renderEngine = userConfig.render_engine_preferences?.[viewType] || 'D3';
    const displayMode = userConfig.content_display_mode_preferences?.[viewType] || 'textEmoji';

    return {
      viewType,
      skinId,
      renderEngine,
      displayMode,
    };
  }, [userConfig]);

  // 处理显示模式变更
  const handleDisplayModeChange = (mode: ContentDisplayMode) => {
    // 获取当前视图类型
    const viewType = userConfig.preferred_view_type || displayOptions.viewType;

    // 更新用户配置
    updateUserConfig({
      content_display_mode_preferences: {
        ...userConfig.content_display_mode_preferences,
        [viewType]: mode,
      },
    });
  };

  // 获取当前皮肤支持的显示模式
  const supportedDisplayModes = currentSkin?.config?.supported_content_modes || [
    'text',
    'emoji',
    'textEmoji',
  ];

  return (
    <div className="space-y-4">
      <p className="text-sm text-muted-foreground">{t('settings.display_mode.description')}</p>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
        {supportedDisplayModes.includes('text') && (
          <OptionButton
            label={t('settings.display_mode.text')}
            tooltip={t('settings.display_mode.text.description')}
            isSelected={displayOptions.displayMode === 'text'}
            onClick={() => handleDisplayModeChange('text')}
          />
        )}

        {supportedDisplayModes.includes('emoji') && (
          <OptionButton
            label={t('settings.display_mode.emoji')}
            tooltip={t('settings.display_mode.emoji.description')}
            isSelected={displayOptions.displayMode === 'emoji'}
            onClick={() => handleDisplayModeChange('emoji')}
          />
        )}

        {supportedDisplayModes.includes('textEmoji') && (
          <OptionButton
            label={t('settings.display_mode.textEmoji')}
            tooltip={t('settings.display_mode.textEmoji.description')}
            isSelected={displayOptions.displayMode === 'textEmoji'}
            onClick={() => handleDisplayModeChange('textEmoji')}
          />
        )}
      </div>
    </div>
  );
};

export default DisplayModeOptions;
