/**
 * Quiz系统 tRPC 路由 - 新架构
 * 基于统一类型定义和服务架构设计
 */

import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../router';
import { TRPCError } from '@trpc/server';
import { QuizService } from '../services/QuizService';
import { QuizEngineService } from '../services/QuizEngineService';

// ==================== 输入验证Schema ====================

const GetQuizPacksInputSchema = z.object({
  category: z.string().optional(),
  difficultyLevel: z.number().int().min(1).max(5).optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

const CreateQuizSessionInputSchema = z.object({
  packId: z.string(),
  userId: z.string(),
  sessionType: z.string().default('standard'),
});

const SubmitAnswerInputSchema = z.object({
  session_id: z.string(),
  question_id: z.string(),
  selected_option_ids: z.array(z.string()),
  answer_value: z.string(),
  answer_text: z.string().optional(),
  confidence_score: z.number().min(0).max(100).optional(),
  response_time_ms: z.number().min(0).optional(),
  session_presentation_config_id: z.string().optional(),
});

const GetCurrentQuestionInputSchema = z.object({
  session_id: z.string(),
});

const GetQuizResultInputSchema = z.object({
  session_id: z.string(),
});

const GetUserSessionsInputSchema = z.object({
  user_id: z.string(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
});

// 离线数据处理Schema
const OfflineQuizDataSchema = z.object({
  sessions: z.array(z.any()),
  answers: z.array(z.any()),
  results: z.array(z.any()).optional(),
  metadata: z.object({
    client_timestamp: z.string(),
    sync_version: z.number(),
    device_id: z.string(),
  }),
});

const QuizSyncDataSchema = z.object({
  client_data: OfflineQuizDataSchema,
  last_sync_timestamp: z.string().optional(),
  conflict_resolution: z.enum(['client_wins', 'server_wins', 'merge']).default('merge'),
});

// ==================== Quiz路由定义 ====================

export const quizRouter = router({
  // 获取Quiz包列表
  getQuizPacks: publicProcedure
    .input(GetQuizPacksInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.getAvailableQuizPacks('current-user', 'free');
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch quiz packs',
          cause: error,
        });
      }
    }),

  // 获取推荐Quiz包
  getRecommendedQuizPacks: publicProcedure
    .query(async ({ ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.getRecommendedQuizPacks('current-user');
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch recommended quiz packs',
          cause: error,
        });
      }
    }),

  // 根据类型获取Quiz包
  getQuizPacksByType: publicProcedure
    .input(z.object({ quiz_type: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.getQuizPacksByType(input.quiz_type);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch quiz packs by type',
          cause: error,
        });
      }
    }),

  // 搜索Quiz包
  searchQuizPacks: publicProcedure
    .input(z.object({ query: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.searchQuizPacks(input.query);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to search quiz packs',
          cause: error,
        });
      }
    }),

  // 获取Quiz包详情
  getQuizPackDetails: publicProcedure
    .input(z.object({ pack_id: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.getQuizPackDetails(input.pack_id);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch quiz pack details',
          cause: error,
        });
      }
    }),

  // 创建Quiz会话
  createQuizSession: publicProcedure
    .input(CreateQuizSessionInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.createQuizSession(input.packId, input.userId);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create quiz session',
          cause: error,
        });
      }
    }),

  // 获取当前问题
  getCurrentQuestion: publicProcedure
    .input(GetCurrentQuestionInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.getCurrentQuestionData(input.session_id);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get current question',
          cause: error,
        });
      }
    }),

  // 提交答案
  submitAnswer: publicProcedure
    .input(SubmitAnswerInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.submitAnswer(input);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to submit answer',
          cause: error,
        });
      }
    }),

  // 获取用户会话列表
  getUserSessions: publicProcedure
    .input(GetUserSessionsInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.getUserSessions(input.user_id, input.limit);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get user sessions',
          cause: error,
        });
      }
    }),

  // 获取会话答案
  getSessionAnswers: publicProcedure
    .input(z.object({ session_id: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.getSessionAnswers(input.session_id);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get session answers',
          cause: error,
        });
      }
    }),

  // 暂停会话
  pauseSession: publicProcedure
    .input(z.object({ session_id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.pauseSession(input.session_id);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to pause session',
          cause: error,
        });
      }
    }),

  // 恢复会话
  resumeSession: publicProcedure
    .input(z.object({ session_id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      try {
        const quizService = QuizService.getInstance();
        return await quizService.resumeSession(input.session_id);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to resume session',
          cause: error,
        });
      }
    }),

  // 获取Quiz结果 (需要在QuizService中添加此方法)
  getQuizResult: publicProcedure
    .input(GetQuizResultInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        // 注意：这个方法在QuizService中还不存在，需要添加
        const quizService = QuizService.getInstance();
        // 临时返回会话答案作为结果
        return await quizService.getSessionAnswers(input.session_id);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get quiz result',
          cause: error,
        });
      }
    }),

  // ==================== 离线数据处理端点 ====================

  // 处理离线Quiz数据
  processOfflineData: publicProcedure
    .input(OfflineQuizDataSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const quizEngineService = QuizEngineService.getInstance();
        const result = await quizEngineService.processOfflineQuizData(input);

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to process offline data',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process offline quiz data',
          cause: error,
        });
      }
    }),

  // 双向同步Quiz数据
  syncQuizData: publicProcedure
    .input(QuizSyncDataSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const quizEngineService = QuizEngineService.getInstance();
        const result = await quizEngineService.syncQuizData(input);

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to sync quiz data',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to sync quiz data',
          cause: error,
        });
      }
    }),

  // 验证Quiz数据完整性
  validateQuizData: publicProcedure
    .input(OfflineQuizDataSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const quizEngineService = QuizEngineService.getInstance();
        const result = await quizEngineService.validateQuizData(input);

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to validate quiz data',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to validate quiz data',
          cause: error,
        });
      }
    }),
});
