/**
 * SVG轮盘视图
 * 使用SVG实现的轮盘视图
 */

import type { Emotion, ContentDisplayMode, ViewConfig, RenderEngine, SkinConfig } from '@/types';
import { BaseEmotionView } from '@/views/base/BaseEmotionView';
import type React from 'react';

// 使用新的组件，不依赖旧的轮盘实现
import { SVGWheelComponent } from '@/views/components/wheels/SVGWheelComponent';

/**
 * SVG轮盘视图类
 * 使用SVG实现的轮盘视图
 */
export class SVGWheelView extends BaseEmotionView {
  private implementation: RenderEngine = 'SVG';

  /**
   * 构造函数
   * @param contentType 内容显示模式
   * @param skinConfig 皮肤配置
   */
  constructor(contentType: ContentDisplayMode, skinConfig: SkinConfig) {
    super('wheel', contentType, skinConfig);
  }

  /**
   * 获取轮盘实现类型
   * @returns 轮盘实现类型
   */
  getImplementation(): RenderEngine {
    return this.implementation;
  }

  /**
   * 设置轮盘实现类型
   * @param implementation 新的轮盘实现类型
   */
  setImplementation(implementation: RenderEngine): void {
    this.implementation = implementation;
  }

  // 注意：原来的 setContentStrategy 方法已被移除，
  // 现在使用 setContentDisplayMode 方法来设置内容显示模式

  /**
   * 渲染轮盘
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  renderWheel(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    // 从配置中获取额外属性
    const onBack = config?.onBack;
    const selectedPath = config?.selectedPath;

    return (
      <SVGWheelComponent
        emotions={emotions}
        tierLevel={tierLevel}
        contentDisplayMode={this.contentDisplayMode}
        skinConfig={this.skinConfig}
        onSelect={onSelect}
        onBack={onBack}
        selectedPath={selectedPath}
      />
    );
  }

  /**
   * 渲染视图
   * 实现基类的抽象方法
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  render(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    return this.renderWheel(emotions, tierLevel, onSelect, config);
  }
}
