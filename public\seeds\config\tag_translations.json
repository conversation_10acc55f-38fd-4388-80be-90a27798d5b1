{"tag_translations": [{"tag_id": "tag_work", "language_code": "en", "translated_name": "Work", "translated_description": "Emotions and experiences related to work", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_work", "language_code": "es", "translated_name": "Trabajo", "translated_description": "Emociones y experiencias relacionadas con el trabajo", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_family", "language_code": "en", "translated_name": "Family", "translated_description": "Emotions and experiences related to family members", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_family", "language_code": "es", "translated_name": "Familia", "translated_description": "Emociones y experiencias relacionadas con miembros de la familia", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_friends", "language_code": "en", "translated_name": "Friends", "translated_description": "Emotions and experiences related to social interactions with friends", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_friends", "language_code": "es", "translated_name": "Amigos", "translated_description": "Emociones y experiencias relacionadas con interacciones sociales con amigos", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_health", "language_code": "en", "translated_name": "Health", "translated_description": "Emotions and experiences related to physical health", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_health", "language_code": "es", "translated_name": "<PERSON><PERSON>", "translated_description": "Emociones y experiencias relacionadas con la salud física", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_exercise", "language_code": "en", "translated_name": "Exercise", "translated_description": "Emotions and experiences related to physical exercise", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_exercise", "language_code": "es", "translated_name": "<PERSON><PERSON><PERSON><PERSON>", "translated_description": "Emociones y experiencias relacionadas con el ejercicio físico", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_study", "language_code": "en", "translated_name": "Study", "translated_description": "Emotions and experiences related to learning and education", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_study", "language_code": "es", "translated_name": "Estudio", "translated_description": "Emociones y experiencias relacionadas con el aprendizaje y la educación", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_rest", "language_code": "en", "translated_name": "Rest", "translated_description": "Emotions and experiences related to rest and relaxation", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_rest", "language_code": "es", "translated_name": "Descanso", "translated_description": "Emociones y experiencias relacionadas con el descanso y la relajación", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_travel", "language_code": "en", "translated_name": "Travel", "translated_description": "Emotions and experiences related to travel and trips", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_travel", "language_code": "es", "translated_name": "<PERSON><PERSON>", "translated_description": "Emociones y experiencias relacionadas con viajes y excursiones", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_hobby", "language_code": "en", "translated_name": "<PERSON>bby", "translated_description": "Emotions and experiences related to personal hobbies", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_hobby", "language_code": "es", "translated_name": "Pasatiempo", "translated_description": "Emociones y experiencias relacionadas con pasatiempos personales", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_stress", "language_code": "en", "translated_name": "Stress", "translated_description": "Situations where stress is felt", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_stress", "language_code": "es", "translated_name": "Estrés", "translated_description": "Situaciones donde se siente estrés", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_anxiety", "language_code": "en", "translated_name": "Anxiety", "translated_description": "Situations where anxiety is felt", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_anxiety", "language_code": "es", "translated_name": "Ansiedad", "translated_description": "Situaciones donde se siente ansiedad", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_happiness", "language_code": "en", "translated_name": "Happiness", "translated_description": "Situations where happiness is felt", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_happiness", "language_code": "es", "translated_name": "Felicidad", "translated_description": "Situaciones donde se siente felicidad", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_sadness", "language_code": "en", "translated_name": "Sadness", "translated_description": "Situations where sadness is felt", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_sadness", "language_code": "es", "translated_name": "Tristeza", "translated_description": "Situaciones donde se siente tristeza", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_anger", "language_code": "en", "translated_name": "Anger", "translated_description": "Situations where anger is felt", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_anger", "language_code": "es", "translated_name": "Ira", "translated_description": "Situaciones donde se siente ira", "translation_quality": 0.9, "translator_type": "ai"}, {"tag_id": "tag_calm", "language_code": "en", "translated_name": "Calm", "translated_description": "Situations where calmness is felt", "translation_quality": 1.0, "translator_type": "human"}, {"tag_id": "tag_calm", "language_code": "es", "translated_name": "Calma", "translated_description": "Situaciones donde se siente calma", "translation_quality": 0.9, "translator_type": "ai"}]}