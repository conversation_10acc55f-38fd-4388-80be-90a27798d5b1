/**
 * 路径别名配置测试
 * 测试 tsconfig 和 vitest config 是否正确配置，能够使用 @ 路径别名找到 src 下的主要文件夹
 */

import { describe, it, expect } from 'vitest';

// 尝试导入各个主要文件夹中的文件
describe('路径别名配置测试', () => {
  it('应该能够导入 contexts 文件夹中的文件', async () => {
    // 动态导入，避免实际执行上下文逻辑
    const module = await import('@/contexts/ColorModeContext');
    expect(module).toBeDefined();
  });

  it('应该能够导入 types 文件夹中的文件', async () => {
    const module = await import('@/types');
    expect(module).toBeDefined();
  });

  it('应该能够导入 utils 文件夹中的文件', async () => {
    const module = await import('@/utils/colorUtils');
    expect(module).toBeDefined();
  });

  it('应该能够导入 views 文件夹中的文件', async () => {
    const module = await import('@/views/base/BaseEmotionView');
    expect(module).toBeDefined();
  });

  it('应该能够导入 hooks 文件夹中的文件', async () => {
    const module = await import('@/hooks/useLocalStorage');
    expect(module).toBeDefined();
  });

  it('应该能够导入 services 文件夹中的文件', async () => {
    const module = await import('@/services/emotionService');
    expect(module).toBeDefined();
  });

  it('应该能够导入 components 文件夹中的文件', async () => {
    // 使用 ui 文件夹中的组件，因为它更可能是稳定的
    const module = await import('@/components/ui/button');
    expect(module).toBeDefined();
  });

  it('应该能够导入 lib 文件夹中的文件', async () => {
    const module = await import('@/lib/utils');
    expect(module).toBeDefined();
  });
});
