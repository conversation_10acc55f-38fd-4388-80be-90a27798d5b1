/**
 * SpecialViewsTest 页面样式
 * 为新的Quiz组件系统测试页面提供样式
 */

/* 全局容器样式 */
.special-views-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

/* 标题样式 */
.special-views-test h1 {
  text-align: center;
  margin-bottom: 40px;
  color: #2E7D32;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.special-views-test h2 {
  color: #1976D2;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 20px;
  border-bottom: 2px solid #E3F2FD;
  padding-bottom: 10px;
}

.special-views-test h3 {
  color: #424242;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
}

/* 控制面板样式 */
.control-panel {
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f5f5, #e8f5e8);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.mode-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.mode-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mode-button.active {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.mode-button.inactive {
  background: #ddd;
  color: #666;
}

.mode-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-button.reset {
  background: linear-gradient(135deg, #FF5722, #FF7043);
  color: white;
}

.action-button.refresh {
  background: linear-gradient(135deg, #2196F3, #42A5F5);
  color: white;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 状态显示样式 */
.status-display {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
  padding: 10px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

/* 测试内容区域样式 */
.test-content {
  margin-bottom: 60px;
}

.tier-navigation-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

.component-grid {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.component-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.component-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.emotion-wheel-container {
  display: flex;
  gap: 40px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 状态信息样式 */
.status-section {
  margin-bottom: 40px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.status-card {
  background: linear-gradient(135deg, #f5f5f5, #e8f5e8);
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-card h3 {
  color: #2E7D32;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.status-card p {
  margin: 5px 0;
  font-size: 14px;
}

.status-card strong {
  color: #1976D2;
}

.status-list {
  margin: 5px 0;
  padding-left: 20px;
}

.status-list li {
  font-size: 12px;
  margin: 2px 0;
  color: #424242;
}

/* 交互日志样式 */
.interaction-log {
  background: linear-gradient(135deg, #f5f5f5, #e3f2fd);
  padding: 16px;
  border-radius: 12px;
  max-height: 300px;
  overflow: auto;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.log-entry {
  margin-bottom: 8px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #4CAF50;
}

.log-entry-content {
  font-weight: 600;
  color: #2E7D32;
}

.log-entry-time {
  color: #666;
  font-size: 10px;
  margin-top: 4px;
}

.no-logs {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

/* 架构说明样式 */
.architecture-info {
  margin-top: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
  border-radius: 12px;
  border: 2px solid #4CAF50;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.1);
}

.architecture-info h2 {
  color: #2E7D32;
  margin-bottom: 15px;
  border-bottom: 2px solid #4CAF50;
}

.architecture-description {
  font-size: 14px;
  line-height: 1.6;
  color: #424242;
}

.architecture-description p {
  margin: 10px 0;
  padding: 8px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  border-left: 4px solid #4CAF50;
}

.architecture-description strong {
  color: #2E7D32;
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .special-views-test {
    padding: 10px;
  }
  
  .special-views-test h1 {
    font-size: 2rem;
  }
  
  .mode-buttons {
    flex-direction: column;
  }
  
  .mode-button {
    width: 100%;
    text-align: center;
  }
  
  .component-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .emotion-wheel-container {
    flex-direction: column;
    align-items: center;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 20px;
}

.error-container {
  padding: 20px;
  text-align: center;
  background: #ffebee;
  border-radius: 8px;
  border: 2px solid #f44336;
  color: #c62828;
}

.error-container h2 {
  color: #c62828;
  margin-bottom: 10px;
}

.error-container button {
  margin-top: 15px;
  padding: 10px 20px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.error-container button:hover {
  background: #d32f2f;
}
