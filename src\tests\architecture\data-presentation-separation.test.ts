/**
 * 数据与展现分离验证测试 (P0 最高优先级)
 * 基于 docs/quiz/ 目录的设计原则
 * 验证数据层纯净性和展现层独立性
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock database connection
const mockDb = {
  query: vi.fn(),
  run: vi.fn(),
  execute: vi.fn(),
  prepare: vi.fn(),
};

describe('数据与展现分离验证测试 (P0)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('1. 数据层纯净性验证', () => {
    it('应该验证 Quiz包不包含展现配置', async () => {
      const mockQuizPack = {
        id: 'pack123',
        name: 'TCM Emotions Pack',
        description: 'Traditional Chinese Medicine emotions assessment',
        category: 'tcm',
        version: '1.0.0',
        questions: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      mockDb.query.mockResolvedValueOnce([mockQuizPack]);

      const result = await mockDb.query('SELECT * FROM quiz_packs WHERE id = ?', ['pack123']);
      const pack = result[0];

      // 验证Quiz包只包含数据，不包含展现配置
      expect(pack.id).toBeTruthy();
      expect(pack.name).toBeTruthy();
      expect(pack.description).toBeTruthy();
      expect(pack.category).toBeTruthy();
      expect(pack.version).toBeTruthy();
      
      // 确保不包含展现相关字段
      expect(pack.emoji_mapping).toBeUndefined();
      expect(pack.skin_config).toBeUndefined();
      expect(pack.presentation_config).toBeUndefined();
      expect(pack.ui_theme).toBeUndefined();
    });

    it('应该验证问题数据只包含逻辑配置', async () => {
      const mockQuestion = {
        id: 'q1',
        quiz_pack_id: 'pack123',
        question_text: 'How do you feel about your energy level?',
        question_type: 'single_choice',
        order_index: 1,
        is_required: true,
        scoring_config: JSON.stringify({
          scoring_type: 'weighted',
          max_score: 100
        }),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      mockDb.query.mockResolvedValueOnce([mockQuestion]);

      const result = await mockDb.query('SELECT * FROM quiz_questions WHERE id = ?', ['q1']);
      const question = result[0];

      // 验证问题只包含逻辑配置，不包含展现配置
      expect(question.id).toBeTruthy();
      expect(question.quiz_pack_id).toBeTruthy();
      expect(question.question_text).toBeTruthy();
      expect(question.question_type).toBeTruthy();
      expect(question.scoring_config).toBeTruthy();
      
      // 确保不包含展现相关字段
      expect(question.emoji_set).toBeUndefined();
      expect(question.color_scheme).toBeUndefined();
      expect(question.layout_config).toBeUndefined();
      expect(question.animation_config).toBeUndefined();
    });

    it('应该验证选项数据只包含内容和基础属性', async () => {
      const mockOption = {
        id: 'opt1',
        question_id: 'q1',
        option_text: 'Very energetic',
        option_value: 'high_energy',
        scoring_value: 80,
        order_index: 1,
        is_correct: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      mockDb.query.mockResolvedValueOnce([mockOption]);

      const result = await mockDb.query('SELECT * FROM quiz_question_options WHERE id = ?', ['opt1']);
      const option = result[0];

      // 验证选项只包含内容和基础属性
      expect(option.id).toBeTruthy();
      expect(option.question_id).toBeTruthy();
      expect(option.option_text).toBeTruthy();
      expect(option.option_value).toBeTruthy();
      expect(option.scoring_value).toBeDefined();
      
      // 确保不包含展现相关字段
      expect(option.emoji).toBeUndefined();
      expect(option.icon_url).toBeUndefined();
      expect(option.color).toBeUndefined();
      expect(option.background_image).toBeUndefined();
    });
  });

  describe('2. 展现层独立性验证', () => {
    it('应该验证展现配置完全独立存储', async () => {
      const mockPresentationConfig = {
        id: 'config1',
        quiz_pack_id: 'pack123',
        default_emoji_set: JSON.stringify({
          'high_energy': '⚡',
          'medium_energy': '🔋',
          'low_energy': '🪫'
        }),
        presentation_config: JSON.stringify({
          theme: 'tcm_traditional',
          color_scheme: 'earth_tones',
          layout: 'circular'
        }),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      mockDb.query.mockResolvedValueOnce([mockPresentationConfig]);

      const result = await mockDb.query('SELECT * FROM pack_presentation_configs WHERE quiz_pack_id = ?', ['pack123']);
      const config = result[0];

      // 验证展现配置独立存储
      expect(config.id).toBeTruthy();
      expect(config.quiz_pack_id).toBeTruthy();
      expect(config.default_emoji_set).toBeTruthy();
      expect(config.presentation_config).toBeTruthy();
      
      // 验证配置内容是有效的JSON
      expect(() => JSON.parse(config.default_emoji_set)).not.toThrow();
      expect(() => JSON.parse(config.presentation_config)).not.toThrow();
    });

    it('应该验证用户个性化展现配置独立', async () => {
      const mockUserOverride = {
        id: 'override1',
        user_id: 'user123',
        question_id: 'q1',
        emoji_mapping: JSON.stringify({
          'high_energy': '🚀',
          'medium_energy': '🌟',
          'low_energy': '💤'
        }),
        custom_config: JSON.stringify({
          animation_speed: 'slow',
          sound_enabled: false
        }),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      mockDb.query.mockResolvedValueOnce([mockUserOverride]);

      const result = await mockDb.query('SELECT * FROM question_presentation_overrides WHERE user_id = ? AND question_id = ?', ['user123', 'q1']);
      const override = result[0];

      // 验证用户个性化配置独立存储
      expect(override.id).toBeTruthy();
      expect(override.user_id).toBeTruthy();
      expect(override.question_id).toBeTruthy();
      expect(override.emoji_mapping).toBeTruthy();
      
      // 验证配置内容是有效的JSON
      expect(() => JSON.parse(override.emoji_mapping)).not.toThrow();
      expect(() => JSON.parse(override.custom_config)).not.toThrow();
    });
  });

  describe('3. 6层配置架构验证', () => {
    it('应该验证6层配置的层级关系', async () => {
      const mockUserPreferences = {
        id: 'pref1',
        user_id: 'user123',
        quiz_pack_id: 'pack123',
        layer_0_config: JSON.stringify({ datasetId: 'tcm-emotions' }),
        layer_1_config: JSON.stringify({ selectedEmojiSet: 'traditional' }),
        layer_2_config: JSON.stringify({ renderStrategy: 'optimized' }),
        layer_3_config: JSON.stringify({ skinTheme: 'light' }),
        layer_4_config: JSON.stringify({ viewDetails: 'expanded', emotionDisplay: 'icons' }),
        layer_5_config: JSON.stringify({ accessibility: 'enhanced' }),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      mockDb.query.mockResolvedValueOnce([mockUserPreferences]);

      const result = await mockDb.query('SELECT * FROM user_quiz_preferences WHERE user_id = ? AND quiz_pack_id = ?', ['user123', 'pack123']);
      const preferences = result[0];

      // 验证所有6层配置都存在
      expect(preferences.layer_0_config).toBeTruthy();
      expect(preferences.layer_1_config).toBeTruthy();
      expect(preferences.layer_2_config).toBeTruthy();
      expect(preferences.layer_3_config).toBeTruthy();
      expect(preferences.layer_4_config).toBeTruthy();
      expect(preferences.layer_5_config).toBeTruthy();

      // 验证每层配置都是有效的JSON
      for (let i = 0; i <= 5; i++) {
        const layerConfig = preferences[`layer_${i}_config`];
        expect(() => JSON.parse(layerConfig)).not.toThrow();
      }
    });

    it('应该验证配置层级的优先级顺序', async () => {
      // 模拟配置合并逻辑
      const baseConfig = { theme: 'default', emoji: '😊' };
      const layer1Config = { emoji: '🎉' }; // 覆盖emoji
      const layer2Config = { animation: 'bounce' }; // 新增animation
      
      // 模拟配置合并
      const mergedConfig = {
        ...baseConfig,
        ...layer1Config,
        ...layer2Config
      };

      expect(mergedConfig.theme).toBe('default'); // 保持基础配置
      expect(mergedConfig.emoji).toBe('🎉'); // 被layer1覆盖
      expect(mergedConfig.animation).toBe('bounce'); // layer2新增
    });
  });

  describe('4. 数据完整性验证', () => {
    it('应该验证数据与展现的关联完整性', async () => {
      // 验证Quiz包存在对应的展现配置
      const quizPackId = 'pack123';
      
      mockDb.query
        .mockResolvedValueOnce([{ id: quizPackId, name: 'Test Pack' }]) // Quiz包存在
        .mockResolvedValueOnce([{ id: 'config1', quiz_pack_id: quizPackId }]); // 展现配置存在

      const packResult = await mockDb.query('SELECT * FROM quiz_packs WHERE id = ?', [quizPackId]);
      const configResult = await mockDb.query('SELECT * FROM pack_presentation_configs WHERE quiz_pack_id = ?', [quizPackId]);

      expect(packResult).toHaveLength(1);
      expect(configResult).toHaveLength(1);
      expect(configResult[0].quiz_pack_id).toBe(quizPackId);
    });

    it('应该验证展现配置不影响数据查询', async () => {
      // 验证即使没有展现配置，数据查询仍然正常
      const quizPackId = 'pack456';
      
      mockDb.query
        .mockResolvedValueOnce([{ id: quizPackId, name: 'Data Only Pack' }]) // Quiz包存在
        .mockResolvedValueOnce([]); // 没有展现配置

      const packResult = await mockDb.query('SELECT * FROM quiz_packs WHERE id = ?', [quizPackId]);
      const configResult = await mockDb.query('SELECT * FROM pack_presentation_configs WHERE quiz_pack_id = ?', [quizPackId]);

      // 数据查询正常
      expect(packResult).toHaveLength(1);
      expect(packResult[0].id).toBe(quizPackId);
      
      // 没有展现配置也不影响数据使用
      expect(configResult).toHaveLength(0);
    });
  });

  describe('5. 架构原则验证', () => {
    it('应该验证Quiz包作为数据集的正确使用', async () => {
      const mockQuizPack = {
        id: 'tcm-pack',
        name: 'TCM Emotion Assessment',
        description: 'Traditional Chinese Medicine based emotion assessment',
        category: 'medical',
        version: '2.0.0',
        metadata: JSON.stringify({
          source: 'tcm_research',
          validation_status: 'peer_reviewed',
          language_support: ['zh', 'en']
        })
      };

      mockDb.query.mockResolvedValueOnce([mockQuizPack]);

      const result = await mockDb.query('SELECT * FROM quiz_packs WHERE category = ?', ['medical']);
      const pack = result[0];

      // 验证Quiz包作为数据集的特征
      expect(pack.name).toBeTruthy();
      expect(pack.description).toBeTruthy();
      expect(pack.category).toBe('medical');
      expect(pack.version).toBeTruthy();
      expect(pack.metadata).toBeTruthy();
      
      // 验证metadata是有效的JSON
      const metadata = JSON.parse(pack.metadata);
      expect(metadata.source).toBeTruthy();
      expect(metadata.validation_status).toBeTruthy();
      expect(Array.isArray(metadata.language_support)).toBe(true);
    });

    it('应该验证16个组件系统的完整性', async () => {
      // 模拟16个核心组件的存在性检查
      const expectedComponents = [
        'QuizEngine', 'QuestionRenderer', 'OptionSelector', 'ProgressTracker',
        'ResultCalculator', 'EmojiMapper', 'SkinManager', 'ConfigMerger',
        'UserPreferences', 'PresentationLayer', 'DataLayer', 'ValidationEngine',
        'TranslationService', 'AccessibilityHelper', 'AnalyticsCollector', 'SyncCoordinator'
      ];

      // 验证组件数量
      expect(expectedComponents).toHaveLength(16);
      
      // 验证每个组件名称都有意义
      expectedComponents.forEach(component => {
        expect(component).toBeTruthy();
        expect(typeof component).toBe('string');
        expect(component.length).toBeGreaterThan(5);
      });
    });
  });
});
