/**
 * Quiz管理页面专用Hook
 * 提供Quiz包、问题、选项的CRUD操作和云端同步功能
 */

import { useState, useCallback } from 'react';
import { Services } from '@/services';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { QuizPack, QuizQuestion, QuizQuestionOption } from '@/types/schema/base';
import { toast } from 'sonner';

export interface QuizManagementState {
  // 数据状态
  quizPacks: QuizPack[];
  questions: QuizQuestion[];
  options: QuizQuestionOption[];
  
  // 选择状态
  selectedPack: QuizPack | null;
  selectedQuestion: QuizQuestion | null;
  
  // 加载状态
  isLoading: boolean;
  isSyncing: boolean;
  error: string | null;
  
  // 同步状态
  lastSyncTime: Date | null;
}

export interface QuizManagementActions {
  // 数据加载
  loadQuizPacks: () => Promise<void>;
  loadQuestions: (packId: string) => Promise<void>;
  loadOptions: (questionId: string) => Promise<void>;
  
  // 选择操作
  selectPack: (pack: QuizPack) => void;
  selectQuestion: (question: QuizQuestion) => void;
  clearSelection: () => void;
  
  // CRUD操作
  createQuizPack: (packData: Partial<QuizPack>) => Promise<boolean>;
  updateQuizPack: (packId: string, updates: Partial<QuizPack>) => Promise<boolean>;
  deleteQuizPack: (packId: string) => Promise<boolean>;
  
  createQuestion: (questionData: Partial<QuizQuestion>) => Promise<boolean>;
  updateQuestion: (questionId: string, updates: Partial<QuizQuestion>) => Promise<boolean>;
  deleteQuestion: (questionId: string) => Promise<boolean>;
  
  createOption: (optionData: Partial<QuizQuestionOption>) => Promise<boolean>;
  updateOption: (optionId: string, updates: Partial<QuizQuestionOption>) => Promise<boolean>;
  deleteOption: (optionId: string) => Promise<boolean>;
  
  // 云端同步
  syncToCloud: () => Promise<void>;
  downloadFromCloud: () => Promise<void>;
  
  // 批量操作
  importFromCSV: (csvData: string) => Promise<boolean>;
  exportToCSV: () => Promise<string>;
  
  // 搜索和过滤
  searchQuizPacks: (searchTerm: string) => Promise<QuizPack[]>;
  filterQuestionsByType: (quizType: string) => QuizQuestion[];
}

export function useQuizManagement(): QuizManagementState & QuizManagementActions {
  const { isOnline } = useNetworkStatus();
  
  const [state, setState] = useState<QuizManagementState>({
    quizPacks: [],
    questions: [],
    options: [],
    selectedPack: null,
    selectedQuestion: null,
    isLoading: false,
    isSyncing: false,
    error: null,
    lastSyncTime: null,
  });

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<QuizManagementState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 加载Quiz包
  const loadQuizPacks = useCallback(async () => {
    try {
      updateState({ isLoading: true, error: null });
      
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.getAll();
      
      if (result.success) {
        updateState({ 
          quizPacks: result.data,
          isLoading: false 
        });
      } else {
        throw new Error(result.error || 'Failed to load quiz packs');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load quiz packs';
      updateState({ 
        error: errorMessage,
        isLoading: false 
      });
      toast.error(errorMessage);
    }
  }, [updateState]);

  // 加载问题
  const loadQuestions = useCallback(async (packId: string) => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.getQuizPackQuestions(packId);
      
      if (result.success) {
        updateState({ questions: result.data });
      } else {
        toast.error('Failed to load questions: ' + result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load questions';
      toast.error(errorMessage);
    }
  }, [updateState]);

  // 加载选项
  const loadOptions = useCallback(async (questionId: string) => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.getQuestionOptions(questionId);
      
      if (result.success) {
        updateState({ options: result.data });
      } else {
        toast.error('Failed to load options: ' + result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load options';
      toast.error(errorMessage);
    }
  }, [updateState]);

  // 选择操作
  const selectPack = useCallback((pack: QuizPack) => {
    updateState({ 
      selectedPack: pack,
      selectedQuestion: null,
      questions: [],
      options: []
    });
    loadQuestions(pack.id);
  }, [updateState, loadQuestions]);

  const selectQuestion = useCallback((question: QuizQuestion) => {
    updateState({ 
      selectedQuestion: question,
      options: []
    });
    loadOptions(question.id);
  }, [updateState, loadOptions]);

  const clearSelection = useCallback(() => {
    updateState({
      selectedPack: null,
      selectedQuestion: null,
      questions: [],
      options: []
    });
  }, [updateState]);

  // CRUD操作 - Quiz包
  const createQuizPack = useCallback(async (packData: Partial<QuizPack>): Promise<boolean> => {
    try {
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.create(packData as any);
      
      if (result.success) {
        toast.success('Quiz包创建成功');
        await loadQuizPacks();
        return true;
      } else {
        toast.error('创建失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create quiz pack';
      toast.error(errorMessage);
      return false;
    }
  }, [loadQuizPacks]);

  const updateQuizPack = useCallback(async (packId: string, updates: Partial<QuizPack>): Promise<boolean> => {
    try {
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.update(packId, updates as any);
      
      if (result.success) {
        toast.success('Quiz包更新成功');
        await loadQuizPacks();
        return true;
      } else {
        toast.error('更新失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update quiz pack';
      toast.error(errorMessage);
      return false;
    }
  }, [loadQuizPacks]);

  const deleteQuizPack = useCallback(async (packId: string): Promise<boolean> => {
    try {
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.delete(packId);
      
      if (result.success) {
        toast.success('Quiz包删除成功');
        await loadQuizPacks();
        clearSelection();
        return true;
      } else {
        toast.error('删除失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete quiz pack';
      toast.error(errorMessage);
      return false;
    }
  }, [loadQuizPacks, clearSelection]);

  // CRUD操作 - 问题
  const createQuestion = useCallback(async (questionData: Partial<QuizQuestion>): Promise<boolean> => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.create(questionData as any);
      
      if (result.success) {
        toast.success('问题创建成功');
        if (state.selectedPack) {
          await loadQuestions(state.selectedPack.id);
        }
        return true;
      } else {
        toast.error('创建失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create question';
      toast.error(errorMessage);
      return false;
    }
  }, [state.selectedPack, loadQuestions]);

  const updateQuestion = useCallback(async (questionId: string, updates: Partial<QuizQuestion>): Promise<boolean> => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.update(questionId, updates as any);
      
      if (result.success) {
        toast.success('问题更新成功');
        if (state.selectedPack) {
          await loadQuestions(state.selectedPack.id);
        }
        return true;
      } else {
        toast.error('更新失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update question';
      toast.error(errorMessage);
      return false;
    }
  }, [state.selectedPack, loadQuestions]);

  const deleteQuestion = useCallback(async (questionId: string): Promise<boolean> => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.delete(questionId);
      
      if (result.success) {
        toast.success('问题删除成功');
        if (state.selectedPack) {
          await loadQuestions(state.selectedPack.id);
        }
        updateState({ selectedQuestion: null, options: [] });
        return true;
      } else {
        toast.error('删除失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete question';
      toast.error(errorMessage);
      return false;
    }
  }, [state.selectedPack, loadQuestions, updateState]);

  // CRUD操作 - 选项
  const createOption = useCallback(async (optionData: Partial<QuizQuestionOption>): Promise<boolean> => {
    try {
      const quizQuestionOptionService = await Services.quizQuestionOption();
      const result = await quizQuestionOptionService.create(optionData as any);
      
      if (result.success) {
        toast.success('选项创建成功');
        if (state.selectedQuestion) {
          await loadOptions(state.selectedQuestion.id);
        }
        return true;
      } else {
        toast.error('创建失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create option';
      toast.error(errorMessage);
      return false;
    }
  }, [state.selectedQuestion, loadOptions]);

  const updateOption = useCallback(async (optionId: string, updates: Partial<QuizQuestionOption>): Promise<boolean> => {
    try {
      const quizQuestionOptionService = await Services.quizQuestionOption();
      const result = await quizQuestionOptionService.update(optionId, updates as any);
      
      if (result.success) {
        toast.success('选项更新成功');
        if (state.selectedQuestion) {
          await loadOptions(state.selectedQuestion.id);
        }
        return true;
      } else {
        toast.error('更新失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update option';
      toast.error(errorMessage);
      return false;
    }
  }, [state.selectedQuestion, loadOptions]);

  const deleteOption = useCallback(async (optionId: string): Promise<boolean> => {
    try {
      const quizQuestionOptionService = await Services.quizQuestionOption();
      const result = await quizQuestionOptionService.delete(optionId);
      
      if (result.success) {
        toast.success('选项删除成功');
        if (state.selectedQuestion) {
          await loadOptions(state.selectedQuestion.id);
        }
        return true;
      } else {
        toast.error('删除失败: ' + result.error);
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete option';
      toast.error(errorMessage);
      return false;
    }
  }, [state.selectedQuestion, loadOptions]);

  // 云端同步功能
  const syncToCloud = useCallback(async () => {
    if (!isOnline) {
      toast.error('网络未连接，无法同步到云端');
      return;
    }

    try {
      updateState({ isSyncing: true });
      
      // TODO: 实现具体的云端同步逻辑
      console.log('[useQuizManagement] Syncing to cloud...');
      
      // 模拟同步过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      updateState({ 
        isSyncing: false,
        lastSyncTime: new Date()
      });
      toast.success('数据已同步到云端');
    } catch (error) {
      console.error('Failed to sync to cloud:', error);
      updateState({ isSyncing: false });
      toast.error('同步到云端失败');
    }
  }, [isOnline, updateState]);

  const downloadFromCloud = useCallback(async () => {
    if (!isOnline) {
      toast.error('网络未连接，无法从云端下载');
      return;
    }

    try {
      updateState({ isLoading: true });
      
      // TODO: 实现具体的云端下载逻辑
      console.log('[useQuizManagement] Downloading from cloud...');
      
      // 模拟下载过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 重新加载本地数据
      await loadQuizPacks();
      
      updateState({ 
        lastSyncTime: new Date()
      });
      toast.success('已从云端下载最新数据');
    } catch (error) {
      console.error('Failed to download from cloud:', error);
      updateState({ isLoading: false });
      toast.error('从云端下载失败');
    }
  }, [isOnline, updateState, loadQuizPacks]);

  // 批量操作
  const importFromCSV = useCallback(async (csvData: string): Promise<boolean> => {
    try {
      // TODO: 实现CSV导入逻辑
      console.log('[useQuizManagement] Importing from CSV...');
      
      // 模拟导入过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      await loadQuizPacks();
      toast.success('CSV导入成功');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to import CSV';
      toast.error(errorMessage);
      return false;
    }
  }, [loadQuizPacks]);

  const exportToCSV = useCallback(async (): Promise<string> => {
    try {
      // TODO: 实现CSV导出逻辑
      console.log('[useQuizManagement] Exporting to CSV...');
      
      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const csvData = 'id,name,description\n' + 
        state.quizPacks.map(pack => 
          `${pack.id},${pack.name},${pack.description || ''}`
        ).join('\n');
      
      toast.success('CSV导出成功');
      return csvData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to export CSV';
      toast.error(errorMessage);
      return '';
    }
  }, [state.quizPacks]);

  // 搜索和过滤
  const searchQuizPacks = useCallback(async (searchTerm: string): Promise<QuizPack[]> => {
    try {
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.searchQuizPacks(searchTerm);
      
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Failed to search quiz packs:', error);
      return [];
    }
  }, []);

  const filterQuestionsByType = useCallback((quizType: string): QuizQuestion[] => {
    return state.questions.filter(question => 
      question.quiz_type === quizType || !quizType
    );
  }, [state.questions]);

  return {
    ...state,
    loadQuizPacks,
    loadQuestions,
    loadOptions,
    selectPack,
    selectQuestion,
    clearSelection,
    createQuizPack,
    updateQuizPack,
    deleteQuizPack,
    createQuestion,
    updateQuestion,
    deleteQuestion,
    createOption,
    updateOption,
    deleteOption,
    syncToCloud,
    downloadFromCloud,
    importFromCSV,
    exportToCSV,
    searchQuizPacks,
    filterQuestionsByType,
  };
}
