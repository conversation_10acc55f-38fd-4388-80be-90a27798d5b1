-- 中医体质问卷 - 19个独立证素量表包
-- 基于 docs/quiz/raw/raw-tcm-quiz.md 的中医证素分类

-- 1. 创建肝证素量表包
INSERT INTO quiz_packs (
  id, name, name_localized, description, description_localized,
  quiz_type, quiz_style, category, difficulty_level, 
  estimated_duration_minutes, tags, metadata, is_active,
  created_at, updated_at
) VALUES (
  'tcm_liver_syndrome',
  'Liver Syndrome Assessment',
  '{"zh-CN": "肝证素评估", "en-US": "Liver Syndrome Assessment"}',
  'Traditional Chinese Medicine liver syndrome element assessment',
  '{"zh-CN": "中医肝证素评估，通过15个相关症状评估肝脏功能状态", "en-US": "TCM liver syndrome assessment through 15 related symptoms"}',
  'tcm_assessment',
  'likert_scale',
  'tcm',
  3,
  8,
  '["tcm", "liver", "syndrome", "assessment", "emotion"]',
  '{
    "syndrome_element": "liver",
    "total_questions": 15,
    "scoring_method": "weighted_sum",
    "score_range": {"min": 0, "max": 735},
    "assessment_levels": {
      "normal": {"min": 0, "max": 100},
      "mild": {"min": 101, "max": 300},
      "moderate": {"min": 301, "max": 500},
      "severe": {"min": 501, "max": 735}
    },
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 2. 创建肾证素量表包
INSERT INTO quiz_packs (
  id, name, name_localized, description, description_localized,
  quiz_type, quiz_style, category, difficulty_level, 
  estimated_duration_minutes, tags, metadata, is_active,
  created_at, updated_at
) VALUES (
  'tcm_kidney_syndrome',
  'Kidney Syndrome Assessment', 
  '{"zh-CN": "肾证素评估", "en-US": "Kidney Syndrome Assessment"}',
  'Traditional Chinese Medicine kidney syndrome element assessment',
  '{"zh-CN": "中医肾证素评估，通过13个相关症状评估肾脏功能状态", "en-US": "TCM kidney syndrome assessment through 13 related symptoms"}',
  'tcm_assessment',
  'likert_scale',
  'tcm',
  3,
  7,
  '["tcm", "kidney", "syndrome", "assessment", "urinary"]',
  '{
    "syndrome_element": "kidney",
    "total_questions": 13,
    "scoring_method": "weighted_sum",
    "score_range": {"min": 0, "max": 1131},
    "assessment_levels": {
      "normal": {"min": 0, "max": 150},
      "mild": {"min": 151, "max": 400},
      "moderate": {"min": 401, "max": 700},
      "severe": {"min": 701, "max": 1131}
    },
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 3. 创建气虚证素量表包
INSERT INTO quiz_packs (
  id, name, name_localized, description, description_localized,
  quiz_type, quiz_style, category, difficulty_level, 
  estimated_duration_minutes, tags, metadata, is_active,
  created_at, updated_at
) VALUES (
  'tcm_qi_deficiency',
  'Qi Deficiency Assessment',
  '{"zh-CN": "气虚证素评估", "en-US": "Qi Deficiency Assessment"}',
  'Traditional Chinese Medicine qi deficiency syndrome assessment',
  '{"zh-CN": "中医气虚证素评估，通过9个相关症状评估气虚状态", "en-US": "TCM qi deficiency assessment through 9 related symptoms"}',
  'tcm_assessment',
  'likert_scale',
  'tcm',
  3,
  5,
  '["tcm", "qi", "deficiency", "energy", "fatigue"]',
  '{
    "syndrome_element": "qi_deficiency",
    "total_questions": 9,
    "scoring_method": "weighted_sum",
    "score_range": {"min": -37.5, "max": 615},
    "assessment_levels": {
      "normal": {"min": -37.5, "max": 50},
      "mild": {"min": 51, "max": 200},
      "moderate": {"min": 201, "max": 400},
      "severe": {"min": 401, "max": 615}
    },
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5},
    "has_negative_scores": true
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 4. 创建肝证素问题

-- 问题1: 情绪急躁
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, metadata, created_at, updated_at
) VALUES (
  'tcm_liver_q001',
  'tcm_liver_syndrome',
  'Do you easily become irritable or angry?',
  '{"zh-CN": "您容易急躁或发怒吗？", "en-US": "Do you easily become irritable or angry?"}',
  'Assess emotional irritability and anger tendency',
  'likert_scale',
  1,
  1,
  '{
    "syndrome_element": "liver",
    "base_score": 31,
    "symptom_category": "emotional",
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 问题2: 情绪低沉
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, metadata, created_at, updated_at
) VALUES (
  'tcm_liver_q002',
  'tcm_liver_syndrome',
  'Do you feel depressed or in low spirits?',
  '{"zh-CN": "您感到闷闷不乐，情绪低沉吗？", "en-US": "Do you feel depressed or in low spirits?"}',
  'Assess emotional depression and low mood',
  'likert_scale',
  2,
  1,
  '{
    "syndrome_element": "liver",
    "base_score": 40,
    "symptom_category": "emotional",
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 问题3: 经常叹气
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, metadata, created_at, updated_at
) VALUES (
  'tcm_liver_q003',
  'tcm_liver_syndrome',
  'Do you often sigh?',
  '{"zh-CN": "您经常叹气吗？", "en-US": "Do you often sigh?"}',
  'Assess frequency of sighing behavior',
  'likert_scale',
  3,
  1,
  '{
    "syndrome_element": "liver",
    "base_score": 40,
    "symptom_category": "emotional",
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 性别限制问题示例: 月经不调
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, condition_logic, metadata, created_at, updated_at
) VALUES (
  'tcm_liver_q015',
  'tcm_liver_syndrome',
  'Do you experience irregular menstrual cycles?',
  '{"zh-CN": "您会出现每次月经相隔时间都不确定，或提前或延后，或长或短吗？", "en-US": "Do you experience irregular menstrual cycles?"}',
  'Assess menstrual irregularity (female only)',
  'likert_scale',
  15,
  0,
  '{"show_if": {"user_profile": "gender", "value": "female"}}',
  '{
    "syndrome_element": "liver",
    "base_score": 38,
    "symptom_category": "reproductive",
    "gender_restriction": "female",
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 5. 创建肾证素问题

-- 问题1: 下肢发冷
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, metadata, created_at, updated_at
) VALUES (
  'tcm_kidney_q001',
  'tcm_kidney_syndrome',
  'Do you experience cold sensation in lower limbs?',
  '{"zh-CN": "您会出现下肢发冷吗？", "en-US": "Do you experience cold sensation in lower limbs?"}',
  'Assess cold sensation in legs and feet',
  'likert_scale',
  1,
  1,
  '{
    "syndrome_element": "kidney",
    "base_score": 25,
    "symptom_category": "circulation",
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 问题2: 腰膝酸软
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, metadata, created_at, updated_at
) VALUES (
  'tcm_kidney_q007',
  'tcm_kidney_syndrome',
  'Do you experience soreness and weakness in waist and knees?',
  '{"zh-CN": "您会出现腰膝酸软或腰部酸痛吗？", "en-US": "Do you experience soreness and weakness in waist and knees?"}',
  'Assess lower back and knee weakness',
  'likert_scale',
  7,
  1,
  '{
    "syndrome_element": "kidney",
    "base_score": 35,
    "symptom_category": "musculoskeletal",
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 性别限制问题: 遗精
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, condition_logic, metadata, created_at, updated_at
) VALUES (
  'tcm_kidney_q013',
  'tcm_kidney_syndrome',
  'Do you experience nocturnal emission without sexual activity or dreams?',
  '{"zh-CN": "您会出现不因性交、做梦而泄出精液的情况吗？", "en-US": "Do you experience nocturnal emission without sexual activity or dreams?"}',
  'Assess involuntary semen emission (male only)',
  'likert_scale',
  13,
  0,
  '{"show_if": {"user_profile": "gender", "value": "male"}}',
  '{
    "syndrome_element": "kidney",
    "base_score": 38,
    "symptom_category": "reproductive",
    "gender_restriction": "male",
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 6. 创建气虚证素问题 (包含负分值示例)

-- 问题1: 怕风
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, metadata, created_at, updated_at
) VALUES (
  'tcm_qi_def_q001',
  'tcm_qi_deficiency',
  'Do you fear wind or feel cold when exposed to wind?',
  '{"zh-CN": "您有出现自觉怕风，或遇风则怕冷战栗，避风则缓吗？", "en-US": "Do you fear wind or feel cold when exposed to wind?"}',
  'Assess wind sensitivity and cold aversion',
  'likert_scale',
  1,
  1,
  '{
    "syndrome_element": "qi_deficiency",
    "base_score": 31,
    "symptom_category": "environmental_sensitivity",
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 负分值问题示例: 气喘
INSERT INTO quiz_questions (
  id, pack_id, question_text, question_text_localized,
  question_description, question_type, order_index, 
  is_required, metadata, created_at, updated_at
) VALUES (
  'tcm_qi_def_q008',
  'tcm_qi_deficiency',
  'Do you experience shortness of breath or asthma?',
  '{"zh-CN": "您有出现气喘或气短吗？", "en-US": "Do you experience shortness of breath or asthma?"}',
  'Assess breathing difficulties (negative scoring)',
  'likert_scale',
  8,
  1,
  '{
    "syndrome_element": "qi_deficiency",
    "base_score": -25,
    "symptom_category": "respiratory",
    "is_negative_score": true,
    "weight_coefficients": {"none": 0, "mild": 0.7, "moderate": 1.0, "severe": 1.5}
  }',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 7. 创建标准化TCM选项 (适用于所有TCM问题)

-- 为肝证素问题创建选项
INSERT INTO quiz_question_options (
  id, question_id, option_text, option_text_localized,
  option_value, order_index, metadata, created_at, updated_at
) VALUES 
-- 肝证素问题1的选项
('tcm_liver_q001_none', 'tcm_liver_q001', 'None', '{"zh-CN": "无", "en-US": "None"}', 'none', 1, 
 '{"weight_coefficient": 0, "score_multiplier": 0, "description": "无此症状"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tcm_liver_q001_mild', 'tcm_liver_q001', 'Mild', '{"zh-CN": "轻", "en-US": "Mild"}', 'mild', 2,
 '{"weight_coefficient": 0.7, "score_multiplier": 0.7, "description": "症状轻微"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tcm_liver_q001_moderate', 'tcm_liver_q001', 'Moderate', '{"zh-CN": "中", "en-US": "Moderate"}', 'moderate', 3,
 '{"weight_coefficient": 1.0, "score_multiplier": 1.0, "description": "症状中等"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tcm_liver_q001_severe', 'tcm_liver_q001', 'Severe', '{"zh-CN": "重", "en-US": "Severe"}', 'severe', 4,
 '{"weight_coefficient": 1.5, "score_multiplier": 1.5, "description": "症状严重"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 肝证素问题2的选项
('tcm_liver_q002_none', 'tcm_liver_q002', 'None', '{"zh-CN": "无", "en-US": "None"}', 'none', 1,
 '{"weight_coefficient": 0, "score_multiplier": 0, "description": "无此症状"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tcm_liver_q002_mild', 'tcm_liver_q002', 'Mild', '{"zh-CN": "轻", "en-US": "Mild"}', 'mild', 2,
 '{"weight_coefficient": 0.7, "score_multiplier": 0.7, "description": "症状轻微"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tcm_liver_q002_moderate', 'tcm_liver_q002', 'Moderate', '{"zh-CN": "中", "en-US": "Moderate"}', 'moderate', 3,
 '{"weight_coefficient": 1.0, "score_multiplier": 1.0, "description": "症状中等"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tcm_liver_q002_severe', 'tcm_liver_q002', 'Severe', '{"zh-CN": "重", "en-US": "Severe"}', 'severe', 4,
 '{"weight_coefficient": 1.5, "score_multiplier": 1.5, "description": "症状严重"}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 注意：这里只展示了部分数据结构示例
-- 完整的数据需要包含所有19个证素量表包和191个问题
-- 每个问题都需要4个标准化选项 (无、轻、中、重)
-- 可以通过脚本批量生成剩余的数据
