# Quiz管理系统测试验证

本文档提供Quiz管理系统的完整测试验证清单。

## 🚀 快速测试步骤

### 1. 访问页面
```bash
# 确保开发服务器运行
npm run dev

# 在浏览器中访问
http://localhost:4080/quiz-management
# 或兼容路径
http://localhost:4080/emotion-data-editor
```

### 2. 页面加载验证
- [ ] 页面正常加载，无控制台错误
- [ ] 标题显示："Quiz量表管理"
- [ ] 四个标签正确显示：Quiz包管理、问题管理、选项管理、CSV导入
- [ ] 默认显示Quiz包管理标签

## 📋 功能测试清单

### Quiz包管理测试

#### 基础功能
- [ ] "创建新Quiz包"按钮正常显示
- [ ] 点击创建按钮弹出表单
- [ ] 表单包含所有必要字段：
  - [ ] 包名称（必填）
  - [ ] 描述
  - [ ] Quiz类型下拉选择
  - [ ] 类别选择
  - [ ] 难度等级（1-5）
  - [ ] 预计时长
  - [ ] 激活状态复选框
  - [ ] 公开可见复选框

#### 创建Quiz包
- [ ] 填写必填字段后可以成功创建
- [ ] 创建成功后显示成功提示
- [ ] 新创建的包出现在列表中
- [ ] 包卡片显示正确信息：
  - [ ] 包名称
  - [ ] 描述
  - [ ] 状态徽章（活跃、公开、类型）
  - [ ] 元信息（类别、难度、时长）

#### 包操作功能
- [ ] "管理问题"按钮正常工作
- [ ] "编辑"按钮打开编辑表单
- [ ] "复制"按钮创建包副本
- [ ] "删除"按钮显示确认对话框
- [ ] 删除确认后包被移除

### 问题管理测试

#### 导航测试
- [ ] 选择Quiz包后自动切换到问题管理标签
- [ ] 面包屑导航显示当前包名
- [ ] 问题管理标签显示问题数量徽章

#### 问题创建
- [ ] "创建新问题"按钮正常显示
- [ ] 表单包含所有字段：
  - [ ] 问题文本（必填）
  - [ ] 问题类型选择
  - [ ] 问题顺序
  - [ ] 问题分组
  - [ ] 层级等级
  - [ ] 必填问题复选框
  - [ ] 激活状态复选框

#### 问题操作
- [ ] 创建问题成功
- [ ] 问题列表正确显示
- [ ] 问题卡片显示：
  - [ ] 问题顺序号
  - [ ] 问题文本
  - [ ] 状态徽章
  - [ ] 分组信息（如有）
- [ ] "管理选项"按钮正常工作
- [ ] 编辑、复制、删除功能正常
- [ ] 上下调整顺序功能正常

### 选项管理测试

#### 导航测试
- [ ] 选择问题后自动切换到选项管理标签
- [ ] 面包屑显示完整路径
- [ ] 选项管理标签显示选项数量

#### 选项创建
- [ ] "创建新选项"按钮正常显示
- [ ] 表单包含所有字段：
  - [ ] 选项文本（必填）
  - [ ] 选项值（必填）
  - [ ] 选项顺序
  - [ ] 评分值
  - [ ] Emoji输入
  - [ ] 颜色选择器
  - [ ] 描述文本框
  - [ ] 正确答案复选框
  - [ ] 激活状态复选框

#### 选项操作
- [ ] 创建选项成功
- [ ] 选项列表正确显示
- [ ] 选项卡片显示：
  - [ ] 选项顺序号
  - [ ] Emoji图标（带颜色背景）
  - [ ] 选项文本和值
  - [ ] 状态徽章
- [ ] 编辑、复制、删除功能正常
- [ ] 上下调整顺序功能正常

### CSV导入测试

#### 模板下载
- [ ] 四个模板下载按钮正常显示
- [ ] 点击"完整模板"下载CSV文件
- [ ] 点击"Quiz包模板"下载CSV文件
- [ ] 点击"问题模板"下载CSV文件
- [ ] 点击"选项模板"下载CSV文件
- [ ] 下载的文件格式正确，包含示例数据

#### 文件上传
- [ ] "选择CSV文件"按钮正常工作
- [ ] 文件选择器只接受.csv文件
- [ ] 选择文件后显示预览区域

#### 数据预览
- [ ] 预览区域显示解析统计：
  - [ ] Quiz包数量
  - [ ] 问题数量
  - [ ] 选项数量
- [ ] 如有错误，显示错误列表
- [ ] 预览操作按钮：
  - [ ] "取消"按钮清除预览
  - [ ] "开始导入"按钮（无错误时可用）

#### 导入执行
- [ ] 点击"开始导入"执行导入
- [ ] 导入过程显示进度提示
- [ ] 导入完成显示成功消息
- [ ] 导入成功后自动切换到Quiz包管理
- [ ] 导入的数据正确显示在列表中

## 🎨 界面测试

### 响应式设计
- [ ] 桌面端布局正常
- [ ] 平板端适配良好
- [ ] 移动端响应式正常
- [ ] 表单在小屏幕上可用

### 视觉效果
- [ ] 标签切换动画流畅
- [ ] 按钮悬停效果正常
- [ ] 卡片悬停效果正常
- [ ] 表单弹窗居中显示
- [ ] 颜色搭配协调

### 交互体验
- [ ] 点击反馈及时
- [ ] 加载状态显示
- [ ] 错误提示友好
- [ ] 成功提示清晰
- [ ] 确认对话框明确

## 🔧 技术测试

### 数据持久化
- [ ] 创建的数据正确保存到数据库
- [ ] 编辑的数据正确更新
- [ ] 删除操作正确执行
- [ ] 页面刷新后数据保持

### 错误处理
- [ ] 网络错误时显示友好提示
- [ ] 数据验证错误时显示具体信息
- [ ] 服务器错误时有降级处理
- [ ] 导入错误时显示详细错误列表

### 性能表现
- [ ] 页面加载速度 < 2秒
- [ ] 标签切换响应 < 500ms
- [ ] 表单提交响应 < 1秒
- [ ] CSV导入处理合理时间内完成

## 📊 数据验证测试

### 创建完整Quiz流程
1. **创建Quiz包**
   - [ ] 创建名为"测试情绪Quiz"的包
   - [ ] 设置类型为"emotion_wheel"
   - [ ] 设置难度为3，时长为10分钟

2. **添加问题**
   - [ ] 添加问题"请选择您当前的主要情绪"
   - [ ] 设置类型为"single_choice"
   - [ ] 设置为必填问题

3. **添加选项**
   - [ ] 添加选项"快乐" - 值"happy" - emoji"😊" - 颜色"#FFD700"
   - [ ] 添加选项"悲伤" - 值"sad" - emoji"😢" - 颜色"#4169E1"
   - [ ] 添加选项"愤怒" - 值"angry" - emoji"😠" - 颜色"#FF4500"

4. **验证数据完整性**
   - [ ] 在Quiz包列表中查看创建的包
   - [ ] 进入问题管理查看创建的问题
   - [ ] 进入选项管理查看创建的选项
   - [ ] 所有数据显示正确

### CSV导入测试
1. **准备测试数据**
   - [ ] 下载完整模板
   - [ ] 填写3个Quiz包的完整数据
   - [ ] 每个包包含2-3个问题
   - [ ] 每个问题包含3-5个选项

2. **执行导入**
   - [ ] 上传准备的CSV文件
   - [ ] 预览数据无错误
   - [ ] 执行导入操作
   - [ ] 导入成功完成

3. **验证导入结果**
   - [ ] 所有Quiz包正确创建
   - [ ] 所有问题正确关联到对应包
   - [ ] 所有选项正确关联到对应问题
   - [ ] 元数据（emoji、颜色）正确保存

## 🐛 边界情况测试

### 数据验证
- [ ] 空字符串输入处理
- [ ] 超长文本输入处理
- [ ] 特殊字符输入处理
- [ ] 重复数据处理
- [ ] 无效数据类型处理

### 操作限制
- [ ] 删除有关联数据的包时的处理
- [ ] 删除有选项的问题时的处理
- [ ] 修改已使用的Quiz包时的处理
- [ ] 并发编辑冲突处理

### CSV导入边界
- [ ] 空CSV文件处理
- [ ] 格式错误的CSV处理
- [ ] 超大CSV文件处理
- [ ] 包含无效数据的CSV处理
- [ ] 网络中断时的导入处理

## ✅ 验证完成标准

### 基础功能 (必须全部通过)
- [ ] Quiz包管理功能完整
- [ ] 问题管理功能完整
- [ ] 选项管理功能完整
- [ ] CSV导入功能正常

### 高级功能 (推荐通过)
- [ ] 响应式设计完美
- [ ] 错误处理完善
- [ ] 性能表现良好
- [ ] 数据验证严格

### 用户体验 (优秀标准)
- [ ] 界面美观现代
- [ ] 交互直观友好
- [ ] 反馈及时准确
- [ ] 整体体验流畅

## 📈 测试报告模板

### 测试信息
- **测试日期**: ___________
- **测试人员**: ___________
- **测试环境**: ___________
- **浏览器版本**: ___________

### 测试结果
- **通过项目**: _____ / _____
- **失败项目**: _____ / _____
- **跳过项目**: _____ / _____

### 主要问题
1. ___________
2. ___________
3. ___________

### 建议改进
1. ___________
2. ___________
3. ___________

### 总体评价
- [ ] 优秀 - 所有功能完美运行
- [ ] 良好 - 主要功能正常，有小问题
- [ ] 一般 - 基础功能可用，需要改进
- [ ] 差 - 存在重大问题，需要修复

---

**注意**: 此测试清单应该在每次重大更新后执行，确保Quiz管理系统的稳定性和可用性。
