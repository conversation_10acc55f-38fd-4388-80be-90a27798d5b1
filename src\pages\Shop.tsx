/**
 * 商店页面
 * 支持皮肤和表情集的购买和管理
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useAuth';
import { useShop } from '@/hooks/useShop';
import { useVip } from '@/hooks/useVip';
import type { ShopItem } from '@/hooks/useShop';
import { allViewTypes } from '@/types/schema/base';
import {
  AlertCircle,
  Check,
  Palette,
  RefreshCw,
  ShoppingCart,
  Smile,
  Sparkles,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
// import { ViewType } from '@/types';

/**
 * 商店页面
 */
export default function Shop() {
  const { t } = useLanguage();
  const { isAuthenticated, isOnline } = useAuth();
  const { isVip, vipPlans, purchaseVip } = useVip();
  const {
    skins,
    emojiSets,
    purchaseHistory,
    isLoading,
    error,
    refresh,
    forceSync,
    purchaseSkin,
    purchaseEmojiSet,
    activateSkin,
    activateEmojiSet,
    canPurchaseItem,
  } = useShop();

  // 获取当前激活的皮肤和表情集
  const activeSkin = skins.find(skin => skin.is_active);
  const activeEmojiSet = emojiSets.find(emojiSet => emojiSet.is_active);

  // 本地状态
  const [activeTab, setActiveTab] = useState('skins');
  const [filterOwned, setFilterOwned] = useState<boolean | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedViewType, setSelectedViewType] = useState<string | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('pm_test_card');
  const [isProcessing, setIsProcessing] = useState(false);

  // 网络状态提示
  const showOfflineWarning = !isOnline && isAuthenticated;

  // 处理刷新
  const handleRefresh = async () => {
    try {
      setIsProcessing(true);
      await refresh();
      toast.success(t('shop.refresh_success', { fallback: '数据已刷新' }));
    } catch (error) {
      toast.error(t('shop.refresh_error', { fallback: '刷新失败' }));
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理强制同步
  const handleForceSync = async () => {
    if (!isOnline) {
      toast.error(t('shop.sync_requires_internet', { fallback: '同步需要网络连接' }));
      return;
    }

    try {
      setIsProcessing(true);
      await forceSync();
      toast.success(t('shop.sync_success', { fallback: '同步成功' }));
    } catch (error) {
      toast.error(t('shop.sync_error', { fallback: '同步失败' }));
    } finally {
      setIsProcessing(false);
    }
  };

  // 过滤皮肤
  const filteredSkins = skins.filter((skin) => {
    // 按拥有状态过滤
    if (filterOwned === true && !skin.is_unlocked) return false;
    if (filterOwned === false && skin.is_unlocked) return false;

    // 按搜索词过滤
    if (searchTerm && !skin.name.toLowerCase().includes(searchTerm.toLowerCase())) return false;

    return true;
  });

  // 过滤表情集
  const filteredEmojiSets = emojiSets.filter((emojiSet) => {
    // 按拥有状态过滤
    if (filterOwned === true && !emojiSet.is_unlocked) return false;
    if (filterOwned === false && emojiSet.is_unlocked) return false;

    // 按搜索词过滤
    if (searchTerm && !emojiSet.name.toLowerCase().includes(searchTerm.toLowerCase())) return false;

    return true;
  });

  // 处理购买皮肤
  const handlePurchaseSkin = async (skinItem: any) => {
    if (!isAuthenticated) {
      toast.error(t('shop.login_required', { fallback: '请先登录' }));
      return;
    }

    if (!canPurchaseItem(skinItem)) {
      toast.error(t('shop.cannot_purchase', { fallback: '无法购买此商品' }));
      return;
    }

    try {
      setIsProcessing(true);
      const result = await purchaseSkin(skinItem.id, selectedPaymentMethod);

      if (result.success) {
        toast.success(
          t('shop.purchase_success', {
            name: skinItem.name,
            fallback: `成功购买 ${skinItem.name}`,
          })
        );
      } else {
        toast.error(result.error || t('shop.purchase_error', { fallback: '购买失败' }));
      }
    } catch (error) {
      console.error('Failed to purchase skin:', error);
      toast.error(t('shop.purchase_error', { fallback: '购买失败' }));
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理购买表情集
  const handlePurchaseEmojiSet = async (emojiSetItem: any) => {
    if (!isAuthenticated) {
      toast.error(t('shop.login_required', { fallback: '请先登录' }));
      return;
    }

    if (!canPurchaseItem(emojiSetItem)) {
      toast.error(t('shop.cannot_purchase', { fallback: '无法购买此商品' }));
      return;
    }

    try {
      setIsProcessing(true);
      const result = await purchaseEmojiSet(emojiSetItem.id, selectedPaymentMethod);

      if (result.success) {
        toast.success(
          t('shop.purchase_success', {
            name: emojiSetItem.name,
            fallback: `成功购买 ${emojiSetItem.name}`,
          })
        );
      } else {
        toast.error(result.error || t('shop.purchase_error', { fallback: '购买失败' }));
      }
    } catch (error) {
      console.error('Failed to purchase emoji set:', error);
      toast.error(t('shop.purchase_error', { fallback: '购买失败' }));
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理设置活动皮肤
  const handleSetActiveSkin = async (skinId: string) => {
    try {
      setIsProcessing(true);
      const result = await activateSkin(skinId);

      if (result.success) {
        toast.success(t('shop.skin_activated', { fallback: '皮肤已激活' }));
      } else {
        toast.error(result.error || t('shop.activation_error', { fallback: '激活失败' }));
      }
    } catch (error) {
      console.error('Failed to activate skin:', error);
      toast.error(t('shop.activation_error', { fallback: '激活失败' }));
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理设置活动表情集
  const handleSetActiveEmojiSet = async (emojiSetId: string) => {
    try {
      setIsProcessing(true);
      const result = await activateEmojiSet(emojiSetId);

      if (result.success) {
        toast.success(t('shop.emoji_set_activated', { fallback: '表情集已激活' }));
      } else {
        toast.error(result.error || t('shop.activation_error', { fallback: '激活失败' }));
      }
    } catch (error) {
      console.error('Failed to activate emoji set:', error);
      toast.error(t('shop.activation_error', { fallback: '激活失败' }));
    } finally {
      setIsProcessing(false);
    }
  };

  // 渲染表情集预览
  const renderEmojiSetPreview = (emojiSet: ShopItem) => {
    // ShopItem 类型不包含具体的表情项，显示简单预览
    return (
      <div className="flex flex-wrap gap-2 justify-center">
        <div className="text-muted-foreground text-sm">
          {t('shop.emoji_set_preview', { fallback: '表情集预览' })}
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">{t('shop.title', { fallback: '商店' })}</h1>
        <div className="flex items-center gap-2">
          {isLoading && <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />}
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isProcessing}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {t('shop.refresh', { fallback: '刷新' })}
          </Button>
          {isOnline && (
            <Button variant="outline" size="sm" onClick={handleForceSync} disabled={isProcessing}>
              {t('shop.sync', { fallback: '同步' })}
            </Button>
          )}
        </div>
      </div>

      {/* 状态提示 */}
      {showOfflineWarning && (
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t('shop.offline_warning', { fallback: '当前离线，部分功能可能不可用' })}
          </AlertDescription>
        </Alert>
      )}

      {!isAuthenticated && (
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t('shop.login_for_purchase', { fallback: '登录后可购买商品' })}
          </AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="skins" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            {t('shop.skins', { fallback: '皮肤' })}
          </TabsTrigger>
          <TabsTrigger value="emoji_sets" className="flex items-center gap-2">
            <Smile className="h-4 w-4" />
            {t('shop.emoji_sets', { fallback: '表情集' })}
          </TabsTrigger>
        </TabsList>

        {/* 过滤器 */}
        <div className="my-4 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">{t('shop.search', { fallback: '搜索' })}</Label>
              <Input
                id="search"
                placeholder={t('shop.search_placeholder', { fallback: '搜索名称...' })}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div>
              <Label>{t('shop.filter_owned', { fallback: '过滤拥有状态' })}</Label>
              <Select
                value={filterOwned === null ? 'all' : filterOwned ? 'owned' : 'not_owned'}
                onValueChange={(value) => {
                  if (value === 'all') setFilterOwned(null);
                  else if (value === 'owned') setFilterOwned(true);
                  else setFilterOwned(false);
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue
                    placeholder={t('shop.filter_placeholder', { fallback: '选择过滤条件' })}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('shop.all', { fallback: '全部' })}</SelectItem>
                  <SelectItem value="owned">{t('shop.owned', { fallback: '已拥有' })}</SelectItem>
                  <SelectItem value="not_owned">
                    {t('shop.not_owned', { fallback: '未拥有' })}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {activeTab === 'skins' && (
              <div>
                <Label>{t('shop.view_type', { fallback: '视图类型' })}</Label>
                <Select
                  value={selectedViewType || 'all'}
                  onValueChange={(value) => {
                    if (value === 'all') setSelectedViewType(null);
                    else setSelectedViewType(value);
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue
                      placeholder={t('shop.view_type_placeholder', { fallback: '选择视图类型' })}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('shop.all', { fallback: '全部' })}</SelectItem>
                    {allViewTypes.map((viewType: string) => (
                      <SelectItem key={viewType} value={viewType}>
                        {viewType}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>

        {/* 皮肤标签页 */}
        <TabsContent value="skins" className="space-y-4">
          {/* 推荐皮肤区域 */}
          <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-purple-600" />
                精选推荐皮肤
              </CardTitle>
              <CardDescription>
                支持最新内容显示模式的高品质皮肤，提供丰富的视觉体验
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* 推荐皮肤卡片 */}
                <div className="p-4 bg-white rounded-lg border border-purple-200">
                  <div className="space-y-2">
                    <h4 className="font-medium text-purple-900">多媒体增强皮肤</h4>
                    <p className="text-sm text-purple-700">
                      支持文本、图片、动画的全功能皮肤
                    </p>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline" className="text-xs">文本</Badge>
                      <Badge variant="outline" className="text-xs">图片</Badge>
                      <Badge variant="outline" className="text-xs">动画</Badge>
                      <Badge variant="outline" className="text-xs">富文本</Badge>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-white rounded-lg border border-purple-200">
                  <div className="space-y-2">
                    <h4 className="font-medium text-purple-900">表情专用皮肤</h4>
                    <p className="text-sm text-purple-700">
                      专为表情符号优化的轻量级皮肤
                    </p>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline" className="text-xs">表情</Badge>
                      <Badge variant="outline" className="text-xs">图标</Badge>
                      <Badge variant="outline" className="text-xs">动画</Badge>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-white rounded-lg border border-purple-200">
                  <div className="space-y-2">
                    <h4 className="font-medium text-purple-900">极简文本皮肤</h4>
                    <p className="text-sm text-purple-700">
                      专注于文本显示的简洁皮肤
                    </p>
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline" className="text-xs">文本</Badge>
                      <Badge variant="outline" className="text-xs">图标</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {filteredSkins.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                {t('shop.no_skins_found', { fallback: '没有找到符合条件的皮肤' })}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredSkins.map((skin: ShopItem) => {
                // ShopItem 类型不包含配置信息，这里使用特性数组
                const supported_view_types = skin.features || [];

                return (
                  <Card
                    key={skin.id}
                    className={`overflow-hidden ${skin.id === activeSkin?.id ? 'border-primary' : ''}`}
                  >
                    <CardHeader className="p-4">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{skin.name}</CardTitle>
                        <div className="flex gap-1">
                          {skin.is_unlocked && (
                            <Badge variant="outline" className="bg-green-500/10 text-green-500">
                              {t('shop.owned', { fallback: '已拥有' })}
                            </Badge>
                          )}
                          {skin.unlockCondition === 'vip' && (
                            <Badge variant="outline" className="bg-amber-500/10 text-amber-500">
                              <Sparkles className="h-3 w-3 mr-1" />
                              {t('shop.premium', { fallback: '高级' })}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <CardDescription>{skin.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
                        {skin.previewUrl ? (
                          <img
                            src={skin.previewUrl}
                            alt={skin.name}
                            className="w-full h-full object-cover rounded-md"
                          />
                        ) : (
                          <div className="text-muted-foreground">
                            {t('shop.no_preview', { fallback: '无预览可用' })}
                          </div>
                        )}
                      </div>
                      <div className="mt-4 space-y-2">
                        {/* 支持的视图类型 */}
                        <div className="flex flex-wrap gap-2">
                          {supported_view_types.map((viewType: string) => (
                            <Badge key={viewType} variant="secondary">
                              {viewType}
                            </Badge>
                          ))}
                        </div>

                        {/* 支持的内容类型 */}
                        <div className="flex flex-wrap gap-1">
                          {/* 根据皮肤类型显示支持的内容类型 */}
                          {skin.name.includes('多媒体') || skin.name.includes('增强') ? (
                            <>
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">文本</Badge>
                              <Badge variant="outline" className="text-xs bg-green-50 text-green-700">图片</Badge>
                              <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700">动画</Badge>
                              <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700">富文本</Badge>
                            </>
                          ) : skin.name.includes('表情') || skin.name.includes('emoji') ? (
                            <>
                              <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700">表情</Badge>
                              <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700">图标</Badge>
                              <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700">动画</Badge>
                            </>
                          ) : skin.name.includes('简约') || skin.name.includes('文本') ? (
                            <>
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">文本</Badge>
                              <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700">图标</Badge>
                            </>
                          ) : (
                            <>
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">文本</Badge>
                              <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700">表情</Badge>
                              <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700">图标</Badge>
                            </>
                          )}
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="p-4 pt-0 flex justify-between">
                      {skin.is_unlocked ? (
                        <Button
                          variant={skin.is_active ? 'default' : 'outline'}
                          onClick={() => handleSetActiveSkin(skin.id)}
                          disabled={skin.is_active}
                          className="w-full"
                        >
                          {skin.is_active ? (
                            <>
                              <Check className="h-4 w-4 mr-2" />{' '}
                              {t('shop.active', { fallback: '当前使用中' })}
                            </>
                          ) : (
                            t('shop.use', { fallback: '使用' })
                          )}
                        </Button>
                      ) : (
                        <Button
                          variant="default"
                          onClick={() => handlePurchaseSkin(skin)}
                          className="w-full"
                        >
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          {skin.price > 0
                            ? `${skin.price} ${t('shop.coins', { fallback: '金币' })}`
                            : t('shop.unlock', { fallback: '解锁' })}
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>

        {/* 表情集标签页 */}
        <TabsContent value="emoji_sets" className="space-y-4">
          {filteredEmojiSets.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                {t('shop.no_emoji_sets_found', { fallback: '没有找到符合条件的表情集' })}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredEmojiSets.map((emojiSet) => (
                <Card
                  key={emojiSet.id}
                  className={`overflow-hidden ${emojiSet.id === activeEmojiSet?.id ? 'border-primary' : ''}`}
                >
                  <CardHeader className="p-4">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{emojiSet.name}</CardTitle>
                      <div className="flex gap-1">
                        {emojiSet.is_unlocked && (
                          <Badge variant="outline" className="bg-green-500/10 text-green-500">
                            {t('shop.owned', { fallback: '已拥有' })}
                          </Badge>
                        )}
                        {emojiSet.unlockCondition === 'vip' && (
                          <Badge variant="outline" className="bg-blue-500/10 text-blue-500">
                            {t('shop.system', { fallback: '系统' })}
                          </Badge>
                        )}
                        {emojiSet.is_active && (
                          <Badge variant="outline" className="bg-purple-500/10 text-purple-500">
                            {t('shop.default', { fallback: '默认' })}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <CardDescription>{emojiSet.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className="bg-muted rounded-md p-4 flex items-center justify-center">
                      {renderEmojiSetPreview(emojiSet)}
                    </div>
                    <div className="mt-4 flex flex-wrap gap-2">
                      <Badge variant="secondary">{emojiSet.type || 'unicode'}</Badge>
                    </div>
                  </CardContent>
                  <CardFooter className="p-4 pt-0 flex justify-between">
                    {emojiSet.is_unlocked ? (
                      <Button
                        variant={emojiSet.is_active ? 'default' : 'outline'}
                        onClick={() => handleSetActiveEmojiSet(emojiSet.id)}
                        disabled={emojiSet.is_active}
                        className="w-full"
                      >
                        {emojiSet.is_active ? (
                          <>
                            <Check className="h-4 w-4 mr-2" />{' '}
                            {t('shop.active', { fallback: '当前使用中' })}
                          </>
                        ) : (
                          t('shop.use', { fallback: '使用' })
                        )}
                      </Button>
                    ) : (
                      <Button
                        variant="default"
                        onClick={() => handlePurchaseEmojiSet(emojiSet)}
                        className="w-full"
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        {emojiSet.price
                          ? `${emojiSet.price} ${t('shop.coins', { fallback: '金币' })}`
                          : t('shop.unlock', { fallback: '解锁' })}
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
