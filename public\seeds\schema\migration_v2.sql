-- Migration Script: v1 to v2 Quiz System
-- Migrates from old emotion-focused schema to new universal quiz system
-- IMPORTANT: Backup your database before running this migration!

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Start transaction for atomic migration
BEGIN TRANSACTION;

-- ============================================================================
-- MIGRATION METADATA
-- ============================================================================

-- Create migration tracking table if it doesn't exist
CREATE TABLE IF NOT EXISTS schema_migrations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    rollback_sql TEXT
);

-- Record this migration
INSERT OR REPLACE INTO schema_migrations (version, description, rollback_sql) VALUES (
    '2.0.0',
    'Migration to universal quiz system with data-presentation separation',
    '-- Rollback instructions would go here'
);

-- ============================================================================
-- BACKUP EXISTING DATA
-- ============================================================================

-- Create backup tables for critical data
CREATE TABLE IF NOT EXISTS backup_emotion_data_sets AS SELECT * FROM emotion_data_sets WHERE 1=0;
CREATE TABLE IF NOT EXISTS backup_emotion_data_set_tiers AS SELECT * FROM emotion_data_set_tiers WHERE 1=0;
CREATE TABLE IF NOT EXISTS backup_user_configs AS SELECT * FROM user_configs WHERE 1=0;

-- Backup existing data
INSERT INTO backup_emotion_data_sets SELECT * FROM emotion_data_sets;
INSERT INTO backup_emotion_data_set_tiers SELECT * FROM emotion_data_set_tiers;
INSERT INTO backup_user_configs SELECT * FROM user_configs;

-- ============================================================================
-- CREATE NEW QUIZ SYSTEM TABLES
-- ============================================================================

-- Create all new tables (from quiz_system_v2.sql)
-- Note: This assumes the new tables don't exist yet

-- Core Infrastructure Tables (if they don't exist)
-- Users table should already exist, but ensure it has all required fields
-- Add missing columns to users table if needed
ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT 0;
ALTER TABLE users ADD COLUMN is_banned BOOLEAN DEFAULT 0;
ALTER TABLE users ADD COLUMN vip_tier TEXT;
ALTER TABLE users ADD COLUMN login_count INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN last_active_at TIMESTAMP;
ALTER TABLE users ADD COLUMN timezone TEXT DEFAULT 'UTC';
ALTER TABLE users ADD COLUMN locale TEXT DEFAULT 'en';
ALTER TABLE users ADD COLUMN sync_status TEXT DEFAULT 'local';
ALTER TABLE users ADD COLUMN server_id TEXT;
ALTER TABLE users ADD COLUMN last_synced_at TIMESTAMP;

-- App Settings Table
CREATE TABLE IF NOT EXISTS app_settings (
    key TEXT PRIMARY KEY NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    data_type TEXT DEFAULT 'string',
    is_system BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- UI Labels Table
CREATE TABLE IF NOT EXISTS ui_labels (
    key TEXT PRIMARY KEY NOT NULL,
    default_text TEXT NOT NULL,
    description TEXT,
    category TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- UI Label Translations Table
CREATE TABLE IF NOT EXISTS ui_label_translations (
    id TEXT PRIMARY KEY NOT NULL,
    label_key TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(label_key, language_code),
    FOREIGN KEY (label_key) REFERENCES ui_labels(key) ON DELETE CASCADE
);

-- User Streaks Table
CREATE TABLE IF NOT EXISTS user_streaks (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_entry_date DATE,
    streak_type TEXT DEFAULT 'daily',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, streak_type)
);

-- Skins Table
CREATE TABLE IF NOT EXISTS skins (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT,
    supported_view_types TEXT,
    supported_render_engines TEXT,
    is_premium BOOLEAN DEFAULT 0,
    is_unlocked BOOLEAN DEFAULT 1,
    price INTEGER,
    preview_image_light TEXT,
    preview_image_dark TEXT,
    config TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Emoji Sets Table
CREATE TABLE IF NOT EXISTS emoji_sets (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL DEFAULT 'unicode',
    is_default BOOLEAN DEFAULT 0,
    is_system BOOLEAN DEFAULT 1,
    is_unlocked BOOLEAN DEFAULT 1,
    price INTEGER,
    preview_image TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Emoji Set Translations Table
CREATE TABLE IF NOT EXISTS emoji_set_translations (
    emoji_set_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (emoji_set_id, language_code),
    FOREIGN KEY (emoji_set_id) REFERENCES emoji_sets(id) ON DELETE CASCADE
);

-- Emotions Table (if not exists)
CREATE TABLE IF NOT EXISTS emotions (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    emoji TEXT,
    color TEXT,
    tier_level INTEGER CHECK (tier_level BETWEEN 1 AND 5),
    parent_id TEXT,
    keywords TEXT,
    image_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,
    is_deleted BOOLEAN DEFAULT 0,
    deleted_at TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES emotions(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Emotion Translations Table
CREATE TABLE IF NOT EXISTS emotion_translations (
    emotion_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (emotion_id, language_code),
    FOREIGN KEY (emotion_id) REFERENCES emotions(id) ON DELETE CASCADE
);

-- Emoji Items Table
CREATE TABLE IF NOT EXISTS emoji_items (
    id TEXT PRIMARY KEY NOT NULL,
    emoji_set_id TEXT NOT NULL,
    emotion_id TEXT NOT NULL,
    unicode TEXT,
    animation_url TEXT,
    animation_type TEXT,
    animation_data TEXT,
    tags TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (emoji_set_id) REFERENCES emoji_sets(id) ON DELETE CASCADE,
    FOREIGN KEY (emotion_id) REFERENCES emotions(id) ON DELETE CASCADE,
    UNIQUE(emoji_set_id, emotion_id)
);

-- Quiz Packs Table
CREATE TABLE IF NOT EXISTS quiz_packs (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    version TEXT NOT NULL DEFAULT '1.0.0',
    category TEXT NOT NULL,
    quiz_type TEXT NOT NULL,
    difficulty_level TEXT DEFAULT 'regular',
    quiz_style TEXT,
    quiz_logic_config TEXT NOT NULL,
    default_presentation_hints TEXT,
    metadata TEXT NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Quiz Questions Table
CREATE TABLE IF NOT EXISTS quiz_questions (
    id TEXT PRIMARY KEY NOT NULL,
    pack_id TEXT NOT NULL,
    question_text TEXT NOT NULL,
    question_text_localized TEXT,
    question_type TEXT NOT NULL,
    question_order INTEGER NOT NULL,
    question_group TEXT,
    tier_level INTEGER DEFAULT 1,
    question_config TEXT,
    validation_rules TEXT,
    scoring_config TEXT,
    parent_question_id TEXT,
    dependency_rules TEXT,
    is_required BOOLEAN DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_question_id) REFERENCES quiz_questions(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(pack_id, question_order)
);

-- Quiz Question Options Table
CREATE TABLE IF NOT EXISTS quiz_question_options (
    id TEXT PRIMARY KEY NOT NULL,
    question_id TEXT NOT NULL,
    option_text TEXT NOT NULL,
    option_text_localized TEXT,
    option_value TEXT NOT NULL,
    option_type TEXT NOT NULL DEFAULT 'choice',
    option_order INTEGER NOT NULL,
    option_group TEXT,
    scoring_value REAL,
    scoring_weight REAL DEFAULT 1.0,
    min_value REAL,
    max_value REAL,
    step_value REAL,
    media_url TEXT,
    media_type TEXT,
    media_thumbnail_url TEXT,
    media_alt_text TEXT,
    matrix_row_id TEXT,
    matrix_column_id TEXT,
    reference_pack_id TEXT,
    reference_option_id TEXT,
    metadata TEXT,
    tags TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
    FOREIGN KEY (reference_pack_id) REFERENCES quiz_packs(id) ON DELETE SET NULL,
    FOREIGN KEY (reference_option_id) REFERENCES quiz_question_options(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(question_id, option_order)
);

-- User Presentation Configs Table (Quiz-specific configurations)
CREATE TABLE IF NOT EXISTS user_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    config_name TEXT NOT NULL DEFAULT 'default',
    presentation_config TEXT NOT NULL,
    config_version TEXT DEFAULT '2.0',
    personalization_level INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);

-- Global App Configs Table (Global application settings)
CREATE TABLE IF NOT EXISTS global_app_configs (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    config_name TEXT NOT NULL DEFAULT 'default',
    theme_mode TEXT DEFAULT 'system',
    language TEXT DEFAULT 'en',
    notifications_enabled BOOLEAN DEFAULT 1,
    sound_enabled BOOLEAN DEFAULT 1,
    accessibility TEXT,
    is_active BOOLEAN DEFAULT 1,
    is_default BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_name)
);

-- Pack Presentation Overrides Table
CREATE TABLE IF NOT EXISTS pack_presentation_overrides (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,
    presentation_overrides TEXT,
    tier_presentation_overrides TEXT,
    override_reason TEXT,
    override_priority INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    UNIQUE(user_id, pack_id)
);

-- Quiz Sessions Table
CREATE TABLE IF NOT EXISTS quiz_sessions (
    id TEXT PRIMARY KEY NOT NULL,
    pack_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'INITIATED',
    current_question_index INTEGER DEFAULT 0,
    current_question_id TEXT,
    current_tier_level INTEGER,
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_active_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    total_duration_seconds INTEGER,
    total_questions INTEGER,
    answered_questions INTEGER DEFAULT 0,
    skipped_questions INTEGER DEFAULT 0,
    completion_percentage REAL DEFAULT 0,
    session_type TEXT DEFAULT 'standard',
    device_info TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (current_question_id) REFERENCES quiz_questions(id) ON DELETE SET NULL
);

-- Quiz Session Presentation Configs Table
CREATE TABLE IF NOT EXISTS quiz_session_presentation_configs (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    config_name TEXT NOT NULL,
    config_version TEXT NOT NULL DEFAULT '2.0',
    presentation_config TEXT NOT NULL,
    session_overrides TEXT,
    personalization_level INTEGER DEFAULT 50,
    config_source TEXT DEFAULT 'user_preference',
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(session_id)
);

-- Quiz Answers Table
CREATE TABLE IF NOT EXISTS quiz_answers (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL,
    question_id TEXT NOT NULL,
    session_presentation_config_id TEXT,
    selected_option_ids TEXT,
    answer_value TEXT,
    answer_text TEXT,
    scale_value REAL,
    scale_label TEXT,
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
    response_time_ms INTEGER,
    is_skipped BOOLEAN DEFAULT 0,
    is_revised BOOLEAN DEFAULT 0,
    revision_count INTEGER DEFAULT 0,
    answered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE RESTRICT,
    FOREIGN KEY (session_presentation_config_id) REFERENCES quiz_session_presentation_configs(id) ON DELETE SET NULL
);

-- Quiz Results Table
CREATE TABLE IF NOT EXISTS quiz_results (
    id TEXT PRIMARY KEY NOT NULL,
    session_id TEXT NOT NULL UNIQUE,
    user_id TEXT NOT NULL,
    pack_id TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'COMPLETED',
    completion_percentage REAL CHECK (completion_percentage BETWEEN 0 AND 100),
    total_duration_seconds INTEGER,
    average_response_time_ms INTEGER,
    total_questions INTEGER,
    answered_questions INTEGER,
    skipped_questions INTEGER,
    dominant_emotions TEXT,
    emotion_pattern_analysis TEXT,
    intensity_analysis TEXT,
    category_distribution TEXT,
    temporal_patterns TEXT,
    overall_score REAL,
    emotional_stability_score REAL,
    emotional_complexity_score REAL,
    generated_tags TEXT,
    recommendations TEXT,
    narrative_summary TEXT,
    visual_summary_config TEXT,
    ai_analysis_status TEXT DEFAULT 'NOT_REQUESTED',
    ai_analysis_id TEXT,
    ai_analysis_version TEXT,
    result_version TEXT DEFAULT '2.0',
    analysis_algorithm_version TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES quiz_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (pack_id) REFERENCES quiz_packs(id) ON DELETE RESTRICT
);

-- Emotion Pattern Analyses Table
CREATE TABLE IF NOT EXISTS emotion_pattern_analyses (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    result_id TEXT NOT NULL,
    analysis_type TEXT NOT NULL,
    analysis_period_start TIMESTAMP,
    analysis_period_end TIMESTAMP,
    dominant_emotions TEXT,
    emotion_transitions TEXT,
    intensity_patterns TEXT,
    temporal_patterns TEXT,
    category_distribution TEXT,
    emotional_stability_index REAL,
    emotional_complexity_index REAL,
    emotional_variability_index REAL,
    pattern_consistency_score REAL,
    trend_direction TEXT,
    trend_confidence REAL CHECK (trend_confidence BETWEEN 0 AND 1),
    analysis_algorithm_version TEXT,
    analysis_confidence REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES quiz_results(id) ON DELETE CASCADE
);

-- Recommendations Table
CREATE TABLE IF NOT EXISTS recommendations (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    result_id TEXT,
    analysis_id TEXT,
    recommendation_type TEXT NOT NULL,
    category TEXT,
    priority_level INTEGER CHECK (priority_level BETWEEN 1 AND 5),
    title TEXT NOT NULL,
    description TEXT,
    detailed_explanation TEXT,
    action_steps TEXT,
    reasoning TEXT,
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
    evidence_data TEXT,
    resources TEXT,
    external_links TEXT,
    user_rating INTEGER CHECK (user_rating BETWEEN 1 AND 5),
    user_feedback TEXT,
    is_helpful BOOLEAN,
    is_implemented BOOLEAN DEFAULT 0,
    implementation_date TIMESTAMP,
    recommendation_algorithm_version TEXT,
    personalization_level INTEGER,
    is_active BOOLEAN DEFAULT 1,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES quiz_results(id) ON DELETE CASCADE,
    FOREIGN KEY (analysis_id) REFERENCES emotion_pattern_analyses(id) ON DELETE CASCADE
);

-- ============================================================================
-- MIGRATE EXISTING DATA
-- ============================================================================

-- Migrate emotion_data_sets to quiz_packs
INSERT OR IGNORE INTO quiz_packs (
    id, name, description, version, category, quiz_type, quiz_style, difficulty_level,
    quiz_logic_config, metadata, is_active, is_default, sort_order, created_at
)
SELECT
    eds.id,
    eds.name,
    eds.description,
    COALESCE(eds.version, '1.0.0'),
    'daily', -- Default category for emotion data sets
    'emotion_wheel', -- Default quiz type
    'mainstream', -- Default style
    'regular', -- Default difficulty
    COALESCE(eds.config, '{"question_flow":{"type":"hierarchical_progression"}}'),
    json_object(
        'estimated_duration_minutes', 5,
        'tags', json_array('emotion', 'tracking'),
        'migrated_from', 'emotion_data_sets',
        'original_id', eds.id
    ),
    eds.is_active,
    eds.is_default,
    COALESCE(eds.sort_order, 0),
    eds.created_at
FROM emotion_data_sets eds
WHERE eds.id IS NOT NULL;

-- Migrate emotion_data_set_tiers to quiz_questions
INSERT OR IGNORE INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, tier_level,
    question_config, is_required, is_active, created_at
)
SELECT
    'q-' || edst.id,
    edst.emotion_data_set_id,
    COALESCE(edst.question_text_key, 'Select your emotion'),
    COALESCE(edst.question_type, 'emotion_wheel'),
    edst.tier_level,
    edst.tier_level,
    COALESCE(edst.question_config, '{}'),
    1, -- Required by default
    1, -- Active by default
    CURRENT_TIMESTAMP
FROM emotion_data_set_tiers edst
WHERE edst.emotion_data_set_id IS NOT NULL;

-- Migrate user_configs to user_quiz_preferences and global_app_configs
-- First, migrate global app settings
INSERT OR IGNORE INTO global_app_configs (
    id, user_id, config_name, theme_mode, language, notifications_enabled, sound_enabled,
    accessibility, is_active, is_default, created_at
)
SELECT
    'global-' || uc.id,
    uc.user_id,
    'migrated_default',
    COALESCE(uc.theme_mode, 'system'),
    COALESCE(uc.language, 'en'),
    COALESCE(uc.notifications_enabled, 1),
    COALESCE(uc.sound_enabled, 1),
    COALESCE(uc.accessibility, '{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}'),
    1, -- Active
    1, -- Default
    uc.created_at
FROM user_configs uc
WHERE uc.user_id IS NOT NULL;

-- Then, migrate Quiz-specific settings to user_presentation_configs
INSERT OR IGNORE INTO user_presentation_configs (
    id, user_id, config_name, presentation_config, config_version,
    personalization_level, is_active, is_default, created_at
)
SELECT
    'quiz-pref-' || uc.id,
    uc.user_id,
    'migrated_default',
    json_object(
        'layer0_dataset_presentation', json_object(
            'preferred_pack_categories', COALESCE(json_extract(uc.quiz_preferences, '$.preferred_categories'), '["daily"]'),
            'default_difficulty_preference', 'regular',
            'session_length_preference', 'medium',
            'auto_select_recommended', true,
            'restore_progress', true
        ),
        'layer1_user_choice', json_object(
            'preferred_view_type', COALESCE(uc.preferred_view_type, 'wheel'),
            'active_skin_id', COALESCE(uc.active_skin_id, 'default-light'),
            'color_mode', COALESCE(uc.color_mode, 'warm'),
            'user_level', 'regular'
        ),
        'layer2_rendering_strategy', json_object(
            'render_engine_preferences', COALESCE(uc.render_engine_preferences, '{"wheel": "D3", "card": "CSS", "bubble": "Canvas"}'),
            'performance_mode', 'balanced',
            'supported_content_types', '{"text": true, "emoji": true, "images": false, "animations": true}'
        ),
        'layer3_skin_base', json_object(
            'selected_skin_id', COALESCE(uc.active_skin_id, 'default-light'),
            'colors', '{}',
            'animations', json_object(
                'enable_animations', true,
                'animation_speed', 'normal',
                'reduce_motion', false
            )
        ),
        'layer4_view_detail', json_object(
            'wheel_config', COALESCE(uc.view_detail_configs, '{"container_size": 350, "wheel_radius": 175, "emotion_display_mode": "standard", "show_labels": true, "show_emojis": true}')
        ),
        'layer5_accessibility', COALESCE(uc.accessibility, '{"high_contrast": false, "large_text": false, "reduce_motion": false, "keyboard_navigation": true, "voice_guidance": false}'),
        'migrated_from', 'user_configs'
    ),
    '2.0',
    50, -- Default personalization level
    1, -- Active
    1, -- Default
    uc.created_at
FROM user_configs uc
WHERE uc.user_id IS NOT NULL;

-- ============================================================================
-- ENHANCE EXISTING TABLES
-- ============================================================================

-- Add quiz-related fields to emotion_data_set_tiers if they don't exist
ALTER TABLE emotion_data_set_tiers ADD COLUMN question_text_key TEXT;
ALTER TABLE emotion_data_set_tiers ADD COLUMN question_type TEXT DEFAULT 'EMOTION_WHEEL_SELECT';
ALTER TABLE emotion_data_set_tiers ADD COLUMN question_config TEXT;
ALTER TABLE emotion_data_set_tiers ADD COLUMN ui_hints TEXT;
ALTER TABLE emotion_data_set_tiers ADD COLUMN navigation_config TEXT;
ALTER TABLE emotion_data_set_tiers ADD COLUMN validation_rules TEXT;

-- Add quiz-related fields to user_configs if they don't exist
ALTER TABLE user_configs ADD COLUMN quiz_preferences TEXT;
ALTER TABLE user_configs ADD COLUMN default_quiz_pack_id TEXT;
ALTER TABLE user_configs ADD COLUMN quiz_history_retention_days INTEGER DEFAULT 365;
ALTER TABLE user_configs ADD COLUMN enable_ai_analysis BOOLEAN DEFAULT 0;
ALTER TABLE user_configs ADD COLUMN privacy_settings TEXT;

-- Add foreign key constraint for default_quiz_pack_id
-- Note: This might fail if there are existing invalid references
-- ALTER TABLE user_configs ADD CONSTRAINT fk_default_quiz_pack
--     FOREIGN KEY (default_quiz_pack_id) REFERENCES quiz_packs(id) ON DELETE SET NULL;

-- ============================================================================
-- CREATE INDEXES FOR NEW TABLES
-- ============================================================================

-- Essential indexes for immediate performance
CREATE INDEX IF NOT EXISTS idx_quiz_packs_category_type ON quiz_packs(category, quiz_type);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_active ON quiz_packs(is_active, sort_order);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_pack_order ON quiz_questions(pack_id, question_order);
CREATE INDEX IF NOT EXISTS idx_question_options_question_order ON quiz_question_options(question_id, option_order);
CREATE INDEX IF NOT EXISTS idx_user_presentation_configs_user ON user_presentation_configs(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_user_status ON quiz_sessions(user_id, status);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_session_question ON quiz_answers(session_id, question_id);
CREATE INDEX IF NOT EXISTS idx_quiz_results_user_created ON quiz_results(user_id, created_at);

-- ============================================================================
-- DATA VALIDATION AND CLEANUP
-- ============================================================================

-- Validate migrated data
SELECT
    'Quiz Packs Migrated: ' || COUNT(*) as migration_status
FROM quiz_packs
WHERE json_extract(metadata, '$.migrated_from') = 'emotion_data_sets';

SELECT
    'Questions Migrated: ' || COUNT(*) as migration_status
FROM quiz_questions
WHERE pack_id IN (SELECT id FROM quiz_packs WHERE json_extract(metadata, '$.migrated_from') = 'emotion_data_sets');

SELECT
    'User Configs Migrated: ' || COUNT(*) as migration_status
FROM user_presentation_configs
WHERE json_extract(presentation_config, '$.migrated_from') = 'user_configs';

-- ============================================================================
-- FINALIZE MIGRATION
-- ============================================================================

-- Update schema version
UPDATE schema_migrations
SET applied_at = CURRENT_TIMESTAMP
WHERE version = '2.0.0';

-- Commit the transaction
COMMIT;

-- ============================================================================
-- POST-MIGRATION NOTES
-- ============================================================================

-- After running this migration:
-- 1. Run quiz_indexes_v2.sql for complete index optimization
-- 2. Test the migrated data thoroughly
-- 3. Consider loading quiz_sample_data.sql for additional test data
-- 4. Update application code to use new table structures
-- 5. Monitor performance and adjust indexes as needed

-- The old tables (emotion_data_sets, emotion_data_set_tiers) are preserved
-- for reference and can be dropped after confirming successful migration

-- Backup tables can be dropped after confirming data integrity:
-- DROP TABLE backup_emotion_data_sets;
-- DROP TABLE backup_emotion_data_set_tiers;
-- DROP TABLE backup_user_configs;
