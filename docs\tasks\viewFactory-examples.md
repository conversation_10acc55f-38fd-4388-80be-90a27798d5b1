# ViewFactory 示例代码

本文档提供了 ViewFactory 的示例代码，展示如何在实际项目中使用 ViewFactory。

## 1. 基本使用

### 1.1 创建轮盘视图

```tsx
import React, { useState } from 'react';
import { ViewFactory } from '@/utils/viewFactory';
import { SkinManager } from '@/utils/skinManager';
import { Emotion } from '@/types/emotionDataTypes';
import { SkinConfig } from '@/types/skinTypes';
import { UserConfig } from '@/types/userConfigTypes';

// 示例情绪数据
const emotions: Emotion[] = [
  { id: '1', name: '快乐', emoji: '😊', color: '#FFD700', tier: 1 },
  { id: '2', name: '悲伤', emoji: '😢', color: '#1E90FF', tier: 1 },
  { id: '3', name: '愤怒', emoji: '😠', color: '#FF4500', tier: 1 },
  { id: '4', name: '恐惧', emoji: '😨', color: '#800080', tier: 1 },
  { id: '5', name: '惊讶', emoji: '😲', color: '#FF69B4', tier: 1 },
  { id: '6', name: '厌恶', emoji: '🤢', color: '#32CD32', tier: 1 }
];

// 示例皮肤配置
const skinConfig: SkinConfig = {
  supported_view_types: ['wheel', 'card', 'bubble', 'galaxy'],
  supported_content_modes: ['text', 'emoji', 'textEmoji', 'animatedEmoji'],
  supported_render_engines: ['D3', 'SVG', 'R3F'],
  colors: {
    primary: '#4CAF50',
    secondary: '#2196F3',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#FF4081'
  },
  fonts: {
    family: 'Arial, sans-serif',
    size: {
      small: 12,
      medium: 14,
      large: 18
    },
    weight: {
      normal: 400,
      bold: 700
    }
  },
  effects: {
    shadows: true,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowBlur: 5,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    animations: true,
    animationDuration: 300,
    animationEasing: 'ease-in-out',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderStyle: 'solid',
    opacity: 1
  },
  view_configs: {
    wheel: {
      containerSize: 400,
      wheelRadius: 180,
      sectorGap: 2,
      sectorBorderRadius: 0,
      sectorBorderColor: '#FFFFFF',
      sectorBorderWidth: 1,
      textColor: '#000000',
      textSize: 14,
      emojiSize: 24
    }
  }
};

// 示例用户配置
const userConfig: UserConfig = {
  id: 'user-1',
  name: 'User Config',
  activeEmotionDataId: 'data-1',
  activeSkinId: 'skin-1',
  preferredViewType: 'wheel',
  darkMode: false,
  colorMode: 'warm',
  contentDisplayModePreferences: {
    wheel: 'textEmoji'
  },
  renderEnginePreferences: {
    wheel: 'D3'
  },
  viewTypeSkinIds: {
    wheel: 'skin-1'
  },
  layoutPreferences: {},
  created_at: new Date().toISOString(),
  lastUpdated: new Date()
};

// 示例组件
const EmotionWheelExample: React.FC = () => {
  const [tierLevel, setTierLevel] = useState<number>(1);
  const [selectedEmotions, setSelectedEmotions] = useState<Emotion[]>([]);
  const [selectedPath, setSelectedPath] = useState<string>('');

  // 获取皮肤管理器
  const skinManager = new SkinManager();
  skinManager.addSkin({ id: 'skin-1', config: skinConfig });

  // 处理情绪选择
  const handleSelect = (emotion: Emotion) => {
    // 更新选中的情绪
    const newSelectedEmotions = [...selectedEmotions, emotion];
    setSelectedEmotions(newSelectedEmotions);

    // 更新选中路径
    const newPath = newSelectedEmotions.map(e => e.name).join(' > ');
    setSelectedPath(newPath);

    // 更新层级
    setTierLevel(tierLevel + 1);
  };

  // 处理返回
  const handleBack = () => {
    if (tierLevel > 1) {
      // 移除最后一个选中的情绪
      const newSelectedEmotions = selectedEmotions.slice(0, -1);
      setSelectedEmotions(newSelectedEmotions);

      // 更新选中路径
      const newPath = newSelectedEmotions.map(e => e.name).join(' > ');
      setSelectedPath(newPath);

      // 更新层级
      setTierLevel(tierLevel - 1);
    }
  };

  // 创建视图
  const view = ViewFactory.createViewFromUserConfig(userConfig, skinManager);

  // 渲染视图
  return (
    <div className="emotion-wheel-container">
      <h1>情绪轮盘示例</h1>
      <div className="emotion-wheel">
        {view.render(emotions, tierLevel, handleSelect, {
          onBack: handleBack,
          selectedPath: selectedPath
        })}
      </div>
    </div>
  );
};

export default EmotionWheelExample;
```

## 2. 高级使用

### 2.1 使用不同的渲染引擎

```tsx
import React, { useState } from 'react';
import { ViewFactory } from '@/utils/viewFactory';
import { SkinManager } from '@/utils/skinManager';
import { Emotion } from '@/types/emotionDataTypes';
import { SkinConfig } from '@/types/skinTypes';
import { UserConfig } from '@/types/userConfigTypes';
import { RenderEngine } from '@/types/previewTypes';

// 示例组件
const RenderEngineExample: React.FC = () => {
  const [renderEngine, setRenderEngine] = useState<RenderEngine>('D3');
  const [emotions, setEmotions] = useState<Emotion[]>(/* 情绪数据 */);
  const [tierLevel, setTierLevel] = useState<number>(1);
  const [skinConfig, setSkinConfig] = useState<SkinConfig>(/* 皮肤配置 */);

  // 处理情绪选择
  const handleSelect = (emotion: Emotion) => {
    // 处理情绪选择
  };

  // 处理渲染引擎切换
  const handleRenderEngineChange = (engine: RenderEngine) => {
    setRenderEngine(engine);
  };

  // 创建视图
  const view = ViewFactory.createWheel(renderEngine, 'textEmoji', skinConfig);

  // 渲染视图
  return (
    <div className="render-engine-example">
      <h1>渲染引擎示例</h1>
      <div className="render-engine-controls">
        <button onClick={() => handleRenderEngineChange('D3')}>D3</button>
        <button onClick={() => handleRenderEngineChange('SVG')}>SVG</button>
        <button onClick={() => handleRenderEngineChange('R3F')}>R3F</button>
      </div>
      <div className="emotion-wheel">
        {view.render(emotions, tierLevel, handleSelect)}
      </div>
    </div>
  );
};

export default RenderEngineExample;
```

### 2.2 使用不同的内容显示模式

```tsx
import React, { useState } from 'react';
import { ViewFactory } from '@/utils/viewFactory';
import { SkinManager } from '@/utils/skinManager';
import { Emotion } from '@/types/emotionDataTypes';
import { SkinConfig } from '@/types/skinTypes';
import { UserConfig } from '@/types/userConfigTypes';
import { ContentDisplayMode } from '@/types/previewTypes';

// 示例组件
const ContentDisplayModeExample: React.FC = () => {
  const [contentDisplayMode, setContentDisplayMode] = useState<ContentDisplayMode>('textEmoji');
  const [emotions, setEmotions] = useState<Emotion[]>(/* 情绪数据 */);
  const [tierLevel, setTierLevel] = useState<number>(1);
  const [skinConfig, setSkinConfig] = useState<SkinConfig>(/* 皮肤配置 */);

  // 处理情绪选择
  const handleSelect = (emotion: Emotion) => {
    // 处理情绪选择
  };

  // 处理内容显示模式切换
  const handleContentDisplayModeChange = (mode: ContentDisplayMode) => {
    setContentDisplayMode(mode);
  };

  // 创建视图
  const view = ViewFactory.createWheel('D3', contentDisplayMode, skinConfig);

  // 渲染视图
  return (
    <div className="content-display-mode-example">
      <h1>内容显示模式示例</h1>
      <div className="content-display-mode-controls">
        <button onClick={() => handleContentDisplayModeChange('text')}>文本</button>
        <button onClick={() => handleContentDisplayModeChange('emoji')}>表情</button>
        <button onClick={() => handleContentDisplayModeChange('textEmoji')}>文本和表情</button>
        <button onClick={() => handleContentDisplayModeChange('animatedEmoji')}>动画表情</button>
      </div>
      <div className="emotion-wheel">
        {view.render(emotions, tierLevel, handleSelect)}
      </div>
    </div>
  );
};

export default ContentDisplayModeExample;
```

### 2.3 使用 3D 效果

```tsx
import React, { useState } from 'react';
import { ViewFactory } from '@/utils/viewFactory';
import { SkinManager } from '@/utils/skinManager';
import { Emotion } from '@/types/emotionDataTypes';
import { SkinConfig } from '@/types/skinTypes';
import { UserConfig } from '@/types/userConfigTypes';

// 示例组件
const ThreeDEffectsExample: React.FC = () => {
  const [use3DEffects, setUse3DEffects] = useState<boolean>(false);
  const [emotions, setEmotions] = useState<Emotion[]>(/* 情绪数据 */);
  const [tierLevel, setTierLevel] = useState<number>(1);
  const [skinConfig, setSkinConfig] = useState<SkinConfig>(/* 皮肤配置 */);

  // 处理情绪选择
  const handleSelect = (emotion: Emotion) => {
    // 处理情绪选择
  };

  // 处理 3D 效果切换
  const handle3DEffectsChange = (use3D: boolean) => {
    setUse3DEffects(use3D);

    // 更新皮肤配置
    const newSkinConfig = {
      ...skinConfig,
      view_configs: {
        ...skinConfig.view_configs,
        wheel: {
          ...skinConfig.view_configs?.wheel,
          use3DEffects: use3D,
          perspective: 1000,
          depth: 50
        }
      }
    };

    setSkinConfig(newSkinConfig);
  };

  // 创建视图
  const view = ViewFactory.createWheel(use3DEffects ? 'R3F' : 'D3', 'textEmoji', skinConfig);

  // 渲染视图
  return (
    <div className="three-d-effects-example">
      <h1>3D 效果示例</h1>
      <div className="three-d-effects-controls">
        <button onClick={() => handle3DEffectsChange(false)}>2D</button>
        <button onClick={() => handle3DEffectsChange(true)}>3D</button>
      </div>
      <div className="emotion-wheel">
        {view.render(emotions, tierLevel, handleSelect)}
      </div>
    </div>
  );
};

export default ThreeDEffectsExample;
```
