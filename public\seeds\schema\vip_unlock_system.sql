-- VIP and Unlock System Database Schema
-- Extracted from server/public/seeds/schema/full.sql
-- Implements VIP subscription management and content unlock system

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- VIP FIELDS IN USERS TABLE
-- ============================================================================
-- Note: VIP fields are already included in the main users table in quiz_system_v2.sql
-- This file adds the additional VIP-related tables for subscription management

-- VIP fields in users table (for reference):
-- is_vip BOOLEAN DEFAULT 0,
-- vip_tier TEXT, -- 'basic', 'premium', 'enterprise'
-- vip_expires_at TIMESTAMP,
-- vip_auto_renew BOOLEAN DEFAULT 0,

-- ============================================================================
-- USER UNLOCK TABLES
-- ============================================================================

-- User Skin Unlocks Table: Tracks which skins users have unlocked (Server-controlled)
-- CLIENT PERMISSIONS: READ-ONLY (unlocks managed by server)
CREATE TABLE IF NOT EXISTS user_skin_unlocks (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    skin_id TEXT NOT NULL,
    unlock_method TEXT NOT NULL CHECK (unlock_method IN ('purchase', 'vip', 'achievement', 'free', 'promotion')),
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP, -- For temporary unlocks
    transaction_id TEXT, -- Reference to payment transaction
    promotion_code TEXT, -- If unlocked via promotion
    
    -- Sync fields
    sync_status TEXT DEFAULT 'synced' NOT NULL,
    server_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skin_id) REFERENCES skins(id) ON DELETE CASCADE,
    UNIQUE(user_id, skin_id)
);

-- User Emoji Set Unlocks Table: Tracks which emoji sets users have unlocked (Server-controlled)
-- CLIENT PERMISSIONS: READ-ONLY (unlocks managed by server)
CREATE TABLE IF NOT EXISTS user_emoji_set_unlocks (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    emoji_set_id TEXT NOT NULL,
    unlock_method TEXT NOT NULL CHECK (unlock_method IN ('purchase', 'vip', 'achievement', 'free', 'promotion')),
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP, -- For temporary unlocks
    transaction_id TEXT, -- Reference to payment transaction
    promotion_code TEXT, -- If unlocked via promotion
    
    -- Sync fields
    sync_status TEXT DEFAULT 'synced' NOT NULL,
    server_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (emoji_set_id) REFERENCES emoji_sets(id) ON DELETE CASCADE,
    UNIQUE(user_id, emoji_set_id)
);

-- ============================================================================
-- VIP SUBSCRIPTION SYSTEM
-- ============================================================================

-- User Subscription History Table: Tracks VIP/Premium subscription history (Server-only)
-- CLIENT PERMISSIONS: NONE (sensitive financial data, server-only)
CREATE TABLE IF NOT EXISTS user_subscription_history (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    subscription_type TEXT NOT NULL, -- 'vip_basic', 'vip_premium', 'vip_enterprise', etc.
    status TEXT NOT NULL CHECK (status IN ('active', 'cancelled', 'expired', 'refunded', 'pending')),
    
    -- Subscription timing
    started_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    auto_renew BOOLEAN DEFAULT FALSE,
    
    -- Payment information
    payment_method TEXT, -- 'stripe', 'paypal', 'apple_pay', 'google_pay', etc.
    transaction_id TEXT,
    amount REAL, -- Subscription amount
    currency TEXT DEFAULT 'USD',
    
    -- Billing information
    billing_cycle TEXT, -- 'monthly', 'yearly', 'lifetime'
    next_billing_date TIMESTAMP,
    
    -- Metadata
    subscription_source TEXT, -- 'web', 'mobile', 'promotion', etc.
    promotion_code TEXT, -- If subscription was via promotion
    notes TEXT, -- Additional notes
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ============================================================================
-- VIP PLANS AND FEATURES
-- ============================================================================

-- VIP Plans Table: Defines available VIP subscription plans
CREATE TABLE IF NOT EXISTS vip_plans (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL, -- 'Basic VIP', 'Premium VIP', 'Enterprise VIP'
    description TEXT,
    tier TEXT NOT NULL, -- 'basic', 'premium', 'enterprise'
    
    -- Pricing
    price REAL NOT NULL,
    currency TEXT DEFAULT 'USD',
    billing_cycle TEXT NOT NULL, -- 'monthly', 'yearly', 'lifetime'
    
    -- Features (JSON array of feature IDs)
    features TEXT NOT NULL, -- JSON: ['unlimited_skins', 'premium_emoji_sets', 'advanced_analytics']
    
    -- Limits and quotas
    skin_unlock_limit INTEGER, -- NULL for unlimited
    emoji_set_unlock_limit INTEGER, -- NULL for unlimited
    storage_limit_mb INTEGER, -- NULL for unlimited
    
    -- Plan status
    is_active BOOLEAN DEFAULT 1,
    is_featured BOOLEAN DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- VIP Features Table: Defines individual VIP features
CREATE TABLE IF NOT EXISTS vip_features (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    feature_type TEXT NOT NULL, -- 'unlock', 'limit', 'access', 'customization'
    
    -- Feature configuration (JSON)
    config TEXT, -- JSON: feature-specific configuration
    
    -- Status
    is_active BOOLEAN DEFAULT 1,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- User unlock indexes
CREATE INDEX IF NOT EXISTS idx_user_skin_unlocks_user_id ON user_skin_unlocks(user_id);
CREATE INDEX IF NOT EXISTS idx_user_skin_unlocks_skin_id ON user_skin_unlocks(skin_id);
CREATE INDEX IF NOT EXISTS idx_user_skin_unlocks_method ON user_skin_unlocks(unlock_method);
CREATE INDEX IF NOT EXISTS idx_user_skin_unlocks_expires ON user_skin_unlocks(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_emoji_set_unlocks_user_id ON user_emoji_set_unlocks(user_id);
CREATE INDEX IF NOT EXISTS idx_user_emoji_set_unlocks_emoji_set_id ON user_emoji_set_unlocks(emoji_set_id);
CREATE INDEX IF NOT EXISTS idx_user_emoji_set_unlocks_method ON user_emoji_set_unlocks(unlock_method);
CREATE INDEX IF NOT EXISTS idx_user_emoji_set_unlocks_expires ON user_emoji_set_unlocks(expires_at);

-- User subscription indexes
CREATE INDEX IF NOT EXISTS idx_user_subscription_history_user_id ON user_subscription_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscription_history_status ON user_subscription_history(status);
CREATE INDEX IF NOT EXISTS idx_user_subscription_history_expires_at ON user_subscription_history(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_subscription_history_type ON user_subscription_history(subscription_type);
CREATE INDEX IF NOT EXISTS idx_user_subscription_history_billing ON user_subscription_history(next_billing_date);

-- VIP plans indexes
CREATE INDEX IF NOT EXISTS idx_vip_plans_tier ON vip_plans(tier);
CREATE INDEX IF NOT EXISTS idx_vip_plans_active ON vip_plans(is_active, sort_order);
CREATE INDEX IF NOT EXISTS idx_vip_plans_billing_cycle ON vip_plans(billing_cycle);

-- VIP features indexes
CREATE INDEX IF NOT EXISTS idx_vip_features_type ON vip_features(feature_type);
CREATE INDEX IF NOT EXISTS idx_vip_features_active ON vip_features(is_active);
