export * from './schema';
// 注意：常用类型别名已通过 export * from './schema' 和其他模块导出
// 避免重复导出和循环引用

// 注意：类型守卫函数已迁移到 unified.ts
// 这里只保留必要的业务逻辑类型

// 注意：工具类型（EntityType, ServiceResult, PaginatedResult 等）已迁移到 schema
// 这里只保留应用特定的业务逻辑类型

// 注意：数据库、API响应、错误类型等已通过 unified.ts 导出
// 这里只保留应用特定的配置类型

// 应用配置类型（应用特定，不在 schema 中）
export type AppConfig = {
  database: {
    url: string;
    pool_size: number;
    timeout: number;
  };
  translation: {
    default_language: string;
    supported_languages: string[];
    fallback_strategy: string;
  };
  cache: {
    enabled: boolean;
    ttl: number;
    max_size: number;
  };
  logging: {
    level: string;
    format: string;
  };
};
