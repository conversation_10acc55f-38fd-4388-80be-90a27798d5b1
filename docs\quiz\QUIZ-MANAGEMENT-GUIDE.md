# Quiz量表管理系统

本文档介绍新的Quiz量表管理系统，该系统替代了原有的emotion-data-editor，提供了更强大的Quiz包、问题和选项管理功能，并支持CSV批量导入。

## 系统概述

### 访问路径
- **主要路径**: `http://localhost:4080/quiz-management`
- **兼容路径**: `http://localhost:4080/emotion-data-editor` (重定向到新系统)
- **带ID路径**: `http://localhost:4080/quiz-management/:id` (直接进入指定Quiz包)

### 核心功能
1. **Quiz包管理** - 创建、编辑、删除Quiz包
2. **问题管理** - 管理Quiz包中的问题
3. **选项管理** - 管理问题的选项
4. **CSV导入** - 批量导入数据的便携选项

## 功能详解

### 1. Quiz包管理

#### 功能特性
- **创建新包**: 支持多种Quiz类型（情绪轮盘、中医评估、调查问卷等）
- **编辑包信息**: 修改包名、描述、类型、难度等
- **复制包**: 快速创建包的副本
- **删除包**: 安全删除（需确认）
- **状态管理**: 激活/停用包，设置公开/私有

#### 支持的Quiz类型
- `emotion_wheel` - 情绪轮盘
- `tcm_assessment` - 中医评估
- `survey` - 调查问卷
- `personality` - 性格测试
- `mood_tracker` - 情绪追踪

#### 包属性配置
```typescript
interface QuizPackData {
  name: string;                    // 包名称
  description: string;             // 描述
  quiz_type: string;              // Quiz类型
  category: string;               // 类别
  difficulty_level: number;       // 难度等级 (1-5)
  estimated_duration_minutes: number; // 预计时长
  is_active: boolean;             // 是否激活
  is_public: boolean;             // 是否公开
}
```

### 2. 问题管理

#### 功能特性
- **创建问题**: 支持多种问题类型
- **编辑问题**: 修改问题文本、类型、顺序等
- **问题排序**: 上下调整问题顺序
- **复制问题**: 快速创建问题副本
- **分组管理**: 支持问题分组
- **层级设置**: 支持多层级问题结构

#### 支持的问题类型
- `single_choice` - 单选题
- `multiple_choice` - 多选题
- `emotion_wheel` - 情绪轮盘
- `scale_rating` - 量表评分
- `slider` - 滑块
- `text_input` - 文本输入

#### 问题属性配置
```typescript
interface QuestionData {
  question_text: string;          // 问题文本
  question_type: string;          // 问题类型
  question_order: number;         // 问题顺序
  question_group: string;         // 问题分组
  tier_level: number;             // 层级等级
  is_required: boolean;           // 是否必填
  is_active: boolean;             // 是否激活
}
```

### 3. 选项管理

#### 功能特性
- **创建选项**: 为问题添加选项
- **编辑选项**: 修改选项文本、值、顺序等
- **选项排序**: 上下调整选项顺序
- **复制选项**: 快速创建选项副本
- **元数据管理**: 支持emoji、颜色、描述等
- **评分设置**: 支持选项评分值
- **正确答案**: 标记正确答案（用于测试类Quiz）

#### 选项属性配置
```typescript
interface OptionData {
  option_text: string;            // 选项文本
  option_value: string;           // 选项值
  option_order: number;           // 选项顺序
  scoring_value: number | null;   // 评分值
  is_correct: boolean | null;     // 是否正确答案
  is_active: boolean;             // 是否激活
  metadata: {
    emoji?: string;               // emoji图标
    color?: string;               // 颜色
    description?: string;         // 描述
    parent_option_id?: string;    // 父选项ID
  };
}
```

### 4. CSV批量导入

#### 功能特性
- **模板下载**: 提供4种CSV模板
- **智能解析**: 自动识别数据类型
- **预览功能**: 导入前预览数据
- **错误检查**: 导入前验证数据
- **批量导入**: 一次性导入大量数据

#### 支持的模板类型

##### 1. 完整模板 (complete)
包含Quiz包、问题和选项的完整信息
```csv
pack_name,description,quiz_type,question_text,question_type,question_order,option_text,option_value,option_order,emoji,color
示例Quiz包,完整示例,emotion_wheel,请选择情绪,single_choice,1,快乐,happy,1,😊,#FFD700
```

##### 2. Quiz包模板 (packs)
仅包含Quiz包信息
```csv
pack_name,description,quiz_type,category,difficulty_level,estimated_duration_minutes,is_active,is_public
示例Quiz包,这是一个示例Quiz包,emotion_wheel,emotion,1,5,true,false
```

##### 3. 问题模板 (questions)
包含问题信息，需要指定所属Quiz包
```csv
pack_name,question_text,question_type,question_order,question_group,tier_level,is_required,is_active
示例Quiz包,请选择您的情绪,single_choice,1,主要情绪,1,true,true
```

##### 4. 选项模板 (options)
包含选项信息，需要指定所属问题
```csv
question_text,option_text,option_value,option_order,scoring_value,is_correct,is_active,emoji,color,description
请选择您的情绪,快乐,happy,1,5,false,true,😊,#FFD700,表示快乐的情绪
```

#### CSV导入流程
1. **下载模板** - 选择合适的模板文件
2. **填写数据** - 按模板格式填写数据
3. **上传文件** - 选择填写好的CSV文件
4. **预览数据** - 检查解析结果和错误信息
5. **确认导入** - 执行批量导入操作

#### 数据验证规则
- **必填字段**: name, question_text, option_text等
- **数据类型**: 数值字段必须为有效数字
- **布尔值**: 支持true/false或1/0
- **关联性**: 问题必须关联到存在的Quiz包
- **唯一性**: 同一Quiz包内问题顺序不能重复

## 界面导航

### 标签导航
- **📦 Quiz包管理** - 管理Quiz包
- **❓ 问题管理** - 管理选中包的问题（需先选择包）
- **📝 选项管理** - 管理选中问题的选项（需先选择问题）
- **📤 CSV导入** - 批量导入数据

### 面包屑导航
显示当前位置：Quiz包 › 选中的包 › 选中的问题

### 操作流程
1. **选择或创建Quiz包**
2. **进入问题管理，创建或编辑问题**
3. **进入选项管理，为问题添加选项**
4. **使用CSV导入批量添加数据**

## 技术架构

### 数据流
```
QuizManagementPage
├── QuizPackManager (Quiz包管理)
├── QuizQuestionManager (问题管理)
├── QuizOptionManager (选项管理)
└── CSVImporter (CSV导入)
```

### 服务层集成
- **Services.quizPack()** - Quiz包服务
- **Services.quizQuestion()** - 问题和选项服务
- **数据库表**: quiz_packs → quiz_questions → quiz_question_options

### 状态管理
- **选择状态**: 当前选中的包和问题
- **表单状态**: 创建/编辑表单的显示和数据
- **导入状态**: CSV导入的预览和进度

## 使用指南

### 创建第一个Quiz包
1. 访问 `http://localhost:4080/quiz-management`
2. 点击"创建新Quiz包"
3. 填写包信息（名称、描述、类型等）
4. 点击"创建"保存

### 添加问题
1. 在Quiz包列表中点击"管理问题"
2. 点击"创建新问题"
3. 填写问题信息（文本、类型、顺序等）
4. 点击"创建"保存

### 添加选项
1. 在问题列表中点击"管理选项"
2. 点击"创建新选项"
3. 填写选项信息（文本、值、emoji、颜色等）
4. 点击"创建"保存

### 使用CSV导入
1. 切换到"CSV导入"标签
2. 下载合适的模板文件
3. 填写数据并保存为CSV格式
4. 上传文件并预览
5. 确认无误后执行导入

## 最佳实践

### Quiz包设计
- **明确目标**: 确定Quiz的目的和受众
- **合理分类**: 选择合适的类型和类别
- **难度设置**: 根据目标用户设置合适的难度
- **时长估算**: 准确估算完成时间

### 问题设计
- **清晰表达**: 问题文本要清晰易懂
- **逻辑顺序**: 合理安排问题顺序
- **层级结构**: 利用层级实现复杂逻辑
- **分组管理**: 使用分组组织相关问题

### 选项设计
- **选项平衡**: 确保选项数量和质量平衡
- **视觉设计**: 合理使用emoji和颜色
- **评分设置**: 为需要评分的Quiz设置分值
- **元数据利用**: 充分利用元数据增强体验

### CSV导入技巧
- **数据准备**: 导入前仔细检查数据格式
- **分批导入**: 大量数据建议分批导入
- **备份数据**: 导入前备份现有数据
- **测试验证**: 导入后验证数据正确性

## 故障排除

### 常见问题
1. **导入失败**: 检查CSV格式和数据类型
2. **关联错误**: 确保问题和选项正确关联
3. **权限问题**: 确认用户有相应操作权限
4. **数据冲突**: 检查重复数据和约束条件

### 错误处理
- **友好提示**: 系统会显示详细的错误信息
- **数据回滚**: 导入失败时自动回滚
- **日志记录**: 重要操作会记录日志
- **重试机制**: 支持操作重试

## 未来规划

### 短期目标
- **导入优化**: 提升CSV导入的性能和容错性
- **批量操作**: 支持批量编辑和删除
- **模板扩展**: 提供更多专业模板

### 中期目标
- **可视化编辑**: 提供拖拽式问题编辑器
- **预览功能**: 实时预览Quiz效果
- **版本管理**: 支持Quiz包版本控制

### 长期目标
- **协作编辑**: 支持多人协作编辑
- **智能推荐**: AI辅助问题和选项设计
- **数据分析**: 提供Quiz使用情况分析
