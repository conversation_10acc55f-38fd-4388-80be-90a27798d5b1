/**
 * Quiz包仓储 - 修复版本
 * 纯数据访问层，不包含业务逻辑
 */

import { BaseRepository } from '../base/BaseRepository';
import { QuizPack } from '../../types/schema/base';
import { CreateQuizPackInput, UpdateQuizPackInput } from '../../types/schema/api';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class QuizPackRepository extends BaseRepository<
  QuizPack,
  CreateQuizPackInput,
  UpdateQuizPackInput
> {
  constructor(db?: SQLiteDBConnection) {
    super('quiz_packs', db);
  }

  /**
   * 获取活跃的Quiz包
   */
  async findActiveQuizPacks(): Promise<QuizPack[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE is_active = 1
      ORDER BY created_at DESC
    `;

    const result = await db.query(query);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据类型获取Quiz包
   */
  async findByQuizType(quizType: string): Promise<QuizPack[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE quiz_type = ? AND is_active = 1
      ORDER BY created_at DESC
    `;

    const result = await db.query(query, [quizType]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据分类获取Quiz包
   */
  async findByCategory(category: string): Promise<QuizPack[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE category = ? AND is_active = 1
      ORDER BY created_at DESC
    `;

    const result = await db.query(query, [category]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 搜索Quiz包
   */
  async searchQuizPacks(searchTerm: string): Promise<QuizPack[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE (name LIKE ? OR description LIKE ?) AND is_active = 1
      ORDER BY created_at DESC
    `;

    const searchPattern = `%${searchTerm}%`;
    const result = await db.query(query, [searchPattern, searchPattern]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据难度级别获取Quiz包
   */
  async findByDifficultyLevel(difficultyLevel: number): Promise<QuizPack[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE difficulty_level = ? AND is_active = 1
      ORDER BY created_at DESC
    `;

    const result = await db.query(query, [difficultyLevel]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据标签获取Quiz包
   */
  async findByTag(tag: string): Promise<QuizPack[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE tags LIKE ? AND is_active = 1
      ORDER BY created_at DESC
    `;

    const tagPattern = `%"${tag}"%`;
    const result = await db.query(query, [tagPattern]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 获取Quiz包的统计信息
   */
  async getQuizPackStats(packId: string): Promise<any> {
    const db = this.getDb();
    const query = `
      SELECT
        COUNT(DISTINCT qs.id) as total_sessions,
        COUNT(DISTINCT CASE WHEN qs.status = 'COMPLETED' THEN qs.id END) as completed_sessions,
        COUNT(DISTINCT qq.id) as total_questions,
        AVG(CASE WHEN qs.status = 'COMPLETED' THEN
          (julianday(qs.end_time) - julianday(qs.start_time)) * 24 * 60
        END) as avg_completion_time_minutes
      FROM ${this.tableName} qp
      LEFT JOIN quiz_sessions qs ON qp.id = qs.pack_id
      LEFT JOIN quiz_questions qq ON qp.id = qq.pack_id
      WHERE qp.id = ?
      GROUP BY qp.id
    `;

    const result = await db.query(query, [packId]);
    const stats = result.values?.[0];

    return stats || {
      total_sessions: 0,
      completed_sessions: 0,
      total_questions: 0,
      avg_completion_time_minutes: 0
    };
  }

  /**
   * 获取Quiz包的问题列表
   */
  async getQuestionsByPackId(packId: string): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM quiz_questions
        WHERE pack_id = ? AND is_active = 1
        ORDER BY question_order ASC
      `;

      const result = await db.query(query, [packId]);
      return {
        success: true,
        data: result.values || []
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get questions: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * 获取问题的选项列表
   */
  async getOptionsByQuestionId(questionId: string): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM quiz_question_options
        WHERE question_id = ? AND is_active = 1
        ORDER BY option_order ASC
      `;

      const result = await db.query(query, [questionId]);
      return {
        success: true,
        data: result.values || []
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to get options: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  protected mapRowToEntity(row: any): QuizPack {
    return {
      id: row.id,
      name: row.name,
      name_localized: row.name_localized, // 保持为字符串，符合 schema
      description: row.description,
      description_localized: row.description_localized, // 保持为字符串，符合 schema
      quiz_type: row.quiz_type,
      quiz_style: row.quiz_style,
      category: row.category,
      difficulty_level: row.difficulty_level || 1,
      estimated_duration_minutes: row.estimated_duration_minutes,
      tags: row.tags, // 保持为字符串，符合 schema
      metadata: row.metadata, // 保持为字符串，符合 schema
      is_active: Boolean(row.is_active),
      created_at: row.created_at,
      updated_at: row.updated_at,
      version: row.version || '1.0.0',
      quiz_logic_config: row.quiz_logic_config,
      default_presentation_hints: row.default_presentation_hints,
      is_default: Boolean(row.is_default),
      sort_order: row.sort_order || 0,
      created_by: row.created_by,
      updated_by: row.updated_by
    };
  }

  protected mapEntityToRow(entity: Partial<QuizPack>): Record<string, any> {
    return {
      id: entity.id,
      name: entity.name,
      name_localized: entity.name_localized, // 直接使用字符串
      description: entity.description,
      description_localized: entity.description_localized, // 直接使用字符串
      version: entity.version,
      quiz_type: entity.quiz_type,
      quiz_style: entity.quiz_style,
      category: entity.category,
      difficulty_level: entity.difficulty_level,
      estimated_duration_minutes: entity.estimated_duration_minutes,
      tags: entity.tags, // 直接使用字符串
      quiz_logic_config: entity.quiz_logic_config,
      default_presentation_hints: entity.default_presentation_hints,
      metadata: entity.metadata, // 直接使用字符串
      is_active: entity.is_active ? 1 : 0,
      is_default: entity.is_default ? 1 : 0,
      sort_order: entity.sort_order,
      created_at: entity.created_at,
      updated_at: entity.updated_at,
      created_by: entity.created_by,
      updated_by: entity.updated_by
    };
  }

  protected buildInsertQuery(data: CreateQuizPackInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const packId = `pack_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const query = `
      INSERT INTO ${this.tableName} (
        id, name, name_localized, description, description_localized, version,
        quiz_type, quiz_style, category, difficulty_level,
        estimated_duration_minutes, tags, metadata, is_active,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      packId,
      data.name,
      this.stringifyJSON(data.name_localized),
      data.description || null,
      this.stringifyJSON(data.description_localized),
      '1.0.0', // 默认版本
      data.quiz_type,
      data.quiz_style || null,
      data.category || null,
      data.difficulty_level || 1,
      data.estimated_duration_minutes || null,
      this.stringifyJSON(data.tags),
      this.stringifyJSON(data.metadata),
      data.is_active ? 1 : 0,
      now,
      now
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateQuizPackInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      fields.push('name = ?');
      values.push(data.name);
    }

    if (data.name_localized !== undefined) {
      fields.push('name_localized = ?');
      values.push(this.stringifyJSON(data.name_localized));
    }

    if (data.description !== undefined) {
      fields.push('description = ?');
      values.push(data.description);
    }

    if (data.description_localized !== undefined) {
      fields.push('description_localized = ?');
      values.push(this.stringifyJSON(data.description_localized));
    }

    if (data.quiz_type !== undefined) {
      fields.push('quiz_type = ?');
      values.push(data.quiz_type);
    }

    if (data.quiz_style !== undefined) {
      fields.push('quiz_style = ?');
      values.push(data.quiz_style);
    }

    if (data.category !== undefined) {
      fields.push('category = ?');
      values.push(data.category);
    }

    if (data.difficulty_level !== undefined) {
      fields.push('difficulty_level = ?');
      values.push(data.difficulty_level);
    }

    if (data.estimated_duration_minutes !== undefined) {
      fields.push('estimated_duration_minutes = ?');
      values.push(data.estimated_duration_minutes);
    }

    if (data.tags !== undefined) {
      fields.push('tags = ?');
      values.push(this.stringifyJSON(data.tags));
    }

    if (data.metadata !== undefined) {
      fields.push('metadata = ?');
      values.push(this.stringifyJSON(data.metadata));
    }

    if (data.is_active !== undefined) {
      fields.push('is_active = ?');
      values.push(data.is_active ? 1 : 0);
    }

    // Always update updated_at
    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.quiz_type) {
      conditions.push('quiz_type = ?');
      values.push(filters.quiz_type);
    }

    if (filters?.category) {
      conditions.push('category = ?');
      values.push(filters.category);
    }

    if (filters?.difficulty_level) {
      conditions.push('difficulty_level = ?');
      values.push(filters.difficulty_level);
    }

    if (filters?.is_active !== undefined) {
      conditions.push('is_active = ?');
      values.push(filters.is_active ? 1 : 0);
    } else {
      // Default to active only
      conditions.push('is_active = 1');
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY created_at DESC';

    if (filters?.limit) {
      query += ' LIMIT ?';
      values.push(filters.limit);
    }

    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.quiz_type) {
      conditions.push('quiz_type = ?');
      values.push(filters.quiz_type);
    }

    if (filters?.category) {
      conditions.push('category = ?');
      values.push(filters.category);
    }

    if (filters?.is_active !== undefined) {
      conditions.push('is_active = ?');
      values.push(filters.is_active ? 1 : 0);
    } else {
      conditions.push('is_active = 1');
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    return { query, values };
  }

  protected extractIdFromCreateData(_data: CreateQuizPackInput): string {
    return `pack_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }



  private stringifyJSON(obj: any): string | null {
    if (obj === null || obj === undefined) return null;
    try {
      return JSON.stringify(obj);
    } catch {
      return null;
    }
  }
}
