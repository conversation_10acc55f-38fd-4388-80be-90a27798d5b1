/**
 * 动画表情组件
 * 用于显示和控制动画表情
 */

import { cn } from '@/lib/utils';
import { AnimationType, type EmojiItem } from '@/types/emojiTypes';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';

interface AnimatedEmojiProps {
  emojiItem: EmojiItem;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  autoPlay?: boolean;
  loop?: boolean;
  onComplete?: () => void;
  className?: string;
}

/**
 * 动画表情组件
 * @param emojiItem 表情项
 * @param size 表情大小
 * @param autoPlay 是否自动播放
 * @param loop 是否循环播放
 * @param onComplete 动画完成回调
 * @param className 自定义类名
 */
const AnimatedEmoji: React.FC<AnimatedEmojiProps> = ({
  emojiItem,
  size = 'md',
  autoPlay = true,
  loop = true,
  onComplete,
  className,
}) => {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [currentFrame, setCurrentFrame] = useState(0);
  const animationRef = useRef<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 获取大小样式
  const getSizeStyle = () => {
    const sizes = {
      xs: '1rem',
      sm: '1.25rem',
      md: '1.5rem',
      lg: '2rem',
      xl: '2.5rem',
      '2xl': '3rem',
      '3xl': '3.5rem',
      '4xl': '4rem',
    };

    return sizes[size] || sizes.md;
  };

  // 处理 GIF 动画
  const renderGifAnimation = () => {
    if (!emojiItem.animation_url) return null;

    return (
      <img
        src={emojiItem.animation_url}
        alt="animated emoji"
        className="w-full h-full"
        style={{ display: isPlaying ? 'block' : 'none' }}
      />
    );
  };

  // 处理 Lottie 动画
  const renderLottieAnimation = () => {
    // 这里将来会使用 lottie-react 渲染 Lottie 动画
    // 目前只是一个占位符
    return (
      <div className="lottie-placeholder w-full h-full flex items-center justify-center bg-muted rounded-md">
        <span className="text-xs">Lottie</span>
      </div>
    );
  };

  // 处理精灵图动画
  const renderSpriteAnimation = () => {
    if (!emojiItem.sprite_sheet) return null;

    const { url, frames, width, height } = emojiItem.sprite_sheet;

    return (
      <div
        className="sprite-container w-full h-full"
        style={{
          backgroundImage: `url(${url})`,
          backgroundPosition: `-${currentFrame * width}px 0`,
          backgroundSize: `${frames * width}px ${height}px`,
          width: `${width}px`,
          height: `${height}px`,
          transform: `scale(${Number.parseFloat(getSizeStyle()) / Math.max(width, height)})`,
          transformOrigin: 'center',
        }}
      />
    );
  };

  // 播放精灵图动画
  useEffect(() => {
    if (!emojiItem.sprite_sheet || !isPlaying) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
      return;
    }

    const { frames, fps } = emojiItem.sprite_sheet;
    let frame = currentFrame;
    let lastTime = 0;
    const frameDuration = 1000 / fps;

    const animate = (time: number) => {
      if (!lastTime) lastTime = time;

      const delta = time - lastTime;

      if (delta >= frameDuration) {
        frame = (frame + 1) % frames;
        setCurrentFrame(frame);
        lastTime = time;

        if (frame === 0 && !loop) {
          setIsPlaying(false);
          if (onComplete) onComplete();
          return;
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [emojiItem.sprite_sheet, isPlaying, currentFrame, loop, onComplete]);

  // 播放/暂停动画
  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  // 重置动画
  const resetAnimation = () => {
    setCurrentFrame(0);
    setIsPlaying(true);
  };

  // 渲染动画控制按钮
  const renderControls = () => {
    return (
      <div className="absolute bottom-0 left-0 right-0 flex justify-center p-1 bg-background/80 rounded-b-md opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={togglePlay}
          className="w-6 h-6 flex items-center justify-center rounded-full bg-primary/10 text-primary text-xs mr-1"
        >
          {isPlaying ? '⏸' : '▶'}
        </button>
        <button
          onClick={resetAnimation}
          className="w-6 h-6 flex items-center justify-center rounded-full bg-primary/10 text-primary text-xs"
        >
          ↻
        </button>
      </div>
    );
  };

  // 检查 emojiItem 是否为有效对象
  if (!emojiItem || !emojiItem.id) {
    return (
      <div
        className={cn('flex items-center justify-center', className)}
        style={{
          width: getSizeStyle(),
          height: getSizeStyle(),
        }}
      >
        <span>❓</span>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={cn('relative group', className)}
      style={{
        width: getSizeStyle(),
        height: getSizeStyle(),
      }}
    >
      {emojiItem.animation_type === 'gif' && renderGifAnimation()}
      {emojiItem.animation_type === 'lottie' && renderLottieAnimation()}
      {emojiItem.animation_type === 'sprite' && renderSpriteAnimation()}
      {renderControls()}
    </div>
  );
};

// 提供默认导出和命名导出，以支持不同的导入方式
export { AnimatedEmoji };
export default AnimatedEmoji;
