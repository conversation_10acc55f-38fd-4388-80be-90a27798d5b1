/**
 * Emoji映射服务
 * 管理不同Quiz包中选项的emoji展现映射
 */

import { UserQuizPreferencesRepository } from './UserQuizPreferencesRepository';
import { QuizPackOverridesRepository } from './QuizPackOverridesRepository';

export interface EmojiMapping {
  primary: string;
  alternatives: string[];
}

export interface EmotionPresentation {
  emoji_mapping: Record<string, EmojiMapping>;
  color_mapping: Record<string, string>;
  animation_mapping: Record<string, string>;
}

export interface EmojiMappingResult {
  emoji: string;
  color: string;
  animation: string;
  source: 'system' | 'user' | 'pack_override';
}

export class EmojiMappingService {
  private userPrefsRepo: UserQuizPreferencesRepository;
  private packOverridesRepo: QuizPackOverridesRepository;

  // 系统默认emoji映射
  private static readonly SYSTEM_DEFAULT_MAPPING: EmotionPresentation = {
    emoji_mapping: {
      happy: { primary: "😊", alternatives: ["😄", "😃", "🙂", "😌"] },
      sad: { primary: "😢", alternatives: ["😭", "😞", "☹️", "😔"] },
      angry: { primary: "😠", alternatives: ["😡", "🤬", "😤", "😒"] },
      fearful: { primary: "😨", alternatives: ["😰", "😱", "😧", "😟"] },
      surprised: { primary: "😲", alternatives: ["😮", "😯", "🤯", "😦"] },
      disgusted: { primary: "🤢", alternatives: ["🤮", "😖", "😣", "🙄"] },
      neutral: { primary: "😐", alternatives: ["😑", "😶", "🤔", "😏"] },
      excited: { primary: "🤩", alternatives: ["🎉", "⚡", "🚀", "✨"] },
      grateful: { primary: "🙏", alternatives: ["💖", "🌺", "🌸", "💝"] },
      playful: { primary: "😜", alternatives: ["😋", "🤪", "😝", "🤭"] },
      content: { primary: "😌", alternatives: ["😊", "🙂", "😇", "🥰"] }
    },
    color_mapping: {
      happy: "#4CAF50", sad: "#2196F3", angry: "#F44336", fearful: "#9C27B0",
      surprised: "#FF9800", disgusted: "#795548", neutral: "#9E9E9E",
      excited: "#FF6B35", grateful: "#E91E63", playful: "#9C27B0", content: "#4CAF50"
    },
    animation_mapping: {
      happy: "bounce", sad: "fade", angry: "shake", fearful: "tremble",
      surprised: "pop", disgusted: "recoil", neutral: "none",
      excited: "pulse", grateful: "glow", playful: "wiggle", content: "gentle"
    }
  };

  constructor(db?: any) {
    this.userPrefsRepo = new UserQuizPreferencesRepository('user_presentation_configs', db);
    this.packOverridesRepo = new QuizPackOverridesRepository('pack_presentation_overrides', db);
  }

  /**
   * 获取选项的emoji映射
   * 优先级: Pack Override > User Preference > System Default
   */
  async getOptionPresentation(
    userId: string,
    packId: string,
    optionValue: string
  ): Promise<EmojiMappingResult> {
    try {
      // 1. 尝试获取包特定覆盖
      const packOverride = await this.getPackOverrideMapping(userId, packId, optionValue);
      if (packOverride) {
        return { ...packOverride, source: 'pack_override' };
      }

      // 2. 尝试获取用户全局配置
      const userMapping = await this.getUserGlobalMapping(userId, optionValue);
      if (userMapping) {
        return { ...userMapping, source: 'user' };
      }

      // 3. 使用系统默认
      const systemMapping = this.getSystemDefaultMapping(optionValue);
      return { ...systemMapping, source: 'system' };

    } catch (error) {
      console.error('Error getting option presentation:', error);
      // 出错时返回系统默认
      const systemMapping = this.getSystemDefaultMapping(optionValue);
      return { ...systemMapping, source: 'system' };
    }
  }

  /**
   * 获取包特定的emoji覆盖
   */
  private async getPackOverrideMapping(
    userId: string,
    packId: string,
    optionValue: string
  ): Promise<Omit<EmojiMappingResult, 'source'> | null> {
    try {
      const override = await this.packOverridesRepo.findByUserAndPack(userId, packId);
      if (!override || !override.presentation_overrides) {
        return null;
      }

      const config = JSON.parse(override.presentation_overrides);
      const emotionPresentation = config?.layer4_view_detail?.emotion_presentation;
      
      if (!emotionPresentation) {
        return null;
      }

      return this.extractPresentationFromConfig(emotionPresentation, optionValue);
    } catch (error) {
      console.error('Error getting pack override mapping:', error);
      return null;
    }
  }

  /**
   * 获取用户全局emoji配置
   */
  private async getUserGlobalMapping(
    userId: string,
    optionValue: string
  ): Promise<Omit<EmojiMappingResult, 'source'> | null> {
    try {
      const userPrefs = await this.userPrefsRepo.findByUserId(userId);
      if (!userPrefs || !userPrefs.presentation_config) {
        return null;
      }

      const config = JSON.parse(userPrefs.presentation_config);
      const emotionPresentation = config?.layer4_view_detail?.emotion_presentation;
      
      if (!emotionPresentation) {
        return null;
      }

      return this.extractPresentationFromConfig(emotionPresentation, optionValue);
    } catch (error) {
      console.error('Error getting user global mapping:', error);
      return null;
    }
  }

  /**
   * 从配置中提取展现信息
   */
  private extractPresentationFromConfig(
    emotionPresentation: any,
    optionValue: string
  ): Omit<EmojiMappingResult, 'source'> | null {
    const emojiMapping = emotionPresentation.emoji_mapping?.[optionValue];
    const color = emotionPresentation.color_mapping?.[optionValue];
    const animation = emotionPresentation.animation_mapping?.[optionValue];

    if (!emojiMapping?.primary) {
      return null;
    }

    return {
      emoji: emojiMapping.primary,
      color: color || EmojiMappingService.SYSTEM_DEFAULT_MAPPING.color_mapping[optionValue] || '#9E9E9E',
      animation: animation || EmojiMappingService.SYSTEM_DEFAULT_MAPPING.animation_mapping[optionValue] || 'none'
    };
  }

  /**
   * 获取系统默认映射
   */
  private getSystemDefaultMapping(optionValue: string): Omit<EmojiMappingResult, 'source'> {
    const defaultMapping = EmojiMappingService.SYSTEM_DEFAULT_MAPPING;
    
    return {
      emoji: defaultMapping.emoji_mapping[optionValue]?.primary || '📝',
      color: defaultMapping.color_mapping[optionValue] || '#9E9E9E',
      animation: defaultMapping.animation_mapping[optionValue] || 'none'
    };
  }

  /**
   * 更新用户的emoji映射配置
   */
  async updateUserEmojiMapping(
    userId: string,
    optionValue: string,
    mapping: EmojiMapping,
    color?: string,
    animation?: string
  ): Promise<boolean> {
    try {
      const userPrefs = await this.userPrefsRepo.findByUserId(userId);
      if (!userPrefs) {
        return false;
      }

      const config = JSON.parse(userPrefs.presentation_config);
      
      // 确保结构存在
      if (!config.layer4_view_detail) {
        config.layer4_view_detail = {};
      }
      if (!config.layer4_view_detail.emotion_presentation) {
        config.layer4_view_detail.emotion_presentation = {
          emoji_mapping: {},
          color_mapping: {},
          animation_mapping: {}
        };
      }

      // 更新映射
      config.layer4_view_detail.emotion_presentation.emoji_mapping[optionValue] = mapping;
      
      if (color) {
        config.layer4_view_detail.emotion_presentation.color_mapping[optionValue] = color;
      }
      
      if (animation) {
        config.layer4_view_detail.emotion_presentation.animation_mapping[optionValue] = animation;
      }

      // 保存更新
      await this.userPrefsRepo.update(userPrefs.id, {
        presentation_config: JSON.stringify(config)
      });

      return true;
    } catch (error) {
      console.error('Error updating user emoji mapping:', error);
      return false;
    }
  }

  /**
   * 更新包特定的emoji映射覆盖
   */
  async updatePackEmojiOverride(
    userId: string,
    packId: string,
    optionValue: string,
    mapping: EmojiMapping,
    color?: string,
    animation?: string
  ): Promise<boolean> {
    try {
      let override = await this.packOverridesRepo.findByUserAndPack(userId, packId);
      
      let config: any = {};
      if (override?.presentation_overrides) {
        config = JSON.parse(override.presentation_overrides);
      }

      // 确保结构存在
      if (!config.layer4_view_detail) {
        config.layer4_view_detail = {};
      }
      if (!config.layer4_view_detail.emotion_presentation) {
        config.layer4_view_detail.emotion_presentation = {
          emoji_mapping: {},
          color_mapping: {},
          animation_mapping: {}
        };
      }

      // 更新映射
      config.layer4_view_detail.emotion_presentation.emoji_mapping[optionValue] = mapping;
      
      if (color) {
        config.layer4_view_detail.emotion_presentation.color_mapping[optionValue] = color;
      }
      
      if (animation) {
        config.layer4_view_detail.emotion_presentation.animation_mapping[optionValue] = animation;
      }

      if (override) {
        // 更新现有覆盖
        await this.packOverridesRepo.update(override.id, {
          presentation_overrides: JSON.stringify(config)
        });
      } else {
        // 创建新覆盖
        await this.packOverridesRepo.create({
          id: `override_${userId}_${packId}_${Date.now()}`,
          user_id: userId,
          pack_id: packId,
          presentation_overrides: JSON.stringify(config),
          override_reason: 'user_preference',
          override_priority: 1,
          is_active: true
        });
      }

      return true;
    } catch (error) {
      console.error('Error updating pack emoji override:', error);
      return false;
    }
  }

  /**
   * 获取选项的所有可用emoji选择
   */
  async getAvailableEmojis(
    userId: string,
    packId: string,
    optionValue: string
  ): Promise<string[]> {
    try {
      const presentation = await this.getOptionPresentation(userId, packId, optionValue);
      const systemMapping = EmojiMappingService.SYSTEM_DEFAULT_MAPPING.emoji_mapping[optionValue];
      
      if (systemMapping) {
        return [presentation.emoji, ...systemMapping.alternatives].filter((emoji, index, arr) => 
          arr.indexOf(emoji) === index // 去重
        );
      }

      return [presentation.emoji];
    } catch (error) {
      console.error('Error getting available emojis:', error);
      return ['📝'];
    }
  }

  /**
   * 获取所有可用的表情符号集
   * 返回系统预定义的表情符号集合
   */
  async getAllEmojiSets(): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      // 返回基于系统默认映射的表情符号集
      const emojiSets = [
        {
          id: 'default',
          name: 'Default Emoji Set',
          description: 'System default emoji mappings',
          is_active: true,
          items: EmojiMappingService.SYSTEM_DEFAULT_MAPPING.emoji_mapping
        },
        {
          id: 'minimal',
          name: 'Minimal Emoji Set',
          description: 'Simple emoji mappings',
          is_active: false,
          items: {
            happy: { primary: "🙂", alternatives: ["😊"] },
            sad: { primary: "😔", alternatives: ["😢"] },
            angry: { primary: "😠", alternatives: ["😡"] },
            neutral: { primary: "😐", alternatives: ["😑"] }
          }
        }
      ];

      return { success: true, data: emojiSets };
    } catch (error) {
      console.error('Error getting all emoji sets:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 设置活动的表情符号集
   * 更新用户的全局表情符号配置
   */
  async setActiveEmojiSet(setId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // 这里可以实现设置活动表情符号集的逻辑
      // 目前返回成功，因为表情符号映射是通过用户配置管理的
      console.log(`Setting active emoji set: ${setId}`);

      // TODO: 实现实际的设置逻辑
      // 可能需要更新用户的全局配置来指定默认的表情符号集

      return { success: true };
    } catch (error) {
      console.error('Error setting active emoji set:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 获取用户的表情符号映射统计
   */
  async getUserEmojiMappingStats(userId: string): Promise<{
    totalCustomMappings: number;
    packOverrides: number;
    lastUpdated: Date | null;
  }> {
    try {
      const userPrefs = await this.userPrefsRepo.findByUserId(userId);
      const packOverrides = await this.packOverridesRepo.findByUserId(userId);

      let totalCustomMappings = 0;
      let lastUpdated: Date | null = null;

      // 统计用户全局自定义映射
      if (userPrefs?.presentation_config) {
        const config = JSON.parse(userPrefs.presentation_config);
        const emojiMappings = config?.layer4_view_detail?.emotion_presentation?.emoji_mapping || {};
        totalCustomMappings += Object.keys(emojiMappings).length;

        if (userPrefs.updated_at) {
          lastUpdated = new Date(userPrefs.updated_at);
        }
      }

      // 统计包特定覆盖
      let packOverrideCount = 0;
      if (packOverrides && Array.isArray(packOverrides)) {
        for (const override of packOverrides) {
          if (override.presentation_overrides) {
            const config = JSON.parse(override.presentation_overrides);
            const emojiMappings = config?.layer4_view_detail?.emotion_presentation?.emoji_mapping || {};
            packOverrideCount += Object.keys(emojiMappings).length;

            if (override.updated_at) {
              const overrideDate = new Date(override.updated_at);
              if (!lastUpdated || overrideDate > lastUpdated) {
                lastUpdated = overrideDate;
              }
            }
          }
        }
      }

      return {
        totalCustomMappings,
        packOverrides: packOverrideCount,
        lastUpdated
      };
    } catch (error) {
      console.error('Error getting user emoji mapping stats:', error);
      return {
        totalCustomMappings: 0,
        packOverrides: 0,
        lastUpdated: null
      };
    }
  }

  /**
   * 重置用户的表情符号映射到系统默认
   */
  async resetUserEmojiMappings(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const userPrefs = await this.userPrefsRepo.findByUserId(userId);
      if (!userPrefs) {
        return { success: false, error: 'User preferences not found' };
      }

      const config = JSON.parse(userPrefs.presentation_config);

      // 重置emoji映射到系统默认
      if (config.layer4_view_detail?.emotion_presentation) {
        config.layer4_view_detail.emotion_presentation = {
          emoji_mapping: {},
          color_mapping: {},
          animation_mapping: {}
        };
      }

      await this.userPrefsRepo.update(userPrefs.id, {
        presentation_config: JSON.stringify(config)
      });

      return { success: true };
    } catch (error) {
      console.error('Error resetting user emoji mappings:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 设置数据库连接
   */
  setDb(db: any): void {
    this.userPrefsRepo.setDatabase(db);
    this.packOverridesRepo.setDatabase(db);
  }
}
