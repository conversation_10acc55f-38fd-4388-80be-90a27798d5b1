#!/usr/bin/env node

/**
 * Biome 迁移脚本
 * 帮助从 ESLint + Prettier 迁移到 Biome
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 开始迁移到 Biome...\n');

// 1. 安装 Biome
console.log('📦 安装 Biome...');
try {
  execSync('npm install --save-dev @biomejs/biome', { stdio: 'inherit' });
  console.log('✅ Biome 安装成功\n');
} catch (error) {
  console.error('❌ Biome 安装失败:', error.message);
  process.exit(1);
}

// 2. 检查现有配置
console.log('🔍 检查现有配置...');
const hasESLint = fs.existsSync('eslint.config.js') || fs.existsSync('.eslintrc.js') || fs.existsSync('.eslintrc.json');
const hasPrettier = fs.existsSync('.prettierrc') || fs.existsSync('prettier.config.js') || fs.existsSync('.prettierrc.json');

console.log(`ESLint 配置: ${hasESLint ? '✅ 找到' : '❌ 未找到'}`);
console.log(`Prettier 配置: ${hasPrettier ? '✅ 找到' : '❌ 未找到'}\n`);

// 3. 创建 Biome 配置（如果不存在）
if (!fs.existsSync('biome.json')) {
  console.log('📝 创建 Biome 配置...');
  console.log('✅ biome.json 已创建\n');
} else {
  console.log('📝 biome.json 已存在，跳过创建\n');
}

// 4. 更新 package.json 脚本
console.log('📝 更新 package.json 脚本...');
try {
  const packageJsonPath = 'package.json';
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // 添加 Biome 脚本
  packageJson.scripts = {
    ...packageJson.scripts,
    'format': 'biome format --write .',
    'format:check': 'biome format .',
    'lint:biome': 'biome lint .',
    'lint:biome:fix': 'biome lint --write .',
    'check': 'biome check .',
    'check:fix': 'biome check --write .',
    'ci:check': 'biome ci .'
  };
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ package.json 脚本已更新\n');
} catch (error) {
  console.error('❌ 更新 package.json 失败:', error.message);
}

// 5. 运行初始格式化
console.log('🎨 运行初始格式化...');
try {
  execSync('npx biome format --write .', { stdio: 'inherit' });
  console.log('✅ 代码格式化完成\n');
} catch (error) {
  console.warn('⚠️ 格式化过程中有警告，请检查输出\n');
}

// 6. 运行 Linting
console.log('🔍 运行代码检查...');
try {
  execSync('npx biome lint .', { stdio: 'inherit' });
  console.log('✅ 代码检查完成\n');
} catch (error) {
  console.warn('⚠️ 代码检查发现问题，请查看上面的输出\n');
}

// 7. 创建 VS Code 设置建议
console.log('💡 创建 VS Code 设置建议...');
const vscodeDir = '.vscode';
const settingsPath = path.join(vscodeDir, 'settings.json');

if (!fs.existsSync(vscodeDir)) {
  fs.mkdirSync(vscodeDir);
}

const vscodeSettings = {
  "editor.defaultFormatter": "biomejs.biome",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[css]": {
    "editor.defaultFormatter": "biomejs.biome"
  }
};

try {
  let existingSettings = {};
  if (fs.existsSync(settingsPath)) {
    existingSettings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
  }
  
  const mergedSettings = { ...existingSettings, ...vscodeSettings };
  fs.writeFileSync(settingsPath, JSON.stringify(mergedSettings, null, 2));
  console.log('✅ VS Code 设置已更新\n');
} catch (error) {
  console.error('❌ 更新 VS Code 设置失败:', error.message);
}

// 8. 显示迁移完成信息
console.log('🎉 Biome 迁移完成！\n');
console.log('📋 下一步建议:');
console.log('1. 安装 VS Code Biome 扩展: biomejs.biome');
console.log('2. 运行 "npm run check" 检查所有文件');
console.log('3. 运行 "npm run check:fix" 自动修复问题');
console.log('4. 考虑移除 ESLint 和 Prettier 依赖（可选）');
console.log('\n📚 有用的命令:');
console.log('- npm run format        # 格式化所有文件');
console.log('- npm run lint:biome    # 检查代码质量');
console.log('- npm run check         # 运行所有检查');
console.log('- npm run check:fix     # 修复所有可修复的问题');
console.log('- npm run ci:check      # CI 环境检查');
