# Quiz系统实现指南

## 🎯 已完成的实现

### 1. 数据库设计
- ✅ **Quiz表结构**: `public/seeds/schema/quiz_tables.sql`
- ✅ **索引优化**: `public/seeds/schema/quiz_indexes.sql`
- ✅ **测试数据**: `public/seeds/test/quiz_test_data.sql`
- ✅ **Schema集成**: 更新了 `master.sql` 加载顺序

### 2. 类型定义
- ✅ **Zod Schema**: 在 `src/types/schema/base.ts` 中添加了完整的Quiz相关Schema
- ✅ **TypeScript类型**: 包含所有Quiz实体的类型定义
- ✅ **验证模式**: 运行时数据验证和转换

### 3. tRPC API实现
- ✅ **Quiz路由**: `src/server/trpc/routers/quiz.ts`
- ✅ **个性化路由**: `src/server/trpc/routers/personalization.ts`
- ✅ **类型安全**: 全栈TypeScript类型共享

### 4. 前端界面
- ✅ **Quiz设置页面**: `src/pages/QuizSettings.tsx`
- ✅ **6层配置界面**: 完整的个性化配置UI
- ✅ **路由集成**: 添加到应用路由系统
- ✅ **设置页面链接**: 在主设置页面添加入口

## 🚀 如何使用

### 1. 数据库初始化

```bash
# 1. 确保数据库包含Quiz表
# 数据库初始化时会自动加载以下文件：
# - public/seeds/schema/quiz_tables.sql (表结构)
# - public/seeds/schema/quiz_indexes.sql (索引)

# 2. 加载测试数据 (可选)
# 可以手动执行测试数据文件：
# - public/seeds/test/quiz_test_data.sql
```

### 2. 访问Quiz设置

```bash
# 1. 启动应用
npm run dev

# 2. 访问设置页面
# 导航到 /settings

# 3. 找到"高级功能"卡片
# 点击"Quiz系统设置"按钮

# 4. 或直接访问
# 导航到 /quiz-settings
```

### 3. 配置个性化设置

在Quiz设置页面中，您可以：

- **查看架构概览**: 了解数据与展现分离的设计
- **配置6层个性化**:
  - Layer 0: 数据集展现偏好
  - Layer 1: 基础用户选择
  - Layer 2: 渲染策略配置
  - Layer 3: 皮肤基础设置
  - Layer 4: 视图细节配置
  - Layer 5: 可访问性增强

## 📊 数据结构说明

### 核心表关系

```
quiz_packs (量表包)
├── emotion_data_set_id → emotion_data_sets
├── quiz_logic_config (JSON: 量表逻辑)
└── default_presentation_hints (JSON: 默认展现建议)

user_presentation_configs (用户配置)
├── user_id → users
├── presentation_config (JSON: 6层配置)
└── personalization_level (0-100)

quiz_sessions (会话)
├── pack_id → quiz_packs
├── user_id → users
└── current_tier_id → emotion_data_set_tiers

quiz_answers (答案)
├── session_id → quiz_sessions
├── emotion_id → emotions
└── response_metadata (JSON)

quiz_results (结果)
├── session_id → quiz_sessions
├── emotion_analysis (JSON)
└── recommendations (JSON)
```

### 6层个性化配置结构

```typescript
interface PersonalizationConfig {
  layer0_dataset_presentation: {
    preferred_pack_categories: string[];
    default_difficulty_preference: string;
    session_length_preference: string;
    auto_select_recommended: boolean;
  };
  layer1_user_choice: {
    preferred_view_type: 'wheel' | 'card' | 'bubble' | 'galaxy';
    active_skin_id: string;
    dark_mode: boolean;
    color_mode: 'warm' | 'cool' | 'mixed' | 'game';
    user_level: 'beginner' | 'regular' | 'advanced' | 'vip';
  };
  layer2_rendering_strategy: {
    render_engine_preferences: Record<string, string>;
    content_display_mode_preferences: Record<string, string>;
    layout_preferences: Record<string, string>;
    performance_mode: 'performance' | 'balanced' | 'quality';
  };
  layer3_skin_base: {
    colors: Record<string, string>;
    fonts: FontConfig;
    effects: EffectConfig;
    animations: AnimationConfig;
  };
  layer4_view_detail: {
    wheel_config?: WheelConfig;
    card_config?: CardConfig;
    emotion_presentation: EmotionPresentationConfig;
    interaction_presentation: InteractionPresentationConfig;
  };
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    keyboard_navigation: boolean;
    voice_guidance: boolean;
    emotion_accessibility: EmotionAccessibilityConfig;
    focus_management: FocusManagementConfig;
  };
}
```

## 🔧 API使用示例

### 获取量表包列表

```typescript
import { trpc } from '@/utils/trpc';

// 获取量表包
const { data: quizPacks } = trpc.quiz.packs.list.useQuery({
  category: 'daily',
  difficulty: 'regular',
  limit: 10
});
```

### 创建Quiz会话

```typescript
// 创建新会话
const createSession = trpc.quiz.sessions.create.useMutation();

const session = await createSession.mutateAsync({
  pack_id: 'daily-mood-tracker',
  personalization_overrides: {
    layer1_user_choice: {
      preferred_view_type: 'wheel'
    }
  }
});
```

### 获取个性化配置

```typescript
// 获取用户配置
const { data: userConfig } = trpc.personalization.getUserConfig.useQuery({
  config_name: 'default'
});

// 更新配置
const updateConfig = trpc.personalization.updateConfig.useMutation();

await updateConfig.mutateAsync({
  presentation_config: {
    layer1_user_choice: {
      preferred_view_type: 'card',
      dark_mode: true
    }
  },
  set_as_default: true
});
```

## 🎨 UI组件集成

### ViewFactory集成

Quiz系统与现有的ViewFactory深度集成：

```typescript
// 创建Quiz专用组件
const quizComponent = viewFactory.createQuizComponent(
  'EmotionWheel',
  questionPresentationData,
  {
    onEmotionSelect: handleEmotionSelect,
    realtimeInsights: insights
  }
);
```

### 动态配置应用

```typescript
// 基于个性化配置动态调整UI
const adjustedConfig = applyPersonalizationToWheelConfig(
  baseWheelConfig,
  userPersonalizationConfig
);
```

## 🧪 测试数据

系统包含3个预设的量表包：

1. **中医情绪评估量表** (`tcm-emotion-assessment`)
   - 基于中医理论的情绪评估
   - 适合治疗场景
   - 预计8分钟完成

2. **日常情绪追踪量表** (`daily-mood-tracker`)
   - 快速的日常情绪记录
   - 适合习惯养成
   - 预计3分钟完成

3. **专业心理评估量表** (`professional-psych-assessment`)
   - 深度心理状态评估
   - 适合专业咨询
   - 预计15分钟完成

## 🔄 下一步开发

1. **完善tRPC实现**: 添加更多业务逻辑
2. **实现Quiz引擎**: 核心量表执行逻辑
3. **集成ViewFactory**: 完整的组件渲染系统
4. **添加分析功能**: 情绪模式分析和推荐
5. **优化用户体验**: 实时反馈和动画效果

## 📝 注意事项

- 确保数据库已正确初始化Quiz表
- 个性化配置采用JSON存储，注意数据验证
- tRPC路由需要适当的权限控制
- 前端组件需要处理加载状态和错误情况
- 测试数据仅用于开发，生产环境需要真实数据

这个实现为Quiz系统提供了坚实的基础，支持高度个性化的用户体验和专业级的功能需求。
