/**
 * Better-Auth 客户端配置
 * 与现有的OnlineServices和tRPC集成
 */

import { OnlineServices } from '@/services/online';
import { createAuthClient } from '@better-auth/react';

// 创建认证客户端
export const authClient = createAuthClient({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8788',

  // 与现有系统集成的配置
  fetchOptions: {
    onRequest: (context) => {
      // 添加自定义请求头
      const deviceId = localStorage.getItem('device-id');
      if (deviceId) {
        context.request.headers.set('X-Device-ID', deviceId);
      }

      // 添加应用版本信息
      context.request.headers.set('X-App-Version', process.env.REACT_APP_VERSION || '1.0.0');
    },

    onResponse: (context) => {
      // 处理认证响应
      if (context.response.status === 401) {
        // 认证失败，清理本地状态
        localStorage.removeItem('auth-token');
        // 可以触发重新登录流程
      }
    },

    onError: (context) => {
      console.error('[Auth] Request failed:', context.error);
      // 可以显示错误提示
    },
  },
});

// 导出认证hooks和方法
export const { useSession, signIn, signUp, signOut, useUser, useListSessions, updateUser } =
  authClient;

/**
 * 扩展的认证服务类
 * 集成better-auth和现有的业务逻辑
 */
export class AuthService {
  private static instance: AuthService;

  private constructor() {}

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * 邮箱密码登录
   */
  async signInWithEmail(email: string, password: string, rememberMe = false) {
    try {
      const result = await signIn.email({
        email,
        password,
        rememberMe,
      });

      if (result.data?.user) {
        // 登录成功后同步用户数据到离线存储
        await this.syncUserToOfflineStorage(result.data.user);

        // 触发用户数据同步
        await this.triggerFullDataSync(result.data.user.id);
      }

      return result;
    } catch (error) {
      console.error('[Auth] Sign in failed:', error);
      throw error;
    }
  }

  /**
   * 邮箱密码注册
   */
  async signUpWithEmail(email: string, password: string, displayName?: string) {
    try {
      const result = await signUp.email({
        email,
        password,
        name: displayName,
      });

      if (result.data?.user) {
        // 注册成功后初始化用户数据
        await this.initializeNewUser(result.data.user);
      }

      return result;
    } catch (error) {
      console.error('[Auth] Sign up failed:', error);
      throw error;
    }
  }

  /**
   * Google登录
   */
  async signInWithGoogle() {
    try {
      const result = await signIn.social({
        provider: 'google',
        callbackURL: '/auth/callback',
      });

      return result;
    } catch (error) {
      console.error('[Auth] Google sign in failed:', error);
      throw error;
    }
  }

  /**
   * Apple登录
   */
  async signInWithApple() {
    try {
      const result = await signIn.social({
        provider: 'apple',
        callbackURL: '/auth/callback',
      });

      return result;
    } catch (error) {
      console.error('[Auth] Apple sign in failed:', error);
      throw error;
    }
  }

  /**
   * 登出
   */
  async signOut() {
    try {
      // 清理离线存储中的用户数据
      await this.clearOfflineUserData();

      // 执行better-auth登出
      const result = await signOut();

      // 清理本地缓存
      localStorage.removeItem('auth-token');
      localStorage.removeItem('user-preferences');

      return result;
    } catch (error) {
      console.error('[Auth] Sign out failed:', error);
      throw error;
    }
  }

  /**
   * 更新用户资料
   */
  async updateProfile(data: {
    displayName?: string;
    avatar?: string;
  }) {
    try {
      const result = await updateUser(data);

      if (result.data?.user) {
        // 更新成功后同步到离线存储
        await this.syncUserToOfflineStorage(result.data.user);
      }

      return result;
    } catch (error) {
      console.error('[Auth] Update profile failed:', error);
      throw error;
    }
  }

  /**
   * 获取当前用户的完整信息（包括VIP状态）
   */
  async getCurrentUserProfile() {
    try {
      const onlineServices = OnlineServices.getInstance();
      return await onlineServices.api.auth.me.query();
    } catch (error) {
      console.error('[Auth] Get user profile failed:', error);
      throw error;
    }
  }

  /**
   * 同步用户数据到离线存储
   */
  private async syncUserToOfflineStorage(user: any) {
    try {
      const { Services } = await import('@/services');
      const userService = await Services.user();

      // 只同步非敏感的用户信息
      const userData = {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.displayName,
        avatar: user.avatar,
        isVip: user.isVip,
        vipExpiresAt: user.vipExpiresAt,
        isVerified: user.isVerified,
        lastLoginAt: user.lastLoginAt,
        created_at: user.created_at,
        updated_at: user.updated_at,
      };

      await userService.syncUserFromServer(userData);
      console.log('[Auth] User data synced to offline storage');
    } catch (error) {
      console.error('[Auth] Failed to sync user to offline storage:', error);
    }
  }

  /**
   * 初始化新用户
   */
  private async initializeNewUser(user: any) {
    try {
      // 同步用户数据到离线存储
      await this.syncUserToOfflineStorage(user);

      // 触发完整数据同步
      await this.triggerFullDataSync(user.id);

      // 初始化默认用户配置
      await this.initializeDefaultUserConfig(user.id);

      console.log('[Auth] New user initialized successfully');
    } catch (error) {
      console.error('[Auth] Failed to initialize new user:', error);
    }
  }

  /**
   * 触发完整数据同步
   */
  private async triggerFullDataSync(userId: string) {
    try {
      const { useDataSync } = await import('@/hooks/useDataSync');
      // 这里需要在组件外部调用，可能需要重构
      // 或者直接调用OnlineServices的同步方法
      const onlineServices = OnlineServices.getInstance();
      await onlineServices.performFullSync();

      console.log('[Auth] Full data sync triggered');
    } catch (error) {
      console.error('[Auth] Failed to trigger full data sync:', error);
    }
  }

  /**
   * 初始化默认用户配置
   */
  private async initializeDefaultUserConfig(userId: string) {
    try {
      const { Services } = await import('@/services');
      const userConfigService = await Services.userConfig();

      // 创建默认配置
      const defaultConfig = {
        id: `config_${userId}`,
        name: 'Default Config',
        user_id: userId,
        active_emotion_data_id: 'default',
        active_skin_id: 'default-light',
        preferred_view_type: 'wheel' as const,
        dark_mode: false,
        is_active: true,
        created_at: new Date().toISOString(),
        last_updated: new Date().toISOString(),
      };

      await userConfigService.create(defaultConfig);
      console.log('[Auth] Default user config created');
    } catch (error) {
      console.error('[Auth] Failed to create default user config:', error);
    }
  }

  /**
   * 清理离线用户数据
   */
  private async clearOfflineUserData() {
    try {
      const { Services } = await import('@/services');

      // 清理用户相关的离线数据
      const userService = await Services.user();
      await userService.clearUserData();

      // 清理用户配置
      const userConfigService = await Services.userConfig();
      await userConfigService.clearAll();

      console.log('[Auth] Offline user data cleared');
    } catch (error) {
      console.error('[Auth] Failed to clear offline user data:', error);
    }
  }
}

/**
 * 认证状态管理Hook
 */
export const useAuthState = () => {
  const { data: session, isLoading: sessionLoading } = useSession();
  const { data: user, isLoading: userLoading } = useUser();

  const isLoading = sessionLoading || userLoading;
  const isAuthenticated = !!session && !!user;

  return {
    session,
    user,
    isLoading,
    isAuthenticated,
    authService: AuthService.getInstance(),
  };
};

/**
 * VIP状态管理Hook
 */
export const useVipStatus = () => {
  const { user } = useAuthState();
  const onlineServices = OnlineServices.getInstance();

  const {
    data: vipStatus,
    isLoading,
    refetch,
  } = onlineServices.api.subscription.getVipStatus.useQuery(undefined, {
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  const isVip = vipStatus?.isVip || false;
  const isExpired = vipStatus?.expiresAt ? new Date(vipStatus.expiresAt) < new Date() : false;
  const actualVipStatus = isVip && !isExpired;

  return {
    isVip: actualVipStatus,
    vipStatus,
    isLoading,
    refetch,
  };
};

export default AuthService;
