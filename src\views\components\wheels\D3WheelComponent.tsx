/**
 * D3轮盘组件
 * 使用D3.js实现的轮盘，不依赖旧的轮盘实现
 *
 * 此组件是新视图系统的一部分，用于替代旧的 D3WheelComponent
 * 它直接实现了轮盘的渲染，不依赖旧的 D3Wheel 类
 */

import AnimatedEmoji from '@/components/emoji/AnimatedEmoji';
import { useColorMode } from '@/contexts/ColorModeContext';
import { useEmoji } from '@/contexts/EmojiContext';
import { useTheme } from '@/contexts/ThemeContext';
import type { Emotion, ContentDisplayMode, SkinConfig } from '@/types';
import { assignColorsToEmotions } from '@/utils/colorUtils';
import * as d3 from 'd3';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { extract_wheel_config_for_engine, type WheelConfig } from '../../../utils/wheelConfigExtractor';

interface D3WheelComponentProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  onBack?: () => void;
  selectedPath?: any;
}
/**
 * D3轮盘组件
 */
export const D3WheelComponent: React.FC<D3WheelComponentProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  onBack,
  selectedPath,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [hoveredEmotion, setHoveredEmotion] = useState<string | null>(null);
  const { getEmojiItemForEmotionId } = useEmoji();
  const { colorMode } = useColorMode();
  const { theme } = useTheme();

  // 检查是否为深色模式
  const isDarkMode = theme === 'dark';

  // 提取轮盘配置
  const wheelConfig = extract_wheel_config_for_engine.d3(skinConfig);

  // 状态变量，用于拖拽和缩放
  const [transform, setTransform] = useState({ x: 0, y: 0, scale: 1, rotating: false, angle: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const dragStartRef = useRef({ x: 0, y: 0 });
  const transformRef = useRef(transform);

  // 使用颜色模式为情绪分配颜色
  const emotionsWithColors = assignColorsToEmotions(
    emotions,
    isDarkMode,
    tierLevel === 1 ? 'primary' : tierLevel === 2 ? 'secondary' : 'tertiary',
    colorMode
  );

  // 响应式尺寸调整
  const [containerSize, setContainerSize] = useState(wheelConfig.container_size);

  // 监听窗口大小变化
  useEffect(() => {
    if (!wheelConfig.responsive_scaling) return;

    const handleResize = () => {
      if (!containerRef.current) return;

      // 获取容器宽度
      const containerWidth = containerRef.current.clientWidth;
      const containerHeight = window.innerHeight * 0.8; // 使用视口高度的80%

      // 计算合适的尺寸
      let newSize = Math.min(containerWidth, containerHeight);

      // 限制最小和最大尺寸
      newSize = Math.max(wheelConfig.min_size, Math.min(wheelConfig.max_size, newSize));

      // 更新尺寸
      setContainerSize(newSize);
    };

    // 初始调整
    handleResize();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [wheelConfig.responsive_scaling, wheelConfig.min_size, wheelConfig.max_size]);

  // 更新 transformRef 当 transform 变化时
  useEffect(() => {
    transformRef.current = transform;
  }, [transform]);

  // 处理鼠标按下事件
  const handleMouseDown = (event: React.MouseEvent) => {
    if (!wheelConfig.drag_enabled) return;

    event.preventDefault();
    const { clientX, clientY } = event;
    dragStartRef.current = { x: clientX, y: clientY };
    setIsDragging(true);
  };

  // 处理鼠标移动事件
  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging || !wheelConfig.drag_enabled) return;

    event.preventDefault();
    const { clientX, clientY } = event;
    const dx = clientX - dragStartRef.current.x;
    const dy = clientY - dragStartRef.current.y;

    // 更新拖拽位置
    setTransform((prev) => {
      const newTransform = {
        ...prev,
        x: prev.x + dx,
        y: prev.y + dy,
      };

      // 更新拖拽起始位置
      dragStartRef.current = { x: clientX, y: clientY };
      return newTransform;
    });
  };

  // 处理鼠标释放事件
  const handleMouseUp = () => {
    if (!wheelConfig.drag_enabled) return;
    setIsDragging(false);
  };

  // 处理鼠标滚轮事件
  const handleWheel = (event: React.WheelEvent) => {
    if (!wheelConfig.zoom_enabled) return;

    event.preventDefault();
    const delta = event.deltaY;
    const scaleFactor = delta > 0 ? 0.9 : 1.1; // 缩小或放大

    setTransform((prev) => {
      const newScale = Math.max(0.5, Math.min(3, prev.scale * scaleFactor)); // 限制缩放范围
      return {
        ...prev,
        scale: newScale,
      };
    });
  };

  // 处理旋转
  const handleRotate = (clockwise: boolean) => {
    if (!wheelConfig.rotation_enabled) return;

    setTransform((prev) => {
      const rotationAngle = clockwise ? 15 : -15; // 每次旋转15度
      return {
        ...prev,
        angle: (prev.angle + rotationAngle) % 360,
      };
    });
  };

  // 重置变换
  const resetTransform = () => {
    setTransform({ x: 0, y: 0, scale: 1, rotating: false, angle: 0 });
  };

  useEffect(() => {
    if (!svgRef.current || emotions.length === 0) return;

    // 清除现有内容
    d3.select(svgRef.current).selectAll('*').remove();

    // 设置尺寸和中心点
    const width = containerSize;
    const height = containerSize;
    // 根据容器大小调整轮盘半径
    const radius = wheelConfig.wheel_radius * (containerSize / wheelConfig.container_size);
    const centerX = width / 2;
    const centerY = height / 2;

    // 创建SVG元素
    const svg = d3
      .select(svgRef.current)
      .attr('width', width)
      .attr('height', height)
      .attr('viewBox', `0 0 ${width} ${height}`)
      .attr('style', 'max-width: 100%; height: auto;');

    // 创建轮盘组
    const wheel = svg.append('g').attr(
      'transform',
      `translate(${centerX + transform.x}, ${centerY + transform.y})
                          scale(${transform.scale})
                          rotate(${transform.angle})`
    );

    // 如果启用了旋转动画，添加自动旋转
    if (wheelConfig.rotation_enabled && wheelConfig.rotation_auto_play) {
      const rotateWheel = () => {
        const currentAngle = transformRef.current.angle;
        const newAngle = (currentAngle + (wheelConfig.rotation_clockwise ? 0.2 : -0.2)) % 360;

        setTransform((prev) => ({
          ...prev,
          angle: newAngle,
        }));

        if (wheelConfig.rotation_enabled && wheelConfig.rotation_auto_play) {
          requestAnimationFrame(rotateWheel);
        }
      };

      const animationId = requestAnimationFrame(rotateWheel);
      return () => cancelAnimationFrame(animationId);
    }

    // 根据布局类型创建不同的布局
    let arcs: d3.PieArcDatum<Emotion>[] = [];

    switch (wheelConfig.wheel_layout) {
      case 'concentric': {
        // 同心圆布局
        // 将情绪分成多个同心圆
        const numRings = Math.min(3, Math.ceil(emotionsWithColors.length / 8));
        const emotionsPerRing = Math.ceil(emotionsWithColors.length / numRings);

        // 创建多个饼图，每个饼图代表一个同心圆
        for (let ringIndex = 0; ringIndex < numRings; ringIndex++) {
          const startIndex = ringIndex * emotionsPerRing;
          const endIndex = Math.min(startIndex + emotionsPerRing, emotionsWithColors.length);
          const ringEmotions = emotionsWithColors.slice(startIndex, endIndex);

          if (ringEmotions.length === 0) continue;

          // 创建饼图生成器
          const pie = d3
            .pie<Emotion>()
            .value(() => 1) // 每个扇区大小相等
            .sort(null); // 不排序，保持原始顺序

          // 创建弧形生成器
          const ringRadius = radius * (1 - ringIndex * 0.3);
          const innerRadius = ringIndex === 0 ? 0 : radius * (1 - (ringIndex + 0.8) * 0.3);

          const arc = d3
            .arc<d3.PieArcDatum<Emotion>>()
            .innerRadius(innerRadius)
            .outerRadius(ringRadius);

          // 生成扇区数据
          const ringArcs = pie(ringEmotions);

          // 添加到弧形数组
          ringArcs.forEach((arcData) => {
            // 保存弧形生成器和圆环索引
            (arcData as any).arc = arc;
            (arcData as any).ringIndex = ringIndex;
            arcs.push(arcData);
          });
        }
        break;
      }
      case 'spiral': {
        // 螺旋布局
        const pie = d3
          .pie<Emotion>()
          .value(() => 1)
          .sort(null);

        const spiralArcs = pie(emotionsWithColors);

        // 为每个扇区创建不同的弧形生成器
        spiralArcs.forEach((arcData, i) => {
          const angle = (i / emotionsWithColors.length) * 2 * Math.PI;
          const spiralFactor = 1 + (i / emotionsWithColors.length) * 0.5;

          const arc = d3
            .arc<d3.PieArcDatum<Emotion>>()
            .innerRadius(0)
            .outerRadius(radius * spiralFactor)
            .startAngle(angle)
            .endAngle(angle + (2 * Math.PI) / emotionsWithColors.length);

          // 保存弧形生成器
          (arcData as any).arc = arc;
          arcs.push(arcData);
        });
        break;
      }
      case 'flower': {
        // 花瓣布局
        const pie = d3
          .pie<Emotion>()
          .value(() => 1)
          .sort(null);

        const flowerArcs = pie(emotionsWithColors);

        // 为每个扇区创建不同的弧形生成器
        flowerArcs.forEach((arcData, i) => {
          const petalRadius = radius * (0.8 + Math.sin(i * 0.5) * 0.2);

          const arc = d3
            .arc<d3.PieArcDatum<Emotion>>()
            .innerRadius(radius * 0.2)
            .outerRadius(petalRadius)
            .padAngle(0.03);

          // 保存弧形生成器
          (arcData as any).arc = arc;
          arcs.push(arcData);
        });
        break;
      }
      default: {
        // 默认标准轮盘布局
        // 创建饼图生成器
        const pie = d3
          .pie<Emotion>()
          .value(() => 1) // 每个扇区大小相等
          .sort(null); // 不排序，保持原始顺序

        // 创建弧形生成器
        const arc = d3.arc<d3.PieArcDatum<Emotion>>().innerRadius(0).outerRadius(radius);

        // 生成扇区数据
        arcs = pie(emotionsWithColors);

        // 为每个弧形保存弧形生成器
        arcs.forEach((arcData) => {
          (arcData as any).arc = arc;
        });
        break;
      }
    }

    // 绘制扇区
    wheel
      .selectAll('path')
      .data(arcs)
      .enter()
      .append('path')
      .attr('d', (d) => (d as any).arc(d)) // 使用保存的弧形生成器
      .attr('fill', (d) => d.data.color) // 使用分配的颜色
      .attr('stroke', wheelConfig.background_color)
      .attr('stroke-width', wheelConfig.sector_gap)
      .style('cursor', 'pointer')
      .style('transition', `all ${wheelConfig.transition_duration}ms`)
      .on('mouseover', function (_, d) {
        setHoveredEmotion(d.data.id);
        const element = d3.select(this);

        // 应用不同的悬停效果
        switch (wheelConfig.hover_effect) {
          case 'scale':
            element
              .transition()
              .duration(wheelConfig.transition_duration)
              .attr('transform', 'scale(1.05)');
            break;
          case 'glow': {
            // 添加发光滤镜
            const filterId = `glow-${d.data.id}`;
            const defs = svg.select('defs');

            // 检查是否已存在滤镜
            if (defs.select(`#${filterId}`).empty()) {
              const filter = defs
                .append('filter')
                .attr('id', filterId)
                .attr('x', '-50%')
                .attr('y', '-50%')
                .attr('width', '200%')
                .attr('height', '200%');

              filter.append('feGaussianBlur').attr('stdDeviation', '3').attr('result', 'blur');

              filter
                .append('feFlood')
                .attr('flood-color', wheelConfig.colors?.accent || '#FFFFFF')
                .attr('flood-opacity', '0.7')
                .attr('result', 'color');

              filter
                .append('feComposite')
                .attr('in', 'color')
                .attr('in2', 'blur')
                .attr('operator', 'in')
                .attr('result', 'glow');

              filter
                .append('feMerge')
                .selectAll('feMergeNode')
                .data(['glow', 'SourceGraphic'])
                .enter()
                .append('feMergeNode')
                .attr('in', (d) => d);
            }

            element.attr('filter', `url(#${filterId})`);
            break;
          }
          case 'lift':
            // 添加阴影和轻微缩放
            element
              .transition()
              .duration(wheelConfig.transition_duration)
              .attr('transform', 'translate(0, -5) scale(1.02)')
              .style('filter', 'drop-shadow(0 6px 8px rgba(0, 0, 0, 0.3))');
            break;
          case 'pulse':
            // 添加脉冲动画
            element
              .transition()
              .duration(wheelConfig.transition_duration / 2)
              .attr('transform', 'scale(1.05)')
              .transition()
              .duration(wheelConfig.transition_duration / 2)
              .attr('transform', 'scale(1)')
              .transition()
              .duration(wheelConfig.transition_duration / 2)
              .attr('transform', 'scale(1.03)')
              .transition()
              .duration(wheelConfig.transition_duration / 2)
              .attr('transform', 'scale(1)');
            break;
          case 'highlight': {
            // 改变填充颜色
            const originalColor = d.data.color;
            const brighterColor =
              d3.color(originalColor)?.brighter(0.5).toString() || originalColor;
            element
              .transition()
              .duration(wheelConfig.transition_duration)
              .attr('fill', brighterColor);
            break;
          }
          default:
            // 不应用效果
            break;
        }
      })
      .on('mouseout', function (_, d) {
        setHoveredEmotion(null);
        const element = d3.select(this);

        // 重置悬停效果
        switch (wheelConfig.hover_effect) {
          case 'scale':
          case 'lift':
          case 'pulse':
            element
              .transition()
              .duration(wheelConfig.transition_duration)
              .attr('transform', 'scale(1)')
              .style('filter', null);
            break;
          case 'glow':
            element.attr('filter', null);
            break;
          case 'highlight':
            element
              .transition()
              .duration(wheelConfig.transition_duration)
              .attr('fill', d.data.color);
            break;
          default:
            // 不应用效果
            break;
        }
      })
      .on('click', function (_, d) {
        const element = d3.select(this);

        // 应用选择动画效果
        switch (wheelConfig.selection_animation) {
          case 'pulse':
            element
              .transition()
              .duration(wheelConfig.transition_duration / 4)
              .attr('transform', 'scale(1.1)')
              .transition()
              .duration(wheelConfig.transition_duration / 4)
              .attr('transform', 'scale(0.95)')
              .transition()
              .duration(wheelConfig.transition_duration / 4)
              .attr('transform', 'scale(1.05)')
              .transition()
              .duration(wheelConfig.transition_duration / 4)
              .attr('transform', 'scale(1)')
              .on('end', () => onSelect(d.data));
            break;
          case 'spin':
            element
              .transition()
              .duration(wheelConfig.transition_duration)
              .attrTween('transform', () => (t) => {
                const scale = 1 + 0.1 * Math.sin(Math.PI * t);
                const rotate = 360 * t;
                return `rotate(${rotate}) scale(${scale})`;
              })
              .on('end', function () {
                d3.select(this)
                  .transition()
                  .duration(100)
                  .attr('transform', 'rotate(0) scale(1)')
                  .on('end', () => onSelect(d.data));
              });
            break;
          case 'bounce':
            element
              .transition()
              .duration(wheelConfig.transition_duration / 5)
              .attr('transform', 'scale(1.1) translate(0, -10)')
              .transition()
              .duration(wheelConfig.transition_duration / 5)
              .attr('transform', 'scale(1) translate(0, 0)')
              .transition()
              .duration(wheelConfig.transition_duration / 5)
              .attr('transform', 'scale(1.05) translate(0, -5)')
              .transition()
              .duration(wheelConfig.transition_duration / 5)
              .attr('transform', 'scale(1) translate(0, 0)')
              .transition()
              .duration(wheelConfig.transition_duration / 5)
              .attr('transform', 'scale(1.02) translate(0, -2)')
              .transition()
              .duration(wheelConfig.transition_duration / 5)
              .attr('transform', 'scale(1) translate(0, 0)')
              .on('end', () => onSelect(d.data));
            break;
          case 'flash': {
            const originalColor = d.data.color;
            const flashColor = wheelConfig.colors?.accent || '#FFFFFF';

            element
              .transition()
              .duration(wheelConfig.transition_duration / 6)
              .attr('fill', flashColor)
              .transition()
              .duration(wheelConfig.transition_duration / 6)
              .attr('fill', originalColor)
              .transition()
              .duration(wheelConfig.transition_duration / 6)
              .attr('fill', flashColor)
              .transition()
              .duration(wheelConfig.transition_duration / 6)
              .attr('fill', originalColor)
              .transition()
              .duration(wheelConfig.transition_duration / 6)
              .attr('fill', flashColor)
              .transition()
              .duration(wheelConfig.transition_duration / 6)
              .attr('fill', originalColor)
              .on('end', () => onSelect(d.data));
            break;
          }
          case 'fade':
            element
              .transition()
              .duration(wheelConfig.transition_duration / 2)
              .style('opacity', 0.3)
              .transition()
              .duration(wheelConfig.transition_duration / 2)
              .style('opacity', 1)
              .on('end', () => onSelect(d.data));
            break;
          default:
            // 直接调用选择回调
            onSelect(d.data);
            break;
        }
      });

    // 添加文本和表情
    wheel
      .selectAll('g.label')
      .data(arcs)
      .enter()
      .append('g')
      .attr('class', 'label')
      .attr('transform', (d) => {
        // 计算扇区中心点
        const centroid = (d as any).arc.centroid(d);

        // 根据布局类型调整位置
        let x = centroid[0];
        let y = centroid[1];

        if (wheelConfig.wheelLayout === 'concentric') {
          // 同心圆布局，根据圆环索引调整位置
          const ringIndex = (d as any).ringIndex || 0;
          const factor = 0.7 - ringIndex * 0.1;
          x *= factor;
          y *= factor;
        } else if (wheelConfig.wheelLayout === 'spiral') {
          // 螺旋布局，调整位置使文本位于扇区中心
          x *= 0.6;
          y *= 0.6;
        } else if (wheelConfig.wheelLayout === 'flower') {
          // 花瓣布局，调整位置使文本位于花瓣中心
          x *= 0.6;
          y *= 0.6;
        } else {
          // 默认布局，调整位置使文本位于扇区中心
          x *= 0.7;
          y *= 0.7;
        }

        return `translate(${x}, ${y})`;
      })
      .each(function (d) {
        const g = d3.select(this);

        // 根据内容类型显示文本和表情
        if (contentDisplayMode === 'text' || contentDisplayMode === 'textEmoji') {
          g.append('text')
            .attr('text-anchor', 'middle')
            .attr('dy', contentDisplayMode === 'textEmoji' ? '1.5em' : '0.3em')
            .attr('fill', wheelConfig.text_color)
            .attr('font-family', wheelConfig.font_family)
            .attr('font-size', wheelConfig.font_size)
            .text(d.data.name);
        }

        if (contentDisplayMode === 'emoji' || contentDisplayMode === 'textEmoji') {
          g.append('text')
            .attr('text-anchor', 'middle')
            .attr('dy', contentDisplayMode === 'textEmoji' ? '-0.5em' : '0.3em')
            .attr('fill', wheelConfig.text_color)
            .attr('font-family', wheelConfig.font_family)
            .attr('font-size', wheelConfig.emojiSize)
            .text(d.data.emoji);
        }

        if (contentDisplayMode === 'image' || contentDisplayMode === 'textImage') {
          // 检查情绪是否有图片
          if (d.data.image_url) {
            // 创建图片元素
            const imageSize = wheelConfig.emojiSize * 1.5;

            // 创建一个剪切路径，使图片呈现圆形
            const clipPathId = `clip-${d.data.id}`;
            const defs = g.append('defs');

            defs
              .append('clipPath')
              .attr('id', clipPathId)
              .append('circle')
              .attr('r', imageSize / 2);

            // 添加图片
            g.append('image')
              .attr('href', d.data.image_url)
              .attr('width', imageSize)
              .attr('height', imageSize)
              .attr('x', -imageSize / 2)
              .attr('y', contentDisplayMode === 'textImage' ? -imageSize / 2 - 10 : -imageSize / 2)
              .attr('clip-path', `url(#${clipPathId})`);

            // 如果是 textImage 模式或需要添加文本标签
            if (contentDisplayMode === 'textImage') {
              g.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', imageSize / 2 + 15)
                .attr('fill', wheelConfig.text_color)
                .attr('font-family', wheelConfig.font_family)
                .attr('font-size', wheelConfig.font_size)
                .text(d.data.name);
            }
          } else {
            // 如果没有图片，回退到表情
            g.append('text')
              .attr('text-anchor', 'middle')
              .attr('dy', '0.3em')
              .attr('fill', wheelConfig.text_color)
              .attr('font-family', wheelConfig.font_family)
              .attr('font-size', wheelConfig.emojiSize)
              .text(d.data.emoji);

            // 如果是 textImage 模式，添加文本
            if (contentDisplayMode === 'textImage') {
              g.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', '1.5em')
                .attr('fill', wheelConfig.text_color)
                .attr('font-family', wheelConfig.font_family)
                .attr('font-size', wheelConfig.font_size)
                .text(d.data.name);
            }
          }
        }

        if (contentDisplayMode === 'mixed') {
          // 根据情绪的可用内容自动选择显示方式
          if (d.data.image_url) {
            // 如果有图片，显示图片和文本
            const imageSize = wheelConfig.emojiSize * 1.5;

            // 创建一个剪切路径，使图片呈现圆形
            const clipPathId = `clip-${d.data.id}`;
            const defs = g.append('defs');

            defs
              .append('clipPath')
              .attr('id', clipPathId)
              .append('circle')
              .attr('r', imageSize / 2);

            // 添加图片
            g.append('image')
              .attr('href', d.data.image_url)
              .attr('width', imageSize)
              .attr('height', imageSize)
              .attr('x', -imageSize / 2)
              .attr('y', -imageSize / 2 - 10)
              .attr('clip-path', `url(#${clipPathId})`);

            // 添加文本
            g.append('text')
              .attr('text-anchor', 'middle')
              .attr('dy', imageSize / 2 + 15)
              .attr('fill', wheelConfig.text_color)
              .attr('font-family', wheelConfig.font_family)
              .attr('font-size', wheelConfig.font_size)
              .text(d.data.name);
          } else if (d.data.emoji) {
            // 如果有表情集，显示表情和文本
            g.append('text')
              .attr('text-anchor', 'middle')
              .attr('dy', '-0.5em')
              .attr('fill', wheelConfig.text_color)
              .attr('font-family', wheelConfig.font_family)
              .attr('font-size', wheelConfig.emojiSize)
              .text(d.data.emoji);

            g.append('text')
              .attr('text-anchor', 'middle')
              .attr('dy', '1.5em')
              .attr('fill', wheelConfig.text_color)
              .attr('font-family', wheelConfig.font_family)
              .attr('font-size', wheelConfig.font_size)
              .text(d.data.name);
          } else {
            // 如果只有基本信息，显示文本
            g.append('text')
              .attr('text-anchor', 'middle')
              .attr('dy', '0.3em')
              .attr('fill', wheelConfig.text_color)
              .attr('font-family', wheelConfig.font_family)
              .attr('font-size', wheelConfig.font_size)
              .text(d.data.name);
          }
        }
      });

    // 添加装饰效果
    if (wheelConfig.decorations) {
      wheel
        .append('circle')
        .attr('cx', 0)
        .attr('cy', 0)
        .attr('r', radius + 5)
        .attr('fill', 'none')
        .attr('stroke', wheelConfig.shadowColor)
        .attr('stroke-width', 2)
        .attr('opacity', 0.5);
    }

    // 添加阴影效果
    if (wheelConfig.shadow_enabled) {
      svg
        .append('defs')
        .append('filter')
        .attr('id', 'shadow')
        .append('feDropShadow')
        .attr('dx', 0)
        .attr('dy', 0)
        .attr('stdDeviation', wheelConfig.shadowBlur)
        .attr('flood-color', wheelConfig.shadowColor);

      wheel.attr('filter', 'url(#shadow)');
    }
  }, [
    emotionsWithColors,
    tierLevel,
    contentDisplayMode,
    skinConfig,
    wheelConfig,
    onSelect,
    colorMode,
    isDarkMode,
    containerSize,
    transform,
  ]);

  // 渲染动画表情（如果内容类型为 animatedEmoji）
  const renderAnimatedEmojis = () => {
    if (contentDisplayMode !== 'animatedEmoji' || !emotionsWithColors.length) return null;

    return emotionsWithColors.map((emotion, index) => {
      const angle = (index / emotionsWithColors.length) * 2 * Math.PI;
      const radius = wheelConfig.wheel_radius * 0.7; // 调整半径，使表情位于扇区中心
      const x = Math.cos(angle) * radius + wheelConfig.container_size / 2;
      const y = Math.sin(angle) * radius + wheelConfig.container_size / 2;

      const emojiItem = getEmojiItemForEmotionId(emotion.id);

      return (
        <div
          key={emotion.id}
          style={{
            position: 'absolute',
            left: `${x}px`,
            top: `${y}px`,
            transform: 'translate(-50%, -50%)',
            zIndex: hoveredEmotion === emotion.id ? 10 : 1,
            transition: `all ${wheelConfig.transition_duration}ms`,
            cursor: 'pointer',
          }}
          onClick={() => onSelect(emotion)}
          onMouseEnter={() => setHoveredEmotion(emotion.id)}
          onMouseLeave={() => setHoveredEmotion(null)}
        >
          {emojiItem ? (
            <AnimatedEmoji
              emojiItem={emojiItem}
              size={
                wheelConfig.emojiSize >= 40
                  ? '4xl'
                  : wheelConfig.emojiSize >= 32
                    ? '3xl'
                    : wheelConfig.emojiSize >= 24
                      ? '2xl'
                      : wheelConfig.emojiSize >= 20
                        ? 'xl'
                        : wheelConfig.emojiSize >= 16
                          ? 'lg'
                          : wheelConfig.emojiSize >= 14
                            ? 'md'
                            : wheelConfig.emojiSize >= 12
                              ? 'sm'
                              : 'xs'
              }
              autoPlay={true}
              loop={true}
            />
          ) : (
            <span style={{ fontSize: `${wheelConfig.emojiSize}px` }}>{emotion.emoji}</span>
          )}
          {/* 显示文本 */}
          <div
            style={{
              textAlign: 'center',
              fontSize: `${wheelConfig.font_size}px`,
              fontFamily: wheelConfig.font_family,
              color: wheelConfig.text_color,
              marginTop: '4px',
            }}
          >
            {emotion.name}
          </div>
        </div>
      );
    });
  };

  // 渲染返回按钮
  const renderBackButton = () => {
    if (!onBack || tierLevel === 1) return null;

    return (
      <button
        onClick={onBack}
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          boxShadow: wheelConfig.shadow_enabled ? `0 0 5px ${wheelConfig.shadowColor}` : 'none',
        }}
      >
        ←
      </button>
    );
  };

  // 渲染选中路径
  const renderSelectedPath = () => {
    if (!selectedPath) return null;

    // 确保 selectedPath 是可渲染的字符串
    const pathText =
      typeof selectedPath === 'string'
        ? selectedPath
        : typeof selectedPath === 'object'
          ? JSON.stringify(selectedPath)
          : String(selectedPath);

    return (
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 100,
          fontSize: `${wheelConfig.font_size}px`,
          fontFamily: wheelConfig.font_family,
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '5px',
          background: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(5px)',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {pathText}
      </div>
    );
  };

  // 渲染交互控件
  const renderControls = () => {
    if (!wheelConfig.drag_enabled && !wheelConfig.zoom_enabled && !wheelConfig.rotation_enabled) {
      return null;
    }

    return (
      <div
        style={{
          position: 'absolute',
          bottom: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          gap: '10px',
          background: 'rgba(255, 255, 255, 0.7)',
          padding: '5px 10px',
          borderRadius: '20px',
          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)',
          zIndex: 100,
        }}
      >
        {wheelConfig.zoom_enabled && (
          <>
            <button
              onClick={() =>
                setTransform((prev) => ({
                  ...prev,
                  scale: Math.min(wheelConfig.zoom_max_scale, prev.scale * 1.1),
                }))
              }
              style={{
                background: 'transparent',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                color: wheelConfig.colors?.primary,
              }}
              title="放大"
            >
              +
            </button>
            <button
              onClick={() =>
                setTransform((prev) => ({
                  ...prev,
                  scale: Math.max(wheelConfig.zoom_min_scale, prev.scale * 0.9),
                }))
              }
              style={{
                background: 'transparent',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                color: wheelConfig.colors?.primary,
              }}
              title="缩小"
            >
              -
            </button>
          </>
        )}

        {wheelConfig.rotation_enabled && (
          <>
            <button
              onClick={() => handleRotate(false)}
              style={{
                background: 'transparent',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                color: wheelConfig.colors?.primary,
              }}
              title="逆时针旋转"
            >
              ↺
            </button>
            <button
              onClick={() => handleRotate(true)}
              style={{
                background: 'transparent',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                color: wheelConfig.colors?.primary,
              }}
              title="顺时针旋转"
            >
              ↻
            </button>
          </>
        )}

        <button
          onClick={resetTransform}
          style={{
            background: 'transparent',
            border: 'none',
            cursor: 'pointer',
            fontSize: '14px',
            padding: '0 10px',
            height: '30px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '15px',
            color: wheelConfig.colors?.primary,
          }}
          title="重置"
        >
          重置
        </button>
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width: `${containerSize}px`,
        height: `${containerSize}px`,
        margin: '0 auto',
        cursor: wheelConfig.drag_enabled ? (isDragging ? 'grabbing' : 'grab') : 'default',
        transition: 'width 0.3s ease, height 0.3s ease',
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onWheel={handleWheel}
    >
      {renderBackButton()}
      {renderSelectedPath()}
      {renderControls()}
      <svg
        ref={svgRef}
        style={{
          display: contentDisplayMode === 'animatedEmoji' ? 'none' : 'block',
          borderRadius: '50%',
          boxShadow: wheelConfig.shadow_enabled
            ? `${wheelConfig.shadowOffsetX}px ${wheelConfig.shadowOffsetY}px ${wheelConfig.shadowBlur}px ${wheelConfig.shadowColor}`
            : 'none',
          transform: wheelConfig.use_3d_effects
            ? `perspective(${wheelConfig.perspective}px) rotateX(${wheelConfig.rotate_x}deg) rotateY(${wheelConfig.rotate_y}deg)`
            : 'none',
        }}
      />
      {contentDisplayMode === 'animatedEmoji' && renderAnimatedEmojis()}
    </div>
  );
};
