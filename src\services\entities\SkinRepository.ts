/**
 * 皮肤仓储 - 修复版本
 * 纯数据访问层，不包含业务逻辑
 */

import { BaseRepository } from '../base/BaseRepository';
import { Skin } from '../../types/schema/base';
import { CreateSkinInput, UpdateSkinInput } from '../../types/schema/api';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class SkinRepository extends BaseRepository<
  Skin,
  CreateSkinInput,
  UpdateSkinInput
> {
  constructor(db?: SQLiteDBConnection) {
    super('skins', db);
  }

  /**
   * 获取免费皮肤
   */
  async findFreeSkins(): Promise<Skin[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE is_premium = 0
      ORDER BY name ASC
    `;

    const result = await db.query(query);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 获取高级皮肤
   */
  async findPremiumSkins(): Promise<Skin[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE is_premium = 1
      ORDER BY name ASC
    `;

    const result = await db.query(query);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 获取已解锁的皮肤
   */
  async findUnlockedSkins(): Promise<Skin[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE is_unlocked = 1
      ORDER BY name ASC
    `;

    const result = await db.query(query);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据分类获取皮肤
   */
  async findByCategory(category: string): Promise<Skin[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE category = ?
      ORDER BY name ASC
    `;

    const result = await db.query(query, [category]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据标签获取皮肤
   */
  async findByTag(tag: string): Promise<Skin[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE tags LIKE ?
      ORDER BY name ASC
    `;

    const tagPattern = `%"${tag}"%`;
    const result = await db.query(query, [tagPattern]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 搜索皮肤
   */
  async searchSkins(searchTerm: string): Promise<Skin[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE (name LIKE ? OR description LIKE ?)
      ORDER BY name ASC
    `;

    const searchPattern = `%${searchTerm}%`;
    const result = await db.query(query, [searchPattern, searchPattern]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 检查皮肤名称是否已存在
   */
  async isNameExists(name: string, excludeId?: string): Promise<boolean> {
    const db = this.getDb();
    let query = `
      SELECT COUNT(*) as count
      FROM ${this.tableName}
      WHERE LOWER(name) = LOWER(?)
    `;
    const params = [name];

    if (excludeId) {
      query += ` AND id != ?`;
      params.push(excludeId);
    }

    const result = await db.query(query, params);
    return (result.values?.[0]?.count || 0) > 0;
  }

  /**
   * 获取所有分类
   */
  async getCategories(): Promise<string[]> {
    const db = this.getDb();
    const query = `
      SELECT DISTINCT category
      FROM ${this.tableName}
      WHERE category IS NOT NULL
      ORDER BY category ASC
    `;

    const result = await db.query(query);
    return (result.values || []).map(row => row.category);
  }

  /**
   * 获取所有标签
   */
  async getAllTags(): Promise<string[]> {
    const db = this.getDb();
    const query = `SELECT DISTINCT tags FROM ${this.tableName} WHERE tags IS NOT NULL`;

    const result = await db.query(query);
    const allTags = new Set<string>();

    (result.values || []).forEach(row => {
      if (row.tags) {
        try {
          const tags = JSON.parse(row.tags);
          if (Array.isArray(tags)) {
            tags.forEach(tag => allTags.add(tag));
          }
        } catch {
          // 忽略解析错误
        }
      }
    });

    return Array.from(allTags).sort();
  }

  /**
   * 获取皮肤使用统计
   */
  async getUsageStats(skinId: string): Promise<{
    usage_count: number;
    last_used?: string;
    users_count: number;
  }> {
    const db = this.getDb();
    // 这里需要根据实际的用户配置表来查询
    // 假设有一个 user_configs 表记录用户的皮肤使用情况
    const query = `
      SELECT
        COUNT(*) as usage_count,
        MAX(updated_at) as last_used,
        COUNT(DISTINCT user_id) as users_count
      FROM user_configs
      WHERE config_value LIKE ?
    `;

    const skinPattern = `%"current_skin_id":"${skinId}"%`;
    const result = await db.query(query, [skinPattern]);
    const row = result.values?.[0];

    return {
      usage_count: parseInt(row?.usage_count) || 0,
      last_used: row?.last_used,
      users_count: parseInt(row?.users_count) || 0
    };
  }

  protected mapRowToEntity(row: any): Skin {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      category: row.category,
      version: row.version,
      tags: this.parseJSON(row.tags) || [],
      preview_image_light: row.preview_image_light,
      preview_image_dark: row.preview_image_dark,
      is_premium: Boolean(row.is_premium),
      is_unlocked: Boolean(row.is_unlocked),
      unlock_conditions: this.parseJSON(row.unlock_conditions),
      author: row.author,
      supported_content_modes: this.parseJSON(row.supported_content_modes) || [],
      supported_view_types: this.parseJSON(row.supported_view_types) || [],
      supported_render_engines: this.parseJSON(row.supported_render_engines) || [],
      config: this.parseJSON(row.config),
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  protected mapEntityToRow(entity: Partial<Skin>): Record<string, any> {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      category: entity.category,
      version: entity.version,
      tags: this.stringifyJSON(entity.tags),
      preview_image_light: entity.preview_image_light,
      preview_image_dark: entity.preview_image_dark,
      is_premium: entity.is_premium ? 1 : 0,
      is_unlocked: entity.is_unlocked ? 1 : 0,
      unlock_conditions: this.stringifyJSON(entity.unlock_conditions),
      author: entity.author,
      supported_content_modes: this.stringifyJSON(entity.supported_content_modes),
      supported_view_types: this.stringifyJSON(entity.supported_view_types),
      supported_render_engines: this.stringifyJSON(entity.supported_render_engines),
      config: this.stringifyJSON(entity.config),
      created_at: entity.created_at,
      updated_at: entity.updated_at
    };
  }

  protected buildInsertQuery(data: CreateSkinInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const skinId = `skin_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const query = `
      INSERT INTO ${this.tableName} (
        id, name, description, category, version, tags,
        preview_image_light, preview_image_dark, is_premium, is_unlocked,
        unlock_conditions, author, supported_content_modes, supported_view_types,
        supported_render_engines, config, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      skinId,
      data.name,
      data.description || null,
      data.category,
      data.version || '1.0.0',
      this.stringifyJSON(data.tags),
      data.preview_image_light || null,
      data.preview_image_dark || null,
      data.is_premium ? 1 : 0,
      data.is_unlocked ? 1 : 0,
      this.stringifyJSON(data.unlock_conditions),
      data.author || null,
      this.stringifyJSON(data.supported_content_modes),
      this.stringifyJSON(data.supported_view_types),
      this.stringifyJSON(data.supported_render_engines),
      this.stringifyJSON(data.config),
      now,
      now
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateSkinInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      fields.push('name = ?');
      values.push(data.name);
    }

    if (data.description !== undefined) {
      fields.push('description = ?');
      values.push(data.description);
    }

    if (data.category !== undefined) {
      fields.push('category = ?');
      values.push(data.category);
    }

    if (data.version !== undefined) {
      fields.push('version = ?');
      values.push(data.version);
    }

    if (data.tags !== undefined) {
      fields.push('tags = ?');
      values.push(this.stringifyJSON(data.tags));
    }

    if (data.preview_image_light !== undefined) {
      fields.push('preview_image_light = ?');
      values.push(data.preview_image_light);
    }

    if (data.preview_image_dark !== undefined) {
      fields.push('preview_image_dark = ?');
      values.push(data.preview_image_dark);
    }

    if (data.is_premium !== undefined) {
      fields.push('is_premium = ?');
      values.push(data.is_premium ? 1 : 0);
    }

    if (data.is_unlocked !== undefined) {
      fields.push('is_unlocked = ?');
      values.push(data.is_unlocked ? 1 : 0);
    }

    if (data.unlock_conditions !== undefined) {
      fields.push('unlock_conditions = ?');
      values.push(this.stringifyJSON(data.unlock_conditions));
    }

    if (data.author !== undefined) {
      fields.push('author = ?');
      values.push(data.author);
    }

    if (data.supported_content_modes !== undefined) {
      fields.push('supported_content_modes = ?');
      values.push(this.stringifyJSON(data.supported_content_modes));
    }

    if (data.supported_view_types !== undefined) {
      fields.push('supported_view_types = ?');
      values.push(this.stringifyJSON(data.supported_view_types));
    }

    if (data.supported_render_engines !== undefined) {
      fields.push('supported_render_engines = ?');
      values.push(this.stringifyJSON(data.supported_render_engines));
    }

    if (data.config !== undefined) {
      fields.push('config = ?');
      values.push(this.stringifyJSON(data.config));
    }

    // Always update updated_at
    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.category) {
      conditions.push('category = ?');
      values.push(filters.category);
    }

    if (filters?.is_premium !== undefined) {
      conditions.push('is_premium = ?');
      values.push(filters.is_premium ? 1 : 0);
    }

    if (filters?.is_unlocked !== undefined) {
      conditions.push('is_unlocked = ?');
      values.push(filters.is_unlocked ? 1 : 0);
    }

    if (filters?.author) {
      conditions.push('author = ?');
      values.push(filters.author);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY name ASC';

    if (filters?.limit) {
      query += ' LIMIT ?';
      values.push(filters.limit);
    }

    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.category) {
      conditions.push('category = ?');
      values.push(filters.category);
    }

    if (filters?.is_premium !== undefined) {
      conditions.push('is_premium = ?');
      values.push(filters.is_premium ? 1 : 0);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    return { query, values };
  }

  protected extractIdFromCreateData(_data: CreateSkinInput): string {
    return `skin_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private parseJSON(jsonString: string | null): any {
    if (!jsonString) return null;
    try {
      return JSON.parse(jsonString);
    } catch {
      return null;
    }
  }

  private stringifyJSON(obj: any): string | null {
    if (obj === null || obj === undefined) return null;
    try {
      return JSON.stringify(obj);
    } catch {
      return null;
    }
  }
}
