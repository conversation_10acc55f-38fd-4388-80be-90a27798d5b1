/**
 * 星系布局预览组件
 * 用于在设置页面中展示不同的星系布局选项
 */

import { useTheme } from '@/contexts/ThemeContext';
import type { GalaxyLayout } from '@/types/userConfigTypes';
import type React from 'react';

interface GalaxyLayoutPreviewProps {
  layout: GalaxyLayout;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  onClick?: () => void;
  isSelected?: boolean;
}

/**
 * 星系布局预览组件
 * 展示不同星系布局的预览
 */
const GalaxyLayoutPreview: React.FC<GalaxyLayoutPreviewProps> = ({
  layout,
  size = 'md',
  showLabel = false,
  onClick,
  isSelected = false,
}) => {
  const { theme } = useTheme();
  const isDarkMode =
    theme === 'dark' ||
    (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // 根据尺寸设置样式
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return { container: 'w-16 h-12', star: 'w-1.5 h-1.5' };
      case 'lg':
        return { container: 'w-32 h-24', star: 'w-3 h-3' };
      default:
        return { container: 'w-24 h-18', star: 'w-2 h-2' };
    }
  };

  const sizeClass = getSizeClass();

  // 获取布局标签
  const getLayoutLabel = () => {
    switch (layout) {
      case 'orbital':
        return '轨道布局';
      case 'spiral':
        return '螺旋布局';
      case 'nebula':
        return '星云布局';
      default:
        return layout;
    }
  };

  // 生成示例星星
  const generateStars = () => {
    const colors = [
      '#EA4335', // Red
      '#FBBC05', // Yellow
      '#34A853', // Green
      '#4285F4', // Blue
      '#8E44AD', // Purple
      '#16A085', // Teal
    ];

    return colors.map((color, index) => ({
      id: `star-${index}`,
      color,
    }));
  };

  const stars = generateStars();

  // 渲染轨道布局预览
  const renderOrbitalPreview = () => {
    const centerX = Number.parseInt(sizeClass.container.split(' ')[0].substring(2)) / 2;
    const centerY = Number.parseInt(sizeClass.container.split(' ')[1].substring(2)) / 2;

    // 创建两个轨道
    const orbits = [
      { radius: Math.min(centerX, centerY) * 0.4 },
      { radius: Math.min(centerX, centerY) * 0.7 },
    ];

    return (
      <div className="relative w-full h-full">
        {/* 中心星 */}
        <div
          className="absolute rounded-full bg-primary"
          style={{
            width: sizeClass.star.split(' ')[0],
            height: sizeClass.star.split(' ')[1],
            left: `${centerX}px`,
            top: `${centerY}px`,
            transform: 'translate(-50%, -50%)',
          }}
        />

        {/* 轨道 */}
        {orbits.map((orbit, orbitIndex) => (
          <div
            key={`orbit-${orbitIndex}`}
            className="absolute rounded-full border border-dashed border-gray-400 dark:border-gray-600"
            style={{
              width: `${orbit.radius * 2}px`,
              height: `${orbit.radius * 2}px`,
              left: `${centerX}px`,
              top: `${centerY}px`,
              transform: 'translate(-50%, -50%)',
            }}
          />
        ))}

        {/* 星星 */}
        {stars.slice(0, 6).map((star, index) => {
          const orbitIndex = index < 3 ? 0 : 1;
          const indexInOrbit = index < 3 ? index : index - 3;
          const totalInOrbit = index < 3 ? 3 : 3;

          const angle = (indexInOrbit / totalInOrbit) * 2 * Math.PI;
          const radius = orbits[orbitIndex].radius;

          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);

          return (
            <div
              key={star.id}
              className="absolute rounded-full"
              style={{
                backgroundColor: star.color,
                width: sizeClass.star.split(' ')[0],
                height: sizeClass.star.split(' ')[1],
                left: `${x}px`,
                top: `${y}px`,
                transform: 'translate(-50%, -50%)',
              }}
            />
          );
        })}
      </div>
    );
  };

  // 渲染螺旋布局预览
  const renderSpiralPreview = () => {
    const centerX = Number.parseInt(sizeClass.container.split(' ')[0].substring(2)) / 2;
    const centerY = Number.parseInt(sizeClass.container.split(' ')[1].substring(2)) / 2;
    const maxRadius = Math.min(centerX, centerY) * 0.9;

    return (
      <div className="relative w-full h-full">
        {/* 中心星 */}
        <div
          className="absolute rounded-full bg-primary"
          style={{
            width: sizeClass.star.split(' ')[0],
            height: sizeClass.star.split(' ')[1],
            left: `${centerX}px`,
            top: `${centerY}px`,
            transform: 'translate(-50%, -50%)',
          }}
        />

        {/* 星星 */}
        {stars.slice(0, 6).map((star, index) => {
          const angle = index * 1.0; // 角度增量
          const radius = Math.min(5 + 5 * angle, maxRadius);

          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);

          return (
            <div
              key={star.id}
              className="absolute rounded-full"
              style={{
                backgroundColor: star.color,
                width: sizeClass.star.split(' ')[0],
                height: sizeClass.star.split(' ')[1],
                left: `${x}px`,
                top: `${y}px`,
                transform: 'translate(-50%, -50%)',
              }}
            />
          );
        })}
      </div>
    );
  };

  // 渲染星云布局预览
  const renderNebulaPreview = () => {
    const centerX = Number.parseInt(sizeClass.container.split(' ')[0].substring(2)) / 2;
    const centerY = Number.parseInt(sizeClass.container.split(' ')[1].substring(2)) / 2;
    const maxRadius = Math.min(centerX, centerY) * 0.9;

    return (
      <div className="relative w-full h-full">
        {/* 中心星 */}
        <div
          className="absolute rounded-full bg-primary"
          style={{
            width: sizeClass.star.split(' ')[0],
            height: sizeClass.star.split(' ')[1],
            left: `${centerX}px`,
            top: `${centerY}px`,
            transform: 'translate(-50%, -50%)',
          }}
        />

        {/* 星星 */}
        {stars.slice(0, 6).map((star, index) => {
          const goldenAngle = Math.PI * (3 - Math.sqrt(5)); // 黄金角约为137.5度
          const angle = index * goldenAngle;

          // 使用平方根缩放来使分布更均匀
          const radius = Math.sqrt(index / 6) * maxRadius;

          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);

          return (
            <div
              key={star.id}
              className="absolute rounded-full"
              style={{
                backgroundColor: star.color,
                width: sizeClass.star.split(' ')[0],
                height: sizeClass.star.split(' ')[1],
                left: `${x}px`,
                top: `${y}px`,
                transform: 'translate(-50%, -50%)',
              }}
            />
          );
        })}
      </div>
    );
  };

  return (
    <div
      className={`flex flex-col items-center ${onClick ? 'cursor-pointer' : ''} ${isSelected ? 'ring-2 ring-primary p-1 rounded' : 'p-1'}`}
      onClick={onClick}
    >
      <div
        className={`${sizeClass.container} bg-card rounded p-2 flex items-center justify-center border border-border`}
        style={{
          background: isDarkMode
            ? 'radial-gradient(circle, rgba(20,20,40,1) 0%, rgba(10,10,20,1) 100%)'
            : 'radial-gradient(circle, rgba(240,240,255,1) 0%, rgba(220,220,240,1) 100%)',
        }}
      >
        {layout === 'orbital' && renderOrbitalPreview()}
        {layout === 'spiral' && renderSpiralPreview()}
        {layout === 'nebula' && renderNebulaPreview()}
      </div>
      {showLabel && <span className="text-xs text-muted-foreground mt-1">{getLayoutLabel()}</span>}
    </div>
  );
};

export default GalaxyLayoutPreview;
