# 类型系统清理计划

本文档详细描述了类型系统的清理计划，包括需要移除的旧类型、需要保留的兼容性类型以及清理的步骤和时间安排。

## 类型系统现状

当前，项目中存在三套情绪类型定义：

1. **旧版类型系统**（`src/types/mood.ts`）
   - `EmotionOption` 接口
   - `EmotionTier` 类型（字符串联合类型）
   - 使用 `parent_id` 表示父级关系

2. **中间版类型系统**（`src/types/emotionTypes.ts`）
   - `EmotionOption` 接口（改进版）
   - `EmotionTier` 枚举
   - 使用 `parentId` 表示父级关系
   - 添加了 `emojiItem` 支持

3. **新版类型系统**（`src/types/emotionDataTypes.ts`）
   - `Emotion` 接口
   - `EmotionTier` 接口
   - 使用数字 `level` 表示层级
   - 支持 `emoji_sets` 和多语言

此外，还有两套内容显示模式类型：

1. **旧版内容类型**（`src/types/wheelTypes.ts`）
   - `WheelContentType` 类型
   - `WheelType` 类型

2. **新版内容类型**（`src/types/previewTypes.ts`）
   - `ContentDisplayMode` 类型
   - `RenderEngine` 类型
   - `ViewType` 类型

## 兼容性层

为了确保平滑过渡，项目中实现了兼容性层（`src/types/compatibilityTypes.ts`），提供了类型转换函数：

- `convertOldEmotionToUnified`
- `convertIntermediateEmotionToUnified`
- `convertNewEmotionToUnified`
- `convertUnifiedToOldEmotion`
- `convertUnifiedToIntermediateEmotion`
- `convertUnifiedToNewEmotion`
- `convertWheelContentTypeToDisplayMode`
- `convertDisplayModeToWheelContentType`
- `convertTierStringToNumber`
- `convertTierEnumToNumber`
- `convertNumberToTierString`

## 清理目标

1. **基于 skinTypes.ts 构建类型系统**
   - 使用 `skinTypes.ts` 作为视图系统的基础
   - 使用 `SkinConfig` 作为统一的配置接口
   - 使用 `view_configs` 属性为不同视图类型提供特定配置

2. **直接废弃 wheelTypes.ts**
   - 完全移除 `src/types/wheelTypes.ts` 文件
   - 统一使用 `ContentDisplayMode` 替代 `WheelContentType`
   - 统一使用 `RenderEngine` 替代 `WheelType`
   - 统一使用 `SkinConfig` 替代 `WheelConfig`
   - 统一使用 `Skin` 替代 `WheelSkin`
   - 移除 `WheelContentStrategy` 接口

3. **标记废弃类型**
   - 标记 `EmotionOption`（旧版）为废弃
   - 标记 `EmotionTier`（字符串联合类型）为废弃
   - 标记 `EmotionOption`（中间版）为废弃

4. **更新导入语句**
   - 更新所有导入 `wheelTypes.ts` 的文件
   - 改为导入 `previewTypes.ts` 和 `skinTypes.ts`

## 清理步骤

### 步骤 1: 更新类型导入

1. 搜索所有导入旧类型的地方
2. 更新导入语句，使用新类型
3. 使用兼容性层进行类型转换

### 步骤 2: 查找所有导入 wheelTypes.ts 的文件

1. 使用代码搜索工具，查找所有导入 `wheelTypes.ts` 的文件
2. 创建一个列表，记录需要更新的文件和导入语句

### 步骤 3: 更新 previewTypes.ts

1. 移除对 `wheelTypes.ts` 的导入
2. 添加以下类型定义（如果尚未存在）：
   ```typescript
   export type RenderEngine = 'D3' | 'SVG' | 'R3F';
   export type ContentDisplayMode = 'text' | 'emoji' | 'textEmoji' | 'animatedEmoji';
   ```
3. 更新注释，说明这些类型现在是主要类型定义

### 步骤 4: 更新 mood.ts

1. 标记 `EmotionOption` 接口为废弃
2. 标记 `EmotionTier` 类型为废弃
3. 导入 `Emotion` 和 `EmotionTier` 类型
4. 添加类型别名

### 步骤 5: 更新 emotionTypes.ts

1. 标记 `EmotionOption` 接口为废弃
2. 标记 `EmotionTier` 枚举为废弃
3. 更新注释

### 步骤 6: 更新 compatibilityTypes.ts

1. 移除对 `wheelTypes.ts` 的导入
2. 更新 `UnifiedContentDisplayMode` 类型定义：
   ```typescript
   export type UnifiedContentDisplayMode = ContentDisplayMode;
   ```
3. 移除 `convertWheelContentTypeToDisplayMode` 和 `convertDisplayModeToWheelContentType` 函数
4. 更新现有函数的注释
5. 确保所有转换函数正确处理新类型

### 步骤 7: 移除 wheelTypes.ts 文件

1. 确认所有导入 `wheelTypes.ts` 的文件都已更新
2. 移除 `src/types/wheelTypes.ts` 文件

### 步骤 8: 更新文档

1. 更新开发指南，说明类型系统的变化
2. 提供代码示例，展示如何使用新的类型系统
3. 更新组件文档，反映类型系统的变化

## 测试计划

每次更新类型定义后，需要运行以下测试：

1. **类型测试**
   - 运行 `npm run test:types` 确保类型定义正确
   - 检查 TypeScript 编译错误

2. **单元测试**
   - 运行 `npm test` 确保所有测试通过
   - 特别关注使用类型转换函数的测试

3. **手动测试**
   - 在开发环境中测试相关功能
   - 确保类型转换正确

## 风险和缓解措施

1. **类型不兼容**
   - 风险：更新类型可能导致现有代码不兼容
   - 缓解：使用类型别名和兼容性层，确保平滑过渡

2. **运行时错误**
   - 风险：类型转换可能导致运行时错误
   - 缓解：增加测试覆盖率，确保类型转换函数正确处理边缘情况

3. **开发体验**
   - 风险：多套类型系统可能导致开发者混淆
   - 缓解：提供清晰的文档和注释，说明类型系统的演进和使用方法

## 时间安排

| 阶段 | 预计时间 | 开始日期 | 结束日期 |
|------|---------|---------|---------|
| 查找导入 wheelTypes.ts 的文件 | 0.5天 | TBD | TBD |
| 更新导入语句 | 1天 | TBD | TBD |
| 更新 previewTypes.ts | 0.5天 | TBD | TBD |
| 更新 mood.ts | 0.5天 | TBD | TBD |
| 更新 emotionTypes.ts | 0.5天 | TBD | TBD |
| 更新 compatibilityTypes.ts | 0.5天 | TBD | TBD |
| 移除 wheelTypes.ts | 0.5天 | TBD | TBD |
| 更新文档 | 1天 | TBD | TBD |
| 测试和验证 | 1天 | TBD | TBD |

## 结论

通过执行这个类型系统清理计划，我们将直接废弃 `wheelTypes.ts` 文件，统一使用 `skinTypes.ts` 和 `previewTypes.ts` 中的类型定义。这种更直接的方法可以减少代码库中的冗余类型定义，简化类型系统，提高代码的可维护性和可读性。

虽然这种方法可能会导致一些短期的不兼容性，但长期来看，它将为开发者提供更清晰、更一致的类型系统，减少混淆，并为未来的功能开发奠定坚实的基础。通过提供详细的文档和示例，我们可以帮助开发者快速适应新的类型系统。
