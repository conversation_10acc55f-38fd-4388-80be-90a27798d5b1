import { useLanguage } from "@/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from "@/components/ui/collapsible";
import { 
  Palette, ChevronDown, ChevronRight, Sun, Snowflake, 
  <PERSON>lend, Sparkles, Clock
} from "lucide-react";

interface ColorModeSelectorProps {
  userLevel: 'beginner' | 'regular' | 'advanced' | 'vip';
  currentColorMode: string;
  onColorModeChange: (colorMode: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

const ColorModeSelector: React.FC<ColorModeSelectorProps> = ({
  userLevel,
  currentColorMode,
  onColorModeChange,
  isOpen,
  onToggle
}) => {
  const { t } = useLanguage();

  // 颜色模式配置
  const colorModes = [
    {
      id: 'warm',
      name: t('color_mode.warm', '暖色模式'),
      description: t('color_mode.warm.desc', '温馨舒适，适合放松'),
      icon: Sun,
      colors: ['#FFD700', '#FF8C42', '#FF6B6B', '#F4A261'],
      available: true
    },
    {
      id: 'cool',
      name: t('color_mode.cool', '冷色模式'),
      description: t('color_mode.cool.desc', '清爽专业，适合工作'),
      icon: Snowflake,
      colors: ['#4ECDC4', '#45B7D1', '#96CEB4', '#A8E6CF'],
      available: true
    },
    {
      id: 'mixed',
      name: t('color_mode.mixed', '混合模式'),
      description: t('color_mode.mixed.desc', '平衡色调，适应不同情绪'),
      icon: Blend,
      colors: ['#FF6B6B', '#4ECDC4', '#FFD93D', '#6BCF7F'],
      available: true
    },
    {
      id: 'auto',
      name: t('color_mode.auto', '自动模式'),
      description: t('color_mode.auto.desc', '根据时间和情境智能调整'),
      icon: Clock,
      colors: ['#FF8C42', '#4ECDC4', '#FFD93D', '#A8E6CF'],
      available: userLevel === 'advanced' || userLevel === 'vip',
      badge: 'smart'
    }
  ];

  // 渲染颜色模式卡片
  const renderColorModeCard = (mode: typeof colorModes[0]) => {
    const Icon = mode.icon;
    const isSelected = currentColorMode === mode.id;
    const isDisabled = !mode.available;

    return (
      <Button
        key={mode.id}
        variant={isSelected ? "default" : "outline"}
        className={`h-auto p-4 flex flex-col items-start space-y-3 relative ${
          isDisabled ? 'opacity-50 cursor-not-allowed' : ''
        }`}
        onClick={() => mode.available && onColorModeChange(mode.id)}
        disabled={isDisabled}
      >
        {/* 模式图标和名称 */}
        <div className="flex items-center space-x-2 w-full">
          <Icon className="h-5 w-5" />
          <span className="font-medium">{mode.name}</span>
          {mode.badge && (
            <Badge variant="secondary" className="text-xs ml-auto">
              <Sparkles className="h-3 w-3 mr-1" />
              {t(`color_mode.badge.${mode.badge}`, mode.badge)}
            </Badge>
          )}
        </div>

        {/* 描述 */}
        <p className="text-xs text-left opacity-80">
          {mode.description}
        </p>

        {/* 颜色预览 */}
        <div className="flex space-x-1 w-full">
          {mode.colors.map((color, index) => (
            <div
              key={index}
              className="w-6 h-6 rounded-full border-2 border-background"
              style={{ backgroundColor: color }}
            />
          ))}
        </div>

        {/* 选中指示器 */}
        {isSelected && (
          <div className="absolute top-2 right-2">
            <div className="w-3 h-3 bg-primary rounded-full" />
          </div>
        )}
      </Button>
    );
  };

  return (
    <Collapsible open={isOpen} onOpenChange={onToggle}>
      <CollapsibleTrigger asChild>
        <Button variant="ghost" className="w-full justify-between p-4 h-auto">
          <div className="flex items-center space-x-2">
            <Palette className="h-5 w-5" />
            <span className="font-medium">{t('settings.color_mode', '颜色模式')}</span>
            <Badge variant="outline" className="text-xs">
              {colorModes.find(m => m.id === currentColorMode)?.name || currentColorMode}
            </Badge>
          </div>
          {isOpen ? 
            <ChevronDown className="h-4 w-4" /> : 
            <ChevronRight className="h-4 w-4" />
          }
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="px-4 pb-4">
        <div className="space-y-4 pt-2">
          {/* 颜色模式说明 */}
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">
              {t('color_mode.explanation', '颜色模式会影响整个应用的色彩风格，与皮肤主题协同工作。')}
            </p>
          </div>

          {/* 颜色模式选择网格 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {colorModes.map(renderColorModeCard)}
          </div>

          {/* 自动模式详细说明 */}
          {currentColorMode === 'auto' && (
            <div className="p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-start space-x-2">
                <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    {t('color_mode.auto.smart_title', '智能颜色调整')}
                  </p>
                  <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                    <p>• {t('color_mode.auto.time_based', '6:00-12:00 冷色调，18:00-22:00 暖色调')}</p>
                    <p>• {t('color_mode.auto.context_based', '根据使用场景自动调整')}</p>
                    <p>• {t('color_mode.auto.emotion_based', '基于情绪状态优化色彩')}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* VIP功能提示 */}
          {userLevel !== 'advanced' && userLevel !== 'vip' && (
            <div className="p-3 bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-lg">
              <div className="flex items-start space-x-2">
                <Sparkles className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
                    {t('color_mode.upgrade_hint', '解锁更多颜色模式')}
                  </p>
                  <p className="text-xs text-amber-700 dark:text-amber-300">
                    {t('color_mode.upgrade_desc', '升级到高级版本以使用智能自动模式和更多个性化选项')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};

export default ColorModeSelector;
