/**
 * 轮盘扇区修复测试
 * 测试修复后的扇区生成逻辑，特别关注少量情绪的情况
 */

// 模拟 D3.js 的 pie 布局
function createMockPie() {
  return (data) => {
    const total = data.length;
    const result = [];

    // 计算每个扇区的角度
    const anglePerSector = (2 * Math.PI) / total;

    for (let i = 0; i < total; i++) {
      result.push({
        data: data[i],
        index: i,
        value: 1,
        startAngle: i * anglePerSector,
        endAngle: (i + 1) * anglePerSector,
        padAngle: 0,
      });
    }

    return result;
  };
}

// 修复后的 prepareWheelData 函数
function prepareWheelData(emotions) {
  // 如果没有情绪，返回一个完整的占位符圆
  if (emotions.length === 0) {
    return Array(4)
      .fill(0)
      .map((_, i) => ({
        id: `placeholder-${i}`,
        name: '',
      }));
  }

  // 如果情绪数量太少，添加占位符确保至少有4个扇区
  if (emotions.length < 4) {
    const result = [...emotions];

    // 特殊处理2个情绪的情况，确保它们位于对角线上
    if (emotions.length === 2) {
      // 添加两个透明的占位符，形成4个扇区的完整圆形
      // 确保真实情绪位于0°和180°位置
      const emotion1 = result[0];
      const emotion2 = result[1];
      result.length = 0; // 清空数组

      // 重新排列：真实情绪1, 占位符1, 真实情绪2, 占位符2
      result.push(emotion1);
      result.push({
        id: 'placeholder-1',
        name: '',
      });
      result.push(emotion2);
      result.push({
        id: 'placeholder-2',
        name: '',
      });
    } else if (emotions.length === 1) {
      // 对于1个情绪，添加3个占位符形成完整圆形
      // 确保真实情绪位于0°位置
      const emotion = result[0];
      result.length = 0; // 清空数组

      // 重新排列：真实情绪, 占位符1, 占位符2, 占位符3
      result.push(emotion);
      for (let i = 0; i < 3; i++) {
        result.push({
          id: `placeholder-${i}`,
          name: '',
        });
      }
    } else if (emotions.length === 3) {
      // 对于3个情绪，添加1个占位符使总数达到4个
      // 确保均匀分布在圆周上
      result.push({
        id: 'placeholder-0',
        name: '',
      });
    }

    return result;
  }

  // 情绪数量足够，直接使用原始数据
  return [...emotions];
}

// 测试函数
function testWheelSectors(emotionsCount) {
  console.log(`\n测试 ${emotionsCount} 个情绪的扇区生成:`);

  // 创建测试情绪数据
  const emotions = [];
  for (let i = 0; i < emotionsCount; i++) {
    emotions.push({
      id: `emotion-${i}`,
      name: `Emotion ${i}`,
      color: `#${Math.floor(Math.random() * 16777215)
        .toString(16)
        .padStart(6, '0')}`,
    });
  }

  // 准备轮盘数据
  const preparedData = prepareWheelData(emotions);
  console.log(`原始情绪数量: ${emotions.length}`);
  console.log(`处理后扇区数量: ${preparedData.length}`);

  // 检查是否有占位符
  const placeholders = preparedData.filter((e) => e.id.startsWith('placeholder'));
  console.log(`占位符数量: ${placeholders.length}`);

  // 使用模拟的 pie 布局生成扇区数据
  const mockPie = createMockPie();
  const pieData = mockPie(preparedData);

  // 检查扇区角度分布
  console.log('\n扇区角度分布:');
  let totalAngle = 0;

  pieData.forEach((sector, index) => {
    const isPlaceholder = sector.data.id.startsWith('placeholder');
    const angleInDegrees = (((sector.endAngle - sector.startAngle) * 180) / Math.PI).to(2);
    console.log(`扇区 ${index + 1}${isPlaceholder ? ' (占位符)' : ''}: ${angleInDegrees}°`);
    totalAngle += sector.endAngle - sector.startAngle;
  });

  // 检查是否形成完整的圆
  const totalAngleInDegrees = ((totalAngle * 180) / Math.PI).to(2);
  console.log(`\n总角度: ${totalAngleInDegrees}°`);
  console.log(`是否形成完整的圆: ${Math.abs(totalAngle - 2 * Math.PI) < 0.0001 ? '是' : '否'}`);

  // 检查相邻扇区的连接
  console.log('\n相邻扇区连接检查:');
  let hasGap = false;

  for (let i = 0; i < pieData.length; i++) {
    const currentSector = pieData[i];
    const nextSector = pieData[(i + 1) % pieData.length];

    const gap = Math.abs(currentSector.endAngle - nextSector.startAngle);
    const gapInDegrees = ((gap * 180) / Math.PI).to(5);

    console.log(
      `扇区 ${i + 1} 和 扇区 ${((i + 1) % pieData.length) + 1} 之间的间隙: ${gapInDegrees}°`
    );

    if (gap > 0.0001) {
      hasGap = true;
    }
  }

  console.log(`是否存在间隙: ${hasGap ? '是' : '否'}`);

  // 检查2个情绪的特殊情况
  if (emotionsCount === 2) {
    console.log('\n2个情绪的特殊情况检查:');

    // 检查真实情绪是否位于对角线上
    const realEmotions = pieData.filter((sector) => !sector.data.id.startsWith('placeholder'));

    if (realEmotions.length === 2) {
      const angle1 = (realEmotions[0].startAngle + realEmotions[0].endAngle) / 2;
      const angle2 = (realEmotions[1].startAngle + realEmotions[1].endAngle) / 2;

      // 计算角度差，应该接近180度
      const angleDiff = Math.abs(((angle2 - angle1) * 180) / Math.PI);
      const isOpposite = Math.abs(angleDiff - 180) < 0.1;

      console.log(`两个真实情绪的角度差: ${angleDiff.to(2)}°`);
      console.log(`是否位于对角线上: ${isOpposite ? '是' : '否'}`);
    }
  }

  return {
    hasGap,
    totalAngle: totalAngle,
    isComplete: Math.abs(totalAngle - 2 * Math.PI) < 0.0001,
  };
}

// 运行测试
console.log('轮盘扇区修复测试');
console.log('===========================================');

// 测试不同数量的情绪
const testCases = [0, 1, 2, 3, 4, 5, 6, 8];
const results = {};

testCases.forEach((count) => {
  results[count] = testWheelSectors(count);
});

// 总结测试结果
console.log('\n===========================================');
console.log('测试总结:');

testCases.forEach((count) => {
  const result = results[count];
  console.log(
    `${count}个情绪: ${result.isComplete ? '✅ 完整圆形' : '❌ 不完整'}, ${result.hasGap ? '❌ 有间隙' : '✅ 无间隙'}`
  );
});

console.log('===========================================');
