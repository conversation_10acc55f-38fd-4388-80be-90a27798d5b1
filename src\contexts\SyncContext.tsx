import { Services } from '@/services';
import type React from 'react';
import {
  type ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { toast } from 'sonner';
import { useDatabaseContext } from './DatabaseContext';
import { useLanguage } from './LanguageContext';
import { useNetwork } from './NetworkContext';

// 同步项目类型
export type SyncItem = {
  id: string;
  type: 'mood_entry' | 'user_preference' | 'emotion_data' | 'tag';
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  priority: 'high' | 'medium' | 'low';
  lastAttempt?: Date;
  attempts: number;
  error?: string;
};

// 同步配置类型
export type SyncConfig = {
  autoSync: boolean;
  syncOnWifiOnly: boolean;
  syncInterval: number; // 分钟
  maxRetries: number;
  syncTypes: {
    mood_entries: boolean;
    user_preferences: boolean;
    emotion_data: boolean;
    tags: boolean;
  };
};

// 同步状态类型
export type SyncStatus = {
  isSyncing: boolean;
  lastSyncAttempt: Date | null;
  lastSuccessfulSync: Date | null;
  pendingItems: number;
  failedItems: number;
  progress: number; // 0-100
  currentSyncItem?: SyncItem;
};

// 同步上下文类型
interface SyncContextType {
  // 基本同步控制
  onlineSyncEnabled: boolean;
  setOnlineSyncEnabled: (enabled: boolean) => void;
  toggleOnlineSync: () => void;

  // 同步配置
  syncConfig: SyncConfig;
  updateSyncConfig: (config: Partial<SyncConfig>) => void;

  // 同步状态
  syncStatus: SyncStatus;

  // 同步操作
  startSync: (types?: Array<SyncItem['type']>) => Promise<boolean>;
  cancelSync: () => void;
  retryFailedItems: () => Promise<boolean>;

  // 同步队列管理
  pendingItems: SyncItem[];
  failedItems: SyncItem[];
  clearFailedItems: () => void;

  // 冲突处理
  resolveConflict: (itemId: string, useLocalVersion: boolean) => Promise<void>;
}

// 默认同步配置
const DEFAULT_SYNC_CONFIG: SyncConfig = {
  autoSync: true,
  syncOnWifiOnly: true,
  syncInterval: 30, // 30分钟
  maxRetries: 3,
  syncTypes: {
    mood_entries: true,
    user_preferences: true,
    emotion_data: true,
    tags: true,
  },
};

// 默认同步状态
const DEFAULT_SYNC_STATUS: SyncStatus = {
  isSyncing: false,
  lastSyncAttempt: null,
  lastSuccessfulSync: null,
  pendingItems: 0,
  failedItems: 0,
  progress: 0,
};

// 创建同步上下文
const SyncContext = createContext<SyncContextType | undefined>(undefined);

export const SyncProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { t } = useLanguage();
  const { isOnline, isInternetReachable, connectionType } = useNetwork();
  const { databaseService, isInitialized } = useDatabaseContext();

  // 同步启用状态
  const [onlineSyncEnabled, setOnlineSyncEnabledState] = useState<boolean>(() => {
    const storedValue = localStorage.getItem('onlineSyncEnabled');
    return storedValue ? JSON.parse(storedValue) : true; // 默认为启用
  });

  // 同步配置
  const [syncConfig, setSyncConfig] = useState<SyncConfig>(() => {
    const storedConfig = localStorage.getItem('syncConfig');
    return storedConfig ? JSON.parse(storedConfig) : DEFAULT_SYNC_CONFIG;
  });

  // 同步状态
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(DEFAULT_SYNC_STATUS);

  // 同步队列
  const [pendingItems, setPendingItems] = useState<SyncItem[]>([]);
  const [failedItems, setFailedItems] = useState<SyncItem[]>([]);

  // 同步计时器
  const syncTimerRef = useRef<number | null>(null);
  // 取消同步标志
  const cancelSyncRef = useRef<boolean>(false);

  // 保存同步启用状态到本地存储
  useEffect(() => {
    localStorage.setItem('onlineSyncEnabled', JSON.stringify(onlineSyncEnabled));
  }, [onlineSyncEnabled]);

  // 保存同步配置到本地存储
  useEffect(() => {
    localStorage.setItem('syncConfig', JSON.stringify(syncConfig));
  }, [syncConfig]);

  // 设置自动同步计时器
  useEffect(() => {
    // 清除现有计时器
    if (syncTimerRef.current !== null) {
      window.clearInterval(syncTimerRef.current);
      syncTimerRef.current = null;
    }

    // 如果启用了自动同步，设置新的计时器
    if (onlineSyncEnabled && syncConfig.autoSync) {
      syncTimerRef.current = window.setInterval(
        () => {
          // 检查是否应该同步
          const shouldSync =
            isOnline &&
            isInternetReachable &&
            (!syncConfig.syncOnWifiOnly || connectionType === 'wifi') &&
            pendingItems.length > 0 &&
            !syncStatus.isSyncing;

          if (shouldSync) {
            startSync();
          }
        },
        syncConfig.syncInterval * 60 * 1000
      ); // 转换为毫秒
    }

    // 清理函数
    return () => {
      if (syncTimerRef.current !== null) {
        window.clearInterval(syncTimerRef.current);
      }
    };
  }, [
    onlineSyncEnabled,
    syncConfig.autoSync,
    syncConfig.syncInterval,
    syncConfig.syncOnWifiOnly,
    isOnline,
    isInternetReachable,
    connectionType,
    pendingItems.length,
    syncStatus.isSyncing,
  ]);

  // 处理设置同步启用状态
  const handleSetOnlineSyncEnabled = (enabled: boolean) => {
    setOnlineSyncEnabledState(enabled);
    toast.success(enabled ? t('sync.enabled_message') : t('sync.disabled_message'));
  };

  // 处理切换同步启用状态
  const handleToggleOnlineSync = () => {
    setOnlineSyncEnabledState((prev) => {
      const newState = !prev;
      toast.success(newState ? t('sync.enabled_message') : t('sync.disabled_message'));
      return newState;
    });
  };

  // 更新同步配置
  const updateSyncConfig = useCallback((config: Partial<SyncConfig>) => {
    setSyncConfig((prev) => ({ ...prev, ...config }));
  }, []);

  // 获取实际的待同步项目
  const getActualPendingItems = useCallback(
    async (types?: Array<SyncItem['type']>): Promise<SyncItem[]> => {
      if (!isInitialized) {
        return [];
      }

      try {
        const items: SyncItem[] = [];

        // 检查心情记录 - 注意：MoodEntryService已deprecated
        if (!types || types.includes('mood_entry')) {
          try {
            const moodEntryService = await Services.moodEntry();
            const unsyncedResult = await moodEntryService.getUnsynced('default-user'); // TODO: 使用实际用户ID

            if (unsyncedResult.success && unsyncedResult.data) {
              for (const entry of unsyncedResult.data) {
                items.push({
                  id: entry.id,
                  type: 'mood_entry',
                  status: 'pending',
                  priority: 'high',
                  attempts: 0,
                });
              }
            }
          } catch (serviceError) {
            console.warn('[SyncContext] MoodEntryService is deprecated:', serviceError);
            // 跳过心情记录同步，因为服务已deprecated
          }
        }

        // 检查用户偏好设置
        if (!types || types.includes('user_preference')) {
          // TODO: 实现用户偏好设置的同步检查
        }

        // 检查情绪数据
        if (!types || types.includes('emotion_data')) {
          // TODO: 实现情绪数据的同步检查
        }

        // 检查标签
        if (!types || types.includes('tag')) {
          // TODO: 实现标签的同步检查
        }

        return items;
      } catch (error) {
        console.error('Failed to get pending items:', error);
        return [];
      }
    },
    [isInitialized]
  );

  // 执行实际的同步操作
  const performActualSync = useCallback(async (item: SyncItem): Promise<boolean> => {
    try {
      switch (item.type) {
        case 'mood_entry': {
          // 同步心情记录 - 注意：MoodEntryService已deprecated
          try {
            const moodEntryService = await Services.moodEntry();
            // TODO: 实现实际的云端同步逻辑
            // 这里应该调用云端API上传数据
            console.log(`Syncing mood entry: ${item.id}`);

            // 模拟同步延迟
            await new Promise((resolve) => setTimeout(resolve, 500));

            // 标记为已同步
            await moodEntryService.markAsSynced(item.id);
            return true;
          } catch (serviceError) {
            console.warn('[SyncContext] MoodEntryService is deprecated:', serviceError);
            // 返回false表示同步失败，但不是因为网络问题
            return false;
          }
        }

        case 'user_preference':
          // 同步用户偏好设置
          console.log(`Syncing user preference: ${item.id}`);
          await new Promise((resolve) => setTimeout(resolve, 300));
          return true;

        case 'emotion_data':
          // 同步情绪数据
          console.log(`Syncing emotion data: ${item.id}`);
          await new Promise((resolve) => setTimeout(resolve, 200));
          return true;

        case 'tag':
          // 同步标签
          console.log(`Syncing tag: ${item.id}`);
          await new Promise((resolve) => setTimeout(resolve, 100));
          return true;

        default:
          console.warn(`Unknown sync item type: ${item.type}`);
          return false;
      }
    } catch (error) {
      console.error(`Failed to sync item ${item.id}:`, error);
      return false;
    }
  }, []);

  // 开始同步
  const startSync = useCallback(
    async (types?: Array<SyncItem['type']>): Promise<boolean> => {
      // 如果已经在同步中，返回false
      if (syncStatus.isSyncing) {
        toast.error(t('sync.already_syncing'));
        return false;
      }

      // 如果没有网络连接，返回false
      if (!isOnline || !isInternetReachable) {
        toast.error(t('sync.no_internet'));
        return false;
      }

      // 如果只在WiFi下同步，但当前不是WiFi，返回false
      if (syncConfig.syncOnWifiOnly && connectionType !== 'wifi') {
        toast.error(t('sync.wifi_only'));
        return false;
      }

      // 重置取消标志
      cancelSyncRef.current = false;

      // 更新同步状态
      setSyncStatus((prev) => ({
        ...prev,
        isSyncing: true,
        lastSyncAttempt: new Date().toISOString(),
        progress: 0,
      }));

      // 显示同步开始提示
      toast.info(t('sync.started'));

      try {
        // 检查数据库是否已初始化
        if (!isInitialized) {
          toast.error(t('sync.database_not_ready'));
          return false;
        }

        // 获取要同步的项目
        const itemsToSync = await getActualPendingItems(types);

        // 更新待同步项目列表
        setPendingItems(itemsToSync);

        // 按优先级排序
        itemsToSync.sort((a, b) => {
          const priorityOrder = { high: 0, medium: 1, low: 2 };
          return priorityOrder[a.priority] - priorityOrder[b.priority];
        });

        // 同步每个项目
        const totalItems = itemsToSync.length;
        let completedItems = 0;
        const failedItemsInThisSync: SyncItem[] = [];

        for (const item of itemsToSync) {
          // 检查是否取消了同步
          if (cancelSyncRef.current) {
            break;
          }

          // 更新当前同步项目
          setSyncStatus((prev) => ({
            ...prev,
            currentSyncItem: item,
            progress: Math.round((completedItems / totalItems) * 100),
          }));

          try {
            // 实际同步操作
            const syncSuccess = await performActualSync(item);

            if (syncSuccess) {
              // 同步成功
              completedItems++;

              // 从待同步队列中移除
              setPendingItems((prev) => prev.filter((i) => i.id !== item.id));
            } else {
              // 同步失败
              const updatedItem = {
                ...item,
                status: 'failed' as const,
                attempts: item.attempts + 1,
                lastAttempt: new Date().toISOString(),
                error: '同步失败，请稍后重试',
              };

              failedItemsInThisSync.push(updatedItem);

              // 如果尝试次数超过最大重试次数，移到失败队列
              if (updatedItem.attempts >= syncConfig.maxRetries) {
                setPendingItems((prev) => prev.filter((i) => i.id !== item.id));
                setFailedItems((prev) => [...prev, updatedItem]);
              } else {
                // 否则更新待同步队列中的项目
                setPendingItems((prev) => prev.map((i) => (i.id === item.id ? updatedItem : i)));
              }
            }
          } catch (error) {
            // 处理同步错误
            const errorMessage = error instanceof Error ? error.message : '未知错误';

            const updatedItem = {
              ...item,
              status: 'failed' as const,
              attempts: item.attempts + 1,
              lastAttempt: new Date().toISOString(),
              error: errorMessage,
            };

            failedItemsInThisSync.push(updatedItem);

            // 如果尝试次数超过最大重试次数，移到失败队列
            if (updatedItem.attempts >= syncConfig.maxRetries) {
              setPendingItems((prev) => prev.filter((i) => i.id !== item.id));
              setFailedItems((prev) => [...prev, updatedItem]);
            } else {
              // 否则更新待同步队列中的项目
              setPendingItems((prev) => prev.map((i) => (i.id === item.id ? updatedItem : i)));
            }
          }

          // 更新进度
          setSyncStatus((prev) => ({
            ...prev,
            progress: Math.round((completedItems / totalItems) * 100),
          }));
        }

        // 更新同步状态
        setSyncStatus((prev) => ({
          ...prev,
          isSyncing: false,
          lastSuccessfulSync: new Date().toISOString(),
          pendingItems: pendingItems.length - completedItems + failedItemsInThisSync.length,
          failedItems:
            failedItems.length +
            failedItemsInThisSync.filter((item) => item.attempts >= syncConfig.maxRetries).length,
          progress: cancelSyncRef.current ? prev.progress : 100,
          currentSyncItem: undefined,
        }));

        // 显示同步完成提示
        if (cancelSyncRef.current) {
          toast.warning(t('sync.cancelled'));
          return false;
        }
        if (failedItemsInThisSync.length > 0) {
          toast.warning(
            t('sync.completed_with_errors', { count: String(failedItemsInThisSync.length) })
          );
        } else {
          toast.success(t('sync.completed'));
        }

        return failedItemsInThisSync.length === 0;
      } catch (error) {
        // 处理整体同步错误
        console.error('Sync error:', error);

        // 更新同步状态
        setSyncStatus((prev) => ({
          ...prev,
          isSyncing: false,
          currentSyncItem: undefined,
        }));

        // 显示同步错误提示
        toast.error(t('sync.error'));

        return false;
      }
    },
    [
      syncStatus.isSyncing,
      isOnline,
      isInternetReachable,
      syncConfig.syncOnWifiOnly,
      connectionType,
      pendingItems,
      failedItems.length,
      syncConfig.maxRetries,
      t,
    ]
  );

  // 取消同步
  const cancelSync = useCallback(() => {
    if (!syncStatus.isSyncing) {
      return;
    }

    cancelSyncRef.current = true;
    toast.info(t('sync.cancelling'));
  }, [syncStatus.isSyncing, t]);

  // 重试失败的项目
  const retryFailedItems = useCallback(async (): Promise<boolean> => {
    if (failedItems.length === 0) {
      toast.info(t('sync.no_failed_items'));
      return true;
    }

    // 将失败的项目移回待同步队列
    const itemsToRetry = failedItems.map((item) => ({
      ...item,
      status: 'pending' as const,
      attempts: 0,
      error: undefined,
    }));

    setPendingItems((prev) => [...prev, ...itemsToRetry]);
    setFailedItems([]);

    // 开始同步
    return startSync();
  }, [failedItems, startSync, t]);

  // 清除失败的项目
  const clearFailedItems = useCallback(() => {
    setFailedItems([]);
    toast.info(t('sync.failed_items_cleared'));
  }, [t]);

  // 解决冲突
  const resolveConflict = useCallback(
    async (itemId: string, useLocalVersion: boolean): Promise<void> => {
      // TODO: 实现实际的冲突解决逻辑
      toast.info(useLocalVersion ? t('sync.using_local_version') : t('sync.using_remote_version'));

      // 从失败队列中移除
      setFailedItems((prev) => prev.filter((item) => item.id !== itemId));
    },
    [t]
  );

  return (
    <SyncContext.Provider
      value={{
        // 基本同步控制
        onlineSyncEnabled,
        setOnlineSyncEnabled: handleSetOnlineSyncEnabled,
        toggleOnlineSync: handleToggleOnlineSync,

        // 同步配置
        syncConfig,
        updateSyncConfig,

        // 同步状态
        syncStatus,

        // 同步操作
        startSync,
        cancelSync,
        retryFailedItems,

        // 同步队列管理
        pendingItems,
        failedItems,
        clearFailedItems,

        // 冲突处理
        resolveConflict,
      }}
    >
      {children}
    </SyncContext.Provider>
  );
};

export const useSync = (): SyncContextType => {
  const context = useContext(SyncContext);
  if (context === undefined) {
    throw new Error('useSync must be used within a SyncProvider');
  }
  return context;
};
