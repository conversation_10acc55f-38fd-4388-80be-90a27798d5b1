# 配置系统 API 接口文档 (更新版)

## 📋 概述

配置系统采用混合架构模式，完全分离全局应用设置与Quiz系统配置：
- **tRPC在线服务**: 简单的云端配置同步和基础CRUD操作
- **离线服务**: 复杂的配置合并逻辑和本地数据管理
- **React Hooks**: 类型安全的状态管理和自动同步

## 🏗️ 架构设计

### 混合架构模式
```
简单操作: 页面 → tRPC → 云端数据库
复杂业务: 页面 → Hooks → 离线服务 → 本地SQLite → 同步到云端
```

### 配置系统分层
```
1. 全局应用设置 (主题、语言、通知等)
2. Quiz系统配置 (6层个性化配置)
3. 问题特定覆盖 (针对特定问题的emoji映射) **新增**
4. 包特定覆盖 (针对特定Quiz包的配置)
5. Quiz包默认配置 (Quiz包的默认emoji映射) **新增**
6. 会话配置快照 (最终合并的配置)
```

## 🌐 tRPC在线服务 API

### 全局应用配置路由 (`config.global`)

#### `getUserConfig`
获取用户的全局应用配置
```typescript
// 输入
{
  config_name?: string; // 默认: 'default'
}

// 输出
{
  success: boolean;
  data: GlobalAppConfig | null;
  error: string | null;
}

// GlobalAppConfig 类型
{
  id: string;
  user_id: string;
  config_name: string;
  theme_mode: 'light' | 'dark' | 'system';
  language: 'zh-CN' | 'en-US';
  notifications_enabled: boolean;
  sound_enabled: boolean;
  accessibility: string; // JSON字符串
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}
```

#### `updateUserConfig`
更新用户的全局应用配置
```typescript
// 输入
{
  config_name?: string;
  theme_mode?: 'light' | 'dark' | 'system';
  language?: 'zh-CN' | 'en-US';
  notifications_enabled?: boolean;
  sound_enabled?: boolean;
  accessibility?: string;
}

// 输出
{
  success: boolean;
  data: GlobalAppConfig | null;
  error: string | null;
}
```

### Quiz系统配置路由 (`config.quiz`)

#### `getUserPreferences`
获取用户的Quiz偏好配置
```typescript
// 输入
{
  config_name?: string; // 默认: 'default'
}

// 输出
{
  success: boolean;
  data: UserQuizPreferences | null;
  error: string | null;
}

// UserQuizPreferences 类型
{
  id: string;
  user_id: string;
  config_name: string;
  presentation_config: string; // QuizPresentationConfig的JSON序列化
  config_version: string;
  personalization_level: number; // 0-100
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}
```

#### `updateUserPreferences`
更新用户的Quiz偏好配置
```typescript
// 输入
{
  config_name?: string;
  presentation_config?: string;
  personalization_level?: number; // 0-100
}

// 输出
{
  success: boolean;
  data: UserQuizPreferences | null;
  error: string | null;
}
```

#### `generateSessionConfig`
生成Quiz会话的最终配置 (包含emoji映射合并)
```typescript
// 输入
{
  pack_id: string;
  session_id: string;
  question_ids?: string[]; // **新增**: 用于问题特定emoji映射
}

// 输出
{
  success: boolean;
  data: QuizSessionConfig | null;
  error: string | null;
}

// QuizSessionConfig 类型
{
  id: string;
  session_id: string;
  user_id: string;
  pack_id: string;
  final_presentation_config: string; // 完整QuizPresentationConfig的JSON序列化
  config_sources: string; // ConfigSources的JSON序列化
  personalization_level: number;
  config_version: string;
  created_at: string;
}
```

### Emoji映射配置路由 (`config.emoji`) **新增**

#### `getOptionPresentation`
获取选项的emoji展现配置
```typescript
// 输入
{
  pack_id: string;
  option_value: string;
  question_id?: string; // 可选，用于问题特定映射
}

// 输出
{
  success: boolean;
  data: {
    emoji: string;
    color: string;
    animation: string;
    source: 'system' | 'pack' | 'user' | 'question';
  } | null;
  error: string | null;
}
```

#### `updateUserEmojiMapping`
更新用户的emoji映射配置
```typescript
// 输入
{
  option_value: string;
  emoji_mapping: {
    primary: string;
    alternatives: string[];
  };
  color?: string;
  animation?: string;
}

// 输出
{
  success: boolean;
  data: boolean;
  error: string | null;
}
```

#### `updateQuestionEmojiOverride`
更新问题特定的emoji映射覆盖
```typescript
// 输入
{
  question_id: string;
  option_value: string;
  emoji_mapping: {
    primary: string;
    alternatives: string[];
  };
  color?: string;
  animation?: string;
  override_reason?: string;
}

// 输出
{
  success: boolean;
  data: boolean;
  error: string | null;
}
```

## 🔧 React Hooks API

### useGlobalConfig Hook

#### 基本用法
```typescript
import { useGlobalConfig } from '@/hooks/useGlobalConfig';

const {
  // 状态
  config,
  isLoading,
  error,
  isOnline,
  lastSyncTime,
  
  // 操作
  updateConfig,
  resetToDefault,
  syncToCloud,
  refreshConfig,
  
  // 便捷访问器
  themeMode,
  language,
  notificationsEnabled,
  soundEnabled,
  accessibilityConfig
} = useGlobalConfig();
```

#### 类型定义
```typescript
interface UseGlobalConfigReturn {
  // 状态
  config: GlobalAppConfig | null;
  isLoading: boolean;
  error: string | null;
  isOnline: boolean;
  lastSyncTime: Date | null;
  
  // 操作
  updateConfig: (updates: UpdateGlobalAppConfigInput) => Promise<boolean>;
  resetToDefault: () => Promise<boolean>;
  syncToCloud: () => Promise<boolean>;
  refreshConfig: () => Promise<void>;
  
  // 便捷访问器
  themeMode: 'light' | 'dark' | 'system';
  language: 'zh-CN' | 'en-US';
  notificationsEnabled: boolean;
  soundEnabled: boolean;
  accessibilityConfig: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    screen_reader_support: boolean;
  };
}
```

### useQuizConfig Hook

#### 基本用法
```typescript
import { useQuizConfig } from '@/hooks/useQuizConfig';

const {
  // 状态
  preferences,
  presentationConfig,
  isLoading,
  error,
  isOnline,
  lastSyncTime,
  
  // 操作
  updatePreferences,
  updatePresentationConfig,
  resetToDefault,
  syncToCloud,
  refreshConfig,
  
  // 会话配置
  generateSessionConfig,
  getPackOverrides,
  
  // 便捷访问器
  preferredViewType,
  colorMode,
  personalizationLevel,
  performanceMode
} = useQuizConfig();
```

#### QuizPresentationConfig 类型
```typescript
interface QuizPresentationConfig {
  layer0_dataset_presentation: {
    preferred_pack_categories: string[];
    default_difficulty_preference: 'beginner' | 'regular' | 'advanced' | 'expert';
    session_length_preference: 'short' | 'medium' | 'long';
    auto_select_recommended: boolean;
    restore_progress: boolean;
  };
  layer1_user_choice: {
    preferred_view_type: 'wheel' | 'card' | 'bubble' | 'list';
    active_skin_id: string;
    color_mode: 'warm' | 'cool' | 'neutral';
    user_level: 'beginner' | 'regular' | 'advanced';
  };
  layer2_rendering_strategy: {
    render_engine_preferences: Record<string, string>;
    performance_mode: 'high_quality' | 'balanced' | 'performance';
    supported_content_types: Record<string, boolean>;
  };
  layer3_skin_base: {
    selected_skin_id: string;
    colors: Record<string, string>;
    animations: {
      enable_animations: boolean;
      animation_speed: 'slow' | 'normal' | 'fast';
      reduce_motion: boolean;
    };
  };
  layer4_view_detail: {
    wheel_config: {
      container_size: number;
      wheel_radius: number;
      emotion_display_mode: string;
      show_labels: boolean;
      show_emojis: boolean;
    };
    // **新增**: 情绪展现配置
    emotion_presentation: {
      emoji_mapping: Record<string, EmojiMapping>;
      color_mapping: Record<string, string>;
      animation_mapping: Record<string, string>;
    };
  };
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    keyboard_navigation: boolean;
    voice_guidance: boolean;
  };
}
```

## 📊 数据流设计

### 全局应用设置流程
```
用户操作 → useGlobalConfig → GlobalAppConfigService → 本地SQLite
                                                    ↓
云端同步 ← tRPC config.global ← 网络可用时自动同步
```

### Quiz配置流程
```
用户偏好 + 包覆盖 + 系统默认 → QuizConfigMergerService → 最终会话配置
                                                      ↓
                              Quiz组件使用 ← useQuizConfig Hook
```

## 🎯 使用示例

### 全局配置管理
```typescript
const SettingsPage = () => {
  const {
    themeMode,
    language,
    updateConfig,
    isOnline,
    lastSyncTime
  } = useGlobalConfig();

  const handleThemeChange = async (theme: 'light' | 'dark' | 'system') => {
    await updateConfig({ theme_mode: theme });
  };

  return (
    <div>
      <p>当前主题: {themeMode}</p>
      <p>网络状态: {isOnline ? '在线' : '离线'}</p>
      <p>最后同步: {lastSyncTime?.toLocaleString()}</p>
    </div>
  );
};
```

### Quiz配置管理
```typescript
const QuizSettingsPage = () => {
  const {
    preferredViewType,
    colorMode,
    personalizationLevel,
    updatePresentationConfig,
    generateSessionConfig
  } = useQuizConfig();

  const handleViewTypeChange = async (viewType: 'wheel' | 'card') => {
    await updatePresentationConfig({
      layer1_user_choice: {
        preferred_view_type: viewType
      }
    });
  };

  const startQuizSession = async (packId: string) => {
    const sessionId = `session_${Date.now()}`;
    const sessionConfig = await generateSessionConfig(packId, sessionId);
    // 使用会话配置启动Quiz
  };

  return (
    <div>
      <p>偏好视图: {preferredViewType}</p>
      <p>个性化级别: {personalizationLevel}%</p>
    </div>
  );
};
```

## 🚀 技术特点

1. **类型安全**: 端到端TypeScript类型保护
2. **离线优先**: 本地SQLite存储，网络可用时自动同步
3. **配置合并**: 智能的多层配置优先级合并
4. **实时同步**: 网络状态变化时自动同步配置
5. **错误处理**: 完善的错误处理和用户反馈
6. **性能优化**: 懒加载服务实例和配置缓存

这个配置系统为应用提供了强大而灵活的配置管理能力，支持复杂的个性化需求，同时保持了良好的开发体验和用户体验。
