-- =============================================
-- 全局应用配置测试数据 (新配置系统)
-- 对应数据库中的 user_configs 表 (重构后)
-- 
-- 配置分离架构：
-- - 全局应用设置：主题、语言、通知、音效、无障碍
-- - Quiz系统配置：单独管理，存储在 user_quiz_preferences 表
-- =============================================

-- 清除旧的配置数据
DELETE FROM user_configs;

-- 插入新的全局应用配置测试数据
INSERT OR IGNORE INTO user_configs (
    id,
    user_id,
    config_name,
    theme_mode,
    language,
    notifications_enabled,
    sound_enabled,
    accessibility,
    is_active,
    is_default,
    created_at,
    updated_at
) VALUES
    -- 用户1：默认配置
    (
        'global_config_user_1',
        'test-user-1',
        'default',
        'system', -- theme_mode
        'zh-CN', -- language
        1, -- notifications_enabled
        1, -- sound_enabled
        '{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}', -- accessibility
        1, -- is_active
        1, -- is_default
        datetime('now', '-30 days'),
        datetime('now', '-1 hour')
    ),
    
    -- 用户2：深色主题 + 英文
    (
        'global_config_user_2',
        'test-user-2',
        'default',
        'dark', -- theme_mode
        'en-US', -- language
        1, -- notifications_enabled
        0, -- sound_enabled (关闭音效)
        '{"high_contrast": true, "large_text": false, "reduce_motion": true, "screen_reader_support": false}', -- accessibility
        1, -- is_active
        1, -- is_default
        datetime('now', '-15 days'),
        datetime('now', '-2 hours')
    ),
    
    -- 用户3：无障碍配置
    (
        'global_config_user_3',
        'test-user-3',
        'default',
        'light', -- theme_mode
        'zh-CN', -- language
        0, -- notifications_enabled (关闭通知)
        1, -- sound_enabled
        '{"high_contrast": true, "large_text": true, "reduce_motion": true, "screen_reader_support": true}', -- accessibility (完整无障碍支持)
        1, -- is_active
        1, -- is_default
        datetime('now', '-3 days'),
        datetime('now', '-1 day')
    ),
    
    -- 系统默认模板配置
    (
        'global_config_default_template',
        NULL, -- 全局模板
        'system_default',
        'system', -- theme_mode
        'zh-CN', -- language
        1, -- notifications_enabled
        1, -- sound_enabled
        '{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}', -- accessibility
        0, -- is_active (模板，非激活状态)
        0, -- is_default
        datetime('now', '-60 days'),
        datetime('now', '-60 days')
    ),
    
    -- VIP用户配置
    (
        'global_config_vip_user',
        'test-user-1',
        'vip_premium',
        'dark', -- theme_mode
        'en-US', -- language
        1, -- notifications_enabled
        1, -- sound_enabled
        '{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}', -- accessibility
        0, -- is_active (备用配置)
        0, -- is_default
        datetime('now', '-7 days'),
        datetime('now', '-1 hour')
    ),
    
    -- 工作模式配置
    (
        'global_config_work_mode',
        'test-user-2',
        'work_mode',
        'light', -- theme_mode (工作时间浅色主题)
        'en-US', -- language
        0, -- notifications_enabled (工作时关闭通知)
        0, -- sound_enabled (工作时关闭音效)
        '{"high_contrast": false, "large_text": false, "reduce_motion": true, "screen_reader_support": false}', -- accessibility (减少动画干扰)
        0, -- is_active (备用配置)
        0, -- is_default
        datetime('now', '-5 days'),
        datetime('now', '-30 minutes')
    ),
    
    -- 夜间模式配置
    (
        'global_config_night_mode',
        'test-user-1',
        'night_mode',
        'dark', -- theme_mode
        'zh-CN', -- language
        0, -- notifications_enabled (夜间关闭通知)
        0, -- sound_enabled (夜间关闭音效)
        '{"high_contrast": false, "large_text": true, "reduce_motion": true, "screen_reader_support": false}', -- accessibility (夜间护眼设置)
        0, -- is_active (备用配置)
        0, -- is_default
        datetime('now', '-2 days'),
        datetime('now', '-15 minutes')
    );

-- 验证插入的数据
SELECT 
    id,
    user_id,
    config_name,
    theme_mode,
    language,
    notifications_enabled,
    sound_enabled,
    is_active,
    is_default
FROM user_configs 
ORDER BY created_at DESC;
