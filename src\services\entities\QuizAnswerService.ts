/**
 * Quiz答案服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 */

import { BaseService } from '../base/BaseService';
import { QuizAnswerRepository } from './QuizAnswerRepository';
import { QuizAnswer } from '../../types/schema/base';
import { CreateQuizAnswerInput, UpdateQuizAnswerInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export interface QuizAnswerStats {
  total_answers: number;
  average_response_time: number;
  average_confidence: number;
  most_selected_options: Array<{
    option_id: string;
    selection_count: number;
    percentage: number;
  }>;
  response_patterns: Array<{
    pattern: string;
    frequency: number;
  }>;
}

export class QuizAnswerService extends BaseService<
  QuizAnswer,
  CreateQuizAnswerInput,
  UpdateQuizAnswerInput
> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new QuizAnswerRepository(db);
    super(repository);
  }

  /**
   * 保存Quiz答案
   */
  async saveAnswer(input: CreateQuizAnswerInput): Promise<ServiceResult<QuizAnswer>> {
    try {
      // 验证输入
      await this.validateCreate(input);

      // 调用Repository保存答案
      const answer = await this.repository.create(input);

      // 发射业务事件
      this.emit('answerSaved', answer);

      return this.createSuccessResult(answer);
    } catch (error) {
      return this.createErrorResult('Failed to save quiz answer', error);
    }
  }

  /**
   * 批量保存答案
   */
  async batchSaveAnswers(answers: CreateQuizAnswerInput[]): Promise<ServiceResult<QuizAnswer[]>> {
    try {
      // 验证所有输入
      for (const answer of answers) {
        await this.validateCreate(answer);
      }

      // 调用Repository批量保存
      const savedAnswers = await (this.repository as QuizAnswerRepository).batchInsertAnswers(answers);

      // 发射业务事件
      this.emit('answersBatchSaved', savedAnswers);

      return this.createSuccessResult(savedAnswers);
    } catch (error) {
      return this.createErrorResult('Failed to batch save quiz answers', error);
    }
  }

  /**
   * 获取所有答案
   */
  async getAllAnswers(limit: number = 1000): Promise<ServiceResult<QuizAnswer[]>> {
    try {
      const answers = await (this.repository as QuizAnswerRepository).getAllAnswers(limit);
      return this.createSuccessResult(answers);
    } catch (error) {
      return this.createErrorResult('Failed to get all quiz answers', error);
    }
  }

  /**
   * 获取会话的所有答案
   */
  async getSessionAnswers(sessionId: string): Promise<ServiceResult<QuizAnswer[]>> {
    try {
      const answers = await (this.repository as QuizAnswerRepository).findBySessionId(sessionId);
      return this.createSuccessResult(answers);
    } catch (error) {
      return this.createErrorResult('Failed to get session answers', error);
    }
  }

  /**
   * 获取特定问题的答案
   */
  async getQuestionAnswer(sessionId: string, questionId: string): Promise<ServiceResult<QuizAnswer | null>> {
    try {
      const answer = await (this.repository as QuizAnswerRepository).findByQuestionId(sessionId, questionId);
      return this.createSuccessResult(answer);
    } catch (error) {
      return this.createErrorResult('Failed to get question answer', error);
    }
  }

  /**
   * 获取用户的答案历史
   */
  async getUserAnswerHistory(
    userId: string,
    packId?: string,
    limit: number = 100
  ): Promise<ServiceResult<QuizAnswer[]>> {
    try {
      const answers = await (this.repository as QuizAnswerRepository).findByUserId(userId, packId, limit);
      return this.createSuccessResult(answers);
    } catch (error) {
      return this.createErrorResult('Failed to get user answer history', error);
    }
  }

  /**
   * 获取答案统计
   */
  async getAnswerStats(sessionId: string): Promise<ServiceResult<any>> {
    try {
      const stats = await (this.repository as QuizAnswerRepository).getAnswerStats(sessionId);
      return this.createSuccessResult(stats);
    } catch (error) {
      return this.createErrorResult('Failed to get answer stats', error);
    }
  }

  /**
   * 获取选项选择统计
   */
  async getOptionSelectionStats(packId: string): Promise<ServiceResult<any[]>> {
    try {
      const stats = await (this.repository as QuizAnswerRepository).getOptionSelectionStats(packId);
      return this.createSuccessResult(stats);
    } catch (error) {
      return this.createErrorResult('Failed to get option selection stats', error);
    }
  }

  /**
   * 获取用户答案分析
   */
  async getUserAnswerAnalysis(userId: string, packId?: string): Promise<ServiceResult<QuizAnswerStats>> {
    try {
      const answers = await (this.repository as QuizAnswerRepository).findByUserId(userId, packId, 1000);

      const totalAnswers = answers.length;

      if (totalAnswers === 0) {
        return this.createSuccessResult({
          total_answers: 0,
          average_response_time: 0,
          average_confidence: 0,
          most_selected_options: [],
          response_patterns: []
        });
      }

      // 计算平均响应时间
      const averageResponseTime = Math.round(
        answers.reduce((sum, answer) => sum + (answer.response_time_ms || 0), 0) / totalAnswers
      );

      // 计算平均置信度
      const averageConfidence = Math.round(
        answers.reduce((sum, answer) => sum + (answer.confidence_score || 0), 0) / totalAnswers
      );

      // 统计最常选择的选项
      const optionCounts: { [key: string]: number } = {};
      answers.forEach(answer => {
        if (answer.selected_option_ids) {
          const optionIds = Array.isArray(answer.selected_option_ids)
            ? answer.selected_option_ids
            : JSON.parse(answer.selected_option_ids);
          optionIds.forEach((optionId: string) => {
            optionCounts[optionId] = (optionCounts[optionId] || 0) + 1;
          });
        }
      });

      const mostSelectedOptions = Object.entries(optionCounts)
        .map(([option_id, selection_count]) => ({
          option_id,
          selection_count,
          percentage: Math.round((selection_count / totalAnswers) * 100)
        }))
        .sort((a, b) => b.selection_count - a.selection_count)
        .slice(0, 10);

      // 分析响应模式
      const patternCounts: { [key: string]: number } = {};
      answers.forEach(answer => {
        if (answer.answer_value) {
          patternCounts[answer.answer_value] = (patternCounts[answer.answer_value] || 0) + 1;
        }
      });

      const responsePatterns = Object.entries(patternCounts)
        .map(([pattern, frequency]) => ({ pattern, frequency }))
        .sort((a, b) => b.frequency - a.frequency)
        .slice(0, 10);

      const stats: QuizAnswerStats = {
        total_answers: totalAnswers,
        average_response_time: averageResponseTime,
        average_confidence: averageConfidence,
        most_selected_options: mostSelectedOptions,
        response_patterns: responsePatterns
      };

      return this.createSuccessResult(stats);
    } catch (error) {
      return this.createErrorResult('Failed to get user answer analysis', error);
    }
  }

  /**
   * 删除会话的所有答案
   */
  async deleteSessionAnswers(sessionId: string): Promise<ServiceResult<boolean>> {
    try {
      const result = await (this.repository as QuizAnswerRepository).deleteBySessionId(sessionId);

      if (result) {
        this.emit('sessionAnswersDeleted', { sessionId });
      }

      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to delete session answers', error);
    }
  }

  /**
   * 更新答案
   */
  async updateAnswer(answerId: string, updates: UpdateQuizAnswerInput): Promise<ServiceResult<QuizAnswer>> {
    try {
      // 验证更新数据
      await this.validateUpdate(updates);

      // 调用Repository更新
      const answer = await this.repository.update(answerId, updates);

      // 发射业务事件
      this.emit('answerUpdated', answer);

      return this.createSuccessResult(answer);
    } catch (error) {
      return this.createErrorResult('Failed to update quiz answer', error);
    }
  }

  // 实现抽象方法
  protected async validateCreate(data: CreateQuizAnswerInput): Promise<void> {
    if (!data.session_id) {
      throw new Error('Session ID is required');
    }
    if (!data.question_id) {
      throw new Error('Question ID is required');
    }
    if (!data.selected_option_ids || data.selected_option_ids.length === 0) {
      throw new Error('At least one selected option is required');
    }
    if (!data.answer_value) {
      throw new Error('Answer value is required');
    }
    if (data.confidence_score !== undefined && (data.confidence_score < 0 || data.confidence_score > 100)) {
      throw new Error('Confidence score must be between 0 and 100');
    }
    if (data.response_time_ms !== undefined && data.response_time_ms < 0) {
      throw new Error('Response time must be non-negative');
    }
  }

  protected async validateUpdate(data: UpdateQuizAnswerInput): Promise<void> {
    if (data.selected_option_ids !== undefined && data.selected_option_ids.length === 0) {
      throw new Error('At least one selected option is required');
    }
    if (data.confidence_score !== undefined && (data.confidence_score < 0 || data.confidence_score > 100)) {
      throw new Error('Confidence score must be between 0 and 100');
    }
    if (data.response_time_ms !== undefined && data.response_time_ms < 0) {
      throw new Error('Response time must be non-negative');
    }
  }
}
