/**
 * Quiz问题选项服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 */

import { BaseService } from '../base/BaseService';
import { QuizQuestionOptionRepository } from './QuizQuestionOptionRepository';
import { QuizQuestionOption } from '../../types/schema/base';
import {
  CreateQuizQuestionOptionInput,
  UpdateQuizQuestionOptionInput,
  QuizQuestionOptionStats,
  OptionGroupResult
} from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class QuizQuestionOptionService extends BaseService<
  QuizQuestionOption,
  CreateQuizQuestionOptionInput,
  UpdateQuizQuestionOptionInput
> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new QuizQuestionOptionRepository(db);
    super(repository);
  }

  /**
   * 创建新的问题选项
   */
  async createOption(input: CreateQuizQuestionOptionInput): Promise<ServiceResult<QuizQuestionOption>> {
    try {
      // 验证输入
      await this.validateCreate(input);

      // 如果没有指定顺序，自动分配
      if (input.option_order === undefined) {
        const maxOrder = await (this.repository as QuizQuestionOptionRepository).getMaxOptionOrder(input.question_id);
        input.option_order = maxOrder + 1;
      }

      // 调用Repository创建选项
      const option = await this.repository.create(input);

      // 发射业务事件
      this.emit('optionCreated', option);

      return this.createSuccessResult(option);
    } catch (error) {
      return this.createErrorResult('Failed to create quiz question option', error);
    }
  }

  /**
   * 批量创建选项
   */
  async batchCreateOptions(options: CreateQuizQuestionOptionInput[]): Promise<ServiceResult<QuizQuestionOption[]>> {
    try {
      // 验证所有输入
      for (const option of options) {
        await this.validateCreate(option);
      }

      // 为没有指定顺序的选项自动分配顺序
      const questionIds = [...new Set(options.map(opt => opt.question_id))];
      const maxOrders: { [questionId: string]: number } = {};

      for (const questionId of questionIds) {
        maxOrders[questionId] = await (this.repository as QuizQuestionOptionRepository).getMaxOptionOrder(questionId);
      }

      let orderCounters: { [questionId: string]: number } = {};
      for (const questionId of questionIds) {
        orderCounters[questionId] = maxOrders[questionId];
      }

      for (const option of options) {
        if (option.option_order === undefined) {
          orderCounters[option.question_id]++;
          option.option_order = orderCounters[option.question_id];
        }
      }

      // 批量创建
      const savedOptions = await (this.repository as QuizQuestionOptionRepository).batchInsertOptions(options);

      // 发射业务事件
      this.emit('optionsBatchCreated', savedOptions);

      return this.createSuccessResult(savedOptions);
    } catch (error) {
      return this.createErrorResult('Failed to batch create quiz question options', error);
    }
  }

  /**
   * 获取问题的所有选项
   */
  async getQuestionOptions(questionId: string): Promise<ServiceResult<QuizQuestionOption[]>> {
    try {
      const options = await (this.repository as QuizQuestionOptionRepository).findByQuestionId(questionId);
      return this.createSuccessResult(options);
    } catch (error) {
      return this.createErrorResult('Failed to get question options', error);
    }
  }

  /**
   * 根据选项组获取选项
   */
  async getOptionsByGroup(questionId: string, optionGroup: string): Promise<ServiceResult<QuizQuestionOption[]>> {
    try {
      if (!optionGroup || optionGroup.trim().length === 0) {
        throw new Error('Option group cannot be empty');
      }

      const options = await (this.repository as QuizQuestionOptionRepository).findByOptionGroup(questionId, optionGroup.trim());
      return this.createSuccessResult(options);
    } catch (error) {
      return this.createErrorResult('Failed to get options by group', error);
    }
  }

  /**
   * 获取问题的所有选项组
   */
  async getQuestionOptionGroups(questionId: string): Promise<ServiceResult<OptionGroupResult[]>> {
    try {
      const allOptions = await (this.repository as QuizQuestionOptionRepository).findByQuestionId(questionId);

      // 按组分类选项
      const groupMap: { [group: string]: QuizQuestionOption[] } = {};

      allOptions.forEach(option => {
        // 使用metadata中的group信息，或者根据option_type分组
        const group = this.extractGroupFromOption(option);
        if (!groupMap[group]) {
          groupMap[group] = [];
        }
        groupMap[group].push(option);
      });

      // 转换为结果格式
      const groupResults: OptionGroupResult[] = Object.entries(groupMap).map(([groupName, options]) => ({
        group_name: groupName,
        options: options.sort((a, b) => a.option_order - b.option_order),
        total_count: options.length
      }));

      return this.createSuccessResult(groupResults);
    } catch (error) {
      return this.createErrorResult('Failed to get question option groups', error);
    }
  }

  /**
   * 获取正确答案选项
   */
  async getCorrectOptions(questionId: string): Promise<ServiceResult<QuizQuestionOption[]>> {
    try {
      const options = await (this.repository as QuizQuestionOptionRepository).findCorrectOptions(questionId);
      return this.createSuccessResult(options);
    } catch (error) {
      return this.createErrorResult('Failed to get correct options', error);
    }
  }

  /**
   * 根据内容显示模式获取选项
   */
  async getOptionsByDisplayMode(questionId: string, displayMode: string): Promise<ServiceResult<QuizQuestionOption[]>> {
    try {
      if (!displayMode || displayMode.trim().length === 0) {
        throw new Error('Display mode cannot be empty');
      }

      const options = await (this.repository as QuizQuestionOptionRepository).findByContentDisplayMode(questionId, displayMode.trim());
      return this.createSuccessResult(options);
    } catch (error) {
      return this.createErrorResult('Failed to get options by display mode', error);
    }
  }

  /**
   * 获取选项统计信息
   */
  async getOptionStats(questionId: string): Promise<ServiceResult<QuizQuestionOptionStats>> {
    try {
      const rawStats = await (this.repository as QuizQuestionOptionRepository).getOptionStats(questionId);

      const stats: QuizQuestionOptionStats = {
        total_options: rawStats.total_options || 0,
        correct_options: rawStats.correct_options || 0,
        option_groups: rawStats.option_groups || 0,
        options_with_images: rawStats.options_with_images || 0,
        options_with_emojis: rawStats.options_with_emojis || 0,
        avg_scoring_value: Math.round((rawStats.avg_scoring_value || 0) * 100) / 100
      };

      return this.createSuccessResult(stats);
    } catch (error) {
      return this.createErrorResult('Failed to get option stats', error);
    }
  }

  /**
   * 重新排序选项
   */
  async reorderOptions(
    questionId: string,
    optionOrders: Array<{ id: string; option_order: number }>
  ): Promise<ServiceResult<boolean>> {
    try {
      // 验证所有选项都属于指定的问题
      const questionOptions = await (this.repository as QuizQuestionOptionRepository).findByQuestionId(questionId);
      const questionOptionIds = new Set(questionOptions.map(opt => opt.id));

      for (const item of optionOrders) {
        if (!questionOptionIds.has(item.id)) {
          throw new Error(`Option ${item.id} does not belong to question ${questionId}`);
        }
        if (item.option_order < 0) {
          throw new Error('Option order must be non-negative');
        }
      }

      // 批量更新顺序
      const success = await (this.repository as QuizQuestionOptionRepository).batchUpdateOptionOrder(optionOrders);

      if (success) {
        this.emit('optionsReordered', { questionId, optionOrders });
      }

      return this.createSuccessResult(success);
    } catch (error) {
      return this.createErrorResult('Failed to reorder options', error);
    }
  }

  /**
   * 复制选项到另一个问题
   */
  async duplicateOption(optionId: string, targetQuestionId: string): Promise<ServiceResult<QuizQuestionOption>> {
    try {
      // 获取原选项
      const originalOption = await this.repository.findById(optionId);
      if (!originalOption) {
        throw new Error('Original option not found');
      }

      // 获取目标问题的最大顺序号
      const maxOrder = await (this.repository as QuizQuestionOptionRepository).getMaxOptionOrder(targetQuestionId);

      // 创建复制的选项数据
      const duplicateData: CreateQuizQuestionOptionInput = {
        question_id: targetQuestionId,
        option_text: `${originalOption.option_text} (Copy)`,
        option_text_localized: originalOption.option_text_localized,
        option_value: `${originalOption.option_value}_copy`,
        option_type: originalOption.option_type,
        option_order: maxOrder + 1,
        scoring_value: originalOption.scoring_value,
        scoring_weight: originalOption.scoring_weight,
        min_value: originalOption.min_value,
        max_value: originalOption.max_value,
        step_value: originalOption.step_value,
        media_url: originalOption.media_url,
        media_type: originalOption.media_type,
        media_thumbnail_url: originalOption.media_thumbnail_url,
        media_alt_text: originalOption.media_alt_text,
        matrix_row_id: originalOption.matrix_row_id,
        matrix_column_id: originalOption.matrix_column_id,
        reference_pack_id: originalOption.reference_pack_id,
        reference_option_id: originalOption.reference_option_id,
        metadata: originalOption.metadata,
        tags: originalOption.tags,
        is_active: originalOption.is_active,
      };

      // 创建复制的选项
      const duplicatedOption = await this.repository.create(duplicateData);

      this.emit('optionDuplicated', { originalOption, duplicatedOption });

      return this.createSuccessResult(duplicatedOption);
    } catch (error) {
      return this.createErrorResult('Failed to duplicate option', error);
    }
  }

  /**
   * 删除问题的所有选项
   */
  async deleteQuestionOptions(questionId: string): Promise<ServiceResult<boolean>> {
    try {
      const result = await (this.repository as QuizQuestionOptionRepository).deleteByQuestionId(questionId);

      if (result) {
        this.emit('questionOptionsDeleted', { questionId });
      }

      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to delete question options', error);
    }
  }

  /**
   * 更新选项
   */
  async updateOption(optionId: string, updates: UpdateQuizQuestionOptionInput): Promise<ServiceResult<QuizQuestionOption>> {
    try {
      // 验证更新数据
      await this.validateUpdate(updates);

      // 调用Repository更新
      const option = await this.repository.update(optionId, updates);

      // 发射业务事件
      this.emit('optionUpdated', option);

      return this.createSuccessResult(option);
    } catch (error) {
      return this.createErrorResult('Failed to update quiz question option', error);
    }
  }

  // 实现抽象方法
  protected async validateCreate(data: CreateQuizQuestionOptionInput): Promise<void> {
    if (!data.question_id || data.question_id.trim().length === 0) {
      throw new Error('Question ID is required');
    }
    if (!data.option_text || data.option_text.trim().length === 0) {
      throw new Error('Option text is required');
    }
    if (data.option_text.length > 500) {
      throw new Error('Option text must be less than 500 characters');
    }
    if (!data.option_value || data.option_value.trim().length === 0) {
      throw new Error('Option value is required');
    }
    if (data.option_order !== undefined && data.option_order < 0) {
      throw new Error('Option order must be non-negative');
    }
  }

  protected async validateUpdate(data: UpdateQuizQuestionOptionInput): Promise<void> {
    if (data.option_text !== undefined && (!data.option_text || data.option_text.trim().length === 0)) {
      throw new Error('Option text cannot be empty');
    }
    if (data.option_text !== undefined && data.option_text.length > 500) {
      throw new Error('Option text must be less than 500 characters');
    }
    if (data.option_value !== undefined && (!data.option_value || data.option_value.trim().length === 0)) {
      throw new Error('Option value cannot be empty');
    }
    if (data.option_order !== undefined && data.option_order < 0) {
      throw new Error('Option order must be non-negative');
    }
  }

  /**
   * 从选项中提取分组信息
   */
  private extractGroupFromOption(option: QuizQuestionOption): string {
    // 尝试从metadata中解析group信息
    if (option.metadata) {
      try {
        const metadata = JSON.parse(option.metadata);
        if (metadata.group) {
          return metadata.group;
        }
      } catch {
        // 忽略JSON解析错误
      }
    }

    // 根据option_type分组
    return option.option_type || 'default';
  }
}
