-- Quiz量表皮肤配置数据
-- 为情绪追踪问卷和中医体质问卷提供多种皮肤主题

-- 1. 情绪追踪问卷皮肤配置

-- 默认现代风格皮肤
INSERT INTO skin_configs (
  id, name, name_localized, description, description_localized,
  skin_type, target_type, target_id, config_data, is_active,
  created_at, updated_at
) VALUES (
  'emotion_quiz_modern',
  'Modern Emotion Theme',
  '{"zh-CN": "现代情绪主题", "en-US": "Modern Emotion Theme"}',
  'Clean and modern design for emotion tracking quiz',
  '{"zh-CN": "情绪追踪问卷的简洁现代设计", "en-US": "Clean and modern design for emotion tracking quiz"}',
  'quiz_theme',
  'quiz_pack',
  'mood_track_branching_v1',
  '{
    "color_scheme": {
      "primary": "#4F46E5",
      "secondary": "#06B6D4",
      "accent": "#F59E0B",
      "background": "#FFFFFF",
      "surface": "#F8FAFC",
      "text_primary": "#1F2937",
      "text_secondary": "#6B7280"
    },
    "typography": {
      "font_family": "Inter, system-ui, sans-serif",
      "question_title_size": "1.5rem",
      "option_text_size": "1rem",
      "description_size": "0.875rem"
    },
    "layout": {
      "container_max_width": "600px",
      "question_spacing": "2rem",
      "option_spacing": "1rem",
      "border_radius": "0.75rem"
    },
    "emotion_specific": {
      "show_emoji": true,
      "emoji_size": "2rem",
      "show_emotion_colors": true,
      "progress_style": "linear",
      "path_visualization": "breadcrumb"
    },
    "animations": {
      "transition_duration": "300ms",
      "hover_scale": "1.02",
      "selection_animation": "pulse"
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 温暖治愈风格皮肤
INSERT INTO skin_configs (
  id, name, name_localized, description, description_localized,
  skin_type, target_type, target_id, config_data, is_active,
  created_at, updated_at
) VALUES (
  'emotion_quiz_warm',
  'Warm Healing Theme',
  '{"zh-CN": "温暖治愈主题", "en-US": "Warm Healing Theme"}',
  'Warm and healing design for emotional wellness',
  '{"zh-CN": "温暖治愈的情绪健康设计", "en-US": "Warm and healing design for emotional wellness"}',
  'quiz_theme',
  'quiz_pack',
  'mood_track_branching_v1',
  '{
    "color_scheme": {
      "primary": "#F97316",
      "secondary": "#EAB308",
      "accent": "#EC4899",
      "background": "#FEF7ED",
      "surface": "#FFF7ED",
      "text_primary": "#9A3412",
      "text_secondary": "#C2410C"
    },
    "typography": {
      "font_family": "Nunito, system-ui, sans-serif",
      "question_title_size": "1.625rem",
      "option_text_size": "1.125rem",
      "description_size": "1rem"
    },
    "layout": {
      "container_max_width": "650px",
      "question_spacing": "2.5rem",
      "option_spacing": "1.25rem",
      "border_radius": "1rem"
    },
    "emotion_specific": {
      "show_emoji": true,
      "emoji_size": "2.5rem",
      "show_emotion_colors": true,
      "progress_style": "circular",
      "path_visualization": "tree",
      "warm_gradients": true
    },
    "animations": {
      "transition_duration": "400ms",
      "hover_scale": "1.05",
      "selection_animation": "glow"
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 极简风格皮肤
INSERT INTO skin_configs (
  id, name, name_localized, description, description_localized,
  skin_type, target_type, target_id, config_data, is_active,
  created_at, updated_at
) VALUES (
  'emotion_quiz_minimal',
  'Minimal Clean Theme',
  '{"zh-CN": "极简清洁主题", "en-US": "Minimal Clean Theme"}',
  'Minimalist design focusing on content',
  '{"zh-CN": "专注内容的极简主义设计", "en-US": "Minimalist design focusing on content"}',
  'quiz_theme',
  'quiz_pack',
  'mood_track_branching_v1',
  '{
    "color_scheme": {
      "primary": "#000000",
      "secondary": "#6B7280",
      "accent": "#3B82F6",
      "background": "#FFFFFF",
      "surface": "#FAFAFA",
      "text_primary": "#111827",
      "text_secondary": "#4B5563"
    },
    "typography": {
      "font_family": "SF Pro Display, system-ui, sans-serif",
      "question_title_size": "1.375rem",
      "option_text_size": "1rem",
      "description_size": "0.875rem"
    },
    "layout": {
      "container_max_width": "500px",
      "question_spacing": "1.5rem",
      "option_spacing": "0.75rem",
      "border_radius": "0.5rem"
    },
    "emotion_specific": {
      "show_emoji": false,
      "show_emotion_colors": false,
      "progress_style": "minimal",
      "path_visualization": "text",
      "clean_borders": true
    },
    "animations": {
      "transition_duration": "200ms",
      "hover_scale": "1.01",
      "selection_animation": "fade"
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 2. 中医体质问卷皮肤配置

-- 传统中医风格皮肤
INSERT INTO skin_configs (
  id, name, name_localized, description, description_localized,
  skin_type, target_type, target_id, config_data, is_active,
  created_at, updated_at
) VALUES (
  'tcm_quiz_traditional',
  'Traditional TCM Theme',
  '{"zh-CN": "传统中医主题", "en-US": "Traditional TCM Theme"}',
  'Traditional Chinese Medicine inspired design',
  '{"zh-CN": "传统中医风格设计", "en-US": "Traditional Chinese Medicine inspired design"}',
  'quiz_theme',
  'quiz_pack',
  'tcm_liver_syndrome',
  '{
    "color_scheme": {
      "primary": "#B91C1C",
      "secondary": "#D97706",
      "accent": "#059669",
      "background": "#FEF7ED",
      "surface": "#FFF8F1",
      "text_primary": "#7C2D12",
      "text_secondary": "#A16207"
    },
    "typography": {
      "font_family": "Noto Serif SC, serif",
      "question_title_size": "1.5rem",
      "option_text_size": "1.125rem",
      "description_size": "1rem"
    },
    "layout": {
      "container_max_width": "700px",
      "question_spacing": "2rem",
      "option_spacing": "1rem",
      "border_radius": "0.5rem"
    },
    "tcm_specific": {
      "show_syndrome_colors": true,
      "severity_indicators": "traditional",
      "progress_style": "yin_yang",
      "score_visualization": "meridian",
      "traditional_borders": true,
      "five_element_colors": {
        "wood": "#059669",
        "fire": "#DC2626", 
        "earth": "#D97706",
        "metal": "#6B7280",
        "water": "#1D4ED8"
      }
    },
    "animations": {
      "transition_duration": "350ms",
      "hover_scale": "1.02",
      "selection_animation": "ripple"
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 现代中医风格皮肤
INSERT INTO skin_configs (
  id, name, name_localized, description, description_localized,
  skin_type, target_type, target_id, config_data, is_active,
  created_at, updated_at
) VALUES (
  'tcm_quiz_modern',
  'Modern TCM Theme',
  '{"zh-CN": "现代中医主题", "en-US": "Modern TCM Theme"}',
  'Modern interpretation of TCM principles',
  '{"zh-CN": "中医理念的现代诠释", "en-US": "Modern interpretation of TCM principles"}',
  'quiz_theme',
  'quiz_pack',
  'tcm_liver_syndrome',
  '{
    "color_scheme": {
      "primary": "#7C3AED",
      "secondary": "#06B6D4",
      "accent": "#10B981",
      "background": "#FFFFFF",
      "surface": "#F8FAFC",
      "text_primary": "#1F2937",
      "text_secondary": "#6B7280"
    },
    "typography": {
      "font_family": "Inter, system-ui, sans-serif",
      "question_title_size": "1.5rem",
      "option_text_size": "1rem",
      "description_size": "0.875rem"
    },
    "layout": {
      "container_max_width": "650px",
      "question_spacing": "2rem",
      "option_spacing": "1rem",
      "border_radius": "0.75rem"
    },
    "tcm_specific": {
      "show_syndrome_colors": true,
      "severity_indicators": "modern",
      "progress_style": "linear_gradient",
      "score_visualization": "chart",
      "modern_cards": true,
      "syndrome_icons": true
    },
    "animations": {
      "transition_duration": "300ms",
      "hover_scale": "1.03",
      "selection_animation": "slide"
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 3. 通用皮肤配置 (适用于所有Quiz类型)

-- 深色主题皮肤
INSERT INTO skin_configs (
  id, name, name_localized, description, description_localized,
  skin_type, target_type, target_id, config_data, is_active,
  created_at, updated_at
) VALUES (
  'quiz_dark_theme',
  'Dark Theme',
  '{"zh-CN": "深色主题", "en-US": "Dark Theme"}',
  'Dark mode theme for all quiz types',
  '{"zh-CN": "适用于所有问卷类型的深色主题", "en-US": "Dark mode theme for all quiz types"}',
  'quiz_theme',
  'global',
  '*',
  '{
    "color_scheme": {
      "primary": "#8B5CF6",
      "secondary": "#06B6D4",
      "accent": "#F59E0B",
      "background": "#111827",
      "surface": "#1F2937",
      "text_primary": "#F9FAFB",
      "text_secondary": "#D1D5DB"
    },
    "typography": {
      "font_family": "Inter, system-ui, sans-serif",
      "question_title_size": "1.5rem",
      "option_text_size": "1rem",
      "description_size": "0.875rem"
    },
    "layout": {
      "container_max_width": "600px",
      "question_spacing": "2rem",
      "option_spacing": "1rem",
      "border_radius": "0.75rem"
    },
    "dark_mode": {
      "enabled": true,
      "surface_opacity": 0.8,
      "border_opacity": 0.2,
      "shadow_intensity": "low"
    },
    "animations": {
      "transition_duration": "300ms",
      "hover_scale": "1.02",
      "selection_animation": "glow"
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 高对比度无障碍皮肤
INSERT INTO skin_configs (
  id, name, name_localized, description, description_localized,
  skin_type, target_type, target_id, config_data, is_active,
  created_at, updated_at
) VALUES (
  'quiz_accessibility',
  'High Contrast Accessibility',
  '{"zh-CN": "高对比度无障碍", "en-US": "High Contrast Accessibility"}',
  'High contrast theme for accessibility',
  '{"zh-CN": "无障碍高对比度主题", "en-US": "High contrast theme for accessibility"}',
  'quiz_theme',
  'global',
  '*',
  '{
    "color_scheme": {
      "primary": "#000000",
      "secondary": "#FFFFFF",
      "accent": "#0000FF",
      "background": "#FFFFFF",
      "surface": "#F5F5F5",
      "text_primary": "#000000",
      "text_secondary": "#333333"
    },
    "typography": {
      "font_family": "Arial, sans-serif",
      "question_title_size": "1.75rem",
      "option_text_size": "1.25rem",
      "description_size": "1.125rem"
    },
    "layout": {
      "container_max_width": "800px",
      "question_spacing": "3rem",
      "option_spacing": "1.5rem",
      "border_radius": "0.25rem"
    },
    "accessibility": {
      "high_contrast": true,
      "large_text": true,
      "bold_borders": true,
      "focus_indicators": "enhanced",
      "reduced_motion": true
    },
    "animations": {
      "transition_duration": "0ms",
      "hover_scale": "1.0",
      "selection_animation": "none"
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- 4. 为特定证素创建专门皮肤

-- 肾证素专用皮肤 (水元素主题)
INSERT INTO skin_configs (
  id, name, name_localized, description, description_localized,
  skin_type, target_type, target_id, config_data, is_active,
  created_at, updated_at
) VALUES (
  'tcm_kidney_water_theme',
  'Kidney Water Element Theme',
  '{"zh-CN": "肾水元素主题", "en-US": "Kidney Water Element Theme"}',
  'Water element inspired theme for kidney syndrome assessment',
  '{"zh-CN": "肾证素评估的水元素主题", "en-US": "Water element inspired theme for kidney syndrome assessment"}',
  'quiz_theme',
  'quiz_pack',
  'tcm_kidney_syndrome',
  '{
    "color_scheme": {
      "primary": "#1E40AF",
      "secondary": "#0EA5E9",
      "accent": "#3B82F6",
      "background": "#F0F9FF",
      "surface": "#E0F2FE",
      "text_primary": "#1E3A8A",
      "text_secondary": "#1E40AF"
    },
    "typography": {
      "font_family": "Noto Sans SC, sans-serif",
      "question_title_size": "1.5rem",
      "option_text_size": "1rem",
      "description_size": "0.875rem"
    },
    "layout": {
      "container_max_width": "650px",
      "question_spacing": "2rem",
      "option_spacing": "1rem",
      "border_radius": "1rem"
    },
    "element_specific": {
      "element": "water",
      "flowing_animations": true,
      "water_gradients": true,
      "kidney_specific_icons": true,
      "severity_colors": {
        "none": "#E0F2FE",
        "mild": "#BAE6FD", 
        "moderate": "#7DD3FC",
        "severe": "#38BDF8"
      }
    },
    "animations": {
      "transition_duration": "400ms",
      "hover_scale": "1.02",
      "selection_animation": "wave"
    }
  }',
  1,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);
