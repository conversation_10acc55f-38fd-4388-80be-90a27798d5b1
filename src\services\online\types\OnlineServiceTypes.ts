/**
 * 在线服务类型定义 (简化版)
 * 只保留基础类型，业务逻辑类型通过tRPC定义
 */

// API 响应基础类型 (保留用于基础服务)
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  timestamp?: string;
}

// API 错误类型
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
  timestamp?: string;
}

// 网络状态类型
export interface NetworkStatus {
  isOnline: boolean;
  connectionType?: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  isSlowConnection?: boolean;
  lastOnlineTime?: Date;
  lastOfflineTime?: Date;
}

// API 请求配置
export interface ApiRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  requireAuth?: boolean;
  skipErrorHandling?: boolean;
}

// 上传进度信息
export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number; // bytes per second
  estimatedTime?: number; // seconds
}

// 下载进度信息
export interface DownloadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number; // bytes per second
  estimatedTime?: number; // seconds
}

// 服务器信息类型
export interface ServerInfo {
  version: string;
  environment: 'development' | 'staging' | 'production';
  features: string[];
  maintenance?: {
    scheduled: boolean;
    startTime?: Date;
    endTime?: Date;
    message?: string;
  };
  limits?: {
    maxRequestSize: number;
    maxBatchSize: number;
    rateLimit: number;
  };
}

// 健康检查响应
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  services: {
    database: 'up' | 'down' | 'degraded';
    cache: 'up' | 'down' | 'degraded';
    storage: 'up' | 'down' | 'degraded';
  };
  responseTime: number; // milliseconds
  version: string;
}

// 在线服务事件类型 (简化版)
export type OnlineServiceEvent =
  | { type: 'network_status_changed'; data: NetworkStatus }
  | { type: 'server_error'; data: ApiError }
  | { type: 'connection_lost'; data: { timestamp: Date } }
  | { type: 'connection_restored'; data: { timestamp: Date } };

// 缓存策略类型
export interface CacheStrategy {
  type: 'memory' | 'localStorage' | 'indexedDB' | 'none';
  ttl?: number; // seconds
  maxSize?: number; // bytes
  compression?: boolean;
}

// API 端点配置
export interface ApiEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  requireAuth: boolean;
  cache?: CacheStrategy;
  timeout?: number;
  retries?: number;
  rateLimit?: {
    requests: number;
    window: number; // seconds
  };
}

// WebSocket 连接状态
export interface WebSocketStatus {
  connected: boolean;
  connecting: boolean;
  lastConnectedAt?: Date;
  lastDisconnectedAt?: Date;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  reconnectDelay: number;
}

// 服务质量指标
export interface ServiceQualityMetrics {
  averageResponseTime: number; // milliseconds
  successRate: number; // percentage
  errorRate: number; // percentage
  uptime: number; // percentage
  lastMeasuredAt: Date;
  sampleSize: number;
}

// 数据使用统计
export interface DataUsageStats {
  uploadedBytes: number;
  downloadedBytes: number;
  requestCount: number;
  cacheHitRate: number; // percentage
  compressionRatio: number; // percentage
  period: {
    start: Date;
    end: Date;
  };
}

// tRPC 客户端类型 (用于业务服务)
export interface TRPCClientType {
  // 认证相关
  login: { mutate: (data: any) => Promise<any> };
  register: { mutate: (data: any) => Promise<any> };
  logout: { mutate: () => Promise<any> };

  // VIP和支付相关
  getVipPlans: { query: () => Promise<any> };
  getVipStatus: { query: () => Promise<any> };
  purchaseVip: { mutate: (data: any) => Promise<any> };
  purchaseSkin: { mutate: (data: any) => Promise<any> };
  getPurchaseHistory: { query: () => Promise<any> };

  // 数据同步相关
  synchronizeData: { mutate: (data: any) => Promise<any> };
  performFullSync: { mutate: (data: any) => Promise<any> };

  // 数据库查询相关
  query: { query: (data: any) => Promise<any> };
  batch: { mutate: (data: any) => Promise<any> };

  // 分析相关
  getMoodAnalytics: { query: (data: any) => Promise<any> };
  getEmotionUsageStats: { query: (data: any) => Promise<any> };
  getUserActivityStats: { query: (data: any) => Promise<any> };
}

// 认证令牌类型 (保留用于API客户端)
export interface AuthToken {
  accessToken: string;
  tokenType: string;
  expiresAt: Date;
  refreshToken?: string;
}

// 批量操作结果类型 (保留用于API客户端)
export interface BatchOperationResult<T = any> {
  results: T[];
  errors: Array<{ index: number; error: string }>;
  totalProcessed: number;
  totalSuccessful: number;
  totalFailed: number;
}

// 在线服务配置类型 (扩展版)
export interface OnlineServiceConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  authConfig: {
    tokenStorageKey: string;
    autoRefresh: boolean;
    refreshThreshold: number; // minutes before expiry
  };
}

// 注意：以下类型已移除，现在通过tRPC定义：
// - AuthUser, AuthState, LoginCredentials, RegisterData
// - MoodEntry, UserProfile, PaymentPlan, PaymentResult
// - SyncStatus, SyncConfig, SyncItem, SyncBatch
// - ConflictResolution, RealtimeUpdate
//
// 这些类型现在在以下位置定义：
// - 认证相关：src/hooks/useAuth.ts 或 src/lib/trpc.ts
// - 业务数据：server/lib/router.ts (tRPC路由定义)
// - 同步相关：src/hooks/useDataSync.ts
