import { initTRPC } from '@trpc/server';
import { z } from 'zod';
import { executeQuery, batchStatements, executeScript, fetchAllFromTable, InStatement } from './database/index.js';
import { AuthService } from './services/AuthService.js';
import { SyncService } from './services/SyncService.js';
import { AnalyticsService } from './services/AnalyticsService.js';
import { UserManagementService } from './services/UserManagementService.js';
import { DatabaseInitializationService } from './services/DatabaseInitializationService.js';
import { PaymentService } from './services/PaymentService.js';
import { QuizService } from './services/QuizService.js';
import { auth } from './auth/better-auth-config.js';

// 导入统一的 Schema 定义
import {
  SqlQueryInputSchema,
  BatchStatementsInputSchema,
  SqlScriptInputSchema,
  TableQueryInputSchema,
  TableQueryWithLimitInputSchema,
  LoginInputSchema,
  RegisterInputSchema,
  VerifyTokenInputSchema,
  UpdateVipStatusInputSchema,
  DataSynchronizeInputSchema,
  FullSyncInputSchema,
  GetMoodAnalyticsInputSchema,
  GetEmotionUsageStatsInputSchema,
  GetUserActivityStatsInputSchema,
  UpdateUserPreferencesInputSchema,
  UnlockSkinInputSchema,
  GetUserProfileInputSchema,
  GetVipStatusInputSchema,
  GetUserConfigInputSchema,
  UpdateUserConfigInputSchema,
  GetUserUnlockedSkinsInputSchema,
  GetUserUnlockedEmojiSetsInputSchema,
  PurchaseVipInputSchema,
  PurchaseSkinInputSchema,
  PurchaseEmojiSetInputSchema,
  GetPurchaseHistoryInputSchema,
  ResetDatabaseInputSchema,
  // Quiz相关Schema
  GetQuizPacksInputSchema,
  SearchQuizPacksInputSchema,
  GetQuizPackDetailsInputSchema,
  CreateQuizSessionInputSchema,
  GetCurrentQuestionInputSchema,
  SubmitAnswerInputSchema,
  GetUserSessionsInputSchema,
  GetSessionAnswersInputSchema,
  SessionOperationInputSchema
} from '../../src/types/schema/api.js';

// 导入路由模块
import { configRouter } from './routers/config.js';
import { quizRouter } from './routers/quiz.js';
import { paymentRouter } from './routers/payment.js';
import { syncRouter } from './routers/sync.js';

// Create context interface with better-auth integration
interface Context {
  userId?: string;
  isAuthenticated: boolean;
  userRole?: string;
  session?: any;
  user?: any;
}

// Create context function for better-auth integration
export const createContext = async (req: Request): Promise<Context> => {
  try {
    // Get session from better-auth
    const session = await auth.api.getSession({
      headers: req.headers
    });

    return {
      session,
      user: session?.user,
      userId: session?.user?.id,
      isAuthenticated: !!session?.user,
      userRole: session?.user?.role || 'user'
    };
  } catch (error) {
    console.error('[tRPC] Context creation error:', error);
    return {
      isAuthenticated: false
    };
  }
};

// Create a tRPC instance with context
const t = initTRPC.context<Context>().create();

// Export the router and procedure helpers
export const router = t.router;
export const publicProcedure = t.procedure;

// Create authenticated procedure
export const authenticatedProcedure = t.procedure.use(async (opts) => {
  const { ctx } = opts;

  if (!ctx.isAuthenticated || !ctx.userId) {
    throw new Error('Authentication required');
  }

  return opts.next({
    ctx: {
      ...ctx,
      userId: ctx.userId,
    },
  });
});

// Create protected procedure (alias for authenticatedProcedure)
export const protectedProcedure = authenticatedProcedure;

/**
 * Helper function to handle errors in a consistent way
 * @param error The error that occurred
 * @param operation The operation that was being performed
 * @returns A standardized error response
 */
const handleError = (error: unknown, operation: string) => {
  console.error(`Error during ${operation}:`, error);

  // 获取错误消息
  const errorMessage = error instanceof Error
    ? error.message
    : `Unknown error during ${operation}`;

  // 返回标准化的错误响应
  return {
    success: false,
    data: null,
    error: errorMessage
  };
};

// 注意：Schema 定义已移至 src/types/schema/api.ts 统一管理

// Create the tRPC router with database procedures
export const appRouter = router({
  // Execute a single SQL query
  query: publicProcedure
    .input(SqlQueryInputSchema)
    .query(async ({ input }) => {
      try {
        const result = await executeQuery(input);
        return {
          success: true,
          data: result,
          error: null
        };
      } catch (error) {
        return handleError(error, 'query execution');
      }
    }),

  // Execute a batch of SQL statements
  batch: publicProcedure
    .input(BatchStatementsInputSchema)
    .mutation(async ({ input }) => {
      try {
        const result = await batchStatements(
          input.statements as InStatement[],
          input.mode
        );
        return {
          success: true,
          data: result,
          error: null
        };
      } catch (error) {
        return handleError(error, 'batch execution');
      }
    }),

  // Execute a SQL script
  executeScript: publicProcedure
    .input(SqlScriptInputSchema)
    .mutation(async ({ input }) => {
      try {
        await executeScript(input.script);
        return {
          success: true,
          error: null
        };
      } catch (error) {
        return handleError(error, 'script execution');
      }
    }),

  // Fetch all rows from a table
  fetchTable: publicProcedure
    .input(TableQueryInputSchema)
    .query(async ({ input }) => {
      try {
        const rows = await fetchAllFromTable(input.tableName);
        return {
          success: true,
          data: rows,
          error: null
        };
      } catch (error) {
        return handleError(error, 'table fetch');
      }
    }),

  // Fetch rows from a table with limit
  fetchTableWithLimit: publicProcedure
    .input(TableQueryWithLimitInputSchema)
    .query(async ({ input }) => {
      try {
        const rows = await fetchAllFromTable(input.tableName, input.limit);
        return {
          success: true,
          data: rows,
          error: null
        };
      } catch (error) {
        return handleError(error, 'table fetch with limit');
      }
    }),

  // Synchronize data
  synchronizeData: publicProcedure
    .input(DataSynchronizeInputSchema)
    .mutation(async ({ input }) => {
      try {
        const { moodEntriesToUpload, emotionSelectionsToUpload, lastSyncTimestamp, userId } = input;
        const currentSyncTimestamp = new Date().toISOString();
        let uploadedCount = 0;
        let downloadedCount = 0;

        // 1. 上传心情记录
        if (moodEntriesToUpload && moodEntriesToUpload.length > 0) {
          const statements: InStatement[] = [];

          for (const moodEntry of moodEntriesToUpload) {
            // 构建心情记录的SQL语句（基于实际数据库 schema）
            const moodEntrySql = `
              INSERT INTO mood_entries (
                id, user_id, timestamp, emotion_data_set_id,
                intensity, reflection, tags,
                emoji_set_id, emoji_set_version,
                skin_id, skin_config_snapshot,
                view_type_used, render_engine_used, display_mode_used,
                user_config_snapshot,
                created_at, updated_at, sync_status
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              ON CONFLICT(id) DO UPDATE SET
                user_id = excluded.user_id,
                timestamp = excluded.timestamp,
                emotion_data_set_id = excluded.emotion_data_set_id,
                intensity = excluded.intensity,
                reflection = excluded.reflection,
                tags = excluded.tags,
                emoji_set_id = excluded.emoji_set_id,
                emoji_set_version = excluded.emoji_set_version,
                skin_id = excluded.skin_id,
                skin_config_snapshot = excluded.skin_config_snapshot,
                view_type_used = excluded.view_type_used,
                render_engine_used = excluded.render_engine_used,
                display_mode_used = excluded.display_mode_used,
                user_config_snapshot = excluded.user_config_snapshot,
                updated_at = excluded.updated_at,
                sync_status = excluded.sync_status
            `;

            statements.push({
              sql: moodEntrySql,
              args: [
                moodEntry.id,
                moodEntry.user_id,
                moodEntry.timestamp,
                moodEntry.emotion_data_set_id || null,
                moodEntry.intensity,
                moodEntry.reflection || null,
                moodEntry.tags ? JSON.stringify(moodEntry.tags) : null,
                moodEntry.emoji_set_id || null,
                moodEntry.emoji_set_version || null,
                moodEntry.skin_id || null,
                moodEntry.skin_config_snapshot || null,
                moodEntry.view_type_used || null,
                moodEntry.render_engine_used || null,
                moodEntry.display_mode_used || null,
                moodEntry.user_config_snapshot || null,
                moodEntry.created_at,
                moodEntry.updated_at,
                'synced' // server 端默认为已同步
              ]
            });

            // 如果有情绪选择，则添加情绪选择的SQL语句
            const relatedSelections = emotionSelectionsToUpload?.filter(
              selection => selection.mood_entry_id === moodEntry.id
            );

            if (relatedSelections && relatedSelections.length > 0) {
              // 首先删除现有的情绪选择
              statements.push({
                sql: 'DELETE FROM emotion_selections WHERE mood_entry_id = ?',
                args: [moodEntry.id]
              });

              // 然后添加新的情绪选择
              for (const selection of relatedSelections) {
                const selectionId = selection.id || `${moodEntry.id}_${selection.emotion_id}_${selection.tier_level}`;

                statements.push({
                  sql: `
                    INSERT INTO emotion_selections (
                      id, mood_entry_id, emotion_id, tier_level, emotion_data_set_emotion_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                  `,
                  args: [
                    selectionId,
                    moodEntry.id,
                    selection.emotion_id,
                    selection.tier_level,
                    selection.emotion_data_set_emotion_id || null,
                    selection.created_at || new Date().toISOString()
                  ]
                });
              }
            }

            // 如果有标签，则添加标签的SQL语句
            if (moodEntry.tags && moodEntry.tags.length > 0) {
              // 首先删除现有的标签关联
              statements.push({
                sql: 'DELETE FROM mood_entry_tags WHERE mood_entry_id = ?',
                args: [moodEntry.id]
              });

              // 然后添加新的标签关联
              for (const tagId of moodEntry.tags) {
                statements.push({
                  sql: 'INSERT INTO mood_entry_tags (mood_entry_id, tag_id) VALUES (?, ?)',
                  args: [moodEntry.id, tagId]
                });
              }
            }
          }

          // 执行批处理
          if (statements.length > 0) {
            await batchStatements(statements);
            uploadedCount = moodEntriesToUpload.length;
          }
        }

        // 2. 下载新的心情记录（基于实际数据库 schema）
        let downloadQuery = `
          SELECT me.*
          FROM mood_entries me
          WHERE me.user_id = ?
        `;
        const downloadArgs: any[] = [userId];

        if (lastSyncTimestamp) {
          downloadQuery += ' AND me.updated_at > ?';
          downloadArgs.push(lastSyncTimestamp);
        }

        downloadQuery += ' ORDER BY me.timestamp DESC';

        const downloadResult = await executeQuery({ sql: downloadQuery, args: downloadArgs });
        const newMoodEntries = downloadResult.rows.map((row: any) => ({
          id: String(row.id),
          user_id: String(row.user_id),
          timestamp: String(row.timestamp),
          emotion_data_set_id: row.emotion_data_set_id ? String(row.emotion_data_set_id) : undefined,
          intensity: Number(row.intensity),
          reflection: row.reflection ? String(row.reflection) : undefined,
          tags: row.tags ? JSON.parse(row.tags) : [],

          // 表情集关联
          emoji_set_id: row.emoji_set_id ? String(row.emoji_set_id) : undefined,
          emoji_set_version: row.emoji_set_version ? String(row.emoji_set_version) : undefined,

          // 皮肤配置快照
          skin_id: row.skin_id ? String(row.skin_id) : undefined,
          skin_config_snapshot: row.skin_config_snapshot ? String(row.skin_config_snapshot) : undefined,

          // 显示配置快照
          view_type_used: row.view_type_used ? String(row.view_type_used) : undefined,
          render_engine_used: row.render_engine_used ? String(row.render_engine_used) : undefined,
          display_mode_used: row.display_mode_used ? String(row.display_mode_used) : undefined,

          // 用户配置快照
          user_config_snapshot: row.user_config_snapshot ? String(row.user_config_snapshot) : undefined,

          created_at: String(row.created_at),
          updated_at: String(row.updated_at),
          sync_status: String(row.sync_status || 'synced')
        }));

        // 3. 获取情绪选择
        const newEmotionSelections: any[] = [];
        if (newMoodEntries.length > 0) {
          const moodEntryIds = newMoodEntries.map((entry: any) => entry.id);
          const placeholders = moodEntryIds.map(() => '?').join(',');

          const selectionsQuery = `
            SELECT es.*, e.name, e.color, e.emoji
            FROM emotion_selections es
            JOIN emotions e ON es.emotion_id = e.id
            WHERE es.mood_entry_id IN (${placeholders})
            ORDER BY es.tier_level
          `;

          const selectionsResult = await executeQuery({ sql: selectionsQuery, args: moodEntryIds });

          selectionsResult.rows.forEach((row: any) => {
            newEmotionSelections.push({
              id: String(row.id),
              mood_entry_id: String(row.mood_entry_id),
              emotion_id: String(row.emotion_id),
              tier_level: Number(row.tier_level),
              emotion_data_set_emotion_id: row.emotion_data_set_emotion_id ? String(row.emotion_data_set_emotion_id) : undefined,
              created_at: String(row.created_at)
            });
          });
        }

        downloadedCount = newMoodEntries.length;

        // 4. 返回同步结果
        return {
          success: true,
          newMoodEntriesFromServer: newMoodEntries,
          newEmotionSelectionsFromServer: newEmotionSelections,
          serverTimestamp: currentSyncTimestamp,
          uploadedCount,
          downloadedCount,
          error: null
        };
      } catch (error) {
        return handleError(error, 'data synchronization');
      }
    }),

  // ==================== 认证服务 ====================

  // 用户登录
  login: publicProcedure
    .input(LoginInputSchema)
    .mutation(async ({ input }) => {
      const authService = AuthService.getInstance();
      return await authService.login(input);
    }),

  // 用户注册
  register: publicProcedure
    .input(RegisterInputSchema)
    .mutation(async ({ input }) => {
      const authService = AuthService.getInstance();
      return await authService.register(input);
    }),

  // 验证令牌
  verifyToken: publicProcedure
    .input(VerifyTokenInputSchema)
    .query(async ({ input }) => {
      const authService = AuthService.getInstance();
      return await authService.verifyToken(input.token);
    }),

  // 更新VIP状态
  updateVipStatus: authenticatedProcedure
    .input(UpdateVipStatusInputSchema)
    .mutation(async ({ input }) => {
      const authService = AuthService.getInstance();
      if (input.user_id) {
        return await authService.updateVipStatus(input.user_id as string, input.isVip as boolean, input.expiresAt as string);
      }
      return {
        success: false,
        error: 'User ID is required'
      };
    }),

  // ==================== 同步服务 ====================

  // 完整数据同步
  performFullSync: authenticatedProcedure
    .input(FullSyncInputSchema)
    .mutation(async ({ input }) => {
      const syncService = SyncService.getInstance();
      return await syncService.performFullSync(input);
    }),

  // ==================== 分析服务 ====================

  // 获取心情分析
  getMoodAnalytics: authenticatedProcedure
    .input(GetMoodAnalyticsInputSchema)
    .query(async ({ input }) => {
      const analyticsService = AnalyticsService.getInstance();
      return await analyticsService.getMoodAnalytics(input);
    }),

  // 获取情绪使用统计
  getEmotionUsageStats: authenticatedProcedure
    .input(GetEmotionUsageStatsInputSchema)
    .query(async ({ input }) => {
      const analyticsService = AnalyticsService.getInstance();
      return await analyticsService.getEmotionUsageStats(input);
    }),

  // 获取用户活动统计
  getUserActivityStats: authenticatedProcedure
    .input(GetUserActivityStatsInputSchema)
    .query(async ({ input }) => {
      const analyticsService = AnalyticsService.getInstance();
      return await analyticsService.getUserActivityStats(input.user_id, input);
    }),

  // ==================== 用户管理服务 ====================

  // 获取用户配置文件
  getUserProfile: authenticatedProcedure
    .input(GetUserProfileInputSchema)
    .query(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.getUserProfile(input.user_id);
    }),

  // 更新用户偏好设置
  updateUserPreferences: authenticatedProcedure
    .input(UpdateUserPreferencesInputSchema)
    .mutation(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.updateUserPreferences(input.user_id, input.preferences);
    }),

  // 获取VIP状态
  getVipStatus: authenticatedProcedure
    .input(GetVipStatusInputSchema)
    .query(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.getVipStatus(input.user_id as string);
    }),

  // 获取用户配置
  getUserConfig: authenticatedProcedure
    .input(GetUserConfigInputSchema)
    .query(async ({ input }) => {
      try {
        const result = await executeQuery({
          sql: 'SELECT * FROM user_configs WHERE user_id = ?',
          args: [input.user_id]
        });

        if (result.rows && result.rows.length > 0) {
          return {
            success: true,
            data: result.rows[0]
          };
        } else {
          return {
            success: false,
            error: 'User config not found'
          };
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get user config'
        };
      }
    }),

  // 更新用户配置
  updateUserConfig: authenticatedProcedure
    .input(UpdateUserConfigInputSchema)
    .mutation(async ({ input }) => {
      try {
        const { userId, config } = input;
        const updateFields: string[] = [];
        const updateValues: any[] = [];

        // 动态构建更新字段
        Object.entries(config as Record<string, any>).forEach(([key, value]) => {
          if (value !== undefined) {
            updateFields.push(`${key} = ?`);
            updateValues.push(value);
          }
        });

        if (updateFields.length === 0) {
          return {
            success: false,
            error: 'No fields to update'
          };
        }

        updateValues.push(new Date().toISOString()); // updated_at
        updateValues.push(userId);

        const sql = `
          UPDATE user_configs
          SET ${updateFields.join(', ')}, updated_at = ?
          WHERE user_id = ?
        `;

        await executeQuery({ sql, args: updateValues });

        return {
          success: true,
          message: 'User config updated successfully'
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update user config'
        };
      }
    }),

  // 解锁皮肤
  unlockSkin: authenticatedProcedure
    .input(UnlockSkinInputSchema)
    .mutation(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.unlockSkin(input.user_id, input.skinId, input.unlockMethod, input.transactionId);
    }),

  // 获取用户解锁的皮肤
  getUserUnlockedSkins: authenticatedProcedure
    .input(GetUserUnlockedSkinsInputSchema)
    .query(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.getUserUnlockedSkins(input.user_id as string);
    }),

  // ==================== 支付服务 ====================

  // 获取VIP计划
  getVipPlans: publicProcedure
    .query(async () => {
      const paymentService = PaymentService.getInstance();
      return await paymentService.getVipPlans();
    }),

  // 购买VIP
  purchaseVip: authenticatedProcedure
    .input(PurchaseVipInputSchema)
    .mutation(async ({ input, ctx }) => {
      const paymentService = PaymentService.getInstance();
      return await paymentService.processVipPurchase(
        ctx.user_id!,
        input.planId as string,
        input.paymentMethodId as string
      );
    }),

  // 购买皮肤
  purchaseSkin: authenticatedProcedure
    .input(PurchaseSkinInputSchema)
    .mutation(async ({ input, ctx }) => {
      const paymentService = PaymentService.getInstance();
      return await paymentService.processSkinPurchase(
        ctx.user_id!,
        input.skinId as string,
        input.paymentMethodId as string
      );
    }),

  // 购买表情集
  purchaseEmojiSet: authenticatedProcedure
    .input(PurchaseEmojiSetInputSchema)
    .mutation(async ({ input, ctx }) => {
      const paymentService = PaymentService.getInstance();
      return await paymentService.processEmojiSetPurchase(
        ctx.user_id!,
        input.emojiSetId as string,
        input.paymentMethodId as string
      );
    }),

  // 获取用户购买历史
  getPurchaseHistory: authenticatedProcedure
    .input(GetPurchaseHistoryInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const result = await executeQuery({
          sql: `
            SELECT pt.*, s.name as skin_name
            FROM payment_transactions pt
            LEFT JOIN skins s ON pt.description LIKE '%' || s.id || '%'
            WHERE pt.user_id = ?
            ORDER BY pt.created_at DESC
            LIMIT ? OFFSET ?
          `,
          args: [ctx.user_id!, input.limit || 50, input.offset || 0]
        });

        return {
          success: true,
          data: result.rows
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get purchase history'
        };
      }
    }),

  // 获取用户解锁的表情集
  getUserUnlockedEmojiSets: authenticatedProcedure
    .input(GetUserUnlockedEmojiSetsInputSchema)
    .query(async ({ input }) => {
      try {
        const result = await executeQuery({
          sql: `
            SELECT es.*, uesu.unlocked_at, uesu.unlock_method
            FROM emoji_sets es
            JOIN user_emoji_set_unlocks uesu ON es.id = uesu.emoji_set_id
            WHERE uesu.user_id = ?
            ORDER BY uesu.unlocked_at DESC
          `,
          args: [input.user_id]
        });

        return {
          success: true,
          data: result.rows
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get unlocked emoji sets'
        };
      }
    }),

  // 获取可购买的商品列表
  getShopItems: publicProcedure
    .query(async () => {
      try {
        // 获取付费皮肤
        const skinsResult = await executeQuery({
          sql: `
            SELECT id, name, description, is_premium, unlock_conditions,
                   preview_image_light, preview_image_dark, created_at
            FROM skins
            WHERE is_premium = 1
            ORDER BY created_at DESC
          `,
          args: []
        });

        // 获取付费表情集
        const emojiSetsResult = await executeQuery({
          sql: `
            SELECT id, name, description, price, preview_image, created_at
            FROM emoji_sets
            WHERE price > 0
            ORDER BY created_at DESC
          `,
          args: []
        });

        return {
          success: true,
          data: {
            skins: skinsResult.rows || [],
            emojiSets: emojiSetsResult.rows || []
          }
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get shop items'
        };
      }
    }),

  // ==================== Better-Auth 集成 ====================

  // 获取当前用户信息（包括VIP状态）
  me: publicProcedure
    .query(async ({ ctx }) => {
      if (!ctx.user) {
        return { user: null, session: null };
      }

      try {
        // 从数据库获取完整用户信息
        const userService = UserManagementService.getInstance();
        const profile = await userService.getUserProfile(ctx.user.id);

        return {
          user: ctx.user,
          profile: profile.success ? profile.data : null,
          session: ctx.session
        };
      } catch (error) {
        console.error('[tRPC] Get user profile error:', error);
        return {
          user: ctx.user,
          profile: null,
          session: ctx.session
        };
      }
    }),

  // ==================== 数据库管理 ====================

  // 初始化数据库
  initializeDatabase: publicProcedure
    .mutation(async () => {
      const dbInitService = DatabaseInitializationService.getInstance();
      return await dbInitService.initializeDatabase();
    }),

  // 重置数据库（仅用于开发/测试）
  resetDatabase: publicProcedure
    .input(ResetDatabaseInputSchema)
    .mutation(async ({ input }) => {
      if (input.confirm !== 'RESET_DATABASE_CONFIRM') {
        return {
          success: false,
          error: 'Invalid confirmation'
        };
      }

      const dbInitService = DatabaseInitializationService.getInstance();
      return await dbInitService.resetDatabase();
    }),

  // ==================== Quiz系统路由 ====================

  // Quiz相关功能
  quiz: router({
    // 获取Quiz包列表
    packs: router({
      list: publicProcedure
        .input(z.object({
          category: z.enum(['daily', 'therapy', 'assessment', 'research']).optional(),
          difficulty: z.enum(['beginner', 'regular', 'advanced', 'expert']).optional(),
          limit: z.number().min(1).max(100).default(20),
          offset: z.number().min(0).default(0),
        }))
        .query(async ({ input }) => {
          try {
            // 简化的Quiz包查询逻辑
            const mockPacks = [
              {
                id: 'daily-mood-tracker',
                name: '日常情绪追踪',
                description: '适合日常使用的情绪状态评估工具',
                version: '1.0',
                emotion_data_set_id: 'basic-emotions',
                metadata: {
                  category: 'daily',
                  difficulty_level: 'regular',
                  estimated_duration_minutes: 5,
                  completion_count: 1250,
                  average_rating: 4.6,
                  is_featured: true
                },
                is_active: true,
                is_default: true,
                sort_order: 1,
                created_at: new Date(),
                updated_at: new Date()
              },
              {
                id: 'therapy-assessment',
                name: '治疗评估量表',
                description: '专业的心理治疗情绪评估工具',
                version: '1.0',
                emotion_data_set_id: 'therapy-emotions',
                metadata: {
                  category: 'therapy',
                  difficulty_level: 'advanced',
                  estimated_duration_minutes: 15,
                  completion_count: 320,
                  average_rating: 4.8,
                  is_featured: false
                },
                is_active: true,
                is_default: false,
                sort_order: 2,
                created_at: new Date(),
                updated_at: new Date()
              }
            ];

            const filteredPacks = mockPacks.filter(pack => {
              if (input.category && pack.metadata.category !== input.category) return false;
              if (input.difficulty && pack.metadata.difficulty_level !== input.difficulty) return false;
              return true;
            });

            return {
              success: true,
              data: {
                quiz_packs: filteredPacks.slice(input.offset, input.offset + input.limit),
                total: filteredPacks.length,
                has_more: filteredPacks.length > input.offset + input.limit
              },
              error: null
            };
          } catch (error) {
            return handleError(error, 'quiz packs list');
          }
        }),

      getById: publicProcedure
        .input(z.object({ pack_id: z.string() }))
        .query(async ({ input }) => {
          try {
            // 模拟获取特定Quiz包
            const mockPack = {
              id: input.pack_id,
              name: '日常情绪追踪',
              description: '适合日常使用的情绪状态评估工具',
              version: '1.0',
              emotion_data_set_id: 'basic-emotions',
              quiz_logic_config: {
                tier_question_configs: {
                  1: { question_type: 'emotion_select', max_selections: 1 },
                  2: { question_type: 'emotion_select', max_selections: 1 }
                }
              },
              metadata: {
                category: 'daily',
                difficulty_level: 'regular',
                estimated_duration_minutes: 5
              },
              is_active: true,
              created_at: new Date(),
              updated_at: new Date()
            };

            return {
              success: true,
              data: mockPack,
              error: null
            };
          } catch (error) {
            return handleError(error, 'quiz pack get by id');
          }
        }),

      getRecommendations: publicProcedure
        .input(z.object({
          limit: z.number().min(1).max(20).default(10),
          based_on: z.enum(['history', 'preferences', 'ai_analysis']).default('preferences')
        }))
        .query(async ({ input }) => {
          try {
            // 模拟推荐逻辑
            const recommendations = [
              {
                quiz_pack: {
                  id: 'daily-mood-tracker',
                  name: '日常情绪追踪',
                  description: '适合日常使用的情绪状态评估工具',
                  version: '1.0',
                  emotion_data_set_id: 'basic-emotions',
                  metadata: {
                    category: 'daily',
                    difficulty_level: 'regular',
                    estimated_duration_minutes: 5
                  },
                  is_active: true,
                  created_at: new Date(),
                  updated_at: new Date()
                },
                recommendation_score: 0.9,
                recommendation_reason: '基于您的使用偏好推荐'
              }
            ];

            return {
              success: true,
              data: { recommendations },
              error: null
            };
          } catch (error) {
            return handleError(error, 'quiz recommendations');
          }
        })
    }),

    // Quiz会话管理
    sessions: router({
      create: publicProcedure
        .input(z.object({
          pack_id: z.string(),
          personalization_overrides: z.record(z.any()).optional()
        }))
        .mutation(async ({ input }) => {
          try {
            const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            return {
              success: true,
              data: {
                session_id: sessionId,
                pack_id: input.pack_id,
                user_id: 'mock_user',
                status: 'INITIATED' as const,
                total_tiers: 2,
                created_at: new Date()
              },
              error: null
            };
          } catch (error) {
            return handleError(error, 'quiz session create');
          }
        }),

      getCurrentQuestion: publicProcedure
        .input(z.object({ session_id: z.string() }))
        .query(async ({ input }) => {
          try {
            // 模拟问题数据
            const mockQuestion = {
              session_id: input.session_id,
              question_id: 'tier_1',
              question_text_localized: '请选择您当前的情绪状态',
              primary_interaction_type: 'EMOTION_WHEEL_SELECT',
              personalized_ui_config: {
                background_config: {
                  background_id: 'default_bg',
                  theme: 'light'
                },
                theme_config: {
                  color_palette: {
                    primary: '#4F46E5',
                    secondary: '#7C3AED',
                    accent: '#F59E0B'
                  },
                  font_config: {
                    primary_font: 'Inter',
                    size_scale: 1.0
                  }
                },
                wheel_config: {
                  container_size: 400,
                  wheel_radius: 180,
                  sector_gap: 2,
                  emotion_display_mode: 'hierarchical'
                },
                view_type: 'wheel' as const
              },
              emotion_options: [
                {
                  emotion_id: 'joy',
                  emotion_name: '喜悦',
                  tier_level: 1,
                  display_config: {
                    component_type: 'emotion_sector',
                    visual_style: {},
                    content: {
                      text_localized: '喜悦',
                      emoji: '😊',
                      color: '#FEF3C7',
                      intensity_indicator: 0.8
                    }
                  }
                },
                {
                  emotion_id: 'sadness',
                  emotion_name: '悲伤',
                  tier_level: 1,
                  display_config: {
                    component_type: 'emotion_sector',
                    visual_style: {},
                    content: {
                      text_localized: '悲伤',
                      emoji: '😢',
                      color: '#DBEAFE',
                      intensity_indicator: 0.6
                    }
                  }
                }
              ],
              progress_info: {
                current_tier: 1,
                total_tiers: 2,
                completion_percentage: 50,
                estimated_remaining_time: '2分钟'
              },
              navigation_config: {
                show_back_button: false,
                show_skip_button: true,
                show_progress_indicator: true
              }
            };

            return {
              success: true,
              data: mockQuestion,
              error: null
            };
          } catch (error) {
            return handleError(error, 'get current question');
          }
        }),

      submitEmotionAnswer: publicProcedure
        .input(z.object({
          session_id: z.string(),
          tier_id: z.string(),
          emotion_id: z.string(),
          confidence: z.number().min(0).max(1).optional(),
          response_time_ms: z.number().min(0).optional(),
          interaction_method: z.string().optional()
        }))
        .mutation(async ({ input }) => {
          try {
            return {
              success: true,
              data: {
                session_id: input.session_id,
                question_id: input.tier_id,
                is_valid: true,
                next_action_hint: 'QUIZ_COMPLETED',
                realtime_insights: [
                  {
                    type: 'emotion_selection',
                    message: '您的情绪选择已记录',
                    confidence: 0.9
                  }
                ],
                progress_update: {
                  current_tier: 2,
                  total_tiers: 2,
                  completion_percentage: 100
                }
              },
              error: null
            };
          } catch (error) {
            return handleError(error, 'submit emotion answer');
          }
        }),

      complete: publicProcedure
        .input(z.object({ session_id: z.string() }))
        .query(async ({ input }) => {
          try {
            const mockResult = {
              result_id: `result_${Date.now()}`,
              session_id: input.session_id,
              user_id: 'mock_user',
              pack_id: 'daily-mood-tracker',
              completion_percentage: 100,
              emotion_analysis: {
                dominant_emotions: ['joy', 'contentment', 'optimism'],
                emotional_stability_index: 0.75,
                emotional_complexity_index: 0.6,
                pattern_analysis: {
                  emotional_categories: [
                    { name: '积极情绪', value: 65, color: '#10B981' },
                    { name: '中性情绪', value: 25, color: '#6B7280' },
                    { name: '消极情绪', value: 10, color: '#EF4444' }
                  ],
                  intensity_distribution: [
                    { level: '低强度', count: 2 },
                    { level: '中强度', count: 5 },
                    { level: '高强度', count: 3 }
                  ]
                }
              },
              personalized_recommendations: [
                {
                  type: 'mindfulness',
                  title: '正念冥想练习',
                  description: '基于您的情绪模式，建议每日进行10-15分钟的正念冥想练习。',
                  confidence: 0.85,
                  priority: 1
                }
              ],
              visual_summary: {
                primary_visualization: {},
                secondary_visualizations: [],
                export_options: ['PDF', 'PNG', 'JSON']
              },
              created_at: new Date()
            };

            return {
              success: true,
              data: mockResult,
              error: null
            };
          } catch (error) {
            return handleError(error, 'complete quiz session');
          }
        })
    })
  }),

  // ==================== 模块化路由系统 ====================

  // 配置系统路由 (全局应用设置 + Quiz系统配置)
  config: configRouter,

  // Quiz 系统路由 (重构后的模块化实现)
  quiz: quizRouter,

  // 支付系统路由 (Stripe 集成)
  payment: paymentRouter,

  // 数据同步路由 (扩展功能)
  sync: syncRouter,

  // ==================== Quiz 系统服务 ====================

  // 获取可用的Quiz包列表
  getQuizPacks: publicProcedure
    .input(GetQuizPacksInputSchema)
    .query(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.getAvailableQuizPacks(input.userId || 'anonymous', input.userType);
    }),

  // 获取推荐的Quiz包
  getRecommendedQuizPacks: authenticatedProcedure
    .query(async ({ ctx }) => {
      const quizService = QuizService.getInstance();
      return await quizService.getRecommendedQuizPacks(ctx.userId!);
    }),

  // 根据类型获取Quiz包
  getQuizPacksByType: publicProcedure
    .input(z.object({ quizType: z.string() }))
    .query(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.getQuizPacksByType(input.quizType);
    }),

  // 搜索Quiz包
  searchQuizPacks: publicProcedure
    .input(SearchQuizPacksInputSchema)
    .query(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.searchQuizPacks(input.searchTerm, input.filters);
    }),

  // 获取Quiz包详情
  getQuizPackDetails: publicProcedure
    .input(GetQuizPackDetailsInputSchema)
    .query(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.getQuizPackDetails(input.packId);
    }),

  // 创建Quiz会话
  createQuizSession: authenticatedProcedure
    .input(CreateQuizSessionInputSchema)
    .mutation(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.createQuizSession(input.packId, input.userId);
    }),

  // 获取当前问题数据
  getCurrentQuestion: authenticatedProcedure
    .input(GetCurrentQuestionInputSchema)
    .query(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.getCurrentQuestionData(input.sessionId);
    }),

  // 提交答案
  submitAnswer: authenticatedProcedure
    .input(SubmitAnswerInputSchema)
    .mutation(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.submitAnswer(input);
    }),

  // 获取用户的Quiz会话
  getUserSessions: authenticatedProcedure
    .input(GetUserSessionsInputSchema)
    .query(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.getUserSessions(input.userId, input.limit);
    }),

  // 获取会话答案
  getSessionAnswers: authenticatedProcedure
    .input(GetSessionAnswersInputSchema)
    .query(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.getSessionAnswers(input.sessionId);
    }),

  // 暂停会话
  pauseSession: authenticatedProcedure
    .input(SessionOperationInputSchema)
    .mutation(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.pauseSession(input.sessionId);
    }),

  // 恢复会话
  resumeSession: authenticatedProcedure
    .input(SessionOperationInputSchema)
    .mutation(async ({ input }) => {
      const quizService = QuizService.getInstance();
      return await quizService.resumeSession(input.sessionId);
    })
  })
});

// Export type definition of API
export type AppRouter = typeof appRouter;
