# 类型文件整理和迁移计划

本文档说明了从分散的类型文件向统一 Schema 架构迁移的计划和状态。

## 📋 迁移状态总览

### ✅ 已完成迁移的类型

以下类型已经成功迁移到统一的 Schema 架构中：

#### 基础实体类型 (base.ts)
- ✅ `User` - 用户实体
- ✅ `Emotion` - 情绪实体  
- ✅ `EmotionDataSet` - 情绪数据集
- ✅ `EmotionDataSetTier` - 情绪数据集层级
- ✅ `EmotionDataSetEmotion` - 情绪数据集关联
- ✅ `EmojiSet` - 表情集
- ✅ `EmojiItem` - 表情项
- ✅ `Skin` - 皮肤主题
- ✅ `Tag` - 标签
- ✅ `MoodEntry` - 心情记录
- ✅ `EmotionSelection` - 情绪选择
- ✅ `MoodEntryTag` - 心情记录标签关联
- ✅ `UserConfig` - 用户配置
- ✅ `UILabel` - UI标签

#### 翻译相关类型 (base.ts + translation.ts)
- ✅ `EmotionTranslation` - 情绪翻译
- ✅ `EmotionDataSetTranslation` - 情绪数据集翻译
- ✅ `EmojiSetTranslation` - 表情集翻译
- ✅ `SkinTranslation` - 皮肤翻译
- ✅ `TagTranslation` - 标签翻译
- ✅ `UILabelTranslation` - UI标签翻译

#### 视图和布局类型 (base.ts)
- ✅ `ViewType` - 视图类型枚举
- ✅ `ContentDisplayMode` - 内容显示模式
- ✅ `RenderEngine` - 渲染引擎
- ✅ `CardLayout`, `BubbleLayout`, `GalaxyLayout` 等布局类型
- ✅ `EmojiSetType`, `AnimationType` - 表情相关类型
- ✅ `UnlockConditions` - 解锁条件

#### API 类型 (api.ts)
- ✅ Repository 数据类型 (Create/Update/Filter)
- ✅ Service 输入类型 (CreateInput/UpdateInput)
- ✅ 配置类型 (PreviewConfig, LayoutConfig, SkinConfig)
- ✅ 心情记录输入类型

### 📁 需要整理的原有类型文件

以下文件中的类型已经迁移到统一架构，可以考虑重构或移除：

#### 1. `src/types/emojiTypes.ts`
**状态**: 🔄 可以重构
**内容分析**:
- `EmojiSetType`, `AnimationType` → 已迁移到 `base.ts`
- `EmojiConfig`, `EmojiSetConfig` → 可以整合到 `SkinConfig`
- `EmojiItemData` → 已有 `EmojiItem` 类型

**建议操作**:
```typescript
// 保留文件，但重新导出统一类型
export {
  type EmojiSetType,
  type AnimationType,
  type EmojiSet,
  type EmojiItem
} from './schema';

// 添加特定的配置类型（如果需要）
export interface EmojiConfig {
  // 表情特定配置
}
```

#### 2. `src/types/emotionDataTypes.ts`
**状态**: 🔄 可以重构
**内容分析**:
- `EmotionDataSet`, `EmotionDataSetTier` → 已迁移到 `base.ts`
- `EmotionDataSetConfig` → 可以整合到配置类型

**建议操作**:
```typescript
// 重新导出统一类型
export {
  type EmotionDataSet,
  type EmotionDataSetTier,
  type EmotionDataSetEmotion
} from './schema';
```

#### 3. `src/types/layoutTypes.ts`
**状态**: ✅ 可以移除
**内容分析**:
- 所有布局类型已迁移到 `base.ts`

**建议操作**:
```typescript
// 完全重新导出
export {
  type ViewType,
  type ContentDisplayMode,
  type RenderEngine,
  type CardLayout,
  type BubbleLayout,
  type GalaxyLayout,
  type ListLayout,
  type GridLayout,
  type TreeLayout,
  type FlowLayout,
  type TagCloudLayout
} from './schema';
```

#### 4. `src/types/mood.ts`
**状态**: 🔄 可以重构
**内容分析**:
- `MoodEntry`, `EmotionSelection` → 已迁移到 `base.ts`
- 可能包含特定的业务逻辑类型

**建议操作**:
```typescript
// 重新导出基础类型
export {
  type MoodEntry,
  type EmotionSelection,
  type CreateMoodEntryInput,
  type UpdateMoodEntryInput
} from './schema';

// 保留业务逻辑相关的类型
export interface MoodAnalysis {
  // 心情分析相关类型
}
```

#### 5. `src/types/previewTypes.ts`
**状态**: 🔄 可以重构
**内容分析**:
- 预览相关类型已添加到 `api.ts`

**建议操作**:
```typescript
export {
  type PreviewConfig,
  type LayoutConfig
} from './schema';
```

#### 6. `src/types/skinTypes.ts`
**状态**: 🔄 可以重构
**内容分析**:
- `Skin` → 已迁移到 `base.ts`
- `SkinConfig` → 已添加到 `api.ts`

**建议操作**:
```typescript
export {
  type Skin,
  type SkinConfig,
  type UnlockConditions
} from './schema';
```

#### 7. `src/types/tagTypes.ts`
**状态**: ✅ 可以移除
**内容分析**:
- `Tag` → 已迁移到 `base.ts`

**建议操作**:
```typescript
export { type Tag } from './schema';
```

#### 8. `src/types/translationTypes.ts`
**状态**: ✅ 可以移除
**内容分析**:
- 所有翻译类型已迁移到 `translation.ts`

**建议操作**:
```typescript
export {
  type Translation,
  type TranslatableEntity,
  type TranslationInput,
  // ... 其他翻译类型
} from './schema';
```

#### 9. `src/types/uiLabelTypes.ts`
**状态**: ✅ 可以移除
**内容分析**:
- `UILabel`, `UILabelTranslation` → 已迁移到 `base.ts`

**建议操作**:
```typescript
export {
  type UILabel,
  type UILabelTranslation
} from './schema';
```

#### 10. `src/types/userConfigTypes.ts`
**状态**: 🔄 可以重构
**内容分析**:
- `UserConfig` → 已迁移到 `base.ts`
- 可能包含特定的配置逻辑

**建议操作**:
```typescript
export {
  type UserConfig,
  type CreateUserConfigInput,
  type UpdateUserConfigInput
} from './schema';
```

#### 11. `src/types/unified.ts`
**状态**: ❓ 需要评估
**内容分析**:
- 可能包含统一的类型定义
- 需要检查是否与新架构重复

**建议操作**:
- 检查内容，移除重复定义
- 保留有用的统一类型

### 🔄 迁移执行计划

#### 阶段 1: 重构类型文件 (1-2天)
1. 更新所有 `src/types/*.ts` 文件，使其重新导出统一类型
2. 移除重复的类型定义
3. 保留特定的业务逻辑类型

#### 阶段 2: 更新导入引用 (2-3天)
1. 扫描整个代码库，找到使用旧类型文件的地方
2. 更新导入语句，使用统一的 `src/types/schema` 导入
3. 测试所有更改，确保类型兼容性

#### 阶段 3: 清理和优化 (1天)
1. 移除不再需要的文件
2. 更新文档和注释
3. 运行完整的类型检查和测试

### 📝 迁移脚本示例

```bash
#!/bin/bash
# 类型迁移脚本

echo "开始类型文件迁移..."

# 1. 备份原有文件
mkdir -p backup/types
cp -r src/types/*.ts backup/types/

# 2. 更新类型文件
for file in src/types/*.ts; do
  if [[ $file != *"schema"* ]]; then
    echo "更新文件: $file"
    # 这里可以添加自动化的重构逻辑
  fi
done

# 3. 更新导入引用
find src -name "*.ts" -o -name "*.tsx" | xargs grep -l "from.*types/" | while read file; do
  echo "更新导入: $file"
  # 这里可以添加自动化的导入更新逻辑
done

echo "迁移完成！"
```

### 🎯 预期收益

#### 1. 类型一致性
- 所有类型定义来自单一数据源
- 减少类型不匹配的错误
- 更好的 TypeScript 智能提示

#### 2. 维护性提升
- 集中管理类型定义
- 减少重复代码
- 更容易添加新的类型

#### 3. 开发体验改善
- 统一的导入方式
- 更清晰的类型层次结构
- 更好的文档和注释

#### 4. 代码质量提升
- 运行时类型验证
- 更严格的类型检查
- 减少运行时错误

### ⚠️ 注意事项

1. **向后兼容性**: 确保迁移过程中不破坏现有功能
2. **测试覆盖**: 在每个阶段都要进行充分的测试
3. **团队协调**: 确保团队成员了解新的类型架构
4. **文档更新**: 及时更新相关文档和使用指南

---

通过这个迁移计划，我们将建立一个更加统一、可维护和类型安全的代码架构。
