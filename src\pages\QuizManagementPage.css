/**
 * Quiz管理页面样式
 */

.quiz-management-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 页面头部 */
.quiz-management-header {
  margin-bottom: 30px;
  text-align: center;
}

.quiz-management-header h1 {
  color: #2E7D32;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.quiz-management-header p {
  color: #666;
  font-size: 1.1rem;
}

/* 面包屑导航 */
.quiz-management-breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 15px;
  background: #f5f5f5;
  border-radius: 8px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #666;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.breadcrumb-item.clickable {
  color: #2196F3;
  cursor: pointer;
  text-decoration: underline;
}

.breadcrumb-item.clickable:hover {
  color: #1976D2;
}

.breadcrumb-separator {
  margin: 0 10px;
  color: #999;
  font-weight: bold;
}

/* 标签导航 */
.quiz-management-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 30px;
  background: #f5f5f5;
  padding: 5px;
  border-radius: 12px;
  overflow-x: auto;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #666;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
}

.tab-button:hover:not(:disabled) {
  background: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.tab-button.active {
  background: linear-gradient(135deg, #2196F3, #42A5F5);
  color: white;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.tab-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tab-icon {
  font-size: 16px;
}

.tab-badge {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.tab-button:not(.active) .tab-badge {
  background: #2196F3;
  color: white;
}

/* 主要内容区域 */
.quiz-management-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 管理器通用样式 */
.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.manager-header h2 {
  color: #2E7D32;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

/* 卡片列表样式 */
.quiz-pack-list,
.quiz-question-list,
.quiz-option-list {
  padding: 20px;
  display: grid;
  gap: 15px;
}

.quiz-pack-card,
.quiz-question-card,
.quiz-option-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quiz-pack-card:hover,
.quiz-question-card:hover,
.quiz-option-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #2196F3;
}

/* 包卡片样式 */
.pack-header,
.question-header,
.option-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.pack-name,
.question-text {
  color: #2E7D32;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  cursor: pointer;
  text-decoration: none;
}

.pack-name:hover,
.question-text:hover {
  color: #1976D2;
  text-decoration: underline;
}

.pack-badges,
.question-badges,
.option-badges {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.badge.active {
  background: #4CAF50;
  color: white;
}

.badge.public {
  background: #2196F3;
  color: white;
}

.badge.type {
  background: #FF9800;
  color: white;
}

.badge.required {
  background: #F44336;
  color: white;
}

.badge.tier {
  background: #9C27B0;
  color: white;
}

.badge.correct {
  background: #4CAF50;
  color: white;
}

.badge.score {
  background: #607D8B;
  color: white;
}

.pack-description {
  color: #666;
  margin: 10px 0;
  line-height: 1.5;
}

.pack-meta {
  display: flex;
  gap: 15px;
  color: #888;
  font-size: 14px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

/* 操作按钮 */
.pack-actions,
.question-actions,
.option-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.action-button.primary {
  background: linear-gradient(135deg, #2196F3, #42A5F5);
  color: white;
}

.action-button.secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.action-button.danger {
  background: linear-gradient(135deg, #F44336, #EF5350);
  color: white;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 顺序控制按钮 */
.order-controls {
  display: flex;
  gap: 2px;
}

.order-button {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.3s ease;
}

.order-button:hover:not(:disabled) {
  background: #2196F3;
  color: white;
  border-color: #2196F3;
}

.order-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* 问题特殊样式 */
.question-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.question-order {
  background: #2196F3;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

.question-group {
  color: #888;
  font-size: 14px;
  margin: 5px 0;
}

/* 选项特殊样式 */
.option-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.option-order {
  background: #4CAF50;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  min-width: 30px;
  text-align: center;
}

.option-emoji {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  font-weight: bold;
}

.option-text-info h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.option-value {
  color: #888;
  font-size: 12px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* 错误状态 */
.quiz-management-error {
  text-align: center;
  padding: 60px 20px;
  background: #ffebee;
  border-radius: 12px;
  margin: 20px;
}

.quiz-management-error h2 {
  color: #c62828;
  margin-bottom: 15px;
}

.quiz-management-error p {
  color: #666;
  margin-bottom: 20px;
}

.retry-button,
.back-button {
  margin: 0 10px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
}

.retry-button {
  background: #4CAF50;
  color: white;
}

.back-button {
  background: #f5f5f5;
  color: #666;
}

/* 表单样式 */
.quiz-pack-form-overlay,
.quiz-question-form-overlay,
.quiz-option-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.quiz-pack-form,
.quiz-question-form,
.quiz-option-form {
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.quiz-pack-form h3,
.quiz-question-form h3,
.quiz-option-form h3 {
  margin: 0 0 20px 0;
  color: #2E7D32;
  font-size: 1.5rem;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.checkbox-group {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

/* CSV导入样式 */
.csv-importer {
  padding: 30px;
}

.importer-header {
  text-align: center;
  margin-bottom: 40px;
}

.importer-header h2 {
  color: #2E7D32;
  margin-bottom: 10px;
}

.template-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.template-section h3 {
  color: #2E7D32;
  margin-bottom: 15px;
}

.template-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.template-button {
  padding: 15px 20px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  font-weight: 600;
}

.template-button:hover {
  border-color: #2196F3;
  background: #f3f8ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.upload-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.upload-section h3 {
  color: #2E7D32;
  margin-bottom: 15px;
}

.upload-area {
  text-align: center;
  padding: 40px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  background: white;
}

.upload-button {
  padding: 15px 30px;
  background: linear-gradient(135deg, #2196F3, #42A5F5);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 10px;
}

.upload-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.preview-section {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.preview-section h3 {
  color: #2E7D32;
  margin-bottom: 15px;
}

.preview-errors {
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.preview-errors h4 {
  color: #c62828;
  margin: 0 0 10px 0;
}

.error-item {
  color: #d32f2f;
  font-size: 14px;
  margin: 5px 0;
}

.preview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e0e0e0;
}

.stat-label {
  display: block;
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  color: #2E7D32;
  font-size: 24px;
  font-weight: bold;
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quiz-management-page {
    padding: 10px;
  }

  .quiz-management-tabs {
    flex-direction: column;
  }

  .tab-button {
    justify-content: center;
  }

  .manager-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .pack-actions,
  .question-actions,
  .option-actions {
    justify-content: center;
  }

  .pack-meta {
    justify-content: center;
  }

  .breadcrumb-item {
    max-width: 120px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .template-buttons {
    grid-template-columns: 1fr;
  }

  .preview-stats {
    grid-template-columns: 1fr;
  }

  .quiz-pack-form,
  .quiz-question-form,
  .quiz-option-form {
    margin: 10px;
    padding: 20px;
  }
}
