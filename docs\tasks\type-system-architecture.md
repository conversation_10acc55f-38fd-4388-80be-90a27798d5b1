# 泛化类型系统架构文档

本文档详细说明了项目的泛化类型系统架构，包括 Schema 定义、类型生成、验证机制和最佳实践。这是一个可复用的类型系统设计，适用于任何需要类型安全和多语言支持的项目。

## 📋 目录

- [架构概览](#架构概览)
- [Schema 定义系统](#schema-定义系统)
- [类型生成机制](#类型生成机制)
- [验证系统](#验证系统)
- [多语言类型支持](#多语言类型支持)
- [Repository 模式](#repository-模式)
- [Service 模式](#service-模式)
- [最佳实践](#最佳实践)

## 🏗️ 架构概览

### 设计哲学

我们的类型系统基于以下核心原则：

1. **单一数据源** - SQL Schema 作为类型定义的唯一真实来源
2. **分层架构** - 基础类型、API 类型、扩展类型清晰分离
3. **类型安全** - 编译时 + 运行时双重类型检查
4. **自动生成** - 减少手工维护，提高一致性
5. **可扩展性** - 易于添加新的实体和功能

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Pages/Components)                 │
├─────────────────────────────────────────────────────────────┤
│                    Hook 层 (Data Hooks)                     │
├─────────────────────────────────────────────────────────────┤
│                   Service 层 (Business Logic)               │
├─────────────────────────────────────────────────────────────┤
│                 Repository 层 (Data Access)                 │
├─────────────────────────────────────────────────────────────┤
│                   Schema 层 (Type Definitions)              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │   base.ts   │   api.ts    │translation.ts│generator.ts │   │
│  │ (基础类型)   │ (API类型)    │ (翻译扩展)   │ (生成工具)   │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                   数据库层 (SQL Schema)                      │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. Schema 定义层
- **base.ts**: 基础实体类型（直接映射数据库表）
- **api.ts**: API 输入输出类型（服务层需求）
- **translation.ts**: 多语言扩展类型
- **generator.ts**: 类型生成和验证工具

#### 2. 验证层
- **Zod Schema**: 运行时类型验证
- **TypeScript**: 编译时类型检查
- **Custom Validators**: 业务规则验证

#### 3. 应用层
- **Repository**: 数据访问抽象
- **Service**: 业务逻辑封装
- **Hook**: React 数据管理

## 📐 Schema 定义系统

### 基础 Schema 模式

#### 1. 实体 Schema 定义
```typescript
// 基础实体 Schema 模板
export const EntitySchema = z.object({
  // 标准字段
  id: IdSchema,
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  
  // 软删除支持
  is_deleted: z.boolean().default(false),
  deleted_at: TimestampSchema.optional(),
  
  // 审计字段
  created_by: OptionalIdSchema,
  updated_by: OptionalIdSchema,
  
  // 业务字段
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  
  // 扩展字段（根据需要添加）
  // ...
});

export type Entity = z.infer<typeof EntitySchema>;
```

#### 2. 通用 Schema 组件
```typescript
// 可复用的 Schema 组件
export const IdSchema = z.string().uuid();
export const OptionalIdSchema = z.string().uuid().optional();
export const TimestampSchema = z.string().datetime();
export const LanguageCodeSchema = z.enum([
  'en', 'zh', 'zh-TW', 'ja', 'ko', 'es', 'fr', 'de', 
  'it', 'pt', 'ru', 'ar', 'hi', 'th', 'vi'
]);

// 分页 Schema
export const PaginationSchema = z.object({
  limit: z.number().int().min(1).max(1000).default(100),
  offset: z.number().int().min(0).default(0)
});

// 排序 Schema
export const SortingSchema = z.object({
  orderBy: z.string().optional(),
  orderDirection: z.enum(['ASC', 'DESC']).default('ASC')
});

// 过滤器基础 Schema
export const BaseFilterSchema = z.object({
  searchTerm: z.string().optional(),
  is_deleted: z.boolean().optional(),
  created_after: TimestampSchema.optional(),
  created_before: TimestampSchema.optional()
}).merge(PaginationSchema).merge(SortingSchema);
```

### API Schema 模式

#### 1. CRUD 操作 Schema
```typescript
// 创建操作 Schema 模板
export const CreateEntityDataSchema = EntitySchema
  .omit({
    created_at: true,
    updated_at: true,
    is_deleted: true,
    deleted_at: true,
    updated_by: true
  })
  .extend({
    id: IdSchema, // 必需，用于客户端生成
    // 其他创建时的特殊字段
  });

// 更新操作 Schema 模板
export const UpdateEntityDataSchema = EntitySchema
  .omit({
    id: true,
    created_at: true,
    updated_at: true,
    created_by: true
  })
  .partial() // 所有字段都是可选的
  .extend({
    updated_by: OptionalIdSchema
  });

// 过滤器 Schema 模板
export const EntityFilterSchema = BaseFilterSchema.extend({
  // 实体特定的过滤字段
  name: z.string().optional(),
  created_by: OptionalIdSchema,
  // ...
});
```

#### 2. Service 输入 Schema
```typescript
// Service 层输入 Schema 模板
export const CreateEntityInputSchema = z.object({
  // 业务字段（用户友好的格式）
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  
  // 关联数据
  tags: z.array(z.string()).max(10).optional(),
  
  // 翻译数据
  translations: z.array(TranslationInputSchema).optional(),
  
  // 其他业务逻辑字段
  // ...
});

export const UpdateEntityInputSchema = CreateEntityInputSchema
  .partial()
  .extend({
    // 更新特定的字段
  });
```

## 🔄 类型生成机制

### 自动类型生成

#### 1. Schema 到 TypeScript 类型
```typescript
// 自动生成 TypeScript 类型
export type Entity = z.infer<typeof EntitySchema>;
export type CreateEntityData = z.infer<typeof CreateEntityDataSchema>;
export type UpdateEntityData = z.infer<typeof UpdateEntityDataSchema>;
export type EntityFilter = z.infer<typeof EntityFilterSchema>;
export type CreateEntityInput = z.infer<typeof CreateEntityInputSchema>;
export type UpdateEntityInput = z.infer<typeof UpdateEntityInputSchema>;
```

#### 2. 类型生成工具
```typescript
// generator.ts - 类型生成辅助工具
export function generateCRUDSchemas<T extends z.ZodObject<any>>(
  baseSchema: T,
  entityName: string,
  options?: {
    excludeFromCreate?: (keyof z.infer<T>)[];
    excludeFromUpdate?: (keyof z.infer<T>)[];
    additionalFilters?: z.ZodObject<any>;
  }
) {
  const createSchema = baseSchema
    .omit({
      created_at: true,
      updated_at: true,
      is_deleted: true,
      deleted_at: true,
      updated_by: true,
      ...(options?.excludeFromCreate?.reduce((acc, key) => ({ ...acc, [key]: true }), {}) || {})
    })
    .extend({
      id: IdSchema
    });

  const updateSchema = baseSchema
    .omit({
      id: true,
      created_at: true,
      updated_at: true,
      created_by: true,
      ...(options?.excludeFromUpdate?.reduce((acc, key) => ({ ...acc, [key]: true }), {}) || {})
    })
    .partial()
    .extend({
      updated_by: OptionalIdSchema
    });

  const filterSchema = BaseFilterSchema.extend({
    name: z.string().optional(),
    created_by: OptionalIdSchema,
    ...(options?.additionalFilters?.shape || {})
  });

  return {
    createSchema,
    updateSchema,
    filterSchema,
    // 生成对应的 TypeScript 类型
    types: {
      Entity: baseSchema,
      created_ata: createSchema,
      updated_ata: updateSchema,
      Filter: filterSchema
    }
  };
}
```

### 使用示例

#### 1. 快速生成实体 Schema
```typescript
// 定义基础实体
const UserSchema = z.object({
  id: IdSchema,
  username: z.string().min(1).max(50),
  email: z.string().email().optional(),
  display_name: z.string().max(100).optional(),
  is_vip: z.boolean().default(false),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: OptionalIdSchema,
  updated_by: OptionalIdSchema,
  is_deleted: z.boolean().default(false),
  deleted_at: TimestampSchema.optional()
});

// 自动生成 CRUD Schema
const userSchemas = generateCRUDSchemas(UserSchema, 'User', {
  excludeFromCreate: ['is_vip'], // VIP 状态不能在创建时设置
  additionalFilters: z.object({
    is_vip: z.boolean().optional(),
    username_contains: z.string().optional()
  })
});

// 导出生成的 Schema 和类型
export const {
  createSchema: CreateUserDataSchema,
  updateSchema: UpdateUserDataSchema,
  filterSchema: UserFilterSchema
} = userSchemas;

export type User = z.infer<typeof UserSchema>;
export type CreateUserData = z.infer<typeof CreateUserDataSchema>;
export type UpdateUserData = z.infer<typeof UpdateUserDataSchema>;
export type UserFilter = z.infer<typeof UserFilterSchema>;
```

## ✅ 验证系统

### 多层验证架构

#### 1. Schema 验证层
```typescript
// 基础验证函数
export function validateSchema<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: string[] } {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  }
  
  return {
    success: false,
    errors: result.error.issues.map(issue => 
      `${issue.path.join('.')}: ${issue.message}`
    )
  };
}

// 异步验证支持
export async function validateSchemaAsync<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  customValidators?: Array<(data: T) => Promise<string | null>>
): Promise<{ success: true; data: T } | { success: false; errors: string[] }> {
  // 基础 Schema 验证
  const baseResult = validateSchema(schema, data);
  if (!baseResult.success) {
    return baseResult;
  }

  // 自定义异步验证
  if (customValidators) {
    const errors: string[] = [];
    for (const validator of customValidators) {
      const error = await validator(baseResult.data);
      if (error) {
        errors.push(error);
      }
    }
    
    if (errors.length > 0) {
      return { success: false, errors };
    }
  }

  return { success: true, data: baseResult.data };
}
```

#### 2. 业务规则验证
```typescript
// 业务规则验证器
export class EntityValidator<T> {
  private customRules: Array<(data: T) => Promise<string | null>> = [];

  addRule(rule: (data: T) => Promise<string | null>): this {
    this.customRules.push(rule);
    return this;
  }

  async validate(schema: z.ZodSchema<T>, data: unknown): Promise<ValidationResult<T>> {
    return validateSchemaAsync(schema, data, this.customRules);
  }
}

// 使用示例
const userValidator = new EntityValidator<CreateUserInput>()
  .addRule(async (data) => {
    // 检查用户名是否已存在
    const exists = await userRepository.existsByUsername(data.username);
    return exists ? 'Username already exists' : null;
  })
  .addRule(async (data) => {
    // 检查邮箱格式和唯一性
    if (data.email) {
      const exists = await userRepository.existsByEmail(data.email);
      return exists ? 'Email already exists' : null;
    }
    return null;
  });
```

### 验证中间件

#### 1. Repository 层验证
```typescript
export abstract class ValidatedRepository<T, created_ata, updated_ata> 
  extends BaseRepository<T, created_ata, updated_ata> {
  
  protected abstract getCreateSchema(): z.ZodSchema<created_ata>;
  protected abstract getUpdateSchema(): z.ZodSchema<updated_ata>;
  protected abstract getValidator(): EntityValidator<created_ata>;

  async create(context: DatabaseContext, data: unknown): Promise<T> {
    // 验证创建数据
    const validation = await this.getValidator().validate(
      this.getCreateSchema(), 
      data
    );
    
    if (!validation.success) {
      throw new ValidationError(validation.errors);
    }

    return super.create(context, validation.data);
  }

  async update(context: DatabaseContext, id: string, data: unknown): Promise<T> {
    // 验证更新数据
    const validation = validateSchema(this.getUpdateSchema(), data);
    
    if (!validation.success) {
      throw new ValidationError(validation.errors);
    }

    return super.update(context, id, validation.data);
  }
}
```

#### 2. Service 层验证
```typescript
export abstract class ValidatedService<T, CreateInput, UpdateInput> 
  extends BaseService<T, CreateInput, UpdateInput> {
  
  protected abstract getCreateInputSchema(): z.ZodSchema<CreateInput>;
  protected abstract getUpdateInputSchema(): z.ZodSchema<UpdateInput>;

  async create(input: unknown): Promise<ServiceResult<T>> {
    try {
      // 验证输入数据
      const validation = validateSchema(this.getCreateInputSchema(), input);
      
      if (!validation.success) {
        return this.createErrorResult('VALIDATION_ERROR', validation.errors.join('; '));
      }

      // 转换为 Repository 数据格式
      const created_ata = await this.transformCreateInput(validation.data);
      
      // 调用 Repository
      const result = await this.repository.create(
        await this.getDatabaseContext(),
        created_ata
      );

      return this.createSuccessResult(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  protected abstract transformCreateInput(input: CreateInput): Promise<any>;
}
```

## 🌍 多语言类型支持

### 翻译类型系统

#### 1. 基础翻译类型
```typescript
// 通用翻译接口
export const TranslationSchema = z.object({
  entityId: z.string().min(1),
  languageCode: LanguageCodeSchema,
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional()
});

// 可翻译实体基础类型
export const TranslatableEntitySchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1), // 默认语言
  description: z.string().optional(), // 默认语言
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  
  // 运行时翻译字段
  localizedName: z.string().optional(),
  localizedDescription: z.string().optional(),
  translations: z.array(TranslationSchema).optional()
});

// 翻译输入类型
export const TranslationInputSchema = z.object({
  languageCode: LanguageCodeSchema,
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional()
});
```

#### 2. 翻译扩展生成器
```typescript
// 为任何实体添加翻译支持
export function makeTranslatable<T extends z.ZodObject<any>>(
  baseSchema: T,
  entityName: string
) {
  // 扩展基础 Schema 以支持翻译
  const translatableSchema = baseSchema.extend({
    localizedName: z.string().optional(),
    localizedDescription: z.string().optional(),
    translations: z.array(TranslationSchema).optional()
  });

  // 生成翻译表 Schema
  const translationTableSchema = z.object({
    [`${entityName.toLowerCase()}_id`]: IdSchema,
    language_code: LanguageCodeSchema,
    translated_name: z.string().min(1),
    translated_description: z.string().optional()
  });

  // 生成创建输入 Schema（支持翻译）
  const createInputSchema = baseSchema
    .omit({
      id: true,
      created_at: true,
      updated_at: true,
      is_deleted: true,
      deleted_at: true
    })
    .extend({
      translations: z.array(TranslationInputSchema).optional()
    });

  return {
    translatableSchema,
    translationTableSchema,
    createInputSchema,
    types: {
      TranslatableEntity: translatableSchema,
      TranslationTable: translationTableSchema,
      CreateInput: createInputSchema
    }
  };
}
```

### 使用示例

#### 1. 创建可翻译实体
```typescript
// 基础情绪 Schema
const EmotionBaseSchema = z.object({
  id: IdSchema,
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  emoji: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  is_deleted: z.boolean().default(false),
  deleted_at: TimestampSchema.optional()
});

// 添加翻译支持
const emotionSchemas = makeTranslatable(EmotionBaseSchema, 'Emotion');

// 导出类型
export const EmotionSchema = emotionSchemas.translatableSchema;
export const EmotionTranslationSchema = emotionSchemas.translationTableSchema;
export const CreateEmotionInputSchema = emotionSchemas.createInputSchema;

export type Emotion = z.infer<typeof EmotionSchema>;
export type EmotionTranslation = z.infer<typeof EmotionTranslationSchema>;
export type CreateEmotionInput = z.infer<typeof CreateEmotionInputSchema>;
```

## 📚 相关文档

- [数据库设计文档](./database-design.md)
- [多语言系统文档](./internationalization.md)
- [类型系统使用指南](../src/types/README.md)
- [Repository 模式文档](./repository-pattern.md)
- [Service 模式文档](./service-pattern.md)

---

通过这个泛化的类型系统架构，我们实现了：
- **类型安全**：编译时和运行时的双重保障
- **代码复用**：可复用的 Schema 组件和生成器
- **自动化**：减少手工维护，提高开发效率
- **可扩展性**：易于添加新的实体和功能
- **多语言支持**：完整的国际化类型基础设施
