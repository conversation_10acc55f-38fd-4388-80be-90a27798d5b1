/**
 * Quiz图片组件
 * 支持多种中医文化样式的图片组件
 */

import React, { useState } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { ImageComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type ImageComponentConfig = z.infer<typeof ImageComponentConfigSchema>;

export interface ImageComponentProps extends BaseQuizComponentProps<ImageComponentConfig> {
  onLoad?: () => void;
  onError?: (error: string) => void;
  onClick?: () => void;
}

interface ImageComponentState extends ComponentState {
  is_loaded: boolean;
  has_error: boolean;
  error_message: string | null;
}

/**
 * 图片组件类
 */
export class ImageComponent extends BaseQuizComponent<
  ImageComponentConfig,
  ImageComponentProps,
  ImageComponentState
> {
  private imageRef = React.createRef<HTMLImageElement>();

  extractConfig(props: ImageComponentProps): ImageComponentConfig {
    return props.config;
  }

  getInitialState(): ImageComponentState {
    return {
      is_loading: true,
      is_interactive: !!this.props.onClick,
      is_disabled: false,
      selected_items: [],
      animation_state: 'idle',
      is_loaded: false,
      has_error: false,
      error_message: null
    };
  }

  /**
   * 处理图片加载成功
   */
  private handleImageLoad = (): void => {
    this.setState({
      is_loading: false,
      is_loaded: true,
      has_error: false,
      error_message: null
    });

    this.props.onLoad?.();

    // 发送交互事件
    this.emitInteractionEvent('focus', {
      action: 'image_loaded',
      image_url: this.config.content.image_url
    });
  };

  /**
   * 处理图片加载失败
   */
  private handleImageError = (): void => {
    const errorMessage = 'Failed to load image';
    
    this.setState({
      is_loading: false,
      is_loaded: false,
      has_error: true,
      error_message: errorMessage
    });

    this.props.onError?.(errorMessage);

    // 发送交互事件
    this.emitInteractionEvent('focus', {
      action: 'image_error',
      image_url: this.config.content.image_url,
      error: errorMessage
    });
  };

  /**
   * 处理图片点击
   */
  private handleImageClick = (): void => {
    if (!this.state.is_interactive || this.state.has_error) return;

    this.props.onClick?.();

    // 触发触觉反馈
    this.triggerHapticFeedback('light');

    // 发送交互事件
    this.emitInteractionEvent('click', {
      action: 'image_clicked',
      image_url: this.config.content.image_url
    });
  };

  /**
   * 获取边框样式类名
   */
  private getBorderStyleClassName(): string {
    const borderStyle = this.config.style.border_style;
    return `quiz-image-border-${borderStyle}`;
  }

  /**
   * 获取悬停效果类名
   */
  private getHoverEffectClassName(): string {
    const hoverEffect = this.config.style.hover_effect;
    return hoverEffect !== 'none' ? `quiz-image-hover-${hoverEffect}` : '';
  }

  /**
   * 获取图片替代文本
   */
  private getAltText(): string {
    const { language } = this.context || { language: 'zh' };
    const altText = this.config.content.alt_text;
    return altText[language] || altText['zh'] || altText['en'] || '';
  }

  /**
   * 获取图片标题
   */
  private getCaption(): string | null {
    if (!this.config.content.caption) return null;
    
    const { language } = this.context || { language: 'zh' };
    const caption = this.config.content.caption;
    return caption[language] || caption['zh'] || caption['en'] || null;
  }

  /**
   * 渲染加载状态
   */
  private renderLoadingState(): React.ReactNode {
    return (
      <div className="quiz-image-loading">
        <div className="quiz-image-loading-spinner" />
        <span className="quiz-image-loading-text">
          {this.context?.language === 'zh' ? '加载中...' : 'Loading...'}
        </span>
      </div>
    );
  }

  /**
   * 渲染错误状态
   */
  private renderErrorState(): React.ReactNode {
    return (
      <div className="quiz-image-error">
        <div className="quiz-image-error-icon">⚠️</div>
        <span className="quiz-image-error-text">
          {this.context?.language === 'zh' ? '图片加载失败' : 'Failed to load image'}
        </span>
      </div>
    );
  }

  /**
   * 渲染图片内容
   */
  private renderImageContent(): React.ReactNode {
    if (this.state.is_loading) {
      return this.renderLoadingState();
    }

    if (this.state.has_error) {
      return this.renderErrorState();
    }

    const altText = this.getAltText();

    return (
      <img
        ref={this.imageRef}
        src={this.config.content.image_url}
        alt={altText}
        className="quiz-image-element"
        onLoad={this.handleImageLoad}
        onError={this.handleImageError}
        style={{
          width: this.config.style.size.width ? `${this.config.style.size.width}px` : undefined,
          height: this.config.style.size.height ? `${this.config.style.size.height}px` : undefined,
          aspectRatio: this.config.style.size.aspect_ratio || undefined,
          borderRadius: `${this.config.style.border_radius}px`,
        }}
      />
    );
  }

  /**
   * 渲染图片标题
   */
  private renderCaption(): React.ReactNode {
    const caption = this.getCaption();
    if (!caption) return null;

    return (
      <div className="quiz-image-caption">
        {caption}
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-image-component',
      this.getBorderStyleClassName(),
      this.getHoverEffectClassName(),
      this.state.is_interactive && 'quiz-image-interactive',
      this.state.is_loading && 'quiz-image-loading-state',
      this.state.has_error && 'quiz-image-error-state',
      this.config.style.shadow_effect && 'quiz-image-shadow',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        onClick={this.handleImageClick}
        {...accessibilityProps}
        role={this.state.is_interactive ? 'button' : 'img'}
        tabIndex={this.state.is_interactive ? 0 : -1}
      >
        {/* 图片内容 */}
        <div className="quiz-image-container">
          {this.renderImageContent()}
        </div>

        {/* 图片标题 */}
        {this.renderCaption()}
      </div>
    );
  }

  protected getAriaRole(): string {
    return this.state.is_interactive ? 'button' : 'img';
  }

  protected getAriaLabel(): string {
    const altText = this.getAltText();
    const caption = this.getCaption();
    
    if (this.state.is_interactive) {
      return `Clickable image: ${altText}${caption ? ` - ${caption}` : ''}`;
    }
    
    return altText;
  }
}

// 使用Context的函数式组件包装器
const ImageComponentWrapper: React.FC<ImageComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <ImageComponent.contextType.Provider value={{ language }}>
      <ImageComponent {...props} />
    </ImageComponent.contextType.Provider>
  );
};

// 设置Context类型
ImageComponent.contextType = React.createContext({ language: 'zh' });

export default ImageComponentWrapper;
