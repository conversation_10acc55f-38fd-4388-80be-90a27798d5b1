{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/documentation/documentation-validation.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 83130, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 83130, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 520, "endOffset": 28672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 563, "endOffset": 624, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 679, "endOffset": 6312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 738, "endOffset": 3809, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3863, "endOffset": 6304, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6363, "endOffset": 11530, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6419, "endOffset": 9273, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9326, "endOffset": 11522, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11581, "endOffset": 16730, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11635, "endOffset": 14543, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14594, "endOffset": 16722, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16782, "endOffset": 22527, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16836, "endOffset": 19683, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19733, "endOffset": 22519, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22581, "endOffset": 28668, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22634, "endOffset": 25663, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25713, "endOffset": 28660, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}