# 服务层重构实施计划

## 🎯 总体目标

基于在线服务架构分析，采用混合架构模式重构服务层：
- **简单操作**: 直接使用tRPC (认证、同步、查询)
- **复杂业务**: 通过OnlineServices (支付、分析)
- **数据操作**: 优先使用离线Services

## 📋 详细实施计划

### 阶段1: 修复现有问题 (1-2天)

#### 任务1.1: 修复useShop支付功能 🔧

**当前问题**:
```typescript
// src/hooks/useShop.ts - 第170行左右
const paymentService = await onlineServices.payment(); // ❌ 服务不存在
```

**解决步骤**:

1. **创建PaymentService** (30分钟)
   ```bash
   # 创建目录
   mkdir -p src/services/online/services
   
   # 创建PaymentService
   touch src/services/online/services/PaymentService.ts
   ```

2. **更新OnlineServices** (20分钟)
   - 添加payment()方法
   - 注册PaymentService

3. **更新useShop** (15分钟)
   - 修复payment服务调用
   - 添加错误处理

4. **测试验证** (30分钟)
   - 编写PaymentService单元测试
   - 编写useShop集成测试

#### 任务1.2: 重构useDataSync 🔧

**当前问题**:
```typescript
// src/hooks/useDataSync.ts
// 使用模拟逻辑，未调用实际tRPC端点
console.log('Simulating sync...');
setTimeout(() => { /* 模拟逻辑 */ }, 2000);
```

**解决步骤**:

1. **重构useDataSync** (45分钟)
   - 移除模拟逻辑
   - 添加tRPC调用
   - 实现本地数据获取和处理

2. **添加辅助函数** (30分钟)
   - getLocalPendingData()
   - processServerData()
   - getLastSyncTimestamp()

3. **测试验证** (30分钟)
   - 编写useDataSync单元测试
   - 编写数据同步集成测试

#### 任务1.3: 简化OnlineServices架构 🔧

**当前问题**:
- 复杂的Factory模式
- 功能重复
- 不必要的抽象层

**解决步骤**:

1. **重构OnlineServices.ts** (30分钟)
   - 移除复杂的Factory模式
   - 只保留基础服务和复杂业务服务
   - 添加单例模式

2. **简化index.ts** (15分钟)
   - 移除OnlineServiceFactory
   - 简化导出

3. **测试验证** (20分钟)
   - 验证现有功能不受影响
   - 测试服务注册机制

### 阶段2: 完善业务服务 (2-3天)

#### 任务2.1: 创建复杂业务服务

**PaymentService实现** (2小时):
```typescript
export class PaymentService {
  constructor(private trpc: any) {}

  async purchaseVip(planId: string, paymentMethodId: string) {
    // 1. 验证支付方法
    // 2. 创建支付意图
    // 3. 处理支付
    // 4. 更新本地VIP状态
  }

  async purchaseSkin(skinId: string, paymentMethodId: string) {
    // 复杂的皮肤购买流程
  }
}
```

**AnalyticsService实现** (1.5小时):
```typescript
export class AnalyticsService {
  constructor(private trpc: any) {}

  async getMoodAnalytics(timeRange: string) {
    // 复杂的数据分析逻辑
  }

  async generateReport(userId: string, options: any) {
    // 生成分析报告
  }
}
```

#### 任务2.2: 更新相关Hooks

**useVip重构** (1小时):
- 评估复杂度
- 选择直接tRPC或PaymentService
- 实现VIP状态管理

**useAnalytics创建** (1小时):
- 使用AnalyticsService
- 实现数据缓存
- 添加错误处理

### 阶段3: 测试和优化 (2-3天)

#### 任务3.1: 编写全面测试

**单元测试** (1天):
- PaymentService.test.ts
- AnalyticsService.test.ts
- useShop.test.ts
- useDataSync.test.ts
- useVip.test.ts

**集成测试** (1天):
- 支付流程集成测试
- 数据同步集成测试
- 服务注册集成测试

**端到端测试** (1天):
- 完整支付流程E2E
- 数据同步E2E
- 用户体验E2E

#### 任务3.2: 性能优化

**缓存策略** (半天):
- 实现智能缓存
- 添加缓存失效机制

**错误处理** (半天):
- 统一错误处理
- 网络重试机制

### 阶段4: 扩展功能 (按需实施)

#### 任务4.1: 实现新的业务Hooks
- useHomeData
- useHistoryData
- useSettingsData
- useExportData

#### 任务4.2: 服务端完善
- Better-Auth集成
- 支付处理逻辑
- 数据分析端点

## 🧪 测试执行计划

### 测试环境准备

1. **本地测试环境**:
   ```bash
   # 安装测试依赖
   npm install --save-dev @testing-library/react-hooks
   npm install --save-dev @testing-library/jest-dom
   npm install --save-dev vitest
   
   # 配置测试环境
   # 更新vitest.config.ts
   ```

2. **Mock服务配置**:
   ```typescript
   // 创建tRPC mock
   const mockTrpc = {
     login: { mutate: vi.fn() },
     synchronizeData: { mutate: vi.fn() },
     purchaseVip: { mutate: vi.fn() }
   };
   ```

### 测试执行顺序

1. **单元测试** (每个任务完成后立即执行)
2. **集成测试** (阶段完成后执行)
3. **端到端测试** (所有功能完成后执行)
4. **回归测试** (发布前执行)

## 📊 进度跟踪

### 里程碑检查点

- [ ] **里程碑1**: useShop支付功能修复完成
- [ ] **里程碑2**: useDataSync重构完成
- [ ] **里程碑3**: OnlineServices架构简化完成
- [ ] **里程碑4**: PaymentService和AnalyticsService实现完成
- [ ] **里程碑5**: 所有测试通过
- [ ] **里程碑6**: 性能优化完成

### 质量标准

1. **代码覆盖率**: ≥80%
2. **测试通过率**: 100%
3. **TypeScript编译**: 无错误
4. **ESLint检查**: 无警告
5. **性能测试**: 响应时间<500ms

## 🚀 部署计划

### 分阶段部署

1. **开发环境**: 每个任务完成后部署
2. **测试环境**: 每个阶段完成后部署
3. **预生产环境**: 所有功能完成后部署
4. **生产环境**: 充分测试后部署

### 回滚计划

1. **代码回滚**: Git分支管理
2. **数据库回滚**: 数据库备份
3. **配置回滚**: 配置文件版本控制

## ⚠️ 风险评估

### 高风险项

1. **支付功能**: 涉及金钱交易，需要充分测试
2. **数据同步**: 可能导致数据丢失，需要备份机制
3. **用户认证**: 影响用户体验，需要向后兼容

### 风险缓解措施

1. **充分测试**: 多轮测试验证
2. **渐进式部署**: 分阶段发布
3. **监控告警**: 实时监控系统状态
4. **快速回滚**: 准备回滚方案

## 📞 团队协作

### 责任分工

- **架构设计**: 技术负责人
- **代码实现**: 前端开发工程师
- **测试验证**: QA工程师
- **部署运维**: DevOps工程师

### 沟通机制

- **日常同步**: 每日站会
- **进度汇报**: 每周进度会
- **问题升级**: 及时沟通解决

这个实施计划确保了重构工作的有序进行，同时保证了代码质量和系统稳定性。
