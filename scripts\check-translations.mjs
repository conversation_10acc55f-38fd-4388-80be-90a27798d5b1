/**
 * Translation Key Checker
 *
 * This script scans the codebase for translation keys used in the UI,
 * and compares them with keys defined in database seed files and JSON translation files.
 * It helps identify missing or unused translation keys.
 */

import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  srcDir: path.resolve(__dirname, '../src'),
  seedDir: path.resolve(__dirname, '../public/seeds'),
  localesDir: path.resolve(__dirname, '../src/locales'),
  uiLabelsSeedFile: 'ui_labels.sql',
  uiLabelTranslationsSeedFile: 'ui_label_translations.sql',
  jsonFiles: ['en.json', 'zh.json'],
  fileExtensions: ['.tsx', '.ts'],
  excludeDirs: ['node_modules', 'dist', 'build', '.git'],
  translationFunctionNames: ['t'], // Add more if you use different function names for translations
};

// Regular expressions for finding translation keys
const createTranslationKeyRegex = (funcName) => {
  // Match t('key') or t("key") patterns
  return new RegExp(`${funcName}\\(\\s*['"]([^'"]+)['"]`, 'g');
};

// Regular expressions for extracting keys from SQL files
const uiLabelsKeyRegex =
  /INSERT\s+OR\s+IGNORE\s+INTO\s+ui_labels\s+\(\s*key\s*,\s*default_text\s*\)\s+VALUES\s*\(?\s*['"]([^'"]+)['"]/g;
const uiLabelTranslationsKeyRegex =
  /INSERT\s+OR\s+IGNORE\s+INTO\s+ui_label_translations\s+\(\s*label_key\s*,\s*language_code\s*,\s*translated_text\s*\)\s+VALUES\s*\(?\s*['"]([^'"]+)['"]/g;

/**
 * Recursively scans a directory for files with specified extensions
 */
async function scanDirectory(dir, extensions, excludeDirs) {
  const files = [];
  const entries = await fs.readdir(dir);

  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stats = await fs.stat(fullPath);

    if (stats.isDirectory()) {
      if (!excludeDirs.includes(entry)) {
        const subDirFiles = await scanDirectory(fullPath, extensions, excludeDirs);
        files.push(...subDirFiles);
      }
    } else if (stats.isFile() && extensions.some((ext) => entry.endsWith(ext))) {
      files.push(fullPath);
    }
  }

  return files;
}

/**
 * Extracts translation keys from code files
 */
async function extractKeysFromCode(files, translationFunctionNames) {
  const keys = new Set();

  for (const file of files) {
    const content = await fs.readFile(file, 'utf8');

    for (const funcName of translationFunctionNames) {
      const regex = createTranslationKeyRegex(funcName);
      let match;

      while ((match = regex.exec(content)) !== null) {
        keys.add(match[1]);
      }
    }
  }

  return keys;
}

/**
 * Extracts keys from SQL seed files
 */
async function extractKeysFromSeedFiles(seedDir, uiLabelsFile, uiLabelTranslationsFile) {
  const keys = new Set();

  // Extract from ui_labels.sql
  const uiLabelsPath = path.join(seedDir, uiLabelsFile);
  try {
    await fs.access(uiLabelsPath);
    const content = await fs.readFile(uiLabelsPath, 'utf8');
    let match;

    while ((match = uiLabelsKeyRegex.exec(content)) !== null) {
      keys.add(match[1]);
    }
  } catch (error) {
    // File doesn't exist or can't be accessed
    console.warn(`Warning: Could not access ${uiLabelsPath}`);
  }

  // Extract from ui_label_translations.sql
  const uiLabelTranslationsPath = path.join(seedDir, uiLabelTranslationsFile);
  try {
    await fs.access(uiLabelTranslationsPath);
    const content = await fs.readFile(uiLabelTranslationsPath, 'utf8');
    let match;

    while ((match = uiLabelTranslationsKeyRegex.exec(content)) !== null) {
      keys.add(match[1]);
    }
  } catch (error) {
    // File doesn't exist or can't be accessed
    console.warn(`Warning: Could not access ${uiLabelTranslationsPath}`);
  }

  return keys;
}

/**
 * Extracts keys from JSON translation files
 */
async function extractKeysFromJsonFiles(localesDir, jsonFiles) {
  const keys = new Set();

  for (const jsonFile of jsonFiles) {
    const jsonPath = path.join(localesDir, jsonFile);
    try {
      await fs.access(jsonPath);
      const content = await fs.readFile(jsonPath, 'utf8');
      const json = JSON.parse(content);

      // Add all keys from the flattened JSON
      const flattenedKeys = flattenObject(json);
      Object.keys(flattenedKeys).forEach((key) => keys.add(key));
    } catch (error) {
      // File doesn't exist or can't be accessed
      console.warn(`Warning: Could not access ${jsonPath}`);
    }
  }

  return keys;
}

/**
 * Flattens a nested object into a single-level object with dot-notation keys
 */
function flattenObject(obj, prefix = '') {
  return Object.keys(obj).reduce((acc, key) => {
    const prefixedKey = prefix ? `${prefix}.${key}` : key;

    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      Object.assign(acc, flattenObject(obj[key], prefixedKey));
    } else {
      acc[prefixedKey] = obj[key];
    }

    return acc;
  }, {});
}

/**
 * Compares sets and returns differences
 */
function compareSets(setA, setB, labelA, labelB) {
  const onlyInA = [...setA].filter((key) => !setB.has(key));
  const onlyInB = [...setB].filter((key) => !setA.has(key));

  return {
    [`onlyIn${labelA}`]: onlyInA,
    [`onlyIn${labelB}`]: onlyInB,
  };
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('Scanning for translation keys...');

    // Scan code files
    const codeFiles = await scanDirectory(config.srcDir, config.fileExtensions, config.excludeDirs);
    console.log(`Found ${codeFiles.length} code files to scan.`);

    // Extract keys from different sources
    const codeKeys = await extractKeysFromCode(codeFiles, config.translationFunctionNames);
    console.log(`Found ${codeKeys.size} unique translation keys in code.`);

    const seedKeys = await extractKeysFromSeedFiles(
      config.seedDir,
      config.uiLabelsSeedFile,
      config.uiLabelTranslationsSeedFile
    );
    console.log(`Found ${seedKeys.size} unique translation keys in seed files.`);

    const jsonKeys = await extractKeysFromJsonFiles(config.localesDir, config.jsonFiles);
    console.log(`Found ${jsonKeys.size} unique translation keys in JSON files.`);

    // Compare keys
    const codeVsSeed = compareSets(codeKeys, seedKeys, 'Code', 'Seed');
    const codeVsJson = compareSets(codeKeys, jsonKeys, 'Code', 'Json');
    const seedVsJson = compareSets(seedKeys, jsonKeys, 'Seed', 'Json');

    // Output results
    console.log('\n=== TRANSLATION KEY ANALYSIS ===\n');

    console.log('Keys used in code but missing from seed files:');
    if (codeVsSeed.onlyInCode.length > 0) {
      codeVsSeed.onlyInCode.forEach((key) => console.log(`  - ${key}`));
    } else {
      console.log('  None');
    }

    console.log('\nKeys used in code but missing from JSON files:');
    if (codeVsJson.onlyInCode.length > 0) {
      codeVsJson.onlyInCode.forEach((key) => console.log(`  - ${key}`));
    } else {
      console.log('  None');
    }

    console.log('\nKeys in seed files but not used in code:');
    if (codeVsSeed.onlyInSeed.length > 0) {
      codeVsSeed.onlyInSeed.forEach((key) => console.log(`  - ${key}`));
    } else {
      console.log('  None');
    }

    console.log('\nKeys in JSON files but not used in code:');
    if (codeVsJson.onlyInJson.length > 0) {
      codeVsJson.onlyInJson.forEach((key) => console.log(`  - ${key}`));
    } else {
      console.log('  None');
    }

    console.log('\nKeys in seed files but missing from JSON files:');
    if (seedVsJson.onlyInSeed.length > 0) {
      seedVsJson.onlyInSeed.forEach((key) => console.log(`  - ${key}`));
    } else {
      console.log('  None');
    }

    console.log('\nKeys in JSON files but missing from seed files:');
    if (seedVsJson.onlyInJson.length > 0) {
      seedVsJson.onlyInJson.forEach((key) => console.log(`  - ${key}`));
    } else {
      console.log('  None');
    }

    console.log('\n=== ANALYSIS COMPLETE ===');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the script
main();
