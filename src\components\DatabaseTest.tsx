import type { SQLiteDBConnection } from '@capacitor-community/sqlite';
import type React from 'react';
import { useEffect, useState } from 'react';
import { useDatabaseContext } from '../contexts/DatabaseContext';
import { useSync } from '../contexts/SyncContext';
import { useDataSync } from '../hooks/useDataSync';

/**
 * 数据库测试组件
 * 使用新的 DatabaseProvider 和 DatabaseContext
 */
export const DatabaseTest: React.FC = () => {
  const { databaseService, isInitialized, isInitializing, error, platform } = useDatabaseContext();
  const { syncStatus, startSync, stopSync, pendingItems } = useSync();
  const { lastSyncTime, syncInProgress, forceSync } = useDataSync();
  const [db, setDb] = useState<SQLiteDBConnection | null>(null);
  const dbName = 'mindful_mood_db';

  useEffect(() => {
    const getConnection = async () => {
      if (isInitialized && !db) {
        try {
          const connection = await databaseService.getConnection();
          setDb(connection);
        } catch (err) {
          console.error('[DatabaseTest] Error getting connection:', err);
        }
      }
    };

    getConnection();
  }, [isInitialized, databaseService, db]);

  const handleTestQuery = async () => {
    if (!db) {
      console.log('[DatabaseTest] Database connection not available');
      return;
    }

    try {
      // 测试查询基础表
      const emotionsResult = await db.query('SELECT COUNT(*) as count FROM emotions;');
      const usersResult = await db.query('SELECT COUNT(*) as count FROM users;');
      const tagsResult = await db.query('SELECT COUNT(*) as count FROM tags;');

      console.log('[DatabaseTest] Query results:', {
        emotions: emotionsResult.values?.[0]?.count || 0,
        users: usersResult.values?.[0]?.count || 0,
        tags: tagsResult.values?.[0]?.count || 0,
      });

      // 显示结果
      alert(
        `数据库查询成功！\n情绪数据: ${emotionsResult.values?.[0]?.count || 0} 条\n用户数据: ${usersResult.values?.[0]?.count || 0} 条\n标签数据: ${tagsResult.values?.[0]?.count || 0} 条`
      );
    } catch (error) {
      console.error('[DatabaseTest] Query error:', error);
      alert(`查询失败: ${error}`);
    }
  };

  const handleInsertData = async () => {
    if (!db) {
      console.log('[DatabaseTest] Database connection not available');
      return;
    }

    try {
      const timestamp = new Date().toISOString();
      await db.execute(`
        INSERT INTO test_table (name) VALUES ('manual_entry_${timestamp}');
      `);
      console.log('[DatabaseTest] Data inserted successfully');

      // 手动保存到存储以确保持久化
      try {
        const jeepSqliteEl = document.querySelector('jeep-sqlite');
        if (jeepSqliteEl) {
          await (jeepSqliteEl as any).saveToStore(dbName);
          console.log('[DatabaseTest] Database saved to store after insert');
        }
      } catch (saveError) {
        console.warn('[DatabaseTest] Could not save to store:', saveError);
      }

      alert('数据插入成功！');
    } catch (error) {
      console.error('[DatabaseTest] Insert error:', error);
      alert(`插入失败: ${error}`);
    }
  };

  const handleCheckDatabaseList = async () => {
    try {
      const jeepSqliteEl = document.querySelector('jeep-sqlite');
      if (jeepSqliteEl) {
        const dbList = await (jeepSqliteEl as any).getDatabaseList();
        console.log('[DatabaseTest] Current databases in store:', dbList);

        const dbNames = dbList.values || [];
        alert(`当前数据库列表:\n${dbNames.join('\n')}\n\n目标数据库: ${dbName}`);
      } else {
        alert('jeep-sqlite 元素未找到');
      }
    } catch (error) {
      console.error('[DatabaseTest] Error getting database list:', error);
      alert(`获取数据库列表失败: ${error}`);
    }
  };

  const handleCheckTables = async () => {
    if (!db) {
      console.log('[DatabaseTest] Database connection not available');
      return;
    }

    try {
      // 查询所有表
      const tablesResult = await db.query(
        "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"
      );
      const tables = tablesResult.values?.map((row) => row.name) || [];

      console.log('[DatabaseTest] Database tables:', tables);

      // 显示表列表
      alert(`数据库表结构:\n共 ${tables.length} 个表\n\n${tables.join('\n')}`);
    } catch (error) {
      console.error('[DatabaseTest] Error checking tables:', error);
      alert(`检查表结构失败: ${error}`);
    }
  };

  const handleTestSync = async () => {
    try {
      console.log('[DatabaseTest] Starting sync test...');
      const success = await startSync();

      if (success) {
        alert('同步测试启动成功！请查看控制台日志了解详情。');
      } else {
        alert('同步测试启动失败！');
      }
    } catch (error) {
      console.error('[DatabaseTest] Sync test error:', error);
      alert(`同步测试失败: ${error}`);
    }
  };

  const handleForceSync = async () => {
    try {
      console.log('[DatabaseTest] Starting force sync...');
      await forceSync();
      alert('强制同步完成！请查看控制台日志了解详情。');
    } catch (error) {
      console.error('[DatabaseTest] Force sync error:', error);
      alert(`强制同步失败: ${error}`);
    }
  };

  const handleTestPersistence = async () => {
    if (!db) {
      console.log('[DatabaseTest] Database connection not available');
      return;
    }

    try {
      // 插入一个带时间戳的测试记录
      const timestamp = new Date().toISOString();
      const testName = `persistence_test_${timestamp}`;

      await db.execute(`
        INSERT INTO test_table (name) VALUES ('${testName}');
      `);

      // 强制保存到存储
      const jeepSqliteEl = document.querySelector('jeep-sqlite');
      if (jeepSqliteEl) {
        await (jeepSqliteEl as any).saveToStore(dbName);
        console.log('[DatabaseTest] Database saved to store for persistence test');
      }

      alert(
        `持久化测试记录已创建: ${testName}\n\n请刷新页面，然后点击"测试查询"查看数据是否仍然存在。`
      );
    } catch (error) {
      console.error('[DatabaseTest] Persistence test error:', error);
      alert(`持久化测试失败: ${error}`);
    }
  };

  const handleResetDatabase = async () => {
    if (!confirm('确定要重置数据库吗？这将删除所有数据并重新初始化。')) {
      return;
    }

    try {
      console.log('[DatabaseTest] Starting database reset...');

      // 1. 关闭当前连接
      if (db) {
        await db.close();
        setDb(null);
      }

      // 2. 删除 IndexedDB 中的数据库
      if ('indexedDB' in window) {
        const deleteRequest = indexedDB.deleteDatabase('jeep-sqlite');
        await new Promise((resolve, reject) => {
          deleteRequest.onsuccess = () => {
            console.log('[DatabaseTest] IndexedDB database deleted successfully');
            resolve(true);
          };
          deleteRequest.onerror = () => {
            console.error('[DatabaseTest] Error deleting IndexedDB database');
            reject(new Error('Failed to delete IndexedDB database'));
          };
        });
      }

      // 3. 清除相关的 localStorage 数据
      localStorage.removeItem('jeep-sqlite-store');
      localStorage.removeItem('database-initialized');

      // 4. 重新初始化 jeep-sqlite
      const jeepSqliteEl = document.querySelector('jeep-sqlite');
      if (jeepSqliteEl) {
        await (jeepSqliteEl as any).initWebStore();
        console.log('[DatabaseTest] Web store reinitialized');
      }

      alert('数据库已重置！页面将在3秒后自动刷新以重新初始化数据库。');

      // 5. 延迟刷新页面
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } catch (error) {
      console.error('[DatabaseTest] Database reset error:', error);
      alert(`数据库重置失败: ${error}`);
    }
  };

  const handleResetVersion = async () => {
    if (!db) {
      alert('数据库连接不可用');
      return;
    }

    if (!confirm('确定要重置数据库版本号吗？这将触发数据重新初始化。')) {
      return;
    }

    try {
      console.log('[DatabaseTest] Resetting database version...');

      // 重置版本号为 0
      await db.execute('PRAGMA user_version = 0');

      alert('数据库版本号已重置为 0！页面将在3秒后自动刷新以触发重新初始化。');

      // 3秒后自动刷新页面
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } catch (error) {
      console.error('[DatabaseTest] Version reset error:', error);
      alert(`版本重置失败: ${error}`);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">数据库测试</h1>

      <div className="space-y-4">
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">数据库状态</h2>
          <p>
            <strong>数据库名称:</strong> {dbName}
          </p>
          <p>
            <strong>初始化状态:</strong> {isInitialized ? '已完成' : '未完成'}
          </p>
          <p>
            <strong>初始化中:</strong> {isInitializing ? '是' : '否'}
          </p>
          <p>
            <strong>连接状态:</strong> {db ? '已连接' : '未连接'}
          </p>
          <p>
            <strong>平台:</strong> {platform}
          </p>
          {error && (
            <p className="text-red-600">
              <strong>错误:</strong> {error}
            </p>
          )}
        </div>

        <div className="bg-blue-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">同步状态</h2>
          <p>
            <strong>同步状态:</strong> {syncStatus.isSyncing ? '同步中' : '空闲'}
          </p>
          <p>
            <strong>上次同步:</strong>{' '}
            {lastSyncTime ? new Date(lastSyncTime).toLocaleString() : '从未同步'}
          </p>
          <p>
            <strong>待同步项目:</strong> {pendingItems.length} 个
          </p>
          <p>
            <strong>同步进度:</strong> {syncStatus.progress}%
          </p>
          {syncStatus.currentItem && (
            <p>
              <strong>当前同步:</strong> {syncStatus.currentItem.type} - {syncStatus.currentItem.id}
            </p>
          )}
          {syncStatus.error && (
            <p className="text-red-600">
              <strong>同步错误:</strong> {syncStatus.error}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <button
            onClick={handleCheckDatabaseList}
            className="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            检查数据库列表
          </button>

          <button
            onClick={handleCheckTables}
            disabled={!isInitialized || !db}
            className="w-full bg-indigo-500 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded"
          >
            检查表结构
          </button>

          <button
            onClick={handleTestQuery}
            disabled={!isInitialized || !db}
            className="w-full bg-green-500 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded"
          >
            测试数据查询
          </button>

          <button
            onClick={handleTestSync}
            disabled={!isInitialized || syncStatus.isSyncing}
            className="w-full bg-orange-500 hover:bg-orange-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded"
          >
            测试同步功能
          </button>

          <button
            onClick={handleForceSync}
            disabled={!isInitialized || syncInProgress}
            className="w-full bg-red-500 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded"
          >
            强制同步
          </button>

          <button
            onClick={handleInsertData}
            disabled={!isInitialized || !db}
            className="w-full bg-purple-500 hover:bg-purple-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded"
          >
            插入测试数据
          </button>

          <button
            onClick={handleTestPersistence}
            disabled={!isInitialized || !db}
            className="w-full bg-yellow-500 hover:bg-yellow-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded"
          >
            测试数据持久化
          </button>

          <button
            onClick={handleResetDatabase}
            className="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
          >
            🔄 重置数据库
          </button>

          <button
            onClick={handleResetVersion}
            className="w-full bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded"
          >
            🔢 重置版本号
          </button>
        </div>

        <div className="bg-yellow-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">测试说明:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>使用新的 DatabaseProvider 和整合的 DatabaseService</li>
            <li>自动加载 full.sql 架构和 init.sql 数据</li>
            <li>按照 master.sql 定义的顺序加载所有数据文件</li>
            <li>
              <strong>数据库功能：</strong>检查表结构和测试数据查询
            </li>
            <li>
              <strong>同步功能：</strong>测试数据同步和强制同步功能
            </li>
            <li>
              <strong>持久化：</strong>Web平台自动调用 saveToStore() 确保数据持久化
            </li>
            <li>检查浏览器控制台查看详细的初始化和同步日志</li>
          </ul>
        </div>

        <div className="bg-blue-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">saveToStore() 的作用:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>
              <strong>数据持久化：</strong>将内存中的 SQLite 数据库保存到浏览器的 IndexedDB
            </li>
            <li>
              <strong>页面刷新保留：</strong>确保数据在页面刷新后仍然存在
            </li>
            <li>
              <strong>老版本缺失：</strong>这就是为什么老版本数据可能在刷新后丢失的原因
            </li>
            <li>
              <strong>Web 平台必需：</strong>在 Web 浏览器中，这是数据持久化的关键步骤
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default DatabaseTest;
