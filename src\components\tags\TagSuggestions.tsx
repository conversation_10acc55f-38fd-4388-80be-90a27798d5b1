import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useLanguage } from '@/contexts/LanguageContext';
import { useLocalTagsData } from '@/hooks/useLocalTagsData';
import type { Emotion } from '@/types/emotionDataTypes';
import type { Tag } from '@/types/tagTypes';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';

interface TagSuggestionsProps {
  selectedTags: string[];
  onChange: (tags: string[]) => void;
  emotion?: Emotion | null;
  className?: string;
}

export const TagSuggestions = ({
  selectedTags,
  onChange,
  emotion,
  className,
}: TagSuggestionsProps) => {
  const { t } = useLanguage();
  const { commonTags, suggestedTags, isLoading, error } = useLocalTagsData();
  const [newTag, setNewTag] = useState('');

  // 处理标签选择
  const handleTagSelect = (tagName: string) => {
    if (!selectedTags.includes(tagName)) {
      onChange([...selectedTags, tagName]);
    }
  };

  // 处理标签移除
  const handleTagRemove = (tagName: string) => {
    onChange(selectedTags.filter((tag) => tag !== tagName));
  };

  // 处理新标签添加
  const handleAddNewTag = () => {
    const trimmedTag = newTag.trim();
    if (trimmedTag && !selectedTags.includes(trimmedTag)) {
      onChange([...selectedTags, trimmedTag]);
      setNewTag('');
    }
  };

  // 处理回车键添加标签
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddNewTag();
    }
  };

  if (isLoading) {
    return <div className="text-sm text-muted-foreground">{t('common.loading')}</div>;
  }

  if (error) {
    return <p className="text-red-500 text-sm">{t('mood.error_loading_tags')}</p>;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 已选择的标签 */}
      {selectedTags.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">{t('mood.selected_tags')}</h3>
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tagName) => (
              <motion.div
                key={tagName}
                className="flex items-center gap-1 px-3 py-1 text-sm rounded-full bg-primary text-primary-foreground"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
              >
                #{tagName}
                <button
                  onClick={() => handleTagRemove(tagName)}
                  className="ml-1 hover:bg-primary-foreground/20 rounded-full p-0.5"
                >
                  <X size={12} />
                </button>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* 添加新标签 */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-muted-foreground">{t('mood.add_new_tag')}</h3>
        <div className="flex gap-2">
          <Input
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={t('mood.tag_placeholder')}
            className="flex-1"
          />
          <Button onClick={handleAddNewTag} disabled={!newTag.trim()} size="sm">
            {t('common.add')}
          </Button>
        </div>
      </div>

      {/* 常用标签 */}
      {commonTags && commonTags.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">{t('mood.common_tags')}</h3>
          <div className="flex flex-wrap gap-2">
            {commonTags.map((tag: Tag) => (
              <motion.button
                key={tag.id}
                onClick={() => handleTagSelect(tag.name)}
                disabled={selectedTags.includes(tag.name)}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${
                  selectedTags.includes(tag.name)
                    ? 'bg-muted text-muted-foreground cursor-not-allowed'
                    : 'bg-muted hover:bg-primary/10'
                }`}
                whileHover={selectedTags.includes(tag.name) ? {} : { scale: 1.05 }}
                whileTap={selectedTags.includes(tag.name) ? {} : { scale: 0.95 }}
              >
                #{tag.name}
              </motion.button>
            ))}
          </div>
        </div>
      )}

      {/* 推荐标签 */}
      {suggestedTags && suggestedTags.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">{t('mood.suggested_tags')}</h3>
          <div className="flex flex-wrap gap-2">
            {suggestedTags.map((tag: Tag) => (
              <motion.button
                key={tag.id}
                onClick={() => handleTagSelect(tag.name)}
                disabled={selectedTags.includes(tag.name)}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${
                  selectedTags.includes(tag.name)
                    ? 'bg-muted text-muted-foreground cursor-not-allowed'
                    : 'bg-primary/10 hover:bg-primary/20'
                }`}
                whileHover={selectedTags.includes(tag.name) ? {} : { scale: 1.05 }}
                whileTap={selectedTags.includes(tag.name) ? {} : { scale: 0.95 }}
              >
                #{tag.name}
              </motion.button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
