-- =============================================
-- UI Labels & Translations Seed Data
-- =============================================

-- Populate ui_labels table (using English as default)
INSERT OR IGNORE INTO ui_labels (key, default_text) VALUES
    ('app.title', 'Mindful Mood'),
    ('app.home', 'Home'),
    ('app.history', 'History'),
    ('app.analytics', 'Analytics'),
    ('app.settings', 'Settings'),
    ('app.loading', 'Loading application...'),

    -- Navigation labels
    ('nav.home', 'Home'),
    ('nav.history', 'History'),
    ('nav.analytics', 'Analytics'),
    ('nav.settings', 'Settings'),
    ('mood.log', 'Log Mood'),
    ('mood.how_feeling', 'How are you feeling today?'),
    ('mood.select_primary', 'Select primary emotion'),
    ('mood.select_secondary', 'Select secondary emotion'),
    ('mood.select_tertiary', 'Select tertiary emotion'),
    ('mood.intensity', 'Intensity'),
    ('mood.tags', 'Tags'),
    ('mood.add_tags', 'Add tags'),
    ('mood.tags_hint', 'Separate with commas'),
    ('mood.reflection', 'Reflection'),
    ('mood.add_reflection', 'Add reflection (optional)'),
    ('mood.save', 'Save Entry'),
    ('mood.save_success', 'Mood saved successfully'),
    ('mood.select_error', 'Please select at least primary and secondary emotions'),
    ('mood.select_error_all_tiers', 'Please select primary, secondary, and tertiary emotions before saving.'),
    ('mood.tags_too_long', 'Tag length cannot exceed 20 characters'),
    ('mood.reflection_too_long', 'Reflection cannot exceed 500 characters'),
    ('mood.validation_error', 'Please check your input'),
    ('mood.draft_saved', 'Draft saved'),
    ('mood.draft_loaded', 'Previous draft loaded'),
    ('mood.common_tags', 'Common Tags'),
    ('mood.suggested_tags', 'Suggested Tags'),
    ('mood.tag_trends', 'Tag Trends'),
    ('mood.tag_trend_up', 'Up'),
    ('mood.tag_trend_down', 'Down'),
    ('mood.tag_trend_stable', 'Stable'),
    ('mood.tag_suggestions', 'Smart Suggestions'),
    ('mood.tag_usage', 'Usage Count'),
    ('mood.tag_recent', 'Recently Used'),
    ('mood.tag_popular', 'Popular Tags'),
    ('mood.remove_primary', 'Remove primary emotion selection and start over'),
    ('mood.remove_secondary', 'Remove secondary emotion selection and go back to secondary choice'),
    ('mood.remove_tertiary', 'Remove tertiary emotion selection and choose a different tertiary emotion'),
    ('mood.error_loading_tags', 'Error loading tags.'),
    ('history.title', 'Mood History'),
    ('history.filter', 'Filter'),
    ('history.timeline', 'Timeline'),
    ('history.calendar', 'Calendar'),
    ('history.no_entries', 'No entries yet'),
    ('history.empty_state', 'Your mood entries will appear here'),
    ('history.today', 'Today'),
    ('history.yesterday', 'Yesterday'),
    ('history.details', 'Details'),
    ('analytics.title', 'Insights & Analytics'),
    ('analytics.no_data', 'No data to analyze yet'),
    ('analytics.empty_state', 'Start tracking your moods to see analytics'),
    ('analytics.total_entries', 'Total Entries'),
    ('analytics.streak', 'Current Streak'),
    ('analytics.top_emotion', 'Top Emotion'),
    ('analytics.avg_intensity', 'Avg. Intensity'),
    ('analytics.emotions_distribution', 'Emotions Distribution'),
    ('analytics.weekly_pattern', 'Weekly Pattern'),
    ('analytics.common_tags', 'Most Used Tags'),
    ('analytics.week', 'Week'),
    ('analytics.month', 'Month'),
    ('analytics.year', 'Year'),
    ('analytics.days', 'days'),
    ('analytics.intensity', 'Intensity'),
    ('settings.title', 'Settings'),
    ('settings.language', 'Language'),
    ('settings.language.english', 'English'),
    ('settings.language.chinese', '中文'),
    ('settings.theme', 'Theme'),
    ('settings.theme.light', 'Light'),
    ('settings.theme.dark', 'Dark'),
    ('settings.theme.system', 'System'),
    ('settings.theme_changed', 'Theme updated'),
    ('settings.notifications', 'Notifications'),
    ('settings.notifications.enable', 'Enable Notifications'),
    ('settings.notifications_enabled', 'Notifications enabled'),
    ('settings.notifications_disabled', 'Notifications disabled'),
    ('settings.reminders', 'Daily Reminders'),
    ('settings.reminders.enable', 'Enable Reminders'),
    ('settings.reminders_enabled', 'Daily reminders enabled'),
    ('settings.reminders_disabled', 'Daily reminders disabled'),
    ('settings.reminders.time', 'Reminder Time'),
    ('settings.export', 'Export Data'),
    ('settings.export_data', 'Export as JSON'),
    ('settings.export_success', 'Data exported successfully'),
    ('settings.privacy', 'Privacy Policy'),
    ('settings.about', 'About'),
    ('settings.emotion_style', 'Emotion Navigation Style'),
    ('settings.emotion_style_changed', 'Emotion navigation style updated'),
    ('settings.data_sync', 'Data Sync'),
    ('settings.data_sync_enable_first', 'Please enable online sync first.'),
    ('settings.data_sync_enable_online', 'Enable Online Sync'),
    ('settings.data_sync_syncing', 'Syncing...'),
    ('settings.data_sync_sync_now', 'Sync Now'),
    ('settings.data_sync_syncing_in_progress', 'Synchronization in progress...'),
    ('settings.data_sync_last_sync_status', 'Last Sync'),
    ('settings.data_sync_uploaded', 'Uploaded: {{count}}'),
    ('settings.data_sync_downloaded', 'Downloaded: {{count}}'),
    ('settings.data_sync_updated_by_server', 'Updated by server: {{count}}'),
    ('settings.data_sync_error', 'Sync Error'),
    ('error.not_found', 'Page not found'),
    ('error.go_home', 'Go back home'),
    ('error.go_back', 'Go back'),
    ('error.retry', 'Retry'),
    ('error.server_error', 'Server Error'),
    ('error.network_error', 'Network Error'),
    ('error.network_message', 'There seems to be a problem with your network connection'),
    ('error.auth_error', 'Authentication Error'),
    ('error.auth_message', 'Please log in to access this page'),
    ('error.unknown_error', 'Unknown Error'),
    ('error.unknown_message', 'Something unexpected happened, please try again later'),
    ('error.generic', 'An unexpected error occurred.'),
    ('error.failed_to_save_data', 'Failed to save data.'),
    ('error.failed_to_delete_data', 'Failed to delete data.'),
    ('error.db_not_initialized', 'Database not initialized. Please try again later.'),
    ('gesture.swipe_left', 'Swipe Left'),
    ('gesture.swipe_right', 'Swipe Right'),
    ('gesture.swipe_up', 'Swipe Up'),
    ('gesture.swipe_down', 'Swipe Down'),
    ('gesture.pull_to_refresh', 'Pull to Refresh'),
    ('gesture.refreshing', 'Refreshing...'),
    ('gesture.tap', 'Tap'),
    ('gesture.double_tap', 'Double Tap'),
    ('gesture.long_press', 'Long Press'),
    ('gesture.pinch', 'Pinch'),
    ('gesture.rotate', 'Rotate'),
    ('mood.style.wheel', 'Classic Wheel'),
    ('mood.style.galaxy', 'Galaxy View'),
    ('mood.style.layered', 'Card Layout'),
    ('mood.style.floating', 'Floating Bubbles'),
    ('mood.tier.primary', 'Primary Emotion'),
    ('mood.tier.secondary', 'Secondary Emotion'),
    ('mood.tier.tertiary', 'Tertiary Emotion'),
    ('mood.galaxy.select_planet', 'Select an emotion planet'),
    ('mood.galaxy.select_region', 'Select an emotion region'),
    ('mood.galaxy.select_crystal', 'Select an emotion crystal'),
    ('mood.layered.select_primary', 'How are you feeling?'),
    ('mood.layered.select_secondary', 'More specifically?'),
    ('mood.layered.select_tertiary', 'Choose the exact feeling'),
    ('mood.layered.selected', 'You selected'),
    ('mood.layered.path', 'Your emotion path'),
    ('errors.failed_to_load_emotions', 'Failed to load emotion data. Details: {{details}}'),
    ('errors.failed_to_load_history', 'Failed to load history. Details: {{details}}'),
    ('errors.failed_to_load_analytics', 'Failed to load analytics data. Details: {{details}}'),
    ('errors.failed_to_load_tags', 'Failed to load tags. Details: {{details}}'),
    ('sidebar.toggle', 'Toggle Sidebar'),
    ('settings.toast.language_changed_to_english', 'Language changed to English'),
    ('settings.toast.language_changed_to_chinese', '语言已更改为中文'),
    ('history.sun', 'Sun'),
    ('history.mon', 'Mon'),
    ('history.tue', 'Tue'),
    ('history.wed', 'Wed'),
    ('history.thu', 'Thu'),
    ('history.fri', 'Fri'),
    ('history.sat', 'Sat'),
    ('history.map', 'Map'),
    ('history.map_title', 'Discovered Emotion Regions'),
    ('history.map_empty_title', 'Your Emotion Map Awaits'),
    ('history.map_empty_subtitle', 'Start logging your moods to discover regions on your personal emotion map!'),
    ('history.map_region_explored', 'Region Explored'),
    ('history.map_region_undiscovered', 'Undiscovered'),
    ('history.map_start_logging_prompt', 'Start logging your moods to discover and explore these regions on your personal emotion map!'),

    -- Calendar empty states
    ('history.no_entries_for_selected_date', 'No entries found for this date.'),
    ('history.no_reflection', 'No reflection for this entry.'),
    ('history.no_entries_for_calendar', 'No mood entries to display in calendar.'),
    ('history.empty_state_calendar', 'Record your moods to see them in the calendar.'),
    ('history.entries_for_date', 'Entries for {{date}}'),

    -- Error messages (already defined above)

    -- Settings
    ('settings.emoji_set_title', 'Emoji Set Style'),
    ('settings.loading_emoji_sets', 'Loading emoji sets'),
    ('settings.emoji_set_changed', 'Emoji set updated'),
    ('settings.emoji_set_change_failed', 'Failed to update emoji set'),
    ('settings.no_emoji_sets_available', 'No emoji sets available at the moment.'),
    ('settings.exporting_data', 'Exporting data...'),
    ('settings.export_data_csv', 'Export as CSV'),
    ('settings.exporting_data_csv', 'Exporting CSV...'),
    ('settings.export_error_csv', 'CSV Export Failed'),

    -- TierEditor labels
    ('tier_editor.tiers', 'Emotion Tiers'),
    ('tier_editor.emoji_mapping', 'Emoji Mapping'),
    ('tier_editor.emoji_sets_load_error', 'Failed to load emoji sets'),
    ('tier_editor.no_tiers', 'No tiers'),
    ('tier_editor.create_first', 'Create first tier'),
    ('tier_editor.no_tiers_title', 'Start creating emotion tiers'),
    ('tier_editor.no_tiers_description', 'Emotion tiers are the foundation for building emotion data. You need to create at least one tier before you can add emotions.'),
    ('tier_editor.level_1_short', 'L1'),
    ('tier_editor.level_2_short', 'L2'),
    ('tier_editor.level_3_short', 'L3'),
    ('tier_editor.level_1', 'Level 1 (Primary Emotions)'),
    ('tier_editor.level_2', 'Level 2 (Secondary Emotions)'),
    ('tier_editor.level_3', 'Level 3 (Detailed Emotions)'),
    ('tier_editor.level_1_description', 'Level 1 emotions'),
    ('tier_editor.level_2_description', 'Level 2 emotions'),
    ('tier_editor.level_3_description', 'Level 3 emotions'),
    ('tier_editor.cannot_delete_parent', 'Cannot delete tier with child tiers'),
    ('tier_editor.delete_tier_title', 'Delete Tier'),
    ('tier_editor.delete_tier_description', 'Are you sure you want to delete this tier? This will delete all emotions in this tier and cannot be undone.'),
    ('tier_editor.edit_tier', 'Edit Tier'),
    ('tier_editor.edit_tier_description', 'Modify tier information'),
    ('tier_editor.add_tier', 'Add Tier'),
    ('tier_editor.add_tier_description', 'Create new emotion tier'),
    ('tier_editor.manage_tier', 'Manage Tier'),
    ('tier_editor.manage_tier_description', 'Manage emotions in tier'),
    ('tier_editor.tier_info', 'Tier Information'),
    ('tier_editor.level', 'Level'),
    ('tier_editor.parent', 'Parent Tier'),
    ('tier_editor.name_placeholder', 'Enter tier name'),
    ('tier_editor.select_level', 'Select level'),
    ('tier_editor.select_parent', 'Select parent tier'),
    ('tier_editor.emoji_mapping_description', 'Set corresponding emoji for each emotion in different emoji sets'),
    ('tier_editor.select_emoji_set', 'Select Emoji Set'),
    ('tier_editor.select_emoji_set_placeholder', 'Please select an emoji set'),

    -- Common labels
    ('common.add', 'Add'),
    ('common.edit', 'Edit'),
    ('common.delete', 'Delete'),
    ('common.cancel', 'Cancel'),
    ('common.save', 'Save'),
    ('common.close', 'Close'),
    ('common.manage', 'Manage'),
    ('common.name', 'Name'),
    ('common.loading', 'Loading...'),
    ('common.default', 'Default'),

    -- EmotionEditor labels
    ('emotion_editor.emotions', 'Emotions'),

    -- EmojiMappingEditor labels
    ('emoji_mapping.load_error', 'Failed to load emoji mappings'),
    ('emoji_mapping.save_success', 'Emoji mappings saved successfully'),
    ('emoji_mapping.save_error', 'Failed to save emoji mappings'),
    ('emoji_mapping.loading', 'Loading emoji mappings...'),
    ('emoji_mapping.search_emotions', 'Search emotions...'),
    ('emoji_mapping.no_emotions_found', 'No matching emotions found'),
    ('emoji_mapping.no_emotions', 'No emotions available'),

    -- Additional common labels
    ('common.saving', 'Saving...'),
    ('common.save_all', 'Save All'),

    -- EmotionEditor additional labels
    ('emotion_editor.no_emotions', 'No emotions'),
    ('emotion_editor.create_first', 'Create first emotion'),
    ('emotion_editor.name_placeholder', 'Enter emotion name'),
    ('emotion_editor.emoji', 'Emoji'),
    ('emotion_editor.choose_emoji', 'Choose emoji'),
    ('emotion_editor.color', 'Color'),
    ('emotion_editor.add_emotion', 'Add Emotion'),
    ('emotion_editor.edit_emotion', 'Edit Emotion'),
    ('emotion_editor.delete_emotion_title', 'Delete Emotion'),
    ('emotion_editor.delete_emotion_description', 'Are you sure you want to delete this emotion? This action cannot be undone.'),

    -- EmotionDataEditor labels
    ('emotion_editor.create_data', 'Create Emotion Data'),
    ('emotion_editor.edit_data', 'Edit Emotion Data'),
    ('emotion_editor.new_emotion_data', 'New Emotion Data'),
    ('emotion_editor.from_template', 'From Template'),
    ('emotion_editor.system_default_name_hint', 'System default emotion data name cannot be modified'),
    ('emotion_editor.general', 'General'),
    ('emotion_editor.tiers', 'Tiers'),
    ('emotion_editor.preview', 'Preview'),
    ('emotion_editor.description', 'Description'),
    ('emotion_editor.description_placeholder', 'Enter emotion data description'),
    ('emotion_editor.actions', 'Actions'),
    ('emotion_editor.duplicate', 'Duplicate'),
    ('emotion_editor.export', 'Export'),
    ('emotion_editor.import', 'Import'),
    ('emotion_editor.activate', 'Activate'),
    ('emotion_editor.delete_data', 'Delete Data'),
    ('emotion_editor.import_data', 'Import Data'),
    ('emotion_editor.import_description', 'Import emotion data from JSON'),
    ('emotion_editor.import_placeholder', 'Paste JSON data here...'),
    ('emotion_editor.import_error', 'Import failed'),
    ('emotion_editor.import_success', 'Data imported successfully');