/**
 * Quiz配置 Hook
 * 管理用户的Quiz系统个性化配置
 * 采用离线优先 + 在线同步的混合模式
 */

import { useState, useEffect, useCallback } from 'react';
import { Services } from '../services';
import { trpc } from '../lib/trpc';
import { UserQuizPreferences, QuizPackOverrides, QuizSessionConfig } from '../types/schema/base';
import { UpdateUserQuizPreferencesInput } from '../types/schema/api';
import { useAuth } from './useAuth';
import { useNetworkStatus } from './useNetworkStatus';

export interface QuizPresentationConfig {
  layer0_dataset_presentation: {
    preferred_pack_categories: string[];
    default_difficulty_preference: 'beginner' | 'regular' | 'advanced' | 'expert';
    session_length_preference: 'short' | 'medium' | 'long';
    auto_select_recommended: boolean;
    restore_progress: boolean;
    question_display_fields?: {
      show_question_text: boolean;
      show_question_description: boolean;
      show_question_order: boolean;
      show_progress_indicator: boolean;
      show_answer_options: boolean;
      show_option_descriptions: boolean;
      show_option_icons: boolean;
    };
    interaction_behavior?: {
      auto_advance_after_selection: boolean;
      auto_advance_delay_ms: number;
      allow_answer_change: boolean;
      show_confirmation_dialog: boolean;
    };
    question_management?: {
      selected_quiz_pack_id: string;
      available_quiz_packs: Array<{
        id: string;
        name: string;
        description: string;
        category: string;
        total_questions: number;
      }>;
      question_customization: {
        [quiz_pack_id: string]: {
          enabled_questions: Array<{
            id: string;
            title: string;
            description: string;
            order: number;
            enabled: boolean;
            required: boolean;
          }>;
          custom_order: string[];
        };
      };
    };
  };
  layer1_user_choice: {
    preferred_view_type: 'wheel' | 'card' | 'bubble' | 'list';
    active_skin_id: string;
    color_mode: 'warm' | 'cool' | 'neutral';
    user_level: 'beginner' | 'regular' | 'advanced';
  };
  layer2_rendering_strategy: {
    render_engine_preferences: Record<string, string>;
    performance_mode: 'high_quality' | 'balanced' | 'performance';
    content_display_mode_preferences?: Record<string, string[]>;
    layout_preferences?: Record<string, string>;
    supported_content_types?: {
      text: boolean;
      emoji: boolean;
      image: boolean;
      icon: boolean;
      audio: boolean;
      video: boolean;
      animation: boolean;
      rich_text: boolean;
    };
  };
  layer3_skin_base: {
    selected_skin_id: string;
    colors: Record<string, string>;
    animations: {
      enable_animations: boolean;
      animation_speed: 'slow' | 'normal' | 'fast';
      reduce_motion: boolean;
    };
    available_skins?: Array<{
      id: string;
      name: string;
      description: string;
      preview_image?: string;
      category: string;
    }>;
    fonts?: {
      primary_font: string;
      size_scale: number;
    };
  };
  layer4_view_detail: {
    wheel_config?: {
      container_size: number;
      wheel_radius: number;
      emotion_display_mode: string;
      show_labels: boolean;
      show_emojis: boolean;
      tier_spacing?: number;
      center_radius?: number;
    };
    card_config?: {
      grid_columns: number;
      card_size: string;
      card_spacing: number;
      show_descriptions: boolean;
      hover_effects: boolean;
    };
    bubble_config?: {
      container_width: number;
      container_height: number;
      bubble_size_range: [number, number];
      physics_enabled: boolean;
      collision_detection: boolean;
    };
    list_config?: {
      item_height: number;
      show_icons: boolean;
      show_descriptions: boolean;
      grouping_enabled: boolean;
    };
    emotion_presentation: {
      emotion_grouping_style: string;
      tier_transition_animation: string;
      emoji_mapping?: Record<string, { primary: string; alternatives: string[]; }>;
      color_mapping?: Record<string, string>;
      animation_mapping?: Record<string, string>;
    };
  };
  layer5_accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    keyboard_navigation: boolean;
    voice_guidance: boolean;
  };
}

export interface UseQuizConfigReturn {
  // 状态
  preferences: UserQuizPreferences | null;
  presentationConfig: QuizPresentationConfig | null;
  isLoading: boolean;
  error: string | null;
  isOnline: boolean;
  lastSyncTime: Date | null;
  
  // 操作
  updatePreferences: (updates: UpdateUserQuizPreferencesInput) => Promise<boolean>;
  updatePresentationConfig: (config: Partial<QuizPresentationConfig>) => Promise<boolean>;
  resetToDefault: () => Promise<boolean>;
  syncToCloud: () => Promise<boolean>;
  refreshConfig: () => Promise<void>;
  
  // 会话配置
  generateSessionConfig: (packId: string, sessionId: string) => Promise<QuizSessionConfig | null>;
  getPackOverrides: (packId: string) => Promise<QuizPackOverrides | null>;
  
  // 便捷访问器
  preferredViewType: 'wheel' | 'card' | 'bubble' | 'list';
  colorMode: 'warm' | 'cool' | 'neutral';
  personalizationLevel: number;
  performanceMode: 'high_quality' | 'balanced' | 'performance';
}

export const useQuizConfig = (configName: string = 'default'): UseQuizConfigReturn => {
  const [preferences, setPreferences] = useState<UserQuizPreferences | null>(null);
  const [presentationConfig, setPresentationConfig] = useState<QuizPresentationConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  
  const { user, isAuthenticated } = useAuth();
  const { isOnline } = useNetworkStatus();

  /**
   * 解析展现配置
   */
  const parsePresentationConfig = useCallback((configString: string): QuizPresentationConfig | null => {
    try {
      return JSON.parse(configString);
    } catch {
      return null;
    }
  }, []);

  /**
   * 从本地数据库加载配置
   */
  const loadLocalConfig = useCallback(async () => {
    if (!user?.id) return;

    try {
      const userQuizPreferencesService = await Services.userQuizPreferences();
      const result = await userQuizPreferencesService.getUserPreferences(user.id, configName);
      
      if (result.success && result.data) {
        setPreferences(result.data);
        const config = parsePresentationConfig(result.data.presentation_config);
        setPresentationConfig(config);
        setError(null);
      } else {
        setError(result.error || 'Failed to load quiz config');
      }
    } catch (err) {
      console.error('Error loading local quiz config:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  }, [user?.id, configName, parsePresentationConfig]);

  /**
   * 从云端同步配置
   */
  const syncFromCloud = useCallback(async (): Promise<boolean> => {
    if (!isAuthenticated || !isOnline) return false;

    try {
      const result = await trpc.config.quiz.getUserPreferences.query({ config_name: configName });
      
      if (result.success && result.data) {
        // 更新本地数据库
        const userQuizPreferencesService = await Services.userQuizPreferences();
        await userQuizPreferencesService.updateUserPreferences(user!.id, configName, {
          presentation_config: result.data.presentation_config,
          personalization_level: result.data.personalization_level
        });
        
        setPreferences(result.data);
        const config = parsePresentationConfig(result.data.presentation_config);
        setPresentationConfig(config);
        setLastSyncTime(new Date());
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error syncing quiz config from cloud:', err);
      return false;
    }
  }, [isAuthenticated, isOnline, configName, user, parsePresentationConfig]);

  /**
   * 同步配置到云端
   */
  const syncToCloud = useCallback(async (): Promise<boolean> => {
    if (!isAuthenticated || !isOnline || !preferences) return false;

    try {
      const result = await trpc.config.quiz.updateUserPreferences.mutate({
        config_name: configName,
        presentation_config: preferences.presentation_config,
        personalization_level: preferences.personalization_level
      });
      
      if (result.success) {
        setLastSyncTime(new Date());
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error syncing quiz config to cloud:', err);
      return false;
    }
  }, [isAuthenticated, isOnline, preferences, configName]);

  /**
   * 更新偏好配置
   */
  const updatePreferences = useCallback(async (updates: UpdateUserQuizPreferencesInput): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      setIsLoading(true);
      
      const userQuizPreferencesService = await Services.userQuizPreferences();
      const result = await userQuizPreferencesService.updateUserPreferences(user.id, configName, updates);
      
      if (result.success && result.data) {
        setPreferences(result.data);
        const config = parsePresentationConfig(result.data.presentation_config);
        setPresentationConfig(config);
        setError(null);
        
        // 如果在线，尝试同步到云端
        if (isOnline && isAuthenticated) {
          await syncToCloud();
        }
        
        return true;
      } else {
        setError(result.error || 'Failed to update quiz preferences');
        return false;
      }
    } catch (err) {
      console.error('Error updating quiz preferences:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, configName, parsePresentationConfig, isOnline, isAuthenticated, syncToCloud]);

  /**
   * 更新展现配置
   */
  const updatePresentationConfig = useCallback(async (configUpdates: Partial<QuizPresentationConfig>): Promise<boolean> => {
    if (!presentationConfig) return false;

    const updatedConfig = { ...presentationConfig, ...configUpdates };
    
    return await updatePreferences({
      presentation_config: JSON.stringify(updatedConfig)
    });
  }, [presentationConfig, updatePreferences]);

  /**
   * 重置为默认配置
   */
  const resetToDefault = useCallback(async (): Promise<boolean> => {
    const userQuizPreferencesService = await Services.userQuizPreferences();
    return (await userQuizPreferencesService.resetToDefault(user!.id, configName)).success;
  }, [user, configName]);

  /**
   * 生成会话配置
   */
  const generateSessionConfig = useCallback(async (packId: string, sessionId: string): Promise<QuizSessionConfig | null> => {
    if (!user?.id) return null;

    try {
      const quizConfigMergerService = await Services.quizConfigMerger();
      const result = await quizConfigMergerService.generateSessionConfig(user.id, packId, sessionId);
      
      return result.success ? result.data : null;
    } catch (err) {
      console.error('Error generating session config:', err);
      return null;
    }
  }, [user?.id]);

  /**
   * 获取包覆盖配置
   */
  const getPackOverrides = useCallback(async (packId: string): Promise<QuizPackOverrides | null> => {
    if (!user?.id) return null;

    try {
      const quizPackOverridesService = await Services.quizPackOverrides();
      const result = await quizPackOverridesService.getPackOverrides(user.id, packId);
      
      return result.success ? result.data : null;
    } catch (err) {
      console.error('Error getting pack overrides:', err);
      return null;
    }
  }, [user?.id]);

  /**
   * 刷新配置
   */
  const refreshConfig = useCallback(async () => {
    setIsLoading(true);
    
    const syncSuccess = await syncFromCloud();
    if (!syncSuccess) {
      await loadLocalConfig();
    }
    
    setIsLoading(false);
  }, [syncFromCloud, loadLocalConfig]);

  // 初始化加载
  useEffect(() => {
    if (user?.id) {
      refreshConfig();
    }
  }, [user?.id, refreshConfig]);

  // 网络状态变化时尝试同步
  useEffect(() => {
    if (isOnline && isAuthenticated && preferences) {
      syncToCloud();
    }
  }, [isOnline, isAuthenticated, preferences, syncToCloud]);

  return {
    // 状态
    preferences,
    presentationConfig,
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    
    // 操作
    updatePreferences,
    updatePresentationConfig,
    resetToDefault,
    syncToCloud,
    refreshConfig,
    
    // 会话配置
    generateSessionConfig,
    getPackOverrides,
    
    // 便捷访问器
    preferredViewType: presentationConfig?.layer1_user_choice?.preferred_view_type || 'wheel',
    colorMode: presentationConfig?.layer1_user_choice?.color_mode || 'warm',
    personalizationLevel: preferences?.personalization_level || 50,
    performanceMode: presentationConfig?.layer2_rendering_strategy?.performance_mode || 'balanced'
  };
};
