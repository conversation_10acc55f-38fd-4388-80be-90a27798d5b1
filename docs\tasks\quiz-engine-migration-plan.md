# QuizEngineV3 迁移和架构重构计划

## 🎯 迁移目标

### 1. 文件位置重构
- **当前位置**: `src/services/quiz/QuizEngineV3.ts`
- **目标位置**: `src/services/entities/QuizEngineV3.ts`
- **原因**: 遵循 `src/services/README.md` 的entities目录规范

### 2. 架构对齐
- **替代旧架构**: emotion_data_set tier 管理方式 → quiz_packs/quiz_questions 架构
- **服务端支持**: 支持server端离线存储和处理
- **统一类型**: 使用 `src/types/schema/base.ts` 的统一类型定义

### 3. 服务层集成
- **Repository模式**: 集成现有的Repository层
- **Service模式**: 遵循BaseService架构
- **混合数据访问**: 支持在线/离线混合模式

## 📋 详细迁移计划

### 阶段1: 文件移动和重构 (高优先级)

#### 1.1 移动QuizEngineV3.ts
```bash
# 移动文件
src/services/quiz/QuizEngineV3.ts → src/services/entities/QuizEngineV3.ts

# 更新导入路径
- 所有引用QuizEngineV3的文件
- server/lib/services/QuizService.ts
- 相关测试文件
```

#### 1.2 重构QuizEngineV3架构
```typescript
// 新的QuizEngineV3架构
export class QuizEngineV3 extends BaseService {
  constructor(
    private quizPackRepository: QuizPackRepository,
    private quizSessionRepository: QuizSessionRepository,
    private quizAnswerRepository: QuizAnswerRepository,
    private quizResultRepository: QuizResultRepository,
    config?: Partial<QuizEngineConfig>
  ) {
    super();
    // 初始化逻辑
  }
}
```

#### 1.3 服务端适配
```typescript
// server/lib/services/QuizEngineService.ts (新建)
export class QuizEngineService {
  private quizEngine: QuizEngineV3;
  
  constructor(db: DatabaseInterface) {
    // 使用server端的Repository实例
    this.quizEngine = new QuizEngineV3(
      new QuizPackRepository(db),
      new QuizSessionRepository(db),
      new QuizAnswerRepository(db),
      new QuizResultRepository(db)
    );
  }
}
```

### 阶段2: 旧架构清理 (中优先级)

#### 2.1 emotion_data_set tier 迁移
```sql
-- 数据迁移脚本: migrate_emotion_to_quiz.sql
-- 将 emotion_data_sets → quiz_packs
-- 将 emotion_data_set_tiers → quiz_questions
-- 将 emotions → quiz_question_options
```

#### 2.2 旧文件标记废弃
```typescript
// src/services/entities/EmotionDataSetService.ts
/**
 * @deprecated 使用 QuizPackService 替代
 * emotion_data_set tier 是旧版本的数据管理方式
 */
export class EmotionDataSetService {
  // 保留向后兼容，但标记废弃
}
```

#### 2.3 更新文档
```markdown
# 架构迁移说明
- emotion_data_set → quiz_pack (概念统一)
- tier → question (问题管理)
- emotion → question_option (选项管理)
```

### 阶段3: 服务端离线存储支持 (中优先级)

#### 3.1 Server端Repository层
```typescript
// server/lib/repositories/ (新建目录)
├── QuizPackRepository.ts      # 服务端Quiz包仓储
├── QuizSessionRepository.ts   # 服务端会话仓储
├── QuizAnswerRepository.ts    # 服务端答案仓储
└── QuizResultRepository.ts    # 服务端结果仓储
```

#### 3.2 离线数据处理
```typescript
// server/lib/services/OfflineDataService.ts (新建)
export class OfflineDataService {
  async processOfflineQuizData(data: OfflineQuizData) {
    // 处理离线Quiz数据
    // 验证数据完整性
    // 存储到服务端数据库
    // 返回处理结果
  }
  
  async syncQuizData(clientData: QuizSyncData) {
    // 双向同步Quiz数据
    // 冲突解决
    // 增量更新
  }
}
```

#### 3.3 tRPC路由扩展
```typescript
// server/lib/routers/quiz.ts (扩展)
export const quizRouter = router({
  // 现有路由...
  
  // 离线数据处理
  processOfflineData: publicProcedure
    .input(OfflineQuizDataSchema)
    .mutation(async ({ input, ctx }) => {
      const offlineService = new OfflineDataService(ctx.db);
      return await offlineService.processOfflineQuizData(input);
    }),
    
  // 数据同步
  syncQuizData: publicProcedure
    .input(QuizSyncDataSchema)
    .mutation(async ({ input, ctx }) => {
      const offlineService = new OfflineDataService(ctx.db);
      return await offlineService.syncQuizData(input);
    }),
});
```

## 🏗️ 新架构优势

### 1. 统一概念模型
```typescript
// 旧架构 (废弃)
emotion_data_set {
  tiers: [
    { emotions: [...] }
  ]
}

// 新架构 (推荐)
quiz_pack {
  questions: [
    { options: [...] }
  ]
}
```

### 2. 灵活的问题类型支持
```typescript
// 支持多种Quiz类型
interface QuizQuestion {
  question_type: 
    | 'emotion_wheel'      // 情绪轮盘 (替代旧的emotion选择)
    | 'single_choice'      // 单选题
    | 'multiple_choice'    // 多选题
    | 'scale_rating'       // 量表评分
    | 'text_input'         // 文本输入
    | 'slider'             // 滑块
    | 'matrix'             // 矩阵题
    // ... 更多类型
}
```

### 3. 数据与展现分离
```typescript
// 纯数据存储
quiz_question_options {
  option_text: string;
  option_value: string;
  scoring_value: number;
  // 无展现相关字段
}

// 展现配置分离
quiz_session_presentation_configs {
  presentation_config: JSON; // 6层个性化配置
  session_overrides: JSON;   // 会话特定覆盖
}
```

## 📊 迁移对比

| 方面 | 旧架构 (emotion_data_set tier) | 新架构 (quiz_packs) |
|------|--------------------------------|---------------------|
| 概念模型 | emotion_data_set → tier → emotion | quiz_pack → question → option |
| 数据管理 | 层级式tier管理 | 灵活的问题管理 |
| 问题类型 | 仅支持情绪选择 | 支持多种Quiz类型 |
| 展现配置 | 混合在数据中 | 完全分离 |
| 扩展性 | 受限于emotion概念 | 支持任意Quiz类型 |
| 向后兼容 | N/A | 通过迁移脚本支持 |

## 🔧 实施步骤

### 步骤1: 准备工作
1. **备份数据**: 备份现有emotion_data_set相关数据
2. **创建迁移脚本**: 编写数据迁移SQL脚本
3. **更新类型定义**: 确保统一类型定义完整

### 步骤2: 文件移动
1. **移动QuizEngineV3.ts**: `src/services/quiz/` → `src/services/entities/`
2. **更新导入路径**: 修复所有引用
3. **删除空目录**: 清理 `src/services/quiz/` 目录

### 步骤3: 架构重构
1. **重构QuizEngineV3**: 集成Repository模式
2. **创建服务端支持**: 新建server端Repository和Service
3. **扩展tRPC路由**: 添加离线数据处理端点

### 步骤4: 数据迁移
1. **执行迁移脚本**: 将旧数据迁移到新表结构
2. **验证数据完整性**: 确保迁移无数据丢失
3. **更新应用逻辑**: 使用新的数据访问方式

### 步骤5: 测试验证
1. **单元测试**: 测试新的QuizEngineV3
2. **集成测试**: 测试完整的Quiz流程
3. **数据一致性测试**: 验证离线/在线数据同步

## 📚 相关文档更新

### 需要更新的文档
1. **src/services/README.md**: 添加QuizEngineV3到entities目录说明
2. **docs/quiz/04-database-design.md**: 标记旧架构为废弃
3. **API文档**: 更新Quiz相关API文档
4. **开发者指南**: 添加迁移指南

### 新增文档
1. **docs/quiz/migration-guide.md**: 详细的迁移指南
2. **docs/quiz/new-architecture.md**: 新架构说明
3. **server/README.md**: 服务端Quiz支持说明

---

**迁移完成后**: Quiz系统将完全基于新的统一架构，支持多种Quiz类型，提供更好的扩展性和维护性。
