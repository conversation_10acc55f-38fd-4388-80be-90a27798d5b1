[TursoService] Error executing Turso query: 
Object { sql: "\n        SELECT es.*,\n               est.translated_name,\n               est.translated_description\n        FROM emoji_sets es\n        LEFT JOIN emoji_set_translations est\n          ON es.id = est.emoji_set_id\n          AND est.language_code = ?\n      ", args: (1) […] }
 Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    loadEmojiData EmojiContext.tsx:41
    EmojiProvider EmojiContext.tsx:58
    React 47
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
tursoService.ts:109:17
[DataAccessService] 执行查询失败: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    loadEmojiData EmojiContext.tsx:41
    EmojiProvider EmojiContext.tsx:58
    React 47
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
dataAccessService.ts:107:21
Failed to load emoji sets from database: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    loadEmojiData EmojiContext.tsx:41
    EmojiProvider EmojiContext.tsx:58
    React 47
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
emojiService.ts:275:17
[TursoService] Error executing Turso query: 
Object { sql: "\n        SELECT es.*,\n               est.translated_name,\n               est.translated_description\n        FROM emoji_sets es\n        LEFT JOIN emoji_set_translations est\n          ON es.id = est.emoji_set_id\n          AND est.language_code = ?\n      ", args: (1) […] }
 Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    getActiveEmojiSet emojiService.ts:433
    loadEmojiData EmojiContext.tsx:42
    EmojiProvider EmojiContext.tsx:58
    React 47
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
tursoService.ts:109:17
[DataAccessService] 执行查询失败: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    getActiveEmojiSet emojiService.ts:433
    loadEmojiData EmojiContext.tsx:42
    EmojiProvider EmojiContext.tsx:58
    React 47
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
dataAccessService.ts:107:21
Failed to load emoji sets from database: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    getActiveEmojiSet emojiService.ts:433
    loadEmojiData EmojiContext.tsx:42
    EmojiProvider EmojiContext.tsx:58
    React 47
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
emojiService.ts:275:17
[TursoService] Error executing Turso query: 
Object { sql: "\n        SELECT es.*,\n               est.translated_name,\n               est.translated_description\n        FROM emoji_sets es\n        LEFT JOIN emoji_set_translations est\n          ON es.id = est.emoji_set_id\n          AND est.language_code = ?\n      ", args: (1) […] }
 Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    loadEmojiData EmojiContext.tsx:41
    EmojiProvider EmojiContext.tsx:58
    React 49
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
tursoService.ts:109:17
[DataAccessService] 执行查询失败: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    loadEmojiData EmojiContext.tsx:41
    EmojiProvider EmojiContext.tsx:58
    React 49
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
dataAccessService.ts:107:21
Failed to load emoji sets from database: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    loadEmojiData EmojiContext.tsx:41
    EmojiProvider EmojiContext.tsx:58
    React 49
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
emojiService.ts:275:17
[TursoService] Error executing Turso query: 
Object { sql: "\n        SELECT es.*,\n               est.translated_name,\n               est.translated_description\n        FROM emoji_sets es\n        LEFT JOIN emoji_set_translations est\n          ON es.id = est.emoji_set_id\n          AND est.language_code = ?\n      ", args: (1) […] }
 Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    getActiveEmojiSet emojiService.ts:433
    loadEmojiData EmojiContext.tsx:42
    EmojiProvider EmojiContext.tsx:58
    React 49
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
tursoService.ts:109:17
[DataAccessService] 执行查询失败: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    getActiveEmojiSet emojiService.ts:433
    loadEmojiData EmojiContext.tsx:42
    EmojiProvider EmojiContext.tsx:58
    React 49
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
dataAccessService.ts:107:21
Failed to load emoji sets from database: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    getAvailableEmojiSets emojiService.ts:280
    getActiveEmojiSet emojiService.ts:433
    loadEmojiData EmojiContext.tsx:42
    EmojiProvider EmojiContext.tsx:58
    React 49
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
emojiService.ts:275:17
已拦截跨源请求：同源策略禁止读取位于 https://local/v2/pipeline 的远程资源。（原因：CORS 请求未能成功）。状态码：(null)。 2
No active emotion data set found Home.tsx:405:17
No active emotion data set found Home.tsx:405:17
已拦截跨源请求：同源策略禁止读取位于 https://local/v2/pipeline 的远程资源。（原因：CORS 请求未能成功）。状态码：(null)。 6
[TursoService] Error executing Turso query: 
Object { sql: "\n          SELECT\n            ed.id, ed.name, ed.description, ed.created_at, ed.updated_at,\n            ed.is_system, ed.is_default, ed.default_emoji_set\n          FROM emotion_data_sets ed\n          WHERE ed.is_system = 1\n          LIMIT 1\n        " }
 Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    initializeSystemDefaultData emotionDataManager.ts:169
    fetchEmotionDataSets useEmotionDataSets.ts:43
    useEmotionDataSets useEmotionDataSets.ts:85
    React 3
tursoService.ts:109:17
[DataAccessService] 执行查询失败: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    initializeSystemDefaultData emotionDataManager.ts:169
    fetchEmotionDataSets useEmotionDataSets.ts:43
    useEmotionDataSets useEmotionDataSets.ts:85
    React 3
dataAccessService.ts:107:21
[EmotionDataManager] Failed to initialize system default emotion data: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    initializeSystemDefaultData emotionDataManager.ts:169
    fetchEmotionDataSets useEmotionDataSets.ts:43
    useEmotionDataSets useEmotionDataSets.ts:85
    React 3
emotionDataManager.ts:294:21
[TursoService] Error executing Turso query: 
Object { sql: "\n          SELECT\n            ed.id, ed.name, ed.description, ed.created_at, ed.updated_at,\n            ed.is_system, ed.is_default, ed.default_emoji_set\n          FROM emotion_data_sets ed\n          WHERE ed.is_system = 1\n          LIMIT 1\n        " }
 Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    initializeSystemDefaultData emotionDataManager.ts:169
    fetchEmotionDataSets useEmotionDataSets.ts:43
    useEmotionDataSets useEmotionDataSets.ts:85
    React 3
tursoService.ts:109:17
[DataAccessService] 执行查询失败: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    initializeSystemDefaultData emotionDataManager.ts:169
    fetchEmotionDataSets useEmotionDataSets.ts:43
    useEmotionDataSets useEmotionDataSets.ts:85
    React 3
dataAccessService.ts:107:21
[EmotionDataManager] Failed to initialize system default emotion data: Error: [TursoService] Query timeout after 5 seconds
    timeoutPromise tursoService.ts:126
    setTimeout handler*executeTursoQuery/timeoutPromise< tursoService.ts:125
    executeTursoQuery tursoService.ts:124
    executeQuery dataAccessService.ts:123
    initializeSystemDefaultData emotionDataManager.ts:169
    fetchEmotionDataSets useEmotionDataSets.ts:43
    useEmotionDataSets useEmotionDataSets.ts:85
    React 3
emotionDataManager.ts:294:21
Uncaught Error: Objects are not valid as a React child (found: object with keys {}). If you meant to render a collection of children, use an array instead.
    React 14
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    React 2
    __require chunk-WG7MCETX.js:20
    dom React
    __require chunk-WG7MCETX.js:20
    <anonymous> Html.js:3
chunk-FHUZ6L5V.js:5440:15
已拦截跨源请求：同源策略禁止读取位于 https://local/v2/pipeline 的远程资源。（原因：CORS 请求未能成功）。状态码：(null)。 4
