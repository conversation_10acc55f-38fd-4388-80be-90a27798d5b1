import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useLanguage, Language } from "@/contexts/LanguageContext";
import { useTheme } from "@/contexts/ThemeContext";
import { useGlobalConfig } from "@/hooks/useGlobalConfig";
import { useDataSync } from "@/hooks/useDataSync";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronRight, User, Palette, Globe, BellRing,
  Shield, Database, Cloud, Download, Moon, Sun,
  Monitor, Eye, Crown, Brain
} from "lucide-react";
import { toast } from "sonner";
import { PageTransition } from "@/components/ui/transition";
import type { ColorMode } from "@/types";
import { useAuth } from "@/hooks/useAuth";
import { useVip } from "@/hooks/useVip";

// 移动端优化组件
import MobileSettingsHeader from "@/components/settings/mobile/MobileSettingsHeader";

const Settings = () => {
  const { t, language, setLanguage } = useLanguage();
  const { theme, setTheme } = useTheme();
  const {
    config: globalConfig,
    isLoading: configLoading,
    error: configError,
    updateConfig,
    themeMode,
    language: configLanguage,
    notificationsEnabled,
    soundEnabled: configSoundEnabled,
    accessibilityConfig,
    isOnline,
    lastSyncTime
  } = useGlobalConfig();
  const { syncStatus, performSync } = useDataSync();
  const { isVip, vipStatus } = useAuth();

  // 本地状态
  const [notifications, setNotifications] = useState(notificationsEnabled);
  const [soundEnabled, setSoundEnabled] = useState(configSoundEnabled);

  // 同步配置状态到本地状态
  useEffect(() => {
    setNotifications(notificationsEnabled);
    setSoundEnabled(configSoundEnabled);
  }, [notificationsEnabled, configSoundEnabled]);

  // 简化的用户类型检测：现在直接使用useAuth的isVip
  const userType = isVip ? 'vip' : 'regular';

  // 设置页面不再需要加载Quiz相关数据，这些都在QuizSettings中处理

  // 第1层：基础选择层处理函数
  const handleLanguageChange = async (lang: Language) => {
    setLanguage(lang);

    // 同时更新全局配置
    await updateConfig({
      language: lang === 'en' ? 'en-US' : 'zh-CN'
    });

    const successMessage = lang === "en"
      ? "Language changed to English"
      : "语言已更改为中文";
    toast.success(successMessage);
  };

  const handleThemeChange = async (newTheme: "light" | "dark" | "system") => {
    setTheme(newTheme);

    // 同时更新全局配置
    await updateConfig({
      theme_mode: newTheme
    });

    toast.success(t('settings.theme_changed') || '主题已更改');
  };

  // Quiz相关的配置函数已移至QuizSettings页面

  // 第5层：可访问性增强层处理函数
  const handleAccessibilityChange = async (setting: string, value: boolean) => {
    try {
      const updatedAccessibility = { ...accessibilityConfig, [setting]: value };

      const success = await updateConfig({
        accessibility: JSON.stringify(updatedAccessibility)
      });

      if (success) {
        // 应用CSS类
        if (setting === 'high_contrast') {
          if (value) {
            document.documentElement.classList.add('high-contrast');
          } else {
            document.documentElement.classList.remove('high-contrast');
          }
        } else if (setting === 'large_text') {
          if (value) {
            document.documentElement.classList.add('large-text');
          } else {
            document.documentElement.classList.remove('large-text');
          }
        } else if (setting === 'reduce_motion') {
          if (value) {
            document.documentElement.classList.add('reduce-motion');
          } else {
            document.documentElement.classList.remove('reduce-motion');
          }
        }

        toast.success(t(`settings.${setting}_changed`) || '无障碍设置已更改');
      } else {
        toast.error(t('settings.update_failed') || '设置更新失败');
      }
    } catch (error) {
      console.error('Failed to update accessibility setting:', error);
      toast.error(t('settings.update_failed') || '设置更新失败');
    }
  };



  // 视图类型更改已移至QuizSettings页面

  // 处理通知设置
  const handleNotificationToggle = async (enabled: boolean) => {
    setNotifications(enabled);

    if (enabled) {
      if ('Notification' in window) {
        const permission = await Notification.requestPermission();
        if (permission === 'granted') {
          await updateConfig({ notifications_enabled: enabled });
          toast.success(t('settings.notifications_enabled') || '通知已启用');
        } else {
          toast.error(t('settings.notifications_denied') || '通知权限被拒绝');
          setNotifications(false);
        }
      }
    } else {
      await updateConfig({ notifications_enabled: enabled });
      toast.success(t('settings.notifications_disabled') || '通知已禁用');
    }
  };

  // 处理音效设置
  const handleSoundToggle = async (enabled: boolean) => {
    setSoundEnabled(enabled);

    await updateConfig({ sound_enabled: enabled });

    toast.success(enabled ?
      (t('settings.sound_enabled') || '音效已启用') :
      (t('settings.sound_disabled') || '音效已禁用')
    );
  };

  // 获取当前无障碍设置
  const getAccessibilitySetting = (setting: string): boolean => {
    return accessibilityConfig[setting] || false;
  };

  return (
    <PageTransition>
      <div className="min-h-screen bg-background pb-20">
        {/* 移动端标题栏 */}
        <MobileSettingsHeader userType={userType} />

        {/* 设置内容 */}
        <div className="max-w-[550px] mx-auto px-4 py-4 space-y-6">

          {/* 个人资料卡片 */}
          <Card>
            <CardContent className="p-4 space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">用户</h3>
                  <p className="text-sm text-muted-foreground">
                    {globalConfig?.created_at ?
                      `使用 ${Math.floor((Date.now() - new Date(globalConfig.created_at).getTime()) / (1000 * 60 * 60 * 24))} 天` :
                      '新用户'
                    }
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  {/* 用户类型徽章 */}
                  <Badge
                    variant={userType === 'vip' ? 'destructive' : 'outline'}
                    className="flex items-center space-x-1"
                  >
                    {userType === 'vip' && <Crown className="h-3 w-3" />}
                    <span>
                      {userType === 'vip' ? 'VIP' : '普通用户'}
                    </span>
                  </Badge>
                </div>
              </div>

              {/* VIP状态切换（仅用于演示） */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">VIP功能</Label>
                    <p className="text-xs text-muted-foreground">
                      {isVip ?
                        'VIP用户可以访问Quiz系统设置和高级功能' :
                        '升级到VIP以解锁Quiz系统设置和更多功能'
                      }
                    </p>
                  </div>
                  {/* 如果您想添加一个跳转到VIP页面的按钮，可以在这里添加 */}
                  <Link to="/vip">
                    <Button variant="outline" size="sm">
                      {isVip ? '管理VIP' : '升级VIP'}
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 外观设置卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                外观设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 主题模式 */}
              <div className="space-y-2">
                <Label htmlFor="theme-mode">主题模式</Label>
                <Select
                  value={themeMode || theme}
                  onValueChange={handleThemeChange}
                >
                  <SelectTrigger id="theme-mode">
                    <SelectValue placeholder="选择主题" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">
                      <div className="flex items-center gap-2">
                        <Sun className="h-4 w-4" />
                        <span>亮色模式</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="dark">
                      <div className="flex items-center gap-2">
                        <Moon className="h-4 w-4" />
                        <span>暗色模式</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="system">
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        <span>跟随系统</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 语言设置 */}
              <div className="space-y-2">
                <Label htmlFor="language-select">语言</Label>
                <Select
                  value={language}
                  onValueChange={(value: Language) => handleLanguageChange(value)}
                >
                  <SelectTrigger id="language-select">
                    <SelectValue placeholder="选择语言" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="zh">中文</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* 通知与音效卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BellRing className="h-5 w-5" />
                通知与音效
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 通知 */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifications-toggle">通知</Label>
                  <p className="text-sm text-muted-foreground">
                    接收应用通知和提醒
                  </p>
                </div>
                <Switch
                  id="notifications-toggle"
                  checked={notifications}
                  onCheckedChange={handleNotificationToggle}
                />
              </div>
              {/* 音效 */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sound-toggle">音效</Label>
                  <p className="text-sm text-muted-foreground">
                    启用应用内的声音效果
                  </p>
                </div>
                <Switch
                  id="sound-toggle"
                  checked={soundEnabled}
                  onCheckedChange={handleSoundToggle}
                />
              </div>
            </CardContent>
          </Card>

          {/* 无障碍设置卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                无障碍设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="high-contrast-toggle">高对比度模式</Label>
                  <p className="text-sm text-muted-foreground">
                    增强文本和元素的对比度，提高可读性
                  </p>
                </div>
                <Switch
                  id="high-contrast-toggle"
                  checked={getAccessibilitySetting('high_contrast')}
                  onCheckedChange={(checked) => handleAccessibilityChange('high_contrast', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="large-text-toggle">大字体</Label>
                  <p className="text-sm text-muted-foreground">
                    增加应用内所有文本的大小
                  </p>
                </div>
                <Switch
                  id="large-text-toggle"
                  checked={getAccessibilitySetting('large_text')}
                  onCheckedChange={(checked) => handleAccessibilityChange('large_text', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="reduce-motion-toggle">减少动画</Label>
                  <p className="text-sm text-muted-foreground">
                    禁用或减少界面动画效果，缓解视觉不适
                  </p>
                </div>
                <Switch
                  id="reduce-motion-toggle"
                  checked={getAccessibilitySetting('reduce_motion')}
                  onCheckedChange={(checked) => handleAccessibilityChange('reduce_motion', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="keyboard-navigation-toggle">键盘导航</Label>
                  <p className="text-sm text-muted-foreground">
                    启用通过键盘进行所有界面操作
                  </p>
                </div>
                <Switch
                  id="keyboard-navigation-toggle"
                  checked={getAccessibilitySetting('keyboard_navigation')}
                  onCheckedChange={(checked) => handleAccessibilityChange('keyboard_navigation', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="voice-guidance-toggle">语音指导</Label>
                  <p className="text-sm text-muted-foreground">
                    为视觉障碍用户提供语音提示和导航
                  </p>
                </div>
                <Switch
                  id="voice-guidance-toggle"
                  checked={getAccessibilitySetting('voice_guidance')}
                  onCheckedChange={(checked) => handleAccessibilityChange('voice_guidance', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* 数据与同步卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                数据与同步
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>同步状态</Label>
                  <p className="text-sm text-muted-foreground">
                    {syncStatus === 'synced' ? '您的数据已同步到云端' : '数据正在同步中或需要同步'}
                  </p>
                </div>
                <Badge variant={syncStatus === 'synced' ? 'default' : 'secondary'}>
                  {syncStatus === 'synced' ? '已同步' : '未同步'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <Label>上次同步</Label>
                <span className="text-sm text-muted-foreground">
                  {lastSyncTime ? new Date(lastSyncTime).toLocaleString() : 'N/A'}
                </span>
              </div>
              <Button
                onClick={performSync}
                className="w-full"
                disabled={syncStatus === 'syncing'}
              >
                <Cloud className="h-4 w-4 mr-2" />
                {syncStatus === 'syncing' ? '同步中...' : '立即同步数据'}
              </Button>
            </CardContent>
          </Card>

          {/* Quiz系统设置入口 */}
          <Card>
            <CardContent className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Brain className="h-6 w-6 text-orange-500" />
                <h3 className="font-medium">Quiz系统设置</h3>
              </div>
              <Link to="/quiz-settings">
                <Button variant="ghost" size="icon">
                  <ChevronRight className="h-5 w-5" />
                </Button>
              </Link>
            </CardContent>
          </Card>

        </div>
      </div>
    </PageTransition>
  );
};

export default Settings;
