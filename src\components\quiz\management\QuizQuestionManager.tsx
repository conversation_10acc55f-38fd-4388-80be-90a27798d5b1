/**
 * Quiz问题管理组件
 * 用于管理Quiz问题的创建、编辑、删除等操作
 */

import React, { useState } from 'react';
import { QuizPack, QuizQuestion } from '@/types/schema/base';
import { Services } from '@/services';
import { toast } from 'sonner';
import { useLanguage } from '@/contexts/LanguageContext';

interface QuizQuestionManagerProps {
  quizPack: QuizPack;
  questions: QuizQuestion[];
  onQuestionSelect: (question: QuizQuestion) => void;
  onDataUpdate: () => void;
}

interface QuestionFormData {
  question_text: string;
  question_type: string;
  question_order: number;
  question_group: string;
  tier_level: number;
  is_required: boolean;
  is_active: boolean;
}

const QuizQuestionManager: React.FC<QuizQuestionManagerProps> = ({
  quizPack,
  questions,
  onQuestionSelect,
  onDataUpdate
}) => {
  const { t } = useLanguage();
  const [isCreating, setIsCreating] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(null);
  const [formData, setFormData] = useState<QuestionFormData>({
    question_text: '',
    question_type: 'single_choice',
    question_order: questions.length + 1,
    question_group: '',
    tier_level: 1,
    is_required: true,
    is_active: true
  });

  /**
   * 重置表单
   */
  const resetForm = () => {
    setFormData({
      question_text: '',
      question_type: 'single_choice',
      question_order: questions.length + 1,
      question_group: '',
      tier_level: 1,
      is_required: true,
      is_active: true
    });
    setIsCreating(false);
    setEditingQuestion(null);
  };

  /**
   * 开始创建新问题
   */
  const startCreating = () => {
    resetForm();
    setIsCreating(true);
  };

  /**
   * 开始编辑问题
   */
  const startEditing = (question: QuizQuestion) => {
    setFormData({
      question_text: question.question_text,
      question_type: question.question_type,
      question_order: question.question_order,
      question_group: question.question_group || '',
      tier_level: question.tier_level || 1,
      is_required: question.is_required,
      is_active: question.is_active
    });
    setEditingQuestion(question);
    setIsCreating(true);
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const quizQuestionService = await Services.quizQuestion();
      
      if (editingQuestion) {
        // 更新现有问题
        const result = await quizQuestionService.update(editingQuestion.id, {
          ...formData,
          pack_id: quizPack.id
        });
        if (result.success) {
          toast.success('问题更新成功');
          onDataUpdate();
          resetForm();
        } else {
          throw new Error(result.error || 'Update failed');
        }
      } else {
        // 创建新问题
        const result = await quizQuestionService.create({
          ...formData,
          pack_id: quizPack.id,
          created_by: 'user'
        });
        if (result.success) {
          toast.success('问题创建成功');
          onDataUpdate();
          resetForm();
        } else {
          throw new Error(result.error || 'Create failed');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Operation failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 删除问题
   */
  const handleDelete = async (question: QuizQuestion) => {
    if (!confirm(`确定要删除问题"${question.question_text}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.delete(question.id);
      
      if (result.success) {
        toast.success('问题删除成功');
        onDataUpdate();
      } else {
        throw new Error(result.error || 'Delete failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Delete failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 复制问题
   */
  const handleDuplicate = async (question: QuizQuestion) => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.create({
        question_text: `${question.question_text} (副本)`,
        question_type: question.question_type,
        question_order: questions.length + 1,
        question_group: question.question_group,
        tier_level: question.tier_level,
        pack_id: quizPack.id,
        is_required: question.is_required,
        is_active: false, // 副本默认不激活
        created_by: 'user'
      });
      
      if (result.success) {
        toast.success('问题复制成功');
        onDataUpdate();
      } else {
        throw new Error(result.error || 'Duplicate failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Duplicate failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 调整问题顺序
   */
  const handleReorder = async (question: QuizQuestion, direction: 'up' | 'down') => {
    const currentIndex = questions.findIndex(q => q.id === question.id);
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    if (targetIndex < 0 || targetIndex >= questions.length) {
      return;
    }

    try {
      const quizQuestionService = await Services.quizQuestion();
      const targetQuestion = questions[targetIndex];
      
      // 交换顺序
      await Promise.all([
        quizQuestionService.update(question.id, { question_order: targetQuestion.question_order }),
        quizQuestionService.update(targetQuestion.id, { question_order: question.question_order })
      ]);
      
      toast.success('问题顺序调整成功');
      onDataUpdate();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Reorder failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 渲染问题列表
   */
  const renderQuestionList = () => (
    <div className="quiz-question-list">
      {questions
        .sort((a, b) => a.question_order - b.question_order)
        .map((question, index) => (
          <div key={question.id} className="quiz-question-card">
            <div className="question-header">
              <div className="question-info">
                <span className="question-order">#{question.question_order}</span>
                <h4 className="question-text" onClick={() => onQuestionSelect(question)}>
                  {question.question_text}
                </h4>
              </div>
              <div className="question-badges">
                {question.is_required && <span className="badge required">必填</span>}
                {question.is_active && <span className="badge active">活跃</span>}
                <span className="badge type">{question.question_type}</span>
                {question.tier_level && <span className="badge tier">层级 {question.tier_level}</span>}
              </div>
            </div>
            
            {question.question_group && (
              <p className="question-group">分组: {question.question_group}</p>
            )}
            
            <div className="question-actions">
              <button 
                className="action-button primary"
                onClick={() => onQuestionSelect(question)}
              >
                管理选项
              </button>
              <button 
                className="action-button secondary"
                onClick={() => startEditing(question)}
              >
                编辑
              </button>
              <button 
                className="action-button secondary"
                onClick={() => handleDuplicate(question)}
              >
                复制
              </button>
              <div className="order-controls">
                <button 
                  className="order-button"
                  onClick={() => handleReorder(question, 'up')}
                  disabled={index === 0}
                >
                  ↑
                </button>
                <button 
                  className="order-button"
                  onClick={() => handleReorder(question, 'down')}
                  disabled={index === questions.length - 1}
                >
                  ↓
                </button>
              </div>
              <button 
                className="action-button danger"
                onClick={() => handleDelete(question)}
              >
                删除
              </button>
            </div>
          </div>
        ))}
    </div>
  );

  /**
   * 渲染创建/编辑表单
   */
  const renderForm = () => (
    <div className="quiz-question-form-overlay">
      <div className="quiz-question-form">
        <h3>{editingQuestion ? '编辑问题' : '创建新问题'}</h3>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label>问题文本 *</label>
            <textarea
              value={formData.question_text}
              onChange={(e) => setFormData({...formData, question_text: e.target.value})}
              required
              placeholder="输入问题文本"
              rows={3}
            />
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>问题类型 *</label>
              <select
                value={formData.question_type}
                onChange={(e) => setFormData({...formData, question_type: e.target.value})}
                required
              >
                <option value="single_choice">单选题</option>
                <option value="multiple_choice">多选题</option>
                <option value="emotion_wheel">情绪轮盘</option>
                <option value="scale_rating">量表评分</option>
                <option value="slider">滑块</option>
                <option value="text_input">文本输入</option>
              </select>
            </div>
            
            <div className="form-group">
              <label>问题顺序</label>
              <input
                type="number"
                min="1"
                value={formData.question_order}
                onChange={(e) => setFormData({...formData, question_order: parseInt(e.target.value)})}
              />
            </div>
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>问题分组</label>
              <input
                type="text"
                value={formData.question_group}
                onChange={(e) => setFormData({...formData, question_group: e.target.value})}
                placeholder="可选的问题分组"
              />
            </div>
            
            <div className="form-group">
              <label>层级等级</label>
              <input
                type="number"
                min="1"
                max="10"
                value={formData.tier_level}
                onChange={(e) => setFormData({...formData, tier_level: parseInt(e.target.value)})}
              />
            </div>
          </div>
          
          <div className="form-group">
            <div className="checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.is_required}
                  onChange={(e) => setFormData({...formData, is_required: e.target.checked})}
                />
                必填问题
              </label>
              
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                />
                激活状态
              </label>
            </div>
          </div>
          
          <div className="form-actions">
            <button type="button" onClick={resetForm} className="action-button secondary">
              取消
            </button>
            <button type="submit" className="action-button primary">
              {editingQuestion ? '更新' : '创建'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );

  return (
    <div className="quiz-question-manager">
      <div className="manager-header">
        <h2>问题管理 - {quizPack.name}</h2>
        <button className="action-button primary" onClick={startCreating}>
          创建新问题
        </button>
      </div>
      
      {questions.length === 0 ? (
        <div className="empty-state">
          <p>暂无问题，点击上方按钮创建第一个问题</p>
        </div>
      ) : (
        renderQuestionList()
      )}
      
      {isCreating && renderForm()}
    </div>
  );
};

export { QuizQuestionManager };
