# 云端离线对齐服务改造计划

基于新的类型系统、SQL schema 和 docs/quiz 架构文档，制定云端离线对齐服务的全面改造计划。

## 📋 现状分析

### 1. 现有架构问题

#### 客户端 (src/services)
- ❌ 部分服务仍使用废弃的 emotion_data_set 架构
- ❌ VIP 和 unlock 系统缺乏完整的离线支持
- ❌ 标签系统未完全实现新的分类和翻译功能
- ❌ Quiz 系统的 6 层个性化配置未完全对齐
- ❌ 同步机制不支持新的 VIP 和标签数据

#### 服务端 (server)
- ❌ 缺乏完整的 VIP 和 unlock 系统服务
- ❌ 标签系统服务不完整
- ❌ Quiz 系统服务未完全迁移到新架构
- ❌ 同步服务不支持新的数据表结构

### 2. 新架构要求

#### 数据库层面
- ✅ 新的 VIP 和 unlock 系统表结构已定义
- ✅ 增强的标签系统表结构已定义
- ✅ Quiz 系统新架构表结构已定义
- ✅ 类型系统已完全对齐

#### 业务需求
- 🔄 支持离线优先的 VIP 功能
- 🔄 支持智能标签系统和翻译
- 🔄 支持 6 层个性化配置
- 🔄 支持增量和全量数据同步

## 🎯 改造目标

### 1. 统一架构模式
```
离线优先 + 云端同步 + 智能回退
├── 本地 SQLite (主要数据存储)
├── 云端数据库 (备份和同步)
├── 智能同步机制 (增量 + 冲突解决)
└── 网络容错处理 (离线模式支持)
```

### 2. 服务层重构
```
客户端服务层 (src/services)
├── 核心实体服务 (CRUD + 业务逻辑)
├── 复合业务服务 (跨实体操作)
├── 同步协调服务 (数据同步管理)
└── 配置管理服务 (6层个性化配置)

服务端服务层 (server/lib/services)
├── 数据访问服务 (Repository 模式)
├── 业务逻辑服务 (Domain Services)
├── 同步处理服务 (Sync Orchestration)
└── VIP 管理服务 (Subscription Management)
```

## 🚀 实施计划

### 阶段 1: 类型系统和基础设施 (1-2 周)

#### 1.1 完善类型定义
- [x] ✅ 已完成：VIP 和 unlock 系统类型定义
- [x] ✅ 已完成：标签系统类型定义
- [x] ✅ 已完成：API Schema 更新
- [ ] 🔄 验证类型与数据库 schema 的完全一致性

#### 1.2 数据库迁移脚本
```sql
-- 需要创建的迁移脚本
public/seeds/migrations/
├── 001_add_vip_system.sql
├── 002_enhance_tags_system.sql
├── 003_quiz_system_migration.sql
└── 004_sync_metadata_tables.sql
```

#### 1.3 基础设施服务更新
```typescript
// src/services/core/DatabaseService.ts
- 支持新表结构的初始化
- 支持数据迁移和版本管理
- 支持事务和批量操作优化

// src/services/core/SyncCoordinator.ts (新建)
- 统一的同步协调器
- 支持增量同步和冲突解决
- 支持优先级和依赖管理
```

### 阶段 2: 核心实体服务重构 (2-3 周)

#### 2.1 VIP 和 Unlock 系统服务

**客户端服务 (src/services/entities/)**
```typescript
// VipPlanService.ts (新建)
class VipPlanService {
  async getAvailablePlans(): Promise<ServiceResult<VipPlan[]>>
  async getPlanDetails(planId: string): Promise<ServiceResult<VipPlan>>
  async validatePlanAccess(userId: string, planId: string): Promise<ServiceResult<boolean>>
}

// UserSubscriptionService.ts (新建)
class UserSubscriptionService {
  async getActiveSubscription(userId: string): Promise<ServiceResult<UserSubscriptionHistory>>
  async getSubscriptionHistory(userId: string): Promise<ServiceResult<UserSubscriptionHistory[]>>
  async updateSubscriptionStatus(subscriptionId: string, status: SubscriptionStatus): Promise<ServiceResult<void>>
}

// UnlockService.ts (新建)
class UnlockService {
  async getUserUnlocks(userId: string): Promise<ServiceResult<{skins: UserSkinUnlock[], emojiSets: UserEmojiSetUnlock[]}>>
  async unlockContent(input: UnlockContentInput): Promise<ServiceResult<void>>
  async checkUnlockStatus(userId: string, contentType: 'skin' | 'emoji_set', contentId: string): Promise<ServiceResult<boolean>>
}
```

**服务端服务 (server/lib/services/)**
```typescript
// VipManagementService.ts (增强)
class VipManagementService {
  async processSubscription(subscriptionData: SubscriptionData): Promise<ServiceResult<UserSubscriptionHistory>>
  async validateVipAccess(userId: string, feature: string): Promise<ServiceResult<boolean>>
  async syncVipStatus(userId: string): Promise<ServiceResult<VipStatus>>
}

// UnlockManagementService.ts (新建)
class UnlockManagementService {
  async processUnlock(unlockData: UnlockData): Promise<ServiceResult<void>>
  async validateUnlockAccess(userId: string, contentId: string): Promise<ServiceResult<boolean>>
  async syncUnlockStatus(userId: string): Promise<ServiceResult<UnlockStatus>>
}
```

#### 2.2 标签系统服务重构

**客户端服务**
```typescript
// TagService.ts (增强)
class TagService {
  async getTagsByCategory(category: TagCategory): Promise<ServiceResult<Tag[]>>
  async createUserTag(tagData: CreateTagData): Promise<ServiceResult<Tag>>
  async getTagTranslations(tagId: string, languageCode?: string): Promise<ServiceResult<TagTranslation[]>>
  async updateTagUsageCount(tagId: string): Promise<ServiceResult<void>>
}

// QuizResultTagService.ts (新建)
class QuizResultTagService {
  async addTagsToResult(resultId: string, tagIds: string[], source: TagSource): Promise<ServiceResult<void>>
  async getResultTags(resultId: string): Promise<ServiceResult<QuizResultTag[]>>
  async removeTagFromResult(resultId: string, tagId: string): Promise<ServiceResult<void>>
}
```

#### 2.3 Quiz 系统服务迁移

**客户端服务更新**
```typescript
// QuizPackService.ts (增强)
class QuizPackService {
  async getPacksByCategory(category: string): Promise<ServiceResult<QuizPack[]>>
  async getPackWithQuestions(packId: string): Promise<ServiceResult<QuizPackWithQuestions>>
  async validatePackAccess(userId: string, packId: string): Promise<ServiceResult<boolean>>
}

// QuizSessionService.ts (增强)
class QuizSessionService {
  async createSession(packId: string, userId: string, config: QuizSessionConfig): Promise<ServiceResult<QuizSession>>
  async updateSessionProgress(sessionId: string, progress: SessionProgress): Promise<ServiceResult<void>>
  async completeSession(sessionId: string): Promise<ServiceResult<QuizResult>>
}

// QuizConfigService.ts (新建)
class QuizConfigService {
  async getUserPresentationConfig(userId: string): Promise<ServiceResult<UserPresentationConfig>>
  async updatePresentationConfig(userId: string, config: QuizPresentationConfig): Promise<ServiceResult<void>>
  async getPackOverrides(userId: string, packId: string): Promise<ServiceResult<PackPresentationOverride>>
  async generateSessionConfig(sessionId: string): Promise<ServiceResult<QuizSessionPresentationConfig>>
}
```

### 阶段 3: 同步机制重构 (2-3 周)

#### 3.1 增量同步系统

**同步协调器 (src/services/sync/)**
```typescript
// SyncCoordinator.ts (新建)
class SyncCoordinator {
  async performIncrementalSync(userId: string): Promise<ServiceResult<SyncResult>>
  async performFullSync(userId: string): Promise<ServiceResult<SyncResult>>
  async resolveSyncConflicts(conflicts: SyncConflict[]): Promise<ServiceResult<ConflictResolution[]>>
  async getSyncStatus(userId: string): Promise<ServiceResult<SyncStatus>>
}

// SyncStrategy.ts (新建)
interface SyncStrategy {
  canSync(entity: SyncableEntity): boolean
  syncUp(entity: SyncableEntity): Promise<SyncResult>
  syncDown(entity: SyncableEntity): Promise<SyncResult>
  resolveConflict(conflict: SyncConflict): Promise<ConflictResolution>
}

// 具体策略实现
class VipSyncStrategy implements SyncStrategy { ... }
class TagSyncStrategy implements SyncStrategy { ... }
class QuizSyncStrategy implements SyncStrategy { ... }
```

#### 3.2 服务端同步服务重构

**服务端同步服务 (server/lib/services/)**
```typescript
// SyncService.ts (重构)
class SyncService {
  async performFullSync(request: FullSyncRequest): Promise<FullSyncResponse>
  async performIncrementalSync(request: IncrementalSyncRequest): Promise<IncrementalSyncResponse>
  async handleSyncConflicts(conflicts: SyncConflict[]): Promise<ConflictResolution[]>
  
  // 新增：支持新数据表的同步
  private async syncVipData(userId: string, lastSync?: string): Promise<VipSyncResult>
  private async syncTagData(userId: string, lastSync?: string): Promise<TagSyncResult>
  private async syncQuizData(userId: string, lastSync?: string): Promise<QuizSyncResult>
}
```

### 阶段 4: 业务逻辑服务增强 (2-3 周)

#### 4.1 复合业务服务

**客户端复合服务 (src/services/business/)**
```typescript
// VipExperienceService.ts (新建)
class VipExperienceService {
  async checkFeatureAccess(userId: string, feature: VipFeature): Promise<ServiceResult<boolean>>
  async getVipBenefits(userId: string): Promise<ServiceResult<VipBenefits>>
  async processVipUpgrade(userId: string, planId: string): Promise<ServiceResult<UpgradeResult>>
}

// PersonalizationService.ts (新建)
class PersonalizationService {
  async generatePersonalizedConfig(userId: string, packId: string): Promise<ServiceResult<QuizPresentationConfig>>
  async applyUserPreferences(config: QuizPresentationConfig, preferences: UserPreferences): Promise<ServiceResult<QuizPresentationConfig>>
  async optimizeForAccessibility(config: QuizPresentationConfig, needs: AccessibilityNeeds): Promise<ServiceResult<QuizPresentationConfig>>
}

// SmartTaggingService.ts (新建)
class SmartTaggingService {
  async suggestTags(resultId: string, content: string): Promise<ServiceResult<Tag[]>>
  async autoTagResult(resultId: string): Promise<ServiceResult<QuizResultTag[]>>
  async learnFromUserTagging(userId: string, patterns: TaggingPattern[]): Promise<ServiceResult<void>>
}
```

#### 4.2 配置管理服务

**6层配置管理 (src/services/config/)**
```typescript
// ConfigMergeService.ts (新建)
class ConfigMergeService {
  async mergeConfigurations(layers: ConfigurationLayer[]): Promise<ServiceResult<FinalConfiguration>>
  async validateConfiguration(config: QuizPresentationConfig): Promise<ServiceResult<ValidationResult>>
  async optimizeConfiguration(config: QuizPresentationConfig, constraints: OptimizationConstraints): Promise<ServiceResult<QuizPresentationConfig>>
}

// ConfigVersionService.ts (新建)
class ConfigVersionService {
  async migrateConfiguration(oldConfig: any, targetVersion: string): Promise<ServiceResult<QuizPresentationConfig>>
  async validateConfigVersion(config: QuizPresentationConfig): Promise<ServiceResult<boolean>>
  async getConfigSchema(version: string): Promise<ServiceResult<ConfigSchema>>
}
```

### 阶段 5: 测试和优化 (1-2 周)

#### 5.1 单元测试
```typescript
// 测试覆盖目标
- 所有新服务的单元测试 (>90% 覆盖率)
- 同步机制的集成测试
- 配置合并逻辑的测试
- VIP 功能的端到端测试
```

#### 5.2 性能优化
```typescript
// 优化重点
- 数据库查询优化 (索引和查询计划)
- 同步性能优化 (批量操作和压缩)
- 内存使用优化 (缓存策略和垃圾回收)
- 网络传输优化 (数据压缩和增量传输)
```

## 📊 成功指标

### 1. 功能完整性
- [ ] 所有新数据表的 CRUD 操作完全支持
- [ ] VIP 功能离线模式正常工作
- [ ] 标签系统支持分类和翻译
- [ ] 6层配置系统正常运行
- [ ] 同步机制支持所有新数据类型

### 2. 性能指标
- [ ] 同步延迟 < 2秒 (增量同步)
- [ ] 离线模式响应时间 < 500ms
- [ ] 数据一致性 > 99.9%
- [ ] 同步成功率 > 99%

### 3. 用户体验
- [ ] 离线功能无缝切换
- [ ] VIP 功能即时生效
- [ ] 配置变更实时反映
- [ ] 错误恢复自动处理

## 🔄 迁移策略

### 1. 渐进式迁移
- 保持现有功能正常运行
- 逐步替换旧服务
- 并行运行新旧系统
- 数据完整性验证

### 2. 回滚计划
- 完整的数据备份策略
- 快速回滚机制
- 错误监控和告警
- 用户影响最小化

## 📅 时间线

| 阶段 | 时间 | 主要交付物 |
|------|------|-----------|
| 阶段1 | 1-2周 | 类型系统完善、数据库迁移 |
| 阶段2 | 2-3周 | 核心实体服务重构 |
| 阶段3 | 2-3周 | 同步机制重构 |
| 阶段4 | 2-3周 | 业务逻辑服务增强 |
| 阶段5 | 1-2周 | 测试和优化 |
| **总计** | **8-13周** | **完整的云端离线对齐系统** |

## 🔍 详细服务架构对比

### 现有服务架构分析

#### 客户端 (src/services) - 现状
```
src/services/
├── core/
│   ├── DatabaseService.ts ❌ (使用旧 schema)
│   ├── SyncService.ts ❌ (不支持新数据表)
│   └── ConfigService.ts ❌ (不支持6层配置)
├── entities/
│   ├── MoodEntryService.ts ✅ (基本可用)
│   ├── UserService.ts ⚠️ (需要VIP字段更新)
│   └── EmotionService.ts ❌ (使用废弃架构)
└── business/
    ├── AnalyticsService.ts ⚠️ (需要新数据支持)
    └── NotificationService.ts ✅ (基本可用)
```

#### 服务端 (server/lib/services) - 现状
```
server/lib/services/
├── AuthService.ts ✅ (基本可用)
├── UserManagementService.ts ⚠️ (需要VIP功能)
├── MoodEntryService.ts ✅ (基本可用)
├── SyncService.ts ❌ (不支持新数据表)
├── AnalyticsService.ts ⚠️ (需要新数据支持)
├── QuizService.ts ❌ (使用旧架构)
├── QuizEngineService.ts ❌ (使用旧架构)
└── PaymentService.ts ⚠️ (需要VIP集成)
```

### 目标服务架构

#### 客户端 (src/services) - 目标
```
src/services/
├── core/
│   ├── DatabaseService.ts ✨ (支持新schema)
│   ├── SyncCoordinator.ts ✨ (统一同步协调)
│   ├── ConfigMergeService.ts ✨ (6层配置合并)
│   └── CacheService.ts ✨ (智能缓存管理)
├── entities/
│   ├── TagService.ts ✨ (新标签系统)
│   ├── VipPlanService.ts ✨ (VIP计划管理)
│   ├── UnlockService.ts ✨ (内容解锁管理)
│   ├── QuizPackService.ts ✨ (Quiz包管理)
│   ├── QuizSessionService.ts ✨ (会话管理)
│   ├── UserSubscriptionService.ts ✨ (订阅管理)
│   └── QuizResultTagService.ts ✨ (结果标签)
├── business/
│   ├── VipExperienceService.ts ✨ (VIP体验)
│   ├── PersonalizationService.ts ✨ (个性化)
│   ├── SmartTaggingService.ts ✨ (智能标签)
│   └── QuizRecommendationService.ts ✨ (推荐)
└── sync/
    ├── SyncStrategy.ts ✨ (同步策略)
    ├── ConflictResolver.ts ✨ (冲突解决)
    └── SyncQueue.ts ✨ (同步队列)
```

#### 服务端 (server/lib/services) - 目标
```
server/lib/services/
├── core/
│   ├── DatabaseInitializationService.ts ✨ (增强)
│   ├── SyncService.ts ✨ (重构)
│   └── ValidationService.ts ✨ (数据验证)
├── entities/
│   ├── VipManagementService.ts ✨ (VIP管理)
│   ├── UnlockManagementService.ts ✨ (解锁管理)
│   ├── TagManagementService.ts ✨ (标签管理)
│   ├── QuizManagementService.ts ✨ (Quiz管理)
│   └── SubscriptionService.ts ✨ (订阅服务)
├── business/
│   ├── RecommendationEngine.ts ✨ (推荐引擎)
│   ├── AnalyticsEngine.ts ✨ (分析引擎)
│   └── PersonalizationEngine.ts ✨ (个性化引擎)
└── integration/
    ├── PaymentGateway.ts ✨ (支付网关)
    ├── NotificationService.ts ✨ (通知服务)
    └── ExternalApiService.ts ✨ (外部API)
```

## 🎯 下一步行动

### 立即行动 (本周)
1. **验证类型一致性**: 确保 TypeScript 类型与 SQL schema 完全对齐
2. **创建迁移脚本**: 准备数据库结构迁移和数据迁移脚本
3. **设置测试环境**: 建立独立的开发和测试数据库

### 短期目标 (1-2周)
1. **基础设施准备**: 完成 DatabaseService 和 SyncCoordinator 的基础框架
2. **核心服务开发**: 开始 VIP 和 unlock 系统的核心服务开发
3. **测试框架搭建**: 建立完整的单元测试和集成测试框架

### 中期目标 (3-6周)
1. **服务逐步迁移**: 按优先级逐步迁移和重构现有服务
2. **同步机制实现**: 完成增量同步和冲突解决机制
3. **业务逻辑完善**: 实现复合业务服务和智能功能

### 长期目标 (7-13周)
1. **系统集成测试**: 完成端到端的系统集成测试
2. **性能优化**: 进行全面的性能调优和优化
3. **生产部署**: 完成生产环境的部署和监控
