/**
 * UI标签服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 */

import { BaseService } from '../base/BaseService';
import { BaseRepository } from '../base/BaseRepository';
import { UILabel } from '../../types/schema/base';
import { CreateUILabelInput, UpdateUILabelInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

// 简化的UILabelRepository实现
class UILabelRepository extends BaseRepository<UILabel, CreateUILabelInput, UpdateUILabelInput> {
  constructor(db?: SQLiteDBConnection) {
    super('ui_labels', db);
  }

  async findByCategory(category: string): Promise<UILabel[]> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE category = ? ORDER BY key ASC`;
    const result = await db.query(query, [category]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  async findByContext(context: string): Promise<UILabel[]> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE context = ? ORDER BY key ASC`;
    const result = await db.query(query, [context]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  async findByKey(key: string): Promise<UILabel | null> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE key = ? LIMIT 1`;
    const result = await db.query(query, [key]);
    const row = result.values?.[0];
    return row ? this.mapRowToEntity(row) : null;
  }

  async findSystemLabels(): Promise<UILabel[]> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} ORDER BY key ASC`;
    const result = await db.query(query);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  async searchLabels(searchTerm: string): Promise<UILabel[]> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE (key LIKE ? OR default_text LIKE ?) ORDER BY key ASC`;
    const searchPattern = `%${searchTerm}%`;
    const result = await db.query(query, [searchPattern, searchPattern]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  async getCategories(): Promise<string[]> {
    const db = this.getDb();
    const query = `SELECT DISTINCT category FROM ${this.tableName} WHERE category IS NOT NULL ORDER BY category ASC`;
    const result = await db.query(query);
    return (result.values || []).map(row => row.category);
  }

  async getContexts(): Promise<string[]> {
    const db = this.getDb();
    const query = `SELECT DISTINCT context FROM ${this.tableName} WHERE context IS NOT NULL ORDER BY context ASC`;
    const result = await db.query(query);
    return (result.values || []).map(row => row.context);
  }

  protected mapRowToEntity(row: any): UILabel {
    return {
      key: row.key,
      default_text: row.default_text,
      category: row.category,
      description: row.description,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  protected mapEntityToRow(entity: Partial<UILabel>): Record<string, any> {
    return {
      key: entity.key,
      default_text: entity.default_text,
      category: entity.category,
      description: entity.description,
      created_at: entity.created_at,
      updated_at: entity.updated_at
    };
  }

  protected buildInsertQuery(data: CreateUILabelInput): { query: string; values: any[] } {
    const now = new Date().toISOString();

    const query = `
      INSERT INTO ${this.tableName} (
        key, default_text, category, description, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;

    const values = [
      data.key, data.default_text, data.category || null,
      data.description || null, now, now
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateUILabelInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'is_system' || key === 'is_active') {
          fields.push(`${key} = ?`);
          values.push(value ? 1 : 0);
        } else {
          fields.push(`${key} = ?`);
          values.push(value);
        }
      }
    });

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.category) {
      conditions.push('category = ?');
      values.push(filters.category);
    }

    if (filters?.context) {
      conditions.push('context = ?');
      values.push(filters.context);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY key ASC';
    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.category) {
      conditions.push('category = ?');
      values.push(filters.category);
    }

    if (filters?.context) {
      conditions.push('context = ?');
      values.push(filters.context);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    return { query, values };
  }

  protected extractIdFromCreateData(data: CreateUILabelInput): string {
    return data.key;
  }
}

export class UILabelService extends BaseService<UILabel, CreateUILabelInput, UpdateUILabelInput> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new UILabelRepository(db);
    super(repository);
  }

  async createLabel(input: CreateUILabelInput): Promise<ServiceResult<UILabel>> {
    try {
      await this.validateCreate(input);
      const label = await this.repository.create(input);
      this.emit('labelCreated', label);
      return this.createSuccessResult(label);
    } catch (error) {
      return this.createErrorResult('Failed to create UI label', error);
    }
  }

  async getLabelsByCategory(category: string): Promise<ServiceResult<UILabel[]>> {
    try {
      const labels = await (this.repository as UILabelRepository).findByCategory(category);
      return this.createSuccessResult(labels);
    } catch (error) {
      return this.createErrorResult('Failed to get labels by category', error);
    }
  }

  async getLabelsByContext(context: string): Promise<ServiceResult<UILabel[]>> {
    try {
      const labels = await (this.repository as UILabelRepository).findByContext(context);
      return this.createSuccessResult(labels);
    } catch (error) {
      return this.createErrorResult('Failed to get labels by context', error);
    }
  }

  async getLabelByKey(key: string): Promise<ServiceResult<UILabel | null>> {
    try {
      const label = await (this.repository as UILabelRepository).findByKey(key);
      return this.createSuccessResult(label);
    } catch (error) {
      return this.createErrorResult('Failed to get label by key', error);
    }
  }

  async getSystemLabels(): Promise<ServiceResult<UILabel[]>> {
    try {
      const labels = await (this.repository as UILabelRepository).findSystemLabels();
      return this.createSuccessResult(labels);
    } catch (error) {
      return this.createErrorResult('Failed to get system labels', error);
    }
  }

  async searchLabels(searchTerm: string): Promise<ServiceResult<UILabel[]>> {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new Error('Search term must be at least 2 characters long');
      }
      const labels = await (this.repository as UILabelRepository).searchLabels(searchTerm.trim());
      return this.createSuccessResult(labels);
    } catch (error) {
      return this.createErrorResult('Failed to search labels', error);
    }
  }

  async getCategories(): Promise<ServiceResult<string[]>> {
    try {
      const categories = await (this.repository as UILabelRepository).getCategories();
      return this.createSuccessResult(categories);
    } catch (error) {
      return this.createErrorResult('Failed to get label categories', error);
    }
  }

  async getContexts(): Promise<ServiceResult<string[]>> {
    try {
      const contexts = await (this.repository as UILabelRepository).getContexts();
      return this.createSuccessResult(contexts);
    } catch (error) {
      return this.createErrorResult('Failed to get label contexts', error);
    }
  }

  async updateLabel(labelId: string, updates: UpdateUILabelInput): Promise<ServiceResult<UILabel>> {
    try {
      await this.validateUpdate(updates);
      const label = await this.repository.update(labelId, updates);
      this.emit('labelUpdated', label);
      return this.createSuccessResult(label);
    } catch (error) {
      return this.createErrorResult('Failed to update UI label', error);
    }
  }

  protected async validateCreate(data: CreateUILabelInput): Promise<void> {
    if (!data.key || data.key.trim().length === 0) {
      throw new Error('Label key is required');
    }
    if (data.key.length > 100) {
      throw new Error('Label key must be less than 100 characters');
    }
    if (!data.default_text || data.default_text.trim().length === 0) {
      throw new Error('Default text is required');
    }
    if (data.default_text.length > 500) {
      throw new Error('Default text must be less than 500 characters');
    }
  }

  protected async validateUpdate(data: UpdateUILabelInput): Promise<void> {
    if (data.key !== undefined && (!data.key || data.key.trim().length === 0)) {
      throw new Error('Label key cannot be empty');
    }
    if (data.key !== undefined && data.key.length > 100) {
      throw new Error('Label key must be less than 100 characters');
    }
    if (data.default_text !== undefined && (!data.default_text || data.default_text.trim().length === 0)) {
      throw new Error('Default text cannot be empty');
    }
    if (data.default_text !== undefined && data.default_text.length > 500) {
      throw new Error('Default text must be less than 500 characters');
    }
  }
}
