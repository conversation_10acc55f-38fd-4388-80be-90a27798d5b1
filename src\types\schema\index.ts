/**
 * Schema 统一导出文件
 *
 * 提供所有 schema 相关类型和工具的统一导入入口
 */

// ==================== 基础 Schema 导出 ====================
export {
  // 基础类型 Schema
  SyncStatusSchema,
  TimestampSchema,
  IdSchema,
  OptionalIdSchema,
  // 用户相关 Schema
  UserSchema,
  // 注意：原emotion相关Schema已废弃，使用Quiz架构替代
  // EmotionSchema,
  // EmotionDataSetSchema,
  // EmotionDataSetTierSchema,
  // 表情相关 Schema
  EmojiSetTypeSchema,
  EmojiSetSchema,
  AnimationTypeSchema,
  // EmojiItemSchema, // 已废弃，使用EmojiMapping接口替代
  // 皮肤相关 Schema
  SkinSchema,
  // 翻译相关 Schema
  LanguageCodeSchema,
  UILabelSchema,
  UILabelTranslationSchema,
  // EmotionTranslationSchema, // 已废弃，使用Quiz相关翻译替代
  // EmotionDataSetTranslationSchema, // 已废弃，使用Quiz相关翻译替代
  EmojiSetTranslationSchema,
  SkinTranslationSchema,
  TagTranslationSchema,
  // 心情记录 Schema
  MoodEntrySchema,
  EmotionSelectionSchema,
  // 用户配置 Schema
  UserConfigSchema,
  // 标签 Schema
  TagSchema,
  QuizResultTagSchema,
  MoodEntryTagSchema,
  // VIP 和 Unlock 系统 Schema
  VipTierSchema,
  SubscriptionStatusSchema,
  BillingCycleSchema,
  PaymentMethodSchema,
  SubscriptionSourceSchema,
  UnlockMethodSchema,
  VipFeatureTypeSchema,
  TranslatorTypeSchema,
  TagSourceSchema,
  TagCategorySchema,
  UserSkinUnlockSchema,
  UserEmojiSetUnlockSchema,
  UserSubscriptionHistorySchema,
  VipPlanSchema,
  VipFeatureSchema,
  // EmotionDataSetEmotionSchema, // 已废弃，使用Quiz架构替代
  // 新增基础类型 Schema
  ViewTypeSchema,
  ContentDisplayModeSchema,
  RenderEngineSchema,
  CardLayoutSchema,
  BubbleLayoutSchema,
  GalaxyLayoutSchema,
  ListLayoutSchema,
  GridLayoutSchema,
  TreeLayoutSchema,
  FlowLayoutSchema,
  TagCloudLayoutSchema,
  IntensityLevelSchema,
  EmotionCategorySchema,
  ColorModeSchema,
  UnlockConditionsSchema,
  // 基础类型
  type User,
  // type Emotion, // 已废弃，使用QuizQuestionOption替代
  // type EmotionDataSet, // 已废弃，使用QuizPack替代
  // type EmotionDataSetTier, // 已废弃，使用QuizQuestion替代
  // type EmotionDataSetEmotion, // 已废弃，使用Quiz架构替代
  type EmojiSet,
  // type EmojiItem, // 已废弃，使用EmojiMapping接口替代
  type DatabaseSkin, // 数据库版本的 Skin
  type MoodEntry,
  type EmotionSelection,
  type MoodEntryTag,
  type UserConfig,
  type Tag,
  type TagTranslation,
  type QuizResultTag,
  // VIP 和 Unlock 系统类型
  type UserSkinUnlock,
  type UserEmojiSetUnlock,
  type UserSubscriptionHistory,
  type VipPlan,
  type VipFeature,
  type VipTier,
  type SubscriptionStatus,
  type BillingCycle,
  type PaymentMethod,
  type SubscriptionSource,
  type UnlockMethod,
  type VipFeatureType,
  type TranslatorType,
  type TagSource,
  type TagCategory,
  type LanguageCode,
  type UILabel,
  type UILabelTranslation,
  // type EmotionTranslation, // 已废弃，使用Quiz相关翻译替代
  // type EmotionDataSetTranslation, // 已废弃，使用Quiz相关翻译替代
  type EmojiSetTranslation,
  type SkinTranslation,
  type TagTranslation,
  // 新增基础类型
  type ViewType,
  type ContentDisplayMode,
  type RenderEngine,
  type CardLayout,
  type BubbleLayout,
  type GalaxyLayout,
  type ListLayout,
  type GridLayout,
  type TreeLayout,
  type FlowLayout,
  type TagCloudLayout,
  type EmojiSetType,
  type AnimationType,
  type IntensityLevel,
  type EmotionCategory,
  type ColorMode,
  type SyncStatus,
  type UnlockConditions,
  type allViewTypes,
  type allContentDisplayModes,
  type allRenderEngines,
    // type allCardLayouts,
  // type allBubbleLayouts,
  // type allGalaxyLayouts,
  // type allListLayouts,
  // type allGridLayouts,
  // type allTreeLayouts,
  // type allFlowLayouts,
  // type allTagCloudLayouts,
  // **新增**: Emoji映射相关Schema和类型
  QuestionPresentationOverrideSchema,
  PackPresentationConfigSchema,
  type QuestionPresentationOverride,
  type PackPresentationConfig,
  type EmojiMapping,
  type EmojiMappingResult,
  type EmotionPresentation,
} from './base';

// ==================== Emoji映射API Schema 导出 ====================
export {
  // Emoji映射配置Schema
  GetOptionPresentationInputSchema,
  OptionPresentationResponseSchema,
  EmojiMappingConfigSchema,
  UpdateUserEmojiMappingInputSchema,
  UpdateQuestionEmojiOverrideInputSchema,
  GetAvailableEmojisInputSchema,
  AvailableEmojisResponseSchema,
  BatchUpdateEmojiMappingInputSchema,
  ResetEmojiMappingInputSchema,
  GetEmojiMappingConfigInputSchema,
  EmojiMappingConfigResponseSchema,
  ImportEmojiThemeInputSchema,
  ExportEmojiThemeInputSchema,
  SearchEmojiInputSchema,
  SearchEmojiResponseSchema,
  EmojiApiResponseSchema,
  // Emoji映射API类型
  type GetOptionPresentationInput,
  type OptionPresentationResponse,
  type EmojiMappingConfig,
  type UpdateUserEmojiMappingInput,
  type UpdateQuestionEmojiOverrideInput,
  type GetAvailableEmojisInput,
  type AvailableEmojisResponse,
  type BatchUpdateEmojiMappingInput,
  type ResetEmojiMappingInput,
  type GetEmojiMappingConfigInput,
  type EmojiMappingConfigResponse,
  type ImportEmojiThemeInput,
  type ExportEmojiThemeInput,
  type SearchEmojiInput,
  type SearchEmojiResponse,
  type EmojiApiResponse,
} from './emoji-mapping-api';

// ==================== API Schema 导出 ====================
export {
  // 通用 API Schema
  PaginationSchema,
  SortSchema,
  ApiResponseSchema,
  PaginatedResponseSchema,
  // 认证相关 Schema
  LoginInputSchema,
  RegisterInputSchema,
  AuthResponseSchema,
  // 数据库操作 Schema
  SqlQueryInputSchema,
  BatchOperationInputSchema,
  FetchTableInputSchema,
  // 支付相关 Schema
  VipPlanSchema,
  PurchaseVipInputSchema,
  PurchaseSkinInputSchema,
  PurchaseEmojiSetInputSchema,
  GetPurchaseHistoryInputSchema,
  PurchaseResultSchema,
  PurchaseHistoryItemSchema,
  // VIP 状态 Schema
  GetVipStatusInputSchema,
  VipStatusSchema,
  // VIP 和 Unlock 系统 Schema
  GetUserUnlocksInputSchema,
  UserUnlocksResponseSchema,
  UnlockContentInputSchema,
  GetSubscriptionHistoryInputSchema,
  SubscriptionHistoryResponseSchema,
  // 数据同步 Schema
  SynchronizeDataInputSchema,
  PerformFullSyncInputSchema,
  // 翻译 API Schema
  GetTranslationInputSchema,
  CreateTranslationInputSchema,
  UpdateTranslationInputSchema,
  BatchTranslationInputSchema,
  GetUILabelsInputSchema,
  GetTranslationStatsInputSchema,
  LanguageSupportSchema,
  TranslationStatsSchema,
  BatchTranslationResultSchema,
  // 数据库操作 Schema (统一自 server)
  BatchStatementsInputSchema,
  SqlScriptInputSchema,
  TableQueryInputSchema,
  TableQueryWithLimitInputSchema,
  // 认证相关 Schema (统一自 server)
  AuthTokenSchema,
  VerifyTokenInputSchema,
  UpdateVipStatusInputSchema,
  // 支付相关 Schema (统一自 server)
  PaymentMethodSchema,
  SkinPurchaseSchema,
  EmojiSetPurchaseSchema,
  // 数据同步相关 Schema (统一自 server)
  MoodEntryUploadSchema,
  EmotionSelectionUploadSchema,
  DataSynchronizeInputSchema,
  FullSyncInputSchema,
  // 分析服务相关 Schema (统一自 server)
  GetMoodAnalyticsInputSchema,
  GetEmotionUsageStatsInputSchema,
  GetUserActivityStatsInputSchema,
  // 用户管理相关 Schema (统一自 server)
  UpdateUserPreferencesInputSchema,
  UnlockSkinInputSchema,
  GetUserProfileInputSchema,
  GetUserConfigInputSchema,
  GetUserUnlockedSkinsInputSchema,
  GetUserUnlockedEmojiSetsInputSchema,
  // 服务层接口 Schema (统一自 server)
  LoginCredentialsSchema,
  RegisterDataSchema,
  UserProfileSchema,
  UserPreferencesSchema,
  SkinUnlockSchema,
  // 分析服务相关 Schema (统一自 AnalyticsService)
  AnalyticsQuerySchema,
  MoodAnalyticsSchema,
  EmotionUsageStatsSchema,
  UserActivityStatsSchema,
  // 数据库管理相关 Schema (统一自 server)
  ResultSetSchema,
  InStatementSchema,
  TransactionModeSchema,
  ResetDatabaseInputSchema,
  // 客户端服务层 Schema (统一自 src/services)
  CompleteMoodEntryInputSchema,
  MoodEntryWithSelectionsSchema,
  MoodTrackingStatsSchema,
  // 在线服务相关 Schema (统一自 src/services/online)
  VipPurchaseDataSchema,
  SkinPurchaseDataSchema,
  EmojiSetPurchaseDataSchema,
  SqlQueryConfigSchema,
  BatchSqlConfigSchema,
  // Repository 层数据类型 Schema (统一自 src/services/entities)
  CreateMoodEntryDataSchema,
  UpdateMoodEntryDataSchema,
  MoodEntryFilterSchema,
  CreateEmotionSelectionDataSchema,
  UpdateEmotionSelectionDataSchema,
  EmotionSelectionFilterSchema,
  CreateEmotionDataSchema,
  UpdateEmotionDataSchema,
  EmotionFilterSchema,
  CreateSkinDataSchema,
  UpdateSkinDataSchema,
  CreateEmojiSetDataSchema,
  UpdateEmojiSetDataSchema,
  // CreateEmojiItemDataSchema, // 已废弃，使用EmojiMapping替代
  // UpdateEmojiItemDataSchema, // 已废弃，使用EmojiMapping替代
  CreateUserConfigDataSchema,
  UpdateUserConfigDataSchema,
  UserConfigFilterSchema,
  // 新增的 Repository 数据类型 Schema (统一自 src/services/entities)
  CreateTagDataSchema,
  UpdateTagDataSchema,
  CreateEmotionDataSetDataSchema,
  UpdateEmotionDataSetDataSchema,
  EmotionDataSetFilterSchema,
  CreateEmotionDataSetTierDataSchema,
  UpdateEmotionDataSetTierDataSchema,
  EmotionDataSetTierFilterSchema,
  CreateMoodEntryTagDataSchema,
  UpdateMoodEntryTagDataSchema,
  MoodEntryTagFilterSchema,
  CreateEmotionInputSchema,
  UpdateEmotionInputSchema,
  CreateUILabelDataSchema,
  UpdateUILabelDataSchema,
  // 新增配置类型 Schema
  Preview_configschema,
  LayoutConfigSchema,
  SkinConfigSchema,
  CreateMoodEntryInputSchema,
  // 新增查询过滤器 Schema
  EmojiSetFilterSchema,
  EmojiItemFilterSchema,
  SkinFilterSchema,
  // 新增统计信息 Schema
  EmojiSetStatsSchema,
  EmojiUsageStatsSchema,
  MoodStatsSchema,
  MoodTrendSchema,
  // 新增业务逻辑 Schema
  ServiceResultSchema,
  PaginatedResultSchema,
  LayoutPreferencesSchema,
  RenderEnginePreferencesSchema,
  // 新增业务逻辑类型 Schema
  // EmotionEmojiMappingSchema, // 已废弃，使用EmojiMapping接口替代
  // EmojiMappingSchema, // 已废弃，使用EmojiMapping接口替代
  EmojiSetConfigSchema,
  EmotionTreeNodeSchema,
  RelatedDataSyncStatusSchema,
  ComputedMoodDataSchema,
  DisplayPreferencesSchema,
  MoodEntryDisplayDataSchema,
  EmotionVisualizationDataSchema,
  SkinUsageSchema,
  UILabelCategorySchema,
  UILabelUsageStatsSchema,
  UILabelTranslationCoverageSchema,
  UserConfigExtendedSchema,
  // 业务逻辑版本的 Skin 类型 Schema
  BusinessSkinConfigSchema,
  BusinessSkinSchema,
  // API 类型
  type LoginInput,
  type RegisterInput,
  type SqlQueryInput,
  type BatchOperationInput,
  type FetchTableInput,
  type PurchaseVipInput,
  type PurchaseSkinInput,
  type PurchaseEmojiSetInput,
  type GetPurchaseHistoryInput,
  type GetVipStatusInput,
  type GetUserUnlocksInput,
  type UserUnlocksResponse,
  type UnlockContentInput,
  type GetSubscriptionHistoryInput,
  type SubscriptionHistoryResponse,
  type SynchronizeDataInput,
  type PerformFullSyncInput,
  type GetTranslationInput,
  type CreateTranslationInput,
  type UpdateTranslationInput,
  type BatchTranslationInput,
  type GetUILabelsInput,
  type GetTranslationStatsInput,
  // 数据库操作类型 (统一自 server)
  type BatchStatementsInput,
  type SqlScriptInput,
  type TableQueryInput,
  type TableQueryWithLimitInput,
  // 认证相关类型 (统一自 server)
  type AuthToken,
  type VerifyTokenInput,
  type UpdateVipStatusInput,
  // 支付相关类型 (统一自 server)
  type PaymentMethod,
  type SkinPurchase,
  type EmojiSetPurchase,
  // 数据同步相关类型 (统一自 server)
  type MoodEntryUpload,
  type EmotionSelectionUpload,
  type DataSynchronizeInput,
  type FullSyncInput,
  // 分析服务相关类型 (统一自 server)
  type GetMoodAnalyticsInput,
  type GetEmotionUsageStatsInput,
  type GetUserActivityStatsInput,
  // 用户管理相关类型 (统一自 server)
  type UpdateUserPreferencesInput,
  type UnlockSkinInput,
  type GetUserProfileInput,
  type GetUserConfigInput,
  type GetUserUnlockedSkinsInput,
  type GetUserUnlockedEmojiSetsInput,
  // 服务层接口类型 (统一自 server)
  type LoginCredentials,
  type RegisterData,
  type UserProfile,
  type UserPreferences,
  type SkinUnlock,
  // 分析服务相关类型 (统一自 AnalyticsService)
  type AnalyticsQuery,
  type MoodAnalytics,
  type EmotionUsageStats,
  type UserActivityStats,
  // 数据库管理相关类型 (统一自 server)
  type ResultSet,
  type InStatement,
  type TransactionMode,
  type ResetDatabaseInput,
  // 客户端服务层类型 (统一自 src/services)
  type CompleteMoodEntryInput,
  type MoodEntryWithSelections,
  type MoodTrackingStats,
  // 在线服务相关类型 (统一自 src/services/online)
  type VipPurchaseData,
  type SkinPurchaseData,
  type EmojiSetPurchaseData,
  type SqlQueryConfig,
  type BatchSqlConfig,
  // Repository 层数据类型 (统一自 src/services/entities)
  type CreateMoodEntryData,
  type UpdateMoodEntryData,
  type MoodEntryFilter,
  type CreateEmotionSelectionData,
  type UpdateEmotionSelectionData,
  type EmotionSelectionFilter,
  type CreateEmotionData,
  type UpdateEmotionData,
  type EmotionFilter,
  type CreateSkinData,
  type UpdateSkinData,
  type CreateEmojiSetData,
  type UpdateEmojiSetData,
  // type CreateEmojiItemData, // 已废弃，使用EmojiMapping替代
  // type UpdateEmojiItemData, // 已废弃，使用EmojiMapping替代
  // 全局应用设置类型
  type CreateGlobalAppConfigInput,
  type UpdateGlobalAppConfigInput,
  // Quiz系统配置类型
  type CreateUserQuizPreferencesInput,
  type UpdateUserQuizPreferencesInput,
  type CreateQuizPackOverridesInput,
  type UpdateQuizPackOverridesInput,
  type CreateQuizSessionConfigInput,
  // 配置系统 tRPC 类型
  type GetGlobalAppConfigInput,
  type UpdateGlobalAppConfigTRPCInput,
  type CreateGlobalAppConfigTRPCInput,
  type GetUserQuizPreferencesInput,
  type UpdateUserQuizPreferencesTRPCInput,
  type CreateUserQuizPreferencesTRPCInput,
  type GetQuizPackOverridesInput,
  type UpdateQuizPackOverridesTRPCInput,
  type CreateQuizPackOverridesTRPCInput,
  type GenerateQuizSessionConfigInput,
  type GetQuizSessionConfigInput,
  // 配置系统 tRPC Response 类型
  type ConfigResponse,
  type GlobalAppConfigResponse,
  type GlobalAppConfigListResponse,
  type UserQuizPreferencesResponse,
  type QuizPackOverridesResponse,
  type QuizSessionConfigResponse,
  // 向后兼容的用户配置类型
  type CreateUserConfigData,
  type UpdateUserConfigData,
  type UserConfigFilter,
  // 新增的 Repository 数据类型 (统一自 src/services/entities)
  type CreateTagData,
  type UpdateTagData,
  type CreateEmotionDataSetData,
  type UpdateEmotionDataSetData,
  type EmotionDataSetFilter,
  type CreateEmotionDataSetTierData,
  type UpdateEmotionDataSetTierData,
  type EmotionDataSetTierFilter,
  type CreateMoodEntryTagData,
  type UpdateMoodEntryTagData,
  type MoodEntryTagFilter,
  type CreateEmotionInput,
  type UpdateEmotionInput,
  type CreateUILabelData,
  type UpdateUILabelData,
  // 新增配置类型
  type PreviewConfig,
  type LayoutConfig,
  type DatabaseSkinConfig, // 数据库版本的 SkinConfig
  type ViewConfig,
  type CreateMoodEntryInput,
  type AuthResponse,
  type PurchaseResult,
  type PurchaseHistoryItem,
  type VipPlan,
  type VipStatus,
  type LanguageSupport,
  type TranslationStats,
  type BatchTranslationResult,
  type Pagination,
  type Sort,
  // 新增业务逻辑类型
  // type EmotionEmojiMapping, // 已废弃，使用EmojiMapping接口替代
  // type EmojiMapping, // 已废弃，使用新的EmojiMapping接口替代
  type EmojiSetConfig,
  type EmotionTreeNode,
  type RelatedDataSyncStatus,
  type ComputedMoodData,
  type DisplayPreferences,
  type MoodEntryDisplayData,
  type EmotionVisualizationData,
  type SkinUsage,
  type UILabelCategory,
  type UILabelUsageStats,
  type UILabelTranslationCoverage,
  type UserConfigExtended,
  // 配置系统类型
  type GlobalAppConfig,
  type UserQuizPreferences,
  type QuizPackOverrides,
  type QuizSessionConfig,
  // 业务逻辑版本的 Skin 类型
  type BusinessSkinConfig,
  type BusinessSkin,
  type SkinConfig, // 业务逻辑版本的别名
  type Skin, // 业务逻辑版本的别名
} from './api';

// ==================== 翻译扩展 Schema 导出 ====================
export {
  // 扩展翻译 Schema
  TranslationSchema,
  TranslatableEntitySchema,
  TranslationInputSchema,
  TranslatableCreateInputSchema,
  TranslatableUpdateInputSchema,
  TranslationFilterSchema,
  LanguageSupportExtendedSchema,
  TranslationValidationSchema,
  TranslationExportSchema,
  TranslationContextSchema,
  TranslationEventSchema,
  AnyTranslationSchema,
  EntityTypeSchema,
  TranslationUtilsConfigSchema,
  // 扩展翻译类型
  type Translation,
  type TranslatableEntity,
  type TranslationInput,
  type TranslatableCreateInput,
  type TranslatableUpdateInput,
  type TranslationFilter,
  type LanguageSupportExtended,
  type TranslationValidation,
  type TranslationExport,
  type TranslationContext,
  type TranslationEvent,
  type AnyTranslation,
  type EntityType,
  type TranslationUtilsConfig,
  // 常量
  LANGUAGE_CODES,
  DEFAULT_SUPPORTED_LANGUAGES,
  ENTITY_TYPE_MAPPING,
} from './translation';

// ==================== 生成工具导出 ====================
export {
  // 映射和工具
  SQL_TO_ZOD_MAPPING,
  CONSTRAINT_TO_ZOD_MAPPING,
  generateZodSchemaFromField,
  generateZodSchemaFromTable,
  generateTypeScriptInterface,
  generateZodSchemaCode,
  validateSchemaConsistency,
  parseCreateTableStatement,
  // 工具类型
  type DatabaseField,
  type DatabaseTable,
} from './generator';

// ==================== 便捷导出组合 ====================

// 注意：便捷导出组合类型已移除，因为存在循环引用问题
// 如需使用组合类型，请直接从各自的模块导入

// ==================== 注意 ====================
// 常用组合 Schema、验证工具等已移除，因为存在依赖问题
// 如需使用这些功能，请直接从相应的模块导入
