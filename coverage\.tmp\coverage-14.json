{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 6}, {"startOffset": 872, "endOffset": 895, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/entities/__tests__/QuizAnswerServiceFixed.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 50105, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 50105, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 888, "endOffset": 18963, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 972, "endOffset": 1224, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1276, "endOffset": 6210, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1357, "endOffset": 2792, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2881, "endOffset": 3459, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3549, "endOffset": 4127, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4218, "endOffset": 4777, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4868, "endOffset": 5454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5554, "endOffset": 6202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6268, "endOffset": 8866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6358, "endOffset": 7839, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7008, "endOffset": 7434, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7924, "endOffset": 8858, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8925, "endOffset": 10943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9007, "endOffset": 10935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11006, "endOffset": 15120, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11096, "endOffset": 14328, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14418, "endOffset": 15112, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15174, "endOffset": 16574, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15253, "endOffset": 16566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16636, "endOffset": 17182, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16721, "endOffset": 17174, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17238, "endOffset": 18959, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17321, "endOffset": 18951, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}, {"scriptId": "1325", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/entities/QuizAnswerService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31791, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31791, "count": 1}], "isBlockCoverage": true}, {"functionName": "saveAnswer", "ranges": [{"startOffset": 642, "endOffset": 1086, "count": 7}, {"startOffset": 747, "endOffset": 975, "count": 2}, {"startOffset": 975, "endOffset": 1080, "count": 5}], "isBlockCoverage": true}, {"functionName": "batchSaveAnswers", "ranges": [{"startOffset": 1113, "endOffset": 1674, "count": 2}, {"startOffset": 1224, "endOffset": 1290, "count": 4}, {"startOffset": 1276, "endOffset": 1290, "count": 3}, {"startOffset": 1290, "endOffset": 1668, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAllAnswers", "ranges": [{"startOffset": 1701, "endOffset": 2002, "count": 0}], "isBlockCoverage": false}, {"functionName": "getSessionAnswers", "ranges": [{"startOffset": 2032, "endOffset": 2339, "count": 1}, {"startOffset": 2225, "endOffset": 2333, "count": 0}], "isBlockCoverage": true}, {"functionName": "getQuestionAnswer", "ranges": [{"startOffset": 2369, "endOffset": 2699, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserAnswerHistory", "ranges": [{"startOffset": 2729, "endOffset": 3070, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAnswerStats", "ranges": [{"startOffset": 3097, "endOffset": 3393, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOptionSelectionStats", "ranges": [{"startOffset": 3422, "endOffset": 3740, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserAnswerAnalysis", "ranges": [{"startOffset": 3769, "endOffset": 6473, "count": 2}, {"startOffset": 4000, "endOffset": 6354, "count": 1}, {"startOffset": 6354, "endOffset": 6467, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4392, "endOffset": 4443, "count": 3}, {"startOffset": 4438, "endOffset": 4442, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4552, "endOffset": 4603, "count": 3}, {"startOffset": 4598, "endOffset": 4602, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4715, "endOffset": 5119, "count": 3}, {"startOffset": 4886, "endOffset": 4926, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4966, "endOffset": 5085, "count": 3}, {"startOffset": 5053, "endOffset": 5057, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5195, "endOffset": 5397, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5404, "endOffset": 5449, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5553, "endOffset": 5743, "count": 3}, {"startOffset": 5701, "endOffset": 5705, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5817, "endOffset": 5921, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5928, "endOffset": 5961, "count": 1}], "isBlockCoverage": true}, {"functionName": "deleteSessionAnswers", "ranges": [{"startOffset": 6503, "endOffset": 6959, "count": 1}, {"startOffset": 6842, "endOffset": 6953, "count": 0}], "isBlockCoverage": true}, {"functionName": "updateAnswer", "ranges": [{"startOffset": 6984, "endOffset": 7460, "count": 2}, {"startOffset": 7347, "endOffset": 7454, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateCreate", "ranges": [{"startOffset": 7479, "endOffset": 8330, "count": 11}, {"startOffset": 7538, "endOffset": 7604, "count": 2}, {"startOffset": 7604, "endOffset": 7636, "count": 9}, {"startOffset": 7636, "endOffset": 7703, "count": 1}, {"startOffset": 7703, "endOffset": 7782, "count": 8}, {"startOffset": 7784, "endOffset": 7868, "count": 1}, {"startOffset": 7868, "endOffset": 7901, "count": 7}, {"startOffset": 7901, "endOffset": 7969, "count": 1}, {"startOffset": 7969, "endOffset": 8018, "count": 6}, {"startOffset": 8018, "endOffset": 8079, "count": 2}, {"startOffset": 8081, "endOffset": 8167, "count": 1}, {"startOffset": 8167, "endOffset": 8216, "count": 5}, {"startOffset": 8216, "endOffset": 8244, "count": 1}, {"startOffset": 8246, "endOffset": 8324, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateUpdate", "ranges": [{"startOffset": 8335, "endOffset": 8902, "count": 2}, {"startOffset": 8415, "endOffset": 8455, "count": 0}, {"startOffset": 8457, "endOffset": 8541, "count": 0}, {"startOffset": 8590, "endOffset": 8651, "count": 1}, {"startOffset": 8653, "endOffset": 8739, "count": 0}, {"startOffset": 8788, "endOffset": 8816, "count": 1}, {"startOffset": 8818, "endOffset": 8896, "count": 0}], "isBlockCoverage": true}, {"functionName": "QuizAnswerService", "ranges": [{"startOffset": 8907, "endOffset": 9035, "count": 14}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 9143, "endOffset": 9176, "count": 14}], "isBlockCoverage": true}], "startOffset": 185}, {"scriptId": "1326", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/base/BaseService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16012, "count": 1}], "isBlockCoverage": true}, {"functionName": "_define_property", "ranges": [{"startOffset": 220, "endOffset": 518, "count": 28}, {"startOffset": 285, "endOffset": 461, "count": 0}], "isBlockCoverage": true}, {"functionName": "on", "ranges": [{"startOffset": 576, "endOffset": 739, "count": 2}], "isBlockCoverage": true}, {"functionName": "off", "ranges": [{"startOffset": 744, "endOffset": 1000, "count": 0}], "isBlockCoverage": false}, {"functionName": "emit", "ranges": [{"startOffset": 1005, "endOffset": 1374, "count": 6}, {"startOffset": 1101, "endOffset": 1368, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1133, "endOffset": 1356, "count": 2}, {"startOffset": 1226, "endOffset": 1342, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeAllListeners", "ranges": [{"startOffset": 1379, "endOffset": 1532, "count": 0}], "isBlockCoverage": false}, {"functionName": "BrowserEventEmitter", "ranges": [{"startOffset": 1537, "endOffset": 1610, "count": 14}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 1686, "endOffset": 2104, "count": 0}], "isBlockCoverage": false}, {"functionName": "findById", "ranges": [{"startOffset": 2133, "endOffset": 2488, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 2513, "endOffset": 3027, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 3052, "endOffset": 3495, "count": 0}], "isBlockCoverage": false}, {"functionName": "findAll", "ranges": [{"startOffset": 3522, "endOffset": 3799, "count": 0}], "isBlockCoverage": false}, {"functionName": "count", "ranges": [{"startOffset": 3826, "endOffset": 4099, "count": 0}], "isBlockCoverage": false}, {"functionName": "createSuccessResult", "ranges": [{"startOffset": 4116, "endOffset": 4221, "count": 9}], "isBlockCoverage": true}, {"functionName": "createErrorResult", "ranges": [{"startOffset": 4226, "endOffset": 4536, "count": 6}, {"startOffset": 4330, "endOffset": 4345, "count": 0}], "isBlockCoverage": true}, {"functionName": "BaseService", "ranges": [{"startOffset": 4541, "endOffset": 4664, "count": 14}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4766, "endOffset": 4793, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}, {"scriptId": "1327", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/entities/QuizAnswerRepository.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33409, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33409, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAllAnswers", "ranges": [{"startOffset": 505, "endOffset": 848, "count": 0}], "isBlockCoverage": false}, {"functionName": "findBySessionId", "ranges": [{"startOffset": 879, "endOffset": 1237, "count": 0}], "isBlockCoverage": false}, {"functionName": "findByQuestionId", "ranges": [{"startOffset": 1268, "endOffset": 1718, "count": 0}], "isBlockCoverage": false}, {"functionName": "findByUserId", "ranges": [{"startOffset": 1751, "endOffset": 2371, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteBySessionId", "ranges": [{"startOffset": 2401, "endOffset": 2682, "count": 0}], "isBlockCoverage": false}, {"functionName": "batchInsertAnswers", "ranges": [{"startOffset": 2709, "endOffset": 3223, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAnswerStats", "ranges": [{"startOffset": 3252, "endOffset": 3840, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOptionSelectionStats", "ranges": [{"startOffset": 3869, "endOffset": 4453, "count": 0}], "isBlockCoverage": false}, {"functionName": "mapRowToEntity", "ranges": [{"startOffset": 4458, "endOffset": 5101, "count": 0}], "isBlockCoverage": false}, {"functionName": "mapEntityToRow", "ranges": [{"startOffset": 5106, "endOffset": 5775, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildInsertQuery", "ranges": [{"startOffset": 5780, "endOffset": 6772, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildUpdateQuery", "ranges": [{"startOffset": 6777, "endOffset": 7958, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildSelectQuery", "ranges": [{"startOffset": 7963, "endOffset": 8718, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildCountQuery", "ranges": [{"startOffset": 8723, "endOffset": 9333, "count": 0}], "isBlockCoverage": false}, {"functionName": "extractIdFromCreateData", "ranges": [{"startOffset": 9338, "endOffset": 9457, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseJSON", "ranges": [{"startOffset": 9462, "endOffset": 9640, "count": 0}], "isBlockCoverage": false}, {"functionName": "stringifyJSON", "ranges": [{"startOffset": 9645, "endOffset": 9839, "count": 0}], "isBlockCoverage": false}, {"functionName": "QuizAnswerRepository", "ranges": [{"startOffset": 9844, "endOffset": 9907, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10018, "endOffset": 10054, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}, {"scriptId": "1328", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/services/base/BaseRepository.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27358, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27358, "count": 1}], "isBlockCoverage": true}, {"functionName": "_define_property", "ranges": [{"startOffset": 216, "endOffset": 514, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDatabase", "ranges": [{"startOffset": 565, "endOffset": 610, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDb", "ranges": [{"startOffset": 638, "endOffset": 771, "count": 0}], "isBlockCoverage": false}, {"functionName": "create", "ranges": [{"startOffset": 797, "endOffset": 1349, "count": 0}], "isBlockCoverage": false}, {"functionName": "findById", "ranges": [{"startOffset": 1378, "endOffset": 2019, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 2044, "endOffset": 2545, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 2570, "endOffset": 2950, "count": 0}], "isBlockCoverage": false}, {"functionName": "find<PERSON>any", "ranges": [{"startOffset": 2977, "endOffset": 3426, "count": 0}], "isBlockCoverage": false}, {"functionName": "count", "ranges": [{"startOffset": 3453, "endOffset": 3913, "count": 0}], "isBlockCoverage": false}, {"functionName": "batchInsert", "ranges": [{"startOffset": 3938, "endOffset": 4594, "count": 0}], "isBlockCoverage": false}, {"functionName": "batchUpdate", "ranges": [{"startOffset": 4619, "endOffset": 5284, "count": 0}], "isBlockCoverage": false}, {"functionName": "executeRawQuery", "ranges": [{"startOffset": 5314, "endOffset": 5609, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildSelectQuery", "ranges": [{"startOffset": 5636, "endOffset": 6608, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildCountQuery", "ranges": [{"startOffset": 6613, "endOffset": 7161, "count": 0}], "isBlockCoverage": false}, {"functionName": "buildWhereClause", "ranges": [{"startOffset": 7191, "endOffset": 7662, "count": 0}], "isBlockCoverage": false}, {"functionName": "escapeIdentifier", "ranges": [{"startOffset": 7691, "endOffset": 7781, "count": 0}], "isBlockCoverage": false}, {"functionName": "BaseRepository", "ranges": [{"startOffset": 7786, "endOffset": 7976, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8081, "endOffset": 8111, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}