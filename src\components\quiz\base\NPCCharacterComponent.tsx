/**
 * Quiz NPC角色组件
 * 支持多种中医文化样式的NPC角色组件
 */

import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { NPCCharacterComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type NPCCharacterComponentConfig = z.infer<typeof NPCCharacterComponentConfigSchema>;

export interface NPCCharacterComponentProps extends BaseQuizComponentProps<NPCCharacterComponentConfig> {
  characterName: Record<string, string>;
  characterDescription?: Record<string, string>;
  avatarUrl?: string;
  onInteract?: () => void;
  disabled?: boolean;
}

interface NPCCharacterComponentState extends ComponentState {
  current_emotion: string;
  is_speaking: boolean;
  is_thinking: boolean;
  animation_cycle: number;
  hover_state: boolean;
}

/**
 * NPC角色组件类
 */
export class NPCCharacterComponent extends BaseQuizComponent<
  NPCCharacterComponentConfig,
  NPCCharacterComponentProps,
  NPCCharacterComponentState
> {
  private animationIntervalId?: number;
  private containerRef = React.createRef<HTMLDivElement>();

  extractConfig(props: NPCCharacterComponentProps): NPCCharacterComponentConfig {
    return props.config;
  }

  getInitialState(): NPCCharacterComponentState {
    return {
      is_loading: false,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      current_emotion: this.config.character.default_emotion,
      is_speaking: false,
      is_thinking: false,
      animation_cycle: 0,
      hover_state: false
    };
  }

  componentDidMount(): void {
    if (this.config.character.animated) {
      this.startIdleAnimation();
    }
  }

  componentWillUnmount(): void {
    if (this.animationIntervalId) {
      clearInterval(this.animationIntervalId);
    }
  }

  componentDidUpdate(prevProps: NPCCharacterComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.disabled !== this.props.disabled) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled
      });
    }
  }

  /**
   * 开始空闲动画
   */
  private startIdleAnimation(): void {
    if (this.personalization.layer5_accessibility?.reduce_motion) {
      return;
    }

    this.animationIntervalId = window.setInterval(() => {
      this.setState(prevState => ({
        animation_cycle: (prevState.animation_cycle + 1) % 4
      }));
    }, 2000); // 每2秒一个动画周期
  }

  /**
   * 处理角色交互
   */
  private handleCharacterClick = (): void => {
    if (this.state.is_disabled) return;

    this.setState({ is_speaking: true });
    this.props.onInteract?.();

    // 触发触觉反馈
    this.triggerHapticFeedback('medium');

    // 发送交互事件
    this.emitInteractionEvent('click', {
      action: 'character_interact',
      character_emotion: this.state.current_emotion
    });

    // 说话动画持续2秒
    setTimeout(() => {
      this.setState({ is_speaking: false });
    }, 2000);
  };

  /**
   * 处理鼠标悬停
   */
  private handleMouseEnter = (): void => {
    if (this.state.is_disabled) return;

    this.setState({ hover_state: true });
    
    // 发送交互事件
    this.emitInteractionEvent('focus', {
      action: 'character_hover'
    });
  };

  /**
   * 处理鼠标离开
   */
  private handleMouseLeave = (): void => {
    this.setState({ hover_state: false });
  };

  /**
   * 改变角色情绪
   */
  public changeEmotion(emotion: string): void {
    this.setState({ current_emotion: emotion });
    
    // 发送交互事件
    this.emitInteractionEvent('focus', {
      action: 'emotion_changed',
      new_emotion: emotion
    });
  }

  /**
   * 触发思考状态
   */
  public startThinking(): void {
    this.setState({ is_thinking: true });
    
    // 思考动画持续3秒
    setTimeout(() => {
      this.setState({ is_thinking: false });
    }, 3000);
  }

  /**
   * 获取角色名称
   */
  private getCharacterName(): string {
    const { language } = this.context || { language: 'zh' };
    return this.props.characterName[language] || this.props.characterName['zh'] || this.props.characterName['en'] || '';
  }

  /**
   * 获取角色描述
   */
  private getCharacterDescription(): string {
    if (!this.props.characterDescription) return '';
    
    const { language } = this.context || { language: 'zh' };
    return this.props.characterDescription[language] || this.props.characterDescription['zh'] || this.props.characterDescription['en'] || '';
  }

  /**
   * 获取角色样式类名
   */
  private getCharacterStyleClassName(): string {
    const style = this.config.character.character_style;
    return `quiz-npc-character-${style}`;
  }

  /**
   * 获取情绪样式类名
   */
  private getEmotionClassName(): string {
    return `quiz-npc-emotion-${this.state.current_emotion}`;
  }

  /**
   * 渲染角色头像
   */
  private renderAvatar(): React.ReactNode {
    const avatarUrl = this.props.avatarUrl || this.config.character.default_avatar;
    
    if (!avatarUrl) {
      // 默认角色图标
      return (
        <div className="quiz-npc-default-avatar">
          {this.getCharacterStyleEmoji()}
        </div>
      );
    }

    return (
      <img
        src={avatarUrl}
        alt={this.getCharacterName()}
        className="quiz-npc-avatar-image"
        onError={(e) => {
          // 头像加载失败时显示默认图标
          e.currentTarget.style.display = 'none';
          const defaultAvatar = e.currentTarget.parentElement?.querySelector('.quiz-npc-default-avatar');
          if (defaultAvatar) {
            (defaultAvatar as HTMLElement).style.display = 'flex';
          }
        }}
      />
    );
  }

  /**
   * 获取角色样式对应的表情符号
   */
  private getCharacterStyleEmoji(): string {
    const style = this.config.character.character_style;
    const emotion = this.state.current_emotion;
    
    const emojiMap: Record<string, Record<string, string>> = {
      traditional_doctor: {
        happy: '👨‍⚕️',
        neutral: '🧑‍⚕️',
        concerned: '😟',
        wise: '🧙‍♂️'
      },
      wise_elder: {
        happy: '👴',
        neutral: '🧓',
        concerned: '😔',
        wise: '🧙‍♂️'
      },
      friendly_guide: {
        happy: '😊',
        neutral: '🙂',
        concerned: '😕',
        wise: '🤔'
      },
      mystical_sage: {
        happy: '🧙‍♂️',
        neutral: '🧙',
        concerned: '😰',
        wise: '🔮'
      }
    };
    
    return emojiMap[style]?.[emotion] || '🙂';
  }

  /**
   * 渲染角色状态指示器
   */
  private renderStatusIndicators(): React.ReactNode {
    return (
      <div className="quiz-npc-status-indicators">
        {/* 说话指示器 */}
        {this.state.is_speaking && (
          <div className="quiz-npc-speaking-indicator">
            <div className="quiz-npc-speech-bubble">💬</div>
          </div>
        )}

        {/* 思考指示器 */}
        {this.state.is_thinking && (
          <div className="quiz-npc-thinking-indicator">
            <div className="quiz-npc-thought-bubble">💭</div>
          </div>
        )}

        {/* 动画指示器 */}
        {this.config.character.animated && !this.state.is_speaking && !this.state.is_thinking && (
          <div className={`quiz-npc-idle-indicator cycle-${this.state.animation_cycle}`}>
            ✨
          </div>
        )}
      </div>
    );
  }

  /**
   * 渲染角色信息
   */
  private renderCharacterInfo(): React.ReactNode {
    const characterName = this.getCharacterName();
    const characterDescription = this.getCharacterDescription();
    
    if (!this.config.character.show_name && !characterDescription) {
      return null;
    }

    return (
      <div className="quiz-npc-character-info">
        {this.config.character.show_name && characterName && (
          <div className="quiz-npc-character-name">
            {characterName}
          </div>
        )}
        
        {characterDescription && (
          <div className="quiz-npc-character-description">
            {characterDescription}
          </div>
        )}
      </div>
    );
  }

  /**
   * 渲染交互提示
   */
  private renderInteractionHint(): React.ReactNode {
    if (!this.state.hover_state || this.state.is_disabled) return null;
    
    return (
      <div className="quiz-npc-interaction-hint">
        <span className="quiz-npc-hint-text">
          {this.context?.language === 'zh' ? '点击交互' : 'Click to interact'}
        </span>
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-npc-character-component',
      this.getCharacterStyleClassName(),
      this.getEmotionClassName(),
      this.state.is_speaking && 'quiz-npc-speaking',
      this.state.is_thinking && 'quiz-npc-thinking',
      this.state.hover_state && 'quiz-npc-hovered',
      this.state.is_disabled && 'quiz-npc-disabled',
      this.config.character.animated && 'quiz-npc-animated',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        ref={this.containerRef}
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
        onClick={this.handleCharacterClick}
        onMouseEnter={this.handleMouseEnter}
        onMouseLeave={this.handleMouseLeave}
        role="button"
        aria-label={`${this.getCharacterName()} - ${this.getCharacterDescription()}`}
        tabIndex={this.state.is_disabled ? -1 : 0}
      >
        {/* 角色头像容器 */}
        <div className="quiz-npc-avatar-container">
          {this.renderAvatar()}
          
          {/* 默认头像备用 */}
          <div className="quiz-npc-default-avatar" style={{ display: 'none' }}>
            {this.getCharacterStyleEmoji()}
          </div>
        </div>

        {/* 状态指示器 */}
        {this.renderStatusIndicators()}

        {/* 角色信息 */}
        {this.renderCharacterInfo()}

        {/* 交互提示 */}
        {this.renderInteractionHint()}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'button';
  }

  protected getAriaLabel(): string {
    const characterName = this.getCharacterName();
    const emotion = this.state.current_emotion;
    return `${characterName} character (${emotion})`;
  }
}

// 使用Context的函数式组件包装器
const NPCCharacterComponentWrapper: React.FC<NPCCharacterComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <NPCCharacterComponent.contextType.Provider value={{ language }}>
      <NPCCharacterComponent {...props} />
    </NPCCharacterComponent.contextType.Provider>
  );
};

// 设置Context类型
NPCCharacterComponent.contextType = React.createContext({ language: 'zh' });

export default NPCCharacterComponentWrapper;
