# tRPC API设计

## 🎯 API设计原则

### 1. 类型安全优先
- 全栈TypeScript类型共享
- Zod运行时验证
- 自动生成的API客户端

### 2. 模块化路由设计
- 按功能域划分路由
- 清晰的命名约定
- 一致的错误处理

### 3. 性能优化
- 智能缓存策略
- 批量操作支持
- 流式数据传输

## 🏗️ tRPC路由结构

```typescript
// 主路由器结构
export const appRouter = router({
  // 量表包管理
  quizPacks: quizPacksRouter,
  
  // 会话管理
  quizSessions: quizSessionsRouter,
  
  // 个性化配置
  personalization: personalizationRouter,
  
  // 情绪分析
  emotionAnalysis: emotionAnalysisRouter,
  
  // 用户数据
  userData: userDataRouter,
  
  // 推荐系统
  recommendations: recommendationsRouter,
});

export type AppRouter = typeof appRouter;
```

## 📦 量表包路由 (Quiz Packs Router)

```typescript
import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../trpc';

// Zod验证模式
const QuizPackFilterSchema = z.object({
  category: z.enum(['daily', 'therapy', 'assessment', 'research']).optional(),
  difficulty: z.enum(['beginner', 'regular', 'advanced', 'vip']).optional(),
  user_id: z.string().optional(),
  include_inactive: z.boolean().default(false),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

const QuizPackResponseSchema = z.object({
  pack_id: z.string(),
  name: z.string(),
  description: z.string(),
  version: z.string(),
  emotion_data_set_id: z.string(),
  metadata: z.object({
    category: z.string(),
    difficulty_level: z.string(),
    estimated_duration_minutes: z.number(),
    total_questions_approx: z.number(),
  }),
  default_presentation_hints: z.object({
    suggested_view_type: z.enum(['wheel', 'card', 'bubble', 'galaxy']),
    suggested_interaction_style: z.string(),
    content_complexity_level: z.enum(['simple', 'standard', 'detailed']),
  }).optional(),
  is_active: z.boolean(),
  created_at: z.date(),
  updated_at: z.date(),
});

export const quizPacksRouter = router({
  // 获取量表包列表
  list: publicProcedure
    .input(QuizPackFilterSchema)
    .output(z.object({
      quiz_packs: z.array(QuizPackResponseSchema),
      total: z.number(),
      has_more: z.boolean(),
    }))
    .query(async ({ input, ctx }) => {
      const { category, difficulty, user_id, include_inactive, limit, offset } = input;
      
      const quizPackService = new QuizPackService(ctx.db);
      const result = await quizPackService.findQuizPacks({
        category,
        difficulty,
        userId: user_id,
        includeInactive: include_inactive,
        limit,
        offset,
      });
      
      return {
        quiz_packs: result.items,
        total: result.total,
        has_more: result.total > offset + limit,
      };
    }),

  // 获取特定量表包详情
  getById: publicProcedure
    .input(z.object({
      pack_id: z.string(),
    }))
    .output(QuizPackResponseSchema)
    .query(async ({ input, ctx }) => {
      const quizPackService = new QuizPackService(ctx.db);
      const quizPack = await quizPackService.findById(input.pack_id);
      
      if (!quizPack) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Quiz pack not found',
        });
      }
      
      return quizPack;
    }),

  // 获取用户推荐的量表包
  getRecommendations: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(20).default(10),
      based_on: z.enum(['history', 'preferences', 'ai_analysis']).default('preferences'),
    }))
    .output(z.object({
      recommendations: z.array(z.object({
        quiz_pack: QuizPackResponseSchema,
        recommendation_score: z.number().min(0).max(1),
        recommendation_reason: z.string(),
      })),
    }))
    .query(async ({ input, ctx }) => {
      const recommendationService = new QuizPackRecommendationService(ctx.db);
      const recommendations = await recommendationService.generateRecommendations(
        ctx.user.id,
        input.based_on,
        input.limit
      );
      
      return { recommendations };
    }),
});
```

## 🎮 会话管理路由 (Quiz Sessions Router)

```typescript
// 会话相关的Zod模式
const CreateSessionSchema = z.object({
  pack_id: z.string(),
  personalization_overrides: z.object({
    layer1_user_choice: z.object({
      preferred_view_type: z.enum(['wheel', 'card', 'bubble', 'galaxy']).optional(),
      color_mode: z.enum(['warm', 'cool', 'neutral']).optional(),
    }).optional(),
    layer4_view_detail: z.object({
      emotion_presentation: z.object({
        emotion_grouping_style: z.enum(['by_category', 'by_intensity']).optional(),
        tier_transition_animation: z.enum(['fade', 'rotate', 'zoom']).optional(),
      }).optional(),
    }).optional(),
  }).optional(),
});

const QuestionPresentationDataSchema = z.object({
  session_id: z.string(),
  question_id: z.string(),
  question_text_localized: z.string(),
  primary_interaction_type: z.string(),
  
  // 个性化UI配置
  personalized_ui_config: z.object({
    background_config: z.object({
      background_id: z.string(),
      theme: z.string(),
    }),
    theme_config: z.object({
      color_palette: z.record(z.string()),
      font_config: z.object({
        primary_font: z.string(),
        size_scale: z.number(),
      }),
    }),
    wheel_config: z.object({
      container_size: z.number(),
      wheel_radius: z.number(),
      sector_gap: z.number(),
      emotion_display_mode: z.string(),
    }).optional(),
  }),
  
  // 情绪选项
  emotion_options: z.array(z.object({
    emotion_id: z.string(),
    emotion_name: z.string(),
    tier_level: z.number(),
    display_config: z.object({
      component_type: z.string(),
      visual_style: z.record(z.any()),
      content: z.object({
        text_localized: z.string(),
        emoji: z.string(),
        color: z.string(),
        intensity_indicator: z.number().optional(),
      }),
    }),
  })),
  
  // 进度信息
  progress_info: z.object({
    current_tier: z.number(),
    total_tiers: z.number(),
    completion_percentage: z.number(),
    estimated_remaining_time: z.string().optional(),
  }),
  
  // 导航配置
  navigation_config: z.object({
    show_back_button: z.boolean(),
    show_skip_button: z.boolean(),
    show_progress_indicator: z.boolean(),
  }),
});

const SubmitEmotionAnswerSchema = z.object({
  tier_id: z.string(),
  emotion_id: z.string(),
  confidence: z.number().min(0).max(1).optional(),
  response_time_ms: z.number().min(0).optional(),
  interaction_method: z.enum(['click', 'touch', 'keyboard', 'voice']).optional(),
});

export const quizSessionsRouter = router({
  // 创建新的量表会话
  create: protectedProcedure
    .input(CreateSessionSchema)
    .output(z.object({
      session_id: z.string(),
      pack_id: z.string(),
      user_id: z.string(),
      status: z.enum(['INITIATED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABORTED']),
      total_tiers: z.number(),
      created_at: z.date(),
    }))
    .mutation(async ({ input, ctx }) => {
      const sessionService = new QuizSessionService(ctx.db);
      const session = await sessionService.createPersonalizedSession(
        input.pack_id,
        ctx.user.id,
        input.personalization_overrides
      );
      
      return session;
    }),

  // 获取当前问题呈现数据
  getCurrentQuestion: protectedProcedure
    .input(z.object({
      session_id: z.string(),
    }))
    .output(QuestionPresentationDataSchema.nullable())
    .query(async ({ input, ctx }) => {
      const sessionService = new QuizSessionService(ctx.db);
      
      // 验证会话所有权
      await sessionService.validateSessionOwnership(input.session_id, ctx.user.id);
      
      const questionData = await sessionService.getPersonalizedQuestionPresentation(
        input.session_id
      );
      
      return questionData;
    }),

  // 提交情绪选择答案
  submitEmotionAnswer: protectedProcedure
    .input(z.object({
      session_id: z.string(),
    }).merge(SubmitEmotionAnswerSchema))
    .output(z.object({
      session_id: z.string(),
      question_id: z.string(),
      is_valid: z.boolean(),
      validation_error_key: z.string().optional(),
      next_action_hint: z.string().optional(),
      realtime_insights: z.array(z.object({
        type: z.string(),
        message: z.string(),
        confidence: z.number(),
      })).optional(),
      progress_update: z.object({
        current_tier: z.number(),
        total_tiers: z.number(),
        completion_percentage: z.number(),
      }).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const sessionService = new QuizSessionService(ctx.db);
      
      // 验证会话所有权
      await sessionService.validateSessionOwnership(input.session_id, ctx.user.id);
      
      const result = await sessionService.submitEmotionAnswer(
        input.session_id,
        input.tier_id,
        input.emotion_id,
        {
          confidence: input.confidence,
          responseTime: input.response_time_ms,
          interactionMethod: input.interaction_method,
        }
      );
      
      return result;
    }),

  // 获取实时洞察
  getRealtimeInsights: protectedProcedure
    .input(z.object({
      session_id: z.string(),
    }))
    .output(z.object({
      insights: z.array(z.object({
        type: z.string(),
        message: z.string(),
        confidence: z.number(),
      })),
      confidence: z.number(),
    }))
    .query(async ({ input, ctx }) => {
      const sessionService = new QuizSessionService(ctx.db);
      await sessionService.validateSessionOwnership(input.session_id, ctx.user.id);
      
      const insights = await sessionService.getRealtimeInsights(input.session_id);
      return insights;
    }),

  // 完成会话并获取结果
  complete: protectedProcedure
    .input(z.object({
      session_id: z.string(),
    }))
    .output(z.object({
      result_id: z.string(),
      session_id: z.string(),
      user_id: z.string(),
      pack_id: z.string(),
      completion_percentage: z.number(),
      emotion_analysis: z.object({
        dominant_emotions: z.array(z.string()),
        emotional_stability_index: z.number(),
        emotional_complexity_index: z.number(),
        pattern_analysis: z.record(z.any()),
      }),
      personalized_recommendations: z.array(z.object({
        type: z.string(),
        title: z.string(),
        description: z.string(),
        confidence: z.number(),
        priority: z.number(),
      })),
      visual_summary: z.object({
        primary_visualization: z.record(z.any()),
        secondary_visualizations: z.array(z.record(z.any())),
        export_options: z.array(z.string()),
      }),
      created_at: z.date(),
    }))
    .mutation(async ({ input, ctx }) => {
      const sessionService = new QuizSessionService(ctx.db);
      await sessionService.validateSessionOwnership(input.session_id, ctx.user.id);
      
      const result = await sessionService.completeEmotionQuizSession(input.session_id);
      return result;
    }),

  // 会话控制操作
  pause: protectedProcedure
    .input(z.object({ session_id: z.string() }))
    .output(z.object({ status: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const sessionService = new QuizSessionService(ctx.db);
      await sessionService.validateSessionOwnership(input.session_id, ctx.user.id);
      await sessionService.pauseSession(input.session_id);
      return { status: 'PAUSED' };
    }),

  resume: protectedProcedure
    .input(z.object({ session_id: z.string() }))
    .output(QuestionPresentationDataSchema.nullable())
    .mutation(async ({ input, ctx }) => {
      const sessionService = new QuizSessionService(ctx.db);
      await sessionService.validateSessionOwnership(input.session_id, ctx.user.id);
      const questionData = await sessionService.resumeSession(input.session_id);
      return questionData;
    }),

  abort: protectedProcedure
    .input(z.object({ session_id: z.string() }))
    .output(z.object({ status: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const sessionService = new QuizSessionService(ctx.db);
      await sessionService.validateSessionOwnership(input.session_id, ctx.user.id);
      await sessionService.abortSession(input.session_id);
      return { status: 'ABORTED' };
    }),
});
```

## 🎨 个性化配置路由 (Personalization Router)

```typescript
const UserPresentationConfigSchema = z.object({
  layer0_dataset_presentation: z.object({
    preferred_pack_categories: z.array(z.string()),
    default_difficulty_preference: z.enum(['beginner', 'regular', 'advanced', 'vip']),
    session_length_preference: z.enum(['short', 'medium', 'long']),
  }),
  layer1_user_choice: z.object({
    preferred_view_type: z.enum(['wheel', 'card', 'bubble', 'galaxy']),
    active_skin_id: z.string(),
    dark_mode: z.boolean(),
    color_mode: z.enum(['warm', 'cool', 'neutral']),
    user_level: z.enum(['beginner', 'regular', 'advanced', 'vip']),
  }),
  // ... 其他层级配置
});

export const personalizationRouter = router({
  // 获取用户个性化配置
  getConfig: protectedProcedure
    .output(UserPresentationConfigSchema)
    .query(async ({ ctx }) => {
      const personalizationService = new PersonalizationService(ctx.db);
      const config = await personalizationService.getUserPresentationConfig(ctx.user.id);
      return config;
    }),

  // 更新用户个性化配置
  updateConfig: protectedProcedure
    .input(UserPresentationConfigSchema.partial())
    .output(UserPresentationConfigSchema)
    .mutation(async ({ input, ctx }) => {
      const personalizationService = new PersonalizationService(ctx.db);
      const updatedConfig = await personalizationService.updateUserPresentationConfig(
        ctx.user.id,
        input
      );
      return updatedConfig;
    }),

  // 获取量表特定的展现覆盖
  getPackOverrides: protectedProcedure
    .input(z.object({ pack_id: z.string() }))
    .output(z.object({
      presentation_overrides: UserPresentationConfigSchema.partial().optional(),
      tier_presentation_overrides: z.record(z.any()).optional(),
    }))
    .query(async ({ input, ctx }) => {
      const personalizationService = new PersonalizationService(ctx.db);
      const overrides = await personalizationService.getPackSpecificOverrides(
        ctx.user.id,
        input.pack_id
      );
      return overrides || { presentation_overrides: undefined, tier_presentation_overrides: undefined };
    }),

  // 更新量表特定的展现覆盖
  updatePackOverrides: protectedProcedure
    .input(z.object({
      pack_id: z.string(),
      presentation_overrides: UserPresentationConfigSchema.partial().optional(),
      tier_presentation_overrides: z.record(z.any()).optional(),
    }))
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ input, ctx }) => {
      const personalizationService = new PersonalizationService(ctx.db);
      await personalizationService.updatePackSpecificOverrides(
        ctx.user.id,
        input.pack_id,
        {
          presentation_overrides: input.presentation_overrides,
          tier_presentation_overrides: input.tier_presentation_overrides,
        }
      );
      return { success: true };
    }),
});
```

## 🔧 错误处理和中间件

```typescript
// 自定义错误类型
export class QuizSessionError extends TRPCError {
  constructor(message: string, cause?: unknown) {
    super({
      code: 'BAD_REQUEST',
      message,
      cause,
    });
  }
}

export class SessionOwnershipError extends TRPCError {
  constructor() {
    super({
      code: 'FORBIDDEN',
      message: 'You do not have permission to access this session',
    });
  }
}

// 会话验证中间件
const sessionValidationMiddleware = middleware(async ({ ctx, next, input }) => {
  if ('session_id' in input && typeof input.session_id === 'string') {
    const sessionService = new QuizSessionService(ctx.db);
    const session = await sessionService.findById(input.session_id);
    
    if (!session) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Session not found',
      });
    }
    
    if (session.user_id !== ctx.user?.id) {
      throw new SessionOwnershipError();
    }
  }
  
  return next();
});

// 应用中间件到需要的路由
export const protectedSessionProcedure = protectedProcedure.use(sessionValidationMiddleware);
```

这个tRPC API设计提供了类型安全、模块化和高性能的API接口，完美支持Quiz系统的所有功能需求。
