/**
 * WebGPU轮盘视图
 * 使用WebGPU实现的轮盘视图
 *
 * 此组件是新视图系统的一部分，用于替代旧的轮盘实现
 * 它直接实现了轮盘的渲染，不依赖旧的轮盘类
 */

import type { Emotion, ContentDisplayMode, ViewConfig, RenderEngine, SkinConfig } from '@/types';
import { BaseEmotionView } from '@/views/base/BaseEmotionView';
import type React from 'react';

// 使用新的组件，不依赖旧的轮盘实现
import { WebGPUWheelComponent } from '@/views/components/wheels/WebGPUWheelComponent';

/**
 * WebGPU轮盘视图类
 * 使用WebGPU实现的轮盘视图
 */
export class WebGPUWheelView extends BaseEmotionView {
  /**
   * 构造函数
   * @param contentDisplayMode 内容显示模式
   * @param skinConfig 皮肤配置
   */
  constructor(contentDisplayMode: ContentDisplayMode, skinConfig: SkinConfig) {
    super('wheel', contentDisplayMode, skinConfig);
  }

  /**
   * 获取实现类型
   * @returns 实现类型
   */
  getImplementation(): RenderEngine {
    return 'WebGPU';
  }

  // 注意：原来的 setContentStrategy 方法已被移除，
  // 现在使用 setContentDisplayMode 方法来设置内容显示模式

  /**
   * 渲染轮盘
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  renderWheel(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    // 从配置中获取额外属性
    const onBack = config?.onBack;
    const selectedPath = config?.selectedPath;

    return (
      <WebGPUWheelComponent
        emotions={emotions}
        tierLevel={tierLevel}
        contentDisplayMode={this.contentDisplayMode}
        skinConfig={this.skinConfig}
        onSelect={onSelect}
        onBack={onBack}
        selectedPath={selectedPath}
      />
    );
  }

  /**
   * 渲染视图
   * 实现基类的抽象方法
   * @param emotions 情绪列表
   * @param tierLevel 层级
   * @param onSelect 选择回调
   * @param config 视图配置（可选）
   * @returns React节点
   */
  render(
    emotions: Emotion[],
    tierLevel: number,
    onSelect: (emotion: Emotion) => void,
    config?: ViewConfig
  ): React.ReactNode {
    return this.renderWheel(emotions, tierLevel, onSelect, config);
  }
}
