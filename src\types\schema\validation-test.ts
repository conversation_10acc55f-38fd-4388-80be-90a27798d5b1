/**
 * Schema 验证测试
 * 
 * 验证三个 schema 文件之间的一致性和正确性
 */

import { 
  UserSchema, 
  EmotionSchema, 
  MoodEntrySchema,
  SyncStatusSchema,
  EmojiSetTypeSchema,
  AnimationTypeSchema
} from './base';

import {
  LoginInputSchema,
  RegisterInputSchema,
  UserConfigSchema,
  PaginationSchema
} from './api';

import {
  SQL_TO_ZOD_MAPPING,
  generateZodSchemaFromField,
  validateSchemaConsistency,
  parseCreateTableStatement,
  type DatabaseField,
  type DatabaseTable
} from './generator';

// ==================== 基础验证测试 ====================

/**
 * 测试基础 Schema 的有效性
 */
export function testBaseSchemas() {
  const results: { schema: string; isValid: boolean; error?: string }[] = [];

  try {
    // 测试用户 Schema
    const testUser = {
      id: 'user-123',
      username: 'testuser',
      email: '<EMAIL>',
      is_active: true,
      is_verified: false,
      is_banned: false,
      is_vip: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sync_status: 'synced' as const,
      server_updated_at: new Date().toISOString()
    };
    
    UserSchema.parse(testUser);
    results.push({ schema: 'UserSchema', isValid: true });
  } catch (error) {
    results.push({ 
      schema: 'UserSchema', 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  try {
    // 测试情绪 Schema
    const testEmotion = {
      id: 'emotion-123',
      name: 'Happy',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_deleted: false
    };
    
    EmotionSchema.parse(testEmotion);
    results.push({ schema: 'EmotionSchema', isValid: true });
  } catch (error) {
    results.push({ 
      schema: 'EmotionSchema', 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  try {
    // 测试心情记录 Schema
    const testMoodEntry = {
      id: 'mood-123',
      user_id: 'user-123',
      timestamp: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sync_status: 'sync_required' as const
    };
    
    MoodEntrySchema.parse(testMoodEntry);
    results.push({ schema: 'MoodEntrySchema', isValid: true });
  } catch (error) {
    results.push({ 
      schema: 'MoodEntrySchema', 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  return results;
}

/**
 * 测试 API Schema 的有效性
 */
export function testApiSchemas() {
  const results: { schema: string; isValid: boolean; error?: string }[] = [];

  try {
    // 测试登录输入 Schema
    const testLogin = {
      email: '<EMAIL>',
      password: 'password123'
    };
    
    LoginInputSchema.parse(testLogin);
    results.push({ schema: 'LoginInputSchema', isValid: true });
  } catch (error) {
    results.push({ 
      schema: 'LoginInputSchema', 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  try {
    // 测试注册输入 Schema
    const testRegister = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    };
    
    RegisterInputSchema.parse(testRegister);
    results.push({ schema: 'RegisterInputSchema', isValid: true });
  } catch (error) {
    results.push({ 
      schema: 'RegisterInputSchema', 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  try {
    // 测试分页 Schema
    const testPagination = {
      limit: 20,
      offset: 0
    };
    
    PaginationSchema.parse(testPagination);
    results.push({ schema: 'PaginationSchema', isValid: true });
  } catch (error) {
    results.push({ 
      schema: 'PaginationSchema', 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  return results;
}

/**
 * 测试生成器工具的功能
 */
export function testGeneratorTools() {
  const results: { test: string; isValid: boolean; error?: string }[] = [];

  try {
    // 测试字段生成
    const testField: DatabaseField = {
      name: 'test_field',
      type: 'TEXT NOT NULL',
      nullable: false,
      primaryKey: false,
      unique: false
    };
    
    const schema = generateZodSchemaFromField(testField);
    results.push({ test: 'generateZodSchemaFromField', isValid: !!schema });
  } catch (error) {
    results.push({ 
      test: 'generateZodSchemaFromField', 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  try {
    // 测试 SQL 映射
    const textMapper = SQL_TO_ZOD_MAPPING['TEXT'];
    const schema = textMapper();
    results.push({ test: 'SQL_TO_ZOD_MAPPING', isValid: !!schema });
  } catch (error) {
    results.push({ 
      test: 'SQL_TO_ZOD_MAPPING', 
      isValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  return results;
}

/**
 * 运行所有验证测试
 */
export function runAllValidationTests() {
  console.log('🧪 开始运行 Schema 验证测试...\n');

  const baseResults = testBaseSchemas();
  const apiResults = testApiSchemas();
  const generatorResults = testGeneratorTools();

  console.log('📋 基础 Schema 测试结果:');
  baseResults.forEach(result => {
    const status = result.isValid ? '✅' : '❌';
    console.log(`  ${status} ${result.schema}`);
    if (!result.isValid && result.error) {
      console.log(`     错误: ${result.error}`);
    }
  });

  console.log('\n📋 API Schema 测试结果:');
  apiResults.forEach(result => {
    const status = result.isValid ? '✅' : '❌';
    console.log(`  ${status} ${result.schema}`);
    if (!result.isValid && result.error) {
      console.log(`     错误: ${result.error}`);
    }
  });

  console.log('\n📋 生成器工具测试结果:');
  generatorResults.forEach(result => {
    const status = result.isValid ? '✅' : '❌';
    console.log(`  ${status} ${result.test}`);
    if (!result.isValid && result.error) {
      console.log(`     错误: ${result.error}`);
    }
  });

  const totalTests = baseResults.length + apiResults.length + generatorResults.length;
  const passedTests = [...baseResults, ...apiResults, ...generatorResults]
    .filter(r => r.isValid).length;

  console.log(`\n📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  return {
    total: totalTests,
    passed: passedTests,
    baseResults,
    apiResults,
    generatorResults
  };
}
