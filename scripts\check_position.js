import fs from 'node:fs';

// 读取文件内容
const filePath = 'public/seeds/init.sql';
const content = fs.readFileSync(filePath, 'utf8');

// 找到 < 字符的位置
const lessThanIndex = content.indexOf('<');
if (lessThanIndex !== -1) {
  console.log(`Found < character at position ${lessThanIndex}`);

  // 显示更多上下文
  const start = Math.max(0, lessThanIndex - 100);
  const end = Math.min(content.length, lessThanIndex + 100);
  console.log('Extended context (100 chars before and after):');
  console.log(
    `"${content.substring(start, lessThanIndex)}<<<HERE>>>${content.substring(lessThanIndex, end)}"`
  );

  // 显示行号
  const lines = content.substring(0, lessThanIndex).split('\n');
  const lineNumber = lines.length;
  const column = lines[lines.length - 1].length + 1;
  console.log(`Line number: ${lineNumber}, Column: ${column}`);

  // 显示该行的内容
  const allLines = content.split('\n');
  console.log(`Line content: "${allLines[lineNumber - 1]}"`);
} else {
  console.log('No < character found in the file');
}
