# PowerShell script to organize SQL seed files

# Define file categories
$schemaFiles = @(
    "init.sql",
    "update_emotion_selections.sql",
    "update_mood_entries.sql",
    "update_emoji_items.sql"
)

$configFiles = @(
    "emotions.sql",
    "update_emotions.sql",
    "update_tertiary_emotions.sql",
    "emoji_sets.sql",
    "additional_emoji_sets.sql",
    "image_emoji_sets.sql",
    "animated_emoji_sets.sql",
    "emotion_translations.sql",
    "ui_labels.sql",
    "ui_labels_updated.sql",
    "ui_labels_complete.sql",
    "ui_label_translations.sql",
    "ui_label_translations_updated.sql",
    "ui_label_translations_complete.sql",
    "additional_missing_ui_labels.sql",
    "tags.sql",
    "tag_translations.sql",
    "create_emotion_data_sets.sql",
    "app_settings.sql",
    "version.sql"
)

$testFiles = @(
    "users.sql",
    "user_preferences.sql",
    "user_streaks.sql",
    "mood_entries.sql",
    "mood_entry_tags.sql"
)

# Source and destination directories
$sourceDir = "public\seeds"
$schemaDir = "public\seeds\schema"
$configDir = "public\seeds\config"
$testDir = "public\seeds\test"

# Create directories if they don't exist
if (-not (Test-Path $schemaDir)) {
    New-Item -Path $schemaDir -ItemType Directory -Force
}

if (-not (Test-Path $configDir)) {
    New-Item -Path $configDir -ItemType Directory -Force
}

if (-not (Test-Path $testDir)) {
    New-Item -Path $testDir -ItemType Directory -Force
}

# Copy schema files
foreach ($file in $schemaFiles) {
    $sourcePath = Join-Path $sourceDir $file
    $destPath = Join-Path $schemaDir $file
    
    if (Test-Path $sourcePath) {
        Write-Host "Copying $file to schema directory..."
        Copy-Item -Path $sourcePath -Destination $destPath -Force
    } else {
        Write-Host "Warning: $file not found in source directory."
    }
}

# Copy config files
foreach ($file in $configFiles) {
    $sourcePath = Join-Path $sourceDir $file
    $destPath = Join-Path $configDir $file
    
    if (Test-Path $sourcePath) {
        Write-Host "Copying $file to config directory..."
        Copy-Item -Path $sourcePath -Destination $destPath -Force
    } else {
        Write-Host "Warning: $file not found in source directory."
    }
}

# Copy test files
foreach ($file in $testFiles) {
    $sourcePath = Join-Path $sourceDir $file
    $destPath = Join-Path $testDir $file
    
    if (Test-Path $sourcePath) {
        Write-Host "Copying $file to test directory..."
        Copy-Item -Path $sourcePath -Destination $destPath -Force
    } else {
        Write-Host "Warning: $file not found in source directory."
    }
}

# Copy master_new.sql to master.sql
$masterNewPath = Join-Path $sourceDir "master_new.sql"
$masterPath = Join-Path $sourceDir "master.sql"

if (Test-Path $masterNewPath) {
    Write-Host "Copying master_new.sql to master.sql..."
    Copy-Item -Path $masterNewPath -Destination $masterPath -Force
}

Write-Host "File organization completed successfully."
