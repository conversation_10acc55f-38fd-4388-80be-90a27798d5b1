# Zod数据验证模式

## 🎯 Zod模式设计原则

### 1. 类型安全优先
- 运行时类型验证
- 编译时类型推导
- 错误信息友好

### 2. 模块化组织
- 按功能域划分模式
- 可复用的基础模式
- 清晰的依赖关系

### 3. 性能优化
- 惰性验证
- 缓存验证结果
- 最小化验证开销

## 🏗️ 基础模式定义

```typescript
import { z } from 'zod';

// 基础类型模式
export const BaseSchemas = {
  // ID模式
  id: z.string().min(1, 'ID不能为空'),
  uuid: z.string().uuid('无效的UUID格式'),
  
  // 时间模式
  timestamp: z.date(),
  isoString: z.string().datetime('无效的ISO时间格式'),
  
  // 枚举模式
  userLevel: z.enum(['beginner', 'regular', 'advanced', 'vip'], {
    errorMap: () => ({ message: '无效的用户等级' })
  }),
  
  viewType: z.enum(['wheel', 'card', 'bubble', 'galaxy'], {
    errorMap: () => ({ message: '无效的视图类型' })
  }),
  
  colorMode: z.enum(['warm', 'cool', 'neutral'], {
    errorMap: () => ({ message: '无效的颜色模式' })
  }),
  
  renderEngine: z.enum(['D3', 'SVG', 'R3F', 'Canvas', 'WebGL', 'WebGPU'], {
    errorMap: () => ({ message: '无效的渲染引擎' })
  }),
  
  contentDisplayMode: z.enum(['text', 'emoji', 'textEmoji', 'animatedEmoji'], {
    errorMap: () => ({ message: '无效的内容显示模式' })
  }),
  
  // 数值模式
  percentage: z.number().min(0).max(100),
  confidence: z.number().min(0).max(1),
  positiveInt: z.number().int().positive(),
  nonNegativeInt: z.number().int().min(0),
  
  // 文本模式
  nonEmptyString: z.string().min(1, '不能为空字符串'),
  localizationKey: z.string().regex(/^[a-zA-Z0-9._-]+$/, '无效的本地化键格式'),
};
```

## 📦 量表包相关模式

```typescript
// 量表包元数据模式
export const QuizPackMetadataSchema = z.object({
  category: z.enum(['daily', 'therapy', 'assessment', 'research']),
  difficulty_level: BaseSchemas.userLevel,
  estimated_duration_minutes: BaseSchemas.positiveInt,
  total_questions_approx: BaseSchemas.positiveInt,
  target_age_range: z.tuple([z.number().int().min(0), z.number().int().max(120)]).optional(),
  requires_supervision: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
});

// 默认展现建议模式
export const DefaultPresentationHintsSchema = z.object({
  suggested_view_type: BaseSchemas.viewType,
  suggested_interaction_style: z.enum(['touch', 'click', 'keyboard', 'voice']),
  content_complexity_level: z.enum(['simple', 'standard', 'detailed']),
  recommended_session_length: z.enum(['short', 'medium', 'long']).optional(),
});

// 层级问题逻辑配置模式
export const TierQuestionLogicConfigSchema = z.object({
  tier_id: BaseSchemas.id,
  question_text_key: BaseSchemas.localizationKey,
  question_type: z.enum(['EMOTION_WHEEL_SELECT', 'EMOTION_INTENSITY_RATING', 'EMOTION_COMPARISON']),
  
  // 逻辑配置
  logic_config: z.object({
    selection_mode: z.enum(['single', 'multiple', 'rating']),
    validation_rules: z.array(z.object({
      rule_type: z.string(),
      parameters: z.record(z.any()),
    })),
    branching_logic: z.array(z.object({
      condition: z.string(),
      target_tier_id: BaseSchemas.id,
    })).optional(),
    scoring_rules: z.array(z.object({
      rule_type: z.string(),
      weight: z.number(),
      parameters: z.record(z.any()),
    })).optional(),
  }),
  
  // 情绪选项逻辑配置
  emotion_options_logic: z.object({
    inclusion_strategy: z.enum(['all_emotions', 'filtered_by_category', 'adaptive_selection']),
    filter_criteria: z.object({
      categories: z.array(z.string()).optional(),
      intensity_range: z.tuple([z.number(), z.number()]).optional(),
      exclude_emotions: z.array(BaseSchemas.id).optional(),
    }).optional(),
    max_options: BaseSchemas.positiveInt.optional(),
    randomize_order: z.boolean().default(false),
  }),
  
  // 导航逻辑
  navigation_logic: z.object({
    time_limit: BaseSchemas.positiveInt.optional(),
    allow_skip: z.boolean().default(true),
    allow_back: z.boolean().default(true),
    auto_advance_conditions: z.array(z.object({
      condition_type: z.string(),
      parameters: z.record(z.any()),
    })).optional(),
  }),
});

// 量表逻辑配置模式
export const QuizLogicConfigSchema = z.object({
  question_flow: z.object({
    type: z.enum(['tier_sequential', 'adaptive_branching', 'user_guided']),
    tier_progression_rules: z.array(z.object({
      rule_type: z.string(),
      parameters: z.record(z.any()),
    })).optional(),
  }),
  
  tier_question_configs: z.record(TierQuestionLogicConfigSchema),
  
  evaluation_strategy_id: BaseSchemas.id,
  evaluation_strategy_config: z.record(z.any()),
  
  completion_criteria: z.object({
    min_questions_answered: BaseSchemas.nonNegativeInt,
    allow_skip: z.boolean(),
    max_skip_count: BaseSchemas.nonNegativeInt.optional(),
    min_completion_percentage: BaseSchemas.percentage.optional(),
  }),
});

// 量表包模式
export const QuizPackSchema = z.object({
  pack_id: BaseSchemas.id,
  name: BaseSchemas.nonEmptyString,
  description: z.string(),
  version: z.string().regex(/^\d+\.\d+\.\d+$/, '版本号格式应为 x.y.z'),
  
  emotion_data_set_id: BaseSchemas.id,
  quiz_logic_config: QuizLogicConfigSchema,
  default_presentation_hints: DefaultPresentationHintsSchema.optional(),
  metadata: QuizPackMetadataSchema,
  
  is_active: z.boolean().default(true),
  sort_order: BaseSchemas.nonNegativeInt.default(0),
  
  created_at: BaseSchemas.timestamp,
  updated_at: BaseSchemas.timestamp,
});
```

## 🎨 个性化配置模式

```typescript
// 颜色配置模式
export const ColorPaletteSchema = z.object({
  primary: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '无效的颜色格式'),
  secondary: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '无效的颜色格式'),
  accent: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '无效的颜色格式'),
  background: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '无效的颜色格式'),
  text: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '无效的颜色格式'),
  border: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '无效的颜色格式'),
});

// 字体配置模式
export const FontConfigurationSchema = z.object({
  primary_font: z.string(),
  secondary_font: z.string().optional(),
  size_scale: z.number().min(0.5).max(2.0),
  line_height: z.number().min(1.0).max(2.0),
  letter_spacing: z.number().min(-0.1).max(0.2).optional(),
});

// 动画配置模式
export const AnimationConfigSchema = z.object({
  enable_animations: z.boolean().default(true),
  animation_speed: z.enum(['slow', 'normal', 'fast']).default('normal'),
  transition_duration: z.number().min(0).max(2000).default(300),
  easing_function: z.enum(['ease', 'ease-in', 'ease-out', 'ease-in-out']).default('ease-out'),
  reduce_motion: z.boolean().default(false),
});

// 轮盘展现配置模式
export const WheelPresentationConfigSchema = z.object({
  container_size: BaseSchemas.positiveInt.min(200).max(800),
  wheel_radius: BaseSchemas.positiveInt.min(50).max(400),
  inner_radius: BaseSchemas.nonNegativeInt.max(200),
  sector_gap: z.number().min(0).max(10),
  
  emotion_display_mode: z.enum(['hierarchical', 'flat', 'clustered']),
  tier_transition_animation: z.enum(['fade', 'rotate', 'zoom', 'slide']),
  
  visual_effects: z.object({
    use_3d_effects: z.boolean().default(false),
    shadow_enabled: z.boolean().default(true),
    glow_effects: z.boolean().default(false),
    particle_effects: z.boolean().default(false),
  }),
  
  interaction_config: z.object({
    hover_preview: z.boolean().default(true),
    selection_confirmation: z.boolean().default(false),
    confidence_rating: z.boolean().default(false),
    touch_sensitivity: z.number().min(0.1).max(2.0).default(1.0),
  }),
});

// 6层个性化配置模式
export const UserPresentationConfigSchema = z.object({
  user_id: BaseSchemas.id,
  
  layer0_dataset_presentation: z.object({
    preferred_pack_categories: z.array(z.string()),
    default_difficulty_preference: BaseSchemas.userLevel,
    session_length_preference: z.enum(['short', 'medium', 'long']),
    auto_select_recommended: z.boolean().default(false),
  }),
  
  layer1_user_choice: z.object({
    preferred_view_type: BaseSchemas.viewType,
    active_skin_id: BaseSchemas.id,
    dark_mode: z.boolean(),
    color_mode: BaseSchemas.colorMode,
    user_level: BaseSchemas.userLevel,
  }),
  
  layer2_rendering_strategy: z.object({
    render_engine_preferences: z.record(BaseSchemas.viewType, BaseSchemas.renderEngine),
    content_display_mode_preferences: z.record(BaseSchemas.viewType, BaseSchemas.contentDisplayMode),
    layout_preferences: z.record(BaseSchemas.viewType, z.string()),
    performance_mode: z.enum(['quality', 'balanced', 'performance']).default('balanced'),
  }),
  
  layer3_skin_base: z.object({
    colors: ColorPaletteSchema,
    fonts: FontConfigurationSchema,
    effects: z.object({
      blur_intensity: z.number().min(0).max(10).default(0),
      shadow_intensity: z.number().min(0).max(10).default(3),
      border_radius: z.number().min(0).max(50).default(8),
    }),
    animations: AnimationConfigSchema,
  }),
  
  layer4_view_detail: z.object({
    wheel_config: WheelPresentationConfigSchema.optional(),
    card_config: z.object({
      card_size: z.enum(['small', 'medium', 'large']).default('medium'),
      cards_per_row: BaseSchemas.positiveInt.min(1).max(6).default(3),
      card_spacing: z.number().min(0).max(50).default(16),
      show_card_shadows: z.boolean().default(true),
    }).optional(),
    
    emotion_presentation: z.object({
      emotion_grouping_style: z.enum(['by_category', 'by_intensity', 'by_color']),
      tier_transition_animation: z.enum(['fade', 'rotate', 'zoom', 'slide']),
      emotion_display_style: z.object({
        show_intensity_indicators: z.boolean().default(true),
        use_color_coding: z.boolean().default(true),
        show_category_labels: z.boolean().default(false),
        adaptive_sizing: z.boolean().default(true),
      }),
    }),
    
    interaction_presentation: z.object({
      hover_effects: z.object({
        enable_hover: z.boolean().default(true),
        hover_scale: z.number().min(1.0).max(1.5).default(1.05),
        hover_opacity: z.number().min(0.5).max(1.0).default(0.8),
      }),
      selection_feedback: z.object({
        feedback_type: z.enum(['highlight', 'scale', 'glow', 'bounce']).default('highlight'),
        feedback_duration: z.number().min(100).max(1000).default(300),
        show_selection_indicator: z.boolean().default(true),
      }),
      confirmation_style: z.object({
        confirmation_method: z.enum(['button', 'double_click', 'hold', 'auto']).default('button'),
        confirmation_delay: z.number().min(0).max(3000).default(0),
      }),
    }),
  }),
  
  layer5_accessibility: z.object({
    high_contrast: z.boolean().default(false),
    large_text: z.boolean().default(false),
    reduce_motion: z.boolean().default(false),
    screen_reader_support: z.boolean().default(false),
    keyboard_navigation: z.boolean().default(true),
    voice_guidance: z.boolean().default(false),
    haptic_feedback: z.boolean().default(false),
    
    emotion_accessibility: z.object({
      audio_descriptions: z.boolean().default(false),
      simplified_labels: z.boolean().default(false),
      color_blind_friendly: z.boolean().default(false),
      cognitive_load_reduction: z.boolean().default(false),
    }),
    
    focus_management: z.object({
      focus_ring_style: z.enum(['default', 'enhanced', 'custom']).default('default'),
      focus_ring_color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
      skip_links_enabled: z.boolean().default(true),
    }),
  }),
});

// 量表特定展现覆盖模式
export const PackSpecificPresentationOverrideSchema = z.object({
  user_id: BaseSchemas.id,
  pack_id: BaseSchemas.id,
  
  presentation_overrides: UserPresentationConfigSchema.omit({ user_id: true }).partial().optional(),
  
  tier_presentation_overrides: z.record(z.object({
    tier_id: BaseSchemas.id,
    visual_overrides: z.object({
      background_theme: z.string().optional(),
      color_scheme: ColorPaletteSchema.partial().optional(),
      animation_style: z.string().optional(),
      layout_variant: z.string().optional(),
    }).optional(),
    interaction_overrides: z.object({
      input_method_preference: z.enum(['touch', 'click', 'keyboard', 'voice']).optional(),
      feedback_style: z.string().optional(),
      confirmation_behavior: z.string().optional(),
    }).optional(),
  })).optional(),
});
```

## 🎮 会话和答案模式

```typescript
// 会话状态模式
export const QuizSessionStatusSchema = z.enum([
  'INITIATED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABORTED'
]);

// 会话模式
export const QuizSessionSchema = z.object({
  session_id: BaseSchemas.id,
  pack_id: BaseSchemas.id,
  user_id: BaseSchemas.id,
  
  status: QuizSessionStatusSchema,
  current_tier_index: BaseSchemas.nonNegativeInt,
  current_tier_id: BaseSchemas.id.optional(),
  
  start_time: BaseSchemas.timestamp,
  last_active_time: BaseSchemas.timestamp,
  end_time: BaseSchemas.timestamp.optional(),
  total_duration_seconds: BaseSchemas.nonNegativeInt.optional(),
  
  total_tiers: BaseSchemas.positiveInt,
  answered_questions: BaseSchemas.nonNegativeInt.default(0),
  skipped_questions: BaseSchemas.nonNegativeInt.default(0),
  
  created_at: BaseSchemas.timestamp,
  updated_at: BaseSchemas.timestamp,
});

// 答案提交模式
export const AnswerSubmissionSchema = z.object({
  session_id: BaseSchemas.id,
  tier_id: BaseSchemas.id,
  emotion_id: BaseSchemas.id,
  confidence: BaseSchemas.confidence.optional(),
  response_time_ms: BaseSchemas.positiveInt.optional(),
  interaction_method: z.enum(['click', 'touch', 'keyboard', 'voice']).optional(),
  submission_timestamp: BaseSchemas.timestamp.default(() => new Date()),
});

// 答案记录模式
export const QuizAnswerSchema = z.object({
  id: BaseSchemas.id,
  session_id: BaseSchemas.id,
  question_id: BaseSchemas.id,
  question_index: BaseSchemas.nonNegativeInt,
  
  emotion_id: BaseSchemas.id,
  emotion_name: BaseSchemas.nonEmptyString,
  tier_level: BaseSchemas.positiveInt,
  
  confidence_score: BaseSchemas.confidence.optional(),
  response_time_ms: BaseSchemas.positiveInt.optional(),
  is_skipped: z.boolean().default(false),
  is_revised: z.boolean().default(false),
  revision_count: BaseSchemas.nonNegativeInt.default(0),
  
  view_type_used: BaseSchemas.viewType.optional(),
  render_engine_used: BaseSchemas.renderEngine.optional(),
  interaction_method: z.enum(['click', 'touch', 'keyboard', 'voice']).optional(),
  
  answered_at: BaseSchemas.timestamp,
  created_at: BaseSchemas.timestamp,
  updated_at: BaseSchemas.timestamp,
});
```

## 🔧 验证工具函数

```typescript
// 自定义验证函数
export const ValidationUtils = {
  // 验证颜色值
  isValidColor: (color: string): boolean => {
    return /^#[0-9A-Fa-f]{6}$/.test(color);
  },
  
  // 验证版本号
  isValidVersion: (version: string): boolean => {
    return /^\d+\.\d+\.\d+$/.test(version);
  },
  
  // 验证本地化键
  isValidLocalizationKey: (key: string): boolean => {
    return /^[a-zA-Z0-9._-]+$/.test(key);
  },
  
  // 验证配置完整性
  validateConfigCompleteness: (config: any): boolean => {
    // 实现配置完整性检查逻辑
    return true;
  },
};

// 模式转换工具
export const SchemaTransforms = {
  // 将数据库记录转换为API响应
  dbToApi: <T>(schema: z.ZodSchema<T>) => {
    return schema.transform((data) => {
      // 执行必要的数据转换
      return data;
    });
  },
  
  // 将API输入转换为数据库格式
  apiToDb: <T>(schema: z.ZodSchema<T>) => {
    return schema.transform((data) => {
      // 执行必要的数据转换
      return data;
    });
  },
};

// 错误处理
export const ValidationErrors = {
  formatZodError: (error: z.ZodError): string => {
    return error.errors
      .map(err => `${err.path.join('.')}: ${err.message}`)
      .join('; ');
  },
  
  createValidationError: (message: string, path?: string[]): z.ZodError => {
    return new z.ZodError([{
      code: 'custom',
      message,
      path: path || [],
    }]);
  },
};
```

这些Zod模式提供了完整的类型安全验证，确保Quiz系统的数据完整性和一致性。
