import { type Client, createClient } from '@libsql/client';
import type {
  DatabaseInterface,
  InStatement,
  ResultSet,
  Transaction,
  TransactionMode,
} from './DatabaseInterface.js';

/**
 * Turso 数据库适配器
 * 实现 DatabaseInterface 接口，提供 Turso 数据库的具体实现
 */
export class TursoAdapter implements DatabaseInterface {
  private client: Client | null = null;
  private url: string;
  private authToken?: string;

  /**
   * 构造函数
   * @param url Turso 数据库 URL
   * @param authToken Turso 认证令牌
   */
  constructor(url: string, authToken?: string) {
    this.url = url;
    this.authToken = authToken;
  }

  /**
   * 获取 Turso 客户端实例
   */
  getConnection(): Client {
    if (this.client) return this.client;

    try {
      // 创建并存储客户端实例
      this.client = createClient({
        url: this.url,
        authToken: this.authToken,
      });
      console.log('[TursoAdapter] Turso client initialized successfully.');
      return this.client;
    } catch (error) {
      console.error('[TursoAdapter] Failed to initialize Turso client:', error);
      throw error; // 重新抛出错误，由调用者处理
    }
  }

  /**
   * 执行单个 SQL 查询
   * @param sql SQL 查询或 InStatement 对象
   */
  async executeQuery(sql: string | InStatement): Promise<ResultSet> {
    const client = this.getConnection();
    const sqlString = typeof sql === 'string' ? sql : sql.sql;

    try {
      const result = await client.execute(sql);

      // 记录查询执行情况（截断长查询以便更好地记录）
      const truncatedSql = sqlString.length > 100 ? `${sqlString.substring(0, 100)}...` : sqlString;
      console.log(
        `[TursoAdapter] Executed query: ${truncatedSql} (${result.rowsAffected} rows affected)`
      );

      return result;
    } catch (error) {
      console.error(`[TursoAdapter] Error executing query: ${sqlString}`, error);
      throw error;
    }
  }

  /**
   * 在事务中执行一批 SQL 语句
   * @param statements InStatement 对象数组
   * @param mode 事务模式
   */
  async batchStatements(
    statements: InStatement[],
    mode: TransactionMode = 'write'
  ): Promise<ResultSet[] | null> {
    if (!statements || statements.length === 0) {
      console.warn('[TursoAdapter] No statements provided for batch execution');
      return [];
    }

    const client = this.getConnection();
    let tx: Transaction | null = null;

    try {
      // 开始事务
      tx = await client.transaction(mode);

      // 执行批处理
      const results = await tx.batch(statements);

      // 提交事务
      await tx.commit();

      console.log(`[TursoAdapter] Successfully executed batch of ${statements.length} statements`);
      return results;
    } catch (error) {
      console.error('[TursoAdapter] Error in batch transaction:', error);

      // 如果事务存在，则回滚
      if (tx) {
        try {
          await tx.rollback();
          console.log('[TursoAdapter] Transaction rolled back successfully');
        } catch (rollbackError) {
          console.error('[TursoAdapter] Failed to rollback transaction:', rollbackError);
        }
      }

      throw error;
    }
  }

  /**
   * 执行多语句 SQL 脚本
   * @param sqlScript 包含多个语句的 SQL 脚本
   */
  async executeScript(sqlScript: string): Promise<void> {
    if (!sqlScript || sqlScript.trim() === '') {
      console.warn('[TursoAdapter] Empty SQL script provided');
      return;
    }

    const client = this.getConnection();

    try {
      // 通过计算分号来估计语句数量
      const statementCount = (sqlScript.match(/;/g) || []).length + 1;

      await client.executeMultiple(sqlScript);
      console.log(
        `[TursoAdapter] Successfully executed SQL script with ~${statementCount} statements`
      );
    } catch (error) {
      console.error('[TursoAdapter] Failed to execute SQL script:', error);
      throw error;
    }
  }

  /**
   * 从指定表中获取所有行
   * @param tableName 表名
   * @param limit 可选的行数限制
   */
  async fetchAllFromTable(tableName: string, limit?: number): Promise<any[]> {
    // 验证表名以防止 SQL 注入
    if (!/^[a-zA-Z0-9_]+$/.test(tableName)) {
      throw new Error(`Invalid table name: ${tableName}`);
    }

    // 构建带有可选限制的查询
    const query = limit
      ? `SELECT * FROM ${tableName} LIMIT ${limit}`
      : `SELECT * FROM ${tableName}`;

    try {
      const result = await this.executeQuery(query);
      console.log(`[TursoAdapter] Fetched ${result.rows.length} rows from table '${tableName}'`);
      return result.rows;
    } catch (error) {
      console.error(`[TursoAdapter] Failed to fetch data from table '${tableName}':`, error);
      throw error;
    }
  }

  /**
   * 开始事务
   * @param mode 事务模式
   */
  async transaction(mode: TransactionMode = 'write'): Promise<Transaction> {
    const client = this.getConnection();
    return client.transaction(mode);
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.client) {
      // Turso 客户端没有显式的关闭方法，但我们可以将引用设置为 null
      this.client = null;
      console.log('[TursoAdapter] Database connection closed');
    }
  }

  /**
   * 获取数据库类型
   */
  getDatabaseType(): string {
    return 'turso';
  }
}
