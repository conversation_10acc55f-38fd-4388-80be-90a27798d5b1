/**
 * 数据库服务导出文件
 * 简化导入过程
 */

// 导出接口和类型
export * from './DatabaseInterface.js';
export * from './DatabaseFactory.js';
export * from './DatabaseService.js';
export * from './DatabaseInitializer.js';

// 显式导出 InStatement 类型
export type { InStatement, ResultSet, Transaction, TransactionMode } from './DatabaseInterface.js';

// 导出适配器
export * from './TursoAdapter.js';
export * from './SQLiteAdapter.js';
export * from './D1Adapter.js';

import { type DatabaseConfig, DatabaseFactory, DatabaseType } from './DatabaseFactory.js';
import type { DatabaseInitOptions } from './DatabaseInitializer.js';
import type { InStatement, ResultSet } from './DatabaseInterface.js';
// 导出便捷函数
import { DatabaseService } from './DatabaseService.js';

/**
 * 获取数据库服务实例
 */
export const getDatabaseService = (): DatabaseService => {
  return DatabaseService.getInstance();
};

/**
 * 初始化数据库服务
 * @param config 数据库配置
 */
export const initializeDatabaseService = (config: DatabaseConfig): void => {
  const service = DatabaseService.getInstance();
  service.initialize(config);
};

/**
 * 执行单个 SQL 查询
 * @param sql SQL 查询或 InStatement 对象
 */
export const executeQuery = async (sql: string | InStatement): Promise<ResultSet> => {
  return getDatabaseService().executeQuery(sql);
};

/**
 * 在事务中执行一批 SQL 语句
 * @param statements InStatement 对象数组
 * @param mode 事务模式
 */
export const batchStatements = async (
  statements: InStatement[],
  mode?: 'deferred' | 'write' | 'read'
): Promise<ResultSet[] | null> => {
  return getDatabaseService().batchStatements(statements, mode);
};

/**
 * 执行多语句 SQL 脚本
 * @param sqlScript 包含多个语句的 SQL 脚本
 */
export const executeScript = async (sqlScript: string): Promise<void> => {
  return getDatabaseService().executeScript(sqlScript);
};

/**
 * 从指定表中获取所有行
 * @param tableName 表名
 * @param limit 可选的行数限制
 */
export const fetchAllFromTable = async (tableName: string, limit?: number): Promise<any[]> => {
  return getDatabaseService().fetchAllFromTable(tableName, limit);
};

/**
 * 初始化数据库
 * @param options 初始化选项
 */
export const initializeDatabase = async (options: DatabaseInitOptions = {}): Promise<void> => {
  return getDatabaseService().initializeDatabase(options);
};

/**
 * 创建 Turso 数据库配置
 * @param url Turso 数据库 URL
 * @param authToken Turso 认证令牌
 */
export const createTursoConfig = (url: string, authToken?: string): DatabaseConfig => {
  return {
    type: DatabaseType.TURSO,
    url,
    authToken,
  };
};

/**
 * 创建 SQLite 数据库配置
 * @param dbPath SQLite 数据库文件路径
 */
export const createSQLiteConfig = (dbPath?: string): DatabaseConfig => {
  return {
    type: DatabaseType.SQLITE,
    dbPath,
  };
};

/**
 * 创建 D1 数据库配置
 * @param d1Instance D1 数据库实例
 */
export const createD1Config = (d1Instance: any): DatabaseConfig => {
  return {
    type: DatabaseType.D1,
    d1Instance,
  };
};
