import { useToast } from '@/components/ui/use-toast';
import { OnlineServices } from '@/services/online';
import type React from 'react';
import { createContext, useContext, useEffect, useState } from 'react';

// 网络状态类型
interface NetworkStatus {
  connected: boolean;
  isInternetReachable: boolean;
  connectionType: 'wifi' | 'cellular' | 'none' | 'unknown';
}

// 网络上下文类型
interface NetworkContextType {
  status: NetworkStatus;
  isOnline: boolean;
  isInternetReachable: boolean;
  connectionType: NetworkStatus['connectionType'];
  checkInternetReachability: () => Promise<boolean>;
}

// 创建网络上下文
const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

// 网络提供者属性类型
interface NetworkProviderProps {
  children: React.ReactNode;
  showToasts?: boolean;
}

/**
 * 网络状态提供者组件
 * 提供网络状态信息给整个应用
 */
export const NetworkProvider: React.FC<NetworkProviderProps> = ({
  children,
  showToasts = true,
}) => {
  const [status, setStatus] = useState<NetworkStatus>({
    connected: navigator.onLine,
    isInternetReachable: navigator.onLine,
    connectionType: 'unknown',
  });
  const { toast } = useToast();

  // 检查互联网可达性
  const checkInternetReachability = async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache',
      });
      return response.ok;
    } catch {
      return false;
    }
  };

  // 初始化网络状态监听
  useEffect(() => {
    const handleOnline = async () => {
      const isReachable = await checkInternetReachability();
      const newStatus: NetworkStatus = {
        connected: true,
        isInternetReachable: isReachable,
        connectionType: 'unknown',
      };
      setStatus(newStatus);

      if (showToasts) {
        toast({
          title: '网络已连接',
          description: '网络连接已恢复',
          variant: 'default',
        });
      }
    };

    const handleOffline = () => {
      const newStatus: NetworkStatus = {
        connected: false,
        isInternetReachable: false,
        connectionType: 'none',
      };
      setStatus(newStatus);

      if (showToasts) {
        toast({
          title: '网络已断开',
          description: '您现在处于离线模式，部分功能可能受限',
          variant: 'destructive',
        });
      }
    };

    // 添加网络状态监听器
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 初始检查网络状态
    const initializeStatus = async () => {
      const isReachable = navigator.onLine ? await checkInternetReachability() : false;
      const initialStatus: NetworkStatus = {
        connected: navigator.onLine,
        isInternetReachable: isReachable,
        connectionType: navigator.onLine ? 'unknown' : 'none',
      };
      setStatus(initialStatus);
    };

    initializeStatus();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [showToasts, toast]);

  // 提供网络状态和检查互联网可达性的方法
  const value: NetworkContextType = {
    status,
    isOnline: status.connected,
    isInternetReachable: status.isInternetReachable,
    connectionType: status.connectionType,
    checkInternetReachability,
  };

  return <NetworkContext.Provider value={value}>{children}</NetworkContext.Provider>;
};

/**
 * 使用网络状态的自定义Hook
 */
export const useNetwork = (): NetworkContextType => {
  const context = useContext(NetworkContext);
  if (context === undefined) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
};

export default NetworkContext;
