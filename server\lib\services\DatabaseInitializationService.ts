/**
 * 数据库初始化服务
 * 负责在服务器启动时初始化数据库schema和基础数据
 */

import * as fs from 'node:fs';
import * as path from 'node:path';
import { fileURLToPath } from 'node:url';
import { executeQuery, executeScript } from '../database/index.js';

// 获取当前文件的目录路径 (ES模块中的__dirname替代)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export interface InitializationResult {
  success: boolean;
  tablesCreated: number;
  dataInserted: number;
  error?: string;
}

export class DatabaseInitializationService {
  private static instance: DatabaseInitializationService;

  private constructor() {}

  static getInstance(): DatabaseInitializationService {
    if (!DatabaseInitializationService.instance) {
      DatabaseInitializationService.instance = new DatabaseInitializationService();
    }
    return DatabaseInitializationService.instance;
  }

  /**
   * 初始化数据库
   */
  async initializeDatabase(): Promise<InitializationResult> {
    try {
      console.log('[DatabaseInitializationService] Starting database initialization...');

      // 检查是否已经初始化
      const isInitialized = await this.checkIfInitialized();
      if (isInitialized) {
        console.log('[DatabaseInitializationService] Database already initialized');
        return {
          success: true,
          tablesCreated: 0,
          dataInserted: 0,
        };
      }

      // 执行初始化脚本
      const initResult = await this.executeInitScript();

      if (initResult.success) {
        // 标记为已初始化
        await this.markAsInitialized();
        console.log(
          '[DatabaseInitializationService] Database initialization completed successfully'
        );
      }

      return initResult;
    } catch (error) {
      console.error('[DatabaseInitializationService] Database initialization failed:', error);
      return {
        success: false,
        tablesCreated: 0,
        dataInserted: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 检查数据库是否已经初始化
   */
  private async checkIfInitialized(): Promise<boolean> {
    try {
      // 检查是否存在用户表
      const result = await executeQuery({
        sql: "SELECT name FROM sqlite_master WHERE type='table' AND name='users'",
        args: [],
      });

      return result.rows && result.rows.length > 0;
    } catch (error) {
      console.warn('[DatabaseInitializationService] Error checking initialization status:', error);
      return false;
    }
  }

  /**
   * 执行初始化脚本
   */
  private async executeInitScript(): Promise<InitializationResult> {
    try {
      // 读取初始化脚本
      const initScript = await this.readInitScript();

      if (!initScript) {
        return {
          success: false,
          tablesCreated: 0,
          dataInserted: 0,
          error: 'Init script not found',
        };
      }

      // 执行脚本
      await executeScript(initScript);

      // 统计创建的表数量
      const tablesResult = await executeQuery({
        sql: "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
        args: [],
      });

      const tablesCreated = tablesResult.rows[0]?.count || 0;

      // 统计插入的数据量（检查几个关键表）
      let dataInserted = 0;

      try {
        const skinsResult = await executeQuery({
          sql: 'SELECT COUNT(*) as count FROM skins',
          args: [],
        });
        dataInserted += skinsResult.rows[0]?.count || 0;

        const emotionsResult = await executeQuery({
          sql: 'SELECT COUNT(*) as count FROM emotions',
          args: [],
        });
        dataInserted += emotionsResult.rows[0]?.count || 0;

        const dataSetsResult = await executeQuery({
          sql: 'SELECT COUNT(*) as count FROM emotion_data_sets',
          args: [],
        });
        dataInserted += dataSetsResult.rows[0]?.count || 0;
      } catch (error) {
        console.warn('[DatabaseInitializationService] Error counting inserted data:', error);
      }

      return {
        success: true,
        tablesCreated: Number.parseInt(tablesCreated),
        dataInserted,
      };
    } catch (error) {
      console.error('[DatabaseInitializationService] Error executing init script:', error);
      return {
        success: false,
        tablesCreated: 0,
        dataInserted: 0,
        error: error instanceof Error ? error.message : 'Script execution failed',
      };
    }
  }

  /**
   * 读取初始化脚本 - 使用public目录下的SQL文件
   */
  private async readInitScript(): Promise<string | null> {
    try {
      // 按照客户端DatabaseService的顺序加载SQL文件
      const dataFiles = [
        'public/seeds/schema/full.sql', // 1. 完整schema
        'public/seeds/schema/init.sql', // 2. 核心数据
        'public/seeds/schema/emotion_data_set_tiers.sql', // 3. 情绪数据集层级
        'public/seeds/schema/additional_emoji_init.sql', // 4. 额外表情集
        'public/seeds/schema/extended_emoji_items.sql', // 5. 扩展表情项
        'public/seeds/schema/ui_labels.sql', // 6. UI标签
        'public/seeds/schema/ui_label_translations.sql', // 7. UI标签翻译
      ];

      let combinedScript = '';

      for (const relativePath of dataFiles) {
        const possiblePaths = [
          path.join(process.cwd(), relativePath),
          path.join(process.cwd(), '..', relativePath),
          path.join(__dirname, '..', '..', '..', relativePath),
          path.join(__dirname, '..', '..', '..', '..', relativePath),
        ];

        let fileContent = '';
        let fileFound = false;

        for (const filePath of possiblePaths) {
          try {
            if (fs.existsSync(filePath)) {
              console.log(
                `[DatabaseInitializationService] Reading ${relativePath} from: ${filePath}`
              );
              fileContent = fs.readFileSync(filePath, 'utf-8');
              fileFound = true;
              break;
            }
          } catch (error) {
            console.warn(`[DatabaseInitializationService] Could not read from ${filePath}:`, error);
          }
        }

        if (fileFound && fileContent.trim()) {
          combinedScript += '\n-- =============================================\n';
          combinedScript += `-- Loading: ${relativePath}\n`;
          combinedScript += '-- =============================================\n';
          combinedScript += `${fileContent}\n`;
        } else {
          console.warn(
            `[DatabaseInitializationService] Could not find ${relativePath}, skipping...`
          );
        }
      }

      if (combinedScript.trim()) {
        return combinedScript;
      }

      // 如果找不到任何文件，返回内联的基础schema
      console.warn('[DatabaseInitializationService] No SQL files found, using inline schema');
      return this.getInlineSchema();
    } catch (error) {
      console.error('[DatabaseInitializationService] Error reading init script:', error);
      return null;
    }
  }

  /**
   * 获取内联的基础schema（作为备用）- 基于客户端schema但添加server特有字段
   */
  private getInlineSchema(): string {
    return `
      -- 基础schema（备用）- 基于客户端full.sql但添加server特有字段
      PRAGMA foreign_keys=OFF;
      PRAGMA ignore_check_constraints=ON;

      -- 用户表（添加server特有的认证字段）
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY NOT NULL,
        username TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL, -- Server-specific: for authentication
        roles TEXT DEFAULT '["user"]', -- JSON array of roles
        permissions TEXT DEFAULT '["read_own_data", "write_own_data"]', -- JSON array of permissions
        is_vip BOOLEAN DEFAULT FALSE,
        vip_expires_at TEXT, -- ISO 8601 timestamp
        last_login_at TEXT, -- ISO 8601 timestamp
        is_verified BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- 情绪表（与客户端保持一致）
      CREATE TABLE IF NOT EXISTS emotions (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        emoji TEXT,
        color TEXT,
        tier_level INTEGER,
        parent_id TEXT,
        keywords TEXT,
        image_url TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT,
        updated_by TEXT,
        is_deleted BOOLEAN DEFAULT 0,
        deleted_at TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES emotions(id) ON DELETE SET NULL
      );

      -- 心情记录表（与客户端保持一致，但sync_status默认为synced）
      CREATE TABLE IF NOT EXISTS mood_entries (
        id TEXT PRIMARY KEY NOT NULL,
        user_id TEXT NOT NULL,
        timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        emotion_data_set_id TEXT,
        intensity INTEGER CHECK (intensity BETWEEN 0 AND 100),
        reflection TEXT,
        tags TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        sync_status TEXT DEFAULT 'synced' NOT NULL, -- Server default: synced
        server_id TEXT UNIQUE,
        last_synced_at TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (emotion_data_set_id) REFERENCES emotion_data_sets(id) ON DELETE SET NULL
      );

      -- 情绪数据集表
      CREATE TABLE IF NOT EXISTS emotion_data_sets (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        is_active BOOLEAN NOT NULL DEFAULT 0,
        is_default BOOLEAN DEFAULT 0,
        is_system_default BOOLEAN DEFAULT 0,
        default_emoji_set_id TEXT,
        supported_languages TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT,
        FOREIGN KEY (default_emoji_set_id) REFERENCES emoji_sets(id) ON DELETE SET NULL
      );

      -- 皮肤表（与客户端保持一致）
      CREATE TABLE IF NOT EXISTS skins (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT,
        version TEXT,
        tags TEXT,
        preview_image_light TEXT,
        preview_image_dark TEXT,
        is_premium BOOLEAN DEFAULT 0,
        is_unlocked BOOLEAN DEFAULT 0,
        unlock_conditions TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        author TEXT,
        supported_content_modes TEXT,
        supported_view_types TEXT,
        supported_render_engines TEXT,
        config TEXT NOT NULL
      );

      -- Server特有表：皮肤解锁记录
      CREATE TABLE IF NOT EXISTS skin_unlocks (
        id TEXT PRIMARY KEY NOT NULL,
        user_id TEXT NOT NULL,
        skin_id TEXT NOT NULL,
        unlocked_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        unlock_method TEXT NOT NULL CHECK (unlock_method IN ('purchase', 'vip', 'achievement', 'free')),
        transaction_id TEXT,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (skin_id) REFERENCES skins(id) ON DELETE CASCADE,
        UNIQUE(user_id, skin_id)
      );

      -- Server特有表：用户会话
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY NOT NULL,
        user_id TEXT NOT NULL,
        token_hash TEXT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_used_at TIMESTAMP,
        user_agent TEXT,
        ip_address TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );

      -- 创建基础索引
      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
      CREATE INDEX IF NOT EXISTS idx_mood_entries_user_id ON mood_entries(user_id);
      CREATE INDEX IF NOT EXISTS idx_mood_entries_timestamp ON mood_entries(timestamp);

      -- 插入默认数据
      INSERT OR IGNORE INTO emotion_data_sets (id, name, description, is_default, created_at, updated_at) VALUES
      ('default', 'Default Emotion Set', 'The default set of emotions', TRUE, datetime('now'), datetime('now'));

      INSERT OR IGNORE INTO emotions (id, name, color, emoji, tier_level, created_by, created_at, updated_at) VALUES
      ('happy', 'Happy', '#FFFF00', '😊', 1, 'system', datetime('now'), datetime('now')),
      ('sad', 'Sad', '#ADD8E6', '😢', 1, 'system', datetime('now'), datetime('now')),
      ('angry', 'Angry', '#FF0000', '😠', 1, 'system', datetime('now'), datetime('now'));

      INSERT OR IGNORE INTO skins (id, name, description, is_premium, is_unlocked, author, config, created_at, updated_at) VALUES
      ('default-light', 'Default Light', 'Default light theme skin', 0, 1, 'system', '{"theme": "light", "primaryColor": "#3B82F6"}', datetime('now'), datetime('now'));

      PRAGMA ignore_check_constraints=OFF;
      PRAGMA foreign_keys=ON;
    `;
  }

  /**
   * 标记数据库为已初始化
   */
  private async markAsInitialized(): Promise<void> {
    try {
      // 创建一个系统配置表来跟踪初始化状态
      await executeQuery({
        sql: `
          CREATE TABLE IF NOT EXISTS system_config (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        `,
        args: [],
      });

      await executeQuery({
        sql: `
          INSERT OR REPLACE INTO system_config (key, value, created_at, updated_at)
          VALUES ('database_initialized', 'true', datetime('now'), datetime('now'))
        `,
        args: [],
      });
    } catch (error) {
      console.warn('[DatabaseInitializationService] Could not mark as initialized:', error);
    }
  }

  /**
   * 重置数据库（仅用于开发/测试）
   */
  async resetDatabase(): Promise<InitializationResult> {
    try {
      console.log('[DatabaseInitializationService] Resetting database...');

      // 禁用外键约束
      await executeQuery({
        sql: 'PRAGMA foreign_keys=OFF',
        args: [],
      });

      // 获取所有表名
      const tablesResult = await executeQuery({
        sql: "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
        args: [],
      });

      // 删除所有表
      for (const table of tablesResult.rows) {
        await executeQuery({
          sql: `DROP TABLE IF EXISTS ${table.name}`,
          args: [],
        });
      }

      // 重新启用外键约束
      await executeQuery({
        sql: 'PRAGMA foreign_keys=ON',
        args: [],
      });

      // 重新初始化
      return await this.initializeDatabase();
    } catch (error) {
      console.error('[DatabaseInitializationService] Database reset failed:', error);
      return {
        success: false,
        tablesCreated: 0,
        dataInserted: 0,
        error: error instanceof Error ? error.message : 'Reset failed',
      };
    }
  }
}
