#!/usr/bin/env node

/**
 * 修复 bubbleSkins.ts 中的字段命名问题
 */

const fs = require('fs');
const path = require('path');

// 字段映射
const fieldMappings = {
  // effects 字段
  'borderRadius': 'border_radius',
  'shadowColor': 'shadow_color',
  'shadowBlur': 'shadow_blur',
  
  // bubble 配置字段
  'bubbleSize': 'bubble_size',
  'bubbleMinSize': 'bubble_min_size',
  'bubbleMaxSize': 'bubble_max_size',
  'bubbleSpacing': 'bubble_spacing',
  'bubbleShape': 'bubble_shape',
  'bubbleBorderRadius': 'bubble_border_radius',
  'bubbleBorderWidth': 'bubble_border_width',
  'bubbleBorderColor': 'bubble_border_color',
  'bubbleBackground': 'bubble_background',
  'bubbleBackgroundOpacity': 'bubble_background_opacity',
  'bubbleGradient': 'bubble_gradient',
  'bubbleGradientColors': 'bubble_gradient_colors',
  'bubbleGradientDirection': 'bubble_gradient_direction',
  'bubbleShadow': 'bubble_shadow',
  'bubbleShadowColor': 'bubble_shadow_color',
  'bubbleShadowBlur': 'bubble_shadow_blur',
  'bubbleHoverEffect': 'bubble_hover_effect',
  'bubbleHoverScale': 'bubble_hover_scale',
  'bubbleSelectedEffect': 'bubble_selected_effect',
  'bubbleSelectedGlowColor': 'bubble_selected_glow_color',
  'emojiSize': 'emoji_size',
  'textVisible': 'text_visible',
  'textSize': 'text_size',
  'textColor': 'text_color',
  'textBackground': 'text_background',
  'textBackgroundOpacity': 'text_background_opacity',
  'textPosition': 'text_position',
  'floatingAnimation': 'floating_animation',
  'floatingSpeed': 'floating_speed',
  'floatingAmplitude': 'floating_amplitude',
  'responsiveScaling': 'responsive_scaling',
  'collisionDetection': 'collision_detection',
  'dragEnabled': 'drag_enabled',
  'snapToGrid': 'snap_to_grid',
  
  // 皮肤对象字段
  'previewImage': 'preview_image_light',
  'isUnlocked': 'is_unlocked',
  'isDefault': 'is_default',
  'created_by': 'author'
};

function fixBubbleSkins() {
  const filePath = path.join(process.cwd(), 'src/skins/bubbleSkins.ts');
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    // 修复字段命名
    for (const [oldField, newField] of Object.entries(fieldMappings)) {
      // 修复对象属性: oldField: value
      const propertyRegex = new RegExp(`(\\s+)${oldField}(\\s*:)`, 'g');
      if (propertyRegex.test(content)) {
        content = content.replace(propertyRegex, `$1${newField}$2`);
        hasChanges = true;
        console.log(`✅ 修复字段: ${oldField} → ${newField}`);
      }
    }
    
    // 修复 config 字段序列化
    content = content.replace(/config: (\w+SkinConfig),/g, 'config: JSON.stringify($1),');
    
    // 修复 tags 字段序列化
    content = content.replace(/tags: (\[.*?\]),/g, 'tags: JSON.stringify($1),');
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log('✅ bubbleSkins.ts 修复完成');
    } else {
      console.log('ℹ️ bubbleSkins.ts 无需修复');
    }
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  }
}

// 运行修复
fixBubbleSkins();
