# 废弃组件和服务文档

本文档记录了项目中已废弃的组件、服务和钩子，以及迁移建议。

## 视图系统

### TierNavigationContext

**状态**: 已废弃

**替代方案**: 使用 `UserConfigContext`

**迁移指南**:
- 使用 `useUserConfig()` 替代 `useNavigationStyle()`
- 使用 `setPreferredViewType()` 替代 `setNavigationStyle()`
- 使用 `setViewRenderingEngine()` 替代 `setRenderEngine()`
- 使用 `setViewSkinId()` 或 `setActiveSkinId()` 替代 `setActiveSkinId()`

**示例**:
```tsx
// 旧代码
const { navigationStyle, setNavigationStyle, RenderEngine } = useNavigationStyle();

// 新代码
const { userConfig, setPreferredViewType, setViewRenderingEngine } = useUserConfig();
const viewType = userConfig.preferredViewType;
const renderEngine = userConfig.renderEnginePreferences?.wheel;
```

### WheelAdapter.tsx

**状态**: 已废弃

**替代方案**: 使用 `DisplayAdapter.tsx`

**迁移指南**:
- 使用 `DisplayAdapter` 组件替代 `WheelAdapter`
- 使用 `ViewFactory` 创建视图实例

**示例**:
```tsx
// 旧代码
<WheelAdapter emotionData={emotionData} onSelect={handleSelect} />

// 新代码
<DisplayAdapter emotionData={emotionData} onSelect={handleSelect} />
```

## 自定义轮盘系统

### CustomWheelMappingService

**状态**: 可能已废弃

**替代方案**: 待确认

**迁移指南**:
- 如果需要保留自定义轮盘分析功能，可以创建一个新的服务类，使用新的视图系统的概念和组件
- 如果不再需要此功能，可以移除相关代码

### useCustomWheelAnalytics

**状态**: 可能已废弃

**替代方案**: 待确认

**迁移指南**:
- 如果需要保留自定义轮盘分析功能，可以创建一个新的钩子，使用新的视图系统的概念和组件
- 如果不再需要此功能，可以移除相关代码

### CustomWheelManager

**状态**: 可能已废弃

**替代方案**: 待确认

**迁移指南**:
- 如果需要保留自定义轮盘管理功能，可以创建一个新的管理器类，使用新的视图系统的概念和组件
- 如果不再需要此功能，可以移除相关代码

## 清理计划

以下是清理废弃组件和服务的计划：

1. **标记废弃**:
   - 使用 `@deprecated` 标记废弃的组件、服务和钩子
   - 添加迁移指南，说明替代方案

2. **更新测试**:
   - 更新测试文件，使用新的组件、服务和钩子
   - 确保所有测试都能通过

3. **移除废弃代码**:
   - 在确认没有代码依赖废弃的组件、服务和钩子后，可以考虑完全移除它们
   - 使用 `scripts/cleanup-deprecated-components.js` 脚本移除废弃的文件和目录

4. **更新文档**:
   - 更新开发指南，说明废弃的组件、服务和钩子
   - 提供迁移示例，展示如何从旧的组件、服务和钩子迁移到新的

## 注意事项

- 在移除废弃代码之前，请确保所有依赖它们的代码都已经迁移到新的组件、服务和钩子
- 如果不确定是否可以移除某个组件、服务或钩子，请咨询团队成员
- 在移除废弃代码后，请运行测试，确保没有引入新的问题
