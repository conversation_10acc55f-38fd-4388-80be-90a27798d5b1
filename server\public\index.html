<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mindful Mood API</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2563eb;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 10px;
    }
    h2 {
      color: #4b5563;
      margin-top: 30px;
    }
    code {
      background-color: #f3f4f6;
      padding: 2px 5px;
      border-radius: 4px;
      font-family: 'Courier New', Courier, monospace;
    }
    pre {
      background-color: #f3f4f6;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
    }
    .endpoint {
      margin-bottom: 30px;
      padding: 15px;
      border-left: 4px solid #2563eb;
      background-color: #f9fafb;
    }
    .method {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 4px;
      font-weight: bold;
      margin-right: 10px;
    }
    .get {
      background-color: #10b981;
      color: white;
    }
    .post {
      background-color: #3b82f6;
      color: white;
    }
  </style>
</head>
<body>
  <h1>Mindful Mood API</h1>
  <p>Welcome to the Mindful Mood API server. This server provides a tRPC API for accessing the Turso database.</p>

  <h2>API Endpoints</h2>

  <div class="endpoint">
    <h3><span class="method post">POST</span> /trpc/query</h3>
    <p>Execute a single SQL query against the Turso database.</p>
    <h4>Example Request:</h4>
    <pre><code>{
  "json": {
    "sql": "SELECT * FROM users LIMIT 5"
  }
}</code></pre>
  </div>

  <div class="endpoint">
    <h3><span class="method post">POST</span> /trpc/batch</h3>
    <p>Execute a batch of SQL statements in a transaction.</p>
    <h4>Example Request:</h4>
    <pre><code>{
  "json": {
    "statements": [
      {
        "sql": "INSERT INTO users (name, email) VALUES (?, ?)",
        "args": ["User 1", "<EMAIL>"]
      },
      {
        "sql": "INSERT INTO users (name, email) VALUES (?, ?)",
        "args": ["User 2", "<EMAIL>"]
      }
    ],
    "mode": "write"
  }
}</code></pre>
  </div>

  <div class="endpoint">
    <h3><span class="method post">POST</span> /trpc/executeScript</h3>
    <p>Execute a multi-statement SQL script.</p>
    <h4>Example Request:</h4>
    <pre><code>{
  "json": {
    "script": "CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY, name TEXT, email TEXT); INSERT INTO users (name, email) VALUES ('User', '<EMAIL>');"
  }
}</code></pre>
  </div>

  <div class="endpoint">
    <h3><span class="method post">POST</span> /trpc/fetchTable</h3>
    <p>Fetch all rows from a specified table.</p>
    <h4>Example Request:</h4>
    <pre><code>{
  "json": {
    "tableName": "users"
  }
}</code></pre>
  </div>

  <div class="endpoint">
    <h3><span class="method post">POST</span> /trpc/fetchTableWithLimit</h3>
    <p>Fetch rows from a specified table with an optional limit.</p>
    <h4>Example Request:</h4>
    <pre><code>{
  "json": {
    "tableName": "users",
    "limit": 10
  }
}</code></pre>
  </div>

  <h2>Client Integration</h2>
  <p>To use this API in your client application, you need to set up a tRPC client. See the documentation for more details.</p>

  <footer>
    <p>Mindful Mood API Server - Powered by Cloudflare Pages</p>
  </footer>
</body>
</html>
