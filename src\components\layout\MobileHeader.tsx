import { useLanguage } from '@/contexts/LanguageContext';

interface MobileHeaderProps {
  title: string;
}

const MobileHeader = ({ title }: MobileHeaderProps) => {
  const { language } = useLanguage();

  return (
    <header className="sticky top-0 z-10 bg-background/80 backdrop-blur-lg border-b border-border px-4 py-3">
      <div className="flex justify-center items-center relative">
        <h1 className="text-lg font-semibold">{title}</h1>
        <div className="absolute right-0 text-xs rounded-full bg-primary text-primary-foreground px-2 py-0.5">
          {language.toUpperCase()}
        </div>
      </div>
    </header>
  );
};

export default MobileHeader;
