# 客户端服务网络依赖模式分类

基于对客户端服务架构的深入分析，明确各种服务的网络依赖模式和实现策略。

## 📋 **网络依赖模式分类**

### 🔴 **必须在线 (Online-Only)**
这些服务必须有网络连接才能工作，无法离线使用：

#### **1. 认证服务 (Authentication)**
```typescript
// 位置: src/lib/auth/better-auth-client.ts
// 调用方式: 通过 tRPC 直接调用服务端

// 必须在线的操作
await apiClient.call('auth.login', { email, password });
await apiClient.call('auth.register', userData);
await apiClient.call('auth.logout');
await apiClient.call('auth.refreshToken');

// 原因: 安全验证必须在服务端进行
// 离线策略: 无 - 认证失败时显示登录界面
```

#### **2. 支付服务 (Payment)**
```typescript
// 位置: src/services/online/services/PaymentService.ts
// 调用方式: 通过 tRPC 调用服务端支付API

// 必须在线的操作
await trpc.purchaseVip.mutate({ planId, paymentMethodId, userId });
await trpc.purchaseSkin.mutate({ skinId, paymentMethodId, userId });
await trpc.getPurchaseHistory.query();

// 原因: 支付验证和交易处理必须实时进行
// 离线策略: 无 - 支付功能在离线时不可用
```

#### **3. VIP状态验证**
```typescript
// 位置: src/lib/auth/better-auth-client.ts (useVipStatus)
// 调用方式: 通过 tRPC 查询服务端

// 必须在线的操作
await onlineServices.api.subscription.getVipStatus.useQuery();

// 原因: VIP状态需要实时验证，防止本地篡改
// 离线策略: 使用本地缓存的VIP状态，但功能受限
```

### 🟡 **在线优先 (Online-First)**
这些服务优先使用在线版本，网络不通时回退到离线版本：

#### **4. 数据查询服务**
```typescript
// 数据获取流程 (docs/tasks/OFFLINE_ONLINE_IMPLEMENTATION_SUMMARY.md)
用户请求 → 页面级Hook → 混合数据Hook → 在线服务 (优先)
                                        ↓ (失败)
                                    离线服务 (回退)

// 实现示例
const {
  activeDataSet,
  tiers,
  emotions,
  isLoading,
  error,
  isOnline,
  refresh,
  saveMoodEntry
} = useHomeData(); // 自动处理在线/离线切换
```

#### **5. 内容获取 (Quiz包、皮肤、表情集)**
```typescript
// 在线优先获取最新内容
try {
  const onlineContent = await apiClient.call('content.getQuizPacks');
  return onlineContent;
} catch (error) {
  // 回退到离线缓存
  const offlineContent = await localQuizPackService.getAll();
  return offlineContent;
}
```

### 🟢 **离线优先 (Offline-First)**
这些服务完全在离线环境下工作，定期同步到云端：

#### **6. Quiz引擎 (QuizEngineV3)**
```typescript
// 位置: src/services/entities/QuizEngineV3.ts (355行)
// 完全离线实现

// 离线可用的操作
const quizEngine = new QuizEngineV3();
const session = await quizEngine.createQuizSession(packId, userId);
const questionData = await quizEngine.getCurrentQuestionData(sessionId);
const result = await quizEngine.submitAnswer(sessionId, questionId, answer);

// 同步策略: 定期将Quiz会话和答案同步到云端
// 网络恢复时: 自动上传未同步的Quiz数据
```

#### **7. 心情记录 (Mood Entries)**
```typescript
// 位置: src/services/entities/ (各种实体服务)
// 完全离线实现

// 离线可用的操作
const moodService = await Services.moodEntry();
await moodService.create(moodData); // 本地存储
await moodService.findAll(); // 本地查询

// 同步策略: 标记为 sync_status: 'pending'
// 网络恢复时: SyncCoordinator 自动上传
```

#### **8. 用户配置 (User Configs)**
```typescript
// 位置: src/services/entities/UserConfigService.ts
// 完全离线实现

// 离线可用的操作
const configService = await Services.userConfig();
await configService.updateConfig(userId, configData);
await configService.getByUserId(userId);

// 同步策略: 高优先级同步 ('user_configs': 'high')
// 冲突解决: 通常客户端优先 ('client_wins')
```

#### **9. VIP计划管理 (本地缓存)**
```typescript
// 位置: src/services/entities/VipPlanService.ts (336行)
// 离线可用，定期更新

// 离线可用的操作
const vipService = new VipPlanService();
const plans = await vipService.getAvailablePlans(); // 本地缓存
const analysis = await vipService.getPlanValueAnalysis(planId);

// 同步策略: 定期从服务端更新VIP计划数据
// 购买操作: 必须在线进行
```

#### **10. 解锁系统 (UnlockService)**
```typescript
// 位置: src/services/entities/UnlockService.ts (411行)
// 离线可用，同步解锁状态

// 离线可用的操作
const unlockService = new UnlockService();
const isUnlocked = await unlockService.isContentUnlocked(userId, contentType, contentId);
const stats = await unlockService.getUserUnlockStats(userId);

// 同步策略: 高优先级同步 ('user_unlocks': 'high')
// 解锁操作: 根据方式决定 (VIP需验证，购买需在线)
```

### 🔄 **智能同步 (Smart Sync)**
这些服务负责协调离线和在线数据：

#### **11. 同步协调器 (SyncCoordinator)**
```typescript
// 位置: src/services/sync/SyncCoordinator.ts (458行)
// 智能同步策略

class SyncCoordinator {
  // 同步优先级
  syncPriorities: {
    'user_configs': 'high',      // 用户配置最高优先级
    'vip_subscriptions': 'high', // VIP状态高优先级
    'user_unlocks': 'high',      // 解锁状态高优先级
    'mood_entries': 'medium',    // 心情记录中等优先级
    'quiz_sessions': 'medium',   // Quiz会话中等优先级
    'quiz_answers': 'medium',    // Quiz答案中等优先级
    'tags': 'low',               // 标签数据低优先级
    'ui_labels': 'low'           // UI标签最低优先级
  }

  // 自动同步触发
  - 网络恢复时自动同步
  - 定期同步 (可配置间隔)
  - 用户主动触发同步
  - 应用启动时同步
}
```

#### **12. 网络状态服务 (NetworkStatusService)**
```typescript
// 位置: src/services/online/NetworkStatusService.ts
// 网络状态监控和管理

class NetworkStatusService {
  // 功能
  - 实时网络状态检测
  - 连接类型识别 (WiFi, 4G, 3G等)
  - 连接质量评估 (慢速连接检测)
  - 网络变化事件通知

  // 触发同步
  - 网络恢复时通知 SyncCoordinator
  - 连接质量变化时调整同步策略
}
```

## 🎯 **实现策略总结**

### **数据流模式**

#### **数据获取流程**
```
用户请求 → 在线服务 (优先) → 成功返回
           ↓ (网络失败)
         离线服务 (回退) → 本地数据返回
```

#### **数据提交流程**
```
用户提交 → 在线服务 (优先) → 成功提交
           ↓ (网络失败)
         离线服务 (回退) → 本地存储 + 标记待同步
           ↓
         网络恢复时 → SyncCoordinator → 自动同步
```

### **同步机制**

#### **自动同步触发条件**
- ✅ 网络状态从离线变为在线
- ✅ 应用启动时 (如果有网络)
- ✅ 定期同步 (可配置间隔，默认1小时)
- ✅ 用户主动下拉刷新

#### **同步优先级处理**
1. **高优先级**: 用户配置、VIP状态、解锁状态 - 立即同步
2. **中等优先级**: 心情记录、Quiz数据 - 批量同步
3. **低优先级**: 标签、UI标签 - 后台同步

#### **冲突解决策略**
- **默认策略**: `client_wins` - 客户端数据优先
- **特殊情况**: VIP状态、支付记录 - `server_wins` - 服务端权威
- **手动解决**: 重要数据冲突时提示用户选择

### **离线功能保障**

#### **核心功能离线可用**
- ✅ Quiz 游戏完整流程
- ✅ 心情记录和查看
- ✅ 用户配置管理
- ✅ 已解锁内容使用
- ✅ 历史数据查看

#### **在线功能限制**
- ❌ 用户注册和登录
- ❌ VIP购买和支付
- ❌ 新内容下载
- ❌ 实时数据分析
- ❌ 社交功能 (如果有)

这个分类为服务端改造提供了清晰的指导，确保服务端专注于必须在线的功能，与客户端的离线优先架构完美配合。
