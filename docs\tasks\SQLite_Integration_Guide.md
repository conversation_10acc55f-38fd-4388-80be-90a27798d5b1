# SQLite 离线存储集成指南

本指南详细说明如何在 Mindful Mood 应用中集成 `@capacitor-community/sqlite` 实现离线存储。

## 概述

基于对 `ionic7-react-sqlite-app` 示例应用的深入分析，我们实现了一套完整的 SQLite 离线存储解决方案，整合了四个核心服务架构：

- **SQLiteService** - 离线存储核心，管理连接和基础操作
- **DbVersionService** - 版本管理，跟踪数据库升级状态
- **StorageService** - 数据服务，处理业务逻辑和持久化
- **InitializeAppService** - 应用初始化，协调所有服务

这四个服务已完全整合到我们的 `DatabaseService` 中，提供统一的接口和最佳的用户体验。

## 四个核心服务架构

### 1. 整合的服务架构

我们将示例应用的四个核心服务完全整合到了 `DatabaseService` 中：

```
React Components/Hooks
    ↓
Integrated DatabaseService
    ├── SQLiteService (连接管理)
    ├── DbVersionService (版本管理)
    ├── StorageService (数据持久化)
    └── InitializeAppService (应用初始化)
    ↓
@capacitor-community/sqlite
```

### 2. 服务依赖关系

```
InitializeAppService (应用初始化)
    ↓
StorageService (数据服务)
    ↓
SQLiteService + DbVersionService (核心服务)
    ↓
@capacitor-community/sqlite
```

### 3. 整合的 DatabaseService 功能

#### SQLiteService 功能
- 管理 SQLite 连接的底层操作
- 处理平台特定的初始化逻辑 (Web/Native)
- 提供连接状态检查和管理
- 数据库文件操作 (saveToStore, saveToLocalDisk)

#### DbVersionService 功能
- 跟踪数据库版本信息
- 管理升级状态
- 版本信息持久化

#### StorageService 功能
- 业务数据操作和持久化
- 集成升级语句执行
- 使用 BehaviorSubject 通知状态变化
- 初始数据插入和管理

#### InitializeAppService 功能
- 协调所有服务的初始化过程
- 处理平台特定逻辑 (jeep-sqlite 等待)
- Web平台的特殊处理流程
- 完整的错误处理和重试机制

## 实现步骤

### 1. 安装依赖

```bash
npm install @capacitor-community/sqlite
```

### 2. 配置数据库升级语句

创建 `src/database/upgrades/MindfulMoodUpgradeStatements.ts`：

```typescript
import { DatabaseUpgradeStatement } from '../../services/base/DatabaseService';

export const MindfulMoodUpgradeStatements: DatabaseUpgradeStatement[] = [
  {
    toVersion: 1,
    statements: [
      `CREATE TABLE IF NOT EXISTS mood_entries (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        mood_score INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );`,
      `CREATE TABLE IF NOT EXISTS emotion_data_sets (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        emoji_set TEXT NOT NULL,
        is_active INTEGER DEFAULT 1
      );`,
      // 更多表定义...
    ]
  }
];
```

### 3. 使用整合的 DatabaseService

#### 方法一：直接使用 DatabaseService

```typescript
import { DatabaseService } from './services/base/DatabaseService';
import { MindfulMoodUpgradeStatements } from './database/upgrades/MindfulMoodUpgradeStatements';

const App: React.FC = () => {
  useEffect(() => {
    const setupDatabase = async () => {
      const dbService = DatabaseService.getInstance();

      // 设置升级语句
      dbService.setUpgradeStatements(MindfulMoodUpgradeStatements);

      // 初始化应用
      await dbService.initializeApp();
    };
    setupDatabase();
  }, []);

  return (
    <IonApp>
      {/* 你的应用内容 */}
    </IonApp>
  );
};
```

#### 方法二：使用 Context Provider

```typescript
import { SQLiteProvider } from './contexts/SQLiteContext';
import SQLiteAppInitializer from './components/SQLiteAppInitializer';

const App: React.FC = () => (
  <SQLiteProvider>
    <SQLiteAppInitializer>
      <IonApp>
        {/* 你的应用内容 */}
      </IonApp>
    </SQLiteAppInitializer>
  </SQLiteProvider>
);
```

#### 方法三：使用服务工厂

```typescript
import { Services, initializeServices } from './services';

const App: React.FC = () => {
  useEffect(() => {
    const setupApp = async () => {
      // 使用服务初始化函数
      await initializeServices();

      // 或者直接使用整合的数据库服务
      const dbService = await Services.integratedDatabase();
      console.log('Database ready:', dbService.isInitialized());
    };
    setupApp();
  }, []);

  return <IonApp>{/* 你的应用内容 */}</IonApp>;
};
```

### 4. 在组件中使用数据库

#### 方法一：直接使用 DatabaseService

```typescript
import { DatabaseService } from '../services/base/DatabaseService';

const MyComponent: React.FC = () => {
  const [data, setData] = useState([]);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const setupDatabase = async () => {
      const dbService = DatabaseService.getInstance();

      // 监听初始化状态
      const subscription = dbService.isInitCompleted.subscribe((initialized) => {
        setIsReady(initialized);
        if (initialized) {
          loadData();
        }
      });

      return () => subscription.unsubscribe();
    };

    const loadData = async () => {
      const dbService = DatabaseService.getInstance();
      const db = dbService.getConnection();
      if (db) {
        const result = await db.query('SELECT * FROM mood_entries');
        setData(result.values || []);
      }
    };

    setupDatabase();
  }, []);

  if (!isReady) {
    return <div>Loading database...</div>;
  }

  return <div>{/* 渲染数据 */}</div>;
};
```

#### 方法二：使用整合的 Hook

```typescript
import { useSQLiteIntegrated, useSQLiteQuery } from '../hooks/useSQLiteIntegrated';

const MyComponent: React.FC = () => {
  const { state } = useSQLiteIntegrated();
  const { executeQuery, isReady } = useSQLiteQuery();
  const [data, setData] = useState([]);

  useEffect(() => {
    if (isReady) {
      const loadData = async () => {
        const result = await executeQuery('SELECT * FROM mood_entries');
        setData(result);
      };
      loadData();
    }
  }, [isReady]);

  if (!state.isDatabaseInitialised) {
    return <div>Loading database...</div>;
  }

  return <div>{/* 渲染数据 */}</div>;
};
```

#### 方法三：使用 Context

```typescript
import { useSQLiteContext, useStorageService } from '../contexts/SQLiteContext';

const MyComponent: React.FC = () => {
  const { platform } = useSQLiteContext();
  const storageService = useStorageService();
  const [data, setData] = useState([]);

  useEffect(() => {
    const subscription = storageService.isInitCompleted.subscribe((initialized) => {
      if (initialized) {
        const db = storageService.getConnection();
        if (db) {
          db.query('SELECT * FROM mood_entries').then(result => {
            setData(result.values || []);
          });
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [storageService]);

  return <div>{/* 渲染数据 */}</div>;
};
```

## 四个核心服务详解

### 1. SQLiteService - 连接管理核心

整合到 `DatabaseService` 中的 SQLite 连接管理功能：

```typescript
// 平台检测
getPlatform(): string

// Web平台初始化
initWebStore(): Promise<void>

// 数据库连接管理
openDatabase(dbName: string, loadToVersion: number, readOnly: boolean): Promise<SQLiteDBConnection>
closeDatabase(dbName: string, readOnly: boolean): Promise<void>
isConnection(dbName: string, readOnly: boolean): Promise<boolean>
checkConnectionsConsistency(): Promise<boolean>

// 数据持久化
saveToStore(dbName: string): Promise<void>
saveToLocalDisk(dbName: string): Promise<void>
```

### 2. DbVersionService - 版本管理

数据库版本跟踪和管理功能：

```typescript
// 版本管理
setDbVersion(dbName: string, version: number): void
getDbVersion(dbName: string): number | undefined
getAllVersions(): Map<string, number>
clearVersion(dbName: string): void
hasVersion(dbName: string): boolean
```

### 3. StorageService - 数据持久化

业务数据操作和状态管理：

```typescript
// 数据库初始化
initializeDatabase(): Promise<void>
insertInitialData(): Promise<void>

// 状态通知
isInitCompleted: BehaviorSubject<boolean>

// 升级管理
setUpgradeStatements(statements: DatabaseUpgradeStatement[]): void
```

### 4. InitializeAppService - 应用初始化

协调所有服务的初始化流程：

```typescript
// 应用初始化
initializeApp(): Promise<boolean>

// 平台特定处理
waitForJeepSQLite(): Promise<void>  // Web平台

// 状态管理
isInitialized(): boolean
cleanup(): Promise<void>
```

## 初始化流程详解

### 完整的初始化序列

```
1. 检查平台 (Web/Native)
   ↓
2. Web平台特殊处理
   - 等待 jeep-sqlite 元素加载
   - 初始化 Web Store
   ↓
3. 设置升级语句
   - 配置数据库schema升级路径
   ↓
4. 打开数据库连接
   - 检查现有连接
   - 创建新连接或复用现有连接
   ↓
5. 执行数据库升级
   - 自动执行待处理的升级
   ↓
6. 插入初始数据
   - 检查是否为新数据库
   - 插入默认数据
   ↓
7. 设置版本信息
   - 更新版本记录
   ↓
8. Web平台保存
   - 保存到 Web Store
   ↓
9. 通知初始化完成
   - 发送 BehaviorSubject 通知
```

## 平台特定处理

### Web 平台

1. **jeep-sqlite 元素**: 自动等待元素加载完成
2. **Web Store**: 自动初始化和数据保存
3. **连接管理**: 特殊的连接检查逻辑
4. **存储持久化**: 调用 `saveToStore()` 确保数据持久化

### Native 平台

1. **直接连接**: 无需额外初始化步骤
2. **文件系统**: 直接访问设备存储
3. **性能优化**: 原生 SQLite 性能
4. **自动持久化**: 数据自动保存到文件系统

## 数据库升级

### 升级流程

1. 检查当前数据库版本
2. 确定需要执行的升级
3. 在事务中执行升级语句
4. 更新版本号
5. 提交或回滚

### 升级语句示例

```typescript
{
  toVersion: 2,
  statements: [
    `ALTER TABLE mood_entries ADD COLUMN notes TEXT;`,
    `CREATE INDEX IF NOT EXISTS idx_mood_entries_notes ON mood_entries (notes);`
  ]
}
```

## 错误处理

### 常见错误类型

1. **连接失败**: 数据库文件不存在或权限问题
2. **升级失败**: SQL 语法错误或约束冲突
3. **平台兼容**: Web/Native 平台差异

### 错误处理策略

1. **详细日志**: 记录所有操作和错误
2. **用户提示**: 友好的错误消息
3. **自动重试**: 网络或临时错误的重试机制
4. **降级处理**: 关键功能的备用方案

## 与现有代码兼容性

### 1. 兼容现有 useSQLiteDB Hook

我们提供了兼容性 Hook，确保现有代码无需修改：

```typescript
// src/hooks/useSQLiteIntegrated.ts
export const useSQLiteDBCompatible = () => {
  const { state } = useSQLiteIntegrated();
  const dbConnection = useRef<SQLiteDBConnection | null>(state.db);

  return {
    db: state.db,
    dbConnection,
    isDatabaseInitialised: state.isDatabaseInitialised,
    isInitializing: state.isInitializing,
    dbName: state.dbName
  };
};
```

### 2. 更新现有 BaseRepository

```typescript
// 方法一：直接使用整合的 DatabaseService
import { DatabaseService } from '../base/DatabaseService';

export class BaseRepository<T, TCreate, TUpdate> {
  protected async getConnection(): Promise<SQLiteDBConnection> {
    const dbService = DatabaseService.getInstance();
    const connection = dbService.getConnection();
    if (!connection) {
      throw new Error('Database not initialized');
    }
    return connection;
  }
}

// 方法二：使用 Hook 方式
import { useSQLiteIntegrated } from '../hooks/useSQLiteIntegrated';

export class BaseRepository<T, TCreate, TUpdate> {
  protected useDatabase() {
    const { state } = useSQLiteIntegrated();
    return {
      db: state.db,
      isReady: state.isDatabaseInitialised
    };
  }
}
```

### 3. 迁移现有组件

#### 从现有 useSQLiteDB 迁移

```typescript
// 旧代码
import { useSQLiteDB } from '../hooks/useSQLiteDB';

const MyComponent = () => {
  const { db, isDatabaseInitialised } = useSQLiteDB();
  // ...
};

// 新代码 - 兼容性版本
import { useSQLiteDBCompatible } from '../hooks/useSQLiteIntegrated';

const MyComponent = () => {
  const { db, isDatabaseInitialised } = useSQLiteDBCompatible();
  // 代码无需修改
};

// 新代码 - 推荐版本
import { useSQLiteIntegrated } from '../hooks/useSQLiteIntegrated';

const MyComponent = () => {
  const { state } = useSQLiteIntegrated();
  const { db, isDatabaseInitialised } = state;
  // 获得更多功能和更好的错误处理
};
```

### 4. 渐进式迁移策略

#### 阶段1：保持现有代码运行
```typescript
// 使用兼容性 Hook，无需修改现有代码
import { useSQLiteDBCompatible as useSQLiteDB } from '../hooks/useSQLiteIntegrated';
```

#### 阶段2：逐步迁移到新接口
```typescript
// 逐个组件迁移到新的 Hook
import { useSQLiteIntegrated } from '../hooks/useSQLiteIntegrated';
```

#### 阶段3：利用新功能
```typescript
// 使用新的错误处理、状态管理等功能
const { state, actions } = useSQLiteIntegrated();
const diagnostics = await actions.getDiagnostics();
```

## 性能优化

### 1. 连接管理
- 单例模式避免重复连接
- 连接池管理（如需要）
- 及时关闭不用的连接

### 2. 查询优化
- 使用索引优化查询
- 批量操作使用事务
- 避免 N+1 查询问题

### 3. 数据库维护
- 定期执行 VACUUM
- 更新统计信息 (ANALYZE)
- 监控数据库大小

## 测试策略

### 1. 单元测试
- 服务层逻辑测试
- 数据库操作测试
- 错误处理测试

### 2. 集成测试
- 完整初始化流程测试
- 升级流程测试
- 平台兼容性测试

### 3. 端到端测试
- 用户操作流程测试
- 数据持久化测试
- 离线功能测试

## 监控和调试

### 1. 日志系统
- 结构化日志记录
- 不同级别的日志
- 性能指标收集

### 2. 调试工具
- 数据库状态指示器
- 连接状态监控
- 查询性能分析

### 3. 错误报告
- 自动错误收集
- 用户反馈机制
- 远程诊断支持

## 最佳实践

### 1. 数据库设计
- 合理的表结构设计
- 适当的索引策略
- 数据类型选择

### 2. 代码组织
- 清晰的服务层分离
- 统一的错误处理
- 可测试的代码结构

### 3. 用户体验
- 平滑的加载体验
- 清晰的错误提示
- 离线功能支持

## 故障排除

### 常见问题

1. **Web 平台初始化失败**
   - 检查 jeep-sqlite 元素
   - 验证 Web Store 配置

2. **数据库连接失败**
   - 检查文件权限
   - 验证数据库路径

3. **升级失败**
   - 检查 SQL 语法
   - 验证数据完整性

### 解决方案

1. **重置数据库**: 开发阶段可以删除重建
2. **手动修复**: 生产环境的数据修复
3. **版本回退**: 升级失败的回退策略

## 总结

通过整合 `ionic7-react-sqlite-app` 示例应用的四个核心服务架构，我们实现了：

### 🎯 四个核心服务整合
- **SQLiteService** - 离线存储核心，管理连接和基础操作
- **DbVersionService** - 版本管理，跟踪数据库升级状态
- **StorageService** - 数据服务，处理业务逻辑和持久化
- **InitializeAppService** - 应用初始化，协调所有服务

### ✅ 主要成果
- ✅ 跨平台的离线存储 (Web/Native)
- ✅ 完整的数据库管理和升级系统
- ✅ 与现有架构的无缝集成和向后兼容
- ✅ 用户友好的初始化体验和错误处理
- ✅ 健壮的状态管理和实时通知
- ✅ 企业级的性能优化和监控

### 🔧 技术优势
- **单一服务**: 四个服务整合到 `DatabaseService` 中，简化使用
- **状态管理**: BehaviorSubject 实时状态通知
- **平台适配**: 自动处理 Web/Native 平台差异
- **兼容性**: 完全兼容现有 `useSQLiteDB` Hook
- **错误处理**: 完善的错误处理和重试机制
- **调试友好**: 丰富的诊断和监控功能

### 🚀 使用方式
1. **直接使用**: `DatabaseService.getInstance().initializeApp()`
2. **Hook 方式**: `useSQLiteIntegrated()` 或兼容的 `useSQLiteDBCompatible()`
3. **Context 方式**: `SQLiteProvider` + `SQLiteAppInitializer`
4. **服务工厂**: `Services.integratedDatabase()`

### 📈 与示例应用对比
| 功能 | 示例应用 | 我们的实现 |
|------|----------|------------|
| 四服务架构 | ✅ | ✅ 完全整合 |
| 平台适配 | ✅ | ✅ 更智能 |
| 错误处理 | 基础 | ✅ 企业级 |
| Hook 集成 | 无 | ✅ 丰富的 Hook |
| 现有代码兼容 | 无 | ✅ 完全兼容 |
| 状态管理 | BehaviorSubject | ✅ 增强版 |

这套解决方案不仅学习了示例应用的最佳实践，还在此基础上提供了更好的开发体验、更强的错误处理能力和与现有代码的完美兼容性。为 Mindful Mood 应用提供了可靠的离线数据存储能力，确保用户在任何网络条件下都能正常使用应用功能。
