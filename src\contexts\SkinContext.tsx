/**
 * 皮肤上下文
 * 用于管理皮肤的选择和应用
 */

import { useDatabaseContext } from '@/contexts/DatabaseContext';
import { Services } from '@/services';
import type { Skin } from '@/types';
import type React from 'react';
import { type ReactNode, createContext, useContext, useEffect, useState } from 'react';
import { toast } from 'sonner';

interface SkinContextType {
  skins: Skin[];
  activeSkin: Skin | null;
  setActiveSkin: (skinId: string) => Promise<boolean>;
  unlockSkin: (skinId: string) => Promise<boolean>;
  isLoading: boolean;
  // ⚠️ 兼容性属性 - 计划在下一版本中移除
  /** @deprecated 使用 activeSkin 替代 */
  currentSkin: Skin | null;
  /** @deprecated 使用 skins 替代 */
  availableSkins: Skin[];
  /** @deprecated 使用 skins.filter(s => s.is_unlocked) 替代 */
  unlockedSkins: Skin[];
}

const SkinContext = createContext<SkinContextType | undefined>(undefined);

export const SkinProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isInitialized } = useDatabaseContext();
  const [skins, setSkins] = useState<Skin[]>([]);
  const [activeSkin, setActiveSkin] = useState<Skin | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 加载皮肤数据
  useEffect(() => {
    const loadSkins = async () => {
      setIsLoading(true);
      try {
        if (isInitialized) {
          // 从数据库加载皮肤
          const skinService = Services.getSkinService();
          const skinsResult = await skinService.findAll();

          if (skinsResult.success && skinsResult.data) {
            setSkins(skinsResult.data);

            // 获取默认皮肤（选择第一个解锁的皮肤，或者第一个皮肤）
            const unlockedSkin = skinsResult.data.find((skin: Skin) => skin.is_unlocked);
            if (unlockedSkin) {
              setActiveSkin(unlockedSkin);
            } else if (skinsResult.data.length > 0) {
              setActiveSkin(skinsResult.data[0]);
            }
          } else {
            console.warn('数据库中没有皮肤数据');
            setSkins([]);
            setActiveSkin(null);
          }
        } else {
          console.log('数据库未初始化，等待初始化完成');
          setSkins([]);
          setActiveSkin(null);
        }
      } catch (error) {
        console.error('Failed to load skins:', error);
        toast.error('加载皮肤失败');
        setSkins([]);
        setActiveSkin(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadSkins();
  }, [isInitialized]);

  // 设置活动皮肤
  const handleSetActiveSkin = async (skinId: string) => {
    try {
      const skin = skins.find(s => s.id === skinId);
      if (skin) {
        setActiveSkin(skin);
        return true;
      }

      // 如果在当前列表中找不到，尝试从服务获取
      if (isInitialized) {
        const skinService = Services.getSkinService();
        const result = await skinService.findById(skinId);
        if (result.success && result.data) {
          setActiveSkin(result.data);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Failed to set active skin:', error);
      return false;
    }
  };

  // 解锁皮肤
  const handleUnlockSkin = async (skinId: string): Promise<boolean> => {
    try {
      if (isInitialized) {
        const skinService = Services.getSkinService();
        const result = await skinService.unlockSkin(skinId);
        if (result.success) {
          // 刷新皮肤列表
          const skinsResult = await skinService.findAll();
          if (skinsResult.success && skinsResult.data) {
            setSkins(skinsResult.data);
          }
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Failed to unlock skin:', error);
      return false;
    }
  };

  // ⚠️ 计算兼容性属性 - 计划在下一版本中移除
  const unlockedSkins = skins.filter((skin) => skin.is_unlocked);

  return (
    <SkinContext.Provider
      value={{
        skins,
        activeSkin,
        setActiveSkin: handleSetActiveSkin,
        unlockSkin: handleUnlockSkin,
        isLoading,
        // ⚠️ 兼容性属性 - 计划在下一版本中移除
        currentSkin: activeSkin, // @deprecated
        availableSkins: skins, // @deprecated
        unlockedSkins, // @deprecated
      }}
    >
      {children}
    </SkinContext.Provider>
  );
};

// 添加 displayName 以支持 Hot Reload
SkinProvider.displayName = 'SkinProvider';

export const useSkinManager = (): SkinContextType => {
  const context = useContext(SkinContext);
  if (context === undefined) {
    throw new Error('useSkinManager must be used within a SkinProvider');
  }
  return context;
};
