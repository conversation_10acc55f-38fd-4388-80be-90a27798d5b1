/**
 * 网络状态管理服务
 * 监控网络连接状态，提供网络状态信息和事件通知
 */

import { type NetworkStatus, OnlineServiceEvent } from './types/OnlineServiceTypes';

export type NetworkStatusListener = (status: NetworkStatus) => void;

export class NetworkStatusService {
  private static instance: NetworkStatusService;
  private listeners: Set<NetworkStatusListener> = new Set();
  private currentStatus: NetworkStatus;
  private checkInterval?: number;
  private isMonitoring = false;

  private constructor() {
    this.currentStatus = {
      isOnline: navigator.onLine,
      connectionType: this.detectConnectionType(),
      isSlowConnection: false,
      lastOnlineTime: navigator.onLine ? new Date() : undefined,
      lastOfflineTime: !navigator.onLine ? new Date() : undefined,
    };

    this.setupEventListeners();
  }

  static getInstance(): NetworkStatusService {
    if (!NetworkStatusService.instance) {
      NetworkStatusService.instance = new NetworkStatusService();
    }
    return NetworkStatusService.instance;
  }

  /**
   * 获取当前网络状态
   */
  getCurrentStatus(): NetworkStatus {
    return { ...this.currentStatus };
  }

  /**
   * 添加网络状态监听器
   */
  addListener(listener: NetworkStatusListener): void {
    this.listeners.add(listener);
  }

  /**
   * 移除网络状态监听器
   */
  removeListener(listener: NetworkStatusListener): void {
    this.listeners.delete(listener);
  }

  /**
   * 开始监控网络状态
   */
  startMonitoring(checkInterval = 30000): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;

    // 定期检查网络状态
    this.checkInterval = window.setInterval(() => {
      this.checkNetworkStatus();
    }, checkInterval);

    console.log('[NetworkStatusService] Started monitoring network status');
  }

  /**
   * 停止监控网络状态
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;

    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = undefined;
    }

    console.log('[NetworkStatusService] Stopped monitoring network status');
  }

  /**
   * 手动检查网络状态
   */
  async checkNetworkStatus(): Promise<NetworkStatus> {
    const wasOnline = this.currentStatus.isOnline;
    const isOnline = navigator.onLine;

    // 检测连接类型
    const connectionType = this.detectConnectionType();

    // 检测连接速度
    const isSlowConnection = await this.detectSlowConnection();

    // 更新状态
    const newStatus: NetworkStatus = {
      isOnline,
      connectionType,
      isSlowConnection,
      lastOnlineTime: isOnline && !wasOnline ? new Date() : this.currentStatus.lastOnlineTime,
      lastOfflineTime: !isOnline && wasOnline ? new Date() : this.currentStatus.lastOfflineTime,
    };

    // 如果状态发生变化，通知监听器
    if (this.hasStatusChanged(this.currentStatus, newStatus)) {
      this.currentStatus = newStatus;
      this.notifyListeners(newStatus);
    }

    return newStatus;
  }

  /**
   * 测试网络连接
   */
  async testConnection(url?: string, timeout = 5000): Promise<boolean> {
    if (!navigator.onLine) {
      return false;
    }

    // 使用tRPC端点进行连接测试
    const testUrl = url || '/trpc/health';

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(testUrl, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      console.warn('[NetworkStatusService] Connection test failed:', error);
      return false;
    }
  }

  /**
   * 获取网络质量信息
   */
  async getNetworkQuality(): Promise<{
    rtt?: number;
    downlink?: number;
    effectiveType?: string;
  }> {
    // 使用 Network Information API（如果可用）
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    if (connection) {
      return {
        rtt: connection.rtt,
        downlink: connection.downlink,
        effectiveType: connection.effectiveType,
      };
    }

    // 如果 Network Information API 不可用，进行简单的延迟测试
    const startTime = performance.now();
    const isConnected = await this.testConnection();
    const endTime = performance.now();

    if (isConnected) {
      return {
        rtt: endTime - startTime,
      };
    }

    return {};
  }

  /**
   * 等待网络连接恢复
   */
  async waitForConnection(timeout = 30000): Promise<boolean> {
    if (this.currentStatus.isOnline) {
      return true;
    }

    return new Promise((resolve) => {
      const timeoutId = setTimeout(() => {
        this.removeListener(listener);
        resolve(false);
      }, timeout);

      const listener: NetworkStatusListener = (status) => {
        if (status.isOnline) {
          clearTimeout(timeoutId);
          this.removeListener(listener);
          resolve(true);
        }
      };

      this.addListener(listener);
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听在线/离线事件
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // 监听连接变化（如果支持）
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    if (connection) {
      connection.addEventListener('change', this.handleConnectionChange.bind(this));
    }

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
  }

  /**
   * 处理在线事件
   */
  private handleOnline(): void {
    console.log('[NetworkStatusService] Network connection restored');
    this.checkNetworkStatus();
  }

  /**
   * 处理离线事件
   */
  private handleOffline(): void {
    console.log('[NetworkStatusService] Network connection lost');
    this.checkNetworkStatus();
  }

  /**
   * 处理连接变化事件
   */
  private handleConnectionChange(): void {
    console.log('[NetworkStatusService] Network connection changed');
    this.checkNetworkStatus();
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    if (!document.hidden) {
      // 页面变为可见时检查网络状态
      this.checkNetworkStatus();
    }
  }

  /**
   * 检测连接类型
   */
  private detectConnectionType(): 'wifi' | 'cellular' | 'ethernet' | 'unknown' {
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    if (connection?.type) {
      switch (connection.type) {
        case 'wifi':
          return 'wifi';
        case 'cellular':
          return 'cellular';
        case 'ethernet':
          return 'ethernet';
        default:
          return 'unknown';
      }
    }

    return 'unknown';
  }

  /**
   * 检测慢速连接
   */
  private async detectSlowConnection(): Promise<boolean> {
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    if (connection) {
      // 使用 Network Information API
      if (connection.effectiveType) {
        return connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';
      }

      if (connection.downlink !== undefined) {
        return connection.downlink < 1; // 小于 1 Mbps 认为是慢速连接
      }
    }

    // 如果没有 Network Information API，进行简单的延迟测试
    try {
      const quality = await this.getNetworkQuality();
      return (quality.rtt || 0) > 1000; // RTT 大于 1 秒认为是慢速连接
    } catch {
      return false;
    }
  }

  /**
   * 检查状态是否发生变化
   */
  private hasStatusChanged(oldStatus: NetworkStatus, newStatus: NetworkStatus): boolean {
    return (
      oldStatus.isOnline !== newStatus.isOnline ||
      oldStatus.connectionType !== newStatus.connectionType ||
      oldStatus.isSlowConnection !== newStatus.isSlowConnection
    );
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(status: NetworkStatus): void {
    this.listeners.forEach((listener) => {
      try {
        listener(status);
      } catch (error) {
        console.error('[NetworkStatusService] Error in status listener:', error);
      }
    });
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopMonitoring();
    this.listeners.clear();

    // 移除事件监听器
    window.removeEventListener('online', this.handleOnline.bind(this));
    window.removeEventListener('offline', this.handleOffline.bind(this));
    document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    if (connection) {
      connection.removeEventListener('change', this.handleConnectionChange.bind(this));
    }
  }
}
