# ViewFactory 使用指南

本文档提供了 ViewFactory 的使用示例和最佳实践。

## 1. 基本使用

### 1.1 创建视图

使用 `createView` 方法创建视图：

```typescript
import { ViewFactory } from '@/utils/viewFactory';
import { SkinManager } from '@/utils/skinManager';

// 创建视图
const view = ViewFactory.createView('wheel', 'textEmoji', skinConfig);

// 渲染视图
const renderedView = view.render(emotions, tierLevel, onSelect);
```

### 1.2 使用用户配置

使用 `createViewFromUserConfig` 方法根据用户配置创建视图：

```typescript
import { ViewFactory } from '@/utils/viewFactory';
import { SkinManager } from '@/utils/skinManager';

// 获取皮肤管理器
const skinManager = new SkinManager();

// 创建视图
const view = ViewFactory.createViewFromUserConfig(userConfig, skinManager);

// 渲染视图
const renderedView = view.render(emotions, tierLevel, onSelect);
```

### 1.3 使用特定渲染引擎

使用 `createWheel` 方法创建特定渲染引擎的轮盘视图：

```typescript
import { ViewFactory } from '@/utils/viewFactory';

// 创建 R3F 轮盘视图
const view = ViewFactory.createWheel('R3F', 'textEmoji', skinConfig);

// 渲染视图
const renderedView = view.render(emotions, tierLevel, onSelect);
```

### 1.4 使用特定内容显示模式

使用 `createWheel` 方法创建特定内容显示模式的轮盘视图：

```typescript
import { ViewFactory } from '@/utils/viewFactory';

// 创建仅显示表情的轮盘视图
const view = ViewFactory.createWheel('D3', 'emoji', skinConfig);

// 渲染视图
const renderedView = view.render(emotions, tierLevel, onSelect);
```

### 1.5 使用视图配置

使用 `render` 方法的 `config` 参数传递视图配置：

```typescript
import { ViewFactory } from '@/utils/viewFactory';

// 创建视图
const view = ViewFactory.createWheel('D3', 'textEmoji', skinConfig);

// 渲染视图，传递配置
const renderedView = view.render(emotions, tierLevel, onSelect, {
  onBack: () => {
    // 返回上一级
    setTierLevel(tierLevel - 1);
  },
  selectedPath: 'Path > To > Emotion'
});
```

## 2. 高级使用

### 2.1 皮肤兼容性检查

ViewFactory 提供了皮肤兼容性检查功能，确保用户选择的视图类型、内容显示模式和渲染引擎与当前皮肤兼容：

```typescript
import { ViewFactory } from '@/utils/viewFactory';

// 检查皮肤是否支持指定的视图类型
const isSupportedViewType = ViewFactory.isSupportedViewType('wheel', skinConfig);

// 获取皮肤支持的回退视图类型
const fallbackViewType = ViewFactory.getFallbackViewType(skinConfig);

// 检查皮肤是否支持指定的内容显示模式
const isSupportedContentMode = ViewFactory.isSupportedContentMode('emoji', 'wheel', skinConfig);

// 获取皮肤支持的回退内容显示模式
const fallbackContentMode = ViewFactory.getFallbackContentMode('wheel', skinConfig);

// 检查皮肤是否支持指定的渲染引擎
const isSupportedRenderEngine = ViewFactory.isSupportedRenderEngine('R3F', skinConfig);

// 获取皮肤支持的回退渲染引擎
const fallbackRenderEngine = ViewFactory.getFallbackRenderEngine(skinConfig);
```

### 2.2 高级内容显示模式

ViewFactory 支持高级内容显示模式，包括动画表情和图片模式：

```typescript
import { ViewFactory } from '@/utils/viewFactory';

// 创建动画表情模式的轮盘视图
const view = ViewFactory.createWheel('D3', 'animatedEmoji', skinConfig);

// 渲染视图
const renderedView = view.render(emotions, tierLevel, onSelect);
```

### 2.3 轮盘视图特殊配置

ViewFactory 支持轮盘视图的特殊配置，包括自定义轮盘大小、自定义扇区间隙和边框、3D 效果等：

```typescript
import { ViewFactory } from '@/utils/viewFactory';

// 创建带有 3D 效果的轮盘视图
const skinConfigWith3D = {
  ...skinConfig,
  view_configs: {
    ...skinConfig.view_configs,
    wheel: {
      ...skinConfig.view_configs?.wheel,
      use3DEffects: true,
      perspective: 1000,
      depth: 50
    }
  }
};

const view = ViewFactory.createWheel('R3F', 'textEmoji', skinConfigWith3D);

// 渲染视图
const renderedView = view.render(emotions, tierLevel, onSelect);
```

### 2.4 高级用户功能

ViewFactory 支持高级用户功能，包括自定义布局和动画效果：

```typescript
import { ViewFactory } from '@/utils/viewFactory';

// 创建带有自定义布局的轮盘视图
const skinConfigWithCustomLayout = {
  ...skinConfig,
  view_configs: {
    ...skinConfig.view_configs,
    wheel: {
      ...skinConfig.view_configs?.wheel,
      customLayout: {
        sectorSpacing: 'dynamic',
        sectorSizing: 'proportional',
        centerOffset: 20,
        rotationOffset: 45
      }
    }
  }
};

const view = ViewFactory.createWheel('D3', 'textEmoji', skinConfigWithCustomLayout);

// 渲染视图
const renderedView = view.render(emotions, tierLevel, onSelect);
```

### 2.5 设备适配功能

ViewFactory 支持设备适配功能，包括设备类型适配和离线模式支持：

```typescript
import { ViewFactory } from '@/utils/viewFactory';
import { SkinManager } from '@/utils/skinManager';

// 获取皮肤管理器
const skinManager = new SkinManager();

// 创建适配移动设备的视图
const mobileConfig = {
  ...userConfig,
  deviceType: 'mobile'
};

const view = ViewFactory.createViewFromUserConfig(mobileConfig, skinManager);

// 渲染视图
const renderedView = view.render(emotions, tierLevel, onSelect);
```

## 3. 最佳实践

### 3.1 使用 createViewFromUserConfig 方法

推荐使用 `createViewFromUserConfig` 方法创建视图，因为它会根据用户配置自动选择合适的视图类型、内容显示模式和渲染引擎，并进行兼容性检查。

### 3.2 使用皮肤管理器

推荐使用 `SkinManager` 管理皮肤，而不是直接传递皮肤配置。

### 3.3 使用视图配置

推荐使用 `render` 方法的 `config` 参数传递视图配置，而不是修改视图对象的属性。

### 3.4 使用 EmotionView 接口

推荐使用 `EmotionView` 接口定义视图类型，而不是使用具体的视图类型。

### 3.5 使用 BaseEmotionView 类

推荐继承 `BaseEmotionView` 类实现自定义视图，而不是从头实现 `EmotionView` 接口。
