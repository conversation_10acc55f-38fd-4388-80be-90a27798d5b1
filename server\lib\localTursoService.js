import * as fs from 'node:fs';
import * as path from 'node:path';
import { createClient } from '@libsql/client';
// Singleton client instance
let clientInstance = null;
/**
 * Initializes and returns a singleton SQLite client instance.
 * @returns {Client} The SQLite client.
 * @throws {Error} If SQLite DB URL is not configured.
 */
export const getTursoClient = () => {
  if (clientInstance) return clientInstance;
  // Use local SQLite file
  const dbPath = path.resolve(process.cwd(), 'local.db');
  // Check if the database file exists
  const dbExists = fs.existsSync(dbPath);
  if (!dbExists) {
    console.log(
      `[LocalTursoService] Database file not found at ${dbPath}. A new one will be created.`
    );
  } else {
    console.log(`[LocalTursoService] Using existing database file at ${dbPath}`);
  }
  try {
    // Create and store the client instance
    clientInstance = createClient({
      url: `file:${dbPath}`,
    });
    console.log('[LocalTursoService] SQLite client initialized successfully.');
    return clientInstance;
  } catch (error) {
    console.error('[LocalTursoService] Failed to initialize SQLite client:', error);
    throw error; // Re-throw the error to be handled by the caller
  }
};
/**
 * Executes a single SQL statement against the SQLite database.
 * @param {string | InStatement} sql The SQL statement or an InStatement object.
 * @returns {Promise<ResultSet>} A promise that resolves with the query result.
 * @throws {Error} If the client is not initialized or query fails.
 */
export const executeTursoQuery = async (sql) => {
  const client = getTursoClient();
  const sqlString = typeof sql === 'string' ? sql : sql.sql;
  try {
    const result = await client.execute(sql);
    // Log query execution (truncate long queries for better logs)
    const truncatedSql = sqlString.length > 100 ? `${sqlString.substring(0, 100)}...` : sqlString;
    console.log(
      `[LocalTursoService] Executed query: ${truncatedSql} (${result.rowsAffected} rows affected)`
    );
    return result;
  } catch (error) {
    console.error(`[LocalTursoService] Error executing query: ${sqlString}`, error);
    throw error;
  }
};
/**
 * Executes a batch of SQL statements in a transaction against the SQLite database.
 * @param {InStatement[]} statements An array of InStatement objects.
 * @param {'deferred' | 'write' | 'read'} [mode='write'] The transaction mode.
 * @returns {Promise<ResultSet[] | null>} A promise that resolves with an array of query results or null if transaction failed.
 * @throws {Error} If the client is not initialized or transaction fails.
 */
export const batchTursoStatements = async (statements, mode = 'write') => {
  if (!statements || statements.length === 0) {
    console.warn('[LocalTursoService] No statements provided for batch execution');
    return [];
  }
  const client = getTursoClient();
  let tx = null;
  try {
    // Start transaction
    tx = await client.transaction(mode);
    // Execute batch
    const results = await tx.batch(statements);
    // Commit transaction
    await tx.commit();
    console.log(
      `[LocalTursoService] Successfully executed batch of ${statements.length} statements`
    );
    return results;
  } catch (error) {
    console.error('[LocalTursoService] Error in batch transaction:', error);
    // Rollback transaction if it exists
    if (tx) {
      try {
        await tx.rollback();
        console.log('[LocalTursoService] Transaction rolled back successfully');
      } catch (rollbackError) {
        console.error('[LocalTursoService] Failed to rollback transaction:', rollbackError);
      }
    }
    throw error;
  }
};
/**
 * Executes a multi-statement SQL script against the SQLite database.
 * @param {string} sqlScript The SQL script containing multiple statements separated by semicolons.
 * @returns {Promise<void>} A promise that resolves when the script is executed.
 * @throws {Error} If the client is not initialized or script execution fails.
 */
export const executeSqlScript = async (sqlScript) => {
  if (!sqlScript || sqlScript.trim() === '') {
    console.warn('[LocalTursoService] Empty SQL script provided');
    return;
  }
  const client = getTursoClient();
  try {
    // Count approximate number of statements by counting semicolons
    const statementCount = (sqlScript.match(/;/g) || []).length + 1;
    await client.executeMultiple(sqlScript);
    console.log(
      `[LocalTursoService] Successfully executed SQL script with ~${statementCount} statements`
    );
  } catch (error) {
    console.error('[LocalTursoService] Failed to execute SQL script:', error);
    throw error;
  }
};
/**
 * Fetches all rows from a specified table in SQLite.
 * @param {string} tableName The name of the table to fetch from.
 * @param {number} [limit] Optional limit on the number of rows to return.
 * @returns {Promise<any[]>} A promise that resolves with an array of rows.
 * @throws {Error} If the table name is invalid or the query fails.
 */
export const fetchAllFromTursoTable = async (tableName, limit) => {
  // Validate table name to prevent SQL injection
  if (!/^[a-zA-Z0-9_]+$/.test(tableName)) {
    throw new Error(`Invalid table name: ${tableName}`);
  }
  // Build query with optional limit
  const query = limit ? `SELECT * FROM ${tableName} LIMIT ${limit}` : `SELECT * FROM ${tableName}`;
  try {
    const result = await executeTursoQuery(query);
    console.log(`[LocalTursoService] Fetched ${result.rows.length} rows from table '${tableName}'`);
    return result.rows;
  } catch (error) {
    console.error(`[LocalTursoService] Failed to fetch data from table '${tableName}':`, error);
    throw error;
  }
};
