{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/integration/integration.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 62677, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 62677, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 517, "endOffset": 23003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 560, "endOffset": 621, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 673, "endOffset": 5463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 747, "endOffset": 2151, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2225, "endOffset": 3689, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3761, "endOffset": 5455, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5048, "endOffset": 5063, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5515, "endOffset": 10463, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5572, "endOffset": 7196, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7248, "endOffset": 8872, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8922, "endOffset": 10455, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10515, "endOffset": 14609, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10570, "endOffset": 12665, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12716, "endOffset": 14601, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14662, "endOffset": 19134, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14715, "endOffset": 16826, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15281, "endOffset": 15809, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15887, "endOffset": 16179, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16255, "endOffset": 16261, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16876, "endOffset": 19126, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17588, "endOffset": 17972, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19187, "endOffset": 22999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19241, "endOffset": 20971, "count": 1}, {"startOffset": 20398, "endOffset": 20413, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21021, "endOffset": 22991, "count": 1}, {"startOffset": 22324, "endOffset": 22557, "count": 0}], "isBlockCoverage": true}], "startOffset": 185}]}