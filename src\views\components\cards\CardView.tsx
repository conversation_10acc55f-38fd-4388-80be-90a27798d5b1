/**
 * 卡片视图组件
 * 使用卡片布局显示情绪
 */

import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import type { Emotion, ContentDisplayMode, SkinConfig, CardLayout } from '@/types';
import { motion } from 'framer-motion';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';

interface CardViewProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  layout: CardLayout;
  onBack?: () => void;
  selectedPath?: any;
}

/**
 * 卡片视图组件
 * 使用卡片布局显示情绪
 */
export const CardView: React.FC<CardViewProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  layout,
  onBack,
  selectedPath,
}) => {
  const { theme } = useTheme();
  // 计算是否为深色模式
  const isDarkMode =
    theme === 'dark' ||
    (theme === 'system' &&
      typeof window !== 'undefined' &&
      window.matchMedia('(prefers-color-scheme: dark)').matches);
  const { t } = useLanguage();
  // 注意：hoveredIndex 和 setHoveredIndex 在未来的交互增强中会使用
  // 目前保留以便将来实现卡片悬停效果
  // const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const masonryRef = useRef<HTMLDivElement>(null);
  const [masonryColumns, setMasonryColumns] = useState<Emotion[][]>([]);

  // 创建一个安全的 skinConfig 对象，确保所有必要属性存在
  const safeSkinConfig: SkinConfig = {
    // 必需的支持配置
    supported_content_modes: skinConfig?.supported_content_modes || ['emoji', 'text', 'mixed'],
    supported_view_types: skinConfig?.supported_view_types || ['grid', 'list'],
    supported_render_engines: skinConfig?.supported_render_engines || ['CSS', 'SVG'],

    // 字体配置
    fonts: skinConfig?.fonts || {
      family: 'Arial, sans-serif',
      size: { small: 12, medium: 14, large: 18 },
      weight: { normal: 400, bold: 700 },
    },

    // 颜色配置
    colors: skinConfig?.colors || {
      text: isDarkMode ? '#FFFFFF' : '#000000',
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: isDarkMode ? '#1f2937' : '#FFFFFF',
    },

    // 效果配置
    effects: skinConfig?.effects || {
      shadows: true,
      animations: true,
      borderRadius: 8,
      opacity: 1,
    },

    // 视图配置
    view_configs: skinConfig?.viewConfigs || {
      card: {
        card_size: 100,
        card_spacing: 16,
        card_height: 60,
        card_border_radius: 8,
        card_hover_effect: 'scale',
        card_hover_scale: 1.05,
        layout: 'grid',
        columns: 3,
        aspect_ratio: 1,
      },
    },
  };

  // 记录调试信息
  if (!skinConfig) {
    console.warn('CardView: skinConfig is undefined, using default config');
  } else {
    if (!skinConfig.fonts) console.warn('CardView: skinConfig.fonts is undefined, using default');
    if (!skinConfig.colors) console.warn('CardView: skinConfig.colors is undefined, using default');
    if (!skinConfig.effects)
      console.warn('CardView: skinConfig.effects is undefined, using default');
  }

  // 计算卡片大小和布局
  const cardConfig = safeSkinConfig.viewConfigs?.card || {};
  const cardSize = cardConfig.cardSize || 100;
  const cardSpacing = cardConfig.cardSpacing || 16;

  // 列表布局的卡片高度
  const listCardHeight = cardConfig.cardHeight || 60;

  // 响应式布局
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);

    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  // 瀑布流布局计算
  useEffect(() => {
    if (layout === 'masonry') {
      const calculateMasonryLayout = () => {
        if (!containerRef.current) return;

        const containerWidth = containerRef.current.offsetWidth;
        const columnCount = Math.max(2, Math.floor(containerWidth / (cardSize + cardSpacing)));
        const columns: Emotion[][] = Array.from({ length: columnCount }, () => []);

        // 将情绪分配到各列，尽量保持高度平衡
        emotions.forEach((emotion) => {
          // 找出当前高度最小的列
          const shortestColumnIndex = columns
            .map((column, i) => ({
              height: column.reduce((sum, _) => sum + cardSize + cardSpacing, 0),
              index: i,
            }))
            .sort((a, b) => a.height - b.height)[0].index;

          columns[shortestColumnIndex].push(emotion);
        });

        setMasonryColumns(columns);
      };

      calculateMasonryLayout();
      window.addEventListener('resize', calculateMasonryLayout);

      return () => {
        window.removeEventListener('resize', calculateMasonryLayout);
      };
    }
  }, [layout, emotions, cardSize, cardSpacing]);

  // 计算每行卡片数量
  const calculateCardsPerRow = () => {
    if (!containerWidth) return Math.min(emotions.length, 4);

    const availableWidth = containerWidth;
    const cardWithSpacing = cardSize + cardSpacing;
    const maxCards = Math.floor(availableWidth / cardWithSpacing);

    return Math.max(1, Math.min(maxCards, emotions.length));
  };

  const cardsPerRow = calculateCardsPerRow();

  // 获取情绪内容
  const getEmotionContent = (emotion: Emotion) => {
    const text = emotion.name;
    const emoji = emotion.emoji;

    switch (contentDisplayMode) {
      case 'text':
        return { text, emoji: '' };
      case 'emoji':
        return { text: '', emoji };
      default:
        return { text, emoji };
    }
  };

  // 获取情绪颜色
  const getEmotionColor = (emotion: Emotion, index: number) => {
    // 如果情绪有自定义颜色，优先使用
    if (emotion.color) {
      return emotion.color;
    }

    // 否则根据层级和索引生成颜色
    const hue = (index * 360) / emotions.length;
    const saturation = 70;
    const lightness = isDarkMode ? 50 : 60;

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  // 处理卡片悬停
  const handleCardHover = (index: number) => {
    // 暂时不实现悬停效果
    // setHoveredIndex(index);
    console.log('Card hover:', index);
  };

  // 处理卡片离开
  const handleCardLeave = () => {
    // 暂时不实现悬停效果
    // setHoveredIndex(null);
    console.log('Card leave');
  };

  // 渲染网格布局
  const renderGridLayout = () => {
    return (
      <div
        className="card-view-grid"
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(${cardsPerRow}, ${cardSize}px)`,
          gap: `${cardSpacing}px`,
          justifyContent: 'center',
        }}
      >
        {emotions.map((emotion, index) => renderCard(emotion, index))}
      </div>
    );
  };

  // 渲染列表布局
  const renderListLayout = () => {
    return (
      <div
        className="card-view-list"
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: `${cardSpacing}px`,
          width: '100%',
          maxWidth: '600px',
          margin: '0 auto',
        }}
      >
        {emotions.map((emotion, index) => renderListCard(emotion, index))}
      </div>
    );
  };

  // 渲染瀑布流布局
  const renderMasonryLayout = () => {
    return (
      <div
        className="card-view-masonry"
        ref={masonryRef}
        style={{
          display: 'flex',
          gap: `${cardSpacing}px`,
          width: '100%',
          justifyContent: 'center',
        }}
      >
        {masonryColumns.map((column, columnIndex) => (
          <div
            key={`column-${columnIndex}`}
            className="masonry-column"
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: `${cardSpacing}px`,
              width: `${cardSize}px`,
            }}
          >
            {column.map((emotion, index) => renderCard(emotion, index, `${columnIndex}-${index}`))}
          </div>
        ))}
      </div>
    );
  };

  // 计算文本颜色（根据背景色自动调整）
  const getTextColor = (bgColor: string) => {
    if (!bgColor.startsWith('#')) return safeSkinConfig.colors?.text || '#000000';

    // 简单的亮度计算，用于决定文本颜色
    const r = Number.parseInt(bgColor.slice(1, 3), 16);
    const g = Number.parseInt(bgColor.slice(3, 5), 16);
    const b = Number.parseInt(bgColor.slice(5, 7), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    return brightness > 128 ? '#000000' : '#FFFFFF';
  };

  // 渲染卡片
  const renderCard = (emotion: Emotion, index: number, key?: string) => {
    const { text, emoji } = getEmotionContent(emotion);
    const backgroundColor = getEmotionColor(emotion, index);
    const textColor = emotion.color?.startsWith('#')
      ? getTextColor(emotion.color)
      : safeSkinConfig.colors?.text || '#000000';

    return (
      <motion.div
        key={key || emotion.id}
        className="card"
        style={{
          width: `${cardSize}px`,
          height: `${cardSize}px`,
          backgroundColor,
          borderRadius: `${safeSkinConfig.effects?.borderRadius || 8}px`,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          boxShadow: safeSkinConfig.effects?.shadows ? '0 4px 8px rgba(0, 0, 0, 0.2)' : 'none',
          color: textColor,
          padding: '8px',
          position: 'relative',
          overflow: 'hidden',
        }}
        whileHover={{
          scale: 1.05,
          boxShadow: '0 8px 16px rgba(0, 0, 0, 0.3)',
        }}
        onClick={() => onSelect(emotion)}
        onMouseEnter={() => handleCardHover(index)}
        onMouseLeave={handleCardLeave}
      >
        {/* 背景装饰 */}
        {safeSkinConfig.effects?.animations && (
          <div
            className="card-decoration"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: 0.1,
              background: `radial-gradient(circle at ${Math.random() * 100}% ${Math.random() * 100}%, rgba(255,255,255,0.8), transparent)`,
              pointerEvents: 'none',
            }}
          />
        )}

        {/* 内容 */}
        <div className="card-content" style={{ zIndex: 1 }}>
          {emoji && (
            <div
              className="emoji"
              style={{
                fontSize: `${safeSkinConfig.fonts?.size?.large || 18}px`,
                marginBottom: text ? '8px' : 0,
              }}
            >
              {emoji}
            </div>
          )}

          {text && (
            <div
              className="text"
              style={{
                fontSize: `${safeSkinConfig.fonts?.size?.medium || 14}px`,
                fontWeight: safeSkinConfig.fonts?.weight?.normal || 400,
                fontFamily: safeSkinConfig.fonts?.family || 'Arial, sans-serif',
                textAlign: 'center',
              }}
            >
              {text}
            </div>
          )}
        </div>
      </motion.div>
    );
  };

  // 渲染列表卡片
  const renderListCard = (emotion: Emotion, index: number) => {
    const { text, emoji } = getEmotionContent(emotion);
    const backgroundColor = getEmotionColor(emotion, index);
    const textColor = emotion.color?.startsWith('#')
      ? getTextColor(emotion.color)
      : safeSkinConfig.colors?.text || '#000000';

    return (
      <motion.div
        key={emotion.id}
        className="list-card"
        style={{
          width: '100%',
          height: `${listCardHeight}px`,
          backgroundColor,
          borderRadius: `${safeSkinConfig.effects?.borderRadius || 8}px`,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          boxShadow: safeSkinConfig.effects?.shadows ? '0 2px 4px rgba(0, 0, 0, 0.2)' : 'none',
          color: textColor,
          padding: '0 16px',
          position: 'relative',
          overflow: 'hidden',
        }}
        whileHover={{
          scale: 1.02,
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
        }}
        onClick={() => onSelect(emotion)}
        onMouseEnter={() => handleCardHover(index)}
        onMouseLeave={handleCardLeave}
      >
        {/* 背景装饰 */}
        {safeSkinConfig.effects?.animations && (
          <div
            className="card-decoration"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: 0.1,
              background: 'linear-gradient(to right, rgba(255,255,255,0.8), transparent)',
              pointerEvents: 'none',
            }}
          />
        )}

        {/* 内容 */}
        <div
          className="list-card-content"
          style={{
            display: 'flex',
            alignItems: 'center',
            zIndex: 1,
          }}
        >
          {emoji && (
            <div
              className="emoji"
              style={{
                fontSize: `${safeSkinConfig.fonts?.size?.large || 18}px`,
                marginRight: text ? '12px' : 0,
              }}
            >
              {emoji}
            </div>
          )}

          {text && (
            <div
              className="text"
              style={{
                fontSize: `${safeSkinConfig.fonts?.size?.medium || 14}px`,
                fontWeight: safeSkinConfig.fonts?.weight?.normal || 400,
                fontFamily: safeSkinConfig.fonts?.family || 'Arial, sans-serif',
              }}
            >
              {text}
            </div>
          )}
        </div>
      </motion.div>
    );
  };

  // 渲染返回按钮
  const renderBackButton = () => {
    if (!onBack || tierLevel === 1) return null;

    return (
      <button
        onClick={onBack}
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          color: safeSkinConfig.colors?.text || '#000000',
          padding: '5px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          boxShadow: safeSkinConfig.effects?.shadows ? '0 2px 4px rgba(0, 0, 0, 0.2)' : 'none',
        }}
      >
        ←
      </button>
    );
  };

  // 渲染选中路径
  const renderSelectedPath = () => {
    if (!selectedPath) return null;

    return (
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 100,
          fontSize: `${safeSkinConfig.fonts?.size?.medium || 14}px`,
          fontFamily: safeSkinConfig.fonts?.family || 'Arial, sans-serif',
          color: safeSkinConfig.colors?.text || '#000000',
          padding: '5px',
          borderRadius: '5px',
          background: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(5px)',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {selectedPath}
      </div>
    );
  };

  return (
    <div
      className="card-view"
      ref={containerRef}
      style={{
        width: '100%',
        maxWidth: '800px',
        margin: '0 auto',
        padding: '16px',
        position: 'relative',
      }}
    >
      {renderBackButton()}
      {renderSelectedPath()}

      {/* 标题 */}
      <h3
        className="tier-title"
        style={{
          textAlign: 'center',
          marginBottom: '16px',
          fontSize: `${safeSkinConfig.fonts?.size?.large || 18}px`,
          fontWeight: safeSkinConfig.fonts?.weight?.bold || 700,
          fontFamily: safeSkinConfig.fonts?.family || 'Arial, sans-serif',
          color: safeSkinConfig.colors?.text || '#000000',
        }}
      >
        {t(`tier_level.${tierLevel}`, { fallback: `第${tierLevel}级情绪` })}
      </h3>

      {/* 卡片布局 */}
      {layout === 'list' && renderListLayout()}
      {layout === 'masonry' && renderMasonryLayout()}
      {layout === 'grid' && renderGridLayout()}
      {(!layout || (layout !== 'list' && layout !== 'masonry' && layout !== 'grid')) &&
        renderGridLayout()}
    </div>
  );
};
