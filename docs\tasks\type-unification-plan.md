# 类型统一架构实施计划

## 🎯 目标

建立基于 schema + zod + tRPC 的统一类型系统，解决当前类型定义分散、重复和不一致的问题。

## 📊 当前问题分析

### ❌ 主要问题

1. **类型定义分散**
   - `src/types/` 目录下有 12 个独立的类型文件
   - 重复定义相似接口（如多个 `User`, `MoodEntry` 定义）
   - 缺乏统一的类型生成机制

2. **与数据库 Schema 不同步**
   - 字段名称不匹配（如 `unicode_char` vs `unicode`）
   - 缺少数据库中的新字段（如 `sync_status`, `server_updated_at`）
   - 类型定义滞后于数据库结构变化

3. **tRPC 类型安全缺失**
   - 使用 `(trpc as any)` 绕过类型检查
   - 缺乏 API 输入输出的类型验证
   - 客户端和服务端类型不一致

## 🏗️ 新架构设计

### 核心原则

1. **Schema First**: 以数据库 schema 为单一数据源
2. **Type Safety**: 使用 zod 确保运行时类型安全
3. **DRY**: 避免重复定义
4. **Consistency**: 客户端和服务端类型一致

### 架构层次

```
public/seeds/schema/full.sql (数据库 Schema)
           ↓
src/types/schema/base.ts (基础 Zod Schema)
           ↓
src/types/schema/api.ts (API Schema)
           ↓
src/types/unified.ts (统一导出)
           ↓
应用代码 (hooks, services, components)
```

## 📁 新文件结构

```
src/types/
├── schema/
│   ├── base.ts          # 基础 Schema 定义
│   ├── api.ts           # API Schema 定义
│   └── generator.ts     # 类型生成器工具
├── unified.ts           # 统一类型导出
└── legacy/              # 旧类型文件（迁移期间保留）
    ├── mood.ts
    ├── emotionDataTypes.ts
    └── ...
```

## 🔧 实施步骤

### Phase 1: 基础架构搭建 ✅

- [x] 创建 `src/types/schema/base.ts` - 基础 Schema 定义
- [x] 创建 `src/types/schema/api.ts` - API Schema 定义
- [x] 创建 `src/types/schema/generator.ts` - 类型生成器
- [x] 创建 `src/types/unified.ts` - 统一导出

### Phase 2: 服务端对齐

1. **更新 tRPC 路由**
   ```typescript
   // 替换内联 Schema
   .input(z.object({ email: z.string().email() }))
   
   // 使用统一 Schema
   .input(LoginInputSchema)
   ```

2. **更新服务类**
   ```typescript
   // 使用统一类型
   import { User, MoodEntry, ApiResponse } from '../types/unified';
   ```

### Phase 3: 客户端迁移

1. **更新 hooks**
   ```typescript
   // 旧方式
   import { MoodEntry } from '../types/mood';
   
   // 新方式
   import { MoodEntry, MoodEntryWithRelations } from '../types/unified';
   ```

2. **修复 tRPC 客户端**
   ```typescript
   // 移除类型断言
   const result = await (trpc as any).purchaseVip.mutate({...});
   
   // 使用正确类型
   const result = await trpc.purchaseVip.mutate({...});
   ```

### Phase 4: 组件更新

1. **更新组件 props**
2. **更新表单验证**
3. **更新数据处理逻辑**

### Phase 5: 清理和优化

1. **移除旧类型文件**
2. **更新导入语句**
3. **添加类型测试**

## 🎯 具体实施任务

### 高优先级

1. **修复 tRPC 类型推断**
   - 更新 `src/lib/trpc.ts` 的类型导入
   - 移除所有 `(trpc as any)` 断言
   - 确保 API 调用的类型安全

2. **统一 MoodEntry 类型**
   - 替换 `src/types/mood.ts` 中的定义
   - 更新所有使用 MoodEntry 的文件
   - 确保字段名称与数据库一致

3. **统一用户相关类型**
   - 合并 User, VipStatus 等类型
   - 更新认证相关的 hooks 和服务

### 中优先级

1. **表情和皮肤类型统一**
   - 合并 EmojiSet, EmojiItem 类型
   - 合并 Skin, SkinConfig 类型
   - 更新相关组件

2. **API 响应类型标准化**
   - 统一所有 API 响应格式
   - 添加错误处理类型
   - 更新错误边界组件

### 低优先级

1. **添加类型生成工具**
   - 从数据库 schema 自动生成类型
   - 添加类型验证测试
   - 设置 CI/CD 类型检查

## 📋 迁移检查清单

### 服务端

- [ ] 更新 `server/lib/router.ts` 使用统一 Schema
- [ ] 更新所有服务类的类型导入
- [ ] 确保 API 响应格式一致
- [ ] 添加运行时验证

### 客户端

- [ ] 更新 `src/lib/trpc.ts` 类型导入
- [ ] 修复所有 hooks 中的类型使用
- [ ] 更新组件 props 类型
- [ ] 移除 `(trpc as any)` 断言

### 测试

- [ ] 更新所有测试文件的类型导入
- [ ] 添加类型验证测试
- [ ] 确保测试数据符合新 Schema

## 🔍 验证标准

1. **编译检查**: 所有 TypeScript 编译无错误
2. **类型安全**: 无 `any` 类型使用（除必要情况）
3. **一致性**: 客户端和服务端类型完全一致
4. **完整性**: 所有数据库字段都有对应类型
5. **可维护性**: 类型定义集中管理，易于更新

## 🎉 预期收益

1. **类型安全**: 编译时发现更多错误
2. **开发效率**: 更好的 IDE 支持和自动补全
3. **维护性**: 统一的类型定义，易于维护
4. **一致性**: 客户端和服务端类型完全同步
5. **可扩展性**: 基于 Schema 的类型生成，易于扩展

## 🚀 开始实施

建议按照以下顺序开始实施：

1. 首先修复 tRPC 类型推断问题
2. 然后逐步迁移核心类型（User, MoodEntry）
3. 最后清理旧类型文件和更新导入

每个阶段完成后进行全面测试，确保功能正常运行。
