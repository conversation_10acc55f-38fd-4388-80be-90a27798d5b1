/**
 * 数据同步 tRPC 路由
 * 支持全量同步、增量同步和冲突解决
 */

import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../router';
import { TRPCError } from '@trpc/server';
import { SyncService } from '../services/SyncService';

// ==================== 输入验证Schema ====================

const FullSyncInputSchema = z.object({
  userId: z.string(),
  lastSyncTimestamp: z.string().optional(),
  moodEntriesToUpload: z.array(z.any()).default([]),
  emotionSelectionsToUpload: z.array(z.any()).default([]),
  userConfigsToUpload: z.array(z.any()).default([]),
  tagsToUpload: z.array(z.any()).default([]),
  quizSessionsToUpload: z.array(z.any()).default([]),
  quizAnswersToUpload: z.array(z.any()).default([]),
  quizResultsToUpload: z.array(z.any()).default([]),
});

const IncrementalSyncInputSchema = z.object({
  userId: z.string(),
  lastSyncTimestamp: z.string(),
});

const ResolveConflictsInputSchema = z.object({
  userId: z.string(),
  conflicts: z.array(z.object({
    type: z.enum([
      'mood_entry', 'emotion_selection', 'user_config', 'tag',
      'quiz_session', 'quiz_answer', 'quiz_result',
      'payment_transaction', 'subscription_history',
      'skin_unlock', 'emoji_set_unlock', 'presentation_config'
    ]),
    localData: z.any(),
    serverData: z.any(),
    conflictReason: z.string(),
  })),
  resolutionStrategy: z.enum(['client_wins', 'server_wins', 'merge']).default('server_wins'),
});

const GetSyncStatisticsInputSchema = z.object({
  userId: z.string(),
});

// ==================== 同步路由定义 ====================

export const syncRouter = router({
  // 执行完整数据同步
  performFullSync: protectedProcedure
    .input(FullSyncInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const syncService = SyncService.getInstance();
        const result = await syncService.performFullSync(input);

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to perform full sync',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to perform full sync',
          cause: error,
        });
      }
    }),

  // 执行增量同步
  performIncrementalSync: protectedProcedure
    .input(IncrementalSyncInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const syncService = SyncService.getInstance();
        const result = await syncService.performIncrementalSync(
          input.userId,
          input.lastSyncTimestamp
        );

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to perform incremental sync',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to perform incremental sync',
          cause: error,
        });
      }
    }),

  // 解决数据冲突
  resolveConflicts: protectedProcedure
    .input(ResolveConflictsInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const syncService = SyncService.getInstance();
        const result = await syncService.resolveConflicts(
          input.userId,
          input.conflicts,
          input.resolutionStrategy
        );

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to resolve conflicts',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to resolve conflicts',
          cause: error,
        });
      }
    }),

  // 获取同步统计信息
  getSyncStatistics: protectedProcedure
    .input(GetSyncStatisticsInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const syncService = SyncService.getInstance();
        const result = await syncService.getSyncStatistics(input.userId);

        if (!result.success) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: result.error || 'Failed to get sync statistics',
          });
        }

        return result;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get sync statistics',
          cause: error,
        });
      }
    }),

  // 检查同步状态
  checkSyncStatus: protectedProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const syncService = SyncService.getInstance();
        const statistics = await syncService.getSyncStatistics(input.userId);

        if (!statistics.success) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: statistics.error || 'Failed to check sync status',
          });
        }

        // 计算同步状态
        const now = new Date();
        const lastSync = statistics.statistics?.lastSyncTimestamp 
          ? new Date(statistics.statistics.lastSyncTimestamp)
          : null;
        
        const timeSinceLastSync = lastSync 
          ? now.getTime() - lastSync.getTime()
          : null;

        const syncStatus = {
          isUpToDate: timeSinceLastSync ? timeSinceLastSync < 24 * 60 * 60 * 1000 : false, // 24小时内
          lastSyncTimestamp: statistics.statistics?.lastSyncTimestamp,
          timeSinceLastSync,
          needsSync: !lastSync || timeSinceLastSync > 60 * 60 * 1000, // 1小时以上
          statistics: statistics.statistics
        };

        return {
          success: true,
          data: syncStatus
        };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to check sync status',
          cause: error,
        });
      }
    }),

  // 强制同步（清除本地缓存并重新同步）
  forceSync: protectedProcedure
    .input(z.object({ 
      userId: z.string(),
      clearLocalData: z.boolean().default(false)
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        const syncService = SyncService.getInstance();
        
        // 执行完整同步，不传递 lastSyncTimestamp 以获取所有数据
        const result = await syncService.performFullSync({
          userId: input.userId,
          // 不传递 lastSyncTimestamp，强制获取所有数据
          moodEntriesToUpload: [],
          emotionSelectionsToUpload: [],
          userConfigsToUpload: [],
          tagsToUpload: [],
          quizSessionsToUpload: [],
          quizAnswersToUpload: [],
          quizResultsToUpload: []
        });

        if (!result.success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: result.error || 'Failed to force sync',
          });
        }

        return {
          ...result,
          forcedSync: true,
          clearedLocalData: input.clearLocalData
        };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to force sync',
          cause: error,
        });
      }
    }),
});
