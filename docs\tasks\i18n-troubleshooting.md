# 多语言问题排查指南

## 常见问题

在项目中实现多语言功能时，可能会遇到以下常见问题：

1. **翻译未显示**：组件显示翻译键而不是翻译文本
2. **语言切换无效**：切换语言后，某些组件未更新显示新语言
3. **部分翻译缺失**：某些文本未被翻译，显示为原始键名
4. **翻译不一致**：同一文本在不同位置显示不同的翻译
5. **数据库翻译与JSON翻译不一致**：导致界面显示混乱

## 问题排查步骤

### 1. 检查翻译键是否存在

首先，确认翻译键是否存在于以下位置：

- **数据库**：检查`ui_labels`和`ui_label_translations`表
  ```sql
  SELECT * FROM ui_labels WHERE key = 'your.key';
  SELECT * FROM ui_label_translations WHERE label_key = 'your.key';
  ```

- **JSON文件**：检查`src/locales/en.json`和`src/locales/zh.json`文件

### 2. 检查组件中的翻译调用

检查组件中的翻译调用是否正确：

```jsx
// 错误示例
<span>{t('app.home', { fallback: t('app.home') })}</span>

// 正确示例
<span>{isLanguageReady ? t('app.home') : 'Home'}</span>
```

### 3. 检查语言准备状态

确保组件在使用翻译前检查`isLanguageReady`状态：

```jsx
const { t, language, isLanguageReady } = useLanguage();

// 在翻译前检查语言准备状态
return (
  <div>
    {isLanguageReady ? t('app.title') : 'Mindful Mood'}
  </div>
);
```

### 4. 检查语言变化监听

确保组件正确监听语言变化：

```jsx
useEffect(() => {
  // 语言变化时的处理逻辑
  console.log(`Language changed to: ${language}, isReady: ${isLanguageReady}`);
}, [language, isLanguageReady]);
```

### 5. 检查数据获取中的语言参数

确保在数据获取时正确传递语言参数：

```jsx
// 在数据获取函数中传递语言参数
const data = await dbGetAnalyticsData(dbConnection.current, userId, period, language);
```

## 解决方案

### 1. 修复底部导航栏的翻译调用

底部导航栏(`MobileNavbar.tsx`)中的翻译调用存在问题，需要修改为：

```jsx
// 修改前
<span>{t('app.home', { fallback: t('app.home') })}</span>

// 修改后
<span>{isLanguageReady ? t('app.home') : 'Home'}</span>
```

完整的修复示例：

```jsx
import { NavLink } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import { Home, Clock, BarChart3, Settings } from 'lucide-react';
import { useEffect } from 'react';

const MobileNavbar = () => {
  const { t, language, isLanguageReady } = useLanguage();

  // 监听语言变化
  useEffect(() => {
    console.log(`[MobileNavbar] Language changed to: ${language}, isReady: ${isLanguageReady}`);
  }, [language, isLanguageReady]);
  
  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-lg border-t border-border">
      <div className="max-w-[550px] mx-auto flex justify-around items-center h-16 px-2">
        <NavLink 
          to="/" 
          className={({ isActive }) => `nav-link ${isActive ? 'active' : ''}`} 
          end
        >
          <Home size={20} />
          <span>{isLanguageReady ? t('app.home') : 'Home'}</span>
        </NavLink>
        
        <NavLink 
          to="/history" 
          className={({ isActive }) => `nav-link ${isActive ? 'active' : ''}`}
        >
          <Clock size={20} />
          <span>{isLanguageReady ? t('app.history') : 'History'}</span>
        </NavLink>
        
        <NavLink 
          to="/analytics" 
          className={({ isActive }) => `nav-link ${isActive ? 'active' : ''}`}
        >
          <BarChart3 size={20} />
          <span>{isLanguageReady ? t('app.analytics') : 'Analytics'}</span>
        </NavLink>
        
        <NavLink 
          to="/settings" 
          className={({ isActive }) => `nav-link ${isActive ? 'active' : ''}`}
        >
          <Settings size={20} />
          <span>{isLanguageReady ? t('app.settings') : 'Settings'}</span>
        </NavLink>
      </div>
    </nav>
  );
};

export default MobileNavbar;
```

### 2. 更新Settings页面的多语言实现

Settings页面也应该检查`isLanguageReady`状态：

```jsx
const Settings = () => {
  const { t, language, setLanguage, isLanguageReady } = useLanguage();
  
  // 监听语言变化
  useEffect(() => {
    console.log(`[Settings] Language changed to: ${language}, isReady: ${isLanguageReady}`);
  }, [language, isLanguageReady]);
  
  // ...其他代码
  
  return (
    <div className="py-4 space-y-6 animate-fade-in">
      <Card className="p-4">
        <h3 className="text-lg font-medium mb-4">
          {isLanguageReady ? t('settings.language') : 'Language'}
        </h3>
        {/* ...其他代码 */}
      </Card>
      {/* ...其他代码 */}
    </div>
  );
};
```

### 3. 确保翻译键的一致性

确保所有翻译键在数据库和JSON文件中都存在且一致：

1. 检查`ui_labels.sql`和`ui_label_translations.sql`文件
2. 检查`src/locales/en.json`和`src/locales/zh.json`文件
3. 使用脚本检查翻译键的一致性

### 4. 添加缺失的翻译

如果发现缺失的翻译，可以创建一个SQL文件添加这些翻译：

```sql
-- 添加缺失的UI标签
INSERT OR IGNORE INTO ui_labels (key, default_text) VALUES
    ('missing.key1', 'Default text 1'),
    ('missing.key2', 'Default text 2');

-- 添加英文翻译
INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES
    ('missing.key1', 'en', 'English text 1'),
    ('missing.key2', 'en', 'English text 2');

-- 添加中文翻译
INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES
    ('missing.key1', 'zh', '中文文本1'),
    ('missing.key2', 'zh', '中文文本2');
```

## 最佳实践

1. **始终检查语言准备状态**：
   ```jsx
   {isLanguageReady ? t('key') : 'Default text'}
   ```

2. **监听语言变化**：
   ```jsx
   useEffect(() => {
     // 处理语言变化
   }, [language, isLanguageReady]);
   ```

3. **在数据获取中考虑语言因素**：
   ```jsx
   if (!isLanguageReady) return;
   const data = await fetchData(language);
   ```

4. **保持翻译键的一致性**：
   - 使用点表示法组织键名：`section.subsection.key`
   - 例如：`app.title`, `settings.language`, `analytics.no_data`

5. **定期检查翻译完整性**：
   - 使用脚本检查所有翻译键是否都有对应的翻译
   - 确保数据库和JSON文件中的翻译保持同步

## 参考资料

- [完整的多语言实现文档](./internationalization.md)
- [React国际化最佳实践](https://react.i18next.com/guides/quick-start)
- [SQLite数据库文档](https://www.sqlite.org/docs.html)
