/**
 * Quiz包覆盖配置 Repository
 * 管理用户对特定Quiz包的个性化覆盖配置
 */

import { BaseRepository } from '../base/BaseRepository';
import type {
  QuizPackOverrides,
  CreateQuizPackOverridesInput,
  UpdateQuizPackOverridesInput
} from '../../types/schema';

export class QuizPackOverridesRepository extends BaseRepository<QuizPackOverrides, CreateQuizPackOverridesInput, UpdateQuizPackOverridesInput> {
  protected tableName = 'pack_presentation_overrides';

  constructor(tableName?: string, db?: any) {
    super(tableName || 'pack_presentation_overrides', db);
  }

  /**
   * 获取用户对特定Quiz包的覆盖配置 (别名方法)
   */
  async findByUserAndPack(userId: string, packId: string): Promise<QuizPackOverrides | null> {
    return this.getPackOverrides(userId, packId);
  }

  /**
   * 获取用户对特定Quiz包的覆盖配置
   */
  async getPackOverrides(userId: string, packId: string): Promise<QuizPackOverrides | null> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName}
        WHERE user_id = ? AND pack_id = ? AND is_active = 1
        ORDER BY override_priority DESC, updated_at DESC
        LIMIT 1
      `;

      const result = await db.query(query, [userId, packId]);
      const overrides = result.values?.[0];

      return overrides ? this.mapRowToEntity(overrides) : null;
    } catch (error) {
      console.error('Error getting pack overrides:', error);
      return null;
    }
  }

  /**
   * 获取用户的所有包覆盖配置
   */
  async getUserPackOverrides(userId: string): Promise<QuizPackOverrides[]> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName}
        WHERE user_id = ? AND is_active = 1
        ORDER BY override_priority DESC, updated_at DESC
      `;

      const result = await db.query(query, [userId]);
      return (result.values || []).map(row => this.mapRowToEntity(row));
    } catch (error) {
      console.error('Error getting user pack overrides:', error);
      return [];
    }
  }

  /**
   * 获取特定包的所有覆盖配置（用于管理）
   */
  async getPackOverridesByPack(packId: string): Promise<QuizPackOverrides[]> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName}
        WHERE pack_id = ? AND is_active = 1
        ORDER BY override_priority DESC, updated_at DESC
      `;

      const result = await db.query(query, [packId]);
      return (result.values || []).map(row => this.mapRowToEntity(row));
    } catch (error) {
      console.error('Error getting pack overrides by pack:', error);
      return [];
    }
  }

  /**
   * 更新用户对特定Quiz包的覆盖配置
   */
  async updatePackOverrides(
    userId: string,
    packId: string,
    updates: UpdateQuizPackOverridesInput
  ): Promise<QuizPackOverrides | null> {
    try {
      // 先获取现有覆盖配置
      const existingOverrides = await this.getPackOverrides(userId, packId);

      if (!existingOverrides) {
        // 如果不存在，创建新覆盖配置
        const newOverridesData: CreateQuizPackOverridesInput = {
          id: this.generateId(),
          user_id: userId,
          pack_id: packId,
          presentation_overrides: updates.presentation_overrides,
          override_reason: updates.override_reason || 'user_preference',
          override_priority: updates.override_priority || 1,
          is_active: updates.is_active ?? true
        };

        return await this.create(newOverridesData);
      }

      // 更新现有覆盖配置
      const updatedOverrides = await this.update(existingOverrides.id!, updates);
      return updatedOverrides;
    } catch (error) {
      console.error('Error updating pack overrides:', error);
      return null;
    }
  }

  /**
   * 创建包覆盖配置
   */
  async createPackOverrides(data: CreateQuizPackOverridesInput): Promise<QuizPackOverrides | null> {
    try {
      return await this.create(data);
    } catch (error) {
      console.error('Error creating pack overrides:', error);
      return null;
    }
  }

  /**
   * 删除包覆盖配置（软删除）
   */
  async deletePackOverrides(userId: string, packId: string): Promise<boolean> {
    try {
      const existingOverrides = await this.getPackOverrides(userId, packId);
      if (!existingOverrides) {
        return false;
      }

      const db = this.getDb();
      const query = `
        UPDATE ${this.tableName}
        SET is_active = 0, updated_at = ?
        WHERE user_id = ? AND pack_id = ?
      `;

      await db.query(query, [new Date().toISOString(), userId, packId]);
      return true;
    } catch (error) {
      console.error('Error deleting pack overrides:', error);
      return false;
    }
  }

  /**
   * 获取用户对包的覆盖配置统计
   */
  async getOverrideStats(userId: string): Promise<{
    total_overrides: number;
    by_reason: Record<string, number>;
    by_priority: Record<number, number>;
  }> {
    try {
      const db = this.getDb();

      // 总数统计
      const totalQuery = `
        SELECT COUNT(*) as total FROM ${this.tableName}
        WHERE user_id = ? AND is_active = 1
      `;
      const totalResult = await db.query(totalQuery, [userId]);
      const total = totalResult.values?.[0]?.total || 0;

      // 按原因统计
      const reasonQuery = `
        SELECT override_reason, COUNT(*) as count FROM ${this.tableName}
        WHERE user_id = ? AND is_active = 1
        GROUP BY override_reason
      `;
      const reasonResult = await db.query(reasonQuery, [userId]);
      const byReason: Record<string, number> = {};
      (reasonResult.values || []).forEach((row: any) => {
        byReason[row.override_reason] = row.count;
      });

      // 按优先级统计
      const priorityQuery = `
        SELECT override_priority, COUNT(*) as count FROM ${this.tableName}
        WHERE user_id = ? AND is_active = 1
        GROUP BY override_priority
      `;
      const priorityResult = await db.query(priorityQuery, [userId]);
      const byPriority: Record<number, number> = {};
      (priorityResult.values || []).forEach((row: any) => {
        byPriority[row.override_priority] = row.count;
      });

      return {
        total_overrides: total,
        by_reason: byReason,
        by_priority: byPriority
      };
    } catch (error) {
      console.error('Error getting override stats:', error);
      return {
        total_overrides: 0,
        by_reason: {},
        by_priority: {}
      };
    }
  }

  /**
   * 生成ID
   */
  protected generateId(): string {
    return `pack_override_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 提取创建数据中的ID
   */
  protected extractIdFromCreateData(data: CreateQuizPackOverridesInput): string {
    return data.id || this.generateId();
  }

  /**
   * 构建更新查询
   */
  protected buildUpdateQuery(id: string, data: UpdateQuizPackOverridesInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.presentation_overrides !== undefined) {
      fields.push('presentation_overrides = ?');
      values.push(data.presentation_overrides);
    }

    if (data.override_reason !== undefined) {
      fields.push('override_reason = ?');
      values.push(data.override_reason);
    }

    if (data.override_priority !== undefined) {
      fields.push('override_priority = ?');
      values.push(data.override_priority);
    }

    if (data.is_active !== undefined) {
      fields.push('is_active = ?');
      values.push(data.is_active ? 1 : 0);
    }

    // Always update updated_at
    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  /**
   * 映射数据库行到实体
   */
  protected mapRowToEntity(row: any): QuizPackOverrides {
    return {
      id: row.id,
      user_id: row.user_id,
      pack_id: row.pack_id,
      presentation_overrides: row.presentation_overrides,
      override_reason: row.override_reason,
      override_priority: row.override_priority,
      is_active: Boolean(row.is_active),
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  /**
   * 映射实体到数据库行
   */
  protected mapEntityToRow(entity: QuizPackOverrides): any {
    return {
      id: entity.id,
      user_id: entity.user_id,
      pack_id: entity.pack_id,
      presentation_overrides: entity.presentation_overrides,
      override_reason: entity.override_reason,
      override_priority: entity.override_priority,
      is_active: entity.is_active ? 1 : 0,
      created_at: entity.created_at,
      updated_at: entity.updated_at
    };
  }

  /**
   * 验证创建数据
   */
  protected async validateCreate(data: CreateQuizPackOverridesInput): Promise<void> {
    if (!data.user_id) {
      throw new Error('User ID is required');
    }
    if (!data.pack_id) {
      throw new Error('Pack ID is required');
    }
  }

  /**
   * 验证更新数据
   */
  protected async validateUpdate(_data: UpdateQuizPackOverridesInput): Promise<void> {
    // 基本验证即可
  }

  /**
   * 构建插入查询
   */
  protected buildInsertQuery(data: CreateQuizPackOverridesInput): { query: string; values: any[] } {
    const fields = [
      'id', 'user_id', 'pack_id', 'presentation_overrides',
      'override_reason', 'override_priority', 'is_active',
      'created_at', 'updated_at'
    ];

    const placeholders = fields.map(() => '?').join(', ');
    const query = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;

    const now = new Date().toISOString();
    const values = [
      data.id || this.generateId(),
      data.user_id,
      data.pack_id,
      data.presentation_overrides,
      data.override_reason || 'user_preference',
      data.override_priority || 1,
      data.is_active ? 1 : 0,
      now,
      now
    ];

    return { query, values };
  }
}
