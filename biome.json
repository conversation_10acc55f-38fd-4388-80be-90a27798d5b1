{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["dist/**", "node_modules/**", "build/**", ".next/**", "coverage/**", "*.min.js", "*.bundle.js", "public/assets/**", "server/node_modules/**", "server/dist/**"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto", "ignore": ["**/*.md", "public/**", "dist/**"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "warn"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto"}}, "overrides": [{"include": ["*.test.ts", "*.test.tsx", "*.spec.ts", "*.spec.tsx"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off"}}}}, {"include": ["src/types/**/*.ts"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off"}}}}]}