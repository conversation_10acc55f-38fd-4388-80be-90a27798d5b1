import { TRPCError, initTRPC } from '@trpc/server';
import { z } from 'zod';
import {
  type InStatement,
  batchTursoStatements,
  executeSqlScript,
  executeTursoQuery,
  fetchAllFromTursoTable,
} from './localTursoService.js';

// Create a tRPC instance
const t = initTRPC.create();

// Export the router and procedure helpers
export const router = t.router;
export const publicProcedure = t.procedure;

/**
 * Helper function to handle errors in a consistent way
 * @param error The error that occurred
 * @param operation The operation that was being performed
 * @returns A standardized error response
 */
const handleError = (error: unknown, operation: string) => {
  console.error(`Error during ${operation}:`, error);

  // Convert to TRPCError for better client handling
  if (error instanceof TRPCError) {
    throw error;
  }

  // Create a new TRPCError with the appropriate message
  throw new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: error instanceof Error ? error.message : `Unknown error during ${operation}`,
    cause: error,
  });
};

// Define the input schema for SQL queries
const sqlQueryInputSchema = z.union([
  z.string(),
  z.object({
    sql: z.string(),
    args: z.array(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
  }),
]);

// Define the input schema for batch statements
const batchStatementsInputSchema = z.object({
  statements: z.array(
    z.object({
      sql: z.string(),
      args: z.array(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
    })
  ),
  mode: z.enum(['deferred', 'write', 'read']).optional(),
});

// Define the input schema for SQL scripts
const sqlScriptInputSchema = z.object({
  script: z.string(),
});

// Define the input schema for table queries
const tableQueryInputSchema = z.object({
  tableName: z.string().regex(/^[a-zA-Z0-9_]+$/),
});

// Create the tRPC router with database procedures
export const appRouter = router({
  // Execute a single SQL query
  query: publicProcedure.input(sqlQueryInputSchema).query(async ({ input }) => {
    try {
      const result = await executeTursoQuery(input);
      return result;
    } catch (error) {
      return handleError(error, 'query execution');
    }
  }),

  // Execute a batch of SQL statements
  batch: publicProcedure.input(batchStatementsInputSchema).mutation(async ({ input }) => {
    try {
      const result = await batchTursoStatements(input.statements as InStatement[], input.mode);
      return result;
    } catch (error) {
      return handleError(error, 'batch execution');
    }
  }),

  // Execute a SQL script
  executeScript: publicProcedure.input(sqlScriptInputSchema).mutation(async ({ input }) => {
    try {
      await executeSqlScript(input.script);
      return { success: true };
    } catch (error) {
      return handleError(error, 'script execution');
    }
  }),

  // Fetch all rows from a table
  fetchTable: publicProcedure.input(tableQueryInputSchema).query(async ({ input }) => {
    try {
      const rows = await fetchAllFromTursoTable(input.tableName);
      return rows;
    } catch (error) {
      return handleError(error, 'table fetch');
    }
  }),

  // Fetch rows from a table with limit
  fetchTableWithLimit: publicProcedure
    .input(
      z.object({
        tableName: z.string().regex(/^[a-zA-Z0-9_]+$/),
        limit: z.number().int().positive().optional(),
      })
    )
    .query(async ({ input }) => {
      try {
        const rows = await fetchAllFromTursoTable(input.tableName, input.limit);
        return rows;
      } catch (error) {
        return handleError(error, 'table fetch with limit');
      }
    }),

  // Add a synchronization endpoint for testing
  synchronizeData: publicProcedure
    .input(
      z.object({
        moodEntriesToUpload: z.array(z.any()).optional(),
        lastSyncTimestamp: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        console.log('[LocalTRPC] Received sync request with:', {
          uploadCount: input.moodEntriesToUpload?.length || 0,
          lastSync: input.lastSyncTimestamp,
        });

        // Simulate processing time
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Return a mock response
        return {
          success: true,
          newMoodEntriesFromServer: [], // No new entries from server in this mock
          error: null,
        };
      } catch (error) {
        return handleError(error, 'data synchronization');
      }
    }),
});

// Export type definition of API
export type AppRouter = typeof appRouter;
