@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 260 70% 98%; /* Very light lavender background */
    --foreground: 222.2 84% 4.9%; /* Very dark blue/black text */

    /* Updated: Card color for light mode made lighter blue with dark text */
    --card: 220 45% 88% / 0.65; /* Lighter, softer blue card background with 65% opacity */
    --card-foreground: 222.2 70% 15%; /* Darker blue text on the light card for better contrast */

    --popover: 225 65% 42%;
    --popover-foreground: 0 0% 100%;

    --primary: 335 95% 62%; /* Bright pink for buttons, notifications */
    --primary-foreground: 0 0% 100%; /* White text on primary elements */

    --secondary: 225 45% 28%; /* Darker blue for bottom navigation bar */
    --secondary-foreground: 0 0% 90%; /* Light grey/white for nav icons and text */

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 52 100% 58%; /* Bright yellow for progress bar fill and highlights */
    --accent-foreground: 50 60% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 335 95% 62%;

    --radius: 0.8rem;
  }

  .dark {
    --background: 230 20% 10%;
    --foreground: 240 30% 92%;

    /* Card in dark mode remains a darker blue with light text */
    --card: 225 35% 25% / 0.6;
    --card-foreground: 240 30% 92%;

    --popover: 225 30% 18%;
    --popover-foreground: 240 30% 92%;

    --primary: 335 90% 65%;
    --primary-foreground: 0 0% 100%;

    --secondary: 225 25% 12%;
    --secondary-foreground: 240 25% 85%;

    --muted: 230 15% 22%;
    --muted-foreground: 230 15% 70%;

    --accent: 52 100% 60%;
    --accent-foreground: 50 60% 15%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 95%;

    --border: 230 15% 25%;
    --input: 230 15% 25%;
    --ring: 335 90% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  .mobile-container {
    max-width: 550px;
    margin: 0 auto;
    min-height: 100svh;
    position: relative;
  }

  .emotion-wheel {
    @apply transition-all duration-300 ease-in-out;
  }

  .mood-card {
    /* Uses --card and --card-foreground which are now mode-dependent */
    @apply bg-card text-card-foreground rounded-xl shadow-md p-4 transition-all duration-300 hover:shadow-lg;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }

  .nav-link {
    @apply flex flex-col items-center justify-center gap-1 text-xs font-medium;
    /* Ensure nav link text is appropriate for the nav bar background */
    /* color: hsl(var(--secondary-foreground)); */ /* This is implicitly handled by Tailwind if nav uses bg-secondary and text-secondary-foreground */
  }

  .nav-link.active {
    @apply text-primary;
  }
}
