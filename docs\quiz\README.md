# Quiz系统设计文档集合

## 📋 文档概览

本目录包含了完整的Quiz系统设计文档，涵盖了从架构设计到具体实现的所有方面。

### 🏗️ 核心架构文档
- **[01-architecture-overview.md](./01-architecture-overview.md)** - 系统架构总览
- **[02-data-presentation-separation.md](./02-data-presentation-separation.md)** - 数据与展现分离架构
- **[03-personalization-engine.md](./03-personalization-engine.md)** - 个性化引擎设计

### 🗄️ 数据层设计
- **[04-database-design.md](./04-database-design.md)** - 数据库表结构设计
- **[05-data-models.md](./05-data-models.md)** - 数据模型定义
- **[06-emotion-dataset-integration.md](./06-emotion-dataset-integration.md)** - 情绪数据集集成

### 🎨 展现层设计
- **[07-view-factory-integration.md](./07-view-factory-integration.md)** - ViewFactory集成设计
- **[08-ui-configuration.md](./08-ui-configuration.md)** - UI配置系统
- **[09-personalization-layers.md](./09-personalization-layers.md)** - 6层个性化配置

### ⚙️ 配置系统 ✨ 新增
- **[user-personalization-guide.md](./user-personalization-guide.md)** - 用户个性化配置指南
- **[type-system-architecture.md](./type-system-architecture.md)** - 类型系统架构文档
- **[../implementation/config-system-implementation.md](../implementation/config-system-implementation.md)** - 配置系统实现总结
- **[../api/config-system-api-updated.md](../api/config-system-api-updated.md)** - 配置系统API文档
- **[../implementation/config-system-migration-guide.md](../implementation/config-system-migration-guide.md)** - 配置系统迁移指南

### 📡 API设计
- **[10-trpc-api-design.md](./10-trpc-api-design.md)** - tRPC API设计
- **[11-zod-schemas.md](./11-zod-schemas.md)** - Zod数据验证模式
- **[12-websocket-events.md](./12-websocket-events.md)** - WebSocket事件设计

### 🔧 服务层设计
- **[13-core-services.md](./13-core-services.md)** - 核心服务设计
- **[14-evaluation-engine.md](./14-evaluation-engine.md)** - 评估引擎设计
- **[15-session-management.md](./15-session-management.md)** - 会话管理设计

### 🎯 实现指南
- **[16-implementation-guide.md](./16-implementation-guide.md)** - 实现指南
- **[17-testing-strategy.md](./17-testing-strategy.md)** - 测试策略
- **[18-deployment-guide.md](./18-deployment-guide.md)** - 部署指南

## 🎯 设计原则

### 核心原则
1. **数据与展现完全分离** - 量表数据与UI展现完全解耦
2. **个性化优先** - 基于6层配置的深度个性化
3. **模块化设计** - 高内聚、低耦合的模块化架构
4. **可插拔组件** - 核心组件可独立替换和扩展
5. **配置驱动** - 基于配置的行为控制和UI生成

### 技术栈
- **后端**: Node.js + TypeScript + tRPC + Zod
- **数据库**: SQLite + TypeORM
- **前端**: React + TypeScript + Zustand
- **UI**: Tailwind CSS + Radix UI
- **测试**: Vitest + Testing Library

## 🚀 快速开始

### 1. 阅读顺序建议
对于新开发者，建议按以下顺序阅读文档：

1. **架构理解**: 01 → 02 → 03
2. **数据设计**: 04 → 05 → 06
3. **展现设计**: 07 → 08 → 09
4. **API设计**: 10 → 11 → 12
5. **服务设计**: 13 → 14 → 15
6. **实现部署**: 16 → 17 → 18

### 2. 关键概念
- **情绪数据集作为量表**: 将情绪数据集的层级结构映射为量表的问题结构
- **6层个性化配置**: 从数据集选择到可访问性的全方位个性化
- **会话展现快照**: 运行时生成的个性化配置合并结果
- **动态UI规则**: 基于上下文的智能UI调整

### 3. 核心流程
```
用户启动量表 → 加载个性化配置 → 生成会话快照 →
动态生成问题展现 → 收集用户答案 → 实时分析 →
生成个性化结果 → 推荐建议
```

## 📊 系统特性

### 功能特性
- ✅ **深度个性化**: 基于6层配置的全方位个性化体验
- ✅ **情绪智能**: 专业的情绪模式分析和智能推荐
- ✅ **动态适应**: 基于用户行为和上下文的动态UI调整
- ✅ **多视图支持**: 轮盘、卡片、气泡、星系等多种视图类型
- ✅ **实时反馈**: 答题过程中的实时洞察和分析
- ✅ **可访问性**: 全面的可访问性支持和增强
- ✅ **数据驱动**: 完全基于配置的UI生成和行为控制

### 配置系统特性 ✨ 新增
- ✅ **配置分离**: 全局应用设置与Quiz系统配置完全分离
- ✅ **混合架构**: 离线优先 + 在线同步的混合模式
- ✅ **类型安全**: 端到端TypeScript类型保护和Zod验证
- ✅ **智能合并**: 多层配置优先级和智能合并算法
- ✅ **实时同步**: 网络状态变化时自动同步配置
- ✅ **包覆盖**: 针对特定Quiz包的个性化配置
- ✅ **会话快照**: 最终合并配置的快照生成
- ✅ **Hook接口**: 类型安全的React Hooks接口

### 技术特性
- ✅ **类型安全**: 全栈TypeScript + Zod验证
- ✅ **高性能**: 智能缓存和异步处理
- ✅ **可扩展**: 模块化和可插拔的架构设计
- ✅ **易维护**: 清晰的职责分离和文档化
- ✅ **测试友好**: 完整的测试策略和工具链
- ✅ **部署简单**: 容器化和自动化部署

## 🔄 更新记录

### v2.1.0 (当前版本) ✨ 配置系统重构
- **配置系统重构**: 全局应用设置与Quiz系统配置完全分离
- **混合架构**: 离线优先 + 在线同步的配置管理
- **类型安全Hook**: useGlobalConfig + useQuizConfig React Hooks
- **智能配置合并**: 多层配置优先级和合并算法
- **包覆盖配置**: 针对特定Quiz包的个性化设置
- **会话配置快照**: 最终合并配置的快照生成
- **迁移指南**: 完整的从旧系统到新系统的迁移文档

### v2.0.0 (历史版本)
- 完全重构为数据与展现分离架构
- 集成6层个性化配置系统
- 采用tRPC + Zod技术栈
- 支持情绪数据集作为量表
- 增强的ViewFactory集成

### v1.0.0 (历史版本)
- 基础Quiz系统实现
- 简单的配置系统
- REST API设计
- 基础的情绪分析功能

## 🤝 贡献指南

### 文档贡献
1. 遵循现有的文档结构和格式
2. 使用清晰的标题和章节组织
3. 提供具体的代码示例
4. 包含必要的图表和流程图

### 代码贡献
1. 遵循TypeScript最佳实践
2. 确保类型安全和Zod验证
3. 编写完整的单元测试
4. 更新相关文档

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue讨论具体问题
- 提交PR贡献代码或文档
- 参与代码审查和讨论

---

**注意**: 本文档集合持续更新中，请关注最新版本的变更。
