import { useEffect, useState } from 'react';
import useTursoApi from '../hooks/useTursoApi';

/**
 * 使用 Cloudflare Functions API 访问 Turso 数据库的示例组件
 */
const TursoFunctionsExample = () => {
  const { executeQuery, fetchTable, isLoading, error } = useTursoApi();
  const [users, setUsers] = useState<any[]>([]);
  const [emotions, setEmotions] = useState<any[]>([]);
  const [loadingMessage, setLoadingMessage] = useState<string>('');

  // 加载用户数据
  const loadUsers = async () => {
    try {
      setLoadingMessage('正在加载用户数据...');
      const result = await executeQuery('SELECT * FROM users LIMIT 5');
      setUsers(result.rows);
    } catch (err) {
      console.error('加载用户数据失败:', err);
    }
  };

  // 加载情绪数据
  const loadEmotions = async () => {
    try {
      setLoadingMessage('正在加载情绪数据...');
      const data = await fetchTable('emotions');
      setEmotions(data);
    } catch (err) {
      console.error('加载情绪数据失败:', err);
    } finally {
      setLoadingMessage('');
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadUsers();
    loadEmotions();
  }, []);

  return (
    <div className="p-4 space-y-6">
      <h1 className="text-2xl font-bold">Turso Functions API 示例</h1>

      {isLoading && (
        <div className="p-4 bg-blue-50 text-blue-700 rounded-md">
          {loadingMessage || '加载中...'}
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-50 text-red-700 rounded-md">
          <p className="font-medium">错误:</p>
          <p>{error.message}</p>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">用户数据</h2>
        {users.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {Object.keys(users[0]).map((key) => (
                    <th
                      key={key}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {key}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user, index) => (
                  <tr key={index}>
                    {Object.values(user).map((value: any, i) => (
                      <td key={i} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {String(value)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500">无用户数据</p>
        )}
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">情绪数据</h2>
        {emotions.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {Object.keys(emotions[0]).map((key) => (
                    <th
                      key={key}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {key}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {emotions.map((emotion, index) => (
                  <tr key={index}>
                    {Object.values(emotion).map((value: any, i) => (
                      <td key={i} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {String(value)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500">无情绪数据</p>
        )}
      </div>

      <div className="flex space-x-4">
        <button
          onClick={loadUsers}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          disabled={isLoading}
        >
          重新加载用户
        </button>
        <button
          onClick={loadEmotions}
          className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          disabled={isLoading}
        >
          重新加载情绪
        </button>
      </div>
    </div>
  );
};

export default TursoFunctionsExample;
