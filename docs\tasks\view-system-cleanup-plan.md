# 视图系统清理计划

本文档列出了视图系统迁移完成后需要移除的旧组件和文件。这些组件和文件已经被新的视图系统替代，不再需要保留。

## 清理原则

1. **渐进式清理**：按照依赖关系，从最底层的组件开始清理，逐步向上
2. **保持向后兼容**：在清理过程中，确保不破坏现有功能
3. **完整测试**：每次清理后，运行测试确保系统正常工作
4. **文档更新**：更新相关文档，反映清理后的系统架构

## 需要移除的文件

### 第一阶段：轮盘组件

| 文件路径 | 替代文件 | 状态 |
|---------|---------|------|
| `src/components/mood/BaseWheel.tsx` | `src/views/BaseEmotionView.tsx` | 待移除 |
| `src/components/mood/D3Wheel.tsx` | `src/components/wheels/D3WheelDirectComponent.tsx` | 待移除 |
| `src/components/mood/SVGWheel.tsx` | `src/components/wheels/SVGWheelDirectComponent.tsx` | 待移除 |
| `src/components/mood/R3FWheel.tsx` | `src/components/wheels/R3FWheelDirectComponent.tsx` | 待移除 |
| `src/utils/wheelFactory.ts` | `src/utils/viewFactory.tsx` | 待移除 |

### 第二阶段：适配器组件

| 文件路径 | 替代文件 | 状态 |
|---------|---------|------|
| `src/components/mood/WheelAdapter.tsx` | `src/components/display/DisplayAdapter.tsx` | 待移除 |
| `src/components/mood/EmotionWheel.tsx` | `src/components/display/DisplayAdapter.tsx` | 待移除 |

### 第三阶段：旧的视图组件

| 文件路径 | 替代文件 | 状态 |
|---------|---------|------|
| `src/components/wheels/D3WheelComponent.tsx` | `src/components/wheels/D3WheelDirectComponent.tsx` | 待移除 |
| `src/components/wheels/SVGWheelComponent.tsx` | `src/components/wheels/SVGWheelDirectComponent.tsx` | 待移除 |
| `src/components/wheels/R3FWheelComponent.tsx` | `src/components/wheels/R3FWheelDirectComponent.tsx` | 待移除 |
| `src/components/views/GalaxyView.tsx` | `src/components/galaxy/GalaxyDirectComponent.tsx` | 待移除 |

### 第四阶段：类型定义

| 文件路径 | 替代文件 | 状态 |
|---------|---------|------|
| `src/types/mood.ts` (部分) | `src/types/emotionDataTypes.ts` | 待清理 |
| `src/types/wheelTypes.ts` | `src/types/previewTypes.ts` 和 `src/types/skinTypes.ts` | 待移除 |
| `src/types/emotionTypes.ts` (部分) | `src/types/emotionDataTypes.ts` | 待清理 |

详细的类型系统清理计划请参见 [类型系统清理计划](./type-system-cleanup-plan.md)。

## 清理步骤

### 步骤 1: 更新导入语句

在移除文件之前，需要更新所有导入这些文件的地方，改为导入新的替代文件。

1. 搜索所有导入旧组件的地方 ✅
2. 更新导入语句，使用新组件 ✅
   - 已更新 `WheelComponentTest.tsx` 文件
   - 已更新 `r3f-wheel-test.tsx` 文件
3. 运行测试，确保系统正常工作 ✅

详细的导入更新计划和进度请参见 [视图系统导入更新计划](./view-system-import-updates.md)。

### 步骤 2: 移除旧组件

按照上述顺序，逐步移除旧组件：

1. 移除轮盘组件
2. 移除适配器组件
3. 移除旧的视图组件
4. 清理类型定义

### 步骤 3: 更新文档

更新相关文档，反映清理后的系统架构：

1. 更新 `view-system-implementation-plan.md`
2. 更新 `view-system-migration-plan.md`
3. 添加新的架构图，说明新的视图系统

## 清理后的系统架构

清理完成后，视图系统的架构将更加清晰和一致：

```
src/
├── components/
│   ├── display/
│   │   └── DisplayAdapter.tsx       # 统一的显示适配器
│   ├── wheels/
│   │   ├── D3WheelDirectComponent.tsx    # D3轮盘直接组件
│   │   ├── SVGWheelDirectComponent.tsx   # SVG轮盘直接组件
│   │   └── R3FWheelDirectComponent.tsx   # R3F轮盘直接组件
│   ├── galaxy/
│   │   └── GalaxyDirectComponent.tsx     # 星系直接组件
│   ├── cards/
│   │   └── CardDirectComponent.tsx       # 卡片直接组件
│   └── bubbles/
│       └── BubbleDirectComponent.tsx     # 气泡直接组件
├── views/
│   ├── BaseEmotionView.tsx          # 基础情绪视图
│   ├── wheels/
│   │   ├── WheelView.tsx            # 轮盘视图接口
│   │   ├── D3WheelView.tsx          # D3轮盘视图
│   │   ├── SVGWheelView.tsx         # SVG轮盘视图
│   │   └── R3FWheelView.tsx         # R3F轮盘视图
│   ├── galaxy/
│   │   └── GalaxyView.tsx           # 星系视图
│   ├── cards/
│   │   └── CardView.tsx             # 卡片视图
│   └── bubbles/
│       └── BubbleView.tsx           # 气泡视图
├── types/
│   ├── emotionDataTypes.ts          # 情绪数据类型
│   ├── previewTypes.ts              # 预览类型
│   ├── skinTypes.ts                 # 皮肤类型
│   └── compatibilityTypes.ts        # 兼容性类型
└── utils/
    └── viewFactory.tsx              # 视图工厂
```

## 时间安排

| 阶段 | 预计时间 | 开始日期 | 结束日期 |
|------|---------|---------|---------|
| 更新导入语句 | 1天 | TBD | TBD |
| 移除轮盘组件 | 1天 | TBD | TBD |
| 移除适配器组件 | 1天 | TBD | TBD |
| 移除旧的视图组件 | 1天 | TBD | TBD |
| 清理类型定义 | 1天 | TBD | TBD |
| 更新文档 | 1天 | TBD | TBD |

## 风险和缓解措施

1. **功能回归**
   - 风险：清理过程中可能导致现有功能失效
   - 缓解：增加测试覆盖率，采用渐进式清理，保持向后兼容性

2. **依赖关系**
   - 风险：可能存在未知的依赖关系
   - 缓解：使用代码分析工具，识别所有依赖关系

3. **文档不一致**
   - 风险：文档可能与实际代码不一致
   - 缓解：同步更新文档，确保文档与代码一致

## 结论

通过执行这个清理计划，我们将移除旧的视图系统组件，使代码库更加清晰和一致。清理完成后，开发者将能够更容易地理解和使用视图系统，添加新功能也将变得更加简单。
