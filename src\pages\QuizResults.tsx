/**
 * Quiz结果展示页面
 *
 * 展示Quiz完成后的分析结果、情绪模式和个性化推荐
 */

import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuizResult } from '../hooks/useQuiz';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Progress } from '../components/ui/progress';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import {
  Download,
  Share2,
  RefreshCw,
  TrendingUp,
  Heart,
  Brain,
  Target,
  Calendar,
  Award
} from 'lucide-react';

interface QuizResultsProps {
  sessionId?: string;
}

interface EmotionAnalysis {
  dominant_emotions: string[];
  emotional_stability_index: number;
  emotional_complexity_index: number;
  pattern_analysis: Record<string, any>;
}

interface Recommendation {
  type: string;
  title: string;
  description: string;
  confidence: number;
  priority: number;
}

const QuizResults: React.FC<QuizResultsProps> = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();

  // 状态管理
  const [selectedTab, setSelectedTab] = useState<'overview' | 'analysis' | 'recommendations'>('overview');

  // 使用新的Quiz结果hook
  const {
    data: resultResponse,
    isLoading,
    error
  } = useQuizResult(sessionId || '');

  const resultData = resultResponse?.success ? resultResponse.data : null;

  // 模拟数据（当API数据不可用时）
  const mockResultData = {
    result_id: 'result_123',
    session_id: sessionId || '',
    user_id: 'user_123',
    pack_id: 'daily-mood-tracker',
    completion_percentage: 100,
    emotion_analysis: {
      dominant_emotions: ['joy', 'contentment', 'optimism'],
      emotional_stability_index: 0.75,
      emotional_complexity_index: 0.6,
      pattern_analysis: {
        emotional_categories: [
          { name: '积极情绪', value: 65, color: '#10B981' },
          { name: '中性情绪', value: 25, color: '#6B7280' },
          { name: '消极情绪', value: 10, color: '#EF4444' }
        ],
        intensity_distribution: [
          { level: '低强度', count: 2 },
          { level: '中强度', count: 5 },
          { level: '高强度', count: 3 }
        ],
        temporal_patterns: {
          morning_mood: 0.8,
          afternoon_mood: 0.7,
          evening_mood: 0.6
        }
      }
    },
    personalized_recommendations: [
      {
        type: 'mindfulness',
        title: '正念冥想练习',
        description: '基于您的情绪模式，建议每日进行10-15分钟的正念冥想练习，有助于提升情绪稳定性。',
        confidence: 0.85,
        priority: 1
      },
      {
        type: 'exercise',
        title: '适度运动',
        description: '规律的有氧运动可以帮助维持您当前良好的情绪状态，建议每周3-4次，每次30分钟。',
        confidence: 0.78,
        priority: 2
      },
      {
        type: 'social',
        title: '社交互动',
        description: '保持积极的社交关系对您的情绪健康很重要，建议定期与朋友和家人交流。',
        confidence: 0.72,
        priority: 3
      }
    ],
    visual_summary: {
      primary_visualization: {},
      secondary_visualizations: [],
      export_options: ['PDF', 'PNG', 'JSON']
    },
    created_at: new Date()
  };

  const displayData = resultData || mockResultData;

  // 处理导出
  const handleExport = (format: string) => {
    console.log(`Exporting results in ${format} format`);
    // 实现导出逻辑
  };

  // 处理分享
  const handleShare = () => {
    console.log('Sharing results');
    // 实现分享逻辑
  };

  // 开始新的测评
  const handleNewQuiz = () => {
    navigate('/quiz-settings');
  };

  // 渲染情绪分析雷达图
  const renderEmotionRadar = () => {
    const radarData = [
      { emotion: '喜悦', value: 80 },
      { emotion: '平静', value: 70 },
      { emotion: '自信', value: 65 },
      { emotion: '活力', value: 75 },
      { emotion: '专注', value: 60 },
      { emotion: '满足', value: 85 }
    ];

    return (
      <ResponsiveContainer width="100%" height={300}>
        <RadarChart data={radarData}>
          <PolarGrid />
          <PolarAngleAxis dataKey="emotion" />
          <PolarRadiusAxis angle={90} domain={[0, 100]} />
          <Radar
            name="情绪强度"
            dataKey="value"
            stroke="#3B82F6"
            fill="#3B82F6"
            fillOpacity={0.3}
          />
        </RadarChart>
      </ResponsiveContainer>
    );
  };

  // 渲染情绪分布饼图
  const renderEmotionDistribution = () => {
    const data = displayData.emotion_analysis.pattern_analysis.emotional_categories;

    return (
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, value }) => `${name}: ${value}%`}
          >
            {data.map((entry: any, index: number) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  // 渲染强度分布柱状图
  const renderIntensityDistribution = () => {
    const data = displayData.emotion_analysis.pattern_analysis.intensity_distribution;

    return (
      <ResponsiveContainer width="100%" height={200}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="level" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="count" fill="#10B981" />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在生成分析报告...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-red-500 mb-4">
              <Brain className="mx-auto h-12 w-12" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">加载失败</h2>
            <p className="text-gray-600 mb-4">
              无法加载测评结果，请稍后重试
            </p>
            <Button onClick={() => window.location.reload()}>
              重新加载
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部标题栏 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">情绪测评报告</h1>
              <p className="text-gray-600 mt-1">
                完成时间: {displayData.created_at.toLocaleString()}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                分享
              </Button>
              <Button variant="outline" onClick={() => handleExport('PDF')}>
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
              <Button onClick={handleNewQuiz}>
                <RefreshCw className="h-4 w-4 mr-2" />
                新测评
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 标签导航 */}
      <div className="max-w-6xl mx-auto px-4 py-4">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
          {[
            { key: 'overview', label: '总览', icon: TrendingUp },
            { key: 'analysis', label: '详细分析', icon: Brain },
            { key: 'recommendations', label: '个性化建议', icon: Target }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setSelectedTab(key as any)}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedTab === key
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Icon className="h-4 w-4 mr-2" />
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-6xl mx-auto px-4 pb-8">
        {selectedTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 完成度卡片 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">完成度</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{displayData.completion_percentage}%</div>
                <Progress value={displayData.completion_percentage} className="mt-2" />
              </CardContent>
            </Card>

            {/* 情绪稳定性 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">情绪稳定性</CardTitle>
                <Heart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(displayData.emotion_analysis.emotional_stability_index * 100)}%
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {displayData.emotion_analysis.emotional_stability_index > 0.7 ? '良好' : '需要关注'}
                </p>
              </CardContent>
            </Card>

            {/* 情绪复杂度 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">情绪复杂度</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(displayData.emotion_analysis.emotional_complexity_index * 100)}%
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {displayData.emotion_analysis.emotional_complexity_index > 0.6 ? '丰富' : '简单'}
                </p>
              </CardContent>
            </Card>

            {/* 主导情绪 */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>主导情绪</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {displayData.emotion_analysis.dominant_emotions.map((emotion: string, index: number) => (
                    <Badge key={index} variant="secondary" className="text-sm">
                      {emotion}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 情绪分布 */}
            <Card>
              <CardHeader>
                <CardTitle>情绪分布</CardTitle>
              </CardHeader>
              <CardContent>
                {renderEmotionDistribution()}
              </CardContent>
            </Card>
          </div>
        )}

        {selectedTab === 'analysis' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 情绪雷达图 */}
            <Card>
              <CardHeader>
                <CardTitle>情绪强度分析</CardTitle>
              </CardHeader>
              <CardContent>
                {renderEmotionRadar()}
              </CardContent>
            </Card>

            {/* 强度分布 */}
            <Card>
              <CardHeader>
                <CardTitle>强度分布</CardTitle>
              </CardHeader>
              <CardContent>
                {renderIntensityDistribution()}
              </CardContent>
            </Card>

            {/* 详细分析文本 */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>分析报告</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p className="text-gray-700 leading-relaxed">
                    基于您的测评结果，您当前的情绪状态整体呈现积极向上的趋势。
                    主导情绪以正面情绪为主，情绪稳定性指数达到
                    {Math.round(displayData.emotion_analysis.emotional_stability_index * 100)}%，
                    表明您具有良好的情绪调节能力。
                  </p>
                  <p className="text-gray-700 leading-relaxed mt-4">
                    情绪复杂度为 {Math.round(displayData.emotion_analysis.emotional_complexity_index * 100)}%，
                    说明您的情绪体验较为丰富，能够感知到情绪的细微变化。
                    这种敏感性有助于更好地理解自己的内心状态。
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedTab === 'recommendations' && (
          <div className="space-y-6">
            {displayData.personalized_recommendations.map((recommendation: any, index: number) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{recommendation.title}</CardTitle>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        置信度: {Math.round(recommendation.confidence * 100)}%
                      </Badge>
                      <Badge variant={recommendation.priority <= 2 ? 'default' : 'secondary'}>
                        优先级: {recommendation.priority}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed">
                    {recommendation.description}
                  </p>
                </CardContent>
              </Card>
            ))}

            {/* 后续建议 */}
            <Card>
              <CardHeader>
                <CardTitle>后续建议</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Calendar className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">定期复测</h4>
                      <p className="text-sm text-gray-600">
                        建议每2-4周进行一次情绪测评，跟踪情绪变化趋势
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Target className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">设定目标</h4>
                      <p className="text-sm text-gray-600">
                        根据分析结果设定具体的情绪管理目标
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuizResults;
