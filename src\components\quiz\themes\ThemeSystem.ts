/**
 * Quiz组件主题系统
 * 提供多套主题配置，支持快速切换和自定义
 */

// ==================== 主题配置接口 ====================

export interface ThemeConfig {
  name: string;
  display_name: Record<string, string>;
  description: Record<string, string>;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text_primary: string;
    text_secondary: string;
    border: string;
    shadow: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  fonts: {
    primary: string;
    secondary: string;
    display: string;
    monospace: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  border_radius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  animations: {
    duration_fast: string;
    duration_normal: string;
    duration_slow: string;
    easing_standard: string;
    easing_spring: string;
    easing_ease_out: string;
  };
}

// ==================== 预设主题 ====================

/**
 * 现代简约主题
 */
export const ModernTheme: ThemeConfig = {
  name: "modern",
  display_name: { zh: "现代简约", en: "Modern Clean" },
  description: { zh: "简洁现代的设计风格", en: "Clean and modern design style" },
  colors: {
    primary: "#2196F3",
    secondary: "#FFC107",
    accent: "#4CAF50",
    background: "#FFFFFF",
    surface: "#F5F5F5",
    text_primary: "#212121",
    text_secondary: "#757575",
    border: "#E0E0E0",
    shadow: "rgba(0, 0, 0, 0.1)",
    success: "#4CAF50",
    warning: "#FF9800",
    error: "#F44336",
    info: "#2196F3"
  },
  fonts: {
    primary: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
    secondary: "Inter, sans-serif",
    display: "Inter, sans-serif",
    monospace: "JetBrains Mono, Consolas, monospace"
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48 },
  border_radius: { sm: 4, md: 8, lg: 12, xl: 16 },
  shadows: {
    sm: "0 2px 4px rgba(0, 0, 0, 0.1)",
    md: "0 4px 8px rgba(0, 0, 0, 0.12)",
    lg: "0 8px 16px rgba(0, 0, 0, 0.15)",
    xl: "0 12px 24px rgba(0, 0, 0, 0.18)"
  },
  animations: {
    duration_fast: "150ms",
    duration_normal: "300ms",
    duration_slow: "600ms",
    easing_standard: "cubic-bezier(0.4, 0.0, 0.2, 1)",
    easing_spring: "cubic-bezier(0.175, 0.885, 0.32, 1.275)",
    easing_ease_out: "cubic-bezier(0.0, 0.0, 0.2, 1)"
  }
};

/**
 * 传统中医主题
 */
export const TraditionalTCMTheme: ThemeConfig = {
  name: "traditional_tcm",
  display_name: { zh: "传统中医", en: "Traditional TCM" },
  description: { zh: "融入中医文化元素的传统设计", en: "Traditional design with TCM cultural elements" },
  colors: {
    primary: "#D32F2F",      // 朱砂红
    secondary: "#FFD700",    // 金黄色
    accent: "#4CAF50",       // 翡翠绿
    background: "#FFF8E1",   // 宣纸色
    surface: "#F5F5DC",      // 米色
    text_primary: "#212121", // 墨黑色
    text_secondary: "#5D4037", // 棕色
    border: "#8D6E63",       // 土色
    shadow: "rgba(139, 69, 19, 0.2)",
    success: "#388E3C",      // 深绿
    warning: "#F57C00",      // 深橙
    error: "#D32F2F",        // 朱砂红
    info: "#1976D2"          // 深蓝
  },
  fonts: {
    primary: "PingFang SC, Source Han Sans, sans-serif",
    secondary: "STKaiti, KaiTi, serif",
    display: "STKaiti, KaiTi, serif",
    monospace: "SF Mono, Consolas, monospace"
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48 },
  border_radius: { sm: 2, md: 4, lg: 8, xl: 12 },
  shadows: {
    sm: "0 2px 4px rgba(139, 69, 19, 0.1)",
    md: "0 4px 8px rgba(139, 69, 19, 0.15)",
    lg: "0 8px 16px rgba(139, 69, 19, 0.2)",
    xl: "0 12px 24px rgba(139, 69, 19, 0.25)"
  },
  animations: {
    duration_fast: "200ms",
    duration_normal: "400ms",
    duration_slow: "800ms",
    easing_standard: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
    easing_spring: "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
    easing_ease_out: "cubic-bezier(0.25, 0.46, 0.45, 0.94)"
  }
};

/**
 * 游戏化主题
 */
export const GamifiedTheme: ThemeConfig = {
  name: "gamified",
  display_name: { zh: "游戏化", en: "Gamified" },
  description: { zh: "充满活力的游戏化设计风格", en: "Vibrant gamified design style" },
  colors: {
    primary: "#9C27B0",      // 紫色
    secondary: "#FF9800",    // 橙色
    accent: "#00BCD4",       // 青色
    background: "#1A1A2E",   // 深蓝色
    surface: "#16213E",      // 深蓝灰
    text_primary: "#FFFFFF", // 白色
    text_secondary: "#B0BEC5", // 浅灰
    border: "#37474F",       // 深灰
    shadow: "rgba(156, 39, 176, 0.3)",
    success: "#00E676",      // 亮绿
    warning: "#FFB300",      // 亮橙
    error: "#FF1744",        // 亮红
    info: "#00B0FF"          // 亮蓝
  },
  fonts: {
    primary: "Orbitron, monospace",
    secondary: "Roboto, sans-serif",
    display: "Orbitron, monospace",
    monospace: "Fira Code, monospace"
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48 },
  border_radius: { sm: 8, md: 12, lg: 16, xl: 20 },
  shadows: {
    sm: "0 2px 8px rgba(156, 39, 176, 0.2)",
    md: "0 4px 16px rgba(156, 39, 176, 0.3)",
    lg: "0 8px 24px rgba(156, 39, 176, 0.4)",
    xl: "0 12px 32px rgba(156, 39, 176, 0.5)"
  },
  animations: {
    duration_fast: "100ms",
    duration_normal: "250ms",
    duration_slow: "500ms",
    easing_standard: "cubic-bezier(0.25, 0.8, 0.25, 1)",
    easing_spring: "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
    easing_ease_out: "cubic-bezier(0.0, 0.0, 0.2, 1)"
  }
};

/**
 * 深色主题
 */
export const DarkTheme: ThemeConfig = {
  name: "dark",
  display_name: { zh: "深色模式", en: "Dark Mode" },
  description: { zh: "护眼的深色界面设计", en: "Eye-friendly dark interface design" },
  colors: {
    primary: "#BB86FC",
    secondary: "#03DAC6",
    accent: "#CF6679",
    background: "#121212",
    surface: "#1E1E1E",
    text_primary: "#FFFFFF",
    text_secondary: "#B3B3B3",
    border: "#333333",
    shadow: "rgba(0, 0, 0, 0.5)",
    success: "#4CAF50",
    warning: "#FF9800",
    error: "#CF6679",
    info: "#2196F3"
  },
  fonts: {
    primary: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
    secondary: "Inter, sans-serif",
    display: "Inter, sans-serif",
    monospace: "JetBrains Mono, Consolas, monospace"
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48 },
  border_radius: { sm: 4, md: 8, lg: 12, xl: 16 },
  shadows: {
    sm: "0 2px 4px rgba(0, 0, 0, 0.3)",
    md: "0 4px 8px rgba(0, 0, 0, 0.4)",
    lg: "0 8px 16px rgba(0, 0, 0, 0.5)",
    xl: "0 12px 24px rgba(0, 0, 0, 0.6)"
  },
  animations: {
    duration_fast: "150ms",
    duration_normal: "300ms",
    duration_slow: "600ms",
    easing_standard: "cubic-bezier(0.4, 0.0, 0.2, 1)",
    easing_spring: "cubic-bezier(0.175, 0.885, 0.32, 1.275)",
    easing_ease_out: "cubic-bezier(0.0, 0.0, 0.2, 1)"
  }
};

// ==================== 主题管理器 ====================

export class ThemeManager {
  private static themes: Map<string, ThemeConfig> = new Map([
    [ModernTheme.name, ModernTheme],
    [TraditionalTCMTheme.name, TraditionalTCMTheme],
    [GamifiedTheme.name, GamifiedTheme],
    [DarkTheme.name, DarkTheme]
  ]);
  
  private static currentTheme: ThemeConfig = ModernTheme;
  
  /**
   * 获取所有可用主题
   */
  static getAllThemes(): ThemeConfig[] {
    return Array.from(this.themes.values());
  }
  
  /**
   * 根据名称获取主题
   */
  static getTheme(name: string): ThemeConfig | undefined {
    return this.themes.get(name);
  }
  
  /**
   * 获取当前主题
   */
  static getCurrentTheme(): ThemeConfig {
    return this.currentTheme;
  }
  
  /**
   * 设置当前主题
   */
  static setCurrentTheme(name: string): boolean {
    const theme = this.themes.get(name);
    if (theme) {
      this.currentTheme = theme;
      this.applyThemeToDOM(theme);
      return true;
    }
    return false;
  }
  
  /**
   * 注册新主题
   */
  static registerTheme(theme: ThemeConfig): void {
    this.themes.set(theme.name, theme);
  }
  
  /**
   * 应用主题到DOM
   */
  private static applyThemeToDOM(theme: ThemeConfig): void {
    const root = document.documentElement;
    
    // 应用颜色变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key.replace(/_/g, '-')}`, value);
    });
    
    // 应用字体变量
    Object.entries(theme.fonts).forEach(([key, value]) => {
      root.style.setProperty(`--font-${key}`, value);
    });
    
    // 应用间距变量
    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, `${value}px`);
    });
    
    // 应用圆角变量
    Object.entries(theme.border_radius).forEach(([key, value]) => {
      root.style.setProperty(`--border-radius-${key}`, `${value}px`);
    });
    
    // 应用阴影变量
    Object.entries(theme.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });
    
    // 应用动画变量
    Object.entries(theme.animations).forEach(([key, value]) => {
      root.style.setProperty(`--animation-${key.replace(/_/g, '-')}`, value);
    });
    
    // 设置主题名称
    root.setAttribute('data-theme', theme.name);
  }
  
  /**
   * 生成主题CSS变量
   */
  static generateThemeCSS(theme: ThemeConfig): string {
    const cssVars: string[] = [];
    
    // 颜色变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      cssVars.push(`  --color-${key.replace(/_/g, '-')}: ${value};`);
    });
    
    // 字体变量
    Object.entries(theme.fonts).forEach(([key, value]) => {
      cssVars.push(`  --font-${key}: ${value};`);
    });
    
    // 间距变量
    Object.entries(theme.spacing).forEach(([key, value]) => {
      cssVars.push(`  --spacing-${key}: ${value}px;`);
    });
    
    // 圆角变量
    Object.entries(theme.border_radius).forEach(([key, value]) => {
      cssVars.push(`  --border-radius-${key}: ${value}px;`);
    });
    
    // 阴影变量
    Object.entries(theme.shadows).forEach(([key, value]) => {
      cssVars.push(`  --shadow-${key}: ${value};`);
    });
    
    // 动画变量
    Object.entries(theme.animations).forEach(([key, value]) => {
      cssVars.push(`  --animation-${key.replace(/_/g, '-')}: ${value};`);
    });
    
    return `[data-theme="${theme.name}"] {\n${cssVars.join('\n')}\n}`;
  }
  
  /**
   * 初始化主题系统
   */
  static initialize(defaultTheme: string = 'modern'): void {
    // 应用默认主题
    this.setCurrentTheme(defaultTheme);
    
    // 监听系统主题变化
    if (window.matchMedia) {
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      darkModeQuery.addEventListener('change', (e) => {
        if (e.matches && this.themes.has('dark')) {
          this.setCurrentTheme('dark');
        } else if (!e.matches && this.themes.has('modern')) {
          this.setCurrentTheme('modern');
        }
      });
    }
  }
}

// ==================== 主题工具函数 ====================

/**
 * 获取主题颜色值
 */
export function getThemeColor(colorKey: keyof ThemeConfig['colors']): string {
  return ThemeManager.getCurrentTheme().colors[colorKey];
}

/**
 * 获取主题字体值
 */
export function getThemeFont(fontKey: keyof ThemeConfig['fonts']): string {
  return ThemeManager.getCurrentTheme().fonts[fontKey];
}

/**
 * 获取主题间距值
 */
export function getThemeSpacing(spacingKey: keyof ThemeConfig['spacing']): number {
  return ThemeManager.getCurrentTheme().spacing[spacingKey];
}

/**
 * 创建主题感知的样式对象
 */
export function createThemedStyles(styleFactory: (theme: ThemeConfig) => React.CSSProperties): React.CSSProperties {
  return styleFactory(ThemeManager.getCurrentTheme());
}

export default ThemeManager;
