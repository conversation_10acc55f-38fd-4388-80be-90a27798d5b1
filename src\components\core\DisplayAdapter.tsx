import { useSkinManager } from '@/contexts/SkinContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import type {
  ContentDisplayMode,
  Emotion,
  ViewConfig,
  ViewType,
  RenderEngine,
} from '@/types';
import { ViewFactory } from '@/utils/viewFactory';
import type React from 'react';
import { useMemo } from 'react';

/**
 * 显示适配器组件属性
 */
interface DisplayAdapterProps {
  emotions: Emotion[]; // 情绪选项列表
  tier: number; // 当前层级
  onSelect: (emotion: Emotion) => void; // 选择情绪回调
  onBack?: () => void; // 返回上一级回调
  displayOptions?: {
    // 显示选项（可选，优先级高于userConfig）
    viewType?: ViewType; // 视图类型
    renderEngine?: string; // 渲染引擎
    displayMode?: ContentDisplayMode; // 显示模式
    skinId?: string; // 皮肤ID
  };
  selectedPath?: any; // 选中路径
}

/**
 * 显示适配器组件
 * 用于将情绪数据与显示系统分离
 * 使用新的视图系统
 *
 * 注意：这是新视图系统的一部分，是推荐使用的适配器组件。
 * 旧的适配器组件（如WheelAdapter）将在未来版本中移除。
 * 详见迁移计划文档：docs/view-system-migration-plan.md
 */
const DisplayAdapter: React.FC<DisplayAdapterProps> = ({
  emotions,
  tier,
  onSelect,
  onBack,
  displayOptions: propDisplayOptions,
  selectedPath,
}) => {
  // 获取用户配置
  const { userConfig } = useUserConfig();

  // 获取皮肤管理器
  const { activeSkin } = useSkinManager();

  // 获取用户显示选项的函数
  const getUserDisplayOptions = useMemo(() => {
    return () => {
      // 获取用户首选视图类型
      const viewType = userConfig.preferred_view_type || 'wheel';

      // 获取视图特定的皮肤ID
      const skinId =
        userConfig.view_type_skin_ids?.[viewType] ||
        userConfig.active_skin_id ||
        'default-wheel-skin';

      // 获取视图特定的渲染引擎
      const renderEngine = userConfig.render_engine_preferences?.[viewType] || 'D3';

      // 获取视图特定的内容显示模式
      const displayMode = userConfig.content_display_mode_preferences?.[viewType] || 'textEmoji';

      return {
        viewType,
        skinId,
        renderEngine,
        displayMode,
      };
    };
  }, [userConfig]);

  // 获取当前显示选项，优先使用 props > UserConfig > DisplayContext
  const currentOptions = useMemo(() => {
    // 从 DisplayContext 获取显示选项
    const contextOptions = getUserDisplayOptions();

    // 从 UserConfig 中获取相关设置
    const userViewType = userConfig.preferred_view_type;
    const userSkinId = userConfig.active_skin_id;

    // 获取视图特定的设置
    const viewSkinId = userConfig.view_type_skin_ids?.[userViewType] || userSkinId;
    const renderEngine =
      userConfig.render_engine_preferences?.[userViewType] ||
      (userViewType === 'wheel' ? 'D3' : 'D3');
    const displayMode = userConfig.content_display_mode_preferences?.[userViewType] || 'textEmoji';

    // 构建基于 UserConfig 的选项
    const userBasedOptions = {
      viewType: userViewType,
      skinId: viewSkinId,
      renderEngine,
      displayMode,
    };

    // 合并选项，优先级：props > userConfig > contextOptions
    return { ...contextOptions, ...userBasedOptions, ...propDisplayOptions };
  }, [propDisplayOptions, userConfig, getUserDisplayOptions]);

  // 获取当前皮肤配置
  const currentSkinConfig = useMemo(() => {
    // 创建默认的完整 SkinConfig
    const defaultSkinConfig = {
      // 必需的支持配置
      supported_content_modes: ['emoji', 'text', 'textEmoji'],
      supported_view_types: ['wheel', 'card', 'bubble', 'galaxy'],
      supported_render_engines: ['D3', 'SVG', 'R3F'],

      // 字体配置
      fonts: {
        family: 'Arial, sans-serif',
        size: { small: 12, medium: 14, large: 18 },
        weight: { normal: 400, bold: 700 },
      },

      // 颜色配置
      colors: {
        primary: '#3b82f6',
        secondary: '#64748b',
        background: '#ffffff',
        text: '#1e293b',
        accent: '#f59e0b',
      },

      // 效果配置
      effects: {
        shadows: true,
        animations: true,
        borderRadius: 8,
        opacity: 1,
      },

      // 视图配置
      view_configs: {
        wheel: {
          containerSize: 300,
          wheelRadius: 150,
          sector_gap: 2,
          sector_padding: 2,
          emojiSize: 20,
          emoji_position: 'center',
          text_size: 14,
          text_position: 'center',
          hover_effect: 'scale',
          hover_scale: 1.05,
          wheel_layout: 'standard',
        },
        card: {
          card_size: 100,
          card_spacing: 16,
          list_card_height: 60,
          card_border_radius: 8,
          card_hover_effect: 'scale',
          card_hover_scale: 1.05,
          layout: 'grid',
          columns: 3,
          aspect_ratio: 1,
        },
        bubble: {
          bubble_size: 70,
          bubble_spacing: 10,
          bubble_shape: 'circle',
          bubble_hover_effect: 'scale',
          bubble_hover_scale: 1.15,
          layout: 'circle',
          floating_animation: true,
        },
      },
    };

    // 如果有活动皮肤，合并配置以确保所有必需属性存在
    if (activeSkin?.config) {
      try {
        // 解析皮肤配置字符串为对象
        const parsedConfig =
          typeof activeSkin.config === 'string' ? JSON.parse(activeSkin.config) : activeSkin.config;

        return {
          ...defaultSkinConfig,
          ...parsedConfig,
          // 确保关键配置存在
          fonts: {
            ...defaultSkinConfig.fonts,
            ...parsedConfig.fonts,
          },
          colors: {
            ...defaultSkinConfig.colors,
            ...parsedConfig.colors,
          },
          effects: {
            ...defaultSkinConfig.effects,
            ...parsedConfig.effects,
          },
          view_configs: {
            ...defaultSkinConfig.view_configs,
            ...parsedConfig.view_configs,
          },
        };
      } catch (error) {
        console.error('[DisplayAdapter] Failed to parse skin config:', error);
        return defaultSkinConfig;
      }
    }

    // 否则使用默认的 SkinConfig
    return defaultSkinConfig;
  }, [activeSkin]);

  // 使用新的视图系统渲染组件
  const renderComponent = () => {
    const { viewType = 'wheel', renderEngine = 'D3', displayMode = 'textEmoji' } = currentOptions;

    // 直接使用传入的情绪数据
    const emotionData = emotions;

    // 直接使用传入的层级
    const tierLevel = tier;

    // 使用视图工厂创建视图
    const view = ViewFactory.createView(
      viewType as ViewType,
      displayMode as ContentDisplayMode,
      currentSkinConfig,
      {
        implementation: renderEngine as RenderEngine,
        layout: getLayoutForViewType(viewType as ViewType),
      }
    );

    // 创建视图配置
    const viewConfig: ViewConfig = {
      onBack,
      selectedPath,
    };

    // 渲染视图
    return view.render(emotionData, tierLevel, onSelect as any, viewConfig);
  };

  // 根据视图类型获取默认布局
  const getLayoutForViewType = (viewType: ViewType): string => {
    // 使用新的 layout_preferences 属性
    return (
      userConfig.layout_preferences?.[viewType] || ViewFactory.getDefaultLayoutForViewType(viewType)
    );
  };

  return <div className="display-adapter">{renderComponent()}</div>;
};

export default DisplayAdapter;
