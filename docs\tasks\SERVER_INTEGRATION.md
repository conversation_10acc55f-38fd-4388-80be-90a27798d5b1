# Server 类型定义统一指南

本文档说明如何将 server 目录中的类型定义统一到 `src/types/schema/api.ts` 中。

## 统一完成的类型

### ✅ 已统一的 Schema

#### 1. 数据库操作相关 (从 `server/lib/router.ts`)
- `SqlQueryInputSchema` - SQL 查询输入
- `BatchStatementsInputSchema` - 批量 SQL 语句输入
- `SqlScriptInputSchema` - SQL 脚本输入
- `TableQueryInputSchema` - 表查询输入
- `TableQueryWithLimitInputSchema` - 带限制的表查询输入

#### 2. 认证相关 (从 `server/lib/services/AuthService.ts`)
- `AuthTokenSchema` - 认证令牌
- `VerifyTokenInputSchema` - 令牌验证输入
- `UpdateVipStatusInputSchema` - 更新 VIP 状态输入

#### 3. 支付相关 (从 `server/lib/services/PaymentService.ts`)
- `PaymentMethodSchema` - 支付方式
- `VipPlanSchema` - VIP 计划
- `PurchaseResultSchema` - 购买结果
- `SkinPurchaseSchema` - 皮肤购买信息
- `EmojiSetPurchaseSchema` - 表情集购买信息

#### 4. 数据同步相关 (从 `server/lib/router.ts`)
- `MoodEntryUploadSchema` - 心情记录同步输入
- `EmotionSelectionUploadSchema` - 情绪选择同步输入
- `DataSynchronizeInputSchema` - 数据同步输入
- `FullSyncInputSchema` - 完整同步输入

#### 5. 分析服务相关 (从 `server/lib/services/AnalyticsService.ts`)
- `GetMoodAnalyticsInputSchema` - 心情分析输入
- `GetEmotionUsageStatsInputSchema` - 情绪使用统计输入
- `GetUserActivityStatsInputSchema` - 用户活动统计输入

#### 6. 用户管理相关 (从 `server/lib/services/UserManagementService.ts`)
- `UpdateUserPreferencesInputSchema` - 用户偏好设置输入
- `UnlockSkinInputSchema` - 解锁皮肤输入

#### 7. 数据库管理相关 (从 `server/lib/router.ts`)
- `ResetDatabaseInputSchema` - 重置数据库输入

## 如何在 Server 中使用统一类型

### 1. 导入统一类型

**旧方式 (server/lib/router.ts):**
```typescript
// 内联定义 Schema
const sqlQueryInputSchema = z.union([
  z.string(),
  z.object({
    sql: z.string(),
    args: z.array(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
  }),
]);
```

**新方式:**
```typescript
// 从统一的 schema 导入
import { SqlQueryInputSchema } from '../../../src/types/schema/api.js';

// 直接使用
export const appRouter = router({
  query: publicProcedure
    .input(SqlQueryInputSchema)
    .query(async ({ input }) => {
      // 使用统一的类型
    })
});
```

### 2. 更新 AuthService

**旧方式 (server/lib/services/AuthService.ts):**
```typescript
export interface User {
  id: string;
  email: string;
  // ... 其他字段
}

export interface AuthToken {
  accessToken: string;
  // ... 其他字段
}
```

**新方式:**
```typescript
import { 
  AuthTokenSchema, 
  VerifyTokenInputSchema,
  type AuthToken,
  type VerifyTokenInput 
} from '../../../src/types/schema/api.js';

export class AuthService {
  async verifyToken(input: VerifyTokenInput): Promise<{ success: boolean; user?: User; error?: string }> {
    // 使用 Zod 验证输入
    const validInput = VerifyTokenInputSchema.parse(input);
    // ... 实现
  }
}
```

### 3. 更新 PaymentService

**旧方式 (server/lib/services/PaymentService.ts):**
```typescript
export interface VipPlan {
  id: string;
  name: string;
  // ... 其他字段
}
```

**新方式:**
```typescript
import { 
  VipPlanSchema,
  PurchaseResultSchema,
  type VipPlan,
  type PurchaseResult 
} from '../../../src/types/schema/api.js';

export class PaymentService {
  async getVipPlans(): Promise<{ success: boolean; data?: VipPlan[]; error?: string }> {
    // 使用统一的类型和验证
    const plans = await this.fetchPlans();
    return {
      success: true,
      data: plans.map(plan => VipPlanSchema.parse(plan))
    };
  }
}
```

### 4. 更新 tRPC 路由

**旧方式:**
```typescript
// 内联 Schema 定义
synchronizeData: publicProcedure
  .input(z.object({
    moodEntriesToUpload: z.array(z.object({
      // 大量内联字段定义
    })).optional(),
    // ... 更多内联定义
  }))
```

**新方式:**
```typescript
import { DataSynchronizeInputSchema } from '../../../src/types/schema/api.js';

synchronizeData: publicProcedure
  .input(DataSynchronizeInputSchema)
  .mutation(async ({ input }) => {
    // 输入已经通过 Zod 验证
    // 类型安全且与客户端一致
  })
```

## 迁移步骤

### 1. 立即可用
- 新的统一 Schema 已经可以在 server 中使用
- 通过相对路径导入：`../../../src/types/schema/api.js`

### 2. 渐进迁移
1. **更新导入语句**: 将内联 Schema 替换为统一 Schema 的导入
2. **移除重复定义**: 删除 server 中的重复接口和 Schema 定义
3. **更新类型注解**: 使用统一的 TypeScript 类型
4. **添加运行时验证**: 在数据边界使用 Zod 验证

### 3. 验证迁移
```bash
# 检查 TypeScript 编译
cd server && npx tsc --noEmit

# 运行测试
npm test
```

## 优势

### 🔒 **类型安全**
- 客户端和服务端使用相同的类型定义
- 运行时 Zod 验证确保数据正确性
- 编译时 TypeScript 检查

### 🎯 **一致性**
- 单一数据源，避免类型不一致
- API 输入输出类型完全对齐
- 减少维护成本

### 🚀 **开发效率**
- 自动类型提示和补全
- 重构时自动更新所有引用
- 减少手动类型同步工作

### 🔧 **维护性**
- 集中管理所有 API 类型
- 版本控制友好
- 易于扩展和修改

## 注意事项

1. **导入路径**: 使用相对路径 `../../../src/types/schema/api.js`
2. **文件扩展名**: 在 server 中导入时使用 `.js` 扩展名
3. **运行时验证**: 在 API 边界使用 Zod 验证，内部逻辑可以使用 TypeScript 类型
4. **向后兼容**: 可以逐步迁移，新旧方式可以暂时共存

## 下一步

1. **更新 server/lib/router.ts**: 使用统一的 Schema 替换内联定义
2. **更新服务类**: 使用统一的类型接口
3. **添加验证**: 在适当的地方添加 Zod 运行时验证
4. **清理代码**: 移除重复的类型定义
5. **更新测试**: 确保所有测试使用统一的类型

通过这种统一的类型定义方式，我们实现了客户端和服务端的完全类型一致性，提高了开发效率和代码质量。
