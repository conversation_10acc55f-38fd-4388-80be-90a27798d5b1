import { useAppLoading } from '@/contexts/AppLoadingContext';
import { useLanguage } from '@/contexts/LanguageContext';
import type React from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { AppLoadingScreen } from './ui/AppLoadingScreen';

// 注意：以下deprecated页面的导入已被移除
// import AnimatedEmojiTest from "@/pages/AnimatedEmojiTest";
// import WheelWithAnimatedEmoji from "@/pages/WheelWithAnimatedEmoji";
// import EmojiSystemTestPage from "@/pages/EmojiSystemTestPage";
// import NeutralHappyTest from "@/pages/NeutralHappyTest";
// import EmotionDataEditorPage from '@/pages/EmotionDataEditorPage'; // 已被QuizManagementPage替代

import DatabaseTest from '@/components/DatabaseTest';
import Analytics from '@/pages/Analytics';
import EmojiSetManager from '@/pages/EmojiSetManager';
import QuizManagementPage from '@/pages/QuizManagementPage';
import ErrorPage from '@/pages/ErrorPage';
import History from '@/pages/History';
import Home from '@/pages/NewHome';
import LanguageTest from '@/pages/LanguageTest';
import { Login } from '@/pages/Login';
import { Register } from '@/pages/Register';
import Settings from '@/pages/Settings';
import QuizSettings from '@/pages/QuizSettings';
// import SkinShop from "@/pages/SkinShop";
import Shop from '@/pages/Shop';
import { VIP } from '@/pages/VIP';
// import TursoExamplePage from "@/pages/TursoExamplePage";
// import ColorTest from "@/pages/ColorTest";
// import GameStyleDemo from "@/pages/GameStyleDemo";
import WheelTest from '@/pages/WheelTest';
import QuizComponentTest from '@/components/quiz/QuizComponentTest';
import SpecialViewsTest from '@/components/quiz/SpecialViewsTest';
import QuizLauncher from '@/pages/QuizLauncher';
import QuizSession from '@/pages/QuizSession';
import QuizResults from '@/pages/QuizResults';
import MobileLayout from './layout/MobileLayout';

const AppContent: React.FC = () => {
  const { isAppLoading, loadingMessage } = useAppLoading();
  const { t, isLanguageReady } = useLanguage();

  // Show loading screen if app is still loading
  if (isAppLoading) {
    return <AppLoadingScreen message={isLanguageReady ? t('app.loading') : loadingMessage} />;
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route
          path="/"
          element={
            <MobileLayout>
              <Home />
            </MobileLayout>
          }
        />
        <Route
          path="/history"
          element={
            <MobileLayout>
              <History />
            </MobileLayout>
          }
        />
        <Route
          path="/analytics"
          element={
            <MobileLayout>
              <Analytics />
            </MobileLayout>
          }
        />
        <Route
          path="/settings"
          element={
            <MobileLayout>
              <Settings />
            </MobileLayout>
          }
        />
        <Route
          path="/quiz-settings"
          element={
            <MobileLayout>
              <QuizSettings />
            </MobileLayout>
          }
        />

        {/* 认证相关路由 */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />

        {/* VIP页面 */}
        <Route
          path="/vip"
          element={
            <MobileLayout>
              <VIP />
            </MobileLayout>
          }
        />

        <Route
          path="/language-test"
          element={
            <MobileLayout>
              <LanguageTest />
            </MobileLayout>
          }
        />

        <Route
          path="/wheel-test"
          element={
            <MobileLayout>
              <WheelTest />
            </MobileLayout>
          }
        />

        <Route
          path="/quiz-component-test"
          element={
            <MobileLayout>
              <QuizComponentTest />
            </MobileLayout>
          }
        />

        <Route
          path="/special-views-test"
          element={
            <MobileLayout>
              <SpecialViewsTest />
            </MobileLayout>
          }
        />

        <Route
          path="/quiz-launcher"
          element={
            <MobileLayout>
              <QuizLauncher />
            </MobileLayout>
          }
        />

        <Route
          path="/quiz-session/:sessionId"
          element={
            <MobileLayout>
              <QuizSession />
            </MobileLayout>
          }
        />

        <Route
          path="/quiz-results/:sessionId"
          element={
            <MobileLayout>
              <QuizResults />
            </MobileLayout>
          }
        />

        {/* 注意：/emotion-data-editor 路由已重定向到 QuizManagementPage */}
        <Route
          path="/emotion-data-editor"
          element={
            <MobileLayout>
              <QuizManagementPage />
            </MobileLayout>
          }
        />
        <Route
          path="/emotion-data-editor/:id"
          element={
            <MobileLayout>
              <QuizManagementPage />
            </MobileLayout>
          }
        />
        <Route
          path="/quiz-management"
          element={
            <MobileLayout>
              <QuizManagementPage />
            </MobileLayout>
          }
        />
        <Route
          path="/quiz-management/:id"
          element={
            <MobileLayout>
              <QuizManagementPage />
            </MobileLayout>
          }
        />
        <Route
          path="/shop"
          element={
            <MobileLayout>
              <Shop />
            </MobileLayout>
          }
        />
        <Route
          path="/emoji-set-manager"
          element={
            <MobileLayout>
              <EmojiSetManager />
            </MobileLayout>
          }
        />
        <Route
          path="/database-test"
          element={
            <MobileLayout>
              <DatabaseTest />
            </MobileLayout>
          }
        />

        <Route path="/error" element={<ErrorPage type="500" />} />
        <Route path="/network-error" element={<ErrorPage type="network" />} />
        <Route path="/auth-error" element={<ErrorPage type="auth" />} />
        <Route path="*" element={<ErrorPage type="404" />} />
      </Routes>
    </BrowserRouter>
  );
};

export default AppContent;
