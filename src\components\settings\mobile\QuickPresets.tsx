import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>ap, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Eye } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import { useTheme } from '@/contexts/ThemeContext';
import { toast } from 'sonner';

interface QuickPresetsProps {
  userType: 'beginner' | 'regular' | 'advanced' | 'vip' | 'accessibility';
}

interface Preset {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  theme: 'light' | 'dark' | 'system';
  colorMode: 'warm' | 'cool' | 'mixed' | 'auto';
  viewType: string;
  badge?: string;
  vipOnly?: boolean;
}

const QuickPresets: React.FC<QuickPresetsProps> = ({ userType }) => {
  const { t } = useLanguage();
  const { updateUserConfig } = useUserConfig();
  const { setTheme } = useTheme();

  // 根据用户类型获取可用预设
  const getAvailablePresets = (): Preset[] => {
    const basePresets: Preset[] = [
      {
        id: 'comfort',
        name: '舒适模式',
        description: '温暖色调，适合日常使用',
        icon: Heart,
        theme: 'light',
        colorMode: 'warm',
        viewType: 'wheel',
        badge: '推荐'
      },
      {
        id: 'focus',
        name: '专注模式',
        description: '冷色调，提高专注力',
        icon: Zap,
        theme: 'light',
        colorMode: 'cool',
        viewType: 'card',
        badge: '工作'
      },
      {
        id: 'night',
        name: '夜间模式',
        description: '深色主题，保护视力',
        icon: Moon,
        theme: 'dark',
        colorMode: 'warm',
        viewType: 'wheel',
        badge: '护眼'
      }
    ];

    // 根据用户类型添加额外预设
    if (userType === 'advanced' || userType === 'vip') {
      basePresets.push({
        id: 'dynamic',
        name: '动态模式',
        description: '智能适配，丰富效果',
        icon: Sparkles,
        theme: 'system',
        colorMode: 'mixed',
        viewType: 'bubble',
        badge: '智能'
      });
    }

    if (userType === 'vip') {
      basePresets.push({
        id: 'premium',
        name: 'VIP专享',
        description: '星系视图，极致体验',
        icon: Crown,
        theme: 'dark',
        colorMode: 'auto',
        viewType: 'galaxy',
        badge: 'VIP',
        vipOnly: true
      });
    }

    if (userType === 'accessibility') {
      return [
        {
          id: 'high_contrast_light',
          name: '高对比度浅色',
          description: '最佳可读性，适合视力较弱用户',
          icon: Sun,
          theme: 'light',
          colorMode: 'warm',
          viewType: 'card',
          badge: '无障碍'
        },
        {
          id: 'high_contrast_dark',
          name: '高对比度深色',
          description: '减少眼部疲劳，夜间友好',
          icon: Moon,
          theme: 'dark',
          colorMode: 'cool',
          viewType: 'card',
          badge: '护眼'
        },
        {
          id: 'large_text',
          name: '大字体模式',
          description: '增大文字，便于阅读',
          icon: Eye,
          theme: 'light',
          colorMode: 'warm',
          viewType: 'list',
          badge: '大字体'
        }
      ];
    }

    return basePresets;
  };

  const availablePresets = getAvailablePresets();

  // 应用预设
  const applyPreset = async (preset: Preset) => {
    try {
      // 应用主题
      setTheme(preset.theme);

      // 应用其他配置
      await updateUserConfig({
        color_mode: preset.colorMode,
        preferred_view_type: preset.viewType,
        // 如果是无障碍预设，同时应用无障碍设置
        ...(userType === 'accessibility' && {
          accessibility: JSON.stringify({
            high_contrast: preset.id.includes('high_contrast'),
            large_text: preset.id === 'large_text',
            reduce_motion: true,
            screen_reader_support: true
          })
        })
      });

      // 应用CSS类（用于无障碍功能）
      if (userType === 'accessibility') {
        document.documentElement.classList.remove('high-contrast', 'large-text');
        
        if (preset.id.includes('high_contrast')) {
          document.documentElement.classList.add('high-contrast');
        }
        if (preset.id === 'large_text') {
          document.documentElement.classList.add('large-text');
        }
      }

      toast.success(`已应用${preset.name}`, {
        description: preset.description,
        duration: 3000
      });

    } catch (error) {
      console.error('Failed to apply preset:', error);
      toast.error('应用预设失败', {
        description: '请稍后重试',
        duration: 3000
      });
    }
  };

  // 获取预设的颜色预览
  const getPresetColors = (preset: Preset) => {
    const colors = {
      warm: ['#FF6B6B', '#FFD93D', '#6BCF7F'],
      cool: ['#4ECDC4', '#45B7D1', '#96CEB4'],
      mixed: ['#FF6B6B', '#45B7D1', '#6BCF7F'],
      auto: ['#FF6B6B', '#45B7D1', '#6BCF7F', '#FFD93D']
    };
    return colors[preset.colorMode] || colors.warm;
  };

  if (userType === 'beginner') {
    // 新手用户：简化的预设选择
    return (
      <Card>
        <CardContent className="p-4 space-y-4">
          <div className="flex items-center space-x-3 mb-4">
            <Palette className="h-5 w-5 text-primary" />
            <h3 className="font-medium">快速设置</h3>
            <Badge variant="secondary">一键配置</Badge>
          </div>

          <div className="space-y-3">
            {availablePresets.slice(0, 3).map((preset) => (
              <Button
                key={preset.id}
                variant="outline"
                onClick={() => applyPreset(preset)}
                className="w-full h-auto p-4 flex items-center space-x-3"
              >
                <preset.icon className="h-6 w-6" />
                <div className="flex-1 text-left">
                  <div className="font-medium">{preset.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {preset.description}
                  </div>
                </div>
                {preset.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {preset.badge}
                  </Badge>
                )}
              </Button>
            ))}
          </div>

          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <p className="text-xs text-blue-700 dark:text-blue-300">
              💡 选择一个预设快速开始，稍后可以在设置中进行详细调整
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 其他用户类型：完整的预设网格
  return (
    <Card>
      <CardContent className="p-4 space-y-4">
        <div className="flex items-center space-x-3 mb-4">
          <Sparkles className="h-5 w-5 text-primary" />
          <h3 className="font-medium">
            {userType === 'accessibility' ? '无障碍预设' : '快速预设'}
          </h3>
          <Badge variant="outline">
            {availablePresets.length} 个选项
          </Badge>
        </div>

        <div className="grid grid-cols-1 gap-3">
          {availablePresets.map((preset) => (
            <Button
              key={preset.id}
              variant="outline"
              onClick={() => applyPreset(preset)}
              className="w-full h-auto p-4 flex items-start space-x-3"
            >
              <preset.icon className="h-5 w-5 mt-0.5 flex-shrink-0" />
              <div className="flex-1 text-left">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium">{preset.name}</span>
                  {preset.badge && (
                    <Badge 
                      variant={preset.vipOnly ? "destructive" : "secondary"} 
                      className="text-xs"
                    >
                      {preset.badge}
                    </Badge>
                  )}
                </div>
                <div className="text-xs text-muted-foreground mb-2">
                  {preset.description}
                </div>
                {/* 颜色预览 */}
                <div className="flex space-x-1">
                  {getPresetColors(preset).map((color, index) => (
                    <div
                      key={index}
                      className="w-3 h-3 rounded-full border border-border"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </Button>
          ))}
        </div>

        {userType === 'vip' && (
          <div className="mt-4 p-3 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950 rounded-lg">
            <p className="text-xs text-purple-700 dark:text-purple-300">
              👑 VIP用户专享高级预设和智能自适应功能
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default QuickPresets;
