/**
 * Quiz选项管理组件
 * 用于管理Quiz问题选项的创建、编辑、删除等操作
 */

import React, { useState } from 'react';
import { QuizQuestion, QuizQuestionOption } from '@/types/schema/base';
import { Services } from '@/services';
import { toast } from 'sonner';
import { useLanguage } from '@/contexts/LanguageContext';

interface QuizOptionManagerProps {
  question: QuizQuestion;
  options: QuizQuestionOption[];
  onDataUpdate: () => void;
}

interface OptionFormData {
  option_text: string;
  option_value: string;
  option_order: number;
  scoring_value: number | null;
  is_correct: boolean | null;
  is_active: boolean;
  metadata: {
    emoji?: string;
    color?: string;
    parent_option_id?: string;
    parent_emotion_id?: string;
    description?: string;
  };
}

const QuizOptionManager: React.FC<QuizOptionManagerProps> = ({
  question,
  options,
  onDataUpdate
}) => {
  const { t } = useLanguage();
  const [isCreating, setIsCreating] = useState(false);
  const [editingOption, setEditingOption] = useState<QuizQuestionOption | null>(null);
  const [formData, setFormData] = useState<OptionFormData>({
    option_text: '',
    option_value: '',
    option_order: options.length + 1,
    scoring_value: null,
    is_correct: null,
    is_active: true,
    metadata: {}
  });

  /**
   * 重置表单
   */
  const resetForm = () => {
    setFormData({
      option_text: '',
      option_value: '',
      option_order: options.length + 1,
      scoring_value: null,
      is_correct: null,
      is_active: true,
      metadata: {}
    });
    setIsCreating(false);
    setEditingOption(null);
  };

  /**
   * 开始创建新选项
   */
  const startCreating = () => {
    resetForm();
    setIsCreating(true);
  };

  /**
   * 开始编辑选项
   */
  const startEditing = (option: QuizQuestionOption) => {
    let metadata = {};
    try {
      metadata = typeof option.metadata === 'string' 
        ? JSON.parse(option.metadata) 
        : option.metadata || {};
    } catch {
      metadata = {};
    }

    setFormData({
      option_text: option.option_text,
      option_value: option.option_value,
      option_order: option.option_order,
      scoring_value: option.scoring_value,
      is_correct: option.is_correct,
      is_active: option.is_active,
      metadata
    });
    setEditingOption(option);
    setIsCreating(true);
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const quizQuestionService = await Services.quizQuestion();
      
      const optionData = {
        ...formData,
        question_id: question.id,
        metadata: JSON.stringify(formData.metadata)
      };
      
      if (editingOption) {
        // 更新现有选项
        const result = await quizQuestionService.updateOption(editingOption.id, optionData);
        if (result.success) {
          toast.success('选项更新成功');
          onDataUpdate();
          resetForm();
        } else {
          throw new Error(result.error || 'Update failed');
        }
      } else {
        // 创建新选项
        const result = await quizQuestionService.createOption({
          ...optionData,
          created_by: 'user'
        });
        if (result.success) {
          toast.success('选项创建成功');
          onDataUpdate();
          resetForm();
        } else {
          throw new Error(result.error || 'Create failed');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Operation failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 删除选项
   */
  const handleDelete = async (option: QuizQuestionOption) => {
    if (!confirm(`确定要删除选项"${option.option_text}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.deleteOption(option.id);
      
      if (result.success) {
        toast.success('选项删除成功');
        onDataUpdate();
      } else {
        throw new Error(result.error || 'Delete failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Delete failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 复制选项
   */
  const handleDuplicate = async (option: QuizQuestionOption) => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.createOption({
        option_text: `${option.option_text} (副本)`,
        option_value: `${option.option_value}_copy`,
        option_order: options.length + 1,
        question_id: question.id,
        scoring_value: option.scoring_value,
        is_correct: option.is_correct,
        is_active: false, // 副本默认不激活
        metadata: option.metadata,
        created_by: 'user'
      });
      
      if (result.success) {
        toast.success('选项复制成功');
        onDataUpdate();
      } else {
        throw new Error(result.error || 'Duplicate failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Duplicate failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 调整选项顺序
   */
  const handleReorder = async (option: QuizQuestionOption, direction: 'up' | 'down') => {
    const currentIndex = options.findIndex(o => o.id === option.id);
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    if (targetIndex < 0 || targetIndex >= options.length) {
      return;
    }

    try {
      const quizQuestionService = await Services.quizQuestion();
      const targetOption = options[targetIndex];
      
      // 交换顺序
      await Promise.all([
        quizQuestionService.updateOption(option.id, { option_order: targetOption.option_order }),
        quizQuestionService.updateOption(targetOption.id, { option_order: option.option_order })
      ]);
      
      toast.success('选项顺序调整成功');
      onDataUpdate();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Reorder failed';
      toast.error(errorMessage);
    }
  };

  /**
   * 获取选项的emoji
   */
  const getOptionEmoji = (option: QuizQuestionOption): string => {
    try {
      const metadata = typeof option.metadata === 'string' 
        ? JSON.parse(option.metadata) 
        : option.metadata;
      return metadata?.emoji || '📝';
    } catch {
      return '📝';
    }
  };

  /**
   * 获取选项的颜色
   */
  const getOptionColor = (option: QuizQuestionOption): string => {
    try {
      const metadata = typeof option.metadata === 'string' 
        ? JSON.parse(option.metadata) 
        : option.metadata;
      return metadata?.color || '#3b82f6';
    } catch {
      return '#3b82f6';
    }
  };

  /**
   * 渲染选项列表
   */
  const renderOptionList = () => (
    <div className="quiz-option-list">
      {options
        .sort((a, b) => a.option_order - b.option_order)
        .map((option, index) => (
          <div key={option.id} className="quiz-option-card">
            <div className="option-header">
              <div className="option-info">
                <span className="option-order">#{option.option_order}</span>
                <span 
                  className="option-emoji"
                  style={{ backgroundColor: getOptionColor(option) }}
                >
                  {getOptionEmoji(option)}
                </span>
                <div className="option-text-info">
                  <h4 className="option-text">{option.option_text}</h4>
                  <span className="option-value">值: {option.option_value}</span>
                </div>
              </div>
              <div className="option-badges">
                {option.is_active && <span className="badge active">活跃</span>}
                {option.is_correct && <span className="badge correct">正确答案</span>}
                {option.scoring_value !== null && (
                  <span className="badge score">分值: {option.scoring_value}</span>
                )}
              </div>
            </div>
            
            <div className="option-actions">
              <button 
                className="action-button secondary"
                onClick={() => startEditing(option)}
              >
                编辑
              </button>
              <button 
                className="action-button secondary"
                onClick={() => handleDuplicate(option)}
              >
                复制
              </button>
              <div className="order-controls">
                <button 
                  className="order-button"
                  onClick={() => handleReorder(option, 'up')}
                  disabled={index === 0}
                >
                  ↑
                </button>
                <button 
                  className="order-button"
                  onClick={() => handleReorder(option, 'down')}
                  disabled={index === options.length - 1}
                >
                  ↓
                </button>
              </div>
              <button 
                className="action-button danger"
                onClick={() => handleDelete(option)}
              >
                删除
              </button>
            </div>
          </div>
        ))}
    </div>
  );

  /**
   * 渲染创建/编辑表单
   */
  const renderForm = () => (
    <div className="quiz-option-form-overlay">
      <div className="quiz-option-form">
        <h3>{editingOption ? '编辑选项' : '创建新选项'}</h3>
        
        <form onSubmit={handleSubmit}>
          <div className="form-row">
            <div className="form-group">
              <label>选项文本 *</label>
              <input
                type="text"
                value={formData.option_text}
                onChange={(e) => setFormData({...formData, option_text: e.target.value})}
                required
                placeholder="输入选项文本"
              />
            </div>
            
            <div className="form-group">
              <label>选项值 *</label>
              <input
                type="text"
                value={formData.option_value}
                onChange={(e) => setFormData({...formData, option_value: e.target.value})}
                required
                placeholder="输入选项值"
              />
            </div>
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>选项顺序</label>
              <input
                type="number"
                min="1"
                value={formData.option_order}
                onChange={(e) => setFormData({...formData, option_order: parseInt(e.target.value)})}
              />
            </div>
            
            <div className="form-group">
              <label>评分值</label>
              <input
                type="number"
                value={formData.scoring_value || ''}
                onChange={(e) => setFormData({...formData, scoring_value: e.target.value ? parseInt(e.target.value) : null})}
                placeholder="可选的评分值"
              />
            </div>
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>Emoji</label>
              <input
                type="text"
                value={formData.metadata.emoji || ''}
                onChange={(e) => setFormData({
                  ...formData, 
                  metadata: {...formData.metadata, emoji: e.target.value}
                })}
                placeholder="选择一个emoji"
              />
            </div>
            
            <div className="form-group">
              <label>颜色</label>
              <input
                type="color"
                value={formData.metadata.color || '#3b82f6'}
                onChange={(e) => setFormData({
                  ...formData, 
                  metadata: {...formData.metadata, color: e.target.value}
                })}
              />
            </div>
          </div>
          
          <div className="form-group">
            <label>描述</label>
            <textarea
              value={formData.metadata.description || ''}
              onChange={(e) => setFormData({
                ...formData, 
                metadata: {...formData.metadata, description: e.target.value}
              })}
              placeholder="可选的选项描述"
              rows={2}
            />
          </div>
          
          <div className="form-group">
            <div className="checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.is_correct === true}
                  onChange={(e) => setFormData({...formData, is_correct: e.target.checked ? true : null})}
                />
                正确答案
              </label>
              
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                />
                激活状态
              </label>
            </div>
          </div>
          
          <div className="form-actions">
            <button type="button" onClick={resetForm} className="action-button secondary">
              取消
            </button>
            <button type="submit" className="action-button primary">
              {editingOption ? '更新' : '创建'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );

  return (
    <div className="quiz-option-manager">
      <div className="manager-header">
        <h2>选项管理</h2>
        <div className="question-info">
          <span className="question-text">{question.question_text}</span>
          <span className="question-type">({question.question_type})</span>
        </div>
        <button className="action-button primary" onClick={startCreating}>
          创建新选项
        </button>
      </div>
      
      {options.length === 0 ? (
        <div className="empty-state">
          <p>暂无选项，点击上方按钮创建第一个选项</p>
        </div>
      ) : (
        renderOptionList()
      )}
      
      {isCreating && renderForm()}
    </div>
  );
};

export { QuizOptionManager };
