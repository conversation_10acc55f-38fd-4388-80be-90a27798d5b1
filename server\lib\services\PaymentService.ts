/**
 * 支付服务 - 真实 Stripe 集成
 *
 * 职责:
 * - 处理VIP订阅支付
 * - 处理皮肤和表情集购买
 * - 管理支付状态和订阅生命周期
 * - 处理 Stripe Webhooks
 */

import Stripe from 'stripe';
import { executeQuery, batchStatements } from '../database/index.js';

// 导入统一的类型定义
import {
  type VipPlan,
  type PurchaseResult
} from '../../../src/types/schema/api.js';

// Stripe 配置接口
interface StripeConfig {
  secretKey: string;
  publishableKey: string;
  webhookSecret: string;
  apiVersion: '2024-12-18.acacia';
}

// 支付意图结果接口
interface PaymentIntentResult {
  success: boolean;
  paymentIntent?: Stripe.PaymentIntent;
  clientSecret?: string;
  error?: string;
}

// 订阅结果接口
interface SubscriptionResult {
  success: boolean;
  subscription?: Stripe.Subscription;
  clientSecret?: string;
  error?: string;
}

export class PaymentService {
  private static instance: PaymentService;
  private stripe: Stripe;
  private config: StripeConfig;

  private constructor() {
    // 初始化 Stripe 配置
    this.config = {
      secretKey: process.env.STRIPE_SECRET_KEY || '',
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
      apiVersion: '2024-12-18.acacia'
    };

    // 验证必需的环境变量
    if (!this.config.secretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }

    // 初始化 Stripe 客户端
    this.stripe = new Stripe(this.config.secretKey, {
      apiVersion: this.config.apiVersion,
      typescript: true,
    });

    console.log('[PaymentService] Stripe client initialized');
  }

  static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  /**
   * 获取 Stripe 公钥（用于客户端）
   */
  getPublishableKey(): string {
    return this.config.publishableKey;
  }

  /**
   * 获取可用的VIP计划 (从数据库读取)
   */
  async getVipPlans(): Promise<{ success: boolean; data?: VipPlan[]; error?: string }> {
    try {
      const result = await executeQuery({
        sql: `
          SELECT
            id, name, description, price, currency, billing_cycle as duration,
            features, stripe_price_id as stripePriceId, is_active,
            created_at, updated_at
          FROM vip_plans
          WHERE is_active = 1
          ORDER BY price ASC
        `,
        args: []
      });

      const plans: VipPlan[] = result.rows.map((row: any) => ({
        id: row.id,
        name: row.name,
        description: row.description,
        duration: row.duration,
        price: row.price,
        currency: row.currency,
        features: JSON.parse(row.features || '[]'),
        stripePriceId: row.stripePriceId,
        isActive: row.is_active,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));

      return {
        success: true,
        data: plans
      };
    } catch (error) {
      console.error('[PaymentService] Get VIP plans error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get VIP plans'
      };
    }
  }

  /**
   * 创建VIP订阅 (使用 Stripe Subscriptions)
   */
  async createVipSubscription(
    userId: string,
    planId: string,
    paymentMethodId: string,
    customerEmail?: string
  ): Promise<SubscriptionResult> {
    try {
      // 1. 验证用户和计划
      const userResult = await executeQuery({
        sql: 'SELECT * FROM users WHERE id = ?',
        args: [userId]
      });

      if (!userResult.rows || userResult.rows.length === 0) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const user = userResult.rows[0];
      const plans = await this.getVipPlans();
      const plan = plans.data?.find(p => p.id === planId);

      if (!plan || !plan.stripePriceId) {
        return {
          success: false,
          error: 'Invalid plan or missing Stripe price ID'
        };
      }

      // 2. 创建或获取 Stripe 客户
      let customer: Stripe.Customer;

      if (user.stripe_customer_id) {
        customer = await this.stripe.customers.retrieve(user.stripe_customer_id) as Stripe.Customer;
      } else {
        customer = await this.stripe.customers.create({
          email: customerEmail || user.email,
          metadata: {
            userId: userId,
            source: 'mindful_mood_app'
          }
        });

        // 保存 Stripe 客户 ID
        await executeQuery({
          sql: 'UPDATE users SET stripe_customer_id = ?, updated_at = ? WHERE id = ?',
          args: [customer.id, new Date().toISOString(), userId]
        });
      }

      // 3. 附加支付方式到客户
      await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customer.id,
      });

      // 4. 创建订阅
      const subscription = await this.stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: plan.stripePriceId }],
        default_payment_method: paymentMethodId,
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          userId: userId,
          planId: planId,
          source: 'mindful_mood_app'
        }
      });

      return {
        success: true,
        subscription,
        clientSecret: (subscription.latest_invoice as Stripe.Invoice)?.payment_intent?.client_secret
      };

    } catch (error) {
      console.error('[PaymentService] Create VIP subscription error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create subscription'
      };
    }
  }

  /**
   * 处理VIP购买 (一次性支付)
   */
  async processVipPurchase(
    userId: string,
    planId: string,
    paymentMethodId: string
  ): Promise<PurchaseResult> {
    try {
      // 1. 验证用户和计划
      const userResult = await executeQuery({
        sql: 'SELECT * FROM users WHERE id = ?',
        args: [userId]
      });

      if (!userResult.rows || userResult.rows.length === 0) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const plans = await this.getVipPlans();
      const plan = plans.data?.find(p => p.id === planId);

      if (!plan) {
        return {
          success: false,
          error: 'Invalid plan'
        };
      }

      // 2. 创建支付交易记录
      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      await executeQuery({
        sql: `
          INSERT INTO payment_transactions (
            id, user_id, transaction_type, status, amount, currency,
            payment_provider, provider_payment_method_id, description,
            initiated_at, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        args: [
          transactionId,
          userId,
          'subscription',
          'pending',
          plan.price,
          plan.currency,
          'stripe',
          paymentMethodId,
          `VIP ${plan.name} subscription`,
          new Date().toISOString(),
          new Date().toISOString(),
          new Date().toISOString()
        ]
      });

      // 3. 处理 Stripe 支付
      const paymentResult = await this.createPaymentIntent(
        plan.price,
        plan.currency,
        paymentMethodId,
        transactionId,
        `VIP ${plan.name} subscription for user ${userId}`
      );

      if (paymentResult.success && paymentResult.paymentIntent) {
        // 4. 支付成功，更新用户VIP状态
        const expiresAt = this.calculateVipExpiryDate(plan.duration);

        await batchStatements([
          {
            sql: 'UPDATE users SET is_vip = ?, vip_expires_at = ?, updated_at = ? WHERE id = ?',
            args: [true, expiresAt.toISOString(), new Date().toISOString(), userId]
          },
          {
            sql: `
              UPDATE payment_transactions
              SET status = ?, completed_at = ?, provider_transaction_id = ?, updated_at = ?
              WHERE id = ?
            `,
            args: [
              'completed',
              new Date().toISOString(),
              paymentResult.paymentIntent.id,
              new Date().toISOString(),
              transactionId
            ]
          },
          {
            sql: `
              INSERT INTO user_subscription_history (
                id, user_id, subscription_type, status, started_at, expires_at,
                auto_renew, payment_method, transaction_id, amount, currency, created_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `,
            args: [
              `sub_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
              userId,
              planId,
              'active',
              new Date().toISOString(),
              expiresAt.toISOString(),
              false, // 一次性支付，不自动续费
              'stripe',
              transactionId,
              plan.price,
              plan.currency,
              new Date().toISOString()
            ]
          }
        ]);

        // 5. 解锁VIP皮肤
        await this.unlockVipSkins(userId, transactionId);

        return {
          success: true,
          transactionId,
          paymentIntentId: paymentResult.paymentIntent.id
        };
      } else {
        // 支付失败，更新交易状态
        await executeQuery({
          sql: 'UPDATE payment_transactions SET status = ?, failed_at = ?, updated_at = ? WHERE id = ?',
          args: ['failed', new Date().toISOString(), new Date().toISOString(), transactionId]
        });

        return {
          success: false,
          error: paymentResult.error || 'Payment failed'
        };
      }
    } catch (error) {
      console.error('[PaymentService] VIP purchase error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'VIP purchase failed'
      };
    }
  }

  /**
   * 处理皮肤购买
   */
  async processSkinPurchase(
    userId: string,
    skinId: string,
    paymentMethodId: string
  ): Promise<PurchaseResult> {
    try {
      // 1. 验证皮肤和价格
      const skinResult = await executeQuery({
        sql: 'SELECT * FROM skins WHERE id = ? AND is_premium = 1',
        args: [skinId]
      });

      if (!skinResult.rows || skinResult.rows.length === 0) {
        return {
          success: false,
          error: 'Skin not found or not purchasable'
        };
      }

      const skin = skinResult.rows[0];
      const skinConfig = JSON.parse(skin.config || '{}');
      const price = skinConfig.price || 4.99; // 默认价格

      // 2. 检查是否已经拥有
      const existingUnlock = await executeQuery({
        sql: 'SELECT id FROM user_skin_unlocks WHERE user_id = ? AND skin_id = ?',
        args: [userId, skinId]
      });

      if (existingUnlock.rows && existingUnlock.rows.length > 0) {
        return {
          success: false,
          error: 'Skin already owned'
        };
      }

      // 3. 创建交易记录
      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      await executeQuery({
        sql: `
          INSERT INTO payment_transactions (
            id, user_id, transaction_type, status, amount, currency,
            payment_provider, provider_payment_method_id, description,
            initiated_at, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        args: [
          transactionId,
          userId,
          'skin_purchase',
          'pending',
          price,
          'USD',
          'stripe',
          paymentMethodId,
          `Skin purchase: ${skin.name}`,
          new Date().toISOString(),
          new Date().toISOString(),
          new Date().toISOString()
        ]
      });

      // 4. 处理 Stripe 支付
      const paymentResult = await this.createPaymentIntent(
        price,
        'USD',
        paymentMethodId,
        transactionId,
        `Skin purchase: ${skin.name} for user ${userId}`
      );

      if (paymentResult.success && paymentResult.paymentIntent) {
        // 5. 支付成功，解锁皮肤
        await batchStatements([
          {
            sql: `
              INSERT INTO user_skin_unlocks (
                id, user_id, skin_id, unlock_method, unlocked_at,
                transaction_id, sync_status
              ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `,
            args: [
              `unlock_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
              userId,
              skinId,
              'purchase',
              new Date().toISOString(),
              transactionId,
              'synced'
            ]
          },
          {
            sql: `
              UPDATE payment_transactions
              SET status = ?, completed_at = ?, provider_transaction_id = ?, updated_at = ?
              WHERE id = ?
            `,
            args: [
              'completed',
              new Date().toISOString(),
              paymentResult.paymentIntent.id,
              new Date().toISOString(),
              transactionId
            ]
          }
        ]);

        return {
          success: true,
          transactionId
        };
      } else {
        // 支付失败
        await executeQuery({
          sql: 'UPDATE payment_transactions SET status = ?, failed_at = ?, updated_at = ? WHERE id = ?',
          args: ['failed', new Date().toISOString(), new Date().toISOString(), transactionId]
        });

        return paymentResult;
      }
    } catch (error) {
      console.error('[PaymentService] Skin purchase error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Skin purchase failed'
      };
    }
  }

  /**
   * 处理表情集购买
   */
  async processEmojiSetPurchase(
    userId: string,
    emojiSetId: string,
    paymentMethodId: string
  ): Promise<PurchaseResult> {
    try {
      // 1. 验证表情集和价格
      const emojiSetResult = await executeQuery({
        sql: 'SELECT * FROM emoji_sets WHERE id = ? AND price > 0',
        args: [emojiSetId]
      });

      if (!emojiSetResult.rows || emojiSetResult.rows.length === 0) {
        return {
          success: false,
          error: 'Emoji set not found or not purchasable'
        };
      }

      const emojiSet = emojiSetResult.rows[0];
      const price = emojiSet.price || 2.99; // 默认价格

      // 2. 检查是否已经拥有
      const existingUnlock = await executeQuery({
        sql: 'SELECT id FROM user_emoji_set_unlocks WHERE user_id = ? AND emoji_set_id = ?',
        args: [userId, emojiSetId]
      });

      if (existingUnlock.rows && existingUnlock.rows.length > 0) {
        return {
          success: false,
          error: 'Emoji set already owned'
        };
      }

      // 3. 创建交易记录
      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      await executeQuery({
        sql: `
          INSERT INTO payment_transactions (
            id, user_id, transaction_type, status, amount, currency,
            payment_provider, provider_payment_method_id, description,
            initiated_at, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        args: [
          transactionId,
          userId,
          'emoji_set_purchase',
          'pending',
          price,
          'USD',
          'stripe',
          paymentMethodId,
          `Emoji set purchase: ${emojiSet.name}`,
          new Date().toISOString(),
          new Date().toISOString(),
          new Date().toISOString()
        ]
      });

      // 4. 处理 Stripe 支付
      const paymentResult = await this.createPaymentIntent(
        price,
        'USD',
        paymentMethodId,
        transactionId,
        `Emoji set purchase: ${emojiSet.name} for user ${userId}`
      );

      if (paymentResult.success && paymentResult.paymentIntent) {
        // 5. 支付成功，解锁表情集
        await batchStatements([
          {
            sql: `
              INSERT INTO user_emoji_set_unlocks (
                id, user_id, emoji_set_id, unlock_method, unlocked_at,
                transaction_id, sync_status
              ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `,
            args: [
              `unlock_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
              userId,
              emojiSetId,
              'purchase',
              new Date().toISOString(),
              transactionId,
              'synced'
            ]
          },
          {
            sql: `
              UPDATE payment_transactions
              SET status = ?, completed_at = ?, provider_transaction_id = ?, updated_at = ?
              WHERE id = ?
            `,
            args: [
              'completed',
              new Date().toISOString(),
              paymentResult.paymentIntent.id,
              new Date().toISOString(),
              transactionId
            ]
          }
        ]);

        return {
          success: true,
          transactionId
        };
      } else {
        // 支付失败
        await executeQuery({
          sql: 'UPDATE payment_transactions SET status = ?, failed_at = ?, updated_at = ? WHERE id = ?',
          args: ['failed', new Date().toISOString(), new Date().toISOString(), transactionId]
        });

        return paymentResult;
      }
    } catch (error) {
      console.error('[PaymentService] Emoji set purchase error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Emoji set purchase failed'
      };
    }
  }

  /**
   * 创建 Stripe PaymentIntent (真实实现)
   */
  private async createPaymentIntent(
    amount: number,
    currency: string,
    paymentMethodId: string,
    transactionId: string,
    description: string
  ): Promise<PaymentIntentResult> {
    try {
      // 将金额转换为最小货币单位（美分）
      const amountInCents = Math.round(amount * 100);

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: amountInCents,
        currency: currency.toLowerCase(),
        payment_method: paymentMethodId,
        confirmation_method: 'manual',
        confirm: true,
        description,
        metadata: {
          transactionId,
          source: 'mindful_mood_app'
        },
        return_url: process.env.STRIPE_RETURN_URL || 'https://app.mindful-mood.com/payment/return'
      });

      if (paymentIntent.status === 'succeeded') {
        return {
          success: true,
          paymentIntent,
          clientSecret: paymentIntent.client_secret
        };
      } else if (paymentIntent.status === 'requires_action') {
        return {
          success: true,
          paymentIntent,
          clientSecret: paymentIntent.client_secret
        };
      } else {
        return {
          success: false,
          error: `Payment failed with status: ${paymentIntent.status}`
        };
      }
    } catch (error) {
      console.error('[PaymentService] Stripe PaymentIntent error:', error);

      if (error instanceof Stripe.errors.StripeCardError) {
        return {
          success: false,
          error: error.message
        };
      } else if (error instanceof Stripe.errors.StripeInvalidRequestError) {
        return {
          success: false,
          error: 'Invalid payment request'
        };
      } else {
        return {
          success: false,
          error: 'Payment processing failed'
        };
      }
    }
  }

  /**
   * 确认 PaymentIntent (用于需要额外认证的支付)
   */
  async confirmPaymentIntent(paymentIntentId: string): Promise<PaymentIntentResult> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId);

      return {
        success: paymentIntent.status === 'succeeded',
        paymentIntent,
        clientSecret: paymentIntent.client_secret
      };
    } catch (error) {
      console.error('[PaymentService] Confirm PaymentIntent error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to confirm payment'
      };
    }
  }

  /**
   * 计算VIP过期时间
   */
  private calculateVipExpiryDate(duration: 'monthly' | 'yearly'): Date {
    const now = new Date();

    if (duration === 'monthly') {
      now.setMonth(now.getMonth() + 1);
    } else {
      now.setFullYear(now.getFullYear() + 1);
    }

    return now;
  }

  /**
   * 处理 Stripe Webhook 事件
   */
  async handleWebhook(payload: string, signature: string): Promise<{ success: boolean; error?: string }> {
    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        this.config.webhookSecret
      );

      console.log(`[PaymentService] Received webhook: ${event.type}`);

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
          break;
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;
        default:
          console.log(`[PaymentService] Unhandled webhook event: ${event.type}`);
      }

      return { success: true };
    } catch (error) {
      console.error('[PaymentService] Webhook error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Webhook processing failed'
      };
    }
  }

  /**
   * 处理支付成功事件
   */
  private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      const transactionId = paymentIntent.metadata?.transactionId;
      if (!transactionId) {
        console.warn('[PaymentService] PaymentIntent succeeded but no transactionId in metadata');
        return;
      }

      await executeQuery({
        sql: `
          UPDATE payment_transactions
          SET status = ?, completed_at = ?, provider_transaction_id = ?, updated_at = ?
          WHERE id = ? AND status = 'pending'
        `,
        args: [
          'completed',
          new Date().toISOString(),
          paymentIntent.id,
          new Date().toISOString(),
          transactionId
        ]
      });

      console.log(`[PaymentService] Payment completed: ${transactionId}`);
    } catch (error) {
      console.error('[PaymentService] Error handling payment success:', error);
    }
  }

  /**
   * 处理支付失败事件
   */
  private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      const transactionId = paymentIntent.metadata?.transactionId;
      if (!transactionId) {
        console.warn('[PaymentService] PaymentIntent failed but no transactionId in metadata');
        return;
      }

      await executeQuery({
        sql: `
          UPDATE payment_transactions
          SET status = ?, failed_at = ?, provider_transaction_id = ?, updated_at = ?
          WHERE id = ? AND status = 'pending'
        `,
        args: [
          'failed',
          new Date().toISOString(),
          paymentIntent.id,
          new Date().toISOString(),
          transactionId
        ]
      });

      console.log(`[PaymentService] Payment failed: ${transactionId}`);
    } catch (error) {
      console.error('[PaymentService] Error handling payment failure:', error);
    }
  }

  /**
   * 处理订阅更新事件
   */
  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    try {
      const userId = subscription.metadata?.userId;
      if (!userId) {
        console.warn('[PaymentService] Subscription updated but no userId in metadata');
        return;
      }

      const isActive = subscription.status === 'active';
      const expiresAt = new Date(subscription.current_period_end * 1000);

      await executeQuery({
        sql: `
          UPDATE users
          SET is_vip = ?, vip_expires_at = ?, stripe_subscription_id = ?, updated_at = ?
          WHERE id = ?
        `,
        args: [
          isActive,
          expiresAt.toISOString(),
          subscription.id,
          new Date().toISOString(),
          userId
        ]
      });

      console.log(`[PaymentService] Subscription updated for user ${userId}: ${subscription.status}`);
    } catch (error) {
      console.error('[PaymentService] Error handling subscription update:', error);
    }
  }

  /**
   * 处理订阅删除事件
   */
  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    try {
      const userId = subscription.metadata?.userId;
      if (!userId) {
        console.warn('[PaymentService] Subscription deleted but no userId in metadata');
        return;
      }

      await executeQuery({
        sql: `
          UPDATE users
          SET is_vip = ?, vip_expires_at = ?, stripe_subscription_id = NULL, updated_at = ?
          WHERE id = ?
        `,
        args: [
          false,
          new Date().toISOString(), // 立即过期
          new Date().toISOString(),
          userId
        ]
      });

      console.log(`[PaymentService] Subscription cancelled for user ${userId}`);
    } catch (error) {
      console.error('[PaymentService] Error handling subscription deletion:', error);
    }
  }

  /**
   * 处理发票支付成功事件
   */
  private async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    console.log(`[PaymentService] Invoice payment succeeded: ${invoice.id}`);
    // 订阅续费成功，通常不需要额外处理，因为 subscription.updated 事件会处理
  }

  /**
   * 处理发票支付失败事件
   */
  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    console.log(`[PaymentService] Invoice payment failed: ${invoice.id}`);
    // 可以在这里发送通知给用户，提醒支付失败
  }

  /**
   * 解锁VIP皮肤
   */
  private async unlockVipSkins(userId: string, transactionId: string): Promise<void> {
    try {
      // 获取所有VIP皮肤
      const vipSkinsResult = await executeQuery({
        sql: 'SELECT id FROM skins WHERE is_premium = 1 AND JSON_EXTRACT(unlock_conditions, "$.type") = "vip"',
        args: []
      });

      if (vipSkinsResult.rows && vipSkinsResult.rows.length > 0) {
        const statements = vipSkinsResult.rows.map((skin: any) => ({
          sql: `
            INSERT OR IGNORE INTO user_skin_unlocks (
              id, user_id, skin_id, unlock_method, unlocked_at,
              transaction_id, sync_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `,
          args: [
            `unlock_${Date.now()}_${skin.id}_${Math.random().toString(36).substring(2, 9)}`,
            userId,
            skin.id,
            'vip',
            new Date().toISOString(),
            transactionId,
            'synced'
          ]
        }));

        await batchStatements(statements);
        console.log(`[PaymentService] Unlocked ${vipSkinsResult.rows.length} VIP skins for user ${userId}`);
      }
    } catch (error) {
      console.error('[PaymentService] Failed to unlock VIP skins:', error);
    }
  }
}
