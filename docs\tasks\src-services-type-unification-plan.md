# src/services 类型定义统一计划

## 概述

在 `src/services` 目录中发现了大量内嵌类型定义，需要统一到 Schema 架构中。

## 发现的内嵌类型定义

### 1. **已完成统一的文件**
- ✅ `MoodEntryService.ts` - `CreateMoodEntryInput`, `UpdateMoodEntryInput`
- ✅ `MoodTrackingService.ts` - `CompleteMoodEntryInput`, `MoodEntryWithSelections`, `MoodTrackingStats`
- ✅ `PaymentService.ts` (在线) - `PaymentResult`, `VipPurchaseData`, `SkinPurchaseData`, `EmojiSetPurchaseData`
- ✅ `ApiClientService.ts` - `SqlQueryConfig`, `BatchSqlConfig`
- ✅ `MoodEntryRepository.ts` - `CreateMoodEntryData`, `UpdateMoodEntryData`, `MoodEntryFilter`

### 2. **待统一的 Repository 文件**

#### EmotionRepository.ts
- `CreateEmotionData` 接口
- `UpdateEmotionData` 接口
- `EmotionFilter` 接口

#### UILabelRepository.ts
- `UILabelData` 接口
- `CreateUILabelData` 接口
- `UpdateUILabelData` 接口

#### EmotionDataSetRepository.ts
- `EmotionDataSetFilter` 接口

#### EmojiSetRepository.ts
- `EmojiSetData` 接口
- `CreateEmojiSetData` 接口

#### UserConfigRepository.ts
- `CreateUserConfigData` 接口
- `UpdateUserConfigData` 接口
- `UserConfigFilter` 接口

#### EmojiItemRepository.ts
- `EmojiItemData` 接口
- `CreateEmojiItemData` 接口
- `UpdateEmojiItemData` 接口

#### EmotionDataSetTierRepository.ts
- `UpdateEmotionDataSetTierData` 接口
- `EmotionDataSetTierFilter` 接口

#### EmotionSelectionRepository.ts
- `CreateEmotionSelectionData` 接口
- `UpdateEmotionSelectionData` 接口
- `EmotionSelectionFilter` 接口

#### TagRepository.ts
- `Tag` 接口
- `CreateTagData` 接口
- `UpdateTagData` 接口

#### SkinRepository.ts
- `SkinData` 接口
- `CreateSkinData` 接口
- `UpdateSkinData` 接口

#### MoodEntryTagRepository.ts
- `MoodEntryTagFilter` 接口

#### EmotionDataSetEmotionRepository.ts
- `EmotionDataSetEmotionFilter` 接口

### 3. **基础类型文件**

#### TranslatableRepository.ts
- `Translation` 接口
- `TranslatableEntity` 接口
- `TranslatableFilter` 接口

## 统一策略

### 架构原则
- **`base.ts`**: 持久化的数据库实体类型（对应数据库表结构）
- **`api.ts`**: 服务层的输入输出类型（如 `XxxInput`、`XxxFilter`、`XxxData` 等）

### 分类规则
1. **实体类型** → `base.ts`（如果尚未定义）
2. **Repository 数据类型** → `api.ts`（`CreateXxxData`, `UpdateXxxData`）
3. **过滤器类型** → `api.ts`（`XxxFilter`）
4. **服务输入类型** → `api.ts`（`XxxInput`）
5. **基础接口类型** → `api.ts`（`Translation`, `TranslatableEntity` 等）

## 执行计划

### 阶段 1: 添加缺失的 Schema 定义
1. 在 `api.ts` 中添加所有 Repository 数据类型的 Schema
2. 在 `api.ts` 中添加所有过滤器类型的 Schema
3. 在 `api.ts` 中添加基础接口类型的 Schema

### 阶段 2: 更新 Repository 文件
1. 移除内嵌类型定义
2. 导入统一的类型定义
3. 确保类型兼容性

### 阶段 3: 更新导出文件
1. 更新 `src/types/schema/index.ts`
2. 更新 `src/services/index.ts`

### 阶段 4: 验证和测试
1. 检查 TypeScript 编译错误
2. 运行相关测试
3. 确保类型一致性

## 注意事项

1. **向后兼容性**: 确保现有代码不会因类型更改而破坏
2. **命名一致性**: 保持与现有 Schema 命名约定一致
3. **字段映射**: 注意数据库字段名与 TypeScript 属性名的差异
4. **可选字段**: 正确处理可选字段和默认值
5. **日期类型**: 统一处理 Date 和 string 类型的日期字段

## 预期收益

1. **类型一致性**: 确保整个应用使用统一的类型定义
2. **维护性**: 集中管理类型定义，减少重复代码
3. **类型安全**: 通过 Zod Schema 提供运行时类型验证
4. **开发体验**: 更好的 TypeScript 智能提示和错误检查
5. **可扩展性**: 统一的架构便于未来添加新的类型定义
