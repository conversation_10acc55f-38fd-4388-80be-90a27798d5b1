# Schema Alignment Summary

本文档总结了根据 `public/seeds/schema` 数据库结构更新 `src/types` 类型定义的工作。

## 📋 更新概览

### 1. 新增的 VIP 和 Unlock 系统类型

#### 基础枚举类型
- `VipTierSchema`: VIP 等级 ('basic', 'premium', 'enterprise')
- `SubscriptionStatusSchema`: 订阅状态 ('active', 'cancelled', 'expired', 'refunded', 'pending')
- `BillingCycleSchema`: 计费周期 ('monthly', 'yearly', 'lifetime')
- `PaymentMethodSchema`: 支付方式 ('stripe', 'paypal', 'apple_pay', 'google_pay', 'wechat_pay', 'alipay')
- `SubscriptionSourceSchema`: 订阅来源 ('web', 'mobile', 'promotion', 'admin')
- `UnlockMethodSchema`: 解锁方式 ('purchase', 'vip', 'achievement', 'free', 'promotion')
- `VipFeatureTypeSchema`: VIP 功能类型 ('unlock', 'limit', 'access', 'customization')
- `TranslatorTypeSchema`: 翻译者类型 ('human', 'ai', 'community')
- `TagSourceSchema`: 标签来源 ('user', 'ai_generated', 'system_suggested')
- `TagCategorySchema`: 标签分类 ('emotion', 'context', 'activity', 'time')

#### 数据表对应的 Schema
- `UserSkinUnlockSchema`: 用户皮肤解锁记录
- `UserEmojiSetUnlockSchema`: 用户表情集解锁记录
- `UserSubscriptionHistorySchema`: 用户订阅历史
- `VipPlanSchema`: VIP 计划定义
- `VipFeatureSchema`: VIP 功能定义

### 2. 更新的标签系统类型

#### 增强的标签 Schema
- `TagSchema`: 增加了 `category`, `color`, `icon`, `is_system`, `usage_count` 等字段
- `TagTranslationSchema`: 标签翻译支持
- `QuizResultTagSchema`: Quiz 结果标签关联
- `MoodEntryTagSchema`: 心情记录标签关联（向后兼容）

### 3. 新增的 API Schema

#### VIP 和 Unlock 相关 API
- `GetUserUnlocksInputSchema`: 获取用户解锁内容输入
- `UserUnlocksResponseSchema`: 用户解锁内容响应
- `UnlockContentInputSchema`: 解锁内容输入
- `GetSubscriptionHistoryInputSchema`: 获取订阅历史输入
- `SubscriptionHistoryResponseSchema`: 订阅历史响应

#### 增强的 VIP 状态 API
- `VipStatusSchema`: 增加了 `unlockedEmojiSets` 字段

## 📁 文件更新列表

### 1. `src/types/schema/base.ts`
- ✅ 新增 VIP 相关枚举类型
- ✅ 新增 VIP 和 Unlock 系统 Schema 定义
- ✅ 更新标签系统 Schema
- ✅ 新增类型导出

### 2. `src/types/schema/api.ts`
- ✅ 新增 VIP 和 Unlock 相关 API Schema
- ✅ 更新 VIP 状态相关 Schema
- ✅ 新增类型导出

### 3. `src/types/schema/index.ts`
- ✅ 导出新增的 Schema 和类型
- ✅ 更新导出列表

## 🗄️ 数据库表对应关系

| 数据库表 | TypeScript Schema | 说明 |
|---------|------------------|------|
| `tags` | `TagSchema` | 标签主表 |
| `tag_translations` | `TagTranslationSchema` | 标签翻译表 |
| `quiz_result_tags` | `QuizResultTagSchema` | Quiz结果标签关联表 |
| `mood_entry_tags` | `MoodEntryTagSchema` | 心情记录标签关联表 |
| `user_skin_unlocks` | `UserSkinUnlockSchema` | 用户皮肤解锁表 |
| `user_emoji_set_unlocks` | `UserEmojiSetUnlockSchema` | 用户表情集解锁表 |
| `user_subscription_history` | `UserSubscriptionHistorySchema` | 用户订阅历史表 |
| `vip_plans` | `VipPlanSchema` | VIP计划表 |
| `vip_features` | `VipFeatureSchema` | VIP功能表 |

## 🔧 使用示例

### 1. 标签系统
```typescript
import { Tag, TagTranslation, QuizResultTag } from '@/types/schema';

// 创建标签
const tag: Tag = {
  id: 'tag_work',
  name: '工作',
  description: '与工作相关的情绪和体验',
  category: 'context',
  color: '#4A90E2',
  icon: 'briefcase',
  is_system: true,
  is_active: true,
  usage_count: 0,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  created_by: 'system'
};
```

### 2. VIP 系统
```typescript
import { VipPlan, UserSubscriptionHistory } from '@/types/schema';

// VIP 计划
const vipPlan: VipPlan = {
  id: 'vip_premium',
  name: 'Premium VIP',
  tier: 'premium',
  price: 99.99,
  currency: 'USD',
  billing_cycle: 'yearly',
  features: JSON.stringify(['unlimited_skins', 'premium_emoji_sets']),
  is_active: true,
  // ...其他字段
};
```

### 3. Unlock 系统
```typescript
import { UserSkinUnlock, UserEmojiSetUnlock } from '@/types/schema';

// 皮肤解锁
const skinUnlock: UserSkinUnlock = {
  id: 'unlock_001',
  user_id: 'user_123',
  skin_id: 'skin_premium',
  unlock_method: 'vip',
  unlocked_at: new Date().toISOString(),
  sync_status: 'synced',
  // ...其他字段
};
```

## ✅ 验证清单

- [x] 所有数据库表都有对应的 TypeScript Schema
- [x] 字段类型与数据库定义一致
- [x] 约束条件正确映射到 Zod 验证
- [x] 枚举值与数据库 CHECK 约束一致
- [x] 外键关系在类型中正确表示
- [x] API Schema 与数据库 Schema 对齐
- [x] 所有新类型都正确导出
- [x] 向后兼容性保持

## 🚀 下一步

1. **服务层更新**: 更新相关的服务类以使用新的类型定义
2. **API 路由更新**: 确保 tRPC 路由使用正确的 Schema
3. **数据库迁移**: 确保数据库结构与类型定义同步
4. **测试更新**: 更新单元测试以验证新的类型定义
5. **文档更新**: 更新 API 文档以反映新的类型结构

## 📝 注意事项

1. **向后兼容**: 保留了原有的 `MoodEntryTagSchema` 以确保向后兼容
2. **类型安全**: 所有新类型都使用 Zod 进行运行时验证
3. **数据一致性**: 枚举值与数据库约束保持一致
4. **扩展性**: 新的类型结构支持未来的功能扩展
