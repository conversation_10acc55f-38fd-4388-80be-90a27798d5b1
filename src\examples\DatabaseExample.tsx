import type React from 'react';
import { useEffect, useState } from 'react';
import { useSQLiteDB } from '../lib/useSqLiteWithSingleton';

/**
 * Example component demonstrating the use of the useSQLiteDB hook with singleton pattern
 */
const DatabaseExample: React.FC = () => {
  const { dbConnection, isDatabaseInitialised } = useSQLiteDB();
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!isDatabaseInitialised || !dbConnection.current) {
        console.log('[DatabaseExample] Database not initialized yet. Waiting...');
        return;
      }

      console.log('[DatabaseExample] Database initialized. Fetching data...');
      setLoading(true);
      setError(null);

      try {
        // Example query to fetch data
        const result = await dbConnection.current.query('SELECT * FROM emotions LIMIT 10');

        if (result.values) {
          setData(result.values);
          console.log('[DatabaseExample] Data fetched successfully:', result.values);
        } else {
          setData([]);
          console.log('[DatabaseExample] No data returned from query.');
        }
      } catch (err) {
        console.error('[DatabaseExample] Error fetching data:', err);
        setError(`Error fetching data: ${err instanceof Error ? err.message : String(err)}`);
        setData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isDatabaseInitialised, dbConnection]);

  return (
    <div className="database-example">
      <h2>Database Example</h2>
      <p>Database Status: {isDatabaseInitialised ? 'Initialized' : 'Not Initialized'}</p>

      {loading && isDatabaseInitialised && <p>Loading data...</p>}

      {error && <p className="error">{error}</p>}

      {!loading && data.length > 0 && (
        <div>
          <h3>Emotions Data:</h3>
          <ul>
            {data.map((item, index) => (
              <li key={index}>
                {item.name} - {item.description}
              </li>
            ))}
          </ul>
        </div>
      )}

      {!loading && data.length === 0 && !error && isDatabaseInitialised && <p>No data found.</p>}
    </div>
  );
};

export default DatabaseExample;
