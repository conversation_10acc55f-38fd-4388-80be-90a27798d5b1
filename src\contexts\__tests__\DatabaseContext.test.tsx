/**
 * DatabaseContext 测试
 * 测试数据库上下文功能
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { DatabaseProvider, useDatabaseContext, platform } from '../DatabaseContext';

// Mock Capacitor
vi.mock('@capacitor/core', () => ({
  Capacitor: {
    getPlatform: vi.fn(() => 'web')
  }
}));

// Mock DatabaseService
const mockDatabaseService = {
  getInstance: vi.fn(),
  initializeApp: vi.fn(),
  getConnection: vi.fn(),
  isInitCompleted: {
    subscribe: vi.fn(),
    unsubscribe: vi.fn()
  }
};

vi.mock('../../services/base/DatabaseService', () => ({
  DatabaseService: {
    getInstance: vi.fn(() => mockDatabaseService)
  }
}));

// Mock Services
vi.mock('../../services', () => ({
  Services: {
    setDatabase: vi.fn(),
    healthCheck: vi.fn()
  }
}));

// Test component that uses the context
const TestComponent: React.FC = () => {
  const { databaseService, isInitialized, isInitializing, error, platform } = useDatabaseContext();
  
  return (
    <div>
      <div data-testid="platform">{platform}</div>
      <div data-testid="initialized">{isInitialized.toString()}</div>
      <div data-testid="initializing">{isInitializing.toString()}</div>
      <div data-testid="error">{error || 'no-error'}</div>
      <div data-testid="service">{databaseService ? 'service-available' : 'no-service'}</div>
    </div>
  );
};

describe('DatabaseContext', () => {
  let mockSubscription: any;
  let mockServices: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Get the mocked Services
    const { Services } = await import('../../services');
    mockServices = Services;

    // Setup mock subscription
    mockSubscription = {
      unsubscribe: vi.fn()
    };

    mockDatabaseService.isInitCompleted.subscribe.mockReturnValue(mockSubscription);
    mockDatabaseService.initializeApp.mockResolvedValue(true);
    mockDatabaseService.getConnection.mockResolvedValue({ connection: 'mock' });
    mockServices.healthCheck.mockResolvedValue({ status: 'ok' });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should provide platform information', () => {
    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    expect(screen.getByTestId('platform')).toHaveTextContent('web');
  });

  it('should initialize with correct default state', () => {
    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    expect(screen.getByTestId('initialized')).toHaveTextContent('false');
    expect(screen.getByTestId('initializing')).toHaveTextContent('true');
    expect(screen.getByTestId('error')).toHaveTextContent('no-error');
    expect(screen.getByTestId('service')).toHaveTextContent('service-available');
  });

  it('should handle successful database initialization', async () => {
    // Mock successful initialization
    mockDatabaseService.initializeApp.mockResolvedValue(true);
    mockDatabaseService.getConnection.mockResolvedValue({ connection: 'mock' });
    mockServices.healthCheck.mockResolvedValue({ status: 'ok' });

    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    // Wait for initialization to complete
    await waitFor(() => {
      expect(mockDatabaseService.initializeApp).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(mockServices.setDatabase).toHaveBeenCalledWith({ connection: 'mock' });
    });

    await waitFor(() => {
      expect(mockServices.healthCheck).toHaveBeenCalled();
    });
  });

  it('should handle database initialization failure', async () => {
    // Mock initialization failure
    mockDatabaseService.initializeApp.mockResolvedValue(false);

    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Database initialization failed');
    });

    expect(screen.getByTestId('initialized')).toHaveTextContent('false');
    expect(screen.getByTestId('initializing')).toHaveTextContent('false');
  });

  it('should handle database initialization error', async () => {
    // Mock initialization error
    const errorMessage = 'Connection failed';
    mockDatabaseService.initializeApp.mockRejectedValue(new Error(errorMessage));

    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent(errorMessage);
    });

    expect(screen.getByTestId('initialized')).toHaveTextContent('false');
    expect(screen.getByTestId('initializing')).toHaveTextContent('false');
  });

  it('should handle health check failure gracefully', async () => {
    // Mock successful init but failed health check
    mockDatabaseService.initializeApp.mockResolvedValue(true);
    mockDatabaseService.getConnection.mockResolvedValue({ connection: 'mock' });
    mockServices.healthCheck.mockRejectedValue(new Error('Health check failed'));

    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    await waitFor(() => {
      expect(mockServices.healthCheck).toHaveBeenCalled();
    });

    // Should still be initialized despite health check failure
    await waitFor(() => {
      expect(screen.getByTestId('initialized')).toHaveTextContent('true');
    });
  });

  it('should respond to database service initialization status changes', async () => {
    let subscriptionCallback: (initialized: boolean) => void;

    mockDatabaseService.isInitCompleted.subscribe.mockImplementation((callback) => {
      subscriptionCallback = callback;
      return mockSubscription;
    });

    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    // Initially not initialized
    expect(screen.getByTestId('initialized')).toHaveTextContent('false');

    // Simulate initialization completion
    if (subscriptionCallback!) {
      subscriptionCallback(true);
    }

    await waitFor(() => {
      expect(screen.getByTestId('initialized')).toHaveTextContent('true');
      expect(screen.getByTestId('initializing')).toHaveTextContent('false');
      expect(screen.getByTestId('error')).toHaveTextContent('no-error');
    });
  });

  it('should unsubscribe on unmount', () => {
    const { unmount } = render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    unmount();

    expect(mockSubscription.unsubscribe).toHaveBeenCalled();
  });

  it('should throw error when useDatabaseContext is used outside provider', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useDatabaseContext must be used within a DatabaseProvider');

    consoleSpy.mockRestore();
  });

  it('should provide the same database service instance', () => {
    const TestMultipleComponents: React.FC = () => {
      const context1 = useDatabaseContext();
      const context2 = useDatabaseContext();
      
      return (
        <div>
          <div data-testid="same-service">
            {context1.databaseService === context2.databaseService ? 'same' : 'different'}
          </div>
        </div>
      );
    };

    render(
      <DatabaseProvider>
        <TestMultipleComponents />
      </DatabaseProvider>
    );

    expect(screen.getByTestId('same-service')).toHaveTextContent('same');
  });

  it('should handle connection retrieval failure', async () => {
    // Mock successful init but failed connection retrieval
    mockDatabaseService.initializeApp.mockResolvedValue(true);
    mockDatabaseService.getConnection.mockRejectedValue(new Error('Connection retrieval failed'));

    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Connection retrieval failed');
    });

    expect(screen.getByTestId('initialized')).toHaveTextContent('false');
  });

  it('should handle generic error without message', async () => {
    // Mock initialization error without message
    mockDatabaseService.initializeApp.mockRejectedValue({});

    render(
      <DatabaseProvider>
        <TestComponent />
      </DatabaseProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Database initialization failed');
    });
  });
});
