/**
 * 气泡视图组件
 * 使用气泡布局显示情绪
 */

import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import type { Emotion, ContentDisplayMode, SkinConfig, BubbleLayout } from '@/types';
import { motion, useAnimation } from 'framer-motion';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';

interface BubbleViewProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  layout: BubbleLayout;
  onBack?: () => void;
  selectedPath?: any;
}

/**
 * 气泡视图组件
 * 使用气泡布局显示情绪
 */
export const BubbleView: React.FC<BubbleViewProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  layout,
  onBack,
  selectedPath,
}) => {
  const { theme } = useTheme();
  // 计算是否为深色模式
  const isDarkMode =
    theme === 'dark' ||
    (theme === 'system' &&
      typeof window !== 'undefined' &&
      window.matchMedia('(prefers-color-scheme: dark)').matches);
  const { t } = useLanguage();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();

  // 创建一个安全的 skinConfig 对象，确保所有必要属性存在
  const safeSkinConfig: SkinConfig = {
    // 必需的支持配置
    supported_content_modes: skinConfig?.supported_content_modes || ['emoji', 'text', 'mixed'],
    supported_view_types: skinConfig?.supported_view_types || ['bubble'],
    supported_render_engines: skinConfig?.supported_render_engines || ['CSS', 'SVG'],

    // 字体配置
    fonts: skinConfig?.fonts || {
      family: 'Arial, sans-serif',
      size: { small: 12, medium: 14, large: 18 },
      weight: { normal: 400, bold: 700 },
    },

    // 颜色配置
    colors: skinConfig?.colors || {
      text: isDarkMode ? '#FFFFFF' : '#000000',
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: isDarkMode ? '#1f2937' : '#FFFFFF',
    },

    // 效果配置
    effects: skinConfig?.effects || {
      shadows: true,
      animations: true,
      borderRadius: 8,
      opacity: 1,
    },

    // 视图配置
    view_configs: skinConfig?.viewConfigs || {
      bubble: {
        bubble_size: 70,
        bubble_spacing: 10,
        bubble_shape: 'circle',
        bubble_hover_effect: 'scale',
        bubble_hover_scale: 1.15,
        layout: 'circle',
        floating_animation: true,
      },
    },
  };

  // 记录调试信息
  if (!skinConfig) {
    console.warn('BubbleView: skinConfig is undefined, using default config');
  } else {
    if (!skinConfig.fonts) console.warn('BubbleView: skinConfig.fonts is undefined, using default');
    if (!skinConfig.colors)
      console.warn('BubbleView: skinConfig.colors is undefined, using default');
    if (!skinConfig.effects)
      console.warn('BubbleView: skinConfig.effects is undefined, using default');
  }

  // 气泡配置
  const bubbleConfig = safeSkinConfig.viewConfigs?.bubble || {};
  const bubbleSize = bubbleConfig.bubbleSize || 70;
  const bubbleSpacing = bubbleConfig.bubbleSpacing || 10;

  // 响应式布局
  useEffect(() => {
    if (containerRef.current) {
      const updateSize = () => {
        const { offsetWidth, offsetHeight } = containerRef.current!;
        setContainerSize({
          width: offsetWidth,
          height: Math.max(offsetHeight, offsetWidth),
        });
      };

      updateSize();
      window.addEventListener('resize', updateSize);

      return () => {
        window.removeEventListener('resize', updateSize);
      };
    }
  }, []);

  // 初始动画
  useEffect(() => {
    controls.start({
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, staggerChildren: 0.1 },
    });
  }, [controls]);

  // 获取情绪内容
  const getEmotionContent = (emotion: Emotion) => {
    const text = emotion.name;
    const emoji = emotion.emoji;

    switch (contentDisplayMode) {
      case 'text':
        return { text, emoji: '' };
      case 'emoji':
        return { text: '', emoji };
      default:
        return { text, emoji };
    }
  };

  // 获取情绪颜色
  const getEmotionColor = (emotion: Emotion, index: number) => {
    // 如果情绪有自定义颜色，优先使用
    if (emotion.color) {
      return emotion.color;
    }

    // 否则根据层级和索引生成颜色
    const hue = (index * 360) / emotions.length;
    const saturation = 70;
    const lightness = isDarkMode ? 50 : 60;

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  // 处理气泡悬停
  const handleBubbleHover = (index: number) => {
    setHoveredIndex(index);
  };

  // 处理气泡离开
  const handleBubbleLeave = () => {
    setHoveredIndex(null);
  };

  // 计算气泡位置 - 圆形布局
  const calculateCirclePosition = (index: number, total: number) => {
    const { width, height } = containerSize;
    const centerX = width / 2;
    const centerY = height / 2;

    // 如果只有一个情绪，放在中心
    if (total === 1) {
      return { x: centerX, y: centerY };
    }

    // 计算半径，确保气泡不会超出容器
    const maxRadius = Math.min(width, height) / 2 - bubbleSize;

    // 计算角度和位置
    const angle = (index / total) * 2 * Math.PI;
    // 根据气泡数量和间距调整半径
    const spacingFactor = 1 + bubbleSpacing / 100; // 将间距转换为半径调整因子
    const radius = total <= 3 ? maxRadius * 0.6 * spacingFactor : maxRadius * 0.8 * spacingFactor;

    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);

    return { x, y };
  };

  // 计算气泡位置 - 随机布局
  const calculateRandomPosition = (index: number, total: number) => {
    const { width, height } = containerSize;

    // 使用伪随机位置，但确保不会重叠
    const seed = index * 137.5 + total;
    const randX = Math.sin(seed) * 0.5 + 0.5;
    const randY = Math.cos(seed) * 0.5 + 0.5;

    // 确保气泡不会超出容器
    const margin = bubbleSize / 2 + 10;
    const x = margin + randX * (width - 2 * margin);
    const y = margin + randY * (height - 2 * margin);

    return { x, y };
  };

  // 计算气泡位置 - 力导向布局
  const calculateForcePosition = (index: number, total: number) => {
    const { width, height } = containerSize;
    const centerX = width / 2;
    const centerY = height / 2;

    // 如果只有一个情绪，放在中心
    if (total === 1) {
      return { x: centerX, y: centerY };
    }

    // 使用力导向算法的简化版本
    // 每个节点都有一个基于索引的固定角度，但半径会根据其他节点的位置调整
    const angle = (index / total) * 2 * Math.PI;

    // 基础半径，考虑气泡间距
    const spacingFactor = 1 + bubbleSpacing / 100; // 将间距转换为半径调整因子
    const baseRadius = Math.min(width, height) * 0.35 * spacingFactor;

    // 根据索引调整半径，使节点分布更均匀
    // 这里使用简单的正弦波调整，模拟力导向效果
    const radiusAdjustment = Math.sin(index * 2.5) * 0.2 + 0.8;
    const radius = baseRadius * radiusAdjustment;

    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);

    return { x, y };
  };

  // 计算气泡位置 - 集群布局
  const calculateClusterPosition = (index: number, total: number) => {
    const { width, height } = containerSize;
    const centerX = width / 2;
    const centerY = height / 2;

    // 如果只有一个情绪，放在中心
    if (total === 1) {
      return { x: centerX, y: centerY };
    }

    // 集群参数
    // 将情绪分成几个集群
    const clustersCount = Math.min(3, Math.ceil(total / 3));
    const clusterIndex = Math.floor(index / (total / clustersCount));
    const indexInCluster = index % Math.ceil(total / clustersCount);
    const itemsInCluster = Math.ceil(total / clustersCount);

    // 计算集群中心位置
    const clusterAngle = (clusterIndex / clustersCount) * 2 * Math.PI;
    const clusterRadius = Math.min(width, height) * 0.25;
    const clusterX = centerX + clusterRadius * Math.cos(clusterAngle);
    const clusterY = centerY + clusterRadius * Math.sin(clusterAngle);

    // 计算项目在集群中的位置
    const itemAngle = (indexInCluster / itemsInCluster) * 2 * Math.PI;
    // 使用 bubbleSpacing 来调整气泡之间的间距
    const itemRadius = bubbleSize * 1.2 + bubbleSpacing;

    const x = clusterX + itemRadius * Math.cos(itemAngle);
    const y = clusterY + itemRadius * Math.sin(itemAngle);

    return { x, y };
  };

  // 根据布局类型计算气泡位置
  const calculateBubblePosition = (index: number, total: number) => {
    switch (layout) {
      case 'random':
        return calculateRandomPosition(index, total);
      case 'force':
        return calculateForcePosition(index, total);
      case 'cluster':
        return calculateClusterPosition(index, total);
      default:
        return calculateCirclePosition(index, total);
    }
  };

  // 渲染气泡
  const renderBubble = (emotion: Emotion, index: number) => {
    const { text, emoji } = getEmotionContent(emotion);
    const backgroundColor = getEmotionColor(emotion, index);
    const { x, y } = calculateBubblePosition(index, emotions.length);

    // 计算文本颜色（根据背景色自动调整）
    const getTextColor = (bgColor: string) => {
      // 简单的亮度计算，用于决定文本颜色
      const r = Number.parseInt(bgColor.slice(1, 3), 16);
      const g = Number.parseInt(bgColor.slice(3, 5), 16);
      const b = Number.parseInt(bgColor.slice(5, 7), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;

      return brightness > 128 ? '#000000' : '#FFFFFF';
    };

    const textColor = emotion.color?.startsWith('#')
      ? getTextColor(emotion.color)
      : safeSkinConfig.colors?.text || '#000000';

    // 气泡动画变体
    const bubbleVariants = {
      hidden: { opacity: 0, scale: 0 },
      visible: {
        opacity: 1,
        scale: 1,
        transition: {
          type: 'spring',
          stiffness: 260,
          damping: 20,
          delay: index * 0.05,
        },
      },
      hover: {
        scale: 1.15,
        boxShadow: '0 10px 20px rgba(0,0,0,0.2)',
        transition: {
          type: 'spring',
          stiffness: 400,
          damping: 10,
        },
      },
    };

    return (
      <motion.div
        key={emotion.id}
        className="bubble"
        style={{
          position: 'absolute',
          left: `${x - bubbleSize / 2}px`,
          top: `${y - bubbleSize / 2}px`,
          width: `${bubbleSize}px`,
          height: `${bubbleSize}px`,
          backgroundColor,
          borderRadius: '50%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          boxShadow: safeSkinConfig.effects?.shadows ? '0 4px 8px rgba(0, 0, 0, 0.2)' : 'none',
          color: textColor,
          padding: '4px',
          zIndex: hoveredIndex === index ? 10 : 1,
        }}
        initial="hidden"
        animate="visible"
        whileHover="hover"
        variants={bubbleVariants}
        onClick={() => onSelect(emotion)}
        onMouseEnter={() => handleBubbleHover(index)}
        onMouseLeave={handleBubbleLeave}
      >
        {/* 内容 */}
        <div className="bubble-content">
          {emoji && (
            <div
              className="emoji"
              style={{
                fontSize: `${safeSkinConfig.fonts?.size?.medium || 14}px`,
                marginBottom: text && contentDisplayMode === 'textEmoji' ? '2px' : 0,
              }}
            >
              {emoji}
            </div>
          )}

          {text && (
            <div
              className="text"
              style={{
                fontSize: `${safeSkinConfig.fonts?.size?.small || 12}px`,
                fontWeight: safeSkinConfig.fonts?.weight?.normal || 400,
                fontFamily: safeSkinConfig.fonts?.family || 'Arial, sans-serif',
                textAlign: 'center',
                // 如果文本太长，显示省略号
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '100%',
              }}
            >
              {text}
            </div>
          )}
        </div>
      </motion.div>
    );
  };

  return (
    <div
      className="bubble-view"
      ref={containerRef}
      style={{
        position: 'relative',
        width: '100%',
        height: '400px',
        maxWidth: '800px',
        margin: '0 auto',
        padding: '16px',
      }}
    >
      {/* 标题 */}
      <h3
        className="tier-title"
        style={{
          textAlign: 'center',
          marginBottom: '16px',
          fontSize: `${safeSkinConfig.fonts?.size?.large || 18}px`,
          fontWeight: safeSkinConfig.fonts?.weight?.bold || 700,
          fontFamily: safeSkinConfig.fonts?.family || 'Arial, sans-serif',
          color: safeSkinConfig.colors?.text || '#000000',
        }}
      >
        {t(`tier_level.${tierLevel}`, { fallback: `第${tierLevel}级情绪` })}
      </h3>

      {/* 气泡容器 */}
      <div
        className="bubbles-container"
        style={{
          position: 'relative',
          width: '100%',
          height: 'calc(100% - 50px)',
        }}
      >
        {containerSize.width > 0 && emotions.map((emotion, index) => renderBubble(emotion, index))}
      </div>
    </div>
  );
};
