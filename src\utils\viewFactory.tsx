/**
 * 视图工厂
 * 用于创建不同类型的视图
 *
 * 视图工厂是一个静态类，用于创建不同类型的视图组件，包括轮盘、卡片、气泡和星系视图。
 * 它支持多种内容显示模式，包括文本、表情、文本+表情和动画表情。
 *
 * 主要功能：
 * 1. 基于用户配置创建视图
 * 2. 创建不同类型的视图（轮盘、卡片、气泡、星系）
 * 3. 支持不同的内容显示模式（文本、表情、文本+表情、动画表情）
 * 4. 支持不同的轮盘实现（D3、SVG、React Three Fiber）
 * 5. 支持不同的布局选项（网格、列表、集群、螺旋等）
 */

import {
  ViewType,
  ContentDisplayMode,
  RenderEngine,
  View,
  SkinConfig,
  UserConfig,
  CardLayout,
  BubbleLayout,
  GalaxyLayout,
  ListLayout
} from '@/types';
import { EmotionView } from '@/views/interfaces/EmotionView';
import { D3WheelView } from '@/views/implementations/wheels/D3WheelView';
import { SVGWheelView } from '@/views/implementations/wheels/SVGWheelView';
import { R3FWheelView } from '@/views/implementations/wheels/R3FWheelView';
import { CanvasWheelView } from '@/views/implementations/wheels/CanvasWheelView';
import { WebGLWheelView } from '@/views/implementations/wheels/WebGLWheelView';
import { WebGPUWheelView } from '@/views/implementations/wheels/WebGPUWheelView';
import { CardView } from '@/views/implementations/cards/CardView';
import { BubbleView } from '@/views/implementations/bubbles/BubbleView';
import { GalaxyView } from '@/views/implementations/galaxy/GalaxyView';
import { ListView } from '@/views/implementations/lists/ListView';

/**
 * 视图工厂类
 */
export class ViewFactory {
  /**
   * 创建视图（基于UserConfig）
   *
   * 此方法根据用户配置创建视图，是创建视图的首选方法。
   * 它会根据用户配置中的视图类型、内容显示模式、皮肤等信息创建相应的视图。
   *
   * @param userConfig 用户配置，包含视图类型、内容显示模式、皮肤等信息
   * @param skin 皮肤对象，包含皮肤配置
   * @param viewType 视图类型（可选，默认使用用户首选视图类型）
   * @returns 视图对象
   */
  static createViewFromUserConfig(
    userConfig: UserConfig,
    skin: Skin,
    viewType?: ViewType
  ): View {
    // 使用提供的视图类型或用户首选视图类型
    let type = viewType || userConfig.preferred_view_type;

    if (!skin?.config) {
      throw new Error(`Invalid skin provided`);
    }

    // 解析皮肤配置
    let skinConfig: SkinConfig;
    try {
      skinConfig = typeof skin.config === 'string' ? JSON.parse(skin.config) : skin.config;
    } catch (error) {
      console.error(`[ViewFactory] Failed to parse skin config for ${skin.id}:`, error);
      throw new Error(`Invalid skin config for ${skin.id}`);
    }

    // 检查皮肤是否支持请求的视图类型
    if (!this.isSupportedViewType(type, skinConfig)) {
      // 如果皮肤不支持请求的视图类型，回退到皮肤支持的第一个视图类型
      const fallbackType = this.getFallbackViewType(skinConfig);
      console.warn(`Skin ${skin.id} does not support view type ${type}, falling back to ${fallbackType} view`);
      type = fallbackType;
    }

    // 获取内容显示模式，并检查兼容性
    let contentDisplayMode = userConfig.content_display_mode_preferences?.[type] || 'textEmoji';

    // 检查皮肤是否支持请求的内容显示模式
    if (!this.isSupportedContentMode(contentDisplayMode, type, skinConfig)) {
      // 如果皮肤不支持请求的内容显示模式，回退到皮肤支持的第一个内容显示模式
      const fallbackMode = this.getFallbackContentMode(type, skinConfig);
      console.warn(`Skin ${skin.id} does not support content display mode ${contentDisplayMode} for view type ${type}, falling back to ${fallbackMode}`);
      contentDisplayMode = fallbackMode;
    }

    // 获取渲染引擎，并检查兼容性
    let renderEngine = userConfig.render_engine_preferences?.[type] || (type === 'wheel' ? 'D3' : 'D3');

    // 检查皮肤是否支持请求的渲染引擎
    if (!this.isSupportedRenderEngine(renderEngine, skinConfig)) {
      // 如果皮肤不支持请求的渲染引擎，回退到皮肤支持的第一个渲染引擎
      const fallbackEngine = this.getFallbackRenderEngine(skinConfig);
      console.warn(`Skin ${skin.id} does not support render engine ${renderEngine} for view type ${type}, falling back to ${fallbackEngine}`);
      renderEngine = fallbackEngine;
    }

    // 获取布局
    const layout = userConfig.layout_preferences?.[type] || this.getDefaultLayoutForViewType(type);

    // 根据视图类型创建视图
    switch (type) {
      case 'wheel':
        return this.createWheel(
          renderEngine as RenderEngine,
          contentDisplayMode,
          skinConfig
        );
      case 'card':
        return this.createCard(
          contentDisplayMode,
          skinConfig,
          layout as CardLayout
        );
      case 'bubble':
        return this.createBubble(
          contentDisplayMode,
          skinConfig,
          layout as BubbleLayout
        );
      case 'galaxy':
        return this.createGalaxy(
          contentDisplayMode,
          skinConfig,
          layout as GalaxyLayout
        );
      case 'list':
        return this.createList(
          contentDisplayMode,
          skinConfig,
          layout as ListLayout
        );
      case 'grid':
        // 当网格视图实现后，取消注释下面的代码
        // return this.createGrid(
        //   contentDisplayMode,
        //   skinConfig,
        //   layout as GridLayout
        // );
        console.warn('Grid view not yet implemented, falling back to wheel view');
        return this.createWheel('D3', contentDisplayMode, skinConfig);
      case 'tree':
        // 当树状视图实现后，取消注释下面的代码
        // return this.createTree(
        //   contentDisplayMode,
        //   skinConfig,
        //   layout as TreeLayout
        // );
        console.warn('Tree view not yet implemented, falling back to wheel view');
        return this.createWheel('D3', contentDisplayMode, skinConfig);
      case 'flow':
        // 当流程图视图实现后，取消注释下面的代码
        // return this.createFlow(
        //   contentDisplayMode,
        //   skinConfig,
        //   layout as FlowLayout
        // );
        console.warn('Flow view not yet implemented, falling back to wheel view');
        return this.createWheel('D3', contentDisplayMode, skinConfig);
      case 'tagCloud':
        // 当标签云视图实现后，取消注释下面的代码
        // return this.createTagCloud(
        //   contentDisplayMode,
        //   skinConfig,
        //   layout as TagCloudLayout
        // );
        console.warn('TagCloud view not yet implemented, falling back to wheel view');
        return this.createWheel('D3', contentDisplayMode, skinConfig);
      default:
        console.warn(`Unknown view type: ${type}, falling back to wheel view`);
        return this.createWheel('D3', contentDisplayMode, skinConfig);
    }
  }

  /**
   * deprecated
   * 创建视图（传统方法，保留向后兼容性）
   * @param type 视图类型（wheel、card、bubble、galaxy）
   * @param contentDisplayMode 内容显示模式（text、emoji、textEmoji、animatedEmoji）
   * @param skinConfig 皮肤配置
   * @param options 其他选项，可包含：
   *   - implementation: 轮盘实现类型（D3、SVG、R3F），仅当 type 为 'wheel' 时有效
   *   - layout: 布局类型，根据视图类型不同而不同
   * @returns 视图对象
   */
  static createView(
    type: ViewType,
    contentDisplayMode: ContentDisplayMode,
    skinConfig: SkinConfig,
    options?: any
  ): View {
    let view: EmotionView;

    switch (type) {
      case 'wheel':
        view = this.createWheel(
          options?.implementation || 'D3',
          contentDisplayMode,
          skinConfig
        ) as EmotionView;
        break;
      case 'card':
        view = this.createCard(
          contentDisplayMode,
          skinConfig,
          options?.layout || 'grid'
        ) as EmotionView;
        break;
      case 'bubble':
        view = this.createBubble(
          contentDisplayMode,
          skinConfig,
          options?.layout || 'cluster'
        ) as EmotionView;
        break;
      case 'galaxy':
        view = this.createGalaxy(
          contentDisplayMode,
          skinConfig,
          options?.layout || 'spiral'
        ) as EmotionView;
        break;
      case 'list':
        view = this.createList(
          contentDisplayMode,
          skinConfig,
          options?.layout || 'vertical'
        ) as EmotionView;
        break;
      default:
        view = this.createWheel('D3', contentDisplayMode, skinConfig) as EmotionView;
        break;
    }

    return {
      render: (emotions, tierLevel, onSelect, config) =>
        view.render(emotions, tierLevel, onSelect, config)
    };
  }

  /**
   * 创建轮盘视图
   * @param implementation 轮盘实现类型（D3、SVG、R3F、Canvas、CSS、WebGL、WebGPU）
   * @param contentDisplayMode 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
   * @param skinConfig 皮肤配置
   * @returns 轮盘视图对象
   */
  static createWheel(
    implementation: RenderEngine,
    contentDisplayMode: ContentDisplayMode,
    skinConfig: SkinConfig
  ): View {
    let wheelView: EmotionView;

    switch (implementation) {
      case 'D3':
        wheelView = new D3WheelView(contentDisplayMode, skinConfig);
        break;
      case 'SVG':
        wheelView = new SVGWheelView(contentDisplayMode, skinConfig);
        break;
      case 'R3F':
        wheelView = new R3FWheelView(contentDisplayMode, skinConfig);
        break;
      case 'Canvas':
        wheelView = new CanvasWheelView(contentDisplayMode, skinConfig);
        break;
      case 'CSS':
        console.warn('CSS wheel view not yet implemented, falling back to D3 wheel view');
        wheelView = new D3WheelView(contentDisplayMode, skinConfig);
        break;
      case 'WebGL':
        wheelView = new WebGLWheelView(contentDisplayMode, skinConfig);
        break;
      case 'WebGPU':
        wheelView = new WebGPUWheelView(contentDisplayMode, skinConfig);
        break;
      default:
        wheelView = new D3WheelView(contentDisplayMode, skinConfig);
        break;
    }

    return {
      render: (emotions, tierLevel, onSelect, config) =>
        wheelView.render(emotions, tierLevel, onSelect, config)
    };
  }

  /**
   * 创建卡片视图
   * @param contentDisplayMode 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
   * @param skinConfig 皮肤配置
   * @param layout 卡片布局（grid、list、masonry）
   * @returns 卡片视图对象
   */
  static createCard(
    contentDisplayMode: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: CardLayout = 'grid'
  ): View {
    const cardView = new CardView(contentDisplayMode, skinConfig, layout);

    return {
      render: (emotions, tierLevel, onSelect, config) =>
        cardView.render(emotions, tierLevel, onSelect, config)
    };
  }

  /**
   * 创建气泡视图
   * @param contentDisplayMode 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
   * @param skinConfig 皮肤配置
   * @param layout 气泡布局（cluster、force、random、circle）
   * @returns 气泡视图对象
   */
  static createBubble(
    contentDisplayMode: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: BubbleLayout = 'cluster'
  ): View {
    const bubbleView = new BubbleView(contentDisplayMode, skinConfig, layout);

    return {
      render: (emotions, tierLevel, onSelect, config) =>
        bubbleView.render(emotions, tierLevel, onSelect, config)
    };
  }

  /**
   * 获取视图类型的默认布局
   * @param viewType 视图类型
   * @returns 默认布局
   */
  static getDefaultLayoutForViewType(viewType: ViewType): string {
    switch (viewType) {
      case 'card':
        return 'grid';
      case 'bubble':
        return 'cluster';
      case 'galaxy':
        return 'spiral';
      case 'list':
        return 'vertical';
      case 'grid':
        return 'square';
      case 'tree':
        return 'vertical';
      case 'flow':
        return 'dagre';
      case 'tagCloud':
        return 'circle';
      default:
        return '';
    }
  }

  /**
   * 创建星系视图
   * @param contentDisplayMode 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
   * @param skinConfig 皮肤配置
   * @param layout 星系布局（spiral、orbital、nebula）
   * @returns 星系视图对象
   */
  static createGalaxy(
    contentDisplayMode: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: GalaxyLayout = 'spiral'
  ): View {
    const galaxyView = new GalaxyView(contentDisplayMode, skinConfig, layout);

    return {
      render: (emotions, tierLevel, onSelect, config) =>
        galaxyView.render(emotions, tierLevel, onSelect, config)
    };
  }

  /**
   * 创建列表视图
   * @param contentDisplayMode 内容显示模式（text、emoji、textEmoji、animatedEmoji、image）
   * @param skinConfig 皮肤配置
   * @param layout 列表布局（vertical、horizontal、accordion、tabs）
   * @returns 列表视图对象
   */
  static createList(
    contentDisplayMode: ContentDisplayMode,
    skinConfig: SkinConfig,
    layout: ListLayout = 'vertical'
  ): View {
    const listView = new ListView(contentDisplayMode, skinConfig, layout);

    return {
      render: (emotions, tierLevel, onSelect, config) =>
        listView.render(emotions, tierLevel, onSelect, config)
    };
  }

  /**
   * 检查皮肤是否支持指定的视图类型
   * @param viewType 视图类型
   * @param skinConfig 皮肤配置
   * @returns 是否支持
   */
  static isSupportedViewType(viewType: ViewType, skinConfig: SkinConfig): boolean {
    return skinConfig.supported_view_types?.includes(viewType) || false;
  }

  /**
   * 获取皮肤支持的回退视图类型
   * @param skinConfig 皮肤配置
   * @returns 回退视图类型
   */
  static getFallbackViewType(skinConfig: SkinConfig): ViewType {
    // 默认回退到轮盘视图，如果皮肤支持的话
    if (skinConfig.supported_view_types?.includes('wheel')) {
      return 'wheel';
    }
    // 否则回退到皮肤支持的第一个视图类型
    return skinConfig.supported_view_types?.[0] || 'wheel';
  }

  /**
   * 检查皮肤是否支持指定的内容显示模式
   * @param contentDisplayMode 内容显示模式
   * @param viewType 视图类型
   * @param skinConfig 皮肤配置
   * @returns 是否支持
   */
  static isSupportedContentMode(
    contentDisplayMode: ContentDisplayMode,
    viewType: ViewType,
    skinConfig: SkinConfig
  ): boolean {
    return skinConfig.supported_content_modes?.includes(contentDisplayMode) || false;
  }

  /**
   * 获取皮肤支持的回退内容显示模式
   * @param viewType 视图类型
   * @param skinConfig 皮肤配置
   * @returns 回退内容显示模式
   */
  static getFallbackContentMode(viewType: ViewType, skinConfig: SkinConfig): ContentDisplayMode {
    // 默认回退到文本和表情模式，如果皮肤支持的话
    if (skinConfig.supported_content_modes?.includes('textEmoji')) {
      return 'textEmoji';
    }
    // 否则回退到皮肤支持的第一个内容显示模式
    return skinConfig.supported_content_modes?.[0] || 'textEmoji';
  }

  /**
   * 检查皮肤是否支持指定的渲染引擎
   * @param renderEngine 渲染引擎
   * @param skinConfig 皮肤配置
   * @returns 是否支持
   */
  static isSupportedRenderEngine(
    renderEngine: RenderEngine,
    skinConfig: SkinConfig
  ): boolean {
    return skinConfig.supported_render_engines?.includes(renderEngine) || false;
  }

  /**
   * 获取皮肤支持的回退渲染引擎
   * @param skinConfig 皮肤配置
   * @returns 回退渲染引擎
   */
  static getFallbackRenderEngine(skinConfig: SkinConfig): RenderEngine {
    // 默认回退到D3渲染引擎，如果皮肤支持的话
    if (skinConfig.supported_render_engines?.includes('D3')) {
      return 'D3';
    }
    // 否则回退到皮肤支持的第一个渲染引擎
    return skinConfig.supported_render_engines?.[0] || 'D3';
  }
}
