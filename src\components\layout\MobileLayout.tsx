import { useLanguage } from '@/contexts/LanguageContext';
import type React from 'react';
import { useLocation } from 'react-router-dom';
import MobileHeader from './MobileHeader';
import MobileNavbar from './MobileNavbar';

interface MobileLayoutProps {
  children: React.ReactNode;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { t } = useLanguage();

  // Get title based on current route
  const getTitle = () => {
    const path = location.pathname;
    if (path === '/') return t('app.title');
    if (path === '/history') return t('history.title');
    if (path === '/analytics') return t('app.analytics');
    if (path === '/settings') return t('settings.title');
    if (path === '/quiz-settings') return 'Quiz系统设置';
    return t('app.title');
  };

  return (
    <div className="mobile-container bg-background flex flex-col">
      {/* <MobileHeader title={getTitle()} /> */}
      <main className="flex-1 overflow-auto px-4 pb-20">{children}</main>
      <MobileNavbar />
    </div>
  );
};

export default MobileLayout;
