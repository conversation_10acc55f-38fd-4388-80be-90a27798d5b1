import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useColorMode } from '@/contexts/ColorModeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useSkinManager } from '@/contexts/SkinContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import { Services } from '@/services';
import type { ContentDisplayMode, RenderEngine, ViewType, ColorMode, Skin } from '@/types';
import { Check, ChevronDown, Info } from 'lucide-react';
import React from 'react';
import { toast } from 'sonner';
import ColorModePreview from './ColorModePreview';
import ViewConfigOptions from './display/ViewConfigOptions';

/**
 * 显示选项组件
 * 用于在设置页面中选择显示选项，使用纵向布局
 */
const DisplayOptionsComponentVertical: React.FC = () => {
  const { t } = useLanguage();
  const { activeSkin, skins } = useSkinManager();
  const { colorMode, setColorMode } = useColorMode();
  const {
    userConfig,
    updateUserConfig,
    setViewSkinId,
    setViewContentDisplayMode,
    setViewRenderingEngine,
    setWheelViewRenderingEngine,
    getViewSkinId,
    getViewContentDisplayMode,
    getViewRenderingEngine,
    getViewLayout,
  } = useUserConfig();

  // 获取当前显示选项
  const displayOptions = {
    viewType: userConfig?.preferred_view_type || 'wheel',
    renderEngine:
      userConfig?.render_engine_preferences?.[userConfig?.preferred_view_type || 'wheel'] || 'D3',
    displayMode:
      userConfig?.content_display_mode_preferences?.[userConfig?.preferred_view_type || 'wheel'] ||
      'textEmoji',
    skinId: userConfig?.active_skin_id || 'default',
  };

  // 辅助函数：解析皮肤配置
  const parseSkinConfig = (skin: Skin) => {
    try {
      return skin.config ;
    } catch {
      return {};
    }
  };

  // 辅助函数：解析支持的渲染引擎
  const parse_supported_render_engines = (skin: Skin): RenderEngine[] => {
    try {
      return skin.config.supported_render_engines;
    } catch {
      return ['D3', 'SVG', 'R3F'];
    }
  };

  // 辅助函数：解析支持的视图类型
  const parse_supported_view_types = (skin: Skin): ViewType[] => {
    try {
      // 首先尝试从 config 中获取
      if (typeof skin.config === 'string') {
        const parsedConfig = JSON.parse(skin.config);
        return (parsedConfig.supported_view_types || ['wheel', 'card', 'bubble']) as ViewType[];
      } else if (skin.config?.supported_view_types) {
        return skin.config.supported_view_types as ViewType[];
      }
      // 回退到默认值
      return ['wheel', 'card', 'bubble'];
    } catch {
      return ['wheel', 'card', 'bubble'];
    }
  };

  // 辅助函数：解析支持的内容显示模式
  const parse_supported_content_modes = (skin: Skin): ContentDisplayMode[] => {
    try {
      // 首先尝试从 config 中获取
      if (typeof skin.config === 'string') {
        const parsedConfig = JSON.parse(skin.config);
        return (parsedConfig.supported_content_modes || ['text', 'emoji', 'textEmoji']) as ContentDisplayMode[];
      } else if (skin.config?.supported_content_modes) {
        return skin.config.supported_content_modes as ContentDisplayMode[];
      }
      // 回退到默认值
      return ['text', 'emoji', 'textEmoji'];
    } catch {
      return ['text', 'emoji', 'textEmoji'];
    }
  };

  // 当前皮肤和可用皮肤
  const currentSkin = activeSkin || skins[0];
  const availableSkins = skins;
  const unlockedSkins = skins.filter((skin) => skin.is_unlocked);

  // 处理视图类型变更
  const handleViewTypeChange = (type: ViewType) => {
    // 检查当前皮肤是否支持所选视图类型
    const skinConfig = parseSkinConfig(currentSkin);
    const currentSkinSupportsViewType =
      skinConfig.view_configs && !!skinConfig.view_configs[type];

    // 更新显示模式的函数
    const updateDisplayMode = (viewType: ViewType, renderEngine: string) => {
      const newSupportedModes = getNewSupportedDisplayModes(viewType, renderEngine);

      // 如果当前显示模式不在新支持的模式列表中，自动切换到支持的模式
      if (newSupportedModes.length > 0 && !newSupportedModes.includes(displayOptions.displayMode)) {
        const newMode = newSupportedModes[0];
        // 更新用户配置
        const currentPreferences = userConfig?.content_display_mode_preferences
          ? JSON.parse(userConfig.content_display_mode_preferences)
          : {};
        updateUserConfig({
          content_display_mode_preferences: JSON.stringify({
            ...currentPreferences,
            [viewType]: newMode,
          }),
        });
        setViewContentDisplayMode(viewType, newMode as any);
        toast.info(
          t('settings.display_mode_auto_changed', { defaultValue: '已自动切换到支持的显示模式' }),
          { duration: 3000 }
        );
      }
    };

    if (currentSkinSupportsViewType) {
      // 当前皮肤支持所选视图类型，直接更新用户配置
      updateUserConfig({ preferred_view_type: type });

      // 更新显示模式
      updateDisplayMode(type, displayOptions.renderEngine);
    } else {
      // 当前皮肤不支持所选视图类型，需要找到支持该视图类型的皮肤
      // 优先选择免费皮肤
      const freeSkins = availableSkins.filter((skin) => {
        const skinConfig = parseSkinConfig(skin);
        return (
          skin.is_unlocked &&
          skin.category === 'free' &&
          skinConfig.view_configs &&
          !!skinConfig.view_configs[type]
        );
      });

      if (freeSkins.length > 0) {
        // 找到支持所选视图类型的免费皮肤，选择第一个
        // 同时更新 UserConfig 中的首选视图类型和皮肤
        const currentViewTypeSkinIds = userConfig?.view_type_skin_ids
          ? JSON.parse(userConfig.view_type_skin_ids)
          : {};
        updateUserConfig({
          preferred_view_type: type,
          active_skin_id: freeSkins[0].id,
          view_type_skin_ids: JSON.stringify({
            ...currentViewTypeSkinIds,
            [type]: freeSkins[0].id,
          }),
        });

        // 更新视图特定的皮肤设置
        setViewSkinId(type, freeSkins[0].id);

        // 更新显示模式
        updateDisplayMode(type, displayOptions.renderEngine);

        // 通知用户皮肤已更改
        toast.info(
          t('settings.skin_changed_for_view_type', {
            defaultValue: `已为${t(`settings.view_type.${type}`, { defaultValue: type })}视图切换到${freeSkins[0].name}皮肤`,
            skinName: freeSkins[0].name,
            viewType: t(`settings.view_type.${type}`, { defaultValue: type }),
          }),
          { duration: 3000 }
        );
      } else {
        // 没有找到免费皮肤，查找任何支持该视图类型的已解锁皮肤
        const supportingSkins = availableSkins.filter((skin) => {
          const skinConfig = parseSkinConfig(skin);
          return (
            skin.is_unlocked &&
            skinConfig.view_configs &&
            !!skinConfig.view_configs[type]
          );
        });

        if (supportingSkins.length > 0) {
          // 找到支持所选视图类型的皮肤，选择第一个
          // 同时更新 UserConfig 中的首选视图类型和皮肤
          const currentViewTypeSkinIds = userConfig?.view_type_skin_ids
            ? JSON.parse(userConfig.view_type_skin_ids)
            : {};
          updateUserConfig({
            preferred_view_type: type,
            active_skin_id: supportingSkins[0].id,
            view_type_skin_ids: JSON.stringify({
              ...currentViewTypeSkinIds,
              [type]: supportingSkins[0].id,
            }),
          });

          // 更新视图特定的皮肤设置
          setViewSkinId(type, supportingSkins[0].id);

          // 更新显示模式
          updateDisplayMode(type, displayOptions.renderEngine);

          // 通知用户皮肤已更改
          toast.info(
            t('settings.skin_changed_for_view_type', {
              defaultValue: `已为${t(`settings.view_type.${type}`, { defaultValue: type })}视图切换到${supportingSkins[0].name}皮肤`,
              skinName: supportingSkins[0].name,
              viewType: t(`settings.view_type.${type}`, { defaultValue: type }),
            }),
            { duration: 3000 }
          );
        } else {
          // 没有找到支持所选视图类型的皮肤，仍然设置视图类型
          // 但通知用户没有合适的皮肤

          // 更新 UserConfig 中的首选视图类型
          updateUserConfig({ preferred_view_type: type });

          // 更新显示模式
          updateDisplayMode(type, displayOptions.renderEngine);

          toast.warning(
            t('settings.no_skin_supports_view_type', {
              defaultValue: `没有皮肤支持${t(`settings.view_type.${type}`, { defaultValue: type })}视图`,
              viewType: t(`settings.view_type.${type}`, { defaultValue: type }),
            }),
            { duration: 3000 }
          );
        }
      }
    }
  };

  // 处理渲染引擎变更
  const handleRenderEngineChange = (engine: RenderEngine) => {
    // 更新 UserConfig 中的渲染引擎设置
    const currentRenderEnginePreferences = userConfig?.render_engine_preferences
      ? JSON.parse(userConfig.render_engine_preferences)
      : {};
    updateUserConfig({
      render_engine_preferences: JSON.stringify({
        ...currentRenderEnginePreferences,
        [displayOptions.viewType]: engine,
      }),
    });

    setViewRenderingEngine(displayOptions.viewType as ViewType, engine as any);

    // 如果是轮盘视图，同时更新 UserConfig 中的轮盘实现（兼容旧版本）
    if (displayOptions.viewType === 'wheel') {
      setWheelViewRenderingEngine(engine as any);
    }

    // 检查当前皮肤是否支持该渲染引擎
    const currentSkinSupportedEngines = parse_supported_render_engines(currentSkin);
    if (!currentSkinSupportedEngines.includes(engine)) {
      // 查找支持该渲染引擎的皮肤
      const compatibleSkins = availableSkins.filter((skin) => {
        const supportedEngines = parse_supported_render_engines(skin);
        const supported_view_types = parse_supported_view_types(skin);
        return (
          skin.is_unlocked &&
          supportedEngines.includes(engine) &&
          supported_view_types.includes(displayOptions.viewType as ViewType)
        );
      });

      if (compatibleSkins.length > 0) {
        // 更新 UserConfig 中的活动皮肤
        const currentViewTypeSkinIds = userConfig?.view_type_skin_ids
          ? JSON.parse(userConfig.view_type_skin_ids)
          : {};
        updateUserConfig({
          active_skin_id: compatibleSkins[0].id,
          view_type_skin_ids: JSON.stringify({
            ...currentViewTypeSkinIds,
            [displayOptions.viewType]: compatibleSkins[0].id,
          }),
        });

        // 更新视图特定的皮肤设置
        setViewSkinId(displayOptions.viewType as ViewType, compatibleSkins[0].id);

        toast.info(t('settings.skin_auto_changed', { defaultValue: '已自动切换到兼容的皮肤' }), {
          duration: 3000,
        });
      }
    }

    // 获取新渲染引擎支持的显示模式
    const newSupportedModes = getNewSupportedDisplayModes(displayOptions.viewType as ViewType, engine);

    // 如果当前显示模式不在新支持的模式列表中，自动切换到支持的模式
    if (newSupportedModes.length > 0 && !newSupportedModes.includes(displayOptions.displayMode)) {
      const newMode = newSupportedModes[0];
      // 更新用户配置
      const currentPreferences = userConfig?.content_display_mode_preferences
        ? JSON.parse(userConfig.content_display_mode_preferences)
        : {};
      updateUserConfig({
        content_display_mode_preferences: JSON.stringify({
          ...currentPreferences,
          [displayOptions.viewType]: newMode,
        }),
      });
      setViewContentDisplayMode(displayOptions.viewType as ViewType, newMode as any);
      toast.info(
        t('settings.display_mode_auto_changed', { defaultValue: '已自动切换到支持的显示模式' }),
        { duration: 3000 }
      );
    }
  };

  // 获取新视图类型或渲染引擎支持的显示模式
  const getNewSupportedDisplayModes = (viewType: ViewType, renderEngine: string) => {
    // 确保支持的显示模式存在
    const skinSupportedModes = parse_supported_content_modes(currentSkin);

    // 对于轮盘视图
    if (viewType === 'wheel') {
      // 对于 D3 和 SVG 渲染引擎，支持所有显示模式
      if (['D3', 'SVG'].includes(renderEngine)) {
        // 直接返回所有支持的模式，不再过滤
        return ['text', 'emoji', 'textEmoji'];
      }
      // 对于 R3F 渲染引擎，目前只支持 textEmoji 和 emoji 显示模式
      if (renderEngine === 'R3F') {
        return ['textEmoji', 'emoji'];
      }
    }

    // 对于卡片视图，支持所有显示模式
    if (viewType === 'card') {
      return ['text', 'emoji', 'textEmoji'];
    }

    // 对于气泡视图，支持所有显示模式
    if (viewType === 'bubble') {
      return ['text', 'emoji', 'textEmoji'];
    }

    // 对于星系视图，目前只支持 textEmoji 显示模式
    if (viewType === 'galaxy') {
      return ['textEmoji'];
    }

    // 默认返回所有显示模式
    return ['text', 'emoji', 'textEmoji'];
  };

  // 处理显示模式变更
  const handleDisplayModeChange = (mode: ContentDisplayMode) => {
    // 更新用户配置
    const currentPreferences = userConfig?.content_display_mode_preferences
      ? JSON.parse(userConfig.content_display_mode_preferences)
      : {};
    updateUserConfig({
      content_display_mode_preferences: JSON.stringify({
        ...currentPreferences,
        [displayOptions.viewType]: mode,
      }),
    });

    // 同时更新 UserConfig 中的视图特定显示模式
    setViewContentDisplayMode(displayOptions.viewType as ViewType, mode as any);

    // 显示成功提示
    toast.success(t('settings.display_mode_changed', { defaultValue: '显示模式已更改' }), {
      duration: 3000,
    });

    // 如果当前模式不在支持的模式列表中，自动切换到支持的模式
    if (!supportedDisplayModes.includes(mode) && supportedDisplayModes.length > 0) {
      const newMode = supportedDisplayModes[0];
      // 更新用户配置
      const currentPreferences = userConfig?.content_display_mode_preferences
        ? JSON.parse(userConfig.content_display_mode_preferences)
        : {};
      updateUserConfig({
        content_display_mode_preferences: JSON.stringify({
          ...currentPreferences,
          [displayOptions.viewType]: newMode,
        }),
      });
      setViewContentDisplayMode(displayOptions.viewType as ViewType, newMode as any);
      toast.info(
        t('settings.display_mode_auto_changed', { defaultValue: '已自动切换到支持的显示模式' }),
        { duration: 3000 }
      );
    }
  };

  // 处理配置变更
  const handleConfigChange = async (key: string, value: any) => {
    // 更新用户配置
    updateUserConfig({ [key]: value });

    // 同时更新皮肤配置
    if (displayOptions.viewType && currentSkin) {
      try {
        // 更新皮肤的视图配置
        const skinConfig = parseSkinConfig(currentSkin);
        const updatedViewConfig = {
          ...skinConfig.view_configs?.[displayOptions.viewType],
          [key]: value,
        };

        // 更新完整的皮肤配置
        const updatedSkinConfig = {
          ...skinConfig,
          view_configs: {
            ...skinConfig.view_configs,
            [displayOptions.viewType]: updatedViewConfig,
          },
        };

        // 使用服务更新皮肤
        const skinService = await Services.skin();
        await skinService.update(currentSkin.id, {
          config: updatedSkinConfig,
        });
      } catch (error) {
        console.error('Failed to update skin config:', error);
      }
    }
  };

  // 处理颜色模式变更
  const handleColorModeChange = async (mode: ColorMode) => {
    try {
      await setColorMode(mode);
      toast.success(t('settings.color_mode_changed', { defaultValue: '颜色模式已更改' }), {
        duration: 3000,
      });
    } catch (error) {
      console.error('Failed to change color mode:', error);
      toast.error(t('settings.color_mode_change_failed', { defaultValue: '颜色模式更改失败' }), {
        duration: 3000,
      });
    }
  };

  // 获取当前视图类型和渲染引擎支持的显示模式
  const supportedDisplayModes = React.useMemo(() => {
    return getNewSupportedDisplayModes(displayOptions.viewType as ViewType, displayOptions.renderEngine);
  }, [
    displayOptions.viewType,
    displayOptions.renderEngine,
    currentSkin.config.supported_content_modes,
  ]);

  // 获取当前视图类型支持的渲染引擎
  const supported_render_engines = React.useMemo(() => {
    // 确保支持的渲染引擎存在
    const skinSupportedEngines = parse_supported_render_engines(currentSkin);

    // 对于轮盘视图，支持 D3、SVG 和 R3F
    if (displayOptions.viewType === 'wheel') {
      // 直接返回所有支持的引擎，不再过滤
      return ['D3', 'SVG', 'R3F'];
    }

    // 对于卡片视图，目前只支持 D3
    if (displayOptions.viewType === 'card') {
      return ['D3'];
    }

    // 对于气泡视图，目前只支持 D3
    if (displayOptions.viewType === 'bubble') {
      return ['D3'];
    }

    // 对于星系视图，支持 D3 和 R3F
    if (displayOptions.viewType === 'galaxy') {
      return ['D3', 'R3F'];
    }

    // 默认返回当前皮肤支持的所有渲染引擎
    return skinSupportedEngines;
  }, [currentSkin.config.supported_render_engines, displayOptions.viewType]);

  // 所有可用的视图类型，确保与 displayTypes.ts 中的 ViewType 定义一致
  const allViewTypes: ViewType[] = [
    'wheel',
    'card',
    'bubble',
    'list',
    'grid',
    'galaxy',
    'tree',
    'flow',
    'tagCloud',
  ];

  return (
    <div className="space-y-6">
      {/* 视图类型部分 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">{t('settings.view_type')}</h3>
        <p className="text-sm text-muted-foreground">{t('settings.view_type.description')}</p>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {/* 基础视图类型 */}
          <div className="col-span-3 mb-2">
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              {t('settings.view_type.basic')}
            </h3>
          </div>

          {/* 轮盘视图 */}
          {allViewTypes.includes('wheel') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'wheel' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('wheel')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.wheel')}</span>
                      {displayOptions.viewType === 'wheel' && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.wheel.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* 卡片视图 */}
          {allViewTypes.includes('card') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'card' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('card')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.card')}</span>
                      {displayOptions.viewType === 'card' && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.card.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* 气泡视图 */}
          {allViewTypes.includes('bubble') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'bubble' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('bubble')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.bubble')}</span>
                      {displayOptions.viewType === 'bubble' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.bubble.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* 列表和网格视图 */}
          <div className="col-span-3 mt-4 mb-2">
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              {t('settings.view_type.list_grid')}
            </h3>
          </div>

          {/* 列表视图 */}
          {allViewTypes.includes('list') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'list' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('list')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.list')}</span>
                      {displayOptions.viewType === 'list' && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.list.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* 网格视图 */}
          {allViewTypes.includes('grid') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'grid' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('grid')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.grid')}</span>
                      {displayOptions.viewType === 'grid' && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.grid.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* 标签云视图 */}
          {allViewTypes.includes('tagCloud') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'tagCloud' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('tagCloud')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.tagCloud')}</span>
                      {displayOptions.viewType === 'tagCloud' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.tagCloud.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* 高级视图类型 */}
          <div className="col-span-3 mt-4 mb-2">
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              {t('settings.view_type.advanced')}
            </h3>
          </div>

          {/* 星系视图 */}
          {allViewTypes.includes('galaxy') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'galaxy' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('galaxy')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.galaxy')}</span>
                      {displayOptions.viewType === 'galaxy' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.galaxy.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* 树状视图 */}
          {allViewTypes.includes('tree') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'tree' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('tree')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.tree')}</span>
                      {displayOptions.viewType === 'tree' && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.tree.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* 流程图视图 */}
          {allViewTypes.includes('flow') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.viewType === 'flow' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleViewTypeChange('flow')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.view_type.flow')}</span>
                      {displayOptions.viewType === 'flow' && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.view_type.flow.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>

      {/* 渲染引擎部分 */}
      <div className="space-y-4 mt-8">
        <h3 className="text-lg font-medium">{t('settings.render_engine')}</h3>
        <p className="text-sm text-muted-foreground">{t('settings.render_engine.description')}</p>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {supported_render_engines.includes('D3') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.renderEngine === 'D3' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleRenderEngineChange('D3')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.render_engine.d3')}</span>
                      {displayOptions.renderEngine === 'D3' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.render_engine.d3.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {supported_render_engines.includes('SVG') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.renderEngine === 'SVG' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleRenderEngineChange('SVG')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.render_engine.svg')}</span>
                      {displayOptions.renderEngine === 'SVG' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.render_engine.svg.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {supported_render_engines.includes('R3F') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.renderEngine === 'R3F' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleRenderEngineChange('R3F')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.render_engine.r3f')}</span>
                      {displayOptions.renderEngine === 'R3F' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('settings.render_engine.r3f.description')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>

      {/* 显示模式部分 */}
      <div className="space-y-4 mt-8">
        <h3 className="text-lg font-medium">
          {t('settings.display_mode', { defaultValue: '显示模式' })}
        </h3>
        <p className="text-sm text-muted-foreground">
          {t('settings.display_mode.description', { defaultValue: '选择内容的显示方式' })}
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {supportedDisplayModes.includes('text') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.displayMode === 'text' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleDisplayModeChange('text')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.display_mode.text', { defaultValue: '仅文本' })}</span>
                      {displayOptions.displayMode === 'text' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {t('settings.display_mode.text.description', {
                      defaultValue: '只显示情绪的文本名称',
                    })}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {supportedDisplayModes.includes('emoji') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.displayMode === 'emoji' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleDisplayModeChange('emoji')}
                  >
                    <div className="flex items-center w-full">
                      <span>{t('settings.display_mode.emoji', { defaultValue: '仅表情' })}</span>
                      {displayOptions.displayMode === 'emoji' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {t('settings.display_mode.emoji.description', {
                      defaultValue: '只显示情绪的表情符号',
                    })}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {supportedDisplayModes.includes('textEmoji') && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={displayOptions.displayMode === 'textEmoji' ? 'default' : 'outline'}
                    className="justify-start"
                    onClick={() => handleDisplayModeChange('textEmoji')}
                  >
                    <div className="flex items-center w-full">
                      <span>
                        {t('settings.display_mode.textEmoji', { defaultValue: '文本和表情' })}
                      </span>
                      {displayOptions.displayMode === 'textEmoji' && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {t('settings.display_mode.textEmoji.description', {
                      defaultValue: '同时显示情绪的文本名称和表情符号',
                    })}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>

      {/* 如果没有可用的显示模式，显示提示信息 */}
      {supportedDisplayModes.length === 0 && (
        <div className="p-4 border rounded-md bg-muted/20 text-center">
          <p className="text-sm text-muted-foreground">
            {t('settings.display_mode.not_available', {
              defaultValue: '当前视图类型或渲染引擎不支持任何显示模式',
            })}
          </p>
        </div>
      )}

      {/* 颜色模式部分 */}
      <div className="space-y-4 mt-8">
        <h3 className="text-lg font-medium">
          {t('settings.color_mode', { defaultValue: '颜色模式' })}
        </h3>
        <p className="text-sm text-muted-foreground">
          {t('settings.color_mode.description', { defaultValue: '选择情绪显示的颜色模式' })}
        </p>

        <div className="grid grid-cols-2 gap-4">
          <Button
            variant={colorMode === 'warm' ? 'default' : 'outline'}
            className="justify-start p-4 h-auto"
            onClick={() => handleColorModeChange('warm')}
          >
            <div className="flex flex-col items-center w-full">
              <ColorModePreview colorMode="warm" size="md" />
              <div className="flex items-center w-full mt-2">
                <span>{t('settings.color_mode.warm', { defaultValue: '暖色系' })}</span>
                {colorMode === 'warm' && <Check className="ml-auto h-4 w-4" />}
              </div>
            </div>
          </Button>
          <Button
            variant={colorMode === 'cool' ? 'default' : 'outline'}
            className="justify-start p-4 h-auto"
            onClick={() => handleColorModeChange('cool')}
          >
            <div className="flex flex-col items-center w-full">
              <ColorModePreview colorMode="cool" size="md" />
              <div className="flex items-center w-full mt-2">
                <span>{t('settings.color_mode.cool', { defaultValue: '冷色系' })}</span>
                {colorMode === 'cool' && <Check className="ml-auto h-4 w-4" />}
              </div>
            </div>
          </Button>
          <Button
            variant={colorMode === 'mixed' ? 'default' : 'outline'}
            className="justify-start p-4 h-auto"
            onClick={() => handleColorModeChange('mixed')}
          >
            <div className="flex flex-col items-center w-full">
              <ColorModePreview colorMode="mixed" size="md" />
              <div className="flex items-center w-full mt-2">
                <span>{t('settings.color_mode.mixed', { defaultValue: '混合色系' })}</span>
                {colorMode === 'mixed' && <Check className="ml-auto h-4 w-4" />}
              </div>
            </div>
          </Button>
          <Button
            variant={colorMode === 'game' ? 'default' : 'outline'}
            className="justify-start p-4 h-auto"
            onClick={() => handleColorModeChange('game')}
          >
            <div className="flex flex-col items-center w-full">
              <ColorModePreview colorMode="game" size="md" />
              <div className="flex items-center w-full mt-2">
                <span>{t('settings.color_mode.game', { defaultValue: '游戏风格' })}</span>
                {colorMode === 'game' && <Check className="ml-auto h-4 w-4" />}
              </div>
            </div>
          </Button>
        </div>
      </div>

      {/* 视图配置部分 */}
      <div className="space-y-4 mt-8">
        <h3 className="text-lg font-medium">{t('settings.view_config')}</h3>
        <ViewConfigOptions />
      </div>
    </div>
  );
};

export default DisplayOptionsComponentVertical;
