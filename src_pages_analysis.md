### `src/pages/NewHome.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useUserConfig()`: To get user-specific configurations, particularly `userConfig?.id`.
    *   `useNewHomeData(userConfig?.id)`:
        *   Fetches `defaultQuiz` and `recommendedQuizPacks`.
        *   Provides `loadDefaultQuiz(packId)` to load specific quiz data.
        *   Provides `getQuizPacksByCategory(category)` to fetch quiz packs filtered by category.
    *   `useQuizSession()`:
        *   Provides `startQuizSession(packId, userId)` to initialize a new quiz session.
        *   Provides `submitAnswer(answerData)` to record quiz answers.
*   **State Management:**
    *   `selectedQuizPackId`: Manages the currently selected quiz pack.
    *   `availableQuizPacks`: Stores the list of all available quiz packs.
    *   `currentTierIndex`: Tracks the current tier/question level in a multi-tier quiz.
    *   `selectedEmotions`: Stores user's selections/answers in the multi-tier quiz.
    *   `selectedPath`: Stores the full path of selected options for quiz navigation.
    *   `isLoading`, `error`: Managed by combining states from multiple data hooks.
*   **Data Requirements:**
    *   User ID (from `useUserConfig`) to initiate quiz sessions.
    *   Quiz pack data (ID, name, description, category, questions, difficulty, estimated duration) to display and manage quiz selection.
    *   Quiz question options (ID, text, value) for user interaction.
    *   Answer data (selected option IDs, value, text, confidence, response time) for submission.
*   **Functionality:**
    *   **Quiz Pack Selection:** Allows users to select different quiz packs. Initializes `availableQuizPacks` by fetching and merging various categories.
    *   **Dynamic Quiz Loading:** Loads the `defaultQuiz` data based on the `selectedQuizPackId`.
    *   **Multi-tier Quiz Navigation:** Handles moving between quiz tiers (questions) and tracking user selections.
    *   **Answer Submission:** Submits user answers to the quiz session.
    *   **Mood Entry Saving:** Saves the collected quiz results (emotions, intensity, tags, reflection) by initiating a quiz session and submitting answers.
    *   **Navigation:** Navigates to `/quiz-settings` for configuration and `/history` after saving.
    *   **Loading and Error Handling:** Displays loading indicators and error messages.

### `src/pages/QuizSettings.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useNavigate()`: For programmatic navigation.
    *   `useQuizConfig()`: Central hook for managing quiz-specific configurations. Provides:
        *   `preferences`: User quiz preferences.
        *   `presentationConfig`: Detailed presentation configuration (including skin, view details, emotion presentation, etc.).
        *   `isLoading`, `error`: Loading and error states for configurations.
        *   `updatePreferences`, `updatePresentationConfig`: Functions to update quiz preferences and presentation configurations.
        *   `preferredViewType`, `colorMode`, `personalizationLevel`, `performanceMode`: Convenient accessors for specific config values.
    *   `useEmotionWheelQuiz()`, `useTCMAssessmentQuiz()`: Specialized hooks for specific quiz types, though their direct data calls are not explicitly shown in the provided snippet beyond their instantiation.
    *   `useState`, `useEffect`: Standard React hooks for local component state management and side effects, including:
        *   `config`: Local state to hold the `PersonalizationConfig`.
        *   `activeTab`: Controls the active tab in the settings UI.
        *   `isLoading`, `error`: Local loading/error states.
        *   `localPersonalizationLevel`: Local state for displaying personalization level.
*   **Data Requirements:**
    *   **`PersonalizationConfig`**: This is the core data structure. It's a deeply nested object representing all customizable quiz aspects across 6 layers:
        *   `layer0_dataset_presentation`: Quiz pack categories, difficulty, session length, auto-selection, progress restoration, question display fields, interaction behavior, and **question management** (selected pack, available packs, question customization including enabled questions and custom order).
        *   `layer1_user_choice`: Preferred view type, active skin ID, dark mode, color mode, user level.
        *   `layer2_rendering_strategy`: Render engine preferences, content display mode, layout preferences, performance mode, supported content types.
        *   `layer3_skin_base`: Available skins, selected skin ID, colors, fonts, animations.
        *   `layer4_view_detail`: Dynamic view configurations (wheel, card, bubble, list configs) and **emotion presentation** (grouping style, transition animation, and implicitly, emoji mapping within this structure).
        *   `layer5_accessibility`: Accessibility settings (high contrast, large text, reduce motion, keyboard navigation, voice guidance).
    *   Lists of available quiz packs and skins for selection.
    *   Question data (ID, title, description, order, enabled, required) for customization.
*   **Functionality:**
    *   **Configuration Display and Editing:** Renders a comprehensive UI for viewing and modifying all aspects of the `PersonalizationConfig`.
    *   **Quiz Pack Management:** Allows users to select active quiz packs, enable/disable individual questions within a pack, reorder questions, and reset question order.
    *   **Skin and Display Settings:** Provides controls for selecting skins, adjusting colors, fonts, animations, and various display/rendering preferences (view types, render engines, content display modes).
    *   **Accessibility Settings:** Offers toggles for accessibility features.
    *   **Personalization Level Calculation:** Updates `localPersonalizationLevel` based on user configuration changes.
    *   **Navigation:** Navigates to the home page (presumably `NewHome.tsx`) to start a quiz with the current configuration.
    *   **Loading and Error Handling:** Displays loading states and handles potential errors during configuration loading.
    *   **Simulated Data Loading:** The `useEffect` with `setTimeout` currently simulates loading user configuration, indicating a real API call would fetch this data in a production environment.
    *   **Data Persistence:** The `updateConfig`, `toggleQuestionEnabled`, `moveQuestion`, `resetQuestionOrder`, and `switchQuizPack` functions are designed to update the `config` state, which would then be persisted via the `useQuizConfig` hook's `updatePreferences` or `updatePresentationConfig` (implicitly handled by the hook's internal logic, as per the `config-system-api-updated.md` documentation).

### `src/pages/Settings.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions and setting the global language.
    *   `useTheme()`: For accessing and setting the global theme.
    *   `useGlobalConfig()`: Central hook for managing global application configurations. Provides:
        *   `config`: Global application settings (`GlobalAppConfig`).
        *   `isLoading`, `error`: Loading and error states for global configurations.
        *   `updateConfig`: Function to update global configurations.
        *   `themeMode`, `language`, `notificationsEnabled`, `soundEnabled`, `accessibilityConfig`: Convenient accessors for specific global config values.
        *   `isOnline`, `lastSyncTime`: Network status and last sync time.
    *   `useDataSync()`: Provides `syncStatus` and `performSync` for explicit data synchronization.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `notifications`: Local state for notification toggle.
        *   `soundEnabled`: Local state for sound toggle.
*   **Data Requirements:**
    *   **`GlobalAppConfig`**: This object holds global application settings:
        *   `theme_mode`: Application theme (light, dark, system).
        *   `language`: Application language (zh-CN, en-US).
        *   `notifications_enabled`: Whether notifications are enabled.
        *   `sound_enabled`: Whether sound effects are enabled.
        *   `accessibility`: JSON string representing accessibility settings (high contrast, large text, reduce motion, keyboard navigation, voice guidance).
        *   `created_at`, `updated_at`: Timestamps for user account.
    *   User VIP status (simulated with `localStorage` for demonstration).
*   **Functionality:**
    *   **Global Settings Management:** Provides UI for configuring theme, language, notifications, sound, and accessibility.
    *   **Language and Theme Switching:** Handles updating the global language and theme, and persisting these changes via `useGlobalConfig`.
    *   **Accessibility Features:** Manages accessibility settings, including applying corresponding CSS classes to `document.documentElement`.
    *   **Notifications and Sound Control:** Toggles notification permissions and sound effects, persisting changes.
    *   **Data Sync:** Displays current sync status and allows manual data synchronization.
    *   **User Information Display:** Shows basic user information (e.g., account age) and a simulated VIP status.
    *   **Loading and Error Handling:** Displays loading indicators and handles potential errors during configuration loading or updates.
    *   **`toast` notifications:** Provides user feedback for successful or failed operations.
    *   **Deprecated/Migrated Functionality:** Explicitly states that Quiz-related configurations and view type changes have been moved to `QuizSettings.tsx`. This reinforces that `Settings.tsx` is now solely for global, non-quiz-specific settings.

### `src/pages/EmojiSetManager.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions (`t`).
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `emojiSets`: Stores the list of all emoji sets.
        *   `activeEmojiSet`: Stores the currently active emoji set.
        *   `isLoading`, `error`: Loading and error states for emoji sets.
        *   `activeTab`: Controls the active tab in the UI (list, manage).
        *   `selectedEmojiSet`: Stores the emoji set currently selected for editing/deletion.
        *   `isEditDialogOpen`, `isDeleteDialogOpen`, `isCreateDialogOpen`: Control dialog visibility.
        *   `newEmojiSet`: Form state for creating new emoji sets.
        *   `editingEmojiSet`: Form state for editing existing emoji sets.
*   **Services Used (Implicit via `Services.emojiSet()`):**
    *   This is the primary service for interacting with emoji set data. It provides methods for:
        *   `getAll()`: Fetching all available emoji sets.
        *   `getActiveEmojiSets()`: Fetching the currently active emoji set(s).
        *   `create(createInput)`: Creating a new emoji set.
        *   `update(id, updateInput)`: Updating an existing emoji set.
        *   `delete(id)`: Deleting an emoji set.
        *   `setActiveEmojiSet(emojiSetId)`: Setting an emoji set as active.
*   **Data Requirements:**
    *   `EmojiSet`: Full details of an emoji set (id, name, description, type, `is_default`, `is_system`, `is_unlocked`, price, created\_by).
    *   `CreateEmojiSetInput`: Input for creating a new emoji set.
    *   `UpdateEmojiSetInput`: Input for updating an existing emoji set.
    *   `EmojiItem`: Individual emoji data (not explicitly shown in the snippet, but implied by `EmojiDisplay`).
*   **Functionality:**
    *   **Emoji Set Listing:** Displays a list of all available emoji sets.
    *   **Active Emoji Set Display:** Shows the currently active emoji set.
    *   **CRUD Operations:** Allows users to create, edit, and delete emoji sets through dialogs.
    *   **Emoji Set Activation:** Enables setting a specific emoji set as the active one.
    *   **Form Management:** Manages input forms for creating and editing both emoji sets.
    *   **Input Validation:** Basic validation (e.g., name required).
    *   **User Feedback:** Uses `toast` notifications for success/error messages.
    *   **Data Refresh:** Refreshes the list of emoji sets after creation, update, or deletion.
    *   **Loading and Error Handling:** Manages loading states and displays errors during data operations.

### `src/pages/History.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useUserConfig()`: To get user-specific configurations, presumably for fetching history related to a specific user.
    *   `useHistoryData()`: Custom hook to fetch and manage historical data.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `entries`: Stores the list of history entries.
        *   `isLoading`: Loading state for history data.
        *   `error`: Error state for history data fetching.
        *   `currentPage`, `entriesPerPage`: For pagination.
        *   `sortBy`, `sortOrder`: For sorting history entries.
        *   `filterText`, `filterCategory`: For filtering history entries.
        *   `selectedEntry`: For viewing details of a specific entry.
        *   `isDeleteDialogOpen`: For confirmation dialog before deletion.
*   **Services Used (Implicit via `useHistoryData`):**
    *   Presumably interacts with a `HistoryService` or similar, which would have methods like:
        *   `getAllEntries(userId, filters, sort)`: To fetch history entries with options for filtering and sorting.
        *   `getEntryById(entryId)`: To fetch details of a single entry.
        *   `deleteEntry(entryId)`: To delete a specific history entry.
*   **Data Requirements:**
    *   History entries: Each entry likely contains:
        *   `id`: Unique identifier.
        *   `timestamp`: When the entry was created.
        *   `quizPackName`: Name of the quiz/scale.
        *   `resultSummary`: A brief summary of the result (e.g., emotions selected, score).
        *   `details`: More elaborate data (e.g., intensity, tags, reflection, specific question answers).
        *   `category`: Category of the quiz (emotion, TCM, etc.).
    *   User ID (from `useUserConfig`) to fetch personalized history.
*   **Functionality:**
    *   **History Display:** Shows a paginated and sortable list of past quiz sessions or mood entries.
    *   **Filtering:** Allows filtering entries by text search and category.
    *   **Sorting:** Enables sorting entries by date, quiz name, etc.
    *   **Detail View:** Displays detailed information for a selected history entry.
    *   **Deletion:** Provides functionality to delete history entries with a confirmation dialog.
    *   **Pagination:** Manages displaying a subset of entries per page.
    *   **Loading and Error Handling:** Shows loading indicators and error messages during data operations.

### `src/pages/Analytics.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useUserConfig()`: To get user-specific configurations, likely for personalized analytics or filtering.
    *   `useAnalyticsData()`: Custom hook to fetch and process analytical data. Provides:
        *   `moodTrends`: Data for mood trend charts.
        *   `categoryDistribution`: Data for category distribution charts.
        *   `performanceMetrics`: Data for quiz performance metrics.
        *   `isLoading`, `error`: Loading and error states for analytics data.
        *   `fetchTrends`, `fetchDistribution`, `fetchMetrics`: Functions to fetch specific types of analytics data, possibly with date ranges or filters.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `startDate`, `endDate`: For defining the date range of analytics.
        *   `selectedReportType`: For selecting which type of report to view (trends, distribution, performance).
        *   `selectedQuizPack`: For filtering analytics by a specific quiz pack.
*   **Services Used (Implicit via `useAnalyticsData`):**
    *   Presumably interacts with an `AnalyticsService` or similar, which would have methods like:
        *   `getMoodTrends(userId, startDate, endDate)`: To get mood data over time.
        *   `getCategoryDistribution(userId, quizPackId)`: To get the distribution of answers/moods by category.
        *   `getQuizPerformance(userId, quizPackId)`: To get metrics like average score, response time, etc.
*   **Data Requirements:**
    *   Time-series data for mood trends (e.g., date, average mood score, emotion distribution).
    *   Categorical data for distribution charts (e.g., emotion categories, count/percentage).
    *   Numerical metrics for performance (e.g., average score, time taken per question, completion rate).
    *   User ID (from `useUserConfig`) for personalized analytics.
    *   Quiz Pack IDs for filtering analytics.
*   **Functionality:**
    *   **Dashboard Display:** Presents various analytical reports (mood trends, category distribution, quiz performance).
    *   **Date Range Selection:** Allows users to select a date range for data analysis.
    *   **Report Type Switching:** Enables switching between different types of analytical reports.
    *   **Quiz Pack Filtering:** Allows filtering analytics by specific quiz packs.
    *   **Data Visualization:** Integrates with charting libraries (not explicitly shown, but implied by data types) to visualize data.
    *   **Loading and Error Handling:** Shows loading indicators and error messages during data fetching.

### `src/pages/Shop.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useUserConfig()`: To get user-specific configurations, potentially for checking unlocked items or VIP status.
    *   `useShopData()`: Custom hook to fetch and manage shop-related data. Provides:
        *   `availableItems`: List of items available for purchase (skins, emoji sets, quiz packs, VIP plans).
        *   `isLoading`, `error`: Loading and error states for shop data.
        *   `purchaseItem(itemId, itemType)`: Function to initiate a purchase.
        *   `unlockItem(itemId, itemType)`: Function to unlock items (e.g., through achievements or free unlocks).
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `activeTab`: Controls the active tab for different item categories (Skins, Emoji Sets, Quiz Packs, VIP).
        *   `purchaseConfirmationDialogOpen`: Controls the visibility of a purchase confirmation dialog.
        *   `selectedItemForPurchase`: Stores the item selected by the user for purchase.
*   **Services Used (Implicit via `useShopData`):**
    *   Presumably interacts with a `ShopService` or `PaymentService` and `UnlockService`, which would have methods like:
        *   `getAvailableItems(itemTypeFilter)`: To fetch items from the shop, possibly filtered by type.
        *   `initiatePurchase(itemId, itemType, userId)`: To process payment for an item.
        *   `checkUnlockStatus(itemId, itemType, userId)`: To check if an item is already unlocked.
*   **Data Requirements:**
    *   Shop items: Each item likely contains:
        *   `id`: Unique identifier.
        *   `name`, `description`: Item details.
        *   `type`: (e.g., 'skin', 'emoji_set', 'quiz_pack', 'vip_plan').
        *   `price`: Cost of the item.
        *   `currency`: Currency type.
        *   `previewImage`: Image for display.
        *   `isUnlocked`: Boolean indicating if the user already owns it.
        *   `isPurchased`: Boolean for purchased items.
        *   `category`: (e.g., 'premium', 'free', 'limited_time').
    *   User ID (from `useUserConfig`) for personalized shop experience (e.g., showing owned items).
*   **Functionality:**
    *   **Item Browsing:** Displays different categories of items available for purchase or unlock.
    *   **Purchase Flow:** Initiates a purchase process for selected items, possibly involving a confirmation dialog.
    *   **Item Unlocking:** Handles the logic for unlocking items (e.g., if they are free or earned).
    *   **Display Item Status:** Shows whether an item is already owned or unlocked.
    *   **User Feedback:** Uses `toast` notifications for purchase/unlock results.
    *   **Loading and Error Handling:** Shows loading indicators and error messages.

### `src/pages/QuizManagementPage.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useQuizManagement()`: Custom hook for managing quiz packs and questions. Provides:
        *   `quizPacks`: List of all quiz packs.
        *   `questions`: List of questions for a selected quiz pack.
        *   `isLoading`, `error`: Loading and error states.
        *   `createQuizPack(input)`: To create a new quiz pack.
        *   `updateQuizPack(id, input)`: To update an existing quiz pack.
        *   `deleteQuizPack(id)`: To delete a quiz pack.
        *   `createQuestion(packId, input)`: To create a new question for a pack.
        *   `updateQuestion(questionId, input)`: To update an existing question.
        *   `deleteQuestion(questionId)`: To delete a question.
        *   `reorderQuestions(packId, orderedIds)`: To reorder questions within a pack.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `activeTab`: Controls the active tab (Quiz Packs, Questions).
        *   `selectedQuizPack`: The quiz pack currently being viewed or edited.
        *   `isCreatePackDialogOpen`, `isEditPackDialogOpen`, `isDeletePackDialogOpen`: Dialog visibility for quiz packs.
        *   `isCreateQuestionDialogOpen`, `isEditQuestionDialogOpen`, `isDeleteQuestionDialogOpen`: Dialog visibility for questions.
        *   `newQuizPackForm`, `editingQuizPackForm`: Form states for quiz pack creation/editing.
        *   `newQuestionForm`, `editingQuestionForm`: Form states for question creation/editing.
*   **Services Used (Implicit via `useQuizManagement`):**
    *   Presumably interacts with `QuizPackService` and `QuestionService` or similar, with methods for CRUD operations on quiz packs and questions.
*   **Data Requirements:**
    *   `QuizPack`: Details of a quiz pack (id, name, description, category, difficulty, estimated duration, total questions).
    *   `QuizQuestion`: Details of a quiz question (id, question text, description, tier level, options, type, required, order).
    *   `QuizQuestionOption`: Details of a question option (id, option text, option value).
*   **Functionality:**
    *   **Quiz Pack Management:** Allows administrators or content creators to create, view, edit, and delete quiz packs.
    *   **Question Management:** Within a selected quiz pack, allows creating, viewing, editing, deleting, and reordering questions.
    *   **Form Handling:** Manages input forms for creating and editing both quiz packs and questions.
    *   **Dialogs:** Uses confirmation and input dialogs for various actions.
    *   **User Feedback:** Provides `toast` notifications for operation results.
    *   **Loading and Error Handling:** Displays loading indicators and handles errors during data manipulation.

### `src/pages/QuizResults.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useParams()`: From `react-router-dom` to extract `sessionId` from the URL.
    *   `useQuizResult()`: Custom hook to fetch and process quiz session results. Provides:
        *   `quizResult`: The detailed result of a specific quiz session.
        *   `isLoading`, `error`: Loading and error states for the result.
        *   `fetchResult(sessionId)`: Function to fetch quiz results.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `analysisSummary`: Stores a generated summary of the quiz results.
        *   `recommendations`: Stores recommendations based on the quiz results.
*   **Services Used (Implicit via `useQuizResult`):**
    *   Presumably interacts with `QuizResultService` or similar, with methods like:
        *   `getQuizResult(sessionId)`: To fetch the comprehensive result of a quiz.
        *   `analyzeQuizResult(resultData)`: To perform analysis and generate summaries/recommendations (could be client-side or server-side).
*   **Data Requirements:**
    *   `sessionId`: Obtained from URL parameters to identify the specific quiz session.
    *   `QuizResult`: A comprehensive object containing:
        *   `sessionId`, `userId`, `quizPackId`.
        *   `answers`: List of user answers to each question.
        *   `finalScore` or `overallAssessment`.
        *   `completedAt`: Timestamp of completion.
        *   `quizPackDetails`: Information about the quiz pack.
        *   `questionDetails`: Details for each question answered.
        *   `userSelections`: The choices made by the user.
        *   `moodIntensity`, `tags`, `reflection` (if applicable, e.g., from `MoodForm` integration).
*   **Functionality:**
    *   **Result Display:** Presents a detailed summary of a completed quiz session.
    *   **Dynamic Content:** Generates an analysis summary and personalized recommendations based on the quiz results.
    *   **Visualization (Implied):** Could integrate with charting libraries to visualize aspects of the results (e.g., mood over time, distribution of selected emotions).
    *   **Navigation:** Might offer options to go to history, retake the quiz, or explore related content.
    *   **Loading and Error Handling:** Displays loading indicators and error messages.

### `src/pages/QuizSession.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useParams()`: From `react-router-dom` to extract `sessionId` from the URL.
    *   `useNavigate()`: For programmatic navigation (e.g., to results page).
    *   `useQuizSessionData()`: Custom hook to manage the active quiz session data and logic. Provides:
        *   `currentQuestion`: The current question to display.
        *   `quizProgress`: Current progress (e.g., question number, total questions).
        *   `isLoading`, `error`: Loading and error states for the session.
        *   `submitAnswer(answerData)`: To submit an answer for the current question.
        *   `completeQuiz()`: To mark the quiz session as complete.
        *   `moveToNextQuestion()`: To advance to the next question.
        *   `moveToPreviousQuestion()`: To go back to the previous question.
        *   `skipQuestion()`: To skip a question.
        *   `startSession(sessionId, packId, userId)`: To initialize the quiz session if not already started.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `userAnswer`: Stores the user's current answer for the displayed question.
        *   `responseTime`: Tracks the time taken to answer a question.
        *   `sessionStarted`: Boolean to track if the session has officially begun.
*   **Services Used (Implicit via `useQuizSessionData`):**
    *   Presumably interacts with `QuizSessionService` or `QuizEngineService` with methods like:
        *   `getQuestionForSession(sessionId, questionIndex)`: To retrieve a specific question.
        *   `recordAnswer(sessionId, questionId, answerData)`: To save a user's answer.
        *   `markSessionComplete(sessionId)`: To finalize a quiz session.
*   **Data Requirements:**
    *   `sessionId`: Obtained from URL parameters to identify the ongoing quiz.
    *   `QuizQuestion`: Current question details (text, options, type).
    *   `QuizQuestionOption`: Options for the current question.
    *   Answer data: User's choice, confidence, response time for each question.
    *   Quiz progress: Current question index, total number of questions.
*   **Functionality:**
    *   **Interactive Quiz Flow:** Presents questions one by one, allowing users to select answers.
    *   **Progress Tracking:** Displays current progress within the quiz.
    *   **Answer Submission:** Submits user's answers to the backend/service layer.
    *   **Navigation within Quiz:** Allows moving forward and backward through questions, and skipping.
    *   **Session Completion:** Handles the logic for marking a quiz session as completed and navigating to the results page.
    *   **Timer/Response Time:** Tracks how long a user takes to answer a question.
    *   **Loading and Error Handling:** Displays loading indicators and error messages.

### `src/pages/QuizTypes.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useQuizPackTypes()`: Custom hook to fetch and manage available quiz pack types/categories. Provides:
        *   `packTypes`: A list of distinct quiz pack categories (e.g., 'emotion', 'tcm', 'daily', 'assessment').
        *   `isLoading`, `error`: Loading and error states.
        *   `fetchPackTypes()`: Function to retrieve available types.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects.
*   **Services Used (Implicit via `useQuizPackTypes`):**
    *   Presumably interacts with a `QuizPackService` or similar, with a method like `getDistinctCategories()` or `getPackMetadata()`.
*   **Data Requirements:**
    *   A list of distinct quiz pack categories/types. Each type might have:
        *   `id` or `key`: Unique identifier for the category.
        *   `name`: Display name of the category.
        *   `description`: Short description.
        *   `icon` or `image`: Associated visual.
*   **Functionality:**
    *   **Category Display:** Renders a list of different quiz types or categories.
    *   **Navigation:** Likely provides links or buttons to navigate to pages where users can select quizzes within a specific category.
    *   **Loading and Error Handling:** Shows loading indicators and error messages.
    *   This page likely serves as an entry point for users to explore different kinds of quizzes offered by the application.

### `src/pages/QuizLauncher.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useNavigate()`: For programmatic navigation.
    *   `useParams()`: From `react-router-dom` to extract `quizPackId` from the URL.
    *   `useUserConfig()`: To retrieve the user's ID for starting a quiz session.
    *   `useQuizLauncherData()`: Custom hook to fetch data required before launching a quiz. Provides:
        *   `quizPackDetails`: Details of the specific quiz pack to be launched.
        *   `isLoading`, `error`: Loading and error states.
        *   `fetchQuizPackDetails(quizPackId)`: Function to retrieve details of a specific quiz pack.
    *   `useQuizSession()`: Used to actually start the quiz session. Provides:
        *   `startQuizSession(packId, userId)`: Function to initiate a new quiz session.
        *   `isLoading: sessionStarting`: Loading state specific to session initiation.
*   **Services Used (Implicit via `useQuizLauncherData` and `useQuizSession`):**
    *   `QuizPackService`: `getPackDetails(quizPackId)`.
    *   `QuizSessionService`: `createSession(packId, userId)`.
*   **Data Requirements:**
    *   `quizPackId`: Obtained from URL parameters to identify which quiz to launch.
    *   `quizPackDetails`: Name, description, difficulty, estimated duration, number of questions for the selected quiz pack.
    *   User ID: Required by `startQuizSession`.
*   **Functionality:**
    *   **Quiz Pre-launch Information:** Displays details about the selected quiz before it starts, allowing the user to confirm.
    *   **Quiz Launch:** Initiates a new quiz session upon user confirmation.
    *   **Navigation:** Navigates to the `QuizSession.tsx` page after a successful launch, passing the newly created `sessionId`.
    *   **Loading and Error Handling:** Shows loading indicators and error messages during data fetching and session initiation.

### `src/pages/WheelTest.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `selectedEmotion`: Stores the currently selected emotion on the wheel.
        *   `intensity`: Stores the intensity value.
        *   `tags`, `reflection`: Stores additional input from the user.
        *   `wheelData`: Mock or fetched data representing the structure for the emotion wheel.
    *   `useMockEmotionData()`: Custom hook to provide mock or sample emotion data, likely for testing purposes.
    *   `useUserConfig()`: May be used to fetch user preferences that influence wheel rendering (e.g., custom emoji mappings or skin preferences), although not explicitly used in the snippet.
*   **Data Requirements:**
    *   Emotion data for the wheel: A hierarchical structure of emotions (e.g., primary emotions with sub-emotions or tiers). Each emotion likely has:
        *   `id`, `name`, `value`.
        *   `emoji`: Associated emoji.
        *   `color`: Associated color.
        *   `children`: Nested emotions for tiers.
    *   User input: Intensity, tags, reflection.
*   **Functionality:**
    *   **Emotion Wheel Display:** Renders an interactive emotion wheel for users to select emotions.
    *   **User Input Capture:** Allows users to input intensity, tags, and reflections related to their emotional state.
    *   **Interaction with Wheel:** Handles selection of emotions on the wheel.
    *   **Data Display:** Shows the selected emotion and its properties.
    *   **Simulation/Testing:** This page appears to be primarily for testing the emotion wheel component and its interactions rather than a full-fledged production feature.

### `src/pages/LanguageTest.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: Central hook for managing and accessing language-related state and functions. Provides:
        *   `t`: Translation function.
        *   `language`: Current active language.
        *   `setLanguage`: Function to change the active language.
    *   `useGlobalConfig()`: Likely used to persist the selected language as a global setting. Provides `updateConfig`.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects.
*   **Data Requirements:**
    *   Translations: Key-value pairs for different languages.
    *   Available languages: A list of supported language codes (e.g., 'en', 'zh').
*   **Functionality:**
    *   **Language Selection:** Provides a UI for users to select their preferred language.
    *   **Dynamic Text Display:** Demonstrates how text changes based on the selected language using the `t` function.
    *   **Global Language Persistence:** Updates the global configuration with the chosen language.
    *   **Testing Translations:** Serves as a page to test different language translations and ensure the `useLanguage` hook and translation system are working correctly.

### `src/pages/Register.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useNavigate()`: For programmatic navigation (e.g., to login page or home page after successful registration).
    *   `useAuth()`: Custom hook for authentication-related functionalities. Provides:
        *   `register(credentials)`: Function to register a new user.
        *   `isLoading`, `error`: Loading and error states for the registration process.
    *   `useState`: Standard React hook for local form state, including:
        *   `email`, `password`, `confirmPassword`: Form input fields.
        *   `username`: User-defined username.
*   **Services Used (Implicit via `useAuth`):**
    *   Presumably interacts with an `AuthService` (likely a tRPC client-side service as per `COMPLETE_SERVICE_DESIGN.md`), with a `register` method.
*   **Data Requirements:**
    *   User registration credentials: email, password, confirm password, username.
*   **Functionality:**
    *   **User Registration Form:** Provides input fields for user registration.
    *   **Input Validation:** Performs client-side validation on email, password, and confirmation.
    *   **User Registration:** Submits registration data to the authentication service.
    *   **User Feedback:** Uses `toast` notifications for success or failure of registration.
    *   **Navigation:** Redirects to the login page or home page upon successful registration.
    *   **Loading and Error Handling:** Displays loading state during registration and error messages for invalid input or registration failures.

### `src/pages/VIP.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useNavigate()`: For programmatic navigation.
    *   `useAuth()`: To get current user authentication status (e.g., `user.isVip`).
    *   `useVipPlans()`: Custom hook to fetch and manage VIP plan data. Provides:
        *   `vipPlans`: List of available VIP subscription plans.
        *   `isLoading`, `error`: Loading and error states for VIP plans.
        *   `purchaseVipPlan(planId)`: Function to initiate a VIP plan purchase.
        *   `checkVipStatus(userId)`: Function to check a user's current VIP status.
    *   `useState`, `useEffect`: Standard React hooks for local component state and side effects, including:
        *   `selectedPlan`: Stores the VIP plan selected by the user for purchase.
        *   `isPurchaseDialogOpen`: Controls the visibility of a purchase confirmation dialog.
*   **Services Used (Implicit via `useVipPlans` and `useAuth`):**
    *   `VipPlanService`: `getAvailablePlans()`, `purchasePlan(planId, userId)`.
    *   `AuthService`: `getUserStatus(userId)` to get VIP status.
    *   `PaymentService`: The actual payment processing (likely abstracted by `purchaseVipPlan`).
*   **Data Requirements:**
    *   VIP plan details: ID, name, description, features, price, duration, benefits.
    *   User VIP status: Boolean indicating if the user is a VIP.
    *   User ID: For personalized VIP status and purchase.
*   **Functionality:**
    *   **VIP Plan Display:** Shows different VIP subscription plans with their features and pricing.
    *   **Current VIP Status:** Displays whether the current user is a VIP.
    *   **Purchase Flow:** Allows users to select a VIP plan and initiate a purchase.
    *   **Confirmation Dialog:** Uses a dialog for purchase confirmation.
    *   **User Feedback:** Provides `toast` notifications for purchase results.
    *   **Navigation:** Redirects to a success page or back to the home page after purchase.
    *   **Loading and Error Handling:** Displays loading indicators and error messages during data fetching and purchase.

### `src/pages/Login.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useNavigate()`: For programmatic navigation (e.g., to home page after successful login).
    *   `useAuth()`: Custom hook for authentication-related functionalities. Provides:
        *   `login(credentials)`: Function to log in a user.
        *   `isLoading`, `error`: Loading and error states for the login process.
    *   `useState`: Standard React hook for local form state, including:
        *   `email`, `password`: Form input fields.
*   **Services Used (Implicit via `useAuth`):**
    *   Presumably interacts with an `AuthService` (likely a tRPC client-side service as per `COMPLETE_SERVICE_DESIGN.md`), with a `login` method.
*   **Data Requirements:**
    *   User login credentials: email, password.
*   **Functionality:**
    *   **User Login Form:** Provides input fields for user login.
    *   **Input Validation:** Performs client-side validation on email and password.
    *   **User Login:** Submits login credentials to the authentication service.
    *   **User Feedback:** Uses `toast` notifications for success or failure of login.
    *   **Navigation:** Redirects to the home page upon successful login.
    *   **Loading and Error Handling:** Displays loading state during login and error messages for invalid credentials or login failures.

### `src/pages/NotFound.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useNavigate()`: For programmatic navigation (e.g., back to home).
*   **Data Requirements:**
    *   None, this is a static page.
*   **Functionality:**
    *   **Error Display:** Informs the user that the requested page was not found (404 error).
    *   **Navigation:** Provides a button or link to navigate back to the home page.
    *   **User Experience:** Offers a simple and clear message for a common error scenario.

### `src/pages/ErrorPage.tsx`

**Data Calls and Requirements:**

*   **Hooks Used:**
    *   `useLanguage()`: For accessing translation functions.
    *   `useRouteError()`: From `react-router-dom` to get details about the error that occurred during routing.
    *   `useNavigate()`: For programmatic navigation (e.g., back to home).
*   **Data Requirements:**
    *   Error object: Contains details about the error (e.g., status, message, stack trace).
*   **Functionality:**
    *   **Error Display:** Presents details about an unexpected error, often used as a fallback for uncaught routing errors.
    *   **User Information:** Provides a user-friendly message and potentially technical details for debugging.
    *   **Navigation:** Offers a button or link to return to the home page.
    *   **Debugging Aid:** The `error.stack` can be helpful for developers in debugging. 