import { fetchRe<PERSON><PERSON>and<PERSON> } from '@trpc/server/adapters/fetch';
import { appRouter } from '../../lib/router.js';

// 使用泛型接口替代 Context
interface Context<T = any> {
  env: T;
  request: Request;
}

export interface Env {
  TURSO_DB_URL: string;
  TURSO_AUTH_TOKEN: string;
  ENVIRONMENT: string;
}

/**
 * Create a context for tRPC requests
 * This allows passing data to all procedures
 */
function createContext(context: Context<Env>) {
  // TODO: Add authentication logic here
  // For now, return a basic context that matches the router's Context interface
  return {
    userId: undefined, // Will be set after authentication
    isAuthenticated: false, // Will be set after authentication
    userRole: undefined, // Will be set after authentication
    env: context.env,
    request: context.request,
  };
}

export const onRequest = async (context: Context<Env>) => {
  try {
    // Handle tRPC request
    return await fetchRequestHandler({
      endpoint: '/trpc',
      req: context.request,
      router: appRouter,
      createContext: () => createContext(context),
      onError: ({ error, path }) => {
        console.error(`[tRPC] Error in ${path}:`, error);
      },
    });
  } catch (error) {
    console.error('[tRPC] Unhandled error:', error);

    // Return a structured error response
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        path: context.request.url,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
