# 工厂类整合计划

## 概述

本文档描述了将 `bubbleFactory.ts` 和 `cardFactory.ts` 整合到 `viewFactory.tsx` 的计划。这种整合将减少代码重复，提高代码的可维护性，并提供一个统一的接口来创建不同类型的视图。

## 当前状态

目前，我们有三个独立的工厂类：

1. `src/utils/viewFactory.tsx`：用于创建不同类型的视图，包括轮盘、卡片、气泡和星系视图。
2. `src/utils/bubbleFactory.ts`：专门用于创建气泡视图。
3. `src/utils/cardFactory.ts`：专门用于创建卡片视图。

这种分散的实现导致了代码重复，并使得切换不同的视图类型变得复杂。

## 整合计划

### 步骤 1: 查找导入 bubbleFactory.ts 和 cardFactory.ts 的文件

首先，我们需要找出所有导入 `bubbleFactory.ts` 和 `cardFactory.ts` 的文件，以便更新它们的导入语句。

### 步骤 2: 更新导入语句

对于每个导入 `bubbleFactory.ts` 或 `cardFactory.ts` 的文件，我们需要更新导入语句，使用 `viewFactory.tsx` 替代。

例如：

```typescript
// 旧导入
import { BubbleFactory } from '@/utils/bubbleFactory';
import { CardFactory } from '@/utils/cardFactory';

// 新导入
import { ViewFactory } from '@/utils/viewFactory';
```

### 步骤 3: 更新方法调用

对于每个使用 `BubbleFactory.createBubble()` 或 `CardFactory.createCard()` 的地方，我们需要更新为使用 `ViewFactory.createBubble()` 或 `ViewFactory.createCard()`。

例如：

```typescript
// 旧代码
const bubble = BubbleFactory.createBubble(renderEngine, displayMode, config);
const card = CardFactory.createCard(renderEngine, displayMode, config);

// 新代码
const bubble = ViewFactory.createBubble(contentType, skinConfig, layout);
const card = ViewFactory.createCard(contentType, skinConfig, layout);
```

注意：参数可能需要调整，因为 `ViewFactory` 的方法签名与 `BubbleFactory` 和 `CardFactory` 的方法签名不完全相同。

### 步骤 4: 移除废弃的文件

一旦所有导入和方法调用都已更新，我们可以安全地移除 `bubbleFactory.ts` 和 `cardFactory.ts` 文件。

### 步骤 5: 更新文档

更新相关文档，说明 `bubbleFactory.ts` 和 `cardFactory.ts` 已被废弃，应该使用 `viewFactory.tsx` 替代。

## 优势

整合这些工厂类将带来以下优势：

1. **减少代码重复**：避免在多个工厂类中实现类似的功能。
2. **提高代码的可维护性**：只需要维护一个工厂类，而不是多个。
3. **提供统一的接口**：使得切换不同的视图类型更加容易。
4. **支持更多的配置选项**：`viewFactory.tsx` 支持更多的布局选项和配置参数。
5. **支持从 UserConfig 创建视图**：这是一个更高级的功能，可以简化视图的创建过程。

## 风险和缓解措施

1. **接口不兼容**：`ViewFactory` 的方法签名与 `BubbleFactory` 和 `CardFactory` 的方法签名不完全相同，可能需要调整参数。
   - 缓解措施：在 `ViewFactory` 中添加兼容层，支持旧的参数格式。

2. **依赖关系**：可能有一些代码依赖于 `BubbleFactory` 和 `CardFactory` 的特定行为。
   - 缓解措施：在更新之前进行全面的测试，确保所有功能都正常工作。

## 结论

整合 `bubbleFactory.ts` 和 `cardFactory.ts` 到 `viewFactory.tsx` 是一个合理的决定，可以减少代码重复，提高代码的可维护性，并提供一个统一的接口来创建不同类型的视图。通过仔细规划和测试，我们可以安全地完成这个整合过程。
