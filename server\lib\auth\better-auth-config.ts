/**
 * Better-Auth 配置
 * 集成到现有的tRPC + SQLite架构中
 */

import { drizzleAdapter } from '@better-auth/drizzle';
import { betterAuth } from 'better-auth';
import { executeQuery } from '../database/index.js';

// 自定义SQLite适配器，使用现有的数据库连接
const createSQLiteAdapter = () => {
  return {
    id: 'sqlite-custom',

    async create(table: string, data: Record<string, any>) {
      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = columns.map(() => '?').join(', ');

      const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
      const result = await executeQuery({ sql, args: values });

      return { ...data, id: data.id || result.meta?.last_insert_rowid };
    },

    async findOne(table: string, where: Record<string, any>) {
      const conditions = Object.keys(where);
      const values = Object.values(where);
      const whereClause = conditions.map((col) => `${col} = ?`).join(' AND ');

      const sql = `SELECT * FROM ${table} WHERE ${whereClause} LIMIT 1`;
      const result = await executeQuery({ sql, args: values });

      return result.rows[0] || null;
    },

    async findMany(table: string, where?: Record<string, any>) {
      let sql = `SELECT * FROM ${table}`;
      let args: any[] = [];

      if (where && Object.keys(where).length > 0) {
        const conditions = Object.keys(where);
        const values = Object.values(where);
        const whereClause = conditions.map((col) => `${col} = ?`).join(' AND ');
        sql += ` WHERE ${whereClause}`;
        args = values;
      }

      const result = await executeQuery({ sql, args });
      return result.rows;
    },

    async update(table: string, where: Record<string, any>, data: Record<string, any>) {
      const setColumns = Object.keys(data);
      const setValues = Object.values(data);
      const whereColumns = Object.keys(where);
      const whereValues = Object.values(where);

      const setClause = setColumns.map((col) => `${col} = ?`).join(', ');
      const whereClause = whereColumns.map((col) => `${col} = ?`).join(' AND ');

      const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
      await executeQuery({ sql, args: [...setValues, ...whereValues] });

      return { ...where, ...data };
    },

    async delete(table: string, where: Record<string, any>) {
      const conditions = Object.keys(where);
      const values = Object.values(where);
      const whereClause = conditions.map((col) => `${col} = ?`).join(' AND ');

      const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
      await executeQuery({ sql, args: values });

      return true;
    },
  };
};

export const auth = betterAuth({
  database: createSQLiteAdapter(),

  // 基础配置
  baseURL: process.env.BETTER_AUTH_URL || 'http://localhost:8788',
  secret: process.env.BETTER_AUTH_SECRET || 'your-secret-key-here',

  // 邮箱密码认证
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // 可以后续启用
    minPasswordLength: 6,
    maxPasswordLength: 128,
  },

  // 社交登录
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      redirectURI: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/auth/callback/google',
    },
    apple: {
      clientId: process.env.APPLE_CLIENT_ID!,
      clientSecret: process.env.APPLE_CLIENT_SECRET!,
      redirectURI: process.env.APPLE_REDIRECT_URI || 'http://localhost:3000/auth/callback/apple',
    },
  },

  // 会话配置
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7天
    updateAge: 60 * 60 * 24, // 1天更新一次
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5分钟缓存
    },
  },

  // 用户字段配置
  user: {
    additionalFields: {
      displayName: {
        type: 'string',
        required: false,
        input: true,
        output: true,
      },
      avatar: {
        type: 'string',
        required: false,
        input: true,
        output: true,
      },
      isVip: {
        type: 'boolean',
        required: false,
        input: false, // 只能服务端修改
        output: true,
        defaultValue: false,
      },
      vipExpiresAt: {
        type: 'date',
        required: false,
        input: false,
        output: true,
      },
      isVerified: {
        type: 'boolean',
        required: false,
        input: false,
        output: true,
        defaultValue: false,
      },
      lastLoginAt: {
        type: 'date',
        required: false,
        input: false,
        output: true,
      },
    },
  },

  // 数据库表映射
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          // 创建用户前的处理
          return {
            ...user,
            id: user.id || `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            syncStatus: 'synced',
          };
        },
        after: async (user) => {
          // 创建用户后初始化相关数据
          await initializeUserData(user.id);
          return user;
        },
      },
      update: {
        before: async (userId, data) => {
          return {
            ...data,
            updated_at: new Date().toISOString(),
          };
        },
      },
    },
    session: {
      create: {
        before: async (session) => {
          return {
            ...session,
            id: session.id || `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            created_at: new Date().toISOString(),
            lastUsedAt: new Date().toISOString(),
          };
        },
      },
    },
  },

  // 插件配置
  plugins: [
    {
      id: 'vip-management',
      hooks: {
        after: [
          {
            matcher: (context) => context.path === '/sign-up',
            handler: async (context) => {
              // 新用户注册后初始化VIP状态
              await initializeUserVipStatus(context.user.id);
            },
          },
          {
            matcher: (context) => context.path === '/sign-in',
            handler: async (context) => {
              // 登录后更新最后登录时间
              await updateLastLoginTime(context.user.id);
            },
          },
        ],
      },
    },
    {
      id: 'sync-integration',
      hooks: {
        after: [
          {
            matcher: (context) => ['sign-up', 'sign-in', 'update-user'].includes(context.path),
            handler: async (context) => {
              // 用户信息变更后触发同步
              await triggerUserDataSync(context.user.id);
            },
          },
        ],
      },
    },
  ],

  // 高级配置
  advanced: {
    crossSubDomainCookies: {
      enabled: false, // 根据需要启用
    },
    useSecureCookies: process.env.NODE_ENV === 'production',
    generateId: () => {
      // 自定义ID生成器，与现有系统保持一致
      return `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    },
  },
});

// 辅助函数
async function initializeUserData(userId: string) {
  try {
    // 初始化用户配置
    await executeQuery({
      sql: `
        INSERT OR IGNORE INTO user_configs (
          id, user_id, name, active_emotion_data_id, active_skin_id,
          preferred_view_type, is_active, created_at, last_updated
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
      args: [
        `config_${userId}`,
        userId,
        'Default Config',
        'default',
        'default-light',
        'wheel',
        true,
        new Date().toISOString(),
        new Date().toISOString(),
      ],
    });

    // 初始化用户偏好
    await executeQuery({
      sql: `
        INSERT OR IGNORE INTO user_preferences (
          user_id, theme, language, notifications, sync_enabled,
          auto_sync, sync_interval, wifi_only, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
      args: [
        userId,
        'auto',
        'en',
        true,
        true,
        true,
        300, // 5分钟
        false,
        new Date().toISOString(),
        new Date().toISOString(),
      ],
    });

    console.log(`[Auth] Initialized user data for ${userId}`);
  } catch (error) {
    console.error(`[Auth] Failed to initialize user data for ${userId}:`, error);
  }
}

async function initializeUserVipStatus(userId: string) {
  try {
    // 检查是否有促销活动给新用户VIP
    const hasPromotion = await checkNewUserPromotion();

    if (hasPromotion) {
      const expiresAt = new Date().toISOString();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7天试用

      await executeQuery({
        sql: 'UPDATE users SET is_vip = ?, vip_expires_at = ? WHERE id = ?',
        args: [true, expiresAt.toISOString(), userId],
      });

      console.log(`[Auth] Granted trial VIP to new user ${userId}`);
    }
  } catch (error) {
    console.error(`[Auth] Failed to initialize VIP status for ${userId}:`, error);
  }
}

async function updateLastLoginTime(userId: string) {
  try {
    await executeQuery({
      sql: 'UPDATE users SET last_login_at = ?, login_count = login_count + 1 WHERE id = ?',
      args: [new Date().toISOString(), userId],
    });
  } catch (error) {
    console.error(`[Auth] Failed to update last login time for ${userId}:`, error);
  }
}

async function triggerUserDataSync(userId: string) {
  try {
    // 触发用户数据同步到客户端
    // 这里可以发送WebSocket消息或者设置同步标记
    console.log(`[Auth] Triggered user data sync for ${userId}`);
  } catch (error) {
    console.error(`[Auth] Failed to trigger sync for ${userId}:`, error);
  }
}

async function checkNewUserPromotion(): Promise<boolean> {
  // 检查是否有新用户促销活动
  // 可以从数据库或配置文件读取
  return process.env.NEW_USER_VIP_TRIAL === 'true';
}

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.User;
