/**
 * Quiz进度指示器组件
 * 支持多种中医文化样式的进度指示器组件
 */

import React, { useEffect, useState } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { ProgressIndicatorComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type ProgressIndicatorComponentConfig = z.infer<typeof ProgressIndicatorComponentConfigSchema>;

export interface ProgressIndicatorComponentProps extends BaseQuizComponentProps<ProgressIndicatorComponentConfig> {
  current: number;
  total: number;
  onComplete?: () => void;
}

interface ProgressIndicatorComponentState extends ComponentState {
  current_progress: number;
  percentage: number;
  is_complete: boolean;
  animation_progress: number;
}

/**
 * 进度指示器组件类
 */
export class ProgressIndicatorComponent extends BaseQuizComponent<
  ProgressIndicatorComponentConfig,
  ProgressIndicatorComponentProps,
  ProgressIndicatorComponentState
> {
  private containerRef = React.createRef<HTMLDivElement>();
  private animationFrameId?: number;

  extractConfig(props: ProgressIndicatorComponentProps): ProgressIndicatorComponentConfig {
    return props.config;
  }

  getInitialState(): ProgressIndicatorComponentState {
    const percentage = Math.min(100, Math.max(0, (this.props.current / this.props.total) * 100));
    
    return {
      is_loading: false,
      is_interactive: false,
      is_disabled: false,
      selected_items: [],
      animation_state: 'idle',
      current_progress: this.props.current,
      percentage: percentage,
      is_complete: percentage >= 100,
      animation_progress: 0
    };
  }

  componentDidMount(): void {
    if (this.config.progress.animated) {
      this.animateProgress();
    }
  }

  componentDidUpdate(prevProps: ProgressIndicatorComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.current !== this.props.current || prevProps.total !== this.props.total) {
      const percentage = Math.min(100, Math.max(0, (this.props.current / this.props.total) * 100));
      const isComplete = percentage >= 100;
      
      this.setState({ 
        current_progress: this.props.current,
        percentage: percentage,
        is_complete: isComplete
      });

      if (this.config.progress.animated) {
        this.animateProgress();
      }

      // 完成回调
      if (isComplete && !this.state.is_complete) {
        this.props.onComplete?.();
        
        // 发送交互事件
        this.emitInteractionEvent('focus', {
          action: 'progress_complete',
          current: this.props.current,
          total: this.props.total
        });
      }
    }
  }

  componentWillUnmount(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }
  }

  /**
   * 动画进度更新
   */
  private animateProgress(): void {
    if (this.personalization.layer5_accessibility?.reduce_motion) {
      this.setState({ animation_progress: this.state.percentage });
      return;
    }

    const startProgress = this.state.animation_progress;
    const targetProgress = this.state.percentage;
    const duration = 1000; // 1秒动画
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用缓动函数
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      const currentProgress = startProgress + (targetProgress - startProgress) * easeOutCubic;
      
      this.setState({ animation_progress: currentProgress });

      if (progress < 1) {
        this.animationFrameId = requestAnimationFrame(animate);
      }
    };

    this.animationFrameId = requestAnimationFrame(animate);
  }

  /**
   * 获取进度类型样式类名
   */
  private getTypeClassName(): string {
    const type = this.config.style.type;
    return `quiz-progress-${type}`;
  }

  /**
   * 获取尺寸样式类名
   */
  private getSizeClassName(): string {
    const size = this.config.style.size;
    return `quiz-progress-${size}`;
  }

  /**
   * 获取标签文本
   */
  private getLabelText(): string {
    const { language } = this.context || { language: 'zh' };
    const labelText = this.config.labels.label_text;
    
    if (labelText) {
      return labelText[language] || labelText['zh'] || labelText['en'] || '';
    }
    
    return language === 'zh' ? '进度' : 'Progress';
  }

  /**
   * 渲染条形进度条
   */
  private renderBarProgress(): React.ReactNode {
    const progressWidth = `${this.state.animation_progress}%`;
    
    return (
      <div className="quiz-progress-bar-container">
        <div 
          className="quiz-progress-bar-track"
          style={{
            backgroundColor: this.config.style.background_color,
            borderRadius: `${this.config.style.border_radius}px`,
            height: `${this.config.style.thickness}px`
          }}
        >
          <div 
            className="quiz-progress-bar-fill"
            style={{
              width: progressWidth,
              backgroundColor: this.config.style.color_scheme,
              borderRadius: `${this.config.style.border_radius}px`,
              height: '100%',
              transition: this.config.progress.animated ? 'width 0.3s ease-out' : 'none'
            }}
          />
        </div>
      </div>
    );
  }

  /**
   * 渲染圆形进度条
   */
  private renderCircleProgress(): React.ReactNode {
    const size = this.config.style.size === 'small' ? 60 : this.config.style.size === 'large' ? 120 : 90;
    const strokeWidth = this.config.style.thickness;
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (this.state.animation_progress / 100) * circumference;

    return (
      <div className="quiz-progress-circle-container">
        <svg width={size} height={size} className="quiz-progress-circle">
          {/* 背景圆 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke={this.config.style.background_color}
            strokeWidth={strokeWidth}
          />
          {/* 进度圆 */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke={this.config.style.color_scheme}
            strokeWidth={strokeWidth}
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            transform={`rotate(-90 ${size / 2} ${size / 2})`}
            style={{
              transition: this.config.progress.animated ? 'stroke-dashoffset 0.3s ease-out' : 'none'
            }}
          />
        </svg>
        
        {/* 中心文本 */}
        <div className="quiz-progress-circle-text">
          {this.config.progress.show_percentage && (
            <span className="quiz-progress-percentage">
              {Math.round(this.state.animation_progress)}%
            </span>
          )}
          {this.config.progress.show_fraction && (
            <span className="quiz-progress-fraction">
              {this.props.current}/{this.props.total}
            </span>
          )}
        </div>
      </div>
    );
  }

  /**
   * 渲染莲花进度条
   */
  private renderLotusProgress(): React.ReactNode {
    const petals = 8;
    const activePetals = Math.ceil((this.state.animation_progress / 100) * petals);

    return (
      <div className="quiz-progress-lotus-container">
        <div className="quiz-progress-lotus">
          {Array.from({ length: petals }, (_, index) => (
            <div
              key={index}
              className={`
                quiz-progress-lotus-petal
                ${index < activePetals ? 'quiz-progress-lotus-petal-active' : ''}
              `}
              style={{
                transform: `rotate(${(index * 360) / petals}deg)`,
                animationDelay: this.config.progress.animated ? `${index * 100}ms` : '0ms'
              }}
            >
              🌸
            </div>
          ))}
          
          {/* 中心 */}
          <div className="quiz-progress-lotus-center">
            {this.config.progress.show_percentage && (
              <span className="quiz-progress-percentage">
                {Math.round(this.state.animation_progress)}%
              </span>
            )}
          </div>
        </div>
      </div>
    );
  }

  /**
   * 渲染竹子进度条
   */
  private renderBambooProgress(): React.ReactNode {
    const segments = this.props.total;
    const activeSegments = this.props.current;

    return (
      <div className="quiz-progress-bamboo-container">
        <div className="quiz-progress-bamboo">
          {Array.from({ length: segments }, (_, index) => (
            <div
              key={index}
              className={`
                quiz-progress-bamboo-segment
                ${index < activeSegments ? 'quiz-progress-bamboo-segment-active' : ''}
              `}
              style={{
                animationDelay: this.config.progress.animated ? `${index * 150}ms` : '0ms'
              }}
            >
              <div className="quiz-progress-bamboo-node" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  /**
   * 渲染步骤进度条
   */
  private renderStepsProgress(): React.ReactNode {
    const steps = this.props.total;
    const currentStep = this.props.current;

    return (
      <div className="quiz-progress-steps-container">
        <div className="quiz-progress-steps">
          {Array.from({ length: steps }, (_, index) => (
            <div
              key={index}
              className={`
                quiz-progress-step
                ${index < currentStep ? 'quiz-progress-step-completed' : ''}
                ${index === currentStep ? 'quiz-progress-step-current' : ''}
              `}
            >
              <div className="quiz-progress-step-circle">
                {index < currentStep ? '✓' : index + 1}
              </div>
              {index < steps - 1 && (
                <div className={`
                  quiz-progress-step-line
                  ${index < currentStep - 1 ? 'quiz-progress-step-line-completed' : ''}
                `} />
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  /**
   * 渲染进度内容
   */
  private renderProgressContent(): React.ReactNode {
    const type = this.config.style.type;

    switch (type) {
      case 'circle':
        return this.renderCircleProgress();
      case 'lotus':
        return this.renderLotusProgress();
      case 'bamboo':
        return this.renderBambooProgress();
      case 'steps':
        return this.renderStepsProgress();
      default:
        return this.renderBarProgress();
    }
  }

  /**
   * 渲染标签
   */
  private renderLabel(): React.ReactNode {
    if (!this.config.labels.show_label) return null;

    const labelText = this.getLabelText();
    const labelPosition = this.config.labels.label_position;

    return (
      <div className={`quiz-progress-label quiz-progress-label-${labelPosition}`}>
        {labelText}
        {this.config.progress.show_percentage && (
          <span className="quiz-progress-percentage">
            {Math.round(this.state.percentage)}%
          </span>
        )}
        {this.config.progress.show_fraction && (
          <span className="quiz-progress-fraction">
            ({this.props.current}/{this.props.total})
          </span>
        )}
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-progress-component',
      this.getTypeClassName(),
      this.getSizeClassName(),
      this.state.is_complete && 'quiz-progress-complete',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        ref={this.containerRef}
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
        role="progressbar"
        aria-valuenow={this.props.current}
        aria-valuemin={0}
        aria-valuemax={this.props.total}
        aria-label={this.getLabelText()}
      >
        {/* 标签 (顶部/底部) */}
        {['top', 'bottom'].includes(this.config.labels.label_position) && this.renderLabel()}

        {/* 进度内容 */}
        <div className="quiz-progress-content">
          {this.config.labels.label_position === 'center' && this.renderLabel()}
          {this.renderProgressContent()}
        </div>
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'progressbar';
  }

  protected getAriaLabel(): string {
    return `Progress: ${this.props.current} of ${this.props.total} (${Math.round(this.state.percentage)}%)`;
  }
}

// 使用Context的函数式组件包装器
const ProgressIndicatorComponentWrapper: React.FC<ProgressIndicatorComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <ProgressIndicatorComponent.contextType.Provider value={{ language }}>
      <ProgressIndicatorComponent {...props} />
    </ProgressIndicatorComponent.contextType.Provider>
  );
};

// 设置Context类型
ProgressIndicatorComponent.contextType = React.createContext({ language: 'zh' });

export default ProgressIndicatorComponentWrapper;
