import { useState, useEffect } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible";
import {
  Brush, ChevronDown, ChevronRight, Crown,
  Palette, Sparkles, Loader2, Eye
} from "lucide-react";
import { toast } from "sonner";
import { Services } from "@/services";
import { Skin } from "@/types";

interface SkinSelectorProps {
  userLevel: 'beginner' | 'regular' | 'advanced' | 'vip';
  isOpen: boolean;
  onToggle: () => void;
}

const SkinSelector: React.FC<SkinSelectorProps> = ({
  userLevel,
  isOpen,
  onToggle
}) => {
  const { t } = useLanguage();

  // 状态管理
  const [skins, setSkins] = useState<Skin[]>([]);
  const [activeSkin, setActiveSkin] = useState<Skin | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载皮肤数据
  useEffect(() => {
    const loadSkins = async () => {
      try {
        setError(null);
        const skinService = await Services.skin();
        const skinsResult = await skinService.getAll();

        if (skinsResult.success && skinsResult.data) {
          const validSkins = skinsResult.data.filter(skin =>
            skin && skin.id && skin.name
          );
          setSkins(validSkins);

          // 获取默认皮肤
          const defaultSkinsResult = await skinService.getDefaultSkins();
          if (defaultSkinsResult.success && defaultSkinsResult.data?.length > 0) {
            setActiveSkin(defaultSkinsResult.data[0]);
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load skins';
        setError(errorMessage);
        console.error('Error loading skins:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadSkins();
  }, []);

  // 处理皮肤选择
  const handleSkinSelect = async (skinId: string) => {
    if (!skinId) return;

    try {
      const skinService = await Services.skin();
      const result = await skinService.setDefaultSkin(skinId);

      if (result.success) {
        const selectedSkin = skins.find(skin => skin.id === skinId);
        if (selectedSkin) {
          setActiveSkin(selectedSkin);
          toast.success(t('settings.skin_changed', '皮肤已更改'));
        }
      } else {
        throw new Error(result.error || 'Failed to change skin');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to change skin';
      console.error('Error changing skin:', error);
      toast.error(t('settings.skin_change_failed', '更改皮肤失败') + ': ' + errorMessage);
    }
  };

  // 渲染皮肤卡片
  const renderSkinCard = (skin: Skin) => {
    const isSelected = activeSkin?.id === skin.id;
    const isLocked = skin.is_premium && userLevel !== 'vip';

    return (
      <Button
        key={skin.id}
        variant={isSelected ? "default" : "outline"}
        className={`h-auto p-4 flex flex-col items-start space-y-3 relative ${
          isLocked ? 'opacity-60' : ''
        }`}
        onClick={() => !isLocked && handleSkinSelect(skin.id)}
        disabled={isLocked}
      >
        {/* 皮肤预览 */}
        <div className="w-full h-16 rounded-lg border-2 border-muted overflow-hidden">
          {skin.preview_image ? (
            <img
              src={skin.preview_image}
              alt={skin.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div
              className="w-full h-full"
              style={{
                background: skin.colors ?
                  `linear-gradient(135deg, ${JSON.parse(skin.colors).primary}, ${JSON.parse(skin.colors).secondary})` :
                  'linear-gradient(135deg, #3b82f6, #8b5cf6)'
              }}
            />
          )}
        </div>

        {/* 皮肤信息 */}
        <div className="w-full space-y-1">
          <div className="flex items-center justify-between w-full">
            <span className="font-medium text-sm">{skin.name}</span>
            {skin.is_premium && (
              <Badge variant="secondary" className="text-xs">
                <Crown className="h-3 w-3 mr-1" />
                {t('settings.premium', '高级')}
              </Badge>
            )}
          </div>

          {skin.description && (
            <p className="text-xs text-left opacity-80 line-clamp-2">
              {skin.description}
            </p>
          )}
        </div>

        {/* 选中指示器 */}
        {isSelected && (
          <div className="absolute top-2 right-2">
            <div className="w-3 h-3 bg-primary rounded-full" />
          </div>
        )}

        {/* 锁定指示器 */}
        {isLocked && (
          <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
            <Crown className="h-6 w-6 text-white" />
          </div>
        )}
      </Button>
    );
  };

  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <Brush className="h-5 w-5" />
                <span>{t('settings.skin_customization', '皮肤自定义')}</span>
                {activeSkin && (
                  <Badge variant="outline" className="text-xs">
                    {activeSkin.name}
                  </Badge>
                )}
              </CardTitle>
              {isOpen ?
                <ChevronDown className="h-4 w-4" /> :
                <ChevronRight className="h-4 w-4" />
              }
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="space-y-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>{t('settings.loading_skins', '加载皮肤中...')}</span>
              </div>
            ) : error ? (
              <div className="text-center py-8 space-y-2">
                <p className="text-destructive">{t('settings.error_loading_skins', '加载皮肤失败')}</p>
                <p className="text-sm text-muted-foreground">{error}</p>
              </div>
            ) : (
              <>
                {/* 皮肤说明 */}
                <div className="p-3 bg-muted rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    {t('settings.skin_explanation', '皮肤决定了应用的整体视觉风格，包括颜色、字体和效果。')}
                  </p>
                </div>

                {/* 当前皮肤信息 */}
                {activeSkin && (
                  <div className="p-3 bg-primary/10 border border-primary/20 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-lg border-2 border-primary/20 overflow-hidden">
                        {activeSkin.preview_image ? (
                          <img
                            src={activeSkin.preview_image}
                            alt={activeSkin.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div
                            className="w-full h-full"
                            style={{
                              background: activeSkin.colors ?
                                `linear-gradient(135deg, ${JSON.parse(activeSkin.colors).primary}, ${JSON.parse(activeSkin.colors).secondary})` :
                                'linear-gradient(135deg, #3b82f6, #8b5cf6)'
                            }}
                          />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{activeSkin.name}</span>
                          {activeSkin.is_premium && (
                            <Badge variant="secondary">
                              <Crown className="h-3 w-3 mr-1" />
                              {t('settings.premium', '高级')}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {t('settings.current_skin', '当前使用的皮肤')}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* 皮肤网格 */}
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {skins.map(renderSkinCard)}
                </div>

                {/* VIP提示 */}
                {userLevel !== 'vip' && skins.some(skin => skin.is_premium) && (
                  <div className="p-3 bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <Crown className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5" />
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
                          {t('settings.premium_skins_locked', '高级皮肤已锁定')}
                        </p>
                        <p className="text-xs text-amber-700 dark:text-amber-300">
                          {t('settings.upgrade_for_skins', '升级到VIP以解锁所有高级皮肤和自定义选项')}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* 预览按钮 */}
                <div className="flex justify-center">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    {t('settings.preview_skin', '预览皮肤效果')}
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default SkinSelector;
