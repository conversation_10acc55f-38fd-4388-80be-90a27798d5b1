import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import useTurso from '@/lib/useTurso';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';

/**
 * Example component demonstrating how to use the Turso database
 */
const TursoExample = () => {
  const { isTursoInitialized, isInitializing, error, executeQuery } = useTurso();
  const [emotions, setEmotions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [queryError, setQueryError] = useState<Error | null>(null);

  // Load emotions when Turso is initialized
  useEffect(() => {
    if (isTursoInitialized) {
      loadEmotions();
    }
  }, [isTursoInitialized]);

  // Function to load emotions from the database
  const loadEmotions = async () => {
    setIsLoading(true);
    setQueryError(null);

    try {
      const result = await executeQuery('SELECT * FROM emotions LIMIT 10');
      setEmotions(result.rows);
    } catch (err) {
      console.error('Error loading emotions:', err);
      setQueryError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Turso Database Example</CardTitle>
        <CardDescription>Demonstrates using the Turso online database</CardDescription>
      </CardHeader>

      <CardContent>
        {isInitializing ? (
          <div className="flex items-center justify-center p-6">
            <Loader2 className="w-8 h-8 text-primary animate-spin" />
            <span className="ml-2">Initializing Turso database...</span>
          </div>
        ) : error ? (
          <div className="p-4 bg-destructive/10 text-destructive rounded-md">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              <h3 className="font-medium">Initialization Error</h3>
            </div>
            <p className="mt-2 text-sm">{error.message}</p>
          </div>
        ) : isTursoInitialized ? (
          <div>
            <div className="flex items-center mb-4">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
              <span>Turso database initialized successfully</span>
            </div>

            {isLoading ? (
              <div className="flex justify-center p-4">
                <Loader2 className="w-6 h-6 text-primary animate-spin" />
              </div>
            ) : queryError ? (
              <div className="p-3 bg-destructive/10 text-destructive rounded-md text-sm">
                <p>Error loading emotions: {queryError.message}</p>
              </div>
            ) : (
              <div>
                <h3 className="font-medium mb-2">Emotions from Turso DB:</h3>
                {emotions.length === 0 ? (
                  <p className="text-muted-foreground">No emotions found</p>
                ) : (
                  <ul className="space-y-1 text-sm">
                    {emotions.map((emotion: any) => (
                      <li key={emotion.id} className="p-2 bg-muted/50 rounded">
                        {emotion.name} ({emotion.id})
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            )}
          </div>
        ) : (
          <p className="text-center text-muted-foreground">
            Waiting for Turso database initialization...
          </p>
        )}
      </CardContent>

      <CardFooter className="flex justify-end">
        <Button onClick={loadEmotions} disabled={!isTursoInitialized || isLoading}>
          {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
          Refresh Data
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TursoExample;
