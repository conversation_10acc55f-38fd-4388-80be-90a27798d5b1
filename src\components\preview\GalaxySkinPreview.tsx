/**
 * 星系皮肤预览组件
 * 用于在设置页面中展示星系视图的皮肤预览
 */

import { useLanguage } from '@/contexts/LanguageContext';
import type { ContentDisplayMode } from '@/types/previewTypes';
import type { Skin } from '@/types/skinTypes';
import type { GalaxyLayout } from '@/types/userConfigTypes';
import { ViewFactory } from '@/utils/viewFactory';
import type React from 'react';
import { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

interface GalaxySkinPreviewProps {
  skin: Skin;
  size?: 'sm' | 'md' | 'lg';
  showControls?: boolean;
  className?: string;
}

/**
 * 星系皮肤预览组件
 */
const GalaxySkinPreview: React.FC<GalaxySkinPreviewProps> = ({
  skin,
  size = 'md',
  showControls = true,
  className = '',
}) => {
  const { t } = useLanguage();
  const [contentDisplayMode, setContentDisplayMode] = useState<ContentDisplayMode>('textEmoji');
  const [layout, setLayout] = useState<GalaxyLayout>('orbital');

  // 创建示例情绪数据用于预览
  const sampleEmotions = [
    { id: '1', name: t('emotions.happy', 'Happy'), emoji: '😊', color: '#FFD700' },
    { id: '2', name: t('emotions.sad', 'Sad'), emoji: '😢', color: '#4169E1' },
    { id: '3', name: t('emotions.angry', 'Angry'), emoji: '😠', color: '#FF4500' },
    { id: '4', name: t('emotions.fear', 'Fear'), emoji: '😨', color: '#800080' },
    { id: '5', name: t('emotions.surprise', 'Surprise'), emoji: '😲', color: '#FF69B4' },
    { id: '6', name: t('emotions.disgust', 'Disgust'), emoji: '🤢', color: '#32CD32' },
  ];

  // 获取容器尺寸
  const getContainerSize = () => {
    switch (size) {
      case 'sm':
        return { width: 200, height: 200 };
      case 'lg':
        return { width: 400, height: 400 };
      default:
        return { width: 300, height: 300 };
    }
  };

  const containerSize = getContainerSize();

  // 创建星系视图
  const createGalaxyView = () => {
    // 更新皮肤配置以使用选定的布局
    const updatedSkinConfig = {
      ...skin.config,
      view_configs: {
        ...skin.config.view_configs,
        galaxy: {
          ...skin.config.view_configs?.galaxy,
          layout,
        },
      },
    };

    // 使用 ViewFactory 静态方法创建视图
    const view = ViewFactory.createView('galaxy', contentDisplayMode, updatedSkinConfig, {
      layout,
    });

    // 渲染视图
    return view.render(
      sampleEmotions,
      1,
      () => {} // 空函数，因为这只是预览
    );
  };

  return (
    <div className={`galaxy-skin-preview ${className}`}>
      <Card className="border shadow-sm">
        {showControls && (
          <div className="p-3 border-b">
            <div className="flex flex-col gap-2">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="galaxyLayout" className="text-xs mb-1 block">
                    {t('skin_preview.galaxy_layout', { fallback: '星系布局' })}
                  </Label>
                  <Select
                    value={layout}
                    onValueChange={(value) => setLayout(value as GalaxyLayout)}
                  >
                    <SelectTrigger id="galaxyLayout" className="h-8 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="orbital">
                        {t('skin_preview.orbital_layout', { fallback: '轨道布局' })}
                      </SelectItem>
                      <SelectItem value="spiral">
                        {t('skin_preview.spiral_layout', { fallback: '螺旋布局' })}
                      </SelectItem>
                      <SelectItem value="nebula">
                        {t('skin_preview.nebula_layout', { fallback: '星云布局' })}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <Label htmlFor="contentDisplayMode" className="text-xs mb-1 block">
                    {t('skin_preview.content_display_mode', { fallback: '内容显示' })}
                  </Label>
                  <Select
                    value={contentDisplayMode}
                    onValueChange={(value) => setContentDisplayMode(value as ContentDisplayMode)}
                  >
                    <SelectTrigger id="contentDisplayMode" className="h-8 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">
                        {t('skin_preview.text_only', { fallback: '仅文本' })}
                      </SelectItem>
                      <SelectItem value="emoji">
                        {t('skin_preview.emoji_only', { fallback: '仅表情' })}
                      </SelectItem>
                      <SelectItem value="textEmoji">
                        {t('skin_preview.text_emoji', { fallback: '文本+表情' })}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        )}
        <CardContent className="p-2 flex items-center justify-center">
          <div
            className="galaxy-preview-container"
            style={{
              width: `${containerSize.width}px`,
              height: `${containerSize.height}px`,
              overflow: 'hidden',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {createGalaxyView()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GalaxySkinPreview;
