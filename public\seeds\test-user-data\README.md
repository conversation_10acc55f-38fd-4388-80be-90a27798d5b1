# 测试数据集 (Test Data)

这个目录包含了用户相关的测试数据，用于开发和测试环境。配置数据（如情绪数据集、皮肤配置等）位于 `public/seeds/schema/` 目录中。

## 📁 文件结构

```
public/seeds/test/
├── master.sql              # 主控制文件，按正确顺序加载所有用户测试数据
├── users.sql              # 测试用户数据
├── user_configs.sql       # 用户配置数据
├── user_preferences.sql   # 用户偏好设置 (键值对格式)
├── user_streaks.sql       # 用户连续记录数据
├── mood_entries.sql       # 情绪记录数据 (包含完整字段)
├── mood_entry_tags.sql    # 情绪记录标签关联
└── README.md              # 本文档
```

## 🔗 相关配置数据

配置数据位于 `public/seeds/schema/` 目录：
- `emotion_data_sets.sql` - 情绪数据集配置
- `skin_configs.sql` - 皮肤配置数据

## 🚀 使用方法

### 完整初始化 (推荐)
```bash
# 1. 首先加载基础配置数据
sqlite3 your_database.db < public/seeds/schema/master.sql

# 2. 然后加载测试用户数据
sqlite3 your_database.db < public/seeds/test/master.sql
```

### 只加载测试数据 (假设配置已存在)
```bash
# 使用主控制文件加载所有用户测试数据
sqlite3 your_database.db < public/seeds/test/master.sql
```

### 单独加载特定数据
```bash
# 只加载用户数据
sqlite3 your_database.db < public/seeds/test/users.sql

# 只加载情绪记录
sqlite3 your_database.db < public/seeds/test/mood_entries.sql
```

## 📊 测试数据概览

### 👥 测试用户 (3个)
- **test-user-1** (John Doe) - VIP用户，Premium会员，英文界面，暖色模式
- **test-user-2** (Jane Smith) - 普通用户，中文界面，深色主题，冷色模式
- **test-user-3** (Alex Chen) - 新用户，未验证，自然主题，混合色模式

### 🎨 用户配置类型 (9个)
1. **config-user-1** - 新手用户基础配置 (暖色模式)
2. **config-user-2** - 普通用户平衡配置 (冷色模式)
3. **config-user-3** - 年轻用户简化配置 (混合模式)
4. **config-default-template** - 系统默认模板 (暖色模式)
5. **config-vip-user** - VIP用户高级配置 (冷色模式)
6. **config-accessibility-user** - 无障碍用户专用配置 (暖色模式)
7. **config-advanced-user** - 高级用户个性化配置 (冷色模式)
8. **config-power-user** - 专业用户极致配置 (混合模式)
9. **config-auto-adaptive** - 智能自适应颜色配置 (自动模式)

### 🎨 颜色模式系统
测试数据展示了四种颜色模式的完整应用：

#### 1. **Warm (暖色模式)**
- **使用配置**: config-user-1, config-default-template, config-accessibility-user
- **特点**: 温馨舒适，适合放松和晚间使用
- **色彩**: 红色到黄色色谱，饱和度提升10%
- **适用场景**: 冥想、放松、睡前情绪记录

#### 2. **Cool (冷色模式)**
- **使用配置**: config-user-2, config-vip-user, config-advanced-user
- **特点**: 清爽专业，适合工作和学习
- **色彩**: 青色到紫色色谱，饱和度降低10%
- **适用场景**: 工作时间、专注学习、理性分析

#### 3. **Mixed (混合模式)**
- **使用配置**: config-user-3, config-power-user
- **特点**: 平衡色调，适应不同情绪
- **色彩**: 全色谱，标准饱和度，中性色温
- **适用场景**: 全天候使用，综合情绪记录

#### 4. **Auto (自动模式)**
- **使用配置**: config-auto-adaptive
- **特点**: 智能适配，根据时间和情境自动调整
- **智能规则**: 时间驱动、情境感知、情绪感知
- **适用场景**: 智能适配，无需手动调整

### 📝 情绪记录 (5条)
包含完整的情绪记录数据，包括：
- 基础信息 (用户、时间、强度)
- 环境信息 (位置、天气)
- 心理状态 (能量、睡眠、压力)
- 社交环境 (独处/朋友/家人/同事)
- 身体症状和应对策略
- 感恩笔记和目标进展

## 🔧 数据特点

### 真实性
- 使用真实的时间戳和相对时间
- 包含合理的数据关联和约束
- 模拟真实用户的使用模式

### 完整性
- 覆盖所有用户相关表结构
- 包含不同用户类型和状态
- 支持多语言和多时区

### 多样性
- 不同类型的用户 (新用户、活跃用户、VIP用户)
- 不同的偏好设置和配置
- 多种情绪状态和记录类型
- 各种用户行为模式
- 四种颜色模式的完整覆盖 (warm/cool/mixed/auto)
- 深色/浅色模式的组合应用
- 从基础到专业的个性化配置层次

## 🧪 测试场景

这些测试数据支持以下测试场景：

### 用户管理
- 用户注册和登录
- VIP功能和权限
- 多语言支持

### 情绪记录
- 创建和编辑情绪记录
- 不同视图类型的显示
- 数据导出和分析

### 个性化
- 用户偏好设置管理
- 个人配置定制
- 可访问性设置
- 颜色模式切换和自动适配
- 深色/浅色模式组合
- 皮肤与颜色模式的交互
- ViewConfigOptions 的 collapsible 配置界面

### 数据同步
- 在线/离线数据同步
- 数据备份和恢复
- 跨设备数据一致性

## 📋 数据验证

加载测试数据后，系统会自动验证：
- 数据记录数量
- 关联关系完整性
- 外键约束满足
- 用户配置有效性

## 🔄 更新和维护

当数据库结构发生变化时：
1. 更新相应的 SQL 文件
2. 确保 `master.sql` 中的加载顺序正确
3. 运行完整的数据验证
4. 更新本 README 文档

## 🚨 注意事项

- 这些数据仅用于开发和测试环境
- 不要在生产环境中使用这些测试数据
- 测试数据依赖于 schema 目录中的配置数据
- 定期清理和重置测试数据以保持一致性
- 确保测试数据不包含敏感信息

## 📋 依赖关系

测试数据需要以下配置数据已经加载：
- 情绪数据集 (`emotion_data_sets`)
- 皮肤配置 (`skins`)

这些配置数据位于 `public/seeds/schema/` 目录中。
