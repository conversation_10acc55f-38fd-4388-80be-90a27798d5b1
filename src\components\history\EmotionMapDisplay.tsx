import { Loading } from '@/components/ui/loading';
import { useLanguage } from '@/contexts/LanguageContext';
import { Services } from '@/services';
import type { Emotion } from '@/types/emotionDataTypes';
import type { MoodEntry } from '@/types/mood';
import React, { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

const EmotionMapDisplay = () => {
  const { t } = useLanguage();

  // 新的状态管理 - 使用服务层
  const [history, setHistory] = useState<MoodEntry[]>([]);
  const [emotions, setEmotions] = useState<Emotion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 数据加载 Effect
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 加载历史记录
        const historyResult = await Services.moodEntry.getAll();
        if (historyResult.success) {
          setHistory(historyResult.data);
        } else {
          throw new Error(historyResult.error);
        }

        // 加载情绪数据
        const emotionsResult = await Services.emotion.getAll();
        if (emotionsResult.success) {
          setEmotions(emotionsResult.data);
        } else {
          throw new Error(emotionsResult.error);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load data';
        setError(errorMessage);
        toast.error(`${t('errors.failed_to_load_data')}: ${errorMessage}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [t]);

  const loggedPrimaryEmotionIds = useMemo(() => {
    const ids = new Set<string>();
    if (!history) return ids;
    history.forEach((entry) => {
      // 从情绪选择中获取情绪ID
      if (entry.emotions && entry.emotions.length > 0) {
        entry.emotions.forEach((emotionSelection) => {
          ids.add(emotionSelection.emotion_id);
        });
      }
    });
    return ids;
  }, [history]);

  if (isLoading) {
    return <Loading text={t('app.loading')} size="lg" />;
  }

  // 获取所有主要情绪（父级为null的情绪）
  const allPrimaryEmotions = emotions.filter((emotion) => !emotion.parent_id);

  if (allPrimaryEmotions.length === 0 && !isLoading) {
    // Check if emotions are loaded but empty
    return (
      <div className="flex flex-col items-center justify-center h-[50vh] text-muted-foreground p-4 text-center">
        <h3 className="text-xl font-semibold mb-2">
          {t('history.map_empty_title', { defaultText: 'Your Emotion Map Awaits' })}
        </h3>
        <p className="text-sm">
          {t('history.map_empty_subtitle', {
            defaultText:
              'Emotion definitions seem to be missing. Try restarting the app or checking data sync.',
          })}
        </p>
      </div>
    );
  }

  // Check if there's any history to determine if the user has started logging
  const hasAnyHistory = history && history.length > 0;

  return (
    <div className="p-4 animate-fade-in">
      <h2 className="text-2xl font-bold text-center mb-6">
        {t('history.map_title', { defaultText: 'Emotion Map Regions' })}
      </h2>

      {!hasAnyHistory && allPrimaryEmotions.length > 0 && (
        <div className="flex flex-col items-center justify-center text-muted-foreground p-4 text-center mb-6">
          <p className="text-sm">
            {t('history.map_start_logging_prompt', {
              defaultText:
                'Start logging your moods to discover and explore these regions on your personal emotion map!',
            })}
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {allPrimaryEmotions.map((region: Emotion) => {
          const isExplored = loggedPrimaryEmotionIds.has(region.id);
          const cardStyle = isExplored
            ? {
                backgroundColor: `${region.color}30`,
                borderColor: region.color,
                borderWidth: '2px',
                borderStyle: 'solid',
              }
            : {
                backgroundColor: 'hsl(var(--muted))', // Muted background for unexplored
                borderColor: 'hsl(var(--border))',
                borderWidth: '2px',
                borderStyle: 'dashed', // Dashed border for unexplored
                filter: 'saturate(0.5) opacity(0.7)', // Desaturate and slightly transparent
              };
          const textColor = isExplored ? region.color : 'hsl(var(--muted-foreground))';

          return (
            <div
              key={region.id}
              className="p-6 rounded-xl shadow-lg flex flex-col items-center justify-center text-center transition-all hover:shadow-2xl"
              style={cardStyle}
            >
              <span className="text-3xl mb-3">
                {' '}
                {/* Placeholder for a region icon */}
                {isExplored ? '🌟' : '❓'} {/* Simple icon differentiation */}
              </span>
              <h3 className="text-lg font-semibold" style={{ color: textColor }}>
                {region.name}
              </h3>
              <p className="text-xs text-muted-foreground mt-1">
                {isExplored
                  ? t('history.map_region_explored', { defaultText: 'Region Explored' })
                  : t('history.map_region_undiscovered', { defaultText: 'Undiscovered' })}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default EmotionMapDisplay;
