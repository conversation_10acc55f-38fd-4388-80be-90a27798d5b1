import { useSQLiteDB } from '@/lib/useSqLite';
import { Services } from '@/services';
import type React from 'react';
import { type ReactNode, createContext, useContext, useEffect, useState } from 'react';
import { useLanguage } from './LanguageContext';

interface AppLoadingContextType {
  isAppLoading: boolean;
  loadingMessage: string;
  setLoadingMessage: (message: string) => void;
  markAppAsLoaded: () => void;
}

const AppLoadingContext = createContext<AppLoadingContextType>({
  isAppLoading: true,
  loadingMessage: 'Initializing...',
  setLoadingMessage: () => {},
  markAppAsLoaded: () => {},
});

interface AppLoadingProviderProps {
  children: ReactNode;
}

export const AppLoadingProvider: React.FC<AppLoadingProviderProps> = ({ children }) => {
  const [isAppLoading, setIsAppLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('Initializing...');
  const [_firstLoadComplete, _setFirstLoadComplete] = useState(false);

  // Get database and language initialization status
  const { isDatabaseInitialised } = useSQLiteDB();
  const { isLanguageReady } = useLanguage();

  // Check if app has been loaded before through service layer
  useEffect(() => {
    const checkAppLoadStatus = async () => {
      try {
        // 只有在数据库初始化完成后才尝试访问服务层
        if (!isDatabaseInitialised) {
          console.log('[AppLoadingContext] Database not initialized, skipping service check');
          return;
        }

        // 尝试从服务层获取应用加载状态
        const userConfigService = Services.getUserConfigService();
        const configResult = await userConfigService.getUserConfigs('default-user');

        let hasLoadedBefore = false;
        if (configResult.success && configResult.data && configResult.data.length > 0) {
          // 如果有用户配置，说明应用之前已经加载过
          hasLoadedBefore = true;
        }

        if (hasLoadedBefore) {
          console.log(
            '[AppLoadingContext] App has been loaded before, waiting for critical resources...'
          );
        } else {
          console.log('[AppLoadingContext] First time app load detected, showing full loading screen');
        }
      } catch (error) {
        console.error('[AppLoadingContext] Failed to check app load status from service:', error);
        // 回退到 localStorage
        const hasLoadedBefore = localStorage.getItem('app_loaded_before') === 'true';
        if (hasLoadedBefore) {
          console.log('[AppLoadingContext] App has been loaded before (from localStorage fallback)');
        } else {
          console.log('[AppLoadingContext] First time app load detected (from localStorage fallback)');
        }
      }
    };

    checkAppLoadStatus();
  }, [isDatabaseInitialised]);

  // Monitor initialization of critical app resources
  useEffect(() => {
    console.log(
      `[AppLoadingContext] Checking app readiness: DB=${isDatabaseInitialised}, Lang=${isLanguageReady}, AppLoading=${isAppLoading}`
    );

    // 如果应用已经完成加载，跳过
    if (!isAppLoading) {
      console.log('[AppLoadingContext] App already loaded, skipping check');
      return;
    }

    // 更新加载消息
    if (!isDatabaseInitialised) {
      setLoadingMessage('Initializing database...');
    } else if (!isLanguageReady) {
      setLoadingMessage('Loading translations...');
    } else {
      setLoadingMessage('Starting application...');
    }

    // 检查是否所有关键资源都已准备就绪
    if (isDatabaseInitialised && isLanguageReady) {
      console.log('[AppLoadingContext] All critical resources initialized, app ready to load');

      // 立即设置应用为已加载，不需要延迟
      console.log('[AppLoadingContext] Setting app as loaded');
      setIsAppLoading(false);
      _setFirstLoadComplete(true);

      // 通过服务层保存应用加载状态
      const saveAppLoadStatus = async () => {
        try {
          // 确保数据库已初始化
          if (!isDatabaseInitialised) {
            console.log('[AppLoadingContext] Database not ready, using localStorage fallback');
            localStorage.setItem('app_loaded_before', 'true');
            return;
          }

          const userConfigService = Services.getUserConfigService();
          const configResult = await userConfigService.getUserConfigs('default-user');

          if (configResult.success && configResult.data && configResult.data.length > 0) {
            // User config exists, no need to update anything
            console.log('[AppLoadingContext] App load status already tracked via user config existence');
          }
        } catch (error) {
          console.error('[AppLoadingContext] Failed to save app load status to service:', error);
          // 回退到 localStorage
          localStorage.setItem('app_loaded_before', 'true');
        }
      };

      saveAppLoadStatus();
    }

    // 安全超时：如果30秒后仍未完成，强制加载
    const safetyTimer = setTimeout(async () => {
      if (isAppLoading) {
        console.log('[AppLoadingContext] SAFETY TIMEOUT: Forcing app to load after 30 seconds');
        setIsAppLoading(false);
        _setFirstLoadComplete(true);

        // 通过服务层保存应用加载状态
        try {
          // 确保数据库已初始化
          if (!isDatabaseInitialised) {
            console.log('[AppLoadingContext] Database not ready (safety timeout), using localStorage fallback');
            localStorage.setItem('app_loaded_before', 'true');
            return;
          }

          const userConfigService = Services.getUserConfigService();
          const configResult = await userConfigService.getUserConfigs('default-user');

          if (configResult.success && configResult.data && configResult.data.length > 0) {
            // User config exists, no need to update anything
            console.log('[AppLoadingContext] App load status already tracked via user config existence (safety timeout)');
          }
        } catch (error) {
          console.error('[AppLoadingContext] Failed to save app load status (safety timeout):', error);
          // 回退到 localStorage
          localStorage.setItem('app_loaded_before', 'true');
        }
      }
    }, 30000);

    return () => clearTimeout(safetyTimer);
  }, [isDatabaseInitialised, isLanguageReady, isAppLoading]);

  const markAppAsLoaded = async () => {
    setIsAppLoading(false);

    // 通过服务层保存应用加载状态
    try {
      // 确保数据库已初始化
      if (!isDatabaseInitialised) {
        console.log('[AppLoadingContext] Database not ready (markAppAsLoaded), using localStorage fallback');
        localStorage.setItem('app_loaded_before', 'true');
        return;
      }

      const userConfigService = Services.getUserConfigService();
      const configResult = await userConfigService.getUserConfigs('default-user');

      if (configResult.success && configResult.data && configResult.data.length > 0) {
        // User config exists, no need to update anything
        console.log('[AppLoadingContext] App marked as loaded via service (user config exists)');
      }
    } catch (error) {
      console.error('[AppLoadingContext] Failed to mark app as loaded via service:', error);
      // 回退到 localStorage
      localStorage.setItem('app_loaded_before', 'true');
    }
  };

  return (
    <AppLoadingContext.Provider
      value={{
        isAppLoading,
        loadingMessage,
        setLoadingMessage,
        markAppAsLoaded,
      }}
    >
      {children}
    </AppLoadingContext.Provider>
  );
};

export const useAppLoading = () => useContext(AppLoadingContext);
