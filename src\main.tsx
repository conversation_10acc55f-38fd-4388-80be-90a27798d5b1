// import React from "react"; // Temporarily commented out since StrictMode is disabled
// import ReactDOM from "react-dom/client";
import { Capacitor } from '@capacitor/core';
import App from './App.tsx';
import './index.css';
import { CapacitorSQLite, SQLiteConnection } from '@capacitor-community/sqlite';
import { JeepSqlite } from 'jeep-sqlite/dist/components/jeep-sqlite';
import { createRoot } from 'react-dom/client';

// 暴露到全局作用域以便调试和其他组件使用
(window as any).CapacitorSQLite = CapacitorSQLite;
(window as any).SQLiteConnection = SQLiteConnection;

console.log('[main] Global CapacitorSQLite set:', (window as any).CapacitorSQLite);
console.log('[main] Global SQLiteConnection set:', (window as any).SQLiteConnection);
window.addEventListener('DOMContentLoaded', async () => {
  try {
    const platform = Capacitor.getPlatform();

    // WEB SPECIFIC FUNCTIONALITY
    if (platform === 'web') {
      console.log('[main] Initializing SQLite for web platform...');
      console.log('[main] CapacitorSQLite available:', CapacitorSQLite);
      console.log('[main] SQLiteConnection available:', SQLiteConnection);

      const sqlite = new SQLiteConnection(CapacitorSQLite);
      console.log('[main] SQLiteConnection instance created:', sqlite);

      // Create the 'jeep-sqlite' Stencil component
      console.log('[main] Defining jeep-sqlite custom element...');
      console.log('[main] JeepSqlite class:', JeepSqlite);

      // 检查是否已经定义
      if (!customElements.get('jeep-sqlite')) {
        customElements.define('jeep-sqlite', JeepSqlite);
        console.log('[main] jeep-sqlite custom element defined');
      } else {
        console.log('[main] jeep-sqlite custom element already defined');
      }

      // 检查是否已经存在 jeep-sqlite 元素
      let jeepSqliteEl = document.querySelector('jeep-sqlite');
      if (!jeepSqliteEl) {
        jeepSqliteEl = document.createElement('jeep-sqlite');
        document.body.appendChild(jeepSqliteEl);
        console.log('[main] jeep-sqlite element created and added to DOM');
      } else {
        console.log('[main] jeep-sqlite element already exists in DOM');
      }

      await customElements.whenDefined('jeep-sqlite');
      console.log('[main] jeep-sqlite custom element ready');

      // 检查元素状态
      console.log('[main] jeep-sqlite element:', jeepSqliteEl);
      console.log(
        '[main] jeep-sqlite element methods:',
        Object.getOwnPropertyNames(Object.getPrototypeOf(jeepSqliteEl))
      );

      // Initialize the Web store
      console.log('[main] Initializing web store...');
      try {
        await sqlite.initWebStore();
        console.log('[main] Web store initialized successfully');

        // 验证存储是否可用
        const isStoreOpen = await (jeepSqliteEl as any).isStoreOpen();
        console.log('[main] Store open status:', isStoreOpen);
      } catch (storeError) {
        console.error('[main] Error initializing web store:', storeError);
      }
    }

    const container = document.getElementById('root');
    const root = createRoot(container!);

    // Temporarily disable StrictMode to test for duplicate initialization issues
    // In development, use StrictMode to help find potential problems
    // In production, remove StrictMode to avoid double-rendering
    if (import.meta.env.DEV) {
      console.log(
        '[main] Running in development mode WITHOUT StrictMode (temporarily disabled for testing)'
      );
      root.render(<App />);
    } else {
      console.log('[main] Running in production mode without StrictMode');
      root.render(<App />);
    }
  } catch (e) {
    console.error(e);
  }
});
