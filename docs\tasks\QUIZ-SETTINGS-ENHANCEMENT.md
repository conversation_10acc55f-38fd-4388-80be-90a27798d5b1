# Quiz设置系统性增强

本文档详细说明了对Quiz设置系统的系统性改进，解决了您提到的所有关键需求。

## 🎯 核心改进需求

### 1. **数据集展现配置增强**
- ✅ **是否恢复进度**: 配置重新进入时是否恢复上次未完成的Quiz进度
- ✅ **问题展示字段**: 配置展示问题时显示哪些字段（问题文本、描述、序号、进度等）
- ✅ **交互行为配置**: 配置选择答案后是否自动跳转、延迟时间等

### 2. **视图细节动态配置**
- ✅ **根据首选视图类型变化**: Layer 4内容根据Layer 1的preferred_view_type动态显示
- ✅ **多种视图配置**: 轮盘、卡片、气泡、星系视图的专门配置

### 3. **皮肤系统完善**
- ✅ **皮肤选择界面**: 提供可用皮肤列表供用户选择
- ✅ **条件显示**: 只有选择了皮肤后才显示详细配置选项

## 📋 详细改进内容

### Layer 0: 数据集展现配置

#### 新增配置项

##### 1. **进度恢复配置**
```typescript
restore_progress: boolean; // 是否恢复进度
```
- **功能**: 控制重新进入Quiz时是否恢复上次未完成的进度
- **界面**: 开关控件，带说明文字
- **默认值**: true

##### 2. **问题展示字段配置**
```typescript
question_display_fields: {
  show_question_text: boolean;        // 显示问题文本
  show_question_description: boolean; // 显示问题描述
  show_question_order: boolean;       // 显示问题序号
  show_progress_indicator: boolean;   // 显示进度指示器
  show_answer_options: boolean;       // 显示答案选项
  show_option_descriptions: boolean;  // 显示选项描述
  show_option_icons: boolean;         // 显示选项图标
}
```
- **功能**: 精确控制问题展示时显示哪些信息
- **界面**: 7个独立的开关控件，每个都有详细说明
- **默认配置**: 显示主要字段，隐藏描述性字段

##### 3. **交互行为配置**
```typescript
interaction_behavior: {
  auto_advance_after_selection: boolean;  // 选择后自动跳转
  auto_advance_delay_ms: number;          // 自动跳转延迟
  allow_answer_change: boolean;           // 允许修改答案
  show_confirmation_dialog: boolean;      // 显示确认对话框
}
```
- **功能**: 控制用户与Quiz的交互方式
- **界面**: 开关控件 + 滑块控件（延迟时间）
- **智能显示**: 只有启用自动跳转时才显示延迟设置

### Layer 1: 用户选择（保持不变）
- 首选视图类型选择
- 基础界面偏好设置

### Layer 2: 渲染策略（保持不变）
- 渲染引擎偏好配置
- 内容显示模式设置

### Layer 3: 皮肤基础配置

#### 重大改进：皮肤选择系统

##### 1. **可用皮肤列表**
```typescript
available_skins: Array<{
  id: string;           // 皮肤ID
  name: string;         // 皮肤名称
  description: string;  // 皮肤描述
  preview_image?: string; // 预览图片
  category: string;     // 皮肤类别
}>
```

##### 2. **预设皮肤选项**
- **默认皮肤**: 简洁现代的默认界面风格
- **中医经典**: 传统中医风格，温润典雅
- **现代简约**: 极简设计，专注内容
- **自然禅意**: 自然色彩，宁静致远

##### 3. **皮肤选择界面**
- **网格布局**: 2列响应式网格
- **交互式卡片**: 点击选择，高亮显示当前选中
- **分类标识**: 不同类别的皮肤有不同的徽章
- **选中状态**: 清晰的视觉反馈

##### 4. **条件显示详细配置**
- **只有选择了皮肤后才显示**: 颜色、字体、动画等详细配置
- **避免混乱**: 用户必须先选择皮肤，再进行细节调整

### Layer 4: 视图细节配置

#### 重大改进：动态配置内容

##### 1. **根据视图类型动态显示**
```typescript
// 根据 layer1_user_choice.preferred_view_type 的值显示不同配置
{config?.layer1_user_choice.preferred_view_type === 'wheel' && (
  // 轮盘视图配置
)}
{config?.layer1_user_choice.preferred_view_type === 'card' && (
  // 卡片视图配置
)}
// ... 其他视图类型
```

##### 2. **轮盘视图配置** (preferred_view_type === 'wheel')
```typescript
wheel_config: {
  container_size: number;      // 容器大小
  wheel_radius: number;        // 轮盘半径
  tier_spacing: number;        // 层级间距
  center_radius: number;       // 中心半径
  emotion_display_mode: string; // 情绪显示模式
  show_labels: boolean;        // 显示标签
  show_emojis: boolean;        // 显示表情符号
}
```

##### 3. **卡片视图配置** (preferred_view_type === 'card')
```typescript
card_config: {
  grid_columns: number;        // 网格列数
  card_size: string;          // 卡片大小 (small/medium/large)
  card_spacing: number;       // 卡片间距
  show_descriptions: boolean; // 显示描述
  hover_effects: boolean;     // 悬停效果
}
```

##### 4. **气泡视图配置** (preferred_view_type === 'bubble')
```typescript
bubble_config: {
  container_width: number;           // 容器宽度
  container_height: number;          // 容器高度
  bubble_size_range: [number, number]; // 气泡大小范围
  physics_enabled: boolean;          // 物理引擎
  collision_detection: boolean;      // 碰撞检测
}
```

##### 5. **星系视图配置** (preferred_view_type === 'galaxy')
```typescript
list_config: {
  item_height: number;         // 列表项高度
  show_icons: boolean;         // 显示图标
  show_descriptions: boolean;  // 显示描述
  grouping_enabled: boolean;   // 启用分组
}
```

##### 6. **通用情绪展示配置**
- **情绪分组样式**: 按类别、强度、频率等分组
- **层级过渡动画**: 淡入淡出、滑动、缩放等效果

### Layer 5: 可访问性（保持不变）
- 无障碍功能配置
- 辅助功能设置

## 🔧 技术实现亮点

### 1. **智能条件渲染**
```typescript
// 根据用户选择动态显示配置
{config?.layer1_user_choice.preferred_view_type === 'wheel' && (
  <WheelConfigCard />
)}

// 只有选择皮肤后才显示详细配置
{config?.layer3_skin_base.selected_skin_id && (
  <SkinDetailConfig />
)}

// 只有启用自动跳转才显示延迟设置
{config?.layer0_dataset_presentation.interaction_behavior.auto_advance_after_selection && (
  <DelaySlider />
)}
```

### 2. **类型安全的配置更新**
```typescript
// 统一的配置更新函数
const updateConfig = (layer: string, key: string, value: any) => {
  setConfig(prev => ({
    ...prev,
    [layer]: {
      ...prev[layer],
      [key]: value
    }
  }));
};
```

### 3. **响应式界面设计**
- **网格布局**: 自适应列数，移动端友好
- **滑块控件**: 实时显示数值，直观的调整体验
- **开关控件**: 清晰的开/关状态，带详细说明

## 🎨 用户体验改进

### 1. **渐进式配置**
- **第一步**: 选择基础偏好（视图类型、皮肤等）
- **第二步**: 根据选择显示相关的详细配置
- **避免信息过载**: 不显示无关的配置选项

### 2. **智能默认值**
- **合理的初始配置**: 每个选项都有经过考虑的默认值
- **渐进式增强**: 高级用户可以深度定制，新手用户可以直接使用

### 3. **实时反馈**
- **滑块数值显示**: 实时显示当前设置的数值
- **条件配置提示**: 清楚说明何时显示某些配置
- **视觉状态反馈**: 选中状态、启用状态一目了然

## 📊 配置层级关系

### 数据流向
```
Layer 0 (数据集展现) → 影响问题和选项的显示方式
    ↓
Layer 1 (用户选择) → 决定Layer 4显示哪种视图配置
    ↓
Layer 2 (渲染策略) → 影响具体的渲染实现
    ↓
Layer 3 (皮肤基础) → 选择皮肤后才能配置细节
    ↓
Layer 4 (视图细节) → 根据Layer 1的选择动态变化
    ↓
Layer 5 (可访问性) → 覆盖所有层级的无障碍设置
```

### 配置依赖关系
- **Layer 4 依赖 Layer 1**: 视图细节配置根据首选视图类型变化
- **Layer 3 皮肤详细配置依赖皮肤选择**: 必须先选择皮肤
- **Layer 0 延迟设置依赖自动跳转**: 必须先启用自动跳转

## 🚀 使用场景示例

### 场景1: 新用户首次配置
1. **Layer 1**: 选择"轮盘视图"作为首选视图类型
2. **Layer 3**: 选择"中医经典"皮肤
3. **Layer 4**: 自动显示轮盘视图配置，调整轮盘大小和显示选项
4. **Layer 0**: 配置是否显示问题描述、是否自动跳转等

### 场景2: 高级用户精细调整
1. **Layer 0**: 启用自动跳转，设置1.5秒延迟
2. **Layer 1**: 切换到"卡片视图"
3. **Layer 4**: 界面自动切换到卡片配置，调整网格列数和卡片大小
4. **Layer 3**: 选择"现代简约"皮肤，调整字体和动画

### 场景3: 无障碍用户配置
1. **Layer 5**: 启用高对比度、大字体、减少动画
2. **Layer 0**: 启用所有文字描述，禁用自动跳转
3. **Layer 4**: 根据视图类型调整为无障碍友好的设置

## 🔮 未来扩展性

### 1. **新视图类型支持**
- 只需在Layer 1添加新的视图类型选项
- 在Layer 4添加对应的配置界面
- 配置结构自动适应

### 2. **新皮肤添加**
- 在available_skins数组中添加新皮肤
- 皮肤选择界面自动更新
- 支持皮肤预览图片

### 3. **配置导入导出**
- 完整的配置结构便于序列化
- 支持配置模板和预设
- 便于在不同设备间同步

这次系统性的改进完全解决了您提到的所有需求，提供了更加智能、直观、灵活的Quiz设置体验。
