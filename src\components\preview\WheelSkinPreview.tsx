/**
 * 轮盘皮肤预览组件
 * 用于在设置页面中展示轮盘视图的皮肤预览
 */

import React, { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Skin, ContentDisplayMode, RenderEngine } from '@/types';
import { ViewFactory } from '@/utils/viewFactory';
import { Card, CardContent } from '../ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Label } from '../ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';

interface WheelSkinPreviewProps {
  skin: Skin;
  size?: 'sm' | 'md' | 'lg';
  showControls?: boolean;
  className?: string;
}

/**
 * 轮盘皮肤预览组件
 */
const WheelSkinPreview: React.FC<WheelSkinPreviewProps> = ({
  skin,
  size = 'md',
  showControls = true,
  className = ''
}) => {
  const { t } = useLanguage();
  const [contentDisplayMode, setContentDisplayMode] = useState<ContentDisplayMode>('textEmoji');
  const [RenderEngine, setRenderEngine] = useState<RenderEngine>('D3');

  // 创建示例情绪数据用于预览
  const sampleEmotions = [
    { id: '1', name: t('emotions.happy', { fallback: 'Happy' }), emoji: '😊', color: '#FFD700' },
    { id: '2', name: t('emotions.sad', { fallback: 'Sad' }), emoji: '😢', color: '#4169E1' },
    { id: '3', name: t('emotions.angry', { fallback: 'Angry' }), emoji: '😠', color: '#FF4500' },
    { id: '4', name: t('emotions.fear', { fallback: 'Fear' }), emoji: '😨', color: '#800080' },
    { id: '5', name: t('emotions.surprise', { fallback: 'Surprise' }), emoji: '😲', color: '#FF69B4' },
    { id: '6', name: t('emotions.disgust', { fallback: 'Disgust' }), emoji: '🤢', color: '#32CD32' },
  ];

  // 获取容器尺寸
  const getContainerSize = () => {
    switch (size) {
      case 'sm':
        return { width: 200, height: 200 };
      case 'lg':
        return { width: 400, height: 400 };
      case 'md':
      default:
        return { width: 300, height: 300 };
    }
  };

  const containerSize = getContainerSize();

  // 创建轮盘视图
  const createWheelView = () => {
    // 使用 ViewFactory 静态方法创建视图
    const view = ViewFactory.createView(
      'wheel',
      contentDisplayMode,
      skin.config,
      { implementation: RenderEngine }
    );

    // 渲染视图
    return view.render(
      sampleEmotions,
      1,
      () => {} // 空函数，因为这只是预览
    );
  };

  return (
    <div className={`wheel-skin-preview ${className}`}>
      <Card className="border shadow-sm">
        {showControls && (
          <div className="p-3 border-b">
            <div className="flex flex-col gap-2">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="RenderEngine" className="text-xs mb-1 block">
                    {t('skin_preview.wheel_implementation', { fallback: '轮盘实现' })}
                  </Label>
                  <Select
                    value={RenderEngine}
                    onValueChange={(value) => setRenderEngine(value as RenderEngine)}
                  >
                    <SelectTrigger id="RenderEngine" className="h-8 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="D3">D3</SelectItem>
                      <SelectItem value="SVG">SVG</SelectItem>
                      <SelectItem value="R3F">3D</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <Label htmlFor="contentDisplayMode" className="text-xs mb-1 block">
                    {t('skin_preview.content_display_mode', { fallback: '内容显示' })}
                  </Label>
                  <Select
                    value={contentDisplayMode}
                    onValueChange={(value) => setContentDisplayMode(value as ContentDisplayMode)}
                  >
                    <SelectTrigger id="contentDisplayMode" className="h-8 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">{t('skin_preview.text_only', { fallback: '仅文本' })}</SelectItem>
                      <SelectItem value="emoji">{t('skin_preview.emoji_only', { fallback: '仅表情' })}</SelectItem>
                      <SelectItem value="textEmoji">{t('skin_preview.text_emoji', { fallback: '文本+表情' })}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        )}
        <CardContent className="p-2 flex items-center justify-center">
          <div
            className="wheel-preview-container"
            style={{
              width: `${containerSize.width}px`,
              height: `${containerSize.height}px`,
              overflow: 'hidden',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {createWheelView()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WheelSkinPreview;
