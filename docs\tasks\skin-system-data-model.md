# 皮肤系统数据模型设计

## 概述

本文档详细说明皮肤系统升级后的数据模型设计，包括各个实体的结构、关系和职责。

## 核心实体

### 1. EmotionData（情绪数据）

情绪数据是指情绪的层级结构和内容，与表现形式无关。

```typescript
interface EmotionData {
  id: string;                // 唯一标识符
  name: string;              // 名称
  description?: string;      // 描述
  created_at: Date;           // 创建时间
  updated_at: Date;           // 更新时间
  tiers: EmotionTier[];      // 情绪层级
  isActive: boolean;         // 是否激活
  isDefault: boolean;        // 是否默认
  created_by?: string;        // 创建者（系统预设或用户ID）
}

interface EmotionTier {
  id: string;                // 唯一标识符
  name: string;              // 层级名称
  level: number;             // 层级级别（1=主要，2=次要，3=细分）
  parentTierId?: string;     // 父层级ID
  emotions: Emotion[];       // 层级中的情绪
}

interface Emotion {
  id: string;                // 唯一标识符
  name: string;              // 情绪名称
  emoji: string;             // 情绪表情
  color?: string;            // 情绪颜色（可选）
  keywords?: string[];       // 关键词（可选，用于搜索和分类）
}
```

### 2. Skin（皮肤）

皮肤定义了情绪数据的视觉表现，包括颜色、字体、效果等。

```typescript
interface Skin {
  id: string;                // 唯一标识符
  name: string;              // 皮肤名称
  description?: string;      // 描述
  category: SkinCategory;    // 分类（免费、付费、自定义等）
  config: SkinConfig;        // 皮肤配置
  previewImage?: string;     // 预览图片
  isUnlocked: boolean;       // 是否已解锁
  created_by?: string;        // 创建者（系统预设或用户ID）
  created_at: Date;           // 创建时间
  updated_at: Date;           // 更新时间
}

type SkinCategory =
  | 'free'      // 免费皮肤
  | 'paid'      // 付费皮肤
  | 'basic'     // 基础皮肤（兼容旧版本）
  | 'premium'   // 高级皮肤（兼容旧版本）
  | 'custom'    // 自定义皮肤
  | 'limited'   // 限时皮肤
  | 'seasonal'  // 季节性皮肤
  | 'event'     // 活动皮肤
  | 'special'   // 特殊皮肤
  | 'system';   // 系统皮肤

interface SkinConfig {
  // 支持的内容显示模式和视图类型
  supported_content_modes?: ContentDisplayMode[];  // 支持的内容显示模式
  supported_view_types?: ViewType[];  // 支持的视图类型
  supported_render_engines?: RenderEngine[];  // 支持的渲染引擎

  // 颜色配置
  colors: {
    primary: string;         // 主色
    secondary: string;       // 次色
    background: string;      // 背景色
    text: string;            // 文本色
    accent: string;          // 强调色
  };

  // 字体配置
  fonts: {
    family: string;          // 字体族
    size: {
      small: number;         // 小字体大小
      medium: number;        // 中字体大小
      large: number;         // 大字体大小
    };
    weight: {
      normal: number;        // 普通字重
      bold: number;          // 粗体字重
    };
  };

  // 效果配置
  effects: {
    shadows?: boolean;        // 是否启用阴影
    shadowColor?: string;    // 阴影颜色
    shadowBlur?: number;     // 阴影模糊半径
    shadowOffsetX?: number;  // 阴影X轴偏移
    shadowOffsetY?: number;  // 阴影Y轴偏移
    animations?: boolean;     // 是否启用动画
    animationDuration?: number; // 动画持续时间（毫秒）
    animationEasing?: string;   // 动画缓动函数
    borderRadius?: number;    // 边框圆角
    borderWidth?: number;    // 边框宽度
    borderColor?: string;    // 边框颜色
    borderStyle?: string;    // 边框样式
    opacity?: number;         // 透明度
    blur?: number;           // 模糊效果
    grayscale?: number;      // 灰度效果
    brightness?: number;     // 亮度调整
    contrast?: number;       // 对比度调整
    saturation?: number;     // 饱和度调整
    hueRotate?: number;      // 色相旋转
    backdropFilter?: string; // 背景滤镜
    backgroundGradient?: string; // 背景渐变
    textShadow?: string;     // 文字阴影
  };

  // 视图特定配置
  view_configs?: {
    // 通用视图配置
    common?: {
      background?: {
        type: 'solid' | 'gradient' | 'image' | 'pattern';
        color?: string;                // 纯色背景
        gradient?: string;             // 渐变背景
        imageUrl?: string;             // 图片背景
        patternType?: string;          // 图案类型
        opacity?: number;              // 背景透明度
        blur?: number;                 // 背景模糊效果
      };
      container?: {
        padding?: number;              // 容器内边距
        margin?: number;               // 容器外边距
        maxWidth?: number;             // 最大宽度
        maxHeight?: number;            // 最大高度
        aspectRatio?: number;          // 宽高比
        overflow?: 'visible' | 'hidden' | 'scroll' | 'auto';
        position?: 'static' | 'relative' | 'absolute' | 'fixed';
      };
      animation?: {
        entranceAnimation?: string;    // 入场动画
        exitAnimation?: string;        // 退场动画
        duration?: number;             // 动画持续时间
        delay?: number;                // 动画延迟
        easing?: string;               // 缓动函数
        loop?: boolean;                // 是否循环
      };
      responsive?: {
        breakpoints?: {
          small?: number;              // 小屏幕断点
          medium?: number;             // 中屏幕断点
          large?: number;              // 大屏幕断点
        };
        adaptiveLayout?: boolean;      // 是否启用自适应布局
      };
    };

    // 轮盘视图配置
    wheel?: {
      containerSize?: number;          // 容器大小
      wheelRadius?: number;            // 轮盘半径
      innerRadius?: number;            // 内圆半径（用于多层轮盘）
      sectorGap?: number;              // 扇区间隔
      sectorPadding?: number;          // 扇区内边距
      sectorBorderRadius?: number;     // 扇区边框圆角
      sectorBorderWidth?: number;      // 扇区边框宽度
      sectorBorderColor?: string;      // 扇区边框颜色
      sectorStrokeWidth?: number;      // 扇区描边宽度
      sectorStrokeColor?: string;      // 扇区描边颜色
      centerCircleSize?: number;       // 中心圆大小
      centerCircleColor?: string;      // 中心圆颜色
      emojiSize?: number;              // 表情符号大小
      emojiPosition?: 'center' | 'inner' | 'outer'; // 表情符号位置
      textSize?: number;               // 文本大小
      textPosition?: 'center' | 'inner' | 'outer'; // 文本位置
      textColor?: string;              // 文本颜色
      textVisible?: 'always' | 'hover' | 'never'; // 文本可见性
      highlightOnHover?: boolean;      // 悬停时高亮
      highlightColor?: string;         // 高亮颜色
      decorations?: boolean;           // 是否启用装饰
      decorationType?: 'none'|'glow' | 'sparkles' | 'dots' | 'waves' | 'lines' | 'custom'; // 装饰类型
      decorationDensity?: number;      // 装饰密度
      decorationColor?: string;        // 装饰颜色
      use3DEffects?: boolean;          // 是否使用3D效果
      perspective?: number;            // 3D透视值
      rotateX?: number;                // X轴旋转角度
      rotateY?: number;                // Y轴旋转角度
      rotateZ?: number;                // Z轴旋转角度
      depth?: number;                  // 3D深度
      shadowEnabled?: boolean;         // 是否启用阴影
      shadowColor?: string;            // 阴影颜色
      shadowBlur?: number;             // 阴影模糊
      shadowOffsetX?: number;          // 阴影X偏移
      shadowOffsetY?: number;          // 阴影Y偏移
      hoverEffect?: 'scale' | 'glow' | 'lift' | 'pulse' | 'none'; // 悬停效果
      hoverScale?: number;             // 悬停缩放比例
      selectionAnimation?: 'pulse' | 'spin' | 'bounce' | 'flash' | 'none'; // 选择动画
      selectionIndicator?: 'border' | 'glow' | 'icon' | 'none'; // 选择指示器
      transitionDuration?: number;     // 过渡持续时间
      transitionEasing?: string;       // 过渡缓动函数
      wheelBackground?: string;        // 轮盘背景
      wheelBackgroundOpacity?: number; // 轮盘背景透明度
      tierTransition?: 'zoom' | 'slide' | 'fade' | 'flip' | 'none'; // 层级过渡效果
      tierTransitionDuration?: number; // 层级过渡持续时间
    };

    // 卡片视图配置
    card?: {
      cardSize?: number;               // 卡片大小
      cardWidth?: number;              // 卡片宽度
      cardHeight?: number;             // 卡片高度
      cardSpacing?: number;            // 卡片间距
      cardBorderRadius?: number;       // 卡片边框圆角
      cardBorderWidth?: number;        // 卡片边框宽度
      cardBorderColor?: string;        // 卡片边框颜色
      cardBorderStyle?: 'solid' | 'dashed' | 'dotted' | 'double'; // 卡片边框样式
      cardBackground?: string;         // 卡片背景
      cardBackgroundOpacity?: number;  // 卡片背景透明度
      cardShadow?: string;             // 卡片阴影
      cardElevation?: number;          // 卡片高度（Z轴）
      cardHoverEffect?: 'lift' | 'glow' | 'scale' | 'flip' | 'none'; // 卡片悬停效果
      cardHoverScale?: number;         // 卡片悬停缩放比例
      cardSelectedEffect?: 'border' | 'glow' | 'scale' | 'none'; // 卡片选中效果
      cardSelectedBorderColor?: string; // 卡片选中边框颜色
      cardSelectedGlowColor?: string;  // 卡片选中发光颜色
      cardCornerStyle?: 'rounded' | 'sharp' | 'cut'; // 卡片角落样式
      emojiSize?: number;              // 表情符号大小
      emojiPosition?: 'top' | 'center' | 'bottom'; // 表情符号位置
      textSize?: number;               // 文本大小
      textPosition?: 'top' | 'bottom' | 'overlay'; // 文本位置
      textColor?: string;              // 文本颜色
      textBackground?: string;         // 文本背景
      textBackgroundOpacity?: number;  // 文本背景透明度
      textPadding?: number;            // 文本内边距
      layout?: 'grid' | 'list' | 'masonry' | 'carousel'; // 布局类型
      columns?: number;                // 网格列数
      aspectRatio?: number;            // 卡片宽高比
      cardAnimation?: 'fade' | 'slide' | 'scale' | 'none'; // 卡片动画
      cardAnimationDuration?: number;  // 卡片动画持续时间
      cardAnimationDelay?: number;     // 卡片动画延迟
    };

    // 气泡视图配置
    bubble?: {
      bubbleSize?: number;             // 气泡大小
      bubbleMinSize?: number;          // 气泡最小大小
      bubbleMaxSize?: number;          // 气泡最大大小
      bubbleSpacing?: number;          // 气泡间距
      bubbleShape?: 'circle' | 'oval' | 'rounded-rect' | 'custom'; // 气泡形状
      bubbleBorderRadius?: number;     // 气泡边框圆角
      bubbleBorderWidth?: number;      // 气泡边框宽度
      bubbleBorderColor?: string;      // 气泡边框颜色
      bubbleBackground?: string;       // 气泡背景
      bubbleBackgroundOpacity?: number; // 气泡背景透明度
      bubbleGradient?: boolean;        // 是否使用渐变
      bubbleGradientColors?: string[]; // 渐变颜色
      bubbleGradientDirection?: 'radial' | 'linear'; // 渐变方向
      bubbleShadow?: boolean;          // 是否启用阴影
      bubbleShadowColor?: string;      // 阴影颜色
      bubbleShadowBlur?: number;       // 阴影模糊
      bubbleShadowOffsetX?: number;    // 阴影X偏移
      bubbleShadowOffsetY?: number;    // 阴影Y偏移
      bubbleHoverEffect?: 'scale' | 'glow' | 'pulse' | 'none'; // 气泡悬停效果
      bubbleHoverScale?: number;       // 气泡悬停缩放比例
      bubbleSelectedEffect?: 'border' | 'glow' | 'scale' | 'none'; // 气泡选中效果
      bubbleSelectedBorderColor?: string; // 气泡选中边框颜色
      bubbleSelectedGlowColor?: string; // 气泡选中发光颜色
      emojiSize?: number;              // 表情符号大小
      textVisible?: 'always' | 'hover' | 'never'; // 文本可见性
      textSize?: number;               // 文本大小
      textColor?: string;              // 文本颜色
      textBackground?: string;         // 文本背景
      textBackgroundOpacity?: number;  // 文本背景透明度
      textPosition?: 'inside' | 'outside' | 'tooltip'; // 文本位置
      layout?: 'circle' | 'cluster' | 'float' | 'grid'; // 布局类型
      floatingAnimation?: boolean;     // 是否启用浮动动画
      floatingSpeed?: number;          // 浮动速度
      floatingAmplitude?: number;      // 浮动幅度
      collisionDetection?: boolean;    // 是否启用碰撞检测
      dragEnabled?: boolean;           // 是否可拖动
      snapToGrid?: boolean;            // 是否对齐网格
      responsiveScaling?: boolean;     // 是否响应式缩放
    };

    // 星系视图配置
    galaxy?: {
      starSize?: number;               // 星星大小
      starMinSize?: number;            // 星星最小大小
      starMaxSize?: number;            // 星星最大大小
      orbitSize?: number;              // 轨道大小
      orbitWidth?: number;             // 轨道宽度
      orbitColor?: string;             // 轨道颜色
      orbitOpacity?: number;           // 轨道透明度
      orbitVisible?: boolean;          // 轨道是否可见
      centerSize?: number;             // 中心大小
      centerColor?: string;            // 中心颜色
      centerGlow?: boolean;            // 中心是否发光
      centerGlowColor?: string;        // 中心发光颜色
      centerGlowSize?: number;         // 中心发光大小
      backgroundType?: 'stars' | 'nebula' | 'solid' | 'gradient'; // 背景类型
      backgroundColor?: string;        // 背景颜色
      backgroundStarDensity?: number;  // 背景星星密度
      backgroundStarColor?: string;    // 背景星星颜色
      backgroundNebulaColor?: string;  // 背景星云颜色
      backgroundNebulaOpacity?: number; // 背景星云透明度
      starShape?: 'circle' | 'star' | 'custom'; // 星星形状
      starColor?: string;              // 星星颜色
      starGlow?: boolean;              // 星星是否发光
      starGlowColor?: string;          // 星星发光颜色
      starGlowSize?: number;           // 星星发光大小
      starBorder?: boolean;            // 星星是否有边框
      starBorderColor?: string;        // 星星边框颜色
      starBorderWidth?: number;        // 星星边框宽度
      starHoverEffect?: 'scale' | 'glow' | 'pulse' | 'none'; // 星星悬停效果
      starHoverScale?: number;         // 星星悬停缩放比例
      starSelectedEffect?: 'border' | 'glow' | 'scale' | 'none'; // 星星选中效果
      starSelectedBorderColor?: string; // 星星选中边框颜色
      starSelectedGlowColor?: string;  // 星星选中发光颜色
      emojiSize?: number;              // 表情符号大小
      textVisible?: 'always' | 'hover' | 'never'; // 文本可见性
      textSize?: number;               // 文本大小
      textColor?: string;              // 文本颜色
      textBackground?: string;         // 文本背景
      textBackgroundOpacity?: number;  // 文本背景透明度
      textPosition?: 'inside' | 'outside' | 'tooltip'; // 文本位置
      layout?: 'circular' | 'spiral' | 'random' | 'custom'; // 布局类型
      rotationEnabled?: boolean;       // 是否启用旋转
      rotationSpeed?: number;          // 旋转速度
      rotationDirection?: 'clockwise' | 'counterclockwise'; // 旋转方向
      zoomEnabled?: boolean;           // 是否启用缩放
      zoomMinScale?: number;           // 最小缩放比例
      zoomMaxScale?: number;           // 最大缩放比例
      parallaxEffect?: boolean;        // 是否启用视差效果
      parallaxIntensity?: number;      // 视差强度
      perspective3D?: boolean;         // 是否启用3D透视
      perspective3DIntensity?: number; // 3D透视强度
    };

    // 情绪树视图配置
    tree?: {
      treeType?: 'vertical' | 'horizontal' | 'radial' | 'mindmap'; // 树类型
      nodeSize?: number;               // 节点大小
      nodeSpacing?: number;            // 节点间距
      levelSpacing?: number;           // 层级间距
      nodeBorderRadius?: number;       // 节点边框圆角
      nodeBorderWidth?: number;        // 节点边框宽度
      nodeBorderColor?: string;        // 节点边框颜色
      nodeBackground?: string;         // 节点背景
      nodeBackgroundOpacity?: number;  // 节点背景透明度
      nodeShadow?: boolean;            // 节点是否有阴影
      nodeShadowColor?: string;        // 节点阴影颜色
      nodeShadowBlur?: number;         // 节点阴影模糊
      nodeHoverEffect?: 'scale' | 'glow' | 'pulse' | 'none'; // 节点悬停效果
      nodeSelectedEffect?: 'border' | 'glow' | 'scale' | 'none'; // 节点选中效果
      linkStyle?: 'straight' | 'curved' | 'orthogonal' | 'step'; // 连接线样式
      linkWidth?: number;              // 连接线宽度
      linkColor?: string;              // 连接线颜色
      linkOpacity?: number;            // 连接线透明度
      linkAnimation?: boolean;         // 连接线是否有动画
      linkAnimationType?: 'dash' | 'flow' | 'pulse'; // 连接线动画类型
      linkAnimationSpeed?: number;     // 连接线动画速度
      emojiSize?: number;              // 表情符号大小
      emojiPosition?: 'left' | 'right' | 'top' | 'bottom' | 'center'; // 表情符号位置
      textSize?: number;               // 文本大小
      textColor?: string;              // 文本颜色
      textPosition?: 'inside' | 'outside'; // 文本位置
      collapsible?: boolean;           // 是否可折叠
      initiallyExpanded?: boolean;     // 初始是否展开
      zoomEnabled?: boolean;           // 是否启用缩放
      panEnabled?: boolean;            // 是否启用平移
      minimap?: boolean;               // 是否显示小地图
      minimapSize?: number;            // 小地图大小
      minimapPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'; // 小地图位置
    };

    // 流程图视图配置
    flow?: {
      nodeSize?: number;               // 节点大小
      nodeWidth?: number;              // 节点宽度
      nodeHeight?: number;             // 节点高度
      nodeSpacing?: number;            // 节点间距
      nodeBorderRadius?: number;       // 节点边框圆角
      nodeBorderWidth?: number;        // 节点边框宽度
      nodeBorderColor?: string;        // 节点边框颜色
      nodeBackground?: string;         // 节点背景
      nodeBackgroundOpacity?: number;  // 节点背景透明度
      nodeShadow?: boolean;            // 节点是否有阴影
      nodeHoverEffect?: 'scale' | 'glow' | 'pulse' | 'none'; // 节点悬停效果
      nodeSelectedEffect?: 'border' | 'glow' | 'scale' | 'none'; // 节点选中效果
      edgeStyle?: 'straight' | 'curved' | 'orthogonal' | 'bezier'; // 边样式
      edgeWidth?: number;              // 边宽度
      edgeColor?: string;              // 边颜色
      edgeOpacity?: number;            // 边透明度
      edgeAnimation?: boolean;         // 边是否有动画
      edgeAnimationType?: 'dash' | 'flow' | 'pulse'; // 边动画类型
      edgeAnimationSpeed?: number;     // 边动画速度
      arrowSize?: number;              // 箭头大小
      arrowColor?: string;             // 箭头颜色
      emojiSize?: number;              // 表情符号大小
      emojiPosition?: 'left' | 'right' | 'top' | 'bottom' | 'center'; // 表情符号位置
      textSize?: number;               // 文本大小
      textColor?: string;              // 文本颜色
      textPosition?: 'inside' | 'outside'; // 文本位置
      layout?: 'dagre' | 'force' | 'layered' | 'radial'; // 布局类型
      layoutDirection?: 'LR' | 'RL' | 'TB' | 'BT'; // 布局方向
      zoomEnabled?: boolean;           // 是否启用缩放
      panEnabled?: boolean;            // 是否启用平移
      minimap?: boolean;               // 是否显示小地图
    };

    // 标签云视图配置
    tagCloud?: {
      minFontSize?: number;            // 最小字体大小
      maxFontSize?: number;            // 最大字体大小
      fontFamily?: string;             // 字体族
      fontWeight?: number | string;    // 字体粗细
      fontStyle?: 'normal' | 'italic'; // 字体样式
      textColor?: string;              // 文本颜色
      colorScheme?: string[];          // 颜色方案
      shape?: 'circle' | 'rectangle' | 'triangle' | 'star' | 'custom'; // 形状
      padding?: number;                // 内边距
      rotationRange?: [number, number]; // 旋转范围
      spiral?: 'archimedean' | 'rectangular'; // 螺旋类型
      randomRotation?: boolean;        // 是否随机旋转
      randomColors?: boolean;          // 是否随机颜色
      backgroundColor?: string;        // 背景颜色
      backgroundOpacity?: number;      // 背景透明度
      border?: boolean;                // 是否有边框
      borderColor?: string;            // 边框颜色
      borderWidth?: number;            // 边框宽度
      shadow?: boolean;                // 是否有阴影
      shadowColor?: string;            // 阴影颜色
      shadowBlur?: number;             // 阴影模糊
      hoverEffect?: 'scale' | 'glow' | 'color' | 'none'; // 悬停效果
      hoverScale?: number;             // 悬停缩放比例
      hoverColor?: string;             // 悬停颜色
      selectedEffect?: 'scale' | 'glow' | 'color' | 'none'; // 选中效果
      selectedColor?: string;          // 选中颜色
      animation?: boolean;             // 是否有动画
      animationType?: 'rotate' | 'pulse' | 'float' | 'none'; // 动画类型
      animationSpeed?: number;         // 动画速度
      emojiVisible?: boolean;          // 表情符号是否可见
      emojiSize?: number;              // 表情符号大小
      emojiPosition?: 'prefix' | 'suffix' | 'none'; // 表情符号位置
    };
  };
}
```

### 3. UserConfig（用户配置）

用户配置存储用户的显示偏好，包括活动的情绪数据、皮肤和视图类型。

```typescript
interface UserConfig {
  activeEmotionDataId: string;           // 活动的情绪数据ID
  activeSkinId: string;                  // 活动的皮肤ID
  activeViewType: ViewType;              // 活动的视图类型
  contentDisplayMode: ContentDisplayMode; // 内容显示模式
  RenderEngine?: RenderEngine; // 轮盘实现类型（可选）
  lastUpdated: Date;                     // 最后更新时间
}

type ViewType = 'wheel' | 'card' | 'bubble' | 'list' | 'grid' | 'galaxy' | 'tree' | 'flow' | 'tagCloud';
type ContentDisplayMode = 'text' | 'emoji' | 'textEmoji';
type RenderEngine = 'D3' | 'SVG' | 'R3F' | 'Canvas';
```

## 实体关系

### 1. EmotionData 内部关系

- **EmotionTier 之间的关系**：通过 `parentTierId` 形成层级结构，一个 Tier 可以有一个父 Tier 和多个子 Tier。
- **EmotionTier 与 Emotion 的关系**：一个 Tier 包含多个 Emotion，形成一对多关系。

### 2. EmotionData 与 Skin 的关系

- EmotionData 和 Skin 是完全独立的实体，没有直接关系。
- 用户可以为任何 EmotionData 选择任何 Skin，实现数据与表现的完全分离。

### 3. UserConfig 与其他实体的关系

- UserConfig 通过 `activeEmotionDataId` 引用当前活动的 EmotionData。
- UserConfig 通过 `activeSkinId` 引用当前活动的 Skin。
- UserConfig 还存储其他显示偏好，如视图类型和内容显示模式。

## 数据流

1. **加载流程**：
   - 加载 UserConfig 获取用户偏好
   - 根据 UserConfig 加载活动的 EmotionData 和 Skin
   - 根据 ViewType 和其他配置创建适当的视图

2. **切换情绪数据**：
   - 用户选择新的 EmotionData
   - 更新 UserConfig 中的 `activeEmotionDataId`
   - 重新渲染视图，但保持相同的 Skin 和视图类型

3. **切换皮肤**：
   - 用户选择新的 Skin
   - 更新 UserConfig 中的 `activeSkinId`
   - 重新渲染视图，但保持相同的 EmotionData 和视图类型

4. **切换视图类型**：
   - 用户选择新的视图类型
   - 更新 UserConfig 中的 `activeViewType`
   - 创建新的视图实例，但使用相同的 EmotionData 和 Skin

## 存储策略

1. **EmotionData 存储**：
   - 系统预设的 EmotionData 存储在应用中
   - 用户创建的 EmotionData 存储在本地存储中
   - 支持导入导出功能，便于备份和共享

2. **Skin 存储**：
   - 系统预设的 Skin 存储在应用中
   - 用户创建的 Skin 存储在本地存储中
   - 支持导入导出功能，便于备份和共享

3. **UserConfig 存储**：
   - 存储在本地存储中
   - 定期自动保存，确保用户偏好不丢失

## 迁移策略

从现有的 CustomWheel 模型迁移到新的 EmotionData + Skin 模型：

1. **数据分离**：
   - 将 CustomWheel 中的层级和情绪数据提取为 EmotionData
   - 将 CustomWheel 中的皮肤相关配置提取为 Skin

2. **ID 映射**：
   - 为每个 CustomWheel 创建对应的 EmotionData，保持相同的 ID
   - 为每个 CustomWheel 创建对应的 Skin，生成新的 ID
   - 在 UserConfig 中记录映射关系

3. **默认配置**：
   - 对于缺失的配置，使用合理的默认值
   - 确保迁移后的体验与之前一致

## 视图配置应用场景分析

不同的视图配置适用于不同的应用场景，以下是对各种视图配置的应用场景分析：

### 1. 通用视图配置（common）

通用视图配置适用于所有视图类型，提供基础的视觉和交互体验：

- **背景配置**：
  - **渐变背景**：适用于创造沉浸式体验，如冥想或情绪调节应用
  - **图片背景**：适用于主题化皮肤，如季节性主题或特定情绪主题
  - **模糊效果**：适用于需要突出前景内容的场景，如专注模式

- **容器配置**：
  - **自适应宽高比**：适用于跨设备使用的应用，确保在不同屏幕尺寸下的一致体验
  - **溢出处理**：适用于内容丰富的视图，如详细的情绪分析报告

- **动画配置**：
  - **入场动画**：适用于提升用户体验和引导注意力的场景
  - **循环动画**：适用于需要持续视觉反馈的场景，如冥想或放松应用

- **响应式配置**：
  - **断点设置**：适用于需要在移动设备和桌面设备上都有良好体验的应用
  - **自适应布局**：适用于内容复杂的应用，确保在不同设备上的可用性

### 2. 轮盘视图配置（wheel）

轮盘视图是情绪选择的经典表现形式，适用于以下场景：

- **基础轮盘**（简单配置）：
  - 适用于：日常情绪记录、快速情绪选择
  - 关键配置：`containerSize`、`wheelRadius`、`sectorGap`、`emojiSize`
  - 应用场景：日记应用、情绪追踪工具

- **3D 轮盘**（高级视觉效果）：
  - 适用于：游戏化体验、沉浸式应用
  - 关键配置：`use3DEffects`、`perspective`、`rotateX/Y/Z`、`depth`
  - 应用场景：情绪教育游戏、互动式情绪学习工具

- **装饰轮盘**（视觉丰富）：
  - 适用于：儿童应用、视觉吸引力要求高的场景
  - 关键配置：`decorations`、`decorationType`、`decorationDensity`、`decorationColor`
  - 应用场景：儿童情绪教育、情绪识别训练

- **交互式轮盘**（强调用户交互）：
  - 适用于：需要精细情绪选择的场景
  - 关键配置：`hoverEffect`、`selectionAnimation`、`selectionIndicator`
  - 应用场景：心理健康应用、情绪疗法工具

- **多层级轮盘**（复杂情绪导航）：
  - 适用于：详细情绪分析、专业心理应用
  - 关键配置：`tierTransition`、`tierTransitionDuration`
  - 应用场景：心理咨询辅助工具、情绪研究应用

### 3. 卡片视图配置（card）

卡片视图提供更结构化的情绪展示，适用于以下场景：

- **网格卡片**（整齐排列）：
  - 适用于：情绪概览、情绪库浏览
  - 关键配置：`layout: 'grid'`、`columns`、`cardSpacing`
  - 应用场景：情绪词典、情绪学习工具

- **瀑布流卡片**（视觉丰富）：
  - 适用于：情绪探索、创意应用
  - 关键配置：`layout: 'masonry'`、`cardSize`、`cardBackground`
  - 应用场景：情绪艺术创作、情绪灵感板

- **轮播卡片**（逐个展示）：
  - 适用于：情绪学习、引导式体验
  - 关键配置：`layout: 'carousel'`、`cardAnimation`、`cardAnimationDuration`
  - 应用场景：情绪教育课程、引导式冥想

- **交互式卡片**（强调反馈）：
  - 适用于：游戏化体验、互动应用
  - 关键配置：`cardHoverEffect`、`cardSelectedEffect`、`cardElevation`
  - 应用场景：情绪记忆游戏、情绪匹配活动

### 4. 气泡视图配置（bubble）

气泡视图提供更自由、有机的情绪展示，适用于以下场景：

- **浮动气泡**（动态展示）：
  - 适用于：放松应用、冥想工具
  - 关键配置：`layout: 'float'`、`floatingAnimation`、`floatingSpeed`
  - 应用场景：情绪放松练习、压力释放工具

- **集群气泡**（情绪关联）：
  - 适用于：情绪关系展示、情绪分析
  - 关键配置：`layout: 'cluster'`、`bubbleSize`、`bubbleSpacing`
  - 应用场景：情绪关联分析、情绪网络可视化

- **交互式气泡**（用户参与）：
  - 适用于：游戏化体验、互动应用
  - 关键配置：`dragEnabled`、`collisionDetection`、`bubbleHoverEffect`
  - 应用场景：情绪调节游戏、互动式情绪表达

- **渐变气泡**（视觉吸引力）：
  - 适用于：美学体验、艺术表达
  - 关键配置：`bubbleGradient`、`bubbleGradientColors`、`bubbleGradientDirection`
  - 应用场景：情绪艺术创作、视觉冥想工具

### 5. 星系视图配置（galaxy）

星系视图提供宇宙般的情绪导航体验，适用于以下场景：

- **宇宙探索**（沉浸式体验）：
  - 适用于：冥想应用、深度反思
  - 关键配置：`backgroundType: 'stars'`、`rotationEnabled`、`parallaxEffect`
  - 应用场景：内观冥想、情绪宇宙探索

- **行星系统**（层级关系）：
  - 适用于：情绪关系展示、情绪体系
  - 关键配置：`layout: 'circular'`、`orbitVisible`、`centerGlow`
  - 应用场景：情绪体系教育、情绪关系可视化

- **互动星空**（用户参与）：
  - 适用于：创意表达、个性化体验
  - 关键配置：`zoomEnabled`、`starHoverEffect`、`starSelectedEffect`
  - 应用场景：情绪星图创建、个人情绪宇宙

- **3D 星系**（立体体验）：
  - 适用于：高级视觉体验、沉浸式应用
  - 关键配置：`perspective3D`、`perspective3DIntensity`、`starGlow`
  - 应用场景：VR/AR 情绪体验、高级冥想工具

### 6. 情绪树视图配置（tree）

情绪树视图展示情绪的层级和关联关系，适用于以下场景：

- **垂直树**（传统层级）：
  - 适用于：教育应用、结构化学习
  - 关键配置：`treeType: 'vertical'`、`levelSpacing`、`nodeSpacing`
  - 应用场景：情绪分类学习、情绪层级教育

- **思维导图**（关联展示）：
  - 适用于：情绪关联分析、思考工具
  - 关键配置：`treeType: 'mindmap'`、`linkStyle`、`nodeSize`
  - 应用场景：情绪关联思考、情绪根源分析

- **径向树**（放射状展示）：
  - 适用于：整体情绪概览、关系可视化
  - 关键配置：`treeType: 'radial'`、`nodeBackground`、`linkAnimation`
  - 应用场景：情绪全景图、情绪关系探索

- **交互式树**（用户探索）：
  - 适用于：深度学习、研究工具
  - 关键配置：`collapsible`、`zoomEnabled`、`panEnabled`、`minimap`
  - 应用场景：情绪研究工具、专业心理分析

### 7. 流程图视图配置（flow）

流程图视图展示情绪的因果和转变过程，适用于以下场景：

- **情绪流程**（过程展示）：
  - 适用于：情绪转变学习、应对策略
  - 关键配置：`layoutDirection: 'TB'`、`edgeStyle`、`arrowSize`
  - 应用场景：情绪管理训练、情绪转变策略

- **情绪网络**（关系网络）：
  - 适用于：复杂情绪关系、影响分析
  - 关键配置：`layout: 'force'`、`edgeAnimation`、`nodeSize`
  - 应用场景：情绪影响分析、情绪网络研究

- **决策树**（选择路径）：
  - 适用于：情绪应对指导、决策辅助
  - 关键配置：`layout: 'dagre'`、`nodeBackground`、`edgeColor`
  - 应用场景：情绪应对指南、情绪决策辅助

- **交互式流程**（用户参与）：
  - 适用于：个性化学习、自我探索
  - 关键配置：`nodeHoverEffect`、`edgeAnimationType`、`zoomEnabled`
  - 应用场景：个人情绪旅程映射、情绪成长追踪

### 8. 标签云视图配置（tagCloud）

标签云视图以文字为主展示情绪，适用于以下场景：

- **情绪词云**（文字展示）：
  - 适用于：语言学习、词汇扩展
  - 关键配置：`minFontSize`、`maxFontSize`、`colorScheme`
  - 应用场景：情绪词汇学习、语言表达训练

- **动态词云**（视觉吸引）：
  - 适用于：互动展示、视觉体验
  - 关键配置：`animation`、`animationType`、`randomRotation`
  - 应用场景：情绪词汇展示、情绪语言艺术

- **形状词云**（创意表达）：
  - 适用于：艺术表达、创意应用
  - 关键配置：`shape`、`padding`、`spiral`
  - 应用场景：情绪艺术创作、视觉诗歌

- **交互式词云**（用户探索）：
  - 适用于：词汇学习、语言探索
  - 关键配置：`hoverEffect`、`selectedEffect`、`hoverColor`
  - 应用场景：情绪词汇测验、情绪语言游戏

## 皮肤配置组合策略

为了满足不同用户需求和应用场景，可以采用以下皮肤配置组合策略：

### 1. 目标用户导向组合

- **儿童友好型**：
  - 视图选择：气泡视图或卡片视图
  - 关键配置：鲜艳颜色、大表情符号、简单动画、圆角设计
  - 应用场景：儿童情绪教育、家庭情绪游戏

- **专业用户型**：
  - 视图选择：轮盘视图或树视图
  - 关键配置：精确控制、详细文本、高对比度、最小化装饰
  - 应用场景：心理咨询、情绪研究、专业分析

- **冥想放松型**：
  - 视图选择：星系视图或浮动气泡视图
  - 关键配置：柔和颜色、平滑动画、渐变效果、沉浸式体验
  - 应用场景：冥想应用、压力管理、情绪调节

- **教育学习型**：
  - 视图选择：树视图或流程图视图
  - 关键配置：清晰结构、信息层级、交互式元素、学习反馈
  - 应用场景：情绪教育、社交技能训练、情绪智能发展

### 2. 情绪类型导向组合

- **积极情绪强化**：
  - 视图选择：明亮的卡片视图或动态气泡视图
  - 关键配置：暖色调、上升动画、光芒效果、活力装饰
  - 应用场景：积极心理学应用、幸福感提升工具

- **平静情绪培养**：
  - 视图选择：星系视图或简约轮盘视图
  - 关键配置：蓝色调、缓慢动画、最小化干扰、空间感
  - 应用场景：冥想应用、睡眠辅助、压力管理

- **情绪平衡与转化**：
  - 视图选择：流程图视图或树视图
  - 关键配置：平衡色彩、转换动画、关系连接、过程可视化
  - 应用场景：情绪管理训练、认知行为疗法辅助

### 3. 文化与美学导向组合

- **东方美学**：
  - 视图选择：水墨风格的树视图或轮盘视图
  - 关键配置：留白设计、墨色渐变、流动动画、简约线条
  - 应用场景：东方冥想、太极情绪平衡

- **北欧简约**：
  - 视图选择：几何卡片视图或简约轮盘视图
  - 关键配置：中性色调、简洁线条、功能性设计、适度留白
  - 应用场景：日常情绪追踪、生活平衡工具

- **赛博朋克**：
  - 视图选择：霓虹星系视图或科技感流程图视图
  - 关键配置：霓虹色彩、数字效果、科技线条、高对比度
  - 应用场景：未来主义情绪探索、科技辅助情绪分析

## 扩展性考虑

1. **新视图类型**：
   - 设计允许轻松添加新的视图类型
   - 视图类型通过工厂模式创建，便于扩展
   - 可考虑添加 AR/VR 视图、3D 模型视图等新型交互方式

2. **新皮肤属性**：
   - SkinConfig 设计为可扩展的结构
   - 新属性可以添加到适当的子对象中
   - 未来可考虑添加声音效果、触觉反馈等多感官元素

3. **多语言支持**：
   - EmotionData 可以支持多语言版本的情绪名称
   - Skin 可以根据语言调整字体和布局
   - 考虑不同文化背景下的情绪表达差异

4. **云同步**：
   - 数据模型设计考虑了未来可能的云同步需求
   - 包含创建者、创建时间等元数据，便于多用户环境
   - 支持皮肤共享和社区创建的皮肤市场
