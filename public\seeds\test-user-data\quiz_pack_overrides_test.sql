-- Quiz包覆盖配置测试数据
-- 为测试用户创建个性化的Quiz包配置覆盖

-- 1. 测试用户001的Quiz包覆盖（情绪Quiz个性化）
INSERT OR IGNORE INTO quiz_pack_overrides (
  id, user_id, pack_id, override_type, override_key, override_value,
  is_active, created_at, updated_at
) VALUES 
-- 情绪Quiz包的个性化设置
('override_001_display', 'test-user-001', 'mood_track_quest_main', 'display', 'show_progress_bar', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_001_timing', 'test-user-001', 'mood_track_quest_main', 'timing', 'question_timeout', '30', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_001_style', 'test-user-001', 'mood_track_quest_main', 'style', 'animation_enabled', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_001_difficulty', 'test-user-001', 'mood_track_quest_main', 'difficulty', 'adaptive_difficulty', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 问题顺序个性化
('override_001_order', 'test-user-001', 'mood_track_quest_main', 'questions', 'randomize_order', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_001_skip', 'test-user-001', 'mood_track_quest_main', 'questions', 'allow_skip', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 结果展示个性化
('override_001_results', 'test-user-001', 'mood_track_quest_main', 'results', 'show_detailed_analysis', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_001_sharing', 'test-user-001', 'mood_track_quest_main', 'results', 'enable_sharing', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 2. 高级用户002的Quiz包覆盖（中医Quiz个性化）
INSERT OR IGNORE INTO quiz_pack_overrides (
  id, user_id, pack_id, override_type, override_key, override_value,
  is_active, created_at, updated_at
) VALUES 
-- 中医Quiz包的高级设置
('override_002_display', 'test-user-002', 'tcm_constitution_assessment', 'display', 'show_progress_bar', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_timing', 'test-user-002', 'tcm_constitution_assessment', 'timing', 'question_timeout', '60', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_style', 'test-user-002', 'tcm_constitution_assessment', 'style', 'animation_enabled', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_difficulty', 'test-user-002', 'tcm_constitution_assessment', 'difficulty', 'adaptive_difficulty', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 高级用户的专业设置
('override_002_professional', 'test-user-002', 'tcm_constitution_assessment', 'professional', 'show_technical_terms', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_analysis', 'test-user-002', 'tcm_constitution_assessment', 'analysis', 'detailed_scoring', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_export', 'test-user-002', 'tcm_constitution_assessment', 'export', 'enable_pdf_export', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 问题设置
('override_002_order', 'test-user-002', 'tcm_constitution_assessment', 'questions', 'randomize_order', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_skip', 'test-user-002', 'tcm_constitution_assessment', 'questions', 'allow_skip', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 结果设置
('override_002_results', 'test-user-002', 'tcm_constitution_assessment', 'results', 'show_detailed_analysis', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_sharing', 'test-user-002', 'tcm_constitution_assessment', 'results', 'enable_sharing', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_history', 'test-user-002', 'tcm_constitution_assessment', 'results', 'compare_with_history', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 3. 新用户003的基础覆盖（简化设置）
INSERT OR IGNORE INTO quiz_pack_overrides (
  id, user_id, pack_id, override_type, override_key, override_value,
  is_active, created_at, updated_at
) VALUES 
-- 新用户的简化设置
('override_003_display', 'test-user-003', 'mood_track_quest_main', 'display', 'show_progress_bar', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_timing', 'test-user-003', 'mood_track_quest_main', 'timing', 'question_timeout', '0', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_style', 'test-user-003', 'mood_track_quest_main', 'style', 'animation_enabled', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_help', 'test-user-003', 'mood_track_quest_main', 'help', 'show_hints', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_guidance', 'test-user-003', 'mood_track_quest_main', 'help', 'show_guidance', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 新用户友好设置
('override_003_order', 'test-user-003', 'mood_track_quest_main', 'questions', 'randomize_order', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_skip', 'test-user-003', 'mood_track_quest_main', 'questions', 'allow_skip', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_tutorial', 'test-user-003', 'mood_track_quest_main', 'tutorial', 'show_tutorial', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 简化的结果展示
('override_003_results', 'test-user-003', 'mood_track_quest_main', 'results', 'show_detailed_analysis', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_sharing', 'test-user-003', 'mood_track_quest_main', 'results', 'enable_sharing', 'false', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 4. 跨Quiz包的通用覆盖设置
INSERT OR IGNORE INTO quiz_pack_overrides (
  id, user_id, pack_id, override_type, override_key, override_value,
  is_active, created_at, updated_at
) VALUES 
-- 测试用户001对所有Quiz包的通用设置
('override_001_global_theme', 'test-user-001', '*', 'appearance', 'quiz_theme', '"light"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_001_global_font', 'test-user-001', '*', 'appearance', 'font_size', '"medium"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 高级用户002的全局设置
('override_002_global_theme', 'test-user-002', '*', 'appearance', 'quiz_theme', '"dark"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_global_font', 'test-user-002', '*', 'appearance', 'font_size', '"large"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_002_global_speed', 'test-user-002', '*', 'performance', 'fast_mode', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 新用户003的全局设置
('override_003_global_theme', 'test-user-003', '*', 'appearance', 'quiz_theme', '"auto"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_global_font', 'test-user-003', '*', 'appearance', 'font_size', '"medium"', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('override_003_global_help', 'test-user-003', '*', 'help', 'always_show_help', 'true', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 验证数据
SELECT 'Quiz Pack Overrides loaded:' as info, COUNT(*) as count FROM quiz_pack_overrides;
SELECT 'Users with overrides:' as info, COUNT(DISTINCT user_id) as count FROM quiz_pack_overrides;

-- 按覆盖类型统计
SELECT override_type, COUNT(*) as count 
FROM quiz_pack_overrides 
GROUP BY override_type 
ORDER BY override_type;

-- 按用户统计覆盖数量
SELECT user_id, COUNT(*) as override_count 
FROM quiz_pack_overrides 
GROUP BY user_id 
ORDER BY override_count DESC;
