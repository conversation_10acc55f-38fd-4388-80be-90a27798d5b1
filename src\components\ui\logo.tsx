import type React from 'react';

export const Logo: React.FC = () => {
  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="relative w-full h-full">
        {/* Simple placeholder logo - replace with your actual logo */}
        <div className="absolute inset-0 bg-primary rounded-full opacity-20 animate-pulse" />
        <div className="absolute inset-2 bg-primary rounded-full opacity-40 animate-pulse animation-delay-300" />
        <div className="absolute inset-4 bg-primary rounded-full opacity-60 animate-pulse animation-delay-600" />
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-2xl font-bold text-primary">MM</span>
        </div>
      </div>
    </div>
  );
};
