/**
 * 气泡布局预览组件
 * 用于在设置页面中展示不同的气泡布局选项
 */

import { useTheme } from '@/contexts/ThemeContext';
import type { BubbleLayout } from '@/types/userConfigTypes';
import type React from 'react';

interface BubbleLayoutPreviewProps {
  layout: BubbleLayout;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  onClick?: () => void;
  isSelected?: boolean;
}

/**
 * 气泡布局预览组件
 * 展示不同气泡布局的预览
 */
const BubbleLayoutPreview: React.FC<BubbleLayoutPreviewProps> = ({
  layout,
  size = 'md',
  showLabel = false,
  onClick,
  isSelected = false,
}) => {
  const { theme } = useTheme();
  const isDarkMode =
    theme === 'dark' ||
    (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // 根据尺寸设置样式
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return { container: 'w-16 h-12', bubble: 'w-2 h-2' };
      case 'lg':
        return { container: 'w-32 h-24', bubble: 'w-4 h-4' };
      default:
        return { container: 'w-24 h-18', bubble: 'w-3 h-3' };
    }
  };

  const sizeClass = getSizeClass();

  // 获取布局标签
  const getLayoutLabel = () => {
    switch (layout) {
      case 'cluster':
        return '集群布局';
      case 'force':
        return '力导向布局';
      case 'random':
        return '随机布局';
      case 'circle':
        return '圆形布局';
      default:
        return layout;
    }
  };

  // 生成示例气泡
  const generateBubbles = () => {
    const colors = [
      '#EA4335', // Red
      '#FBBC05', // Yellow
      '#34A853', // Green
      '#4285F4', // Blue
      '#8E44AD', // Purple
      '#16A085', // Teal
    ];

    return colors.map((color, index) => ({
      id: `bubble-${index}`,
      color,
    }));
  };

  const bubbles = generateBubbles();

  // 渲染圆形布局预览
  const renderCirclePreview = () => {
    const centerX = Number.parseInt(sizeClass.container.split(' ')[0].substring(2)) / 2;
    const centerY = Number.parseInt(sizeClass.container.split(' ')[1].substring(2)) / 2;
    const radius = Math.min(centerX, centerY) * 0.7;

    return (
      <div className="relative w-full h-full">
        {bubbles.slice(0, 6).map((bubble, index) => {
          const angle = (index / 6) * 2 * Math.PI;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);

          return (
            <div
              key={bubble.id}
              className="absolute rounded-full"
              style={{
                backgroundColor: bubble.color,
                width: sizeClass.bubble.split(' ')[0],
                height: sizeClass.bubble.split(' ')[1],
                left: `${x}px`,
                top: `${y}px`,
                transform: 'translate(-50%, -50%)',
              }}
            />
          );
        })}
      </div>
    );
  };

  // 渲染集群布局预览
  const renderClusterPreview = () => {
    const centerX = Number.parseInt(sizeClass.container.split(' ')[0].substring(2)) / 2;
    const centerY = Number.parseInt(sizeClass.container.split(' ')[1].substring(2)) / 2;
    const clusterRadius = Math.min(centerX, centerY) * 0.5;

    // 创建两个集群
    const clusters = [
      { x: centerX - clusterRadius * 0.6, y: centerY },
      { x: centerX + clusterRadius * 0.6, y: centerY },
    ];

    return (
      <div className="relative w-full h-full">
        {bubbles.slice(0, 6).map((bubble, index) => {
          const clusterIndex = index % 2;
          const indexInCluster = Math.floor(index / 2);
          const angle = (indexInCluster / 3) * 2 * Math.PI;
          const radius = Number.parseInt(sizeClass.bubble.split(' ')[0].substring(2)) * 1.5;

          const x = clusters[clusterIndex].x + radius * Math.cos(angle);
          const y = clusters[clusterIndex].y + radius * Math.sin(angle);

          return (
            <div
              key={bubble.id}
              className="absolute rounded-full"
              style={{
                backgroundColor: bubble.color,
                width: sizeClass.bubble.split(' ')[0],
                height: sizeClass.bubble.split(' ')[1],
                left: `${x}px`,
                top: `${y}px`,
                transform: 'translate(-50%, -50%)',
              }}
            />
          );
        })}
      </div>
    );
  };

  // 渲染力导向布局预览
  const renderForcePreview = () => {
    const centerX = Number.parseInt(sizeClass.container.split(' ')[0].substring(2)) / 2;
    const centerY = Number.parseInt(sizeClass.container.split(' ')[1].substring(2)) / 2;
    const radius = Math.min(centerX, centerY) * 0.7;

    return (
      <div className="relative w-full h-full">
        {bubbles.slice(0, 6).map((bubble, index) => {
          const angle = (index / 6) * 2 * Math.PI;
          const radiusAdjustment = Math.sin(index * 2.5) * 0.2 + 0.8;
          const adjustedRadius = radius * radiusAdjustment;

          const x = centerX + adjustedRadius * Math.cos(angle);
          const y = centerY + adjustedRadius * Math.sin(angle);

          return (
            <div
              key={bubble.id}
              className="absolute rounded-full"
              style={{
                backgroundColor: bubble.color,
                width: sizeClass.bubble.split(' ')[0],
                height: sizeClass.bubble.split(' ')[1],
                left: `${x}px`,
                top: `${y}px`,
                transform: 'translate(-50%, -50%)',
              }}
            />
          );
        })}
      </div>
    );
  };

  // 渲染随机布局预览
  const renderRandomPreview = () => {
    const containerWidth = Number.parseInt(sizeClass.container.split(' ')[0].substring(2));
    const containerHeight = Number.parseInt(sizeClass.container.split(' ')[1].substring(2));
    const bubbleSize = Number.parseInt(sizeClass.bubble.split(' ')[0].substring(2));
    const margin = bubbleSize / 2 + 1;

    return (
      <div className="relative w-full h-full">
        {bubbles.slice(0, 6).map((bubble, index) => {
          // 使用伪随机位置
          const seed = index * 137.5 + 6;
          const randX = Math.sin(seed) * 0.5 + 0.5;
          const randY = Math.cos(seed) * 0.5 + 0.5;

          const x = margin + randX * (containerWidth - 2 * margin);
          const y = margin + randY * (containerHeight - 2 * margin);

          return (
            <div
              key={bubble.id}
              className="absolute rounded-full"
              style={{
                backgroundColor: bubble.color,
                width: sizeClass.bubble.split(' ')[0],
                height: sizeClass.bubble.split(' ')[1],
                left: `${x}px`,
                top: `${y}px`,
                transform: 'translate(-50%, -50%)',
              }}
            />
          );
        })}
      </div>
    );
  };

  return (
    <div
      className={`flex flex-col items-center ${onClick ? 'cursor-pointer' : ''} ${isSelected ? 'ring-2 ring-primary p-1 rounded' : 'p-1'}`}
      onClick={onClick}
    >
      <div
        className={`${sizeClass.container} bg-card rounded p-2 flex items-center justify-center border border-border`}
      >
        {layout === 'circle' && renderCirclePreview()}
        {layout === 'cluster' && renderClusterPreview()}
        {layout === 'force' && renderForcePreview()}
        {layout === 'random' && renderRandomPreview()}
      </div>
      {showLabel && <span className="text-xs text-muted-foreground mt-1">{getLayoutLabel()}</span>}
    </div>
  );
};

export default BubbleLayoutPreview;
