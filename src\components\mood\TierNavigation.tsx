/**
 * TierNavigation组件
 * 用于显示不同层级的情绪导航
 * 支持多层级情绪结构
 */

import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import { useSkinManager } from '@/contexts/SkinContext';
import { Emotion, ViewType } from '@/types';
import DisplayAdapter from '@/components/core/DisplayAdapter';
import { Haptics, ImpactStyle } from '@capacitor/haptics';

// 选择路径接口（支持多层级）
interface SelectedPath {
  [tierId: string]:    Emotion;
}

// 组件属性接口
interface TierNavigationProps {
  emotions: Emotion[]; // 支持旧的 EmotionOption 和新的 Emotion
  tier:   number | string;   // 支持旧的 EmotionTier 和新的数字层级
  onSelect: (emotion:  Emotion) => void;
  onBack?: () => void;
  selectedPath?: SelectedPath;
  emotionDataId?: string; // 情绪数据集ID
}

/**
 * 情绪层级导航组件
 * 支持多层级情绪结构
 */
const TierNavigation: React.FC<TierNavigationProps> = ({
  emotions,
  tier,
  onSelect,
  onBack,
  selectedPath,
  emotionDataId
}) => {
  const { t } = useLanguage();
  const { userConfig } = useUserConfig();
  const { activeSkin } = useSkinManager();

  // 处理情绪选择
  const handleSelect = async (emotion:  Emotion) => {
    // 触觉反馈
    try {
      await Haptics.impact({ style: ImpactStyle.Medium });
    } catch (error) {
      console.log('Haptics not available', error);
    }

    // 调用选择回调
    onSelect(emotion);
  };

  // 获取当前视图类型
  const viewType = userConfig.preferred_view_type || 'wheel';

  // 获取当前渲染引擎
  const renderEngine = userConfig.render_engine_preferences?.[viewType] || 'D3';

  // 获取当前内容显示模式
  const contentDisplayMode = userConfig.content_display_mode_preferences?.[viewType] || 'textEmoji';

  // 获取当前皮肤ID
  const skinId = userConfig.view_type_skin_ids?.[viewType] || userConfig.active_skin_id || 'default';

  // 简化的数据转换逻辑
  const convertedEmotions = emotions as Emotion[];

  // 简化的层级转换
  const tierLevel = typeof tier === 'number' ? tier : 1;

  // 渲染导航组件
  return (
    <div className="tier-navigation">
      <DisplayAdapter
        emotions={convertedEmotions as any}
        tier={tierLevel as any}
        onSelect={handleSelect}
        onBack={onBack}
        displayOptions={{
          viewType,
          renderEngine,
          displayMode: contentDisplayMode,
          skinId
        }}
        selectedPath={selectedPath}
      />
    </div>
  );
};

export default TierNavigation;
