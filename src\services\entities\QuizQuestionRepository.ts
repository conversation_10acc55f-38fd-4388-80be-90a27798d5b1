/**
 * Quiz问题仓储 - 修复版本
 * 纯数据访问层，不包含业务逻辑
 */

import { BaseRepository } from '../base/BaseRepository';
import { QuizQuestion } from '../../types/schema/base';
import { CreateQuizQuestionInput, UpdateQuizQuestionInput } from '../../types/schema/api';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class QuizQuestionRepository extends BaseRepository<
  QuizQuestion,
  CreateQuizQuestionInput,
  UpdateQuizQuestionInput
> {
  constructor(db?: SQLiteDBConnection) {
    super('quiz_questions', db);
  }

  /**
   * 根据Quiz包ID获取问题
   */
  async findByPackId(packId: string): Promise<QuizQuestion[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE pack_id = ? AND is_active = 1
      ORDER BY question_order ASC
    `;

    const result = await db.query(query, [packId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据层级获取问题
   */
  async findByTierLevel(packId: string, tierLevel: number): Promise<QuizQuestion[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE pack_id = ? AND tier_level = ? AND is_active = 1
      ORDER BY question_order ASC
    `;

    const result = await db.query(query, [packId, tierLevel]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据问题组获取问题
   */
  async findByGroup(packId: string, questionGroup: string): Promise<QuizQuestion[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE pack_id = ? AND question_group = ? AND is_active = 1
      ORDER BY question_order ASC
    `;

    const result = await db.query(query, [packId, questionGroup]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据问题类型获取问题
   */
  async findByType(packId: string, questionType: string): Promise<QuizQuestion[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE pack_id = ? AND question_type = ? AND is_active = 1
      ORDER BY question_order ASC
    `;

    const result = await db.query(query, [packId, questionType]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 获取下一个问题
   */
  async findNextQuestion(packId: string, currentQuestionOrder: number): Promise<QuizQuestion | null> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE pack_id = ? AND question_order > ? AND is_active = 1
      ORDER BY question_order ASC
      LIMIT 1
    `;

    const result = await db.query(query, [packId, currentQuestionOrder]);
    const row = result.values?.[0];
    return row ? this.mapRowToEntity(row) : null;
  }

  /**
   * 获取上一个问题
   */
  async findPreviousQuestion(packId: string, currentQuestionOrder: number): Promise<QuizQuestion | null> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE pack_id = ? AND question_order < ? AND is_active = 1
      ORDER BY question_order DESC
      LIMIT 1
    `;

    const result = await db.query(query, [packId, currentQuestionOrder]);
    const row = result.values?.[0];
    return row ? this.mapRowToEntity(row) : null;
  }

  /**
   * 根据父问题ID获取子问题
   */
  async findByParentQuestionId(parentQuestionId: string): Promise<QuizQuestion[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE parent_question_id = ? AND is_active = 1
      ORDER BY question_order ASC
    `;

    const result = await db.query(query, [parentQuestionId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 获取问题统计信息
   */
  async getQuestionStats(packId: string): Promise<any> {
    const db = this.getDb();
    const query = `
      SELECT
        COUNT(*) as total_questions,
        COUNT(DISTINCT question_type) as unique_types,
        COUNT(DISTINCT tier_level) as tier_levels,
        COUNT(DISTINCT question_group) as question_groups,
        COUNT(CASE WHEN parent_question_id IS NOT NULL THEN 1 END) as conditional_questions,
        AVG(CASE WHEN question_config IS NOT NULL THEN
          CAST(JSON_EXTRACT(question_config, '$.estimated_time_seconds') AS INTEGER)
        END) as avg_estimated_time
      FROM ${this.tableName}
      WHERE pack_id = ? AND is_active = 1
    `;

    const result = await db.query(query, [packId]);
    const stats = result.values?.[0];

    return stats || {
      total_questions: 0,
      unique_types: 0,
      tier_levels: 0,
      question_groups: 0,
      conditional_questions: 0,
      avg_estimated_time: 0
    };
  }

  /**
   * 批量更新问题顺序
   */
  async batchUpdateQuestionOrder(updates: Array<{ id: string; question_order: number }>): Promise<boolean> {
    const db = this.getDb();

    try {
      await db.execute('BEGIN TRANSACTION');

      for (const update of updates) {
        const query = `
          UPDATE ${this.tableName}
          SET question_order = ?, updated_at = ?
          WHERE id = ?
        `;
        await db.run(query, [update.question_order, new Date().toISOString(), update.id]);
      }

      await db.execute('COMMIT');
      return true;
    } catch (error) {
      await db.execute('ROLLBACK');
      throw error;
    }
  }

  /**
   * 获取问题的最大顺序号
   */
  async getMaxQuestionOrder(packId: string): Promise<number> {
    const db = this.getDb();
    const query = `
      SELECT COALESCE(MAX(question_order), 0) as max_order
      FROM ${this.tableName}
      WHERE pack_id = ?
    `;

    const result = await db.query(query, [packId]);
    return result.values?.[0]?.max_order || 0;
  }

  protected mapRowToEntity(row: any): QuizQuestion {
    return {
      id: row.id,
      pack_id: row.pack_id,
      question_text: row.question_text,
      question_text_localized: this.parseJSON(row.question_text_localized),
      question_type: row.question_type,
      question_order: row.question_order,
      question_group: row.question_group,
      tier_level: row.tier_level,
      question_config: this.parseJSON(row.question_config),
      validation_rules: this.parseJSON(row.validation_rules),
      scoring_config: this.parseJSON(row.scoring_config),
      parent_question_id: row.parent_question_id,
      dependency_rules: this.parseJSON(row.dependency_rules),
      is_required: Boolean(row.is_required),
      is_active: Boolean(row.is_active),
      created_at: row.created_at,
      updated_at: row.updated_at,
      created_by: row.created_by,
      updated_by: row.updated_by
    };
  }

  protected mapEntityToRow(entity: Partial<QuizQuestion>): Record<string, any> {
    return {
      id: entity.id,
      pack_id: entity.pack_id,
      question_text: entity.question_text,
      question_text_localized: this.stringifyJSON(entity.question_text_localized),
      question_type: entity.question_type,
      question_order: entity.question_order,
      question_group: entity.question_group,
      tier_level: entity.tier_level,
      question_config: this.stringifyJSON(entity.question_config),
      validation_rules: this.stringifyJSON(entity.validation_rules),
      scoring_config: this.stringifyJSON(entity.scoring_config),
      parent_question_id: entity.parent_question_id,
      dependency_rules: this.stringifyJSON(entity.dependency_rules),
      is_required: entity.is_required ? 1 : 0,
      is_active: entity.is_active ? 1 : 0,
      created_at: entity.created_at,
      updated_at: entity.updated_at,
      created_by: entity.created_by,
      updated_by: entity.updated_by
    };
  }

  protected buildInsertQuery(data: CreateQuizQuestionInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const questionId = `question_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const query = `
      INSERT INTO ${this.tableName} (
        id, pack_id, question_text, question_text_localized, question_type,
        question_order, question_group, tier_level, question_config,
        validation_rules, scoring_config, parent_question_id, dependency_rules,
        is_required, is_active, created_at, updated_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      questionId,
      data.pack_id,
      data.question_text,
      this.stringifyJSON(data.question_text_localized),
      data.question_type,
      data.question_order,
      data.question_group || null,
      data.tier_level || null,
      this.stringifyJSON(data.question_config),
      this.stringifyJSON(data.validation_rules),
      this.stringifyJSON(data.scoring_config),
      data.parent_question_id || null,
      this.stringifyJSON(data.dependency_rules),
      data.is_required ? 1 : 0,
      data.is_active ? 1 : 0,
      now,
      now,
      data.created_by || null
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateQuizQuestionInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.question_text !== undefined) {
      fields.push('question_text = ?');
      values.push(data.question_text);
    }

    if (data.question_text_localized !== undefined) {
      fields.push('question_text_localized = ?');
      values.push(this.stringifyJSON(data.question_text_localized));
    }

    if (data.question_type !== undefined) {
      fields.push('question_type = ?');
      values.push(data.question_type);
    }

    if (data.question_order !== undefined) {
      fields.push('question_order = ?');
      values.push(data.question_order);
    }

    if (data.question_group !== undefined) {
      fields.push('question_group = ?');
      values.push(data.question_group);
    }

    if (data.tier_level !== undefined) {
      fields.push('tier_level = ?');
      values.push(data.tier_level);
    }

    if (data.question_config !== undefined) {
      fields.push('question_config = ?');
      values.push(this.stringifyJSON(data.question_config));
    }

    if (data.validation_rules !== undefined) {
      fields.push('validation_rules = ?');
      values.push(this.stringifyJSON(data.validation_rules));
    }

    if (data.scoring_config !== undefined) {
      fields.push('scoring_config = ?');
      values.push(this.stringifyJSON(data.scoring_config));
    }

    if (data.parent_question_id !== undefined) {
      fields.push('parent_question_id = ?');
      values.push(data.parent_question_id);
    }

    if (data.dependency_rules !== undefined) {
      fields.push('dependency_rules = ?');
      values.push(this.stringifyJSON(data.dependency_rules));
    }

    if (data.is_required !== undefined) {
      fields.push('is_required = ?');
      values.push(data.is_required ? 1 : 0);
    }

    if (data.is_active !== undefined) {
      fields.push('is_active = ?');
      values.push(data.is_active ? 1 : 0);
    }

    if (data.updated_by !== undefined) {
      fields.push('updated_by = ?');
      values.push(data.updated_by);
    }

    // Always update updated_at
    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.pack_id) {
      conditions.push('pack_id = ?');
      values.push(filters.pack_id);
    }

    if (filters?.question_type) {
      conditions.push('question_type = ?');
      values.push(filters.question_type);
    }

    if (filters?.tier_level) {
      conditions.push('tier_level = ?');
      values.push(filters.tier_level);
    }

    if (filters?.question_group) {
      conditions.push('question_group = ?');
      values.push(filters.question_group);
    }

    if (filters?.is_active !== undefined) {
      conditions.push('is_active = ?');
      values.push(filters.is_active ? 1 : 0);
    } else {
      // Default to active only
      conditions.push('is_active = 1');
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY question_order ASC';

    if (filters?.limit) {
      query += ' LIMIT ?';
      values.push(filters.limit);
    }

    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.pack_id) {
      conditions.push('pack_id = ?');
      values.push(filters.pack_id);
    }

    if (filters?.question_type) {
      conditions.push('question_type = ?');
      values.push(filters.question_type);
    }

    if (filters?.is_active !== undefined) {
      conditions.push('is_active = ?');
      values.push(filters.is_active ? 1 : 0);
    } else {
      conditions.push('is_active = 1');
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    return { query, values };
  }

  protected extractIdFromCreateData(_data: CreateQuizQuestionInput): string {
    return `question_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private parseJSON(jsonString: string | null): any {
    if (!jsonString) return null;
    try {
      return JSON.parse(jsonString);
    } catch {
      return null;
    }
  }

  private stringifyJSON(obj: any): string | null {
    if (obj === null || obj === undefined) return null;
    try {
      return JSON.stringify(obj);
    } catch {
      return null;
    }
  }
}
