/**
 * 同步服务 - 扩展版
 *
 * 职责:
 * - 处理客户端和服务器之间的数据同步
 * - 支持所有新数据表的同步
 * - 智能冲突检测和解决
 * - 增量同步优化
 */

import { executeQuery, batchStatements, InStatement } from '../database/index.js';
// 导入统一的类型定义
import {
  type FullSyncInput,
  type MoodEntryUpload,
  type EmotionSelectionUpload,
  type QuizSession,
  type QuizAnswer,
  type QuizResult
} from '../../../src/types/schema/api.js';

// 使用统一的类型定义，保持向后兼容
export type SyncRequest = FullSyncInput;

export interface SyncResponse {
  success: boolean;
  serverTimestamp: string;
  uploadedCount: number;
  downloadedCount: number;

  // 原有数据类型
  newMoodEntriesFromServer: MoodEntryUpload[];
  newEmotionSelectionsFromServer: EmotionSelectionUpload[];
  newUserConfigsFromServer: UserConfigData[];
  newTagsFromServer: TagData[];

  // 新增数据类型
  newQuizSessionsFromServer: QuizSessionData[];
  newQuizAnswersFromServer: QuizAnswerData[];
  newQuizResultsFromServer: QuizResultData[];
  newPaymentTransactionsFromServer: PaymentTransactionData[];
  newSubscriptionHistoryFromServer: SubscriptionHistoryData[];
  newSkinUnlocksFromServer: SkinUnlockData[];
  newEmojiSetUnlocksFromServer: EmojiSetUnlockData[];
  newPresentationConfigsFromServer: PresentationConfigData[];

  conflicts?: ConflictData[];
  error?: string;
}

// 使用统一的类型定义
export type MoodEntryData = MoodEntryUpload;
export type EmotionSelectionData = EmotionSelectionUpload;

// 保留这些接口定义，因为统一 Schema 中还没有对应的定义
export interface UserConfigData {
  id: string;
  user_id: string;
  name: string;
  config_data: string;
  is_active: boolean;
  created_at: string;
  last_updated: string;
}

export interface TagData {
  id: string;
  user_id: string;
  name: string;
  color?: string;
  created_at: string;
  updated_at: string;
}

// 新增数据类型接口
export interface QuizSessionData extends QuizSession {}
export interface QuizAnswerData extends QuizAnswer {}
export interface QuizResultData extends QuizResult {}

export interface PaymentTransactionData {
  id: string;
  user_id: string;
  transaction_type: string;
  status: string;
  amount: number;
  currency: string;
  payment_provider: string;
  provider_payment_method_id?: string;
  provider_transaction_id?: string;
  description?: string;
  initiated_at: string;
  completed_at?: string;
  failed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionHistoryData {
  id: string;
  user_id: string;
  subscription_type: string;
  status: string;
  started_at: string;
  expires_at?: string;
  cancelled_at?: string;
  auto_renew: boolean;
  payment_method: string;
  transaction_id?: string;
  amount: number;
  currency: string;
  created_at: string;
}

export interface SkinUnlockData {
  id: string;
  user_id: string;
  skin_id: string;
  unlock_method: string;
  unlocked_at: string;
  transaction_id?: string;
  sync_status: string;
}

export interface EmojiSetUnlockData {
  id: string;
  user_id: string;
  emoji_set_id: string;
  unlock_method: string;
  unlocked_at: string;
  transaction_id?: string;
  sync_status: string;
}

export interface PresentationConfigData {
  id: string;
  user_id: string;
  config_name: string;
  config_type: string;
  config_data: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ConflictData {
  type: 'mood_entry' | 'emotion_selection' | 'user_config' | 'tag' |
        'quiz_session' | 'quiz_answer' | 'quiz_result' |
        'payment_transaction' | 'subscription_history' |
        'skin_unlock' | 'emoji_set_unlock' | 'presentation_config';
  localData: any;
  serverData: any;
  conflictReason: string;
}

export class SyncService {
  private static instance: SyncService;

  private constructor() {}

  static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  /**
   * 执行完整数据同步
   */
  async performFullSync(request: SyncRequest): Promise<SyncResponse> {
    try {
      const { userId, lastSyncTimestamp } = request;
      const currentTimestamp = new Date().toISOString();
      let uploadedCount = 0;
      let downloadedCount = 0;

      // 1. 上传客户端数据
      const uploadResult = await this.uploadClientData(request);
      uploadedCount = uploadResult.uploadedCount;

      // 2. 下载服务器数据
      const downloadResult = await this.downloadServerData(userId, lastSyncTimestamp);
      downloadedCount = downloadResult.downloadedCount;

      // 3. 检测冲突
      const conflicts = await this.detectConflicts(request, downloadResult);

      return {
        success: true,
        serverTimestamp: currentTimestamp,
        uploadedCount,
        downloadedCount,

        // 原有数据类型
        newMoodEntriesFromServer: downloadResult.moodEntries,
        newEmotionSelectionsFromServer: downloadResult.emotionSelections,
        newUserConfigsFromServer: downloadResult.userConfigs,
        newTagsFromServer: downloadResult.tags,

        // 新增数据类型
        newQuizSessionsFromServer: downloadResult.quizSessions,
        newQuizAnswersFromServer: downloadResult.quizAnswers,
        newQuizResultsFromServer: downloadResult.quizResults,
        newPaymentTransactionsFromServer: downloadResult.paymentTransactions,
        newSubscriptionHistoryFromServer: downloadResult.subscriptionHistory,
        newSkinUnlocksFromServer: downloadResult.skinUnlocks,
        newEmojiSetUnlocksFromServer: downloadResult.emojiSetUnlocks,
        newPresentationConfigsFromServer: downloadResult.presentationConfigs,

        conflicts
      };
    } catch (error) {
      console.error('[SyncService] Full sync error:', error);
      return {
        success: false,
        serverTimestamp: new Date().toISOString(),
        uploadedCount: 0,
        downloadedCount: 0,

        // 原有数据类型
        newMoodEntriesFromServer: [],
        newEmotionSelectionsFromServer: [],
        newUserConfigsFromServer: [],
        newTagsFromServer: [],

        // 新增数据类型
        newQuizSessionsFromServer: [],
        newQuizAnswersFromServer: [],
        newQuizResultsFromServer: [],
        newPaymentTransactionsFromServer: [],
        newSubscriptionHistoryFromServer: [],
        newSkinUnlocksFromServer: [],
        newEmojiSetUnlocksFromServer: [],
        newPresentationConfigsFromServer: [],

        error: error instanceof Error ? error.message : 'Sync failed'
      };
    }
  }

  /**
   * 上传客户端数据
   */
  private async uploadClientData(request: SyncRequest): Promise<{ uploadedCount: number }> {
    const statements: InStatement[] = [];
    let uploadedCount = 0;

    // 上传心情记录（基于实际数据库 schema）
    if (request.moodEntriesToUpload && request.moodEntriesToUpload.length > 0) {
      for (const entry of request.moodEntriesToUpload) {
        statements.push({
          sql: `
            INSERT INTO mood_entries (
              id, user_id, timestamp, emotion_data_set_id,
              intensity, reflection, tags,
              emoji_set_id, emoji_set_version,
              skin_id, skin_config_snapshot,
              view_type_used, render_engine_used, display_mode_used,
              user_config_snapshot,
              created_at, updated_at, sync_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'synced')
            ON CONFLICT(id) DO UPDATE SET
              user_id = excluded.user_id,
              timestamp = excluded.timestamp,
              emotion_data_set_id = excluded.emotion_data_set_id,
              intensity = excluded.intensity,
              reflection = excluded.reflection,
              tags = excluded.tags,
              emoji_set_id = excluded.emoji_set_id,
              emoji_set_version = excluded.emoji_set_version,
              skin_id = excluded.skin_id,
              skin_config_snapshot = excluded.skin_config_snapshot,
              view_type_used = excluded.view_type_used,
              render_engine_used = excluded.render_engine_used,
              display_mode_used = excluded.display_mode_used,
              user_config_snapshot = excluded.user_config_snapshot,
              updated_at = excluded.updated_at,
              sync_status = 'synced'
          `,
          args: [
            entry.id,
            entry.user_id,
            entry.timestamp,
            entry.emotion_data_set_id || null,
            entry.intensity,
            entry.reflection || null,
            entry.tags ? JSON.stringify(entry.tags) : null,
            entry.emoji_set_id || null,
            entry.emoji_set_version || null,
            entry.skin_id || null,
            entry.skin_config_snapshot || null,
            entry.view_type_used || null,
            entry.render_engine_used || null,
            entry.display_mode_used || null,
            entry.user_config_snapshot || null,
            entry.created_at,
            entry.updated_at
          ]
        });
        uploadedCount++;
      }
    }

    // 上传情绪选择
    if (request.emotionSelectionsToUpload && request.emotionSelectionsToUpload.length > 0) {
      for (const selection of request.emotionSelectionsToUpload) {
        // 如果没有 ID，生成一个新的 ID
        const selectionId = selection.id || `emotion_selection_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        statements.push({
          sql: `
            INSERT INTO emotion_selections (
              id, mood_entry_id, emotion_id, tier_level,
              emotion_data_set_emotion_id, created_at,
              intensity, emoji_item_id, emoji_unicode, emoji_image_url,
              emoji_animation_data, selection_path, parent_selection_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(id) DO UPDATE SET
              mood_entry_id = excluded.mood_entry_id,
              emotion_id = excluded.emotion_id,
              tier_level = excluded.tier_level,
              emotion_data_set_emotion_id = excluded.emotion_data_set_emotion_id,
              created_at = excluded.created_at,
              intensity = excluded.intensity,
              emoji_item_id = excluded.emoji_item_id,
              emoji_unicode = excluded.emoji_unicode,
              emoji_image_url = excluded.emoji_image_url,
              emoji_animation_data = excluded.emoji_animation_data,
              selection_path = excluded.selection_path,
              parent_selection_id = excluded.parent_selection_id
          `,
          args: [
            selectionId,
            selection.mood_entry_id,
            selection.emotion_id,
            selection.tier_level,
            selection.emotion_data_set_emotion_id || null,
            selection.created_at || new Date().toISOString(),
            selection.intensity || null,
            selection.emoji_item_id || null,
            selection.emoji_unicode || null,
            selection.emoji_image_url || null,
            selection.emoji_animation_data || null,
            selection.selection_path || null,
            selection.parent_selection_id || null
          ]
        });
      }
    }

    // 上传用户配置
    if (request.userConfigsToUpload && request.userConfigsToUpload.length > 0) {
      for (const config of request.userConfigsToUpload) {
        statements.push({
          sql: `
            INSERT INTO user_configs (
              id, user_id, name, config_data, is_active,
              created_at, last_updated
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(id) DO UPDATE SET
              name = excluded.name,
              config_data = excluded.config_data,
              is_active = excluded.is_active,
              last_updated = excluded.last_updated
          `,
          args: [
            config.id,
            config.user_id,
            config.name,
            config.config_data,
            config.is_active,
            config.created_at,
            config.last_updated
          ]
        });
      }
    }

    // 上传标签
    if (request.tagsToUpload && request.tagsToUpload.length > 0) {
      for (const tag of request.tagsToUpload) {
        statements.push({
          sql: `
            INSERT INTO tags (
              id, user_id, name, color, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            ON CONFLICT(id) DO UPDATE SET
              name = excluded.name,
              color = excluded.color,
              updated_at = excluded.updated_at
          `,
          args: [
            tag.id,
            tag.user_id,
            tag.name,
            tag.color || null,
            tag.created_at,
            tag.updated_at
          ]
        });
      }
    }

    // 上传Quiz会话数据
    if (request.quizSessionsToUpload && request.quizSessionsToUpload.length > 0) {
      for (const session of request.quizSessionsToUpload) {
        statements.push({
          sql: `
            INSERT INTO quiz_sessions (
              id, pack_id, user_id, status, current_question_index, total_questions,
              answered_questions, skipped_questions, completion_percentage,
              start_time, last_active_time, end_time, session_type, session_metadata,
              created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(id) DO UPDATE SET
              status = excluded.status,
              current_question_index = excluded.current_question_index,
              answered_questions = excluded.answered_questions,
              skipped_questions = excluded.skipped_questions,
              completion_percentage = excluded.completion_percentage,
              last_active_time = excluded.last_active_time,
              end_time = excluded.end_time,
              session_metadata = excluded.session_metadata,
              updated_at = excluded.updated_at
          `,
          args: [
            session.id, session.pack_id, session.user_id, session.status,
            session.current_question_index, session.total_questions,
            session.answered_questions, session.skipped_questions, session.completion_percentage,
            session.start_time, session.last_active_time, session.end_time,
            session.session_type, session.session_metadata,
            session.created_at, session.updated_at
          ]
        });
        uploadedCount++;
      }
    }

    // 上传Quiz答案数据
    if (request.quizAnswersToUpload && request.quizAnswersToUpload.length > 0) {
      for (const answer of request.quizAnswersToUpload) {
        statements.push({
          sql: `
            INSERT INTO quiz_answers (
              id, session_id, question_id, session_presentation_config_id,
              selected_option_ids, answer_value, answer_text,
              confidence_score, response_time_ms, answered_at,
              created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(id) DO UPDATE SET
              selected_option_ids = excluded.selected_option_ids,
              answer_value = excluded.answer_value,
              answer_text = excluded.answer_text,
              confidence_score = excluded.confidence_score,
              response_time_ms = excluded.response_time_ms,
              answered_at = excluded.answered_at,
              updated_at = excluded.updated_at
          `,
          args: [
            answer.id, answer.session_id, answer.question_id, answer.session_presentation_config_id,
            answer.selected_option_ids, answer.answer_value, answer.answer_text,
            answer.confidence_score, answer.response_time_ms, answer.answered_at,
            answer.created_at, answer.updated_at
          ]
        });
        uploadedCount++;
      }
    }

    // 上传Quiz结果数据
    if (request.quizResultsToUpload && request.quizResultsToUpload.length > 0) {
      for (const result of request.quizResultsToUpload) {
        statements.push({
          sql: `
            INSERT INTO quiz_results (
              id, session_id, user_id, pack_id, total_questions,
              answered_questions, completion_percentage, total_time_ms,
              started_at, completed_at, result_data, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(id) DO UPDATE SET
              completion_percentage = excluded.completion_percentage,
              total_time_ms = excluded.total_time_ms,
              completed_at = excluded.completed_at,
              result_data = excluded.result_data,
              updated_at = excluded.updated_at
          `,
          args: [
            result.id, result.session_id, result.user_id, result.pack_id,
            result.total_questions, result.answered_questions, result.completion_percentage,
            result.total_time_ms, result.started_at, result.completed_at,
            JSON.stringify(result.result_data), result.created_at, result.updated_at
          ]
        });
        uploadedCount++;
      }
    }

    // 执行批量上传
    if (statements.length > 0) {
      await batchStatements(statements);
    }

    return { uploadedCount };
  }

  /**
   * 下载服务器数据 (扩展版)
   */
  private async downloadServerData(userId: string, lastSyncTimestamp?: string): Promise<{
    downloadedCount: number;
    moodEntries: MoodEntryData[];
    emotionSelections: EmotionSelectionData[];
    userConfigs: UserConfigData[];
    tags: TagData[];
    quizSessions: QuizSessionData[];
    quizAnswers: QuizAnswerData[];
    quizResults: QuizResultData[];
    paymentTransactions: PaymentTransactionData[];
    subscriptionHistory: SubscriptionHistoryData[];
    skinUnlocks: SkinUnlockData[];
    emojiSetUnlocks: EmojiSetUnlockData[];
    presentationConfigs: PresentationConfigData[];
  }> {
    let downloadedCount = 0;

    // 构建时间过滤条件
    const timeFilter = lastSyncTimestamp ? 'AND updated_at > ?' : '';
    const timeArgs = lastSyncTimestamp ? [lastSyncTimestamp] : [];

    // 下载心情记录
    const moodEntriesResult = await executeQuery({
      sql: `
        SELECT * FROM mood_entries
        WHERE user_id = ? ${timeFilter}
        ORDER BY timestamp DESC
      `,
      args: [userId, ...timeArgs]
    });

    // 安全解析JSON字段的辅助函数
    const safeParseJSON = (jsonString: string | null, defaultValue: any = null) => {
      if (!jsonString) return defaultValue;
      try {
        return JSON.parse(jsonString);
      } catch (error) {
        console.warn('[SyncService] Failed to parse JSON field:', error);
        return defaultValue;
      }
    };

    const moodEntries: MoodEntryData[] = moodEntriesResult.rows.map((row: any) => ({
      id: row.id,
      user_id: row.user_id,
      timestamp: row.timestamp,
      emotion_data_set_id: row.emotion_data_set_id,
      intensity: row.intensity,
      reflection: row.reflection,
      tags: safeParseJSON(row.tags, []),

      // 表情集关联
      emoji_set_id: row.emoji_set_id,
      emoji_set_version: row.emoji_set_version,

      // 皮肤配置快照
      skin_id: row.skin_id,
      skin_config_snapshot: row.skin_config_snapshot,

      // 显示配置快照
      view_type_used: row.view_type_used,
      render_engine_used: row.render_engine_used,
      display_mode_used: row.display_mode_used,

      // 用户配置快照
      user_config_snapshot: row.user_config_snapshot,

      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    // 下载情绪选择
    let emotionSelections: EmotionSelectionData[] = [];
    if (moodEntries.length > 0) {
      const moodEntryIds = moodEntries.map(entry => entry.id);
      const placeholders = moodEntryIds.map(() => '?').join(',');

      const selectionsResult = await executeQuery({
        sql: `
          SELECT * FROM emotion_selections
          WHERE mood_entry_id IN (${placeholders})
          ORDER BY tier_level
        `,
        args: moodEntryIds
      });

      emotionSelections = selectionsResult.rows.map((row: any) => ({
        id: row.id,
        mood_entry_id: row.mood_entry_id,
        emotion_id: row.emotion_id,
        tier_level: row.tier_level,
        emotion_data_set_emotion_id: row.emotion_data_set_emotion_id,
        created_at: row.created_at,

        // 新增字段
        intensity: row.intensity,
        emoji_item_id: row.emoji_item_id,
        emoji_unicode: row.emoji_unicode,
        emoji_image_url: row.emoji_image_url,
        emoji_animation_data: row.emoji_animation_data,
        selection_path: row.selection_path,
        parent_selection_id: row.parent_selection_id
      }));
    }

    // 下载用户配置
    const userConfigsResult = await executeQuery({
      sql: `
        SELECT * FROM user_configs
        WHERE user_id = ? ${timeFilter}
        ORDER BY last_updated DESC
      `,
      args: [userId, ...timeArgs]
    });

    const userConfigs: UserConfigData[] = userConfigsResult.rows.map((row: any) => ({
      id: row.id,
      user_id: row.user_id,
      name: row.name,
      config_data: row.config_data,
      is_active: Boolean(row.is_active),
      created_at: row.created_at,
      last_updated: row.last_updated
    }));

    // 下载标签
    const tagsResult = await executeQuery({
      sql: `
        SELECT * FROM tags
        WHERE user_id = ? ${timeFilter}
        ORDER BY name
      `,
      args: [userId, ...timeArgs]
    });

    const tags: TagData[] = tagsResult.rows.map((row: any) => ({
      id: row.id,
      user_id: row.user_id,
      name: row.name,
      color: row.color,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    // 下载Quiz会话数据
    const quizSessionsResult = await executeQuery({
      sql: `
        SELECT * FROM quiz_sessions
        WHERE user_id = ? ${timeFilter}
        ORDER BY created_at DESC
      `,
      args: [userId, ...timeArgs]
    });

    const quizSessions: QuizSessionData[] = quizSessionsResult.rows.map((row: any) => ({
      id: row.id,
      pack_id: row.pack_id,
      user_id: row.user_id,
      status: row.status,
      current_question_index: row.current_question_index,
      total_questions: row.total_questions,
      answered_questions: row.answered_questions,
      skipped_questions: row.skipped_questions,
      completion_percentage: row.completion_percentage,
      start_time: row.start_time,
      last_active_time: row.last_active_time,
      end_time: row.end_time,
      session_type: row.session_type,
      session_metadata: row.session_metadata,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    // 下载Quiz答案数据
    let quizAnswers: QuizAnswerData[] = [];
    if (quizSessions.length > 0) {
      const sessionIds = quizSessions.map(session => session.id);
      const placeholders = sessionIds.map(() => '?').join(',');

      const answersResult = await executeQuery({
        sql: `
          SELECT * FROM quiz_answers
          WHERE session_id IN (${placeholders})
          ORDER BY answered_at
        `,
        args: sessionIds
      });

      quizAnswers = answersResult.rows.map((row: any) => ({
        id: row.id,
        session_id: row.session_id,
        question_id: row.question_id,
        session_presentation_config_id: row.session_presentation_config_id,
        selected_option_ids: row.selected_option_ids,
        answer_value: row.answer_value,
        answer_text: row.answer_text,
        confidence_score: row.confidence_score,
        response_time_ms: row.response_time_ms,
        answered_at: row.answered_at,
        created_at: row.created_at,
        updated_at: row.updated_at
      }));
    }

    // 下载Quiz结果数据
    const quizResultsResult = await executeQuery({
      sql: `
        SELECT * FROM quiz_results
        WHERE user_id = ? ${timeFilter}
        ORDER BY created_at DESC
      `,
      args: [userId, ...timeArgs]
    });

    const quizResults: QuizResultData[] = quizResultsResult.rows.map((row: any) => ({
      id: row.id,
      session_id: row.session_id,
      user_id: row.user_id,
      pack_id: row.pack_id,
      total_questions: row.total_questions,
      answered_questions: row.answered_questions,
      completion_percentage: row.completion_percentage,
      total_time_ms: row.total_time_ms,
      started_at: row.started_at,
      completed_at: row.completed_at,
      result_data: safeParseJSON(row.result_data, {}),
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    // 下载支付交易数据
    const paymentTransactionsResult = await executeQuery({
      sql: `
        SELECT * FROM payment_transactions
        WHERE user_id = ? ${timeFilter}
        ORDER BY created_at DESC
      `,
      args: [userId, ...timeArgs]
    });

    const paymentTransactions: PaymentTransactionData[] = paymentTransactionsResult.rows.map((row: any) => ({
      id: row.id,
      user_id: row.user_id,
      transaction_type: row.transaction_type,
      status: row.status,
      amount: row.amount,
      currency: row.currency,
      payment_provider: row.payment_provider,
      provider_payment_method_id: row.provider_payment_method_id,
      provider_transaction_id: row.provider_transaction_id,
      description: row.description,
      initiated_at: row.initiated_at,
      completed_at: row.completed_at,
      failed_at: row.failed_at,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    // 下载订阅历史数据
    const subscriptionHistoryResult = await executeQuery({
      sql: `
        SELECT * FROM user_subscription_history
        WHERE user_id = ? ${timeFilter}
        ORDER BY created_at DESC
      `,
      args: [userId, ...timeArgs]
    });

    const subscriptionHistory: SubscriptionHistoryData[] = subscriptionHistoryResult.rows.map((row: any) => ({
      id: row.id,
      user_id: row.user_id,
      subscription_type: row.subscription_type,
      status: row.status,
      started_at: row.started_at,
      expires_at: row.expires_at,
      cancelled_at: row.cancelled_at,
      auto_renew: Boolean(row.auto_renew),
      payment_method: row.payment_method,
      transaction_id: row.transaction_id,
      amount: row.amount,
      currency: row.currency,
      created_at: row.created_at
    }));

    // 下载皮肤解锁数据
    const skinUnlocksResult = await executeQuery({
      sql: `
        SELECT * FROM user_skin_unlocks
        WHERE user_id = ? ${timeFilter}
        ORDER BY unlocked_at DESC
      `,
      args: [userId, ...timeArgs]
    });

    const skinUnlocks: SkinUnlockData[] = skinUnlocksResult.rows.map((row: any) => ({
      id: row.id,
      user_id: row.user_id,
      skin_id: row.skin_id,
      unlock_method: row.unlock_method,
      unlocked_at: row.unlocked_at,
      transaction_id: row.transaction_id,
      sync_status: row.sync_status
    }));

    // 下载表情集解锁数据
    const emojiSetUnlocksResult = await executeQuery({
      sql: `
        SELECT * FROM user_emoji_set_unlocks
        WHERE user_id = ? ${timeFilter}
        ORDER BY unlocked_at DESC
      `,
      args: [userId, ...timeArgs]
    });

    const emojiSetUnlocks: EmojiSetUnlockData[] = emojiSetUnlocksResult.rows.map((row: any) => ({
      id: row.id,
      user_id: row.user_id,
      emoji_set_id: row.emoji_set_id,
      unlock_method: row.unlock_method,
      unlocked_at: row.unlocked_at,
      transaction_id: row.transaction_id,
      sync_status: row.sync_status
    }));

    // 下载用户个性化配置数据
    const presentationConfigsResult = await executeQuery({
      sql: `
        SELECT * FROM user_presentation_configs
        WHERE user_id = ? ${timeFilter}
        ORDER BY updated_at DESC
      `,
      args: [userId, ...timeArgs]
    });

    const presentationConfigs: PresentationConfigData[] = presentationConfigsResult.rows.map((row: any) => ({
      id: row.id,
      user_id: row.user_id,
      config_name: row.config_name,
      config_type: row.config_type,
      config_data: row.config_data,
      is_active: Boolean(row.is_active),
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    downloadedCount = moodEntries.length + emotionSelections.length + userConfigs.length + tags.length +
                     quizSessions.length + quizAnswers.length + quizResults.length +
                     paymentTransactions.length + subscriptionHistory.length +
                     skinUnlocks.length + emojiSetUnlocks.length + presentationConfigs.length;

    return {
      downloadedCount,
      moodEntries,
      emotionSelections,
      userConfigs,
      tags,
      quizSessions,
      quizAnswers,
      quizResults,
      paymentTransactions,
      subscriptionHistory,
      skinUnlocks,
      emojiSetUnlocks,
      presentationConfigs
    };
  }

  /**
   * 检测数据冲突 (增强版)
   */
  private async detectConflicts(request: SyncRequest, downloadResult: any): Promise<ConflictData[]> {
    const conflicts: ConflictData[] = [];

    try {
      // 检测心情记录冲突
      if (request.moodEntriesToUpload && request.moodEntriesToUpload.length > 0) {
        for (const clientEntry of request.moodEntriesToUpload) {
          const serverEntry = downloadResult.moodEntries.find((entry: any) => entry.id === clientEntry.id);
          if (serverEntry && serverEntry.updated_at !== clientEntry.updated_at) {
            conflicts.push({
              type: 'mood_entry',
              localData: clientEntry,
              serverData: serverEntry,
              conflictReason: 'Different update timestamps'
            });
          }
        }
      }

      // 检测Quiz会话冲突
      if (request.quizSessionsToUpload && request.quizSessionsToUpload.length > 0) {
        for (const clientSession of request.quizSessionsToUpload) {
          const serverSession = downloadResult.quizSessions.find((session: any) => session.id === clientSession.id);
          if (serverSession && serverSession.updated_at !== clientSession.updated_at) {
            conflicts.push({
              type: 'quiz_session',
              localData: clientSession,
              serverData: serverSession,
              conflictReason: 'Session state conflict'
            });
          }
        }
      }

      console.log(`[SyncService] Detected ${conflicts.length} conflicts`);
    } catch (error) {
      console.error('[SyncService] Error detecting conflicts:', error);
    }

    return conflicts;
  }

  /**
   * 执行增量同步 (新增功能)
   */
  async performIncrementalSync(userId: string, lastSyncTimestamp: string): Promise<SyncResponse> {
    try {
      console.log(`[SyncService] Performing incremental sync for user ${userId} since ${lastSyncTimestamp}`);

      const currentTimestamp = new Date().toISOString();

      // 只下载服务器端的变更
      const downloadResult = await this.downloadServerData(userId, lastSyncTimestamp);

      return {
        success: true,
        serverTimestamp: currentTimestamp,
        uploadedCount: 0, // 增量同步不上传数据
        downloadedCount: downloadResult.downloadedCount,

        // 原有数据类型
        newMoodEntriesFromServer: downloadResult.moodEntries,
        newEmotionSelectionsFromServer: downloadResult.emotionSelections,
        newUserConfigsFromServer: downloadResult.userConfigs,
        newTagsFromServer: downloadResult.tags,

        // 新增数据类型
        newQuizSessionsFromServer: downloadResult.quizSessions,
        newQuizAnswersFromServer: downloadResult.quizAnswers,
        newQuizResultsFromServer: downloadResult.quizResults,
        newPaymentTransactionsFromServer: downloadResult.paymentTransactions,
        newSubscriptionHistoryFromServer: downloadResult.subscriptionHistory,
        newSkinUnlocksFromServer: downloadResult.skinUnlocks,
        newEmojiSetUnlocksFromServer: downloadResult.emojiSetUnlocks,
        newPresentationConfigsFromServer: downloadResult.presentationConfigs,

        conflicts: []
      };
    } catch (error) {
      console.error('[SyncService] Incremental sync error:', error);
      return this.createEmptyErrorResponse(error);
    }
  }

  /**
   * 解决数据冲突 (新增功能)
   */
  async resolveConflicts(
    userId: string,
    conflicts: ConflictData[],
    resolutionStrategy: 'client_wins' | 'server_wins' | 'merge' = 'server_wins'
  ): Promise<{ success: boolean; resolvedCount: number; error?: string }> {
    try {
      const statements: InStatement[] = [];
      let resolvedCount = 0;

      for (const conflict of conflicts) {
        switch (resolutionStrategy) {
          case 'client_wins':
            // 使用客户端数据覆盖服务端数据
            const clientUpdateStatement = this.createUpdateStatement(conflict.type, conflict.localData);
            if (clientUpdateStatement) {
              statements.push(clientUpdateStatement);
              resolvedCount++;
            }
            break;

          case 'server_wins':
            // 服务端数据优先，不需要额外操作
            resolvedCount++;
            break;

          case 'merge':
            // 智能合并策略
            const mergedData = this.mergeConflictData(conflict);
            const mergeStatement = this.createUpdateStatement(conflict.type, mergedData);
            if (mergeStatement) {
              statements.push(mergeStatement);
              resolvedCount++;
            }
            break;
        }
      }

      if (statements.length > 0) {
        await batchStatements(statements);
      }

      console.log(`[SyncService] Resolved ${resolvedCount} conflicts using ${resolutionStrategy} strategy`);

      return {
        success: true,
        resolvedCount
      };
    } catch (error) {
      console.error('[SyncService] Error resolving conflicts:', error);
      return {
        success: false,
        resolvedCount: 0,
        error: error instanceof Error ? error.message : 'Failed to resolve conflicts'
      };
    }
  }

  /**
   * 获取同步统计信息 (新增功能)
   */
  async getSyncStatistics(userId: string): Promise<{
    success: boolean;
    statistics?: {
      totalMoodEntries: number;
      totalQuizSessions: number;
      totalPaymentTransactions: number;
      lastSyncTimestamp?: string;
      syncFrequency: number; // 每天同步次数
    };
    error?: string;
  }> {
    try {
      const [moodEntriesResult, quizSessionsResult, paymentsResult, lastSyncResult] = await Promise.all([
        executeQuery({
          sql: 'SELECT COUNT(*) as count FROM mood_entries WHERE user_id = ?',
          args: [userId]
        }),
        executeQuery({
          sql: 'SELECT COUNT(*) as count FROM quiz_sessions WHERE user_id = ?',
          args: [userId]
        }),
        executeQuery({
          sql: 'SELECT COUNT(*) as count FROM payment_transactions WHERE user_id = ?',
          args: [userId]
        }),
        executeQuery({
          sql: `
            SELECT MAX(updated_at) as last_sync
            FROM (
              SELECT updated_at FROM mood_entries WHERE user_id = ?
              UNION ALL
              SELECT updated_at FROM quiz_sessions WHERE user_id = ?
              UNION ALL
              SELECT updated_at FROM payment_transactions WHERE user_id = ?
            )
          `,
          args: [userId, userId, userId]
        })
      ]);

      return {
        success: true,
        statistics: {
          totalMoodEntries: moodEntriesResult.rows[0]?.count || 0,
          totalQuizSessions: quizSessionsResult.rows[0]?.count || 0,
          totalPaymentTransactions: paymentsResult.rows[0]?.count || 0,
          lastSyncTimestamp: lastSyncResult.rows[0]?.last_sync,
          syncFrequency: 1 // 简化实现，实际可以基于历史记录计算
        }
      };
    } catch (error) {
      console.error('[SyncService] Error getting sync statistics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get sync statistics'
      };
    }
  }

  /**
   * 创建空的错误响应
   */
  private createEmptyErrorResponse(error: any): SyncResponse {
    return {
      success: false,
      serverTimestamp: new Date().toISOString(),
      uploadedCount: 0,
      downloadedCount: 0,

      // 原有数据类型
      newMoodEntriesFromServer: [],
      newEmotionSelectionsFromServer: [],
      newUserConfigsFromServer: [],
      newTagsFromServer: [],

      // 新增数据类型
      newQuizSessionsFromServer: [],
      newQuizAnswersFromServer: [],
      newQuizResultsFromServer: [],
      newPaymentTransactionsFromServer: [],
      newSubscriptionHistoryFromServer: [],
      newSkinUnlocksFromServer: [],
      newEmojiSetUnlocksFromServer: [],
      newPresentationConfigsFromServer: [],

      error: error instanceof Error ? error.message : 'Sync failed'
    };
  }

  /**
   * 创建更新语句
   */
  private createUpdateStatement(type: string, data: any): InStatement | null {
    // 简化实现，实际应该根据数据类型创建相应的更新语句
    switch (type) {
      case 'mood_entry':
        return {
          sql: `UPDATE mood_entries SET updated_at = ? WHERE id = ?`,
          args: [new Date().toISOString(), data.id]
        };
      case 'quiz_session':
        return {
          sql: `UPDATE quiz_sessions SET updated_at = ? WHERE id = ?`,
          args: [new Date().toISOString(), data.id]
        };
      default:
        return null;
    }
  }

  /**
   * 合并冲突数据
   */
  private mergeConflictData(conflict: ConflictData): any {
    // 简化的合并策略：使用最新的时间戳
    const clientTime = new Date(conflict.localData.updated_at || conflict.localData.created_at).getTime();
    const serverTime = new Date(conflict.serverData.updated_at || conflict.serverData.created_at).getTime();

    return clientTime > serverTime ? conflict.localData : conflict.serverData;
  }
}
