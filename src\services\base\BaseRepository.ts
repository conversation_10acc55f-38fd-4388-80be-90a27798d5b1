/**
 * 仓储基类
 * 提供通用的数据访问功能
 */

import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export abstract class BaseRepository<T, TCreate, TUpdate> {
  constructor(
    protected tableName: string,
    protected db?: SQLiteDBConnection
  ) {}

  /**
   * 设置数据库连接
   */
  setDatabase(db: SQLiteDBConnection): void {
    this.db = db;
  }

  /**
   * 获取数据库连接
   */
  protected getDb(): SQLiteDBConnection {
    if (!this.db) {
      throw new Error('Database connection not set');
    }
    return this.db;
  }

  /**
   * 创建新记录
   */
  async create(data: TCreate): Promise<T> {
    const db = this.getDb();
    const { query, values } = this.buildInsertQuery(data);

    try {
      await db.run(query, values);

      // 获取新创建的记录
      const id = this.extractIdFromCreateData(data);
      const result = await this.findById(id);

      if (!result) {
        throw new Error('Failed to retrieve created record');
      }

      return result;
    } catch (error) {
      throw new Error(`Failed to create record: ${error}`);
    }
  }

  /**
   * 根据ID查找记录
   */
  async findById(id: string): Promise<T | null> {
    const db = this.getDb();
    const query = `SELECT * FROM ${this.tableName} WHERE id = ?`;

    try {
      const result = await db.query(query, [id]);

      if (!result.values || result.values.length === 0) {
        return null;
      }

      // 安全检查：确保第一行数据存在
      const firstRow = result.values[0];
      if (!firstRow) {
        return null;
      }

      return this.mapRowToEntity(firstRow);
    } catch (error) {
      throw new Error(`Failed to find record by ID: ${error}`);
    }
  }

  /**
   * 更新记录
   */
  async update(id: string, data: TUpdate): Promise<T> {
    const db = this.getDb();
    const { query, values } = this.buildUpdateQuery(id, data);

    try {
      await db.run(query, values);

      // 获取更新后的记录
      const result = await this.findById(id);

      if (!result) {
        throw new Error('Failed to retrieve updated record');
      }

      return result;
    } catch (error) {
      throw new Error(`Failed to update record: ${error}`);
    }
  }

  /**
   * 删除记录
   */
  async delete(id: string): Promise<boolean> {
    const db = this.getDb();
    const query = `DELETE FROM ${this.tableName} WHERE id = ?`;

    try {
      const result = await db.run(query, [id]);
      return (result.changes?.changes ?? 0) > 0;
    } catch (error) {
      throw new Error(`Failed to delete record: ${error}`);
    }
  }

  /**
   * 查找多条记录
   */
  async findMany(filters?: any): Promise<T[]> {
    const db = this.getDb();
    const { query, values } = this.buildSelectQuery(filters);

    try {
      const result = await db.query(query, values);

      if (!result.values) {
        return [];
      }

      return result.values.map((row: any) => this.mapRowToEntity(row));
    } catch (error) {
      throw new Error(`Failed to find records: ${error}`);
    }
  }

  /**
   * 计算记录数量
   */
  async count(filters?: any): Promise<number> {
    const db = this.getDb();
    const { query, values } = this.buildCountQuery(filters);

    try {
      const result = await db.query(query, values);

      if (!result.values || result.values.length === 0) {
        return 0;
      }

      return Number(result.values[0].count) || 0;
    } catch (error) {
      throw new Error(`Failed to count records: ${error}`);
    }
  }

  /**
   * 批量插入
   */
  async batchInsert(dataList: TCreate[]): Promise<T[]> {
    if (dataList.length === 0) {
      return [];
    }

    const db = this.getDb();

    try {
      // 开始事务
      await db.execute('BEGIN TRANSACTION');

      const results: T[] = [];

      for (const data of dataList) {
        const result = await this.create(data);
        results.push(result);
      }

      // 提交事务
      await db.execute('COMMIT');

      return results;
    } catch (error) {
      // 回滚事务
      await db.execute('ROLLBACK');
      throw new Error(`Failed to batch insert: ${error}`);
    }
  }

  /**
   * 批量更新
   */
  async batchUpdate(updates: Array<{ id: string; data: TUpdate }>): Promise<T[]> {
    if (updates.length === 0) {
      return [];
    }

    const db = this.getDb();

    try {
      // 开始事务
      await db.execute('BEGIN TRANSACTION');

      const results: T[] = [];

      for (const { id, data } of updates) {
        const result = await this.update(id, data);
        results.push(result);
      }

      // 提交事务
      await db.execute('COMMIT');

      return results;
    } catch (error) {
      // 回滚事务
      await db.execute('ROLLBACK');
      throw new Error(`Failed to batch update: ${error}`);
    }
  }

  /**
   * 执行原始SQL查询
   */
  async executeRawQuery(query: string, values?: any[]): Promise<any[]> {
    const db = this.getDb();

    try {
      const result = await db.query(query, values);
      return result.values || [];
    } catch (error) {
      throw new Error(`Failed to execute raw query: ${error}`);
    }
  }

  // 抽象方法，子类需要实现
  protected abstract mapRowToEntity(row: any): T;
  protected abstract buildInsertQuery(data: TCreate): { query: string; values: any[] };
  protected abstract buildUpdateQuery(id: string, data: TUpdate): { query: string; values: any[] };
  protected abstract extractIdFromCreateData(data: TCreate): string;

  // 可选的抽象方法，子类可以重写
  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];

    if (filters) {
      const conditions: string[] = [];

      // 处理基础过滤器
      if (filters.user_id) {
        conditions.push('user_id = ?');
        values.push(filters.user_id);
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      // 处理排序
      if (filters.orderBy) {
        const direction = filters.orderDirection || 'ASC';
        query += ` ORDER BY ${filters.orderBy} ${direction}`;
      }

      // 处理分页
      if (filters.limit) {
        query += ` LIMIT ${filters.limit}`;

        if (filters.offset) {
          query += ` OFFSET ${filters.offset}`;
        }
      }
    }

    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];

    if (filters) {
      const conditions: string[] = [];

      // 处理基础过滤器
      if (filters.user_id) {
        conditions.push('user_id = ?');
        values.push(filters.user_id);
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }
    }

    return { query, values };
  }

  /**
   * 构建WHERE子句
   */
  protected buildWhereClause(filters: any): { clause: string; values: any[] } {
    const conditions: string[] = [];
    const values: any[] = [];

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        conditions.push(`${key} = ?`);
        values.push(value);
      }
    });

    const clause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { clause, values };
  }

  /**
   * 转义SQL标识符
   */
  protected escapeIdentifier(identifier: string): string {
    return `"${identifier.replace(/"/g, '""')}"`;
  }
}
