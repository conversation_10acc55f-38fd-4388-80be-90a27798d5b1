import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/contexts/LanguageContext';
import { type SyncItem, useSync } from '@/contexts/SyncContext';
import { AlertTriangle, ArrowLeftRight, Check, Clock, CloudOff } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';

interface ConflictResolverProps {
  item: SyncItem;
  localData: any;
  remoteData: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * 冲突解决组件
 * 用于解决同步冲突
 */
const ConflictResolver: React.FC<ConflictResolverProps> = ({
  item,
  localData,
  remoteData,
  open,
  onOpenChange,
}) => {
  const { t } = useLanguage();
  const { resolveConflict } = useSync();
  const [selection, setSelection] = useState<'local' | 'remote'>('local');

  // 获取项目类型的显示名称
  const getItemTypeName = (type: SyncItem['type']) => {
    switch (type) {
      case 'mood_entry':
        return t('sync.mood_entry');
      case 'user_preference':
        return t('sync.user_preference');
      case 'emotion_data':
        return t('sync.emotion_data');
      case 'tag':
        return t('sync.tag');
      default:
        return type;
    }
  };

  // 格式化日期时间
  const formatDateTime = (date: Date | string) => {
    if (!date) return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(dateObj);
  };

  // 处理解决冲突
  const handleResolve = async () => {
    await resolveConflict(item.id, selection === 'local');
    onOpenChange(false);
  };

  // 渲染数据差异
  const renderDataDiff = () => {
    // 这里只是一个简单的实现，实际应用中可能需要更复杂的差异比较
    const localKeys = Object.keys(localData);
    const remoteKeys = Object.keys(remoteData);
    const allKeys = [...new Set([...localKeys, ...remoteKeys])].sort();

    return (
      <div className="space-y-2">
        {allKeys.map((key) => {
          const localValue = localData[key];
          const remoteValue = remoteData[key];
          const isDifferent = JSON.stringify(localValue) !== JSON.stringify(remoteValue);

          if (!isDifferent) return null;

          return (
            <div key={key} className="grid grid-cols-2 gap-2">
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">{t('sync.local')}</Label>
                <div
                  className={`p-2 rounded-md text-sm ${selection === 'local' ? 'bg-primary/10 border border-primary/30' : 'bg-muted'}`}
                >
                  {typeof localValue === 'object'
                    ? JSON.stringify(localValue, null, 2)
                    : String(localValue ?? t('sync.empty'))}
                </div>
              </div>

              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">{t('sync.remote')}</Label>
                <div
                  className={`p-2 rounded-md text-sm ${selection === 'remote' ? 'bg-primary/10 border border-primary/30' : 'bg-muted'}`}
                >
                  {typeof remoteValue === 'object'
                    ? JSON.stringify(remoteValue, null, 2)
                    : String(remoteValue ?? t('sync.empty'))}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-warning mr-2" />
            {t('sync.conflict_title')}
          </DialogTitle>
          <DialogDescription>
            {t('sync.conflict_description', { type: getItemTypeName(item.type) })}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <div>
              <span className="text-muted-foreground">{t('sync.item_id')}:</span> {item.id}
            </div>
            <div>
              <span className="text-muted-foreground">{t('sync.type')}:</span>{' '}
              {getItemTypeName(item.type)}
            </div>
          </div>

          <Separator />

          <RadioGroup
            value={selection}
            onValueChange={(value) => setSelection(value as 'local' | 'remote')}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="local" id="local" />
              <Label htmlFor="local" className="flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                {t('sync.use_local')}
                <span className="text-xs text-muted-foreground ml-2">
                  ({t('sync.modified')}: {formatDateTime(localData.updated_at || new Date())})
                </span>
              </Label>
            </div>

            <div className="flex items-center space-x-2 mt-2">
              <RadioGroupItem value="remote" id="remote" />
              <Label htmlFor="remote" className="flex items-center">
                <CloudOff className="h-4 w-4 mr-2" />
                {t('sync.use_remote')}
                <span className="text-xs text-muted-foreground ml-2">
                  ({t('sync.modified')}: {formatDateTime(remoteData.updated_at || new Date())})
                </span>
              </Label>
            </div>
          </RadioGroup>

          <Separator />

          <div className="space-y-2">
            <Label className="text-sm">{t('sync.differences')}</Label>
            <ScrollArea className="h-[200px] rounded-md border p-2">{renderDataDiff()}</ScrollArea>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleResolve}>
            <Check className="h-4 w-4 mr-2" />
            {t('sync.resolve')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConflictResolver;
