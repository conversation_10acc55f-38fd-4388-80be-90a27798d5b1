/**
 * 心情记录服务
 * 处理与心情记录相关的业务逻辑
 */

import { type InStatement, batchStatements, executeQuery } from '../database/index.js';

// 心情记录接口
export interface MoodEntry {
  id: string;
  user_id: string;
  timestamp: string;
  emotion_data_set_id?: string;
  primary_emotion: string;
  secondary_emotion?: string;
  tertiary_emotion?: string;
  intensity: number;
  reflection?: string;
  created_at: string;
  updated_at: string;
  tags?: string[];
}

// 情绪选择接口
export interface EmotionSelection {
  id?: string;
  mood_entry_id: string;
  emotion_id: string;
  tier_level: number;
  emotion_data_set_emotion_id?: string;
  created_at?: string;
}

// 同步请求接口
export interface SyncRequest {
  moodEntriesToUpload: MoodEntry[];
  emotionSelectionsToUpload?: EmotionSelection[];
  lastSyncTimestamp?: string;
  userId: string;
}

// 同步响应接口
export interface SyncResponse {
  newMoodEntriesFromServer: MoodEntry[];
  newEmotionSelectionsFromServer?: EmotionSelection[];
  serverTimestamp: string;
  success: boolean;
  error?: {
    message: string;
    details?: any;
  };
  uploadedCount: number;
  downloadedCount: number;
}

/**
 * 获取用户的心情记录
 * @param userId 用户ID
 * @param since 可选的时间戳，用于获取特定时间之后的记录
 * @returns 心情记录数组
 */
export async function getMoodEntries(userId: string, since?: string): Promise<MoodEntry[]> {
  try {
    let query = `
      SELECT me.*, GROUP_CONCAT(t.id) as tags
      FROM mood_entries me
      LEFT JOIN mood_entry_tags met ON me.id = met.mood_entry_id
      LEFT JOIN tags t ON met.tag_id = t.id
      WHERE me.user_id = ?
    `;
    const args: any[] = [userId];

    if (since) {
      query += ' AND me.updated_at > ?';
      args.push(since);
    }

    query += ' GROUP BY me.id ORDER BY me.timestamp DESC';

    const result = await executeQuery({ sql: query, args });

    return (result.rows || []).map((row: any) => ({
      id: String(row.id),
      user_id: String(row.user_id),
      timestamp: String(row.timestamp),
      emotion_data_set_id: row.emotion_data_set_id ? String(row.emotion_data_set_id) : undefined,
      primary_emotion: String(row.primary_emotion),
      secondary_emotion: row.secondary_emotion ? String(row.secondary_emotion) : undefined,
      tertiary_emotion: row.tertiary_emotion ? String(row.tertiary_emotion) : undefined,
      intensity: Number(row.intensity),
      reflection: row.reflection ? String(row.reflection) : undefined,
      created_at: String(row.created_at),
      updated_at: String(row.updated_at),
      tags: row.tags ? String(row.tags).split(',') : [],
    }));
  } catch (error) {
    console.error('[MoodEntryService] Error getting mood entries:', error);
    throw error;
  }
}

/**
 * 获取心情记录的情绪选择
 * @param moodEntryId 心情记录ID
 * @returns 情绪选择数组
 */
export async function getEmotionSelections(moodEntryId: string): Promise<EmotionSelection[]> {
  try {
    const query = `
      SELECT es.*, e.name, e.color, e.emoji
      FROM emotion_selections es
      JOIN emotions e ON es.emotion_id = e.id
      WHERE es.mood_entry_id = ?
      ORDER BY es.tier_level
    `;

    const result = await executeQuery({ sql: query, args: [moodEntryId] });

    return (result.rows || []).map((row: any) => ({
      id: String(row.id),
      mood_entry_id: String(row.mood_entry_id),
      emotion_id: String(row.emotion_id),
      tier_level: Number(row.tier_level),
      emotion_data_set_emotion_id: row.emotion_data_set_emotion_id
        ? String(row.emotion_data_set_emotion_id)
        : undefined,
      created_at: String(row.created_at),
    }));
  } catch (error) {
    console.error('[MoodEntryService] Error getting emotion selections:', error);
    throw error;
  }
}

/**
 * 获取带有情绪选择的心情记录
 * @param userId 用户ID
 * @param since 可选的时间戳，用于获取特定时间之后的记录
 * @returns 带有情绪选择的心情记录数组
 */
export async function getMoodEntriesWithSelections(
  userId: string,
  since?: string
): Promise<{ moodEntry: MoodEntry; emotionSelections: EmotionSelection[] }[]> {
  try {
    const moodEntries = await getMoodEntries(userId, since);
    const result: { moodEntry: MoodEntry; emotionSelections: EmotionSelection[] }[] = [];

    for (const moodEntry of moodEntries) {
      const emotionSelections = await getEmotionSelections(moodEntry.id);
      result.push({ moodEntry, emotionSelections });
    }

    return result;
  } catch (error) {
    console.error('[MoodEntryService] Error getting mood entries with selections:', error);
    throw error;
  }
}

/**
 * 保存心情记录
 * @param moodEntry 心情记录
 * @param emotionSelections 可选的情绪选择数组
 * @returns 保存的心情记录ID
 */
export async function saveMoodEntry(
  moodEntry: MoodEntry,
  emotionSelections?: EmotionSelection[]
): Promise<string> {
  try {
    const statements: InStatement[] = [];

    // 构建心情记录的SQL语句
    const moodEntrySql = `
      INSERT INTO mood_entries (
        id, user_id, timestamp, emotion_data_set_id,
        primary_emotion, secondary_emotion, tertiary_emotion,
        intensity, reflection, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON CONFLICT(id) DO UPDATE SET
        user_id = excluded.user_id,
        timestamp = excluded.timestamp,
        emotion_data_set_id = excluded.emotion_data_set_id,
        primary_emotion = excluded.primary_emotion,
        secondary_emotion = excluded.secondary_emotion,
        tertiary_emotion = excluded.tertiary_emotion,
        intensity = excluded.intensity,
        reflection = excluded.reflection,
        updated_at = excluded.updated_at
    `;

    const now = new Date().toISOString();

    statements.push({
      sql: moodEntrySql,
      args: [
        moodEntry.id,
        moodEntry.user_id,
        moodEntry.timestamp,
        moodEntry.emotion_data_set_id || null,
        moodEntry.primary_emotion,
        moodEntry.secondary_emotion || null,
        moodEntry.tertiary_emotion || null,
        moodEntry.intensity,
        moodEntry.reflection || null,
        moodEntry.created_at || now,
        moodEntry.updated_at || now,
      ],
    });

    // 如果提供了情绪选择，则添加情绪选择的SQL语句
    if (emotionSelections && emotionSelections.length > 0) {
      // 首先删除现有的情绪选择
      statements.push({
        sql: 'DELETE FROM emotion_selections WHERE mood_entry_id = ?',
        args: [moodEntry.id],
      });

      // 然后添加新的情绪选择
      for (const selection of emotionSelections) {
        const selectionId =
          selection.id || `${moodEntry.id}_${selection.emotion_id}_${selection.tier_level}`;

        statements.push({
          sql: `
            INSERT INTO emotion_selections (
              id, mood_entry_id, emotion_id, tier_level, emotion_data_set_emotion_id, created_at
            ) VALUES (?, ?, ?, ?, ?, ?)
          `,
          args: [
            selectionId,
            moodEntry.id,
            selection.emotion_id,
            selection.tier_level,
            selection.emotion_data_set_emotion_id || null,
            selection.created_at || now,
          ],
        });
      }
    }

    // 如果提供了标签，则添加标签的SQL语句
    if (moodEntry.tags && moodEntry.tags.length > 0) {
      // 首先删除现有的标签关联
      statements.push({
        sql: 'DELETE FROM mood_entry_tags WHERE mood_entry_id = ?',
        args: [moodEntry.id],
      });

      // 然后添加新的标签关联
      for (const tagId of moodEntry.tags) {
        statements.push({
          sql: 'INSERT INTO mood_entry_tags (mood_entry_id, tag_id) VALUES (?, ?)',
          args: [moodEntry.id, tagId],
        });
      }
    }

    // 执行批处理
    await batchStatements(statements);

    return moodEntry.id;
  } catch (error) {
    console.error('[MoodEntryService] Error saving mood entry:', error);
    throw error;
  }
}

/**
 * 同步心情记录
 * @param syncRequest 同步请求
 * @returns 同步响应
 */
export async function synchronizeMoodEntries(syncRequest: SyncRequest): Promise<SyncResponse> {
  try {
    const { moodEntriesToUpload, emotionSelectionsToUpload, lastSyncTimestamp, userId } =
      syncRequest;
    const currentSyncTimestamp = new Date().toISOString();
    let uploadedCount = 0;
    let downloadedCount = 0;

    // 1. 上传心情记录
    if (moodEntriesToUpload && moodEntriesToUpload.length > 0) {
      for (const moodEntry of moodEntriesToUpload) {
        // 获取与此心情记录关联的情绪选择
        const relatedSelections = emotionSelectionsToUpload?.filter(
          (selection) => selection.mood_entry_id === moodEntry.id
        );

        await saveMoodEntry(moodEntry, relatedSelections);
        uploadedCount++;
      }
    }

    // 2. 下载新的心情记录
    const newMoodEntriesWithSelections = await getMoodEntriesWithSelections(
      userId,
      lastSyncTimestamp
    );
    downloadedCount = newMoodEntriesWithSelections.length;

    // 3. 构建响应
    return {
      newMoodEntriesFromServer: newMoodEntriesWithSelections.map((item) => item.moodEntry),
      newEmotionSelectionsFromServer: newMoodEntriesWithSelections.flatMap(
        (item) => item.emotionSelections
      ),
      serverTimestamp: currentSyncTimestamp,
      success: true,
      uploadedCount,
      downloadedCount,
    };
  } catch (error) {
    console.error('[MoodEntryService] Error synchronizing mood entries:', error);
    return {
      newMoodEntriesFromServer: [],
      serverTimestamp: new Date().toISOString(),
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error,
      },
      uploadedCount: 0,
      downloadedCount: 0,
    };
  }
}
