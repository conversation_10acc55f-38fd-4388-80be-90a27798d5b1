/**
 * Quiz会话仓储 - 修复版本
 * 纯数据访问层，不包含业务逻辑
 */

import { BaseRepository } from '../base/BaseRepository';
import { QuizSession } from '../../types/schema/base';
import { CreateQuizSessionInput, UpdateQuizSessionInput } from '../../types/schema/api';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class QuizSessionRepository extends BaseRepository<
  QuizSession,
  CreateQuizSessionInput,
  UpdateQuizSessionInput
> {
  constructor(db?: SQLiteDBConnection) {
    super('quiz_sessions', db);
  }

  /**
   * 根据用户ID查找会话
   */
  async findByUserId(userId: string): Promise<QuizSession[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE user_id = ?
      ORDER BY created_at DESC
    `;

    const result = await db.query(query, [userId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据包ID查找会话
   */
  async findByPackId(packId: string): Promise<QuizSession[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE pack_id = ?
      ORDER BY created_at DESC
    `;

    const result = await db.query(query, [packId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 根据状态查找会话
   */
  async findByStatus(status: string): Promise<QuizSession[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE status = ?
      ORDER BY created_at DESC
    `;

    const result = await db.query(query, [status]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 获取用户的活跃会话
   */
  async findActiveByUserId(userId: string): Promise<QuizSession[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE user_id = ? AND status IN ('INITIATED', 'IN_PROGRESS', 'PAUSED')
      ORDER BY last_active_time DESC
    `;

    const result = await db.query(query, [userId]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  /**
   * 获取用户已完成的会话
   */
  async findCompletedByUserId(userId: string, limit: number = 20): Promise<QuizSession[]> {
    const db = this.getDb();
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE user_id = ? AND status = 'COMPLETED'
      ORDER BY end_time DESC
      LIMIT ?
    `;

    const result = await db.query(query, [userId, limit]);
    return (result.values || []).map(row => this.mapRowToEntity(row));
  }

  protected mapRowToEntity(row: any): QuizSession {
    return {
      id: row.id,
      pack_id: row.pack_id,
      user_id: row.user_id,
      status: row.status,
      current_question_index: row.current_question_index || 0,
      total_questions: row.total_questions,
      answered_questions: row.answered_questions || 0,
      completion_percentage: row.completion_percentage || 0,
      start_time: row.start_time,
      end_time: row.end_time,
      last_active_time: row.last_active_time,
      total_duration_seconds: row.total_duration_seconds,
      session_type: row.session_type || 'standard',
      session_metadata: row.session_metadata,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  protected mapEntityToRow(entity: Partial<QuizSession>): Record<string, any> {
    return {
      id: entity.id,
      pack_id: entity.pack_id,
      user_id: entity.user_id,
      status: entity.status,
      current_question_index: entity.current_question_index,
      total_questions: entity.total_questions,
      answered_questions: entity.answered_questions,
      completion_percentage: entity.completion_percentage,
      start_time: entity.start_time,
      end_time: entity.end_time,
      last_active_time: entity.last_active_time,
      total_duration_seconds: entity.total_duration_seconds,
      session_type: entity.session_type,
      session_metadata: entity.session_metadata,
      created_at: entity.created_at,
      updated_at: entity.updated_at
    };
  }

  protected buildInsertQuery(data: CreateQuizSessionInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const query = `
      INSERT INTO ${this.tableName} (
        id, pack_id, user_id, status, current_question_index,
        total_questions, answered_questions, completion_percentage,
        last_active_time, session_type, session_metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      sessionId,
      data.pack_id,
      data.user_id,
      'INITIATED',
      0,
      0,
      0,
      0,
      now,
      data.session_type || 'standard',
      data.session_metadata ? JSON.stringify(data.session_metadata) : null,
      now,
      now
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateQuizSessionInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    });

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.user_id) {
      conditions.push('user_id = ?');
      values.push(filters.user_id);
    }

    if (filters?.pack_id) {
      conditions.push('pack_id = ?');
      values.push(filters.pack_id);
    }

    if (filters?.status) {
      conditions.push('status = ?');
      values.push(filters.status);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY created_at DESC';

    if (filters?.limit) {
      query += ' LIMIT ?';
      values.push(filters.limit);
    }

    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.user_id) {
      conditions.push('user_id = ?');
      values.push(filters.user_id);
    }

    if (filters?.status) {
      conditions.push('status = ?');
      values.push(filters.status);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    return { query, values };
  }

  protected extractIdFromCreateData(_data: CreateQuizSessionInput): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}
