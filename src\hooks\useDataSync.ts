import { useDatabaseContext } from '@/contexts/DatabaseContext';
import { useNetwork } from '@/contexts/NetworkContext';
import { Services } from '@/services';
import { useCallback, useEffect, useState } from 'react';

/**
 * 数据同步状态
 */
export interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  syncProgress: number;
  error: string | null;
  pendingChanges: number;
}

/**
 * 数据同步钩子
 * 提供离线和在线数据同步功能
 */
export const useDataSync = () => {
  const { isOnline, isInternetReachable } = useNetwork();
  const { databaseService, isInitialized, isInitializing, error } = useDatabaseContext();

  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: false,
    isSyncing: false,
    lastSyncTime: null,
    syncProgress: 0,
    error: null,
    pendingChanges: 0,
  });

  // 更新在线状态
  useEffect(() => {
    setSyncStatus((prev) => ({
      ...prev,
      isOnline: isOnline && isInternetReachable,
    }));
  }, [isOnline, isInternetReachable]);

  /**
   * 执行数据同步
   */
  const performSync = useCallback(async () => {
    if (!isInitialized || !isOnline || !isInternetReachable) {
      return;
    }

    setSyncStatus((prev) => ({
      ...prev,
      isSyncing: true,
      error: null,
      syncProgress: 0,
    }));

    try {
      // 获取待同步的数据数量
      const pendingCount = await getPendingChangesCount();

      setSyncStatus((prev) => ({
        ...prev,
        pendingChanges: pendingCount,
      }));

      if (pendingCount === 0) {
        setSyncStatus((prev) => ({
          ...prev,
          isSyncing: false,
          syncProgress: 100,
          lastSyncTime: new Date().toISOString(),
        }));
        return;
      }

      // 执行同步操作
      await syncMoodEntries();
      setSyncStatus((prev) => ({ ...prev, syncProgress: 25 }));

      await syncUserConfig();
      setSyncStatus((prev) => ({ ...prev, syncProgress: 50 }));

      await syncEmotionData();
      setSyncStatus((prev) => ({ ...prev, syncProgress: 75 }));

      await syncOtherData();
      setSyncStatus((prev) => ({ ...prev, syncProgress: 100 }));

      setSyncStatus((prev) => ({
        ...prev,
        isSyncing: false,
        lastSyncTime: new Date().toISOString(),
        pendingChanges: 0,
      }));
    } catch (error) {
      console.error('Sync failed:', error);
      setSyncStatus((prev) => ({
        ...prev,
        isSyncing: false,
        error: error instanceof Error ? error.message : 'Sync failed',
      }));
    }
  }, [isInitialized, isOnline, isInternetReachable]);

  /**
   * 获取待同步的更改数量
   */
  const getPendingChangesCount = async (): Promise<number> => {
    try {
      // 这里应该查询本地数据库中标记为需要同步的记录数量
      // 暂时返回 0，实际实现需要根据具体的同步策略
      return 0;
    } catch (error) {
      console.error('Failed to get pending changes count:', error);
      return 0;
    }
  };

  /**
   * 同步心情记录
   * @deprecated MoodEntryService已被弃用，建议使用Quiz session-based approach
   */
  const syncMoodEntries = async () => {
    try {
      console.warn('[useDataSync] MoodEntryService is deprecated. Consider using Quiz session-based approach.');

      // 使用新的心情追踪服务进行数据同步
      try {
        const moodTrackingService = await Services.moodTracking();

        // 获取本地未同步的心情记录
        const localEntries = await moodTrackingService.getUnsyncedEntries();

        if (localEntries.success && localEntries.data && localEntries.data.length > 0) {
          // 上传到云端 - 这里需要实现云端同步逻辑
          console.log('[useDataSync] Found unsynced mood entries:', localEntries.data.length);
          // TODO: 实现云端同步逻辑
        }

        console.log('[useDataSync] Mood entries sync completed');
      } catch (serviceError) {
        console.error('[useDataSync] MoodTrackingService error:', serviceError);
        // 不抛出错误，只记录警告
        console.warn('[useDataSync] Skipping mood entries sync due to service error');
      }
    } catch (error) {
      console.error('Failed to sync mood entries:', error);
      throw error;
    }
  };

  /**
   * 同步用户配置
   */
  const syncUserConfig = async () => {
    try {
      // 同步用户配置数据
      // 实际实现需要根据具体的用户配置同步策略
      console.log('Syncing user config...');
    } catch (error) {
      console.error('Failed to sync user config:', error);
      throw error;
    }
  };

  /**
   * 同步情绪数据
   */
  const syncEmotionData = async () => {
    try {
      // 同步情绪数据
      // 实际实现需要根据具体的情绪数据同步策略
      console.log('Syncing emotion data...');
    } catch (error) {
      console.error('Failed to sync emotion data:', error);
      throw error;
    }
  };

  /**
   * 同步其他数据
   */
  const syncOtherData = async () => {
    try {
      // 同步其他数据（皮肤、表情包等）
      console.log('Syncing other data...');
    } catch (error) {
      console.error('Failed to sync other data:', error);
      throw error;
    }
  };

  /**
   * 强制同步
   */
  const forceSync = useCallback(async () => {
    if (!isInitialized) {
      throw new Error('Database not initialized');
    }

    await performSync();
  }, [performSync, isInitialized]);

  /**
   * 清除同步错误
   */
  const clearSyncError = useCallback(() => {
    setSyncStatus((prev) => ({
      ...prev,
      error: null,
    }));
  }, []);

  // 自动同步：当网络状态变为在线时
  useEffect(() => {
    if (isOnline && isInternetReachable && isInitialized) {
      // 延迟一秒后执行同步，避免频繁的网络状态变化
      const timer = setTimeout(() => {
        performSync();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isOnline, isInternetReachable, isInitialized, performSync]);

  return {
    syncStatus,
    performSync,
    forceSync,
    clearSyncError,
    isOnline: syncStatus.isOnline,
    isSyncing: syncStatus.isSyncing,
    lastSyncTime: syncStatus.lastSyncTime,
    syncProgress: syncStatus.syncProgress,
    syncError: syncStatus.error,
    pendingChanges: syncStatus.pendingChanges,
  };
};

export default useDataSync;
