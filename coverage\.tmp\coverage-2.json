{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/functional/functional-verification.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 122225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 122225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 524, "endOffset": 46631, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 628, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 11568, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 739, "endOffset": 3936, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3891, "endOffset": 3907, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3990, "endOffset": 7602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7656, "endOffset": 11560, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7797, "endOffset": 8479, "count": 1}, {"startOffset": 8282, "endOffset": 8287, "count": 0}, {"startOffset": 8348, "endOffset": 8353, "count": 0}, {"startOffset": 8374, "endOffset": 8378, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8568, "endOffset": 9283, "count": 1}, {"startOffset": 9191, "endOffset": 9242, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8647, "endOffset": 8681, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8751, "endOffset": 8790, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9377, "endOffset": 10119, "count": 1}, {"startOffset": 9492, "endOffset": 9503, "count": 0}, {"startOffset": 9683, "endOffset": 9695, "count": 0}, {"startOffset": 9974, "endOffset": 10078, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9649, "endOffset": 9670, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9749, "endOffset": 9775, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11621, "endOffset": 23132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11678, "endOffset": 15502, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15554, "endOffset": 19267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15689, "endOffset": 16812, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15946, "endOffset": 16142, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16231, "endOffset": 16430, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19319, "endOffset": 23124, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20886, "endOffset": 21484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23185, "endOffset": 29448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23238, "endOffset": 26198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23364, "endOffset": 23696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23774, "endOffset": 24713, "count": 1}, {"startOffset": 24527, "endOffset": 24534, "count": 0}, {"startOffset": 24669, "endOffset": 24672, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26248, "endOffset": 29440, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29501, "endOffset": 40689, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29554, "endOffset": 33229, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 30443, "endOffset": 31635, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 32335, "endOffset": 32362, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 33282, "endOffset": 36820, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34446, "endOffset": 34763, "count": 1}, {"startOffset": 34628, "endOffset": 34631, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 34847, "endOffset": 35185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35855, "endOffset": 35880, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36869, "endOffset": 40681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37907, "endOffset": 38251, "count": 1}, {"startOffset": 38137, "endOffset": 38143, "count": 0}, {"startOffset": 38202, "endOffset": 38206, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 38334, "endOffset": 39179, "count": 1}, {"startOffset": 39070, "endOffset": 39076, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 39747, "endOffset": 39767, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40742, "endOffset": 46627, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 40799, "endOffset": 46619, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 44196, "endOffset": 44659, "count": 1}, {"startOffset": 44601, "endOffset": 44609, "count": 0}], "isBlockCoverage": true}], "startOffset": 185}]}