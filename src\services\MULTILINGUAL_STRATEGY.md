# 多语言处理统一策略

## 概述

为了保持数据库设计的一致性，我们采用**独立翻译表**的模式来处理所有多语言内容。

## 设计原则

### 1. 主表 + 翻译表模式
- **主表**: 存储默认语言（通常是英文）的内容和核心数据
- **翻译表**: 存储其他语言的翻译内容
- **命名规范**: `{主表名}_translations`

### 2. 翻译表结构标准
```sql
CREATE TABLE {table_name}_translations (
    {foreign_key} TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_{field_name} TEXT NOT NULL,
    PRIMARY KEY ({foreign_key}, language_code),
    FOREIGN KEY ({foreign_key}) REFERENCES {main_table}(id) ON DELETE CASCADE
);
```

### 3. 语言代码标准
- 使用 ISO 639-1 标准（如：'en', 'zh', 'ja', 'ko'）
- 支持地区变体（如：'zh-CN', 'zh-TW', 'en-US', 'en-GB'）

## 当前表结构分析

### ✅ 已正确实现的表
1. **ui_labels** + **ui_label_translations**
2. **emotions** + **emotion_translations** 
3. **tags** + **tag_translations**
4. **emoji_sets** + **emoji_set_translations**

### ❌ 需要修复的表

#### 1. emotions 表
**问题**: 既有 `localized_names` JSON 字段，又有 `emotion_translations` 表
**解决方案**: 移除 `localized_names` 字段，统一使用 `emotion_translations` 表

#### 2. emotion_data_sets 表
**问题**: 只有 `localized_names` 和 `localized_descriptions` JSON 字段
**解决方案**: 创建 `emotion_data_set_translations` 表

#### 3. emotion_data_set_tiers 表
**问题**: 只有 `localized_names` JSON 字段
**解决方案**: 创建 `emotion_data_set_tier_translations` 表

#### 4. skins 表
**问题**: 没有多语言支持
**解决方案**: 创建 `skin_translations` 表

### 🆕 需要创建的翻译表

#### emotion_data_set_translations
```sql
CREATE TABLE emotion_data_set_translations (
    emotion_data_set_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (emotion_data_set_id, language_code),
    FOREIGN KEY (emotion_data_set_id) REFERENCES emotion_data_sets(id) ON DELETE CASCADE
);
```

#### emotion_data_set_tier_translations
```sql
CREATE TABLE emotion_data_set_tier_translations (
    tier_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (tier_id, language_code),
    FOREIGN KEY (tier_id) REFERENCES emotion_data_set_tiers(id) ON DELETE CASCADE
);
```

#### skin_translations
```sql
CREATE TABLE skin_translations (
    skin_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (skin_id, language_code),
    FOREIGN KEY (skin_id) REFERENCES skins(id) ON DELETE CASCADE
);
```

## 服务层实现策略

### 1. 基础翻译接口
```typescript
interface TranslatableEntity {
  id: string;
  name: string; // 默认语言名称
  translations?: Translation[];
  localizedName?: string; // 当前语言的名称
}

interface Translation {
  entityId: string;
  languageCode: string;
  translatedName: string;
  translatedDescription?: string;
}
```

### 2. 仓储层模式
```typescript
class TranslatableRepository<T extends TranslatableEntity> {
  // 获取带翻译的实体
  async findWithTranslations(id: string, languageCode?: string): Promise<T>;
  
  // 批量获取翻译
  async getTranslations(entityId: string): Promise<Translation[]>;
  
  // 添加/更新翻译
  async upsertTranslation(entityId: string, languageCode: string, translation: Partial<Translation>): Promise<void>;
  
  // 删除翻译
  async deleteTranslation(entityId: string, languageCode: string): Promise<void>;
}
```

### 3. 服务层模式
```typescript
class TranslatableService<T extends TranslatableEntity> {
  // 创建实体（包含翻译）
  async create(data: CreateInput & { translations?: TranslationInput[] }): Promise<ServiceResult<T>>;
  
  // 更新实体（包含翻译）
  async update(id: string, data: UpdateInput & { translations?: TranslationInput[] }): Promise<ServiceResult<T>>;
  
  // 获取本地化实体
  async getLocalized(id: string, languageCode: string): Promise<ServiceResult<T>>;
  
  // 搜索（支持多语言）
  async search(term: string, languageCode?: string): Promise<ServiceResult<T[]>>;
}
```

## 查询模式

### 1. 获取本地化内容
```sql
SELECT 
  e.*,
  COALESCE(et.translated_name, e.name) as localized_name,
  COALESCE(et.translated_description, e.description) as localized_description
FROM emotions e
LEFT JOIN emotion_translations et ON e.id = et.emotion_id AND et.language_code = ?
WHERE e.id = ?
```

### 2. 搜索多语言内容
```sql
SELECT DISTINCT e.*,
  COALESCE(et.translated_name, e.name) as localized_name
FROM emotions e
LEFT JOIN emotion_translations et ON e.id = et.emotion_id AND et.language_code = ?
WHERE e.name LIKE ? OR et.translated_name LIKE ?
ORDER BY localized_name
```

### 3. 获取所有翻译
```sql
SELECT * FROM emotion_translations 
WHERE emotion_id = ?
ORDER BY language_code
```

## 迁移策略

### 阶段1: 创建缺失的翻译表
1. 创建 `emotion_data_set_translations`
2. 创建 `emotion_data_set_tier_translations`  
3. 创建 `skin_translations`

### 阶段2: 数据迁移
1. 将 `emotion_data_sets.localized_names` JSON 数据迁移到翻译表
2. 将 `emotion_data_set_tiers.localized_names` JSON 数据迁移到翻译表
3. 将 `emotions.localized_names` JSON 数据迁移到翻译表

### 阶段3: 清理冗余字段
1. 移除 `emotions.localized_names`
2. 移除 `emotion_data_sets.localized_names` 和 `localized_descriptions`
3. 移除 `emotion_data_set_tiers.localized_names`

### 阶段4: 更新服务层
1. 创建统一的翻译基类
2. 更新所有相关的仓储和服务
3. 更新前端组件以使用新的翻译API

## 优势

1. **一致性**: 所有表使用相同的多语言处理模式
2. **可扩展性**: 易于添加新语言和新的可翻译字段
3. **性能**: 可以根据需要选择是否加载翻译
4. **维护性**: 翻译逻辑集中，易于维护
5. **灵活性**: 支持部分翻译，回退到默认语言

## 注意事项

1. **性能考虑**: 大量数据时考虑使用索引和缓存
2. **数据完整性**: 确保删除主记录时级联删除翻译
3. **默认语言**: 主表中的内容作为默认语言（通常是英文）
4. **回退机制**: 当翻译不存在时，显示默认语言内容
5. **批量操作**: 提供批量翻译的API以提高效率
