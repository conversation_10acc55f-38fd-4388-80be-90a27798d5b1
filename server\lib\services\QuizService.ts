/**
 * Quiz服务 - 服务端专用实现
 *
 * 职责:
 * - 数据验证和持久化
 * - 跨用户数据分析
 * - 服务端专用的Quiz逻辑处理
 * - 与客户端QuizEngineV3协调
 */

import { executeQuery, batchStatements } from '../database/index.js';
import type {
  QuizPack,
  QuizSession,
  QuizAnswer,
  QuizResult
} from '../../../src/types/schema/index.js';

// 服务端专用的响应类型
interface ServerQuizResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Quiz统计数据类型
interface QuizStatistics {
  packId: string;
  totalSessions: number;
  completedSessions: number;
  averageCompletionTime: number;
  popularQuestions: Array<{
    questionId: string;
    answerCount: number;
  }>;
}

// 异常检测报告类型
interface AnomalyReport {
  sessionId: string;
  anomalies: Array<{
    type: 'suspicious_timing' | 'invalid_sequence' | 'duplicate_answers';
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
}

/**
 * 服务端Quiz服务
 * 专注于数据验证、持久化和分析功能
 */
export class QuizService {
  private static instance: QuizService;

  private constructor() {
    // 服务端不需要复杂的初始化，直接使用数据库操作
  }

  public static getInstance(): QuizService {
    if (!QuizService.instance) {
      QuizService.instance = new QuizService();
    }
    return QuizService.instance;
  }

  /**
   * 获取可用的Quiz包列表 (服务端实现)
   */
  async getAvailableQuizPacks(userId: string, userType: string = 'free'): Promise<ServerQuizResponse<QuizPack[]>> {
    try {
      const result = await executeQuery({
        sql: `
          SELECT qp.* FROM quiz_packs qp
          WHERE qp.is_active = 1
          AND (qp.access_level = 'free' OR ? = 'vip')
          ORDER BY qp.display_order, qp.created_at DESC
        `,
        args: [userType]
      });

      return {
        success: true,
        data: result.rows as QuizPack[]
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get quiz packs'
      };
    }
  }

  /**
   * 获取推荐的Quiz包 (基于用户历史)
   */
  async getRecommendedQuizPacks(userId: string): Promise<ServerQuizResponse<QuizPack[]>> {
    try {
      // 基于用户历史推荐Quiz包
      const result = await executeQuery({
        sql: `
          SELECT qp.*, COUNT(qs.id) as usage_count
          FROM quiz_packs qp
          LEFT JOIN quiz_sessions qs ON qp.id = qs.pack_id AND qs.user_id = ?
          WHERE qp.is_active = 1
          GROUP BY qp.id
          ORDER BY usage_count ASC, qp.popularity_score DESC
          LIMIT 10
        `,
        args: [userId]
      });

      return {
        success: true,
        data: result.rows as QuizPack[]
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get recommended quiz packs'
      };
    }
  }

  /**
   * 根据类型获取Quiz包
   */
  async getQuizPacksByType(quizType: string): Promise<ServerQuizResponse<QuizPack[]>> {
    try {
      const result = await executeQuery({
        sql: `
          SELECT * FROM quiz_packs
          WHERE quiz_type = ? AND is_active = 1
          ORDER BY display_order, created_at DESC
        `,
        args: [quizType]
      });

      return {
        success: true,
        data: result.rows as QuizPack[]
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get quiz packs by type'
      };
    }
  }

  /**
   * 搜索Quiz包
   */
  async searchQuizPacks(searchTerm: string, filters?: any): Promise<ServerQuizResponse<QuizPack[]>> {
    try {
      let sql = `
        SELECT * FROM quiz_packs
        WHERE is_active = 1
        AND (title LIKE ? OR description LIKE ?)
      `;
      const args = [`%${searchTerm}%`, `%${searchTerm}%`];

      // 添加过滤条件
      if (filters?.quizType) {
        sql += ' AND quiz_type = ?';
        args.push(filters.quizType);
      }
      if (filters?.accessLevel) {
        sql += ' AND access_level = ?';
        args.push(filters.accessLevel);
      }

      sql += ' ORDER BY title';

      const result = await executeQuery({ sql, args });

      return {
        success: true,
        data: result.rows as QuizPack[]
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search quiz packs'
      };
    }
  }

  /**
   * 验证Quiz会话数据 (服务端专用)
   */
  async validateQuizSession(sessionData: QuizSession): Promise<ServerQuizResponse<boolean>> {
    try {
      // 验证Quiz包存在
      const packResult = await executeQuery({
        sql: 'SELECT id FROM quiz_packs WHERE id = ? AND is_active = 1',
        args: [sessionData.pack_id]
      });

      if (packResult.rows.length === 0) {
        return {
          success: false,
          error: `Quiz pack not found: ${sessionData.pack_id}`
        };
      }

      // 验证用户存在
      const userResult = await executeQuery({
        sql: 'SELECT id FROM users WHERE id = ?',
        args: [sessionData.user_id]
      });

      if (userResult.rows.length === 0) {
        return {
          success: false,
          error: `User not found: ${sessionData.user_id}`
        };
      }

      // 验证会话数据完整性
      if (!sessionData.id || !sessionData.pack_id || !sessionData.user_id) {
        return {
          success: false,
          error: 'Missing required session fields'
        };
      }

      return {
        success: true,
        data: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to validate quiz session'
      };
    }
  }

  /**
   * 持久化Quiz结果 (服务端专用)
   */
  async persistQuizResults(results: QuizResult[]): Promise<ServerQuizResponse<boolean>> {
    try {
      const statements = results.map(result => ({
        sql: `
          INSERT OR REPLACE INTO quiz_results (
            id, session_id, user_id, pack_id, total_questions,
            answered_questions, completion_percentage, total_time_ms,
            started_at, completed_at, result_data, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        args: [
          result.id,
          result.session_id,
          result.user_id,
          result.pack_id,
          result.total_questions,
          result.answered_questions,
          result.completion_percentage,
          result.total_time_ms,
          result.started_at,
          result.completed_at,
          JSON.stringify(result.result_data),
          new Date().toISOString(),
          new Date().toISOString()
        ]
      }));

      const batchResult = await batchStatements(statements);

      return {
        success: batchResult !== null,
        data: batchResult !== null
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to persist quiz results'
      };
    }
  }

  /**
   * 生成Quiz统计数据 (服务端专用)
   */
  async generateQuizStatistics(packId: string): Promise<ServerQuizResponse<QuizStatistics>> {
    try {
      // 获取基础统计
      const statsResult = await executeQuery({
        sql: `
          SELECT
            COUNT(*) as total_sessions,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_sessions,
            AVG(CASE WHEN status = 'COMPLETED' THEN total_time_ms END) as avg_completion_time
          FROM quiz_sessions
          WHERE pack_id = ?
        `,
        args: [packId]
      });

      // 获取热门问题
      const popularQuestionsResult = await executeQuery({
        sql: `
          SELECT qa.question_id, COUNT(*) as answer_count
          FROM quiz_answers qa
          JOIN quiz_sessions qs ON qa.session_id = qs.id
          WHERE qs.pack_id = ?
          GROUP BY qa.question_id
          ORDER BY answer_count DESC
          LIMIT 10
        `,
        args: [packId]
      });

      const stats = statsResult.rows[0];
      const statistics: QuizStatistics = {
        packId,
        totalSessions: stats.total_sessions || 0,
        completedSessions: stats.completed_sessions || 0,
        averageCompletionTime: stats.avg_completion_time || 0,
        popularQuestions: popularQuestionsResult.rows.map((row: any) => ({
          questionId: row.question_id,
          answerCount: row.answer_count
        }))
      };

      return {
        success: true,
        data: statistics
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate quiz statistics'
      };
    }
  }

  /**
   * 检测异常Quiz结果 (服务端专用)
   */
  async detectAnomalousResults(results: QuizResult[]): Promise<ServerQuizResponse<AnomalyReport[]>> {
    try {
      const reports: AnomalyReport[] = [];

      for (const result of results) {
        const anomalies: AnomalyReport['anomalies'] = [];

        // 检测可疑的完成时间
        if (result.total_time_ms && result.total_time_ms < 10000) { // 少于10秒
          anomalies.push({
            type: 'suspicious_timing',
            description: `Quiz completed in ${result.total_time_ms}ms, which is unusually fast`,
            severity: 'high'
          });
        }

        // 检测完成率异常
        if (result.completion_percentage === 100 && result.total_time_ms && result.total_time_ms < 30000) {
          anomalies.push({
            type: 'suspicious_timing',
            description: 'Perfect completion in unusually short time',
            severity: 'medium'
          });
        }

        if (anomalies.length > 0) {
          reports.push({
            sessionId: result.session_id,
            anomalies
          });
        }
      }

      return {
        success: true,
        data: reports
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to detect anomalous results'
      };
    }
  }

  /**
   * 获取用户的Quiz会话 (服务端实现)
   */
  async getUserSessions(userId: string, limit: number = 20): Promise<ServerQuizResponse<QuizSession[]>> {
    try {
      const result = await executeQuery({
        sql: `
          SELECT qs.*, qp.name as pack_name
          FROM quiz_sessions qs
          JOIN quiz_packs qp ON qs.pack_id = qp.id
          WHERE qs.user_id = ?
          ORDER BY qs.created_at DESC
          LIMIT ?
        `,
        args: [userId, limit]
      });

      return {
        success: true,
        data: result.rows as QuizSession[]
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get user sessions'
      };
    }
  }

  /**
   * 获取会话答案 (服务端实现)
   */
  async getSessionAnswers(sessionId: string): Promise<ServerQuizResponse<QuizAnswer[]>> {
    try {
      const result = await executeQuery({
        sql: `
          SELECT qa.*, qq.question_text, qqo.option_text
          FROM quiz_answers qa
          LEFT JOIN quiz_questions qq ON qa.question_id = qq.id
          LEFT JOIN quiz_question_options qqo ON qa.selected_option_ids LIKE '%' || qqo.id || '%'
          WHERE qa.session_id = ?
          ORDER BY qa.answered_at
        `,
        args: [sessionId]
      });

      return {
        success: true,
        data: result.rows as QuizAnswer[]
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get session answers'
      };
    }
  }

  /**
   * 获取Quiz包详情 (服务端实现)
   */
  async getQuizPackDetails(packId: string): Promise<ServerQuizResponse<QuizPack & { questionCount: number; sessionCount: number }>> {
    try {
      // 获取Quiz包基本信息
      const packResult = await executeQuery({
        sql: 'SELECT * FROM quiz_packs WHERE id = ?',
        args: [packId]
      });

      if (packResult.rows.length === 0) {
        return {
          success: false,
          error: 'Quiz pack not found'
        };
      }

      // 获取统计信息
      const statsResult = await executeQuery({
        sql: `
          SELECT
            (SELECT COUNT(*) FROM quiz_questions WHERE pack_id = ?) as question_count,
            (SELECT COUNT(*) FROM quiz_sessions WHERE pack_id = ?) as session_count
        `,
        args: [packId, packId]
      });

      const pack = packResult.rows[0] as QuizPack;
      const stats = statsResult.rows[0];

      return {
        success: true,
        data: {
          ...pack,
          questionCount: stats.question_count || 0,
          sessionCount: stats.session_count || 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get quiz pack details'
      };
    }
  }

  /**
   * 更新会话状态 (服务端实现)
   */
  async updateSessionStatus(sessionId: string, status: 'PAUSED' | 'IN_PROGRESS' | 'COMPLETED'): Promise<ServerQuizResponse<boolean>> {
    try {
      const result = await executeQuery({
        sql: `
          UPDATE quiz_sessions
          SET status = ?, updated_at = ?
          WHERE id = ?
        `,
        args: [status, new Date().toISOString(), sessionId]
      });

      return {
        success: result.changes > 0,
        data: result.changes > 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update session status'
      };
    }
  }

  /**
   * 获取Quiz包的问题数量 (服务端实现)
   */
  async getQuizPackQuestionCount(packId: string): Promise<ServerQuizResponse<number>> {
    try {
      const result = await executeQuery({
        sql: 'SELECT COUNT(*) as count FROM quiz_questions WHERE pack_id = ? AND is_active = 1',
        args: [packId]
      });

      return {
        success: true,
        data: result.rows[0]?.count || 0
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get question count'
      };
    }
  }
}
