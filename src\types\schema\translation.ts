/**
 * 翻译系统扩展 Schema
 * 
 * 基于 base.ts 和 api.ts 的翻译 Schema，提供高级翻译功能和工具类型
 * 与原有的 translationTypes.ts 兼容，但使用新的 Zod 架构
 */

import { z } from 'zod';
import {
  LanguageCodeSchema,
  UILabelTranslationSchema,
  EmotionTranslationSchema,
  EmotionDataSetTranslationSchema,
  EmojiSetTranslationSchema,
  SkinTranslationSchema,
  TagTranslationSchema,
  type LanguageCode
} from './base';

// ==================== 扩展翻译 Schema ====================

/**
 * 通用翻译接口 Schema
 */
export const TranslationSchema = z.object({
  entityId: z.string().min(1),
  languageCode: LanguageCodeSchema,
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional()
});

/**
 * 可翻译实体基础 Schema
 */
export const TranslatableEntitySchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1), // 默认语言名称
  description: z.string().optional(), // 默认语言描述
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  
  // 运行时翻译字段（由服务层填充）
  localizedName: z.string().optional(), // 当前语言的名称
  localizedDescription: z.string().optional(), // 当前语言的描述
  translations: z.array(TranslationSchema).optional() // 所有翻译
});

/**
 * 翻译输入 Schema（用于创建和更新）
 */
export const TranslationInputSchema = z.object({
  languageCode: LanguageCodeSchema,
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional()
});

/**
 * 可翻译创建输入 Schema
 */
export const TranslatableCreateInputSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  translations: z.array(TranslationInputSchema).optional()
});

/**
 * 可翻译更新输入 Schema
 */
export const TranslatableUpdateInputSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  translations: z.array(TranslationInputSchema).optional()
});

/**
 * 翻译查询过滤器 Schema
 */
export const TranslationFilterSchema = z.object({
  languageCode: LanguageCodeSchema.optional(),
  searchTerm: z.string().optional(),
  limit: z.number().int().min(1).max(1000).default(100),
  offset: z.number().int().min(0).default(0),
  orderBy: z.string().optional(),
  orderDirection: z.enum(['ASC', 'DESC']).default('ASC')
});

/**
 * 语言支持信息 Schema（扩展版）
 */
export const LanguageSupportExtendedSchema = z.object({
  code: LanguageCodeSchema,
  name: z.string().min(1),
  nativeName: z.string().min(1),
  region: z.string().optional(),
  isRTL: z.boolean().default(false),
  isSupported: z.boolean().default(true),
  completionPercentage: z.number().min(0).max(100).default(0),
  lastUpdated: z.string().datetime().optional(),
  totalEntities: z.number().int().min(0).default(0),
  translatedEntities: z.number().int().min(0).default(0)
});

/**
 * 翻译验证结果 Schema
 */
export const TranslationValidationSchema = z.object({
  isValid: z.boolean(),
  errors: z.array(z.string()),
  warnings: z.array(z.string()),
  suggestions: z.array(z.string())
});

/**
 * 翻译导入/导出格式 Schema
 */
export const TranslationExportSchema = z.object({
  version: z.string(),
  exportDate: z.string().datetime(),
  languages: z.array(LanguageCodeSchema),
  entities: z.record(z.string(), z.record(z.string(), z.object({
    defaultName: z.string(),
    defaultDescription: z.string().optional(),
    translations: z.record(LanguageCodeSchema, z.object({
      name: z.string(),
      description: z.string().optional()
    }))
  })))
});

/**
 * 翻译上下文 Schema
 */
export const TranslationContextSchema = z.object({
  currentLanguage: LanguageCodeSchema,
  fallbackLanguage: LanguageCodeSchema.default('en'),
  supportedLanguages: z.array(LanguageCodeSchema),
  isRTL: z.boolean().default(false),
  dateFormat: z.string().default('YYYY-MM-DD'),
  numberFormat: z.string().default('en-US'),
  currencyFormat: z.string().default('USD')
});

/**
 * 翻译事件 Schema
 */
export const TranslationEventSchema = z.object({
  type: z.enum(['language_changed', 'translation_updated', 'translation_added', 'translation_removed']),
  entityType: z.enum(['emotion', 'emotion_data_set', 'emoji_set', 'skin', 'tag', 'ui_label']),
  entityId: z.string().min(1),
  languageCode: LanguageCodeSchema,
  oldValue: z.string().optional(),
  newValue: z.string().optional(),
  timestamp: z.string().datetime()
});

// ==================== 联合类型 Schema ====================

/**
 * 所有翻译类型的联合 Schema
 */
export const AnyTranslationSchema = z.union([
  UILabelTranslationSchema,
  EmotionTranslationSchema,
  EmotionDataSetTranslationSchema,
  EmojiSetTranslationSchema,
  SkinTranslationSchema,
  TagTranslationSchema
]);

/**
 * 实体类型枚举 Schema
 */
export const EntityTypeSchema = z.enum([
  'emotion',
  'emotion_data_set', 
  'emoji_set',
  'skin',
  'tag',
  'ui_label'
]);

// ==================== 工具函数类型 Schema ====================

/**
 * 翻译工具函数配置 Schema
 */
export const TranslationUtilsConfigSchema = z.object({
  defaultLanguage: LanguageCodeSchema.default('en'),
  fallbackLanguage: LanguageCodeSchema.default('en'),
  supportedLanguages: z.array(LanguageCodeSchema).default(['en']),
  cacheEnabled: z.boolean().default(true),
  cacheTimeout: z.number().int().min(0).default(3600000), // 1 hour in ms
  validateTranslations: z.boolean().default(true)
});

// ==================== 导出类型 ====================

// 基础翻译类型
export type Translation = z.infer<typeof TranslationSchema>;
export type TranslatableEntity = z.infer<typeof TranslatableEntitySchema>;
export type TranslationInput = z.infer<typeof TranslationInputSchema>;
export type TranslatableCreateInput = z.infer<typeof TranslatableCreateInputSchema>;
export type TranslatableUpdateInput = z.infer<typeof TranslatableUpdateInputSchema>;

// 查询和过滤类型
export type TranslationFilter = z.infer<typeof TranslationFilterSchema>;
export type LanguageSupportExtended = z.infer<typeof LanguageSupportExtendedSchema>;

// 验证和导入导出类型
export type TranslationValidation = z.infer<typeof TranslationValidationSchema>;
export type TranslationExport = z.infer<typeof TranslationExportSchema>;

// 上下文和事件类型
export type TranslationContext = z.infer<typeof TranslationContextSchema>;
export type TranslationEvent = z.infer<typeof TranslationEventSchema>;

// 联合和枚举类型
export type AnyTranslation = z.infer<typeof AnyTranslationSchema>;
export type EntityType = z.infer<typeof EntityTypeSchema>;

// 工具配置类型
export type TranslationUtilsConfig = z.infer<typeof TranslationUtilsConfigSchema>;

// ==================== 常量 ====================

/**
 * 语言代码常量（与原 translationTypes.ts 兼容）
 */
export const LANGUAGE_CODES = {
  ENGLISH: 'en',
  CHINESE_SIMPLIFIED: 'zh',
  CHINESE_TRADITIONAL: 'zh-TW',
  JAPANESE: 'ja',
  KOREAN: 'ko',
  SPANISH: 'es',
  FRENCH: 'fr',
  GERMAN: 'de',
  ITALIAN: 'it',
  PORTUGUESE: 'pt',
  RUSSIAN: 'ru',
  ARABIC: 'ar',
  HINDI: 'hi',
  THAI: 'th',
  VIETNAMESE: 'vi'
} as const;

/**
 * 默认支持的语言列表（使用新的 Schema 验证）
 */
export const DEFAULT_SUPPORTED_LANGUAGES: LanguageSupportExtended[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    region: 'US',
    isRTL: false,
    isSupported: true,
    completionPercentage: 100,
    totalEntities: 0,
    translatedEntities: 0
  },
  {
    code: 'zh',
    name: 'Chinese (Simplified)',
    nativeName: '中文（简体）',
    region: 'CN',
    isRTL: false,
    isSupported: true,
    completionPercentage: 0,
    totalEntities: 0,
    translatedEntities: 0
  },
  {
    code: 'zh-TW',
    name: 'Chinese (Traditional)',
    nativeName: '中文（繁體）',
    region: 'TW',
    isRTL: false,
    isSupported: true,
    completionPercentage: 0,
    totalEntities: 0,
    translatedEntities: 0
  },
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    region: 'JP',
    isRTL: false,
    isSupported: true,
    completionPercentage: 0,
    totalEntities: 0,
    translatedEntities: 0
  },
  {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    region: 'KR',
    isRTL: false,
    isSupported: true,
    completionPercentage: 0,
    totalEntities: 0,
    translatedEntities: 0
  }
];

/**
 * 实体类型映射
 */
export const ENTITY_TYPE_MAPPING = {
  emotion: 'emotions',
  emotion_data_set: 'emotion_data_sets',
  emoji_set: 'emoji_sets',
  skin: 'skins',
  tag: 'tags',
  ui_label: 'ui_labels'
} as const;
