/**
 * Quiz评分组件
 * 支持多种中医文化样式的评分组件
 */

import React, { useState, useRef } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { RatingComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type RatingComponentConfig = z.infer<typeof RatingComponentConfigSchema>;

export interface RatingComponentProps extends BaseQuizComponentProps<RatingComponentConfig> {
  value: number;
  onChange: (value: number) => void;
  onHover?: (value: number | null) => void;
  disabled?: boolean;
  readonly?: boolean;
}

interface RatingComponentState extends ComponentState {
  current_value: number;
  hover_value: number | null;
  is_animating: boolean;
}

/**
 * 评分组件类
 */
export class RatingComponent extends BaseQuizComponent<
  RatingComponentConfig,
  RatingComponentProps,
  RatingComponentState
> {
  private containerRef = React.createRef<HTMLDivElement>();
  private animationTimeoutId?: number;

  extractConfig(props: RatingComponentProps): RatingComponentConfig {
    return props.config;
  }

  getInitialState(): RatingComponentState {
    return {
      is_loading: false,
      is_interactive: !this.props.disabled && !this.props.readonly,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      current_value: this.props.value,
      hover_value: null,
      is_animating: false
    };
  }

  componentDidUpdate(prevProps: RatingComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.value !== this.props.value) {
      this.setState({ current_value: this.props.value });
      this.triggerFillAnimation();
    }
    
    if (prevProps.disabled !== this.props.disabled || prevProps.readonly !== this.props.readonly) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled && !this.props.readonly
      });
    }
  }

  componentWillUnmount(): void {
    if (this.animationTimeoutId) {
      clearTimeout(this.animationTimeoutId);
    }
  }

  /**
   * 处理评分点击
   */
  private handleRatingClick = (value: number): void => {
    if (this.state.is_disabled || this.props.readonly) return;

    // 支持半星评分
    if (this.config.style.allow_half) {
      // 这里可以根据点击位置计算半星
      // 简化实现，直接使用整数值
    }

    this.setState({ current_value: value });
    this.props.onChange(value);

    // 触发触觉反馈
    this.triggerHapticFeedback('light');

    // 触发填充动画
    this.triggerFillAnimation();

    // 发送交互事件
    this.emitInteractionEvent('select', {
      rating_value: value,
      marker_type: this.config.style.marker_type,
      scale_range: `${this.config.scale.min_value}-${this.config.scale.max_value}`
    });
  };

  /**
   * 处理鼠标悬停
   */
  private handleRatingHover = (value: number | null): void => {
    if (this.state.is_disabled || this.props.readonly) return;

    this.setState({ hover_value: value });
    this.props.onHover?.(value);

    if (value !== null) {
      this.triggerHoverEffect(value);
    }
  };

  /**
   * 触发填充动画
   */
  private triggerFillAnimation(): void {
    if (this.personalization.layer5_accessibility?.reduce_motion) {
      return;
    }

    const animationType = this.config.style.fill_animation;
    
    this.setState({ is_animating: true });

    switch (animationType) {
      case 'progressive':
        this.triggerProgressiveAnimation();
        break;
      case 'wave':
        this.triggerWaveAnimation();
        break;
      case 'bloom':
        this.triggerBloomAnimation();
        break;
      default:
        // instant - 无动画
        this.setState({ is_animating: false });
    }
  }

  /**
   * 渐进式填充动画
   */
  private triggerProgressiveAnimation(): void {
    const markers = this.containerRef.current?.querySelectorAll('.quiz-rating-marker');
    if (!markers) return;

    markers.forEach((marker, index) => {
      if (index < this.state.current_value) {
        setTimeout(() => {
          marker.classList.add('quiz-rating-marker-animate-fill');
        }, index * 100);
      }
    });

    this.animationTimeoutId = setTimeout(() => {
      this.setState({ is_animating: false });
      markers.forEach(marker => {
        marker.classList.remove('quiz-rating-marker-animate-fill');
      });
    }, this.state.current_value * 100 + 200) as any;
  }

  /**
   * 波浪填充动画
   */
  private triggerWaveAnimation(): void {
    const container = this.containerRef.current;
    if (!container) return;

    container.classList.add('quiz-rating-wave-animation');

    this.animationTimeoutId = setTimeout(() => {
      container.classList.remove('quiz-rating-wave-animation');
      this.setState({ is_animating: false });
    }, 600) as any;
  }

  /**
   * 绽放动画
   */
  private triggerBloomAnimation(): void {
    const markers = this.containerRef.current?.querySelectorAll('.quiz-rating-marker');
    if (!markers) return;

    markers.forEach((marker, index) => {
      if (index < this.state.current_value) {
        setTimeout(() => {
          marker.classList.add('quiz-rating-marker-bloom');
          setTimeout(() => {
            marker.classList.remove('quiz-rating-marker-bloom');
          }, 300);
        }, index * 50);
      }
    });

    this.animationTimeoutId = setTimeout(() => {
      this.setState({ is_animating: false });
    }, this.state.current_value * 50 + 300) as any;
  }

  /**
   * 触发悬停效果
   */
  private triggerHoverEffect(value: number): void {
    if (this.personalization.layer5_accessibility?.reduce_motion) {
      return;
    }

    const hoverEffect = this.config.style.hover_effect;
    const markers = this.containerRef.current?.querySelectorAll('.quiz-rating-marker');
    if (!markers) return;

    markers.forEach((marker, index) => {
      if (index < value) {
        switch (hoverEffect) {
          case 'scale':
            marker.classList.add('quiz-rating-marker-hover-scale');
            break;
          case 'glow':
            marker.classList.add('quiz-rating-marker-hover-glow');
            break;
          case 'color_change':
            marker.classList.add('quiz-rating-marker-hover-color');
            break;
          case 'bounce':
            marker.classList.add('quiz-rating-marker-hover-bounce');
            break;
        }
      } else {
        marker.classList.remove(
          'quiz-rating-marker-hover-scale',
          'quiz-rating-marker-hover-glow',
          'quiz-rating-marker-hover-color',
          'quiz-rating-marker-hover-bounce'
        );
      }
    });
  }

  /**
   * 处理键盘导航
   */
  protected handleKeyNavigation = (event: React.KeyboardEvent): void => {
    if (!this.personalization.layer5_accessibility?.keyboard_navigation || this.state.is_disabled) {
      return;
    }

    const { min_value, max_value, step } = this.config.scale;
    const currentValue = this.state.current_value;
    let newValue = currentValue;

    switch (event.key) {
      case 'ArrowLeft':
      case 'ArrowDown':
        event.preventDefault();
        newValue = Math.max(min_value, currentValue - step);
        break;
      case 'ArrowRight':
      case 'ArrowUp':
        event.preventDefault();
        newValue = Math.min(max_value, currentValue + step);
        break;
      case 'Home':
        event.preventDefault();
        newValue = min_value;
        break;
      case 'End':
        event.preventDefault();
        newValue = max_value;
        break;
    }

    if (newValue !== currentValue) {
      this.handleRatingClick(newValue);
    }
  };

  /**
   * 获取标记样式类名
   */
  private getMarkerStyleClassName(): string {
    const markerType = this.config.style.marker_type;
    return `quiz-rating-marker-${markerType}`;
  }

  /**
   * 获取尺寸样式类名
   */
  private getSizeClassName(): string {
    const size = this.config.style.size;
    return `quiz-rating-${size}`;
  }

  /**
   * 获取方向样式类名
   */
  private getOrientationClassName(): string {
    const orientation = this.config.style.orientation;
    return `quiz-rating-${orientation}`;
  }

  /**
   * 渲染评分标记
   */
  private renderRatingMarkers(): React.ReactNode {
    const { min_value, max_value } = this.config.scale;
    const markers = [];
    const displayValue = this.state.hover_value ?? this.state.current_value;

    for (let i = min_value; i <= max_value; i++) {
      const isActive = i <= displayValue;
      const isHovered = this.state.hover_value !== null && i <= this.state.hover_value;

      markers.push(
        <div
          key={i}
          className={`
            quiz-rating-marker 
            ${this.getMarkerStyleClassName()}
            ${isActive ? 'quiz-rating-marker-active' : ''}
            ${isHovered ? 'quiz-rating-marker-hovered' : ''}
          `.trim()}
          onClick={() => this.handleRatingClick(i)}
          onMouseEnter={() => this.handleRatingHover(i)}
          onMouseLeave={() => this.handleRatingHover(null)}
          role="radio"
          aria-checked={isActive}
          aria-label={`Rating ${i} of ${max_value}`}
          tabIndex={this.state.is_disabled ? -1 : 0}
        >
          {this.renderMarkerContent(i, isActive)}
        </div>
      );
    }

    return markers;
  }

  /**
   * 渲染标记内容
   */
  private renderMarkerContent(value: number, isActive: boolean): React.ReactNode {
    const markerType = this.config.style.marker_type;

    switch (markerType) {
      case 'stars':
        return <span className="quiz-rating-icon">★</span>;
      case 'hearts':
        return <span className="quiz-rating-icon">♥</span>;
      case 'diamonds':
        return <span className="quiz-rating-icon">♦</span>;
      case 'dots':
        return <span className="quiz-rating-dot" />;
      case 'lotus':
        return <span className="quiz-rating-icon quiz-rating-lotus">🪷</span>;
      case 'gourd':
        return <span className="quiz-rating-icon quiz-rating-gourd">🎃</span>;
      case 'taiji':
        return <span className="quiz-rating-icon quiz-rating-taiji">☯</span>;
      default:
        return <span className="quiz-rating-icon">★</span>;
    }
  }

  /**
   * 渲染标签
   */
  private renderLabels(): React.ReactNode {
    const { language } = this.context || { language: 'zh' };
    const { start_label, end_label } = this.config.labels;

    return (
      <div className="quiz-rating-labels">
        {start_label && (
          <span className="quiz-rating-label-start">
            {start_label[language] || start_label['zh'] || start_label['en'] || ''}
          </span>
        )}
        {end_label && (
          <span className="quiz-rating-label-end">
            {end_label[language] || end_label['zh'] || end_label['en'] || ''}
          </span>
        )}
      </div>
    );
  }

  /**
   * 渲染值显示
   */
  private renderValueDisplay(): React.ReactNode {
    if (!this.config.style.show_value) return null;

    const { value_suffix } = this.config.labels;
    const displayValue = this.state.hover_value ?? this.state.current_value;

    return (
      <div className="quiz-rating-value-display">
        {displayValue}{value_suffix || ''}
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-rating-component',
      this.getSizeClassName(),
      this.getOrientationClassName(),
      this.state.is_disabled && 'quiz-rating-disabled',
      this.props.readonly && 'quiz-rating-readonly',
      this.state.is_animating && 'quiz-rating-animating',
      this.props.className
    ].filter(Boolean).join(' ');

    const containerStyles: React.CSSProperties = {
      ...personalizedStyles,
      gap: `${this.config.style.spacing}px`,
      ...this.props.style
    };

    return (
      <div
        ref={this.containerRef}
        className={className}
        style={containerStyles}
        onKeyDown={this.handleKeyNavigation}
        {...accessibilityProps}
        role="radiogroup"
        aria-label="Rating component"
      >
        {/* 评分标记 */}
        <div className="quiz-rating-markers">
          {this.renderRatingMarkers()}
        </div>

        {/* 值显示 */}
        {this.renderValueDisplay()}

        {/* 标签 */}
        {this.renderLabels()}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'radiogroup';
  }

  protected getAriaLabel(): string {
    return `Rating: ${this.state.current_value} of ${this.config.scale.max_value}`;
  }
}

// 使用Context的函数式组件包装器
const RatingComponentWrapper: React.FC<RatingComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <RatingComponent.contextType.Provider value={{ language }}>
      <RatingComponent {...props} />
    </RatingComponent.contextType.Provider>
  );
};

// 设置Context类型
RatingComponent.contextType = React.createContext({ language: 'zh' });

export default RatingComponentWrapper;
