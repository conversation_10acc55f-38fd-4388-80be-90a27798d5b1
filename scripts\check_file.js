import fs from 'node:fs';

// 读取文件内容
const filePath = 'public/seeds/init.sql';
const buffer = fs.readFileSync(filePath);

// 检查是否有 BOM 标记
if (buffer.length >= 3 && buffer[0] === 0xef && buffer[1] === 0xbb && buffer[2] === 0xbf) {
  console.log('File has UTF-8 BOM marker');

  // 创建一个新的 Buffer，不包含 BOM 标记
  const newBuffer = buffer.slice(3);

  // 将新的 Buffer 写入文件
  fs.writeFileSync(`${filePath}.nobom`, newBuffer);
  console.log(`Created new file without BOM: ${filePath}.nobom`);
} else {
  console.log('File does not have UTF-8 BOM marker');

  // 打印前 20 个字节的十六进制表示
  console.log('First 20 bytes:');
  for (let i = 0; i < Math.min(20, buffer.length); i++) {
    console.log(`Byte ${i}: ${buffer[i].toString(16).padStart(2, '0')}`);
  }
}

// 检查文件中是否有 < 字符
const content = buffer.toString('utf8');
const lessThanIndex = content.indexOf('<');
if (lessThanIndex !== -1) {
  console.log(`Found < character at position ${lessThanIndex}`);
  console.log(
    `Context: "${content.substring(Math.max(0, lessThanIndex - 20), lessThanIndex + 20)}"`
  );
} else {
  console.log('No < character found in the file');
}

// 检查文件中是否有非 ASCII 字符
let nonAsciiFound = false;
for (let i = 0; i < content.length; i++) {
  const charCode = content.charCodeAt(i);
  if (charCode > 127) {
    console.log(`Found non-ASCII character at position ${i}: ${content[i]} (code: ${charCode})`);
    console.log(`Context: "${content.substring(Math.max(0, i - 20), i + 20)}"`);
    nonAsciiFound = true;
    break;
  }
}

if (!nonAsciiFound) {
  console.log('No non-ASCII characters found in the file');
}
