import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/LanguageContext';
import type { Language } from '@/contexts/LanguageContext';
import {
  AlertCircle,
  BarChart3,
  Check,
  Clock,
  Download,
  Home,
  Settings as SettingsIcon,
  X,
} from 'lucide-react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';

/**
 * 语言测试页面 - 用于测试语言切换功能
 */
const LanguageTest: React.FC = () => {
  const { language, setLanguage, t, isLanguageReady, forceReloadTranslations } = useLanguage();
  const [renderCount, setRenderCount] = useState(0);
  const mountTime = useRef(Date.now());
  const [testResults, setTestResults] = useState<{ key: string; value: string; source?: string }[]>(
    []
  );

  // 定义测试结果类型
  interface TranslationResult {
    key: string;
    value: string;
    source?: string;
    status: 'success' | 'warning' | 'error';
    category: string;
  }

  // 导航页面使用的翻译键
  const navKeys = [
    'app.home',
    'app.history',
    'app.analytics',
    'app.settings',
    'app.loading',
    'app.title',
  ];

  // 各页面常用翻译键
  const pageKeys = {
    home: [
      'mood.how_feeling',
      'mood.select_primary',
      'mood.select_secondary',
      'mood.select_tertiary',
      'mood.save',
      'mood.log',
      'mood.reflection',
      'mood.intensity',
      'mood.location',
      'mood.tags',
    ],
    history: [
      'history.title',
      'history.filter',
      'history.timeline',
      'history.calendar',
      'history.no_entries',
      'history.date_range',
      'history.today',
      'history.yesterday',
      'history.this_week',
      'history.this_month',
      'history.custom_range',
    ],
    analytics: [
      'analytics.title',
      'analytics.mood_trends',
      'analytics.emotion_distribution',
      'analytics.tag_analysis',
      'analytics.no_data',
      'analytics.period',
      'analytics.week',
      'analytics.month',
      'analytics.year',
      'analytics.all_time',
    ],
    settings: [
      'settings.language',
      'settings.theme',
      'settings.notifications',
      'settings.reminders',
      'settings.export',
      'settings.account',
      'settings.privacy',
      'settings.about',
      'settings.help',
      'settings.feedback',
    ],
  };

  // 错误和通用消息
  const errorKeys = [
    'error.not_found',
    'error.server_error',
    'error.network_error',
    'error.unknown_error',
    'error.try_again',
    'error.loading_failed',
  ];

  // 通用UI元素
  const commonKeys = [
    'common.save',
    'common.cancel',
    'common.delete',
    'common.edit',
    'common.confirm',
    'common.loading',
    'common.search',
    'common.filter',
    'common.sort',
    'common.yes',
    'common.no',
  ];

  // 合并所有测试键
  const testKeys = [
    ...navKeys,
    ...pageKeys.home,
    ...pageKeys.history,
    ...pageKeys.analytics,
    ...pageKeys.settings,
    ...errorKeys,
    ...commonKeys,
  ];

  // 状态变量
  const [detailedResults, setDetailedResults] = useState<TranslationResult[]>([]);
  const [summaryStats, setSummaryStats] = useState({
    total: 0,
    success: 0,
    warning: 0,
    error: 0,
    successRate: 0,
  });
  const [activeTab, setActiveTab] = useState('summary');
  const [selectedLanguage, setSelectedLanguage] = useState<Language>(language);

  // 使用useEffect监听组件渲染次数（仅用于调试）
  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, []);

  // 监听语言变化
  useEffect(() => {
    const timeSinceMount = Date.now() - mountTime.current;
    console.log(
      `[LanguageTest] Language changed to: '${language}', isReady: ${isLanguageReady}, time since mount: ${timeSinceMount}ms, render count: ${renderCount}`
    );

    // 测试所有键的翻译
    const basicResults = testKeys.map((key) => {
      const value = t(key);
      const source =
        value === key ? 'key (未找到翻译)' : value.includes('{{') ? 'template' : 'translation';
      return { key, value, source };
    });

    setTestResults(basicResults);

    // 生成详细的测试结果
    const results: TranslationResult[] = testKeys.map((key) => {
      const value = t(key);
      const source =
        value === key ? 'key (未找到翻译)' : value.includes('{{') ? 'template' : 'translation';

      // 确定分类
      let category = 'other';
      if (navKeys.includes(key)) category = 'navigation';
      else if (key.startsWith('mood.')) category = 'home';
      else if (key.startsWith('history.')) category = 'history';
      else if (key.startsWith('analytics.')) category = 'analytics';
      else if (key.startsWith('settings.')) category = 'settings';
      else if (key.startsWith('error.')) category = 'error';
      else if (key.startsWith('common.')) category = 'common';

      // 确定状态
      let status: 'success' | 'warning' | 'error' = 'success';
      if (value === key) {
        status = 'error'; // 未找到翻译
      } else if (value.includes('{{') || value.length < 2) {
        status = 'warning'; // 模板字符串或过短的翻译
      }

      return { key, value, source, status, category };
    });

    setDetailedResults(results);

    // 计算统计数据
    const total = results.length;
    const success = results.filter((r) => r.status === 'success').length;
    const warning = results.filter((r) => r.status === 'warning').length;
    const error = results.filter((r) => r.status === 'error').length;
    const successRate = Math.round((success / total) * 100);

    setSummaryStats({ total, success, warning, error, successRate });
    setSelectedLanguage(language);
  }, [language, isLanguageReady, t]);

  // 切换语言
  const handleLanguageChange = (lang: Language) => {
    console.log(`[LanguageTest] 切换语言到: ${lang}`);
    setLanguage(lang);
  };

  // 强制重新渲染
  const forceRerender = () => {
    console.log('[LanguageTest] 强制重新渲染');
    setRenderCount((prev) => prev + 1);
  };

  // 导出翻译报告
  const exportReport = () => {
    // 创建报告内容
    const reportDate = new Date().toISOString().split('T')[0];
    const reportTitle = `Translation Report - ${selectedLanguage.toUpperCase()} - ${reportDate}`;

    let reportContent = `# ${reportTitle}\n\n`;
    reportContent += '## Summary\n\n';
    reportContent += `- **Total Keys**: ${summaryStats.total}\n`;
    reportContent += `- **Success**: ${summaryStats.success} (${summaryStats.successRate}%)\n`;
    reportContent += `- **Warnings**: ${summaryStats.warning}\n`;
    reportContent += `- **Errors**: ${summaryStats.error}\n\n`;

    reportContent += '## Detailed Results\n\n';

    // 按类别分组
    const categories = [
      'navigation',
      'home',
      'history',
      'analytics',
      'settings',
      'error',
      'common',
      'other',
    ];

    categories.forEach((category) => {
      const categoryResults = detailedResults.filter((r) => r.category === category);
      if (categoryResults.length === 0) return;

      reportContent += `### ${category.charAt(0).toUpperCase() + category.slice(1)}\n\n`;
      reportContent += '| Key | Translation | Status |\n';
      reportContent += '| --- | --- | --- |\n';

      categoryResults.forEach((result) => {
        const statusEmoji =
          result.status === 'success' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
        reportContent += `| \`${result.key}\` | ${result.value} | ${statusEmoji} |\n`;
      });

      reportContent += '\n';
    });

    // 创建并下载文件
    const blob = new Blob([reportContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `translation-report-${selectedLanguage}-${reportDate}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 获取特定类别的结果
  const getCategoryResults = (category: string) => {
    return detailedResults.filter((r) => r.category === category);
  };

  // 获取特定状态的结果
  const getStatusResults = (status: 'success' | 'warning' | 'error') => {
    return detailedResults.filter((r) => r.status === status);
  };

  return (
    <div className="p-4 space-y-6 max-w-6xl mx-auto">
      <Card className="p-4">
        <h1 className="text-2xl font-bold mb-4">语言测试页面</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <p>
              <strong>当前语言:</strong> {language}
            </p>
            <p>
              <strong>语言就绪状态:</strong> {isLanguageReady ? '已就绪' : '未就绪'}
            </p>
            <p>
              <strong>渲染次数:</strong> {renderCount}
            </p>
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">翻译完成率:</p>
              <div className="flex items-center gap-2 mt-1">
                <Progress value={summaryStats.successRate} className="h-2 flex-1" />
                <span className="text-sm font-medium">{summaryStats.successRate}%</span>
              </div>
            </div>
          </div>
          <div className="flex flex-col space-y-2">
            <Button
              onClick={() => handleLanguageChange('en')}
              variant={language === 'en' ? 'default' : 'outline'}
            >
              切换到英文
            </Button>
            <Button
              onClick={() => handleLanguageChange('zh')}
              variant={language === 'zh' ? 'default' : 'outline'}
            >
              切换到中文
            </Button>
            <Button variant="outline" onClick={forceRerender}>
              强制重新渲染
            </Button>
            <Button
              variant="outline"
              onClick={() => forceReloadTranslations?.()}
              className="flex items-center gap-2"
            >
              <AlertCircle size={16} />
              强制重新加载翻译
            </Button>
            <Button variant="outline" onClick={exportReport} className="flex items-center gap-2">
              <Download size={16} />
              导出翻译报告
            </Button>
          </div>
        </div>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="summary">摘要</TabsTrigger>
          <TabsTrigger value="navigation">导航</TabsTrigger>
          <TabsTrigger value="pages">页面</TabsTrigger>
          <TabsTrigger value="all">所有翻译</TabsTrigger>
        </TabsList>

        {/* 摘要选项卡 */}
        <TabsContent value="summary" className="space-y-4">
          <Card className="p-4">
            <h2 className="text-xl font-bold mb-4">翻译状态摘要</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="p-4 border rounded-lg bg-muted/50">
                <p className="text-sm text-muted-foreground">总计</p>
                <p className="text-2xl font-bold">{summaryStats.total}</p>
                <p className="text-sm">翻译键</p>
              </div>
              <div className="p-4 border rounded-lg bg-green-50">
                <p className="text-sm text-muted-foreground">成功</p>
                <p className="text-2xl font-bold text-green-600">{summaryStats.success}</p>
                <p className="text-sm">({summaryStats.successRate}%)</p>
              </div>
              <div className="p-4 border rounded-lg bg-yellow-50">
                <p className="text-sm text-muted-foreground">警告</p>
                <p className="text-2xl font-bold text-yellow-600">{summaryStats.warning}</p>
                <p className="text-sm">需要检查</p>
              </div>
              <div className="p-4 border rounded-lg bg-red-50">
                <p className="text-sm text-muted-foreground">错误</p>
                <p className="text-2xl font-bold text-red-600">{summaryStats.error}</p>
                <p className="text-sm">缺失翻译</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <h2 className="text-xl font-bold mb-4">按类别统计</h2>
            <div className="space-y-4">
              {['navigation', 'home', 'history', 'analytics', 'settings', 'error', 'common'].map(
                (category) => {
                  const results = getCategoryResults(category);
                  if (results.length === 0) return null;

                  const categorySuccess = results.filter((r) => r.status === 'success').length;
                  const categorySuccessRate = Math.round((categorySuccess / results.length) * 100);

                  return (
                    <div key={category} className="border rounded-lg p-3">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-semibold capitalize">{category}</h3>
                        <Badge
                          variant={
                            categorySuccessRate === 100
                              ? 'default'
                              : categorySuccessRate >= 70
                                ? 'outline'
                                : 'destructive'
                          }
                        >
                          {categorySuccessRate}%
                        </Badge>
                      </div>
                      <Progress value={categorySuccessRate} className="h-2" />
                      <div className="flex justify-between text-xs text-muted-foreground mt-1">
                        <span>总计: {results.length}</span>
                        <span>成功: {categorySuccess}</span>
                      </div>
                    </div>
                  );
                }
              )}
            </div>
          </Card>

          {summaryStats.error > 0 && (
            <Card className="p-4 border-red-200">
              <div className="flex items-center gap-2 text-red-600 mb-4">
                <AlertCircle size={20} />
                <h2 className="text-xl font-bold">缺失翻译</h2>
              </div>
              <div className="space-y-2">
                {getStatusResults('error').map((result, index) => (
                  <div key={index} className="p-2 border border-red-100 rounded bg-red-50">
                    <p>
                      <strong>键:</strong>{' '}
                      <code className="bg-red-100 px-1 rounded">{result.key}</code>
                    </p>
                    <p className="text-xs text-muted-foreground">类别: {result.category}</p>
                  </div>
                ))}
              </div>
            </Card>
          )}
        </TabsContent>

        {/* 导航选项卡 */}
        <TabsContent value="navigation">
          <Card className="p-4">
            <h2 className="text-xl font-bold mb-4">导航项目测试</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <Link
                to="/"
                className="p-3 border rounded text-center flex flex-col items-center gap-2 hover:bg-muted"
              >
                <Home size={20} />
                <p>{isLanguageReady ? t('app.home') : 'Home'}</p>
              </Link>
              <Link
                to="/history"
                className="p-3 border rounded text-center flex flex-col items-center gap-2 hover:bg-muted"
              >
                <Clock size={20} />
                <p>{isLanguageReady ? t('app.history') : 'History'}</p>
              </Link>
              <Link
                to="/analytics"
                className="p-3 border rounded text-center flex flex-col items-center gap-2 hover:bg-muted"
              >
                <BarChart3 size={20} />
                <p>{isLanguageReady ? t('app.analytics') : 'Analytics'}</p>
              </Link>
              <Link
                to="/settings"
                className="p-3 border rounded text-center flex flex-col items-center gap-2 hover:bg-muted"
              >
                <SettingsIcon size={20} />
                <p>{isLanguageReady ? t('app.settings') : 'Settings'}</p>
              </Link>
            </div>
            <p className="text-sm text-muted-foreground">点击上方导航项可以跳转到对应页面</p>
          </Card>

          <Card className="p-4 mt-4">
            <h2 className="text-xl font-bold mb-4">导航翻译键</h2>
            <div className="space-y-2">
              {navKeys.map((key) => {
                const result = detailedResults.find((r) => r.key === key);
                if (!result) return null;

                return (
                  <div
                    key={key}
                    className={`p-3 border rounded ${
                      result.status === 'error'
                        ? 'border-red-200 bg-red-50'
                        : result.status === 'warning'
                          ? 'border-yellow-200 bg-yellow-50'
                          : 'border-green-200 bg-green-50'
                    }`}
                  >
                    <div className="flex justify-between">
                      <p>
                        <strong>键:</strong>{' '}
                        <code className="bg-white px-1 rounded">{result.key}</code>
                      </p>
                      <Badge
                        variant={
                          result.status === 'error'
                            ? 'destructive'
                            : result.status === 'warning'
                              ? 'outline'
                              : 'default'
                        }
                      >
                        {result.status === 'error'
                          ? '缺失'
                          : result.status === 'warning'
                            ? '警告'
                            : '正常'}
                      </Badge>
                    </div>
                    <p className="mt-2">
                      <strong>翻译:</strong> {result.value}
                    </p>
                  </div>
                );
              })}
            </div>
          </Card>
        </TabsContent>

        {/* 页面选项卡 */}
        <TabsContent value="pages">
          <Card className="p-4">
            <h2 className="text-xl font-bold mb-4">页面翻译键</h2>
            <Accordion type="single" collapsible className="w-full">
              {Object.entries(pageKeys).map(([page, keys]) => {
                const pageResults = keys
                  .map((key) => detailedResults.find((r) => r.key === key))
                  .filter(Boolean) as TranslationResult[];

                const successCount = pageResults.filter((r) => r.status === 'success').length;
                const successRate = Math.round((successCount / pageResults.length) * 100);

                return (
                  <AccordionItem key={page} value={page}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex items-center justify-between w-full pr-4">
                        <span className="font-semibold capitalize">{page} 页面</span>
                        <div className="flex items-center gap-2">
                          <Progress value={successRate} className="w-24 h-2" />
                          <Badge
                            variant={
                              successRate === 100
                                ? 'default'
                                : successRate >= 70
                                  ? 'outline'
                                  : 'destructive'
                            }
                          >
                            {successRate}%
                          </Badge>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                        {pageResults.map((result, idx) => (
                          <div
                            key={idx}
                            className={`p-2 border rounded text-sm ${
                              result.status === 'error'
                                ? 'border-red-200 bg-red-50'
                                : result.status === 'warning'
                                  ? 'border-yellow-200 bg-yellow-50'
                                  : 'border-green-200 bg-green-50'
                            }`}
                          >
                            <div className="flex justify-between">
                              <p>
                                <strong>键:</strong>{' '}
                                <code className="bg-white px-1 rounded">{result.key}</code>
                              </p>
                              {result.status === 'success' ? (
                                <Check size={16} className="text-green-500" />
                              ) : result.status === 'warning' ? (
                                <AlertCircle size={16} className="text-yellow-500" />
                              ) : (
                                <X size={16} className="text-red-500" />
                              )}
                            </div>
                            <p>
                              <strong>值:</strong> {result.value}
                            </p>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </Card>
        </TabsContent>

        {/* 所有翻译选项卡 */}
        <TabsContent value="all">
          <Card className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">所有翻译测试结果</h2>
              <div className="flex gap-2">
                <Badge
                  variant="outline"
                  className="cursor-pointer"
                  onClick={() => setActiveTab('summary')}
                >
                  总计: {summaryStats.total}
                </Badge>
                <Badge variant="default" className="bg-green-600 cursor-pointer">
                  成功: {summaryStats.success}
                </Badge>
                <Badge
                  variant="outline"
                  className="text-yellow-600 border-yellow-600 cursor-pointer"
                >
                  警告: {summaryStats.warning}
                </Badge>
                <Badge variant="destructive" className="cursor-pointer">
                  错误: {summaryStats.error}
                </Badge>
              </div>
            </div>
            <div className="space-y-2 max-h-[600px] overflow-y-auto pr-2">
              {detailedResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-2 border rounded ${
                    result.status === 'error'
                      ? 'border-red-200 bg-red-50'
                      : result.status === 'warning'
                        ? 'border-yellow-200 bg-yellow-50'
                        : 'border-green-200 bg-green-50'
                  }`}
                >
                  <div className="flex justify-between">
                    <p>
                      <strong>键:</strong>{' '}
                      <code className="bg-white px-1 rounded">{result.key}</code>
                    </p>
                    <Badge
                      variant={
                        result.status === 'error'
                          ? 'destructive'
                          : result.status === 'warning'
                            ? 'outline'
                            : 'default'
                      }
                    >
                      {result.category}
                    </Badge>
                  </div>
                  <p>
                    <strong>值:</strong> {result.value}
                  </p>
                  {result.source && (
                    <p className="text-xs text-muted-foreground">
                      <strong>来源:</strong> {result.source}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LanguageTest;
