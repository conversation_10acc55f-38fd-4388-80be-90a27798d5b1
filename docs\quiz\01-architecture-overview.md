# Quiz系统架构总览

## 🎯 系统愿景

构建一个通用的、后端驱动的Quiz逻辑与评估平台，特别针对情绪数据集作为量表的新架构，提供专业级的心理评估和情绪分析能力。

## 🏗️ 整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    Frontend Application Layer                   │
│              (React + TypeScript + ViewFactory)                │
└─────────────────────────┬───────────────────────────────────────┘
                          │ tRPC + Zod API Calls
┌─────────────────────────┴───────────────────────────────────────┐
│                    CORE QUIZ ENGINE                             │
│                                                                 │
│ ┌─────────────────────┐  ┌─────────────────────────────────────┐ │
│ │   tRPC Router       │  │   Personalization Engine           │ │
│ │   + Zod Validation  │  │   (6-Layer Config Integration)     │ │
│ └─────────────────────┘  └─────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────┐  ┌─────────────────────────────────────┐ │
│ │   Session           │  │   Question & Pack Management       │ │
│ │   Management        │  │   (Emotion Dataset as Quiz)        │ │
│ └─────────────────────┘  └─────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────┐  ┌─────────────────────────────────────┐ │
│ │   Presentation      │  │   Dynamic UI Rule Engine           │ │
│ │   Logic Controller  │  │   (Context-aware UI Generation)    │ │
│ └─────────────────────┘  └─────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────┐  ┌─────────────────────────────────────┐ │
│ │   Answer Processing │  │   Evaluation Engine                 │ │
│ │   & Validation      │  │   (Emotion Pattern Analysis)       │ │
│ └─────────────────────┘  └─────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │              Data Layer (SQLite + TypeORM)                 │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🎨 核心设计原则

### 1. 数据与展现完全分离
- **数据层**: 纯粹的量表内容、逻辑、评估规则
- **展现层**: 用户个性化配置、UI样式、交互方式
- **会话层**: 运行时的个性化展现配置快照

### 2. 6层个性化配置架构
```typescript
interface PersonalizationLayers {
  layer0_dataset: EmotionDatasetConfig;      // 情绪数据集选择
  layer1_userChoice: UserChoiceConfig;       // 基础用户选择
  layer2_rendering: RenderingStrategyConfig; // 渲染策略
  layer3_skinBase: SkinBaseConfig;           // 皮肤基础配置
  layer4_viewDetail: ViewDetailConfig;       // 视图细节配置
  layer5_accessibility: AccessibilityConfig; // 可访问性增强
}
```

### 3. 情绪数据集作为量表映射
```typescript
// 概念映射
EmotionDataSet → QuizPack     // 数据集 → 量表包
Tier → Question               // 层级 → 问题
Emotion → Option              // 情绪 → 选项
SelectionPath → AnswerRecord  // 选择路径 → 答案记录
```

### 4. 模块化与可插拔设计
- **评估策略**: 可插拔的评估算法
- **渲染引擎**: 可切换的UI渲染方式
- **存储适配器**: 可替换的数据存储方案
- **分析引擎**: 可扩展的情绪分析算法

## 🔄 核心工作流程

### 1. 会话初始化流程
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant Q as Quiz Engine
    participant P as Personalization
    participant D as Database

    U->>F: 启动量表
    F->>Q: createQuizSession(packId, userId)
    Q->>P: loadPersonalizationConfig(userId)
    P->>D: 查询用户配置
    D-->>P: 返回配置数据
    P-->>Q: 返回合并配置
    Q->>D: 创建会话记录
    Q-->>F: 返回会话ID
    F-->>U: 显示量表开始界面
```

### 2. 问题呈现流程
```mermaid
sequenceDiagram
    participant F as Frontend
    participant Q as Quiz Engine
    participant P as Presentation
    participant D as Dynamic UI
    participant V as ViewFactory

    F->>Q: getNextQuestion(sessionId)
    Q->>P: generateQuestionPresentation()
    P->>D: applyDynamicRules()
    D-->>P: 返回调整后配置
    P-->>Q: 返回问题呈现数据
    Q-->>F: QuestionPresentationData
    F->>V: 渲染个性化UI
    V-->>F: 渲染完成
```

### 3. 答案处理流程
```mermaid
sequenceDiagram
    participant F as Frontend
    participant Q as Quiz Engine
    participant A as Answer Processor
    participant E as Evaluation
    participant D as Database

    F->>Q: submitAnswer(sessionId, answer)
    Q->>A: validateAnswer()
    A-->>Q: 验证结果
    Q->>E: performRealtimeAnalysis()
    E-->>Q: 实时洞察
    Q->>D: 保存答案记录
    Q-->>F: AnswerSubmissionResult
```

## 📊 技术栈选择

### 后端技术栈
- **Runtime**: Node.js + TypeScript
- **API Framework**: tRPC (类型安全的API)
- **Validation**: Zod (运行时类型验证)
- **Database**: SQLite + TypeORM
- **Authentication**: Better-Auth
- **Testing**: Vitest + Supertest

### 前端技术栈
- **Framework**: React + TypeScript
- **State Management**: Zustand
- **UI Components**: Radix UI + Tailwind CSS
- **API Client**: tRPC Client
- **Testing**: Vitest + Testing Library
- **Build Tool**: Vite

### 数据层技术
- **Database**: SQLite (开发) / PostgreSQL (生产)
- **ORM**: TypeORM
- **Migration**: TypeORM Migrations
- **Seeding**: Custom Seed Scripts
- **Backup**: Automated Backup Scripts

## 🎯 核心模块详解

### 1. Personalization Engine (个性化引擎)
```typescript
class PersonalizationEngine {
  // 生成个性化的问题呈现数据
  async generatePersonalizedQuestionPresentation(
    questionId: string,
    userId: string,
    sessionContext: SessionContext
  ): Promise<QuestionPresentationData>;

  // 加载并合并6层配置
  private async loadLayeredConfiguration(
    userId: string,
    context: SessionContext
  ): Promise<LayeredConfig>;
}
```

### 2. Dynamic UI Rule Engine (动态UI规则引擎)
```typescript
class DynamicUIRuleEngine {
  // 基于上下文动态调整UI
  async evaluateUIRules(
    baseUIConfig: UIConfiguration,
    userContext: UserContext,
    sessionContext: SessionContext
  ): Promise<UIConfiguration>;
}
```

### 3. Emotion Evaluation Engine (情绪评估引擎)
```typescript
class EmotionEvaluationEngine {
  // 生成综合情绪分析
  async generateComprehensiveEmotionAnalysis(
    emotionSelections: EmotionSelection[],
    personalizationConfig: PersonalizationConfig
  ): Promise<ComprehensiveEmotionAnalysis>;

  // 生成个性化推荐
  async generatePersonalizedRecommendations(
    analysis: EmotionAnalysis,
    userId: string
  ): Promise<PersonalizedRecommendation[]>;
}
```

### 4. Session Management (会话管理)
```typescript
class QuizSessionManager {
  // 创建个性化会话
  async createSession(
    packId: string,
    userId: string,
    configOverrides?: Partial<PersonalizationConfig>
  ): Promise<QuizSession>;

  // 更新会话进度
  async updateProgress(
    sessionId: string,
    progress: ProgressUpdate
  ): Promise<void>;
}
```

## 🔐 安全性设计

### 1. 数据访问控制
- **用户隔离**: 严格的用户数据隔离
- **权限验证**: 基于角色的访问控制
- **数据脱敏**: 敏感数据的自动脱敏
- **审计日志**: 完整的操作审计记录

### 2. API安全
- **类型验证**: Zod运行时类型验证
- **输入清理**: 自动的输入数据清理
- **速率限制**: API调用频率限制
- **CORS配置**: 严格的跨域访问控制

### 3. 数据保护
- **加密存储**: 敏感数据加密存储
- **传输加密**: HTTPS/WSS加密传输
- **数据备份**: 自动化数据备份
- **隐私保护**: GDPR合规的隐私保护

## 📈 性能优化

### 1. 缓存策略
- **配置缓存**: 用户配置的智能缓存
- **会话缓存**: 活跃会话的内存缓存
- **结果缓存**: 分析结果的持久化缓存
- **静态资源**: CDN静态资源缓存

### 2. 数据库优化
- **索引优化**: 关键查询的索引优化
- **查询优化**: SQL查询性能优化
- **连接池**: 数据库连接池管理
- **分页查询**: 大数据集的分页处理

### 3. 前端优化
- **代码分割**: 按需加载的代码分割
- **组件缓存**: React组件的智能缓存
- **状态优化**: Zustand状态管理优化
- **渲染优化**: ViewFactory渲染性能优化

## 🚀 扩展性设计

### 1. 水平扩展
- **微服务架构**: 可拆分的微服务设计
- **负载均衡**: 多实例负载均衡
- **数据分片**: 大规模数据分片策略
- **缓存集群**: 分布式缓存集群

### 2. 功能扩展
- **插件系统**: 可插拔的功能插件
- **主题系统**: 可扩展的主题系统
- **语言支持**: 多语言国际化支持
- **平台适配**: 多平台适配能力

### 3. 集成扩展
- **第三方集成**: 开放的第三方集成API
- **Webhook支持**: 事件驱动的Webhook
- **数据导出**: 多格式数据导出
- **分析集成**: 第三方分析工具集成

这个架构设计为构建专业级的、高度个性化的情绪评估和分析系统提供了坚实的技术基础。
