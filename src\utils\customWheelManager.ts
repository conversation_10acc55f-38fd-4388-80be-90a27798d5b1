import { v4 as uuidv4 } from 'uuid';
import type { CustomEmotion, CustomTier, CustomWheel } from '../types/emotionTypes';

/**
 * @deprecated 此管理器可能已废弃，与旧的轮盘实现相关联。请确认是否有替代管理器。
 *
 * 自定义轮盘管理器
 * 负责管理用户创建的自定义情绪轮盘
 */
export class CustomWheelManager {
  private customWheels: CustomWheel[] = [];
  private activeWheelId: string | null = null;

  constructor() {
    this.loadCustomWheels();
    this.setActiveWheel();
  }

  /**
   * 获取所有自定义轮盘
   */
  getCustomWheels(): CustomWheel[] {
    return this.customWheels;
  }

  /**
   * 获取当前活动的自定义轮盘
   */
  getActiveWheel(): CustomWheel | null {
    if (!this.activeWheelId) {
      return null;
    }

    return this.customWheels.find((wheel) => wheel.id === this.activeWheelId) || null;
  }

  /**
   * 设置活动轮盘
   * @param wheelId 轮盘ID
   * @returns 是否成功设置
   */
  setActiveWheel(wheelId?: string): boolean {
    if (!wheelId) {
      // 如果没有指定ID，尝试加载上次使用的轮盘
      const savedActiveWheel = localStorage.getItem('active-custom-wheel');

      if (savedActiveWheel && this.customWheels.some((wheel) => wheel.id === savedActiveWheel)) {
        this.activeWheelId = savedActiveWheel;
        return true;
      }

      // 如果没有保存的活动轮盘或者保存的轮盘不存在，使用第一个自定义轮盘
      if (this.customWheels.length > 0) {
        this.activeWheelId = this.customWheels[0].id;
        this.saveActiveWheel();
        return true;
      }

      return false;
    }

    // 检查指定的轮盘是否存在
    if (!this.customWheels.some((wheel) => wheel.id === wheelId)) {
      return false;
    }

    this.activeWheelId = wheelId;
    this.saveActiveWheel();
    return true;
  }

  /**
   * 创建新的自定义轮盘
   * @param name 轮盘名称
   * @param description 轮盘描述
   * @param skinId 皮肤ID
   * @returns 新创建的轮盘
   */
  createCustomWheel(name: string, description: string, skinId: string): CustomWheel {
    const newWheel: CustomWheel = {
      id: uuidv4(),
      name,
      description,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      tiers: [],
      skinId,
      isActive: false,
      isDefault: false,
    };

    this.customWheels.push(newWheel);
    this.saveCustomWheels();

    return newWheel;
  }

  /**
   * 更新自定义轮盘
   * @param wheelId 轮盘ID
   * @param updates 更新内容
   * @returns 是否成功更新
   */
  updateCustomWheel(wheelId: string, updates: Partial<CustomWheel>): boolean {
    const wheelIndex = this.customWheels.findIndex((wheel) => wheel.id === wheelId);

    if (wheelIndex === -1) {
      return false;
    }

    // 不允许更新ID、创建时间和默认状态
    const { id, created_at, isDefault, ...allowedUpdates } = updates;

    this.customWheels[wheelIndex] = {
      ...this.customWheels[wheelIndex],
      ...allowedUpdates,
      updated_at: new Date().toISOString(),
    };

    this.saveCustomWheels();
    return true;
  }

  /**
   * 删除自定义轮盘
   * @param wheelId 轮盘ID
   * @returns 是否成功删除
   */
  deleteCustomWheel(wheelId: string): boolean {
    // 不允许删除默认轮盘
    const wheel = this.customWheels.find((w) => w.id === wheelId);

    if (!wheel || wheel.is_default) {
      return false;
    }

    this.customWheels = this.customWheels.filter((wheel) => wheel.id !== wheelId);

    // 如果删除的是当前活动的轮盘，重新设置活动轮盘
    if (this.activeWheelId === wheelId) {
      this.setActiveWheel();
    }

    this.saveCustomWheels();
    return true;
  }

  /**
   * 添加自定义层级
   * @param wheelId 轮盘ID
   * @param name 层级名称
   * @param level 层级级别
   * @param parentTierId 父层级ID
   * @returns 新创建的层级，如果失败则返回null
   */
  addCustomTier(
    wheelId: string,
    name: string,
    level: number,
    parentTierId?: string
  ): CustomTier | null {
    const wheelIndex = this.customWheels.findIndex((wheel) => wheel.id === wheelId);

    if (wheelIndex === -1) {
      return null;
    }

    const newTier: CustomTier = {
      id: uuidv4(),
      name,
      level,
      emotions: [],
      parentTierId,
    };

    this.customWheels[wheelIndex].tiers.push(newTier);
    this.customWheels[wheelIndex].updated_at = new Date().toISOString();

    this.saveCustomWheels();
    return newTier;
  }

  /**
   * 更新自定义层级
   * @param wheelId 轮盘ID
   * @param tierId 层级ID
   * @param updates 更新内容
   * @returns 是否成功更新
   */
  updateCustomTier(wheelId: string, tierId: string, updates: Partial<CustomTier>): boolean {
    const wheelIndex = this.customWheels.findIndex((wheel) => wheel.id === wheelId);

    if (wheelIndex === -1) {
      return false;
    }

    const tierIndex = this.customWheels[wheelIndex].tiers.findIndex((tier) => tier.id === tierId);

    if (tierIndex === -1) {
      return false;
    }

    // 不允许更新ID
    const { id, ...allowedUpdates } = updates;

    this.customWheels[wheelIndex].tiers[tierIndex] = {
      ...this.customWheels[wheelIndex].tiers[tierIndex],
      ...allowedUpdates,
    };

    this.customWheels[wheelIndex].updated_at = new Date().toISOString();

    this.saveCustomWheels();
    return true;
  }

  /**
   * 删除自定义层级
   * @param wheelId 轮盘ID
   * @param tierId 层级ID
   * @returns 是否成功删除
   */
  deleteCustomTier(wheelId: string, tierId: string): boolean {
    const wheelIndex = this.customWheels.findIndex((wheel) => wheel.id === wheelId);

    if (wheelIndex === -1) {
      return false;
    }

    // 检查是否有子层级依赖于此层级
    const hasChildTiers = this.customWheels[wheelIndex].tiers.some(
      (tier) => tier.parent_tier_id === tierId
    );

    if (hasChildTiers) {
      return false;
    }

    this.customWheels[wheelIndex].tiers = this.customWheels[wheelIndex].tiers.filter(
      (tier) => tier.id !== tierId
    );
    this.customWheels[wheelIndex].updated_at = new Date().toISOString();

    this.saveCustomWheels();
    return true;
  }

  /**
   * 添加自定义情绪
   * @param wheelId 轮盘ID
   * @param tierId 层级ID
   * @param name 情绪名称
   * @param emoji 情绪表情
   * @param color 情绪颜色
   * @returns 新创建的情绪，如果失败则返回null
   */
  addCustomEmotion(
    wheelId: string,
    tierId: string,
    name: string,
    emoji: string,
    color?: string
  ): CustomEmotion | null {
    const wheelIndex = this.customWheels.findIndex((wheel) => wheel.id === wheelId);

    if (wheelIndex === -1) {
      return null;
    }

    const tierIndex = this.customWheels[wheelIndex].tiers.findIndex((tier) => tier.id === tierId);

    if (tierIndex === -1) {
      return null;
    }

    const newEmotion: CustomEmotion = {
      id: uuidv4(),
      name,
      emoji,
      color,
    };

    this.customWheels[wheelIndex].tiers[tierIndex].emotions.push(newEmotion);
    this.customWheels[wheelIndex].updated_at = new Date().toISOString();

    this.saveCustomWheels();
    return newEmotion;
  }

  /**
   * 更新自定义情绪
   * @param wheelId 轮盘ID
   * @param tierId 层级ID
   * @param emotionId 情绪ID
   * @param updates 更新内容
   * @returns 是否成功更新
   */
  updateCustomEmotion(
    wheelId: string,
    tierId: string,
    emotionId: string,
    updates: Partial<CustomEmotion>
  ): boolean {
    const wheelIndex = this.customWheels.findIndex((wheel) => wheel.id === wheelId);

    if (wheelIndex === -1) {
      return false;
    }

    const tierIndex = this.customWheels[wheelIndex].tiers.findIndex((tier) => tier.id === tierId);

    if (tierIndex === -1) {
      return false;
    }

    const emotionIndex = this.customWheels[wheelIndex].tiers[tierIndex].emotions.findIndex(
      (emotion) => emotion.id === emotionId
    );

    if (emotionIndex === -1) {
      return false;
    }

    // 不允许更新ID
    const { id, ...allowedUpdates } = updates;

    this.customWheels[wheelIndex].tiers[tierIndex].emotions[emotionIndex] = {
      ...this.customWheels[wheelIndex].tiers[tierIndex].emotions[emotionIndex],
      ...allowedUpdates,
    };

    this.customWheels[wheelIndex].updated_at = new Date().toISOString();

    this.saveCustomWheels();
    return true;
  }

  /**
   * 删除自定义情绪
   * @param wheelId 轮盘ID
   * @param tierId 层级ID
   * @param emotionId 情绪ID
   * @returns 是否成功删除
   */
  deleteCustomEmotion(wheelId: string, tierId: string, emotionId: string): boolean {
    const wheelIndex = this.customWheels.findIndex((wheel) => wheel.id === wheelId);

    if (wheelIndex === -1) {
      return false;
    }

    const tierIndex = this.customWheels[wheelIndex].tiers.findIndex((tier) => tier.id === tierId);

    if (tierIndex === -1) {
      return false;
    }

    this.customWheels[wheelIndex].tiers[tierIndex].emotions = this.customWheels[wheelIndex].tiers[
      tierIndex
    ].emotions.filter((emotion) => emotion.id !== emotionId);
    this.customWheels[wheelIndex].updated_at = new Date().toISOString();

    this.saveCustomWheels();
    return true;
  }

  /**
   * 导入自定义轮盘
   * @param wheelData 轮盘数据
   * @returns 导入的轮盘，如果失败则返回null
   */
  importCustomWheel(wheelData: any): CustomWheel | null {
    try {
      // 验证轮盘数据
      if (!wheelData.name || !Array.isArray(wheelData.tiers)) {
        return null;
      }

      // 创建新的轮盘ID
      const newWheel: CustomWheel = {
        ...wheelData,
        id: uuidv4(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        isActive: false,
        isDefault: false,
      };

      this.customWheels.push(newWheel);
      this.saveCustomWheels();

      return newWheel;
    } catch (error) {
      console.error('Failed to import custom wheel:', error);
      return null;
    }
  }

  /**
   * 导出自定义轮盘
   * @param wheelId 轮盘ID
   * @returns 轮盘数据，如果失败则返回null
   */
  exportCustomWheel(wheelId: string): any | null {
    const wheel = this.customWheels.find((wheel) => wheel.id === wheelId);

    if (!wheel) {
      return null;
    }

    // 创建一个副本，移除不需要导出的字段
    const { isActive, isDefault, ...exportData } = wheel;

    return exportData;
  }

  /**
   * 私有方法：加载自定义轮盘
   */
  private loadCustomWheels(): void {
    const savedWheels = localStorage.getItem('custom-wheels');

    if (savedWheels) {
      try {
        const parsedWheels = JSON.parse(savedWheels) as CustomWheel[];

        // 保持时间戳为字符串格式
        this.customWheels = parsedWheels.map((wheel) => ({
          ...wheel,
          created_at: wheel.created_at,
          updated_at: wheel.updated_at,
        }));
      } catch (error) {
        console.error('Failed to load custom wheels:', error);
        this.customWheels = [];
      }
    }

    // 如果没有自定义轮盘，创建一个默认的
    if (this.customWheels.length === 0) {
      this.createDefaultWheel();
    }
  }

  /**
   * 私有方法：保存自定义轮盘
   */
  private saveCustomWheels(): void {
    localStorage.setItem('custom-wheels', JSON.stringify(this.customWheels));
  }

  /**
   * 私有方法：保存活动轮盘
   */
  private saveActiveWheel(): void {
    if (this.activeWheelId) {
      localStorage.setItem('active-custom-wheel', this.activeWheelId);
    }
  }

  /**
   * 私有方法：创建默认轮盘
   */
  private createDefaultWheel(): void {
    const defaultWheel: CustomWheel = {
      id: uuidv4(),
      name: '默认情绪轮盘',
      description: '系统默认的情绪轮盘',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      tiers: [],
      skinId: 'classic',
      isActive: true,
      isDefault: true,
    };

    // 添加默认层级
    const primaryTier: CustomTier = {
      id: uuidv4(),
      name: '基础情绪',
      level: 1,
      emotions: [
        { id: uuidv4(), name: '快乐', emoji: '😊', color: '#FFD700' },
        { id: uuidv4(), name: '悲伤', emoji: '😢', color: '#4169E1' },
        { id: uuidv4(), name: '愤怒', emoji: '😠', color: '#FF4500' },
        { id: uuidv4(), name: '恐惧', emoji: '😨', color: '#800080' },
      ],
    };

    defaultWheel.tiers.push(primaryTier);

    this.customWheels.push(defaultWheel);
    this.activeWheelId = defaultWheel.id;

    this.saveCustomWheels();
    this.saveActiveWheel();
  }
}
