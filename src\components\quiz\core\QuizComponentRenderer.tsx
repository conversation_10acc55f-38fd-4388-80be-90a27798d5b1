/**
 * Quiz组件渲染器
 * 基于组件类型和配置动态渲染不同的Quiz组件
 */

import React from 'react';
import { QuizQuestionOption } from '@/types/schema/base';

// 支持的组件类型
export type QuizComponentType = 
  | 'selector_component'
  | 'rating_component' 
  | 'slider_component'
  | 'text_input_component'
  | 'wheel_component'
  | 'card_component'
  | 'bubble_component';

// 基础配置接口
interface BaseQuizConfig {
  id: string;
  question_text: string;
}

// 选择器组件配置
interface SelectorConfig extends BaseQuizConfig {
  options: QuizQuestionOption[];
  multiple?: boolean;
  layout?: 'grid' | 'list' | 'horizontal';
  max_selections?: number;
}

// 评分组件配置
interface RatingConfig extends BaseQuizConfig {
  options: QuizQuestionOption[];
  scale_type?: 'likert' | 'star' | 'numeric';
  min_value?: number;
  max_value?: number;
  step_value?: number;
}

// 滑块组件配置
interface SliderConfig extends BaseQuizConfig {
  min_value: number;
  max_value: number;
  step_value?: number;
  default_value?: number;
  show_labels?: boolean;
  show_value?: boolean;
}

// 文本输入组件配置
interface TextInputConfig extends BaseQuizConfig {
  input_type?: 'text' | 'textarea' | 'number';
  placeholder?: string;
  max_length?: number;
  required?: boolean;
}

// 轮盘组件配置
interface WheelConfig extends BaseQuizConfig {
  options: QuizQuestionOption[];
  wheel_type?: 'emotion' | 'category' | 'custom';
  size?: number;
  center_radius?: number;
}

// 卡片组件配置
interface CardConfig extends BaseQuizConfig {
  options: QuizQuestionOption[];
  card_layout?: 'grid' | 'masonry' | 'carousel';
  columns?: number;
  card_size?: 'small' | 'medium' | 'large';
}

// 气泡组件配置
interface BubbleConfig extends BaseQuizConfig {
  options: QuizQuestionOption[];
  bubble_layout?: 'cluster' | 'flow' | 'random';
  animation?: boolean;
}

// 联合配置类型
type QuizConfig = 
  | SelectorConfig 
  | RatingConfig 
  | SliderConfig 
  | TextInputConfig
  | WheelConfig
  | CardConfig
  | BubbleConfig;

// 组件属性接口
interface QuizComponentRendererProps {
  componentType: QuizComponentType;
  config: QuizConfig;
  onSelect: (value: any) => void;
  onBack?: () => void;
  personalizationConfig?: any;
  className?: string;
}

/**
 * Quiz组件渲染器
 * 根据组件类型和配置渲染相应的Quiz组件
 */
const QuizComponentRenderer: React.FC<QuizComponentRendererProps> = ({
  componentType,
  config,
  onSelect,
  onBack,
  personalizationConfig,
  className
}) => {

  // 渲染选择器组件
  const renderSelectorComponent = (config: SelectorConfig) => {
    const { options, multiple = false, layout = 'grid', max_selections } = config;

    return (
      <div className={`quiz-selector ${layout}`}>
        <h3 className="question-text">{config.question_text}</h3>
        <div className={`options-container ${layout}`}>
          {options.map((option) => (
            <button
              key={option.id}
              className="option-item"
              onClick={() => onSelect(option)}
              data-option-id={option.id}
            >
              <span className="option-text">{option.option_text}</span>
              {option.metadata && (
                <span className="option-emoji">
                  {getEmojiFromMetadata(option.metadata)}
                </span>
              )}
            </button>
          ))}
        </div>
        {onBack && (
          <button className="back-button" onClick={onBack}>
            返回
          </button>
        )}
      </div>
    );
  };

  // 渲染评分组件
  const renderRatingComponent = (config: RatingConfig) => {
    const { options, scale_type = 'likert', min_value = 1, max_value = 5 } = config;

    return (
      <div className="quiz-rating">
        <h3 className="question-text">{config.question_text}</h3>
        <div className="rating-container">
          {Array.from({ length: max_value - min_value + 1 }, (_, index) => {
            const value = min_value + index;
            return (
              <button
                key={value}
                className="rating-item"
                onClick={() => onSelect(value)}
                data-value={value}
              >
                {scale_type === 'star' ? '⭐' : value}
              </button>
            );
          })}
        </div>
        {onBack && (
          <button className="back-button" onClick={onBack}>
            返回
          </button>
        )}
      </div>
    );
  };

  // 渲染滑块组件
  const renderSliderComponent = (config: SliderConfig) => {
    const { 
      min_value, 
      max_value, 
      step_value = 1, 
      default_value = min_value,
      show_labels = true,
      show_value = true
    } = config;

    const [currentValue, setCurrentValue] = React.useState(default_value);

    const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = Number(event.target.value);
      setCurrentValue(value);
    };

    const handleSliderCommit = () => {
      onSelect(currentValue);
    };

    return (
      <div className="quiz-slider">
        <h3 className="question-text">{config.question_text}</h3>
        <div className="slider-container">
          {show_labels && (
            <div className="slider-labels">
              <span>{min_value}</span>
              <span>{max_value}</span>
            </div>
          )}
          <input
            type="range"
            min={min_value}
            max={max_value}
            step={step_value}
            value={currentValue}
            onChange={handleSliderChange}
            className="slider-input"
          />
          {show_value && (
            <div className="slider-value">
              当前值: {currentValue}
            </div>
          )}
        </div>
        <div className="slider-actions">
          <button className="confirm-button" onClick={handleSliderCommit}>
            确认
          </button>
          {onBack && (
            <button className="back-button" onClick={onBack}>
              返回
            </button>
          )}
        </div>
      </div>
    );
  };

  // 渲染文本输入组件
  const renderTextInputComponent = (config: TextInputConfig) => {
    const { 
      input_type = 'text', 
      placeholder = '', 
      max_length,
      required = false 
    } = config;

    const [inputValue, setInputValue] = React.useState('');

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setInputValue(event.target.value);
    };

    const handleSubmit = () => {
      if (required && !inputValue.trim()) {
        alert('请输入内容');
        return;
      }
      onSelect(inputValue);
    };

    return (
      <div className="quiz-text-input">
        <h3 className="question-text">{config.question_text}</h3>
        <div className="input-container">
          {input_type === 'textarea' ? (
            <textarea
              value={inputValue}
              onChange={handleInputChange}
              placeholder={placeholder}
              maxLength={max_length}
              required={required}
              className="text-input"
            />
          ) : (
            <input
              type={input_type}
              value={inputValue}
              onChange={handleInputChange}
              placeholder={placeholder}
              maxLength={max_length}
              required={required}
              className="text-input"
            />
          )}
        </div>
        <div className="input-actions">
          <button className="submit-button" onClick={handleSubmit}>
            提交
          </button>
          {onBack && (
            <button className="back-button" onClick={onBack}>
              返回
            </button>
          )}
        </div>
      </div>
    );
  };

  // 渲染轮盘组件（简化版）
  const renderWheelComponent = (config: WheelConfig) => {
    const { options, size = 300 } = config;

    return (
      <div className="quiz-wheel">
        <h3 className="question-text">{config.question_text}</h3>
        <div 
          className="wheel-container"
          style={{ width: size, height: size }}
        >
          {/* 这里可以集成实际的轮盘组件 */}
          <div className="wheel-placeholder">
            <p>轮盘组件 (待集成)</p>
            <div className="wheel-options">
              {options.map((option) => (
                <button
                  key={option.id}
                  className="wheel-option"
                  onClick={() => onSelect(option)}
                >
                  {option.option_text}
                </button>
              ))}
            </div>
          </div>
        </div>
        {onBack && (
          <button className="back-button" onClick={onBack}>
            返回
          </button>
        )}
      </div>
    );
  };

  // 渲染卡片组件
  const renderCardComponent = (config: CardConfig) => {
    const { options, card_layout = 'grid', columns = 3, card_size = 'medium' } = config;

    return (
      <div className="quiz-cards">
        <h3 className="question-text">{config.question_text}</h3>
        <div 
          className={`cards-container ${card_layout} ${card_size}`}
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {options.map((option) => (
            <div
              key={option.id}
              className="card-item"
              onClick={() => onSelect(option)}
            >
              <div className="card-content">
                <span className="card-emoji">
                  {getEmojiFromMetadata(option.metadata)}
                </span>
                <span className="card-text">{option.option_text}</span>
              </div>
            </div>
          ))}
        </div>
        {onBack && (
          <button className="back-button" onClick={onBack}>
            返回
          </button>
        )}
      </div>
    );
  };

  // 渲染气泡组件
  const renderBubbleComponent = (config: BubbleConfig) => {
    const { options, bubble_layout = 'cluster', animation = true } = config;

    return (
      <div className="quiz-bubbles">
        <h3 className="question-text">{config.question_text}</h3>
        <div className={`bubbles-container ${bubble_layout} ${animation ? 'animated' : ''}`}>
          {options.map((option, index) => (
            <div
              key={option.id}
              className="bubble-item"
              onClick={() => onSelect(option)}
              style={{
                animationDelay: animation ? `${index * 0.1}s` : '0s'
              }}
            >
              <span className="bubble-emoji">
                {getEmojiFromMetadata(option.metadata)}
              </span>
              <span className="bubble-text">{option.option_text}</span>
            </div>
          ))}
        </div>
        {onBack && (
          <button className="back-button" onClick={onBack}>
            返回
          </button>
        )}
      </div>
    );
  };

  // 辅助函数：从metadata中获取emoji
  const getEmojiFromMetadata = (metadata: any): string => {
    try {
      const parsed = typeof metadata === 'string' ? JSON.parse(metadata) : metadata;
      return parsed?.emoji || '😊';
    } catch {
      return '😊';
    }
  };

  // 根据组件类型渲染相应组件
  const renderComponent = () => {
    switch (componentType) {
      case 'selector_component':
        return renderSelectorComponent(config as SelectorConfig);
      
      case 'rating_component':
        return renderRatingComponent(config as RatingConfig);
      
      case 'slider_component':
        return renderSliderComponent(config as SliderConfig);
      
      case 'text_input_component':
        return renderTextInputComponent(config as TextInputConfig);
      
      case 'wheel_component':
        return renderWheelComponent(config as WheelConfig);
      
      case 'card_component':
        return renderCardComponent(config as CardConfig);
      
      case 'bubble_component':
        return renderBubbleComponent(config as BubbleConfig);
      
      default:
        return (
          <div className="quiz-component-error">
            <p>不支持的组件类型: {componentType}</p>
          </div>
        );
    }
  };

  return (
    <div className={`quiz-component-renderer ${className || ''}`}>
      {renderComponent()}
    </div>
  );
};

export { QuizComponentRenderer };
export type { QuizComponentType, QuizConfig };
