-- Quiz System Database Indexes v2.0
-- Optimized indexes for performance and query efficiency

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- CORE INFRASTRUCTURE INDEXES
-- ============================================================================

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active, last_active_at);
CREATE INDEX IF NOT EXISTS idx_users_vip ON users(is_vip, vip_tier, vip_expires_at);
CREATE INDEX IF NOT EXISTS idx_users_sync ON users(sync_status, last_synced_at);

-- App settings indexes
CREATE INDEX IF NOT EXISTS idx_app_settings_system ON app_settings(is_system);
CREATE INDEX IF NOT EXISTS idx_app_settings_type ON app_settings(data_type);

-- UI labels and translations indexes
CREATE INDEX IF NOT EXISTS idx_ui_labels_category ON ui_labels(category);
CREATE INDEX IF NOT EXISTS idx_ui_label_translations_lang ON ui_label_translations(language_code);
CREATE INDEX IF NOT EXISTS idx_ui_label_translations_key_lang ON ui_label_translations(label_key, language_code);

-- User configs indexes
CREATE INDEX IF NOT EXISTS idx_user_configs_user ON user_configs(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_user_configs_default ON user_configs(user_id, is_default);
CREATE INDEX IF NOT EXISTS idx_user_configs_theme ON user_configs(theme_mode);

-- User streaks indexes
CREATE INDEX IF NOT EXISTS idx_user_streaks_user_type ON user_streaks(user_id, streak_type);
CREATE INDEX IF NOT EXISTS idx_user_streaks_date ON user_streaks(last_entry_date);

-- Skins indexes
CREATE INDEX IF NOT EXISTS idx_skins_category ON skins(category);
CREATE INDEX IF NOT EXISTS idx_skins_premium ON skins(is_premium, is_unlocked);
CREATE INDEX IF NOT EXISTS idx_skins_view_types ON skins(supported_view_types);

-- Emoji sets indexes
CREATE INDEX IF NOT EXISTS idx_emoji_sets_type ON emoji_sets(type);
CREATE INDEX IF NOT EXISTS idx_emoji_sets_default ON emoji_sets(is_default, is_system);
CREATE INDEX IF NOT EXISTS idx_emoji_sets_unlocked ON emoji_sets(is_unlocked, price);

-- Emoji set translations indexes
CREATE INDEX IF NOT EXISTS idx_emoji_set_translations_lang ON emoji_set_translations(language_code);

-- 注意：emotions, emotion_translations, emoji_items 索引已移除
-- 这些表已废弃，使用新的Quiz架构替代

-- ============================================================================
-- QUIZ PACKS INDEXES
-- ============================================================================

-- Quiz pack classification and filtering
CREATE INDEX IF NOT EXISTS idx_quiz_packs_category_type ON quiz_packs(category, quiz_type);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_active ON quiz_packs(is_active, sort_order);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_difficulty ON quiz_packs(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_style ON quiz_packs(quiz_style);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_type_style ON quiz_packs(quiz_type, quiz_style);
CREATE INDEX IF NOT EXISTS idx_quiz_packs_default ON quiz_packs(is_default, is_active);

-- ============================================================================
-- QUIZ QUESTIONS INDEXES
-- ============================================================================

-- Question ordering and pack relationships
CREATE INDEX IF NOT EXISTS idx_quiz_questions_pack_order ON quiz_questions(pack_id, question_order);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_type ON quiz_questions(question_type);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_group ON quiz_questions(pack_id, question_group);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_tier ON quiz_questions(pack_id, tier_level);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_parent ON quiz_questions(parent_question_id);
CREATE INDEX IF NOT EXISTS idx_quiz_questions_active ON quiz_questions(is_active, pack_id);

-- Hierarchical question queries
CREATE INDEX IF NOT EXISTS idx_quiz_questions_hierarchy ON quiz_questions(pack_id, tier_level, parent_question_id);

-- ============================================================================
-- QUIZ QUESTION OPTIONS INDEXES
-- ============================================================================

-- Option ordering and question relationships
CREATE INDEX IF NOT EXISTS idx_question_options_question_order ON quiz_question_options(question_id, option_order);
CREATE INDEX IF NOT EXISTS idx_question_options_value ON quiz_question_options(option_value);
CREATE INDEX IF NOT EXISTS idx_question_options_type ON quiz_question_options(option_type);
CREATE INDEX IF NOT EXISTS idx_question_options_matrix ON quiz_question_options(matrix_row_id, matrix_column_id);
CREATE INDEX IF NOT EXISTS idx_question_options_active ON quiz_question_options(is_active, question_id);

-- Scoring and multimedia queries
CREATE INDEX IF NOT EXISTS idx_question_options_scoring ON quiz_question_options(scoring_value, question_id);
CREATE INDEX IF NOT EXISTS idx_question_options_media ON quiz_question_options(media_type, question_id);

-- Content reuse queries
CREATE INDEX IF NOT EXISTS idx_question_options_reference ON quiz_question_options(reference_pack_id, reference_option_id);

-- ============================================================================
-- PRESENTATION CONFIG INDEXES
-- ============================================================================

-- 注意：emotion_data_sets 和 emotion_data_set_tiers 索引已移除
-- 这些表已废弃，使用新的Quiz架构替代

-- User presentation configuration queries
CREATE INDEX IF NOT EXISTS idx_user_presentation_configs_user ON user_presentation_configs(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_user_presentation_configs_default ON user_presentation_configs(user_id, is_default);
CREATE INDEX IF NOT EXISTS idx_user_presentation_configs_version ON user_presentation_configs(config_version, user_id);

-- Global app configuration queries
CREATE INDEX IF NOT EXISTS idx_global_app_configs_user ON global_app_configs(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_global_app_configs_default ON global_app_configs(user_id, is_default);
CREATE INDEX IF NOT EXISTS idx_global_app_configs_theme ON global_app_configs(theme_mode);

-- Pack-specific presentation overrides
CREATE INDEX IF NOT EXISTS idx_pack_overrides_user_pack ON pack_presentation_overrides(user_id, pack_id);
CREATE INDEX IF NOT EXISTS idx_pack_overrides_active ON pack_presentation_overrides(is_active);
CREATE INDEX IF NOT EXISTS idx_pack_overrides_priority ON pack_presentation_overrides(override_priority, user_id);

-- ============================================================================
-- SESSION MANAGEMENT INDEXES
-- ============================================================================

-- Quiz session queries
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_user_status ON quiz_sessions(user_id, status);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_pack_created ON quiz_sessions(pack_id, created_at);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_active ON quiz_sessions(status, last_active_time);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_user_pack ON quiz_sessions(user_id, pack_id, status);

-- Session progress tracking
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_progress ON quiz_sessions(completion_percentage, status);
CREATE INDEX IF NOT EXISTS idx_quiz_sessions_duration ON quiz_sessions(total_duration_seconds, status);

-- Session presentation config queries
CREATE INDEX IF NOT EXISTS idx_session_presentation_configs_session ON quiz_session_presentation_configs(session_id);
CREATE INDEX IF NOT EXISTS idx_session_presentation_configs_user ON quiz_session_presentation_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_session_presentation_configs_source ON quiz_session_presentation_configs(config_source);

-- ============================================================================
-- ANSWER AND RESULT INDEXES
-- ============================================================================

-- Quiz answer queries
CREATE INDEX IF NOT EXISTS idx_quiz_answers_session_question ON quiz_answers(session_id, question_id);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_answered_at ON quiz_answers(answered_at);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_scale_value ON quiz_answers(scale_value);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_presentation_config ON quiz_answers(session_presentation_config_id);

-- Answer analysis queries
CREATE INDEX IF NOT EXISTS idx_quiz_answers_response_time ON quiz_answers(response_time_ms, session_id);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_confidence ON quiz_answers(confidence_score, session_id);
CREATE INDEX IF NOT EXISTS idx_quiz_answers_revised ON quiz_answers(is_revised, revision_count);

-- Quiz result queries
CREATE INDEX IF NOT EXISTS idx_quiz_results_user_created ON quiz_results(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_quiz_results_pack ON quiz_results(pack_id);
CREATE INDEX IF NOT EXISTS idx_quiz_results_status ON quiz_results(status);
CREATE INDEX IF NOT EXISTS idx_quiz_results_ai_analysis ON quiz_results(ai_analysis_status);

-- Result scoring queries
CREATE INDEX IF NOT EXISTS idx_quiz_results_overall_score ON quiz_results(overall_score, user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_results_completion ON quiz_results(completion_percentage, status);

-- ============================================================================
-- ANALYTICS AND RECOMMENDATION INDEXES
-- ============================================================================

-- Emotion pattern analysis queries
CREATE INDEX IF NOT EXISTS idx_emotion_analyses_user_type ON emotion_pattern_analyses(user_id, analysis_type);
CREATE INDEX IF NOT EXISTS idx_emotion_analyses_period ON emotion_pattern_analyses(analysis_period_start, analysis_period_end);
CREATE INDEX IF NOT EXISTS idx_emotion_analyses_result ON emotion_pattern_analyses(result_id);

-- Pattern analysis metrics
CREATE INDEX IF NOT EXISTS idx_emotion_analyses_stability ON emotion_pattern_analyses(emotional_stability_index, user_id);
CREATE INDEX IF NOT EXISTS idx_emotion_analyses_trend ON emotion_pattern_analyses(trend_direction, trend_confidence);

-- Recommendation queries
CREATE INDEX IF NOT EXISTS idx_recommendations_user_active ON recommendations(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_recommendations_type_priority ON recommendations(recommendation_type, priority_level);
CREATE INDEX IF NOT EXISTS idx_recommendations_expires ON recommendations(expires_at);
CREATE INDEX IF NOT EXISTS idx_recommendations_category ON recommendations(category, user_id);

-- Recommendation feedback queries
CREATE INDEX IF NOT EXISTS idx_recommendations_rating ON recommendations(user_rating, is_helpful);
CREATE INDEX IF NOT EXISTS idx_recommendations_implemented ON recommendations(is_implemented, implementation_date);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- ============================================================================

-- User quiz activity analysis
CREATE INDEX IF NOT EXISTS idx_user_quiz_activity ON quiz_sessions(user_id, pack_id, created_at, status);

-- Quiz performance analysis
CREATE INDEX IF NOT EXISTS idx_quiz_performance ON quiz_results(pack_id, completion_percentage, overall_score, created_at);

-- User engagement tracking
CREATE INDEX IF NOT EXISTS idx_user_engagement ON quiz_sessions(user_id, last_active_time, total_duration_seconds, status);

-- Answer pattern analysis
CREATE INDEX IF NOT EXISTS idx_answer_patterns ON quiz_answers(session_id, question_id, scale_value, answered_at);

-- Recommendation effectiveness tracking
CREATE INDEX IF NOT EXISTS idx_recommendation_effectiveness ON recommendations(recommendation_type, user_rating, is_implemented, created_at);

-- ============================================================================
-- FULL-TEXT SEARCH INDEXES (if supported)
-- ============================================================================

-- Note: SQLite FTS requires special syntax and may not be available in all environments
-- These are commented out but can be enabled if FTS is available

-- CREATE VIRTUAL TABLE IF NOT EXISTS quiz_packs_fts USING fts5(name, description, content='quiz_packs', content_rowid='rowid');
-- CREATE VIRTUAL TABLE IF NOT EXISTS quiz_questions_fts USING fts5(question_text, content='quiz_questions', content_rowid='rowid');
-- CREATE VIRTUAL TABLE IF NOT EXISTS quiz_question_options_fts USING fts5(option_text, content='quiz_question_options', content_rowid='rowid');

-- ============================================================================
-- ANALYZE STATISTICS
-- ============================================================================

-- Update query planner statistics for all tables
-- Core infrastructure tables
ANALYZE users;
ANALYZE app_settings;
ANALYZE ui_labels;
ANALYZE ui_label_translations;
ANALYZE user_configs;
ANALYZE user_streaks;
ANALYZE skins;
ANALYZE emoji_sets;
ANALYZE emoji_set_translations;
-- 注意：emotions, emotion_translations, emoji_items 已废弃
-- 注意：emotion_data_sets 和 emotion_data_set_tiers 已废弃

-- Quiz system tables
ANALYZE quiz_packs;
ANALYZE quiz_questions;
ANALYZE quiz_question_options;
ANALYZE user_presentation_configs;
ANALYZE global_app_configs;
ANALYZE pack_presentation_overrides;
ANALYZE quiz_sessions;
ANALYZE quiz_session_presentation_configs;
ANALYZE quiz_answers;
ANALYZE quiz_results;
ANALYZE emotion_pattern_analyses;
ANALYZE recommendations;

-- ============================================================================
-- EMOJI MAPPING AND PRESENTATION INDEXES
-- ============================================================================

-- Question presentation overrides indexes
CREATE INDEX IF NOT EXISTS idx_question_overrides_user ON question_presentation_overrides(user_id);
CREATE INDEX IF NOT EXISTS idx_question_overrides_question ON question_presentation_overrides(question_id);
CREATE INDEX IF NOT EXISTS idx_question_overrides_active ON question_presentation_overrides(is_active) WHERE is_active = 1;
CREATE INDEX IF NOT EXISTS idx_question_overrides_priority ON question_presentation_overrides(override_priority);
CREATE INDEX IF NOT EXISTS idx_question_overrides_reason ON question_presentation_overrides(override_reason);

-- Pack presentation configs indexes
CREATE INDEX IF NOT EXISTS idx_pack_configs_pack ON pack_presentation_configs(pack_id);
CREATE INDEX IF NOT EXISTS idx_pack_configs_type ON pack_presentation_configs(config_type);
CREATE INDEX IF NOT EXISTS idx_pack_configs_active ON pack_presentation_configs(is_active) WHERE is_active = 1;
CREATE INDEX IF NOT EXISTS idx_pack_configs_default ON pack_presentation_configs(is_default) WHERE is_default = 1;
CREATE INDEX IF NOT EXISTS idx_pack_configs_pack_type ON pack_presentation_configs(pack_id, config_type);

-- Emoji mapping performance indexes
CREATE INDEX IF NOT EXISTS idx_question_overrides_user_question ON question_presentation_overrides(user_id, question_id);
CREATE INDEX IF NOT EXISTS idx_pack_configs_pack_active ON pack_presentation_configs(pack_id, is_active);

-- Update statistics for new tables
ANALYZE question_presentation_overrides;
ANALYZE pack_presentation_configs;
