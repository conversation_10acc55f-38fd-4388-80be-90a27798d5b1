#!/usr/bin/env node

/**
 * 字段命名修复脚本
 * 批量修复客户端代码中的字段命名不一致问题
 */

const fs = require('fs');
const path = require('path');

// 需要修复的字段映射 (当前错误的 -> 正确的)
const clientSideFieldMappings = {
  // 只修复客户端代码中的问题，不修复数据库字段名
  'shadowColor': 'shadow_color',
  'shadowBlur': 'shadow_blur', 
  'shadowOffsetX': 'shadow_offset_x',
  'shadowOffsetY': 'shadow_offset_y',
  'borderRadius': 'border_radius',
  'animationDuration': 'animation_duration',
  'animationEasing': 'animation_easing',
  'emojiSize': 'emoji_size',
  'bubbleSize': 'bubble_size',
  'bubbleSpacing': 'bubble_spacing',
  'cardSize': 'card_size',
  'cardSpacing': 'card_spacing',
  'cardHeight': 'card_height',
  'containerSize': 'container_size',
  'wheelRadius': 'wheel_radius',
  'fontFamily': 'font_family',
  'fontSize': 'font_size',
  'textColor': 'text_color',
  'backgroundColor': 'background_color',
  'transitionDuration': 'transition_duration'
};

// 需要检查的客户端文件目录
const clientDirs = [
  'src/views/components',
  'src/utils',
  'src/components'
];

/**
 * 获取所有TypeScript文件
 */
function getAllTsFiles(dir) {
  const files = [];
  
  if (!fs.existsSync(dir)) {
    return files;
  }
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getAllTsFiles(fullPath));
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 修复文件中的字段命名
 */
function fixFieldNaming(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let modifiedContent = content;
    let hasChanges = false;
    
    // 只修复特定的字段访问模式
    for (const [wrongField, correctField] of Object.entries(clientSideFieldMappings)) {
      // 修复对象属性访问: obj.wrongField
      const propertyAccessRegex = new RegExp(`\\.${wrongField}(?=\\s*[\\[\\]\\(\\)\\.,;:\\s}])`, 'g');
      if (propertyAccessRegex.test(modifiedContent)) {
        modifiedContent = modifiedContent.replace(propertyAccessRegex, `.${correctField}`);
        hasChanges = true;
        console.log(`  ✅ 修复 ${filePath}: .${wrongField} → .${correctField}`);
      }
      
      // 修复CSS样式属性: { wrongField: value }
      const cssPropertyRegex = new RegExp(`(\\s+)${wrongField}(\\s*:)`, 'g');
      if (cssPropertyRegex.test(modifiedContent)) {
        modifiedContent = modifiedContent.replace(cssPropertyRegex, `$1${correctField}$2`);
        hasChanges = true;
        console.log(`  ✅ 修复 ${filePath}: CSS ${wrongField} → ${correctField}`);
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, modifiedContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 修复文件失败 ${filePath}:`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复客户端字段命名问题...\n');
  
  let totalFiles = 0;
  let modifiedFiles = 0;
  
  for (const dir of clientDirs) {
    const fullDir = path.join(process.cwd(), dir);
    console.log(`📁 检查目录: ${dir}`);
    
    const files = getAllTsFiles(fullDir);
    totalFiles += files.length;
    
    for (const file of files) {
      if (fixFieldNaming(file)) {
        modifiedFiles++;
      }
    }
  }
  
  console.log(`\n✨ 修复完成!`);
  console.log(`📊 统计: 检查了 ${totalFiles} 个文件，修改了 ${modifiedFiles} 个文件`);
}

// 运行脚本
if (require.main === module) {
  main();
}
