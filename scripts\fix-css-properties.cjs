#!/usr/bin/env node

/**
 * CSS 属性修复脚本
 * 修复错误的 CSS 属性命名（应该使用 camelCase）
 */

const fs = require('fs');
const path = require('path');

// CSS 属性映射 (错误的 snake_case -> 正确的 camelCase)
const cssPropertyMappings = {
  'font_size': 'fontSize',
  'font_family': 'fontFamily',
  'text_color': 'textColor',
  'background_color': 'backgroundColor',
  'border_radius': 'borderRadius',
  'shadow_color': 'shadowColor',
  'shadow_blur': 'shadowBlur',
  'shadow_offset_x': 'shadowOffsetX',
  'shadow_offset_y': 'shadowOffsetY',
  'animation_duration': 'animationDuration',
  'animation_easing': 'animationEasing',
  'container_size': 'containerSize',
  'wheel_radius': 'wheelRadius',
  'emoji_size': 'emojiSize',
  'transition_duration': 'transitionDuration'
};

// 需要检查的客户端文件目录
const clientDirs = [
  'src/views/components',
  'src/components'
];

/**
 * 获取所有TypeScript文件
 */
function getAllTsFiles(dir) {
  const files = [];
  
  if (!fs.existsSync(dir)) {
    return files;
  }
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getAllTsFiles(fullPath));
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 修复文件中的CSS属性命名
 */
function fixCssProperties(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let modifiedContent = content;
    let hasChanges = false;
    
    // 修复CSS样式属性: { wrong_property: value }
    for (const [wrongProperty, correctProperty] of Object.entries(cssPropertyMappings)) {
      const cssPropertyRegex = new RegExp(`(\\s+)${wrongProperty}(\\s*:)`, 'g');
      if (cssPropertyRegex.test(modifiedContent)) {
        modifiedContent = modifiedContent.replace(cssPropertyRegex, `$1${correctProperty}$2`);
        hasChanges = true;
        console.log(`  ✅ 修复 ${filePath}: CSS ${wrongProperty} → ${correctProperty}`);
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, modifiedContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 修复文件失败 ${filePath}:`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复CSS属性命名问题...\n');
  
  let totalFiles = 0;
  let modifiedFiles = 0;
  
  for (const dir of clientDirs) {
    const fullDir = path.join(process.cwd(), dir);
    console.log(`📁 检查目录: ${dir}`);
    
    const files = getAllTsFiles(fullDir);
    totalFiles += files.length;
    
    for (const file of files) {
      if (fixCssProperties(file)) {
        modifiedFiles++;
      }
    }
  }
  
  console.log(`\n✨ 修复完成!`);
  console.log(`📊 统计: 检查了 ${totalFiles} 个文件，修改了 ${modifiedFiles} 个文件`);
}

// 运行脚本
if (require.main === module) {
  main();
}
