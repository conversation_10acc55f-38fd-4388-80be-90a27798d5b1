/**
 * Quiz按钮组件
 * 支持多种中医风格样式和游戏化反馈的按钮组件
 */

import React, { useRef, useState } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { ButtonComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type ButtonComponentConfig = z.infer<typeof ButtonComponentConfigSchema>;

export interface ButtonComponentProps extends BaseQuizComponentProps<ButtonComponentConfig> {
  onClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
  disabled?: boolean;
  loading?: boolean;
  children?: React.ReactNode;
}

interface ButtonComponentState extends ComponentState {
  is_pressed: boolean;
  is_hovered: boolean;
}

/**
 * 按钮组件类
 */
export class ButtonComponent extends BaseQuizComponent<
  ButtonComponentConfig,
  ButtonComponentProps,
  ButtonComponentState
> {
  private buttonRef = React.createRef<HTMLButtonElement>();
  private rippleTimeoutId?: number;

  extractConfig(props: ButtonComponentProps): ButtonComponentConfig {
    return props.config;
  }

  getInitialState(): ButtonComponentState {
    return {
      is_loading: this.props.loading || false,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      is_pressed: false,
      is_hovered: false
    };
  }

  componentDidUpdate(prevProps: ButtonComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.loading !== this.props.loading) {
      this.setState({ is_loading: this.props.loading || false });
    }
    
    if (prevProps.disabled !== this.props.disabled) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled
      });
    }
  }

  componentWillUnmount(): void {
    if (this.rippleTimeoutId) {
      clearTimeout(this.rippleTimeoutId);
    }
  }

  /**
   * 处理按钮点击
   */
  private handleClick = (event: React.MouseEvent<HTMLButtonElement>): void => {
    if (this.state.is_disabled || this.state.is_loading) {
      return;
    }

    // 触发触觉反馈
    if (this.config.feedback.haptic_feedback) {
      this.triggerHapticFeedback('medium');
    }

    // 播放音效
    if (this.config.feedback.sound_effect) {
      this.playSound(this.config.feedback.sound_effect);
    }

    // 触发动画
    this.triggerClickAnimation();

    // 发送交互事件
    this.emitInteractionEvent('click', {
      button_id: this.props.id,
      click_position: { x: event.clientX, y: event.clientY }
    });

    // 调用外部点击处理器
    this.props.onClick(event);
  };

  /**
   * 处理鼠标进入
   */
  private handleMouseEnter = (): void => {
    if (this.state.is_disabled) return;

    this.setState({ is_hovered: true });
    this.triggerHoverAnimation();
    
    this.emitInteractionEvent('hover', { action: 'enter' });
  };

  /**
   * 处理鼠标离开
   */
  private handleMouseLeave = (): void => {
    this.setState({ is_hovered: false });
    this.emitInteractionEvent('hover', { action: 'leave' });
  };

  /**
   * 处理鼠标按下
   */
  private handleMouseDown = (): void => {
    if (this.state.is_disabled) return;
    this.setState({ is_pressed: true });
  };

  /**
   * 处理鼠标释放
   */
  private handleMouseUp = (): void => {
    this.setState({ is_pressed: false });
  };

  /**
   * 触发点击动画
   */
  private triggerClickAnimation(): void {
    const element = this.buttonRef.current;
    if (!element || this.personalization.layer5_accessibility?.reduce_motion) {
      return;
    }

    const animationType = this.config.feedback.animation;
    
    switch (animationType) {
      case 'bounce':
        this.triggerBounceAnimation(element);
        break;
      case 'pulse':
        this.triggerPulseAnimation(element);
        break;
      case 'ripple':
        this.triggerRippleAnimation(element);
        break;
      default:
        this.triggerBounceAnimation(element);
    }
  }

  /**
   * 弹跳动画
   */
  private triggerBounceAnimation(element: HTMLElement): void {
    element.style.transform = 'scale(0.95)';
    element.style.transition = 'transform 0.1s ease-out';
    
    setTimeout(() => {
      element.style.transform = 'scale(1.05)';
      setTimeout(() => {
        element.style.transform = 'scale(1)';
      }, 100);
    }, 100);
  }

  /**
   * 脉冲动画
   */
  private triggerPulseAnimation(element: HTMLElement): void {
    element.style.boxShadow = '0 0 0 0 rgba(var(--primary-rgb), 0.7)';
    element.style.animation = 'quiz-button-pulse 0.6s ease-out';
    
    setTimeout(() => {
      element.style.animation = '';
    }, 600);
  }

  /**
   * 涟漪动画
   */
  private triggerRippleAnimation(element: HTMLElement): void {
    const ripple = document.createElement('span');
    ripple.className = 'quiz-button-ripple';
    element.appendChild(ripple);
    
    this.rippleTimeoutId = setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple);
      }
    }, 600) as any;
  }

  /**
   * 悬停动画
   */
  private triggerHoverAnimation(): void {
    const element = this.buttonRef.current;
    if (!element || this.personalization.layer5_accessibility?.reduce_motion) {
      return;
    }

    const hoverEffect = this.config.style.hover_effect;
    
    switch (hoverEffect) {
      case 'scale':
        element.style.transform = 'scale(1.05)';
        break;
      case 'glow':
        element.style.boxShadow = '0 0 20px rgba(var(--primary-rgb), 0.5)';
        break;
      case 'shadow':
        element.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
        break;
    }
  }

  /**
   * 播放音效
   */
  private playSound(soundName: string): void {
    // 这里可以集成音效播放逻辑
    console.log(`Playing sound: ${soundName}`);
  }

  /**
   * 获取当前语言的文本
   */
  private getCurrentText(): string {
    const { language } = this.context || { language: 'zh' };
    const textLocalized = this.config.content.text_localized;
    
    return textLocalized[language] || textLocalized['zh'] || textLocalized['en'] || '';
  }

  /**
   * 获取布局样式类名
   */
  private getLayoutClassName(): string {
    const layoutId = this.config.layout_id;
    
    switch (layoutId) {
      case 'jade_pendant':
        return 'quiz-button-jade-pendant';
      case 'seal_stamp':
        return 'quiz-button-seal-stamp';
      case 'standard_button':
      default:
        return 'quiz-button-standard';
    }
  }

  /**
   * 获取尺寸样式类名
   */
  private getSizeClassName(): string {
    const size = this.config.style.size;
    return `quiz-button-${size}`;
  }

  /**
   * 获取变体样式类名
   */
  private getVariantClassName(): string {
    const variant = this.config.style.variant;
    return `quiz-button-${variant}`;
  }

  /**
   * 获取形状样式类名
   */
  private getShapeClassName(): string {
    const shape = this.config.style.shape;
    return `quiz-button-${shape}`;
  }

  /**
   * 渲染图标
   */
  private renderIcon(): React.ReactNode {
    const { icon_name } = this.config.content;
    const { icon_position } = this.config.style;
    
    if (!icon_name || icon_position === 'none') {
      return null;
    }

    return (
      <span className={`quiz-button-icon quiz-button-icon-${icon_position}`}>
        {/* 这里可以集成图标库 */}
        <i className={`icon-${icon_name}`} />
      </span>
    );
  }

  /**
   * 渲染加载状态
   */
  private renderLoadingState(): React.ReactNode {
    if (!this.state.is_loading) return null;

    return (
      <span className="quiz-button-loading">
        <span className="quiz-button-spinner" />
      </span>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-button-component',
      this.getLayoutClassName(),
      this.getSizeClassName(),
      this.getVariantClassName(),
      this.getShapeClassName(),
      this.state.is_hovered && 'quiz-button-hovered',
      this.state.is_pressed && 'quiz-button-pressed',
      this.state.is_loading && 'quiz-button-loading',
      this.state.is_disabled && 'quiz-button-disabled',
      this.props.className
    ].filter(Boolean).join(' ');

    const buttonText = this.getCurrentText();
    const showIcon = this.config.style.icon_position !== 'none';
    const iconOnly = this.config.style.icon_position === 'only';

    return (
      <button
        ref={this.buttonRef}
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        disabled={this.state.is_disabled || this.state.is_loading}
        onClick={this.handleClick}
        onMouseEnter={this.handleMouseEnter}
        onMouseLeave={this.handleMouseLeave}
        onMouseDown={this.handleMouseDown}
        onMouseUp={this.handleMouseUp}
        {...accessibilityProps}
        aria-label={iconOnly ? buttonText : undefined}
        aria-busy={this.state.is_loading}
      >
        {/* 图标 - 左侧 */}
        {showIcon && this.config.style.icon_position === 'left' && this.renderIcon()}
        
        {/* 文本内容 */}
        {!iconOnly && (
          <span className="quiz-button-text">
            {buttonText}
          </span>
        )}
        
        {/* 图标 - 右侧或仅图标 */}
        {showIcon && (this.config.style.icon_position === 'right' || iconOnly) && this.renderIcon()}
        
        {/* 加载状态 */}
        {this.renderLoadingState()}
        
        {/* 子元素 */}
        {this.props.children}
      </button>
    );
  }

  protected getAriaRole(): string {
    return 'button';
  }

  protected getAriaLabel(): string {
    return `Button: ${this.getCurrentText()}`;
  }
}

// 使用Context的函数式组件包装器
const ButtonComponentWrapper: React.FC<ButtonComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <ButtonComponent.contextType.Provider value={{ language }}>
      <ButtonComponent {...props} />
    </ButtonComponent.contextType.Provider>
  );
};

// 设置Context类型
ButtonComponent.contextType = React.createContext({ language: 'zh' });

export default ButtonComponentWrapper;
