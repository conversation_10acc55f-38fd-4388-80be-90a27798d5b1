#!/usr/bin/env node

/**
 * 字段命名不一致检测脚本
 * 检测驼峰命名 vs 下划线命名的不一致问题
 */

const fs = require('fs');
const path = require('path');

// 常见的字段命名映射 (驼峰 -> 下划线)
const fieldMappings = {
  // 皮肤相关
  'isUnlocked': 'is_unlocked',
  'isPremium': 'is_premium',
  'isDefault': 'is_default',
  'isSystem': 'is_system',
  'previewImage': 'preview_image',
  'previewImageLight': 'preview_image_light',
  'previewImageDark': 'preview_image_dark',
  'created_at': 'created_at',
  'updated_at': 'updated_at',
  'created_by': 'created_by',
  'lastUpdated': 'last_updated',

  // 配置相关
  'viewConfigs': 'view_configs',
  'borderRadius': 'border_radius',
  'cardSize': 'card_size',
  'cardWidth': 'card_width',
  'cardHeight': 'card_height',
  'cardSpacing': 'card_spacing',
  'cardBorderRadius': 'card_border_radius',
  'bubbleSize': 'bubble_size',
  'bubbleSpacing': 'bubble_spacing',
  'bubbleBackgroundOpacity': 'bubble_background_opacity',
  'textVisible': 'text_visible',

  // 用户配置相关
  'userId': 'user_id',
  'isActive': 'is_active',
  'activeEmotionDataId': 'active_emotion_data_id',
  'activeSkinId': 'active_skin_id',
  'preferredViewType': 'preferred_view_type',
  'darkMode': 'dark_mode',
  'viewTypeSkinIds': 'view_type_skin_ids',
  'renderEnginePreferences': 'render_engine_preferences',
  'contentDisplayModePreferences': 'content_display_mode_preferences',
  'layoutPreferences': 'layout_preferences',

  // 情绪数据相关
  'emotionDataSetId': 'emotion_data_set_id',
  'parentTierId': 'parent_tier_id',
  'defaultEmojiSetId': 'default_emoji_set_id',
  'tierLevel': 'tier_level',
  'tierId': 'tier_id',

  // 其他
  'emojiSize': 'emoji_size',
  'textSize': 'text_size',
  'shadowColor': 'shadow_color',
  'shadowBlur': 'shadow_blur',
  'shadowOffsetX': 'shadow_offset_x',
  'shadowOffsetY': 'shadow_offset_y',
  'animationDuration': 'animation_duration',
  'animationEasing': 'animation_easing',
  'textShadow': 'text_shadow',
};

// 反向映射 (下划线 -> 驼峰)
const reverseFieldMappings = {};
for (const [camel, snake] of Object.entries(fieldMappings)) {
  reverseFieldMappings[snake] = camel;
}

// 获取所有 TypeScript 文件
function getAllTsFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...getAllTsFiles(fullPath));
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }

  return files;
}

// 检测文件中的字段命名问题
function detectFieldIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const issues = [];

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // 检测驼峰命名字段的使用
      for (const [camelField, snakeField] of Object.entries(fieldMappings)) {
        // 匹配 .camelField 或 ?.camelField 的模式
        const camelRegex = new RegExp(`\\b(\\w+\\.|\\w+\\?\\.)${camelField}\\b`, 'g');
        const camelMatches = line.match(camelRegex);

        if (camelMatches) {
          issues.push({
            file: filePath,
            line: lineNumber,
            type: 'camel_to_snake',
            found: camelField,
            suggested: snakeField,
            context: line.trim(),
            severity: 'error'
          });
        }
      }

      // 检测下划线命名字段的使用（如果应该是驼峰）
      for (const [snakeField, camelField] of Object.entries(reverseFieldMappings)) {
        // 匹配 .snake_field 或 ?.snake_field 的模式
        const snakeRegex = new RegExp(`\\b(\\w+\\.|\\w+\\?\\.)${snakeField}\\b`, 'g');
        const snakeMatches = line.match(snakeRegex);

        if (snakeMatches) {
          // 检查是否在类型定义中（这种情况下下划线是正确的）
          if (!line.includes('interface') && !line.includes('type') && !line.includes('Schema')) {
            issues.push({
              file: filePath,
              line: lineNumber,
              type: 'snake_to_camel',
              found: snakeField,
              suggested: camelField,
              context: line.trim(),
              severity: 'warning'
            });
          }
        }
      }

      // 检测特殊情况：parsedConfig, parsedSupportedXxx 等
      const parsedFieldRegex = /\bparsed[A-Z]\w*/g;
      const parsedMatches = line.match(parsedFieldRegex);
      if (parsedMatches) {
        issues.push({
          file: filePath,
          line: lineNumber,
          type: 'missing_field',
          found: parsedMatches.join(', '),
          suggested: '可能需要在类型定义中添加这些字段',
          context: line.trim(),
          severity: 'warning'
        });
      }
    });

    return issues;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return [];
  }
}

// 主函数
function main() {
  const rootDir = process.cwd();
  const srcDir = path.join(rootDir, 'src');
  const serverDir = path.join(rootDir, 'server');

  // 获取所有需要检测的文件
  const srcFiles = getAllTsFiles(srcDir);
  const serverFiles = getAllTsFiles(serverDir);
  const allFiles = [...srcFiles, ...serverFiles];

  console.log(`🔍 检测 ${allFiles.length} 个 TypeScript 文件中的字段命名问题...`);
  console.log(`  - src 目录: ${srcFiles.length} 个文件`);
  console.log(`  - server 目录: ${serverFiles.length} 个文件\n`);

  let totalIssues = 0;
  const issuesByType = {
    camel_to_snake: 0,
    snake_to_camel: 0,
    missing_field: 0
  };

  const allIssues = [];

  for (const file of allFiles) {
    const issues = detectFieldIssues(file);
    if (issues.length > 0) {
      allIssues.push(...issues);
      totalIssues += issues.length;

      issues.forEach(issue => {
        issuesByType[issue.type]++;
      });
    }
  }

  // 按文件分组显示问题
  const issuesByFile = {};
  allIssues.forEach(issue => {
    if (!issuesByFile[issue.file]) {
      issuesByFile[issue.file] = [];
    }
    issuesByFile[issue.file].push(issue);
  });

  // 显示结果
  console.log('📊 检测结果统计:');
  console.log(`总问题数: ${totalIssues}`);
  console.log(`驼峰→下划线问题: ${issuesByType.camel_to_snake}`);
  console.log(`下划线→驼峰问题: ${issuesByType.snake_to_camel}`);
  console.log(`缺失字段问题: ${issuesByType.missing_field}`);
  console.log(`问题文件数: ${Object.keys(issuesByFile).length}\n`);

  // 显示详细问题
  if (totalIssues > 0) {
    console.log('🔍 详细问题列表:\n');

    for (const [file, issues] of Object.entries(issuesByFile)) {
      console.log(`📁 ${file.replace(rootDir, '.')}`);

      issues.forEach(issue => {
        const icon = issue.severity === 'error' ? '❌' : '⚠️';
        console.log(`  ${icon} 第${issue.line}行: ${issue.found} → ${issue.suggested}`);
        console.log(`     ${issue.context}`);
      });

      console.log('');
    }
  } else {
    console.log('✅ 未发现字段命名问题！');
  }
}

main();
