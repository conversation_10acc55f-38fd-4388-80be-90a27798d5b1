/**
 * Quiz下拉选择器组件
 * 支持多种中医文化样式的下拉选择器组件
 */

import React, { useState, useRef, useEffect } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { DropdownComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type DropdownComponentConfig = z.infer<typeof DropdownComponentConfigSchema>;

export interface DropdownOption {
  id: string;
  value: string | number;
  text_localized: Record<string, string>;
  icon_name?: string;
  disabled?: boolean;
}

export interface DropdownComponentProps extends BaseQuizComponentProps<DropdownComponentConfig> {
  options: DropdownOption[];
  value: string | number | null;
  onChange: (value: string | number | null) => void;
  onOpen?: () => void;
  onClose?: () => void;
  disabled?: boolean;
  placeholder?: Record<string, string>;
}

interface DropdownComponentState extends ComponentState {
  is_open: boolean;
  focused_option: string | null;
  search_query: string;
}

/**
 * 下拉选择器组件类
 */
export class DropdownComponent extends BaseQuizComponent<
  DropdownComponentConfig,
  DropdownComponentProps,
  DropdownComponentState
> {
  private containerRef = React.createRef<HTMLDivElement>();
  private menuRef = React.createRef<HTMLDivElement>();
  private triggerRef = React.createRef<HTMLButtonElement>();

  extractConfig(props: DropdownComponentProps): DropdownComponentConfig {
    return props.config;
  }

  getInitialState(): DropdownComponentState {
    return {
      is_loading: false,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: this.props.value ? [String(this.props.value)] : [],
      animation_state: 'idle',
      is_open: false,
      focused_option: null,
      search_query: ''
    };
  }

  componentDidMount(): void {
    document.addEventListener('mousedown', this.handleClickOutside);
    document.addEventListener('keydown', this.handleGlobalKeyDown);
  }

  componentWillUnmount(): void {
    document.removeEventListener('mousedown', this.handleClickOutside);
    document.removeEventListener('keydown', this.handleGlobalKeyDown);
  }

  componentDidUpdate(prevProps: DropdownComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.value !== this.props.value) {
      this.setState({ 
        selected_items: this.props.value ? [String(this.props.value)] : []
      });
    }
    
    if (prevProps.disabled !== this.props.disabled) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled
      });
    }
  }

  /**
   * 处理点击外部区域
   */
  private handleClickOutside = (event: MouseEvent): void => {
    if (this.containerRef.current && !this.containerRef.current.contains(event.target as Node)) {
      this.closeDropdown();
    }
  };

  /**
   * 处理全局键盘事件
   */
  private handleGlobalKeyDown = (event: KeyboardEvent): void => {
    if (this.state.is_open && event.key === 'Escape') {
      this.closeDropdown();
      this.triggerRef.current?.focus();
    }
  };

  /**
   * 打开下拉菜单
   */
  private openDropdown = (): void => {
    if (this.state.is_disabled) return;

    this.setState({ is_open: true });
    this.props.onOpen?.();

    // 发送交互事件
    this.emitInteractionEvent('focus', { action: 'open_dropdown' });

    // 触发触觉反馈
    this.triggerHapticFeedback('light');
  };

  /**
   * 关闭下拉菜单
   */
  private closeDropdown = (): void => {
    this.setState({ 
      is_open: false, 
      focused_option: null,
      search_query: ''
    });
    this.props.onClose?.();

    // 发送交互事件
    this.emitInteractionEvent('blur', { action: 'close_dropdown' });
  };

  /**
   * 切换下拉菜单状态
   */
  private toggleDropdown = (): void => {
    if (this.state.is_open) {
      this.closeDropdown();
    } else {
      this.openDropdown();
    }
  };

  /**
   * 处理选项选择
   */
  private handleOptionSelect = (option: DropdownOption): void => {
    if (option.disabled || this.state.is_disabled) return;

    this.setState({ 
      selected_items: [String(option.value)],
      is_open: false,
      focused_option: null
    });

    this.props.onChange(option.value);

    // 触发触觉反馈
    this.triggerHapticFeedback('medium');

    // 发送交互事件
    this.emitInteractionEvent('select', {
      option_id: option.id,
      option_value: option.value,
      option_text: this.getOptionText(option)
    });

    // 聚焦回触发器
    this.triggerRef.current?.focus();
  };

  /**
   * 处理键盘导航
   */
  protected handleKeyNavigation = (event: React.KeyboardEvent): void => {
    if (!this.personalization.layer5_accessibility?.keyboard_navigation || this.state.is_disabled) {
      return;
    }

    const availableOptions = this.props.options.filter(opt => !opt.disabled);
    const currentFocusIndex = availableOptions.findIndex(opt => opt.id === this.state.focused_option);

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        if (!this.state.is_open) {
          this.openDropdown();
        } else {
          const nextIndex = currentFocusIndex < availableOptions.length - 1 ? currentFocusIndex + 1 : 0;
          this.setState({ focused_option: availableOptions[nextIndex].id });
        }
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        if (this.state.is_open) {
          const prevIndex = currentFocusIndex > 0 ? currentFocusIndex - 1 : availableOptions.length - 1;
          this.setState({ focused_option: availableOptions[prevIndex].id });
        }
        break;
        
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (!this.state.is_open) {
          this.openDropdown();
        } else if (this.state.focused_option) {
          const focusedOption = availableOptions.find(opt => opt.id === this.state.focused_option);
          if (focusedOption) {
            this.handleOptionSelect(focusedOption);
          }
        }
        break;
        
      case 'Escape':
        event.preventDefault();
        this.closeDropdown();
        break;
    }
  };

  /**
   * 获取选项文本
   */
  private getOptionText(option: DropdownOption): string {
    const { language } = this.context || { language: 'zh' };
    return option.text_localized[language] || option.text_localized['zh'] || option.text_localized['en'] || '';
  }

  /**
   * 获取占位符文本
   */
  private getPlaceholderText(): string {
    const { language } = this.context || { language: 'zh' };
    const placeholder = this.props.placeholder || this.config.style.placeholder_text;
    
    if (placeholder) {
      return placeholder[language] || placeholder['zh'] || placeholder['en'] || '';
    }
    
    return language === 'zh' ? '请选择...' : 'Please select...';
  }

  /**
   * 获取当前选中的选项
   */
  private getSelectedOption(): DropdownOption | null {
    return this.props.options.find(opt => opt.value === this.props.value) || null;
  }

  /**
   * 获取箭头样式类名
   */
  private getArrowStyleClassName(): string {
    const arrowStyle = this.config.style.arrow_style;
    return `quiz-dropdown-arrow-${arrowStyle}`;
  }

  /**
   * 获取菜单样式类名
   */
  private getMenuStyleClassName(): string {
    const menuStyle = this.config.style.menu_style;
    return `quiz-dropdown-menu-${menuStyle}`;
  }

  /**
   * 渲染触发器
   */
  private renderTrigger(): React.ReactNode {
    const selectedOption = this.getSelectedOption();
    const placeholderText = this.getPlaceholderText();

    return (
      <button
        ref={this.triggerRef}
        className={`
          quiz-dropdown-trigger
          ${this.state.is_open ? 'quiz-dropdown-trigger-open' : ''}
          ${this.state.is_disabled ? 'quiz-dropdown-trigger-disabled' : ''}
        `.trim()}
        onClick={this.toggleDropdown}
        onKeyDown={this.handleKeyNavigation}
        disabled={this.state.is_disabled}
        aria-haspopup="listbox"
        aria-expanded={this.state.is_open}
        aria-label="Select option"
      >
        <span className="quiz-dropdown-trigger-content">
          {selectedOption ? (
            <>
              {selectedOption.icon_name && (
                <span className="quiz-dropdown-option-icon">
                  <i className={`icon-${selectedOption.icon_name}`} />
                </span>
              )}
              <span className="quiz-dropdown-option-text">
                {this.getOptionText(selectedOption)}
              </span>
            </>
          ) : (
            <span className="quiz-dropdown-placeholder">
              {placeholderText}
            </span>
          )}
        </span>
        
        <span className={`quiz-dropdown-arrow ${this.getArrowStyleClassName()}`}>
          {this.config.style.arrow_style === 'chinese_arrow' ? '▼' : '▼'}
        </span>
      </button>
    );
  }

  /**
   * 渲染选项
   */
  private renderOption = (option: DropdownOption): React.ReactNode => {
    const isSelected = option.value === this.props.value;
    const isFocused = option.id === this.state.focused_option;
    const optionText = this.getOptionText(option);

    return (
      <div
        key={option.id}
        className={`
          quiz-dropdown-option
          ${isSelected ? 'quiz-dropdown-option-selected' : ''}
          ${isFocused ? 'quiz-dropdown-option-focused' : ''}
          ${option.disabled ? 'quiz-dropdown-option-disabled' : ''}
        `.trim()}
        onClick={() => this.handleOptionSelect(option)}
        onMouseEnter={() => this.setState({ focused_option: option.id })}
        role="option"
        aria-selected={isSelected}
        aria-disabled={option.disabled}
      >
        {option.icon_name && (
          <span className="quiz-dropdown-option-icon">
            <i className={`icon-${option.icon_name}`} />
          </span>
        )}
        
        <span className="quiz-dropdown-option-text">
          {optionText}
        </span>
        
        {isSelected && (
          <span className="quiz-dropdown-option-checkmark">
            ✓
          </span>
        )}
      </div>
    );
  };

  /**
   * 渲染下拉菜单
   */
  private renderMenu(): React.ReactNode {
    if (!this.state.is_open) return null;

    return (
      <div
        ref={this.menuRef}
        className={`quiz-dropdown-menu ${this.getMenuStyleClassName()}`}
        style={{
          maxHeight: `${this.config.style.max_height}px`
        }}
        role="listbox"
      >
        <div className="quiz-dropdown-options">
          {this.props.options.map(this.renderOption)}
        </div>
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const className = [
      'quiz-dropdown-component',
      this.state.is_open && 'quiz-dropdown-open',
      this.state.is_disabled && 'quiz-dropdown-disabled',
      this.props.className
    ].filter(Boolean).join(' ');

    return (
      <div
        ref={this.containerRef}
        className={className}
        style={{ ...personalizedStyles, ...this.props.style }}
        {...accessibilityProps}
      >
        {/* 触发器 */}
        {this.renderTrigger()}

        {/* 下拉菜单 */}
        {this.renderMenu()}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'combobox';
  }

  protected getAriaLabel(): string {
    const selectedOption = this.getSelectedOption();
    return selectedOption 
      ? `Selected: ${this.getOptionText(selectedOption)}`
      : 'No option selected';
  }
}

// 使用Context的函数式组件包装器
const DropdownComponentWrapper: React.FC<DropdownComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <DropdownComponent.contextType.Provider value={{ language }}>
      <DropdownComponent {...props} />
    </DropdownComponent.contextType.Provider>
  );
};

// 设置Context类型
DropdownComponent.contextType = React.createContext({ language: 'zh' });

export default DropdownComponentWrapper;
