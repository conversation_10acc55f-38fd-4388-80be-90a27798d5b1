{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/e2e/end-to-end.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 80131, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 80131, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 518, "endOffset": 29281, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 561, "endOffset": 622, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 677, "endOffset": 6737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 736, "endOffset": 4352, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4407, "endOffset": 6729, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6791, "endOffset": 12829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6847, "endOffset": 10111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10163, "endOffset": 12821, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12881, "endOffset": 17626, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12937, "endOffset": 15107, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13069, "endOffset": 13583, "count": 1}, {"startOffset": 13182, "endOffset": 13531, "count": 50}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15161, "endOffset": 17618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17679, "endOffset": 23174, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17734, "endOffset": 20817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20869, "endOffset": 23166, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23228, "endOffset": 29277, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23288, "endOffset": 26280, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26333, "endOffset": 29269, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}