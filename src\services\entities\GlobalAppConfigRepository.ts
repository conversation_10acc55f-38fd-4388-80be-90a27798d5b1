/**
 * 全局应用配置 Repository
 * 管理用户的全局应用设置（主题、语言、通知等）
 */

import { BaseRepository } from '../base/BaseRepository';
import { GlobalAppConfig } from '../../types/schema/base';
import { CreateGlobalAppConfigInput, UpdateGlobalAppConfigInput } from '../../types/schema/api';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export class GlobalAppConfigRepository extends BaseRepository<
  GlobalAppConfig,
  CreateGlobalAppConfigInput,
  UpdateGlobalAppConfigInput
> {
  constructor(db?: SQLiteDBConnection) {
    super('user_configs', db);
  }

  /**
   * 获取用户的活跃全局配置
   */
  async getActiveUserConfig(userId: string, configName: string = 'default'): Promise<GlobalAppConfig | null> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName}
        WHERE user_id = ? AND name = ? AND is_active = 1
        ORDER BY last_updated DESC
        LIMIT 1
      `;

      const result = await db.query(query, [userId, configName]);
      const config = result.values?.[0];

      return config ? this.mapRowToEntity(config) : null;
    } catch (error) {
      console.error('Error getting active user config:', error);
      return null;
    }
  }

  /**
   * 获取用户的所有全局配置
   */
  async getUserConfigs(userId: string): Promise<GlobalAppConfig[]> {
    try {
      const db = this.getDb();
      const query = `
        SELECT * FROM ${this.tableName}
        WHERE user_id = ?
        ORDER BY is_active DESC, last_updated DESC
      `;

      const result = await db.query(query, [userId]);
      return (result.values || []).map(row => this.mapRowToEntity(row));
    } catch (error) {
      console.error('Error getting user configs:', error);
      return [];
    }
  }

  /**
   * 更新用户配置
   */
  async updateUserConfig(userId: string, configName: string, updates: UpdateGlobalAppConfigInput): Promise<GlobalAppConfig | null> {
    try {
      // 先获取现有配置
      const existingConfig = await this.getActiveUserConfig(userId, configName);

      if (!existingConfig) {
        // 如果不存在，创建新配置
        const newConfigData: CreateGlobalAppConfigInput = {
          id: this.extractIdFromCreateData({}),
          name: configName,
          user_id: userId,
          is_active: true,
          theme_mode: updates.theme_mode || 'system',
          language: updates.language || 'zh-CN',
          accessibility: updates.accessibility || JSON.stringify({
            high_contrast: false,
            large_text: false,
            reduce_motion: false,
            screen_reader_support: false
          }),
          notifications_enabled: updates.notifications_enabled ?? true,
          sound_enabled: updates.sound_enabled ?? true
        };

        return await this.create(newConfigData);
      }

      // 更新现有配置
      const updatedConfig = await this.update(existingConfig.id!, updates);
      return updatedConfig;
    } catch (error) {
      console.error('Error updating user config:', error);
      return null;
    }
  }

  /**
   * 创建用户配置
   */
  async createUserConfig(data: CreateGlobalAppConfigInput): Promise<GlobalAppConfig | null> {
    try {
      // 如果创建的是默认配置，先将其他配置设为非活跃
      if (data.is_active && data.name === 'default') {
        await this.deactivateOtherConfigs(data.user_id, data.name);
      }

      return await this.create(data);
    } catch (error) {
      console.error('Error creating user config:', error);
      return null;
    }
  }

  /**
   * 停用其他配置
   */
  private async deactivateOtherConfigs(userId: string, excludeName: string): Promise<void> {
    try {
      const db = this.getDb();
      const query = `
        UPDATE ${this.tableName}
        SET is_active = 0, last_updated = ?
        WHERE user_id = ? AND name != ? AND is_active = 1
      `;

      await db.query(query, [new Date().toISOString(), userId, excludeName]);
    } catch (error) {
      console.error('Error deactivating other configs:', error);
    }
  }



  protected mapRowToEntity(row: any): GlobalAppConfig {
    return {
      id: row.id,
      name: row.name,
      user_id: row.user_id,
      is_active: Boolean(row.is_active),
      theme_mode: row.theme_mode,
      language: row.language,
      accessibility: row.accessibility,
      notifications_enabled: Boolean(row.notifications_enabled),
      sound_enabled: Boolean(row.sound_enabled),
      created_at: row.created_at,
      last_updated: row.last_updated
    };
  }

  protected mapEntityToRow(entity: Partial<GlobalAppConfig>): Record<string, any> {
    return {
      id: entity.id,
      name: entity.name,
      user_id: entity.user_id,
      is_active: entity.is_active ? 1 : 0,
      theme_mode: entity.theme_mode,
      language: entity.language,
      accessibility: entity.accessibility,
      notifications_enabled: entity.notifications_enabled ? 1 : 0,
      sound_enabled: entity.sound_enabled ? 1 : 0,
      created_at: entity.created_at,
      last_updated: entity.last_updated
    };
  }

  protected buildInsertQuery(data: CreateGlobalAppConfigInput): { query: string; values: any[] } {
    const now = new Date().toISOString();
    const configId = this.extractIdFromCreateData(data);

    const query = `
      INSERT INTO ${this.tableName} (
        id, name, user_id, is_active, theme_mode, language,
        accessibility, notifications_enabled, sound_enabled,
        created_at, last_updated
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      configId,
      data.name,
      data.user_id,
      data.is_active ? 1 : 0,
      data.theme_mode || 'system',
      data.language || 'zh-CN',
      data.accessibility || JSON.stringify({
        high_contrast: false,
        large_text: false,
        reduce_motion: false,
        screen_reader_support: false
      }),
      data.notifications_enabled ? 1 : 0,
      data.sound_enabled ? 1 : 0,
      now,
      now
    ];

    return { query, values };
  }

  protected buildUpdateQuery(id: string, data: UpdateGlobalAppConfigInput): { query: string; values: any[] } {
    const fields: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      fields.push('name = ?');
      values.push(data.name);
    }

    if (data.is_active !== undefined) {
      fields.push('is_active = ?');
      values.push(data.is_active ? 1 : 0);
    }

    if (data.theme_mode !== undefined) {
      fields.push('theme_mode = ?');
      values.push(data.theme_mode);
    }

    if (data.language !== undefined) {
      fields.push('language = ?');
      values.push(data.language);
    }

    if (data.accessibility !== undefined) {
      fields.push('accessibility = ?');
      values.push(data.accessibility);
    }

    if (data.notifications_enabled !== undefined) {
      fields.push('notifications_enabled = ?');
      values.push(data.notifications_enabled ? 1 : 0);
    }

    if (data.sound_enabled !== undefined) {
      fields.push('sound_enabled = ?');
      values.push(data.sound_enabled ? 1 : 0);
    }

    // Always update last_updated
    fields.push('last_updated = ?');
    values.push(new Date().toISOString());

    const query = `UPDATE ${this.tableName} SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);

    return { query, values };
  }

  protected buildSelectQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT * FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.user_id) {
      conditions.push('user_id = ?');
      values.push(filters.user_id);
    }

    if (filters?.name) {
      conditions.push('name = ?');
      values.push(filters.name);
    }

    if (filters?.is_active !== undefined) {
      conditions.push('is_active = ?');
      values.push(filters.is_active ? 1 : 0);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY last_updated DESC';

    if (filters?.limit) {
      query += ' LIMIT ?';
      values.push(filters.limit);
    }

    return { query, values };
  }

  protected buildCountQuery(filters?: any): { query: string; values: any[] } {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const values: any[] = [];
    const conditions: string[] = [];

    if (filters?.user_id) {
      conditions.push('user_id = ?');
      values.push(filters.user_id);
    }

    if (filters?.is_active !== undefined) {
      conditions.push('is_active = ?');
      values.push(filters.is_active ? 1 : 0);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    return { query, values };
  }

  protected extractIdFromCreateData(data: CreateGlobalAppConfigInput): string {
    return data.id || `global_config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}
