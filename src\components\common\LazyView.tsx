/**
 * 懒加载视图组件
 * 用于延迟加载视图组件，提高性能
 */

import { useVisibility } from '@/utils/renderOptimizer';
import type React from 'react';
import { Suspense, lazy, useEffect, useState } from 'react';
import { Skeleton } from '../ui/skeleton';

interface LazyViewProps {
  viewType: string;
  fallback?: React.ReactNode;
  onLoad?: () => void;
  [key: string]: any;
}

/**
 * 懒加载视图组件
 * 根据视图类型动态加载对应的视图组件
 */
const LazyView: React.FC<LazyViewProps> = ({ viewType, fallback, onLoad, ...props }) => {
  const [ref, isVisible] = useVisibility({
    rootMargin: '100px', // 提前100px开始加载
    threshold: 0.1, // 当10%的组件可见时开始加载
  });

  const [Component, setComponent] = useState<React.ComponentType<any> | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // 默认的加载占位符
  const defaultFallback = (
    <div className="w-full h-full flex items-center justify-center">
      <Skeleton className="w-full h-full rounded-md" />
    </div>
  );

  // 当组件可见时，动态加载视图组件
  useEffect(() => {
    if (isVisible && !Component) {
      const importComponent = async () => {
        try {
          let loadedComponent;

          // 根据视图类型动态导入对应的组件
          switch (viewType) {
            case 'wheel':
              loadedComponent = (await import('@/components/views/WheelView')).WheelView;
              break;
            case 'card':
              loadedComponent = (await import('@/views/components/cards/CardView')).CardView;
              break;
            case 'bubble':
              loadedComponent = (await import('@/views/components/bubbles/BubbleView')).BubbleView;
              break;
            case 'galaxy':
              loadedComponent = (await import('@/components/views/GalaxyView')).GalaxyView;
              break;
            default:
              throw new Error(`Unknown view type: ${viewType}`);
          }

          setComponent(() => loadedComponent);
          if (onLoad) onLoad();
        } catch (err) {
          console.error(`Failed to load ${viewType} view:`, err);
          setError(err instanceof Error ? err : new Error(String(err)));
        }
      };

      importComponent();
    }
  }, [isVisible, viewType, Component, onLoad]);

  // 如果发生错误，显示错误信息
  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center text-destructive">
        <p>
          Failed to load {viewType} view: {error.message}
        </p>
      </div>
    );
  }

  // 如果组件尚未加载，显示占位符
  if (!Component) {
    return <div ref={ref as React.RefObject<HTMLDivElement>}>{fallback || defaultFallback}</div>;
  }

  // 渲染加载的组件
  return <Component {...props} />;
};

export default LazyView;
