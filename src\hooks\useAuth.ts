/**
 * 认证钩子
 * 实现离线在线混合模式的用户认证管理
 */

import { trpc } from '@/lib/trpc';
import { Services } from '@/services';
import { useCallback, useEffect, useState } from 'react';

// 认证相关类型定义
interface AuthUser {
  id: string;
  email: string;
  username?: string;
  displayName?: string;
  isVip: boolean;
  vipExpiresAt?: Date;
  created_at: Date;
  updated_at: Date;
}

interface AuthState {
  isAuthenticated: boolean;
  user?: AuthUser;
  token?: string;
  isLoading: boolean;
  error?: string;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  username?: string;
  displayName?: string;
}

interface UseAuthReturn extends AuthState {
  // 认证操作
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
  register: (data: RegisterData) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<{ success: boolean; error?: string }>;

  // 用户操作
  updateProfile: (data: Partial<AuthUser>) => Promise<{ success: boolean; error?: string }>;
  changePassword: (
    oldPassword: string,
    newPassword: string
  ) => Promise<{ success: boolean; error?: string }>;

  // 状态管理
  refresh: () => Promise<void>;
  clearError: () => void;

  // 网络状态
  isOnline: boolean;
  lastSyncTime: Date | null;
}

/**
 * 认证钩子
 * 管理用户认证状态和操作
 */
export const useAuth = (): UseAuthReturn => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: undefined,
    token: undefined,
    isLoading: true,
    error: undefined,
  });

  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // 监听网络状态变化
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 从服务层加载认证状态
  const loadAuthFromStorage = useCallback(async () => {
    try {
      // 优先从服务层加载认证状态
      try {
        const userConfigService = await Services.userConfig();
        const configResult = await userConfigService.getByUserId('default-user');

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          const userConfig = configResult.data[0];
          const authData = userConfig.auth_state ? JSON.parse(userConfig.auth_state) : null;

          if (authData) {
            setAuthState((prev) => ({
              ...prev,
              isAuthenticated: authData.isAuthenticated || false,
              user: authData.user,
              token: authData.token,
              isLoading: false,
            }));
            console.log('[useAuth] Loaded auth state from service layer');
            return;
          }
        }
      } catch (serviceError) {
        console.warn('[useAuth] Failed to load auth from service layer:', serviceError);
      }

      // 回退到 localStorage
      const storedAuth = localStorage.getItem('auth_state');
      if (storedAuth) {
        const parsedAuth = JSON.parse(storedAuth);
        setAuthState((prev) => ({
          ...prev,
          isAuthenticated: parsedAuth.isAuthenticated || false,
          user: parsedAuth.user,
          token: parsedAuth.token,
          isLoading: false,
        }));
        console.log('[useAuth] Loaded auth state from localStorage fallback');
      } else {
        setAuthState((prev) => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('[useAuth] Error loading auth from storage:', error);
      setAuthState((prev) => ({ ...prev, isLoading: false }));
    }
  }, []);

  // 保存认证状态到服务层
  const saveAuthToStorage = useCallback(async (state: AuthState) => {
    try {
      const authData = {
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
      };

      // 优先保存到服务层
      try {
        const userConfigService = await Services.userConfig();
        const configResult = await userConfigService.getByUserId('default-user');

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          const userConfig = configResult.data[0];
          await userConfigService.update(userConfig.id, {
            auth_state: JSON.stringify(authData)
          });
          console.log('[useAuth] Saved auth state to service layer');
        }
      } catch (serviceError) {
        console.warn('[useAuth] Failed to save auth to service layer:', serviceError);
        // 回退到 localStorage
        localStorage.setItem('auth_state', JSON.stringify(authData));
        console.log('[useAuth] Saved auth state to localStorage fallback');
      }
    } catch (error) {
      console.error('[useAuth] Error saving auth to storage:', error);
    }
  }, []);

  // 登录
  const login = useCallback(
    async (credentials: LoginCredentials): Promise<{ success: boolean; error?: string }> => {
      try {
        setAuthState((prev) => ({ ...prev, isLoading: true, error: undefined }));

        // 优先尝试在线登录
        if (isOnline) {
          try {
            console.log('[useAuth] Attempting online login...');
            const result = await trpc.login.mutate({
              email: credentials.email,
              password: credentials.password,
            });

            if (result.success && result.data) {
              const newAuthState: AuthState = {
                isAuthenticated: true,
                user: {
                  id: result.data.user.id,
                  email: result.data.user.email,
                  username: result.data.user.username,
                  displayName: result.data.user.displayName,
                  isVip: result.data.user.isVip || false,
                  vipExpiresAt: result.data.user.vipExpiresAt
                    ? new Date(result.data.user.vipExpiresAt)
                    : undefined,
                  created_at: new Date(result.data.user.created_at),
                  updated_at: new Date(result.data.user.updated_at),
                },
                token: result.data.token,
                isLoading: false,
                error: undefined,
              };

              setAuthState(newAuthState);
              saveAuthToStorage(newAuthState);
              setLastSyncTime(new Date());

              console.log('[useAuth] Online login successful');
              return { success: true };
            }
            throw new Error(result.error || 'Login failed');
          } catch (onlineError) {
            console.warn('[useAuth] Online login failed:', onlineError);

            // 如果在线登录失败，尝试离线验证（如果有缓存的凭据）
            // 这里可以实现离线登录逻辑
            throw onlineError;
          }
        } else {
          // 离线模式下的登录处理
          throw new Error('Offline login not implemented yet');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Login failed';
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        console.error('[useAuth] Login error:', error);
        return { success: false, error: errorMessage };
      }
    },
    [isOnline, saveAuthToStorage]
  );

  // 注册
  const register = useCallback(
    async (data: RegisterData): Promise<{ success: boolean; error?: string }> => {
      try {
        setAuthState((prev) => ({ ...prev, isLoading: true, error: undefined }));

        if (!isOnline) {
          throw new Error('Registration requires internet connection');
        }

        console.log('[useAuth] Attempting registration...');
        const result = await trpc.register.mutate({
          email: data.email,
          password: data.password,
          username: data.username,
          displayName: data.displayName,
        });

        if (result.success && result.data) {
          const newAuthState: AuthState = {
            isAuthenticated: true,
            user: {
              id: result.data.user.id,
              email: result.data.user.email,
              username: result.data.user.username,
              displayName: result.data.user.displayName,
              isVip: result.data.user.isVip || false,
              vipExpiresAt: result.data.user.vipExpiresAt
                ? new Date(result.data.user.vipExpiresAt)
                : undefined,
              created_at: new Date(result.data.user.created_at),
              updated_at: new Date(result.data.user.updated_at),
            },
            token: result.data.token,
            isLoading: false,
            error: undefined,
          };

          setAuthState(newAuthState);
          saveAuthToStorage(newAuthState);
          setLastSyncTime(new Date());

          console.log('[useAuth] Registration successful');
          return { success: true };
        }
        throw new Error(result.error || 'Registration failed');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Registration failed';
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        console.error('[useAuth] Registration error:', error);
        return { success: false, error: errorMessage };
      }
    },
    [isOnline, saveAuthToStorage]
  );

  // 登出
  const logout = useCallback(async (): Promise<{ success: boolean; error?: string }> => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true }));

      // 尝试在线登出
      if (isOnline && authState.token) {
        try {
          await trpc.logout.mutate();
        } catch (error) {
          console.warn('[useAuth] Online logout failed:', error);
        }
      }

      // 清除本地状态
      const newAuthState: AuthState = {
        isAuthenticated: false,
        user: undefined,
        token: undefined,
        isLoading: false,
        error: undefined,
      };

      setAuthState(newAuthState);

      // 清除服务层的认证状态
      try {
        const userConfigService = await Services.userConfig();
        const configResult = await userConfigService.getByUserId('default-user');

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          const userConfig = configResult.data[0];
          await userConfigService.update(userConfig.id, { auth_state: null });
          console.log('[useAuth] Cleared auth state from service layer');
        }
      } catch (serviceError) {
        console.warn('[useAuth] Failed to clear auth from service layer:', serviceError);
        // 回退到 localStorage
        localStorage.removeItem('auth_state');
        console.log('[useAuth] Cleared auth state from localStorage fallback');
      }

      console.log('[useAuth] Logout successful');
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Logout failed';
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      console.error('[useAuth] Logout error:', error);
      return { success: false, error: errorMessage };
    }
  }, [isOnline, authState.token]);

  // 更新用户资料
  const updateProfile = useCallback(
    async (data: Partial<AuthUser>): Promise<{ success: boolean; error?: string }> => {
      try {
        if (!authState.isAuthenticated || !authState.user) {
          throw new Error('User not authenticated');
        }

        setAuthState((prev) => ({ ...prev, isLoading: true, error: undefined }));

        if (isOnline) {
          // TODO: 实现更新用户资料的tRPC端点
          throw new Error('Update profile endpoint not implemented yet');
        }
        throw new Error('Profile update requires internet connection');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        return { success: false, error: errorMessage };
      }
    },
    [authState, isOnline, saveAuthToStorage]
  );

  // 修改密码
  const changePassword = useCallback(
    async (
      oldPassword: string,
      newPassword: string
    ): Promise<{ success: boolean; error?: string }> => {
      try {
        if (!authState.isAuthenticated) {
          throw new Error('User not authenticated');
        }

        if (!isOnline) {
          throw new Error('Password change requires internet connection');
        }

        setAuthState((prev) => ({ ...prev, isLoading: true, error: undefined }));

        // TODO: 实现修改密码的tRPC端点
        throw new Error('Change password endpoint not implemented yet');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Password change failed';
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        return { success: false, error: errorMessage };
      }
    },
    [authState.isAuthenticated, isOnline]
  );

  // 刷新认证状态
  const refresh = useCallback(async () => {
    if (!authState.token || !isOnline) {
      return;
    }

    try {
      // TODO: 实现获取当前用户信息的tRPC端点
      console.warn('[useAuth] Refresh user data not implemented yet');
    } catch (error) {
      console.warn('[useAuth] Refresh failed:', error);
    }
  }, [authState, isOnline, saveAuthToStorage]);

  // 清除错误
  const clearError = useCallback(() => {
    setAuthState((prev) => ({ ...prev, error: undefined }));
  }, []);

  // 初始化
  useEffect(() => {
    loadAuthFromStorage();
  }, [loadAuthFromStorage]);

  // 网络恢复时刷新状态
  useEffect(() => {
    if (isOnline && authState.isAuthenticated && authState.token) {
      refresh();
    }
  }, [isOnline, authState.isAuthenticated, authState.token, refresh]);

  return {
    ...authState,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    refresh,
    clearError,
    isOnline,
    lastSyncTime,
  };
};
