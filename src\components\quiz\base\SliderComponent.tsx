/**
 * Quiz滑块组件
 * 支持多种中医文化样式的滑块组件
 */

import React, { useCallback, useRef, useState, useEffect } from 'react';
import { z } from 'zod';
import { useLanguage } from '@/contexts/LanguageContext';
import { SliderComponentConfigSchema } from '@/types/schema/base';
import {
  BaseQuizComponent,
  BaseQuizComponentProps,
  ComponentState
} from './BaseQuizComponent';

// 类型定义
export type SliderComponentConfig = z.infer<typeof SliderComponentConfigSchema>;

export interface SliderComponentProps extends BaseQuizComponentProps<SliderComponentConfig> {
  value: number;
  onChange: (value: number) => void;
  onChangeComplete?: (value: number) => void;
  disabled?: boolean;
}

interface SliderComponentState extends ComponentState {
  current_value: number;
  is_dragging: boolean;
  hover_value: number | null;
}

/**
 * 滑块组件类
 */
export class SliderComponent extends BaseQuizComponent<
  SliderComponentConfig,
  SliderComponentProps,
  SliderComponentState
> {
  private trackRef = React.createRef<HTMLDivElement>();
  private thumbRef = React.createRef<HTMLDivElement>();
  private isDragging = false;

  extractConfig(props: SliderComponentProps): SliderComponentConfig {
    return props.config;
  }

  getInitialState(): SliderComponentState {
    return {
      is_loading: false,
      is_interactive: !this.props.disabled,
      is_disabled: this.props.disabled || false,
      selected_items: [],
      animation_state: 'idle',
      current_value: this.props.value,
      is_dragging: false,
      hover_value: null
    };
  }

  componentDidMount(): void {
    document.addEventListener('mousemove', this.handleMouseMove);
    document.addEventListener('mouseup', this.handleMouseUp);
    document.addEventListener('touchmove', this.handleTouchMove);
    document.addEventListener('touchend', this.handleTouchEnd);
  }

  componentWillUnmount(): void {
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
    document.removeEventListener('touchmove', this.handleTouchMove);
    document.removeEventListener('touchend', this.handleTouchEnd);
  }

  componentDidUpdate(prevProps: SliderComponentProps): void {
    super.componentDidUpdate(prevProps);
    
    if (prevProps.value !== this.props.value) {
      this.setState({ current_value: this.props.value });
    }
    
    if (prevProps.disabled !== this.props.disabled) {
      this.setState({ 
        is_disabled: this.props.disabled || false,
        is_interactive: !this.props.disabled
      });
    }
  }

  /**
   * 计算滑块位置百分比
   */
  private getValuePercentage(value: number): number {
    const { min, max } = this.config.range;
    return ((value - min) / (max - min)) * 100;
  }

  /**
   * 根据位置计算值
   */
  private getValueFromPosition(clientX: number): number {
    const track = this.trackRef.current;
    if (!track) return this.state.current_value;

    const rect = track.getBoundingClientRect();
    const { min, max, step } = this.config.range;
    
    let percentage;
    if (this.config.style.orientation === 'vertical') {
      percentage = 1 - ((clientX - rect.top) / rect.height);
    } else {
      percentage = (clientX - rect.left) / rect.width;
    }
    
    percentage = Math.max(0, Math.min(1, percentage));
    
    let value = min + percentage * (max - min);
    
    // 应用步长
    if (step > 0) {
      value = Math.round(value / step) * step;
    }
    
    return Math.max(min, Math.min(max, value));
  }

  /**
   * 处理鼠标按下
   */
  private handleMouseDown = (event: React.MouseEvent): void => {
    if (this.state.is_disabled) return;

    event.preventDefault();
    this.isDragging = true;
    this.setState({ is_dragging: true });

    const newValue = this.getValueFromPosition(event.clientX);
    this.updateValue(newValue);

    // 触发触觉反馈
    this.triggerHapticFeedback('light');

    // 发送交互事件
    this.emitInteractionEvent('drag', {
      action: 'start',
      value: newValue,
      position: { x: event.clientX, y: event.clientY }
    });
  };

  /**
   * 处理鼠标移动
   */
  private handleMouseMove = (event: MouseEvent): void => {
    if (!this.isDragging || this.state.is_disabled) return;

    const newValue = this.getValueFromPosition(event.clientX);
    this.updateValue(newValue);
  };

  /**
   * 处理鼠标释放
   */
  private handleMouseUp = (): void => {
    if (!this.isDragging) return;

    this.isDragging = false;
    this.setState({ is_dragging: false });

    // 触发完成回调
    this.props.onChangeComplete?.(this.state.current_value);

    // 发送交互事件
    this.emitInteractionEvent('drag', {
      action: 'end',
      value: this.state.current_value
    });
  };

  /**
   * 处理触摸开始
   */
  private handleTouchStart = (event: React.TouchEvent): void => {
    if (this.state.is_disabled) return;

    const touch = event.touches[0];
    this.isDragging = true;
    this.setState({ is_dragging: true });

    const newValue = this.getValueFromPosition(touch.clientX);
    this.updateValue(newValue);

    // 触发触觉反馈
    this.triggerHapticFeedback('medium');
  };

  /**
   * 处理触摸移动
   */
  private handleTouchMove = (event: TouchEvent): void => {
    if (!this.isDragging || this.state.is_disabled) return;

    event.preventDefault();
    const touch = event.touches[0];
    const newValue = this.getValueFromPosition(touch.clientX);
    this.updateValue(newValue);
  };

  /**
   * 处理触摸结束
   */
  private handleTouchEnd = (): void => {
    if (!this.isDragging) return;

    this.isDragging = false;
    this.setState({ is_dragging: false });

    // 触发完成回调
    this.props.onChangeComplete?.(this.state.current_value);
  };

  /**
   * 更新滑块值
   */
  private updateValue(newValue: number): void {
    if (newValue !== this.state.current_value) {
      this.setState({ current_value: newValue });
      this.props.onChange(newValue);
    }
  }

  /**
   * 处理键盘导航
   */
  protected handleKeyNavigation = (event: React.KeyboardEvent): void => {
    if (!this.personalization.layer5_accessibility?.keyboard_navigation || this.state.is_disabled) {
      return;
    }

    const { min, max, step } = this.config.range;
    const currentValue = this.state.current_value;
    let newValue = currentValue;

    switch (event.key) {
      case 'ArrowLeft':
      case 'ArrowDown':
        event.preventDefault();
        newValue = Math.max(min, currentValue - step);
        break;
      case 'ArrowRight':
      case 'ArrowUp':
        event.preventDefault();
        newValue = Math.min(max, currentValue + step);
        break;
      case 'Home':
        event.preventDefault();
        newValue = min;
        break;
      case 'End':
        event.preventDefault();
        newValue = max;
        break;
      case 'PageDown':
        event.preventDefault();
        newValue = Math.max(min, currentValue - step * 10);
        break;
      case 'PageUp':
        event.preventDefault();
        newValue = Math.min(max, currentValue + step * 10);
        break;
    }

    if (newValue !== currentValue) {
      this.updateValue(newValue);
      this.triggerHapticFeedback('light');
    }
  };

  /**
   * 获取轨道样式类名
   */
  private getTrackStyleClassName(): string {
    const trackStyle = this.config.style.track_style;
    return `quiz-slider-track-${trackStyle}`;
  }

  /**
   * 获取滑块样式类名
   */
  private getThumbStyleClassName(): string {
    const thumbStyle = this.config.style.thumb_style;
    return `quiz-slider-thumb-${thumbStyle}`;
  }

  /**
   * 获取方向样式类名
   */
  private getOrientationClassName(): string {
    const orientation = this.config.style.orientation;
    return `quiz-slider-${orientation}`;
  }

  /**
   * 渲染刻度标记
   */
  private renderTicks(): React.ReactNode {
    if (!this.config.style.show_ticks) return null;

    const { min, max, step } = this.config.range;
    const tickCount = Math.floor((max - min) / step) + 1;
    const ticks = [];

    for (let i = 0; i < tickCount; i++) {
      const value = min + i * step;
      const percentage = this.getValuePercentage(value);
      
      ticks.push(
        <div
          key={i}
          className={`quiz-slider-tick quiz-slider-tick-${this.config.style.tick_style || 'dots'}`}
          style={{
            [this.config.style.orientation === 'vertical' ? 'bottom' : 'left']: `${percentage}%`
          }}
        />
      );
    }

    return <div className="quiz-slider-ticks">{ticks}</div>;
  }

  /**
   * 渲染值显示
   */
  private renderValueDisplay(): React.ReactNode {
    if (!this.config.style.show_value) return null;

    const { unit } = this.config.labels;
    const displayValue = `${this.state.current_value}${unit || ''}`;

    return (
      <div className="quiz-slider-value-display">
        {displayValue}
      </div>
    );
  }

  /**
   * 渲染标签
   */
  private renderLabels(): React.ReactNode {
    if (!this.config.style.show_labels) return null;

    const { language } = this.context || { language: 'zh' };
    const { start_label, end_label } = this.config.labels;

    return (
      <div className="quiz-slider-labels">
        {start_label && (
          <span className="quiz-slider-label-start">
            {start_label[language] || start_label['zh'] || start_label['en'] || ''}
          </span>
        )}
        {end_label && (
          <span className="quiz-slider-label-end">
            {end_label[language] || end_label['zh'] || end_label['en'] || ''}
          </span>
        )}
      </div>
    );
  }

  render(): React.ReactNode {
    const personalizedStyles = this.getPersonalizedStyles();
    const accessibilityProps = this.getAccessibilityProps();
    
    const valuePercentage = this.getValuePercentage(this.state.current_value);
    
    const className = [
      'quiz-slider-component',
      this.getOrientationClassName(),
      this.getTrackStyleClassName(),
      this.state.is_dragging && 'quiz-slider-dragging',
      this.state.is_disabled && 'quiz-slider-disabled',
      this.config.style.gradient_effect && 'quiz-slider-gradient',
      this.config.style.glow_effect && 'quiz-slider-glow',
      this.props.className
    ].filter(Boolean).join(' ');

    const trackStyles: React.CSSProperties = {
      ...personalizedStyles,
      '--slider-color': this.config.style.color_scheme,
      '--slider-progress': `${valuePercentage}%`,
      ...this.props.style
    } as React.CSSProperties;

    return (
      <div
        className={className}
        style={trackStyles}
        {...accessibilityProps}
        role="slider"
        aria-valuemin={this.config.range.min}
        aria-valuemax={this.config.range.max}
        aria-valuenow={this.state.current_value}
        aria-orientation={this.config.style.orientation}
        tabIndex={this.state.is_disabled ? -1 : 0}
        onKeyDown={this.handleKeyNavigation}
      >
        {/* 滑块轨道 */}
        <div
          ref={this.trackRef}
          className="quiz-slider-track"
          onMouseDown={this.handleMouseDown}
          onTouchStart={this.handleTouchStart}
        >
          {/* 进度填充 */}
          <div 
            className="quiz-slider-progress"
            style={{
              [this.config.style.orientation === 'vertical' ? 'height' : 'width']: `${valuePercentage}%`
            }}
          />
          
          {/* 刻度标记 */}
          {this.renderTicks()}
          
          {/* 滑块手柄 */}
          <div
            ref={this.thumbRef}
            className={`quiz-slider-thumb ${this.getThumbStyleClassName()}`}
            style={{
              [this.config.style.orientation === 'vertical' ? 'bottom' : 'left']: `${valuePercentage}%`
            }}
          />
        </div>

        {/* 值显示 */}
        {this.renderValueDisplay()}

        {/* 标签 */}
        {this.renderLabels()}
      </div>
    );
  }

  protected getAriaRole(): string {
    return 'slider';
  }

  protected getAriaLabel(): string {
    const { unit } = this.config.labels;
    return `Slider: ${this.state.current_value}${unit || ''}`;
  }
}

// 使用Context的函数式组件包装器
const SliderComponentWrapper: React.FC<SliderComponentProps> = (props) => {
  const { language } = useLanguage();
  
  return (
    <SliderComponent.contextType.Provider value={{ language }}>
      <SliderComponent {...props} />
    </SliderComponent.contextType.Provider>
  );
};

// 设置Context类型
SliderComponent.contextType = React.createContext({ language: 'zh' });

export default SliderComponentWrapper;
