/**
 * 数据库服务导出文件
 * 简化导入过程
 */
// 导出接口和类型
export * from './DatabaseInterface.js';
export * from './DatabaseFactory.js';
export * from './DatabaseService.js';
export * from './DatabaseInitializer.js';
// 导出适配器
export * from './TursoAdapter.js';
export * from './SQLiteAdapter.js';
export * from './D1Adapter.js';
import { DatabaseType } from './DatabaseFactory.js';
// 导出便捷函数
import { DatabaseService } from './DatabaseService.js';
/**
 * 获取数据库服务实例
 */
export const getDatabaseService = () => {
  return DatabaseService.getInstance();
};
/**
 * 初始化数据库服务
 * @param config 数据库配置
 */
export const initializeDatabaseService = (config) => {
  const service = DatabaseService.getInstance();
  service.initialize(config);
};
/**
 * 执行单个 SQL 查询
 * @param sql SQL 查询或 InStatement 对象
 */
export const executeQuery = async (sql) => {
  return getDatabaseService().executeQuery(sql);
};
/**
 * 在事务中执行一批 SQL 语句
 * @param statements InStatement 对象数组
 * @param mode 事务模式
 */
export const batchStatements = async (statements, mode) => {
  return getDatabaseService().batchStatements(statements, mode);
};
/**
 * 执行多语句 SQL 脚本
 * @param sqlScript 包含多个语句的 SQL 脚本
 */
export const executeScript = async (sqlScript) => {
  return getDatabaseService().executeScript(sqlScript);
};
/**
 * 从指定表中获取所有行
 * @param tableName 表名
 * @param limit 可选的行数限制
 */
export const fetchAllFromTable = async (tableName, limit) => {
  return getDatabaseService().fetchAllFromTable(tableName, limit);
};
/**
 * 初始化数据库
 * @param options 初始化选项
 */
export const initializeDatabase = async (options = {}) => {
  return getDatabaseService().initializeDatabase(options);
};
/**
 * 创建 Turso 数据库配置
 * @param url Turso 数据库 URL
 * @param authToken Turso 认证令牌
 */
export const createTursoConfig = (url, authToken) => {
  return {
    type: DatabaseType.TURSO,
    url,
    authToken,
  };
};
/**
 * 创建 SQLite 数据库配置
 * @param dbPath SQLite 数据库文件路径
 */
export const createSQLiteConfig = (dbPath) => {
  return {
    type: DatabaseType.SQLITE,
    dbPath,
  };
};
/**
 * 创建 D1 数据库配置
 * @param d1Instance D1 数据库实例
 */
export const createD1Config = (d1Instance) => {
  return {
    type: DatabaseType.D1,
    d1Instance,
  };
};
