/**
 * 可翻译实体服务基类
 * 提供统一的多语言业务逻辑
 */

import { BaseService } from './BaseService';
import { TranslatableRepository, TranslatableEntity, Translation, TranslatableFilter } from './TranslatableRepository';
import { DatabaseContext, ServiceResult } from '../types/ServiceTypes';
import { DatabaseService } from './DatabaseService';

// 导入统一的翻译类型定义
import {
  type TranslationInput,
  type TranslatableCreateInput,
  type TranslatableUpdateInput
} from '../../types/schema/translation';
import { type LanguageCode } from '../../types/schema/base';

// 为了向后兼容，重新导出类型
export type { TranslationInput, TranslatableCreateInput, TranslatableUpdateInput };

export abstract class TranslatableService<
  T extends TranslatableEntity,
  CreateInput extends TranslatableCreateInput,
  UpdateInput extends TranslatableUpdateInput
> extends BaseService<T, CreateInput, UpdateInput> {

  protected translatableRepository: TranslatableRepository<T, any, any>;
  protected databaseService: DatabaseService;

  constructor(repository: TranslatableRepository<T, any, any>) {
    super(repository);
    this.translatableRepository = repository;
    this.databaseService = DatabaseService.getInstance();
  }

  /**
   * 获取数据库上下文
   */
  protected async getDatabaseContext(): Promise<DatabaseContext> {
    return await this.databaseService.createContext();
  }

  /**
   * 验证翻译数据
   */
  protected validateTranslations(translations?: TranslationInput[]): string[] {
    const errors: string[] = [];

    if (translations) {
      translations.forEach((translation, index) => {
        if (!translation.languageCode) {
          errors.push(`Translation ${index + 1}: language code is required`);
        }
        if (!translation.translatedName || translation.translatedName.trim().length === 0) {
          errors.push(`Translation ${index + 1}: translated name is required`);
        }
        if (translation.translatedName && translation.translatedName.length > 200) {
          errors.push(`Translation ${index + 1}: translated name must be less than 200 characters`);
        }
        if (translation.translatedDescription && translation.translatedDescription.length > 1000) {
          errors.push(`Translation ${index + 1}: translated description must be less than 1000 characters`);
        }
      });

      // 检查重复的语言代码
      const languageCodes = translations.map(t => t.languageCode);
      const duplicates = languageCodes.filter((code, index) => languageCodes.indexOf(code) !== index);
      if (duplicates.length > 0) {
        errors.push(`Duplicate language codes: ${duplicates.join(', ')}`);
      }
    }

    return errors;
  }

  /**
   * 处理翻译的创建/更新
   */
  protected async handleTranslations(
    entityId: string,
    translations?: TranslationInput[]
  ): Promise<void> {
    if (!translations || translations.length === 0) {
      return;
    }

    const context = await this.getDatabaseContext();

    // 使用事务处理翻译
    await this.databaseService.execute(context.db, async (txContext) => {
      for (const translation of translations) {
        await this.translatableRepository.upsertTranslation(
          txContext,
          entityId,
          translation.languageCode,
          translation.translatedName.trim(),
          translation.translatedDescription?.trim()
        );
      }
    });
  }

  /**
   * 获取本地化实体
   */
  async getLocalized(id: string, languageCode?: LanguageCode): Promise<ServiceResult<T>> {
    try {
      const context = await this.getDatabaseContext();
      const entity = await this.translatableRepository.findByIdWithTranslations(
        context,
        id,
        languageCode
      );

      if (!entity) {
        return this.createErrorResult('NOT_FOUND' as any, 'Entity not found');
      }

      return this.createSuccessResult(entity);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 获取本地化实体列表
   */
  async getLocalizedList(
    filters?: Omit<TranslatableFilter, 'languageCode'>,
    languageCode?: LanguageCode
  ): Promise<ServiceResult<T[]>> {
    try {
      const context = await this.getDatabaseContext();
      const entities = await this.translatableRepository.findManyWithTranslations(
        context,
        { ...filters, languageCode }
      );

      return this.createSuccessResult(entities);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 搜索实体（支持多语言）
   */
  async search(
    searchTerm: string,
    languageCode?: LanguageCode,
    limit: number = 10
  ): Promise<ServiceResult<T[]>> {
    try {
      if (!searchTerm || searchTerm.trim().length === 0) {
        return this.createErrorResult('VALIDATION_ERROR' as any, 'Search term is required');
      }

      const context = await this.getDatabaseContext();
      const entities = await this.translatableRepository.searchWithTranslations(
        context,
        searchTerm.trim(),
        languageCode,
        limit
      );

      return this.createSuccessResult(entities);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 获取实体的所有翻译
   */
  async getTranslations(entityId: string): Promise<ServiceResult<Translation[]>> {
    try {
      const context = await this.getDatabaseContext();
      const translations = await this.translatableRepository.getTranslations(context, entityId);
      return this.createSuccessResult(translations);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 获取特定语言的翻译
   */
  async getTranslation(entityId: string, languageCode: LanguageCode): Promise<ServiceResult<Translation | null>> {
    try {
      const context = await this.getDatabaseContext();
      const translation = await this.translatableRepository.getTranslation(
        context,
        entityId,
        languageCode
      );
      return this.createSuccessResult(translation);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 添加或更新翻译
   */
  async upsertTranslation(
    entityId: string,
    languageCode: LanguageCode,
    translatedName: string,
    translatedDescription?: string
  ): Promise<ServiceResult<void>> {
    try {
      // 验证输入
      if (!translatedName || translatedName.trim().length === 0) {
        return this.createErrorResult('VALIDATION_ERROR' as any, 'Translated name is required');
      }

      if (translatedName.length > 200) {
        return this.createErrorResult('VALIDATION_ERROR' as any, 'Translated name must be less than 200 characters');
      }

      if (translatedDescription && translatedDescription.length > 1000) {
        return this.createErrorResult('VALIDATION_ERROR' as any, 'Translated description must be less than 1000 characters');
      }

      // 验证实体是否存在
      const entityExists = await this.getById(entityId);
      if (!entityExists.success) {
        return this.createErrorResult('NOT_FOUND' as any, 'Entity not found');
      }

      const context = await this.getDatabaseContext();
      await this.translatableRepository.upsertTranslation(
        context,
        entityId,
        languageCode,
        translatedName.trim(),
        translatedDescription?.trim()
      );

      return this.createSuccessResult(undefined);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 删除翻译
   */
  async deleteTranslation(entityId: string, languageCode: LanguageCode): Promise<ServiceResult<void>> {
    try {
      const context = await this.getDatabaseContext();
      await this.translatableRepository.deleteTranslation(context, entityId, languageCode);
      return this.createSuccessResult(undefined);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 批量更新翻译
   */
  async batchUpdateTranslations(
    entityId: string,
    translations: TranslationInput[]
  ): Promise<ServiceResult<void>> {
    try {
      // 验证翻译数据
      const translationErrors = this.validateTranslations(translations);
      if (translationErrors.length > 0) {
        return this.createErrorResult('VALIDATION_ERROR' as any, translationErrors.join('; '));
      }

      // 验证实体是否存在
      const entityExists = await this.getById(entityId);
      if (!entityExists.success) {
        return this.createErrorResult('NOT_FOUND' as any, 'Entity not found');
      }

      await this.handleTranslations(entityId, translations);
      return this.createSuccessResult(undefined);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 获取支持的语言列表
   */
  async getSupportedLanguages(): Promise<ServiceResult<string[]>> {
    try {
      const context = await this.getDatabaseContext();
      const languages = await this.translatableRepository.getSupportedLanguages(context);
      return this.createSuccessResult(languages);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 获取翻译完成度统计
   */
  async getTranslationStats(): Promise<ServiceResult<Array<{
    languageCode: string;
    totalEntities: number;
    translatedEntities: number;
    completionPercentage: number;
  }>>> {
    try {
      const context = await this.getDatabaseContext();
      const stats = await this.translatableRepository.getTranslationStats(context);
      return this.createSuccessResult(stats);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 复制翻译到另一个实体
   */
  async copyTranslations(
    sourceEntityId: string,
    targetEntityId: string,
    languageCodes?: string[]
  ): Promise<ServiceResult<void>> {
    try {
      // 验证源实体和目标实体是否存在
      const sourceExists = await this.getById(sourceEntityId);
      const targetExists = await this.getById(targetEntityId);

      if (!sourceExists.success) {
        return this.createErrorResult('NOT_FOUND' as any, 'Source entity not found');
      }

      if (!targetExists.success) {
        return this.createErrorResult('NOT_FOUND' as any, 'Target entity not found');
      }

      const context = await this.getDatabaseContext();

      // 获取源实体的翻译
      const sourceTranslations = await this.translatableRepository.getTranslations(context, sourceEntityId);

      // 过滤指定的语言代码
      const translationsToCopy = languageCodes
        ? sourceTranslations.filter(t => languageCodes.includes(t.languageCode))
        : sourceTranslations;

      // 复制翻译
      await this.databaseService.execute(context.db, async (txContext) => {
        for (const translation of translationsToCopy) {
          await this.translatableRepository.upsertTranslation(
            txContext,
            targetEntityId,
            translation.languageCode,
            translation.translatedName,
            translation.translatedDescription
          );
        }
      });

      return this.createSuccessResult(undefined);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 重写基类的删除方法，同时删除翻译
   */
  async delete(id: string): Promise<ServiceResult<boolean>> {
    try {
      const context = await this.getDatabaseContext();

      // 使用事务删除实体和翻译
      const result = await this.databaseService.execute(context.db, async (txContext) => {
        // 先删除翻译
        await this.translatableRepository.deleteAllTranslations(txContext, id);

        // 再删除主实体
        return await this.repository.delete(txContext, id);
      });

      return this.createSuccessResult(result);
    } catch (error) {
      return this.handleError(error);
    }
  }
}
