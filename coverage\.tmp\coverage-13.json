{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/boundary/boundary.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 90325, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 90325, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 521, "endOffset": 31683, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 564, "endOffset": 625, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 677, "endOffset": 10061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 732, "endOffset": 3878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 871, "endOffset": 1771, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 964, "endOffset": 1370, "count": 500}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1128, "endOffset": 1342, "count": 2500}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1862, "endOffset": 2288, "count": 1}, {"startOffset": 2234, "endOffset": 2247, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2379, "endOffset": 2741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3928, "endOffset": 7114, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4063, "endOffset": 4470, "count": 1}, {"startOffset": 4423, "endOffset": 4429, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4563, "endOffset": 5073, "count": 1}, {"startOffset": 4872, "endOffset": 4886, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5163, "endOffset": 5832, "count": 1}, {"startOffset": 5765, "endOffset": 5774, "count": 0}, {"startOffset": 5784, "endOffset": 5791, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7164, "endOffset": 10053, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7666, "endOffset": 8490, "count": 1}, {"startOffset": 8381, "endOffset": 8489, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10112, "endOffset": 16227, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10164, "endOffset": 13327, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10309, "endOffset": 10915, "count": 1}, {"startOffset": 10447, "endOffset": 10452, "count": 0}, {"startOffset": 10739, "endOffset": 10742, "count": 0}, {"startOffset": 10871, "endOffset": 10874, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11005, "endOffset": 11522, "count": 1}, {"startOffset": 11385, "endOffset": 11388, "count": 0}, {"startOffset": 11477, "endOffset": 11481, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11612, "endOffset": 12141, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13377, "endOffset": 16219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13514, "endOffset": 13946, "count": 1}, {"startOffset": 13895, "endOffset": 13905, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14036, "endOffset": 14488, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14579, "endOffset": 14956, "count": 1}, {"startOffset": 14789, "endOffset": 14799, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16278, "endOffset": 25022, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16330, "endOffset": 21495, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16470, "endOffset": 17561, "count": 11}, {"startOffset": 16565, "endOffset": 16587, "count": 10}, {"startOffset": 16589, "endOffset": 16982, "count": 2}, {"startOffset": 16982, "endOffset": 17084, "count": 9}, {"startOffset": 17084, "endOffset": 17100, "count": 7}, {"startOffset": 17407, "endOffset": 17418, "count": 5}, {"startOffset": 17419, "endOffset": 17432, "count": 4}, {"startOffset": 17483, "endOffset": 17516, "count": 5}, {"startOffset": 17517, "endOffset": 17520, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17650, "endOffset": 18106, "count": 5}, {"startOffset": 17747, "endOffset": 17766, "count": 3}, {"startOffset": 17843, "endOffset": 17851, "count": 3}, {"startOffset": 17852, "endOffset": 17856, "count": 3}, {"startOffset": 17898, "endOffset": 17926, "count": 2}, {"startOffset": 18033, "endOffset": 18042, "count": 3}, {"startOffset": 18060, "endOffset": 18065, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18194, "endOffset": 18669, "count": 6}, {"startOffset": 18433, "endOffset": 18447, "count": 1}, {"startOffset": 18448, "endOffset": 18451, "count": 5}, {"startOffset": 18508, "endOffset": 18529, "count": 1}, {"startOffset": 18593, "endOffset": 18628, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18607, "endOffset": 18627, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18942, "endOffset": 19264, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19478, "endOffset": 20045, "count": 5}, {"startOffset": 19599, "endOffset": 19614, "count": 4}, {"startOffset": 19616, "endOffset": 19807, "count": 3}, {"startOffset": 19807, "endOffset": 20031, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20300, "endOffset": 20771, "count": 5}, {"startOffset": 20424, "endOffset": 20446, "count": 4}, {"startOffset": 20447, "endOffset": 20462, "count": 3}, {"startOffset": 20463, "endOffset": 20481, "count": 2}, {"startOffset": 20483, "endOffset": 20579, "count": 4}, {"startOffset": 20626, "endOffset": 20651, "count": 3}, {"startOffset": 20653, "endOffset": 20757, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21118, "endOffset": 21483, "count": 6}, {"startOffset": 21337, "endOffset": 21469, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21428, "endOffset": 21448, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21544, "endOffset": 25014, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21685, "endOffset": 22921, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23010, "endOffset": 23893, "count": 2}, {"startOffset": 23557, "endOffset": 23564, "count": 0}], "isBlockCoverage": true}, {"functionName": "timeout", "ranges": [{"startOffset": 23107, "endOffset": 23151, "count": 1}], "isBlockCoverage": true}, {"functionName": "retries", "ranges": [{"startOffset": 23188, "endOffset": 23225, "count": 0}], "isBlockCoverage": false}, {"functionName": "cacheSize", "ranges": [{"startOffset": 23264, "endOffset": 23319, "count": 0}], "isBlockCoverage": false}, {"functionName": "fontSize", "ranges": [{"startOffset": 23357, "endOffset": 23394, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25073, "endOffset": 28539, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25129, "endOffset": 28531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25263, "endOffset": 25905, "count": 2}, {"startOffset": 25535, "endOffset": 25550, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25995, "endOffset": 26533, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26626, "endOffset": 27093, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28590, "endOffset": 31679, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28643, "endOffset": 31671, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28781, "endOffset": 29227, "count": 1}, {"startOffset": 29166, "endOffset": 29186, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29319, "endOffset": 29867, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 29957, "endOffset": 30512, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}