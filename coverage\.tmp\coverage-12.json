{"result": [{"scriptId": "1020", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/setupTests.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5477, "count": 1}], "isBlockCoverage": true}, {"functionName": "console.error", "ranges": [{"startOffset": 751, "endOffset": 939, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1093, "endOffset": 1494, "count": 0}], "isBlockCoverage": false}], "startOffset": 185}, {"scriptId": "1324", "url": "file:///D:/Download/audio-visual/heytcm/mindful-mood-mobile/src/tests/monitoring/monitoring-system.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52924, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 52924, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 524, "endOffset": 19810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 567, "endOffset": 628, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 681, "endOffset": 4347, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 736, "endOffset": 1964, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2015, "endOffset": 3127, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3177, "endOffset": 4339, "count": 1}], "isBlockCoverage": true}, {"functionName": "mockLogFilter", "ranges": [{"startOffset": 3492, "endOffset": 3849, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4398, "endOffset": 7327, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4451, "endOffset": 5455, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4948, "endOffset": 4983, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5505, "endOffset": 6572, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6623, "endOffset": 7319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7378, "endOffset": 12111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7432, "endOffset": 8977, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8811, "endOffset": 8837, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8846, "endOffset": 8869, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9030, "endOffset": 10428, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10478, "endOffset": 12103, "count": 1}], "isBlockCoverage": true}, {"functionName": "mockRecoveryProcess", "ranges": [{"startOffset": 10772, "endOffset": 11832, "count": 1}, {"startOffset": 10888, "endOffset": 11818, "count": 2}, {"startOffset": 11026, "endOffset": 11800, "count": 1}, {"startOffset": 11425, "endOffset": 11778, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12164, "endOffset": 15866, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12217, "endOffset": 13649, "count": 1}, {"startOffset": 13488, "endOffset": 13639, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13699, "endOffset": 14613, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14664, "endOffset": 15858, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15919, "endOffset": 19806, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15972, "endOffset": 17318, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16938, "endOffset": 16965, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17037, "endOffset": 17065, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17368, "endOffset": 18142, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17927, "endOffset": 18130, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18190, "endOffset": 19798, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19670, "endOffset": 19700, "count": 1}], "isBlockCoverage": true}], "startOffset": 185}]}