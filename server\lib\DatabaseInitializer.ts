// Database Initializer for local SQLite database
import * as fs from 'node:fs';
import * as path from 'node:path';
import { executeSqlScript, executeTursoQuery } from './localTursoService.js';

/**
 * Type for logger function
 */
type LoggerFunction = (message: string) => void;

/**
 * Class to handle database initialization
 */
export class DatabaseInitializer {
  private dbPath: string;
  private seedsDir: string;
  private forceReinit: boolean;
  private log: LoggerFunction;

  /**
   * Constructor
   * @param {string} dbPath - Path to the database file
   * @param {string} seedsDir - Path to the seeds directory
   * @param {boolean} forceReinit - Whether to force reinitialization even if DB exists
   * @param {function} logger - Logger function
   */
  constructor(
    dbPath: string,
    seedsDir: string,
    forceReinit = false,
    logger: LoggerFunction = console.log
  ) {
    this.dbPath = dbPath;
    this.seedsDir = seedsDir;
    this.forceReinit = forceReinit;
    this.log = logger;
  }

  /**
   * Initialize the database
   * @returns {Promise<boolean>} - Whether initialization was successful
   */
  async initialize(): Promise<boolean> {
    this.log('Initializing local SQLite database...');

    // Check if the database file exists
    const dbExists = fs.existsSync(this.dbPath);

    if (dbExists && !this.forceReinit) {
      this.log('Using existing database file');
      return true;
    }

    if (dbExists && this.forceReinit) {
      this.log('Removing existing database file to force fresh initialization...');
      fs.unlinkSync(this.dbPath);
      this.log('Database file removed.');
    }

    this.log('Database file does not exist. Creating and initializing...');

    try {
      // Disable foreign keys
      await executeTursoQuery('PRAGMA foreign_keys=OFF;');
      await executeTursoQuery('PRAGMA ignore_check_constraints=ON;');
      this.log('Disabled foreign keys and constraints for initialization');

      // 1. Schema (Table Structure)
      this.log('\n=== Executing Schema Files ===');
      await this.executeSqlFile(path.join(this.seedsDir, 'schema/init.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'schema/update_emotion_selections.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'schema/update_mood_entries.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'schema/update_emoji_items.sql'));

      // 2. Configuration Data
      this.log('\n=== Executing Configuration Files ===');

      // Load emotions data
      await this.executeSqlFile(path.join(this.seedsDir, 'config/emotions.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'config/update_emotions.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'config/update_tertiary_emotions.sql'));

      // Load emotion data sets
      await this.executeSqlFile(path.join(this.seedsDir, 'config/create_emotion_data_sets.sql'));
      await this.executeSqlFile(
        path.join(this.seedsDir, 'config/populate_emotion_data_set_emotions.sql')
      );

      // Load emoji sets
      await this.executeSqlFile(path.join(this.seedsDir, 'config/emoji_sets.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'config/additional_emoji_sets.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'config/image_emoji_sets.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'config/animated_emoji_sets.sql'));

      // Load translations
      await this.executeSqlFile(path.join(this.seedsDir, 'config/emotion_translations.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'config/ui_labels.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'config/ui_label_translations.sql'));

      // Load tags
      await this.executeSqlFile(path.join(this.seedsDir, 'config/tags.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'config/tag_translations.sql'));

      // Load app settings
      await this.executeSqlFile(path.join(this.seedsDir, 'config/app_settings.sql'));

      // Set database version
      await this.executeSqlFile(path.join(this.seedsDir, 'config/version.sql'));

      // 3. Test Data
      this.log('\n=== Executing Test Data Files ===');

      // Load test user data
      await this.executeSqlFile(path.join(this.seedsDir, 'test/users.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'test/user_preferences.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'test/user_streaks.sql'));

      // Load test mood entries
      await this.executeSqlFile(path.join(this.seedsDir, 'test/mood_entries.sql'));
      await this.executeSqlFile(path.join(this.seedsDir, 'test/mood_entry_tags.sql'));

      // Re-enable foreign keys and constraints
      await executeTursoQuery('PRAGMA ignore_check_constraints=OFF;');
      await executeTursoQuery('PRAGMA foreign_keys=ON;');
      this.log('Re-enabled foreign keys and constraints');

      this.log('\nDatabase initialization completed successfully');
      return true;
    } catch (error) {
      this.log(
        `Failed to initialize database: ${error instanceof Error ? error.message : String(error)}`
      );
      if (error instanceof Error && error.stack) {
        this.log(error.stack);
      }
      return false;
    }
  }

  /**
   * Execute a SQL file
   * @param {string} filePath - Path to the SQL file
   * @returns {Promise<boolean>} - Whether execution was successful
   */
  async executeSqlFile(filePath: string): Promise<boolean> {
    try {
      this.log(`Executing SQL file: ${filePath}`);
      if (!fs.existsSync(filePath)) {
        this.log(`WARNING: File not found: ${filePath}`);
        return false;
      }

      const sql = fs.readFileSync(filePath, 'utf8');
      await executeSqlScript(sql);
      this.log(`Successfully executed: ${filePath}`);
      return true;
    } catch (error) {
      this.log(
        `WARNING: Error executing ${filePath}: ${error instanceof Error ? error.message : String(error)}`
      );
      // Continue execution despite errors
      return false;
    }
  }
}
