# 在线服务模块 (简化版)

这个模块提供了与云端服务器交互的基础功能。认证、数据同步等复杂业务逻辑现在通过tRPC直接与服务端通信。

## 架构变更说明

### ✅ 保留的服务

#### 1. ApiClientService
- **功能**: 提供统一的tRPC客户端接口
- **特性**: 
  - 支持 tRPC 调用
  - 自动重试机制
  - 请求/响应拦截器
  - 错误处理和日志记录
  - 认证头管理

#### 2. NetworkStatusService
- **功能**: 网络状态监控
- **特性**:
  - 实时网络状态检测
  - 连接类型识别
  - 网络变化事件
  - 连接质量评估

### ❌ 已移除的服务

以下服务已移除，因为相应功能现在在服务端实现：

#### AuthService
- **移除原因**: 认证逻辑应该在服务端处理，客户端只需要管理认证状态
- **替代方案**: 直接调用服务端的 `auth.*` tRPC 端点
- **服务端实现**: `server/lib/services/AuthService.ts`

#### CloudDataService  
- **移除原因**: 数据操作通过tRPC直接调用，无需客户端封装
- **替代方案**: 直接调用服务端的各种 tRPC 端点
- **服务端实现**: 通过 tRPC router 提供所有数据操作

#### OnlineSyncService
- **移除原因**: 复杂的同步逻辑应该在服务端统一处理
- **替代方案**: 调用服务端的 `sync.performFullSync` tRPC 端点
- **服务端实现**: `server/lib/services/SyncService.ts`

## 新的使用方式

### 认证操作
```typescript
// 旧方式 (已移除)
// const authService = OnlineServices.auth;
// await authService.login(credentials);

// 新方式 (通过tRPC)
const apiClient = OnlineServices.api;
const result = await apiClient.call('auth.login', credentials);
```

### 数据操作
```typescript
// 旧方式 (已移除)
// const cloudService = OnlineServices.cloud;
// await cloudService.getMoodEntries(userId, query);

// 新方式 (通过tRPC)
const apiClient = OnlineServices.api;
const entries = await apiClient.call('mood.getMoodEntries', { userId, query });
```

### 数据同步
```typescript
// 旧方式 (已移除)
// const syncService = OnlineServices.sync;
// await syncService.performFullSync(userId);

// 新方式 (通过tRPC)
const apiClient = OnlineServices.api;
const result = await apiClient.call('sync.performFullSync', { userId });
```

## 简化的服务工厂

OnlineServiceFactory 现在只管理基础服务：

```typescript
// 初始化
await initializeOnlineServices({
  baseUrl: 'https://your-server.com',
  timeout: 30000,
  enableLogging: true
});

// 使用
const networkService = OnlineServices.network;
const apiClient = OnlineServices.api;
```

## 健康检查

简化的健康检查只检查网络和API连接：

```typescript
const health = await checkOnlineServicesHealth();
console.log(health); 
// { network: true, api: true, overall: true }
```

## 迁移指南

### 1. 更新导入
```typescript
// 移除这些导入
// import { AuthService, CloudDataService, OnlineSyncService } from '@/services/online';

// 只保留这些
import { NetworkStatusService, ApiClientService } from '@/services/online';
```

### 2. 更新服务调用
所有业务逻辑调用都应该通过 ApiClientService 的 tRPC 客户端进行：

```typescript
const apiClient = OnlineServices.api;

// 认证
await apiClient.call('auth.login', { email, password });
await apiClient.call('auth.register', userData);

// 数据操作
await apiClient.call('mood.createMoodEntry', entryData);
await apiClient.call('mood.getMoodEntries', { userId, query });

// 同步
await apiClient.call('sync.performFullSync', { userId });

// 分析
await apiClient.call('analytics.getMoodAnalytics', { userId, query });
```

### 3. 状态管理
认证状态等应该通过应用级状态管理（如 Context 或 Redux）来处理，而不是依赖已移除的服务。

## 优势

1. **简化架构**: 减少客户端复杂性，业务逻辑集中在服务端
2. **类型安全**: tRPC 提供端到端的类型安全
3. **统一接口**: 所有服务端功能通过统一的 tRPC 接口访问
4. **更好的维护性**: 减少客户端和服务端的代码重复
5. **安全性**: 敏感操作（如认证）在服务端处理，更加安全

## 注意事项

- 确保所有使用已移除服务的代码都已更新
- 认证状态管理需要在应用层面重新实现
- 网络错误处理现在主要通过 ApiClientService 进行
- 离线功能需要与本地数据库服务配合实现
