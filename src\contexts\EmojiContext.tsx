// src/contexts/EmojiContext.tsx
import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import {
  EmojiSet,
  EmojiItem
} from '@/types';
import { Services } from '@/services';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDatabaseContext } from '@/contexts/DatabaseContext';
import InitializationManager, { INITIALIZATION_KEYS } from '@/lib/InitializationManager';

interface EmojiContextType {
  availableEmojiSets: EmojiSet[];
  activeEmojiSet: EmojiSet | null;
  setActiveSet: (setId: string) => Promise<void>;
  getEmoji: (emotionId: string, fallbackEmoji?: string) => string;
  getEmojiItem: (emotionId: string, fallbackEmoji?: string) => EmojiItem;
  getEmojiItemForEmotionId: (emotionId: string, fallbackEmoji?: string) => EmojiItem;
  isLoading: boolean;
}

const EmojiContext = createContext<EmojiContextType | undefined>(undefined);

export const EmojiProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { language } = useLanguage();
  const { isInitialized } = useDatabaseContext();
  const [availableEmojiSets, setAvailableEmojiSets] = useState<EmojiSet[]>([]);
  const [activeEmojiSet, setActiveEmojiSet] = useState<EmojiSet | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // 加载表情集数据
  useEffect(() => {
    const loadEmojiData = async () => {
      const initManager = InitializationManager.getInstance();

      // 使用初始化管理器防止重复加载
      return await initManager.getOrCreateInitialization(
        INITIALIZATION_KEYS.EMOJI_CONTEXT,
        async () => {
          setIsLoading(true);
          try {
            // 注意：EmojiSet和EmojiItem服务已deprecated，暂时设置默认状态
            console.warn('[EmojiContext] EmojiSet and EmojiItem services are deprecated');

            // 设置默认的表情集数据，避免应用崩溃
            const defaultEmojiSet = {
              id: 'default_emoji_set',
              name: 'Default Emojis',
              description: 'Default emoji set for emotions',
              is_default: true,
              is_premium: false,
              created_at: new Date().toISOString(),
              items: [
                { id: 'happy', emoji_set_id: 'default_emoji_set', emotion_id: 'happy', unicode: '😊', created_at: new Date().toISOString() },
                { id: 'sad', emoji_set_id: 'default_emoji_set', emotion_id: 'sad', unicode: '😢', created_at: new Date().toISOString() },
                { id: 'angry', emoji_set_id: 'default_emoji_set', emotion_id: 'angry', unicode: '😠', created_at: new Date().toISOString() },
                { id: 'excited', emoji_set_id: 'default_emoji_set', emotion_id: 'excited', unicode: '🤩', created_at: new Date().toISOString() },
                { id: 'calm', emoji_set_id: 'default_emoji_set', emotion_id: 'calm', unicode: '😌', created_at: new Date().toISOString() },
                { id: 'anxious', emoji_set_id: 'default_emoji_set', emotion_id: 'anxious', unicode: '😰', created_at: new Date().toISOString() },
              ]
            };

            setAvailableEmojiSets([defaultEmojiSet]);
            setActiveEmojiSet(defaultEmojiSet);

            return; // 跳过deprecated服务调用
      } catch (error) {
        console.error("Failed to load emoji data:", error);
        console.error("Error details:", error instanceof Error ? error.message : String(error));

        // 设置默认状态，因为EmojiSet服务已deprecated
        console.warn('[EmojiContext] Using fallback emoji data due to deprecated services');
        setAvailableEmojiSets([]);
        setActiveEmojiSet(null);
          } finally {
            setIsLoading(false);
          }
        }
      );
    };

    // 只有当数据库和语言都准备好时才加载数据
    if (language && isInitialized) {
      console.log('[EmojiContext] Database and language ready, loading emoji data...');
      loadEmojiData();
    } else {
      console.log(`[EmojiContext] Waiting for prerequisites: language=${!!language}, database=${isInitialized}`);
    }
  }, [language, isInitialized]);

  // 设置活动表情集
  const setActiveSet = useCallback(async (setId: string) => {
    setIsLoading(true);
    try {
      console.warn('[EmojiContext] setActiveSet called but EmojiSet services are deprecated');

      // 从可用的表情集中查找指定的表情集
      const targetSet = availableEmojiSets.find(set => set.id === setId);
      if (targetSet) {
        setActiveEmojiSet(targetSet);
      } else {
        console.warn(`[EmojiContext] Emoji set ${setId} not found in available sets`);
      }
    } catch (error) {
      console.error(`Failed to set active emoji set to ${setId}:`, error);
    } finally {
      setIsLoading(false);
    }
  }, [availableEmojiSets]);

  // 获取情绪对应的表情字符串
  const getEmoji = useCallback((emotionId: string, fallbackEmoji = '❓'): string => {
    if (!activeEmojiSet || !activeEmojiSet.items) return fallbackEmoji;

    // 处理 items 可能是数组或对象的情况
    let emojiItem: EmojiItem | undefined;
    if (Array.isArray(activeEmojiSet.items)) {
      emojiItem = activeEmojiSet.items.find(item => item.emotion_id === emotionId);
    } else {
      // 如果是对象，查找对应的表情项
      emojiItem = Object.values(activeEmojiSet.items).find(item => item.emotion_id === emotionId);
    }

    return emojiItem?.unicode || fallbackEmoji;
  }, [activeEmojiSet]);

  // 获取情绪对应的表情项
  const getEmojiItem = useCallback((emotionId: string, fallbackEmoji = '❓'): EmojiItem => {
    try {
      if (!activeEmojiSet || !activeEmojiSet.items) {
        return {
          id: `fallback-${emotionId}`,
          emoji_set_id: 'fallback',
          emotion_id: emotionId,
          unicode: fallbackEmoji,
          created_at: new Date().toISOString()
        };
      }

      // 处理 items 可能是数组或对象的情况
      let emojiItem: EmojiItem | undefined;
      if (Array.isArray(activeEmojiSet.items)) {
        emojiItem = activeEmojiSet.items.find(item => item.emotion_id === emotionId);
      } else {
        // 如果是对象，查找对应的表情项
        emojiItem = Object.values(activeEmojiSet.items).find(item => item.emotion_id === emotionId);
      }

      if (emojiItem) {
        return {
          ...emojiItem,
          unicode: emojiItem.unicode || fallbackEmoji
        };
      }

      return {
        id: `fallback-${emotionId}`,
        emoji_set_id: activeEmojiSet.id,
        emotion_id: emotionId,
        unicode: fallbackEmoji,
        created_at: new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error getting emoji item for emotion ${emotionId}:`, error);
      return {
        id: `fallback-${emotionId}`,
        emoji_set_id: 'fallback',
        emotion_id: emotionId,
        unicode: fallbackEmoji,
        created_at: new Date().toISOString()
      };
    }
  }, [activeEmojiSet]);

  // 获取情绪对应的表情项（别名，保持向后兼容）
  const getEmojiItemForEmotionId = useCallback((emotionId: string, fallbackEmoji = '❓'): EmojiItem => {
    return getEmojiItem(emotionId, fallbackEmoji);
  }, [getEmojiItem]);

  return (
    <EmojiContext.Provider value={{
      availableEmojiSets,
      activeEmojiSet,
      setActiveSet,
      getEmoji,
      getEmojiItem,
      getEmojiItemForEmotionId,
      isLoading
    }}>
      {children}
    </EmojiContext.Provider>
  );
};

// 添加 displayName 以支持 Hot Reload
EmojiProvider.displayName = 'EmojiProvider';

export const useEmoji = (): EmojiContextType => {
  const context = useContext(EmojiContext);
  if (context === undefined) {
    throw new Error('useEmoji must be used within an EmojiProvider');
  }
  return context;
};
