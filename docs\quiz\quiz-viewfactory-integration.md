# Quiz组件ViewFactory集成文档

## 🎯 集成目标

将Quiz系统的专用组件完全集成到现有的ViewFactory架构中，实现统一的组件创建、配置和渲染机制。

## 🏗️ ViewFactory扩展架构

### 1. Quiz组件注册系统

```typescript
// 扩展ViewFactory以支持Quiz组件
class QuizEnhancedViewFactory extends ViewFactory {
  private quizComponentRegistry: Map<string, QuizComponentConstructor>;
  private quizConfigurationEngine: QuizConfigurationEngine;
  private personalizationApplier: PersonalizationApplier;
  
  constructor() {
    super();
    this.quizComponentRegistry = new Map();
    this.quizConfigurationEngine = new QuizConfigurationEngine();
    this.personalizationApplier = new PersonalizationApplier();
    this.registerQuizComponents();
  }
  
  // 注册Quiz专用组件
  private registerQuizComponents(): void {
    // 核心情绪组件
    this.registerQuizComponent('EmotionWheel', EmotionWheelComponent);
    this.registerQuizComponent('EmotionCards', EmotionCardsComponent);
    this.registerQuizComponent('EmotionBubbles', EmotionBubblesComponent);
    this.registerQuizComponent('EmotionGalaxy', EmotionGalaxyComponent);
    
    // 交互组件
    this.registerQuizComponent('QuizProgressIndicator', QuizProgressIndicatorComponent);
    this.registerQuizComponent('QuizNavigationController', QuizNavigationControllerComponent);
    this.registerQuizComponent('RealtimeInsightDisplay', RealtimeInsightDisplayComponent);
    
    // 结果展示组件
    this.registerQuizComponent('QuizResultPresenter', QuizResultPresenterComponent);
    this.registerQuizComponent('EmotionAnalysisChart', EmotionAnalysisChartComponent);
    this.registerQuizComponent('RecommendationPanel', RecommendationPanelComponent);
  }
  
  // 注册单个Quiz组件
  registerQuizComponent(
    componentType: string,
    componentConstructor: QuizComponentConstructor
  ): void {
    this.quizComponentRegistry.set(componentType, componentConstructor);
  }
  
  // 创建Quiz组件实例
  createQuizComponent(
    componentType: string,
    questionPresentationData: QuestionPresentationData,
    additionalProps?: Record<string, any>
  ): React.ReactElement | null {
    const ComponentConstructor = this.quizComponentRegistry.get(componentType);
    
    if (!ComponentConstructor) {
      console.warn(`Quiz component ${componentType} not found`);
      return this.createFallbackComponent(componentType);
    }
    
    // 生成增强的组件属性
    const enhancedProps = this.generateQuizComponentProps(
      componentType,
      questionPresentationData,
      additionalProps
    );
    
    return React.createElement(ComponentConstructor, enhancedProps);
  }
  
  // 生成Quiz组件属性
  private generateQuizComponentProps(
    componentType: string,
    presentationData: QuestionPresentationData,
    additionalProps?: Record<string, any>
  ): QuizComponentProps {
    // 基础属性
    const baseProps: QuizComponentProps = {
      id: `quiz-${componentType}-${Date.now()}`,
      type: componentType as QuizComponentType,
      data: this.extractComponentData(presentationData, componentType),
      config: this.extractComponentConfig(presentationData, componentType),
      personalization: presentationData.personalized_ui_config,
      ...additionalProps
    };
    
    // 应用个性化配置
    const personalizedProps = this.personalizationApplier.applyToComponent(
      baseProps,
      presentationData.personalized_ui_config
    );
    
    // 添加事件处理器
    return this.addEventHandlers(personalizedProps, presentationData);
  }
}
```

### 2. 配置引擎实现

```typescript
// Quiz配置引擎
class QuizConfigurationEngine {
  // 从呈现数据中提取组件配置
  extractComponentConfig(
    presentationData: QuestionPresentationData,
    componentType: string
  ): any {
    const { personalized_ui_config } = presentationData;
    
    switch (componentType) {
      case 'EmotionWheel':
        return this.extractWheelConfig(personalized_ui_config);
      case 'EmotionCards':
        return this.extractCardConfig(personalized_ui_config);
      case 'EmotionBubbles':
        return this.extractBubbleConfig(personalized_ui_config);
      case 'EmotionGalaxy':
        return this.extractGalaxyConfig(personalized_ui_config);
      default:
        return this.extractDefaultConfig(personalized_ui_config);
    }
  }
  
  // 提取轮盘配置
  private extractWheelConfig(uiConfig: PersonalizedUIConfig): EmotionWheelConfig {
    const wheelConfig = uiConfig.wheel_config || {};
    
    return {
      container_size: wheelConfig.container_size || 400,
      wheel_radius: wheelConfig.wheel_radius || 180,
      inner_radius: wheelConfig.inner_radius || 60,
      sector_gap: wheelConfig.sector_gap || 2,
      emotion_display_mode: wheelConfig.emotion_display_mode || 'hierarchical',
      tier_transition_animation: wheelConfig.tier_transition_animation || 'rotate',
      selection_mode: wheelConfig.selection_mode || 'single',
      hover_preview: wheelConfig.hover_preview ?? true,
      confidence_rating: wheelConfig.confidence_rating ?? false,
      keyboard_navigation: uiConfig.accessibility?.keyboard_navigation ?? true,
      screen_reader_support: uiConfig.accessibility?.screen_reader_support ?? false,
      high_contrast_mode: uiConfig.accessibility?.high_contrast ?? false,
      render_engine: uiConfig.rendering?.render_engine_preferences?.wheel || 'SVG',
      animation_quality: this.mapPerformanceModeToAnimationQuality(
        uiConfig.rendering?.performance_mode || 'balanced'
      )
    };
  }
  
  // 提取卡片配置
  private extractCardConfig(uiConfig: PersonalizedUIConfig): EmotionCardsConfig {
    const cardConfig = uiConfig.card_config || {};
    
    return {
      card_size: cardConfig.card_size || 'standard',
      cards_per_row: cardConfig.cards_per_row || 2,
      card_spacing: cardConfig.card_spacing || 16,
      show_card_shadows: cardConfig.show_card_shadows ?? true,
      selection_mode: cardConfig.selection_mode || 'single',
      animation_style: cardConfig.animation_style || 'scale',
      content_layout: cardConfig.content_layout || 'icon_text',
      accessibility: {
        keyboard_navigation: uiConfig.accessibility?.keyboard_navigation ?? true,
        high_contrast: uiConfig.accessibility?.high_contrast ?? false,
        large_text: uiConfig.accessibility?.large_text ?? false
      }
    };
  }
  
  // 性能模式到动画质量的映射
  private mapPerformanceModeToAnimationQuality(
    performanceMode: string
  ): 'low' | 'medium' | 'high' {
    switch (performanceMode) {
      case 'performance': return 'low';
      case 'quality': return 'high';
      default: return 'medium';
    }
  }
}
```

### 3. 个性化应用器

```typescript
// 个性化配置应用器
class PersonalizationApplier {
  // 应用个性化配置到组件
  applyToComponent(
    baseProps: QuizComponentProps,
    personalizationConfig: PersonalizedUIConfig
  ): QuizComponentProps {
    const appliedProps = { ...baseProps };
    
    // 应用主题配置
    appliedProps.theme = this.applyThemeConfig(
      baseProps.theme || {},
      personalizationConfig.theme_config
    );
    
    // 应用渲染配置
    appliedProps.renderConfig = this.applyRenderConfig(
      baseProps.renderConfig || {},
      personalizationConfig.rendering
    );
    
    // 应用可访问性配置
    appliedProps.accessibilityConfig = this.applyAccessibilityConfig(
      baseProps.accessibilityConfig || {},
      personalizationConfig.accessibility
    );
    
    return appliedProps;
  }
  
  // 应用主题配置
  private applyThemeConfig(
    baseTheme: ThemeConfig,
    themeConfig?: ThemeConfig
  ): ThemeConfig {
    if (!themeConfig) return baseTheme;
    
    return {
      ...baseTheme,
      color_palette: {
        ...baseTheme.color_palette,
        ...themeConfig.color_palette
      },
      font_config: {
        ...baseTheme.font_config,
        ...themeConfig.font_config
      },
      spacing: {
        ...baseTheme.spacing,
        ...themeConfig.spacing
      }
    };
  }
  
  // 应用渲染配置
  private applyRenderConfig(
    baseRenderConfig: RenderConfig,
    renderingConfig?: RenderingConfig
  ): RenderConfig {
    if (!renderingConfig) return baseRenderConfig;
    
    return {
      ...baseRenderConfig,
      engine: renderingConfig.render_engine_preferences?.wheel || baseRenderConfig.engine,
      quality: this.mapPerformanceMode(renderingConfig.performance_mode),
      animations_enabled: renderingConfig.animations_enabled ?? baseRenderConfig.animations_enabled
    };
  }
  
  // 应用可访问性配置
  private applyAccessibilityConfig(
    baseAccessibility: AccessibilityConfig,
    accessibilityConfig?: AccessibilityConfig
  ): AccessibilityConfig {
    if (!accessibilityConfig) return baseAccessibility;
    
    return {
      ...baseAccessibility,
      ...accessibilityConfig
    };
  }
}
```

### 4. 动态组件创建

```typescript
// 动态Quiz组件创建器
class DynamicQuizComponentCreator {
  constructor(
    private viewFactory: QuizEnhancedViewFactory,
    private configurationEngine: QuizConfigurationEngine
  ) {}
  
  // 基于问题类型创建组件
  createComponentForQuestion(
    questionData: QuestionPresentationData,
    userPreferences: UserPreferences
  ): React.ReactElement {
    // 确定最佳组件类型
    const componentType = this.determineOptimalComponentType(
      questionData,
      userPreferences
    );
    
    // 创建组件
    const component = this.viewFactory.createQuizComponent(
      componentType,
      questionData,
      {
        onEmotionSelect: this.handleEmotionSelection,
        onProgressUpdate: this.handleProgressUpdate,
        onInsightGenerated: this.handleInsightGenerated
      }
    );
    
    return component || this.createFallbackComponent(questionData);
  }
  
  // 确定最佳组件类型
  private determineOptimalComponentType(
    questionData: QuestionPresentationData,
    userPreferences: UserPreferences
  ): string {
    // 用户偏好优先
    if (userPreferences.preferred_view_type) {
      return this.mapViewTypeToComponent(userPreferences.preferred_view_type);
    }
    
    // 基于问题类型推荐
    const questionType = questionData.primary_interaction_type;
    switch (questionType) {
      case 'EMOTION_WHEEL_SELECT':
        return 'EmotionWheel';
      case 'EMOTION_CARD_SELECT':
        return 'EmotionCards';
      case 'EMOTION_BUBBLE_SELECT':
        return 'EmotionBubbles';
      case 'EMOTION_GALAXY_SELECT':
        return 'EmotionGalaxy';
      default:
        return 'EmotionWheel'; // 默认选择
    }
  }
  
  // 视图类型到组件的映射
  private mapViewTypeToComponent(viewType: string): string {
    const mapping: Record<string, string> = {
      'wheel': 'EmotionWheel',
      'card': 'EmotionCards',
      'bubble': 'EmotionBubbles',
      'galaxy': 'EmotionGalaxy'
    };
    
    return mapping[viewType] || 'EmotionWheel';
  }
  
  // 事件处理器
  private handleEmotionSelection = (emotionId: string, metadata?: any) => {
    // 处理情绪选择事件
    console.log('Emotion selected:', emotionId, metadata);
  };
  
  private handleProgressUpdate = (progress: ProgressInfo) => {
    // 处理进度更新事件
    console.log('Progress updated:', progress);
  };
  
  private handleInsightGenerated = (insight: RealtimeInsight) => {
    // 处理实时洞察事件
    console.log('Insight generated:', insight);
  };
}
```

### 5. 组件生命周期管理

```typescript
// Quiz组件生命周期管理器
class QuizComponentLifecycleManager {
  private activeComponents: Map<string, QuizComponentInstance> = new Map();
  private componentStates: Map<string, ComponentState> = new Map();
  
  // 注册组件实例
  registerComponent(
    componentId: string,
    component: QuizComponentInstance
  ): void {
    this.activeComponents.set(componentId, component);
    this.componentStates.set(componentId, component.getState());
    
    // 设置状态监听
    component.onStateChange((newState) => {
      this.componentStates.set(componentId, newState);
      this.handleStateChange(componentId, newState);
    });
  }
  
  // 卸载组件
  unregisterComponent(componentId: string): void {
    const component = this.activeComponents.get(componentId);
    if (component) {
      component.cleanup();
      this.activeComponents.delete(componentId);
      this.componentStates.delete(componentId);
    }
  }
  
  // 更新组件配置
  updateComponentConfig(
    componentId: string,
    newConfig: any
  ): void {
    const component = this.activeComponents.get(componentId);
    if (component) {
      component.updateConfig(newConfig);
    }
  }
  
  // 处理状态变化
  private handleStateChange(
    componentId: string,
    newState: ComponentState
  ): void {
    // 状态变化处理逻辑
    if (newState.errorState) {
      this.handleComponentError(componentId, newState.errorState);
    }
    
    if (newState.selectedItems.length > 0) {
      this.handleSelectionChange(componentId, newState.selectedItems);
    }
  }
  
  // 处理组件错误
  private handleComponentError(
    componentId: string,
    errorState: ErrorState
  ): void {
    console.error(`Component ${componentId} error:`, errorState);
    
    // 错误恢复策略
    const component = this.activeComponents.get(componentId);
    if (component && errorState.recoverable) {
      component.recover();
    }
  }
  
  // 处理选择变化
  private handleSelectionChange(
    componentId: string,
    selectedItems: string[]
  ): void {
    // 通知其他相关组件
    this.notifyRelatedComponents(componentId, selectedItems);
  }
  
  // 通知相关组件
  private notifyRelatedComponents(
    sourceComponentId: string,
    selectedItems: string[]
  ): void {
    this.activeComponents.forEach((component, componentId) => {
      if (componentId !== sourceComponentId && component.isRelated(sourceComponentId)) {
        component.handleRelatedSelection(selectedItems);
      }
    });
  }
}
```

### 6. 使用示例

```typescript
// Quiz页面组件使用示例
const QuizSessionPage: React.FC<QuizSessionPageProps> = ({ sessionId }) => {
  const [currentQuestion, setCurrentQuestion] = useState<QuestionPresentationData | null>(null);
  const [viewFactory] = useState(() => new QuizEnhancedViewFactory());
  const [componentCreator] = useState(() => new DynamicQuizComponentCreator(viewFactory, new QuizConfigurationEngine()));
  
  // 获取当前问题数据
  const { data: questionData, isLoading } = trpc.quiz.sessions.getCurrentQuestion.useQuery({
    session_id: sessionId
  });
  
  useEffect(() => {
    if (questionData) {
      setCurrentQuestion(questionData);
    }
  }, [questionData]);
  
  // 渲染当前问题组件
  const renderQuestionComponent = () => {
    if (!currentQuestion) return null;
    
    return componentCreator.createComponentForQuestion(
      currentQuestion,
      {
        preferred_view_type: 'wheel', // 从用户配置获取
        accessibility_needs: [],
        performance_preference: 'balanced'
      }
    );
  };
  
  if (isLoading) {
    return <QuizLoadingSpinner />;
  }
  
  return (
    <div className="quiz-session-container">
      {/* 进度指示器 */}
      {currentQuestion && viewFactory.createQuizComponent(
        'QuizProgressIndicator',
        currentQuestion
      )}
      
      {/* 主要问题组件 */}
      <div className="question-component-area">
        {renderQuestionComponent()}
      </div>
      
      {/* 导航控制 */}
      {currentQuestion && viewFactory.createQuizComponent(
        'QuizNavigationController',
        currentQuestion,
        {
          onNext: () => console.log('Next question'),
          onPrevious: () => console.log('Previous question'),
          onSkip: () => console.log('Skip question')
        }
      )}
      
      {/* 实时洞察 */}
      {currentQuestion && viewFactory.createQuizComponent(
        'RealtimeInsightDisplay',
        currentQuestion
      )}
    </div>
  );
};
```

这个集成文档展示了如何将Quiz组件完全集成到ViewFactory架构中，实现统一的组件管理和动态创建机制，为用户提供高度个性化的Quiz体验。
