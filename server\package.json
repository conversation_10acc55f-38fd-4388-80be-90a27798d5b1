{"name": "mindful-mood-server", "version": "1.0.0", "description": "Server implementation for Mindful Mood app with tRPC and Turso on Cloudflare Pages", "type": "module", "scripts": {"dev": "wrangler pages dev --compatibility-date=2023-12-01 .", "build": "tsc", "deploy": "wrangler pages deploy .", "test": "npx vitest run", "test:watch": "npx vitest", "local": "node local-server.js", "local:sqlite": "cross-env DB_TYPE=sqlite node local-server.js", "local:turso": "cross-env DB_TYPE=turso node local-server.js", "local:sqlite:test": "cross-env DB_TYPE=sqlite LOAD_TEST_DATA=true node local-server.js", "local:turso:test": "cross-env DB_TYPE=turso LOAD_TEST_DATA=true node local-server.js", "local:force-init": "cross-env DB_TYPE=sqlite FORCE_INIT=true node local-server.js", "build:watch": "tsc --watch", "test:server": "node test-server.js", "test:server:watch": "tsx watch test-server.js"}, "dependencies": {"@libsql/client": "^0.15.5", "@trpc/server": "^10.45.1", "@types/stripe": "^8.0.417", "stripe": "^18.2.1", "zod": "^3.23.8"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240320.0", "@types/node": "^22.15.14", "cross-env": "^7.0.3", "ts-node": "^10.9.2", "tsx": "^4.7.1", "typescript": "^5.5.3", "vitest": "^3.1.3", "wrangler": "^3.39.0"}}