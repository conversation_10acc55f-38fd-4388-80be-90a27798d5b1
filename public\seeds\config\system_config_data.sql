-- System Configuration Data
-- Core system settings, UI labels, skins, and emoji sets

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- APP SETTINGS - System Configuration
-- ============================================================================

INSERT OR IGNORE INTO app_settings (key, value, description, data_type, is_system) VALUES 
    ('app_version', '2.0.0', 'Current application version', 'string', 1),
    ('default_language', 'en', 'Default application language', 'string', 1),
    ('default_theme', 'light', 'Default application theme', 'string', 1),
    ('sync_interval', '3600', 'Data sync interval in seconds', 'number', 1),
    ('max_offline_entries', '1000', 'Maximum offline entries before sync required', 'number', 1),
    ('quiz_session_timeout', '1800', 'Quiz session timeout in seconds', 'number', 1),
    ('enable_analytics', 'true', 'Enable analytics and tracking', 'boolean', 1),
    ('enable_ai_recommendations', 'true', 'Enable AI-powered recommendations', 'boolean', 1),
    ('max_quiz_sessions_per_day', '10', 'Maximum quiz sessions per day for free users', 'number', 1),
    ('vip_max_quiz_sessions_per_day', '50', 'Maximum quiz sessions per day for VIP users', 'number', 1),
    ('data_retention_days', '365', 'Data retention period in days', 'number', 1),
    ('backup_frequency_hours', '24', 'Backup frequency in hours', 'number', 1);

-- ============================================================================
-- UI LABELS - Internationalization
-- ============================================================================

-- Core application labels
INSERT OR IGNORE INTO ui_labels (key, default_text, description, category) VALUES
    ('app.title', 'Mindful Mood', 'Application title', 'app'),
    ('app.subtitle', 'Your Personal Emotion Tracker', 'Application subtitle', 'app'),
    ('app.home', 'Home', 'Home navigation label', 'navigation'),
    ('app.history', 'History', 'History navigation label', 'navigation'),
    ('app.analytics', 'Analytics', 'Analytics navigation label', 'navigation'),
    ('app.settings', 'Settings', 'Settings navigation label', 'navigation'),
    ('app.profile', 'Profile', 'Profile navigation label', 'navigation');

-- Quiz system labels
INSERT OR IGNORE INTO ui_labels (key, default_text, description, category) VALUES
    ('quiz.start', 'Start Quiz', 'Start quiz button', 'quiz'),
    ('quiz.continue', 'Continue', 'Continue quiz button', 'quiz'),
    ('quiz.complete', 'Complete', 'Complete quiz button', 'quiz'),
    ('quiz.results', 'Results', 'Quiz results label', 'quiz'),
    ('quiz.settings', 'Quiz Settings', 'Quiz settings page title', 'quiz'),
    ('quiz.history', 'Quiz History', 'Quiz history page title', 'quiz'),
    ('quiz.analytics', 'Quiz Analytics', 'Quiz analytics page title', 'quiz'),
    ('quiz.recommendations', 'Recommendations', 'Quiz recommendations section', 'quiz');

-- Common UI elements
INSERT OR IGNORE INTO ui_labels (key, default_text, description, category) VALUES
    ('common.save', 'Save', 'Save button', 'common'),
    ('common.cancel', 'Cancel', 'Cancel button', 'common'),
    ('common.loading', 'Loading...', 'Loading indicator', 'common'),
    ('common.error', 'Error', 'Error label', 'common'),
    ('common.success', 'Success', 'Success label', 'common'),
    ('common.warning', 'Warning', 'Warning label', 'common'),
    ('common.info', 'Information', 'Information label', 'common'),
    ('common.confirm', 'Confirm', 'Confirm button', 'common'),
    ('common.delete', 'Delete', 'Delete button', 'common'),
    ('common.edit', 'Edit', 'Edit button', 'common'),
    ('common.view', 'View', 'View button', 'common'),
    ('common.close', 'Close', 'Close button', 'common');

-- Error messages
INSERT OR IGNORE INTO ui_labels (key, default_text, description, category) VALUES
    ('error.generic', 'An error occurred', 'Generic error message', 'error'),
    ('error.network', 'Network connection error', 'Network error message', 'error'),
    ('error.validation', 'Please check your input', 'Validation error message', 'error'),
    ('error.permission', 'Permission denied', 'Permission error message', 'error'),
    ('error.not_found', 'Resource not found', 'Not found error message', 'error');

-- ============================================================================
-- UI LABEL TRANSLATIONS - Chinese
-- ============================================================================

-- App translations
INSERT OR IGNORE INTO ui_label_translations (id, label_key, language_code, translated_text) VALUES
    ('trans_app_title_zh', 'app.title', 'zh', '正念情绪'),
    ('trans_app_subtitle_zh', 'app.subtitle', 'zh', '您的个人情绪追踪器'),
    ('trans_app_home_zh', 'app.home', 'zh', '首页'),
    ('trans_app_history_zh', 'app.history', 'zh', '历史'),
    ('trans_app_analytics_zh', 'app.analytics', 'zh', '分析'),
    ('trans_app_settings_zh', 'app.settings', 'zh', '设置'),
    ('trans_app_profile_zh', 'app.profile', 'zh', '个人资料');

-- Quiz translations
INSERT OR IGNORE INTO ui_label_translations (id, label_key, language_code, translated_text) VALUES
    ('trans_quiz_start_zh', 'quiz.start', 'zh', '开始测评'),
    ('trans_quiz_continue_zh', 'quiz.continue', 'zh', '继续'),
    ('trans_quiz_complete_zh', 'quiz.complete', 'zh', '完成'),
    ('trans_quiz_results_zh', 'quiz.results', 'zh', '结果'),
    ('trans_quiz_settings_zh', 'quiz.settings', 'zh', '测评设置'),
    ('trans_quiz_history_zh', 'quiz.history', 'zh', '测评历史'),
    ('trans_quiz_analytics_zh', 'quiz.analytics', 'zh', '测评分析'),
    ('trans_quiz_recommendations_zh', 'quiz.recommendations', 'zh', '推荐建议');

-- Common translations
INSERT OR IGNORE INTO ui_label_translations (id, label_key, language_code, translated_text) VALUES
    ('trans_common_save_zh', 'common.save', 'zh', '保存'),
    ('trans_common_cancel_zh', 'common.cancel', 'zh', '取消'),
    ('trans_common_loading_zh', 'common.loading', 'zh', '加载中...'),
    ('trans_common_error_zh', 'common.error', 'zh', '错误'),
    ('trans_common_success_zh', 'common.success', 'zh', '成功'),
    ('trans_common_warning_zh', 'common.warning', 'zh', '警告'),
    ('trans_common_info_zh', 'common.info', 'zh', '信息'),
    ('trans_common_confirm_zh', 'common.confirm', 'zh', '确认'),
    ('trans_common_delete_zh', 'common.delete', 'zh', '删除'),
    ('trans_common_edit_zh', 'common.edit', 'zh', '编辑'),
    ('trans_common_view_zh', 'common.view', 'zh', '查看'),
    ('trans_common_close_zh', 'common.close', 'zh', '关闭');

-- Error translations
INSERT OR IGNORE INTO ui_label_translations (id, label_key, language_code, translated_text) VALUES
    ('trans_error_generic_zh', 'error.generic', 'zh', '发生错误'),
    ('trans_error_network_zh', 'error.network', 'zh', '网络连接错误'),
    ('trans_error_validation_zh', 'error.validation', 'zh', '请检查您的输入'),
    ('trans_error_permission_zh', 'error.permission', 'zh', '权限被拒绝'),
    ('trans_error_not_found_zh', 'error.not_found', 'zh', '未找到资源');

-- ============================================================================
-- SKINS - Theme System
-- ============================================================================

-- Default Light Theme
INSERT OR IGNORE INTO skins (
    id, name, description, category, supported_view_types, supported_render_engines,
    is_premium, is_unlocked, config, created_by
) VALUES
    (
        'default-light',
        'Default Light Theme',
        'Clean and bright default theme suitable for all view types',
        'default',
        '["wheel", "card", "bubble", "galaxy"]',
        '["D3", "SVG", "Canvas", "R3F"]',
        0, 1,
        '{"colors":{"primary":"#3B82F6","secondary":"#64748B","background":"#FFFFFF","surface":"#F8FAFC","text":"#1E293B","accent":"#10B981","success":"#22C55E","warning":"#F59E0B","error":"#EF4444"},"typography":{"font_family":"Inter, sans-serif","font_size_base":"16px","font_weight_normal":"400","font_weight_bold":"600"},"spacing":{"xs":"4px","sm":"8px","md":"16px","lg":"24px","xl":"32px"},"borders":{"radius_sm":"4px","radius_md":"8px","radius_lg":"12px","width":"1px","color":"#E2E8F0"},"shadows":{"sm":"0 1px 2px 0 rgba(0, 0, 0, 0.05)","md":"0 4px 6px -1px rgba(0, 0, 0, 0.1)","lg":"0 10px 15px -3px rgba(0, 0, 0, 0.1)"}}',
        'system'
    );

-- Default Dark Theme
INSERT OR IGNORE INTO skins (
    id, name, description, category, supported_view_types, supported_render_engines,
    is_premium, is_unlocked, config, created_by
) VALUES
    (
        'default-dark',
        'Default Dark Theme',
        'Elegant dark theme with high contrast for better readability',
        'default',
        '["wheel", "card", "bubble", "galaxy"]',
        '["D3", "SVG", "Canvas", "R3F"]',
        0, 1,
        '{"colors":{"primary":"#60A5FA","secondary":"#94A3B8","background":"#0F172A","surface":"#1E293B","text":"#F1F5F9","accent":"#34D399","success":"#10B981","warning":"#F59E0B","error":"#F87171"},"typography":{"font_family":"Inter, sans-serif","font_size_base":"16px","font_weight_normal":"400","font_weight_bold":"600"},"spacing":{"xs":"4px","sm":"8px","md":"16px","lg":"24px","xl":"32px"},"borders":{"radius_sm":"4px","radius_md":"8px","radius_lg":"12px","width":"1px","color":"#334155"},"shadows":{"sm":"0 1px 2px 0 rgba(0, 0, 0, 0.3)","md":"0 4px 6px -1px rgba(0, 0, 0, 0.4)","lg":"0 10px 15px -3px rgba(0, 0, 0, 0.4)"}}',
        'system'
    );

-- Nature Green Theme (Free)
INSERT OR IGNORE INTO skins (
    id, name, description, category, supported_view_types, supported_render_engines,
    is_premium, is_unlocked, config, created_by
) VALUES
    (
        'nature-green',
        'Nature Green',
        'Calming nature-inspired green theme for peaceful mood tracking',
        'nature',
        '["wheel", "card", "bubble"]',
        '["D3", "SVG", "Canvas"]',
        0, 1,
        '{"colors":{"primary":"#059669","secondary":"#6B7280","background":"#F0FDF4","surface":"#ECFDF5","text":"#064E3B","accent":"#10B981","success":"#22C55E","warning":"#F59E0B","error":"#EF4444"},"typography":{"font_family":"Inter, sans-serif","font_size_base":"16px","font_weight_normal":"400","font_weight_bold":"600"},"spacing":{"xs":"4px","sm":"8px","md":"16px","lg":"24px","xl":"32px"},"borders":{"radius_sm":"6px","radius_md":"10px","radius_lg":"14px","width":"1px","color":"#BBF7D0"},"shadows":{"sm":"0 1px 2px 0 rgba(5, 150, 105, 0.1)","md":"0 4px 6px -1px rgba(5, 150, 105, 0.15)","lg":"0 10px 15px -3px rgba(5, 150, 105, 0.2)"}}',
        'system'
    );

-- Ocean Blue Theme (Premium)
INSERT OR IGNORE INTO skins (
    id, name, description, category, supported_view_types, supported_render_engines,
    is_premium, is_unlocked, config, created_by
) VALUES
    (
        'ocean-blue',
        'Ocean Blue',
        'Deep ocean-inspired blue theme with premium 3D effects',
        'premium',
        '["wheel", "card", "bubble", "galaxy"]',
        '["Canvas", "R3F", "WebGL"]',
        1, 0,
        '{"colors":{"primary":"#0EA5E9","secondary":"#64748B","background":"#F0F9FF","surface":"#E0F2FE","text":"#0C4A6E","accent":"#06B6D4","success":"#22C55E","warning":"#F59E0B","error":"#EF4444"},"typography":{"font_family":"Inter, sans-serif","font_size_base":"16px","font_weight_normal":"400","font_weight_bold":"600"},"spacing":{"xs":"4px","sm":"8px","md":"16px","lg":"24px","xl":"32px"},"borders":{"radius_sm":"8px","radius_md":"12px","radius_lg":"16px","width":"1px","color":"#7DD3FC"},"shadows":{"sm":"0 2px 4px 0 rgba(14, 165, 233, 0.1)","md":"0 6px 12px -2px rgba(14, 165, 233, 0.2)","lg":"0 20px 25px -5px rgba(14, 165, 233, 0.3)"},"effects":{"use_3d":true,"depth":"20px","perspective":"1000px","glow":true}}',
        'system'
    );

-- Neon Glow Theme (Premium)
INSERT OR IGNORE INTO skins (
    id, name, description, category, supported_view_types, supported_render_engines,
    is_premium, is_unlocked, config, created_by
) VALUES
    (
        'neon-glow',
        'Neon Glow',
        'Futuristic neon theme with glowing effects and animations',
        'futuristic',
        '["wheel", "bubble", "galaxy"]',
        '["Canvas", "R3F", "WebGL"]',
        1, 0,
        '{"colors":{"primary":"#8B5CF6","secondary":"#6B7280","background":"#0F0F23","surface":"#1A1A2E","text":"#E5E7EB","accent":"#F59E0B","success":"#10B981","warning":"#F59E0B","error":"#F87171"},"typography":{"font_family":"Orbitron, monospace","font_size_base":"16px","font_weight_normal":"400","font_weight_bold":"700"},"spacing":{"xs":"4px","sm":"8px","md":"16px","lg":"24px","xl":"32px"},"borders":{"radius_sm":"2px","radius_md":"4px","radius_lg":"8px","width":"2px","color":"#8B5CF6"},"shadows":{"sm":"0 0 10px rgba(139, 92, 246, 0.3)","md":"0 0 20px rgba(139, 92, 246, 0.4)","lg":"0 0 30px rgba(139, 92, 246, 0.5)"},"effects":{"use_3d":true,"depth":"30px","perspective":"1200px","glow":true,"neon":true,"animations":"enhanced"}}',
        'system'
    );

-- ============================================================================
-- EMOJI SETS
-- ============================================================================

-- Default Unicode Emoji Set
INSERT OR IGNORE INTO emoji_sets (
    id, name, description, type, is_default, is_system, is_unlocked, created_by
) VALUES
    ('default-unicode', 'Default Unicode', 'Standard Unicode emoji set for emotional expression', 'unicode', 1, 1, 1, 'system'),
    ('food-emojis', 'Food Emotions', 'Express emotions through delicious food emojis', 'unicode', 0, 1, 1, 'system'),
    ('weather-emojis', 'Weather Moods', 'Express emotions through weather and nature emojis', 'unicode', 0, 1, 1, 'system'),
    ('animal-emojis', 'Animal Spirits', 'Express emotions through cute animal emojis', 'unicode', 0, 1, 1, 'system'),
    ('activity-emojis', 'Activity Vibes', 'Express emotions through activity and sport emojis', 'unicode', 0, 1, 1, 'system');

-- ============================================================================
-- EMOJI SET TRANSLATIONS
-- ============================================================================

INSERT OR IGNORE INTO emoji_set_translations (emoji_set_id, language_code, translated_name, translated_description) VALUES
    ('default-unicode', 'zh', '默认表情', '标准Unicode表情集，用于情绪表达'),
    ('food-emojis', 'zh', '美食情绪', '用美味的食物表情表达你的情绪'),
    ('weather-emojis', 'zh', '天气心情', '用天气和自然表情表达你的情绪'),
    ('animal-emojis', 'zh', '动物精神', '用可爱的动物表情表达你的情绪'),
    ('activity-emojis', 'zh', '活动氛围', '用活动和运动表情表达你的情绪');

-- ============================================================================
-- 注意：EMOTIONS 数据已移除 (已废弃)
-- ============================================================================
--
-- emotions 表已废弃，新架构使用 quiz_packs → quiz_questions → quiz_question_options
-- 情绪相关内容现在通过Quiz包和问题选项管理

-- ============================================================================
-- 注意：EMOTION TRANSLATIONS 数据已移除 (已废弃)
-- ============================================================================
--
-- emotion_translations 表已废弃，多语言支持现在通过 ui_labels 和 ui_label_translations 实现

-- ============================================================================
-- 注意：EMOJI ITEMS 数据已移除 (已废弃)
-- ============================================================================
--
-- emoji_items 表已废弃，表情符号映射现在通过用户展现配置管理
-- 在 user_presentation_configs 中定义情绪与表情的映射关系

-- 所有emoji_items相关数据已移除，因为该表已废弃

-- ============================================================================
-- 注意：EMOTION DATA SETS 相关数据已移除
-- ============================================================================
--
-- emotion_data_sets 和 emotion_data_set_tiers 表已废弃
-- 新架构使用 quiz_packs → quiz_questions → quiz_question_options
-- 相关的示例数据应该在 quiz_sample_data.sql 中以新格式提供
