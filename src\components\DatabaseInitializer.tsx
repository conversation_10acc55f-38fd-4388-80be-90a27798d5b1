import { useAppLoading } from '@/contexts/AppLoadingContext';
import { useSQLiteDB } from '@/lib/useSqLite';
import { useEffect, useRef, useState } from 'react';

/**
 * Component that initializes the local database as early as possible in the application lifecycle
 * This helps prevent multiple initialization attempts and ensures the database is ready
 * before components that need it are mounted
 *
 * Uses the DatabaseManager service to separate database initialization logic from UI
 */
const DatabaseInitializer: React.FC = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error] = useState<Error | null>(null);
  const initTimestampRef = useRef<number>(Date.now());
  const isMountedRef = useRef<boolean>(true);

  // Get app loading context to update loading state
  const { setLoadingMessage } = useAppLoading();

  // 在组件顶层调用 useSQLiteDB 钩子
  const { isDatabaseInitialised } = useSQLiteDB();

  useEffect(() => {
    // Set up environment info for logging
    const envMode = import.meta.env.MODE || 'unknown';
    const isProd = envMode === 'production';
    const isTest = envMode === 'test';
    const isDev = envMode === 'development';

    console.log(
      `[DatabaseInitializer ${initTimestampRef.current}] Component mounted. Environment: ${envMode} (isProd=${isProd}, isDev=${isDev}, isTest=${isTest})`
    );

    // 如果已经初始化，直接设置为完成
    if (isDatabaseInitialised && !isInitialized) {
      console.log(`[DatabaseInitializer ${initTimestampRef.current}] Database is ready!`);
      setLoadingMessage('Database ready!');
      if (isMountedRef.current) {
        setIsInitialized(true);
      }
      return;
    }

    // 如果还没初始化，设置超时
    if (!isDatabaseInitialised && !isInitialized) {
      console.log(
        `[DatabaseInitializer ${initTimestampRef.current}] Waiting for database initialization...`
      );
      setLoadingMessage('Initializing database...');

      // Set up a safety timeout to prevent hanging
      const safetyTimeout = setTimeout(() => {
        console.log(
          `[DatabaseInitializer ${initTimestampRef.current}] SAFETY TIMEOUT: Database initialization is taking too long. Forcing completion.`
        );
        setLoadingMessage('Database initialization timed out. Proceeding anyway...');
        if (isMountedRef.current) {
          setIsInitialized(true);
        }
      }, 45000); // 45 second safety timeout (increased for complex initialization)

      // Cleanup function
      return () => {
        console.log(
          `[DatabaseInitializer ${initTimestampRef.current}] Component unmounting. Cleanup.`
        );
        isMountedRef.current = false;
        clearTimeout(safetyTimeout);
      };
    }
  }, [isDatabaseInitialised, setLoadingMessage, isInitialized]);

  // 额外的数据库状态监听器
  useEffect(() => {
    if (isDatabaseInitialised && !isInitialized) {
      console.log(
        `[DatabaseInitializer ${initTimestampRef.current}] Database state changed to ready, updating component state...`
      );
      setLoadingMessage('Database ready!');
      setIsInitialized(true);
    }
  }, [isDatabaseInitialised, isInitialized, setLoadingMessage]);

  // Log state changes
  useEffect(() => {
    console.log(
      `[DatabaseInitializer ${initTimestampRef.current}] State updated: isInitialized=${isInitialized}, hasError=${!!error}, dbReady=${isDatabaseInitialised}`
    );

    if (error) {
      console.error(
        `[DatabaseInitializer ${initTimestampRef.current}] Error details:`,
        error.message,
        error.stack
      );
    }
  }, [isInitialized, error, isDatabaseInitialised]);

  // This component doesn't render anything visible
  return null;
};

export default DatabaseInitializer;
