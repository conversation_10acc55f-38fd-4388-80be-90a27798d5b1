/**
 * 在线服务统一导出 (简化版)
 * 提供基础在线服务的便捷访问入口
 * 业务逻辑通过tRPC直接调用服务端
 */

// 主要服务管理器
export { OnlineServices, onlineServices } from './OnlineServices';

// 核心服务
export { NetworkStatusService } from './NetworkStatusService';
export { ApiClientService } from './ApiClientService';

// 复杂业务服务

// 类型导出
export type {
  OnlineServiceConfig,
  TRPCClientType,
  ApiResponse,
  ApiError,
  NetworkStatus,
  AuthToken,
  BatchOperationResult,
} from './types/OnlineServiceTypes';

export type { NetworkStatusListener } from './NetworkStatusService';

export type {
  TrpcRequestInterceptor,
  TrpcResponseInterceptor,
  TrpcErrorInterceptor,
} from './ApiClientService';

// 注意：旧的OnlineServiceFactory已被移除
// 现在使用简化的OnlineServices类来管理服务
// 复杂的Factory模式已被简化为单例模式

// 便捷的初始化函数
export async function initializeOnlineServices(config?: {
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
}): Promise<void> {
  try {
    // 导入OnlineServices类并使用静态方法初始化
    const { OnlineServices } = await import('./OnlineServices');
    await OnlineServices.initialize(config);
    console.log('Online services initialized successfully');
  } catch (error) {
    console.error('Failed to initialize online services:', error);
    throw error;
  }
}

// 便捷的清理函数
export async function cleanupOnlineServices(): Promise<void> {
  try {
    // 导入OnlineServices类并使用静态方法销毁
    const { OnlineServices } = await import('./OnlineServices');
    OnlineServices.destroy();
    console.log('Online services cleaned up successfully');
  } catch (error) {
    console.error('Failed to cleanup online services:', error);
  }
}
