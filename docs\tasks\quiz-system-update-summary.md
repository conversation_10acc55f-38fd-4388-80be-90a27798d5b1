# Quiz系统架构更新总结

## 概述

本次更新将Quiz系统从旧的架构迁移到新的基于服务的架构，实现了数据与展现的分离，提供了更好的可扩展性和维护性。

## 主要更新内容

### 1. 数据库Schema更新 (src/types/schema/base.ts)

#### 新增的Quiz相关Schema：

- **QuizPackSchema**: Quiz包定义，支持多种类型和分类
  - 新增字段：`category`, `quiz_type`, `quiz_style`, `difficulty_level`
  - 支持多语言：`name_localized`, `description_localized`
  - 元数据支持：`tags`, `estimated_duration_minutes`

- **QuizQuestionSchema**: Quiz问题定义
  - 支持多种问题类型：`single_choice`, `multiple_choice`, `emotion_wheel`, `scale_rating`等
  - 层级支持：`tier_level`, `parent_question_id`
  - 配置化：`question_config`, `validation_rules`, `scoring_config`

- **QuizQuestionOptionSchema**: Quiz问题选项
  - 多媒体支持：`media_url`, `media_type`, `media_thumbnail_url`
  - 数值范围：`min_value`, `max_value`, `step_value`
  - 矩阵配置：`matrix_row_id`, `matrix_column_id`

- **QuizSessionSchema**: Quiz会话管理
  - 简化的状态管理：`INITIATED`, `IN_PROGRESS`, `PAUSED`, `COMPLETED`, `ABANDONED`
  - 进度跟踪：`current_question_index`, `completion_percentage`

- **QuizSessionPresentationConfigSchema**: 会话展现配置
  - 个性化级别：`personalization_level`
  - 配置来源：`config_source`

- **QuizAnswerSchema**: 清理后的答案记录
  - 支持多选：`selected_option_ids`
  - 引用展现配置：`session_presentation_config_id`
  - 简化的置信度：`confidence_score` (0-100)

- **QuizResultSchema**: 通用化的结果存储
  - 灵活的结果数据：`result_data` (JSON)
  - AI分析支持：`ai_analysis_status`, `ai_analysis_id`

### 2. 页面组件更新

#### QuizLauncher.tsx
- 更新为使用新的`useQuizPacks`和`useCreateQuizSession` hooks
- 改进的数据结构适配
- 更好的错误处理和加载状态

#### QuizSession.tsx
- 使用新的`useCurrentQuestion`和`useSubmitAnswer` hooks
- 更新的数据转换逻辑以适应新的问题选项结构
- 改进的视图类型判断逻辑

#### QuizSettings.tsx
- 集成快速启动功能
- 使用新的`useEmotionWheelQuiz`和`useTCMAssessmentQuiz` hooks
- 保持原有的6层个性化配置架构

#### QuizResults.tsx
- 更新为使用新的`useQuizResult` hook
- 保持原有的可视化和分析功能

### 3. 架构改进

#### 数据与展现分离
- Quiz包只包含逻辑数据，不包含UI配置
- 展现配置通过独立的配置系统管理
- 会话级别的配置快照确保一致性

#### 类型安全
- 所有新Schema都有对应的TypeScript类型导出
- 使用Zod进行运行时验证

#### 可扩展性
- 支持多种Quiz类型：`emotion_wheel`, `tcm_assessment`, `survey`, `personality_test`等
- 灵活的问题类型系统
- 可配置的评分和验证规则

## 新架构的优势

### 1. 灵活性
- 支持多种Quiz类型和风格
- 可配置的问题和选项类型
- 灵活的结果数据结构

### 2. 可维护性
- 清晰的数据结构分离
- 类型安全的API接口
- 统一的错误处理

### 3. 可扩展性
- 易于添加新的Quiz类型
- 支持复杂的问题依赖关系
- 可扩展的个性化配置

### 4. 用户体验
- 更快的加载速度
- 更好的错误处理
- 一致的UI交互

## 数据迁移注意事项

### 旧数据兼容性
- 需要将旧的`emotion_data_set`数据迁移到新的`quiz_packs`表
- 旧的`quiz_answers`表需要清理和重构
- 展现配置需要从Quiz包中分离出来

### 推荐迁移步骤
1. 备份现有数据
2. 创建新的数据库表结构
3. 编写数据迁移脚本
4. 测试新系统功能
5. 逐步切换到新架构

## 后续工作

### 短期任务
- [ ] 完成数据库迁移脚本
- [ ] 更新剩余的UI组件
- [ ] 完善错误处理和日志记录
- [ ] 添加单元测试

### 中期任务
- [ ] 实现AI分析功能
- [ ] 添加更多Quiz类型
- [ ] 优化性能和缓存
- [ ] 完善文档和示例

### 长期目标
- [ ] 支持实时协作
- [ ] 高级分析和报告
- [ ] 移动端优化
- [ ] 国际化支持

## 技术栈

- **前端**: React, TypeScript, TanStack Query
- **后端**: tRPC, TypeORM
- **数据库**: SQLite (开发), PostgreSQL (生产)
- **验证**: Zod
- **UI**: Tailwind CSS, Radix UI

## 总结

新的Quiz系统架构提供了更好的灵活性、可维护性和可扩展性。通过数据与展现的分离，我们能够支持更多样化的Quiz类型和个性化需求，同时保持代码的清晰和可维护性。

这次更新为未来的功能扩展奠定了坚实的基础，使得系统能够更好地适应不断变化的需求。
