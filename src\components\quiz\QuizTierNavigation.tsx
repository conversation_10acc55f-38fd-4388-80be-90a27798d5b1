/**
 * Quiz层级导航组件
 * 基于新的Quiz组件系统和统一表结构实现
 * 替代原有的TierNavigation，使用quiz_questions和quiz_question_options
 */

import React, { useMemo } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import { QuizQuestion, QuizQuestionOption } from '@/types/schema/base';
import { QuizComponentRenderer } from './core/QuizComponentRenderer';
import { EmotionWheelView } from './special-views/EmotionWheelView';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import './QuizTierNavigation.css';

// 选择路径接口
interface SelectedPath {
  [questionId: string]: QuizQuestionOption;
}

// 组件属性接口
interface QuizTierNavigationProps {
  // 当前问题（包含选项）
  currentQuestion: QuizQuestion & { options: QuizQuestionOption[] };

  // 选择回调
  onSelect: (option: QuizQuestionOption) => void;

  // 返回回调
  onBack?: () => void;

  // 选择路径
  selectedPath?: SelectedPath;

  // Quiz包ID
  packId?: string;

  // 个性化配置
  personalizationConfig?: any;
}

/**
 * Quiz层级导航组件
 * 使用新的组件系统渲染情绪轮盘或其他Quiz界面
 */
const QuizTierNavigation: React.FC<QuizTierNavigationProps> = ({
  currentQuestion,
  onSelect,
  onBack,
  selectedPath,
  packId,
  personalizationConfig
}) => {
  const { t } = useLanguage();
  const { userConfig } = useUserConfig();

  // 处理选项选择
  const handleSelect = async (option: QuizQuestionOption) => {
    // 触觉反馈
    try {
      await Haptics.impact({ style: ImpactStyle.Medium });
    } catch (error) {
      console.log('Haptics not available', error);
    }

    // 调用选择回调
    onSelect(option);
  };

  // 获取当前层级的可用选项
  const availableOptions = useMemo(() => {
    if (!currentQuestion?.options) return [];

    // 如果是第一层级，返回所有选项
    if (currentQuestion.tier_level === 1) {
      return currentQuestion.options;
    }

    // 如果是更高层级，需要根据父选择过滤选项
    if (selectedPath && Object.keys(selectedPath).length > 0) {
      const parentSelections = Object.values(selectedPath);
      const lastParentSelection = parentSelections[parentSelections.length - 1];

      if (lastParentSelection) {
        // 过滤出与父选择相关的选项
        return currentQuestion.options.filter(option => {
          try {
            const metadata = typeof option.metadata === 'string'
              ? JSON.parse(option.metadata)
              : option.metadata;
            return metadata?.parent_option_id === lastParentSelection.id ||
                   metadata?.parent_emotion_id === lastParentSelection.option_value;
          } catch {
            return false;
          }
        });
      }
    }

    return currentQuestion.options;
  }, [currentQuestion, selectedPath]);

  // 根据问题类型选择渲染组件
  const renderQuestionComponent = () => {
    const questionType = currentQuestion.question_type;

    switch (questionType) {
      case 'emotion_wheel':
        return renderEmotionWheel();

      case 'single_choice':
      case 'multiple_choice':
        return renderChoiceQuestion();

      case 'scale_rating':
        return renderScaleQuestion();

      case 'slider':
        return renderSliderQuestion();

      default:
        return renderDefaultQuestion();
    }
  };

  // 渲染情绪轮盘
  const renderEmotionWheel = () => {
    // 转换数据格式以适配EmotionWheelView
    const emotionItems = availableOptions.map(option => ({
      id: option.id,
      name: {
        zh: option.option_text,
        en: option.option_text // 可以从option_text_localized获取
      },
      emoji: getEmojiFromOption(option),
      color: getColorFromOption(option),
      tier_level: currentQuestion.tier_level || 1,
      parent_id: getParentIdFromOption(option)
    }));

    const wheelConfig = {
      size: 300,
      center_radius: 30,
      tier_spacing: 60,
      animation_duration: 300,
      show_labels: true,
      show_emojis: true,
      interactive: true
    };

    return (
      <EmotionWheelView
        emotions={emotionItems}
        config={wheelConfig}
        onSelect={(emotionItem) => {
          const option = availableOptions.find(opt => opt.id === emotionItem.id);
          if (option) {
            handleSelect(option);
          }
        }}
        onBack={onBack}
        personalizationConfig={personalizationConfig}
      />
    );
  };

  // 渲染选择题
  const renderChoiceQuestion = () => {
    return (
      <QuizComponentRenderer
        componentType="selector_component"
        config={{
          id: currentQuestion.id,
          question_text: currentQuestion.question_text,
          options: availableOptions,
          multiple: currentQuestion.question_type === 'multiple_choice',
          layout: 'grid'
        }}
        onSelect={handleSelect}
        onBack={onBack}
        personalizationConfig={personalizationConfig}
      />
    );
  };

  // 渲染量表题
  const renderScaleQuestion = () => {
    return (
      <QuizComponentRenderer
        componentType="rating_component"
        config={{
          id: currentQuestion.id,
          question_text: currentQuestion.question_text,
          options: availableOptions,
          scale_type: 'likert',
          min_value: 1,
          max_value: 5
        }}
        onSelect={handleSelect}
        onBack={onBack}
        personalizationConfig={personalizationConfig}
      />
    );
  };

  // 渲染滑块题
  const renderSliderQuestion = () => {
    const firstOption = availableOptions[0];
    const minValue = firstOption?.min_value || 0;
    const maxValue = firstOption?.max_value || 100;
    const stepValue = firstOption?.step_value || 1;

    return (
      <QuizComponentRenderer
        componentType="slider_component"
        config={{
          id: currentQuestion.id,
          question_text: currentQuestion.question_text,
          min_value: minValue,
          max_value: maxValue,
          step_value: stepValue,
          default_value: minValue
        }}
        onSelect={(value) => {
          // 为滑块创建虚拟选项
          const virtualOption: QuizQuestionOption = {
            id: `${currentQuestion.id}_slider_${value}`,
            question_id: currentQuestion.id,
            option_text: value.toString(),
            option_value: value.toString(),
            option_order: 1,
            is_active: true,
            created_at: new Date(),
            updated_at: new Date()
          };
          handleSelect(virtualOption);
        }}
        onBack={onBack}
        personalizationConfig={personalizationConfig}
      />
    );
  };

  // 渲染默认问题
  const renderDefaultQuestion = () => {
    return (
      <div className="quiz-question-default">
        <h3 className="question-text">{currentQuestion.question_text}</h3>
        <div className="options-grid">
          {availableOptions.map((option) => (
            <button
              key={option.id}
              className="option-button"
              onClick={() => handleSelect(option)}
            >
              {getEmojiFromOption(option)} {option.option_text}
            </button>
          ))}
        </div>
        {onBack && (
          <button className="back-button" onClick={onBack}>
            {t('common.back') || '返回'}
          </button>
        )}
      </div>
    );
  };

  // 辅助函数：从选项中获取emoji
  const getEmojiFromOption = (option: QuizQuestionOption): string => {
    try {
      const metadata = typeof option.metadata === 'string'
        ? JSON.parse(option.metadata)
        : option.metadata;
      return metadata?.emoji || '😊';
    } catch {
      return '😊';
    }
  };

  // 辅助函数：从选项中获取颜色
  const getColorFromOption = (option: QuizQuestionOption): string => {
    try {
      const metadata = typeof option.metadata === 'string'
        ? JSON.parse(option.metadata)
        : option.metadata;
      return metadata?.color || '#3b82f6';
    } catch {
      return '#3b82f6';
    }
  };

  // 辅助函数：从选项中获取父ID
  const getParentIdFromOption = (option: QuizQuestionOption): string | undefined => {
    try {
      const metadata = typeof option.metadata === 'string'
        ? JSON.parse(option.metadata)
        : option.metadata;
      return metadata?.parent_option_id || metadata?.parent_emotion_id;
    } catch {
      return undefined;
    }
  };

  // 渲染进度指示器
  const renderProgressIndicator = () => {
    if (!currentQuestion.tier_level) return null;

    return (
      <div className="tier-progress">
        <span className="tier-label">
          {t('quiz.tier_level') || '层级'} {currentQuestion.tier_level}
        </span>
        <span className="question-title">
          {currentQuestion.question_text}
        </span>
      </div>
    );
  };

  return (
    <div className="quiz-tier-navigation">
      {renderProgressIndicator()}
      <div className="question-content">
        {renderQuestionComponent()}
      </div>
    </div>
  );
};

export default QuizTierNavigation;
