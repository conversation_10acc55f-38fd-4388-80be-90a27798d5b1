import { useUserConfig } from '@/contexts/UserConfigContext';
import type { ColorMode } from '@/types';
import type React from 'react';
import { createContext, useContext } from 'react';

interface ColorModeContextType {
  colorMode: ColorMode;
  setColorMode: (mode: ColorMode) => Promise<void>;
}

const defaultContext: ColorModeContextType = {
  colorMode: 'warm',
  setColorMode: async () => {},
};

const ColorModeContext = createContext<ColorModeContextType>(defaultContext);

export const useColorMode = () => useContext(ColorModeContext);

interface ColorModeProviderProps {
  children: React.ReactNode;
}

/**
 * 颜色模式提供者组件
 * 使用UserConfigContext来管理颜色模式
 */
export const ColorModeProvider: React.FC<ColorModeProviderProps> = ({ children }) => {
  // 使用UserConfigContext
  const { userConfig, isLoading, setColorMode: setUserConfigColorMode } = useUserConfig();

  // 从UserConfig获取颜色模式，如果userConfig为null则使用默认值
  const colorMode = userConfig?.color_mode || 'warm';

  // 设置颜色模式
  const setColorMode = async (mode: ColorMode) => {
    // 只有在userConfig存在时才更新数据库
    if (userConfig && setUserConfigColorMode) {
      try {
        await setUserConfigColorMode(mode);
      } catch (error) {
        console.error('Failed to update color mode in database:', error);
      }
    }

    // 为了向后兼容，同时更新localStorage
    try {
      localStorage.setItem('colorMode', JSON.stringify(mode));
    } catch (error) {
      console.error('Failed to save color mode to localStorage:', error);
    }
  };

  // 如果还在加载中，使用默认值但仍然渲染children
  // 这样可以避免阻塞整个应用的渲染
  return (
    <ColorModeContext.Provider value={{ colorMode, setColorMode }}>
      {children}
    </ColorModeContext.Provider>
  );
};
