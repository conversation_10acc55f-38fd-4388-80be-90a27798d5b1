# VIP and Unlock System

This directory contains the VIP subscription and content unlock system extracted from `server/public/seeds/schema/full.sql` and organized for the client-side application.

## 📁 File Structure

```
public/seeds/
├── schema/
│   └── vip_unlock_system.sql          # Database schema for VIP and unlock tables
├── config/
│   ├── vip_plans.json                 # VIP subscription plans configuration
│   ├── vip_features.json              # VIP features definitions
│   └── insert_vip_data.sql            # SQL script to insert VIP data
└── test-user-data/
    ├── user_unlocks.json              # Sample unlock data for testing
    └── user_subscriptions.json        # Sample subscription history for testing
```

## 🗄️ Database Tables

### Core VIP Tables

1. **`user_skin_unlocks`** - Tracks which skins users have unlocked
2. **`user_emoji_set_unlocks`** - Tracks which emoji sets users have unlocked  
3. **`user_subscription_history`** - VIP subscription history and billing
4. **`vip_plans`** - Available VIP subscription plans
5. **`vip_features`** - Individual VIP features and their configurations

### VIP Fields in Users Table

The main `users` table includes VIP-related fields:
- `is_vip` - Boolean indicating VIP status
- `vip_tier` - VIP tier level ('basic', 'premium', 'enterprise')
- `vip_expires_at` - VIP expiration timestamp
- `vip_auto_renew` - Auto-renewal setting

## 🎯 VIP Plans

### Available Plans

| Plan | Price | Billing | Features |
|------|-------|---------|----------|
| **Basic VIP (Monthly)** | $4.99 | Monthly | Unlimited skins, 10 premium emoji sets, themes, export, support |
| **Basic VIP (Yearly)** | $49.99 | Yearly | Same as monthly with yearly savings |
| **Premium VIP (Monthly)** | $9.99 | Monthly | All basic + unlimited emoji sets, analytics, insights, custom quiz packs |
| **Premium VIP (Yearly)** | $99.99 | Yearly | Same as monthly with yearly savings |
| **Enterprise VIP** | $199.99 | Yearly | All premium + API access, white label, team management |
| **Lifetime VIP** | $299.99 | One-time | All premium features with lifetime access |

### VIP Features

- **Content Unlocks**: Unlimited skins, premium emoji sets
- **Customization**: Advanced themes, custom quiz packs, white labeling
- **Analytics**: Advanced mood analytics, AI insights, trend analysis
- **Data Management**: Export, backup, multi-device sync
- **Support**: Priority support, dedicated representatives
- **Enterprise**: API access, team management, custom integrations

## 🔓 Unlock System

### Unlock Methods

1. **VIP** - Content unlocked through VIP subscription
2. **Purchase** - Individual content purchases
3. **Achievement** - Unlocked through app achievements
4. **Free** - Free content available to all users
5. **Promotion** - Unlocked via promotional codes

### Content Types

- **Skins** - UI themes and visual customizations
- **Emoji Sets** - Premium emoji collections for mood tracking

## 🚀 Setup Instructions

### 1. Create Database Tables

```sql
-- Run the VIP schema
.read public/seeds/schema/vip_unlock_system.sql
```

### 2. Insert VIP Plans and Features

```sql
-- Insert VIP configuration data
.read public/seeds/config/insert_vip_data.sql
```

### 3. Load Test Data (Optional)

```sql
-- For development/testing, load sample data
-- Note: You'll need to create INSERT statements from the JSON files
```

## 🔧 Integration Points

### Service Layer

The VIP system integrates with the service layer through:

- **`VipSubscriptionService`** - Manages VIP subscriptions
- **`UserUnlockService`** - Manages content unlocks
- **`VipPlanService`** - Manages VIP plans and features
- **`PaymentService`** - Handles subscription payments

### Type Definitions

Update `src/types/schema/` with VIP-related types:

```typescript
// VIP subscription types
interface VipSubscription {
  id: string;
  user_id: string;
  subscription_type: string;
  status: 'active' | 'cancelled' | 'expired' | 'refunded' | 'pending';
  started_at: string;
  expires_at?: string;
  auto_renew: boolean;
  // ... other fields
}

// Content unlock types
interface UserUnlock {
  id: string;
  user_id: string;
  content_id: string;
  unlock_method: 'purchase' | 'vip' | 'achievement' | 'free' | 'promotion';
  unlocked_at: string;
  expires_at?: string;
  // ... other fields
}
```

### Hooks Integration

Create hooks for VIP functionality:

```typescript
// VIP status and features
const useVipStatus = () => {
  // Returns user VIP status, tier, features, etc.
};

// Content unlocks
const useContentUnlocks = () => {
  // Returns user's unlocked content
};

// VIP plans
const useVipPlans = () => {
  // Returns available VIP plans
};
```

## 🔒 Security Considerations

1. **Server-Only Data**: Subscription history contains sensitive financial data and should only be accessible server-side
2. **Read-Only Unlocks**: Content unlocks are managed server-side; clients have read-only access
3. **VIP Verification**: Always verify VIP status server-side before granting access to premium features
4. **Payment Security**: All payment processing should be handled through secure payment providers

## 📊 Analytics and Monitoring

Track key VIP metrics:

- Subscription conversion rates
- Feature usage by VIP tier
- Churn rates and renewal patterns
- Content unlock patterns
- Revenue by plan type

## 🧪 Testing

Use the provided test data files for development:

- `user_unlocks.json` - Sample unlock scenarios
- `user_subscriptions.json` - Sample subscription histories

## 🔄 Migration Notes

When migrating from the old system:

1. Preserve existing VIP status in users table
2. Migrate any existing unlock data
3. Update service layer to use new VIP tables
4. Test VIP feature access thoroughly
5. Ensure billing continuity for existing subscribers

## 📝 Next Steps

1. Implement VIP service layer classes
2. Create TypeScript type definitions
3. Build VIP management UI components
4. Integrate with payment providers
5. Add VIP feature gates throughout the app
6. Implement analytics tracking
7. Create admin tools for VIP management
