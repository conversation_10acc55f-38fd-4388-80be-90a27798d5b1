/**
 * useNewHomeData Hook 测试
 */

import { renderHook, waitFor } from '@testing-library/react';
import { useNewHomeData } from '../useNewHomeData';
import { Services } from '@/services';

// Mock Services
jest.mock('@/services', () => ({
  Services: {
    quizPack: jest.fn(),
    quizQuestion: jest.fn(),
  },
}));

const mockQuizPackService = {
  getEmotionWheelQuizPacks: jest.fn(),
  getRecommendedQuizPacks: jest.fn(),
  getAccessibleQuizPacks: jest.fn(),
  getById: jest.fn(),
};

const mockQuizQuestionService = {
  getEmotionWheelQuestions: jest.fn(),
  getQuizPackQuestions: jest.fn(),
};

describe('useNewHomeData', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (Services.quizPack as jest.Mock).mockResolvedValue(mockQuizPackService);
    (Services.quizQuestion as jest.Mock).mockResolvedValue(mockQuizQuestionService);
  });

  it('should load emotion wheel data successfully', async () => {
    // Mock data
    const mockQuizPack = {
      id: 'emotion-wheel-pack-1',
      name: '情绪轮盘',
      description: '基础情绪轮盘测评',
      quiz_type: 'emotion_wheel',
      is_active: true,
    };

    const mockQuestions = [
      {
        id: 'question-1',
        pack_id: 'emotion-wheel-pack-1',
        question_text: '请选择您的主要情绪',
        question_type: 'emotion_wheel',
        tier_level: 1,
        options: [
          {
            id: 'option-1',
            option_text: '快乐',
            option_value: 'happy',
            metadata: JSON.stringify({ emotion_id: 'happy' }),
          },
          {
            id: 'option-2',
            option_text: '悲伤',
            option_value: 'sad',
            metadata: JSON.stringify({ emotion_id: 'sad' }),
          },
        ],
      },
      {
        id: 'question-2',
        pack_id: 'emotion-wheel-pack-1',
        question_text: '请选择您的次要情绪',
        question_type: 'emotion_wheel',
        tier_level: 2,
        options: [
          {
            id: 'option-3',
            option_text: '兴奋',
            option_value: 'excited',
            metadata: JSON.stringify({ 
              emotion_id: 'excited',
              parent_emotion_id: 'happy'
            }),
          },
        ],
      },
    ];

    // Setup mocks
    mockQuizPackService.getEmotionWheelQuizPacks.mockResolvedValue({
      success: true,
      data: [mockQuizPack],
    });

    mockQuizQuestionService.getEmotionWheelQuestions.mockResolvedValue({
      success: true,
      data: mockQuestions,
    });

    // Render hook
    const { result } = renderHook(() => useNewHomeData('test-user-id'));

    // Wait for data to load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Verify data
    expect(result.current.emotionWheelData).toBeDefined();
    expect(result.current.emotionWheelData?.pack).toEqual(mockQuizPack);
    expect(result.current.emotionWheelData?.questions).toEqual(mockQuestions);
    expect(result.current.emotionWheelData?.primaryEmotions).toHaveLength(2);
    expect(result.current.emotionWheelData?.secondaryEmotions).toHaveLength(1);
    expect(result.current.error).toBeNull();
  });

  it('should handle loading errors gracefully', async () => {
    // Setup error mock
    mockQuizPackService.getEmotionWheelQuizPacks.mockResolvedValue({
      success: false,
      error: 'Failed to load quiz packs',
    });

    // Render hook
    const { result } = renderHook(() => useNewHomeData('test-user-id'));

    // Wait for error
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Verify error state
    expect(result.current.error).toBe('Failed to load quiz packs');
    expect(result.current.emotionWheelData).toBeNull();
  });

  it('should load recommended quiz packs', async () => {
    const mockRecommendedPacks = [
      {
        id: 'pack-1',
        name: '推荐包1',
        quiz_type: 'emotion_wheel',
        is_active: true,
      },
      {
        id: 'pack-2',
        name: '推荐包2',
        quiz_type: 'tcm_assessment',
        is_active: true,
      },
    ];

    // Setup mocks
    mockQuizPackService.getEmotionWheelQuizPacks.mockResolvedValue({
      success: true,
      data: [],
    });

    mockQuizPackService.getRecommendedQuizPacks.mockResolvedValue({
      success: true,
      data: mockRecommendedPacks,
    });

    mockQuizPackService.getAccessibleQuizPacks.mockResolvedValue({
      success: true,
      data: mockRecommendedPacks,
    });

    // Render hook
    const { result } = renderHook(() => useNewHomeData('test-user-id'));

    // Wait for data to load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Verify recommended packs
    expect(result.current.recommendedQuizPacks).toEqual(mockRecommendedPacks);
    expect(result.current.recentQuizPacks).toHaveLength(2);
  });

  it('should provide search functionality', async () => {
    const mockSearchResults = [
      {
        id: 'search-pack-1',
        name: '搜索结果1',
        quiz_type: 'emotion_wheel',
        is_active: true,
      },
    ];

    // Setup mocks
    mockQuizPackService.getEmotionWheelQuizPacks.mockResolvedValue({
      success: true,
      data: [],
    });

    mockQuizPackService.searchQuizPacks.mockResolvedValue({
      success: true,
      data: mockSearchResults,
    });

    // Render hook
    const { result } = renderHook(() => useNewHomeData('test-user-id'));

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Test search functionality
    const searchResults = await result.current.searchQuizPacks('情绪');
    expect(searchResults).toEqual(mockSearchResults);
    expect(mockQuizPackService.searchQuizPacks).toHaveBeenCalledWith('情绪');
  });

  it('should provide category filtering', async () => {
    const mockCategoryResults = [
      {
        id: 'category-pack-1',
        name: '分类包1',
        category: 'daily',
        quiz_type: 'emotion_wheel',
        is_active: true,
      },
    ];

    // Setup mocks
    mockQuizPackService.getEmotionWheelQuizPacks.mockResolvedValue({
      success: true,
      data: [],
    });

    mockQuizPackService.searchQuizPacks.mockResolvedValue({
      success: true,
      data: mockCategoryResults,
    });

    // Render hook
    const { result } = renderHook(() => useNewHomeData('test-user-id'));

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Test category filtering
    const categoryResults = await result.current.getQuizPacksByCategory('daily');
    expect(categoryResults).toEqual(mockCategoryResults);
    expect(mockQuizPackService.searchQuizPacks).toHaveBeenCalledWith('', { category: 'daily' });
  });

  it('should refresh data when requested', async () => {
    // Setup mocks
    mockQuizPackService.getEmotionWheelQuizPacks.mockResolvedValue({
      success: true,
      data: [],
    });

    mockQuizPackService.getRecommendedQuizPacks.mockResolvedValue({
      success: true,
      data: [],
    });

    mockQuizPackService.getAccessibleQuizPacks.mockResolvedValue({
      success: true,
      data: [],
    });

    // Render hook
    const { result } = renderHook(() => useNewHomeData('test-user-id'));

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Clear mock calls
    jest.clearAllMocks();

    // Trigger refresh
    await result.current.refreshData();

    // Verify refresh calls
    expect(mockQuizPackService.getEmotionWheelQuizPacks).toHaveBeenCalled();
    expect(mockQuizPackService.getRecommendedQuizPacks).toHaveBeenCalled();
    expect(mockQuizPackService.getAccessibleQuizPacks).toHaveBeenCalled();
  });
});
