/**
 * 集成测试 (P2 中等优先级)
 * 验证系统各组件间的集成和协作
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('集成测试 (P2)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('1. 服务间集成测试', () => {
    it('应该验证QuizService与ConfigService集成', async () => {
      const mockQuizService = {
        loadQuiz: vi.fn(),
        applyConfig: vi.fn(),
        startQuiz: vi.fn().mockResolvedValue({
          quizId: 'quiz123',
          configApplied: true,
          status: 'started'
        })
      };

      const mockConfigService = {
        getUserConfig: vi.fn().mockResolvedValue({
          userId: 'user123',
          theme: 'dark',
          emojiSet: 'traditional',
          language: 'zh'
        }),
        applyConfig: vi.fn().mockResolvedValue(true)
      };

      // 模拟服务集成流程
      const userConfig = await mockConfigService.getUserConfig('user123');
      await mockQuizService.applyConfig(userConfig);
      const quizResult = await mockQuizService.startQuiz('quiz123');

      expect(mockConfigService.getUserConfig).toHaveBeenCalledWith('user123');
      expect(mockQuizService.applyConfig).toHaveBeenCalledWith(userConfig);
      expect(quizResult.configApplied).toBe(true);
      expect(quizResult.status).toBe('started');
    });

    it('应该验证DatabaseService与CacheService集成', async () => {
      const mockDatabaseService = {
        query: vi.fn().mockResolvedValue([
          { id: 'quiz1', name: 'TCM Emotions', cached: false }
        ]),
        insert: vi.fn().mockResolvedValue({ success: true, id: 'new_id' })
      };

      const mockCacheService = {
        get: vi.fn().mockResolvedValue(null), // 缓存未命中
        set: vi.fn().mockResolvedValue(true),
        invalidate: vi.fn().mockResolvedValue(true)
      };

      // 模拟缓存集成逻辑
      const cacheKey = 'quiz_list';
      let data = await mockCacheService.get(cacheKey);
      
      if (!data) {
        data = await mockDatabaseService.query('SELECT * FROM quiz_packs');
        await mockCacheService.set(cacheKey, data, 3600); // 1小时缓存
      }

      expect(mockCacheService.get).toHaveBeenCalledWith(cacheKey);
      expect(mockDatabaseService.query).toHaveBeenCalled();
      expect(mockCacheService.set).toHaveBeenCalledWith(cacheKey, data, 3600);
      expect(data).toHaveLength(1);
    });

    it('应该验证SyncService与StorageService集成', async () => {
      const mockSyncService = {
        syncToCloud: vi.fn().mockResolvedValue({
          success: true,
          syncedItems: 5,
          conflicts: 0
        }),
        resolveConflicts: vi.fn().mockResolvedValue({ resolved: true })
      };

      const mockStorageService = {
        getLocalData: vi.fn().mockResolvedValue([
          { id: 'item1', lastModified: Date.now() - 1000 },
          { id: 'item2', lastModified: Date.now() - 2000 }
        ]),
        updateLocalData: vi.fn().mockResolvedValue(true),
        markAsSynced: vi.fn().mockResolvedValue(true)
      };

      // 模拟同步集成流程
      const localData = await mockStorageService.getLocalData();
      const syncResult = await mockSyncService.syncToCloud(localData);
      
      if (syncResult.success) {
        await mockStorageService.markAsSynced(localData.map(item => item.id));
      }

      expect(mockStorageService.getLocalData).toHaveBeenCalled();
      expect(mockSyncService.syncToCloud).toHaveBeenCalledWith(localData);
      expect(mockStorageService.markAsSynced).toHaveBeenCalled();
      expect(syncResult.syncedItems).toBe(5);
    });
  });

  describe('2. 数据流集成测试', () => {
    it('应该验证Quiz数据完整流转', async () => {
      const mockDataFlow = {
        loadQuizPack: vi.fn().mockResolvedValue({
          id: 'pack123',
          questions: [
            { id: 'q1', text: 'How do you feel?', options: ['happy', 'sad'] }
          ]
        }),
        applyUserConfig: vi.fn().mockResolvedValue({
          theme: 'dark',
          emojiMapping: { 'happy': '😊', 'sad': '😢' }
        }),
        renderQuiz: vi.fn().mockResolvedValue({
          rendered: true,
          questionCount: 1,
          configApplied: true
        })
      };

      // 模拟完整数据流
      const quizPack = await mockDataFlow.loadQuizPack('pack123');
      const userConfig = await mockDataFlow.applyUserConfig('user123', quizPack.id);
      const renderResult = await mockDataFlow.renderQuiz(quizPack, userConfig);

      expect(quizPack.questions).toHaveLength(1);
      expect(userConfig.emojiMapping).toBeDefined();
      expect(renderResult.rendered).toBe(true);
      expect(renderResult.configApplied).toBe(true);
    });

    it('应该验证用户答案处理流程', async () => {
      const mockAnswerFlow = {
        validateAnswer: vi.fn().mockResolvedValue({
          valid: true,
          questionId: 'q1',
          selectedOption: 'happy'
        }),
        calculateScore: vi.fn().mockResolvedValue({
          questionScore: 85,
          totalScore: 85,
          progress: 0.1
        }),
        saveAnswer: vi.fn().mockResolvedValue({
          saved: true,
          answerId: 'ans123'
        }),
        updateProgress: vi.fn().mockResolvedValue({
          currentQuestion: 2,
          totalQuestions: 10,
          percentage: 20
        })
      };

      // 模拟答案处理流程
      const answer = { questionId: 'q1', selectedOption: 'happy' };
      const validation = await mockAnswerFlow.validateAnswer(answer);
      
      if (validation.valid) {
        const score = await mockAnswerFlow.calculateScore(answer);
        const saveResult = await mockAnswerFlow.saveAnswer(answer, score);
        const progress = await mockAnswerFlow.updateProgress('session123');

        expect(score.questionScore).toBe(85);
        expect(saveResult.saved).toBe(true);
        expect(progress.percentage).toBe(20);
      }
    });

    it('应该验证配置变更传播', async () => {
      const mockConfigPropagation = {
        updateUserConfig: vi.fn().mockResolvedValue({
          configId: 'config123',
          updated: true
        }),
        invalidateCache: vi.fn().mockResolvedValue(true),
        notifyComponents: vi.fn().mockResolvedValue({
          notified: ['QuizRenderer', 'EmojiMapper', 'ThemeManager']
        }),
        reapplyConfig: vi.fn().mockResolvedValue({
          applied: true,
          affectedComponents: 3
        })
      };

      // 模拟配置变更传播
      const configUpdate = { theme: 'light', emojiSet: 'modern' };
      const updateResult = await mockConfigPropagation.updateUserConfig('user123', configUpdate);
      
      if (updateResult.updated) {
        await mockConfigPropagation.invalidateCache('user123');
        const notification = await mockConfigPropagation.notifyComponents(configUpdate);
        const reapply = await mockConfigPropagation.reapplyConfig('user123');

        expect(notification.notified).toContain('QuizRenderer');
        expect(reapply.affectedComponents).toBe(3);
      }
    });
  });

  describe('3. API集成测试', () => {
    it('应该验证前后端API集成', async () => {
      const mockAPIIntegration = {
        frontend: {
          sendRequest: vi.fn().mockResolvedValue({
            status: 200,
            data: { quizPacks: [], total: 0 }
          }),
          handleResponse: vi.fn().mockResolvedValue({
            processed: true,
            data: []
          })
        },
        backend: {
          processRequest: vi.fn().mockResolvedValue({
            success: true,
            data: { quizPacks: [], total: 0 }
          }),
          validateAuth: vi.fn().mockResolvedValue({ valid: true, userId: 'user123' })
        }
      };

      // 模拟API调用流程
      const request = { endpoint: '/api/quiz-packs', method: 'GET', headers: { 'Authorization': 'Bearer token123' } };
      
      // 后端处理
      const authResult = await mockAPIIntegration.backend.validateAuth(request.headers.Authorization);
      const backendResponse = await mockAPIIntegration.backend.processRequest(request);
      
      // 前端处理
      const frontendResponse = await mockAPIIntegration.frontend.sendRequest(request);
      const processedData = await mockAPIIntegration.frontend.handleResponse(frontendResponse);

      expect(authResult.valid).toBe(true);
      expect(backendResponse.success).toBe(true);
      expect(frontendResponse.status).toBe(200);
      expect(processedData.processed).toBe(true);
    });

    it('应该验证第三方服务集成', async () => {
      const mockThirdPartyIntegration = {
        paymentService: {
          processPayment: vi.fn().mockResolvedValue({
            success: true,
            transactionId: 'txn123',
            amount: 9.99
          })
        },
        analyticsService: {
          trackEvent: vi.fn().mockResolvedValue({
            tracked: true,
            eventId: 'evt123'
          })
        },
        notificationService: {
          sendNotification: vi.fn().mockResolvedValue({
            sent: true,
            messageId: 'msg123'
          })
        }
      };

      // 模拟第三方服务调用
      const paymentResult = await mockThirdPartyIntegration.paymentService.processPayment({
        amount: 9.99,
        currency: 'USD',
        userId: 'user123'
      });

      const analyticsResult = await mockThirdPartyIntegration.analyticsService.trackEvent({
        event: 'quiz_completed',
        userId: 'user123',
        properties: { quizId: 'quiz123', score: 85 }
      });

      const notificationResult = await mockThirdPartyIntegration.notificationService.sendNotification({
        userId: 'user123',
        message: 'Quiz completed successfully!',
        type: 'success'
      });

      expect(paymentResult.success).toBe(true);
      expect(analyticsResult.tracked).toBe(true);
      expect(notificationResult.sent).toBe(true);
    });
  });

  describe('4. 状态管理集成测试', () => {
    it('应该验证全局状态同步', async () => {
      const mockStateManager = {
        globalState: {
          user: { id: 'user123', name: 'Test User' },
          quiz: { id: null, status: 'idle' },
          config: { theme: 'light', language: 'zh' }
        },
        updateState: vi.fn().mockImplementation((path, value) => {
          const state = { ...mockStateManager.globalState };
          const keys = path.split('.');
          let current = state;
          for (let i = 0; i < keys.length - 1; i++) {
            current = current[keys[i]];
          }
          current[keys[keys.length - 1]] = value;
          mockStateManager.globalState = state;
          return state;
        }),
        getState: vi.fn().mockImplementation((path) => {
          const keys = path.split('.');
          let current = mockStateManager.globalState;
          for (const key of keys) {
            current = current[key];
          }
          return current;
        }),
        subscribe: vi.fn().mockReturnValue(() => {}) // unsubscribe function
      };

      // 模拟状态更新和同步
      mockStateManager.updateState('quiz.id', 'quiz123');
      mockStateManager.updateState('quiz.status', 'in_progress');
      
      const quizState = mockStateManager.getState('quiz');
      const userState = mockStateManager.getState('user');

      expect(quizState.id).toBe('quiz123');
      expect(quizState.status).toBe('in_progress');
      expect(userState.id).toBe('user123');
    });

    it('应该验证组件状态同步', async () => {
      const mockComponentSync = {
        components: {
          QuizRenderer: { state: 'idle', props: {} },
          ProgressBar: { state: 'hidden', props: { progress: 0 } },
          ConfigPanel: { state: 'closed', props: { config: {} } }
        },
        syncComponentState: vi.fn().mockImplementation((componentName, newState) => {
          mockComponentSync.components[componentName].state = newState.state;
          mockComponentSync.components[componentName].props = { ...mockComponentSync.components[componentName].props, ...newState.props };
          return true;
        }),
        broadcastStateChange: vi.fn().mockResolvedValue({
          broadcasted: true,
          affectedComponents: 3
        })
      };

      // 模拟组件状态同步
      await mockComponentSync.syncComponentState('QuizRenderer', {
        state: 'rendering',
        props: { quizId: 'quiz123' }
      });

      await mockComponentSync.syncComponentState('ProgressBar', {
        state: 'visible',
        props: { progress: 20 }
      });

      const broadcast = await mockComponentSync.broadcastStateChange({
        type: 'QUIZ_STARTED',
        payload: { quizId: 'quiz123' }
      });

      expect(mockComponentSync.components.QuizRenderer.state).toBe('rendering');
      expect(mockComponentSync.components.ProgressBar.props.progress).toBe(20);
      expect(broadcast.affectedComponents).toBe(3);
    });
  });

  describe('5. 错误处理集成测试', () => {
    it('应该验证跨服务错误传播', async () => {
      const mockErrorPropagation = {
        serviceA: {
          operation: vi.fn().mockRejectedValue(new Error('Service A failed'))
        },
        serviceB: {
          handleError: vi.fn().mockResolvedValue({
            handled: true,
            fallbackData: { default: true }
          })
        },
        errorHandler: {
          propagateError: vi.fn().mockResolvedValue({
            propagated: true,
            affectedServices: ['ServiceB', 'ServiceC']
          }),
          recoverFromError: vi.fn().mockResolvedValue({
            recovered: true,
            strategy: 'fallback'
          })
        }
      };

      try {
        await mockErrorPropagation.serviceA.operation();
      } catch (error) {
        const propagation = await mockErrorPropagation.errorHandler.propagateError(error);
        const recovery = await mockErrorPropagation.errorHandler.recoverFromError(error);
        const fallback = await mockErrorPropagation.serviceB.handleError(error);

        expect(propagation.propagated).toBe(true);
        expect(recovery.recovered).toBe(true);
        expect(fallback.handled).toBe(true);
      }
    });

    it('应该验证事务回滚集成', async () => {
      const mockTransactionManager = {
        beginTransaction: vi.fn().mockResolvedValue({ transactionId: 'txn123' }),
        commitTransaction: vi.fn().mockResolvedValue({ committed: true }),
        rollbackTransaction: vi.fn().mockResolvedValue({ rolledBack: true }),
        operations: {
          updateUser: vi.fn().mockResolvedValue({ success: true }),
          saveQuizResult: vi.fn().mockRejectedValue(new Error('Save failed')),
          updateProgress: vi.fn().mockResolvedValue({ success: true })
        }
      };

      const transaction = await mockTransactionManager.beginTransaction();
      
      try {
        await mockTransactionManager.operations.updateUser({ userId: 'user123' });
        await mockTransactionManager.operations.saveQuizResult({ score: 85 });
        await mockTransactionManager.operations.updateProgress({ progress: 100 });
        
        await mockTransactionManager.commitTransaction(transaction.transactionId);
      } catch (error) {
        const rollback = await mockTransactionManager.rollbackTransaction(transaction.transactionId);
        expect(rollback.rolledBack).toBe(true);
      }

      expect(mockTransactionManager.beginTransaction).toHaveBeenCalled();
      expect(mockTransactionManager.rollbackTransaction).toHaveBeenCalled();
    });
  });
});
