-- 主配置数据加载文件
-- 按正确顺序加载所有配置数据

-- 1. 基础配置数据
.read ui_labels.sql
.read ui_label_translations.sql
.read app_settings.sql

-- 2. 情绪相关数据 (已废弃，使用Quiz架构替代)
-- .read complete_emotions.sql
-- .read emotion_data_sets.sql
-- .read emotion_data_set_tiers.sql

-- 3. 皮肤和UI配置
.read skin_configs.sql
.read quiz_skin_configs.sql

-- 4. Quiz包数据
.read mood-wheel-quiz-packs.sql
.read tcm-quiz-packs.sql

-- 5. 用户配置系统
.read new_config_system.sql
.read quiz_preferences_configs.sql
.read quiz_pack_overrides_new.sql

-- 6. Emoji映射配置 (新架构)
.read emoji-mapping-init.sql

-- 7. 全局应用配置
.read global_app_configs.sql

-- 验证数据加载
SELECT 'Quiz Packs loaded:' as info, COUNT(*) as count FROM quiz_packs;
SELECT 'Quiz Questions loaded:' as info, COUNT(*) as count FROM quiz_questions;
SELECT 'Quiz Options loaded:' as info, COUNT(*) as count FROM quiz_question_options;
SELECT 'Pack Presentation Configs loaded:' as info, COUNT(*) as count FROM pack_presentation_configs;
SELECT 'Question Presentation Overrides loaded:' as info, COUNT(*) as count FROM question_presentation_overrides;
SELECT 'Skins loaded:' as info, COUNT(*) as count FROM skins;
SELECT 'Emoji Sets loaded:' as info, COUNT(*) as count FROM emoji_sets;
SELECT 'UI Labels loaded:' as info, COUNT(*) as count FROM ui_labels;
