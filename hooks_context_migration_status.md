# Hooks和Context迁移状态分析

## 📋 Context系统迁移状态

### ✅ 已完成迁移的Context

#### 1. **SkinContext** (src/contexts/SkinContext.tsx)
**迁移状态**: ✅ 完全现代化
**关键改进**:
- 集成了现代化的SkinService
- 支持皮肤解锁和激活功能
- 实现了在线离线混合数据获取
- 提供了兼容性属性支持渐进迁移

```typescript
// ✅ 现代化实现
const SkinContext = createContext<SkinContextType | undefined>(undefined);

export const SkinProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [skins, setSkins] = useState<Skin[]>([]);
  const [activeSkin, setActiveSkin] = useState<Skin | null>(null);
  
  // 使用现代化的混合数据获取
  const { data: skinsData, isLoading } = useHybridSkinsData();
  
  // 兼容性属性 (逐步移除)
  const unlockedSkins = skins.filter((skin) => skin.is_unlocked);
  
  return (
    <SkinContext.Provider value={{
      skins,
      activeSkin,
      setActiveSkin: handleSetActiveSkin,
      unlockSkin: handleUnlockSkin,
      isLoading,
      // 兼容性属性
      currentSkin: activeSkin,
      availableSkins: skins,
      unlockedSkins,
    }}>
      {children}
    </SkinContext.Provider>
  );
};
```

#### 2. **EmojiContext** (src/contexts/EmojiContext.tsx)
**迁移状态**: ✅ 完全现代化
**关键改进**:
- 支持表情符号集的动态切换
- 实现了表情符号到情绪的映射
- 提供了fallback机制
- 支持数组和对象两种数据格式

```typescript
// ✅ 现代化实现
export const EmojiProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [availableEmojiSets, setAvailableEmojiSets] = useState<EmojiSet[]>([]);
  const [activeEmojiSet, setActiveEmojiSet] = useState<EmojiSet | null>(null);
  
  // 获取情绪对应的表情字符串
  const getEmoji = useCallback((emotionId: string, fallbackEmoji = '❓'): string => {
    if (!activeEmojiSet || !activeEmojiSet.items) return fallbackEmoji;

    // 处理 items 可能是数组或对象的情况
    let emojiItem: EmojiItem | undefined;
    if (Array.isArray(activeEmojiSet.items)) {
      emojiItem = activeEmojiSet.items.find(item => item.emotion_id === emotionId);
    } else {
      emojiItem = Object.values(activeEmojiSet.items).find(item => item.emotion_id === emotionId);
    }

    return emojiItem?.unicode || fallbackEmoji;
  }, [activeEmojiSet]);
};
```

### 🔄 需要完善的Context

#### 1. **ThemeContext** (src/contexts/ThemeContext.tsx)
**当前状态**: 基础实现完成
**需要完善**:
- 与useGlobalConfig的深度集成
- 主题切换的动画效果
- 系统主题的自动检测

#### 2. **LanguageContext** (src/contexts/LanguageContext.tsx)
**当前状态**: 基础实现完成
**需要完善**:
- 动态语言包加载
- 翻译缓存机制
- 语言切换的平滑过渡

## 🎣 Hooks系统迁移状态

### ✅ 已完成迁移的Hooks

#### 1. **useGlobalConfig** (src/hooks/useGlobalConfig.ts)
**迁移状态**: ✅ 完全现代化
**关键特性**:
- 在线离线混合配置管理
- 自动同步机制
- 类型安全的配置访问
- 便捷的访问器属性

```typescript
// ✅ 现代化Hook实现
export const useGlobalConfig = (configName: string = 'default'): UseGlobalConfigReturn => {
  const [config, setConfig] = useState<GlobalAppConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  
  const { user, isAuthenticated } = useAuth();
  const { isOnline } = useNetworkStatus();

  // 从本地数据库加载配置
  const loadLocalConfig = useCallback(async () => {
    if (!user?.id) return;
    try {
      const globalConfigService = await Services.globalAppConfig();
      const result = await globalConfigService.getActiveUserConfig(user.id, configName);
      if (result.success && result.data) {
        setConfig(result.data);
        setError(null);
      }
    } catch (err) {
      console.error('Error loading local config:', err);
    }
  }, [user?.id, configName]);

  // 便捷访问器
  return {
    config,
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    updateConfig,
    resetToDefault,
    syncToCloud,
    refreshConfig,
    // 便捷访问器
    themeMode: config?.theme_mode || 'system',
    language: config?.language || 'zh-CN',
    notificationsEnabled: config?.notifications_enabled ?? true,
    soundEnabled: config?.sound_enabled ?? true,
    accessibilityConfig
  };
};
```

#### 2. **useQuizConfig** (src/hooks/useQuizConfig.ts)
**迁移状态**: ✅ 基础架构完成
**关键特性**:
- 6层配置架构支持
- 会话配置生成
- 包覆盖管理
- 个性化级别计算

```typescript
// ✅ 现代化Hook实现
export const useQuizConfig = (configName: string = 'default'): UseQuizConfigReturn => {
  const [preferences, setPreferences] = useState<UserQuizPreferences | null>(null);
  const [presentationConfig, setPresentationConfig] = useState<QuizPresentationConfig | null>(null);
  
  // 会话配置生成
  const generateSessionConfig = useCallback(async (packId: string, sessionId: string): Promise<QuizSessionConfig | null> => {
    try {
      // 调用配置合并服务
      const mergerService = await Services.quizConfigMerger();
      const result = await mergerService.generateSessionConfig(user!.id, packId, sessionId);
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error generating session config:', error);
      return null;
    }
  }, [user]);

  return {
    preferences,
    presentationConfig,
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    updatePreferences,
    updatePresentationConfig,
    resetToDefault,
    syncToCloud,
    refreshConfig,
    generateSessionConfig,
    getPackOverrides,
    // 便捷访问器
    preferredViewType: presentationConfig?.layer1_user_choice?.preferred_view_type || 'wheel',
    colorMode: presentationConfig?.layer1_user_choice?.color_mode || 'warm',
    personalizationLevel: preferences?.personalization_level || 50,
    performanceMode: presentationConfig?.layer2_rendering_strategy?.performance_mode || 'balanced'
  };
};
```

#### 3. **useHybridData** (src/hooks/useHybridData.ts)
**迁移状态**: ✅ 核心框架完成
**关键特性**:
- 在线优先，离线回退策略
- 自动同步机制
- 网络状态感知
- 缓存管理

```typescript
// ✅ 现代化混合数据Hook
export function useHybridSkinsData() {
  return useHybridData(
    'skins',
    async () => {
      // 在线获取
      const result = await safeTrpcCall('skins');
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch skins from server');
      }
      return result.data;
    },
    async () => {
      // 离线获取
      const skinService = await Services.skin();
      const result = await skinService.getAll();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    {
      enableOnlineFirst: true,
      enableOfflineFallback: true,
      enableAutoSync: true,
      syncInterval: 30 * 60 * 1000, // 30分钟同步一次
    }
  );
}
```

### 🔄 需要完善的Hooks

#### 1. **useSettingsData** (src/hooks/useSettingsData.ts)
**当前状态**: 基础实现完成
**需要完善**:
- 在线离线切换的优化
- 皮肤解锁逻辑的完善
- 错误处理的改进

```typescript
// 🔄 需要完善的部分
const updateSkin = useCallback(async (skinId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    // 优先尝试在线更新
    if (skinsOnline) {
      try {
        // TODO: 实现在线更新 ⚠️ 需要完善
        console.log('[useSettingsData] Online update not implemented, falling back to offline');
      } catch (onlineError) {
        console.warn('[useSettingsData] Online update failed, falling back to offline:', onlineError);
      }
    }

    // 离线更新
    const skinService = await Services.skin();
    const result = await skinService.setDefaultSkin(skinId);
    // ...
  } catch (error) {
    // ...
  }
}, [skinsOnline, skins]);
```

#### 2. **useAnalyticsData** (src/hooks/useAnalyticsData.ts)
**当前状态**: 基础框架存在
**需要完善**:
- 服务器端聚合数据获取
- 大数据量的性能优化
- 实时数据更新

#### 3. **useHistoryData** (src/hooks/useHistoryData.ts)
**当前状态**: 基础实现
**需要完善**:
- 分页和虚拟化
- 高效的过滤和排序
- 跨服务数据聚合

### ⚠️ 存在问题的Hooks

#### 1. **useHybridData中的废弃服务调用**
```typescript
// ❌ 问题代码
export function useHybridMoodEntriesData() {
  return useHybridData(
    'moodEntries',
    async () => {
      // ...
    },
    async () => {
      const moodEntryService = await Services.moodEntry(); // ❌ 废弃服务
      const result = await moodEntryService.getAll();
      return result.data;
    }
  );
}

// ✅ 修复方案
export function useHybridMoodEntriesData() {
  return useHybridData(
    'moodEntries',
    async () => {
      // ...
    },
    async () => {
      const moodTrackingService = await Services.moodTracking(); // ✅ 新服务
      const result = await moodTrackingService.getAllEntries();
      return result.data;
    }
  );
}
```

## 🎯 迁移优先级和时间表

### 第一阶段 (高优先级 - 1周)
1. **修复useHybridData中的废弃服务调用**
   - 更新moodEntry → moodTracking
   - 更新emotionDataSet → quizPack
   - 添加废弃警告

2. **完善useSettingsData的在线更新**
   - 实现皮肤的在线更新逻辑
   - 优化错误处理和回退机制

### 第二阶段 (中优先级 - 2周)
1. **增强useQuizConfig功能**
   - 完善会话配置生成
   - 实现包覆盖的完整管理
   - 优化个性化配置的应用

2. **优化Context的性能**
   - 实现Context的懒加载
   - 添加缓存机制
   - 减少不必要的重渲染

### 第三阶段 (低优先级 - 3周)
1. **实现高级Hooks**
   - useAnalyticsData的服务器端聚合
   - useHistoryData的性能优化
   - 实时数据更新的WebSocket支持

2. **清理兼容性代码**
   - 移除Context中的兼容性属性
   - 统一Hook的命名和接口
   - 完善TypeScript类型定义

## 📊 迁移完成度评估

| Hook/Context | 迁移状态 | 完成度 | 优先级 |
|-------------|---------|--------|--------|
| SkinContext | ✅ 完成 | 95% | - |
| EmojiContext | ✅ 完成 | 90% | - |
| useGlobalConfig | ✅ 完成 | 95% | - |
| useQuizConfig | 🔄 进行中 | 75% | 高 |
| useHybridData | 🔄 进行中 | 80% | 高 |
| useSettingsData | 🔄 进行中 | 60% | 中 |
| useAnalyticsData | ⚠️ 需要重构 | 40% | 中 |
| useHistoryData | ⚠️ 需要重构 | 45% | 低 |

## 🔧 技术债务清单

### 立即处理
1. **废弃服务调用清理** - useHybridData中的Services.moodEntry()等
2. **兼容性属性移除** - SkinContext中的currentSkin等
3. **TODO注释实现** - useSettingsData中的在线更新逻辑

### 架构改进
1. **Hook组合优化** - 减少Hook之间的耦合
2. **缓存策略统一** - 实现统一的缓存管理
3. **错误边界增强** - 添加Hook级别的错误恢复
4. **性能监控** - 添加Hook性能指标收集

---

**总结**: Context和Hooks系统的现代化迁移已基本完成，核心架构健康。主要工作集中在废弃服务调用的清理、在线功能的完善和性能优化上。预计在2-3个迭代周期内可以达到完全现代化状态。
