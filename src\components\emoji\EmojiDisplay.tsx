/**
 * 表情显示组件
 * 用于显示不同类型的表情（Unicode、图片、SVG、动画）
 */

import { cn } from '@/lib/utils';
import type { EmojiItem } from '@/types/emojiTypes';
import type React from 'react';
import AnimatedEmoji from './AnimatedEmoji';

interface EmojiDisplayProps {
  emojiItem: EmojiItem | null;
  fallbackEmoji?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  className?: string;
}

/**
 * 表情显示组件
 * @param emojiItem 表情项
 * @param fallbackEmoji 默认表情
 * @param size 表情大小
 * @param className 自定义类名
 */
const EmojiDisplay: React.FC<EmojiDisplayProps> = ({
  emojiItem,
  fallbackEmoji = '😶',
  size = 'md',
  className,
}) => {
  // 根据大小获取类名
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
  };

  // 如果没有表情项，显示默认表情
  if (!emojiItem) {
    return <span className={cn(sizeClasses[size], className)}>{fallbackEmoji}</span>;
  }

  // 根据表情类型显示不同的内容
  if (emojiItem.unicode) {
    // Unicode 表情
    return <span className={cn(sizeClasses[size], className)}>{emojiItem.unicode}</span>;
  }
  if (emojiItem.image_url) {
    // 图片表情
    return (
      <img
        src={emojiItem.image_url}
        alt="emoji"
        className={cn('inline-block', className)}
        style={{
          width:
            size === 'xs'
              ? '1rem'
              : size === 'sm'
                ? '1.25rem'
                : size === 'md'
                  ? '1.5rem'
                  : size === 'lg'
                    ? '2rem'
                    : size === 'xl'
                      ? '2.5rem'
                      : size === '2xl'
                        ? '3rem'
                        : size === '3xl'
                          ? '3.5rem'
                          : '4rem',
        }}
      />
    );
  }
  if (emojiItem.svg_content) {
    // SVG 表情
    return (
      <div
        className={cn('inline-block', className)}
        style={{
          width:
            size === 'xs'
              ? '1rem'
              : size === 'sm'
                ? '1.25rem'
                : size === 'md'
                  ? '1.5rem'
                  : size === 'lg'
                    ? '2rem'
                    : size === 'xl'
                      ? '2.5rem'
                      : size === '2xl'
                        ? '3rem'
                        : size === '3xl'
                          ? '3.5rem'
                          : '4rem',
        }}
        dangerouslySetInnerHTML={{ __html: emojiItem.svg_content }}
      />
    );
  }
  if (emojiItem.animation_data || emojiItem.animation_url || emojiItem.animation_type) {
    // 动画表情
    return <AnimatedEmoji emojiItem={emojiItem} size={size} className={className} />;
  }

  // 如果没有任何内容，显示默认表情
  return <span className={cn(sizeClasses[size], className)}>{fallbackEmoji}</span>;
};

export default EmojiDisplay;
