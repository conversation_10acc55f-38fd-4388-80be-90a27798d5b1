/**
 * 卡片布局预览组件
 * 用于在设置页面中展示不同的卡片布局选项
 */

import { useTheme } from '@/contexts/ThemeContext';
import type { CardLayout } from '@/views/components/cards/CardView';
import type React from 'react';

interface CardLayoutPreviewProps {
  layout: CardLayout;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  onClick?: () => void;
  isSelected?: boolean;
}

/**
 * 卡片布局预览组件
 * 展示不同卡片布局的预览
 */
const CardLayoutPreview: React.FC<CardLayoutPreviewProps> = ({
  layout,
  size = 'md',
  showLabel = false,
  onClick,
  isSelected = false,
}) => {
  const { theme } = useTheme();
  const isDarkMode =
    theme === 'dark' ||
    (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // 根据尺寸设置样式
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return { container: 'w-16 h-12', card: 'w-3 h-3' };
      case 'lg':
        return { container: 'w-32 h-24', card: 'w-6 h-6' };
      default:
        return { container: 'w-24 h-18', card: 'w-4 h-4' };
    }
  };

  const sizeClass = getSizeClass();

  // 获取布局标签
  const getLayoutLabel = () => {
    switch (layout) {
      case 'grid':
        return '网格布局';
      case 'list':
        return '列表布局';
      case 'masonry':
        return '瀑布流';
      default:
        return layout;
    }
  };

  // 生成示例卡片
  const generateCards = () => {
    const colors = [
      '#EA4335', // Red
      '#FBBC05', // Yellow
      '#34A853', // Green
      '#4285F4', // Blue
      '#8E44AD', // Purple
      '#16A085', // Teal
    ];

    return colors.map((color, index) => ({
      id: `card-${index}`,
      color,
    }));
  };

  const cards = generateCards();

  // 渲染网格布局预览
  const renderGridPreview = () => {
    return (
      <div className="grid grid-cols-3 gap-1">
        {cards.slice(0, 6).map((card) => (
          <div
            key={card.id}
            className="rounded-sm"
            style={{
              backgroundColor: card.color,
              width: sizeClass.card.w,
              height: sizeClass.card.h,
            }}
          />
        ))}
      </div>
    );
  };

  // 渲染列表布局预览
  const renderListPreview = () => {
    return (
      <div className="flex flex-col gap-1">
        {cards.slice(0, 3).map((card) => (
          <div
            key={card.id}
            className="rounded-sm w-full"
            style={{
              backgroundColor: card.color,
              height: `${Number.parseInt(sizeClass.card.h) / 2}px`,
            }}
          />
        ))}
      </div>
    );
  };

  // 渲染瀑布流布局预览
  const renderMasonryPreview = () => {
    return (
      <div className="flex gap-1">
        <div className="flex flex-col gap-1">
          <div
            className="rounded-sm"
            style={{
              backgroundColor: cards[0].color,
              width: sizeClass.card.w,
              height: `${Number.parseInt(sizeClass.card.h) * 1.5}px`,
            }}
          />
          <div
            className="rounded-sm"
            style={{
              backgroundColor: cards[1].color,
              width: sizeClass.card.w,
              height: sizeClass.card.h,
            }}
          />
        </div>
        <div className="flex flex-col gap-1">
          <div
            className="rounded-sm"
            style={{
              backgroundColor: cards[2].color,
              width: sizeClass.card.w,
              height: sizeClass.card.h,
            }}
          />
          <div
            className="rounded-sm"
            style={{
              backgroundColor: cards[3].color,
              width: sizeClass.card.w,
              height: `${Number.parseInt(sizeClass.card.h) * 1.5}px`,
            }}
          />
        </div>
      </div>
    );
  };

  return (
    <div
      className={`flex flex-col items-center ${onClick ? 'cursor-pointer' : ''} ${isSelected ? 'ring-2 ring-primary p-1 rounded' : 'p-1'}`}
      onClick={onClick}
    >
      <div
        className={`${sizeClass.container} bg-card rounded p-2 flex items-center justify-center border border-border`}
      >
        {layout === 'grid' && renderGridPreview()}
        {layout === 'list' && renderListPreview()}
        {layout === 'masonry' && renderMasonryPreview()}
      </div>
      {showLabel && <span className="text-xs text-muted-foreground mt-1">{getLayoutLabel()}</span>}
    </div>
  );
};

export default CardLayoutPreview;
