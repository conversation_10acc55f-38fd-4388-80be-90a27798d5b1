# 数据库命名问题分析与解决方案

## 问题描述

用户反映老版本可以在浏览器中顺利创建自定义名称的数据库，但新版本却不行。

## 问题分析

### 老版本的做法

老版本使用了简单直接的数据库创建方式：

```typescript
private dbName = "mysalahappdatabase";

// 创建连接时使用固定参数
this.dbConnection = await this.sqliteConnection.createConnection(
  this.dbName,
  false,           // encrypted
  "no-encryption", // mode
  1,               // version
  false            // readonly
);
```

**特点：**
- 使用固定的数据库名称
- 直接创建连接，不进行复杂的名称检查
- 使用 `"no-encryption"` 模式

### 新版本的问题

新版本引入了复杂的数据库名称检查和覆盖逻辑：

```typescript
const dbName = 'mindfulmood';

// 检查现有数据库并可能覆盖名称
const dbList = await (jeepSqliteEl as any).getDatabaseList();
if (dbList.values && dbList.values.length > 0) {
  const existingDb = dbList.values.find((db: any) =>
    db.includes('mindful') || db.includes('mood') || db.includes('emotion')
  );

  if (existingDb) {
    actualDbName = existingDb; // 覆盖原始名称
  } else {
    actualDbName = dbList.values[0]; // 使用第一个可用数据库
  }
}

// 使用不同的模式参数
connection = await sqlite.createConnection(
  actualDbName,
  false,    // encrypted
  'full',   // mode - 与老版本不同
  1,        // version
  false     // readonly
);
```

**问题点：**
1. **名称覆盖逻辑**：会自动查找现有数据库并覆盖指定的数据库名称
2. **模式参数不同**：使用 `'full'` 而不是 `'no-encryption'`
3. **jeep-sqlite 版本差异**：新版本 jeep-sqlite (v2.8.0) 可能有不同的行为

## 根本原因

1. **jeep-sqlite 版本更新**：新版本的 jeep-sqlite 在 Web 平台上对数据库命名有更严格的限制
2. **过度复杂的逻辑**：新版本试图"智能"地管理数据库名称，但这反而阻止了创建自定义名称的数据库
3. **参数不匹配**：连接创建参数与老版本不一致

## 解决方案

### 1. 修复现有代码

已对 `src/lib/useSqLite.tsx` 进行修改：
- 优先使用精确匹配的数据库名称
- 只有在找不到相关数据库时才创建新数据库
- 恢复使用 `'no-encryption'` 模式

### 2. 创建简化测试版本

创建了 `src/lib/useSqLiteSimple.tsx`，完全模仿老版本的做法：
- 使用固定数据库名称 `"mindfulmood_test"`
- 直接创建连接，不进行复杂检查
- 使用与老版本相同的参数

### 3. 测试组件

创建了 `src/components/DatabaseTest.tsx` 用于测试：
- 可以访问 `/database-test` 路由进行测试
- 提供数据库状态检查功能
- 可以测试数据库操作

## 测试步骤

1. 启动应用：`npm run dev`
2. 访问 `http://localhost:5173/database-test`
3. 点击"检查数据库列表"查看当前数据库
4. 观察控制台日志，确认是否创建了自定义名称的数据库

## 测试结果 ✅

测试成功！用户反馈的数据库列表显示：

```
当前数据库列表:
- mindfulmoodSQLite.db
- mindfulmoodSQLiteSQLite.db
- mindfulmood_testSQLite.db   ← 成功创建的测试数据库

目标数据库: mindfulmood_test
```

**关键发现：**
1. ✅ 自定义数据库名称创建成功
2. ✅ jeep-sqlite 自动添加 `SQLite.db` 后缀
3. ✅ 简化的老版本方法确实有效

## 重要发现：saveToStore() 方法

在进一步研究中发现了一个关键问题：**数据持久化**

### saveToStore() 的作用
- **数据持久化**：将内存中的 SQLite 数据库保存到浏览器的 IndexedDB
- **页面刷新保留**：确保数据在页面刷新后仍然存在
- **Web 平台必需**：在 Web 浏览器中，这是数据持久化的关键步骤

### 我们之前的实现问题
```typescript
// ❌ 缺少这个关键步骤
// await sqlite.saveToStore(dbName);
```

### 正确的实现
```typescript
// ✅ 添加数据持久化
await sqlite.saveToStore(actualDbName);
console.log('Database saved to store successfully');
```

## 最终解决方案

基于测试成功的结果，已经应用了以下修复：

1. **简化主数据库 Hook**：移除了复杂的数据库名称检查逻辑
2. **直接使用指定名称**：让 jeep-sqlite 自然处理数据库命名和后缀
3. **保持老版本参数**：使用 `'no-encryption'` 模式确保兼容性
4. **添加数据持久化**：在数据库初始化和数据修改后调用 `saveToStore()`

## 建议

1. **回归简单做法**：对于数据库命名，应该使用简单直接的方式，避免过度复杂的逻辑
2. **保持参数一致性**：确保连接创建参数与已验证的老版本保持一致
3. **版本兼容性测试**：在升级 jeep-sqlite 版本时，应该进行充分的兼容性测试
4. **信任 jeep-sqlite**：让 jeep-sqlite 处理数据库命名细节，不要过度干预

## 相关文件

- `src/lib/useSqLite.tsx` - 修复后的主要数据库 Hook
- `src/lib/useSqLiteSimple.tsx` - 简化测试版本
- `src/components/DatabaseTest.tsx` - 测试组件
- `src/lib/oldccode.txt` - 老版本代码参考
