/**
 * 调试初始化状态的组件
 * 用于监控和显示初始化过程
 */

import { useAppLoading } from '@/contexts/AppLoadingContext';
import { useEmoji } from '@/contexts/EmojiContext';
import { useLanguage } from '@/contexts/LanguageContext';
import InitializationManager, { INITIALIZATION_KEYS } from '@/lib/InitializationManager';
import { useSQLiteDB } from '@/lib/useSqLite';
import { Services } from '@/services';
import type React from 'react';
import { useEffect, useState } from 'react';

interface InitializationStatus {
  database: boolean;
  language: boolean;
  emoji: boolean;
  services: boolean;
}

const DebugInitialization: React.FC = () => {
  const { isDatabaseInitialised } = useSQLiteDB();
  const { language } = useLanguage();
  const { isLoading: emojiLoading, availableEmojiSets } = useEmoji();
  const { isAppLoading, markAppAsLoaded } = useAppLoading();

  const [status, setStatus] = useState<InitializationStatus>({
    database: false,
    language: false,
    emoji: false,
    services: false,
  });

  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testResults, setTestResults] = useState<string[]>([]);

  // 监控初始化状态
  useEffect(() => {
    const updateStatus = () => {
      const initManager = InitializationManager.getInstance();
      const debug = initManager.getDebugInfo();

      setStatus({
        database: isDatabaseInitialised,
        language: !!language,
        emoji: !emojiLoading && availableEmojiSets.length > 0,
        services: debug.states[INITIALIZATION_KEYS.DATABASE_SCHEMA] || false,
      });

      setDebugInfo(debug);
    };

    updateStatus();
    const interval = setInterval(updateStatus, 1000);

    return () => clearInterval(interval);
  }, [isDatabaseInitialised, language, emojiLoading, availableEmojiSets]);

  // 测试服务连接
  const testServices = async () => {
    const results: string[] = [];

    try {
      results.push('🔄 Testing EmojiSetService...');
      const emojiSetService = await Services.emojiSet();
      const emojiSetsResult = await emojiSetService.getAll();

      if (emojiSetsResult.success) {
        results.push(`✅ EmojiSetService: Found ${emojiSetsResult.data?.length || 0} emoji sets`);
      } else {
        results.push(`❌ EmojiSetService: ${emojiSetsResult.error}`);
      }
    } catch (error) {
      results.push(`❌ EmojiSetService: ${error instanceof Error ? error.message : String(error)}`);
    }

    try {
      results.push('🔄 Testing EmotionDataSetService...');
      const emotionDataSetService = await Services.emotionDataSet();
      const dataSetsResult = await emotionDataSetService.getActiveDataSets();

      if (dataSetsResult.success) {
        results.push(
          `✅ EmotionDataSetService: Found ${dataSetsResult.data?.length || 0} active data sets`
        );
        if (dataSetsResult.data && dataSetsResult.data.length > 0) {
          dataSetsResult.data.forEach((ds, index) => {
            results.push(
              `  📋 DataSet ${index + 1}: ${ds.name} (active: ${ds.is_active}, default: ${ds.is_default})`
            );
          });
        }
      } else {
        results.push(`❌ EmotionDataSetService: ${dataSetsResult.error}`);
      }
    } catch (error) {
      results.push(
        `❌ EmotionDataSetService: ${error instanceof Error ? error.message : String(error)}`
      );
    }

    // 测试直接数据库查询
    try {
      results.push('🔄 Testing direct database query...');
      const { DatabaseService } = await import('@/services/base/DatabaseService');
      const dbService = DatabaseService.getInstance();
      const context = await dbService.createContext();

      const queryResult = await context.db.query('SELECT * FROM emotion_data_sets');
      results.push(
        `📊 Direct DB query: Found ${queryResult.values?.length || 0} total emotion data sets`
      );

      if (queryResult.values && queryResult.values.length > 0) {
        queryResult.values.forEach((row, index) => {
          results.push(
            `  📋 Row ${index + 1}: ${row.name} (is_active: ${row.is_active}, is_default: ${row.is_default})`
          );
        });
      }
    } catch (error) {
      results.push(`❌ Direct DB query: ${error instanceof Error ? error.message : String(error)}`);
    }

    setTestResults(results);
  };

  const resetInitialization = () => {
    const initManager = InitializationManager.getInstance();
    initManager.reset();
    setTestResults(['🔄 Initialization state reset']);
  };

  const forceAppLoad = () => {
    console.log('[DebugInitialization] Manually triggering app load...');
    markAppAsLoaded();
    setTestResults(['🚀 App load triggered manually']);
  };

  const fixEmotionDataSets = async () => {
    const results: string[] = [];

    try {
      results.push('🔧 Attempting to fix emotion data sets...');
      const { DatabaseService } = await import('@/services/base/DatabaseService');
      const dbService = DatabaseService.getInstance();
      const context = await dbService.createContext();

      // 更新所有情绪数据集为活跃状态
      const updateResult = await context.db.run(
        'UPDATE emotion_data_sets SET is_active = 1 WHERE is_active = 0 OR is_active IS NULL'
      );

      results.push(`✅ Updated ${updateResult.changes || 0} emotion data sets to active status`);

      // 验证修复
      const verifyResult = await context.db.query(
        'SELECT * FROM emotion_data_sets WHERE is_active = 1'
      );
      results.push(
        `✅ Verification: Found ${verifyResult.values?.length || 0} active emotion data sets`
      );
    } catch (error) {
      results.push(`❌ Fix failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    setTestResults(results);
  };

  const diagnoseDatabase = async () => {
    const results: string[] = [];

    try {
      results.push('🔍 Diagnosing database tables...');
      const { DatabaseService } = await import('@/services/base/DatabaseService');
      const dbService = DatabaseService.getInstance();
      const context = await dbService.createContext();

      // 定义要检查的表
      const tables = [
        'users',
        'emotions',
        'emotion_translations',
        'tags',
        'tag_translations',
        'emotion_data_sets',
        'emotion_data_set_tiers',
        'emotion_data_set_emotions',
        'emoji_sets',
        'emoji_items',
        'skins',
        'ui_labels',
        'ui_label_translations',
      ];

      results.push('📊 Table record counts:');

      for (const table of tables) {
        try {
          const countResult = await context.db.query(`SELECT COUNT(*) as count FROM ${table}`);
          const count = countResult.values?.[0]?.count || 0;
          results.push(`  📋 ${table}: ${count} records`);
        } catch (error) {
          results.push(
            `  ❌ ${table}: Error - ${error instanceof Error ? error.message : String(error)}`
          );
        }
      }
    } catch (error) {
      results.push(
        `❌ Diagnosis failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }

    setTestResults(results);
  };

  const reloadInitialData = async () => {
    const results: string[] = [];

    try {
      results.push('🔄 Reloading initial data...');
      const { DatabaseService } = await import('@/services/base/DatabaseService');
      const dbService = DatabaseService.getInstance();
      const context = await dbService.createContext();

      // 重新加载初始数据 - 使用 master.sql 控制的加载顺序
      // 临时禁用外键约束
      await context.db.run('PRAGMA foreign_keys=OFF');
      results.push('🔧 Disabled foreign key constraints for data loading');

      // 辅助函数：执行数据文件
      const executeDataFile = async (sqlContent: string, fileName: string) => {
        const statements = sqlContent
          .split('\n')
          .map((line) => line.trim())
          .filter((line) => line.length > 0 && !line.startsWith('--') && !line.startsWith('PRAGMA'))
          .join(' ')
          .split(';')
          .map((stmt) => stmt.trim())
          .filter((stmt) => stmt.length > 0 && stmt.toUpperCase().includes('INSERT'));

        results.push(`📝 Found ${statements.length} statements in ${fileName}`);

        let successCount = 0;
        let errorCount = 0;

        for (let i = 0; i < statements.length; i++) {
          try {
            await context.db.run(statements[i]);
            successCount++;
          } catch (error) {
            errorCount++;
            console.error(`Error in ${fileName} statement ${i + 1}:`, statements[i], error);
          }
        }

        results.push(`  ✅ ${fileName}: ${successCount} success, ${errorCount} errors`);
        return { successCount, errorCount };
      };

      let totalSuccess = 0;
      let totalErrors = 0;

      // 1. 加载核心数据 (init.sql)
      results.push('📝 Loading core data from init.sql...');
      const initResponse = await fetch('/seeds/schema/init.sql');
      if (initResponse.ok) {
        const initSQL = await initResponse.text();
        const initResult = await executeDataFile(initSQL, 'init.sql');
        totalSuccess += initResult.successCount;
        totalErrors += initResult.errorCount;
      }

      // 2. 加载情绪数据集层级 (emotion_data_set_tiers.sql)
      results.push('📝 Loading emotion data set tiers from emotion_data_set_tiers.sql...');
      const tiersResponse = await fetch('/seeds/schema/emotion_data_set_tiers.sql');
      if (tiersResponse.ok) {
        const tiersSQL = await tiersResponse.text();
        const tiersResult = await executeDataFile(tiersSQL, 'emotion_data_set_tiers.sql');
        totalSuccess += tiersResult.successCount;
        totalErrors += tiersResult.errorCount;
      }

      // 3. 加载额外的表情集 (additional_emoji_init.sql)
      results.push('📝 Loading additional emoji sets from additional_emoji_init.sql...');
      const additionalEmojiResponse = await fetch('/seeds/schema/additional_emoji_init.sql');
      if (additionalEmojiResponse.ok) {
        const additionalEmojiSQL = await additionalEmojiResponse.text();
        const additionalEmojiResult = await executeDataFile(
          additionalEmojiSQL,
          'additional_emoji_init.sql'
        );
        totalSuccess += additionalEmojiResult.successCount;
        totalErrors += additionalEmojiResult.errorCount;
      }

      // 4. 加载扩展表情项目 (extended_emoji_items.sql)
      results.push('📝 Loading extended emoji items from extended_emoji_items.sql...');
      const extendedEmojiResponse = await fetch('/seeds/schema/extended_emoji_items.sql');
      if (extendedEmojiResponse.ok) {
        const extendedEmojiSQL = await extendedEmojiResponse.text();
        const extendedEmojiResult = await executeDataFile(
          extendedEmojiSQL,
          'extended_emoji_items.sql'
        );
        totalSuccess += extendedEmojiResult.successCount;
        totalErrors += extendedEmojiResult.errorCount;
      }

      // 5. 加载 UI 标签 (ui_labels.sql)
      results.push('📝 Loading UI labels from ui_labels.sql...');
      const uiLabelsResponse = await fetch('/seeds/schema/ui_labels.sql');
      if (uiLabelsResponse.ok) {
        const uiLabelsSQL = await uiLabelsResponse.text();
        const labelsResult = await executeDataFile(uiLabelsSQL, 'ui_labels.sql');
        totalSuccess += labelsResult.successCount;
        totalErrors += labelsResult.errorCount;
      }

      // 6. 加载 UI 标签翻译 (ui_label_translations.sql)
      results.push('📝 Loading UI label translations from ui_label_translations.sql...');
      const uiTranslationsResponse = await fetch('/seeds/schema/ui_label_translations.sql');
      if (uiTranslationsResponse.ok) {
        const uiTranslationsSQL = await uiTranslationsResponse.text();
        const translationsResult = await executeDataFile(
          uiTranslationsSQL,
          'ui_label_translations.sql'
        );
        totalSuccess += translationsResult.successCount;
        totalErrors += translationsResult.errorCount;
      }

      // 重新启用外键约束
      await context.db.run('PRAGMA foreign_keys=ON');
      results.push('🔧 Re-enabled foreign key constraints');

      results.push(`✅ Successfully executed ${totalSuccess} statements`);
      if (totalErrors > 0) {
        results.push(`⚠️ ${totalErrors} statements failed (likely due to existing data)`);
      }

      // 验证数据加载
      const verifyResult = await context.db.query(
        'SELECT COUNT(*) as count FROM emotion_data_sets'
      );
      const count = verifyResult.values?.[0]?.count || 0;
      results.push(`✅ Verification: Found ${count} emotion data sets`);
    } catch (error) {
      results.push(`❌ Reload failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    setTestResults(results);
  };

  const fixMissingData = async () => {
    const results: string[] = [];

    try {
      results.push('🔧 Fixing missing data...');
      const { DatabaseService } = await import('@/services/base/DatabaseService');
      const dbService = DatabaseService.getInstance();
      const context = await dbService.createContext();

      // 1. 插入缺失的 emotion_data_set_tiers
      results.push('📝 Inserting emotion_data_set_tiers...');
      const tierStatements = [
        "INSERT OR IGNORE INTO emotion_data_set_tiers (id, emotion_data_set_id, name, level, description) VALUES ('tier-1', 'default-emotion-wheel', 'Primary Emotions', 1, 'Basic emotional categories')",
        "INSERT OR IGNORE INTO emotion_data_set_tiers (id, emotion_data_set_id, name, level, description) VALUES ('tier-2', 'default-emotion-wheel', 'Secondary Emotions', 2, 'More specific emotional states')",
        "INSERT OR IGNORE INTO emotion_data_set_tiers (id, emotion_data_set_id, name, level, description) VALUES ('tier-3', 'default-emotion-wheel', 'Tertiary Emotions', 3, 'Detailed emotional nuances')",
      ];

      for (const stmt of tierStatements) {
        await context.db.run(stmt);
      }
      results.push('✅ Inserted 3 emotion_data_set_tiers');

      // 2. 插入缺失的 emotion_data_set_emotions
      results.push('📝 Inserting emotion_data_set_emotions...');
      const emotionStatements = [
        // Primary emotions in tier 1
        "INSERT OR IGNORE INTO emotion_data_set_emotions (id, emotion_data_set_id, tier_id, emotion_id, position) VALUES ('eds-1', 'default-emotion-wheel', 'tier-1', 'happy', 1)",
        "INSERT OR IGNORE INTO emotion_data_set_emotions (id, emotion_data_set_id, tier_id, emotion_id, position) VALUES ('eds-2', 'default-emotion-wheel', 'tier-1', 'surprised', 2)",
        "INSERT OR IGNORE INTO emotion_data_set_emotions (id, emotion_data_set_id, tier_id, emotion_id, position) VALUES ('eds-3', 'default-emotion-wheel', 'tier-1', 'bad', 3)",
        "INSERT OR IGNORE INTO emotion_data_set_emotions (id, emotion_data_set_id, tier_id, emotion_id, position) VALUES ('eds-4', 'default-emotion-wheel', 'tier-1', 'fearful', 4)",
        "INSERT OR IGNORE INTO emotion_data_set_emotions (id, emotion_data_set_id, tier_id, emotion_id, position) VALUES ('eds-5', 'default-emotion-wheel', 'tier-1', 'angry', 5)",
        "INSERT OR IGNORE INTO emotion_data_set_emotions (id, emotion_data_set_id, tier_id, emotion_id, position) VALUES ('eds-6', 'default-emotion-wheel', 'tier-1', 'disgusted', 6)",
        "INSERT OR IGNORE INTO emotion_data_set_emotions (id, emotion_data_set_id, tier_id, emotion_id, position) VALUES ('eds-7', 'default-emotion-wheel', 'tier-1', 'sad', 7)",
      ];

      for (const stmt of emotionStatements) {
        await context.db.run(stmt);
      }
      results.push('✅ Inserted 7 emotion_data_set_emotions');

      // 3. 先插入 UI 标签键
      results.push('📝 Inserting ui_labels...');
      const uiLabelStatements = [
        "INSERT OR IGNORE INTO ui_labels (key, default_text) VALUES ('app.title', 'Mindful Mood')",
        "INSERT OR IGNORE INTO ui_labels (key, default_text) VALUES ('app.home', 'Home')",
        "INSERT OR IGNORE INTO ui_labels (key, default_text) VALUES ('app.history', 'History')",
        "INSERT OR IGNORE INTO ui_labels (key, default_text) VALUES ('app.analytics', 'Analytics')",
        "INSERT OR IGNORE INTO ui_labels (key, default_text) VALUES ('app.settings', 'Settings')",
      ];

      for (const stmt of uiLabelStatements) {
        await context.db.run(stmt);
      }
      results.push('✅ Inserted 5 ui_labels');

      // 4. 然后插入 UI 标签翻译
      results.push('📝 Inserting ui_label_translations...');
      const uiTranslationStatements = [
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.title', 'en', 'Mindful Mood')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.home', 'en', 'Home')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.history', 'en', 'History')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.analytics', 'en', 'Analytics')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.settings', 'en', 'Settings')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.title', 'zh', '心情记录')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.home', 'zh', '首页')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.history', 'zh', '历史')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.analytics', 'zh', '分析')",
        "INSERT OR IGNORE INTO ui_label_translations (label_key, language_code, translated_text) VALUES ('app.settings', 'zh', '设置')",
      ];

      for (const stmt of uiTranslationStatements) {
        await context.db.run(stmt);
      }
      results.push('✅ Inserted 10 ui_label_translations');

      // 验证修复
      const verifyTiers = await context.db.query(
        'SELECT COUNT(*) as count FROM emotion_data_set_tiers'
      );
      const verifyEmotions = await context.db.query(
        'SELECT COUNT(*) as count FROM emotion_data_set_emotions'
      );
      const verifyUILabels = await context.db.query('SELECT COUNT(*) as count FROM ui_labels');
      const verifyUITranslations = await context.db.query(
        'SELECT COUNT(*) as count FROM ui_label_translations'
      );

      results.push('✅ Verification:');
      results.push(`  📋 emotion_data_set_tiers: ${verifyTiers.values?.[0]?.count || 0} records`);
      results.push(
        `  📋 emotion_data_set_emotions: ${verifyEmotions.values?.[0]?.count || 0} records`
      );
      results.push(`  📋 ui_labels: ${verifyUILabels.values?.[0]?.count || 0} records`);
      results.push(
        `  📋 ui_label_translations: ${verifyUITranslations.values?.[0]?.count || 0} records`
      );
    } catch (error) {
      results.push(`❌ Fix failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    setTestResults(results);
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-bold mb-4">🔧 Initialization Debug Panel</h3>

      {/* 状态显示 */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="bg-white p-3 rounded">
          <h4 className="font-semibold mb-2">Component Status</h4>
          <div className="space-y-1 text-sm">
            <div
              className={`flex items-center ${status.database ? 'text-green-600' : 'text-red-600'}`}
            >
              {status.database ? '✅' : '❌'} Database: {status.database ? 'Ready' : 'Not Ready'}
            </div>
            <div
              className={`flex items-center ${status.language ? 'text-green-600' : 'text-red-600'}`}
            >
              {status.language ? '✅' : '❌'} Language: {language || 'Not Set'}
            </div>
            <div
              className={`flex items-center ${status.emoji ? 'text-green-600' : 'text-red-600'}`}
            >
              {status.emoji ? '✅' : '❌'} Emoji:{' '}
              {status.emoji ? `${availableEmojiSets.length} sets` : 'Loading...'}
            </div>
            <div
              className={`flex items-center ${status.services ? 'text-green-600' : 'text-red-600'}`}
            >
              {status.services ? '✅' : '❌'} Services: {status.services ? 'Ready' : 'Not Ready'}
            </div>
            <div
              className={`flex items-center ${!isAppLoading ? 'text-green-600' : 'text-orange-600'}`}
            >
              {!isAppLoading ? '✅' : '⏳'} App Loading:{' '}
              {!isAppLoading ? 'Complete' : 'In Progress'}
            </div>
          </div>
        </div>

        <div className="bg-white p-3 rounded">
          <h4 className="font-semibold mb-2">Initialization Manager</h4>
          <div className="text-xs">
            <div>
              <strong>Initialized:</strong>
            </div>
            <ul className="list-disc list-inside">
              {Object.entries(debugInfo.states || {}).map(([key, value]) => (
                <li key={key} className={value ? 'text-green-600' : 'text-red-600'}>
                  {key}: {value ? '✅' : '❌'}
                </li>
              ))}
            </ul>
            <div className="mt-2">
              <strong>Pending:</strong> {debugInfo.pendingPromises?.join(', ') || 'None'}
            </div>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-2 mb-4">
        <button
          onClick={testServices}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          Test Services
        </button>
        <button
          onClick={resetInitialization}
          className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
        >
          Reset Init State
        </button>
        <button
          onClick={forceAppLoad}
          className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
          disabled={!isAppLoading}
        >
          Force App Load
        </button>
        <button
          onClick={fixEmotionDataSets}
          className="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
        >
          Fix Data Sets
        </button>
        <button
          onClick={diagnoseDatabase}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          Diagnose DB
        </button>
        <button
          onClick={reloadInitialData}
          className="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
        >
          Reload Data
        </button>
        <button
          onClick={fixMissingData}
          className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
        >
          Fix Missing Data
        </button>
      </div>

      {/* 测试结果 */}
      {testResults.length > 0 && (
        <div className="bg-white p-3 rounded">
          <h4 className="font-semibold mb-2">Test Results</h4>
          <div className="text-xs space-y-1">
            {testResults.map((result, index) => (
              <div key={index} className="font-mono">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DebugInitialization;
