/**
 * Quiz基础组件抽象类
 * 所有Quiz组件的基类，提供通用功能
 */

import React, { Component } from 'react';
import { z } from 'zod';
import {
  BaseComponentConfigSchema,
  InteractionEventSchema,
  ComponentStateSchema,
  QuizComponentTypeSchema,
  InteractionTypeSchema,
  AnimationEffectSchema
} from '@/types/schema/base';

// 类型定义
export type QuizComponentType = z.infer<typeof QuizComponentTypeSchema>;
export type InteractionType = z.infer<typeof InteractionTypeSchema>;
export type AnimationEffect = z.infer<typeof AnimationEffectSchema>;
export type InteractionEvent = z.infer<typeof InteractionEventSchema>;
export type ComponentState = z.infer<typeof ComponentStateSchema>;
export type BaseComponentConfig = z.infer<typeof BaseComponentConfigSchema>;

// 个性化配置接口
export interface PersonalizationConfig {
  layer3_skin_base?: {
    fonts?: {
      size_scale?: number;
      primary_font?: string;
    };
    colors?: Record<string, string>;
    animations?: {
      enable_animations?: boolean;
      animation_speed?: string;
      reduce_motion?: boolean;
    };
  };
  layer5_accessibility?: {
    large_text?: boolean;
    high_contrast?: boolean;
    reduce_motion?: boolean;
    keyboard_navigation?: boolean;
    screen_reader_support?: boolean;
  };
}

// 基础组件属性接口
export interface BaseQuizComponentProps<TConfig = any> {
  id: string;
  config: TConfig;
  personalization?: PersonalizationConfig;
  onInteraction?: (event: InteractionEvent) => void;
  onStateChange?: (newState: ComponentState) => void;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Quiz基础组件抽象类
 */
export abstract class BaseQuizComponent<
  TConfig extends BaseComponentConfig = BaseComponentConfig,
  TProps extends BaseQuizComponentProps<TConfig> = BaseQuizComponentProps<TConfig>,
  TState extends ComponentState = ComponentState
> extends Component<TProps, TState> {
  
  protected config: TConfig;
  protected personalization: PersonalizationConfig;
  
  constructor(props: TProps) {
    super(props);
    this.config = this.extractConfig(props);
    this.personalization = this.extractPersonalization(props);
    
    // 初始化状态
    this.state = this.getInitialState();
  }
  
  // 抽象方法：子类必须实现
  abstract extractConfig(props: TProps): TConfig;
  abstract getInitialState(): TState;
  abstract render(): React.ReactNode;
  
  // 通用方法
  protected extractPersonalization(props: TProps): PersonalizationConfig {
    return props.personalization || {};
  }
  
  /**
   * 应用个性化配置到基础配置
   */
  protected applyPersonalization(baseConfig: TConfig): TConfig {
    const personalizedConfig = { ...baseConfig };
    
    // 应用字体配置
    if (this.personalization.layer3_skin_base?.fonts) {
      const fonts = this.personalization.layer3_skin_base.fonts;
      if (fonts.size_scale && personalizedConfig.style) {
        // 应用字体缩放
        personalizedConfig.style = {
          ...personalizedConfig.style,
          fontSize: `calc(var(--font-size-base) * ${fonts.size_scale})`
        };
      }
    }
    
    // 应用可访问性配置
    if (this.personalization.layer5_accessibility) {
      const accessibility = this.personalization.layer5_accessibility;
      
      if (accessibility.large_text && personalizedConfig.style) {
        personalizedConfig.style = {
          ...personalizedConfig.style,
          fontSize: 'calc(var(--font-size-base) * 1.2)'
        };
      }
      
      if (accessibility.high_contrast && personalizedConfig.style) {
        personalizedConfig.style = {
          ...personalizedConfig.style,
          filter: 'contrast(1.5)'
        };
      }
    }
    
    return personalizedConfig;
  }
  
  /**
   * 发送交互事件
   */
  protected emitInteractionEvent(type: InteractionType, data: any, metadata?: Record<string, any>): void {
    const event: InteractionEvent = {
      type,
      target: this.props.id,
      data,
      timestamp: Date.now(),
      metadata: {
        component_type: this.config.component_type,
        user_agent: navigator.userAgent,
        ...metadata
      }
    };
    
    this.props.onInteraction?.(event);
  }
  
  /**
   * 更新组件状态
   */
  protected updateComponentState(newState: Partial<TState>): void {
    this.setState(newState as TState, () => {
      this.props.onStateChange?.(this.state);
    });
  }
  
  /**
   * 获取应用了个性化配置的样式
   */
  protected getPersonalizedStyles(): React.CSSProperties {
    const baseStyles: React.CSSProperties = {};
    
    // 应用字体配置
    if (this.personalization.layer3_skin_base?.fonts?.primary_font) {
      baseStyles.fontFamily = this.getFontFamily(
        this.personalization.layer3_skin_base.fonts.primary_font
      );
    }
    
    // 应用颜色配置
    if (this.personalization.layer3_skin_base?.colors) {
      const colors = this.personalization.layer3_skin_base.colors;
      if (colors.primary) baseStyles.color = colors.primary;
    }
    
    // 应用动画配置
    if (this.personalization.layer3_skin_base?.animations?.reduce_motion || 
        this.personalization.layer5_accessibility?.reduce_motion) {
      baseStyles.transition = 'none';
      baseStyles.animation = 'none';
    }
    
    return baseStyles;
  }
  
  /**
   * 获取字体族
   */
  private getFontFamily(family: string): string {
    const fontMap: Record<string, string> = {
      'modern': 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
      'traditional': 'PingFang SC, Source Han Sans, sans-serif',
      'calligraphy': 'STKaiti, KaiTi, serif'
    };
    
    return fontMap[family] || fontMap.modern;
  }
  
  /**
   * 处理键盘导航
   */
  protected handleKeyNavigation = (event: React.KeyboardEvent): void => {
    if (!this.personalization.layer5_accessibility?.keyboard_navigation) {
      return;
    }
    
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        this.emitInteractionEvent('click', { key: event.key });
        break;
      case 'Escape':
        event.preventDefault();
        this.emitInteractionEvent('focus', { action: 'escape' });
        break;
    }
  };
  
  /**
   * 处理触觉反馈
   */
  protected triggerHapticFeedback(type: 'light' | 'medium' | 'heavy' = 'light'): void {
    if ('vibrate' in navigator) {
      const patterns = {
        light: 50,
        medium: 100,
        heavy: 200
      };
      navigator.vibrate(patterns[type]);
    }
  }
  
  /**
   * 获取可访问性属性
   */
  protected getAccessibilityProps(): Record<string, any> {
    const props: Record<string, any> = {};
    
    if (this.personalization.layer5_accessibility?.keyboard_navigation) {
      props.tabIndex = 0;
      props.onKeyDown = this.handleKeyNavigation;
    }
    
    if (this.personalization.layer5_accessibility?.screen_reader_support) {
      props.role = this.getAriaRole();
      props['aria-label'] = this.getAriaLabel();
    }
    
    return props;
  }
  
  /**
   * 获取ARIA角色 - 子类可以重写
   */
  protected getAriaRole(): string {
    return 'button';
  }
  
  /**
   * 获取ARIA标签 - 子类可以重写
   */
  protected getAriaLabel(): string {
    return `${this.config.component_type} component`;
  }
  
  /**
   * 组件更新时的处理
   */
  componentDidUpdate(prevProps: TProps): void {
    if (prevProps.config !== this.props.config) {
      this.config = this.extractConfig(this.props);
    }
    
    if (prevProps.personalization !== this.props.personalization) {
      this.personalization = this.extractPersonalization(this.props);
    }
  }
}

export default BaseQuizComponent;
