/**
 * 服务集成测试
 * 验证重构后的服务功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { PaymentService } from '../../services/PaymentService';
import { SyncService } from '../../services/SyncService';
import { QuizService } from '../../services/QuizService';

// Mock 数据库操作
vi.mock('../../database/index', () => ({
  executeQuery: vi.fn(),
  batchStatements: vi.fn()
}));

// Mock Stripe
vi.mock('stripe', () => ({
  default: vi.fn(() => ({
    customers: { create: vi.fn(), retrieve: vi.fn() },
    paymentMethods: { attach: vi.fn() },
    paymentIntents: { create: vi.fn(), confirm: vi.fn() },
    subscriptions: { create: vi.fn() },
    webhooks: { constructEvent: vi.fn() }
  }))
}));

// 导入 Mock 后的模块
import { executeQuery, batchStatements } from '../../database/index';

const mockExecuteQuery = vi.mocked(executeQuery);
const mockBatchStatements = vi.mocked(batchStatements);

// Mock 环境变量
process.env.STRIPE_SECRET_KEY = 'sk_test_mock_key';
process.env.STRIPE_PUBLISHABLE_KEY = 'pk_test_mock_key';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_mock_secret';

describe('服务端重构后的集成测试', () => {
  let paymentService: PaymentService;
  let syncService: SyncService;
  let quizService: QuizService;

  beforeEach(() => {
    paymentService = PaymentService.getInstance();
    syncService = SyncService.getInstance();
    quizService = QuizService.getInstance();
    vi.clearAllMocks();
  });

  describe('PaymentService 集成测试', () => {
    it('应该成功获取 VIP 计划', async () => {
      mockExecuteQuery.mockResolvedValue({
        rows: [
          {
            id: 'vip_monthly',
            name: 'VIP Monthly',
            description: 'Monthly VIP subscription',
            price: 9.99,
            currency: 'USD',
            duration: 'monthly',
            features: '["unlimited_mood_entries", "advanced_analytics"]',
            stripePriceId: 'price_monthly_test',
            is_active: 1
          }
        ],
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await paymentService.getVipPlans();

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].name).toBe('VIP Monthly');
      expect(result.data![0].features).toEqual(['unlimited_mood_entries', 'advanced_analytics']);
    });

    it('应该返回 Stripe 公钥', () => {
      const result = paymentService.getPublishableKey();
      expect(result).toBe('pk_test_mock_key');
    });

    it('应该处理数据库错误', async () => {
      mockExecuteQuery.mockRejectedValue(new Error('Database connection failed'));

      const result = await paymentService.getVipPlans();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database connection failed');
    });
  });

  describe('SyncService 集成测试', () => {
    it('应该成功执行完整同步', async () => {
      // Mock 所有下载查询返回空结果
      mockExecuteQuery.mockResolvedValue({
        rows: [],
        changes: 0,
        lastInsertRowid: 0
      });

      mockBatchStatements.mockResolvedValue([{
        rows: [],
        changes: 1,
        lastInsertRowid: 0
      }]);

      const syncRequest = {
        userId: 'user123',
        lastSyncTimestamp: '2024-01-01T00:00:00Z',
        moodEntriesToUpload: [{
          id: 'mood1',
          user_id: 'user123',
          timestamp: '2024-01-01T12:00:00Z',
          emotion_data_set_id: 'emotion_wheel',
          intensity: 7,
          reflection: 'Feeling good today',
          tags: ['happy', 'productive'],
          created_at: '2024-01-01T12:00:00Z',
          updated_at: '2024-01-01T12:00:00Z'
        }],
        emotionSelectionsToUpload: [],
        userConfigsToUpload: [],
        tagsToUpload: [],
        quizSessionsToUpload: [],
        quizAnswersToUpload: [],
        quizResultsToUpload: []
      };

      const result = await syncService.performFullSync(syncRequest);

      expect(result.success).toBe(true);
      expect(result.uploadedCount).toBe(1);
      expect(result.newMoodEntriesFromServer).toEqual([]);
      expect(result.newQuizSessionsFromServer).toEqual([]);
    });

    it('应该获取同步统计信息', async () => {
      mockExecuteQuery
        .mockResolvedValueOnce({ rows: [{ count: 25 }], changes: 0, lastInsertRowid: 0 })
        .mockResolvedValueOnce({ rows: [{ count: 5 }], changes: 0, lastInsertRowid: 0 })
        .mockResolvedValueOnce({ rows: [{ count: 2 }], changes: 0, lastInsertRowid: 0 })
        .mockResolvedValueOnce({ rows: [{ last_sync: '2024-01-02T12:00:00Z' }], changes: 0, lastInsertRowid: 0 });

      const result = await syncService.getSyncStatistics('user123');

      expect(result.success).toBe(true);
      expect(result.statistics!.totalMoodEntries).toBe(25);
      expect(result.statistics!.totalQuizSessions).toBe(5);
      expect(result.statistics!.totalPaymentTransactions).toBe(2);
    });

    it('应该处理同步错误', async () => {
      mockExecuteQuery.mockRejectedValue(new Error('Sync service unavailable'));

      const result = await syncService.getSyncStatistics('user123');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Sync service unavailable');
    });
  });

  describe('QuizService 集成测试', () => {
    it('应该获取可用的 Quiz 包', async () => {
      mockExecuteQuery.mockResolvedValue({
        rows: [
          {
            id: 'pack1',
            name: 'Test Pack',
            description: 'Test Description',
            quiz_type: 'emotion_wheel',
            is_active: true,
            metadata: '{"category": "daily", "difficulty": "beginner"}'
          }
        ],
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await quizService.getAvailableQuizPacks('user123', 'free');

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].name).toBe('Test Pack');
    });

    it('应该验证 Quiz 会话', async () => {
      mockExecuteQuery.mockResolvedValue({
        rows: [
          {
            id: 'pack1',
            name: 'Test Pack',
            is_active: 1
          }
        ],
        changes: 0,
        lastInsertRowid: 0
      });

      const sessionData = {
        id: 'session1',
        pack_id: 'pack1',
        user_id: 'user1',
        status: 'ACTIVE' as const,
        current_question_index: 0,
        total_questions: 5,
        answered_questions: 0,
        skipped_questions: 0,
        completion_percentage: 0,
        start_time: '2024-01-01T00:00:00Z',
        last_active_time: '2024-01-01T00:00:00Z',
        end_time: null,
        session_type: 'standard',
        session_metadata: '{}',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = await quizService.validateQuizSession(sessionData);

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
    });

    it('应该处理 Quiz 服务错误', async () => {
      mockExecuteQuery.mockRejectedValue(new Error('Quiz service unavailable'));

      const result = await quizService.getAvailableQuizPacks('user123', 'free');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Quiz service unavailable');
    });
  });

  describe('服务单例模式验证', () => {
    it('PaymentService 应该返回相同的实例', () => {
      const instance1 = PaymentService.getInstance();
      const instance2 = PaymentService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('SyncService 应该返回相同的实例', () => {
      const instance1 = SyncService.getInstance();
      const instance2 = SyncService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('QuizService 应该返回相同的实例', () => {
      const instance1 = QuizService.getInstance();
      const instance2 = QuizService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('架构一致性验证', () => {
    it('所有服务都应该使用统一的数据库操作', () => {
      // 这个测试验证所有服务都使用相同的数据库接口
      expect(mockExecuteQuery).toBeDefined();
      expect(mockBatchStatements).toBeDefined();
    });

    it('所有服务都应该返回标准化的响应格式', async () => {
      mockExecuteQuery.mockResolvedValue({
        rows: [],
        changes: 0,
        lastInsertRowid: 0
      });

      const paymentResult = await paymentService.getVipPlans();
      const syncResult = await syncService.getSyncStatistics('user123');
      const quizResult = await quizService.getAvailableQuizPacks('user123', 'free');

      // 验证所有服务都返回标准化的响应格式
      expect(paymentResult).toHaveProperty('success');
      expect(syncResult).toHaveProperty('success');
      expect(quizResult).toHaveProperty('success');

      expect(typeof paymentResult.success).toBe('boolean');
      expect(typeof syncResult.success).toBe('boolean');
      expect(typeof quizResult.success).toBe('boolean');
    });
  });
});
