import fs from 'node:fs';
import path from 'node:path';

// 时间字段相关的模式
const timeFieldPatterns = {
  // Date 对象创建
  dateCreation: [
    /new Date\(\)/g,
    /new Date\([^)]*\)/g,
  ],

  // Date 方法调用
  dateMethods: [
    /\.getTime\(\)/g,
    /\.toISOString\(\)/g,
    /\.toDateString\(\)/g,
    /\.getFullYear\(\)/g,
    /\.getMonth\(\)/g,
    /\.getDate\(\)/g,
    /\.getHours\(\)/g,
    /\.getMinutes\(\)/g,
    /\.getSeconds\(\)/g,
  ],

  // 时间戳字段访问（可能需要修复的）
  timestampFields: [
    /\.timestamp\b/g,
    /\.created_at\b/g,
    /\.updated_at\b/g,
    /\.last_updated\b/g,
    /\.createdAt\b/g,
    /\.updatedAt\b/g,
    /\.lastUpdated\b/g,
    /\.expiresAt\b/g,
    /\.expires_at\b/g,
    /\.last_synced_at\b/g,
    /\.lastSyncedAt\b/g,
    /\.banned_until\b/g,
    /\.bannedUntil\b/g,
    /\.vip_expires_at\b/g,
    /\.vipExpiresAt\b/g,
  ],

  // Date 类型转换
  dateConversions: [
    /typeof.*===.*['"]string['"].*new Date/g,
    /new Date\([^)]*timestamp[^)]*\)/g,
    /new Date\([^)]*created_at[^)]*\)/g,
    /new Date\([^)]*updated_at[^)]*\)/g,
  ],

  // Date.now() 使用
  dateNow: [
    /Date\.now\(\)/g,
  ],

  // 时间比较操作
  timeComparisons: [
    /\.getTime\(\)\s*[<>=!]+/g,
    /[<>=!]+\s*\.getTime\(\)/g,
  ]
};

// 获取所有 TypeScript 文件
function getAllTsFiles(dir) {
  const files = [];

  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 跳过 node_modules 和其他不需要的目录
        if (!['node_modules', '.git', 'dist', 'build', '.next'].includes(item)) {
          traverse(fullPath);
        }
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

// 检测文件中的时间字段问题
function detectTimeFieldIssues(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];

  lines.forEach((line, index) => {
    const lineNumber = index + 1;

    // 检测各种时间相关模式
    for (const [category, patterns] of Object.entries(timeFieldPatterns)) {
      for (const pattern of patterns) {
        const matches = line.match(pattern);
        if (matches) {
          matches.forEach(match => {
            issues.push({
              file: filePath,
              line: lineNumber,
              category,
              pattern: match,
              context: line.trim(),
              severity: getSeverity(category, match)
            });
          });
        }
      }
    }
  });

  return issues;
}

// 确定问题严重性
function getSeverity(category, match) {
  // 需要立即修复的问题
  if (category === 'timestampFields' && match.includes('At')) {
    return 'error'; // 驼峰命名的时间字段
  }

  if (category === 'dateCreations' && match === 'new Date()') {
    return 'warning'; // 可能需要改为 new Date().toISOString()
  }

  if (category === 'dateConversions') {
    return 'warning'; // 需要检查是否正确处理字符串时间戳
  }

  return 'info'; // 其他情况仅供参考
}

// 主函数
function main() {
  const srcDir = path.join(process.cwd(), 'src');
  const files = getAllTsFiles(srcDir);

  console.log(`🔍 检测 ${files.length} 个 TypeScript 文件中的时间字段使用问题...\n`);

  let totalIssues = 0;
  const issuesByCategory = {};
  const allIssues = [];

  for (const file of files) {
    const issues = detectTimeFieldIssues(file);
    if (issues.length > 0) {
      allIssues.push(...issues);
      totalIssues += issues.length;

      issues.forEach(issue => {
        if (!issuesByCategory[issue.category]) {
          issuesByCategory[issue.category] = 0;
        }
        issuesByCategory[issue.category]++;
      });
    }
  }

  // 按文件分组显示问题
  const issuesByFile = {};
  allIssues.forEach(issue => {
    if (!issuesByFile[issue.file]) {
      issuesByFile[issue.file] = [];
    }
    issuesByFile[issue.file].push(issue);
  });

  // 显示统计信息
  console.log('📊 检测结果统计:');
  console.log(`总问题数: ${totalIssues}`);
  console.log(`问题文件数: ${Object.keys(issuesByFile).length}`);
  console.log('\n按类别统计:');
  for (const [category, count] of Object.entries(issuesByCategory)) {
    console.log(`  ${category}: ${count}`);
  }

  // 显示详细问题
  console.log('\n🔍 详细问题列表:\n');

  for (const [file, issues] of Object.entries(issuesByFile)) {
    const relativePath = path.relative(process.cwd(), file);
    console.log(`📁 ${relativePath}`);

    // 按严重性排序
    const sortedIssues = issues.sort((a, b) => {
      const severityOrder = { error: 0, warning: 1, info: 2 };
      return severityOrder[a.severity] - severityOrder[b.severity];
    });

    sortedIssues.forEach(issue => {
      const icon = issue.severity === 'error' ? '❌' : issue.severity === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`  ${icon} Line ${issue.line}: [${issue.category}] ${issue.pattern}`);
      console.log(`     Context: ${issue.context}`);
    });
    console.log('');
  }

  // 生成修复建议
  console.log('\n💡 修复建议:\n');

  if (issuesByCategory.timestampFields) {
    console.log('🔧 时间戳字段命名问题:');
    console.log('  - 将驼峰命名的时间字段改为 snake_case');
    console.log('  - createdAt → created_at');
    console.log('  - updatedAt → updated_at');
    console.log('  - expiresAt → expires_at');
    console.log('');
  }

  if (issuesByCategory.dateCreation) {
    console.log('🔧 Date 对象创建问题:');
    console.log('  - 考虑使用 new Date().toISOString() 创建字符串时间戳');
    console.log('  - 确保时间戳字段类型一致性');
    console.log('');
  }

  if (issuesByCategory.dateConversions) {
    console.log('🔧 时间戳转换问题:');
    console.log('  - 检查字符串时间戳到 Date 对象的转换是否正确');
    console.log('  - 确保处理无效时间戳的情况');
    console.log('');
  }

  console.log('✅ 检测完成！');
}

main();
