# 测试计划

本文档概述了基于项目依赖关系的测试计划。测试将从底层组件开始，逐步向上测试更高层级的组件。

## 依赖层级

项目的依赖层级如下：

1. **基础层**：types, utils, lib
2. **服务层**：services, hooks
3. **上下文层**：contexts
4. **视图层**：views, components

## 测试顺序

按照依赖关系，我们应该按照以下顺序进行测试：

### 1. 基础层测试

基础层包含类型定义、工具函数和库函数，这些是其他所有组件的基础。

#### 1.1 类型测试 (src/types)

- 测试 `compatibilityTypes.ts`
- 测试 `emojiTypes.ts`
- 测试 `emotionDataTypes.ts`
- 测试 `emotionEnums.ts`
- 测试 `previewTypes.ts`
- 测试 `skinTypes.ts`
- 测试 `userConfigTypes.ts`

#### 1.2 工具函数测试 (src/utils)

- 测试 `colorContrastTest.ts`
- 测试 `colorUtils.ts`
- 测试 `customWheelManager.ts`
- 测试 `dataAdapters.ts`
<!-- - 测试 `dataMigration.ts` -->
- 测试 `emojiSetManager.ts`
- 测试 `emotionDataManager.ts`
- 测试 `gridFactory.tsx`
- 测试 `listFactory.tsx`
- 测试 `renderOptimizer.ts`
- 测试 `sectorUtils.ts`
- 测试 `skinManager.ts`
- 测试 `skinPreviewGenerator.ts`
- 测试 `testNeutralHappy.ts`
- 测试 `typeConverters.ts`
- 测试 `userConfigManager.ts`
- 测试 `viewFactory.tsx`
- 测试 `wheelUtils.ts`

#### 1.3 库函数测试 (src/lib)

- 测试 `constants.tsx`
- 测试 `layers.ts`
- 测试 `trpc.ts`
- 测试 `useSqLiteWithSingleton.tsx`
- 测试 `useTurso.tsx`
- 测试 `useTursoClient.ts`
- 测试 `utils.ts` (位于 src/lib 下)

### 2. 服务层测试

服务层依赖于基础层，提供数据访问和业务逻辑。

#### 2.1 服务测试 (src/services)

- 测试 `analyticsService.ts`
- 测试 `apiService.ts`
- 测试 `customWheelMappingService.ts`
- 测试 `DatabaseService.ts`
- 测试 `defaultEmotionService.ts`
- 测试 `emojiService.ts`
- 测试 `emotionService.ts`
- 测试 `historyService.ts`
- 测试 `syncService.ts`
- 测试 `tagService.ts`
- 测试 `TursoInitializationService.ts`
- 测试 `tursoService.ts`
- 测试 `offline/analyticsService.ts`
- 测试 `offline/dbService.ts`
- 测试 `offline/emotionService.ts`
- 测试 `offline/historyService.ts`
- 测试 `offline/tagService.ts`
- 测试 `offline/uiLabelService.ts`

#### 2.2 钩子测试 (src/hooks)

- 测试 `use-mobile.tsx`
- 测试 `use-toast.ts`
- 测试 `useCustomWheelAnalytics.ts`
- 测试 `useDataSync.ts`
- 测试 `useExportData.ts`
- 测试 `useLocalAnalyticsData.ts`
- 测试 `useLocalEmotionsData.ts`
- 测试 `useLocalHistoryData.ts`
- 测试 `useLocalStorage.ts`
- 测试 `useLocalTagsData.ts`
- 测试 `useMockQuery.ts`
- 测试 `useMoodEntriesData.ts`
- 测试 `useTursoApi.ts`

### 3. 上下文层测试 (src/contexts)

上下文层依赖于服务层和基础层，提供全局状态管理。

- 测试 `AppLoadingContext.tsx`
- 测试 `ColorModeContext.tsx`
- 测试 `DisplayContext.tsx`
- 测试 `EmojiContext.tsx`
- 测试 `LanguageContext.tsx`
- 测试 `SkinContext.tsx`
- 测试 `SyncContext.tsx`
- 测试 `ThemeContext.tsx`
- 测试 `TierNavigationContext.tsx`
- 测试 `UserConfigContext.tsx`

### 4. 视图层测试

视图层依赖于所有其他层，包括视图组件和UI组件。

#### 4.1 视图基础与接口测试 (src/views)

- 测试 `views/base/BaseEmotionView.tsx`
- 测试 `views/interfaces/EmotionView.ts` (接口定义)

#### 4.2 视图实现与组件测试 (src/views/components, src/views/implementations)

- 测试 `views/components/bubbles/BubbleView.tsx`
- 测试 `views/implementations/bubbles/BubbleView.tsx`
- 测试 `views/components/cards/CardView.tsx`
- 测试 `views/implementations/cards/CardView.tsx`
- 测试 `views/components/galaxy/GalaxyComponent.tsx`
- 测试 `views/implementations/galaxy/GalaxyView.tsx`
- 测试 `views/components/wheels/D3WheelComponent.tsx`
- 测试 `views/implementations/wheels/D3WheelView.tsx`
- 测试 `views/components/wheels/R3FWheelComponent.tsx`
- 测试 `views/implementations/wheels/R3FWheelView.tsx`
- 测试 `views/components/wheels/SVGWheelComponent.tsx`
- 测试 `views/implementations/wheels/SVGWheelView.tsx`

#### 4.3核心及UI组件测试 (src/components)

- 测试 `components/AppContent.tsx`
- 测试 `components/DatabaseInitializer.tsx`
- 测试 `components/EmotionDataInitializer.tsx`
- 测试 `components/TursoInitializer.tsx`
- 测试 `components/common/LazyView.tsx`
- 测试 `components/common/ViewContainer.tsx`
- 测试 `components/core/DisplayAdapter.tsx`
- **编辑器组件 (src/components/editor)**
  - 测试 `components/editor/AppearanceEditor.tsx`
  - 测试 `components/editor/CustomSkinEditor.tsx`
  - 测试 `components/editor/EmotionDataEditor.tsx`
  - 测试 `components/editor/EmotionEditor.tsx`
  - 测试 `components/editor/TierEditor.tsx`
- **Emoji 相关组件 (src/components/emoji)**
  - 测试 `components/emoji/AnimatedEmoji.tsx`
  - 测试 `components/emoji/EmojiDisplay.tsx`
- **历史记录组件 (src/components/history)**
  - 测试 `components/history/EmotionMapDisplay.tsx`
- **布局组件 (src/components/layout)**
  - 测试 `components/layout/MobileHeader.tsx`
  - 测试 `components/layout/MobileLayout.tsx`
  - 测试 `components/layout/MobileNavbar.tsx`
- **情绪记录组件 (src/components/mood)**
  - 测试 `components/mood/MoodForm.tsx`
  - 测试 `components/mood/TierNavigation.tsx`
- **预览组件 (src/components/preview)**
  - 测试 `components/preview/SkinPreview.tsx`
  - 测试 `components/preview/WheelSkinPreview.tsx` (及其他特定预览)
- **设置组件 (src/components/settings)**
  - 测试 `components/settings/DisplayOptionsSection.tsx`
  - 测试 `components/settings/SkinSelector.tsx`
  - 测试 `components/settings/ViewTypeSelector.tsx`
  - (以及其他各类设置子组件)
- **通用UI组件 (src/components/ui)**
  - 测试 `components/ui/Button.tsx`
  - 测试 `components/ui/Dialog.tsx`
  - 测试 `components/ui/Select.tsx`
  - (以及其他常用UI基础组件)

#### 4.4 页面级测试 (src/pages)

- 测试 `pages/Analytics.tsx`
- 测试 `pages/History.tsx`
- 测试 `pages/Home.tsx`
- 测试 `pages/Index.tsx`
- 测试 `pages/Settings.tsx`
- 测试 `pages/Shop.tsx` (或 `SkinShop.tsx`)
- 测试 `pages/EmotionDataEditorPage.tsx`

#### 4.5 集成测试

- 测试 settings-home 集成
- 测试 emotion-selection-display 集成
- 测试 theme-colormode 集成
- 测试数据同步与离线功能集成
- 测试用户认证与VIP/皮肤解锁集成

## 测试优先级

根据组件的重要性和复杂性，我们确定以下测试优先级：

### 高优先级

1. `colorUtils.ts` - 颜色处理是情绪轮盘的核心功能
2. `emotionDataManager.ts` - 情绪数据管理是应用的核心
3. `viewFactory.tsx` - 视图工厂是渲染不同视图的关键
4. `UserConfigContext.tsx` - 用户配置影响整个应用
5. `DisplayAdapter.tsx` - 显示适配器是渲染情绪轮盘的核心

### 中优先级

1. `skinManager.ts` - 皮肤管理影响用户体验
2. `emotionService.ts` - 情绪服务提供数据
3. `useLocalEmotionsData.ts` - 本地情绪数据钩子
4. `ColorModeContext.tsx` - 颜色模式影响视觉体验
5. `TierNavigation.tsx` - 层级导航是用户交互的关键

### 低优先级

1. 各种UI组件
2. 辅助工具函数
3. 示例和测试页面

## 测试实施计划

1. **第一阶段**：完成基础层和服务层的单元测试
2. **第二阶段**：完成上下文层的单元测试
3. **第三阶段**：完成视图层的单元测试
4. **第四阶段**：完成集成测试

## 测试覆盖率目标

- 基础层：90%+
- 服务层：80%+
- 上下文层：70%+
- 视图层：60%+
- 整体覆盖率：70%+

## 测试工具和框架

- **Vitest**：测试运行器
- **@testing-library/react**：React组件测试
- **jsdom**：浏览器环境模拟
- **vi.mock**：模拟依赖
- **vi.spyOn**：监视函数调用

## 测试文件组织

测试文件应放在 `src/tests` 目录下，并遵循以下命名约定：

- 组件测试: `src/tests/components/[ComponentName].test.tsx`
- 工具函数测试: `src/tests/utils/[utilName].test.ts`
- 服务测试: `src/tests/services/[serviceName].test.ts`
- 钩子测试: `src/tests/hooks/[hookName].test.ts`
- 上下文测试: `src/tests/contexts/[contextName].test.tsx`
- 集成测试: `src/tests/integration/[featureName].test.tsx`

## 测试执行

测试可以通过以下命令执行：

```bash
# 运行所有测试
npx vitest run

# 运行特定文件的测试
npx vitest run src/tests/utils/colorUtils.test.ts

# 运行特定目录的测试
npx vitest run src/tests/components

# 运行测试并生成覆盖率报告
npx vitest run --coverage

# 运行数据联动相关测试
npx vitest run src/tests/integration/settings-data-linkage.test.tsx
npx vitest run src/tests/components/settings/SettingsPage.test.tsx
npx vitest run src/tests/services/data-linkage-services.test.ts

# 运行端到端测试
npx playwright test src/tests/e2e/settings-workflow.test.ts

# 运行所有Settings相关测试
npx vitest run src/tests/components/settings/
npx vitest run src/tests/integration/settings-*
```

## 测试覆盖率目标

### 数据联动测试覆盖率目标

- **服务层覆盖率**: ≥ 90%
  - EmotionDataSetService: 95%
  - EmojiSetService: 95%
  - SkinService: 95%
  - UserConfigService: 95%

- **组件层覆盖率**: ≥ 85%
  - Settings页面: 90%
  - EmotionDataSelectionComponent: 90%
  - DisplayOptionsComponentVertical: 90%

- **集成测试覆盖率**: ≥ 80%
  - 数据联动流程: 85%
  - 用户配置管理: 85%
  - 状态同步: 80%

## 数据联动测试策略

### 核心数据联动架构

Settings页面围绕以下核心实体的数据联动设计：

1. **EmotionDataSet（情绪数据集）** - 核心数据源，包含情绪层级和情绪项
2. **EmojiSet（表情集）** - 与数据集关联的表情资源
3. **Skin（皮肤）** - 视觉呈现配置，支持不同视图类型
4. **UserConfig（用户配置）** - 用户偏好设置，管理活动状态

### 数据联动关系测试

#### 1. 数据集联动测试
- 数据集选择影响表情集和皮肤的可用性
- 默认数据集的设置和获取
- 数据集切换时的状态同步

#### 2. 皮肤联动测试
- 皮肤与视图类型的兼容性检查
- 皮肤配置对组件渲染的影响
- 皮肤解锁状态管理

#### 3. 表情集联动测试
- 表情集与数据集的关联关系
- 活动表情集的设置和获取
- 表情集切换对情绪显示的影响

## 已实现的集成测试

以下是已经实现的关键集成测试，这些测试覆盖了应用程序的核心功能：

### 离线存储测试 (offline-storage.test.ts)

这个测试文件验证应用程序在离线状态下的数据存储功能：
- 测试数据库初始化过程
- 测试情绪记录的添加功能
- 测试情绪记录的获取功能
- 测试未同步情绪记录的获取功能

### 数据同步测试 (data-sync.test.ts)

这个测试文件验证应用程序的数据同步功能：
- 测试 useDataSync 钩子的初始状态
- 测试 SyncService 的完整同步流程，包括上传本地数据和下载服务器数据

### Settings页面数据联动集成测试 (settings-data-linkage.test.tsx)

这个测试文件验证Settings页面中数据集、皮肤、表情集之间的联动关系：
- 测试数据集选择对表情集和皮肤可用性的影响
- 测试皮肤与视图类型的兼容性检查
- 测试用户配置的数据一致性
- 测试跨组件的状态同步

### Settings页面组件测试 (SettingsPage.test.tsx)

这个测试文件验证Settings页面的整体功能和组件交互：
- 测试页面渲染和组件显示
- 测试组件间的交互逻辑
- 测试响应式设计
- 测试错误处理和无障碍性

### Settings工作流程端到端测试 (settings-workflow.test.ts)

这个测试文件验证完整的用户工作流程：
- 测试数据集管理工作流程（查看、切换、创建）
- 测试显示选项配置工作流程
- 测试表情集管理工作流程
- 测试配置持久化和一致性
- 测试错误处理和边界情况

### 数据联动服务测试 (data-linkage-services.test.ts)

这个测试文件验证核心服务之间的联动逻辑：
- 测试EmotionDataSetService的默认状态管理
- 测试EmojiSetService的类型过滤和解锁状态
- 测试SkinService的视图类型兼容性
- 测试UserConfigService的权限验证
- 测试跨服务的数据一致性

### 网络状态切换测试 (network-state.test.ts)

这个测试文件验证应用程序在不同网络状态下的行为：
- 测试在线/离线状态切换功能
- 测试在线状态下触发同步的行为
- 测试离线状态下触发同步的行为
- 测试后端不可达时的错误处理
- 测试同步过程中发生错误时的异常处理

### 离线-在线同步测试 (offline-online-sync.test.tsx)

这个测试文件验证应用程序在离线和在线状态之间切换时的同步行为：
- 测试启用同步后同步离线数据的功能
- 测试网络不可用时的错误处理
- 测试同步过程中的加载状态显示

### 用户认证与VIP状态同步测试 (user-auth-vip.test.ts)

这个测试文件验证用户认证和VIP状态同步功能：
- 测试用户登录功能
- 测试用户登出功能
- 测试VIP状态获取功能
- 测试未登录状态下的VIP状态处理

### 付费皮肤解锁状态同步测试 (skin-unlock.test.ts)

这个测试文件验证付费皮肤解锁状态同步功能：
- 测试获取用户解锁皮肤的功能
- 测试解锁新皮肤的功能
- 测试设置当前皮肤的功能
- 测试未登录状态下的皮肤访问限制

### 设置-首页集成测试 (settings-home-integration.test.tsx)

这个测试文件验证设置页面的配置是否正确应用到首页的情绪轮盘显示上：
- 测试颜色模式应用到轮盘显示的功能
- 测试深色模式下的颜色模式应用
- 测试游戏风格颜色模式的轮盘渲染

## 持续集成

测试应该集成到CI/CD流程中，确保每次提交都运行测试，并且只有测试通过的代码才能合并到主分支。

## 核心功能与数据同步测试

本部分将重点测试应用的核心功能，特别是涉及离线存储、数据同步、用户认证以及云端特定数据的场景。

### 1. 离线存储 (Capacitor-SQLite)

- **数据读写测试**：
    - 验证核心用户数据（如情绪记录、用户偏好设置）能否正确写入本地SQLite数据库。
    - 验证应用能否从本地SQLite数据库正确读取和展示数据。
    - 测试数据在不同表之间的关联和一致性。
- **离线核心功能可用性**：
    - 确保用户在无网络连接的情况下，仍能使用核心产品功能（例如，记录情绪、查看历史记录、使用基础皮肤和功能）。
    - 测试离线状态下创建的数据在网络恢复后能否被正确处理（例如，标记为待同步）。
- **应用启动数据加载**：
    - 测试应用启动时，本地数据的加载速度和正确性。
- **数据冲突与解决** (如果本地和云端可能同时修改):
    - 测试当本地数据和云端数据发生冲突时的解决策略。

### 2. 数据同步 (tRPC)

- **首次同步**：
    - 用户首次登录或开启同步功能时，测试云端数据能否完整、正确地同步到本地。
    - 测试本地数据能否完整、正确地同步到云端。
- **增量同步**：
    - 在本地或云端产生新数据或修改数据后，测试增量同步的及时性和准确性。
    - 验证只有发生变化的数据被同步，以节省带宽和提高效率。
- **同步状态与反馈**：
    - 测试应用是否清晰地向用户展示当前的同步状态（如同步中、同步完成、同步失败）。
    - 测试同步失败时的错误提示和重试机制。
- **网络状态变化处理**：
    - 测试在同步过程中网络中断（例如，从Wi-Fi切换到移动数据，或完全断网）对同步过程的影响。
    - 测试网络恢复后，同步是否能自动或手动续传。
- **同步性能**：
    - 测试大量数据同步时的性能，确保应用不会因此卡顿或无响应。

### 3. 用户登录与认证

- **登录流程**：
    - 测试通过tRPC与云端服务进行用户身份验证的流程。
    - 验证登录成功后，用户会话（token等）的正确管理和持久化。
    - 测试不同账户（新用户、老用户）的登录场景。
- **登出功能**：
    - 测试用户登出后，本地会话信息是否被清除，敏感数据是否按预期处理。
- **错误处理**：
    - 测试无效凭证、网络错误等情况下的登录失败处理和用户提示。
- **会话有效性**：
    - 测试用户会话的有效期和自动续期机制（如果存在）。

### 4. 云端特定数据 (VIP状态、皮肤解锁)

- **VIP状态同步与应用**：
    - 测试用户VIP状态能否从云端正确获取。
    - 验证VIP特定功能或内容在获取状态后能否正确解锁或限制。
    - 测试VIP状态变化（如购买VIP、VIP到期）后，应用行为的正确性。
- **付费皮肤解锁同步与应用**：
    - 测试用户已解锁的付费皮肤信息能否从云端正确同步。
    - 验证用户能否在应用中正确选择和应用已解锁的皮肤。
    - 测试新解锁皮肤或皮肤状态变化后，应用能否及时更新。
- **离线状态处理**：
    - 测试在离线状态下，应用如何处理VIP状态和皮肤解锁（例如，使用本地缓存的最新状态）。
    - 测试网络恢复后，这些状态能否与云端保持一致。

### 5. 核心产品功能端到端测试

- **情绪记录与同步**：
    - 用户在离线状态下记录情绪，网络恢复后数据能否成功同步到云端。
    - 用户在在线状态下记录情绪，数据能否实时或准实时同步到云端，并在其他设备（如果支持）上可见。
    - 测试情绪记录过程中的各种交互场景（选择不同层级的情绪、添加反思文本、设置情绪强度等）。
    - 验证情绪记录的完整性，包括所有元数据（时间戳、用户ID、同步状态等）。
    - 测试批量情绪记录的同步性能和稳定性。

- **历史数据查看**：
    - 验证用户能否在离线和在线状态下均能查看其情绪历史记录（离线时查看本地数据，在线时可拉取云端最新数据）。
    - 测试历史数据的筛选、排序和搜索功能。
    - 验证历史数据的可视化展示（如情绪分布图、趋势图等）是否正确反映用户的情绪数据。
    - 测试历史数据的导出功能（如支持导出为CSV、PDF等格式）。
    - 验证大量历史数据下的性能表现，确保页面加载和交互流畅。

- **设置同步**：
    - 用户的偏好设置（如主题、语言等）在修改后能否正确同步到云端，并在其他设备上生效。
    - 测试设置变更的实时应用，验证UI是否立即响应设置变化。
    - 验证设置回退机制，当设置变更导致问题时，能否恢复到默认或之前的设置。
    - 测试设置冲突解决策略，当本地和云端设置不一致时的处理方式。
    - 验证敏感设置的安全处理，确保隐私相关设置得到适当保护。

- **情绪轮盘交互**：
    - 测试不同视图类型（轮盘、卡片、气泡、星系等）下的情绪选择交互。
    - 验证不同渲染引擎（D3、R3F、SVG）的性能和兼容性。
    - 测试情绪轮盘的响应式设计，在不同屏幕尺寸和设备上的表现。
    - 验证情绪轮盘的辅助功能，如键盘导航、屏幕阅读器支持等。
    - 测试情绪轮盘的动画和过渡效果，确保流畅且不影响用户体验。

- **皮肤系统功能**：
    - 测试皮肤选择和应用过程，验证UI是否正确反映所选皮肤。
    - 验证不同皮肤在各种视图类型下的兼容性和表现。
    - 测试皮肤预览功能，确保用户可以在应用前查看皮肤效果。
    - 验证付费皮肤的解锁流程，包括购买、激活和同步到云端。
    - 测试自定义皮肤功能（如支持），包括创建、编辑和应用自定义皮肤。

- **多语言支持**：
    - 测试语言切换功能，验证UI文本是否正确翻译和显示。
    - 验证不同语言下的布局适应性，特别是对于长文本或特殊字符的处理。
    - 测试语言设置的持久化和同步。
    - 验证特定语言的本地化内容（如情绪名称、描述等）是否正确加载和显示。
    - 测试语言资源的动态加载性能。

- **离线/在线模式切换**：
    - 测试应用在网络状态变化时的行为，如从在线切换到离线，或从离线恢复到在线。
    - 验证网络状态变化时的用户通知和提示。
    - 测试在不同网络条件下（如弱网络、不稳定网络）的应用性能和用户体验。
    - 验证离线模式下的功能限制和提示是否清晰明确。
    - 测试长时间离线后首次连接时的数据同步行为。

- **用户账户管理**：
    - 测试用户注册、登录和登出流程。
    - 验证用户认证状态的持久化和恢复。
    - 测试账户信息修改（如密码、邮箱等）功能。
    - 验证账户安全措施，如会话超时、异常登录检测等。
    - 测试账户删除功能及其对用户数据的影响。

- **VIP功能访问控制**：
    - 测试VIP状态检查和功能访问控制。
    - 验证VIP特权的正确应用，如高级皮肤、扩展分析等。
    - 测试VIP订阅管理，包括购买、续订和取消流程。
    - 验证VIP状态变更（如升级、降级、到期）后的功能访问调整。
    - 测试VIP试用功能（如支持）的时间限制和到期提醒。
