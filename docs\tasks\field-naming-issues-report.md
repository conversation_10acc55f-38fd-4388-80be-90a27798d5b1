# 字段命名不一致问题报告

## 🔍 **问题分析**

通过 IDE 错误分析，发现了两类主要问题：

### 1. **字段命名不一致问题** (驼峰 vs 下划线)

#### 用户配置相关
```typescript
// ❌ 错误使用 (代码中)
contentDisplayModePreferences
renderEnginePreferences  
viewTypeSkinIds

// ✅ 正确应该是 (类型定义中)
content_display_mode_preferences
render_engine_preferences
view_type_skin_ids
```

#### 皮肤配置相关
```typescript
// ❌ 错误使用 (代码中)
borderRadius
cardSize
cardSpacing
bubbleSize
bubbleSpacing

// ✅ 正确应该是 (类型定义中)
border_radius
card_size
card_spacing
bubble_size
bubble_spacing
```

#### 皮肤属性相关
```typescript
// ❌ 错误使用 (代码中)
isUnlocked
isPremium
previewImage
created_at
updated_at

// ✅ 正确应该是 (类型定义中)
is_unlocked
is_premium
preview_image
created_at
updated_at
```

### 2. **缺失字段定义问题**

#### Skin 类型缺失字段
```typescript
// ❌ 类型定义中缺失
parsedConfig
parsedsupported_render_engines
parsedsupported_view_types
parsedsupported_content_modes
```

#### EmojiSet 类型缺失字段
```typescript
// ❌ 类型定义中缺失
items: Record<string, EmojiItem>
```

## 🎯 **修复策略**

### 策略 1: 修改代码使用下划线命名 (推荐)
- **优点**: 与数据库字段命名一致，符合后端约定
- **缺点**: 需要修改较多代码文件
- **影响**: 中等，主要是前端代码

### 策略 2: 修改类型定义使用驼峰命名
- **优点**: 符合 JavaScript/TypeScript 约定
- **缺点**: 与数据库字段不一致，需要数据转换
- **影响**: 较大，涉及数据库和后端

### 策略 3: 添加缺失字段到类型定义
- **优点**: 解决类型安全问题
- **缺点**: 需要确保运行时数据一致性
- **影响**: 小，主要是类型定义

## 📋 **修复清单**

### 高优先级 (影响功能)
1. ✅ 修复 `is_unlocked` 字段使用
2. ⏳ 修复用户配置字段命名
3. ⏳ 添加 Skin 类型缺失字段
4. ⏳ 添加 EmojiSet 类型缺失字段

### 中优先级 (影响编辑器)
1. ⏳ 修复皮肤配置字段命名
2. ⏳ 修复视图配置字段命名

### 低优先级 (代码清理)
1. ⏳ 统一所有字段命名约定
2. ⏳ 添加 ESLint 规则防止回退

## 🔧 **具体修复方案**

### 1. 修复用户配置字段
```typescript
// 修复前
updateUserConfig({
  contentDisplayModePreferences: { ... }
});

// 修复后
updateUserConfig({
  content_display_mode_preferences: { ... }
});
```

### 2. 修复皮肤配置字段
```typescript
// 修复前
skin.config.effects.borderRadius
skin.config.view_configs?.card?.cardSize

// 修复后
skin.config.effects.border_radius
skin.config.view_configs?.card?.card_size
```

### 3. 添加缺失字段到类型定义
```typescript
// 在 Skin 类型中添加
interface Skin {
  // ... 现有字段
  parsedConfig?: SkinConfig;
  parsedsupported_render_engines?: RenderEngine[];
  parsedsupported_view_types?: ViewType[];
  parsedsupported_content_modes?: ContentDisplayMode[];
}

// 在 EmojiSet 类型中添加
interface EmojiSet {
  // ... 现有字段
  items?: Record<string, EmojiItem>;
}
```

## 📊 **影响评估**

### 文件影响统计
- **需要修复的文件**: ~15 个
- **主要影响目录**: 
  - `src/components/settings/`
  - `src/components/editor/`
  - `src/pages/`
- **类型定义文件**: `src/types/`

### 风险评估
- **破坏性变更**: 无 (仅修复类型不一致)
- **功能影响**: 无 (修复后功能更稳定)
- **性能影响**: 无

## ✅ **验证方法**

1. **TypeScript 编译检查**
   ```bash
   npx tsc --noEmit
   ```

2. **运行时测试**
   - 测试用户配置保存/加载
   - 测试皮肤切换功能
   - 测试表情集加载

3. **功能验证**
   - 设置页面正常工作
   - 编辑器功能正常
   - 商店页面正常

---

**🎯 总结**: 这些字段命名不一致问题主要是历史遗留问题，通过系统性修复可以显著提升代码质量和类型安全性。建议优先修复高优先级问题，确保核心功能稳定运行。
