import { useLanguage } from '@/contexts/LanguageContext';
import { Monitor } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import SettingsNavigation from '../ui/SettingsNavigation';
import SettingsSection from '../ui/SettingsSection';
import DisplayModeOptions from './DisplayModeOptions';
import RenderEngineOptions from './RenderEngineOptions';
import ViewConfigOptions from './ViewConfigOptions';
import ViewTypeOptions from './ViewTypeOptions';

/**
 * 显示选项部分组件
 * 用于设置页面中的显示选项部分，包含显示模式、渲染引擎、视图类型和视图配置
 */
const DisplayOptionsSection: React.FC = () => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('display-mode');

  // 导航项
  const navigationItems = [
    { id: 'display-mode', label: t('settings.display_mode') },
    { id: 'render-engine', label: t('settings.render_engine') },
    { id: 'view-type', label: t('settings.view_type') },
    { id: 'view-config', label: t('settings.view_config') },
  ];

  return (
    <SettingsSection
      title={t('settings.display_options', { fallback: '显示选项' })}
      icon={<Monitor className="h-5 w-5" />}
      description={t('settings.display_options.description')}
    >
      <SettingsNavigation
        items={navigationItems}
        activeItem={activeTab}
        onItemChange={setActiveTab}
        className="mb-4"
      />

      {activeTab === 'display-mode' && <DisplayModeOptions />}
      {activeTab === 'render-engine' && <RenderEngineOptions />}
      {activeTab === 'view-type' && <ViewTypeOptions />}
      {activeTab === 'view-config' && <ViewConfigOptions />}
    </SettingsSection>
  );
};

export default DisplayOptionsSection;
