-- User Subscription History Test Data
-- Test data for user_subscription_history table

-- Insert user subscription history records
INSERT OR IGNORE INTO user_subscription_history (
    id, user_id, subscription_type, status, started_at, expires_at, cancelled_at, 
    auto_renew, payment_method, transaction_id, amount, currency, billing_cycle, 
    next_billing_date, subscription_source, promotion_code, notes, created_at, updated_at
) VALUES
-- Active premium subscription with promotion
('sub_001', 'user_test_001', 'vip_premium', 'active', 
 '2024-01-15T10:00:00Z', '2025-01-15T10:00:00Z', NULL, 
 1, 'stripe', 'txn_stripe_001', 99.99, 'USD', 'yearly', 
 '2025-01-15T10:00:00Z', 'web', 'NEWYEAR2024', 
 'New Year promotion - 20% off first year', 
 '2024-01-15T10:00:00Z', '2024-01-15T10:00:00Z'),

-- Cancelled basic subscription
('sub_002', 'user_test_002', 'vip_basic', 'cancelled', 
 '2024-02-01T14:30:00Z', '2024-08-01T14:30:00Z', '2024-07-15T09:20:00Z', 
 0, 'paypal', 'txn_paypal_002', 4.99, 'USD', 'monthly', 
 NULL, 'mobile', NULL, 
 'User cancelled before renewal', 
 '2024-02-01T14:30:00Z', '2024-07-15T09:20:00Z'),

-- Expired premium subscription
('sub_003', 'user_test_003', 'vip_premium', 'expired', 
 '2023-06-01T12:00:00Z', '2024-06-01T12:00:00Z', NULL, 
 0, 'apple_pay', 'txn_apple_003', 99.99, 'USD', 'yearly', 
 NULL, 'mobile', NULL, 
 'Subscription expired, user did not renew', 
 '2023-06-01T12:00:00Z', '2024-06-01T12:00:00Z'),

-- Active lifetime subscription
('sub_004', 'user_test_004', 'vip_lifetime', 'active', 
 '2024-03-10T16:45:00Z', NULL, NULL, 
 0, 'stripe', 'txn_stripe_004', 299.99, 'USD', 'lifetime', 
 NULL, 'web', 'LIFETIME50', 
 'Lifetime subscription with 50% off promotion', 
 '2024-03-10T16:45:00Z', '2024-03-10T16:45:00Z'),

-- Active enterprise subscription
('sub_005', 'user_test_005', 'vip_enterprise', 'active', 
 '2024-04-01T08:00:00Z', '2025-04-01T08:00:00Z', NULL, 
 1, 'stripe', 'txn_stripe_005', 199.99, 'USD', 'yearly', 
 '2025-04-01T08:00:00Z', 'web', NULL, 
 'Enterprise subscription for team of 25 users', 
 '2024-04-01T08:00:00Z', '2024-04-01T08:00:00Z'),

-- Refunded basic subscription
('sub_006', 'user_test_001', 'vip_basic', 'refunded', 
 '2023-12-01T10:00:00Z', '2024-01-01T10:00:00Z', '2023-12-15T14:30:00Z', 
 0, 'stripe', 'txn_stripe_006', 4.99, 'USD', 'monthly', 
 NULL, 'web', NULL, 
 'Refunded due to technical issues, user upgraded to premium later', 
 '2023-12-01T10:00:00Z', '2023-12-15T14:30:00Z'),

-- Pending premium subscription
('sub_007', 'user_test_006', 'vip_premium', 'pending', 
 '2024-12-01T15:20:00Z', '2025-12-01T15:20:00Z', NULL, 
 1, 'google_pay', 'txn_google_007', 99.99, 'USD', 'yearly', 
 '2025-12-01T15:20:00Z', 'mobile', 'BLACKFRIDAY2024', 
 'Black Friday promotion - payment processing', 
 '2024-12-01T15:20:00Z', '2024-12-01T15:20:00Z');

-- Verify the data was inserted
SELECT 'User Subscription History Count:' as info, COUNT(*) as count FROM user_subscription_history;

-- Show subscription statistics by status
SELECT 
    status,
    COUNT(*) as count,
    AVG(amount) as avg_amount,
    SUM(amount) as total_revenue
FROM user_subscription_history 
GROUP BY status 
ORDER BY count DESC;

-- Show subscription statistics by type
SELECT 
    subscription_type,
    COUNT(*) as count,
    AVG(amount) as avg_amount,
    MIN(amount) as min_amount,
    MAX(amount) as max_amount
FROM user_subscription_history 
GROUP BY subscription_type 
ORDER BY avg_amount DESC;

-- Show active subscriptions
SELECT 
    ush.id,
    ush.user_id,
    ush.subscription_type,
    ush.amount,
    ush.currency,
    ush.billing_cycle,
    ush.expires_at,
    ush.auto_renew
FROM user_subscription_history ush
WHERE ush.status = 'active'
ORDER BY ush.expires_at;
