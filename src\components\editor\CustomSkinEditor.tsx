/**
 * 自定义皮肤编辑器组件
 * 用于创建和编辑皮肤
 */

import { useLanguage } from '@/contexts/LanguageContext';
import { Services } from '@/services';
import type { EmotionData, ContentDisplayMode, ViewType, RenderEngine, Skin, SkinConfig } from '@/types';
import { DataAdapters } from '@/utils/dataAdapters';
import { Copy, Download, Plus, Save, Trash2, Upload } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { SkinPreview } from '../preview/SkinPreview';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Textarea } from '../ui/textarea';
import { AppearanceEditor } from './AppearanceEditor';

interface CustomSkinEditorProps {
  skin?: Skin;
  emotionData: EmotionData;
  onSave?: (skin: Skin) => void;
  onCancel?: () => void;
  onDelete?: (id: string) => void;
  onDuplicate?: (id: string) => void;
  onImport?: (data: string) => Skin | null;
  onExport?: (id: string) => string;
}

/**
 * 自定义皮肤编辑器组件
 */
export const CustomSkinEditor: React.FC<CustomSkinEditorProps> = ({
  skin,
  emotionData,
  onSave,
  onCancel,
  onDelete,
  onDuplicate,
  onImport,
  onExport,
}) => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('general');
  const [isImporting, setIsImporting] = useState(false);
  const [importData, setImportData] = useState('');
  const [importError, setImportError] = useState('');
  const [viewType, setViewType] = useState<ViewType>('wheel');
  const [contentDisplayMode, setContentDisplayMode] = useState<ContentDisplayMode>('textEmoji');
  const [RenderEngine, setRenderEngine] = useState<RenderEngine>('D3');

  // 编辑状态
  const [editingSkin, setEditingSkin] = useState<Skin | null>(null);
  const isCreating = !skin;

  // 初始化编辑数据
  useEffect(() => {
    if (skin) {
      setEditingSkin({ ...skin });
    } else {
      // 创建新的皮肤
      setEditingSkin({
        id: `custom-skin-${Date.now()}`,
        name: t('skin_editor.new_skin', { fallback: '新皮肤' }),
        description: '',
        category: 'custom',
        config: DataAdapters.createDefaultSkinConfig(),
        isUnlocked: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    }
  }, [skin, t]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!editingSkin) return;

    setEditingSkin({
      ...editingSkin,
      [e.target.name]: e.target.value,
      updated_at: new Date().toISOString(),
    });
  };

  // 处理皮肤变化
  const handleSkinChange = (updatedSkin: Skin) => {
    setEditingSkin(updatedSkin);
  };

  // 状态变量
  const [nameError, setNameError] = useState<string | null>(null);

  // 处理保存
  const handleSave = () => {
    if (!editingSkin) return;

    // 验证名称不能为空
    if (!editingSkin.name.trim()) {
      // 显示错误消息
      setNameError(t('skin_editor.name_required', { fallback: '名称不能为空' }));
      return;
    }

    // 清除错误
    setNameError(null);

    // 验证配置是否完整
    if (!editingSkin.config.colors) {
      // 自动修复配置
      const fixedSkin = {
        ...editingSkin,
        config: {
          ...editingSkin.config,
          colors: DataAdapters.createDefaultSkinConfig().colors,
        },
      };
      setEditingSkin(fixedSkin);
      onSave?.(fixedSkin);
      return;
    }

    onSave?.(editingSkin);
  };

  // 处理删除
  const handleDelete = () => {
    if (!editingSkin || !editingSkin.id) return;

    onDelete?.(editingSkin.id);
  };

  // 处理复制
  const handleDuplicate = () => {
    if (!editingSkin || !editingSkin.id) return;

    onDuplicate?.(editingSkin.id);
  };

  // 处理导出
  const handleExport = () => {
    if (!editingSkin || !editingSkin.id || !onExport) return;

    const exportData = onExport(editingSkin.id);
    // 处理导出数据，例如复制到剪贴板或下载文件
  };

  // 处理导入
  const handleImport = () => {
    if (!onImport || !importData.trim()) {
      setImportError(t('skin_editor.import_error_empty', { fallback: '导入数据不能为空' }));
      return;
    }

    try {
      const importedSkin = onImport(importData);
      if (importedSkin) {
        setEditingSkin(importedSkin);
        setIsImporting(false);
        setImportData('');
        setImportError('');
      } else {
        setImportError(t('skin_editor.import_error_invalid', { fallback: '无效的导入数据' }));
      }
    } catch (error) {
      setImportError(error instanceof Error ? error.message : String(error));
      // 不关闭对话框，让用户看到错误消息
      return;
    }
  };

  // 处理视图类型变化
  const handleViewTypeChange = (type: ViewType) => {
    setViewType(type);
  };

  // 处理内容显示模式变化
  const handleContentDisplayModeChange = (mode: ContentDisplayMode) => {
    setContentDisplayMode(mode);
  };

  // 处理轮盘实现类型变化
  const handleRenderEngineChange = (implementation: RenderEngine) => {
    setRenderEngine(implementation);
  };

  if (!editingSkin) {
    return (
      <Card className="h-full flex items-center justify-center">
        <CardContent className="text-center py-10">
          <p className="text-muted-foreground mb-4">
            {t('skin_editor.loading', { fallback: '加载中...' })}
          </p>
        </CardContent>
      </Card>
    );
  }

  // 检查配置是否不完整
  const isConfigIncomplete = !editingSkin.config.colors;

  return (
    <div className="custom-skin-editor">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>
              {isCreating
                ? t('skin_editor.create_skin', { fallback: '创建皮肤' })
                : t('skin_editor.edit_skin', { fallback: '编辑皮肤' })}
            </CardTitle>
            <CardDescription>{editingSkin.name}</CardDescription>
          </div>

          <div className="flex space-x-2">
            {!isCreating && (
              <>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="icon">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>
                        {t('skin_editor.delete_confirm_title', { fallback: '确认删除' })}
                      </AlertDialogTitle>
                      <AlertDialogDescription>
                        {t('skin_editor.delete_confirm_description', {
                          fallback: '确定要删除这个皮肤吗？此操作无法撤销。',
                        })}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>
                        {t('common.cancel', { fallback: '取消' })}
                      </AlertDialogCancel>
                      <AlertDialogAction onClick={handleDelete}>
                        {t('common.confirm_delete', { fallback: '确认删除' })}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                <Button variant="outline" size="icon" onClick={handleDuplicate}>
                  <Copy className="h-4 w-4" />
                </Button>

                <Button variant="outline" size="icon" onClick={handleExport}>
                  <Download className="h-4 w-4" />
                </Button>
              </>
            )}

            <Dialog open={isImporting} onOpenChange={setIsImporting}>
              <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                  <Upload className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {t('skin_editor.import_title', { fallback: '导入皮肤' })}
                  </DialogTitle>
                  <DialogDescription>
                    {t('skin_editor.import_description', { fallback: '粘贴导出的皮肤数据以导入' })}
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  <Textarea
                    value={importData}
                    onChange={(e) => setImportData(e.target.value)}
                    placeholder={t('skin_editor.import_placeholder', {
                      fallback: '粘贴导出的JSON数据',
                    })}
                    rows={10}
                    aria-label="导入数据"
                  />

                  {importError && <div className="text-red-500 text-sm">{importError}</div>}
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsImporting(false)}>
                    {t('common.cancel', { fallback: '取消' })}
                  </Button>
                  <Button onClick={handleImport}>
                    {t('skin_editor.import_button', { fallback: '确认导入' })}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>

        <CardContent>
          {isConfigIncomplete && (
            <div className="mb-4 p-4 border rounded-md bg-yellow-50 text-yellow-800">
              <p>
                {t('skin_editor.config_incomplete', { fallback: '配置不完整，保存时将自动修复' })}
              </p>
            </div>
          )}

          <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="general">
                {t('skin_editor.tab_general', { fallback: '基本信息' })}
              </TabsTrigger>
              <TabsTrigger value="appearance">
                {t('skin_editor.tab_appearance', { fallback: '外观' })}
              </TabsTrigger>
              <TabsTrigger value="preview">
                {t('skin_editor.tab_preview', { fallback: '预览' })}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t('common.name', { fallback: '名称' })}</Label>
                <Input
                  id="name"
                  name="name"
                  value={editingSkin.name}
                  onChange={handleInputChange}
                  placeholder={t('skin_editor.name_placeholder', { fallback: '输入皮肤名称' })}
                  className={nameError ? 'border-red-500' : ''}
                />
                {nameError && <p className="text-sm text-red-500">{nameError}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('common.description', { fallback: '描述' })}</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={editingSkin.description || ''}
                  onChange={handleInputChange}
                  placeholder={t('skin_editor.description_placeholder', {
                    fallback: '输入皮肤描述',
                  })}
                />
              </div>
            </TabsContent>

            <TabsContent value="appearance">
              <AppearanceEditor
                skin={editingSkin}
                emotionData={emotionData}
                onChange={handleSkinChange}
              />
            </TabsContent>

            <TabsContent value="preview">
              <SkinPreview
                emotionData={emotionData}
                skin={editingSkin}
                viewType={viewType}
                contentDisplayMode={contentDisplayMode}
                RenderEngine={RenderEngine}
                onViewTypeChange={handleViewTypeChange}
                onContentDisplayModeChange={handleContentDisplayModeChange}
                onRenderEngineChange={handleRenderEngineChange}
              />
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={onCancel}>
            {t('common.cancel', { fallback: '取消' })}
          </Button>

          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-1" />
            {t('common.save', { fallback: '保存' })}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};
