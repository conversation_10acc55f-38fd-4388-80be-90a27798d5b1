/**
 * R3F轮盘组件
 * 使用React Three Fiber实现的轮盘，不依赖旧的轮盘实现
 *
 * 此组件是新视图系统的一部分，用于替代旧的 R3FWheelComponent
 * 它直接实现了轮盘的渲染，不依赖旧的 R3FWheel 类
 */

import { AnimatedEmoji } from '@/components/emoji/AnimatedEmoji';
import { useEmoji } from '@/contexts/EmojiContext';
import type { Emotion, ContentDisplayMode, SkinConfig } from '@/types';
import { Text } from '@react-three/drei';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import React, { useRef, useState } from 'react';
import * as THREE from 'three';
import { extract_wheel_config_for_engine, type WheelConfig } from '../../../utils/wheelConfigExtractor';

interface R3FWheelComponentProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  onBack?: () => void;
  selectedPath?: any;
}



/**
 * 扇区组件
 */
const Sector = ({
  emotion,
  index,
  total,
  radius,
  onSelect,
  contentDisplayMode,
  wheelConfig,
}: {
  emotion: Emotion;
  index: number;
  total: number;
  radius: number;
  onSelect: (emotion: Emotion) => void;
  contentDisplayMode: ContentDisplayMode;
  wheelConfig: WheelConfig;
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);
  const { camera } = useThree();

  // 计算扇区角度
  const angleStep = (Math.PI * 2) / total;
  const startAngle = index * angleStep;
  const endAngle = (index + 1) * angleStep;

  // 创建扇区形状
  const shape = new THREE.Shape();
  shape.moveTo(0, 0);
  shape.lineTo(radius * Math.cos(startAngle), radius * Math.sin(startAngle));

  // 添加弧形
  const arcCurve = new THREE.EllipseCurve(0, 0, radius, radius, startAngle, endAngle, false, 0);

  const arcPoints = arcCurve.getPoints(16);
  arcPoints.forEach((point) => {
    shape.lineTo(point.x, point.y);
  });

  shape.lineTo(0, 0);

  // 计算文本位置
  const midAngle = (startAngle + endAngle) / 2;
  const textRadius = radius * 0.7;
  const textX = textRadius * Math.cos(midAngle);
  const textY = textRadius * Math.sin(midAngle);

  // 动画效果
  useFrame(() => {
    if (meshRef.current) {
      if (hovered && wheelConfig.hover_effect === 'scale') {
        meshRef.current.scale.lerp(new THREE.Vector3(1.05, 1.05, 1.05), 0.1);
      } else {
        meshRef.current.scale.lerp(new THREE.Vector3(1, 1, 1), 0.1);
      }
    }
  });

  return (
    <group>
      <mesh
        ref={meshRef}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        onClick={() => onSelect(emotion)}
      >
        <shapeGeometry args={[shape]} />
        <meshStandardMaterial
          color={emotion.color || '#cccccc'}
          side={THREE.DoubleSide}
          roughness={0.7}
          metalness={0.2}
        />
      </mesh>

      {(contentDisplayMode === 'text' || contentDisplayMode === 'textEmoji') && (
        <Text
          position={[textX, textY, 0.1]}
          fontSize={wheelConfig.font_size / 10}
          color={wheelConfig.text_color}
          anchorX="center"
          anchorY="middle"
          font={wheelConfig.font_family}
          outlineWidth={0.01}
          outlineColor="#ffffff"
        >
          {emotion.name}
        </Text>
      )}

      {(contentDisplayMode === 'emoji' || contentDisplayMode === 'textEmoji') && (
        <Text
          position={[
            textX,
            textY + (contentDisplayMode === 'textEmoji' ? wheelConfig.font_size / 8 : 0),
            0.1,
          ]}
          fontSize={wheelConfig.emojiSize / 10}
          color={wheelConfig.text_color}
          anchorX="center"
          anchorY="middle"
          font={wheelConfig.font_family}
        >
          {emotion.emoji}
        </Text>
      )}
    </group>
  );
};

/**
 * 轮盘场景组件
 */
const WheelScene = ({
  emotions,
  contentDisplayMode,
  wheelConfig,
  onSelect,
}: {
  emotions: Emotion[];
  contentDisplayMode: ContentDisplayMode;
  wheelConfig: WheelConfig;
  onSelect: (emotion: Emotion) => void;
}) => {
  const { camera } = useThree();

  // 设置相机位置
  React.useEffect(() => {
    camera.position.z = 5;
  }, [camera]);

  return (
    <group>
      {/* 环境光和方向光 */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 10]} intensity={0.5} />

      {/* 渲染扇区 */}
      {emotions.map((emotion, index) => (
        <Sector
          key={emotion.id}
          emotion={emotion}
          index={index}
          total={emotions.length}
          radius={2}
          onSelect={onSelect}
          contentDisplayMode={contentDisplayMode}
          wheelConfig={wheelConfig}
        />
      ))}

      {/* 装饰效果 */}
      {wheelConfig.decorations && (
        <mesh>
          <ringGeometry args={[2.05, 2.1, 64]} />
          <meshBasicMaterial color={wheelConfig.shadowColor} transparent={true} opacity={0.5} />
        </mesh>
      )}
    </group>
  );
};

/**
 * R3F轮盘组件
 */
export const R3FWheelComponent: React.FC<R3FWheelComponentProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  onBack,
  selectedPath,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [hoveredEmotion, setHoveredEmotion] = useState<string | null>(null);
  const { getEmojiItemForEmotionId } = useEmoji();

  // 提取轮盘配置
  const wheelConfig = extract_wheel_config_for_engine.r3f(skinConfig);

  // 渲染动画表情（如果内容类型为 animatedEmoji）
  const renderAnimatedEmojis = () => {
    if (contentDisplayMode !== 'animatedEmoji' || !emotions.length) return null;

    return emotions.map((emotion, index) => {
      const angle = (index / emotions.length) * 2 * Math.PI;
      const radius = wheelConfig.container_size * 0.35; // 调整半径，使表情位于扇区中心
      const x = Math.cos(angle) * radius + wheelConfig.container_size / 2;
      const y = Math.sin(angle) * radius + wheelConfig.container_size / 2;

      const emojiItem = getEmojiItemForEmotionId(emotion.id);

      return (
        <div
          key={emotion.id}
          style={{
            position: 'absolute',
            left: `${x}px`,
            top: `${y}px`,
            transform: 'translate(-50%, -50%)',
            zIndex: hoveredEmotion === emotion.id ? 10 : 1,
            transition: `all ${wheelConfig.transition_duration}ms`,
            cursor: 'pointer',
          }}
          onClick={() => onSelect(emotion)}
          onMouseEnter={() => setHoveredEmotion(emotion.id)}
          onMouseLeave={() => setHoveredEmotion(null)}
        >
          {emojiItem ? (
            <AnimatedEmoji
              emojiItem={emojiItem}
              size={
                wheelConfig.emojiSize >= 40
                  ? '4xl'
                  : wheelConfig.emojiSize >= 32
                    ? '3xl'
                    : wheelConfig.emojiSize >= 24
                      ? '2xl'
                      : wheelConfig.emojiSize >= 20
                        ? 'xl'
                        : wheelConfig.emojiSize >= 16
                          ? 'lg'
                          : wheelConfig.emojiSize >= 14
                            ? 'md'
                            : wheelConfig.emojiSize >= 12
                              ? 'sm'
                              : 'xs'
              }
              autoPlay={true}
              loop={true}
            />
          ) : (
            <span style={{ fontSize: `${wheelConfig.emojiSize}px` }}>{emotion.emoji}</span>
          )}
          {contentDisplayMode === 'textEmoji' && (
            <div
              style={{
                textAlign: 'center',
                fontSize: `${wheelConfig.font_size}px`,
                fontFamily: wheelConfig.font_family,
                color: wheelConfig.text_color,
                marginTop: '4px',
              }}
            >
              {emotion.name}
            </div>
          )}
        </div>
      );
    });
  };

  // 渲染返回按钮
  const renderBackButton = () => {
    if (!onBack || tierLevel === 1) return null;

    return (
      <button
        onClick={onBack}
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          boxShadow: wheelConfig.shadow_enabled ? `0 0 5px ${wheelConfig.shadowColor}` : 'none',
        }}
      >
        ←
      </button>
    );
  };

  // 渲染选中路径
  const renderSelectedPath = () => {
    if (!selectedPath) return null;

    return (
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 100,
          fontSize: `${wheelConfig.font_size}px`,
          fontFamily: wheelConfig.font_family,
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '5px',
          background: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(5px)',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {selectedPath}
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width: `${wheelConfig.container_size}px`,
        height: `${wheelConfig.container_size}px`,
        margin: '0 auto',
      }}
    >
      {renderBackButton()}
      {renderSelectedPath()}
      {contentDisplayMode !== 'animatedEmoji' ? (
        <Canvas
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            boxShadow: wheelConfig.shadow_enabled
              ? `0 0 ${wheelConfig.shadowBlur}px ${wheelConfig.shadowColor}`
              : 'none',
          }}
          camera={{ position: [0, 0, 5], fov: 45 }}
        >
          <WheelScene
            emotions={emotions}
            contentDisplayMode={contentDisplayMode}
            wheelConfig={wheelConfig}
            onSelect={onSelect}
          />
        </Canvas>
      ) : (
        <div
          style={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            background: wheelConfig.background_color,
            boxShadow: wheelConfig.shadow_enabled
              ? `0 0 ${wheelConfig.shadowBlur}px ${wheelConfig.shadowColor}`
              : 'none',
            position: 'relative',
          }}
        >
          {/* 渲染扇区背景 */}
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 100 100"
            style={{ position: 'absolute', top: 0, left: 0 }}
          >
            {emotions.map((emotion, index) => {
              const total = emotions.length;
              const angleStep = (Math.PI * 2) / total;
              const startAngle = index * angleStep;
              const endAngle = (index + 1) * angleStep;

              const x1 = 50 + 50 * Math.cos(startAngle);
              const y1 = 50 + 50 * Math.sin(startAngle);
              const x2 = 50 + 50 * Math.cos(endAngle);
              const y2 = 50 + 50 * Math.sin(endAngle);

              const largeArcFlag = endAngle - startAngle > Math.PI ? 1 : 0;

              return (
                <path
                  key={emotion.id}
                  d={`M 50 50 L ${x1} ${y1} A 50 50 0 ${largeArcFlag} 1 ${x2} ${y2} Z`}
                  fill={emotion.color || '#cccccc'}
                  stroke={wheelConfig.background_color}
                  strokeWidth={wheelConfig.sector_gap}
                />
              );
            })}
          </svg>

          {renderAnimatedEmojis()}
        </div>
      )}
    </div>
  );
};
