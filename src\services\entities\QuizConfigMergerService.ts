/**
 * Quiz配置合并服务
 * 负责合并用户偏好、包覆盖和系统默认配置，生成最终的会话配置
 */

import { UserQuizPreferencesRepository } from './UserQuizPreferencesRepository';
import { QuizPackOverridesRepository } from './QuizPackOverridesRepository';
import { QuizSessionConfigRepository } from './QuizSessionConfigRepository';
import { QuizSessionConfig } from '../../types/schema/base';
import { CreateQuizSessionConfigInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export interface ConfigMergeOptions {
  includeDefaults?: boolean;
  prioritizeAccessibility?: boolean;
  performanceMode?: 'high_quality' | 'balanced' | 'performance';
}

export interface ConfigSource {
  type: 'user_preferences' | 'pack_override' | 'system_default' | 'accessibility_override';
  id: string;
  priority: number;
  applied_at: string;
}

export class QuizConfigMergerService {
  private userPreferencesRepo: UserQuizPreferencesRepository;
  private packOverridesRepo: QuizPackOverridesRepository;
  private sessionConfigRepo: QuizSessionConfigRepository;

  constructor(db?: any) {
    this.userPreferencesRepo = new UserQuizPreferencesRepository('user_presentation_configs', db);
    this.packOverridesRepo = new QuizPackOverridesRepository('pack_presentation_overrides', db);
    this.sessionConfigRepo = new QuizSessionConfigRepository('quiz_session_configs', db);
  }

  /**
   * 生成Quiz会话的最终配置
   */
  async generateSessionConfig(
    userId: string,
    packId: string,
    sessionId: string,
    options: ConfigMergeOptions = {}
  ): Promise<ServiceResult<QuizSessionConfig>> {
    try {
      // 1. 获取用户偏好配置
      const userPreferences = await this.userPreferencesRepo.getUserPreferences(userId);
      
      // 2. 获取包特定覆盖配置
      const packOverrides = await this.packOverridesRepo.getPackOverrides(userId, packId);
      
      // 3. 获取系统默认配置
      const systemDefaults = this.getSystemDefaultConfig();
      
      // 4. 合并配置
      const mergedConfig = this.mergeConfigurations({
        userPreferences,
        packOverrides,
        systemDefaults,
        options
      });

      // 5. 记录配置来源
      const configSources = this.buildConfigSources({
        userPreferences,
        packOverrides,
        hasSystemDefaults: true
      });

      // 6. 创建会话配置
      const sessionConfigData: CreateQuizSessionConfigInput = {
        session_id: sessionId,
        user_id: userId,
        pack_id: packId,
        final_presentation_config: JSON.stringify(mergedConfig),
        config_sources: JSON.stringify(configSources),
        personalization_level: this.calculatePersonalizationLevel(mergedConfig, userPreferences),
        config_version: '2.0'
      };

      const sessionConfig = await this.sessionConfigRepo.createSessionConfig(sessionConfigData);
      
      if (!sessionConfig) {
        return {
          success: false,
          error: 'Failed to create session config'
        };
      }

      return {
        success: true,
        data: sessionConfig
      };
    } catch (error) {
      console.error('Error generating session config:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate session config'
      };
    }
  }

  /**
   * 合并多个配置源
   */
  private mergeConfigurations(params: {
    userPreferences: any;
    packOverrides: any;
    systemDefaults: any;
    options: ConfigMergeOptions;
  }): any {
    const { userPreferences, packOverrides, systemDefaults, options } = params;
    
    // 从系统默认开始
    let mergedConfig = { ...systemDefaults };

    // 应用用户偏好（如果存在）
    if (userPreferences?.presentation_config) {
      try {
        const userConfig = JSON.parse(userPreferences.presentation_config);
        mergedConfig = this.deepMerge(mergedConfig, userConfig);
      } catch (error) {
        console.warn('Failed to parse user preferences config:', error);
      }
    }

    // 应用包覆盖（优先级最高）
    if (packOverrides?.presentation_overrides) {
      try {
        const overrideConfig = JSON.parse(packOverrides.presentation_overrides);
        mergedConfig = this.deepMerge(mergedConfig, overrideConfig);
      } catch (error) {
        console.warn('Failed to parse pack overrides config:', error);
      }
    }

    // 应用性能模式调整
    if (options.performanceMode) {
      mergedConfig = this.applyPerformanceOptimizations(mergedConfig, options.performanceMode);
    }

    // 应用无障碍优化
    if (options.prioritizeAccessibility) {
      mergedConfig = this.applyAccessibilityOptimizations(mergedConfig);
    }

    return mergedConfig;
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * 应用性能优化
   */
  private applyPerformanceOptimizations(config: any, mode: string): any {
    const optimizedConfig = { ...config };
    
    if (mode === 'performance') {
      // 性能优先模式
      if (optimizedConfig.layer2_rendering_strategy) {
        optimizedConfig.layer2_rendering_strategy.performance_mode = 'performance';
        optimizedConfig.layer2_rendering_strategy.supported_content_types = {
          ...optimizedConfig.layer2_rendering_strategy.supported_content_types,
          animation: false,
          video: false,
          rich_text: false
        };
      }
      
      if (optimizedConfig.layer3_skin_base?.animations) {
        optimizedConfig.layer3_skin_base.animations.enable_animations = false;
      }
    } else if (mode === 'high_quality') {
      // 高质量模式
      if (optimizedConfig.layer2_rendering_strategy) {
        optimizedConfig.layer2_rendering_strategy.performance_mode = 'high_quality';
      }
    }
    
    return optimizedConfig;
  }

  /**
   * 应用无障碍优化
   */
  private applyAccessibilityOptimizations(config: any): any {
    const accessibleConfig = { ...config };
    
    if (accessibleConfig.layer5_accessibility) {
      accessibleConfig.layer5_accessibility = {
        ...accessibleConfig.layer5_accessibility,
        keyboard_navigation: true,
        high_contrast: true,
        large_text: true
      };
    }
    
    if (accessibleConfig.layer3_skin_base?.animations) {
      accessibleConfig.layer3_skin_base.animations.reduce_motion = true;
      accessibleConfig.layer3_skin_base.animations.animation_speed = 'slow';
    }
    
    return accessibleConfig;
  }

  /**
   * 构建配置来源记录
   */
  private buildConfigSources(params: {
    userPreferences: any;
    packOverrides: any;
    hasSystemDefaults: boolean;
  }): ConfigSource[] {
    const sources: ConfigSource[] = [];
    const timestamp = new Date().toISOString();

    // 系统默认
    if (params.hasSystemDefaults) {
      sources.push({
        type: 'system_default',
        id: 'system_default_v2',
        priority: 1,
        applied_at: timestamp
      });
    }

    // 用户偏好
    if (params.userPreferences) {
      sources.push({
        type: 'user_preferences',
        id: params.userPreferences.id,
        priority: 2,
        applied_at: timestamp
      });
    }

    // 包覆盖
    if (params.packOverrides) {
      sources.push({
        type: 'pack_override',
        id: params.packOverrides.id,
        priority: 3,
        applied_at: timestamp
      });
    }

    return sources;
  }

  /**
   * 计算个性化级别
   */
  private calculatePersonalizationLevel(mergedConfig: any, userPreferences: any): number {
    if (!userPreferences) {
      return 0; // 完全使用默认配置
    }

    // 基于用户偏好的个性化级别
    let level = userPreferences.personalization_level || 50;

    // 根据配置的复杂度调整
    const configComplexity = this.calculateConfigComplexity(mergedConfig);
    level = Math.min(100, level + configComplexity);

    return Math.round(level);
  }

  /**
   * 计算配置复杂度
   */
  private calculateConfigComplexity(config: any): number {
    let complexity = 0;
    
    // 检查各层配置的自定义程度
    if (config.layer1_user_choice?.preferred_view_type !== 'wheel') {
      complexity += 10;
    }
    
    if (config.layer2_rendering_strategy?.performance_mode !== 'balanced') {
      complexity += 5;
    }
    
    if (config.layer3_skin_base?.selected_skin_id !== 'default') {
      complexity += 15;
    }
    
    if (config.layer5_accessibility?.high_contrast) {
      complexity += 10;
    }
    
    return Math.min(30, complexity); // 最多增加30分
  }

  /**
   * 获取系统默认配置
   */
  private getSystemDefaultConfig(): any {
    return {
      layer0_dataset_presentation: {
        preferred_pack_categories: ['daily', 'assessment'],
        default_difficulty_preference: 'regular',
        session_length_preference: 'medium',
        auto_select_recommended: false,
        restore_progress: true
      },
      layer1_user_choice: {
        preferred_view_type: 'wheel',
        active_skin_id: 'default',
        color_mode: 'warm',
        user_level: 'regular'
      },
      layer2_rendering_strategy: {
        render_engine_preferences: {
          wheel: 'D3',
          card: 'SVG',
          bubble: 'Canvas'
        },
        performance_mode: 'balanced',
        supported_content_types: {
          text: true,
          emoji: true,
          image: true,
          icon: true,
          audio: false,
          video: false,
          animation: true,
          rich_text: false
        }
      },
      layer3_skin_base: {
        selected_skin_id: 'default',
        colors: {
          primary: '#4F46E5',
          secondary: '#7C3AED',
          accent: '#F59E0B'
        },
        animations: {
          enable_animations: true,
          animation_speed: 'normal',
          reduce_motion: false
        }
      },
      layer4_view_detail: {
        wheel_config: {
          container_size: 400,
          wheel_radius: 180,
          emotion_display_mode: 'hierarchical',
          show_labels: true,
          show_emojis: true
        }
      },
      layer5_accessibility: {
        high_contrast: false,
        large_text: false,
        reduce_motion: false,
        keyboard_navigation: true,
        voice_guidance: false
      }
    };
  }
}
