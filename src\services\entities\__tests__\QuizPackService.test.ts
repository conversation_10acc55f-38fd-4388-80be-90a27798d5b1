/**
 * QuizPackService 测试
 * 测试Quiz包服务的各种功能
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { QuizPackService } from '../QuizPackService';
import { QuizPackRepository } from '../QuizPackRepository';
import { QuizPack } from '@/types/schema/base';
import { CreateQuizPackInput, UpdateQuizPackInput } from '@/types/schema/api';

// Mock QuizPackRepository
vi.mock('../QuizPackRepository');

describe('QuizPackService', () => {
  let service: QuizPackService;
  let mockRepository: any;

  const mockQuizPack: QuizPack = {
    id: 'pack1',
    name: '情绪识别测试',
    description: '测试情绪识别能力',
    quiz_type: 'emotion_wheel',
    category: '心理健康',
    difficulty_level: 3,
    estimated_duration_minutes: 15,
    tags: JSON.stringify(['情绪', '心理']),
    is_active: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user1',
    updated_by: 'user1'
  };

  const mockCreateInput: CreateQuizPackInput = {
    name: '新测试包',
    description: '新的测试包描述',
    quiz_type: 'emotion_wheel',
    category: '心理健康',
    difficulty_level: 2,
    estimated_duration_minutes: 10,
    tags: JSON.stringify(['测试']),
    is_active: true
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mock repository
    mockRepository = {
      create: vi.fn(),
      findById: vi.fn(),
      findAll: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      findActiveQuizPacks: vi.fn(),
      findByQuizType: vi.fn(),
      findByCategory: vi.fn(),
      searchQuizPacks: vi.fn(),
      findByDifficultyLevel: vi.fn(),
      findByTag: vi.fn(),
      getQuizPackStats: vi.fn()
    };

    vi.mocked(QuizPackRepository).mockImplementation(() => mockRepository);
    service = new QuizPackService();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('createQuizPack', () => {
    it('should create quiz pack successfully', async () => {
      mockRepository.create.mockResolvedValue(mockQuizPack);

      const result = await service.createQuizPack(mockCreateInput);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockQuizPack);
      expect(mockRepository.create).toHaveBeenCalledWith(mockCreateInput);
    });

    it('should validate required fields', async () => {
      const invalidInput = { ...mockCreateInput, name: '' };

      const result = await service.createQuizPack(invalidInput);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Quiz pack name is required');
    });

    it('should validate name length', async () => {
      const invalidInput = { 
        ...mockCreateInput, 
        name: 'a'.repeat(101) 
      };

      const result = await service.createQuizPack(invalidInput);

      expect(result.success).toBe(false);
      expect(result.error).toContain('must be less than 100 characters');
    });

    it('should validate difficulty level', async () => {
      const invalidInput = { 
        ...mockCreateInput, 
        difficulty_level: 6 
      };

      const result = await service.createQuizPack(invalidInput);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Difficulty level must be between 1 and 5');
    });

    it('should handle repository errors', async () => {
      mockRepository.create.mockRejectedValue(new Error('Database error'));

      const result = await service.createQuizPack(mockCreateInput);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to create quiz pack');
    });
  });

  describe('getActiveQuizPacks', () => {
    it('should return active quiz packs', async () => {
      const mockPacks = [mockQuizPack];
      mockRepository.findActiveQuizPacks.mockResolvedValue(mockPacks);

      const result = await service.getActiveQuizPacks();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPacks);
      expect(mockRepository.findActiveQuizPacks).toHaveBeenCalled();
    });

    it('should handle repository errors', async () => {
      mockRepository.findActiveQuizPacks.mockRejectedValue(new Error('Database error'));

      const result = await service.getActiveQuizPacks();

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to get active quiz packs');
    });
  });

  describe('getQuizPacksByType', () => {
    it('should return quiz packs by type', async () => {
      const mockPacks = [mockQuizPack];
      mockRepository.findByQuizType.mockResolvedValue(mockPacks);

      const result = await service.getQuizPacksByType('emotion_wheel');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPacks);
      expect(mockRepository.findByQuizType).toHaveBeenCalledWith('emotion_wheel');
    });
  });

  describe('searchQuizPacks', () => {
    it('should search quiz packs successfully', async () => {
      const mockPacks = [mockQuizPack];
      mockRepository.searchQuizPacks.mockResolvedValue(mockPacks);

      const result = await service.searchQuizPacks('情绪');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPacks);
      expect(mockRepository.searchQuizPacks).toHaveBeenCalledWith('情绪');
    });

    it('should validate search term length', async () => {
      const result = await service.searchQuizPacks('a');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Search term must be at least 2 characters long');
    });

    it('should handle empty search term', async () => {
      const result = await service.searchQuizPacks('');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Search term must be at least 2 characters long');
    });
  });

  describe('getQuizPacksByDifficulty', () => {
    it('should return quiz packs by difficulty', async () => {
      const mockPacks = [mockQuizPack];
      mockRepository.findByDifficultyLevel.mockResolvedValue(mockPacks);

      const result = await service.getQuizPacksByDifficulty(3);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPacks);
      expect(mockRepository.findByDifficultyLevel).toHaveBeenCalledWith(3);
    });

    it('should validate difficulty level range', async () => {
      const result = await service.getQuizPacksByDifficulty(6);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Difficulty level must be between 1 and 5');
    });
  });

  describe('getQuizPacksByTag', () => {
    it('should return quiz packs by tag', async () => {
      const mockPacks = [mockQuizPack];
      mockRepository.findByTag.mockResolvedValue(mockPacks);

      const result = await service.getQuizPacksByTag('情绪');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPacks);
      expect(mockRepository.findByTag).toHaveBeenCalledWith('情绪');
    });

    it('should validate empty tag', async () => {
      const result = await service.getQuizPacksByTag('');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Tag cannot be empty');
    });
  });

  describe('getQuizPackStats', () => {
    it('should return quiz pack statistics', async () => {
      const mockRawStats = {
        total_sessions: 100,
        completed_sessions: 80,
        total_questions: 20,
        avg_completion_time_minutes: 12.5
      };

      mockRepository.getQuizPackStats.mockResolvedValue(mockRawStats);

      const result = await service.getQuizPackStats('pack1');

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        total_sessions: 100,
        completed_sessions: 80,
        total_questions: 20,
        avg_completion_time_minutes: 13, // rounded
        completion_rate: 80, // 80/100 * 100
        popularity_score: 94 // (100 * 0.7) + (80 * 0.3)
      });
    });

    it('should handle zero sessions gracefully', async () => {
      const mockRawStats = {
        total_sessions: 0,
        completed_sessions: 0,
        total_questions: 10,
        avg_completion_time_minutes: 0
      };

      mockRepository.getQuizPackStats.mockResolvedValue(mockRawStats);

      const result = await service.getQuizPackStats('pack1');

      expect(result.success).toBe(true);
      expect(result.data?.completion_rate).toBe(0);
      expect(result.data?.popularity_score).toBe(0);
    });
  });

  describe('getRecommendedQuizPacks', () => {
    it('should return recommended quiz packs', async () => {
      const mockPacks = [mockQuizPack];
      const mockStats = {
        total_sessions: 50,
        completed_sessions: 40,
        total_questions: 15,
        avg_completion_time_minutes: 10
      };

      mockRepository.findActiveQuizPacks.mockResolvedValue(mockPacks);
      mockRepository.getQuizPackStats.mockResolvedValue(mockStats);

      const result = await service.getRecommendedQuizPacks(5);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data?.[0]).toEqual(mockQuizPack);
    });

    it('should limit results correctly', async () => {
      const mockPacks = Array(15).fill(null).map((_, i) => ({
        ...mockQuizPack,
        id: `pack${i + 1}`
      }));

      mockRepository.findActiveQuizPacks.mockResolvedValue(mockPacks);
      mockRepository.getQuizPackStats.mockResolvedValue({
        total_sessions: 10,
        completed_sessions: 8,
        total_questions: 5,
        avg_completion_time_minutes: 5
      });

      const result = await service.getRecommendedQuizPacks(10);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(10);
    });
  });

  describe('updateQuizPack', () => {
    it('should update quiz pack successfully', async () => {
      const updateInput: UpdateQuizPackInput = {
        name: '更新的测试包',
        difficulty_level: 4
      };

      const updatedPack = { ...mockQuizPack, ...updateInput };
      mockRepository.update.mockResolvedValue(updatedPack);

      const result = await service.updateQuizPack('pack1', updateInput);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(updatedPack);
      expect(mockRepository.update).toHaveBeenCalledWith('pack1', updateInput);
    });

    it('should validate update data', async () => {
      const invalidUpdate: UpdateQuizPackInput = {
        name: '',
        difficulty_level: 6
      };

      const result = await service.updateQuizPack('pack1', invalidUpdate);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Quiz pack name cannot be empty');
    });
  });

  describe('activateQuizPack', () => {
    it('should activate quiz pack', async () => {
      const activatedPack = { ...mockQuizPack, is_active: true };
      mockRepository.update.mockResolvedValue(activatedPack);

      const result = await service.activateQuizPack('pack1');

      expect(result.success).toBe(true);
      expect(result.data?.is_active).toBe(true);
      expect(mockRepository.update).toHaveBeenCalledWith('pack1', { is_active: true });
    });
  });

  describe('deactivateQuizPack', () => {
    it('should deactivate quiz pack', async () => {
      const deactivatedPack = { ...mockQuizPack, is_active: false };
      mockRepository.update.mockResolvedValue(deactivatedPack);

      const result = await service.deactivateQuizPack('pack1');

      expect(result.success).toBe(true);
      expect(result.data?.is_active).toBe(false);
      expect(mockRepository.update).toHaveBeenCalledWith('pack1', { is_active: false });
    });
  });

  describe('getQuizPackCategories', () => {
    it('should return unique categories', async () => {
      const mockPacks = [
        { ...mockQuizPack, category: '心理健康' },
        { ...mockQuizPack, id: 'pack2', category: '认知能力' },
        { ...mockQuizPack, id: 'pack3', category: '心理健康' } // duplicate
      ];

      mockRepository.findActiveQuizPacks.mockResolvedValue(mockPacks);

      const result = await service.getQuizPackCategories();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(['心理健康', '认知能力']);
    });
  });

  describe('getQuizPackTypes', () => {
    it('should return unique quiz types', async () => {
      const mockPacks = [
        { ...mockQuizPack, quiz_type: 'emotion_wheel' },
        { ...mockQuizPack, id: 'pack2', quiz_type: 'multiple_choice' },
        { ...mockQuizPack, id: 'pack3', quiz_type: 'emotion_wheel' } // duplicate
      ];

      mockRepository.findActiveQuizPacks.mockResolvedValue(mockPacks);

      const result = await service.getQuizPackTypes();

      expect(result.success).toBe(true);
      expect(result.data).toEqual(['emotion_wheel', 'multiple_choice']);
    });
  });
});
