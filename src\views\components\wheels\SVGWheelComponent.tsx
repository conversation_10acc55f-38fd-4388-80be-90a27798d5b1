/**
 * SVG轮盘组件
 * 使用SVG实现的轮盘，不依赖旧的轮盘实现
 *
 * 此组件是新视图系统的一部分，用于替代旧的 SVGWheelComponent
 * 它直接实现了轮盘的渲染，不依赖旧的 SVGWheel 类
 */

import { AnimatedEmoji } from '@/components/emoji/AnimatedEmoji';
import { useEmoji } from '@/contexts/EmojiContext';
import type { Emotion, ContentDisplayMode, SkinConfig } from '@/types';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { extract_wheel_config_for_engine } from '../../../utils/wheelConfigExtractor';

interface SVGWheelComponentProps {
  emotions: Emotion[];
  tierLevel: number;
  contentDisplayMode: ContentDisplayMode;
  skinConfig: SkinConfig;
  onSelect: (emotion: Emotion) => void;
  onBack?: () => void;
  selectedPath?: any;
}

/**
 * SVG轮盘组件
 */
export const SVGWheelComponent: React.FC<SVGWheelComponentProps> = ({
  emotions,
  tierLevel,
  contentDisplayMode,
  skinConfig,
  onSelect,
  onBack,
  selectedPath,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [hoveredEmotion, setHoveredEmotion] = useState<string | null>(null);
  const { getEmojiItemForEmotionId } = useEmoji();

  // 提取轮盘配置
  const wheelConfig = extract_wheel_config_for_engine.svg(skinConfig);

  useEffect(() => {
    if (!svgRef.current || emotions.length === 0) return;

    // 清除现有内容
    const svg = svgRef.current;
    while (svg.firstChild) {
      svg.removeChild(svg.firstChild);
    }

    // 设置尺寸和中心点
    const width = wheelConfig.container_size;
    const height = wheelConfig.container_size;
    const radius = wheelConfig.wheel_radius;
    const centerX = width / 2;
    const centerY = height / 2;

    // 设置SVG属性
    svg.setAttribute('width', width.toString());
    svg.setAttribute('height', height.toString());
    svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
    svg.setAttribute('style', 'max-width: 100%; height: auto;');

    // 添加阴影效果
    if (wheelConfig.shadow_enabled) {
      const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
      const filter = document.createElementNS('http://www.w3.org/2000/svg', 'filter');
      filter.setAttribute('id', 'shadow');

      const feDropShadow = document.createElementNS('http://www.w3.org/2000/svg', 'feDropShadow');
      feDropShadow.setAttribute('dx', '0');
      feDropShadow.setAttribute('dy', '0');
      feDropShadow.setAttribute('stdDeviation', wheelConfig.shadowBlur.toString());
      feDropShadow.setAttribute('flood-color', wheelConfig.shadowColor);

      filter.appendChild(feDropShadow);
      defs.appendChild(filter);
      svg.appendChild(defs);
    }

    // 创建轮盘组
    const wheel = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    wheel.setAttribute('transform', `translate(${centerX}, ${centerY})`);

    if (wheelConfig.shadow_enabled) {
      wheel.setAttribute('filter', 'url(#shadow)');
    }

    svg.appendChild(wheel);

    // 计算每个扇区的角度
    const anglePerSector = (2 * Math.PI) / emotions.length;

    // 绘制扇区
    emotions.forEach((emotion, index) => {
      const startAngle = index * anglePerSector;
      const endAngle = (index + 1) * anglePerSector;

      // 创建扇区路径
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');

      // 计算扇区路径
      const x1 = radius * Math.cos(startAngle);
      const y1 = radius * Math.sin(startAngle);
      const x2 = radius * Math.cos(endAngle);
      const y2 = radius * Math.sin(endAngle);

      // 使用SVG路径命令创建扇区
      const largeArcFlag = endAngle - startAngle > Math.PI ? 1 : 0;
      const pathData = `M 0 0 L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;

      path.setAttribute('d', pathData);
      path.setAttribute('fill', emotion.color || '#cccccc');
      path.setAttribute('stroke', wheelConfig.background_color);
      path.setAttribute('stroke-width', wheelConfig.sector_gap.toString());
      path.setAttribute(
        'style',
        `cursor: pointer; transition: all ${wheelConfig.transition_duration}ms`
      );

      // 添加事件监听器
      path.addEventListener('mouseover', () => {
        setHoveredEmotion(emotion.id);
        if (wheelConfig.hover_effect === 'scale') {
          path.setAttribute('transform', 'scale(1.05)');
        }
      });

      path.addEventListener('mouseout', () => {
        setHoveredEmotion(null);
        path.setAttribute('transform', 'scale(1)');
      });

      path.addEventListener('click', () => {
        onSelect(emotion);
      });

      wheel.appendChild(path);

      // 添加文本和表情
      if (contentDisplayMode !== 'animatedEmoji') {
        const labelGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');

        // 计算扇区中心点
        const midAngle = startAngle + (endAngle - startAngle) / 2;
        const labelRadius = radius * 0.7; // 调整半径，使文本位于扇区中心
        const labelX = labelRadius * Math.cos(midAngle);
        const labelY = labelRadius * Math.sin(midAngle);

        labelGroup.setAttribute('transform', `translate(${labelX}, ${labelY})`);

        // 根据内容类型显示文本和表情
        if (contentDisplayMode === 'text' || contentDisplayMode === 'textEmoji') {
          const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          text.setAttribute('text-anchor', 'middle');
          text.setAttribute('dy', contentDisplayMode === 'textEmoji' ? '1.5em' : '0.3em');
          text.setAttribute('fill', wheelConfig.text_color);
          text.setAttribute('font-family', wheelConfig.font_family);
          text.setAttribute('font-size', wheelConfig.font_size.toString());
          text.textContent = emotion.name;

          labelGroup.appendChild(text);
        }

        if (contentDisplayMode === 'emoji' || contentDisplayMode === 'textEmoji') {
          const emojiText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          emojiText.setAttribute('text-anchor', 'middle');
          emojiText.setAttribute('dy', contentDisplayMode === 'textEmoji' ? '-0.5em' : '0.3em');
          emojiText.setAttribute('fill', wheelConfig.text_color);
          emojiText.setAttribute('font-family', wheelConfig.font_family);
          emojiText.setAttribute('font-size', wheelConfig.emojiSize.toString());
          emojiText.textContent = emotion.emoji;

          labelGroup.appendChild(emojiText);
        }

        wheel.appendChild(labelGroup);
      }
    });

    // 添加装饰效果
    if (wheelConfig.decorations) {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', '0');
      circle.setAttribute('cy', '0');
      circle.setAttribute('r', (radius + 5).toString());
      circle.setAttribute('fill', 'none');
      circle.setAttribute('stroke', wheelConfig.shadowColor);
      circle.setAttribute('stroke-width', '2');
      circle.setAttribute('opacity', '0.5');

      wheel.appendChild(circle);
    }
  }, [emotions, tierLevel, contentDisplayMode, skinConfig, wheelConfig, onSelect]);

  // 渲染动画表情（如果内容类型为 animatedEmoji）
  const renderAnimatedEmojis = () => {
    if (contentDisplayMode !== 'animatedEmoji' || !emotions.length) return null;

    return emotions.map((emotion, index) => {
      const angle = (index / emotions.length) * 2 * Math.PI;
      const radius = wheelConfig.wheel_radius * 0.7; // 调整半径，使表情位于扇区中心
      const x = Math.cos(angle) * radius + wheelConfig.container_size / 2;
      const y = Math.sin(angle) * radius + wheelConfig.container_size / 2;

      const emojiItem = getEmojiItemForEmotionId(emotion.id);

      return (
        <div
          key={emotion.id}
          style={{
            position: 'absolute',
            left: `${x}px`,
            top: `${y}px`,
            transform: 'translate(-50%, -50%)',
            zIndex: hoveredEmotion === emotion.id ? 10 : 1,
            transition: `all ${wheelConfig.transition_duration}ms`,
            cursor: 'pointer',
          }}
          onClick={() => onSelect(emotion)}
          onMouseEnter={() => setHoveredEmotion(emotion.id)}
          onMouseLeave={() => setHoveredEmotion(null)}
        >
          {emojiItem ? (
            <AnimatedEmoji
              emojiItem={emojiItem}
              size={
                wheelConfig.emojiSize >= 40
                  ? '4xl'
                  : wheelConfig.emojiSize >= 32
                    ? '3xl'
                    : wheelConfig.emojiSize >= 24
                      ? '2xl'
                      : wheelConfig.emojiSize >= 20
                        ? 'xl'
                        : wheelConfig.emojiSize >= 16
                          ? 'lg'
                          : wheelConfig.emojiSize >= 14
                            ? 'md'
                            : wheelConfig.emojiSize >= 12
                              ? 'sm'
                              : 'xs'
              }
              autoPlay={true}
              loop={true}
            />
          ) : (
            <span style={{ fontSize: `${wheelConfig.emojiSize}px` }}>{emotion.emoji}</span>
          )}
          {/* 在动画表情模式下，可以选择性显示文本标签 */}
          {false && (
            <div
              style={{
                textAlign: 'center',
                fontSize: `${wheelConfig.font_size}px`,
                fontFamily: wheelConfig.font_family,
                color: wheelConfig.text_color,
                marginTop: '4px',
              }}
            >
              {emotion.name}
            </div>
          )}
        </div>
      );
    });
  };

  // 渲染返回按钮
  const renderBackButton = () => {
    if (!onBack || tierLevel === 1) return null;

    return (
      <button
        onClick={onBack}
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 100,
          background: 'transparent',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '40px',
          height: '40px',
          boxShadow: wheelConfig.shadow_enabled ? `0 0 5px ${wheelConfig.shadowColor}` : 'none',
        }}
      >
        ←
      </button>
    );
  };

  // 渲染选中路径
  const renderSelectedPath = () => {
    if (!selectedPath) return null;

    return (
      <div
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          zIndex: 100,
          fontSize: `${wheelConfig.font_size}px`,
          fontFamily: wheelConfig.font_family,
          color: wheelConfig.text_color,
          padding: '5px',
          borderRadius: '5px',
          background: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(5px)',
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {selectedPath}
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width: `${wheelConfig.container_size}px`,
        height: `${wheelConfig.container_size}px`,
        margin: '0 auto',
      }}
    >
      {renderBackButton()}
      {renderSelectedPath()}
      <svg ref={svgRef} />
      {renderAnimatedEmojis()}
    </div>
  );
};
