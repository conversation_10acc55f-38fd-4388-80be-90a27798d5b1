/**
 * 应用层次结构定义
 *
 * 这个文件定义了应用中各种UI元素的z-index层次，
 * 确保元素在正确的层次上显示，避免层次混乱。
 *
 * 层次从低到高排列：
 * - GAME_WORLD: 主要内容区域（情绪轮盘）
 * - HUD: 状态信息（情绪路径、强度指示器）
 * - INTERACTION: 交互元素（按钮、输入框）
 * - NAVIGATION: 导航（底部导航栏、返回按钮）
 * - MODAL: 弹窗（确认对话框、提示）
 */

export const LAYERS = {
  /**
   * 主要内容区域，如情绪轮盘、背景等
   */
  GAME_WORLD: 10,

  /**
   * 状态信息层，如情绪路径、强度指示器等
   */
  HUD: 20,

  /**
   * 交互元素层，如按钮、输入框等
   */
  INTERACTION: 30,

  /**
   * 导航层，如底部导航栏、返回按钮等
   */
  NAVIGATION: 40,

  /**
   * 弹窗层，如确认对话框、提示等
   */
  MODAL: 50,
};
