/**
 * 通用数据库操作接口
 * 定义所有数据库操作的通用方法，不依赖于特定的数据库提供商
 */

// 导入统一的类型定义
import {
  type ResultSet,
  type InStatement,
  type TransactionMode,
  ResultSetSchema,
  InStatementSchema,
  TransactionModeSchema
} from '../../../src/types/schema/api.js';

export interface Transaction {
  execute(sql: string | InStatement): Promise<ResultSet>;
  batch(statements: InStatement[]): Promise<ResultSet[]>;
  commit(): Promise<void>;
  rollback(): Promise<void>;
}

/**
 * 数据库服务接口
 */
export interface DatabaseInterface {
  /**
   * 获取数据库连接
   */
  getConnection(): any;

  /**
   * 执行单个 SQL 查询
   * @param sql SQL 查询或 InStatement 对象
   */
  executeQuery(sql: string | InStatement): Promise<ResultSet>;

  /**
   * 在事务中执行一批 SQL 语句
   * @param statements InStatement 对象数组
   * @param mode 事务模式
   */
  batchStatements(statements: InStatement[], mode?: TransactionMode): Promise<ResultSet[] | null>;

  /**
   * 执行多语句 SQL 脚本
   * @param sqlScript 包含多个语句的 SQL 脚本
   */
  executeScript(sqlScript: string): Promise<void>;

  /**
   * 从指定表中获取所有行
   * @param tableName 表名
   * @param limit 可选的行数限制
   */
  fetchAllFromTable(tableName: string, limit?: number): Promise<any[]>;

  /**
   * 开始事务
   * @param mode 事务模式
   */
  transaction(mode?: TransactionMode): Promise<Transaction>;

  /**
   * 关闭数据库连接
   */
  close(): Promise<void>;

  /**
   * 获取数据库类型
   */
  getDatabaseType(): string;
}
