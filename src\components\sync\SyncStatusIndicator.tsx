import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useLanguage } from '@/contexts/LanguageContext';
import { useNetwork } from '@/contexts/NetworkContext';
import { useSync } from '@/contexts/SyncContext';
import {
  AlertCircle,
  CheckCircle2,
  Clock,
  Cloud,
  CloudOff,
  RefreshCw,
  Wifi,
  WifiOff,
  XCircle,
} from 'lucide-react';
import type React from 'react';

interface SyncStatusIndicatorProps {
  showControls?: boolean;
  showDetails?: boolean;
  variant?: 'default' | 'compact' | 'minimal';
}

/**
 * 同步状态指示器组件
 * 显示同步状态、进度和控制按钮
 */
const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  showControls = true,
  showDetails = true,
  variant = 'default',
}) => {
  const { t } = useLanguage();
  const { isOnline, isInternetReachable, connectionType } = useNetwork();
  const {
    onlineSyncEnabled,
    toggleOnlineSync,
    syncStatus,
    startSync,
    cancelSync,
    retryFailedItems,
    pendingItems,
    failedItems,
  } = useSync();

  // 格式化日期时间
  const formatDateTime = (date: Date | null) => {
    if (!date) return t('sync.never');
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(date);
  };

  // 获取网络状态图标
  const getNetworkStatusIcon = () => {
    if (!isOnline) {
      return <CloudOff className="h-4 w-4 text-destructive" />;
    }

    if (!isInternetReachable) {
      return <AlertCircle className="h-4 w-4 text-warning" />;
    }

    if (connectionType === 'wifi') {
      return <Wifi className="h-4 w-4 text-primary" />;
    }

    return <Cloud className="h-4 w-4 text-primary" />;
  };

  // 获取同步状态图标
  const getSyncStatusIcon = () => {
    if (syncStatus.isSyncing) {
      return <RefreshCw className="h-4 w-4 animate-spin text-primary" />;
    }

    if (failedItems.length > 0) {
      return <XCircle className="h-4 w-4 text-destructive" />;
    }

    if (pendingItems.length > 0) {
      return <Clock className="h-4 w-4 text-warning" />;
    }

    if (syncStatus.lastSuccessfulSync) {
      return <CheckCircle2 className="h-4 w-4 text-success" />;
    }

    return <Cloud className="h-4 w-4 text-muted-foreground" />;
  };

  // 获取同步状态文本
  const getSyncStatusText = () => {
    if (syncStatus.isSyncing) {
      return t('sync.syncing');
    }

    if (failedItems.length > 0) {
      return t('sync.has_failed_items', { count: String(failedItems.length) });
    }

    if (pendingItems.length > 0) {
      return t('sync.has_pending_items', { count: String(pendingItems.length) });
    }

    if (syncStatus.lastSuccessfulSync) {
      return t('sync.last_sync', { time: formatDateTime(syncStatus.lastSuccessfulSync) });
    }

    return t('sync.never_synced');
  };

  // 最小化变体
  if (variant === 'minimal') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-1">
              {getNetworkStatusIcon()}
              {getSyncStatusIcon()}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{getSyncStatusText()}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // 紧凑变体
  if (variant === 'compact') {
    return (
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          {getNetworkStatusIcon()}
          {getSyncStatusIcon()}
        </div>

        <span className="text-xs text-muted-foreground truncate max-w-[150px]">
          {getSyncStatusText()}
        </span>

        {showControls && (
          <div className="flex items-center space-x-1">
            {syncStatus.isSyncing ? (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => cancelSync()}
                disabled={!onlineSyncEnabled}
              >
                <XCircle className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => startSync()}
                disabled={!onlineSyncEnabled || !isOnline || pendingItems.length === 0}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </div>
    );
  }

  // 默认变体
  return (
    <div className="space-y-2 p-2 border rounded-md">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Badge variant={onlineSyncEnabled ? 'default' : 'outline'}>
            {onlineSyncEnabled ? t('sync.enabled') : t('sync.disabled')}
          </Badge>

          <div className="flex items-center space-x-1">
            {getNetworkStatusIcon()}
            <span className="text-xs text-muted-foreground">
              {isOnline
                ? isInternetReachable
                  ? t('sync.connected')
                  : t('sync.no_internet')
                : t('sync.offline')}
            </span>
          </div>
        </div>

        {showControls && (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => toggleOnlineSync()}>
              {onlineSyncEnabled ? t('sync.disable') : t('sync.enable')}
            </Button>
          </div>
        )}
      </div>

      {syncStatus.isSyncing && (
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              {syncStatus.currentSyncItem &&
                t('sync.syncing_item', {
                  current: String(Math.round((syncStatus.progress * pendingItems.length) / 100)),
                  total: String(pendingItems.length),
                })}
            </span>
            <span className="text-xs text-muted-foreground">{syncStatus.progress}%</span>
          </div>
          <Progress value={syncStatus.progress} className="h-1" />
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getSyncStatusIcon()}
          <span className="text-sm">{getSyncStatusText()}</span>
        </div>

        {showControls && (
          <div className="flex items-center space-x-2">
            {syncStatus.isSyncing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => cancelSync()}
                disabled={!onlineSyncEnabled}
              >
                {t('sync.cancel')}
              </Button>
            ) : (
              <>
                {failedItems.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => retryFailedItems()}
                    disabled={!onlineSyncEnabled || !isOnline}
                  >
                    {t('sync.retry')}
                  </Button>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => startSync()}
                  disabled={!onlineSyncEnabled || !isOnline || pendingItems.length === 0}
                >
                  {t('sync.sync_now')}
                </Button>
              </>
            )}
          </div>
        )}
      </div>

      {showDetails && (
        <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
          <div>
            {t('sync.pending_items')}: {pendingItems.length}
          </div>
          <div>
            {t('sync.failed_items')}: {failedItems.length}
          </div>
          <div>
            {t('sync.last_attempt')}: {formatDateTime(syncStatus.lastSyncAttempt)}
          </div>
          <div>
            {t('sync.last_success')}: {formatDateTime(syncStatus.lastSuccessfulSync)}
          </div>
        </div>
      )}
    </div>
  );
};

export default SyncStatusIndicator;
