# Quiz系统数据配置说明

## 📁 文件结构概览

```
public/seeds/
├── schema/                          # 系统默认配置数据
│   ├── tcm-quiz-packs.sql          # 中医体质问卷量表包
│   ├── mood-track-quest-branching.sql # 情绪追踪问卷量表包
│   ├── quiz_skin_configs.sql       # Quiz皮肤配置
│   ├── quiz_component_configs.sql  # Quiz组件配置
│   └── full.sql                    # 主数据库初始化文件
└── test/                           # 测试数据
    ├── quiz_sessions_test.sql      # Quiz会话测试数据
    ├── quiz_answers_test.sql       # Quiz答案测试数据
    ├── quiz_personalization_test.sql # 个性化配置测试数据
    └── master.sql                  # 测试数据主文件
```

## 🎯 数据类型说明

### 1. 系统默认配置 (schema/)

#### **量表包数据**
- **tcm-quiz-packs.sql**: 19个中医证素量表包
  - 脏腑证素: 心、肝、脾、肾、胆、胃、肠、经络、神
  - 病理证素: 气虚、血虚、阴虚、阳虚、气滞、血瘀、痰、湿、热、寒
  - 4级Likert量表 (无、轻、中、重)
  - 权重系数: 0, 0.7, 1.0, 1.5

- **mood-track-quest-branching.sql**: 情绪追踪分支问卷
  - 50个独立问题 (1个主要 + 8个次要 + 41个具体)
  - 条件分支逻辑
  - 3步完成流程

#### **皮肤配置数据**
- **quiz_skin_configs.sql**: 多种Quiz主题皮肤
  - 情绪问卷皮肤: 现代、温暖、极简风格
  - 中医问卷皮肤: 传统、现代中医风格
  - 通用皮肤: 深色主题、高对比度无障碍
  - 专门皮肤: 肾水元素主题等

#### **组件配置数据**
- **quiz_component_configs.sql**: UI组件配置
  - 情绪选择组件: 网格、列表、按钮布局
  - 中医评估组件: Likert量表、症状卡片
  - 进度指示器: 路径进度、评估进度
  - 结果展示: 情绪结果卡片、中医评估报告

### 2. 测试数据 (test/)

#### **会话数据**
- **quiz_sessions_test.sql**: 8个测试会话
  - 完整情绪路径: Happy→Playful→Aroused
  - 部分完成会话: 进行中的测试
  - 中医评估会话: 肝、肾、气虚证素
  - 多证素综合评估
  - 重复评估追踪

#### **答案数据**
- **quiz_answers_test.sql**: 详细答案记录
  - 情绪选择路径完整记录
  - 中医症状评分详细数据
  - 负分值处理示例
  - 性别限制问题处理
  - 响应时间和置信度数据

#### **个性化数据**
- **quiz_personalization_test.sql**: 用户个性化配置
  - 用户Quiz偏好设置
  - 量表包个性化配置
  - 用户统计和历史数据
  - A/B测试配置

## 🔧 使用方法

### 初始化系统默认数据
```sql
-- 加载完整的数据库schema和默认配置
.read public/seeds/schema/full.sql
```

### 加载测试数据
```sql
-- 加载所有测试数据
.read public/seeds/test/master.sql
```

### 单独加载特定数据
```sql
-- 只加载中医问卷
.read public/seeds/schema/tcm-quiz-packs.sql

-- 只加载情绪问卷
.read public/seeds/schema/mood-track-quest-branching.sql

-- 只加载皮肤配置
.read public/seeds/schema/quiz_skin_configs.sql
```

## 📊 数据统计

### 量表包数据量
```
中医体质问卷:
├── 量表包: 19个
├── 问题: 191个 (示例中展示了部分)
├── 选项: 764个 (每题4个选项)
└── 特殊处理: 负分值、性别限制

情绪追踪问卷:
├── 量表包: 1个
├── 问题: 50个 (条件分支)
├── 选项: 164个 (8+41*4+82*2)
└── 用户体验: 每次只回答3个问题
```

### 皮肤配置数量
```
皮肤主题: 8个
├── 情绪问卷专用: 3个 (现代、温暖、极简)
├── 中医问卷专用: 3个 (传统、现代、水元素)
└── 通用主题: 2个 (深色、无障碍)
```

### 组件配置数量
```
UI组件: 8个
├── 情绪选择: 3个 (网格、列表、按钮)
├── 中医评估: 2个 (Likert、卡片)
├── 进度指示: 2个 (路径、评估)
└── 结果展示: 2个 (情绪、中医)
```

### 测试数据量
```
测试会话: 8个
├── 情绪追踪: 4个
├── 中医评估: 3个
└── 综合评估: 1个

测试答案: 20+个详细记录
个性化配置: 10+个用户配置示例
```

## 🎨 皮肤主题预览

### 情绪问卷主题
- **现代风格**: 简洁蓝色系，Inter字体，圆角设计
- **温暖治愈**: 橙黄色系，Nunito字体，温暖渐变
- **极简风格**: 黑白色系，SF Pro字体，最小化设计

### 中医问卷主题
- **传统中医**: 红棕色系，宋体字体，五行配色
- **现代中医**: 紫蓝色系，现代字体，科技感设计
- **水元素**: 蓝色系，专为肾证素设计

### 通用主题
- **深色主题**: 适合夜间使用，护眼设计
- **无障碍主题**: 高对比度，大字体，无动画

## 🔍 数据验证

### 完整性检查
- [ ] 所有量表包都有对应的问题和选项
- [ ] 条件逻辑正确设置
- [ ] 外键关系完整
- [ ] 皮肤配置JSON格式正确

### 功能验证
- [ ] 情绪路径追踪正常工作
- [ ] 中医评分计算准确
- [ ] 皮肤切换正常显示
- [ ] 个性化配置生效

### 性能验证
- [ ] 数据加载时间合理
- [ ] 查询性能优化
- [ ] 索引设置正确
- [ ] 内存使用合理

## 🚀 扩展指南

### 添加新量表包
1. 在schema/目录创建新的SQL文件
2. 定义量表包、问题、选项
3. 更新full.sql引用新文件
4. 创建对应的皮肤配置

### 添加新皮肤主题
1. 在quiz_skin_configs.sql添加新配置
2. 定义color_scheme、typography、layout
3. 设置特定的主题特性
4. 测试在不同设备上的显示效果

### 添加测试数据
1. 在test/目录创建新的测试文件
2. 遵循现有的数据格式
3. 更新master.sql引用新文件
4. 确保数据的真实性和多样性

这套数据配置为Quiz系统提供了完整的基础数据和丰富的测试场景，支持情绪追踪和中医体质评估两大核心功能，同时提供了灵活的皮肤主题和个性化配置选项。
