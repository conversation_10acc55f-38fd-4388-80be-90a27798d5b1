### `src/pages` 页面在量表和配置方面可能存在的滞后分析 (2024年更新)

我已经彻底查看了 `public/seeds/schema` 下的表结构设计（`quiz_system_v2.sql` 和 `migration_v2.sql`）以及 `public/seeds/test-user-data` 下的测试数据（`user_emoji_preferences.sql`、`quiz_pack_overrides_test.sql` 和 `user_configs_test.sql`）。

**🔄 2024年滞后分析更新状态：**

✅ **已解决的滞后问题**：
- 服务层架构统一 - BaseService/BaseRepository模式已建立
- 类型系统对齐 - TypeScript错误已修复，审计字段自动管理
- 配置系统分离 - 全局配置与Quiz配置已完全独立
- Context现代化 - SkinContext, EmojiContext已更新

🔄 **部分解决，需要完善**：
- 表情符号映射的细粒度管理
- Quiz会话配置的完整应用
- 废弃服务的清理和迁移

⚠️ **仍存在的滞后问题**：
- 某些页面仍使用模拟数据（如WheelTest.tsx）
- 在线服务端点的缺失
- 废弃概念的完全清理

以下是我对页面在量表和配置方面可能存在的滞后性的分析：

### 数据库表结构与测试数据总结

**数据库模式的核心变化：**

*   **转向通用测验系统 (v2)：** 数据库架构已从"以情绪为中心"的设计（`emotions`、`emotion_translations`、`emoji_items` 表已废弃）转变为更通用的"测验系统"。这是理解 UI 滞后的关键点。
*   **基于 JSON 的灵活性：** 新架构大量使用 `quiz_packs`、`quiz_questions`、`quiz_question_options` 和 `user_presentation_configs` 中的 JSON 字段来存储详细的配置，包括：
    *   `quiz_packs.quiz_logic_config`：测验逻辑配置。
    *   `quiz_questions.question_config`：问题特定配置。
    *   `quiz_question_options.scoring_value`, `min_value`, `max_value`, `step_value`：直接支持量表类型选项。
    *   `user_presentation_configs.presentation_config`：关键的 6 层个性化配置，用于存储情绪到表情符号的映射、颜色和动画。
    *   `pack_presentation_overrides.presentation_overrides`：测验包特定的展现覆盖。
    *   `quiz_session_presentation_configs.presentation_config`：测验会话开始时展现配置的快照。
*   **旧表的废弃：** `quiz_system_v2.sql` 中明确的废弃注释以及 `migration_v2.sql` 中的迁移策略表明，如果 UI 组件仍依赖 `emotions` 或 `emoji_items` 等旧表，则它们很可能已过时。
*   **量表表示：** 量表现在通过 `quiz_questions` 中的 `question_type = 'scale_rating'` 以及 `quiz_question_options` 中的 `scoring_value`、`min_value`、`max_value`、`step_value` 来表示。

**测试数据中的关键发现：**

*   **丰富的 JSON 个性化数据：** `user_emoji_preferences.sql` 展示了 `user_presentation_configs.presentation_config` 如何存储复杂的 JSON 结构，用于每个情绪的 `emoji_mapping`（主用和备用）、`color_mapping` 和 `animation_mapping`。这证实了个性化数据的灵活深度嵌套。
*   **测验包覆盖：** `quiz_pack_overrides_test.sql` 演示了 `quiz_pack_overrides` 如何自定义测验包特定设置，例如 `show_progress_bar`、`question_timeout`，甚至支持 `pack_id = '*'` 的全局覆盖。
*   **用户全局配置：** `user_configs_test.sql` 显示 `user_configs` 存储全局用户偏好，如 `preferred_language`、`selected_skin_id`，其中 `config_value` 字段可以是 JSON。

### `src/pages` 页面在量表和配置方面可能存在的滞后

综合以上信息，与之前对 `src/pages` 的分析进行交叉引用，以下是页面在量表和配置方面可能存在的"滞后"或不一致的潜在领域：

1.  **`EmojiSetManager.tsx` 和表情符号映射：**
    *   **数据库/数据：** `user_presentation_configs` 和 `question_presentation_overrides` 通过 JSON 字段存储了每个情绪（例如，"快乐"、"悲伤"）的细粒度表情符号、颜色和动画映射。`config-system-api-updated.md` 中提到的 `config.emoji` tRPC 路由旨在与这些细粒度映射进行交互。旧的 `emoji_items` 表已废弃。
    *   **UI 滞后潜力：** 之前的分析指出 `EmojiSetManager.tsx` 在"连接自定义表情符号映射的 UI 与 `config.emoji` tRPC 端点"方面存在差距。如果 `EmojiSetManager.tsx` 仍然主要关注 `emoji_sets`（作为集合仍然存在于架构中），但未能充分暴露 `user_presentation_configs` 和 `question_presentation_overrides` 中定义的细粒度映射能力（情绪 -> 特定表情符号/颜色/动画），那么它就存在滞后。UI 应该允许用户为每个情绪配置 `primary` 和 `alternatives`，以及颜色和动画，这得到了新架构的直接支持。
    *   **量表方面：** 尽管不是直接的"量表"，但 `emoji_mapping` 对于情绪"量表"（或定性情绪状态）如何呈现给用户至关重要。如果 UI 不允许这种详细配置，用户个性化其情绪表达/感知（一种定性量表）的能力将受到限制。

2.  **`QuizSettings.tsx` 和个性化配置：**
    *   **数据库/数据：** `user_presentation_configs.presentation_config` 是 6 层 `QuizPresentationConfig` 的核心 JSON 字段。`quiz_pack_overrides` 允许进行测验包特定的覆盖。`quiz_questions` 和 `quiz_question_options` 定义了问题类型，包括量表评分及其评分方式。
    *   **UI 滞后潜力：** 之前的分析指出在"粒度问题自定义的持久化"方面存在需求。尽管 `updateUserPreferences` 接受 JSON 字符串，但 `QuizSettings.tsx` 的 UI 需要：
        *   **充分暴露并允许编辑 6 层 `presentation_config`：** 特别是 `layer0_dataset_presentation.question_management.question_customization`，用于启用/禁用/重新排序测验包内的问题，这可以被视为测验本身的"量表"配置形式。
        *   **处理 `pack_presentation_overrides`：** UI 应该允许用户设置特定测验包的覆盖，如 `quiz_pack_overrides_test.sql` 中所示（例如，`show_progress_bar`、`question_timeout`）。如果这些覆盖无法从 UI 进行管理，则存在滞后。
        *   **反映 `quiz_questions` 和 `quiz_question_options` 结构：** 如果 `QuizSettings.tsx` UI 仍然依赖于过时的"情绪"概念，或者未能正确利用 `quiz_questions` 和 `quiz_question_options` 中 `question_config` 和 `scoring_config` JSON 字段进行高级量表配置（例如，自定义滑块量表的 `min_value`、`max_value`、`step_value`），则存在差距。UI 应该动态适应 `question_type`（例如，`scale_rating`）并公开相关的配置选项。

3.  **`NewHome.tsx` 和测验包选择/推荐：**
    *   **数据库/数据：** `quiz_packs` 现在包含 `quiz_logic_config` 和 `default_presentation_hints`。用户偏好存储在 `user_presentation_configs` 和 `user_configs` 中。
    *   **UI 滞后潜力：** 之前的分析指出在"充分利用 `UserQuizPreferencesService` 进行个性化测验包选择"方面存在潜在差距。如果 `NewHome.tsx` 仍然基于静态类别或简单的列表呈现测验包，而没有根据 `user_presentation_configs`（例如，`layer0_dataset_presentation.preferred_pack_categories`、`auto_select_recommended`）或 `pack_presentation_overrides` 进行动态过滤/排序，那么它就落后于新架构的个性化能力。UI 应该展示个性化推荐，这可能需要理解 `quiz_packs` 中的 `quiz_logic_config`。

4.  **任何显示量表的页面（例如，测验会话中）：**
    *   **数据库/数据：** 量表主要由 `quiz_questions`（`question_type='scale_rating'`）和 `quiz_question_options`（`scoring_value`、`min_value`、`max_value`、`step_value`）驱动。这些量表的展现随后受 `user_presentation_configs` 和 `quiz_session_presentation_configs`（记录合并后的展现配置快照）的影响。
    *   **UI 滞后潜力：** 如果渲染量表类型问题（例如，在 `QuizSession.tsx` 或 `WheelTest.tsx` 中，如果它是一个量表）的 UI 是硬编码的或依赖于旧的"情绪"概念，这将是一个重大滞后。UI 应该根据 `quiz_questions` 和 `quiz_question_options` 中的 `question_config` 以及 `option_text`/`option_value` 动态渲染量表，并应用任何 `presentation_config` 覆盖（例如，量表点上的自定义表情符号）。`WheelTest.tsx` 明确使用了 `useMockEmotionData()`，这表明它尚未与生产数据源对齐，更重要的是，尚未与 `user_presentation_configs` 中的个性化表情符号映射对齐。

### 总体总结

新数据库架构，特别是其对 `user_presentation_configs`、`quiz_packs`、`quiz_questions` 和 `quiz_question_options` 中 JSON 字段的重度依赖，为定义和个性化量表、配置和表情符号映射提供了高度灵活和细粒度的方式。

UI（`src/pages`）中主要的"滞后"可能源于：

*   **细粒度个性化 UI 不完整：** `EmojiSetManager.tsx` 和 `QuizSettings.tsx` 可能未能完全暴露数据库 JSON 结构支持的表情符号映射（主用/备用、颜色、每个情绪的动画）以及测验包/问题覆盖的详细配置选项。
*   **依赖废弃概念：** 存在一些 UI 元素或逻辑仍然隐式或显式地与"废弃"的 `emotions` 或 `emoji_items` 表/概念交互的风险，而不是完全采用新的 `quiz_questions`、`quiz_question_options` 和 `user_presentation_configs` 表。
*   **动态配置的静态 UI：** 像 `NewHome.tsx` 这样的页面可能未能根据用户特定的 `user_presentation_configs` 和 `quiz_pack_overrides` 动态调整内容展示和过滤。
*   **量表渲染硬编码：** 任何渲染量表类型问题的 UI 组件（例如，在 `QuizSession.tsx` 或 `WheelTest.tsx` 中）可能尚未充分利用数据库中的动态 `question_config` 和 `option` 属性，也可能未应用 `presentation_config` 覆盖进行视觉自定义（如量表点上的自定义表情符号）。

为了解决这些问题，UI 开发需要确保：

*   **UI 控件与 JSON 配置的完全映射：** 设计 UI 元素，允许用户直观地配置 `user_presentation_configs`、`quiz_packs` 和 `quiz_questions` 中嵌套的 JSON 结构。
*   **UI 逻辑的迁移：** 更新 UI 逻辑，直接从新的 `quiz_packs`、`quiz_questions`、`quiz_question_options` 和 `user_presentation_configs` 表检索和解释量表和配置数据，而不是依赖任何废弃的数据库结构或假设。
*   **量表的动态渲染：** 为量表类型问题实现灵活的渲染，可以适应 `question_config` 并应用来自 `presentation_config` 的展现覆盖，以实现真正的个性化体验。 