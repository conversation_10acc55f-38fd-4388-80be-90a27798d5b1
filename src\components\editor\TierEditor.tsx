/**
 * 层级编辑器组件
 * 用于管理情绪数据的层级
 *
 * 该组件允许用户创建、编辑和删除情绪层级，并管理每个层级中的情绪。
 * 支持多层级结构，每个层级可以有父层级关系。
 * 提供响应式设计，适配桌面端和移动端。
 */

import { useLanguage } from '@/contexts/LanguageContext';
import { Services } from '@/services';
import type { EmojiItem, EmojiSet, Emotion, EmotionDataSet, EmotionDataSetTier } from '@/types';
import { ChevronRight, Edit, Plus, Settings, Smile, Trash2 } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { EmojiMappingEditor } from './EmojiMappingEditor';
import { EmotionEditor } from './EmotionEditor';

interface TierEditorProps {
  emotionData: EmotionDataSet;
  onChange: (tiers: EmotionDataSetTier[]) => void;
  isMobileView?: boolean;
  onEmojiMappingChange?: (emojiSetId: string, emotionId: string, emojiItem: EmojiItem) => void;
}

/**
 * 层级编辑器组件
 */
export const TierEditor: React.FC<TierEditorProps> = ({
  emotionData,
  onChange,
  isMobileView,
  onEmojiMappingChange,
}) => {
  const { t } = useLanguage();
  // 使用 state 来跟踪是否为移动端，优先使用传入的 isMobileView 属性
  const [isMobile, setIsMobile] = useState(
    isMobileView !== undefined
      ? isMobileView
      : typeof window !== 'undefined' && window.innerWidth < 768
  );

  // Emoji 相关状态
  const [availableEmojiSets, setAvailableEmojiSets] = useState<EmojiSet[]>([]);
  const [selectedEmojiSetId, setSelectedEmojiSetId] = useState<string>(
    emotionData.default_emoji_set_id || 'default-unicode'
  );
  const [isLoadingEmojiSets, setIsLoadingEmojiSets] = useState(false);
  const [activeTab, setActiveTab] = useState<'tiers' | 'emoji-mapping'>('tiers');

  // 当有层级时，总是默认选择第一个层级
  const [selectedTierId, setSelectedTierId] = useState<string | null>(
    emotionData.tiers && emotionData.tiers.length > 0 ? emotionData.tiers[0].id : null
  );

  // 控制当前视图状态
  const [viewState, setViewState] = useState<'list' | 'detail'>('list');

  // 当 isMobileView 属性变化时更新 isMobile 状态
  useEffect(() => {
    if (isMobileView !== undefined) {
      setIsMobile(isMobileView);
    }
  }, [isMobileView]);

  // 监听窗口大小变化
  useEffect(() => {
    // 如果提供了 isMobileView 属性，则不需要监听窗口大小变化
    if (isMobileView !== undefined) return;

    const handleResize = () => {
      const newIsMobile = window.innerWidth < 768;
      setIsMobile(newIsMobile);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobileView]);

  // 监听层级列表变化，确保当有层级时总是有一个被选中
  useEffect(() => {
    const tiers = emotionData.tiers || [];
    // 如果有层级但没有选择任何层级，则自动选择第一个
    if (tiers.length > 0 && !selectedTierId) {
      setSelectedTierId(tiers[0].id);
    }
    // 如果没有层级，则清除选择
    else if (tiers.length === 0 && selectedTierId) {
      setSelectedTierId(null);
      setViewState('list');
    }
  }, [emotionData.tiers, selectedTierId]);

  // 加载可用的 emoji 集合
  useEffect(() => {
    const loadEmojiSets = async () => {
      setIsLoadingEmojiSets(true);
      try {
        const emojiSetService = await Services.emojiSet();
        const result = await emojiSetService.getActiveEmojiSets();
        if (result.success) {
          setAvailableEmojiSets(result.data || []);
        } else {
          console.error('Failed to load emoji sets:', result.error);
          toast.error(t('tier_editor.emoji_sets_load_error', { fallback: '加载表情集失败' }));
        }
      } catch (error) {
        console.error('Error loading emoji sets:', error);
        toast.error(t('tier_editor.emoji_sets_load_error', { fallback: '加载表情集失败' }));
      } finally {
        setIsLoadingEmojiSets(false);
      }
    };

    loadEmojiSets();
  }, [t]);

  const [isAddingTier, setIsAddingTier] = useState(false);
  const [isEditingTier, setIsEditingTier] = useState(false);
  const [isManagingTier, setIsManagingTier] = useState(false);
  const [managingTierId, setManagingTierId] = useState<string | null>(null);
  const [newTierName, setNewTierName] = useState('');
  const [newTierLevel, setNewTierLevel] = useState('1');
  const [newTierParentId, setNewTierParentId] = useState<string | undefined>(undefined);

  // 获取选中的层级
  const selectedTier =
    selectedTierId && emotionData.tiers
      ? emotionData.tiers.find((tier) => tier.id === selectedTierId)
      : null;

  // 获取正在管理的层级
  const managingTier =
    managingTierId && emotionData.tiers
      ? emotionData.tiers.find((tier) => tier.id === managingTierId)
      : null;

  // 获取可用的父层级
  const availableParentTiers = emotionData.tiers
    ? emotionData.tiers.filter((tier) => {
        // 只有级别低于当前层级的才能作为父层级
        const level = Number.parseInt(newTierLevel);
        return tier.level < level;
      })
    : [];

  // 处理添加层级
  const handleAddTier = () => {
    if (!newTierName.trim()) return;

    const level = Number.parseInt(newTierLevel);
    const newTier: EmotionDataSetTier = {
      id: `tier_${Date.now()}`,
      emotion_data_set_id: emotionData.id,
      name: newTierName,
      level,
      parent_tier_id: newTierParentId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      emotions: [],
    };

    const currentTiers = emotionData.tiers || [];
    const updatedTiers = [...currentTiers, newTier];
    onChange(updatedTiers);
    setSelectedTierId(newTier.id);

    // 重置表单
    resetTierForm();
  };

  // 处理更新层级
  const handleUpdateTier = () => {
    if (!selectedTierId || !newTierName.trim() || !emotionData.tiers) return;

    const level = Number.parseInt(newTierLevel);
    const tierIndex = emotionData.tiers.findIndex((tier) => tier.id === selectedTierId);

    if (tierIndex === -1) return;

    const updatedTier = {
      ...emotionData.tiers[tierIndex],
      name: newTierName,
      level,
      parent_tier_id: newTierParentId,
      updated_at: new Date().toISOString(),
    };

    const updatedTiers = [...emotionData.tiers];
    updatedTiers[tierIndex] = updatedTier;

    onChange(updatedTiers);

    // 重置表单
    setIsEditingTier(false);
  };

  // 处理删除层级
  const handleDeleteTier = (tierId: string) => {
    if (!tierId || !emotionData.tiers) return;

    // 检查是否有子层级
    const hasChildren = emotionData.tiers.some((tier) => tier.parent_tier_id === tierId);
    if (hasChildren) {
      // 如果有子层级，显示错误提示
      alert(t('tier_editor.cannot_delete_parent', { fallback: '无法删除有子层级的层级' }));
      return;
    }

    const updatedTiers = emotionData.tiers.filter((tier) => tier.id !== tierId);
    onChange(updatedTiers);

    // 如果还有其他层级，选择第一个
    if (updatedTiers.length > 0) {
      setSelectedTierId(updatedTiers[0].id);
    } else {
      setSelectedTierId(null);
    }

    // 如果是在移动端，返回列表视图
    if (isMobile) {
      setViewState('list');
    }
  };

  // 处理编辑层级
  const handleEditTier = (tier: EmotionDataSetTier, e?: React.MouseEvent) => {
    // 如果有事件对象，阻止事件冒泡，防止触发选择层级的事件
    if (e) {
      e.stopPropagation();
    }

    // 设置编辑表单的初始值
    setNewTierName(tier.name);
    setNewTierLevel(tier.level.toString());
    setNewTierParentId(tier.parent_tier_id);

    // 打开编辑对话框
    setIsEditingTier(true);

    // 如果当前没有选中任何层级，则选中当前正在编辑的层级
    if (!selectedTierId) {
      setSelectedTierId(tier.id);
    }
  };

  // 处理管理层级
  const handleManageTier = (tier: EmotionDataSetTier, e?: React.MouseEvent) => {
    // 如果有事件对象，阻止事件冒泡，防止触发选择层级的事件
    if (e) {
      e.stopPropagation();
    }

    // 设置正在管理的层级ID
    setManagingTierId(tier.id);

    // 打开管理对话框
    setIsManagingTier(true);

    // 选中当前正在管理的层级
    setSelectedTierId(tier.id);
  };

  // 处理情绪变化
  const handleEmotionsChange = (updatedEmotions: Emotion[]) => {
    if (!selectedTier || !emotionData.tiers) return;

    const tierIndex = emotionData.tiers.findIndex((tier) => tier.id === selectedTier.id);
    if (tierIndex === -1) return;

    const updatedTier = {
      ...emotionData.tiers[tierIndex],
      emotions: updatedEmotions,
      updated_at: new Date().toISOString(),
    };

    const updatedTiers = [...emotionData.tiers];
    updatedTiers[tierIndex] = updatedTier;

    onChange(updatedTiers);
  };

  // 重置层级表单
  const resetTierForm = () => {
    setNewTierName('');
    setNewTierLevel('1');
    setNewTierParentId(undefined);
    setIsAddingTier(false);
  };

  // 处理 emoji 集合选择变化
  const handleEmojiSetChange = (emojiSetId: string) => {
    setSelectedEmojiSetId(emojiSetId);
  };

  // 处理 emoji 映射变化
  const handleEmojiMappingChange = (emotionId: string, emojiItem: EmojiItem) => {
    if (onEmojiMappingChange) {
      onEmojiMappingChange(selectedEmojiSetId, emotionId, emojiItem);
    }
  };

  // 获取所有情绪（从所有层级中）
  const getAllEmotions = (): Emotion[] => {
    if (!emotionData.tiers) return [];

    const emotions: Emotion[] = [];
    emotionData.tiers.forEach((tier) => {
      if (tier.emotions) {
        emotions.push(...tier.emotions);
      }
    });
    return emotions;
  };

  return (
    <div className="tier-editor">
      {/* 主要标签页 */}
      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'tiers' | 'emoji-mapping')}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="tiers">
            <Settings className="h-4 w-4 mr-2" />
            {t('tier_editor.tiers', { fallback: '情绪层级' })}
          </TabsTrigger>
          <TabsTrigger value="emoji-mapping">
            <Smile className="h-4 w-4 mr-2" />
            {t('tier_editor.emoji_mapping', { fallback: 'Emoji 映射' })}
          </TabsTrigger>
        </TabsList>

        {/* 层级编辑标签页 */}
        <TabsContent value="tiers" className="space-y-4">
          {/* 移动端层级选择器 */}
          <div className="md:hidden mb-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-medium">
                {t('tier_editor.tiers', { fallback: '情绪层级' })}
              </h3>
              <Button variant="outline" size="sm" onClick={() => setIsAddingTier(true)}>
                <Plus className="h-4 w-4 mr-1" />
                {t('common.add', { fallback: '添加' })}
              </Button>
            </div>

            {!emotionData.tiers || emotionData.tiers.length === 0 ? (
              <div className="text-center py-4 border rounded-md">
                <p className="text-muted-foreground mb-2">
                  {t('tier_editor.no_tiers', { fallback: '没有层级' })}
                </p>
                <Button variant="outline" size="sm" onClick={() => setIsAddingTier(true)}>
                  <Plus className="h-4 w-4 mr-1" />
                  {t('tier_editor.create_first', { fallback: '创建第一个层级' })}
                </Button>
              </div>
            ) : (
              <div className="pb-2">
                <div className="space-y-2">
                  {(emotionData.tiers || [])
                    .sort((a, b) => a.level - b.level)
                    .map((tier) => (
                      <div key={tier.id} className="flex items-center gap-2">
                        <Button
                          variant={selectedTierId === tier.id ? 'default' : 'outline'}
                          className="w-full justify-between"
                          onClick={() => setSelectedTierId(tier.id)}
                        >
                          <div className="flex items-center">
                            <span className="truncate">{tier.name}</span>
                            <span className="ml-2 text-xs bg-primary/20 px-2 py-0.5 rounded-full">
                              {t(`tier_editor.level_${tier.level}_short`, {
                                fallback: `L${tier.level}`,
                              })}
                            </span>
                          </div>
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="flex-shrink-0"
                            onClick={(e) => handleManageTier(tier, e)}
                            title={t('common.manage', { fallback: '管理' })}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="flex-shrink-0"
                            onClick={(e) => handleEditTier(tier, e)}
                            title={t('common.edit', { fallback: '编辑' })}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* 移动端层级详情已隐藏 */}
          </div>

          {/* 桌面端显示层级列表和详情 */}
          {!isMobile && (
            <div className="flex flex-col gap-4">
              {!emotionData.tiers || emotionData.tiers.length === 0 ? (
                // 当没有层级时，显示一个突出的引导卡片
                <div className="w-full">
                  <Card className="border-primary/20 bg-primary/5">
                    <CardContent className="text-center py-10">
                      <h3 className="text-xl font-medium mb-2">
                        {t('tier_editor.no_tiers_title', { fallback: '开始创建情绪层级' })}
                      </h3>
                      <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                        {t('tier_editor.no_tiers_description', {
                          fallback:
                            '情绪层级是构建情绪数据的基础。您需要先创建至少一个层级，然后才能添加情绪。',
                        })}
                      </p>
                      <Button size="lg" onClick={() => setIsAddingTier(true)}>
                        <Plus className="h-5 w-5 mr-2" />
                        {t('tier_editor.create_first', { fallback: '创建第一个层级' })}
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <>
                  {/* 层级列表 - 桌面端 */}
                  <div className="w-full">
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between">
                        <CardTitle>{t('tier_editor.tiers', { fallback: '情绪层级' })}</CardTitle>

                        <Button variant="outline" size="sm" onClick={() => setIsAddingTier(true)}>
                          <Plus className="h-4 w-4 mr-1" />
                          {t('common.add', { fallback: '添加' })}
                        </Button>
                      </CardHeader>

                      <CardContent>
                        <div className="space-y-2">
                          {(emotionData.tiers || [])
                            .sort((a, b) => a.level - b.level)
                            .map((tier) => (
                              <div key={tier.id} className="flex items-center gap-2">
                                <Button
                                  variant={selectedTierId === tier.id ? 'default' : 'outline'}
                                  className="w-full justify-between"
                                  onClick={() => setSelectedTierId(tier.id)}
                                >
                                  <div className="flex items-center">
                                    <span className="truncate">{tier.name}</span>
                                    <span className="ml-2 text-xs bg-primary/20 px-2 py-0.5 rounded-full">
                                      {t(`tier_editor.level_${tier.level}`, {
                                        fallback: `${tier.level}级`,
                                      })}
                                    </span>
                                  </div>
                                  <ChevronRight className="h-4 w-4" />
                                </Button>
                                <div className="flex">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="flex-shrink-0"
                                    onClick={(e) => handleManageTier(tier, e)}
                                    title={t('common.manage', { fallback: '管理' })}
                                  >
                                    <Settings className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="flex-shrink-0"
                                    onClick={(e) => handleEditTier(tier, e)}
                                    title={t('common.edit', { fallback: '编辑' })}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="flex-shrink-0 text-destructive hover:text-destructive"
                                        title={t('common.delete', { fallback: '删除' })}
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>
                                          {t('tier_editor.delete_tier_title', {
                                            fallback: '删除层级',
                                          })}
                                        </AlertDialogTitle>
                                        <AlertDialogDescription>
                                          {t('tier_editor.delete_tier_description', {
                                            fallback:
                                              '确定要删除这个层级吗？此操作将删除该层级中的所有情绪，且无法恢复。',
                                          })}
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>
                                          {t('common.cancel', { fallback: '取消' })}
                                        </AlertDialogCancel>
                                        <AlertDialogAction
                                          onClick={() => handleDeleteTier(tier.id)}
                                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                        >
                                          {t('common.delete', { fallback: '删除' })}
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                </div>
                              </div>
                            ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 选中层级的详情 - 桌面端 */}
                  {selectedTier && (
                    <div className="w-full">
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                          <div>
                            <CardTitle className="flex items-center gap-2">
                              <span>{selectedTier.name}</span>
                              <span className="text-xs bg-primary/20 px-2 py-0.5 rounded-full">
                                {t(`tier_editor.level_${selectedTier.level}`, {
                                  fallback: `${selectedTier.level}级`,
                                })}
                              </span>
                            </CardTitle>
                            <CardDescription>
                              {t('tier_editor.manage_tier_description', {
                                fallback: '管理层级中的情绪',
                              })}{' '}
                              - {selectedTier.emotions?.length || 0}{' '}
                              {t('emotion_editor.emotions', { fallback: '情绪' })}
                            </CardDescription>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => handleEditTier(selectedTier, e)}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              {t('common.edit', { fallback: '编辑' })}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => handleManageTier(selectedTier, e)}
                            >
                              <Settings className="h-4 w-4 mr-1" />
                              {t('common.manage', { fallback: '管理' })}
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {/* 情绪编辑器 */}
                          <EmotionEditor
                            tier={selectedTier}
                            onChange={(updatedEmotions) => {
                              handleEmotionsChange(updatedEmotions);
                            }}
                            isMobile={false}
                          />
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </>
              )}
            </div>
          )}

          {/* 移动端：只显示层级列表，选择后才显示详情 */}
          {isMobile && !selectedTier && (
            <div className="md:hidden">
              {!emotionData.tiers || emotionData.tiers.length === 0 ? (
                // 当没有层级时，显示一个突出的引导卡片
                <Card className="border-primary/20 bg-primary/5">
                  <CardContent className="text-center py-8">
                    <h3 className="text-lg font-medium mb-2">
                      {t('tier_editor.no_tiers_title', { fallback: '开始创建情绪层级' })}
                    </h3>
                    <p className="text-muted-foreground mb-4 text-sm">
                      {t('tier_editor.no_tiers_description', {
                        fallback:
                          '情绪层级是构建情绪数据的基础。您需要先创建至少一个层级，然后才能添加情绪。',
                      })}
                    </p>
                    <Button onClick={() => setIsAddingTier(true)}>
                      <Plus className="h-4 w-4 mr-1" />
                      {t('tier_editor.create_first', { fallback: '创建第一个层级' })}
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>{t('tier_editor.tiers', { fallback: '情绪层级' })}</CardTitle>
                    <Button variant="outline" size="sm" onClick={() => setIsAddingTier(true)}>
                      <Plus className="h-4 w-4 mr-1" />
                      {t('common.add', { fallback: '添加' })}
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {(emotionData.tiers || [])
                        .sort((a, b) => a.level - b.level)
                        .map((tier) => (
                          <div key={tier.id} className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              className="w-full justify-between"
                              onClick={() => setSelectedTierId(tier.id)}
                            >
                              <div className="flex items-center">
                                <span className="truncate">{tier.name}</span>
                                <span className="ml-2 text-xs bg-primary/20 px-2 py-0.5 rounded-full">
                                  {t(`tier_editor.level_${tier.level}_short`, {
                                    fallback: `L${tier.level}`,
                                  })}
                                </span>
                              </div>
                              <ChevronRight className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="flex-shrink-0"
                              onClick={(e) => handleManageTier(tier, e)}
                              title={t('common.manage', { fallback: '管理' })}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="flex-shrink-0"
                              onClick={(e) => handleEditTier(tier, e)}
                              title={t('common.edit', { fallback: '编辑' })}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="flex-shrink-0 text-destructive hover:text-destructive"
                                  title={t('common.delete', { fallback: '删除' })}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    {t('tier_editor.delete_tier_title', { fallback: '删除层级' })}
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    {t('tier_editor.delete_tier_description', {
                                      fallback:
                                        '确定要删除这个层级吗？此操作将删除该层级中的所有情绪，且无法恢复。',
                                    })}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>
                                    {t('common.cancel', { fallback: '取消' })}
                                  </AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteTier(tier.id)}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  >
                                    {t('common.delete', { fallback: '删除' })}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* 移动端：选中层级的详情 */}
          {isMobile && selectedTier && (
            <div className="md:hidden">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedTierId(null)}
                        className="p-1 h-auto"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <span>{selectedTier.name}</span>
                      <span className="text-xs bg-primary/20 px-2 py-0.5 rounded-full">
                        {t(`tier_editor.level_${selectedTier.level}`, {
                          fallback: `${selectedTier.level}级`,
                        })}
                      </span>
                    </CardTitle>
                    <CardDescription>
                      {selectedTier.emotions?.length || 0}{' '}
                      {t('emotion_editor.emotions', { fallback: '情绪' })}
                    </CardDescription>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => handleEditTier(selectedTier, e)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => handleManageTier(selectedTier, e)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* 情绪编辑器 */}
                  <EmotionEditor
                    tier={selectedTier}
                    onChange={(updatedEmotions) => {
                      handleEmotionsChange(updatedEmotions);
                    }}
                    isMobile={true}
                  />
                </CardContent>
              </Card>
            </div>
          )}

          {/* 编辑层级对话框 */}
          <Dialog open={isEditingTier} onOpenChange={setIsEditingTier}>
            <DialogContent
              className={`${isMobile ? 'w-full h-[100vh] max-w-none p-0 rounded-none border-0' : ''}`}
            >
              <div className={`${isMobile ? 'h-full flex flex-col' : ''}`}>
                <DialogHeader className={`${isMobile ? 'px-4 py-3 border-b' : ''}`}>
                  <DialogTitle>{t('tier_editor.edit_tier', { fallback: '编辑层级' })}</DialogTitle>
                  <DialogDescription>
                    {t('tier_editor.edit_tier_description', { fallback: '修改层级信息' })}
                  </DialogDescription>
                </DialogHeader>

                <div className={`space-y-4 ${isMobile ? 'flex-1 overflow-y-auto px-4 py-2' : ''}`}>
                  <div className="space-y-2">
                    <Label htmlFor="editTierName">{t('common.name', { fallback: '名称' })}</Label>
                    <Input
                      id="editTierName"
                      value={newTierName}
                      onChange={(e) => setNewTierName(e.target.value)}
                      placeholder={t('tier_editor.name_placeholder', { fallback: '输入层级名称' })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="editTierLevel">
                      {t('tier_editor.level', { fallback: '级别' })}
                    </Label>
                    <Select value={newTierLevel} onValueChange={setNewTierLevel}>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t('tier_editor.select_level', { fallback: '选择级别' })}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">
                          {t('tier_editor.level_1', { fallback: '一级 (主要情绪)' })}
                        </SelectItem>
                        <SelectItem value="2">
                          {t('tier_editor.level_2', { fallback: '二级 (次要情绪)' })}
                        </SelectItem>
                        <SelectItem value="3">
                          {t('tier_editor.level_3', { fallback: '三级 (细分情绪)' })}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {Number.parseInt(newTierLevel) > 1 && availableParentTiers.length > 0 && (
                    <div className="space-y-2">
                      <Label htmlFor="editTierParent">
                        {t('tier_editor.parent', { fallback: '父层级' })}
                      </Label>
                      <Select value={newTierParentId} onValueChange={setNewTierParentId}>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t('tier_editor.select_parent', { fallback: '选择父层级' })}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {availableParentTiers.map((tier) => (
                            <SelectItem key={tier.id} value={tier.id}>
                              {tier.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <DialogFooter className={`${isMobile ? 'px-4 py-3 border-t mt-auto' : 'mt-4'}`}>
                  <Button variant="outline" onClick={() => setIsEditingTier(false)}>
                    {t('common.cancel', { fallback: '取消' })}
                  </Button>
                  <Button onClick={handleUpdateTier}>
                    {t('common.save', { fallback: '保存' })}
                  </Button>
                </DialogFooter>
              </div>
            </DialogContent>
          </Dialog>

          {/* 添加层级对话框 */}
          <Dialog open={isAddingTier} onOpenChange={setIsAddingTier}>
            <DialogContent
              className={`${isMobile ? 'w-full h-[100vh] max-w-none p-0 rounded-none border-0' : ''}`}
            >
              <div className={`${isMobile ? 'h-full flex flex-col' : ''}`}>
                <DialogHeader className={`${isMobile ? 'px-4 py-3 border-b' : ''}`}>
                  <DialogTitle>{t('tier_editor.add_tier', { fallback: '添加层级' })}</DialogTitle>
                  <DialogDescription>
                    {t('tier_editor.add_tier_description', { fallback: '创建新的情绪层级' })}
                  </DialogDescription>
                </DialogHeader>

                <div className={`space-y-4 ${isMobile ? 'flex-1 overflow-y-auto px-4 py-2' : ''}`}>
                  <div className="space-y-2">
                    <Label htmlFor="newTierName">{t('common.name', { fallback: '名称' })}</Label>
                    <Input
                      id="newTierName"
                      value={newTierName}
                      onChange={(e) => setNewTierName(e.target.value)}
                      placeholder={t('tier_editor.name_placeholder', { fallback: '输入层级名称' })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="newTierLevel">
                      {t('tier_editor.level', { fallback: '级别' })}
                    </Label>
                    <Select value={newTierLevel} onValueChange={setNewTierLevel}>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t('tier_editor.select_level', { fallback: '选择级别' })}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">
                          {t('tier_editor.level_1', { fallback: '一级 (主要情绪)' })}
                        </SelectItem>
                        <SelectItem value="2">
                          {t('tier_editor.level_2', { fallback: '二级 (次要情绪)' })}
                        </SelectItem>
                        <SelectItem value="3">
                          {t('tier_editor.level_3', { fallback: '三级 (细分情绪)' })}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {Number.parseInt(newTierLevel) > 1 && availableParentTiers.length > 0 && (
                    <div className="space-y-2">
                      <Label htmlFor="newTierParent">
                        {t('tier_editor.parent', { fallback: '父层级' })}
                      </Label>
                      <Select value={newTierParentId} onValueChange={setNewTierParentId}>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t('tier_editor.select_parent', { fallback: '选择父层级' })}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {availableParentTiers.map((tier) => (
                            <SelectItem key={tier.id} value={tier.id}>
                              {tier.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <DialogFooter className={`${isMobile ? 'px-4 py-3 border-t mt-auto' : 'mt-4'}`}>
                  <Button variant="outline" onClick={resetTierForm}>
                    {t('common.cancel', { fallback: '取消' })}
                  </Button>
                  <Button onClick={handleAddTier}>{t('common.add', { fallback: '添加' })}</Button>
                </DialogFooter>
              </div>
            </DialogContent>
          </Dialog>

          {/* 管理层级对话框 */}
          <Dialog open={isManagingTier} onOpenChange={setIsManagingTier}>
            <DialogContent
              className={`${isMobile ? 'w-full h-[100vh] max-w-none p-0 rounded-none border-0' : 'max-w-4xl max-h-[90vh]'} overflow-y-auto`}
            >
              <div className={`${isMobile ? 'h-full flex flex-col' : ''}`}>
                <DialogHeader className={`${isMobile ? 'px-4 py-3 border-b' : ''}`}>
                  <DialogTitle>
                    {managingTier && (
                      <div className="flex items-center gap-2">
                        <span>
                          {t('tier_editor.manage_tier', { fallback: '管理层级' })}:{' '}
                          {managingTier.name}
                        </span>
                        <span className="text-xs bg-primary/20 px-2 py-0.5 rounded-full">
                          {t(`tier_editor.level_${managingTier.level}`, {
                            fallback: `${managingTier.level}级`,
                          })}
                        </span>
                      </div>
                    )}
                  </DialogTitle>
                  <DialogDescription>
                    {t('tier_editor.manage_tier_description', { fallback: '管理层级中的情绪' })}
                  </DialogDescription>
                </DialogHeader>

                {managingTier && (
                  <div
                    className={`space-y-4 ${isMobile ? 'flex-1 overflow-y-auto px-4 py-2' : ''}`}
                  >
                    {/* 层级信息 */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">
                          {t('tier_editor.tier_info', { fallback: '层级信息' })}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm text-muted-foreground">
                              {t('common.name', { fallback: '名称' })}
                            </Label>
                            <div className="font-medium">{managingTier.name}</div>
                          </div>
                          <div>
                            <Label className="text-sm text-muted-foreground">
                              {t('tier_editor.level', { fallback: '级别' })}
                            </Label>
                            <div className="font-medium">
                              {t(`tier_editor.level_${managingTier.level}_description`, {
                                fallback: `${managingTier.level}级情绪`,
                              })}
                            </div>
                          </div>
                          {managingTier.parent_tier_id && (
                            <div>
                              <Label className="text-sm text-muted-foreground">
                                {t('tier_editor.parent', { fallback: '父层级' })}
                              </Label>
                              <div className="font-medium">
                                {
                                  emotionData.tiers.find((t) => t.id === managingTier.parent_tier_id)
                                    ?.name
                                }
                              </div>
                            </div>
                          )}
                          <div>
                            <Label className="text-sm text-muted-foreground">
                              {t('emotion_editor.emotions', { fallback: '情绪' })}
                            </Label>
                            <div className="font-medium">{managingTier.emotions.length}</div>
                          </div>
                        </div>

                        <div className="flex justify-end mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => handleEditTier(managingTier, e)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            {t('common.edit', { fallback: '编辑' })}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    {/* 情绪编辑器 */}
                    <div className="mt-4">
                      <EmotionEditor
                        tier={managingTier}
                        onChange={(updatedEmotions) => {
                          handleEmotionsChange(updatedEmotions);
                        }}
                        isMobile={isMobile}
                      />
                    </div>
                  </div>
                )}

                <DialogFooter className={`mt-4 ${isMobile ? 'px-4 py-3 border-t' : ''}`}>
                  <Button onClick={() => setIsManagingTier(false)}>
                    {t('common.close', { fallback: '关闭' })}
                  </Button>
                </DialogFooter>
              </div>
            </DialogContent>
          </Dialog>
        </TabsContent>

        {/* Emoji 映射标签页 */}
        <TabsContent value="emoji-mapping" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smile className="h-5 w-5" />
                {t('tier_editor.emoji_mapping', { fallback: 'Emoji 映射' })}
              </CardTitle>
              <CardDescription>
                {t('tier_editor.emoji_mapping_description', {
                  fallback: '为每个情绪在不同表情集中设置对应的 emoji',
                })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Emoji 集合选择器 */}
              <div className="space-y-2">
                <Label>{t('tier_editor.select_emoji_set', { fallback: '选择表情集' })}</Label>
                <Select value={selectedEmojiSetId} onValueChange={handleEmojiSetChange}>
                  <SelectTrigger>
                    <SelectValue
                      placeholder={t('tier_editor.select_emoji_set_placeholder', {
                        fallback: '请选择表情集',
                      })}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingEmojiSets ? (
                      <SelectItem value="loading" disabled>
                        {t('common.loading', { fallback: '加载中...' })}
                      </SelectItem>
                    ) : (
                      availableEmojiSets.map((emojiSet) => (
                        <SelectItem key={emojiSet.id} value={emojiSet.id}>
                          <div className="flex items-center gap-2">
                            <span>{emojiSet.name}</span>
                            {emojiSet.is_default && (
                              <span className="text-xs bg-primary/20 px-1 py-0.5 rounded">
                                {t('common.default', { fallback: '默认' })}
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Emoji 映射编辑器 */}
              {selectedEmojiSetId && (
                <EmojiMappingEditor
                  emotions={getAllEmotions()}
                  emojiSetId={selectedEmojiSetId}
                  onEmojiChange={handleEmojiMappingChange}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
