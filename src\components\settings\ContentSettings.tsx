import { useState, useEffect } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Database, Smile, Heart, Brain, Crown, Loader2
} from "lucide-react";
import { toast } from "sonner";
import { Services } from "@/services";
import { EmotionDataSet, EmojiSet } from "@/types";

interface ContentSettingsProps {
  userLevel: 'beginner' | 'regular' | 'advanced' | 'vip';
}

const ContentSettings: React.FC<ContentSettingsProps> = ({ userLevel }) => {
  const { t } = useLanguage();
  
  // 状态管理
  const [emotionDataSets, setEmotionDataSets] = useState<EmotionDataSet[]>([]);
  const [activeEmotionDataSet, setActiveEmotionDataSet] = useState<EmotionDataSet | null>(null);
  const [emojiSets, setEmojiSets] = useState<EmojiSet[]>([]);
  const [activeEmojiSet, setActiveEmojiSet] = useState<EmojiSet | null>(null);
  
  // 加载状态
  const [isLoadingEmotionData, setIsLoadingEmotionData] = useState(true);
  const [isLoadingEmojiSets, setIsLoadingEmojiSets] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载数据
  useEffect(() => {
    const loadData = async () => {
      try {
        setError(null);

        // 加载情绪数据集
        const emotionDataSetService = await Services.emotionDataSet();
        const emotionDataSetsResult = await emotionDataSetService.getAll();

        if (emotionDataSetsResult.success && emotionDataSetsResult.data) {
          const validDataSets = emotionDataSetsResult.data.filter(dataSet => 
            dataSet && dataSet.id && dataSet.name
          );
          setEmotionDataSets(validDataSets);

          // 获取活动的数据集
          const activeDataSetsResult = await emotionDataSetService.getActiveDataSets();
          if (activeDataSetsResult.success && activeDataSetsResult.data?.length > 0) {
            setActiveEmotionDataSet(activeDataSetsResult.data[0]);
          }
        }

        // 加载表情集
        const emojiSetService = await Services.emojiSet();
        const emojiSetsResult = await emojiSetService.getAll();

        if (emojiSetsResult.success && emojiSetsResult.data) {
          const validEmojiSets = emojiSetsResult.data.filter(emojiSet => 
            emojiSet && emojiSet.id && emojiSet.name
          );
          setEmojiSets(validEmojiSets);

          // 获取活动的表情集
          const activeEmojiSetsResult = await emojiSetService.getActiveEmojiSets();
          if (activeEmojiSetsResult.success && activeEmojiSetsResult.data?.length > 0) {
            setActiveEmojiSet(activeEmojiSetsResult.data[0]);
          }
        }

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load content data';
        setError(errorMessage);
        console.error('Error loading content data:', err);
      } finally {
        setIsLoadingEmotionData(false);
        setIsLoadingEmojiSets(false);
      }
    };

    loadData();
  }, []);

  // 处理情绪数据集更改
  const handleEmotionDataChange = async (emotionDataId: string) => {
    if (!emotionDataId) return;

    try {
      const emotionDataSetService = await Services.emotionDataSet();
      const result = await emotionDataSetService.setAsDefault(emotionDataId);

      if (result.success) {
        const selectedDataSet = emotionDataSets.find(ds => ds.id === emotionDataId);
        if (selectedDataSet) {
          setActiveEmotionDataSet(selectedDataSet);
          toast.success(t('settings.emotion_data_changed', '情绪数据已更改'));
        }
      } else {
        throw new Error(result.error || 'Failed to change emotion data');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to change emotion data';
      console.error('Error changing emotion data:', error);
      toast.error(t('settings.emotion_data_change_failed', '更改情绪数据失败') + ': ' + errorMessage);
    }
  };

  // 处理表情集更改
  const handleEmojiSetChange = async (setId: string) => {
    if (!setId) return;

    try {
      const emojiSetService = await Services.emojiSet();
      const result = await emojiSetService.setActiveEmojiSet(setId);

      if (result.success) {
        const selectedSet = emojiSets.find(set => set.id === setId);
        if (selectedSet) {
          setActiveEmojiSet(selectedSet);
          toast.success(t('settings.emoji_set_changed', '表情集已更改'));
        }
      } else {
        throw new Error(result.error || 'Failed to change emoji set');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to change emoji set';
      console.error('Error changing emoji set:', error);
      toast.error(t('settings.emoji_set_change_failed', '更改表情集失败') + ': ' + errorMessage);
    }
  };

  // 渲染情绪数据集选择
  const renderEmotionDataSelection = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Brain className="h-5 w-5" />
          <span>{t('settings.emotion_data', '情绪数据集')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoadingEmotionData ? (
          <div className="flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>{t('settings.loading', '加载中...')}</span>
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {t('settings.select_emotion_data', '选择情绪数据集')}
              </label>
              <Select
                value={activeEmotionDataSet?.id || ''}
                onValueChange={handleEmotionDataChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('settings.select_emotion_data_placeholder', '请选择情绪数据集')} />
                </SelectTrigger>
                <SelectContent>
                  {emotionDataSets.map((dataSet) => (
                    <SelectItem key={dataSet.id} value={dataSet.id}>
                      <div className="flex items-center space-x-2">
                        <span>{dataSet.name}</span>
                        {dataSet.is_premium && (
                          <Badge variant="secondary" className="text-xs">
                            <Crown className="h-3 w-3 mr-1" />
                            {t('settings.premium', '高级')}
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 当前数据集信息 */}
            {activeEmotionDataSet && (
              <div className="p-3 bg-muted rounded-lg space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{activeEmotionDataSet.name}</span>
                  {activeEmotionDataSet.is_premium && (
                    <Badge variant="secondary">
                      <Crown className="h-3 w-3 mr-1" />
                      {t('settings.premium', '高级')}
                    </Badge>
                  )}
                </div>
                {activeEmotionDataSet.description && (
                  <p className="text-sm text-muted-foreground">
                    {activeEmotionDataSet.description}
                  </p>
                )}
                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                  <span>{t('settings.emotions_count', '情绪数量')}: {activeEmotionDataSet.emotions_count || 0}</span>
                  <span>{t('settings.tiers_count', '层级数量')}: {activeEmotionDataSet.tiers?.length || 0}</span>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );

  // 渲染表情集选择
  const renderEmojiSetSelection = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Smile className="h-5 w-5" />
          <span>{t('settings.emoji_sets', '表情集')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoadingEmojiSets ? (
          <div className="flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>{t('settings.loading', '加载中...')}</span>
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {t('settings.select_emoji_set', '选择表情集')}
              </label>
              <Select
                value={activeEmojiSet?.id || ''}
                onValueChange={handleEmojiSetChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('settings.select_emoji_set_placeholder', '请选择表情集')} />
                </SelectTrigger>
                <SelectContent>
                  {emojiSets.map((emojiSet) => (
                    <SelectItem key={emojiSet.id} value={emojiSet.id}>
                      <div className="flex items-center space-x-2">
                        <span>{emojiSet.name}</span>
                        {emojiSet.is_premium && (
                          <Badge variant="secondary" className="text-xs">
                            <Crown className="h-3 w-3 mr-1" />
                            {t('settings.premium', '高级')}
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 当前表情集信息 */}
            {activeEmojiSet && (
              <div className="p-3 bg-muted rounded-lg space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{activeEmojiSet.name}</span>
                  {activeEmojiSet.is_premium && (
                    <Badge variant="secondary">
                      <Crown className="h-3 w-3 mr-1" />
                      {t('settings.premium', '高级')}
                    </Badge>
                  )}
                </div>
                {activeEmojiSet.description && (
                  <p className="text-sm text-muted-foreground">
                    {activeEmojiSet.description}
                  </p>
                )}
                <div className="text-xs text-muted-foreground">
                  {t('settings.emoji_count', '表情数量')}: {activeEmojiSet.emoji_count || 0}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Database className="h-6 w-6" />
          <h2 className="text-2xl font-bold">
            {t('settings.content', '内容设置')}
          </h2>
        </div>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-center space-y-2">
              <p className="text-destructive">{t('settings.error_loading', '加载失败')}</p>
              <p className="text-sm text-muted-foreground">{error}</p>
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
              >
                {t('settings.retry', '重试')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-2">
        <Database className="h-6 w-6" />
        <h2 className="text-2xl font-bold">
          {t('settings.content', '内容设置')}
        </h2>
        {userLevel === 'vip' && (
          <Badge variant="destructive" className="flex items-center space-x-1">
            <Crown className="h-3 w-3" />
            <span>VIP</span>
          </Badge>
        )}
      </div>

      {/* 情绪数据集选择 */}
      {renderEmotionDataSelection()}

      {/* 表情集选择 */}
      {renderEmojiSetSelection()}

      {/* VIP用户额外功能 */}
      {userLevel === 'vip' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Heart className="h-5 w-5" />
              <span>{t('settings.vip_content', 'VIP专属内容')}</span>
              <Badge variant="destructive">VIP</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {t('settings.vip_content.desc', 'VIP用户可以访问更多高级情绪数据集和表情集')}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ContentSettings;
