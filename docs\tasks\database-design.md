# 数据库设计文档

本文档详细说明了 Mindful Mood Mobile 应用的数据库设计架构，包括表结构、关系设计、索引策略和数据完整性约束。

## 📋 目录

- [架构概览](#架构概览)
- [核心表设计](#核心表设计)
- [翻译系统设计](#翻译系统设计)
- [关系设计](#关系设计)
- [索引策略](#索引策略)
- [数据完整性](#数据完整性)
- [性能优化](#性能优化)
- [迁移策略](#迁移策略)

## 🏗️ 架构概览

### 设计原则

1. **规范化设计** - 遵循第三范式，减少数据冗余
2. **多语言支持** - 采用独立翻译表设计
3. **软删除策略** - 重要数据使用软删除而非物理删除
4. **审计追踪** - 记录创建时间、更新时间和操作用户
5. **性能优化** - 合理的索引设计和查询优化

### 数据库技术栈

- **主数据库**: SQLite (离线存储)
- **云数据库**: Turso (在线同步)
- **ORM**: 自定义 Repository 模式
- **迁移**: SQL 脚本管理

### 表分类

```
核心业务表:
├── users              # 用户表
├── emotions           # 情绪表
├── emotion_data_sets  # 情绪数据集
├── emoji_sets         # 表情集
├── skins             # 皮肤主题
├── tags              # 标签
├── mood_entries      # 心情记录
└── user_configs      # 用户配置

翻译表:
├── ui_label_translations        # UI标签翻译
├── emotion_translations         # 情绪翻译
├── emotion_data_set_translations # 情绪数据集翻译
├── emoji_set_translations       # 表情集翻译
├── skin_translations           # 皮肤翻译
└── tag_translations            # 标签翻译

关联表:
├── emotion_data_set_emotions   # 情绪数据集-情绪关联
├── emotion_data_set_tiers      # 情绪数据集层级
├── emoji_items                 # 表情项
├── emotion_selections          # 情绪选择记录
└── mood_entry_tags            # 心情记录-标签关联
```

## 📊 核心表设计

### 用户表 (users)

```sql
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY NOT NULL,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE,
    password_hash TEXT,
    display_name TEXT,
    avatar_url TEXT,
    is_vip BOOLEAN DEFAULT FALSE,
    vip_expires_at TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    last_login_at TEXT
);
```

**设计说明:**
- `id`: UUID 主键，确保全局唯一性
- `username`: 唯一用户名，用于登录
- `email`: 可选邮箱，支持邮箱登录
- `is_vip`: VIP 状态标识
- `vip_expires_at`: VIP 过期时间

### 情绪表 (emotions)

```sql
CREATE TABLE IF NOT EXISTS emotions (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    emoji TEXT,
    color TEXT,
    tier_level INTEGER CHECK (tier_level BETWEEN 1 AND 5),
    parent_id TEXT,
    keywords TEXT, -- JSON array
    image_url TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TEXT,
    FOREIGN KEY (parent_id) REFERENCES emotions(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);
```

**设计说明:**
- 支持层级结构 (`parent_id` 自引用)
- 软删除设计 (`is_deleted`, `deleted_at`)
- 审计字段 (`created_by`, `updated_by`)
- 颜色和表情支持视觉展示

### 心情记录表 (mood_entries)

```sql
CREATE TABLE IF NOT EXISTS mood_entries (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    timestamp TEXT NOT NULL,
    emotion_data_set_id TEXT,
    intensity INTEGER CHECK (intensity BETWEEN 0 AND 100),
    reflection TEXT,
    tags TEXT, -- JSON array

    -- 表情集上下文
    emoji_set_id TEXT,
    emoji_set_version TEXT,

    -- 皮肤配置快照
    skin_id TEXT,
    skin_config_snapshot TEXT, -- JSON

    -- 显示配置快照
    view_type_used TEXT,
    render_engine_used TEXT,
    display_mode_used TEXT,

    -- 用户配置快照
    user_config_snapshot TEXT, -- JSON

    -- 同步字段
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    sync_status TEXT DEFAULT 'sync_required',
    server_id TEXT,
    last_synced_at TEXT,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (emotion_data_set_id) REFERENCES emotion_data_sets(id) ON DELETE SET NULL,
    FOREIGN KEY (emoji_set_id) REFERENCES emoji_sets(id) ON DELETE SET NULL,
    FOREIGN KEY (skin_id) REFERENCES skins(id) ON DELETE SET NULL
);
```

**设计说明:**
- 记录用户的心情状态和相关配置
- 配置快照确保历史记录的完整性
- 同步字段支持离线/在线数据同步

## 🌍 翻译系统设计

### 设计模式

采用 **独立翻译表** 模式，每个需要多语言支持的实体都有对应的翻译表：

```sql
-- 主表存储默认语言内容
CREATE TABLE entities (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,        -- 默认语言名称
    description TEXT,          -- 默认语言描述
    -- 其他字段...
);

-- 翻译表存储多语言内容
CREATE TABLE entity_translations (
    entity_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (entity_id, language_code),
    FOREIGN KEY (entity_id) REFERENCES entities(id) ON DELETE CASCADE
);
```

### 支持的语言

```sql
-- 支持的语言代码（15种语言）
ENUM LanguageCode {
    'en',    -- English
    'zh',    -- Chinese (Simplified)
    'zh-TW', -- Chinese (Traditional)
    'ja',    -- Japanese
    'ko',    -- Korean
    'es',    -- Spanish
    'fr',    -- French
    'de',    -- German
    'it',    -- Italian
    'pt',    -- Portuguese
    'ru',    -- Russian
    'ar',    -- Arabic
    'hi',    -- Hindi
    'th',    -- Thai
    'vi'     -- Vietnamese
}
```

### 翻译表示例

#### UI 标签翻译
```sql
CREATE TABLE IF NOT EXISTS ui_label_translations (
    id TEXT PRIMARY KEY,
    label_key TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_text TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(label_key, language_code),
    FOREIGN KEY (label_key) REFERENCES ui_labels(key) ON DELETE CASCADE
);
```

#### 情绪翻译
```sql
CREATE TABLE IF NOT EXISTS emotion_translations (
    emotion_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (emotion_id, language_code),
    FOREIGN KEY (emotion_id) REFERENCES emotions(id) ON DELETE CASCADE
);
```

## 🔗 关系设计

### 实体关系图 (ERD)

```
users (1) ----< (N) mood_entries
  |                    |
  |                    |
  |                    v
  |              emotion_selections (N)
  |                    |
  |                    v
  |              emotions (1)
  |                    |
  |                    v
  |              emotion_translations (N)
  |
  |
  v
user_configs (N)
  |
  v
emotion_data_sets (1) ----< (N) emotion_data_set_emotions
  |                              |
  |                              v
  |                        emotions (1)
  |
  v
emotion_data_set_translations (N)
```

### 关键关系

#### 1. 用户-心情记录 (1:N)
```sql
-- 一个用户可以有多个心情记录
mood_entries.user_id -> users.id
```

#### 2. 情绪层级关系 (自引用)
```sql
-- 情绪可以有父情绪，形成层级结构
emotions.parent_id -> emotions.id
```

#### 3. 心情记录-情绪选择 (1:N)
```sql
-- 一个心情记录可以包含多个情绪选择
emotion_selections.mood_entry_id -> mood_entries.id
```

#### 4. 实体-翻译 (1:N)
```sql
-- 每个实体可以有多个语言的翻译
entity_translations.entity_id -> entities.id
```

## 📈 索引策略

### 主要索引

#### 性能关键索引
```sql
-- 用户相关查询优化
CREATE INDEX IF NOT EXISTS idx_mood_entries_user_timestamp
ON mood_entries(user_id, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_emotion_selections_mood_entry
ON emotion_selections(mood_entry_id);

-- 翻译查询优化
CREATE INDEX IF NOT EXISTS idx_emotion_translations_lang
ON emotion_translations(language_code);

CREATE INDEX IF NOT EXISTS idx_ui_label_translations_lang
ON ui_label_translations(language_code);

-- 层级查询优化
CREATE INDEX IF NOT EXISTS idx_emotions_parent
ON emotions(parent_id) WHERE parent_id IS NOT NULL;

-- 软删除查询优化
CREATE INDEX IF NOT EXISTS idx_emotions_not_deleted
ON emotions(is_deleted) WHERE is_deleted = FALSE;
```

#### 复合索引
```sql
-- 多条件查询优化
CREATE INDEX IF NOT EXISTS idx_mood_entries_user_date_range
ON mood_entries(user_id, timestamp, is_deleted);

CREATE INDEX IF NOT EXISTS idx_emotions_tier_parent
ON emotions(tier_level, parent_id, is_deleted);
```

### 索引设计原则

1. **查询频率优先** - 为高频查询创建索引
2. **复合索引优化** - 考虑多字段查询模式
3. **选择性考虑** - 优先为高选择性字段创建索引
4. **写入性能平衡** - 避免过多索引影响写入性能

## 🛡️ 数据完整性

### 约束设计

#### 主键约束
```sql
-- 所有表都使用 UUID 作为主键
id TEXT PRIMARY KEY NOT NULL
```

#### 外键约束
```sql
-- 级联删除：删除用户时删除其心情记录
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE

-- 设置为空：删除情绪时将引用设为空
FOREIGN KEY (emotion_id) REFERENCES emotions(id) ON DELETE SET NULL
```

#### 检查约束
```sql
-- 强度值范围检查
intensity INTEGER CHECK (intensity BETWEEN 0 AND 100)

-- 层级范围检查
tier_level INTEGER CHECK (tier_level BETWEEN 1 AND 5)

-- 语言代码检查
language_code TEXT CHECK (language_code IN ('en', 'zh', 'zh-TW', ...))
```

#### 唯一约束
```sql
-- 用户名唯一
username TEXT UNIQUE NOT NULL

-- 翻译组合唯一
UNIQUE(entity_id, language_code)
```

### 数据验证

#### 应用层验证
```typescript
// 使用 Zod Schema 进行运行时验证
export const MoodEntrySchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  timestamp: z.string().datetime(),
  intensity: z.number().int().min(0).max(100).optional(),
  // ...
});
```

#### 数据库层验证
```sql
-- 触发器验证
CREATE TRIGGER validate_mood_entry_timestamp
BEFORE INSERT ON mood_entries
FOR EACH ROW
WHEN NEW.timestamp > datetime('now')
BEGIN
    SELECT RAISE(ABORT, 'Timestamp cannot be in the future');
END;
```

## ⚡ 性能优化

### 查询优化策略

#### 1. 分页查询
```sql
-- 使用 LIMIT 和 OFFSET 进行分页
SELECT * FROM mood_entries
WHERE user_id = ?
ORDER BY timestamp DESC
LIMIT 20 OFFSET 0;
```

#### 2. 预加载关联数据
```sql
-- 一次查询获取心情记录和情绪信息
SELECT
    me.*,
    e.name as emotion_name,
    et.translated_name as emotion_translated_name
FROM mood_entries me
LEFT JOIN emotions e ON me.primary_emotion = e.id
LEFT JOIN emotion_translations et ON e.id = et.emotion_id
    AND et.language_code = ?
WHERE me.user_id = ?;
```

#### 3. 条件索引
```sql
-- 为常用过滤条件创建条件索引
CREATE INDEX idx_active_emotions
ON emotions(name)
WHERE is_deleted = FALSE;
```

### 缓存策略

#### 1. 查询结果缓存
```typescript
// Repository 层实现查询缓存
class EmotionRepository {
  private cache = new Map<string, Emotion[]>();

  async findByTierLevel(tierLevel: number): Promise<Emotion[]> {
    const cacheKey = `tier_${tierLevel}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const result = await this.query(/* SQL */);
    this.cache.set(cacheKey, result);
    return result;
  }
}
```

#### 2. 翻译缓存
```typescript
// 翻译数据缓存
class TranslationCache {
  private translations = new Map<string, Map<string, string>>();

  async getTranslation(entityId: string, languageCode: string): Promise<string> {
    // 缓存逻辑
  }
}
```

## 🔄 迁移策略

### 版本管理

#### 1. 迁移文件命名
```
migrations/
├── 001_initial_schema.sql
├── 002_add_emotion_translations.sql
├── 003_add_user_configs.sql
└── 004_add_soft_delete.sql
```

#### 2. 迁移脚本结构
```sql
-- 002_add_emotion_translations.sql
-- Migration: Add emotion translations support
-- Version: 2
-- Date: 2024-01-15

BEGIN TRANSACTION;

-- Create emotion_translations table
CREATE TABLE IF NOT EXISTS emotion_translations (
    emotion_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    PRIMARY KEY (emotion_id, language_code),
    FOREIGN KEY (emotion_id) REFERENCES emotions(id) ON DELETE CASCADE
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_emotion_translations_lang
ON emotion_translations(language_code);

-- Update schema version
UPDATE schema_version SET version = 2, updated_at = CURRENT_TIMESTAMP;

COMMIT;
```

### 数据迁移

#### 1. 向后兼容
```sql
-- 添加新字段时提供默认值
ALTER TABLE emotions ADD COLUMN tier_level INTEGER DEFAULT 1;
```

#### 2. 数据转换
```sql
-- 迁移现有数据到新结构
INSERT INTO emotion_translations (emotion_id, language_code, translated_name)
SELECT id, 'en', name FROM emotions WHERE name IS NOT NULL;
```

### 回滚策略

#### 1. 回滚脚本
```sql
-- rollback_002.sql
BEGIN TRANSACTION;

-- Remove emotion_translations table
DROP TABLE IF EXISTS emotion_translations;

-- Revert schema version
UPDATE schema_version SET version = 1, updated_at = CURRENT_TIMESTAMP;

COMMIT;
```

#### 2. 数据备份
```typescript
// 迁移前自动备份
class MigrationManager {
  async runMigration(migrationFile: string) {
    await this.createBackup();
    try {
      await this.executeMigration(migrationFile);
    } catch (error) {
      await this.restoreBackup();
      throw error;
    }
  }
}
```

## 🔧 实际应用示例

### 多语言查询示例

#### 获取用户心情记录（带翻译）
```sql
SELECT
    me.id,
    me.timestamp,
    me.intensity,
    me.reflection,
    -- 情绪信息（带翻译）
    e.id as emotion_id,
    COALESCE(et.translated_name, e.name) as emotion_name,
    COALESCE(et.translated_description, e.description) as emotion_description,
    e.color as emotion_color,
    e.emoji as emotion_emoji,
    -- 标签信息（带翻译）
    GROUP_CONCAT(COALESCE(tt.translated_name, t.name)) as tag_names
FROM mood_entries me
LEFT JOIN emotions e ON me.primary_emotion = e.id
LEFT JOIN emotion_translations et ON e.id = et.emotion_id AND et.language_code = ?
LEFT JOIN mood_entry_tags met ON me.id = met.mood_entry_id
LEFT JOIN tags t ON met.tag_id = t.id
LEFT JOIN tag_translations tt ON t.id = tt.tag_id AND tt.language_code = ?
WHERE me.user_id = ?
    AND me.timestamp BETWEEN ? AND ?
    AND (e.is_deleted = FALSE OR e.is_deleted IS NULL)
GROUP BY me.id
ORDER BY me.timestamp DESC
LIMIT 50;
```

#### 获取情绪层级树（带翻译）
```sql
WITH RECURSIVE emotion_tree AS (
    -- 根节点
    SELECT
        e.id,
        e.name,
        COALESCE(et.translated_name, e.name) as display_name,
        e.parent_id,
        e.tier_level,
        e.color,
        e.emoji,
        0 as level
    FROM emotions e
    LEFT JOIN emotion_translations et ON e.id = et.emotion_id AND et.language_code = ?
    WHERE e.parent_id IS NULL AND e.is_deleted = FALSE

    UNION ALL

    -- 子节点
    SELECT
        e.id,
        e.name,
        COALESCE(et.translated_name, e.name) as display_name,
        e.parent_id,
        e.tier_level,
        e.color,
        e.emoji,
        et_parent.level + 1
    FROM emotions e
    LEFT JOIN emotion_translations et ON e.id = et.emotion_id AND et.language_code = ?
    INNER JOIN emotion_tree et_parent ON e.parent_id = et_parent.id
    WHERE e.is_deleted = FALSE
)
SELECT * FROM emotion_tree ORDER BY level, display_name;
```

### 数据分析查询示例

#### 用户情绪统计
```sql
SELECT
    DATE(me.timestamp) as date,
    COALESCE(et.translated_name, e.name) as emotion_name,
    e.color,
    COUNT(*) as count,
    AVG(me.intensity) as avg_intensity,
    MIN(me.intensity) as min_intensity,
    MAX(me.intensity) as max_intensity
FROM mood_entries me
JOIN emotions e ON me.primary_emotion = e.id
LEFT JOIN emotion_translations et ON e.id = et.emotion_id AND et.language_code = ?
WHERE me.user_id = ?
    AND me.timestamp >= date('now', '-30 days')
    AND e.is_deleted = FALSE
GROUP BY DATE(me.timestamp), e.id, et.translated_name, e.color
ORDER BY date DESC, count DESC;
```

#### 标签使用频率统计
```sql
SELECT
    COALESCE(tt.translated_name, t.name) as tag_name,
    COUNT(met.mood_entry_id) as usage_count,
    COUNT(DISTINCT me.user_id) as user_count,
    AVG(me.intensity) as avg_intensity_when_used
FROM tags t
LEFT JOIN tag_translations tt ON t.id = tt.tag_id AND tt.language_code = ?
LEFT JOIN mood_entry_tags met ON t.id = met.tag_id
LEFT JOIN mood_entries me ON met.mood_entry_id = me.id
WHERE t.is_deleted = FALSE
    AND me.timestamp >= date('now', '-90 days')
GROUP BY t.id, tt.translated_name
HAVING usage_count > 0
ORDER BY usage_count DESC
LIMIT 20;
```

## 🚀 性能监控

### 查询性能分析

#### 1. 慢查询识别
```sql
-- 启用查询计划分析
EXPLAIN QUERY PLAN
SELECT /* 你的查询 */;

-- 分析索引使用情况
.eqp on
SELECT /* 你的查询 */;
```

#### 2. 索引效果评估
```sql
-- 检查索引使用统计
SELECT
    name,
    tbl,
    sql
FROM sqlite_master
WHERE type = 'index'
    AND tbl IN ('mood_entries', 'emotions', 'emotion_translations')
ORDER BY tbl, name;
```

### 数据库统计信息

#### 1. 表大小统计
```sql
SELECT
    name as table_name,
    COUNT(*) as row_count
FROM sqlite_master sm
JOIN (
    SELECT 'users' as name UNION ALL
    SELECT 'mood_entries' UNION ALL
    SELECT 'emotions' UNION ALL
    SELECT 'emotion_translations'
) tables ON sm.name = tables.name
WHERE sm.type = 'table';
```

#### 2. 存储空间分析
```sql
-- 获取数据库文件大小信息
PRAGMA page_count;
PRAGMA page_size;
PRAGMA freelist_count;
```

## 🔒 安全考虑

### 数据访问控制

#### 1. 用户数据隔离
```sql
-- 确保用户只能访问自己的数据
SELECT * FROM mood_entries
WHERE user_id = ? -- 当前用户ID
    AND id = ?;   -- 记录ID
```

#### 2. 软删除验证
```sql
-- 查询时排除已删除的数据
SELECT * FROM emotions
WHERE is_deleted = FALSE
    OR is_deleted IS NULL;
```

### 数据完整性保护

#### 1. 事务处理
```typescript
// Repository 层事务示例
async createMoodEntryWithSelections(
  moodEntry: CreateMoodEntryData,
  selections: CreateEmotionSelectionData[]
): Promise<MoodEntry> {
  return await this.withTransaction(async (tx) => {
    // 创建心情记录
    const entry = await this.create(tx, moodEntry);

    // 创建情绪选择
    for (const selection of selections) {
      selection.mood_entry_id = entry.id;
      await this.emotionSelectionRepo.create(tx, selection);
    }

    return entry;
  });
}
```

#### 2. 数据验证
```sql
-- 创建验证触发器
CREATE TRIGGER validate_emotion_hierarchy
BEFORE INSERT ON emotions
FOR EACH ROW
WHEN NEW.parent_id IS NOT NULL
BEGIN
    SELECT CASE
        WHEN (SELECT COUNT(*) FROM emotions WHERE id = NEW.parent_id AND is_deleted = FALSE) = 0
        THEN RAISE(ABORT, 'Parent emotion does not exist or is deleted')
    END;
END;
```

## 📊 监控和维护

### 数据库健康检查

#### 1. 完整性检查
```sql
-- 检查外键完整性
PRAGMA foreign_key_check;

-- 检查数据库完整性
PRAGMA integrity_check;
```

#### 2. 统计信息更新
```sql
-- 更新查询优化器统计信息
ANALYZE;

-- 针对特定表更新统计信息
ANALYZE mood_entries;
ANALYZE emotions;
```

### 维护任务

#### 1. 数据清理
```sql
-- 清理过期的软删除数据（超过1年）
DELETE FROM emotions
WHERE is_deleted = TRUE
    AND deleted_at < date('now', '-1 year');
```

#### 2. 索引维护
```sql
-- 重建索引以优化性能
REINDEX idx_mood_entries_user_timestamp;
REINDEX idx_emotion_translations_lang;
```

## 📚 相关文档

- [类型系统架构文档](../src/types/README.md)
- [多语言系统文档](./internationalization.md)
- [泛化类型系统文档](./type-system-architecture.md)
- [API 设计规范](./api-design.md)
- [性能优化指南](./performance-optimization.md)

---

通过遵循这个数据库设计，我们确保了：
- **数据一致性**：完整的约束和验证机制
- **性能优化**：合理的索引和查询策略
- **可扩展性**：灵活的架构支持功能扩展
- **多语言支持**：完整的国际化数据结构
- **数据安全**：软删除和审计追踪机制
- **监控能力**：完善的性能监控和维护机制
