-- Insert VIP Plans and Features Data
-- This script populates the VIP system with default plans and features

-- ============================================================================
-- VIP FEATURES
-- ============================================================================

INSERT OR REPLACE INTO vip_features (id, name, description, feature_type, config, is_active, created_at, updated_at) VALUES
('unlimited_skins', 'Unlimited Skins', 'Access to all premium skins and themes', 'unlock', '{"unlock_type":"skin","unlock_all":true,"categories":["premium","exclusive","animated"]}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('unlimited_emoji_sets', 'Unlimited Emoji Sets', 'Access to all premium emoji sets and collections', 'unlock', '{"unlock_type":"emoji_set","unlock_all":true,"categories":["premium","animated","custom"]}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('premium_emoji_sets', 'Premium Emoji Sets', 'Access to selected premium emoji sets', 'unlock', '{"unlock_type":"emoji_set","unlock_all":false,"limit":10,"categories":["premium"]}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_themes', 'Advanced Themes', 'Access to advanced theme customization options', 'customization', '{"theme_customization":true,"color_picker":true,"gradient_backgrounds":true,"custom_fonts":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('export_data', 'Data Export', 'Export mood data in various formats (CSV, JSON, PDF)', 'access', '{"formats":["csv","json","pdf"],"include_analytics":true,"custom_date_ranges":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('priority_support', 'Priority Support', 'Priority customer support with faster response times', 'access', '{"response_time_hours":24,"support_channels":["email","chat","phone"],"dedicated_support":false}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('advanced_analytics', 'Advanced Analytics', 'Detailed mood analytics and trend analysis', 'access', '{"trend_analysis":true,"correlation_analysis":true,"predictive_insights":true,"custom_reports":true,"data_visualization":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('mood_insights', 'Mood Insights', 'AI-powered mood insights and recommendations', 'access', '{"ai_insights":true,"personalized_recommendations":true,"mood_patterns":true,"trigger_identification":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('custom_quiz_packs', 'Custom Quiz Packs', 'Create and customize your own quiz packs', 'customization', '{"create_quiz_packs":true,"custom_questions":true,"custom_options":true,"share_quiz_packs":true,"quiz_pack_limit":null}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('data_backup', 'Data Backup', 'Automatic cloud backup of all mood data', 'access', '{"automatic_backup":true,"backup_frequency":"daily","backup_retention_days":365,"restore_capability":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('multi_device_sync', 'Multi-Device Sync', 'Sync data across unlimited devices', 'access', '{"device_limit":null,"real_time_sync":true,"conflict_resolution":true,"offline_sync":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('api_access', 'API Access', 'Access to developer API for custom integrations', 'access', '{"api_key":true,"rate_limit_per_hour":10000,"webhook_support":true,"custom_endpoints":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('white_label', 'White Label', 'Remove branding and customize app appearance', 'customization', '{"remove_branding":true,"custom_logo":true,"custom_colors":true,"custom_app_name":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('team_management', 'Team Management', 'Manage team members and organizational features', 'access', '{"team_size_limit":100,"role_management":true,"team_analytics":true,"bulk_operations":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('custom_integrations', 'Custom Integrations', 'Custom integrations with third-party services', 'access', '{"webhook_integrations":true,"zapier_integration":true,"slack_integration":true,"calendar_integration":true,"health_app_integration":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('dedicated_support', 'Dedicated Support', 'Dedicated support representative and account management', 'access', '{"dedicated_rep":true,"response_time_hours":4,"phone_support":true,"video_calls":true,"custom_training":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('lifetime_updates', 'Lifetime Updates', 'Lifetime access to all future updates and features', 'access', '{"lifetime_access":true,"all_future_features":true,"grandfathered_pricing":true}', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ============================================================================
-- VIP PLANS
-- ============================================================================

INSERT OR REPLACE INTO vip_plans (id, name, description, tier, price, currency, billing_cycle, features, skin_unlock_limit, emoji_set_unlock_limit, storage_limit_mb, is_active, is_featured, sort_order, created_at, updated_at) VALUES
('vip_basic_monthly', 'Basic VIP', 'Essential premium features for enhanced mood tracking experience', 'basic', 4.99, 'USD', 'monthly', '["unlimited_skins","premium_emoji_sets","advanced_themes","export_data","priority_support"]', NULL, 10, 100, 1, 0, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('vip_basic_yearly', 'Basic VIP (Yearly)', 'Essential premium features with yearly savings', 'basic', 49.99, 'USD', 'yearly', '["unlimited_skins","premium_emoji_sets","advanced_themes","export_data","priority_support"]', NULL, 10, 100, 1, 1, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('vip_premium_monthly', 'Premium VIP', 'Complete premium experience with advanced analytics and unlimited content', 'premium', 9.99, 'USD', 'monthly', '["unlimited_skins","unlimited_emoji_sets","advanced_themes","export_data","priority_support","advanced_analytics","mood_insights","custom_quiz_packs","data_backup","multi_device_sync"]', NULL, NULL, 500, 1, 0, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('vip_premium_yearly', 'Premium VIP (Yearly)', 'Complete premium experience with yearly savings', 'premium', 99.99, 'USD', 'yearly', '["unlimited_skins","unlimited_emoji_sets","advanced_themes","export_data","priority_support","advanced_analytics","mood_insights","custom_quiz_packs","data_backup","multi_device_sync"]', NULL, NULL, 500, 1, 1, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('vip_enterprise_yearly', 'Enterprise VIP', 'Ultimate premium experience for power users and organizations', 'enterprise', 199.99, 'USD', 'yearly', '["unlimited_skins","unlimited_emoji_sets","advanced_themes","export_data","priority_support","advanced_analytics","mood_insights","custom_quiz_packs","data_backup","multi_device_sync","api_access","white_label","team_management","custom_integrations","dedicated_support"]', NULL, NULL, NULL, 1, 0, 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('vip_lifetime', 'Lifetime VIP', 'One-time payment for lifetime access to all premium features', 'premium', 299.99, 'USD', 'lifetime', '["unlimited_skins","unlimited_emoji_sets","advanced_themes","export_data","priority_support","advanced_analytics","mood_insights","custom_quiz_packs","data_backup","multi_device_sync","lifetime_updates"]', NULL, NULL, 1000, 1, 1, 6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify VIP features were inserted
SELECT 'VIP Features Count:' as info, COUNT(*) as count FROM vip_features;

-- Verify VIP plans were inserted
SELECT 'VIP Plans Count:' as info, COUNT(*) as count FROM vip_plans;

-- Show all VIP plans with their features
SELECT 
    p.id,
    p.name,
    p.tier,
    p.price,
    p.currency,
    p.billing_cycle,
    p.is_featured,
    p.features
FROM vip_plans p
ORDER BY p.sort_order;
