#!/usr/bin/env python3
"""
脚本用于修复测试文件中的Mock调用
将 mockContext.db.* 替换为 mockContext.connection.*
"""

import re
import sys

def fix_mock_calls(file_path):
    """修复文件中的Mock调用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换模式
        patterns = [
            (r'mockContext\.db\.query', 'mockContext.connection.query'),
            (r'mockContext\.db\.run', 'mockContext.connection.query'),
            (r'mockContext\.db\.execute', 'mockContext.connection.execute'),
        ]
        
        original_content = content
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已修复 {file_path}")
            return True
        else:
            print(f"ℹ️  {file_path} 无需修复")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python fix_mock_calls.py <file_path>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    success = fix_mock_calls(file_path)
    sys.exit(0 if success else 1)
