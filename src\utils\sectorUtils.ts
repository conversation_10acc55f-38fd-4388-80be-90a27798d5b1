/**
 * 扇区工具类 - 提供更精确的扇区生成逻辑
 * 用于解决D3.js生成的扇区之间存在缝隙的问题
 */

import type { EmotionOption, EmotionTier } from '@/types/mood';

/**
 * 扇区数据接口
 */
export interface SectorData {
  id: string;
  name: string;
  emoji?: string;
  color: string;
  startAngle: number;
  endAngle: number;
  innerRadius: number;
  outerRadius: number;
  isPlaceholder?: boolean;
}

/**
 * 生成扇区路径
 * @param sector 扇区数据
 * @returns SVG路径字符串
 */
export function generateSectorPath(sector: SectorData): string {
  const { innerRadius, outerRadius, startAngle, endAngle } = sector;

  // 计算扇区的四个点
  const innerStartX = innerRadius * Math.cos(startAngle);
  const innerStartY = innerRadius * Math.sin(startAngle);
  const innerEndX = innerRadius * Math.cos(endAngle);
  const innerEndY = innerRadius * Math.sin(endAngle);
  const outerStartX = outerRadius * Math.cos(startAngle);
  const outerStartY = outerRadius * Math.sin(startAngle);
  const outerEndX = outerRadius * Math.cos(endAngle);
  const outerEndY = outerRadius * Math.sin(endAngle);

  // 计算是否使用大弧（大于180度）
  const largeArcFlag = endAngle - startAngle > Math.PI ? 1 : 0;

  // 构建SVG路径
  // 从外圆起点开始，顺时针绘制到外圆终点，然后连接到内圆终点，再逆时针绘制到内圆起点，最后闭合
  return `
    M ${outerStartX} ${outerStartY}
    A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${outerEndX} ${outerEndY}
    L ${innerEndX} ${innerEndY}
    A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${innerStartX} ${innerStartY}
    Z
  `
    .trim()
    .replace(/\s+/g, ' ');
}

/**
 * 生成情绪扇区数据
 * @param emotions 情绪数据
 * @param radius 轮盘半径
 * @param tier 情绪层级
 * @returns 扇区数据数组
 */
export function generateEmotionSectors(
  emotions: EmotionOption[],
  radius: number,
  tier: EmotionTier
): SectorData[] {
  if (!emotions.length) return [];

  // 计算内外半径
  let innerRadiusRatio = 0.3;
  if (emotions.length > 8) {
    innerRadiusRatio = 0.35;
  } else if (emotions.length <= 4) {
    innerRadiusRatio = 0.25;
  }

  // 根据tier调整外圆半径
  let outerRadius = radius;
  if (tier === 'tertiary') {
    outerRadius = radius * 0.95;
  } else if (tier === 'secondary') {
    outerRadius = radius;
  } else {
    outerRadius = radius * 1.05;
  }

  const innerRadius = outerRadius * innerRadiusRatio;

  // 处理特殊情况：只有1个或2个情绪
  if (emotions.length === 1) {
    // 单个情绪占据整个圆
    return [
      {
        ...emotions[0],
        startAngle: 0,
        endAngle: 2 * Math.PI,
        innerRadius,
        outerRadius,
      },
    ];
  }

  // 计算每个扇区的角度
  const anglePerSector = (2 * Math.PI) / emotions.length;

  // 生成扇区数据
  return emotions.map((emotion, index) => {
    const startAngle = index * anglePerSector;
    const endAngle = (index + 1) * anglePerSector;

    return {
      ...emotion,
      startAngle,
      endAngle,
      innerRadius,
      outerRadius,
      isPlaceholder: emotion.id.startsWith('placeholder'),
    };
  });
}

/**
 * 生成扇区动画补间函数
 * @param sector 扇区数据
 * @param progress 动画进度 (0-1)
 * @returns 动画中的扇区路径
 */
export function generateSectorTween(sector: SectorData, progress: number): string {
  if (sector.isPlaceholder) {
    // 占位符扇区不参与动画
    return generateSectorPath(sector);
  }

  // 创建动画中的扇区数据
  const tweenedSector = {
    ...sector,
    endAngle: sector.startAngle + (sector.endAngle - sector.startAngle) * progress,
  };

  return generateSectorPath(tweenedSector);
}

/**
 * 计算扇区中心点
 * @param sector 扇区数据
 * @returns [x, y] 坐标
 */
export function calculateSectorCenter(sector: SectorData): [number, number] {
  const angle = (sector.startAngle + sector.endAngle) / 2;
  const radius = (sector.innerRadius + sector.outerRadius) / 2;

  return [radius * Math.cos(angle), radius * Math.sin(angle)];
}

/**
 * 准备轮盘数据，确保完整的360°圆形
 * @param emotions 情绪数组
 * @returns 处理后的数据
 */
export function prepareWheelSectors(emotions: EmotionOption[]): EmotionOption[] {
  // 如果没有情绪，返回一个完整的占位符圆
  if (emotions.length === 0) {
    return Array(4)
      .fill(0)
      .map((_, i) => ({
        id: `placeholder-${i}`,
        name: '',
        color: '#cccccc',
      }));
  }

  // 如果情绪数量太少，添加占位符确保至少有4个扇区
  if (emotions.length < 4) {
    // 特殊处理2个情绪的情况
    if (emotions.length === 2) {
      // 对于2个情绪，不添加占位符，直接返回原始数据
      return [...emotions];
    }
    if (emotions.length === 1) {
      // 对于1个情绪，添加3个占位符形成完整圆形
      const result = [emotions[0]];
      for (let i = 0; i < 3; i++) {
        result.push({
          id: `placeholder-${i}`,
          name: '',
          color: '#cccccc',
        });
      }
      return result;
    }
    if (emotions.length === 3) {
      // 对于3个情绪，添加1个占位符使总数达到4个
      return [
        ...emotions,
        {
          id: 'placeholder-0',
          name: '',
          color: '#cccccc',
        },
      ];
    }
  }

  // 情绪数量足够，直接使用原始数据
  return [...emotions];
}
