import { cn } from '@/lib/utils';
import type React from 'react';

interface SettingsSectionProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

/**
 * 设置部分组件
 * 用于设置页面中的各个部分，提供一致的样式和结构
 */
const SettingsSection: React.FC<SettingsSectionProps> = ({
  title,
  description,
  icon,
  children,
  className = '',
}) => {
  return (
    <div className={cn('rounded-lg border bg-card text-card-foreground shadow-sm p-4', className)}>
      <div className="mb-4">
        <h3 className="text-lg font-medium flex items-center">
          {icon && <span className="mr-2">{icon}</span>}
          {title}
        </h3>
        {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
      </div>
      <div className="space-y-4">{children}</div>
    </div>
  );
};

export default SettingsSection;
