## 🌈 App 名称：**MoodWheel**

一个基于情绪轮盘意象的情绪探索与记录应用，帮助用户更好地理解、表达和管理情绪。

---

## 🧩 核心创意元素

### 🎡 **轮盘式情绪导航**

* 采用 Plutchik 或拓展版的情绪轮盘作为核心视觉与交互组件。
* 用户通过点击不同区域，**逐层探索**：一级（基础情绪）→ 二级（具体感受）→ 三级（细致情绪）。
* 动效模拟「拨开情绪的洋葱」，层层深入。

---

## ✨ 核心功能模块设计

### 1. 🌀 **MoodWheel 情绪探索轮盘**

* 首页即为情绪轮盘，点击某个一级情绪（如“Sad”）展开子类。
* 中心浮现当前选择路径，例如「Sad → Lonely → Abandoned」。
* 每一级有返回按钮，支持自由探索。

---

### 2. 📖 **记录情绪日记**

* 在选中三级情绪后，进入情绪记录界面：

  * 可以打分：0～10 情绪强度
  * 输入文字：描述发生了什么
  * 上传照片/语音（选填）
* 自动记录时间与情绪路径
* 可选择“是否公开给自己未来的我”

---

### 3. 📊 **情绪图谱（历史视图）**

* 通过时间轴/日历/热力图查看自己情绪变化轨迹。
* 支持筛选：查看过去30天的「愤怒」「快乐」趋势。
* 导出 PDF 作为心理复盘日志。

---

### 4. 🧘 **情绪调节空间**

* 根据情绪类型推荐内容：

  * 🧘 放松音乐 / 冥想引导
  * 📝 建议的写作练习
  * 📺 轻松短片 / 视频
  * 🧩 表达性任务（如画画、拼贴）
* 可自定义收藏常用调节工具

---

### 5. 🧭 **心情探索地图（Gamify）**

* 情绪探索如同环游情绪世界：

  * 每选择并记录一种新的三级情绪，解锁一块地图碎片
  * 情绪岛屿风格：悲伤岛、愤怒火山、希望花园、平静湖等
* 集齐「100种情绪」可完成一次完整冒险之旅

---

## 🎨 UI 视觉设计风格建议

| 元素   | 建议                               |
| ---- | -------------------------------- |
| 主色调  | 柔和渐变色 + 情绪色调联动（比如：Sad→蓝，Angry→红） |
| 动画风格 | 轻柔展开、呼吸光效，增强沉浸感                  |
| 轮盘图  | 三层彩色扇形，动态可缩放/转动                  |
| 字体   | 简洁亲和（如 Rounded Gothic、Avenir）    |
| 图标   | 情绪拟人化表情 + 轻量图示                   |

---

## 💡 名称延伸 & slogan 提案

| 名称            | Slogan             |
| ------------- | ------------------ |
| **MoodWheel** | “转动你的感受，发现内心的彩虹。”  |
| EmotionMap    | “每种情绪，都是你的一部分。”    |
| FeelLoop      | “理解情绪的循环，找到自己的节奏。” |
| 心感轮           | “感受，表达，成长。”（中文版名）  |

---

## 🚀 MoodWheel 的未来拓展方向

* **AI 心情分析**：写下文字后自动识别情绪轮盘位置；
* **社交模式**：与好友匿名互换心情、支持倾诉卡片；
* **心理咨询接入**：严重情绪波动时推送专业资源。

**产品需求文档（PRD）：MoodWheel 情绪探索与记录 App**

---

### 一、产品概述

**产品名称**：MoodWheel
**产品类型**：移动端应用（iOS / Android）
**产品目标**：帮助用户理解、识别、记录和管理自身情绪，通过交互式轮盘设计，引导用户逐层探索感受，并通过情绪记录、分析和调节工具提升情绪管理能力。

---

### 二、核心用户需求

1. **识别情绪**：用户难以准确表达或识别自己的具体情绪，需要引导工具帮助探索。
2. **记录情绪**：用户希望记录每日心情和事件，提升自我觉察能力。
3. **分析情绪**：用户希望长期查看自己情绪变化趋势，了解哪些事件易触发某种情绪。
4. **情绪调节**：在体验强烈情绪时，希望获取适当资源以调节情绪状态。

---

### 三、主要功能模块

#### 1. 情绪轮盘导航（MoodWheel）

* 首页即为三层情绪轮盘，可视化基础 → 具体 → 精细情绪结构
* 用户逐层点击展开：一级（如 Happy） → 二级（如 Interested） → 三级（如 Curious）
* 中心动态展示当前路径
* 提供返回上一级按钮，支持回溯与切换
* 支持长按情绪点查看定义与解释

#### 2. 情绪记录（日记）

* 用户在选中三级情绪后可进入记录界面：

  * 当前时间戳自动生成
  * 强度选择：0-10 分
  * 添加文字备注
  * 可选上传照片、语音
  * 标签添加：事件类型、自定义标签
  * 设置是否对未来自己公开
* 提供提醒功能：定时提示每日记录

#### 3. 情绪分析图谱

* 提供以下数据图表：

  * 日历视图：颜色标注每日主要情绪
  * 时间轴视图：情绪随时间的变化轨迹
  * 情绪词云：近期常见情绪频次
  * 情绪类别分布饼图
* 支持筛选查看：

  * 某一类情绪趋势（如“Sad”类）
  * 情绪与事件类别关联（如“愤怒”多发生于“家庭”）

#### 4. 情绪调节空间（Toolkit）

* 根据当前记录的情绪推荐调节工具：

  * 冥想音频、放松音乐
  * 自我表达任务（如写信、画图）
  * 视频片段、小故事
  * 肢体活动建议（如舒展、呼吸）
* 用户可收藏调节方式并设为常用

#### 5. 心情探索地图（Gamify 模式）

* 每记录一种三级情绪，即解锁情绪岛屿图中的一块区域
* 地图包括：悲伤岛、愤怒火山、希望花园、平静湖等
* 解锁全部情绪视为完成一次“心灵冒险之旅”
* 每个情绪岛可展示曾记录的相关情绪日志回顾

---

### 四、目标用户画像

| 用户类型  | 特征描述                |
| ----- | ------------------- |
| 自我探索者 | 喜欢心理成长内容、写日记、进行情绪分析 |
| 情绪敏感者 | 经常体验情绪波动、寻求调节与支持    |
| 忙碌上班族 | 需要快速记录情绪和获得调节建议     |
| 青少年学生 | 初步建立情绪认知和表达能力       |

---

### 五、交互与视觉设计

#### 情绪轮盘设计

* 使用彩色三层同心扇形轮盘表示不同情绪维度
* 每层颜色渐变表示情绪强度差异
* 点击动效：放大该区块并浮出下一级内容
* 中心动态展示已选路径（如：Happy → Proud → Respected）

#### 主色调风格

* 使用柔和渐变配色，根据主情绪切换背景色
* 例如愤怒：温暖红橘；平静：浅蓝灰；快乐：明亮黄绿

#### 字体与图标

* 字体建议：Rounded Gothic / Avenir / HarmonyOS Sans
* 图标风格：手绘风表情、简约图形增强亲和力

---

### 六、技术需求

* 前端：Flutter / React Native
* 后端：Firebase / Node.js + MongoDB
* 数据可视化：Chart.js / Echarts
* 数据分析引擎：支持打标签与情绪推荐算法

---

### 七、版本规划

#### V1.0 MVP 功能：

* 情绪轮盘选择器
* 日志记录
* 情绪分析基础图表
* 情绪调节推荐基础

#### V2.0

* 心情地图玩法上线
* 高级数据分析（关键词情绪识别）
* 图片情绪识别（CV）
* 云同步与账号系统

#### V3.0

* AI 情绪建议助手
* 与朋友共享心情频道
* 精神健康资源接入

---

### 八、数据指标（KPI）

| 指标名称       | 指标目标                   |
| ---------- | ---------------------- |
| 日活跃用户（DAU） | 5,000+                 |
| 每日平均情绪记录次数 | 2+                     |
| 日志记录留存率    | 次日留存 > 40%，7 日留存 > 20% |
| 完整情绪轮盘解锁率  | 首月用户达 20%              |

---

### 九、潜在商业模式

* 付费订阅：高级分析、语音情绪识别、定制冥想包
* 企业合作：情绪监测 SaaS 模块提供给学校/企业
* 情绪调节内容商店：创作者上传音频/绘本分成机制

---

### 十、附录

* 情绪词库结构参考 Plutchik 及扩展轮盘
* 参考产品：Moodnotes、How We Feel、Reflectly
* 本项目 UI/UX 可结合小红书、美感控等平台 A/B 测试视觉设计

---

## 开发环境设置

### 本地开发

1. 安装依赖：
   ```bash
   npm install
   ```

2. 创建本地 SQLite 数据库（用于开发环境）：
   ```bash
   npm run create-local-db
   ```
   这将创建一个 `local.db` 文件，用于在开发环境中模拟 Turso 数据库。

3. 启动开发服务器：
   ```bash
   npm run dev
   ```

### 数据库配置

- **开发环境**：自动使用本地 SQLite 文件 (`local.db`)
- **生产环境**：使用远程 Turso 数据库

更多关于 Turso 数据库设置的详细信息，请参阅 [TURSO_SETUP.md](./TURSO_SETUP.md)。

### 服务器实现

本项目提供了两种服务器实现方案，用于处理 Turso 数据库操作：

#### 方案一：tRPC 服务器

基于 Cloudflare Pages 的 tRPC 服务器，提供类型安全的 API。

1. 启动服务器：
   ```bash
   cd server
   npm install
   npm run dev
   ```

2. 配置客户端连接到服务器：
   在 `.env` 文件中添加：
   ```
   VITE_API_URL=http://localhost:8788
   ```

更多关于 tRPC 服务器实现的详细信息，请参阅 [server/README.md](./server/README.md)。

#### 方案二：Cloudflare Functions

基于 Cloudflare Functions 的 REST API，类似于 `functions/api` 模式。

1. 启动 Functions：
   ```bash
   cd functions
   npm install
   npm run dev
   ```

2. 配置客户端连接到 Functions：
   在 `.env` 文件中添加：
   ```
   VITE_FUNCTIONS_URL=http://localhost:8788
   ```

更多关于 Functions 实现的详细信息，请参阅 [functions/README.md](./functions/README.md)。

您可以选择使用其中一种方案，或者根据需要同时使用两种方案。

---

如需 UI 原型图、Logo 风格、品牌指南等文档支持，可继续推进。