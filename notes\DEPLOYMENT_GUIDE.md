# MoodWheel 部署指南

## 构建流程
1. **安装依赖**：在项目根目录执行 `pnpm install` 安装所有依赖。
2. **构建前端**：执行 `pnpm build` 生成生产环境代码，输出到 `dist/` 目录。
3. **构建服务端**：进入 `server/` 目录，执行 `pnpm build` 生成服务端代码。

---

## 本地测试
1. **启动本地服务**：在 `server/` 目录执行 `pnpm start` 启动服务端。
2. **预览前端**：在项目根目录执行 `pnpm preview` 启动本地预览服务器。
3. **访问**：打开浏览器访问 `http://localhost:4173` 检查功能是否正常。

---

## 预发布
1. **部署到预发布环境**：将 `dist/` 和 `server/` 目录代码部署到预发布服务器。
2. **配置环境变量**：确保预发布环境的 `.env` 文件配置正确。
3. **启动服务**：执行 `pnpm start` 启动预发布服务。
4. **功能验证**：进行功能测试和性能测试，确保无问题。

---

## 正式发布
1. **部署到生产环境**：将预发布环境验证通过的代码部署到生产服务器。
2. **配置环境变量**：确保生产环境的 `.env` 文件配置正确。
3. **启动服务**：执行 `pnpm start` 启动生产服务。
4. **监控**：使用监控工具（如 Sentry、New Relic）监控应用运行状态。

---

## 回滚流程
1. **备份**：部署前备份当前生产环境代码。
2. **回滚命令**：如遇问题，执行 `git checkout <last-stable-commit>` 回滚到稳定版本。
3. **重启服务**：执行 `pnpm start` 重启服务。

---

## 注意事项
- 确保所有环境变量和配置正确，避免敏感信息泄露。
- 部署前进行完整测试，确保功能正常。
- 保持与团队沟通，确保部署过程顺利。 