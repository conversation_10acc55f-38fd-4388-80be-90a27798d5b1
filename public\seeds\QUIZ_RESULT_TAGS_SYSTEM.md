# Quiz Result Tags System

本文档描述了为 `quiz_results` 添加的标签系统，类似于原来 `mood_entries` 的 `mood_entry_tags` 关联表。

## 📁 文件结构

```
public/seeds/
├── schema/
│   └── quiz_system_v2.sql              # 包含新的标签表和关联表
├── config/
│   ├── tags.json                       # 标签基础数据
│   ├── tag_translations.json           # 标签翻译数据
│   └── insert_tags_data.sql            # SQL插入脚本
└── test-user-data/
    └── quiz_result_tags.json           # 测试用的标签关联数据
```

## 🗄️ 数据库表结构

### 1. `tags` 表 - 标签主表

```sql
CREATE TABLE IF NOT EXISTS tags (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    category TEXT, -- 'emotion', 'context', 'activity', 'time'
    color TEXT, -- Hex color code
    icon TEXT, -- Icon identifier
    
    -- Tag metadata
    is_system BOOLEAN DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    usage_count INTEGER DEFAULT 0,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);
```

### 2. `quiz_result_tags` 表 - Quiz结果标签关联表

```sql
CREATE TABLE IF NOT EXISTS quiz_result_tags (
    quiz_result_id TEXT NOT NULL,
    tag_id TEXT NOT NULL,
    
    -- Tag association metadata
    added_by TEXT,
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
    tag_source TEXT DEFAULT 'user', -- 'user', 'ai_generated', 'system_suggested'
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    PRIMARY KEY (quiz_result_id, tag_id),
    FOREIGN KEY (quiz_result_id) REFERENCES quiz_results(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE SET NULL
);
```

### 3. `mood_entry_tags` 表 - 心情记录标签关联表（向后兼容）

```sql
CREATE TABLE IF NOT EXISTS mood_entry_tags (
    mood_entry_id TEXT NOT NULL,
    tag_id TEXT NOT NULL,
    
    -- Tag association metadata
    added_by TEXT,
    confidence_score REAL CHECK (confidence_score BETWEEN 0 AND 1),
    tag_source TEXT DEFAULT 'user',
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    PRIMARY KEY (mood_entry_id, tag_id),
    FOREIGN KEY (mood_entry_id) REFERENCES mood_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE SET NULL
);
```

### 4. `tag_translations` 表 - 标签翻译表

```sql
CREATE TABLE IF NOT EXISTS tag_translations (
    tag_id TEXT NOT NULL,
    language_code TEXT NOT NULL,
    translated_name TEXT NOT NULL,
    translated_description TEXT,
    
    -- Translation metadata
    translation_quality REAL CHECK (translation_quality BETWEEN 0 AND 1),
    translator_type TEXT DEFAULT 'human', -- 'human', 'ai', 'community'
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (tag_id, language_code),
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
) WITHOUT ROWID;
```

## 🏷️ 标签分类系统

### 标签类别

1. **context** - 情境标签
   - 工作 (work)
   - 家庭 (family)
   - 朋友 (friends)
   - 健康 (health)

2. **activity** - 活动标签
   - 运动 (exercise)
   - 学习 (study)
   - 休息 (rest)
   - 旅行 (travel)
   - 爱好 (hobby)

3. **emotion** - 情绪标签
   - 压力 (stress)
   - 焦虑 (anxiety)
   - 快乐 (happiness)
   - 悲伤 (sadness)
   - 愤怒 (anger)
   - 平静 (calm)

4. **time** - 时间标签
   - 早晨 (morning)
   - 下午 (afternoon)
   - 晚上 (evening)
   - 深夜 (night)
   - 周末 (weekend)

## 🔗 标签来源类型

- **user** - 用户手动添加
- **ai_generated** - AI自动生成
- **system_suggested** - 系统建议

## 🚀 使用方式

### 1. 为Quiz结果添加标签

```typescript
// 用户手动添加标签
await quizResultTagService.addTag({
  quiz_result_id: 'result_001',
  tag_id: 'tag_work',
  added_by: 'user_123',
  tag_source: 'user',
  confidence_score: 1.0
});

// AI自动生成标签
await quizResultTagService.addTag({
  quiz_result_id: 'result_001',
  tag_id: 'tag_stress',
  added_by: null,
  tag_source: 'ai_generated',
  confidence_score: 0.85
});
```

### 2. 查询带标签的Quiz结果

```typescript
// 获取Quiz结果及其标签
const resultWithTags = await quizResultService.getWithTags('result_001');

// 按标签筛选Quiz结果
const workRelatedResults = await quizResultService.getByTag('tag_work');

// 获取用户的标签使用统计
const tagStats = await tagService.getUserTagStats('user_123');
```

### 3. 标签管理

```typescript
// 创建新标签
await tagService.create({
  name: '新标签',
  category: 'context',
  color: '#FF5733',
  icon: 'custom-icon',
  is_system: false,
  created_by: 'user_123'
});

// 更新标签使用次数
await tagService.incrementUsageCount('tag_work');

// 获取热门标签
const popularTags = await tagService.getPopularTags(10);
```

## 🔧 TypeScript 类型定义

```typescript
// 标签类型
interface Tag {
  id: string;
  name: string;
  description?: string;
  category?: 'emotion' | 'context' | 'activity' | 'time';
  color?: string;
  icon?: string;
  is_system: boolean;
  is_active: boolean;
  usage_count: number;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

// Quiz结果标签关联类型
interface QuizResultTag {
  quiz_result_id: string;
  tag_id: string;
  added_by?: string;
  confidence_score?: number;
  tag_source: 'user' | 'ai_generated' | 'system_suggested';
  created_at: string;
}

// 标签翻译类型
interface TagTranslation {
  tag_id: string;
  language_code: string;
  translated_name: string;
  translated_description?: string;
  translation_quality?: number;
  translator_type: 'human' | 'ai' | 'community';
  created_at: string;
  updated_at: string;
}
```

## 📊 数据分析功能

### 标签统计分析

1. **使用频率分析** - 统计各标签的使用次数
2. **时间趋势分析** - 分析标签使用的时间模式
3. **情绪关联分析** - 分析标签与情绪状态的关联
4. **用户行为分析** - 分析用户的标签使用习惯

### 智能标签建议

1. **基于历史数据** - 根据用户历史标签使用推荐
2. **基于Quiz内容** - 根据Quiz答案内容自动建议标签
3. **基于时间模式** - 根据时间自动建议时间相关标签
4. **基于情绪分析** - 根据情绪分析结果建议情绪标签

## 🔄 迁移和兼容性

### 向后兼容

- 保留原有的 `mood_entry_tags` 表结构
- 新增字段向后兼容
- 支持同时使用新旧标签系统

### 数据迁移

1. 现有的心情记录标签继续有效
2. 新的Quiz结果可以使用相同的标签
3. 标签数据统一管理，避免重复

## 🚀 下一步计划

1. **实现服务层** - 创建标签相关的服务类
2. **构建UI组件** - 标签选择、管理界面
3. **集成AI分析** - 自动标签生成功能
4. **数据可视化** - 标签使用统计图表
5. **搜索和筛选** - 基于标签的内容搜索
6. **导出功能** - 包含标签的数据导出
