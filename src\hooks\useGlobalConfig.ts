/**
 * 全局应用配置 Hook
 * 管理用户的全局应用设置（主题、语言、通知等）
 * 采用离线优先 + 在线同步的混合模式
 */

import { useState, useEffect, useCallback } from 'react';
import { Services } from '../services';
import { trpc } from '../lib/trpc';
import { GlobalAppConfig } from '../types/schema/base';
import { UpdateGlobalAppConfigInput } from '../types/schema/api';
import { useAuth } from './useAuth';
import { useNetworkStatus } from './useNetworkStatus';

export interface UseGlobalConfigReturn {
  // 状态
  config: GlobalAppConfig | null;
  isLoading: boolean;
  error: string | null;
  isOnline: boolean;
  lastSyncTime: Date | null;
  
  // 操作
  updateConfig: (updates: UpdateGlobalAppConfigInput) => Promise<boolean>;
  resetToDefault: () => Promise<boolean>;
  syncToCloud: () => Promise<boolean>;
  refreshConfig: () => Promise<void>;
  
  // 便捷访问器
  themeMode: 'light' | 'dark' | 'system';
  language: 'zh-CN' | 'en-US';
  notificationsEnabled: boolean;
  soundEnabled: boolean;
  accessibilityConfig: {
    high_contrast: boolean;
    large_text: boolean;
    reduce_motion: boolean;
    screen_reader_support: boolean;
  };
}

export const useGlobalConfig = (configName: string = 'default'): UseGlobalConfigReturn => {
  const [config, setConfig] = useState<GlobalAppConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  
  const { user, isAuthenticated } = useAuth();
  const { isOnline } = useNetworkStatus();

  // 解析无障碍配置
  const accessibilityConfig = config?.accessibility ? 
    (() => {
      try {
        return JSON.parse(config.accessibility);
      } catch {
        return {
          high_contrast: false,
          large_text: false,
          reduce_motion: false,
          screen_reader_support: false
        };
      }
    })() : {
      high_contrast: false,
      large_text: false,
      reduce_motion: false,
      screen_reader_support: false
    };

  /**
   * 从本地数据库加载配置
   */
  const loadLocalConfig = useCallback(async () => {
    if (!user?.id) return;

    try {
      const globalConfigService = await Services.globalAppConfig();
      const result = await globalConfigService.getActiveUserConfig(user.id, configName);
      
      if (result.success && result.data) {
        setConfig(result.data);
        setError(null);
      } else {
        setError(result.error || 'Failed to load config');
      }
    } catch (err) {
      console.error('Error loading local config:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  }, [user?.id, configName]);

  /**
   * 从云端同步配置
   */
  const syncFromCloud = useCallback(async (): Promise<boolean> => {
    if (!isAuthenticated || !isOnline) return false;

    try {
      const result = await trpc.config.global.getUserConfig.query({ config_name: configName });
      
      if (result.success && result.data) {
        // 更新本地数据库
        const globalConfigService = await Services.globalAppConfig();
        await globalConfigService.updateUserConfig(user!.id, {
          theme_mode: result.data.theme_mode,
          language: result.data.language,
          accessibility: result.data.accessibility,
          notifications_enabled: result.data.notifications_enabled,
          sound_enabled: result.data.sound_enabled
        });
        
        setConfig(result.data);
        setLastSyncTime(new Date());
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error syncing from cloud:', err);
      return false;
    }
  }, [isAuthenticated, isOnline, configName, user]);

  /**
   * 同步配置到云端
   */
  const syncToCloud = useCallback(async (): Promise<boolean> => {
    if (!isAuthenticated || !isOnline || !config) return false;

    try {
      const result = await trpc.config.global.updateUserConfig.mutate({
        config_name: configName,
        theme_mode: config.theme_mode,
        language: config.language,
        accessibility: config.accessibility,
        notifications_enabled: config.notifications_enabled,
        sound_enabled: config.sound_enabled
      });
      
      if (result.success) {
        setLastSyncTime(new Date());
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error syncing to cloud:', err);
      return false;
    }
  }, [isAuthenticated, isOnline, config, configName]);

  /**
   * 更新配置
   */
  const updateConfig = useCallback(async (updates: UpdateGlobalAppConfigInput): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      setIsLoading(true);
      
      // 更新本地数据库
      const globalConfigService = await Services.globalAppConfig();
      const result = await globalConfigService.updateUserConfig(user.id, updates);
      
      if (result.success && result.data) {
        setConfig(result.data);
        setError(null);
        
        // 如果在线，尝试同步到云端
        if (isOnline && isAuthenticated) {
          await syncToCloud();
        }
        
        return true;
      } else {
        setError(result.error || 'Failed to update config');
        return false;
      }
    } catch (err) {
      console.error('Error updating config:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, isOnline, isAuthenticated, syncToCloud]);

  /**
   * 重置为默认配置
   */
  const resetToDefault = useCallback(async (): Promise<boolean> => {
    return await updateConfig({
      theme_mode: 'system',
      language: 'zh-CN',
      accessibility: JSON.stringify({
        high_contrast: false,
        large_text: false,
        reduce_motion: false,
        screen_reader_support: false
      }),
      notifications_enabled: true,
      sound_enabled: true
    });
  }, [updateConfig]);

  /**
   * 刷新配置
   */
  const refreshConfig = useCallback(async () => {
    setIsLoading(true);
    
    // 优先从云端同步，失败则从本地加载
    const syncSuccess = await syncFromCloud();
    if (!syncSuccess) {
      await loadLocalConfig();
    }
    
    setIsLoading(false);
  }, [syncFromCloud, loadLocalConfig]);

  // 初始化加载
  useEffect(() => {
    if (user?.id) {
      refreshConfig();
    }
  }, [user?.id, refreshConfig]);

  // 网络状态变化时尝试同步
  useEffect(() => {
    if (isOnline && isAuthenticated && config) {
      syncToCloud();
    }
  }, [isOnline, isAuthenticated, config, syncToCloud]);

  return {
    // 状态
    config,
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    
    // 操作
    updateConfig,
    resetToDefault,
    syncToCloud,
    refreshConfig,
    
    // 便捷访问器
    themeMode: config?.theme_mode || 'system',
    language: config?.language || 'zh-CN',
    notificationsEnabled: config?.notifications_enabled ?? true,
    soundEnabled: config?.sound_enabled ?? true,
    accessibilityConfig
  };
};
