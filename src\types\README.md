# 类型系统架构文档

本文档详细说明了项目的类型系统架构，包括基础实体、API 类型、可翻译实体以及在仓储和服务中的使用方式。

## 📋 目录

- [架构概览](#架构概览)
- [基础实体类型 (base.ts)](#基础实体类型-basets)
- [API 类型 (api.ts)](#api-类型-apits)
- [可翻译实体系统 (translation.ts)](#可翻译实体系统-translationts)
- [在仓储中的使用](#在仓储中的使用)
- [在服务中的使用](#在服务中的使用)
- [命名约定](#命名约定)
- [最佳实践](#最佳实践)

## 🏗️ 架构概览

项目采用分层的类型系统架构，确保类型安全、可维护性和可扩展性：

```
src/types/schema/
├── base.ts          # 基础实体类型（数据库表结构）
├── api.ts           # API 输入输出类型（服务层需求）
├── translation.ts   # 可翻译实体扩展类型
├── generator.ts     # 类型生成工具
├── index.ts         # 统一导出
└── validation-test.ts # 验证测试
```

### 设计原则

1. **SQL 优先** - 基础类型以 SQL 表结构为准
2. **分层清晰** - 基础类型、API 类型、扩展类型分离
3. **类型安全** - 使用 Zod 进行运行时验证
4. **向后兼容** - 渐进式迁移，保持兼容性
5. **统一管理** - 集中定义，避免重复

## 📊 基础实体类型 (base.ts)

基础实体类型直接映射数据库表结构，使用下划线命名约定。

### 核心实体

#### 用户相关
```typescript
// 用户 Schema（基于 users 表）
export const UserSchema = z.object({
  id: IdSchema,
  username: z.string().min(1).max(50),
  email: z.string().email().optional(),
  password_hash: z.string().optional(),
  display_name: z.string().max(100).optional(),
  avatar_url: z.string().url().optional(),
  is_vip: z.boolean().default(false),
  vip_expires_at: TimestampSchema.optional(),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  last_login_at: TimestampSchema.optional()
});

export type User = z.infer<typeof UserSchema>;
```

#### 情绪相关
```typescript
// 情绪 Schema（基于 emotions 表）
export const EmotionSchema = z.object({
  id: IdSchema,
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  emoji: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  tier_level: z.number().int().min(1).max(5).optional(),
  parent_id: OptionalIdSchema,
  keywords: z.string().optional(), // JSON array
  image_url: z.string().url().optional(),
  created_at: TimestampSchema,
  updated_at: TimestampSchema,
  created_by: OptionalIdSchema,
  updated_by: OptionalIdSchema,
  is_deleted: z.boolean().default(false),
  deleted_at: TimestampSchema.optional()
});

export type Emotion = z.infer<typeof EmotionSchema>;
```

### 翻译相关基础类型

#### 翻译表 Schema
```typescript
// 情绪翻译 Schema（基于 emotion_translations 表）
export const EmotionTranslationSchema = z.object({
  emotion_id: IdSchema,
  language_code: LanguageCodeSchema,
  translated_name: z.string().min(1),
  translated_description: z.string().optional()
});

// UI 标签翻译 Schema（基于 ui_label_translations 表）
export const UILabelTranslationSchema = z.object({
  id: IdSchema,
  label_key: z.string().min(1),
  language_code: LanguageCodeSchema,
  translated_text: z.string().min(1),
  created_at: TimestampSchema,
  updated_at: TimestampSchema
});
```

#### 支持的语言代码
```typescript
// 严格的语言代码类型（支持 15 种语言）
export const LanguageCodeSchema = z.enum([
  'en',    // English
  'zh',    // Chinese (Simplified)
  'zh-TW', // Chinese (Traditional)
  'ja',    // Japanese
  'ko',    // Korean
  'es',    // Spanish
  'fr',    // French
  'de',    // German
  'it',    // Italian
  'pt',    // Portuguese
  'ru',    // Russian
  'ar',    // Arabic
  'hi',    // Hindi
  'th',    // Thai
  'vi'     // Vietnamese
]);

export type LanguageCode = z.infer<typeof LanguageCodeSchema>;
```

## 🔌 API 类型 (api.ts)

API 类型定义服务层的输入输出需求，包括创建、更新、过滤等操作。

### Repository 数据类型

#### 创建数据类型
```typescript
// 创建情绪数据 Schema（用于 Repository 层）
export const CreateEmotionDataSchema = z.object({
  id: IdSchema,
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  emoji: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  tier_level: z.number().int().min(1).max(5).optional(),
  parent_id: OptionalIdSchema,
  keywords: z.string().optional(), // JSON string
  image_url: z.string().url().optional(),
  created_by: OptionalIdSchema
});

export type CreateEmotionData = z.infer<typeof CreateEmotionDataSchema>;
```

#### 更新数据类型
```typescript
// 更新情绪数据 Schema（用于 Repository 层）
export const UpdateEmotionDataSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  emoji: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  tier_level: z.number().int().min(1).max(5).optional(),
  parent_id: OptionalIdSchema,
  keywords: z.string().optional(),
  image_url: z.string().url().optional(),
  updated_by: OptionalIdSchema
});

export type UpdateEmotionData = z.infer<typeof UpdateEmotionDataSchema>;
```

#### 过滤器类型
```typescript
// 情绪过滤器 Schema（用于查询）
export const EmotionFilterSchema = z.object({
  languageCode: z.string().optional(),
  searchTerm: z.string().optional(),
  tier_level: z.number().int().min(1).max(5).optional(),
  parent_id: OptionalIdSchema,
  color: z.string().optional(),
  has_parent: z.boolean().optional(),
  is_deleted: z.boolean().optional(),
  created_by: OptionalIdSchema,
  limit: z.number().int().positive().optional(),
  offset: z.number().int().min(0).optional(),
  orderBy: z.enum(['name', 'tier_level', 'created_at', 'updated_at']).optional(),
  orderDirection: z.enum(['ASC', 'DESC']).optional()
});

export type EmotionFilter = z.infer<typeof EmotionFilterSchema>;
```

### Service 输入类型

#### 创建输入类型
```typescript
// 创建情绪输入 Schema（用于 Service 层）
export const CreateEmotionInputSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  emoji: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).optional(),
  tier_level: z.number().int().min(1).max(5).optional(),
  parent_id: OptionalIdSchema,
  keywords: z.array(z.string().max(50)).max(20).optional(),
  image_url: z.string().url().optional(),
  created_by: OptionalIdSchema,
  // 支持翻译输入
  translations: z.array(z.object({
    language_code: z.string(),
    translated_name: z.string().optional(),
    translated_description: z.string().optional()
  })).optional()
});

export type CreateEmotionInput = z.infer<typeof CreateEmotionInputSchema>;
```

## 🌍 可翻译实体系统 (translation.ts)

可翻译实体系统提供统一的多语言支持功能。

### 核心翻译类型

#### 翻译接口
```typescript
// 通用翻译接口 Schema
export const TranslationSchema = z.object({
  entityId: z.string().min(1),
  languageCode: LanguageCodeSchema,
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional()
});

export type Translation = z.infer<typeof TranslationSchema>;
```

#### 可翻译实体基础类型
```typescript
// 可翻译实体基础 Schema
export const TranslatableEntitySchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1), // 默认语言名称
  description: z.string().optional(), // 默认语言描述
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),

  // 运行时翻译字段（由服务层填充）
  localizedName: z.string().optional(), // 当前语言的名称
  localizedDescription: z.string().optional(), // 当前语言的描述
  translations: z.array(TranslationSchema).optional() // 所有翻译
});

export type TranslatableEntity = z.infer<typeof TranslatableEntitySchema>;
```

### 翻译输入类型

#### 翻译输入 Schema
```typescript
// 翻译输入 Schema（用于创建和更新）
export const TranslationInputSchema = z.object({
  languageCode: LanguageCodeSchema,
  translatedName: z.string().min(1),
  translatedDescription: z.string().optional()
});

export type TranslationInput = z.infer<typeof TranslationInputSchema>;
```

#### 可翻译创建输入
```typescript
// 可翻译创建输入 Schema
export const TranslatableCreateInputSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  translations: z.array(TranslationInputSchema).optional()
});

export type TranslatableCreateInput = z.infer<typeof TranslatableCreateInputSchema>;
```

### 翻译查询和过滤

#### 翻译过滤器
```typescript
// 翻译查询过滤器 Schema
export const TranslationFilterSchema = z.object({
  languageCode: LanguageCodeSchema.optional(),
  searchTerm: z.string().optional(),
  limit: z.number().int().min(1).max(1000).default(100),
  offset: z.number().int().min(0).default(0),
  orderBy: z.string().optional(),
  orderDirection: z.enum(['ASC', 'DESC']).default('ASC')
});

export type TranslationFilter = z.infer<typeof TranslationFilterSchema>;
```

## 🗄️ 在仓储中的使用

### TranslatableRepository 基类

```typescript
import {
  type Translation,
  type TranslatableEntity,
  type TranslationFilter,
  type LanguageCode
} from '../../types/schema/translation';

export abstract class TranslatableRepository<
  T extends TranslatableEntity,
  created_ata,
  updated_ata
> extends BaseRepository<T, created_ata, updated_ata> {

  protected translationTableName: string;
  protected foreignKeyName: string;

  constructor(tableName: string, translationTableName: string, foreignKeyName: string) {
    super(tableName);
    this.translationTableName = translationTableName;
    this.foreignKeyName = foreignKeyName;
  }

  // 获取带翻译的实体
  async findByIdWithTranslations(
    context: DatabaseContext,
    id: string,
    languageCode?: LanguageCode
  ): Promise<T | null> {
    // 实现多语言查询逻辑
  }

  // 搜索实体（支持多语言）
  async searchWithTranslations(
    context: DatabaseContext,
    searchTerm: string,
    languageCode?: LanguageCode,
    limit: number = 10
  ): Promise<T[]> {
    // 实现多语言搜索逻辑
  }

  // 添加或更新翻译
  async upsertTranslation(
    context: DatabaseContext,
    entityId: string,
    languageCode: LanguageCode,
    translatedName: string,
    translatedDescription?: string
  ): Promise<void> {
    // 实现翻译更新逻辑
  }
}
```

### 具体 Repository 实现

```typescript
// 导入统一的类型定义
import { type Emotion } from '../../types/schema/base';
import {
  type CreateEmotionData,
  type UpdateEmotionData,
  type EmotionFilter
} from '../../types/schema/api';

// Repository 专用的情绪实体类型（扩展基础类型）
export interface EmotionRepositoryEntity extends TranslatableEntity {
  id: string;
  name: string;
  description?: string;
  emoji?: string;
  color?: string;
  tier_level?: number;
  parent_id?: string;
  keywords?: string; // JSON array of keywords
  image_url?: string;
  created_by?: string;
  updated_by?: string;
  is_deleted: boolean;
  deleted_at?: string;

  // 关联数据（运行时填充）
  children?: EmotionRepositoryEntity[];
  parent?: EmotionRepositoryEntity;
}

export class EmotionRepository extends TranslatableRepository<
  EmotionRepositoryEntity,
  CreateEmotionData,
  UpdateEmotionData
> {
  constructor() {
    super('emotions', 'emotion_translations', 'emotion_id');
  }

  // 映射数据库行到实体
  protected mapRowToEntity(row: any): EmotionRepositoryEntity {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      // ... 其他字段映射
      localizedName: row.localized_name || row.name,
      localizedDescription: row.localized_description || row.description
    };
  }

  // 获取情绪层级树（支持多语言）
  async getEmotionTree(
    context: DatabaseContext,
    languageCode?: LanguageCode
  ): Promise<EmotionRepositoryEntity[]> {
    // 实现层级树查询逻辑
  }
}
```

## 🔧 在服务中的使用

### TranslatableService 基类

```typescript
import {
  type TranslationInput,
  type TranslatableCreateInput,
  type TranslatableUpdateInput,
  type LanguageCode
} from '../../types/schema/translation';

export abstract class TranslatableService<
  T extends TranslatableEntity,
  CreateInput extends TranslatableCreateInput,
  UpdateInput extends TranslatableUpdateInput
> extends BaseService<T, CreateInput, UpdateInput> {

  protected translatableRepository: TranslatableRepository<T, any, any>;

  // 获取本地化实体
  async getLocalized(id: string, languageCode?: LanguageCode): Promise<ServiceResult<T>> {
    try {
      const context = await this.getDatabaseContext();
      const entity = await this.translatableRepository.findByIdWithTranslations(
        context,
        id,
        languageCode
      );

      if (!entity) {
        return this.createErrorResult('NOT_FOUND', 'Entity not found');
      }

      return this.createSuccessResult(entity);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // 搜索实体（支持多语言）
  async search(
    searchTerm: string,
    languageCode?: LanguageCode,
    limit: number = 10
  ): Promise<ServiceResult<T[]>> {
    try {
      if (!searchTerm || searchTerm.trim().length === 0) {
        return this.createErrorResult('VALIDATION_ERROR', 'Search term is required');
      }

      const context = await this.getDatabaseContext();
      const entities = await this.translatableRepository.searchWithTranslations(
        context,
        searchTerm.trim(),
        languageCode,
        limit
      );

      return this.createSuccessResult(entities);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // 添加或更新翻译
  async upsertTranslation(
    entityId: string,
    languageCode: LanguageCode,
    translatedName: string,
    translatedDescription?: string
  ): Promise<ServiceResult<void>> {
    try {
      // 验证输入
      if (!translatedName || translatedName.trim().length === 0) {
        return this.createErrorResult('VALIDATION_ERROR', 'Translated name is required');
      }

      // 验证实体是否存在
      const entityExists = await this.getById(entityId);
      if (!entityExists.success) {
        return this.createErrorResult('NOT_FOUND', 'Entity not found');
      }

      const context = await this.getDatabaseContext();
      await this.translatableRepository.upsertTranslation(
        context,
        entityId,
        languageCode,
        translatedName.trim(),
        translatedDescription?.trim()
      );

      return this.createSuccessResult(undefined);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // 批量更新翻译
  async batchUpdateTranslations(
    entityId: string,
    translations: TranslationInput[]
  ): Promise<ServiceResult<void>> {
    try {
      // 验证翻译数据
      const translationErrors = this.validateTranslations(translations);
      if (translationErrors.length > 0) {
        return this.createErrorResult('VALIDATION_ERROR', translationErrors.join('; '));
      }

      // 验证实体是否存在
      const entityExists = await this.getById(entityId);
      if (!entityExists.success) {
        return this.createErrorResult('NOT_FOUND', 'Entity not found');
      }

      await this.handleTranslations(entityId, translations);
      return this.createSuccessResult(undefined);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // 获取翻译完成度统计
  async getTranslationStats(): Promise<ServiceResult<Array<{
    languageCode: string;
    totalEntities: number;
    translatedEntities: number;
    completionPercentage: number;
  }>>> {
    try {
      const context = await this.getDatabaseContext();
      const stats = await this.translatableRepository.getTranslationStats(context);
      return this.createSuccessResult(stats);
    } catch (error) {
      return this.handleError(error);
    }
  }
}
```

### 具体 Service 实现

```typescript
// 导入统一的类型定义
import { type Emotion } from '../../types/schema/base';
import {
  type CreateEmotionInput,
  type UpdateEmotionInput
} from '../../types/schema/api';

export class EmotionService extends TranslatableService<
  Emotion,
  CreateEmotionInput,
  UpdateEmotionInput
> {

  private emotionRepository: EmotionRepository;

  constructor() {
    const repository = new EmotionRepository();
    super(repository);
    this.emotionRepository = repository;
  }

  // 验证创建数据
  protected async validateCreate(data: CreateEmotionInput): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // 基础验证
    if (!data.name || data.name.trim().length === 0) {
      errors.push('Name is required');
    }

    if (data.name && data.name.length > 100) {
      errors.push('Name must be less than 100 characters');
    }

    // 使用基类的翻译验证
    const translationErrors = this.validateTranslations(data.translations);
    errors.push(...translationErrors);

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 创建情绪（支持翻译）
  async create(data: CreateEmotionInput): Promise<ServiceResult<Emotion>> {
    try {
      // 验证数据
      const validation = await this.validateCreate(data);
      if (!validation.isValid) {
        return this.createErrorResult('VALIDATION_ERROR', validation.errors.join('; '));
      }

      // 转换为 Repository 数据格式
      const created_ata: CreateEmotionData = {
        id: uuidv4(),
        name: data.name.trim(),
        description: data.description?.trim(),
        emoji: data.emoji,
        color: data.color,
        tier_level: data.tier_level,
        parent_id: data.parent_id,
        keywords: data.keywords ? JSON.stringify(data.keywords) : undefined,
        image_url: data.image_url,
        created_by: data.created_by
      };

      // 调用基类创建方法
      const result = await super.create(created_ata as any);

      if (result.success && result.data && data.translations) {
        await this.handleTranslations(result.data.id, data.translations);

        // 重新获取包含翻译的情绪
        const updatedResult = await this.getLocalized(result.data.id);
        if (updatedResult.success) {
          return updatedResult;
        }
      }

      return result;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // 获取情绪层级树（支持多语言）
  async getEmotionTree(languageCode?: LanguageCode): Promise<ServiceResult<Emotion[]>> {
    try {
      const context = await this.getDatabaseContext();
      const tree = await this.emotionRepository.getEmotionTree(context, languageCode);
      return this.createSuccessResult(tree);
    } catch (error) {
      return this.handleError(error);
    }
  }
}
```

## 📝 命名约定

### 数据库字段命名
- 使用下划线分隔 (`snake_case`)
- 示例：`user_id`, `created_at`, `is_deleted`

### TypeScript 类型命名
- 接口和类型使用 PascalCase
- 示例：`User`, `CreateEmotionInput`, `TranslatableEntity`

### Schema 命名
- Schema 名称以 `Schema` 结尾
- 示例：`UserSchema`, `CreateEmotionInputSchema`

### 文件组织
```
src/types/schema/
├── base.ts          # 基础实体类型（数据库映射）
├── api.ts           # API 输入输出类型
├── translation.ts   # 可翻译实体扩展
└── index.ts         # 统一导出
```

## 🎯 最佳实践

### 1. 类型导入
```typescript
// ✅ 推荐：从统一入口导入
import {
  type User,
  type CreateEmotionInput,
  type TranslatableEntity
} from '../../types/schema';

// ❌ 避免：直接从具体文件导入
import { User } from '../../types/schema/base';
```

### 2. Repository 实现
```typescript
// ✅ 推荐：使用统一的类型定义
import {
  type CreateEmotionData,
  type UpdateEmotionData
} from '../../types/schema/api';

export class EmotionRepository extends TranslatableRepository<
  EmotionRepositoryEntity,
  CreateEmotionData,
  UpdateEmotionData
> {
  // 实现
}

// ❌ 避免：内嵌接口定义
export class EmotionRepository {
  interface created_ata {
    // 内嵌定义
  }
}
```

### 3. Service 实现
```typescript
// ✅ 推荐：继承 TranslatableService
export class EmotionService extends TranslatableService<
  Emotion,
  CreateEmotionInput,
  UpdateEmotionInput
> {
  // 自动获得多语言支持
}

// ❌ 避免：重复实现翻译逻辑
export class EmotionService extends BaseService {
  // 手动实现翻译功能
}
```

### 4. 翻译处理
```typescript
// ✅ 推荐：使用严格的 LanguageCode 类型
async getLocalized(id: string, languageCode?: LanguageCode) {
  // 类型安全的语言代码
}

// ❌ 避免：使用 string 类型
async getLocalized(id: string, languageCode?: string) {
  // 可能传入无效的语言代码
}
```

### 5. 错误处理
```typescript
// ✅ 推荐：使用 ServiceResult 模式
async create(data: CreateInput): Promise<ServiceResult<Entity>> {
  try {
    // 业务逻辑
    return this.createSuccessResult(result);
  } catch (error) {
    return this.handleError(error);
  }
}

// ❌ 避免：直接抛出异常
async create(data: CreateInput): Promise<Entity> {
  // 直接抛出异常，不利于错误处理
  throw new Error('Something went wrong');
}
```

## 🔍 类型验证

### 运行时验证
```typescript
// 使用 Zod Schema 进行运行时验证
const result = CreateEmotionInputSchema.safeParse(inputData);
if (!result.success) {
  console.error('Validation errors:', result.error.issues);
}
```

### 编译时检查
```typescript
// TypeScript 编译时类型检查
const emotion: Emotion = {
  id: '123',
  name: 'Happy',
  // TypeScript 会检查所有必需字段
};
```

## 📚 相关文档

- [数据库设计文档](../../docs/database-design.md)
- [多语言系统文档](../../docs/internationalization.md)
- [API 设计规范](../../docs/api-design.md)
- [测试指南](../../docs/testing-guide.md)

---

通过遵循这个类型系统架构，我们确保了：
- **类型安全**：编译时和运行时的双重保障
- **可维护性**：集中管理，避免重复定义
- **可扩展性**：易于添加新的实体和功能
- **国际化支持**：完整的多语言基础设施
- **开发体验**：更好的 IDE 支持和错误提示