1.数据库层（SQL） ✅
-- 主表：存储默认语言内容
CREATE TABLE emotions (id, name, description, ...);

-- 翻译表：存储多语言翻译
CREATE TABLE emotion_translations (
  emotion_id, language_code, translated_name, translated_description
);

2.Schema 层（类型定义） ✅

// 基础实体 Schema (base.ts)
export const EmotionSchema = z.object({...});
export const EmotionTranslationSchema = z.object({...});

// 扩展翻译功能 (translation.ts)
export const TranslatableEntitySchema = z.object({...});
export const TranslationFilterSchema = z.object({...});

// API 数据类型 (api.ts)
export const CreateEmotionInputSchema = z.object({...});

3.Repository 层（数据访问） ✅
// 统一的翻译仓储基类
export abstract class TranslatableRepository<T, created_ata, updated_ata> {
  // 支持多语言的 CRUD 操作
  async findByIdWithTranslations(id, languageCode?: LanguageCode)
  async searchWithTranslations(searchTerm, languageCode?: LanguageCode)
  async upsertTranslation(entityId, languageCode: LanguageCode, ...)
}
4.Service 层（业务逻辑） ✅

// 统一的翻译服务基类
export abstract class TranslatableService<T, CreateInput, UpdateInput> {
  // 支持多语言的业务操作
  async getLocalized(id, languageCode?: LanguageCode)
  async search(searchTerm, languageCode?: LanguageCode)
  async upsertTranslation(entityId, languageCode: LanguageCode, ...)
}

// Repository 层使用
const emotion = await emotionRepo.findByIdWithTranslations(
  context, 
  'happy', 
  'zh' // 严格的 LanguageCode 类型
);

// Service 层使用
const result = await emotionService.getLocalized('happy', 'zh');
const searchResults = await emotionService.search('快乐', 'zh');

// 翻译管理
await emotionService.upsertTranslation(
  'happy',
  'zh', // 严格的 LanguageCode 类型
  '快乐',
  '一种积极的情绪状态'
);