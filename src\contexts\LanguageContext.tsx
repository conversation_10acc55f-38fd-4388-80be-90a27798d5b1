import { Services } from '@/services/ServiceFactoryFixed';
import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  type ReactNode,
  useRef,
} from 'react';
import { useSQLiteDB } from '../lib/useSqLite';

export type Language = 'en' | 'zh';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, string>) => string;
  uiLabels: Record<string, string>;
  labelsLoading?: boolean;
  isLanguageReady?: boolean;
  forceReloadTranslations?: () => void;
}

const defaultLanguage: Language = 'en';

const LanguageContext = createContext<LanguageContextType>({
  language: defaultLanguage,
  setLanguage: () => {},
  t: (key) => key,
  uiLabels: {},
  labelsLoading: true,
  isLanguageReady: false,
});

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, _setLanguage] = useState<Language>(defaultLanguage);
  const [translations, setTranslations] = useState<Record<Language, Record<string, string>>>({
    en: {},
    zh: {},
  });
  const [uiLabels, setUiLabels] = useState<Record<string, string>>({});
  const [labelsLoading, setLabelsLoading] = useState<boolean>(true);
  const [isLanguageReady, _setIsLanguageReady] = useState<boolean>(false);

  // Use refs to track state without triggering re-renders
  const loadingRef = useRef<boolean>(true);
  const dbFetchAttemptedRef = useRef<boolean>(false);
  const dbFetchSuccessRef = useRef<boolean>(false);
  const lastFetchTimeRef = useRef<number>(0);
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  console.log(
    `[LanguageContext] Initializing Provider. Default language: '${defaultLanguage}', initial isLanguageReady: ${false}`
  );

  const { dbConnection, isDatabaseInitialised } = useSQLiteDB();

  const setLanguage = (newLang: Language) => {
    console.log(
      `[LanguageContext] setLanguage called with '${newLang}'. Current language: '${language}', isLanguageReady: ${isLanguageReady}`
    );

    if (language !== newLang) {
      console.log(`[LanguageContext] Language changing from '${language}' to '${newLang}'.`);

      // 不再设置isLanguageReady为false，避免在切换语言过程中显示未翻译的键名
      // setIsLanguageReady(false);
      // console.log(`[LanguageContext] Set isLanguageReady to false while changing language.`);

      // 设置新语言
      _setLanguage(newLang);

      // 记录翻译状态
      console.log(
        `[LanguageContext] Translation state: uiLabels keys: ${Object.keys(uiLabels).length}, translations keys for ${newLang}: ${translations[newLang] ? Object.keys(translations[newLang]).length : 0}`
      );

      // 检查是否已经加载过这种语言的翻译
      if (
        jsonImportCacheRef.current[newLang] &&
        translations[newLang] &&
        Object.keys(translations[newLang]).length > 0
      ) {
        console.log(
          `[LanguageContext] Already have translations for '${newLang}', setting isLanguageReady to true immediately.`
        );
        setIsLanguageReady(true);
      } else {
        console.log(
          `[LanguageContext] Don't have translations for '${newLang}' yet, isLanguageReady will be set by loadJSONTranslations effect.`
        );

        // 设置一个安全超时，确保即使翻译加载失败，isLanguageReady也会被设置为true
        setTimeout(() => {
          if (!isLanguageReady) {
            console.log(
              '[LanguageContext] Safety timeout: Setting isLanguageReady to true after language change.'
            );
            setIsLanguageReady(true);
          }
        }, 2000);
      }
    } else {
      console.log(`[LanguageContext] Language already '${newLang}'. No change.`);

      // 即使语言没有变化，也确保isLanguageReady为true
      if (!isLanguageReady) {
        console.log(
          '[LanguageContext] Language unchanged but isLanguageReady is false. Setting to true.'
        );
        setIsLanguageReady(true);
      }
    }
  };

  const setIsLanguageReady = (ready: boolean) => {
    console.log(
      `[LanguageContext] setIsLanguageReady called with ${ready}. Current isLanguageReady: ${isLanguageReady}.`
    );
    if (isLanguageReady !== ready) {
      console.log(`[LanguageContext] isLanguageReady changed from ${isLanguageReady} to ${ready}.`);
      _setIsLanguageReady(ready);
    } else {
      console.log(`[LanguageContext] isLanguageReady already ${ready}. No change.`);
    }
  };

  useEffect(() => {
    console.log(
      `[LanguageContext][useEffect[]] Attempting to load language from user config. Current language state: '${language}', isLanguageReady: ${isLanguageReady}`
    );

    // 从服务层加载语言偏好
    const loadLanguagePreference = async () => {
      try {
        // 只有在数据库初始化完成后才尝试访问服务层
        if (!isDatabaseInitialised) {
          console.log('[LanguageContext][useEffect[]] Database not initialized, using localStorage fallback');
          // 回退到 localStorage 作为临时方案
          const savedLanguage = localStorage.getItem('language') as Language;
          if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
            setLanguage(savedLanguage);
          }
          setIsLanguageReady(true);
          return;
        }

        const userConfigService = Services.getUserConfigService();
        const configResult = await userConfigService.getUserConfigs('default-user');

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          const userConfig = configResult.data[0];
          // 假设用户配置中有语言偏好字段
          const savedLanguage = userConfig.language_preference as Language;
          if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
            setLanguage(savedLanguage);
            console.log(`[LanguageContext][useEffect[]] Loaded language '${savedLanguage}' from user config.`);
          } else {
            console.log(
              `[LanguageContext][useEffect[]] No valid language preference in user config, using current state: '${language}'.`
            );
          }
        } else {
          console.log(
            `[LanguageContext][useEffect[]] No user config found, using current state: '${language}'.`
          );
        }
      } catch (error) {
        console.error('[LanguageContext][useEffect[]] Failed to load language preference from user config:', error);
        // 回退到 localStorage 作为临时方案
        const savedLanguage = localStorage.getItem('language') as Language;
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
          setLanguage(savedLanguage);
        }
      }

      setIsLanguageReady(true);
    };

    loadLanguagePreference();
  }, [isDatabaseInitialised]); // Re-run when database initialization status changes

  // Debounced function to load labels from database
  const debouncedLoadDBLabels = (lang: Language) => {
    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    // Set a new timeout
    fetchTimeoutRef.current = setTimeout(async () => {
      // Check if we've recently fetched (within last 2 seconds)
      const now = Date.now();
      if (now - lastFetchTimeRef.current < 2000) {
        console.log(
          `[LanguageContext][debouncedLoadDBLabels] Skipping fetch, last fetch was ${now - lastFetchTimeRef.current}ms ago.`
        );
        return;
      }

      console.log(
        `[LanguageContext][debouncedLoadDBLabels] Executing debounced fetch for '${lang}'.`
      );
      lastFetchTimeRef.current = now;
      loadingRef.current = true;
      setLabelsLoading(true);

      try {
        // Only proceed if we have a database connection
        if (!dbConnection?.current) {
          console.log('[LanguageContext][debouncedLoadDBLabels] No database connection available.');
          return;
        }

        dbFetchAttemptedRef.current = true;

        // Retry logic for database access
        let retries = 0;
        const maxRetries = 2; // Reduced from 3 to 2
        let labelsFromDB = {};

        while (retries < maxRetries) {
          try {
            // 使用新的服务架构获取UI标签
            // 注意：UILabelService不支持按语言获取，使用findAll替代
            const uiLabelService = Services.getUILabelService();
            const result = await uiLabelService.findAll();
            if (result.success && result.data) {
              labelsFromDB = result.data.reduce(
                (acc: Record<string, string>, label: any) => {
                  // UILabel结构：key, default_text, category, description
                  acc[label.key] = label.default_text || label.key;
                  return acc;
                },
                {} as Record<string, string>
              );
            }

            // If we got labels, break out of retry loop
            if (Object.keys(labelsFromDB).length > 0) {
              console.log(
                `[LanguageContext][debouncedLoadDBLabels] Successfully loaded ${Object.keys(labelsFromDB).length} labels from DB for '${lang}' on attempt ${retries + 1}.`
              );
              dbFetchSuccessRef.current = true;
              break;
            }

            // If we got no labels but no error was thrown, wait and retry
            console.log(
              `[LanguageContext][debouncedLoadDBLabels] No labels found on attempt ${retries + 1}. Retrying...`
            );
            await new Promise((resolve) => setTimeout(resolve, 300)); // Reduced from 500ms to 300ms
            retries++;
          } catch (retryError) {
            console.warn(
              `[LanguageContext][debouncedLoadDBLabels] Error on attempt ${retries + 1}:`,
              retryError
            );
            await new Promise((resolve) => setTimeout(resolve, 300)); // Reduced from 500ms to 300ms
            retries++;

            // On last retry, rethrow to be caught by outer catch
            if (retries >= maxRetries) throw retryError;
          }
        }

        // Only update state if we have new labels and they're different from current
        if (Object.keys(labelsFromDB).length > 0) {
          const currentLabelsStr = JSON.stringify(uiLabels);
          const newLabelsStr = JSON.stringify(labelsFromDB);

          if (currentLabelsStr !== newLabelsStr) {
            console.log('[LanguageContext][debouncedLoadDBLabels] Labels changed, updating state.');
            setUiLabels(labelsFromDB);
          } else {
            console.log(
              '[LanguageContext][debouncedLoadDBLabels] Labels unchanged, skipping state update.'
            );
          }
        }
      } catch (error) {
        console.error(
          '[LanguageContext][debouncedLoadDBLabels] Error fetching UI labels from DB after retries:',
          error
        );
        // Don't clear existing labels if we had any
        if (Object.keys(uiLabels).length === 0) {
          setUiLabels({});
        }
      } finally {
        loadingRef.current = false;
        setLabelsLoading(false);
      }
    }, 100); // 100ms debounce time
  };

  useEffect(() => {
    console.log(
      `[LanguageContext][loadDBLabels Effect] Triggered. States: language='${language}', isDBInit=${isDatabaseInitialised}, isLangReady=${isLanguageReady}, hasDBConn=${!!dbConnection?.current}`
    );

    // Only proceed if language is ready and we have a database connection
    if (isLanguageReady && dbConnection?.current) {
      console.log(
        '[LanguageContext][loadDBLabels Effect] Conditions met. Triggering debounced fetch.'
      );
      debouncedLoadDBLabels(language);
    } else {
      console.log('[LanguageContext][loadDBLabels Effect] Conditions NOT met. Skipping fetch.');
    }

    // Cleanup function to clear any pending timeout
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
        fetchTimeoutRef.current = null;
      }
    };
  }, [language, isDatabaseInitialised, isLanguageReady]); // Removed dbConnection and uiLabels from dependencies

  // JSON translations cache ref to avoid unnecessary imports
  const jsonImportCacheRef = useRef<Record<Language, boolean>>({
    en: false,
    zh: false,
  });

  // Translation cache ref to improve performance
  const translationCacheRef = useRef<Record<string, string>>({});

  // 跟踪JSON翻译加载状态
  const isLoadingRef = useRef<boolean>(false);

  // Separate effect for loading JSON translations
  useEffect(() => {
    // Skip if language is not ready
    if (!isLanguageReady) {
      console.log(
        '[LanguageContext][loadJSONTranslations] Language not ready, skipping JSON load.'
      );
      return;
    }

    // 在开发模式下，始终重新加载翻译，以便测试
    if (
      import.meta.env.MODE === 'production' &&
      jsonImportCacheRef.current[language] &&
      Object.keys(uiLabels).length > 0
    ) {
      console.log(
        `[LanguageContext][loadJSONTranslations] Already loaded JSON for '${language}' and have DB labels, skipping.`
      );
      return;
    }

    // 在测试模式下，清除翻译缓存
    if (import.meta.env.MODE === 'test') {
      console.log(
        '[LanguageContext][loadJSONTranslations] Test mode detected, clearing translation cache.'
      );
      jsonImportCacheRef.current[language] = false;
      translationCacheRef.current = {};
    }

    const loadJSONTranslations = async () => {
      // 如果已经在加载中，直接返回
      if (isLoadingRef.current) {
        console.log(
          '[LanguageContext][loadJSONTranslations] JSON translations loading already in progress. Skipping.'
        );
        return;
      }

      console.log(
        `[LanguageContext][loadJSONTranslations] Loading JSON translations for '${language}'. Current isLanguageReady: ${isLanguageReady}`
      );

      // 标记为正在加载
      isLoadingRef.current = true;

      // 不再设置isLanguageReady为false，避免在加载过程中显示未翻译的键名
      // setIsLanguageReady(false);
      // console.log(`[LanguageContext][loadJSONTranslations] Set isLanguageReady to false while loading translations.`);

      // Add a safety timeout to prevent infinite loading
      const safetyTimeout = setTimeout(() => {
        console.log(
          '[LanguageContext][loadJSONTranslations] SAFETY TIMEOUT: Translation loading took too long. Setting isLanguageReady=true anyway.'
        );
        setIsLanguageReady(true);
        isLoadingRef.current = false;
      }, 3000); // 3 second safety timeout (reduced from 5 seconds)

      try {
        // Only import if we haven't already or don't have translations for this language
        if (
          !jsonImportCacheRef.current[language] ||
          !translations[language] ||
          Object.keys(translations[language]).length === 0
        ) {
          console.log(
            `[LanguageContext][loadJSONTranslations] Importing JSON file for '${language}'...`
          );

          // Use Promise.race to add timeout to import
          const importPromise = import(`../locales/${language}.json`);
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Import timeout')), 3000)
          );

          const localeTranslations = await Promise.race([importPromise, timeoutPromise]);
          console.log(
            '[LanguageContext][loadJSONTranslations] Import successful, updating translations state...'
          );

          setTranslations((prev) => ({
            ...prev,
            [language]: localeTranslations.default,
          }));

          jsonImportCacheRef.current[language] = true;
          console.log(
            `[LanguageContext][loadJSONTranslations] Successfully loaded translations from JSON for '${language}'. Sample keys:`,
            Object.keys(localeTranslations.default).slice(0, 5)
          );
        } else {
          console.log(
            `[LanguageContext][loadJSONTranslations] Using cached JSON translations for '${language}'. Sample keys:`,
            translations[language] ? Object.keys(translations[language]).slice(0, 5) : []
          );
        }

        // Clear the safety timeout since we completed successfully
        clearTimeout(safetyTimeout);

        // 加载完成后设置isLanguageReady为true
        setIsLanguageReady(true);
        console.log(
          '[LanguageContext][loadJSONTranslations] Set isLanguageReady to true after loading translations.'
        );

        // 重置加载状态
        isLoadingRef.current = false;
      } catch (error) {
        console.error(
          `[LanguageContext][loadJSONTranslations] Failed to load translations from JSON for '${language}':`,
          error
        );
        // Don't clear existing translations if we had any
        if (!translations[language] || Object.keys(translations[language]).length === 0) {
          setTranslations((prev) => ({
            ...prev,
            [language]: {},
          }));
        }

        // Clear the safety timeout since we completed with error
        clearTimeout(safetyTimeout);

        // 即使加载失败，也设置isLanguageReady为true，以便使用fallback
        setIsLanguageReady(true);
        console.log(
          '[LanguageContext][loadJSONTranslations] Set isLanguageReady to true despite loading failure.'
        );

        // 重置加载状态
        isLoadingRef.current = false;
      }
    };

    // Load JSON translations
    loadJSONTranslations();

    // Persist language choice to user config through service layer
    const persistLanguagePreference = async () => {
      try {
        console.log(
          `[LanguageContext][loadJSONTranslations] Persisting language '${language}' to user config.`
        );

        // 只有在数据库初始化完成后才尝试访问服务层
        if (!isDatabaseInitialised) {
          console.log('[LanguageContext][loadJSONTranslations] Database not ready, using localStorage fallback');
          localStorage.setItem('language', language);
          return;
        }

        const userConfigService = Services.getUserConfigService();
        const configResult = await userConfigService.getUserConfigs('default-user');

        if (configResult.success && configResult.data && configResult.data.length > 0) {
          const userConfig = configResult.data[0];
          await userConfigService.update(userConfig.id, {
            language_preference: language
          });
          console.log(`[LanguageContext][loadJSONTranslations] Successfully persisted language '${language}' to user config.`);
        }
      } catch (error) {
        console.error('[LanguageContext][loadJSONTranslations] Failed to persist language preference to user config:', error);
        // 回退到 localStorage 作为临时方案
        localStorage.setItem('language', language);
      }
    };

    persistLanguagePreference();
  }, [language, isLanguageReady]); // Removed uiLabels, labelsLoading, and translations from dependencies

  // Translation function with debug logging (cache disabled for testing)
  // Memoize the translation function to prevent unnecessary re-renders
  const t = React.useCallback(
    (key: string, params?: Record<string, string>) => {
      // 在测试模式下禁用缓存，以便测试
      if (import.meta.env.MODE !== 'test' && !params) {
        // Check cache first
        const cacheKey = `${language}:${key}`;
        if (translationCacheRef.current[cacheKey]) {
          return translationCacheRef.current[cacheKey];
        }
      }

      // First try to get text from database labels
      let text = uiLabels[key];
      let source = text ? 'db' : 'none';

      // If not found in database, try JSON translations for current language
      if (!text && translations[language] && translations[language][key]) {
        text = translations[language][key];
        source = 'json';
      }

      // If still not found, use hardcoded fallbacks for critical UI elements
      if (!text) {
        // Hardcoded fallbacks for critical navigation elements
        const fallbacks: Record<string, Record<'en' | 'zh', string>> = {
          'app.home': { en: 'Home', zh: '首页' },
          'app.history': { en: 'History', zh: '历史' },
          'app.analytics': { en: 'Analytics', zh: '分析' },
          'app.settings': { en: 'Settings', zh: '设置' },
          'settings.language': { en: 'Language', zh: '语言' },
          'settings.language.english': { en: 'English', zh: 'English' },
          'settings.language.chinese': { en: 'Chinese', zh: '中文' },
        };

        if (fallbacks[key]?.[language]) {
          text = fallbacks[key][language];
          source = 'fallback';
        } else {
          text = key;
          source = 'key';
        }

        // Log missing translations only in development mode
        if (import.meta.env.MODE === 'development') {
          console.log(
            `[LanguageContext][t] Translation not found for key '${key}' in language '${language}'. Using ${source}.`
          );
        }
      }

      // Replace any parameters in the text
      if (params) {
        Object.keys(params).forEach((paramKey) => {
          text = text.replace(`{{${paramKey}}}`, params[paramKey]);
        });
      }
      // 在非测试模式下缓存翻译结果
      else if (import.meta.env.MODE !== 'test') {
        // Cache the result if no params
        const cacheKey = `${language}:${key}`;
        translationCacheRef.current[cacheKey] = text;
      }

      // Only log in development mode to reduce console noise
      if (import.meta.env.MODE === 'development') {
        // Log important translation requests for debugging
        const importantKeys = [
          'app.home',
          'app.history',
          'app.analytics',
          'app.settings',
          'settings.language',
        ];
        if (importantKeys.includes(key)) {
          console.log(
            `[LanguageContext][t] Translating '${key}' to '${text}' (${source}) for language '${language}', isLanguageReady: ${isLanguageReady}`
          );
          console.log(
            `[LanguageContext][t] Debug for '${key}': uiLabels has key: ${key in uiLabels}, translations has key: ${translations[language] && key in translations[language]}`
          );
        }
      }

      return text;
    },
    [language, uiLabels, translations, isLanguageReady]
  );

  // 强制重新加载翻译
  const forceReloadTranslations = () => {
    console.log(
      `[LanguageContext][forceReloadTranslations] Forcing reload of translations for '${language}'`
    );
    jsonImportCacheRef.current[language] = false;
    translationCacheRef.current = {};

    // 重新加载翻译
    setIsLanguageReady(false);
    setTimeout(() => {
      setIsLanguageReady(true);
    }, 100);
  };

  return (
    <LanguageContext.Provider
      value={{
        language,
        setLanguage,
        t,
        uiLabels,
        labelsLoading,
        isLanguageReady,
        forceReloadTranslations,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);
