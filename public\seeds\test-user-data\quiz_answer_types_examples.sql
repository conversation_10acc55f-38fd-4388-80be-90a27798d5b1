-- ==================== 各类Quiz答案类型示例数据 ====================
-- 展示不同quiz类型的选项配置方式
-- 创建时间: 2024-12-19

-- 清理现有数据
DELETE FROM quiz_question_options WHERE question_id LIKE 'demo-%';
DELETE FROM quiz_questions WHERE id LIKE 'demo-%';
DELETE FROM quiz_packs WHERE id LIKE 'demo-%';

-- ==================== 示例1: 选择类Quiz ====================

-- 创建选择类Quiz包
INSERT INTO quiz_packs (
    id, name, category, quiz_type, quiz_style, difficulty_level,
    quiz_logic_config, metadata, is_active, created_at
) VALUES (
    'demo-choice-quiz', '选择类Quiz示例', 'education', 'knowledge_quiz', 'mainstream', 'regular',
    '{"question_flow":{"type":"sequential"},"time_limit":60}',
    '{"estimated_duration_minutes":10,"tags":["示例","选择题","教育"]}',
    1, datetime('now')
);

-- 单选题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-single-choice', 'demo-choice-quiz', '中国的首都是哪里？', 'single_choice', 1, datetime('now')
);

-- 单选题选项
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type, option_order, scoring_value, created_at
) VALUES
    ('demo-sc-opt1', 'demo-single-choice', '北京', 'beijing', 'choice', 1, 1, datetime('now')),
    ('demo-sc-opt2', 'demo-single-choice', '上海', 'shanghai', 'choice', 2, 0, datetime('now')),
    ('demo-sc-opt3', 'demo-single-choice', '广州', 'guangzhou', 'choice', 3, 0, datetime('now')),
    ('demo-sc-opt4', 'demo-single-choice', '深圳', 'shenzhen', 'choice', 4, 0, datetime('now'));

-- 多选题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-multiple-choice', 'demo-choice-quiz', '以下哪些是中国的直辖市？', 'multiple_choice', 2, datetime('now')
);

-- 多选题选项
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type, option_order, scoring_value, created_at
) VALUES
    ('demo-mc-opt1', 'demo-multiple-choice', '北京', 'beijing', 'choice', 1, 1, datetime('now')),
    ('demo-mc-opt2', 'demo-multiple-choice', '上海', 'shanghai', 'choice', 2, 1, datetime('now')),
    ('demo-mc-opt3', 'demo-multiple-choice', '广州', 'guangzhou', 'choice', 3, 0, datetime('now')),
    ('demo-mc-opt4', 'demo-multiple-choice', '天津', 'tianjin', 'choice', 4, 1, datetime('now')),
    ('demo-mc-opt5', 'demo-multiple-choice', '重庆', 'chongqing', 'choice', 5, 1, datetime('now'));

-- 图片选择题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-image-choice', 'demo-choice-quiz', '请选择正确的国旗', 'image_choice', 3, datetime('now')
);

-- 图片选择选项
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type, option_order,
    media_url, media_type, media_alt_text, scoring_value, created_at
) VALUES
    ('demo-ic-opt1', 'demo-image-choice', '中国国旗', 'china_flag', 'choice', 1,
     '/images/flags/china.png', 'image', '中华人民共和国国旗', 1, datetime('now')),
    ('demo-ic-opt2', 'demo-image-choice', '美国国旗', 'usa_flag', 'choice', 2,
     '/images/flags/usa.png', 'image', '美利坚合众国国旗', 0, datetime('now')),
    ('demo-ic-opt3', 'demo-image-choice', '日本国旗', 'japan_flag', 'choice', 3,
     '/images/flags/japan.png', 'image', '日本国旗', 0, datetime('now'));

-- ==================== 示例2: 评分类Quiz ====================

-- 创建评分类Quiz包
INSERT INTO quiz_packs (
    id, name, category, quiz_type, quiz_style, difficulty_level,
    quiz_logic_config, metadata, is_active, created_at
) VALUES (
    'demo-rating-quiz', '评分类Quiz示例', 'assessment', 'personality_test', 'mainstream', 'regular',
    '{"question_flow":{"type":"category_based"},"scoring":{"method":"likert_scale"}}',
    '{"estimated_duration_minutes":15,"tags":["示例","评分","心理测评"]}',
    1, datetime('now')
);

-- 量表评分题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-scale-rating', 'demo-rating-quiz', '我经常感到焦虑', 'scale_rating', 1, datetime('now')
);

-- 5点量表选项
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type, option_order, scoring_value, created_at
) VALUES
    ('demo-sr-opt1', 'demo-scale-rating', '完全不同意', 'strongly_disagree', 'scale_point', 1, 1, datetime('now')),
    ('demo-sr-opt2', 'demo-scale-rating', '不同意', 'disagree', 'scale_point', 2, 2, datetime('now')),
    ('demo-sr-opt3', 'demo-scale-rating', '中立', 'neutral', 'scale_point', 3, 3, datetime('now')),
    ('demo-sr-opt4', 'demo-scale-rating', '同意', 'agree', 'scale_point', 4, 4, datetime('now')),
    ('demo-sr-opt5', 'demo-scale-rating', '完全同意', 'strongly_agree', 'scale_point', 5, 5, datetime('now'));

-- 滑块评分题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-slider', 'demo-rating-quiz', '您对当前生活的满意度如何？(0-100)', 'slider', 2, datetime('now')
);

-- 滑块配置
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type,
    min_value, max_value, step_value, option_order, created_at
) VALUES (
    'demo-slider-opt1', 'demo-slider', '满意度滑块', 'satisfaction_slider', 'scale_point',
    0, 100, 1, 1, datetime('now')
);

-- ==================== 示例3: 排序类Quiz ====================

-- 创建排序类Quiz包
INSERT INTO quiz_packs (
    id, name, category, quiz_type, quiz_style, difficulty_level,
    quiz_logic_config, metadata, is_active, created_at
) VALUES (
    'demo-ranking-quiz', '排序类Quiz示例', 'assessment', 'survey', 'mainstream', 'regular',
    '{"question_flow":{"type":"single_page"},"interaction":{"drag_drop":true}}',
    '{"estimated_duration_minutes":8,"tags":["示例","排序","价值观"]}',
    1, datetime('now')
);

-- 排序题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-ranking', 'demo-ranking-quiz', '请按重要性排序以下价值观（最重要的排在第一位）', 'ranking', 1, datetime('now')
);

-- 排序项
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type, option_order, created_at
) VALUES
    ('demo-rank-opt1', 'demo-ranking', '家庭', 'family', 'ranking_item', 1, datetime('now')),
    ('demo-rank-opt2', 'demo-ranking', '事业', 'career', 'ranking_item', 2, datetime('now')),
    ('demo-rank-opt3', 'demo-ranking', '健康', 'health', 'ranking_item', 3, datetime('now')),
    ('demo-rank-opt4', 'demo-ranking', '财富', 'wealth', 'ranking_item', 4, datetime('now')),
    ('demo-rank-opt5', 'demo-ranking', '友谊', 'friendship', 'ranking_item', 5, datetime('now')),
    ('demo-rank-opt6', 'demo-ranking', '自由', 'freedom', 'ranking_item', 6, datetime('now'));

-- ==================== 示例4: 矩阵类Quiz ====================

-- 创建矩阵类Quiz包
INSERT INTO quiz_packs (
    id, name, category, quiz_type, quiz_style, difficulty_level,
    quiz_logic_config, metadata, is_active, created_at
) VALUES (
    'demo-matrix-quiz', '矩阵类Quiz示例', 'assessment', 'survey', 'mainstream', 'advanced',
    '{"question_flow":{"type":"matrix_grid"},"display":{"compact_mode":true}}',
    '{"estimated_duration_minutes":12,"tags":["示例","矩阵","满意度调查"]}',
    1, datetime('now')
);

-- 矩阵题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-matrix', 'demo-matrix-quiz', '请评价我们服务的各个方面', 'matrix', 1, datetime('now')
);

-- 矩阵行（评价项目）
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type, matrix_row_id, option_order, created_at
) VALUES
    ('demo-matrix-row1', 'demo-matrix', '产品质量', 'product_quality', 'matrix_row', 'row_1', 1, datetime('now')),
    ('demo-matrix-row2', 'demo-matrix', '客户服务', 'customer_service', 'matrix_row', 'row_2', 2, datetime('now')),
    ('demo-matrix-row3', 'demo-matrix', '价格合理性', 'price_reasonableness', 'matrix_row', 'row_3', 3, datetime('now')),
    ('demo-matrix-row4', 'demo-matrix', '交付速度', 'delivery_speed', 'matrix_row', 'row_4', 4, datetime('now'));

-- 矩阵列（评分等级）
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type, matrix_column_id, scoring_value, option_order, created_at
) VALUES
    ('demo-matrix-col1', 'demo-matrix', '很差', 'very_poor', 'matrix_column', 'col_1', 1, 1, datetime('now')),
    ('demo-matrix-col2', 'demo-matrix', '较差', 'poor', 'matrix_column', 'col_2', 2, 2, datetime('now')),
    ('demo-matrix-col3', 'demo-matrix', '一般', 'average', 'matrix_column', 'col_3', 3, 3, datetime('now')),
    ('demo-matrix-col4', 'demo-matrix', '较好', 'good', 'matrix_column', 'col_4', 4, 4, datetime('now')),
    ('demo-matrix-col5', 'demo-matrix', '很好', 'excellent', 'matrix_column', 'col_5', 5, 5, datetime('now'));

-- ==================== 数据验证 ====================

-- 验证各类Quiz包
SELECT 'Quiz Packs Created:' AS info;
SELECT id, name, quiz_type, quiz_style FROM quiz_packs WHERE id LIKE 'demo-%';

-- 验证问题类型
SELECT 'Question Types:' AS info;
SELECT pack_id, question_type, COUNT(*) as question_count
FROM quiz_questions
WHERE pack_id LIKE 'demo-%'
GROUP BY pack_id, question_type;

-- 验证选项类型
SELECT 'Option Types:' AS info;
SELECT
    qq.pack_id,
    qq.question_type,
    qqo.option_type,
    COUNT(*) as option_count
FROM quiz_question_options qqo
JOIN quiz_questions qq ON qqo.question_id = qq.id
WHERE qq.pack_id LIKE 'demo-%'
GROUP BY qq.pack_id, qq.question_type, qqo.option_type
ORDER BY qq.pack_id, qq.question_type, qqo.option_type;

-- ==================== 示例5: 输入类Quiz ====================

-- 创建输入类Quiz包
INSERT INTO quiz_packs (
    id, name, category, quiz_type, quiz_style, difficulty_level,
    quiz_logic_config, metadata, is_active, created_at
) VALUES (
    'demo-input-quiz', '输入类Quiz示例', 'assessment', 'mixed', 'alternative', 'regular',
    '{"question_flow":{"type":"adaptive"},"validation":{"real_time":true}}',
    '{"estimated_duration_minutes":20,"tags":["示例","输入","创意测试"]}',
    1, datetime('now')
);

-- 文本输入题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-text-input', 'demo-input-quiz', '请用三个词描述您理想中的工作环境', 'text_input', 1, datetime('now')
);

-- 文本输入约束
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type,
    metadata, option_order, created_at
) VALUES (
    'demo-text-constraint', 'demo-text-input', '文本输入约束', 'text_constraint', 'input_constraint',
    '{"input_type":"text","max_length":200,"min_words":3,"max_words":3,"required":true,"placeholder":"请输入三个词，用空格分隔..."}',
    1, datetime('now')
);

-- 数字输入题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-number-input', 'demo-input-quiz', '您的年龄是？', 'number_input', 2, datetime('now')
);

-- 数字输入约束
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type,
    min_value, max_value, metadata, option_order, created_at
) VALUES (
    'demo-number-constraint', 'demo-number-input', '年龄输入约束', 'age_constraint', 'input_constraint',
    13, 120, '{"input_type":"number","required":true,"placeholder":"请输入您的年龄"}',
    1, datetime('now')
);

-- 日期输入题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-date-input', 'demo-input-quiz', '您最难忘的一天是什么时候？', 'date_input', 3, datetime('now')
);

-- 日期输入约束
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type,
    metadata, option_order, created_at
) VALUES (
    'demo-date-constraint', 'demo-date-input', '日期输入约束', 'date_constraint', 'input_constraint',
    '{"input_type":"date","min_date":"1900-01-01","max_date":"2024-12-31","required":true}',
    1, datetime('now')
);

-- 文件上传题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-file-upload', 'demo-input-quiz', '请上传一张能代表您个性的图片', 'file_upload', 4, datetime('now')
);

-- 文件上传约束
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type,
    metadata, option_order, created_at
) VALUES (
    'demo-file-constraint', 'demo-file-upload', '文件上传约束', 'file_constraint', 'input_constraint',
    '{"allowed_types":["image/jpeg","image/png","image/gif"],"max_size_mb":5,"required":false}',
    1, datetime('now')
);

-- 绘图题
INSERT INTO quiz_questions (
    id, pack_id, question_text, question_type, question_order, created_at
) VALUES (
    'demo-drawing', 'demo-input-quiz', '请画出您心目中的理想房屋', 'drawing', 5, datetime('now')
);

-- 绘图约束
INSERT INTO quiz_question_options (
    id, question_id, option_text, option_value, option_type,
    metadata, option_order, created_at
) VALUES (
    'demo-drawing-constraint', 'demo-drawing', '绘图约束', 'drawing_constraint', 'input_constraint',
    '{"canvas_width":800,"canvas_height":600,"tools":["pen","eraser","shapes"],"colors":["black","red","blue","green"],"max_time_minutes":10}',
    1, datetime('now')
);

SELECT '=== 各类Quiz答案类型示例完成 ===' AS status;
SELECT '支持选择、评分、排序、矩阵、输入等多种答案类型' AS features;
