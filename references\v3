```markdown
**Pinnacle Gamified App Prototype Generator (User-Focused - Functionality-Rich - PRD-Independent - Research-Augmented - Immersive Game UI - Unified Style)** - **Ultimate Edition for Maximum User Engagement, Expanded Features, and Captivating Game UI - Outputs app.html & Detailed Design Specification - Single Cohesive Style - APEX EDITION**

**Objective:** Even with a **potentially incomplete `prd.md` product requirements document (lacking detail or comprehensive information)**, the AI will **proactively investigate and furnish essential details, and autonomously expand features based on user needs.** The primary objective is to architect a **supremely user-friendly** and **deeply immersive gamified application prototype** (choice of iOS or Android), **maximizing user engagement, *driving subscription conversion rates*, and amplifying overall product value.**  This necessitates **seamlessly integrating mobile user experience best practices and the highest standards of game UI design**, adopting proven strategies from leading applications and games, **guaranteeing universal compatibility across mainstream mobile devices**, and delivering a production-ready **App prototype in HTML (`app.html`) accompanied by a comprehensive App Design Specification (`app-design-brief.md`)**. A single, platform-agnostic app prototype is sufficient, representing the pinnacle of product design excellence. **This exceptionally enhanced version is built upon insights demonstrating that user-centric design, intuitive user journeys, a dedicated focus on core features, and consistent brand messaging are key to boosting app interaction and user satisfaction.  GAME UI STYLES are now central, ensuring unparalleled user immersion and enjoyment. These principles are the core foundation of this design blueprint.** The final deliverables are a fully functional `app.html` file and a detailed design specification document, both meticulously crafted in **a single, cohesive Game UI style**. **This prototype MUST incorporate language selection (within settings) and a leaderboard (added if not in PRD), optimizing user experience and gamification.**

**Core Philosophy:** The AI's approach goes beyond simply following the PRD. It will conduct independent, in-depth market research, develop detailed user personas (specifically for gaming audiences), and perform rigorous competitive analysis (focusing on game app UI/UX). Furthermore, it will deeply learn mobile user experience best practices and **game UI design expertise**. This proactive methodology, combined with **User-Focused Design Principles, Targeted Feature Sets, and Seamless Game UI Style Integration**, compensates for any PRD shortcomings. The AI is authorized to autonomously expand or refine app features based on its analysis, ensuring the prototype is supremely user-friendly, deeply engaging, and designed for maximum user participation. A universal app prototype design will perfectly adapt to mainstream mobile devices and will be deeply aligned with the characteristics of gamified apps and **modern mobile user expectations for both applications and game UI/UX**.  When the PRD lacks information or design improvements are needed, **user experience and superior game UI design will be the primary guiding principles**. This is achieved by **directly applying User-Centric Design strategies, Core Feature Prioritization methodologies, and a strong focus on Intuitive Navigation, Brand Consistency, and seamless Game UI Style Integration**. Drawing upon mobile design standards, user behavior psychology, brand identity, user retention techniques, and **insights from mobile user research, app conversion studies, and Game UI/UX research**, the AI will make design choices optimized for user satisfaction and product value. **A streamlined approach, focusing on essential app features and intuitive user flows, will be prioritized to maximize user engagement, as supported by research. The generated App prototype (`app.html`) MUST be intuitive to navigate, prioritize core user needs and the product's value proposition, and incorporate a cohesive and captivating Game UI style.** **The prototype will include easy-to-use language switching within the settings and a leaderboard (if not already in PRD), enhancing global accessibility and competitive engagement.**

**App Structure and Navigation Blueprint:**

To ensure the app prototype is well-organized and easy to navigate, please clearly define the following structural elements. This is crucial for the AI to understand the app's overall architecture and create a prototype that meets user expectations.

*   **Primary Navigation Pages:** List the main sections of your app, typically accessed through a bottom navigation bar, sidebar, or top tabs. For example:
    *   Home Dashboard
    *   Game Center
    *   Missions
    *   Profile
    *   Settings
    *   Store
    *   … (Add any other primary pages relevant to your app)

*   **Secondary Pages:** List the pages users will access by tapping buttons, cards, or other interactive elements within the primary pages. Clearly indicate the primary page and element that leads to each secondary page. For example:
    *   **From Home Dashboard:**
        *   **Onboarding Sequence:** (For first-time users or initial app launch)
        *   **Subscription Paywall:** (For accessing premium features, if applicable)
        *   **Event Details Page:** (Accessed by tapping an event card)
    *   **From Game Center:**
        *   **Game Details Page:** (Accessed by selecting a game from the list)
        *   **Leaderboard Details:** (Accessed via a leaderboard button or link)
    *   **From Missions:**
        *   **Mission Details Page:** (Accessed by selecting a mission card)
    *   **From Profile:**
        *   **Edit Profile Page:** (Accessed via an "Edit Profile" button)
        *   **Share Profile/Artwork Page:** (Accessed via a "Share" button)
        *   **Achievements Page:** (Accessed via an "Achievements" link)
    *   **From Settings:**
        *   **Language Selection Page:** (Accessed via a "Language" option)
        *   **Help & Support Page:** (Accessed via "Help & Support")
        *   **About Us Page:** (Accessed via "About Us")
    *   … (Add any other secondary pages, detailing the primary page and element that links to them. Be as detailed as possible in describing the page hierarchy and navigation flow.)

**Providing a detailed map of your app's structure and navigation is essential. This will help the AI create a more complete and user-friendly app prototype.**

**Game UI Style Directives (Implementation Guide for AI):**

**(User-Centric Design Principles & Core Feature Focus - [Maintain previously defined principles])**

*   **Game UI Style Selection and Integration:** The AI must select one or more of the following game UI styles, deeply integrating the chosen style(s) into the App prototype (`app.html`) design to amplify game immersion and visual appeal. The AI must comprehensively justify its Game UI style selection within the `app-design-brief.md` document, explaining its relevance to the game product's theme, genre, and target audience. **If a Game UI style is explicitly specified by the user, the AI MUST IMPLEMENT THAT SPECIFIC STYLE without deviation. If no Game UI style is specified, the AI MUST CHOOSE AND IMPLEMENT ONE OR A COMBINATION OF THE FOLLOWING GAME UI STYLES IN THE APP PROTOTYPE (`app.html`), AND JUSTIFY ITS CHOICE AND IMPLEMENTATION IN THE `app-design-brief.md` DOCUMENT, EXPLAINING ITS RELEVANCE TO THE GAME PRODUCT'S THEME, GENRE, AND TARGET AUDIENCE.**

    **Detailed Game UI Style Descriptions & Reference Cases:**

    **10. Game UI（游戏界面）魔幻风格 (Magic Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Dark, mysterious colors (deep blue, dark green, dark purple, dark red), with high-saturation accent colors (gold, bright blue, emerald green) for magical effects. Optional: element-based color schemes (fire: red, orange, black; ice: light blue, white, silver). Keywords: Dark Fantasy, Mystical, Magical, High Contrast Descriptive Note: Evokes ancient spellbooks, enchanted forests, powerful artifacts.
        *   **Decoration:**
            *   Magic Elements: Spell circles (complex geometric patterns, glowing lines), runes (ancient文字, carved effect, animated), magical light effects (particles, beams, auras). Keywords: Magic Circles, Runes, Spell Effects Descriptive Note: Conveys power and mystery.
            *   Ornate Metal Borders: Carved gold edges, bronze borders, jewel-encrusted borders. Optional: stylized leather borders, bone borders (savage, primal magic). Keywords: Ornate Metal, Jewel-Encrusted, Leather, Bone Descriptive Note: Border design reflects UI element power or origin.
            *   Parchment Texture Backgrounds: Aged effect, stains, wrinkles, static texture or subtle animation (scrolling, burning edges). Optional: magic-type backgrounds (starry sky, mist, lava). Keywords: Parchment, Aged Paper, Scroll, Stellar, Volcanic Descriptive Note: Evokes ancient knowledge, forgotten power.
        *   **Typography:** Classic serif fonts, handwritten fonts, gothic fonts (legibility important!). Glowing outlines or shadows can be used. Keywords: Serif Fonts, Gothic Fonts, Illuminated Text Descriptive Note: Legible yet evocative of a bygone era.
        *   **Layout:** Circular layouts (spell circle center), layered layouts (creating depth of field), symmetrical layouts (enhancing ritualistic feel), asymmetrical layouts (increasing mystery). Keywords: Circular Layout, Layered, Asymmetrical, Symmetrical Descriptive Note: Arrangement suggests power, balance, or enigma.
        *   **Interaction Effects:**
            *   Particle Effects: Magical particles (floating, rotating, gathering), color and shape changes based on element type (sparks, ice crystals, stardust). Linked to button clicks and spell casting. Keywords: Particle Effects, Sparkle, Dust, Fire, Ice Descriptive Note: Enhances impact of player actions.
            *   Animation: Spell casting animations (slow motion, zoom, rotation), energy wave effects (breathing light effect, pulsating glow), radiant transitions (soft halo diffusion, holy light effect). Keywords: Slow Motion, Pulsing Glow, Radiant Transitions Descriptive Note: Dramatic and visually stunning animations.
            *   Hints/Feedback: Runes glow on hover, energy burst animation on click, parchment scroll effect when flipping through inventory. Keywords: Reactive Elements, Energetic FX Descriptive Note: UI should respond and engage the user.
        *   **Sound Design:** Echoing sound effects, mystical whispers, chanting sounds. Keywords: Echoing Sounds, Mystical Whispers Descriptive Note: Sound is key to creating an immersive atmosphere.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《原神》(Genshin Impact)**
            *   **Visual Characteristics:** Color scheme mainly dark tones, gold and bright blue emphasize magic. Carved metal borders, parchment texture backgrounds. Classic serif fonts, glowing outlines.
            *   **Magic Element Decoration:** Spell circles, magical light effect skill icons, special effects.
            *   **Interaction Effects:** Particle effects, radiant animations for skill casting.
        *   **Case 2 (iOS/Android): 《暗黑破坏神：不朽》(Diablo Immortal)**
            *   **Visual Characteristics:** Dark fantasy style, color scheme leans towards dark red, dark green, and dark purple. Bronze borders inlaid with宝石, gothic fonts, shadows.
            *   **Decoration:** Skill and item bar borders convey a strong sense of magic and power.
            *   **Interaction Effects:** Energy burst and particle effects when clicking skills or items.
        *   **Case 3 (iOS/Android): 《万象物语》(Sdorica Sunset)**
            *   **Visual Characteristics:** Although overall leaning towards a picture book style, magical elements are incorporated into skill releases and some UI elements. Color scheme uses darker colors, with highlights and special textures to express the mystery of magic.
            *   **Decoration:** Runes and magical light effects are visible in skill icons and animations.
            *   **Interaction Effects:** Unique magical animation effects for skill releases.

    **11. Game UI（游戏界面）科技风格 (Tech Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Neon light effects (bright blue, pink, green, purple), cool-toned backgrounds (black, dark gray, silver), high contrast color scheme. Optional: company/faction based color schemes. Keywords: Neon, Futuristic, High-Tech, Corporate Branding Descriptive Note: Draws inspiration from circuit boards, virtual reality, streamlined design.
        *   **Decoration:**
            *   Geometric Lines: Precise straight lines, sharp angles, triangles, used to separate information or as background patterns. Keywords: Geometric Lines, Sharp Angles, Triangles Descriptive Note: Sharp edges and clean lines for a sense of precision.
            *   Holographic Effects: Semi-transparent floating interfaces, blurred backgrounds, simulates 3D depth. Optional: Glitchy holograms (enhancing cyberpunk feel). Keywords: Holographic, Translucent, Glitch, Cyperpunk Descriptive Note: Depth and complexity with overlapping semi-transparent layers.
            *   Data Stream Animations: Rapidly scrolling code, numbers, letters, emphasizing information transfer. Keywords: Data Streams, Code Scrolling Descriptive Note: Constant information transfer is key.
            *   Binary code, circuit board textures, warning signs. Keywords: Binary Code, Circuit Board, Caution Symbols Descriptive Note: UI grounded in building blocks of technology.
        *   **Typography:** Sans-serif fonts (clear, modern), Mono fonts (code style). Glowing effects or scanline effects can be used. Keywords: Sans-Serif, Mono Fonts Descriptive Note: Legible and conveys technological accuracy.
        *   **Layout:** Grid layouts (high information density), modular layouts (customizable), asymmetrical layouts (highlighting key points). Keywords: Grid Layout, Modular, Asymmetrical Descriptive Note: Efficiency, customizability, and focus are important.
        *   **Interaction Effects:**
            *   Animation: Electronic scanlines (horizontal or vertical scanning, highlighting information), glitch art effects (brief image distortion, color shifting), data loading animations (progress bars, circular loaders). Keywords: Scanning Lines, Glitch Effects, Loading Bars Descriptive Note: UI should highlight and emphasize information.
            *   Hints/Feedback: Circuit boards light up on hover, data transmission animations on click, data streams accelerate on scroll. Keywords: Reactive Elements, Data Transmission FX Descriptive Note: User experience should be engaging and stimulating.
            *   Energy Charging Effects: Energy bar filling animations, pulsating glows, used to indicate skill cooldowns or resource acquisition. Keywords: Energy Charging, Pulsing Glow Descriptive Note: Reinforce important in-game events.
        *   **Sound Design:** Electronic beeping sounds, mechanical sound effects, data transmission sounds. Keywords: Electronic Sounds, Mechanical Effects Descriptive Note: Sound design mirrors the visuals.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《崩坏：星穹铁道》(Honkai: Star Rail)**
            *   **Visual Characteristics:** Overall UI is simple and modern, color scheme is mainly cool-toned dark gray and silver, with bright blue and purple neon colors as accents. Geometric lines and data stream animations are prominent.
            *   **Decoration:** Interface borders and elements are often separated by precise geometric lines.
            *   **Interaction Effects:** Skill releases and interface transitions are often accompanied by electronic scanlines and data loading animations.
        *   **Case 2 (iOS/Android): 《赛博朋克 2077: 边缘行者》**
            *   **Visual Characteristics:** Strong neon light effects, glitch art effects, and holographic projection feel. High-contrast color schemes, often using bright pink, bright green, and bright blue.
            *   **Decoration:** Simulated glitchy holograms and warning signs are common in the interface.
            *   **Interaction Effects:** Expected UI interactions should include glitch art effects and data transmission animations.
        *   **Case 3 (iOS/Android): 《明日方舟》(Arknights)**
            *   **Visual Characteristics:** Although there are some military elements, the overall UI leans towards a futuristic tech feel. Color scheme is mainly dark, with high-brightness blue and orange accents. Geometric lines and modular layouts are clear.
            *   **Decoration:** Interface elements often use simple geometric shapes and lines for decoration.
            *   **Interaction Effects:** Button clicks and skill releases have obvious tech-style animations, such as energy charging and light effects.

    **12. Game UI（游戏界面）东方风格 (Eastern Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Chinese color system (primary colors: red, yellow, blue, white, black; secondary colors: green, purple, pink, etc.), elegant ink wash tones, gold and red used for emphasis. Keywords: Chinese Colors, Ink Wash, Calligraphic Color Descriptive Note: Balance cultural legacy and modern design.
        *   **Decoration:**
            *   Ink Wash Landscapes: Elegant landscape backgrounds, ink wash effects, dynamically generated. Keywords: Ink Wash Landscape, Dynamic Imagery Descriptive Note: Give depth and atmosphere to the interface.
            *   Traditional Motifs: Auspicious cloud patterns, 回纹 patterns, dragon patterns, phoenix patterns, used for borders and backgrounds. Optional: motifs based on historical dynasties (e.g.,牡丹纹 of the Tang Dynasty). Keywords: Traditional Motifs, Clouds, Dragons, Phoenix Descriptive Note: Represent heritage and give the interface richness.
            *   Calligraphic Fonts: Brushstroke style, readability needs to be considered, avoid excessive artistic stylization. Keywords: Calligraphic Fonts, Brushstrokes Descriptive Note: Balance artistic beauty and readability.
            *   Lanterns, window frames, Chinese knots. Keywords: Lanterns, Window Frames, Chinese Knots Descriptive Note: Bring in traditional imagery.
        *   **Layout:** Symmetrical layouts (balance, stability), whitespace design (Zen-like), layered layouts (creating depth of field). Keywords: Symmetrical, Balanced Layout Descriptive Note: Mirror the principles of Chinese art in the UI.
        *   **Interaction Effects:**
            *   Animation: Ink wash animations (ink diffusion effect), scroll unrolling effects (slowly unrolling to reveal content), ribbon flow effects (soft飘动, adding dynamism), brush writing animations (simulating handwriting). Keywords: Ink Wash Animation, Scroll Unrolling Descriptive Note: Create a sense of fluid motion.
            *   Hints/Feedback: Ink wash effect on hover, scroll unrolling on click, ribbon flow on scroll. Keywords: Reactive Ink, Interactive Motion Descriptive Note: Respond and engage the user.
        *   **Sound Design:** Chinese musical instruments such as guzheng and dizi, flowing water sounds, bird鸣 sounds. Keywords: Traditional Instruments, Water Sounds Descriptive Note: Recreate the feeling of being surrounded by natural sounds.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《阴阳师》(Onmyoji)**
            *   **Visual Characteristics:** Heavily uses traditional Chinese colors, such as red and gold as accent colors. Ink wash style backgrounds and纹样 decorations, such as auspicious clouds and 回纹 patterns. Use of calligraphic fonts.
            *   **Decoration:** Interface borders and backgrounds often feature ink wash landscapes and traditional纹样 decorations.
            *   **Interaction Effects:** Interface transitions and skill releases are often accompanied by ink wash diffusion and scroll unrolling animations.
        *   **Case 2 (iOS/Android): 《剑网3：指尖江湖》(JX3 Mobile)**
            *   **Visual Characteristics:** Strong martial arts style, elegant color scheme, often using elegant ink wash tones and Chinese colors. Traditional纹样 and wood-like textures are visible in UI borders and decorations.
            *   **Decoration:** Traditional elements such as auspicious clouds and window frames are common in the interface.
            *   **Interaction Effects:** Some operations have animations similar to scroll unrolling and ink wash diffusion.
        *   **Case 3 (iOS/Android): 《忘川风华录》(Tale of Food)**
            *   **Visual Characteristics:** Integrates the style of ancient Chinese paintings into the UI design, with a simple and elegant color scheme. Interface decorations feature traditional纹样 and ink wash elements.
            *   **Decoration:** Background and border designs have a strong cultural atmosphere.
            *   **Interaction Effects:** Some interface transitions and operations have ink diffusion visual effects.

    **13. Game UI（游戏界面）休闲风格 (Casual Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Bright and lively color schemes (candy colors, macaron colors), high saturation colors, avoid being too glaring. Keywords: Candy Colors, Macaron Colors, High Saturation Descriptive Note: Think of a brightly-lit toy store or ice cream parlor.
        *   **Decoration:**
            *   Rounded Graphic Elements: Rounded rectangles, circles, ellipses. Keywords: Rounded Shapes, Smooth Edges Descriptive Note: Make everything approachable and inviting.
            *   Cartoon Style Icons: Cute images, exaggerated proportions. Keywords: Cartoonish Icons, Cute Characters Descriptive Note: Use exaggerated features to enhance the charm.
            *   Simple Geometric Decorations: Polka dots, stripes, stars. Keywords: Polka Dots, Stripes, Stars Descriptive Note: Simple visual enhancements add appeal.
            *   Soft shadows, gradient colors. Keywords: Soft Shadows, Gradient Colors Descriptive Note: Add depth and dimension without sacrificing the overall playfulness.
        *   **Typography:** Rounded fonts, handwritten fonts, cute style fonts. Keywords: Rounded Fonts, Handwritten Fonts Descriptive Note: Select fonts that are cheerful and easy to read.
        *   **Layout:** Simple and clear, avoid being too crowded, centrally symmetrical or balanced layouts. Keywords: Simple, Clear Layout Descriptive Note: Focus on usability without overwhelming the player.
        *   **Interaction Effects:**
            *   Animation: Elastic animation effects (bounce effect when buttons are clicked), bubble floating animations (gentle up and down floating), light过渡 effects (fade in and fade out, sliding). Keywords: Bouncy Animation, Floating Bubbles Descriptive Note: Focus on fluid and playful motion.
            *   Hints/Feedback: Zoom in on hover, shake on click, accelerate on scroll. Keywords: Hover Zoom, Click Shakes, Fast Scrolls Descriptive Note: Reactive and fun interfaces are critical.
            *   Pleasant Feedback Animations: Stars twinkling, ribbons falling, gold coins dropping. Keywords: Rewarding Visuals Descriptive Note: Celebrate player accomplishments.
        *   **Sound Design:** Cute sound effects, bubble bursting sounds, lighthearted music. Keywords: Happy, Lighthearted sounds Descriptive Note: Reinforce the joy of play.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《糖果传奇》(Candy Crush Saga)**
            *   **Visual Characteristics:** Typical candy and macaron color schemes, high saturation. Rounded graphic elements and cartoon-style icons.
            *   **Decoration:** Heavy use of rounded shapes like rounded rectangles and circles.
            *   **Interaction Effects:** Bounce effect when buttons are clicked and explosion effect when candies are eliminated.
        *   **Case 2 (iOS/Android): 《开心消消乐》(Happy Match)**
            *   **Visual Characteristics:** Bright and lively color schemes, cute cartoon animal images as icons. Rounded borders and bubble elements.
            *   **Decoration:** Interface elements are mostly rounded corners and circles.
            *   **Interaction Effects:** Elastic animations during elimination and pleasant feedback animations.
        *   **Case 3 (iOS/Android): 《梦幻花园》(Gardenscapes)**
            *   **Visual Characteristics:** Bright and lively colors, cartoon-style characters and decorations. Interface elements are rounded and cute.
            *   **Decoration:** Use of a large number of brightly colored cartoon elements.
            *   **Interaction Effects:** Smooth and lighthearted animation effects when clicking and swiping.

    **14. Game UI（游戏界面）二次元风格 (Anime Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Fresh color schemes (pink, blue, white), high saturation colors, gradient colors, avoid being too dim. Keywords: Anime Colors, Pastel Hues, High Saturation Descriptive Note: Reference classic manga and anime.
        *   **Decoration:**
            *   Anime Character Elements: Character avatars, chibi images,立绘. Keywords: Anime, Chibi, Character Art Descriptive Note: Make characters front and center.
            *   Cute Icon Design: Big eyes, small mouths, cute elements. Keywords: Cute Icons Descriptive Note: Make icons inviting and pleasing.
            *   Hand-Drawn Style Decorations: Line art, graffiti style, stickers. Keywords: Hand-Drawn Elements Descriptive Note: Make graphics feel personal and unique.
            *   Stars, hearts, flowers. Keywords: Stars, Hearts, Flowers Descriptive Note: Emphasize the whimsical.
        *   **Typography:** Cute style fonts, handwritten fonts. Keywords: Anime-Style Fonts Descriptive Note: Select a playful typeface.
        *   **Layout:** Flexible and varied, asymmetrical layout, highlighting character images. Keywords: Flexible Layout Descriptive Note: Design should be energetic and free.
        *   **Interaction Effects:**
            *   Animation: Character facial expression animations (blinking, smiling, shy), manga panel effects (quickly switching screens), speech bubble animations (following speech display), bouncy button effects (deformation when pressed). Keywords: Exaggerated Animation Descriptive Note: Connect animation with manga aesthetics.
            *   Hints/Feedback: Flicker on hover, bounce on click, accelerate on scroll. Keywords: Reactive Animation Descriptive Note: Design should engage and stimulate.
        *   **Sound Design:** Anime character voice acting, cute sound effects, lighthearted music. Keywords: Anime-Style VO Descriptive Note: Sound design should complement the visuals.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《崩坏3》(Honkai Impact 3rd)**
            *   **Visual Characteristics:** Fresh color schemes, high saturation colors and obvious gradients. Anime character images are prominent, UI layout is flexible.
            *   **Decoration:** Heavy use of character avatars and chibi images.
            *   **Interaction Effects:** Character skill releases have exaggerated animation effects, dialogue boxes often use speech bubbles.
        *   **Case 2 (iOS/Android): 《公主连结！Re:Dive》(Princess Connect! Re:Dive)**
            *   **Visual Characteristics:** Fresh pink and blue main color scheme, cute icon designs and hand-drawn style decorations.
            *   **Decoration:** Common cute elements in the interface such as stars, hearts, and flowers.
            *   **Interaction Effects:** Characters have rich facial expression animations, buttons often have a Q-弹 effect when clicked.
        *   **Case 3 (iOS/Android): 《碧蓝航线》(Azur Lane)**
            *   **Visual Characteristics:** Fresh and bright color scheme, exquisite character立绘. UI layout is relatively simple but highlights character images.
            *   **Decoration:** Some interfaces have hand-drawn style decorative elements.
            *   **Interaction Effects:** Characters have dynamic effects and voice acting during interactions.

    **15. Game UI（游戏界面）极简主义风格 (Minimalist Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Minimalist color scheme (black, white, gray, monochrome), low saturation colors, highlighting content. Keywords: Monochrome, Simple Descriptive Note: Focus on only the essentials.
        *   **Decoration:**
            *   Geometric Shapes: Simple lines, squares, circles. Keywords: Basic Shapes Descriptive Note: Minimize complexity in drawing elements.
            *   Whitespace Design Principle: Large areas of blank space, highlighting information, creating a sense of breathing room. Keywords: Negative Space Descriptive Note: Use as little as possible.
            *   Borderless Design: Removing redundant decorations, simplifying the interface. Keywords: Frame Free Descriptive Note: Simplicity in drawing elements.
        *   **Typography:** Simple sans-serif fonts, light fonts, large letter and line spacing. Keywords: San Serif Descriptive Note: Easy reading, little visual noise.
        *   **Layout:** Grid layouts, alignment principles, highlighting information hierarchy. Keywords: Grid Based Descriptive Note: Ensure easy navigation.
        *   **Interaction Effects:**
            *   Animation: Simple switching animations (fade in and fade out, sliding), restrained feedback effects (color changes, subtle displacements). Keywords: Subtle Transition Descriptive Note: Smooth and seamless.
            *   Hints/Feedback: Color deepens on hover, slight displacement on click, smooth transitions on scroll. Keywords: Simple FX Descriptive Note: Responsive and quick.
            *   Smooth Transitions: Fluid animation effects, avoiding stuttering. Keywords: Seamless Descriptive Note: Little lag time.
        *   **Sound Design:** Minimalist sound effects, crisp clicks, low-pitched background music. Keywords: Minimal Sound Design Descriptive Note: Make small and easy to hear.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《纪念碑谷》(Monument Valley)**
            *   **Visual Characteristics:** Extremely minimalist color scheme, often using monochrome or black and white gray. Geometric shapes, large amounts of whitespace.
            *   **Decoration:** Almost no redundant decoration, entirely composed of geometric shapes.
            *   **Interaction Effects:** Simple switching animations and restrained feedback effects.
        *   **Case 2 (iOS/Android): 独立益智游戏** (例如一些数字解谜或几何解谜游戏)
            *   **Visual Characteristics:** Emphasizes content itself, UI elements are very simple, color scheme is single-toned.
            *   **Decoration:** Almost no decoration, only necessary text and graphics.
            *   **Interaction Effects:** Smooth transition animations and precise operation responses.
        *   **Case 3 (iOS/Android): 一些纯粹的功能性App的UI设计也可作为参考** (虽然不是游戏，但设计理念相通)
            *   **Visual Characteristics:** Black, white, and gray tones are dominant, sans-serif fonts, large amounts of whitespace.
            *   **Decoration:** Very few lines and geometric graphics.
            *   **Interaction Effects:** Simple animations and quick feedback.

    **16. Game UI（游戏界面）复古风格 (Retro Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Retro color schemes (earthy yellow, dark green, dark brown), low saturation colors, limitcolor count. Keywords: Earth Tones, Limited Colors Descriptive Note: Nostalgia and simple graphics are important.
        *   **Decoration:**
            *   Pixel Art Style: Low-resolution images, jagged edges,粗糙 lines. Keywords: Pixel Art Descriptive Note: Embrace the limitations of early computer games.
            *   8-bit Icon Design: Simple shapes, limited color count. Keywords: Simple, Easy, Clear Descriptive Note: Usability comes before visual splendor.
            *   CRT Monitor Effects: Scanlines, blur, color distortion. Keywords: Scan Lines Descriptive Note: Create effects from early gaming experiences.
        *   **Typography:** Pixel fonts, bold fonts. Keywords: Easy Read, Visual Sound Descriptive Note: Make fonts accessible.
        *   **Layout:** Simple and clear, alignment principles, information-focused. Keywords: Simple Layout, Aligned Descriptive Note: Quick interactions are key.
        *   **Interaction Effects:**
            *   Animation: Pixel transition animations (pixel block movement, flashing), scanline effects (simulating CRT monitor scanlines), old TV noise (random noise overlay). Keywords: Easy Graphics Descriptive Note: Usability comes before visual splendor.
            *   Hints/Feedback: Pixel flicker on hover, pixel shake on click, scanlines accelerate on scroll. Keywords: Visually Responsive Descriptive Note: Keep users informed.
        *   **Sound Design:** Retro button sound effects (simple clicks, beeps), 8-bit music. Keywords: 8-bit Sounds Descriptive Note: Sounds should match the visuals.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 经典像素风重制版** (例如一些经典FC或SFC游戏的移植版)
            *   **Visual Characteristics:** Low-saturation retro color schemes, pixel art style icons and interface elements.
            *   **Decoration:** Obvious pixel dots and粗糙 lines.
            *   **Interaction Effects:** Pixel block movement and flashing, and other retro animations.
        *   **Case 2 (iOS/Android): 一些模拟经营类游戏** (例如模拟早期电脑游戏的风格)
            *   **Visual Characteristics:** Use of retro color schemes such as earthy yellow and dark green, simple geometric graphics as UI elements.
            *   **Decoration:** Imitates the style of old computer interfaces.
            *   **Interaction Effects:** Simple pixel transition effects and old-fashioned button sound effects.
        *   **Case 3 (iOS/Android): 部分独立游戏会故意采用低分辨率和有限的颜色**
            *   **Visual Characteristics:** Limited color count, pixel fonts, bold fonts.
            *   **Decoration:** Simple shapes and 8-bit style icons.
            *   **Interaction Effects:** Pixel flicker on hover, pixel shake on click.

    **17. Game UI（游戏界面）写实/拟真风格 (Realistic/Simulative Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Color schemes close to the real world (natural colors, earth tones, relatively low saturation). Emphasis on light and shadow effects, simulating real materials. Keywords: Muted Colors, Real Tones Descriptive Note: Mimic effects from actual life.
        *   **Decoration:**
            *   Realistic Material Simulation: Textures and gloss of materials such as leather, metal, and wood, realistic details. For example, worn metal,粗糙 wood, stitched leather. Keywords: Realistic Materials Descriptive Note: Effects must have high accuracy.
            *   Physics-Based Effects: Dirt, scratches, water stains, etc., to increase realism. Keywords: Aging, Weathering Descriptive Note: Accurate age and weathering is important.
            *   Game World Based UI: For example, if it is a military theme, the UI may look like a display screen on military equipment. Keywords: Game Based Descriptive Note: Designs should complement game environment.
        *   **Typography:** Clear and legible modern fonts, may have a slight aged effect (if it matches the game world setting). Keywords: Simple, Easy, Clean Descriptive Note: Make fonts simple and easy to read.
        *   **Layout:** Close to real-world devices or scenes, tends to have a clear information hierarchy, easy to operate. Keywords: Function over Form Descriptive Note: Easy to understand and operate.
        *   **Interaction Effects:**
            *   Animation: Simulating real physical effects (e.g., slight凹陷 when a button is pressed, smooth movement of a pointer on a dial). Keywords: Realistic Animation Descriptive Note: Effects must have high accuracy.
            *   Feedback: Mechanical feedback sound of button clicks, ticking sound of dial pointers, friction sound when sliding. Keywords: Clear Sound Design Descriptive Note: Easy to understand sound is important.
            *   Hints/Feedback: Highlight on hover, simulate real button feedback on click. Keywords: Subtle UX Descriptive Note: Help users learn quickly.
        *   **Information Presentation:** Present information in as intuitive a way as possible, avoid being too abstract. For example, use a realistic health bar instead of an abstract energy bar. Keywords: Show Information Descriptive Note: Key stats at a glance.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 模拟驾驶类游戏** (例如《狂野飙车》系列的部分界面)
            *   **Visual Characteristics:** UI simulating real racing car instruments, using textures of materials such as metal and leather.
            *   **Decoration:** Instrument panels, buttons, etc., are designed to simulate real equipment as much as possible.
            *   **Interaction Effects:** Smooth movement of pointers and mechanical feedback sound when buttons are pressed.
        *   **Case 2 (iOS/Android): 军事题材策略游戏** (例如某些SLG的资源管理界面)
            *   **Visual Characteristics:** UI may look like a display screen on military equipment, using dark tones and colors close to reality.
            *   **Decoration:** Simulating metal textures and some warning signs.
            *   **Interaction Effects:** Button clicks may have feedback similar to mechanical buttons.
        *   **Case 3 (iOS/Android): 部分生活模拟游戏** (例如模拟经营餐厅或农场的游戏)
            *   **Visual Characteristics:** Use of natural colors and material textures, such as wood or fabric.
            *   **Decoration:** Imitating items and interfaces in real life.
            *   **Interaction Effects:** Simulating the tactile feel and sound of real objects when clicked.

    **18. Game UI（游戏界面）卡通渲染/3D卡通风格 (Cel-Shaded/3D Cartoon Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Bright and vibrant colors, high saturation colors, emphasizing cartoon feel. Keywords: Happy Colors Descriptive Note: Should visually be appealing.
        *   **Decoration:**
            *   Bold Outlines: Highlighting the shapes of objects, similar to hand-drawn effects. Keywords: Cartoon, Bold Outline Descriptive Note: Easy to see and understand.
            *   Flat Shading: Reducing light and shadow details, clear color boundaries. Keywords: Flat Colors Descriptive Note: Visually pleasing.
            *   Exaggerated Proportions: The proportions of characters and objects may not match reality, increasing the cartoon feel. Keywords: Fun Proportions Descriptive Note: Make the cartoon feel larger than life.
            *   Use of bevels and hard edges: Reducing smooth transitions, simulating cartoon rendering effects. Keywords: Cartoon Finish Descriptive Note: Should visually be appealing.
        *   **Typography:** Cartoon style fonts, can be handwritten style or rounded fonts. Keywords: Cartoon Font Descriptive Note: Easy to understand and operate.
        *   **Layout:** Simple and clear, highlighting characters and objects, asymmetrical layouts can be used to increase fun. Keywords: Engaging Descriptive Note: Show key objectives.
        *   **Interaction Effects:**
            *   Animation: Exaggerated animation effects (e.g., deformation of characters when jumping, explosion effects during attacks). Keywords: Cartoon Animation Descriptive Note: Effects must have high visual appeal.
            *   Feedback: Elastic effects when buttons are clicked, changes in character expressions, sound effect coordination. Keywords: Cartoon Sounds Descriptive Note: Easy to understand sound is important.
            *   Hints/Feedback: Zoom in on hover, rotate on click, bounce on scroll. Keywords: subtle UX Descriptive Note: Help users learn quickly.
        *   **UI Elements Integrated with Game World:** UI elements can be designed as part of the game world, for example, a wooden bulletin board as a quest bar. Keywords: Intermix game and menus Descriptive Note: All items are related.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《无主之地》系列手游** (如有)** (可参考主机/PC版)**
            *   **Visual Characteristics:** Bright and vibrant colors,粗线条 outlines, flat shading.
            *   **Decoration:** Strong cartoon hand-drawn feel.
            *   **Interaction Effects:** Exaggerated animation effects and cartoon sound effects.
        *   **Case 2 (iOS/Android): 一些采用卡通渲染的动作冒险游戏**
            *   **Visual Characteristics:** Colors are bright and vibrant, character and object proportions are exaggerated, emphasizing the cartoon feel.
            *   **Decoration:** Use of bevels and hard edges, reducing smooth transitions.
            *   **Interaction Effects:** Deformation of characters when jumping and explosion effects during attacks.
        *   **Case 3 (iOS/Android): 部分儿童向游戏**
            *   **Visual Characteristics:** Bright colors, rounded shapes, cute cartoon images.
            *   **Decoration:** Simple geometric shapes and cartoon patterns.
            *   **Interaction Effects:** Elastic animations and pleasant feedback animations.

    **19. Game UI（游戏界面）恐怖风格 (Horror Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Depressing colors, dark tones (black, dark brown, dark red), low saturation colors, highlighting the horror atmosphere. Use high-contrast colors to emphasize key points (e.g., blood red). Keywords: Dark, Depressing Descriptive Note: Create unease.
        *   **Decoration:**
            *   Blood, Stains, Damaged Materials: Increase the sense of horror. Keywords: Aged, Blood, Rust Descriptive Note: Accurate age and rust is important.
            *   Distorted Fonts, Scrawled Handwriting: Increase the sense of unease. Keywords: Unsettling Descriptive Note: Make users uneasy.
            *   Shadows and Darkness: Heavy use of shadows, hiding details, creating a sense of mystery. Keywords: Deep Shadow Descriptive Note: Increase suspense.
            *   Theme-Based Horror UI: For example, a zombie theme might use biohazard symbols and rusted metal. Keywords: Theme Driven Descriptive Note: All elements should reinforce the theme.
        *   **Typography:** Distorted fonts, handwritten fonts, broken fonts. Keywords: Distorted Descriptive Note: Unsettle users.
        *   **Layout:** Asymmetrical layouts, crowded interfaces, creating a sense of oppression. Keywords: Unbalanced Descriptive Note: Design to create unease.
        *   **Interaction Effects:**
            *   Animation: Flickering, shaking, tearing effects, increasing the sense of jump scares. Keywords: Unstable Animation Descriptive Note: Make it jumpy and jerky.
            *   Feedback: Suddenly appearing screens, eerie sound effects, low background music. Keywords: Jump Scares Descriptive Note: Spook them.
            *   Hints/Feedback: Strange sounds on hover, horror images appear on click. Keywords: Frightening Sounds Descriptive Note: Make them scream.
        *   **Unstable UI:** The UI may malfunction, flicker, or have unexplained phenomena to enhance the sense of horror. Keywords: Unstable UI Descriptive Note: Test the users' patience.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《第五人格》(Identity V)**
            *   **Visual Characteristics:** Depressing colors, dark tones, distorted fonts and damaged materials.
            *   **Decoration:** Use of bloodstains,污渍, and shadows.
            *   **Interaction Effects:** Flickering, shaking, and other unstable effects, as well as eerie sound effects.
        *   **Case 2 (iOS/Android): 《逃生》系列手游** (如有)** (可参考主机/PC版)**
            *   **Visual Characteristics:** Extreme darkness, low saturation colors,粗糙 interface elements.
            *   **Decoration:** Imitation of bloodstains and污渍,潦草 fonts.
            *   **Interaction Effects:** Suddenly appearing screens and unsettling sounds.
        *   **Case 3 (iOS/Android): 部分解谜逃脱类恐怖游戏**
            *   **Visual Characteristics:** Dark and depressing color scheme, UI elements are dilapidated and不堪.
            *   **Decoration:** Use of shadows and elements based on specific horror themes.
            *   **Interaction Effects:** Strange sounds on hover, horror images appear on click.

    **20. Game UI (游戏界面）蒸汽朋克风格 (Steampunk Style Game UI)**

    *   **Visual Characteristics:**
        *   **Color Palette:** Mainly brown, gold, and copper, with dark blue or dark green accents. Creates a retro industrial era atmosphere. Keywords: Brown, Copper, Old Tones Descriptive Note: Muted tones are important.
        *   **Decoration:**
            *   Mechanical Elements such as Gears, Cogs, and Pipes: Heavily used. Keywords: Gears, Mechanical Elements Descriptive Note: Highlight the technology.
            *   Textures of materials such as brass and leather: Keywords: Leather, Brass Descriptive Note: Emphasize craftsmanship.
            *   Complex mechanical structures and精密 instrument panels. Keywords: Dials, Gauges Descriptive Note: Visually explain function.
            *   Retro elements such as hand-drawn maps and nautical charts. Keywords: Hand Drawn Maps Descriptive Note: Impart knowledge.
        *   **Typography:** Fonts with a retro feel, such as gothic or handwritten fonts, but readability must be ensured. Keywords: Old Fonts Descriptive Note: Easy to read.
        *   **Layout:** Neat arrangement, emphasizing functionality. Mechanical structures connect various elements. Keywords: Organized Descriptive Note: Functional and visually sound.
        *   **Interaction Effects:**
            *   Animation: Mechanical animations such as gear rotation, dial pointer swing, and steam喷发. Keywords: Gearing, Revolving Descriptive Note: Visually explain action.
            *   Feedback: Mechanical button sounds, gear meshing sounds, steam喷发 sounds. Keywords: Mechanical sounds Descriptive Note: Audible feedback.
            *   Hints/Feedback: Gears start to rotate on mouse hover, mechanical structures link together on click. Keywords: Visual indication Descriptive Note: Usable feedback.
        *   **Instrument Panels Display Real-time Data:** Use complex instrument panels to display various values in the game, enhancing the sense of immersion. Keywords: Real-time Display Descriptive Note: Immediate results.
    *   **Reference Cases:**
        *   **Case 1 (iOS/Android): 《机械迷城》(Machinarium)**
            *   **Visual Characteristics:** Mainly brown, gold, and copper, heavily using mechanical elements such as gears, cogs, and pipes.
            *   **Decoration:** Textures of brass and leather are prominent, complex mechanical structures.
            *   **Interaction Effects:** Gear rotation and mechanical structure linkage animations.
        *   **Case 2 (iOS/Android): 部分策略或解谜游戏会采用蒸汽朋克风格**
            *   **Visual Characteristics:** Retro color schemes, borders and decorations with mechanical elements.
            *   **Decoration:** 精密 instrument panels and hand-drawn maps and other elements.
            *   **Interaction Effects:** Instrument panel pointer swings and steam喷发 effects.
        *   **Case 3 (iOS/Android): 一些带有维多利亚时代背景的游戏**
            *   **Visual Characteristics:** Use of retro tones such as brown and dark blue, UI element design with a mechanical feel.
            *   **Decoration:** Brass-colored borders and gear patterns.
            *   **Interaction Effects:** Mechanical button sounds and gear meshing sounds.

**请提供你的 `prd.md` 文档 (即使不完善)，以及尽可能详细的游戏 App 产品信息 (即使 PRD 中缺失，也请在 Prompt 中补充)，** **并根据上述 “App 结构与导航” 部分，明确 App 的页面结构与导航关系。** **AI 将会：**

1.  **分析 `prd.md` 文档：**  尽力从中提取所有可用的游戏 App 产品信息、目标用户描述 (游戏用户画像)、品牌风格偏好、**以及页面结构与导航相关信息 (若 PRD 载有)** 等.
2.  **自主研究补充信息：**  **当 `prd.md` 信息不足时，AI 将主动进行以下研究 (但不限于):**
    *   **游戏 App 市场调研：**  基于游戏 App 产品名称与核心卖点，进行市场调研，洞悉市场规模、趋势、用户需求 (游戏用户偏好)、竞争格局 (游戏 App UI/UX)、**及常用 App 导航模式与结构范式** 等.  **Research should include analysis of successful game apps in the same genre and UI style research, AND COMMON APP NAVIGATION PATTERNS.**
    *   **目标游戏用户深度分析：**  基于 PRD 中已有的目标用户描述 (即使简略)，深入游戏用户分析，包括：用户画像细化、用户痛点挖掘、用户移动游戏习惯、UI 偏好、**及用户对 App 导航与页面结构之偏好** . **This will include understanding user mobile usage patterns, UI preferences, and genre-specific expectations to inform app design, AND USER PREDILECTIONS FOR APP NAVIGATION AND PAGE ORGANIZATION.**
    *   **竞品 App 剖析：**  主动搜寻并剖析竞品 App 及同类游戏 App, **NOW DIRECTLY GUIDED BY THE PRE-DEFINED PRINCIPLES OF USER-CENTRIC APP DESIGN AND FEATURE FOCUS**, 深度研究其功能特点、用户体验、设计风格、**及导航架构与页面布局**. **Analyze competitor apps for best practices in UI/UX, navigation schemes, page arrangement, and feature execution.**
    *   **移动端用户体验至臻实践 **深度** 研习：**  进行 **更深入、更系统化** 的移动端用户体验至臻实践研习, **NOW DIRECTLY IMPLEMENTING THE PREVIOUSLY DEFINED USER-CENTRIC DESIGN PRINCIPLES AND FEATURE FOCUS STRATEGIES, AND PRIORITIZING EFFORTLESS NAVIGATION AND BRAND CONSISTENCY**, **重点研习以下 App 成功的关键要素 (基于已供研究、成功 App 共性、及前述用户至上设计原则):**
        *   **流畅导航与用户路径 (Effortless Navigation & User Journeys):**  **Implement straightforward and lucid navigation, refine essential user pathways, and utilize visual hierarchy for informational clarity, in line with the pre-defined principles.**
        *   **核心功能聚焦与价值彰显 (Core Feature Focus & Value Demonstration):** 突出 App 核心功能，清晰展现 App 用户价值，与产品落地页相辅相成. **Concentrate on vital app features that enhance product value, prioritize indispensable features, and prevent feature overload, directly applying the feature focus principles from the summary.**
        *   **用户参与与个性化体验 (User Participation & Tailored Experiences):**  考量用户个性化诉求，设计互动性强 App 组件，提供清晰用户反馈与进度指引. **Integrate personalized user encounters, dynamic and compelling elements, and unambiguous feedback/progress signals, directly implementing the user engagement principles from the summary.**
        *   **品牌和谐与视觉呈现 (Brand Harmony & Visual Presentation):** 保持 App 视觉设计与产品落地页、品牌形象一致。 采用精简现代 UI 设计风格，优化视觉元素适配移动设备. **Uphold brand uniformity with the landing page, adopt a refined and modern UI design, and employ mobile-optimized visual components, directly implementing the brand consistency and visual design principles from the summary.**
        *   **流畅用户互动与迅捷响应 (Fluid User Interactions & Responsive Speed):** 确保 App 操作流畅、响应迅捷，避免卡顿、延迟，提升用户满意度. **Optimize app performance for smooth user interactions and responsiveness, guaranteeing a seamless mobile experience.**
        *   **移动平台特性善用 (Leveraging Mobile Platform Capabilities):** 审慎考虑移动平台特性，如推送通知、地理位置服务、设备传感器等，增强 App 功能与用户体验 (酌情采用). **Consider harnessing mobile platform features (push notifications, location services, sensors) to augment app functionality and user experience where pertinent.**
    *   **用户行为心理学研习 (User Behavior Psychology Research - App 使用与模式):**  研习用户移动 App 行为心理学，洞察用户习惯、偏好、痛点、**及用户 App 内导航行为模式**. **Research user behavior psychology in mobile apps to comprehend usage patterns, preferences, and pain points, AND USER NAVIGATION PATTERNS WITHIN APPLICATIONS.**
    *   **品牌美学解析 (Brand Aesthetic Analysis - App 设计一致性):**  解析产品品牌美学，确保 App 设计风格与品牌形象契合，并与落地页保持连贯统一. **Guarantee app design style resonates with brand identity and sustains consistency with the landing page.**
    *   **移动 UI/UX 设计最佳实践应用 (Mobile UI/UX Design Best Practices Application):** 学习并应用通用移动 UI/UX 设计最佳实践, **尤为关注导航设计与信息架构之精髓**. **Adhere to mobile UI/UX design best practices for intuitive and user-friendly app interfaces, ESPECIALLY SUPERIOR PRACTICES FOR NAVIGATION DESIGN AND INFORMATION ARCHITECTURE.**
    *   **游戏化 App 最佳实践研习 (Gamified App Best Practices Study):**  研习成功游戏化 App 设计范式、用户激励机制、排行榜设计、社交互动功能，并学习 UI/UX 设计最佳实践, **包括游戏 App 常用导航模式与结构布局**. **Research successful gamified applications for design blueprints, user motivation, leaderboard design, social features, and UI/UX best practices, INCORPORATING COMMON NAVIGATION SCHEMES AND PAGE STRUCTURES IN GAMING APPLICATIONS.**
    *   **多语言支持最佳实践研习 (Multilingual Support Best Practices Study):**  探索移动 App 多语言支持实现方案、用户体验优化、语言切换流程设计最佳实践。 **AI 需于研习阶段构思排行榜功能与语言切换功能设计方案，为后续原型创作与文档撰写奠定基础。** **[NEW - 研究补充： App 导航结构与页面层级关系探索]**
    *   **App 导航结构与页面层级关系探索 (App Navigation Structure and Page Hierarchy Exploration):** 研究常用移动 App 导航模式 (如底部导航、顶部标签、抽屉导航) 及页面层级设计原则。 结合游戏化 App 特性，为 App 原型遴选适宜导航结构与页面层级方案。

**(Continue with sections 4, 5, 6 and Industry Style Optimization Checklist, ensuring all sections are updated to reflect the new pre-computed principles implementation and user-centric design focus, and now comprehensively incorporating App Structure & Navigation considerations AND Subscription UI/UX best practices.)**

**4. App 原型构建 (App Prototype Development - 用户体验精进 + 功能模块实现 + 品牌美学应用 + 响应式布局 + 用户友好原型输出):**

*   **App 原型务必极致用户友好，易于理解、操作，突出核心功能与用户流程. **AND MUST BE INTUITIVE AND FOCUS ON KEY USER NEEDS**. AI 需于 `app-design-brief.md` 文档中，概述原型构建策略，及用户友好性 **AND USER-CENTRIC DESIGN** 保障机制. **Prioritize clear and simple UI components, intuitive navigation, and a focus on core user tasks.**
*   **原型工具之选 (Prototyping Tool Selection - Figma/Sketch/Adobe XD 等):**  采用 Figma, Sketch, Adobe XD 或其他专业原型设计工具，高效创建 App 原型. **Choose a prototyping tool enabling efficient creation of interactive and user-friendly app prototypes.**
*   **线框图或低保真原型优先 (Wireframes or Low-Fidelity Prototypes First):**  原型初版可侧重线框图或低保真原型，加速迭代与设计方案验证. **Start with wireframes or low-fidelity prototypes for rapid iteration and design validation, focusing on user flow and key features initially.**
*   **模块化设计策略 (Modular Design Approach):**  运用模块化设计方法，拆解 App 界面为可复用模块，利于设计维护. **Implement modular design principles to create reusable UI components and facilitate design consistency and maintainability.**
*   **响应式布局 (Responsive Layout - 适配主流设备):**  设计响应式布局，确保 App 原型于主流移动设备皆可良好呈现. **Ensure responsive design principles are applied to adapt the app prototype to various screen sizes of standard mobile devices.**
*   **交互设计 (Interactive Design - 用户反馈机制):**  于原型中融入基础交互设计，如按钮点击反馈、页面切换动画等，提升用户体验. **Incorporate basic interactions and user feedback mechanisms (button taps, page transitions) to enhance user experience and prototype usability.**
*   **性能考量 (Performance Considerations - 运行流畅性):**  原型设计需兼顾性能，避免过度繁复动画及特效，确保原型运行流畅. **Consider performance implications in prototype design, avoiding overly complex animations or effects to ensure smooth prototype performance.**
*   **品牌风格一致性 (Brand Aesthetic Consistency - 视觉和谐):**  App 原型设计务必与产品品牌风格保持一致，包括色彩、字体、图标等. **Maintain visual consistency with product brand style, including colors, fonts, and icons, ensuring brand identity is reflected in the app prototype.**
*   **语言切换功能实现 (Language Switching Functionality Implementation):** (沿用前述实现细节)
*   **排行榜功能实现 (Leaderboard Functionality Implementation - 若 PRD 未提及):** (沿用前述实现细节)
    *   **Subscription UI/UX Implementation (Optimized for Conversion):** (As detailed in the previous response, incorporating Tiered Prompts, PRO Zone, Visual Contrast Enhancement, Contextual Trigger Design, Subscription Defense UI, and Social Proof Integration)
*   **导航结构与页面层级实现 (Navigation Structure and Page Hierarchy Implementation):**
    *   **依据 “App 结构与导航” 部分之界定，实现 App 导航结构与页面层级关系。**
    *   **择取适宜导航模式 (如底部导航、顶部标签、抽屉导航等)，确保导航清晰、直观、易于用户理解与操作。**
    *   **设计页面间跳转与返回流程，确保用户 App 内导航流畅自然。**
    *   **于原型中体现主导航页面与二级页面层级关系，例如运用链接、按钮、卡片等元素引导用户进入二级页面。**
    *   **确保原型所有页面皆可通过导航或交互元素访问，杜绝死链或页面无法访问之情形。**
    *   **重点关注 Onboarding 引导页、Paywall 付费墙 (若有)、分享海报页等关键二级页面之用户流程与页面设计。**

**6. 生成 `app-design-brief.md` 文档 (Generate `app-design-brief.md` Document - 极致详述版 - 突出 AI 自主研究、用户体验优化、功能拓展决策及移动端最佳实践应用 - Enhanced Detail & Justification & User-Centric Approach):**

*   **极致详述版设计文档 (Ultra-Detailed Specification Document):** 生成 `app-design-brief.md` 文档，**务必提供前所未有的详尽程度**。 不仅记录 App 原型所有设计决策，更需极致详尽记录 AI 于 PRD 信息匮乏时，如何开展自主研究、分析、决策，如何实现用户体验优化，如何进行功能拓展与优化，以及 **至关重要者，移动端用户体验至臻实践之深度融合与应用，并阐明用户行为与 App 使用模式研究，** **AND SPECIFICALLY THE PRE-DEFINED PRINCIPLES OF USER-CENTRIC APP DESIGN AND FEATURE FOCUS**, 如何影响设计**.**  文档内容务必涵盖:
    *   **PRD 信息分析概要 (PRD Information Analysis Summary):**  简述自 `prd.md` 提取之关键信息及信息缺失状况, **含页面结构与导航相关信息**.
    *   **AI 自主研究报告 (AI Autonomous Research Report):**  详述 AI 市场调研过程, **INCLUDING ANALYSIS OF HIGH-PERFORMING APPLICATIONS**, 用户分析 **(含移动用户行为分析)**、竞品 App 剖析 **(INCLUDING UI/UX ANALYSIS)** 等研究历程与成果, **含导航模式与页面结构研究**. **Detail how user research, app conversion studies AND MOBILE APP RESEARCH, INCLUDING NAVIGATION PATTERN AND PAGE STRUCTURE INVESTIGATIONS, were incorporated.**
    *   **移动端用户体验最佳实践研习报告 (Mobile User Experience Best Practices Learning Report):**  **极致详尽描述 AI 如何研习与理解移动端用户体验最佳实践，涵盖研习资源、方法、成果, **AND HOW THE PRE-DEFINED PRINCIPLES OF USER-CENTRIC APP DESIGN AND FEATURE FOCUS SERVED AS A DIRECT IMPLEMENTATION GUIDE**. 总结 App 成功关键要素 (须与 Prompt 列举要素一致).  **Provide specific examples of applications analyzed (if relevant beyond general research) and how their best practices, **AND SPECIFICALLY ELEMENTS FROM THE PRE-DEFINED PRINCIPLES, **were adopted, PARTICULARLY CONCERNING NAVIGATION AND INFORMATION ARCHITECTURE.**
    *   **功能拓展决策依据 (Feature Expansion Rationale):**  详述 AI 如何基于用户需求与产品价值，自主拓展 App 功能，并提供决策理由与依据. **Justify feature expansion decisions based on user needs, product value proposition, and mobile app best practices.**
    *   **设计决策依据 (Design Decision Rationale):**  针对每项关键设计决策 (如：导航设计、界面布局、功能模块设计、交互设计、视觉风格、响应式策略、**移动端最佳实践应用, User-Centric Approach, Feature Focus, Brand Consistency, INTUITIVE NAVIGATION, USER FLOW OPTIMIZATION**) ， **务必极致详尽** 阐述 AI 决策理由与依据. **务必清晰指出每项设计决策如何立足用户体验最佳实践，并援引具体移动设计原则或案例佐证. Explicitly state how user understanding, intuitive navigation, key feature focus, brand consistency, **AND USER-CENTRIC DESIGN PRINCIPLES FROM THE SUMMARY** informed each design decision. **Specifically detail how the pre-defined principles were directly implemented in each design aspect AND HOW USER EXPERIENCE CONSIDERATIONS WERE ADDRESSED IN EACH SECTION.**
    *   **App 导航结构与页面层级关系设计详解 (App Navigation Structure and Page Hierarchy Design Specification):** 详尽描述 App 导航结构与页面层级关系设计，包括：
        *   **App 信息架构图 (App Information Architecture Diagram):**  运用图表或树状结构，清晰展现 App 主页面与二级页面层级关系及导航路径。
        *   **导航模式选择理由 (Navigation Model Selection Justification):**  详述 AI 所选导航模式 (如底部导航、顶部标签、抽屉导航等) 及选择理由与依据，如用户研究、竞品分析、最佳实践参考等。
        *   **页面跳转与返回流程设计 (Page Transition and Back Navigation Flow Design):**  描述页面间跳转方式 (如 push, modal) 及返回流程设计，并解释如何确保导航流程的流畅性和一致性。
        *   **关键二级页面设计深析 (Key Secondary Page Design Deep Dive - Onboarding, Paywall, 分享海报等):**  针对 Onboarding 引导页、Paywall 付费墙 (若有)、分享海报页等关键二级页面，详细描述其页面设计、用户流程和功能特点。
        *   **导航可用性和易用性评估 (Navigation Usability and Accessibility Evaluation):**  描述 AI 如何评估并确保 App 导航的可用性和易用性，例如用户测试、启发式评估等方法 (若适用)。
        *   **导航设计与游戏化 App 特点结合 (Navigation Design Integration with Gamified App Characteristics):**  说明导航设计如何与游戏化 App 的特点相结合，例如如何通过导航引导用户参与游戏、完成任务、查看排行榜等。
        *   **提供关键页面 (例如首页、游戏中心、设置页、Onboarding, Paywall, 分享海报页等) 原型截图或线框图，并在文档中进行标注和说明，解释页面布局、元素设计和交互逻辑。**
    *   **Subscription UI/UX Design Specification:**
        *   **Subscription UI/UX Rationale:** Justify the chosen subscription UI/UX elements based on best practices and behavioral psychology.
        *   **Detailed Implementation:** Detail the implementation of each subscription UI/UX element (Tiered Prompts, PRO Zone, Visual Contrast, Contextual Triggers, Subscription Defense UI, Social Proof).
        *   **AB Testing Strategy:** Explain the AB testing strategy for optimizing subscription UI/UX elements, referencing suggested priorities.
    *   **文案创意来源与行业范本文案风格借鉴 (Copywriting Creative Source and Industry-Inspired Copy Style Reference):**  (沿用前述细节，可将 "ClickBank" 泛化为 "行业最佳实践" 以求措辞多样化.)
    *   **素材甄选考量与行业范本素材风格参考 (Visual Asset Selection Considerations and Industry-Inspired Visual Style Reference):** (沿用前述细节，可将 "ClickBank" 泛化为 "行业最佳实践" 以求措辞多样化.)
    *   **响应式设计策略详解 (Responsive Design Strategy Breakdown):**  (沿用前述细节)
    *   **颜色方案决策详解 (Color Scheme Decision Breakdown):** (沿用前述细节)
    *   **信任建立策略详解与行业信任要素强化 (Trust Building Strategy Breakdown and Industry Trust Factor Reinforcement):** (沿用前述细节，可将 "ClickBank" 泛化为 "行业最佳实践" 以求措辞多样化.)
    *   **CTA 按钮设计详解与行业范本 CTA 按钮风格借鉴 (CTA Button Design Breakdown and Industry CTA Button Style Reference):** (沿用前述细节，可将 "ClickBank" 泛化为 "行业最佳实践" 以求措辞多样化.)
    *   **价格部分设计与实现细节 (Price Section Design & Implementation Details):** (沿用前述细节)
    *   **技术实现策略详解与行业技术标准对齐说明 (Technical Implementation Strategy Breakdown and Industry Technical Standard Alignment Explanation):** (沿用前述细节，可将 "ClickBank" 泛化为 "行业性能技术标准" 以求措辞多样化.)
    *   **性能优化策略 (Performance Optimization Strategies):** (沿用前述细节)
    *   **合规策略 (Compliance Strategy):** (沿用前述细节)
    *   **语言切换功能设计详解 (Language Switching Functionality Design Specification):** (沿用前述细节)
    *   **排行榜功能设计详解 (Leaderboard Functionality Design Specification - 若 PRD 未提及):** (沿用前述细节)

**行业风格优化核对清单 (Industry Style Optimization Checklist) (AI 自主研究、决策、响应式设计、颜色方案优化和行业最佳实践应用验证) - Enhanced Checklist & Direct Summary & SEO Implementation Verification :**

*   ✅ **高转化率导向 (AI 极致目标):**  设计目标是 **极致提升转化率**，所有设计决策都必须服务于这个目标， **并以行业最佳实践为最高指导原则 and the pre-computed design practice summary as a direct implementation guide, **AND SEO BEST PRACTICES**.**
*   ✅ **深度融入行业最佳实践 & Pre-computed Summary Implementation & SEO Optimization:**  落地页设计必须深度融入行业最佳落地页实践 **AND DIRECTLY IMPLEMENT THE PRE-COMPUTED DESIGN PRACTICE SUMMARY, **AND BE OPTIMIZED FOR SEO**, 并体现在各个方面 (用户分析、结构规划、文案、UI 设计、技术实现等).  `landing-page-brief.md` 文档需要 **极致详细地** 论证行业最佳实践的应用情况 **AND DEMONSTRATE DIRECT IMPLEMENTATION OF EACH POINT IN THE PRE-COMPUTED SUMMARY, **AND EXPLICITLY DETAIL SEO IMPLEMENTATION STRATEGIES**.**
*   ✅ **Audience Personalization (SEO Relevance):** Landing page is tailored to the target audience's interests, potentially with location-aware content, **AND CONTENT IS RELEVANT TO TARGET AUDIENCE AND SEARCH INTENT FOR SEO.**
*   ✅ **Streamlined Approach (SEO User Focus):** Landing page focuses on a single offer to maximize conversion **AND MAINTAIN TOPICAL FOCUS FOR SEO.**
*   ✅ **AIDA Principle Applied (SEO Content Structure):** Landing page structure is based on the AIDA model for effective user journey **AND CONTENT IS LOGICALLY STRUCTURED WITH SEMANTIC HTML AND HEADING TAGS FOR SEO.**
*   ✅ **痛点营销 (AI 自主挖掘 + 行业痛点策略 + Summary Implementation + **SEO Keywords**):**  AI 主动挖掘用户痛点，并在落地页中进行 **极致** 痛点营销， **借鉴行业成功案例的痛点策略 AND DIRECTLY IMPLEMENTING PROBLEM-FOCUSED HEADLINES AND PAIN POINT EMPHASIS FROM THE PRE-COMPUTED SUMMARY, **AND INCORPORATING RELEVANT SEO KEYWORDS NATURALLY**.** **Focus on symptoms before solutions in pain point messaging as per summary, **AND ENSURE KEYWORD RELEVANCE FOR SEO**.**
*   ✅ **利益驱动 (AI 自主提炼 + 行业利益呈现 + Summary Implementation + **SEO Keywords**):**  AI 主动提炼产品核心卖点和用户利益，并在落地页中 **极致突出** 用户利益， **借鉴行业成功案例的利益呈现方式 AND DIRECTLY IMPLEMENTING BENEFIT-DRIVEN HEADLINES AND COPY AS PER THE PRE-COMPUTED SUMMARY, **AND INTEGRATING RELEVANT SEO KEYWORDS**.** **Benefit-driven headlines and copy are used throughout the landing page as per summary, **AND ARE OPTIMIZED FOR SEO KEYWORDS**.**
*   ✅ **销售型文案 (AI 自主创作 + 行业极致转化文案风格 + Summary Implementation + **SEO Optimization**):**  AI 自主创作销售型文案，力求 **极致简洁有力，Benefit-Driven**，并 **深度契合行业极致转化文案风格 AND DIRECTLY IMPLEMENTING PERSUASIVE COPY GUIDELINES FROM THE PRE-COMPUTED SUMMARY, **AND OPTIMIZING FOR SEO**.** **Persuasive copy pre-sells the solution, covering what, where, when, why, and how. Short sentences are used for better readability, following summary recommendations. **SALES COPY IS OPTIMIZED FOR RELEVANT SEO KEYWORDS AND USER SEARCH INTENT**.**
*   ✅ **视觉冲击力 (AI 自主设计 + 颜色方案优化 + 行业视觉风格借鉴 + Simple & Engaging Design + Summary Implementation + **SEO Visuals**):**  AI 自主设计具有视觉冲击力的界面风格， **并极致优化颜色方案以增强视觉冲击力，借鉴行业成功案例的视觉风格 AND DIRECTLY IMPLEMENTING CLEAN & SIMPLE DESIGN PRINCIPLES FROM THE PRE-COMPUTED SUMMARY, **AND ENSURING VISUAL ELEMENTS ARE OPTIMIZED FOR SEO**.** **Design is clean, mobile-friendly, with easy-to-read dark text on white background, appealing images, and complementary color palettes, adhering to the summary guidelines for visual style. **IMAGES AND VIDEOS ARE OPTIMIZED FOR SEO (FILE NAMES, ALT TEXT).**
*   ✅ **信任建立 (AI 自主策略 + 行业信任要素极致强化 + Social Proof & Testimonials & Trust Signals + Summary Implementation + **SEO E-A-T**):**  AI 自主设计信任建立策略， **并极致强化行业平台用户更看重的信任要素. Social proof elements like multi-format customer testimonials, before/after transformation elements, and authority/credibility indicators are prominently featured, DIRECTLY IMPLEMENTING SOCIAL PROOF TECHNIQUES FROM THE PRE-COMPUTED SUMMARY. **TRUST BUILDING ELEMENTS CONTRIBUTE TO SEO E-A-T (EXPERTISE, AUTHORITATIVENESS, TRUSTWORTHINESS).**
*   ✅ **紧迫感营造 (AI 自主策略 + 行业紧迫感策略 + Summary Implementation - **SEO Engagement**):**  AI 自主设计紧迫感营造策略， **借鉴行业成功案例的紧迫感策略 AND DIRECTLY IMPLEMENTING URGENCY & SCARCITY TACTICS FROM THE PRE-COMPUTED SUMMARY (LIMITED TIME DISCOUNTS, COUNTDOWN TIMERS, LIMITED STOCK MESSAGING). **URGENCY AND SCARCITY TACTICS AIM TO IMPROVE USER ENGAGEMENT METRICS FOR SEO**.**
*   ✅ **响应式设计 (通用标准 + 行业移动优先 - **SEO Mobile-Friendliness**):**  生成响应式落地页，适配不同屏幕尺寸， **极致重视移动端用户体验，与行业移动优先策略对齐. **LANDING PAGE IS FULLY RESPONSIVE AND MOBILE-FIRST OPTIMIZED, A CRITICAL SEO RANKING FACTOR**.**
*   ✅ **性能优化 (通用标准 + 行业技术标准对齐 - **SEO Page Speed**):**  页面性能 **极致优化**，与行业平台技术标准对齐. **PAGE LOADING SPEED IS EXTREMELY OPTIMIZED FOR MAXIMUM CONVERSION AND INDUSTRY TECHNICAL REQUIREMENTS, AND FOR SEO PAGE SPEED BENEFITS**.**
*   ✅ **AI 自主研究和决策透明度 (SEO Justification):**  `landing-page-brief.md` 文档 **极致详细地** 记录 AI 的自主研究过程、设计决策和依据 **(包括颜色方案决策和行业最佳实践应用, AND DIRECT IMPLEMENTATION OF THE PRE-COMPUTED SUMMARY, **AND SEO STRATEGY JUSTIFICATION**)**. **Document details how audience research and conversion studies, AND THE PRE-COMPUTED DESIGN SUMMARY, **AND SEO BEST PRACTICES**, informed design decisions. **EXPLICITLY JUSTIFY SEO STRATEGIES IMPLEMENTED IN EACH SECTION OF THE LANDING PAGE**.**
*   ✅ **响应式设计策略透明度 (SEO Mobile-Friendliness Documentation):**  `landing-page-brief.md` 文档 **极致详细地** 记录 AI 采用的响应式设计策略和技术 **(包括响应式颜色方案实现)**. **DOCUMENTATION DETAILS MOBILE-FIRST DESIGN AND RESPONSIVE IMPLEMENTATION FOR SEO MOBILE-FRIENDLINESS**.**
*   ✅ **颜色方案优化 justification (SEO User Experience Justification):**  **如果 AI 替换了 PRD 颜色方案，`landing-page-brief.md` 文档必须提供极致充分的理由和优势说明. Color scheme choice is justified based on color psychology, brand tonality, and industry best practices, referencing the pre-computed summary if applicable. **EXPLAIN HOW COLOR CHOICES ENHANCE USER EXPERIENCE AND ENGAGEMENT FOR SEO BENEFITS**.**
*   ✅ **行业最佳实践应用 & Summary Implementation Justification (SEO Best Practice Alignment):**  **`landing-page-brief.md` 文档必须详细论证落地页设计是如何深度融入和应用行业最佳落地页实践 AND THE PRE-COMPUTED DESIGN SUMMARY, 并提供具体的案例和分析. Document provides specific examples of industry landing pages analyzed (if applicable) and **EXPLICITLY DEMONSTRATES HOW EACH POINT OF THE PRE-COMPUTED DESIGN SUMMARY WAS DIRECTLY IMPLEMENTED IN THE GENERATED LANDING PAGE PROTOTYPE, **AND HOW INDUSTRY BEST PRACTICES ALIGN WITH SEO PRINCIPLES**.**
*   ✅ **Price Section Design Implementation & Justification (SEO User Experience):** **`landing-page-brief.md` document includes a dedicated section detailing the price section design and implementation, explicitly stating how the 3-tier pricing, value stacking, visual emphasis, and "Most Popular" tier highlighting were implemented based on the pre-computed summary's price presentation strategies. Provides visual examples or mockups of the price section. **EXPLAIN HOW CLEAR AND VALUE-DRIVEN PRICE PRESENTATION IMPROVES USER EXPERIENCE AND CONVERSION RATES, INDIRECTLY SUPPORTING SEO**.**
*   ✅ **SEO-Friendly HTML Output Verification:** **`landing-page-brief.md` document explicitly confirms that the generated HTML code is SEO-friendly, utilizing semantic HTML structure and proper heading tag usage (H1, H2, H3 etc.).  Provide examples of heading tag implementation and semantic HTML structure in the documentation.**
*   ✅ **Compliance with FTC Regulations:** Affiliate disclaimer is included for industry products to comply with FTC regulations.
    *   ✅ **Subscription UI/UX Best Practices Implementation:**  The App prototype demonstrably implements industry best practices for subscription UI/UX design, as detailed in the `app-design-brief.md` document, and incorporates elements from the pre-computed summary to optimize subscription conversion.
    *   ✅ **AB Testing Considerations for Subscription UI Documented:** The `app-design-brief.md` document outlines a clear AB testing strategy for the subscription UI/UX elements, including specific elements to test and metrics to track for optimization.

**请提供你的 `prd.md` 文档 (即使不完善，且可能包含颜色方案)，以及尽可能详细的产品信息 (即使 PRD 中缺失，也请在 Prompt 中补充)，** **并根据上述 “App 结构与导航” 部分，明确 App 的页面结构与导航关系。** **AI 将会基于这些信息，进行极致深入的自主研究和设计，为你 \[你的 App 产品名称] 设计 **极致高转化率的响应式 App 原型** (行业最佳实践融入版 - 颜色方案自主优化版 - 响应式设计优化版 - PRD 不完善友好型 - 自主研究补充版) - Enhanced Version with Pre-computed Design Practice Summary, Direct Implementation & SEO Optimization.** **本次生成的 App 原型将包含** **语言切换功能** **和** **排行榜功能 (如果 PRD 未提及)** **，并会** **根据您提供的 “App 结构与导航” 信息，构建清晰的 App 页面结构和导航体系。** **请在评估原型时重点关注 App 的导航是否清晰易用，页面层级关系是否合理, 以及订阅引导是否自然有效。**

**使用说明:**

1.  **务必极其详细地提供你的 `prd.md` 文档和产品信息，即使 PRD 不完善，且可能包含颜色方案.**  信息越详细、越精准，AI 的研究和设计就越有方向性，越能生成极致高转化的落地页原型.
2.  **请务必根据上述 “App 结构与导航” 部分，详细列出 App 的主要导航页面和二级页面，并明确页面之间的层级关系和入口。** 这对于 AI 理解 App 的整体架构至关重要。
3.  将此 Prompt 和你的 `prd.md` 文档 (以及补充的产品信息和页面结构信息) 一起输入给 AI 模型. **请极其明确地告知 AI 模型，你需要其深度学习行业最佳实践 , 主动进行极致深入的研究和补充信息，生成极致高转化率的响应式落地页，并在颜色方案选择上具有自主权，可以优化或替换 PRD 颜色方案.  **Crucially emphasize that the AI MUST DIRECTLY IMPLEMENT THE PROVIDED PRE-COMPUTED SUMMARY OF INDUSTRY-LEADING APP DESIGN PRACTICES across all design aspects, including headlines, CTAs, social proof, urgency, layout, and price presentation, **AND ENSURE THE GENERATED HTML IS OPTIMIZED FOR SEO, PARTICULARLY WITH SEMANTIC HTML AND APPROPRIATE HEADING TAG USAGE (H1, H2, H3 etc.)**.** Emphasize the importance of data-driven design based on audience research and conversion optimization studies.** **请特别强调 AI 需要在原型中实现** **语言切换功能 (在设置页面)** **和** **排行榜功能 (如果 PRD 未提及)** **，并在 `app-design-brief.md` 文档中详细记录这两个功能的实现方案和设计决策。** **同时，请强调 AI 需要** **根据您提供的 “App 结构与导航” 信息，构建清晰的 App 页面结构和导航体系，并在 `app-design-brief.md` 文档中详细描述 App 的信息架构和导航设计, 以及详细描述订阅引导UI/UX的设计和AB测试方案。**
4.  **极其仔细地审查生成的原型和 `app-design-brief.md` 文档,**  **重点评估以下几个方面:**
    *   **是否深度融入了行业最佳实践 & PRE-COMPUTED SUMMARY IMPLEMENTATION & SEO OPTIMIZATION?**  `app-design-brief.md` 文档中是否提供了极致充分的论证和案例 **AND EXPLICITLY DEMONSTRATES DIRECT IMPLEMENTATION OF EACH POINT IN THE PRE-COMPUTED SUMMARY, **AND CLEARLY JUSTIFIES SEO IMPLEMENTATION STRATEGIES**.**  **Is the design demonstrably and verifiably aligned with industry best practices AND THE PRE-COMPUTED DESIGN SUMMARY, **AND OPTIMIZED FOR SEO**, as detailed in the `app-design-brief.md`?**
    *   **AI 的自主研究和设计决策是否合理？**  `app-design-brief.md` 文档中是否提供了极致充分的理由和依据？ **Are AI's research and design decisions well-justified in the `app-design-brief.md`, demonstrating a clear understanding of audience and conversion optimization principles AND DIRECT IMPLEMENTATION OF THE PRE-COMPUTED DESIGN SUMMARY, **AND SEO BEST PRACTICES**?**
    *   **响应式效果是否良好？**  在不同屏幕尺寸下测试落地页的显示效果. **Is the landing page truly responsive and mobile-first optimized for industry standards AND SEO MOBILE-FRIENDLINESS?**
    *   **颜色方案选择是否恰当？**  是否符合品牌调性和用户偏好？如果替换了 PRD 颜色方案，理由是否充分？ **Is the color scheme choice justified, effective for conversion, AND ALIGNED WITH INDUSTRY VISUAL BEST PRACTICES, **AND CONTRIBUTES TO POSITIVE USER EXPERIENCE FOR SEO**?**
    *   **落地页的整体设计是否极致契合行业平台高转化落地页的风格和策略 AND DIRECTLY IMPLEMENTING THE PRE-COMPUTED DESIGN SUMMARY, **AND OPTIMIZED FOR SEO**?** **Does the overall design convincingly and verifiably align with industry styles and strategies for high-conversion landing pages AND DIRECTLY IMPLEMENT THE PRE-COMPUTED DESIGN SUMMARY, **AND IS IT DEMONSTRABLY SEO-FRIENDLY**?**
    *   **Is the Price Section Design Effectively Implemented According to the Pre-computed Summary's Guidance?**  **Does the `app-design-brief.md` document detail and justify the price section design and its direct implementation of the pre-computed summary's price presentation strategies, including visual examples or mockups of the price section. **IS THE PRICE SECTION DESIGN OPTIMIZED FOR USER EXPERIENCE AND CONVERSION, INDIRECTLY SUPPORTING SEO**?**
    *   **Is the HTML Output SEO-Friendly?**  **Does the `app-design-brief.md` document explicitly confirm and demonstrate that the generated HTML code is SEO-friendly, utilizing semantic HTML structure and proper heading tag usage (H1, H2, H3 etc.)? **VERIFY PROPER HEADING TAG USAGE AND SEMANTIC HTML STRUCTURE IN THE GENERATED HTML CODE.**
    *   **Is Page Speed Optimized for SEO and Industry Standards?** **Does the `app-design-brief.md` document detail specific performance optimization strategies implemented for SEO page speed benefits and industry technical standards?  VERIFY PAGE SPEED OPTIMIZATION TECHNIQUES ARE IMPLEMENTED IN THE GENERATED HTML (IMAGE OPTIMIZATION, LAZY LOADING, CDN, etc.).**
    *   **语言切换功能是否完善且易用？**  测试语言切换功能是否能够正常切换语言，多语言文本显示是否正确，切换流程是否流畅自然。
    *   **排行榜功能是否符合游戏化 App 的特点？ (如果添加了排行榜)**  评估排行榜的设计是否符合游戏化 App 的竞技性和激励性，UI 风格是否与 App 整体风格一致，排行榜数据展示是否清晰易懂。
    *   **App 导航是否清晰易用？页面层级关系是否合理？**  测试 App 的导航是否直观易懂，主要功能是否能够快速访问，页面之间的跳转是否流畅，用户是否容易找到所需信息和功能。 **请重点评估 Onboarding 引导页、Paywall 付费墙、分享海报页等关键二级页面的用户流程和导航体验。**
        *   **Is Subscription UI/UX Design Effective and User-Friendly?** Evaluate whether the App prototype effectively implements the specified subscription UI/UX best practices. Assess if the subscription funnel is designed to be user-friendly yet conversion-optimized, as justified in the `app-design-brief.md` document.

5.  根据生成的原型和文档进行 **极致精细的** 调整和优化，并用于实际开发和 A/B 测试. **Remember to proofread the copy with Grammarly.com and get a second opinion on design and relevance.  Ensure all design elements are consistent with industry best practices AND THE PRE-COMPUTED DESIGN SUMMARY, **AND OPTIMIZED FOR SEO**, for maximum engagement potential and search engine visibility on the industry platform.**

**补充说明:**

*   这个 Prompt 模板 **达到了前所未有的精细度和复杂度**，旨在引导 AI 生成 **极致高转化率、深度契合行业最佳实践, AND DIRECTLY IMPLEMENTING THE PRE-COMPUTED DESIGN SUMMARY, **AND OPTIMIZED FOR SEO** 的电商产品落地页原型. **This elevated version is meticulously crafted to leverage research-backed insights, industry best practices, AND NOW SEO OPTIMIZATION PRINCIPLES, **AND THE PRE-COMPUTED DESIGN SUMMARY FOR DIRECT IMPLEMENTATION**, for maximum engagement potential and search engine visibility.**
*   **`app-design-brief.md` 文档的详细程度和质量至关重要，**  你需要仔细研读这份文档，评估 AI 的设计决策和行业最佳实践应用情况, **AND VERIFY DIRECT IMPLEMENTATION OF EACH POINT IN THE PRE-COMPUTED DESIGN SUMMARY, **AND SEO STRATEGY JUSTIFICATION**, 并根据文档进行更深入的优化. **The `app-design-brief.md` serves as a vital resource for understanding, verifying, and validating the AI's design process and rationale, **AND ITS ADHERENCE TO THE PRE-COMPUTED DESIGN SUMMARY AND SEO BEST PRACTICES.**
*   **务必在 Prompt 中极其明确地告知 AI 模型需要深度学习行业最佳实践 (as detailed in provided resources)，并将其作为最高设计指导原则, **AND, CRUCIALLY, TO DIRECTLY IMPLEMENT THE PROVIDED PRE-COMPUTED SUMMARY OF INDUSTRY-LEADING APP DESIGN PRACTICES AND OPTIMIZE FOR SEO THROUGHOUT THE DESIGN AND HTML GENERATION PROCESS.** Emphasize the importance of data-driven design based on audience research and conversion optimization studies, **NOW WITH DIRECT IMPLEMENTATION OF THE PRE-COMPUTED DESIGN SUMMARY AND SEO BEST PRACTICES AS THE CENTRAL GUIDELINES.**
*   **最终的落地页效果仍然需要通过 A/B 测试进行验证和优化，**  生成的原型只是第一步，持续迭代和优化是提升转化率和搜索引擎排名 **请记住，本次生成的 App 原型将包含** **语言切换功能** **和** **排行榜功能 (如果 PRD 未提及)** **，并会** **根据您提供的 “App 结构与导航” 信息，构建清晰的 App 页面结构和导航体系。** **这两个方面都是提升用户体验和游戏化深度的重要组成部分，请在后续的迭代和优化中持续关注, 此外, 重点关注和优化订阅引导UI/UX，最大化App的商业价值。**

**请在生成的 App 原型中 **务必实现** 语言切换功能，并在设置页面提供语言切换选项。使用 JSON 文件管理多语言数据，并在 HTML 中使用 `data-i18n` 属性进行多语言文本显示。**

**如果提供的 `prd.md` 文档中没有明确提及排行榜功能，请 **自主判断并决定是否添加** 排行榜功能。如果添加，请在原型中实现排行榜功能，并在 `app-design-brief.md` 文档中详细说明添加排行榜功能的理由和设计方案。**

**请务必根据上述 “App 结构与导航” 部分，详细列出 App 的主要导航页面和二级页面，并明确页面之间的层级关系和入口。** AI 将 **严格按照您提供的页面结构和导航关系** 构建 App 原型。 如果您未提供明确的页面结构和导航信息，AI 将 **基于自主研究和最佳实践** 假设 App 的页面结构和导航，并在 `app-design-brief.md` 文档中详细说明其假设和理由。**

**请务必指示AI 严格执行 "App 原型构建" -> "Subscription UI/UX Implementation (Optimized for Conversion)" 部分 的所有细则, 以确保原型包含最佳的订阅引导用户体验。**
```