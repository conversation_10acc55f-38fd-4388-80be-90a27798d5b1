/**
 * 轮盘测试页面专用Hook
 * 提供真实Quiz数据的轮盘测试功能
 */

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useNewHomeData } from '@/hooks/useNewHomeData';
import { useQuizConfig } from '@/hooks/useQuizConfig';
import { Services } from '@/services';
import type { Emotion, ContentDisplayMode, RenderEngine, ViewType } from '@/types';
import type { QuizPack, QuizQuestion, QuizQuestionOption } from '@/types/schema/base';

export interface WheelTestConfig {
  emotionCount: number;
  tierLevel: number;
  viewType: ViewType;
  renderEngine: RenderEngine;
  displayMode: ContentDisplayMode;
  selectedPackId: string;
}

export interface WheelTestData {
  currentEmotions: Emotion[];
  availablePacks: QuizPack[];
  selectedPack: QuizPack | null;
  selectedQuestion: QuizQuestion | null;
  isUsingRealData: boolean;
}

export interface WheelTestActions {
  updateConfig: (updates: Partial<WheelTestConfig>) => void;
  selectPack: (packId: string) => Promise<void>;
  selectQuestion: (questionId: string) => Promise<void>;
  handleEmotionSelect: (emotion: Emotion) => void;
  loadPacksByCategory: (category: string) => Promise<QuizPack[]>;
  generateTestData: (count: number) => Emotion[];
  refreshData: () => Promise<void>;
}

export function useWheelTest(): {
  config: WheelTestConfig;
  data: WheelTestData;
  actions: WheelTestActions;
  isLoading: boolean;
  error: string | null;
} {
  // 配置状态
  const [config, setConfig] = useState<WheelTestConfig>({
    emotionCount: 6,
    tierLevel: 1,
    viewType: 'wheel',
    renderEngine: 'D3',
    displayMode: 'textEmoji',
    selectedPackId: '',
  });

  // 数据状态
  const [selectedPack, setSelectedPack] = useState<QuizPack | null>(null);
  const [selectedQuestion, setSelectedQuestion] = useState<QuizQuestion | null>(null);
  const [availablePacks, setAvailablePacks] = useState<QuizPack[]>([]);

  // 使用现有的Hooks
  const { 
    defaultQuiz, 
    recommendedQuizPacks, 
    isLoading: homeDataLoading, 
    error: homeDataError,
    getQuizPacksByCategory,
    refreshData: refreshHomeData 
  } = useNewHomeData();

  const { 
    generateSessionConfig,
    preferredViewType,
    colorMode: configColorMode 
  } = useQuizConfig();

  // 从Quiz选项转换为Emotion格式
  const convertQuizOptionsToEmotions = useCallback((options: QuizQuestionOption[]): Emotion[] => {
    return options.slice(0, config.emotionCount).map((option) => ({
      id: option.id,
      name: option.option_text,
      emoji: option.emoji || '😀',
      color: option.color || `#${Math.floor(Math.random() * 16777215).toString(16)}`,
      created_at: option.created_at,
      updated_at: option.updated_at,
      is_deleted: false,
    }));
  }, [config.emotionCount]);

  // 生成测试数据
  const generateTestData = useCallback((count: number): Emotion[] => {
    const defaultEmotions = [
      { name: '快乐', emoji: '😊' },
      { name: '悲伤', emoji: '😢' },
      { name: '愤怒', emoji: '😠' },
      { name: '恐惧', emoji: '😨' },
      { name: '惊讶', emoji: '😲' },
      { name: '厌恶', emoji: '🤢' },
      { name: '中性', emoji: '😐' },
      { name: '兴奋', emoji: '🤩' },
      { name: '感激', emoji: '🙏' },
      { name: '顽皮', emoji: '😜' },
      { name: '满足', emoji: '😌' },
      { name: '困惑', emoji: '😕' }
    ];

    const emotions: Emotion[] = [];
    for (let i = 0; i < count; i++) {
      const defaultEmotion = defaultEmotions[i % defaultEmotions.length];
      emotions.push({
        id: `test-emotion-${i}`,
        name: defaultEmotion.name,
        emoji: defaultEmotion.emoji,
        color: `#${Math.floor(Math.random() * 16777215).toString(16)}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_deleted: false,
      });
    }
    return emotions;
  }, []);

  // 获取当前使用的情绪数据
  const currentEmotions = useMemo(() => {
    // 优先使用选中的问题
    if (selectedQuestion?.options && selectedQuestion.options.length > 0) {
      return convertQuizOptionsToEmotions(selectedQuestion.options);
    }
    
    // 其次使用选中的包的第一个问题
    if (selectedPack?.questions && selectedPack.questions.length > 0) {
      const firstQuestion = selectedPack.questions[0];
      if (firstQuestion.options && firstQuestion.options.length > 0) {
        return convertQuizOptionsToEmotions(firstQuestion.options);
      }
    }
    
    // 再次使用默认Quiz数据
    if (defaultQuiz?.questions && defaultQuiz.questions.length > 0) {
      const firstQuestion = defaultQuiz.questions[0];
      if (firstQuestion.options && firstQuestion.options.length > 0) {
        return convertQuizOptionsToEmotions(firstQuestion.options);
      }
    }
    
    // 最后使用测试数据
    return generateTestData(config.emotionCount);
  }, [selectedQuestion, selectedPack, defaultQuiz, config.emotionCount, convertQuizOptionsToEmotions, generateTestData]);

  // 判断是否使用真实数据
  const isUsingRealData = useMemo(() => {
    return !!(selectedQuestion?.options || selectedPack?.questions || defaultQuiz?.questions);
  }, [selectedQuestion, selectedPack, defaultQuiz]);

  // 更新配置
  const updateConfig = useCallback((updates: Partial<WheelTestConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  // 选择Quiz包
  const selectPack = useCallback(async (packId: string) => {
    try {
      const quizPackService = await Services.quizPack();
      const packResult = await quizPackService.findById(packId);
      
      if (packResult.success) {
        const pack = packResult.data;
        setSelectedPack(pack);
        
        // 加载包的问题
        const quizQuestionService = await Services.quizQuestion();
        const questionsResult = await quizQuestionService.getQuizPackQuestions(packId);
        
        if (questionsResult.success && questionsResult.data.length > 0) {
          // 自动选择第一个问题
          const firstQuestion = questionsResult.data[0];
          
          // 加载问题的选项
          const optionsResult = await quizQuestionService.getQuestionOptions(firstQuestion.id);
          if (optionsResult.success) {
            setSelectedQuestion({
              ...firstQuestion,
              options: optionsResult.data
            });
          }
        }
        
        updateConfig({ selectedPackId: packId });
      }
    } catch (error) {
      console.error('Failed to select pack:', error);
    }
  }, [updateConfig]);

  // 选择问题
  const selectQuestion = useCallback(async (questionId: string) => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const questionResult = await quizQuestionService.findById(questionId);
      
      if (questionResult.success) {
        const question = questionResult.data;
        
        // 加载问题的选项
        const optionsResult = await quizQuestionService.getQuestionOptions(questionId);
        if (optionsResult.success) {
          setSelectedQuestion({
            ...question,
            options: optionsResult.data
          });
        }
      }
    } catch (error) {
      console.error('Failed to select question:', error);
    }
  }, []);

  // 处理情绪选择
  const handleEmotionSelect = useCallback((emotion: Emotion) => {
    console.log('选择了情绪:', emotion);
    
    // 记录选择信息
    const selectionInfo = {
      emotion,
      isRealData: isUsingRealData,
      pack: selectedPack?.name || defaultQuiz?.pack.name,
      question: selectedQuestion?.question_text,
      timestamp: new Date().toISOString()
    };
    
    console.log('选择详情:', selectionInfo);
    
    // 这里可以添加实际的选择处理逻辑
    // 比如记录到数据库、触发事件等
  }, [isUsingRealData, selectedPack, defaultQuiz, selectedQuestion]);

  // 按分类加载Quiz包
  const loadPacksByCategory = useCallback(async (category: string): Promise<QuizPack[]> => {
    try {
      const packs = await getQuizPacksByCategory(category);
      setAvailablePacks(prev => {
        // 合并新的包，避免重复
        const existingIds = new Set(prev.map(p => p.id));
        const newPacks = packs.filter(p => !existingIds.has(p.id));
        return [...prev, ...newPacks];
      });
      return packs;
    } catch (error) {
      console.error('Failed to load packs by category:', error);
      return [];
    }
  }, [getQuizPacksByCategory]);

  // 刷新数据
  const refreshData = useCallback(async () => {
    await refreshHomeData();
    
    // 重新加载当前选中的包和问题
    if (config.selectedPackId) {
      await selectPack(config.selectedPackId);
    }
  }, [refreshHomeData, config.selectedPackId, selectPack]);

  // 初始化可用包列表
  useEffect(() => {
    const initializeAvailablePacks = () => {
      const packs: QuizPack[] = [];
      
      if (defaultQuiz) {
        packs.push(defaultQuiz.pack);
      }
      
      packs.push(...recommendedQuizPacks);
      
      // 去重
      const uniquePacks = packs.filter((pack, index, self) => 
        index === self.findIndex(p => p.id === pack.id)
      );
      
      setAvailablePacks(uniquePacks);
    };

    if (!homeDataLoading) {
      initializeAvailablePacks();
    }
  }, [defaultQuiz, recommendedQuizPacks, homeDataLoading]);

  // 自动选择默认包
  useEffect(() => {
    if (defaultQuiz && !config.selectedPackId) {
      selectPack(defaultQuiz.pack.id);
    }
  }, [defaultQuiz, config.selectedPackId, selectPack]);

  return {
    config,
    data: {
      currentEmotions,
      availablePacks,
      selectedPack,
      selectedQuestion,
      isUsingRealData,
    },
    actions: {
      updateConfig,
      selectPack,
      selectQuestion,
      handleEmotionSelect,
      loadPacksByCategory,
      generateTestData,
      refreshData,
    },
    isLoading: homeDataLoading,
    error: homeDataError,
  };
}
