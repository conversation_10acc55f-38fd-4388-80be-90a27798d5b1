/**
 * 皮肤服务 - 修复版本
 * 业务逻辑层，使用正确的架构模式
 */

import { BaseService } from '../base/BaseService';
import { SkinRepository } from './SkinRepository';
import { Skin } from '../../types/schema/base';
import { CreateSkinInput, UpdateSkinInput } from '../../types/schema/api';
import { ServiceResult } from '../types/ServiceTypes';
import type { SQLiteDBConnection } from '@capacitor-community/sqlite';

export interface SkinUsageStats {
  usage_count: number;
  last_used?: string;
  users_count: number;
}

export interface SkinUnlockResult {
  skin: Skin;
  unlocked: boolean;
  reason?: string;
}

export interface SkinFilter {
  category?: string;
  is_premium?: boolean;
  is_unlocked?: boolean;
  author?: string;
  tags?: string[];
  search_term?: string;
}

export class SkinService extends BaseService<
  Skin,
  CreateSkinInput,
  UpdateSkinInput
> {
  constructor(db?: SQLiteDBConnection) {
    const repository = new SkinRepository(db);
    super(repository);
  }

  /**
   * 创建新皮肤
   */
  async createSkin(input: CreateSkinInput): Promise<ServiceResult<Skin>> {
    try {
      // 验证输入
      await this.validateCreate(input);

      // 检查名称是否已存在
      const nameExists = await (this.repository as SkinRepository).isNameExists(input.name);
      if (nameExists) {
        throw new Error('Skin name already exists');
      }

      // 调用Repository创建皮肤
      const skin = await this.repository.create(input);

      // 发射业务事件
      this.emit('skinCreated', skin);

      return this.createSuccessResult(skin);
    } catch (error) {
      return this.createErrorResult('Failed to create skin', error);
    }
  }

  /**
   * 获取免费皮肤
   */
  async getFreeSkins(): Promise<ServiceResult<Skin[]>> {
    try {
      const skins = await (this.repository as SkinRepository).findFreeSkins();
      return this.createSuccessResult(skins);
    } catch (error) {
      return this.createErrorResult('Failed to get free skins', error);
    }
  }

  /**
   * 获取高级皮肤
   */
  async getPremiumSkins(): Promise<ServiceResult<Skin[]>> {
    try {
      const skins = await (this.repository as SkinRepository).findPremiumSkins();
      return this.createSuccessResult(skins);
    } catch (error) {
      return this.createErrorResult('Failed to get premium skins', error);
    }
  }

  /**
   * 获取已解锁的皮肤
   */
  async getUnlockedSkins(): Promise<ServiceResult<Skin[]>> {
    try {
      const skins = await (this.repository as SkinRepository).findUnlockedSkins();
      return this.createSuccessResult(skins);
    } catch (error) {
      return this.createErrorResult('Failed to get unlocked skins', error);
    }
  }

  /**
   * 根据分类获取皮肤
   */
  async getSkinsByCategory(category: string): Promise<ServiceResult<Skin[]>> {
    try {
      if (!category || category.trim().length === 0) {
        throw new Error('Category cannot be empty');
      }

      const skins = await (this.repository as SkinRepository).findByCategory(category.trim());
      return this.createSuccessResult(skins);
    } catch (error) {
      return this.createErrorResult('Failed to get skins by category', error);
    }
  }

  /**
   * 根据标签获取皮肤
   */
  async getSkinsByTag(tag: string): Promise<ServiceResult<Skin[]>> {
    try {
      if (!tag || tag.trim().length === 0) {
        throw new Error('Tag cannot be empty');
      }

      const skins = await (this.repository as SkinRepository).findByTag(tag.trim());
      return this.createSuccessResult(skins);
    } catch (error) {
      return this.createErrorResult('Failed to get skins by tag', error);
    }
  }

  /**
   * 搜索皮肤
   */
  async searchSkins(searchTerm: string): Promise<ServiceResult<Skin[]>> {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new Error('Search term must be at least 2 characters long');
      }

      const skins = await (this.repository as SkinRepository).searchSkins(searchTerm.trim());
      return this.createSuccessResult(skins);
    } catch (error) {
      return this.createErrorResult('Failed to search skins', error);
    }
  }

  /**
   * 获取皮肤使用统计
   */
  async getSkinUsageStats(skinId: string): Promise<ServiceResult<SkinUsageStats>> {
    try {
      const stats = await (this.repository as SkinRepository).getUsageStats(skinId);
      return this.createSuccessResult(stats);
    } catch (error) {
      return this.createErrorResult('Failed to get skin usage stats', error);
    }
  }

  /**
   * 解锁皮肤
   */
  async unlockSkin(skinId: string, userId: string, vipStatus: boolean = false): Promise<ServiceResult<SkinUnlockResult>> {
    try {
      // 获取皮肤信息
      const skin = await this.repository.findById(skinId);
      if (!skin) {
        throw new Error('Skin not found');
      }

      // 如果已经解锁，直接返回
      if (skin.is_unlocked) {
        return this.createSuccessResult({
          skin,
          unlocked: true,
          reason: 'Already unlocked'
        });
      }

      // 检查是否为高级皮肤且用户没有VIP权限
      if (skin.is_premium && !vipStatus) {
        return this.createSuccessResult({
          skin,
          unlocked: false,
          reason: 'VIP membership required for premium skins'
        });
      }

      // 检查解锁条件
      if (skin.unlock_conditions) {
        const conditionsMet = await this.checkUnlockConditions(skin.unlock_conditions, userId);
        if (!conditionsMet) {
          return this.createSuccessResult({
            skin,
            unlocked: false,
            reason: 'Unlock conditions not met'
          });
        }
      }

      // 解锁皮肤
      const unlockedSkin = await this.repository.update(skinId, { is_unlocked: true });

      // 发射业务事件
      this.emit('skinUnlocked', { skin: unlockedSkin, userId });

      return this.createSuccessResult({
        skin: unlockedSkin,
        unlocked: true,
        reason: 'Successfully unlocked'
      });
    } catch (error) {
      return this.createErrorResult('Failed to unlock skin', error);
    }
  }

  /**
   * 复制皮肤
   */
  async duplicateSkin(sourceId: string, newName: string, newAuthor?: string): Promise<ServiceResult<Skin>> {
    try {
      // 获取源皮肤
      const sourceSkin = await this.repository.findById(sourceId);
      if (!sourceSkin) {
        throw new Error('Source skin not found');
      }

      // 检查新名称是否已存在
      const nameExists = await (this.repository as SkinRepository).isNameExists(newName);
      if (nameExists) {
        throw new Error('New skin name already exists');
      }

      // 创建复制的皮肤数据
      const duplicateData: CreateSkinInput = {
        name: newName,
        description: sourceSkin.description,
        category: sourceSkin.category,
        version: '1.0.0', // 重置版本
        tags: Array.isArray(sourceSkin.tags) ? sourceSkin.tags : [sourceSkin.tags],
        preview_image_light: sourceSkin.preview_image_light,
        preview_image_dark: sourceSkin.preview_image_dark,
        is_premium: false, // 复制的皮肤默认为免费
        is_unlocked: true, // 复制的皮肤默认解锁
        unlock_conditions: undefined, // 清除解锁条件
        author: newAuthor || sourceSkin.author,
        supported_content_modes: sourceSkin.supported_content_modes,
        supported_view_types: sourceSkin.supported_view_types,
        supported_render_engines: sourceSkin.supported_render_engines,
        config: sourceSkin.config
      };

      // 创建复制的皮肤
      const duplicatedSkin = await this.repository.create(duplicateData);

      this.emit('skinDuplicated', { sourceSkin, duplicatedSkin });

      return this.createSuccessResult(duplicatedSkin);
    } catch (error) {
      return this.createErrorResult('Failed to duplicate skin', error);
    }
  }

  /**
   * 获取所有分类
   */
  async getSkinCategories(): Promise<ServiceResult<string[]>> {
    try {
      const categories = await (this.repository as SkinRepository).getCategories();
      return this.createSuccessResult(categories);
    } catch (error) {
      return this.createErrorResult('Failed to get skin categories', error);
    }
  }

  /**
   * 获取所有标签
   */
  async getAllSkinTags(): Promise<ServiceResult<string[]>> {
    try {
      const tags = await (this.repository as SkinRepository).getAllTags();
      return this.createSuccessResult(tags);
    } catch (error) {
      return this.createErrorResult('Failed to get all skin tags', error);
    }
  }

  /**
   * 获取推荐皮肤
   */
  async getRecommendedSkins(_userId: string, limit: number = 10): Promise<ServiceResult<Skin[]>> {
    try {
      // 获取所有解锁的皮肤
      const allSkins = await (this.repository as SkinRepository).findUnlockedSkins();

      // 为每个皮肤计算推荐分数
      const skinsWithScores = await Promise.all(
        allSkins.map(async (skin) => {
          const statsResult = await this.getSkinUsageStats(skin.id);
          const stats = statsResult.success ? statsResult.data! : {
            usage_count: 0,
            users_count: 0
          };

          // 推荐分数算法：使用量 + 用户数 + 新鲜度
          const daysSinceCreated = Math.floor(
            (Date.now() - new Date(skin.created_at).getTime()) / (1000 * 60 * 60 * 24)
          );
          const freshnessScore = Math.max(0, 30 - daysSinceCreated); // 新皮肤有额外分数

          const recommendationScore =
            stats.usage_count * 0.4 +
            stats.users_count * 0.4 +
            freshnessScore * 0.2;

          return {
            skin,
            score: recommendationScore
          };
        })
      );

      // 按分数排序并返回前N个
      const recommendedSkins = skinsWithScores
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
        .map(item => item.skin);

      return this.createSuccessResult(recommendedSkins);
    } catch (error) {
      return this.createErrorResult('Failed to get recommended skins', error);
    }
  }

  /**
   * 更新皮肤
   */
  async updateSkin(skinId: string, updates: UpdateSkinInput): Promise<ServiceResult<Skin>> {
    try {
      // 验证更新数据
      await this.validateUpdate(updates);

      // 如果更新名称，检查是否已存在
      if (updates.name) {
        const nameExists = await (this.repository as SkinRepository).isNameExists(updates.name, skinId);
        if (nameExists) {
          throw new Error('Skin name already exists');
        }
      }

      // 调用Repository更新
      const skin = await this.repository.update(skinId, updates);

      // 发射业务事件
      this.emit('skinUpdated', skin);

      return this.createSuccessResult(skin);
    } catch (error) {
      return this.createErrorResult('Failed to update skin', error);
    }
  }

  /**
   * 检查解锁条件
   */
  private async checkUnlockConditions(_conditions: any, _userId: string): Promise<boolean> {
    // 这里可以实现具体的解锁条件检查逻辑
    // 例如：完成特定任务、达到特定等级等

    if (!_conditions || typeof _conditions !== 'object') {
      return true; // 没有条件则默认可以解锁
    }

    // 示例条件检查
    if (_conditions.min_level && _conditions.min_level > 0) {
      // 检查用户等级（需要实现用户等级查询）
      // const userLevel = await this.getUserLevel(_userId);
      // return userLevel >= _conditions.min_level;
    }

    if (_conditions.required_achievements && Array.isArray(_conditions.required_achievements)) {
      // 检查用户成就（需要实现成就查询）
      // const userAchievements = await this.getUserAchievements(_userId);
      // return _conditions.required_achievements.every(achievement => userAchievements.includes(achievement));
    }

    return true; // 默认返回true
  }

  // 实现抽象方法
  protected async validateCreate(data: CreateSkinInput): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Skin name is required');
    }
    if (data.name.length > 100) {
      throw new Error('Skin name must be less than 100 characters');
    }
    if (!data.category || data.category.trim().length === 0) {
      throw new Error('Skin category is required');
    }
    if (data.version && !/^\d+\.\d+\.\d+$/.test(data.version)) {
      throw new Error('Version must be in format x.y.z');
    }
  }

  protected async validateUpdate(data: UpdateSkinInput): Promise<void> {
    if (data.name !== undefined && (!data.name || data.name.trim().length === 0)) {
      throw new Error('Skin name cannot be empty');
    }
    if (data.name !== undefined && data.name.length > 100) {
      throw new Error('Skin name must be less than 100 characters');
    }
    if (data.category !== undefined && (!data.category || data.category.trim().length === 0)) {
      throw new Error('Skin category cannot be empty');
    }
    if (data.version && !/^\d+\.\d+\.\d+$/.test(data.version)) {
      throw new Error('Version must be in format x.y.z');
    }
  }
}
