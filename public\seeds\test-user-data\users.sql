-- =============================================
-- 测试用户数据
-- 根据最新表结构更新
-- =============================================

-- 插入测试用户 (根据新的 users 表结构)
INSERT OR IGNORE INTO users (
    id,
    username,
    email,
    display_name,
    avatar_url,
    is_active,
    is_verified,
    is_banned,
    is_vip,
    vip_tier,
    vip_expires_at,
    last_login_at,
    login_count,
    last_active_at,
    timezone,
    locale,
    created_at,
    updated_at,
    sync_status
) VALUES
    (
        'test-user-1',
        'john_doe',
        '<EMAIL>',
        '<PERSON>',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
        1, -- is_active
        1, -- is_verified
        0, -- is_banned
        1, -- is_vip
        'premium', -- vip_tier
        datetime('now', '+1 year'), -- vip_expires_at
        datetime('now', '-1 hour'), -- last_login_at
        25, -- login_count
        datetime('now', '-30 minutes'), -- last_active_at
        'America/New_York', -- timezone
        'en', -- locale
        datetime('now', '-30 days'), -- created_at
        datetime('now', '-1 hour'), -- updated_at
        'synced' -- sync_status
    ),
    (
        'test-user-2',
        'jane_smith',
        '<EMAIL>',
        'Jane Smith',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=jane',
        1, -- is_active
        1, -- is_verified
        0, -- is_banned
        0, -- is_vip
        NULL, -- vip_tier
        NULL, -- vip_expires_at
        datetime('now', '-2 hours'), -- last_login_at
        15, -- login_count
        datetime('now', '-1 hour'), -- last_active_at
        'Asia/Shanghai', -- timezone
        'zh', -- locale
        datetime('now', '-15 days'), -- created_at
        datetime('now', '-2 hours'), -- updated_at
        'synced' -- sync_status
    ),
    (
        'test-user-3',
        'alex_chen',
        '<EMAIL>',
        'Alex Chen',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=alex',
        1, -- is_active
        0, -- is_verified (未验证用户)
        0, -- is_banned
        0, -- is_vip
        NULL, -- vip_tier
        NULL, -- vip_expires_at
        datetime('now', '-1 day'), -- last_login_at
        3, -- login_count (新用户)
        datetime('now', '-6 hours'), -- last_active_at
        'Europe/London', -- timezone
        'en', -- locale
        datetime('now', '-3 days'), -- created_at
        datetime('now', '-1 day'), -- updated_at
        'synced' -- sync_status
    );