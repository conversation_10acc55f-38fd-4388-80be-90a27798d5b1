/**
 * QuizEngineService 单元测试
 * 验证重构后的服务端专用实现
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuizEngineService } from '../QuizEngineService';

// Mock 数据库操作
vi.mock('../../database/index', () => ({
  executeQuery: vi.fn(),
  batchStatements: vi.fn()
}));

// 导入 Mock 后的模块
import { executeQuery, batchStatements } from '../../database/index';

const mockExecuteQuery = vi.mocked(executeQuery);
const mockBatchStatements = vi.mocked(batchStatements);

describe('QuizEngineService', () => {
  let quizEngineService: QuizEngineService;

  beforeEach(() => {
    quizEngineService = QuizEngineService.getInstance();
    vi.clearAllMocks();
  });

  describe('processOfflineQuizData', () => {
    it('should process offline quiz data successfully', async () => {
      const offlineData = {
        sessions: [
          {
            id: 'session1',
            pack_id: 'pack1',
            user_id: 'user1',
            status: 'COMPLETED' as const,
            current_question_index: 5,
            total_questions: 5,
            answered_questions: 5,
            skipped_questions: 0,
            completion_percentage: 100,
            start_time: '2024-01-01T00:00:00Z',
            last_active_time: '2024-01-01T00:05:00Z',
            end_time: '2024-01-01T00:05:00Z',
            session_type: 'standard',
            session_metadata: '{}',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:05:00Z'
          }
        ],
        answers: [
          {
            id: 'answer1',
            session_id: 'session1',
            question_id: 'question1',
            session_presentation_config_id: null,
            selected_option_ids: '["option1"]',
            answer_value: 'happy',
            answer_text: null,
            confidence_score: 0.8,
            response_time_ms: 2000,
            answered_at: '2024-01-01T00:01:00Z',
            created_at: '2024-01-01T00:01:00Z',
            updated_at: '2024-01-01T00:01:00Z'
          }
        ],
        results: [
          {
            id: 'result1',
            session_id: 'session1',
            user_id: 'user1',
            pack_id: 'pack1',
            total_questions: 5,
            answered_questions: 5,
            completion_percentage: 100,
            total_time_ms: 300000,
            started_at: '2024-01-01T00:00:00Z',
            completed_at: '2024-01-01T00:05:00Z',
            result_data: { score: 85 },
            created_at: '2024-01-01T00:05:00Z',
            updated_at: '2024-01-01T00:05:00Z'
          }
        ],
        metadata: {
          client_timestamp: '2024-01-01T00:05:00Z',
          sync_version: 1,
          device_id: 'device123'
        }
      };

      // Mock 数据库查询返回空结果（表示没有现有数据）
      mockExecuteQuery.mockResolvedValue({
        rows: [],
        changes: 0,
        lastInsertRowid: 0
      });

      // Mock 批量操作成功
      mockBatchStatements.mockResolvedValue([{
        rows: [],
        changes: 1,
        lastInsertRowid: 1
      }]);

      const result = await quizEngineService.processOfflineQuizData(offlineData);

      expect(result.success).toBe(true);
      expect(result.data?.processed_sessions).toBe(1);
      expect(result.data?.processed_answers).toBe(1);
      expect(result.data?.processed_results).toBe(1);
      expect(result.data?.conflicts).toHaveLength(0);

      // 验证数据库操作被正确调用
      expect(mockExecuteQuery).toHaveBeenCalledTimes(3); // 检查session、answer、result是否存在
      expect(mockBatchStatements).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            sql: expect.stringContaining('INSERT INTO quiz_sessions'),
            args: expect.arrayContaining(['session1', 'pack1', 'user1'])
          })
        ])
      );
    });

    it('should detect conflicts when data already exists', async () => {
      const offlineData = {
        sessions: [
          {
            id: 'session1',
            pack_id: 'pack1',
            user_id: 'user1',
            status: 'COMPLETED' as const,
            current_question_index: 5,
            total_questions: 5,
            answered_questions: 5,
            skipped_questions: 0,
            completion_percentage: 100,
            start_time: '2024-01-01T00:00:00Z',
            last_active_time: '2024-01-01T00:05:00Z',
            end_time: '2024-01-01T00:05:00Z',
            session_type: 'standard',
            session_metadata: '{}',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:05:00Z'
          }
        ],
        answers: [],
        metadata: {
          client_timestamp: '2024-01-01T00:05:00Z',
          sync_version: 1,
          device_id: 'device123'
        }
      };

      // Mock 数据库查询返回现有数据（不同的updated_at）
      mockExecuteQuery.mockResolvedValue({
        rows: [{
          id: 'session1',
          pack_id: 'pack1',
          user_id: 'user1',
          updated_at: '2024-01-01T00:03:00Z' // 不同的时间戳
        }],
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await quizEngineService.processOfflineQuizData(offlineData);

      expect(result.success).toBe(true);
      expect(result.data?.processed_sessions).toBe(0); // 因为有冲突，没有处理
      expect(result.data?.conflicts).toHaveLength(1);
      expect(result.data?.conflicts[0].type).toBe('session_conflict');
      expect(result.data?.conflicts[0].id).toBe('session1');
    });
  });

  describe('validateQuizData', () => {
    it('should validate quiz data successfully', async () => {
      const offlineData = {
        sessions: [
          {
            id: 'session1',
            pack_id: 'pack1',
            user_id: 'user1',
            status: 'COMPLETED' as const,
            current_question_index: 5,
            total_questions: 5,
            answered_questions: 5,
            skipped_questions: 0,
            completion_percentage: 100,
            start_time: '2024-01-01T00:00:00Z',
            last_active_time: '2024-01-01T00:05:00Z',
            end_time: '2024-01-01T00:05:00Z',
            session_type: 'standard',
            session_metadata: '{}',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:05:00Z'
          }
        ],
        answers: [
          {
            id: 'answer1',
            session_id: 'session1',
            question_id: 'question1',
            session_presentation_config_id: null,
            selected_option_ids: '["option1"]',
            answer_value: 'happy',
            answer_text: null,
            confidence_score: 0.8,
            response_time_ms: 2000,
            answered_at: '2024-01-01T00:01:00Z',
            created_at: '2024-01-01T00:01:00Z',
            updated_at: '2024-01-01T00:01:00Z'
          }
        ],
        metadata: {
          client_timestamp: '2024-01-01T00:05:00Z',
          sync_version: 1,
          device_id: 'device123'
        }
      };

      // Mock 验证查询
      mockExecuteQuery
        .mockResolvedValueOnce({ // Quiz包存在
          rows: [{ id: 'pack1' }],
          changes: 0,
          lastInsertRowid: 0
        })
        .mockResolvedValueOnce({ // 问题存在
          rows: [{ id: 'question1' }],
          changes: 0,
          lastInsertRowid: 0
        });

      const result = await quizEngineService.validateQuizData(offlineData);

      expect(result.success).toBe(true);
      expect(result.data?.is_valid).toBe(true);
      expect(result.data?.validation_errors).toHaveLength(0);
    });

    it('should detect validation errors', async () => {
      const offlineData = {
        sessions: [
          {
            id: 'session1',
            pack_id: 'nonexistent_pack',
            user_id: 'user1',
            status: 'COMPLETED' as const,
            current_question_index: 5,
            total_questions: 5,
            answered_questions: 5,
            skipped_questions: 0,
            completion_percentage: 100,
            start_time: '2024-01-01T00:00:00Z',
            last_active_time: '2024-01-01T00:05:00Z',
            end_time: '2024-01-01T00:05:00Z',
            session_type: 'standard',
            session_metadata: '{}',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:05:00Z'
          }
        ],
        answers: [],
        metadata: {
          client_timestamp: '2024-01-01T00:05:00Z',
          sync_version: 1,
          device_id: 'device123'
        }
      };

      // Mock Quiz包不存在
      mockExecuteQuery.mockResolvedValue({
        rows: [],
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await quizEngineService.validateQuizData(offlineData);

      expect(result.success).toBe(true);
      expect(result.data?.is_valid).toBe(false);
      expect(result.data?.validation_errors).toHaveLength(1);
      expect(result.data?.validation_errors[0]).toContain('Quiz pack not found');
    });
  });

  describe('syncQuizData', () => {
    it('should sync quiz data successfully', async () => {
      const syncData = {
        client_data: {
          sessions: [],
          answers: [],
          metadata: {
            client_timestamp: '2024-01-01T00:05:00Z',
            sync_version: 1,
            device_id: 'device123'
          }
        },
        last_sync_timestamp: '2024-01-01T00:00:00Z',
        conflict_resolution: 'merge' as const
      };

      // Mock 空的服务端变更
      mockExecuteQuery.mockResolvedValue({
        rows: [],
        changes: 0,
        lastInsertRowid: 0
      });

      const result = await quizEngineService.syncQuizData(syncData);

      expect(result.success).toBe(true);
      expect(result.data?.server_changes).toBeDefined();
      expect(result.data?.sync_timestamp).toBeDefined();
      expect(result.data?.conflicts_resolved).toBe(0);
    });
  });
});
