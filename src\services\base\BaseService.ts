/**
 * 基础服务类
 * 提供通用的服务功能和错误处理
 */

import type { ServiceResult } from '../types/ServiceTypes';
import type { BaseRepository } from './BaseRepository';

/**
 * 简单的浏览器兼容事件发射器
 */
class BrowserEventEmitter {
  private events: Map<string, Function[]> = new Map();

  on(event: string, listener: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.events.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event: string, ...args: any[]): void {
    const listeners = this.events.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  removeAllListeners(event?: string): void {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }
}

export abstract class BaseService<T, TCreate, TUpdate> extends BrowserEventEmitter {
  constructor(protected repository: BaseRepository<T, TCreate, TUpdate>) {
    super();
  }

  /**
   * 创建新记录
   */
  async create(data: TCreate): Promise<ServiceResult<T>> {
    try {
      // 验证数据
      await this.validateCreate(data);

      // 执行创建操作
      const result = await this.repository.create(data);

      // 发射事件
      this.emit('created', result);

      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to create record', error);
    }
  }

  /**
   * 根据ID获取记录
   */
  async findById(id: string): Promise<ServiceResult<T | null>> {
    try {
      if (!id) {
        throw new Error('ID is required');
      }

      const result = await this.repository.findById(id);
      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to find record', error);
    }
  }

  /**
   * 更新记录
   */
  async update(id: string, data: TUpdate): Promise<ServiceResult<T>> {
    try {
      if (!id) {
        throw new Error('ID is required');
      }

      // 验证数据
      await this.validateUpdate(data);

      // 执行更新操作
      const result = await this.repository.update(id, data);

      // 发射事件
      this.emit('updated', result);

      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to update record', error);
    }
  }

  /**
   * 删除记录
   */
  async delete(id: string): Promise<ServiceResult<boolean>> {
    try {
      if (!id) {
        throw new Error('ID is required');
      }

      const result = await this.repository.delete(id);

      // 发射事件
      this.emit('deleted', { id });

      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to delete record', error);
    }
  }

  /**
   * 获取记录列表
   */
  async findAll(filters?: any): Promise<ServiceResult<T[]>> {
    try {
      const result = await this.repository.findMany(filters);
      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to find records', error);
    }
  }

  /**
   * 获取记录数量
   */
  async count(filters?: any): Promise<ServiceResult<number>> {
    try {
      const result = await this.repository.count(filters);
      return this.createSuccessResult(result);
    } catch (error) {
      return this.createErrorResult('Failed to count records', error);
    }
  }

  // 抽象方法，子类需要实现
  protected abstract validateCreate(data: TCreate): Promise<void>;
  protected abstract validateUpdate(data: TUpdate): Promise<void>;

  // 辅助方法
  protected createSuccessResult<TResult>(data: TResult): ServiceResult<TResult> {
    return { success: true, data };
  }

  protected createErrorResult<TResult>(message: string, error?: any): ServiceResult<TResult> {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[${this.constructor.name}] ${message}:`, errorMessage);

    return {
      success: false,
      error: `${message}: ${errorMessage}`,
    };
  }
}
