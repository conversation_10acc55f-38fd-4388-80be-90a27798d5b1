/**
 * 颜色工具类 - 提供颜色分配和对比度检查的功能
 * 用于在不同的导航样式中复用颜色逻辑
 */

import type { ColorMode } from '@/types';

// 暖色系颜色集合 - 浅色主题
export const warmLightColors = [
  '#EA4335', // Google Red
  '#FBBC05', // Google Yellow
  '#D35400', // Orange
  '#C0392B', // Dark Red
  '#F39C12', // Amber
  '#E74C3C', // Light Red
  '#F1C40F', // Light Yellow
  '#E67E22', // Light Orange
  '#A93226', // Deep Red
  '#B9770E', // Deep Orange
  '#BA4A00', // Rust
  '#7E5109', // <PERSON>
  '#922B21', // Burgundy
  '#B03A2E', // Brick Red
  '#CB4335', // Fire Brick
  '#D35400', // Pumpkin
  '#E59866', // <PERSON> Brown
  '#DC7633', // Alloy Orange
  '#AF601A', // <PERSON>
  '#B7950B', // Dark Goldenrod
  '#B9770E', // Golden Brown
  '#D4AC0D', // Gold
  '#D68910', // Dark Orange
  '#CA6F1E', // Chocolate
];

// 冷色系颜色集合 - 浅色主题
export const coolLightColors = [
  '#4285F4', // Google Blue
  '#34A853', // Google Green
  '#8E44AD', // Purple
  '#16A085', // Teal
  '#2980B9', // Dark Blue
  '#27AE60', // Dark Green
  '#9B59B6', // Light Purple
  '#2ECC71', // Light Green
  '#3498DB', // Light Blue
  '#1ABC9C', // Light Teal
  '#7D3C98', // Deep Purple
  '#1F618D', // Deep Blue
  '#186A3B', // Deep Green
  '#884EA0', // Medium Purple
  '#0E6655', // Deep Teal
  '#0B5345', // Forest Green
  '#1B4F72', // Navy Blue
  '#633974', // Violet
  '#154360', // Midnight Blue
  '#1A5276', // Dark Blue
  '#21618C', // Medium Blue
  '#2874A6', // Steel Blue
  '#148F77', // Sea Green
  '#117864', // Teal Green
];

// 暖色系颜色集合 - 深色主题
export const warmDarkColors = [
  '#F28B82', // Light Red
  '#FDD663', // Light Yellow
  '#FCAD70', // Light Orange
  '#EF9A9A', // Soft Red
  '#FFCC80', // Soft Orange
  '#FFF59D', // Soft Yellow
  '#F48FB1', // Soft Pink
  '#FFAB91', // Soft Deep Orange
  '#FF8A65', // Bright Orange
  '#FFD54F', // Bright Yellow
  '#FF80AB', // Bright Pink
  '#FFE57F', // Amber
  '#FF9E80', // Deep Orange
  '#FFAB91', // Peach
  '#FFCCBC', // Light Salmon
  '#FFAB91', // Light Coral
  '#FF8A65', // Coral
  '#FF7043', // Deep Orange
  '#FFAB40', // Amber
  '#FFD740', // Yellow
  '#FFCA28', // Amber
  '#FFC107', // Amber
  '#FFB300', // Amber
  '#FFA000', // Amber
];

// 冷色系颜色集合 - 深色主题
export const coolDarkColors = [
  '#8AB4F8', // Light Blue
  '#81C995', // Light Green
  '#D7AEFB', // Light Purple
  '#78D9EC', // Light Teal
  '#90CAF9', // Soft Blue
  '#A5D6A7', // Soft Green
  '#CE93D8', // Soft Purple
  '#80DEEA', // Soft Teal
  '#81D4FA', // Soft Sky Blue
  '#B39DDB', // Soft Deep Purple
  '#64FFDA', // Bright Teal
  '#EA80FC', // Bright Purple
  '#80D8FF', // Bright Blue
  '#CCFF90', // Bright Green
  '#A7FFEB', // Aqua
  '#B388FF', // Lavender
  '#84FFFF', // Cyan
  '#80CBC4', // Teal
  '#80DEEA', // Cyan
  '#81D4FA', // Light Blue
  '#90CAF9', // Blue
  '#82B1FF', // Blue
  '#B39DDB', // Purple
  '#9FA8DA', // Indigo
];

// 默认颜色集合 - 浅色主题（混合暖色和冷色）
export const lightModeColors = [
  ...warmLightColors.slice(0, 12),
  ...coolLightColors.slice(0, 12),
  ...warmLightColors.slice(12, 18),
  ...coolLightColors.slice(12, 18),
];

// 默认颜色集合 - 深色主题（混合暖色和冷色）
export const darkModeColors = [
  ...warmDarkColors.slice(0, 12),
  ...coolDarkColors.slice(0, 12),
  ...warmDarkColors.slice(12, 18),
  ...coolDarkColors.slice(12, 18),
];

// 游戏风格颜色集合 - 浅色主题（类似Candy Crush, Royal Match等游戏）
export const gameLightColors = [
  // 高对比度颜色对 - 专为 Neutral 和 Happy 设计
  '#9C27B0', // 深紫色 (Neutral)
  '#FFEB3B', // 亮黄色 (Happy)
  '#6A1B9A', // 更深紫色 (Neutral 变体)
  '#FFC107', // 琥珀色 (Happy 变体)
  '#8E24AA', // 中紫色 (Neutral 变体)
  '#CDDC39', // 酸橙色 (Happy 变体)

  // Candy Crush 真实颜色（基于截图，增强对比度）
  '#D500F9', // 亮紫色糖果 (增强亮度)
  '#FF6D00', // 深橙色糖果 (增强饱和度)
  '#FFD600', // 亮黄色糖果 (增强亮度)
  '#00C853', // 亮绿色糖果 (增强饱和度)
  '#2979FF', // 亮蓝色糖果 (增强亮度)
  '#FF1744', // 亮红色糖果 (增强饱和度)

  // Candy Crush 扩展颜色（增强对比度）
  '#AA00FF', // 深紫色
  '#FF3D00', // 深橙色
  '#FFAB00', // 琥珀色
  '#00E676', // 亮绿色
  '#00B0FF', // 亮蓝色
  '#F50057', // 亮粉色

  // Royal Match 风格颜色（增强对比度）
  '#FF1744', // 亮红色
  '#FF9100', // 亮橙色
  '#FFEA00', // 亮黄色
  '#00E676', // 亮绿色
  '#2979FF', // 亮蓝色
  '#D500F9', // 亮紫色
  '#FF4081', // 亮粉色

  // Block Blast 风格颜色（增强对比度）
  '#F44336', // 亮红色
  '#FF9800', // 亮橙色
  '#FFEB3B', // 亮黄色
  '#4CAF50', // 亮绿色
  '#2196F3', // 亮蓝色
  '#9C27B0', // 亮紫色
  '#E91E63', // 亮粉色
];

// 游戏风格颜色集合 - 深色主题
export const gameDarkColors = [
  // 高对比度颜色对 - 专为 Neutral 和 Happy 设计（深色主题）
  '#E040FB', // 亮紫色 (Neutral)
  '#FFFF00', // 亮黄色 (Happy)
  '#AA00FF', // 深紫色 (Neutral 变体)
  '#FFD600', // 亮金黄色 (Happy 变体)
  '#D500F9', // 亮紫色 (Neutral 变体)
  '#EEFF41', // 亮酸橙色 (Happy 变体)

  // Candy Crush 真实颜色（深色主题，增强对比度）
  '#EA80FC', // 亮紫色糖果 (增强亮度)
  '#FFAB40', // 亮橙色糖果 (增强亮度)
  '#FFFF00', // 亮黄色糖果 (增强亮度)
  '#69F0AE', // 亮绿色糖果 (增强亮度)
  '#40C4FF', // 亮蓝色糖果 (增强亮度)
  '#FF5252', // 亮红色糖果 (增强亮度)

  // Candy Crush 扩展颜色（深色主题，增强对比度）
  '#CE93D8', // 亮紫色
  '#FFAB40', // 亮橙色
  '#FFF176', // 亮黄色
  '#81C784', // 亮绿色
  '#4FC3F7', // 亮蓝色
  '#F06292', // 亮粉色
  '#7986CB', // 亮靛蓝色
  '#4DD0E1', // 亮青色
  '#FF8A65', // 亮珊瑚色

  // Royal Match 深色风格（增强对比度）
  '#FF8A80', // 亮红色
  '#FFAB40', // 亮橙色
  '#FFFF8D', // 亮黄色
  '#B9F6CA', // 亮绿色
  '#80D8FF', // 亮蓝色
  '#EA80FC', // 亮紫色
  '#FF80AB', // 亮粉色

  // Block Blast 深色风格（增强对比度）
  '#EF5350', // 亮红色
  '#FFA726', // 亮橙色
  '#FFEE58', // 亮黄色
  '#66BB6A', // 亮绿色
  '#42A5F5', // 亮蓝色
  '#AB47BC', // 亮紫色
  '#EC407A', // 亮粉色
];

/**
 * 获取当前背景颜色
 * @returns 背景颜色的十六进制或RGB表示
 */
export const getBackgroundColor = (): string => {
  // 在Node.js环境中，没有window对象
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    // 在测试环境中返回默认颜色
    return '#ffffff';
  }

  // 获取文档的背景颜色
  const bodyBgColor = window.getComputedStyle(document.body).background_color;
  return bodyBgColor || '#ffffff'; // 默认为白色
};

/**
 * 将RGB颜色字符串转换为RGB对象
 * @param color RGB颜色字符串，如 "rgb(255, 0, 0)"
 * @returns RGB对象 {r, g, b}
 */
export const parseRgb = (color: string): { r: number; g: number; b: number } => {
  // 处理rgb(r, g, b)格式
  const rgbMatch = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i);
  if (rgbMatch) {
    return {
      r: Number.parseInt(rgbMatch[1], 10) / 255,
      g: Number.parseInt(rgbMatch[2], 10) / 255,
      b: Number.parseInt(rgbMatch[3], 10) / 255,
    };
  }

  // 处理rgba(r, g, b, a)格式
  const rgbaMatch = color.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]+\s*\)/i);
  if (rgbaMatch) {
    return {
      r: Number.parseInt(rgbaMatch[1], 10) / 255,
      g: Number.parseInt(rgbaMatch[2], 10) / 255,
      b: Number.parseInt(rgbaMatch[3], 10) / 255,
    };
  }

  // 处理十六进制格式
  let hex = color.replace('#', '');
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  if (hex.length === 6) {
    const r = Number.parseInt(hex.substring(0, 2), 16) / 255;
    const g = Number.parseInt(hex.substring(2, 4), 16) / 255;
    const b = Number.parseInt(hex.substring(4, 6), 16) / 255;
    return { r, g, b };
  }

  // 默认返回黑色
  return { r: 0, g: 0, b: 0 };
};

/**
 * 计算两个颜色之间的对比度
 * @param color1 第一个颜色
 * @param color2 第二个颜色
 * @returns 对比度比率
 */
export const calculateContrastRatio = (color1: string, color2: string): number => {
  const rgb1 = parseRgb(color1);
  const rgb2 = parseRgb(color2);

  // 计算相对亮度
  const getLuminance = (rgb: { r: number; g: number; b: number }) => {
    const sRGB = {
      r: rgb.r,
      g: rgb.g,
      b: rgb.b,
    };

    // 转换为线性RGB
    const linearRGB = {
      r: sRGB.r <= 0.03928 ? sRGB.r / 12.92 : ((sRGB.r + 0.055) / 1.055) ** 2.4,
      g: sRGB.g <= 0.03928 ? sRGB.g / 12.92 : ((sRGB.g + 0.055) / 1.055) ** 2.4,
      b: sRGB.b <= 0.03928 ? sRGB.b / 12.92 : ((sRGB.b + 0.055) / 1.055) ** 2.4,
    };

    return 0.2126 * linearRGB.r + 0.7152 * linearRGB.g + 0.0722 * linearRGB.b;
  };

  const l1 = getLuminance(rgb1);
  const l2 = getLuminance(rgb2);

  // 计算对比度比率
  return (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
};

/**
 * 将RGB颜色转换为HSL颜色
 * @param r 红色分量 (0-1)
 * @param g 绿色分量 (0-1)
 * @param b 蓝色分量 (0-1)
 * @returns HSL颜色对象 {h, s, l}，其中h为0-360度，s和l为0-1
 */
export const rgbToHsl = (r: number, g: number, b: number): { h: number; s: number; l: number } => {
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }

    h *= 60;
  }

  return { h, s, l };
};

/**
 * 计算两个颜色之间的相似度
 * @param color1 第一个颜色
 * @param color2 第二个颜色
 * @returns 相似度值（0-1之间，值越小表示越相似）
 */
export const calculateColorSimilarity = (color1: string, color2: string): number => {
  const rgb1 = parseRgb(color1);
  const rgb2 = parseRgb(color2);

  // 计算RGB空间中的欧几里得距离
  const rDiff = rgb1.r - rgb2.r;
  const gDiff = rgb1.g - rgb2.g;
  const bDiff = rgb1.b - rgb2.b;

  // 归一化距离值到0-1范围
  return Math.sqrt(rDiff * rDiff + gDiff * gDiff + bDiff * bDiff) / Math.sqrt(3);
};

/**
 * 检查两个颜色是否太相似
 * @param color1 第一个颜色
 * @param color2 第二个颜色
 * @param tier 情绪层级
 * @param colorMode 颜色模式
 * @returns 如果颜色太相似，则返回true
 */
export const areColorsTooSimilar = (
  color1: string,
  color2: string,
  tier: 'primary' | 'secondary' | 'tertiary' = 'primary',
  colorMode: ColorMode = 'mixed'
): boolean => {
  try {
    // 计算颜色相似度
    const similarity = calculateColorSimilarity(color1, color2);

    // 计算对比度
    const contrastRatio = calculateContrastRatio(color1, color2);

    // 根据tier和colorMode调整相似度阈值
    let similarityThreshold = 0.3; // 基础阈值
    let contrastThreshold = 1.8; // 基础对比度阈值

    // 游戏风格颜色需要更高的对比度
    if (colorMode === 'game') {
      similarityThreshold = 0.4; // 游戏风格颜色可以更不同
      contrastThreshold = 2.5; // 游戏风格颜色需要更高的对比度
    } else {
      if (tier === 'tertiary') {
        // 第三级情绪需要更高的区分度
        similarityThreshold = 0.35;
        contrastThreshold = 2.0;
      } else if (tier === 'secondary') {
        // 第二级情绪使用标准阈值
        similarityThreshold = 0.3;
        contrastThreshold = 1.8;
      } else {
        // 第一级情绪可以稍微宽松一些，但仍需要足够的对比度
        similarityThreshold = 0.25;
        contrastThreshold = 1.5;
      }
    }

    // 如果相似度低于阈值或对比度低于阈值，则认为颜色太相似
    return similarity < similarityThreshold || contrastRatio < contrastThreshold;
  } catch (e) {
    console.error('Error checking color similarity:', e);
    return false;
  }
};

/**
 * 检查颜色是否与背景色太相似
 * @param color 要检查的颜色
 * @param isDarkMode 是否为深色模式
 * @param tier 情绪层级
 * @param colorMode 颜色模式
 * @returns 如果颜色与背景色太相似，则返回true
 */
export const isTooSimilarToBackground = (
  color: string,
  isDarkMode: boolean,
  tier: 'primary' | 'secondary' | 'tertiary' = 'primary',
  colorMode: ColorMode = 'mixed'
): boolean => {
  try {
    const bgColor = getBackgroundColor();
    if (!bgColor) return false;

    // 简单的相似度检查 - 如果是完全相同的颜色
    if (color.toLowerCase() === bgColor.toLowerCase()) return true;

    // 计算对比度
    const contrastRatio = calculateContrastRatio(color, bgColor);

    // 计算颜色相似度
    const colorSimilarity = calculateColorSimilarity(color, bgColor);

    // 根据tier和colorMode调整对比度阈值
    let contrastThreshold = 4.5; // 提高基础阈值，确保更好的对比度
    let similarityThreshold = 0.4; // 相似度阈值
    let brightnessCutoffLight = 0.75; // 浅色模式下的亮度阈值
    let brightnessCutoffDark = 0.25; // 深色模式下的亮度阈值

    // 游戏风格颜色需要更高的对比度和更鲜艳的颜色
    if (colorMode === 'game') {
      // 游戏风格颜色通常更鲜艳，可以降低对比度要求
      contrastThreshold = 3.5;
      // 但需要更高的相似度阈值，确保颜色更加不同
      similarityThreshold = 0.5;
      // 游戏风格颜色可以更亮一些
      brightnessCutoffLight = 0.85;
      // 游戏风格颜色在深色模式下可以更暗一些
      brightnessCutoffDark = 0.2;
    } else {
      if (tier === 'tertiary') {
        // 第三级情绪需要更高的对比度
        contrastThreshold = 5.0;
        similarityThreshold = 0.45;
      } else if (tier === 'secondary') {
        // 第二级情绪使用较高阈值
        contrastThreshold = 4.5;
        similarityThreshold = 0.4;
      } else {
        // 第一级情绪使用标准阈值
        contrastThreshold = 4.0;
        similarityThreshold = 0.35;
      }
    }

    // 检查是否是浅色系颜色（在浅色模式下排除）
    if (!isDarkMode) {
      const rgb = parseRgb(color);
      // 计算颜色的亮度 (使用加权平均法，更符合人眼感知)
      const brightness = 0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b;
      // 如果是浅色系颜色（亮度大于阈值），在浅色模式下排除
      if (brightness > brightnessCutoffLight) {
        return true;
      }
    }

    // 检查是否是深色系颜色（在深色模式下排除）
    if (isDarkMode) {
      const rgb = parseRgb(color);
      // 计算颜色的亮度 (使用加权平均法)
      const brightness = 0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b;
      // 如果是深色系颜色（亮度小于阈值），在深色模式下排除
      if (brightness < brightnessCutoffDark) {
        return true;
      }
    }

    // 增强对比度检查：检查颜色是否与背景色在同一色系
    const bgRgb = parseRgb(bgColor);
    const colorRgb = parseRgb(color);

    // 计算色相差异
    const bgHue = rgbToHsl(bgRgb.r, bgRgb.g, bgRgb.b).h;
    const colorHue = rgbToHsl(colorRgb.r, colorRgb.g, colorRgb.b).h;

    // 计算色相差异（考虑色环的循环性）
    const hueDiff = Math.min(Math.abs(colorHue - bgHue), 360 - Math.abs(colorHue - bgHue));

    // 如果色相差异小于30度，认为颜色在同一色系，需要更高的对比度
    if (hueDiff < 30) {
      // 同色系需要更高的对比度和亮度差异
      contrastThreshold += 1.5;
      similarityThreshold += 0.1;
    }

    // 如果对比度太低或相似度太高，则认为颜色与背景色太相似
    return contrastRatio < contrastThreshold || colorSimilarity < similarityThreshold;
  } catch (e) {
    console.error('Error checking color similarity:', e);
    return false;
  }
};

/**
 * 获取颜色池
 * @param colorMode 颜色模式
 * @param isDarkMode 是否为深色模式
 * @returns 颜色池数组
 */
export const getColorPool = (colorMode: ColorMode, isDarkMode: boolean): string[] => {
  switch (colorMode) {
    case 'warm':
      return isDarkMode ? [...warmDarkColors] : [...warmLightColors];
    case 'cool':
      return isDarkMode ? [...coolDarkColors] : [...coolLightColors];
    case 'game':
      return isDarkMode ? [...gameDarkColors] : [...gameLightColors];
    default:
      return isDarkMode ? [...darkModeColors] : [...lightModeColors];
  }
};

/**
 * 为情绪分配颜色
 * @param emotions 情绪数组
 * @param isDarkMode 是否为深色模式
 * @param tier 情绪层级
 * @param colorMode 颜色模式
 * @returns 带有颜色的情绪数组
 */
export const assignColorsToEmotions = <T extends { id: string; color?: string; name: string }>(
  emotions: T[],
  isDarkMode: boolean,
  tier: 'primary' | 'secondary' | 'tertiary' = 'primary',
  colorMode: ColorMode = 'mixed'
): (T & { color: string })[] => {
  // 如果没有情绪，直接返回空数组
  if (emotions.length === 0) {
    return [];
  }

  // 过滤掉占位符情绪
  const realEmotions = emotions.filter((e) => !e.id.startsWith('placeholder'));

  // 如果没有真实情绪，只处理占位符
  if (realEmotions.length === 0) {
    // 只有占位符的情况，直接返回带透明颜色的占位符
    return emotions.map(emotion => ({
      ...emotion,
      color: 'transparent'
    } as T & { color: string }));
  }

  // 根据颜色模式和主题选择颜色数组
  const defaultColors = getColorPool(colorMode, isDarkMode);

  // 过滤掉与背景色太相似的颜色，传入颜色模式
  const filteredColors = defaultColors.filter(
    (color) => !isTooSimilarToBackground(color, isDarkMode, tier, colorMode)
  );

  // 如果过滤后颜色太少，使用原始颜色池
  // 对于游戏风格颜色，我们需要确保有足够的鲜艳颜色
  const colorPool =
    filteredColors.length < realEmotions.length / 2
      ? colorMode === 'game'
        ? defaultColors.slice(0, Math.min(defaultColors.length, realEmotions.length * 3)) // 游戏风格使用前面的鲜艳颜色
        : defaultColors
      : filteredColors;

  // 为每个情绪分配唯一的颜色
  const usedColors: string[] = [];
  const result: (T & { color: string })[] = [];
  const placeholders: T[] = [];

  // 先分离占位符和真实情绪
  emotions.forEach((emotion) => {
    if (emotion.id.startsWith('placeholder')) {
      placeholders.push(emotion);
    }
  });

  // 特殊处理 Neutral 和 Happy 情绪对
  // 这是为了解决它们之间对比度不足的问题
  if (colorMode === 'game' && realEmotions.length >= 2) {
    const neutralIndex = realEmotions.findIndex((e) => e.name === 'Neutral');
    const happyIndex = realEmotions.findIndex((e) => e.name === 'Happy');

    // 如果同时存在 Neutral 和 Happy
    if (neutralIndex !== -1 && happyIndex !== -1) {
      // 从颜色池中选择专门为 Neutral 和 Happy 设计的高对比度颜色对
      const neutralColors = isDarkMode
        ? ['#E040FB', '#AA00FF', '#D500F9'] // 深色主题下的紫色系
        : ['#9C27B0', '#6A1B9A', '#8E24AA']; // 浅色主题下的紫色系

      const happyColors = isDarkMode
        ? ['#FFFF00', '#FFD600', '#EEFF41'] // 深色主题下的黄色系
        : ['#FFEB3B', '#FFC107', '#CDDC39']; // 浅色主题下的黄色系

      // 为 Neutral 分配紫色
      const neutralColor = neutralColors[0];
      usedColors[neutralIndex] = neutralColor;

      // 为 Happy 分配黄色
      const happyColor = happyColors[0];
      usedColors[happyIndex] = happyColor;

      // 验证对比度
      const contrastRatio = calculateContrastRatio(neutralColor, happyColor);
      console.log(`Neutral 和 Happy 之间的对比度: ${contrastRatio.toFixed(3)}`);

      // 如果对比度不足，尝试其他颜色组合
      if (contrastRatio < 2.0) {
        for (let i = 0; i < neutralColors.length; i++) {
          for (let j = 0; j < happyColors.length; j++) {
            if (i === 0 && j === 0) continue; // 跳过已经测试过的组合

            const newNeutralColor = neutralColors[i];
            const newHappyColor = happyColors[j];
            const newContrastRatio = calculateContrastRatio(newNeutralColor, newHappyColor);

            if (newContrastRatio >= 2.0) {
              usedColors[neutralIndex] = newNeutralColor;
              usedColors[happyIndex] = newHappyColor;
              console.log(`找到更好的颜色组合，对比度: ${newContrastRatio.toFixed(3)}`);
              break;
            }
          }
          if (calculateContrastRatio(usedColors[neutralIndex], usedColors[happyIndex]) >= 2.0) {
            break;
          }
        }
      }
    }
  }

  // 使用改进的贪心算法分配颜色，确保相邻扇区颜色对比度高
  for (let i = 0; i < realEmotions.length; i++) {
    const emotion = realEmotions[i];

    // 如果情绪已有颜色且不是默认灰色，优先使用该颜色
    let color = emotion.color && emotion.color !== '#cccccc' ? emotion.color : '';

    // 如果颜色与背景色太相似，不使用该颜色
    if (color && isTooSimilarToBackground(color, isDarkMode, tier, colorMode)) {
      color = '';
    }

    // 如果没有颜色或颜色已被使用，从颜色池中选择一个合适的颜色
    if (!color || usedColors.includes(color)) {
      // 获取相邻情绪的颜色（如果已分配）
      const prevIndex = (i - 1 + realEmotions.length) % realEmotions.length; // 循环索引
      const nextIndex = (i + 1) % realEmotions.length; // 循环索引

      const prevColor =
        i > 0
          ? usedColors[prevIndex]
          : realEmotions.length > 1
            ? usedColors[realEmotions.length - 1]
            : null;
      const nextColor =
        i < realEmotions.length - 1
          ? usedColors[nextIndex] || null
          : realEmotions.length > 1
            ? usedColors[0]
            : null;

      // 对颜色池进行评分，选择最适合的颜色
      let bestColor = '';
      let bestScore = -1;

      // 创建候选颜色列表，确保有足够的颜色可选
      const candidates = [...colorPool];

      // 如果候选颜色不足，添加一些随机生成的颜色
      if (candidates.length < realEmotions.length * 3) {
        // 增加候选颜色数量
        for (let j = 0; j < realEmotions.length * 2; j++) {
          // 生成更多随机颜色
          candidates.push(
            generateRandomColor(
              isDarkMode,
              tier,
              usedColors,
              getBackgroundColor(),
              undefined,
              colorMode
            )
          );
        }
      }

      // 对候选颜色进行预筛选，移除与背景色太相似的颜色
      const filteredCandidates = candidates.filter(
        (candidateColor) => !isTooSimilarToBackground(candidateColor, isDarkMode, tier, colorMode)
      );

      // 如果筛选后的候选颜色太少，使用原始候选颜色
      const finalCandidates =
        filteredCandidates.length < realEmotions.length ? candidates : filteredCandidates;

      for (const candidateColor of finalCandidates) {
        // 如果颜色已被使用，跳过
        if (usedColors.includes(candidateColor)) {
          continue;
        }

        // 计算与背景色的对比度
        const bgContrastRatio = calculateContrastRatio(candidateColor, getBackgroundColor());

        // 初始分数基于与背景色的对比度
        let score = bgContrastRatio * 5; // 增加背景对比度的权重

        // 设置对比度阈值，根据颜色模式调整
        let minContrastRatio = 2.0; // 提高最小对比度要求
        let minSimilarity = 0.35; // 提高最小相似度要求

        // 游戏风格颜色需要更高的对比度
        if (colorMode === 'game') {
          minContrastRatio = 2.8; // 提高游戏风格的对比度要求
          minSimilarity = 0.45; // 提高游戏风格的相似度要求
        } else if (tier === 'tertiary') {
          minContrastRatio = 2.2;
          minSimilarity = 0.4;
        }

        // 如果有前一个颜色，考虑与前一个颜色的对比度
        if (prevColor) {
          const prevContrastRatio = calculateContrastRatio(candidateColor, prevColor);
          const prevSimilarity = calculateColorSimilarity(candidateColor, prevColor);

          // 对比度越高，相似度越低，分数越高
          score += prevContrastRatio * 6 + prevSimilarity * 20; // 增加相邻颜色对比度的权重

          // 如果对比度太低或相似度太高，大幅降低分数
          if (prevContrastRatio < minContrastRatio || prevSimilarity < minSimilarity) {
            score -= 80; // 增加惩罚力度

            // 对于游戏风格，如果对比度特别低，直接跳过这个颜色
            if (colorMode === 'game' && prevContrastRatio < 1.8) {
              continue;
            }
          }
        }

        // 如果有下一个颜色，考虑与下一个颜色的对比度
        if (nextColor) {
          const nextContrastRatio = calculateContrastRatio(candidateColor, nextColor);
          const nextSimilarity = calculateColorSimilarity(candidateColor, nextColor);

          // 对比度越高，相似度越低，分数越高
          score += nextContrastRatio * 6 + nextSimilarity * 20; // 增加相邻颜色对比度的权重

          // 如果对比度太低或相似度太高，大幅降低分数
          if (nextContrastRatio < minContrastRatio || nextSimilarity < minSimilarity) {
            score -= 80; // 增加惩罚力度

            // 对于游戏风格，如果对比度特别低，直接跳过这个颜色
            if (colorMode === 'game' && nextContrastRatio < 1.8) {
              continue;
            }
          }
        }

        // 检查颜色是否与背景色在同一色系
        const bgRgb = parseRgb(getBackgroundColor());
        const colorRgb = parseRgb(candidateColor);

        // 计算色相差异
        const bgHue = rgbToHsl(bgRgb.r, bgRgb.g, bgRgb.b).h;
        const colorHue = rgbToHsl(colorRgb.r, colorRgb.g, colorRgb.b).h;

        // 计算色相差异（考虑色环的循环性）
        const hueDiff = Math.min(Math.abs(colorHue - bgHue), 360 - Math.abs(colorHue - bgHue));

        // 如果色相差异小于30度，认为颜色在同一色系，降低分数
        if (hueDiff < 30) {
          score -= 40;
        }

        // 更新最佳颜色
        if (score > bestScore) {
          bestScore = score;
          bestColor = candidateColor;
        }
      }

      // 如果找到了合适的颜色，使用它
      if (bestColor) {
        color = bestColor;
      } else {
        // 如果没有找到合适的颜色，生成一个随机颜色
        color = generateRandomColor(
          isDarkMode,
          tier,
          usedColors,
          getBackgroundColor(),
          prevColor,
          colorMode
        );
      }
    }

    // 记录已使用的颜色
    usedColors.push(color);

    // 添加到结果数组
    result.push({
      ...emotion,
      color,
    });
  }

  // 最后一次检查：确保第一个和最后一个颜色不会太相似
  if (realEmotions.length > 1) {
    const firstColor = result[0].color;
    const lastColor = result[result.length - 1].color;

    if (areColorsTooSimilar(firstColor, lastColor, tier, colorMode)) {
      // 如果太相似，重新生成最后一个颜色
      const newLastColor = generateRandomColor(
        isDarkMode,
        tier,
        usedColors.slice(0, -1),
        getBackgroundColor(),
        firstColor,
        colorMode
      );

      // 更新最后一个颜色
      result[result.length - 1] = {
        ...result[result.length - 1],
        color: newLastColor,
      };

      // 更新已使用的颜色数组
      usedColors[usedColors.length - 1] = newLastColor;
    }
  }

  // 添加占位符情绪，使用透明颜色
  placeholders.forEach((placeholder) => {
    result.push({
      ...placeholder,
      color: 'transparent',
    } as T & { color: string });
  });

  // 重新按照原始情绪顺序排序结果
  const finalResult: (T & { color: string })[] = [];
  emotions.forEach((emotion) => {
    const matchingResult = result.find((r) => r.id === emotion.id);
    if (matchingResult) {
      finalResult.push(matchingResult);
    }
  });

  return finalResult;
};

/**
 * 生成随机颜色，确保与已使用的颜色和背景色有足够的对比度
 * @param isDarkMode 是否为深色模式
 * @param tier 情绪层级
 * @param usedColors 已使用的颜色数组
 * @param bgColor 背景色
 * @param avoidColor 需要避免相似的特定颜色（可选）
 * @param colorMode 颜色模式（可选）
 * @returns 生成的颜色
 */
const generateRandomColor = (
  isDarkMode: boolean,
  tier: 'primary' | 'secondary' | 'tertiary',
  usedColors: string[],
  bgColor: string,
  avoidColor?: string,
  colorMode: ColorMode = 'mixed'
): string => {
  let attempts = 0;
  let generatedColor = '';
  let bestColor = '';
  let bestScore = 0;

  // 设置对比度阈值，根据tier和colorMode调整
  let contrastThreshold = 4.5;

  // 游戏风格颜色需要更高的对比度和更鲜艳的颜色
  if (colorMode === 'game') {
    contrastThreshold = 3.5; // 游戏风格可以接受稍低的对比度，因为颜色更鲜艳
  } else {
    if (tier === 'tertiary') {
      contrastThreshold = 5.0;
    } else if (tier === 'secondary') {
      contrastThreshold = 4.5;
    } else {
      contrastThreshold = 4.0;
    }
  }

  // 尝试多次生成颜色，选择最佳的一个
  while (attempts < 50) {
    // 增加尝试次数，提高找到好颜色的概率
    // 使用均匀分布的色相值，避免相似色相
    let hue: number;

    // 获取背景色的色相
    const bgRgb = parseRgb(bgColor);
    const bgHsl = rgbToHsl(bgRgb.r, bgRgb.g, bgRgb.b);
    const bgHue = bgHsl.h;

    if (usedColors.length > 0) {
      // 尝试选择与已有颜色色相差距较大的值
      const usedHues = usedColors.map((color) => {
        try {
          const rgb = parseRgb(color);
          return rgbToHsl(rgb.r, rgb.g, rgb.b).h;
        } catch {
          return Math.random() * 360;
        }
      });

      // 添加背景色的色相到已使用色相列表
      usedHues.push(bgHue);

      // 找到色相空间中的最大间隙
      let maxGap = 0;
      let gapStart = 0;

      // 对已使用的色相排序
      const sortedHues = [...usedHues].sort((a, b) => a - b);

      // 检查每对相邻色相之间的间隙
      for (let i = 0; i < sortedHues.length; i++) {
        const current = sortedHues[i];
        const next = sortedHues[(i + 1) % sortedHues.length];
        const gap = next > current ? next - current : next + 360 - current;

        if (gap > maxGap) {
          maxGap = gap;
          gapStart = current;
        }
      }

      // 在最大间隙中选择一个色相值
      hue = (gapStart + maxGap / 2) % 360;

      // 确保新色相与背景色相差至少30度
      const hueDiffToBg = Math.min(Math.abs(hue - bgHue), 360 - Math.abs(hue - bgHue));
      if (hueDiffToBg < 30) {
        // 如果太接近背景色相，调整色相值
        hue = (bgHue + 180) % 360; // 选择补色
      }
    } else {
      // 如果没有已使用的颜色，选择与背景色相差较大的色相
      // 优先选择补色（色相差180度）
      hue = (bgHue + 180) % 360;

      // 添加一些随机性
      hue = (hue + (Math.random() * 60 - 30)) % 360;
      if (hue < 0) hue += 360;
    }

    let saturation: number;
    let lightness: number;

    // 根据颜色模式调整饱和度和亮度
    if (colorMode === 'game') {
      // 游戏风格颜色：更加鲜艳、饱和度更高
      if (isDarkMode) {
        // 深色主题：更鲜艳、更亮的颜色
        saturation = 90 + Math.floor(Math.random() * 10); // 90-100%，非常高的饱和度
        lightness = 70 + Math.floor(Math.random() * 15); // 70-85%，更亮的颜色
      } else {
        // 浅色主题：更深沉、更饱和的颜色
        saturation = 90 + Math.floor(Math.random() * 10); // 90-100%，非常高的饱和度
        lightness = 40 + Math.floor(Math.random() * 15); // 40-55%，中等亮度
      }
    } else {
      if (isDarkMode) {
        // 深色主题：更鲜艳、更亮的颜色
        saturation = 85 + Math.floor(Math.random() * 15); // 85-100%，增加饱和度
        lightness = 65 + Math.floor(Math.random() * 15); // 65-80%，增加亮度
      } else {
        // 浅色主题：更深沉、更饱和的颜色
        saturation = 85 + Math.floor(Math.random() * 15); // 85-100%，增加饱和度
        lightness = 25 + Math.floor(Math.random() * 15); // 25-40%，降低亮度增加对比度
      }
    }

    // 特殊处理tier3情况，进一步增强对比度
    if (tier === 'tertiary') {
      if (isDarkMode) {
        // 深色主题下的tier3，使用更亮的颜色
        lightness = Math.min(lightness + 10, 85);
      } else {
        // 浅色主题下的tier3，使用更深的颜色
        lightness = Math.max(lightness - 5, 20);
      }
      // 增加饱和度，使颜色更鲜明
      saturation = Math.min(saturation + 10, 100);
    }

    // 对于游戏风格，使用更加鲜艳的颜色
    if (colorMode === 'game') {
      // 确保饱和度非常高
      saturation = Math.max(saturation, 90);

      // 确保亮度适中，不会太暗或太亮
      if (isDarkMode) {
        lightness = Math.max(lightness, 60); // 确保在深色模式下足够亮
      } else {
        lightness = Math.min(Math.max(lightness, 40), 70); // 确保在浅色模式下不会太亮或太暗
      }
    }

    // 确保与背景色有足够的亮度差异
    const bgLightness = bgHsl.l;
    const lightnessDiff = Math.abs(lightness / 100 - bgLightness);

    // 如果亮度差异不够，调整亮度
    if (lightnessDiff < 0.3) {
      if (bgLightness > 0.5) {
        // 背景较亮，使颜色更暗
        lightness = Math.max(Math.min(bgLightness * 100 - 30, 50), 20);
      } else {
        // 背景较暗，使颜色更亮
        lightness = Math.min(Math.max(bgLightness * 100 + 30, 60), 85);
      }
    }

    generatedColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;

    // 计算与背景色的对比度
    const bgContrastRatio = calculateContrastRatio(generatedColor, bgColor);

    // 初始分数基于与背景色的对比度
    let score = bgContrastRatio * 3;

    // 检查与已使用颜色的相似度
    let tooSimilarToUsed = false;
    for (const usedColor of usedColors) {
      if (areColorsTooSimilar(generatedColor, usedColor, tier, colorMode)) {
        tooSimilarToUsed = true;
        break;
      }

      // 增加分数基于与已使用颜色的差异
      score += calculateColorSimilarity(generatedColor, usedColor);
    }

    // 如果需要避免特定颜色，检查与该颜色的相似度
    if (avoidColor && areColorsTooSimilar(generatedColor, avoidColor, tier, colorMode)) {
      tooSimilarToUsed = true;
    } else if (avoidColor) {
      // 增加分数基于与需要避免的颜色的差异
      score += calculateColorSimilarity(generatedColor, avoidColor) * 2;
    }

    // 如果生成的颜色与已使用颜色太相似，尝试下一个
    if (tooSimilarToUsed) {
      attempts++;
      continue;
    }

    // 如果对比度足够高且分数最高，直接使用这个颜色
    if (bgContrastRatio > contrastThreshold && score > bestScore) {
      bestColor = generatedColor;
      bestScore = score;

      // 如果分数非常高，可以提前结束
      if (score > 25) {
        // 提高提前结束的分数阈值
        break;
      }
    }
    // 否则记录分数最高的颜色
    else if (score > bestScore) {
      bestScore = score;
      bestColor = generatedColor;
    }

    attempts++;
  }

  // 使用找到的最佳颜色
  return bestColor || generatedColor;
};
