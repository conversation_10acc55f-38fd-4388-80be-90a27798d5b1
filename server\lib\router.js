import { initTRPC } from '@trpc/server';
import { z } from 'zod';
import {
  batchStatements,
  executeQuery,
  executeScript,
  fetchAllFromTable,
} from './database/index.js';
import { AnalyticsService } from './services/AnalyticsService.js';
import { AuthService } from './services/AuthService.js';
import { DatabaseInitializationService } from './services/DatabaseInitializationService.js';
import { SyncService } from './services/SyncService.js';
import { UserManagementService } from './services/UserManagementService.js';
// Create a tRPC instance with context
const t = initTRPC.context().create();
// Export the router and procedure helpers
export const router = t.router;
export const publicProcedure = t.procedure;
// Create authenticated procedure
export const authenticatedProcedure = t.procedure.use(async (opts) => {
  const { ctx } = opts;
  if (!ctx.isAuthenticated || !ctx.userId) {
    throw new Error('Authentication required');
  }
  return opts.next({
    ctx: {
      ...ctx,
      userId: ctx.userId,
    },
  });
});
/**
 * Helper function to handle errors in a consistent way
 * @param error The error that occurred
 * @param operation The operation that was being performed
 * @returns A standardized error response
 */
const handleError = (error, operation) => {
  console.error(`Error during ${operation}:`, error);
  // 获取错误消息
  const errorMessage = error instanceof Error ? error.message : `Unknown error during ${operation}`;
  // 返回标准化的错误响应
  return {
    success: false,
    data: null,
    error: errorMessage,
  };
};
// Define the input schema for SQL queries
const sqlQueryInputSchema = z.union([
  z.string(),
  z.object({
    sql: z.string(),
    args: z.array(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
  }),
]);
// Define the input schema for batch statements
const batchStatementsInputSchema = z.object({
  statements: z.array(
    z.object({
      sql: z.string(),
      args: z.array(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
    })
  ),
  mode: z.enum(['deferred', 'write', 'read']).optional(),
});
// Define the input schema for SQL scripts
const sqlScriptInputSchema = z.object({
  script: z.string(),
});
// Define the input schema for table queries
const tableQueryInputSchema = z.object({
  tableName: z.string().regex(/^[a-zA-Z0-9_]+$/),
});
// Create the tRPC router with database procedures
export const appRouter = router({
  // Execute a single SQL query
  query: publicProcedure.input(sqlQueryInputSchema).query(async ({ input }) => {
    try {
      const result = await executeQuery(input);
      return {
        success: true,
        data: result,
        error: null,
      };
    } catch (error) {
      return handleError(error, 'query execution');
    }
  }),
  // Execute a batch of SQL statements
  batch: publicProcedure.input(batchStatementsInputSchema).mutation(async ({ input }) => {
    try {
      const result = await batchStatements(input.statements, input.mode);
      return {
        success: true,
        data: result,
        error: null,
      };
    } catch (error) {
      return handleError(error, 'batch execution');
    }
  }),
  // Execute a SQL script
  executeScript: publicProcedure.input(sqlScriptInputSchema).mutation(async ({ input }) => {
    try {
      await executeScript(input.script);
      return {
        success: true,
        error: null,
      };
    } catch (error) {
      return handleError(error, 'script execution');
    }
  }),
  // Fetch all rows from a table
  fetchTable: publicProcedure.input(tableQueryInputSchema).query(async ({ input }) => {
    try {
      const rows = await fetchAllFromTable(input.tableName);
      return {
        success: true,
        data: rows,
        error: null,
      };
    } catch (error) {
      return handleError(error, 'table fetch');
    }
  }),
  // Fetch rows from a table with limit
  fetchTableWithLimit: publicProcedure
    .input(
      z.object({
        tableName: z.string().regex(/^[a-zA-Z0-9_]+$/),
        limit: z.number().int().positive().optional(),
      })
    )
    .query(async ({ input }) => {
      try {
        const rows = await fetchAllFromTable(input.tableName, input.limit);
        return {
          success: true,
          data: rows,
          error: null,
        };
      } catch (error) {
        return handleError(error, 'table fetch with limit');
      }
    }),
  // Synchronize data
  synchronizeData: publicProcedure
    .input(
      z.object({
        moodEntriesToUpload: z
          .array(
            z.object({
              id: z.string(),
              user_id: z.string(),
              timestamp: z.string(),
              emotion_data_set_id: z.string().optional(),
              primary_emotion: z.string(),
              secondary_emotion: z.string().optional(),
              tertiary_emotion: z.string().optional(),
              intensity: z.number(),
              reflection: z.string().optional(),
              created_at: z.string(),
              updated_at: z.string(),
              tags: z.array(z.string()).optional(),
            })
          )
          .optional(),
        emotionSelectionsToUpload: z
          .array(
            z.object({
              id: z.string().optional(),
              mood_entry_id: z.string(),
              emotion_id: z.string(),
              tier_level: z.number(),
              emotion_data_set_emotion_id: z.string().optional(),
              created_at: z.string().optional(),
            })
          )
          .optional(),
        lastSyncTimestamp: z.string().optional(),
        userId: z.string(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const { moodEntriesToUpload, emotionSelectionsToUpload, lastSyncTimestamp, userId } = input;
        const currentSyncTimestamp = new Date().toISOString();
        let uploadedCount = 0;
        let downloadedCount = 0;
        // 1. 上传心情记录
        if (moodEntriesToUpload && moodEntriesToUpload.length > 0) {
          const statements = [];
          for (const moodEntry of moodEntriesToUpload) {
            // 构建心情记录的SQL语句
            const moodEntrySql = `
              INSERT INTO mood_entries (
                id, user_id, timestamp, emotion_data_set_id,
                primary_emotion, secondary_emotion, tertiary_emotion,
                intensity, reflection, created_at, updated_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              ON CONFLICT(id) DO UPDATE SET
                user_id = excluded.user_id,
                timestamp = excluded.timestamp,
                emotion_data_set_id = excluded.emotion_data_set_id,
                primary_emotion = excluded.primary_emotion,
                secondary_emotion = excluded.secondary_emotion,
                tertiary_emotion = excluded.tertiary_emotion,
                intensity = excluded.intensity,
                reflection = excluded.reflection,
                updated_at = excluded.updated_at
            `;
            statements.push({
              sql: moodEntrySql,
              args: [
                moodEntry.id,
                moodEntry.user_id,
                moodEntry.timestamp,
                moodEntry.emotion_data_set_id || null,
                moodEntry.primary_emotion,
                moodEntry.secondary_emotion || null,
                moodEntry.tertiary_emotion || null,
                moodEntry.intensity,
                moodEntry.reflection || null,
                moodEntry.created_at,
                moodEntry.updated_at,
              ],
            });
            // 如果有情绪选择，则添加情绪选择的SQL语句
            const relatedSelections = emotionSelectionsToUpload?.filter(
              (selection) => selection.mood_entry_id === moodEntry.id
            );
            if (relatedSelections && relatedSelections.length > 0) {
              // 首先删除现有的情绪选择
              statements.push({
                sql: 'DELETE FROM emotion_selections WHERE mood_entry_id = ?',
                args: [moodEntry.id],
              });
              // 然后添加新的情绪选择
              for (const selection of relatedSelections) {
                const selectionId =
                  selection.id || `${moodEntry.id}_${selection.emotion_id}_${selection.tier_level}`;
                statements.push({
                  sql: `
                    INSERT INTO emotion_selections (
                      id, mood_entry_id, emotion_id, tier_level, emotion_data_set_emotion_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                  `,
                  args: [
                    selectionId,
                    moodEntry.id,
                    selection.emotion_id,
                    selection.tier_level,
                    selection.emotion_data_set_emotion_id || null,
                    selection.created_at || new Date().toISOString(),
                  ],
                });
              }
            }
            // 如果有标签，则添加标签的SQL语句
            if (moodEntry.tags && moodEntry.tags.length > 0) {
              // 首先删除现有的标签关联
              statements.push({
                sql: 'DELETE FROM mood_entry_tags WHERE mood_entry_id = ?',
                args: [moodEntry.id],
              });
              // 然后添加新的标签关联
              for (const tagId of moodEntry.tags) {
                statements.push({
                  sql: 'INSERT INTO mood_entry_tags (mood_entry_id, tag_id) VALUES (?, ?)',
                  args: [moodEntry.id, tagId],
                });
              }
            }
          }
          // 执行批处理
          if (statements.length > 0) {
            await batchStatements(statements);
            uploadedCount = moodEntriesToUpload.length;
          }
        }
        // 2. 下载新的心情记录
        let downloadQuery = `
          SELECT me.*, GROUP_CONCAT(t.id) as tags
          FROM mood_entries me
          LEFT JOIN mood_entry_tags met ON me.id = met.mood_entry_id
          LEFT JOIN tags t ON met.tag_id = t.id
          WHERE me.user_id = ?
        `;
        const downloadArgs = [userId];
        if (lastSyncTimestamp) {
          downloadQuery += ' AND me.updated_at > ?';
          downloadArgs.push(lastSyncTimestamp);
        }
        downloadQuery += ' GROUP BY me.id ORDER BY me.timestamp DESC';
        const downloadResult = await executeQuery({ sql: downloadQuery, args: downloadArgs });
        const newMoodEntries = downloadResult.rows.map((row) => ({
          id: String(row.id),
          user_id: String(row.user_id),
          timestamp: String(row.timestamp),
          emotion_data_set_id: row.emotion_data_set_id
            ? String(row.emotion_data_set_id)
            : undefined,
          primary_emotion: String(row.primary_emotion),
          secondary_emotion: row.secondary_emotion ? String(row.secondary_emotion) : undefined,
          tertiary_emotion: row.tertiary_emotion ? String(row.tertiary_emotion) : undefined,
          intensity: Number(row.intensity),
          reflection: row.reflection ? String(row.reflection) : undefined,
          created_at: String(row.created_at),
          updated_at: String(row.updated_at),
          tags: row.tags ? String(row.tags).split(',') : [],
        }));
        // 3. 获取情绪选择
        const newEmotionSelections = [];
        if (newMoodEntries.length > 0) {
          const moodEntryIds = newMoodEntries.map((entry) => entry.id);
          const placeholders = moodEntryIds.map(() => '?').join(',');
          const selectionsQuery = `
            SELECT es.*, e.name, e.color, e.emoji
            FROM emotion_selections es
            JOIN emotions e ON es.emotion_id = e.id
            WHERE es.mood_entry_id IN (${placeholders})
            ORDER BY es.tier_level
          `;
          const selectionsResult = await executeQuery({ sql: selectionsQuery, args: moodEntryIds });
          selectionsResult.rows.forEach((row) => {
            newEmotionSelections.push({
              id: String(row.id),
              mood_entry_id: String(row.mood_entry_id),
              emotion_id: String(row.emotion_id),
              tier_level: Number(row.tier_level),
              emotion_data_set_emotion_id: row.emotion_data_set_emotion_id
                ? String(row.emotion_data_set_emotion_id)
                : undefined,
              created_at: String(row.created_at),
            });
          });
        }
        downloadedCount = newMoodEntries.length;
        // 4. 返回同步结果
        return {
          success: true,
          newMoodEntriesFromServer: newMoodEntries,
          newEmotionSelectionsFromServer: newEmotionSelections,
          serverTimestamp: currentSyncTimestamp,
          uploadedCount,
          downloadedCount,
          error: null,
        };
      } catch (error) {
        return handleError(error, 'data synchronization');
      }
    }),
  // ==================== 认证服务 ====================
  // 用户登录
  login: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        password: z.string().min(6),
      })
    )
    .mutation(async ({ input }) => {
      const authService = AuthService.getInstance();
      return await authService.login(input);
    }),
  // 用户注册
  register: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        password: z.string().min(6),
        username: z.string().optional(),
        displayName: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const authService = AuthService.getInstance();
      return await authService.register(input);
    }),
  // 验证令牌
  verifyToken: publicProcedure
    .input(
      z.object({
        token: z.string(),
      })
    )
    .query(async ({ input }) => {
      const authService = AuthService.getInstance();
      return await authService.verifyToken(input.token);
    }),
  // 更新VIP状态
  updateVipStatus: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
        isVip: z.boolean(),
        expiresAt: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const authService = AuthService.getInstance();
      return await authService.updateVipStatus(input.userId, input.isVip, input.expiresAt);
    }),
  // ==================== 同步服务 ====================
  // 完整数据同步
  performFullSync: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
        lastSyncTimestamp: z.string().optional(),
        moodEntriesToUpload: z
          .array(
            z.object({
              id: z.string(),
              user_id: z.string(),
              timestamp: z.string(),
              emotion_data_set_id: z.string().optional(),
              intensity: z.number(),
              reflection: z.string().optional(),
              created_at: z.string(),
              updated_at: z.string(),
              sync_status: z.string().optional(),
            })
          )
          .optional(),
        emotionSelectionsToUpload: z
          .array(
            z.object({
              id: z.string(),
              mood_entry_id: z.string(),
              emotion_id: z.string(),
              tier_level: z.number(),
              emotion_data_set_emotion_id: z.string().optional(),
              created_at: z.string(),
            })
          )
          .optional(),
        userConfigsToUpload: z
          .array(
            z.object({
              id: z.string(),
              user_id: z.string(),
              name: z.string(),
              config_data: z.string(),
              is_active: z.boolean(),
              created_at: z.string(),
              last_updated: z.string(),
            })
          )
          .optional(),
        tagsToUpload: z
          .array(
            z.object({
              id: z.string(),
              user_id: z.string(),
              name: z.string(),
              color: z.string().optional(),
              created_at: z.string(),
              updated_at: z.string(),
            })
          )
          .optional(),
      })
    )
    .mutation(async ({ input }) => {
      const syncService = SyncService.getInstance();
      return await syncService.performFullSync(input);
    }),
  // ==================== 分析服务 ====================
  // 获取心情分析
  getMoodAnalytics: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        groupBy: z.enum(['day', 'week', 'month', 'year']).optional(),
        metrics: z.array(z.string()).optional(),
      })
    )
    .query(async ({ input }) => {
      const analyticsService = AnalyticsService.getInstance();
      return await analyticsService.getMoodAnalytics(input);
    }),
  // 获取情绪使用统计
  getEmotionUsageStats: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
      })
    )
    .query(async ({ input }) => {
      const analyticsService = AnalyticsService.getInstance();
      return await analyticsService.getEmotionUsageStats(input);
    }),
  // 获取用户活动统计
  getUserActivityStats: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
      })
    )
    .query(async ({ input }) => {
      const analyticsService = AnalyticsService.getInstance();
      return await analyticsService.getUserActivityStats(input.userId, input);
    }),
  // ==================== 用户管理服务 ====================
  // 获取用户配置文件
  getUserProfile: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
      })
    )
    .query(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.getUserProfile(input.userId);
    }),
  // 更新用户偏好设置
  updateUserPreferences: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
        preferences: z.object({
          theme: z.enum(['light', 'dark', 'auto']).optional(),
          language: z.string().optional(),
          notifications: z.boolean().optional(),
          syncEnabled: z.boolean().optional(),
          autoSync: z.boolean().optional(),
          syncInterval: z.number().optional(),
          wifiOnly: z.boolean().optional(),
        }),
      })
    )
    .mutation(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.updateUserPreferences(input.userId, input.preferences);
    }),
  // 获取VIP状态
  getVipStatus: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
      })
    )
    .query(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.getVipStatus(input.userId);
    }),
  // 解锁皮肤
  unlockSkin: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
        skinId: z.string(),
        unlockMethod: z.string(),
        transactionId: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.unlockSkin(
        input.userId,
        input.skinId,
        input.unlockMethod,
        input.transactionId
      );
    }),
  // 获取用户解锁的皮肤
  getUserUnlockedSkins: authenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
      })
    )
    .query(async ({ input }) => {
      const userService = UserManagementService.getInstance();
      return await userService.getUserUnlockedSkins(input.userId);
    }),
  // ==================== 数据库管理 ====================
  // 初始化数据库
  initializeDatabase: publicProcedure.mutation(async () => {
    const dbInitService = DatabaseInitializationService.getInstance();
    return await dbInitService.initializeDatabase();
  }),
  // 重置数据库（仅用于开发/测试）
  resetDatabase: publicProcedure
    .input(
      z.object({
        confirm: z.literal('RESET_DATABASE_CONFIRM'),
      })
    )
    .mutation(async ({ input }) => {
      if (input.confirm !== 'RESET_DATABASE_CONFIRM') {
        return {
          success: false,
          error: 'Invalid confirmation',
        };
      }
      const dbInitService = DatabaseInitializationService.getInstance();
      return await dbInitService.resetDatabase();
    }),
});
