import { useLanguage } from '@/contexts/LanguageContext';
import { useUserConfig } from '@/contexts/UserConfigContext';
import type { MoodEntry } from '@/types';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { useHybridMoodEntriesData } from './useHybridData';
interface UseExportDataReturn {
  exportDataHandler: (currentTheme: string) => Promise<void>;
  isExporting: boolean;
  exportError: Error | null;
  exportDataAsCsvHandler: () => Promise<void>;
  isExportingCsv: boolean;
  exportCsvError: Error | null;
}

export const useExportData = (): UseExportDataReturn => {
  const { t, language } = useLanguage();
  const { userConfig } = useUserConfig();
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [exportError, setExportError] = useState<Error | null>(null);
  const [isExportingCsv, setIsExportingCsv] = useState<boolean>(false);
  const [exportCsvError, setExportCsvError] = useState<Error | null>(null);

  // 使用混合数据获取钩子
  const { data: moodEntries, isLoading: isLoadingData } = useHybridMoodEntriesData();

  const exportDataHandler = useCallback(
    async (currentTheme: string) => {
      setIsExporting(true);
      setExportError(null);

      try {
        // 使用混合数据 - 优先在线，回退到离线
        if (!moodEntries) {
          throw new Error('No mood entries data available for export');
        }

        const allMoodEntries: MoodEntry[] = moodEntries;

        const userId = userConfig.user_id || 'user';
        const exportObject = {
          user: { id: userId, preferences: { language, theme: currentTheme } },
          entries: allMoodEntries,
        };

        const dataStr = JSON.stringify(exportObject, null, 2);
        const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
        const exportFileDefaultName = `mindful-mood-export-${userId}-${new Date().toISOString().slice(0, 10)}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        document.body.appendChild(linkElement); // Required for Firefox
        linkElement.click();
        document.body.removeChild(linkElement); // Clean up

        toast.success(t('settings.export_success'));
      } catch (error) {
        console.error('Failed to export data:', error);
        toast.error(t('error.generic'));
        setExportError(error instanceof Error ? error : new Error(t('error.generic')));
      } finally {
        setIsExporting(false);
      }
    },
    [language, t, userConfig, moodEntries]
  );

  const convertToCSV = (data: MoodEntry[]): string => {
    if (!data || data.length === 0) {
      return '';
    }
    const headers = [
      'id',
      'timestamp',
      'primaryEmotion',
      'secondaryEmotion',
      'tertiaryEmotion',
      'intensity',
      'reflection',
      'tags',
      'primaryEmotionColor',
      'secondaryEmotionColor',
      'tertiaryEmotionColor',
      'userId',
    ];
    const csvRows = [];
    csvRows.push(headers.join(','));

    for (const row of data) {
      const values = headers.map((header) => {
        const escaped = `${row[header as keyof MoodEntry]}`.replace(/"/g, '""');
        return `"${escaped}"`;
      });
      csvRows.push(values.join(','));
    }
    return csvRows.join('\n');
  };

  const exportDataAsCsvHandler = useCallback(async () => {
    setIsExportingCsv(true);
    setExportCsvError(null);

    try {
      // 使用混合数据 - 优先在线，回退到离线
      if (!moodEntries) {
        throw new Error('No mood entries data available for export');
      }

      const allMoodEntries: MoodEntry[] = moodEntries;

      if (allMoodEntries.length === 0) {
        toast.info(t('history.no_entries')); // Or a more specific message like 'No data to export as CSV'
        setIsExportingCsv(false);
        return;
      }

      const csvData = convertToCSV(allMoodEntries);
      const dataUri = `data:text/csv;charset=utf-8,${encodeURIComponent(csvData)}`;
      const userId = userConfig.user_id || 'user';
      const exportFileDefaultName = `mindful-mood-export-${userId}-${new Date().toISOString().slice(0, 10)}.csv`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      document.body.appendChild(linkElement); // Required for Firefox
      linkElement.click();
      document.body.removeChild(linkElement); // Clean up

      toast.success(t('settings.export_success')); // Consider adding a specific CSV success message
    } catch (error) {
      console.error('Failed to export data as CSV:', error);
      toast.error(t('settings.export_error')); // Consider adding a specific CSV error message
      setExportCsvError(error instanceof Error ? error : new Error(t('settings.export_error')));
    } finally {
      setIsExportingCsv(false);
    }
  }, [language, t, userConfig, moodEntries]);

  return {
    exportDataHandler,
    isExporting,
    exportError,
    exportDataAsCsvHandler,
    isExportingCsv,
    exportCsvError,
  };
};
