-- =============================================
-- 新配置系统初始化文件
-- 清除旧配置，初始化新的配置系统数据
-- 
-- 配置系统架构：
-- 1. 全局应用设置 (user_configs) - 主题、语言、通知、音效、无障碍
-- 2. Quiz系统配置 (user_quiz_preferences) - 6层个性化配置
-- 3. 包覆盖配置 (quiz_pack_overrides) - 针对特定Quiz包的配置
-- 4. 会话配置 (quiz_session_configs) - 最终合并的配置快照
-- =============================================

-- 清除旧的配置数据
DELETE FROM user_configs WHERE id LIKE 'config-%';
DELETE FROM user_presentation_configs;
DELETE FROM pack_presentation_overrides;
DELETE FROM quiz_session_presentation_configs;
DELETE FROM session_presentation_snapshots;

-- 重置自增ID (如果使用)
-- DELETE FROM sqlite_sequence WHERE name IN ('user_configs', 'user_quiz_preferences', 'quiz_pack_overrides', 'quiz_session_configs');

-- 插入系统默认配置模板
INSERT OR IGNORE INTO user_configs (
    id,
    user_id,
    config_name,
    theme_mode,
    language,
    notifications_enabled,
    sound_enabled,
    accessibility,
    is_active,
    is_default,
    created_at,
    updated_at
) VALUES (
    'system_default_global_config',
    NULL, -- 系统模板
    'system_default',
    'system', -- 跟随系统主题
    'zh-CN', -- 默认中文
    1, -- 启用通知
    1, -- 启用音效
    '{"high_contrast": false, "large_text": false, "reduce_motion": false, "screen_reader_support": false}', -- 默认无障碍设置
    0, -- 非激活状态（模板）
    0, -- 非默认配置
    datetime('now'),
    datetime('now')
);

-- 插入系统默认Quiz配置模板
INSERT OR IGNORE INTO user_quiz_preferences (
    id,
    user_id,
    config_name,
    presentation_config,
    config_version,
    personalization_level,
    is_active,
    is_default,
    created_at,
    updated_at
) VALUES (
    'system_default_quiz_config',
    NULL, -- 系统模板
    'system_default',
    '{
        "layer0_dataset_presentation": {
            "preferred_pack_categories": ["daily"],
            "default_difficulty_preference": "regular",
            "session_length_preference": "medium",
            "auto_select_recommended": true,
            "restore_progress": true
        },
        "layer1_user_choice": {
            "preferred_view_type": "wheel",
            "active_skin_id": "default-light",
            "color_mode": "warm",
            "user_level": "regular"
        },
        "layer2_rendering_strategy": {
            "render_engine_preferences": {
                "wheel": "D3",
                "card": "CSS",
                "bubble": "CSS",
                "list": "CSS"
            },
            "performance_mode": "balanced",
            "supported_content_types": {
                "text": true,
                "emoji": true,
                "images": true,
                "animations": true
            }
        },
        "layer3_skin_base": {
            "selected_skin_id": "default-light",
            "colors": {
                "primary": "#4F46E5",
                "secondary": "#7C3AED",
                "background": "#FFFFFF",
                "surface": "#F9FAFB",
                "text": "#111827",
                "accent": "#F59E0B"
            },
            "animations": {
                "enable_animations": true,
                "animation_speed": "normal",
                "reduce_motion": false
            }
        },
        "layer4_view_detail": {
            "wheel_config": {
                "container_size": 400,
                "wheel_radius": 200,
                "emotion_display_mode": "standard",
                "show_labels": true,
                "show_emojis": true
            }
        },
        "layer5_accessibility": {
            "high_contrast": false,
            "large_text": false,
            "reduce_motion": false,
            "keyboard_navigation": true,
            "voice_guidance": false
        }
    }',
    '2.0', -- 配置版本
    50, -- 默认个性化级别
    0, -- 非激活状态（模板）
    0, -- 非默认配置
    datetime('now'),
    datetime('now')
);

-- 验证新配置系统数据
SELECT 'Global Configs' as table_name, COUNT(*) as count FROM user_configs
UNION ALL
SELECT 'Quiz Preferences' as table_name, COUNT(*) as count FROM user_quiz_preferences
UNION ALL
SELECT 'Pack Overrides' as table_name, COUNT(*) as count FROM quiz_pack_overrides
UNION ALL
SELECT 'Session Configs' as table_name, COUNT(*) as count FROM quiz_session_configs;

-- 显示配置系统状态
SELECT 
    'Configuration System Status' as status,
    'New config system initialized successfully' as message,
    datetime('now') as timestamp;
