# ViewFactory 详细测试计划

## 背景

ViewFactory 是应用程序中负责创建不同类型视图的核心组件。随着我们计划移除 `createView` 兼容性方法并增强 ViewFactory 的功能，我们需要一个详细的测试计划来确保所有功能正常工作。

## 测试目标

1. 验证移除 `createView` 兼容性方法后，所有功能仍然正常工作
2. 测试 ViewFactory 的所有方法和功能
3. 测试不同的用户配置、视图类型、渲染引擎和显示模式
4. 测试边界条件和错误处理
5. 测试性能和内存使用
6. 测试用户体验和辅助功能

## 详细测试计划

### 1. 单元测试

#### 1.1 基础方法测试

##### 1.1.1 createViewFromUserConfig 方法测试

```typescript
describe('createViewFromUserConfig 方法', () => {
  it('应该根据用户配置创建轮盘视图', () => {
    const userConfig = createTestUserConfig();
    const view = ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证创建了正确的视图类型
    expect(D3WheelView).toHaveBeenCalled();

    // 验证视图有 render 方法
    expect(view.render).toBeDefined();
    expect(typeof view.render).toBe('function');
  });

  it('应该使用提供的视图类型覆盖用户首选视图类型', () => {
    const userConfig = createTestUserConfig();
    const view = ViewFactory.createViewFromUserConfig(userConfig, skinManager, 'card');

    // 验证创建了卡片视图
    expect(CardView).toHaveBeenCalled();
  });

  it('应该使用视图类型特定的皮肤ID', () => {
    const userConfig = createTestUserConfig();
    ViewFactory.createViewFromUserConfig(userConfig, skinManager, 'bubble');

    // 验证使用了气泡视图的皮肤ID
    expect(skinManager.getSkinById).toHaveBeenCalledWith('skin-3');
  });

  it('应该在找不到皮肤时抛出错误', () => {
    const userConfig = createTestUserConfig();
    (skinManager.getSkinById as any).mockReturnValueOnce(null);

    // 验证抛出错误
    expect(() => {
      ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    }).toThrow('Skin with ID skin-1 not found');
  });

  it('应该对未实现的视图类型回退到轮盘视图', () => {
    const userConfig = { ...createTestUserConfig(), preferredViewType: 'list' };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证警告消息
    expect(console.warn).toHaveBeenCalledWith('List view not yet implemented, falling back to wheel view');
  });
});
```

##### 1.1.2 createWheel 方法测试

```typescript
describe('createWheel 方法', () => {
  it('应该创建D3轮盘视图', () => {
    const view = ViewFactory.createWheel('D3', 'textEmoji', skinConfig);

    // 验证创建了D3轮盘视图
    expect(D3WheelView).toHaveBeenCalledWith('textEmoji', skinConfig);
  });

  it('应该创建SVG轮盘视图', () => {
    const view = ViewFactory.createWheel('SVG', 'textEmoji', skinConfig);

    // 验证创建了SVG轮盘视图
    expect(SVGWheelView).toHaveBeenCalledWith('textEmoji', skinConfig);
  });

  it('应该创建R3F轮盘视图', () => {
    const view = ViewFactory.createWheel('R3F', 'textEmoji', skinConfig);

    // 验证创建了R3F轮盘视图
    expect(R3FWheelView).toHaveBeenCalledWith('textEmoji', skinConfig);
  });

  it('应该对未知实现类型回退到D3轮盘视图', () => {
    const view = ViewFactory.createWheel('unknown' as RenderEngine, 'textEmoji', skinConfig);

    // 验证创建了D3轮盘视图（默认回退）
    expect(D3WheelView).toHaveBeenCalledWith('textEmoji', skinConfig);
  });
});
```

##### 1.1.3 createCard 方法测试

```typescript
describe('createCard 方法', () => {
  it('应该创建卡片视图', () => {
    const view = ViewFactory.createCard('emoji', skinConfig);

    // 验证创建了卡片视图
    expect(CardView).toHaveBeenCalledWith('emoji', skinConfig, 'grid');
  });

  it('应该使用提供的布局', () => {
    ViewFactory.createCard('emoji', skinConfig, 'masonry');

    // 验证创建了卡片视图，使用masonry布局
    expect(CardView).toHaveBeenCalledWith('emoji', skinConfig, 'masonry');
  });
});
```

##### 1.1.4 createBubble 方法测试

```typescript
describe('createBubble 方法', () => {
  it('应该创建气泡视图', () => {
    const view = ViewFactory.createBubble('text', skinConfig);

    // 验证创建了气泡视图
    expect(BubbleView).toHaveBeenCalledWith('text', skinConfig, 'cluster');
  });

  it('应该使用提供的布局', () => {
    ViewFactory.createBubble('text', skinConfig, 'force');

    // 验证创建了气泡视图，使用force布局
    expect(BubbleView).toHaveBeenCalledWith('text', skinConfig, 'force');
  });
});
```

##### 1.1.5 createGalaxy 方法测试

```typescript
describe('createGalaxy 方法', () => {
  it('应该创建星系视图', () => {
    const view = ViewFactory.createGalaxy('textEmoji', skinConfig);

    // 验证创建了星系视图
    expect(GalaxyView).toHaveBeenCalledWith('textEmoji', skinConfig, 'spiral');
  });

  it('应该使用提供的布局', () => {
    ViewFactory.createGalaxy('textEmoji', skinConfig, 'orbital');

    // 验证创建了星系视图，使用orbital布局
    expect(GalaxyView).toHaveBeenCalledWith('textEmoji', skinConfig, 'orbital');
  });
});
```

##### 1.1.6 getDefaultLayoutForViewType 方法测试

```typescript
describe('getDefaultLayoutForViewType 方法', () => {
  it('应该返回卡片视图的默认布局', () => {
    const layout = ViewFactory.getDefaultLayoutForViewType('card');
    expect(layout).toBe('grid');
  });

  it('应该返回气泡视图的默认布局', () => {
    const layout = ViewFactory.getDefaultLayoutForViewType('bubble');
    expect(layout).toBe('cluster');
  });

  it('应该返回星系视图的默认布局', () => {
    const layout = ViewFactory.getDefaultLayoutForViewType('galaxy');
    expect(layout).toBe('spiral');
  });

  it('应该对未知视图类型返回空字符串', () => {
    const layout = ViewFactory.getDefaultLayoutForViewType('unknown' as ViewType);
    expect(layout).toBe('');
  });
});
```

#### 1.2 不同用户配置测试

##### 1.2.1 模拟不同用户配置

```typescript
// 创建标准测试用户配置
const createTestUserConfig = (): UserConfig => ({
  id: 'config-1',
  name: 'Standard Test Config',
  activeEmotionDataId: 'data-1',
  activeSkinId: 'skin-1',
  preferredViewType: 'wheel',
  darkMode: false,
  colorMode: 'warm',
  contentDisplayModePreferences: {
    wheel: 'textEmoji',
    card: 'emoji',
    bubble: 'text',
    galaxy: 'textEmoji'
  },
  renderEnginePreferences: {
    wheel: 'D3',
    card: 'SVG',
    bubble: 'D3',
    galaxy: 'R3F'
  },
  viewTypeSkinIds: {
    wheel: 'skin-1',
    card: 'skin-2',
    bubble: 'skin-3',
    galaxy: 'skin-4'
  },
  layoutPreferences: {
    card: 'grid',
    bubble: 'cluster',
    galaxy: 'spiral'
  },
  created_at: new Date().toISOString(),
  lastUpdated: new Date()
});

// 创建偏好卡片视图的用户配置
const createCardPreferredUserConfig = (): UserConfig => ({
  id: 'config-2',
  name: 'Card Preferred Config',
  activeEmotionDataId: 'data-1',
  activeSkinId: 'skin-2',
  preferredViewType: 'card',
  darkMode: true,
  colorMode: 'cool',
  contentDisplayModePreferences: {
    wheel: 'text',
    card: 'textEmoji',
    bubble: 'emoji',
    galaxy: 'animatedEmoji'
  },
  renderEnginePreferences: {
    wheel: 'SVG',
    card: 'D3',
    bubble: 'SVG',
    galaxy: 'R3F'
  },
  viewTypeSkinIds: {
    wheel: 'skin-2',
    card: 'skin-1',
    bubble: 'skin-4',
    galaxy: 'skin-3'
  },
  layoutPreferences: {
    card: 'masonry',
    bubble: 'force',
    galaxy: 'orbital'
  },
  created_at: new Date().toISOString(),
  lastUpdated: new Date()
});

// 创建最小配置（缺少某些偏好设置）
const createMinimalUserConfig = (): UserConfig => ({
  id: 'config-4',
  name: 'Minimal Config',
  activeEmotionDataId: 'data-3',
  activeSkinId: 'skin-1',
  preferredViewType: 'bubble',
  darkMode: false,
  // 没有设置colorMode
  // 没有设置contentDisplayModePreferences
  // 没有设置renderEnginePreferences
  // 没有设置viewTypeSkinIds
  // 没有设置layoutPreferences
  created_at: new Date().toISOString(),
  lastUpdated: new Date()
});
```

##### 1.2.2 测试不同用户配置

```typescript
describe('不同用户配置测试', () => {
  it('应该使用偏好卡片视图的用户配置创建卡片视图', () => {
    const cardPreferredConfig = createCardPreferredUserConfig();
    ViewFactory.createViewFromUserConfig(cardPreferredConfig, skinManager);

    // 验证使用了卡片视图的皮肤ID
    expect(skinManager.getSkinById).toHaveBeenCalledWith('skin-1');

    // 验证创建了卡片视图，使用textEmoji模式和masonry布局
    expect(CardView).toHaveBeenCalledWith('textEmoji', expect.any(Object), 'masonry');
  });

  it('应该为最小配置使用默认值', () => {
    const minimalConfig = createMinimalUserConfig();
    ViewFactory.createViewFromUserConfig(minimalConfig, skinManager);

    // 验证使用了气泡视图的默认皮肤ID
    expect(skinManager.getSkinById).toHaveBeenCalledWith('skin-1');

    // 验证创建了气泡视图，使用默认的textEmoji模式和cluster布局
    expect(BubbleView).toHaveBeenCalledWith('textEmoji', expect.any(Object), 'cluster');
  });
});
```

#### 1.3 内容显示模式测试

```typescript
describe('内容显示模式测试', () => {
  it('应该将内容显示模式传递给视图', () => {
    // 测试不同的内容显示模式
    const contentModes: ContentDisplayMode[] = ['text', 'emoji', 'textEmoji', 'animatedEmoji'];

    contentModes.forEach(mode => {
      vi.clearAllMocks();
      ViewFactory.createWheel('D3', mode, skinConfig);
      expect(D3WheelView).toHaveBeenCalledWith(mode, skinConfig);
    });
  });
});
```

#### 1.4 边界条件测试

```typescript
describe('边界条件测试', () => {
  it('应该处理无效的视图类型', () => {
    const userConfig = { ...createTestUserConfig(), preferredViewType: 'invalid' as ViewType };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证警告消息
    expect(console.warn).toHaveBeenCalledWith('Unknown view type: invalid, falling back to wheel view');
  });

  it('应该处理无效的渲染引擎', () => {
    const userConfig = {
      ...createTestUserConfig(),
      renderEnginePreferences: {
        wheel: 'invalid' as RenderEngine
      }
    };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证创建了D3轮盘视图（默认回退）
    expect(D3WheelView).toHaveBeenCalled();
  });
});
```

### 2. 集成测试

#### 2.1 与皮肤系统集成测试

##### 2.1.1 创建多种测试皮肤

```typescript
// 创建基础测试皮肤配置
const createBasicSkinConfig = (): SkinConfig => ({
  supported_view_types: ['wheel', 'card', 'bubble', 'galaxy'],
  supported_content_modes: ['text', 'emoji', 'textEmoji'],
  supported_render_engines: ['D3', 'SVG', 'R3F'],
  colors: {
    primary: '#3498db',
    secondary: '#2ecc71',
    background: '#ffffff',
    text: '#333333',
    accent: '#e74c3c'
  },
  fonts: {
    family: 'Arial, sans-serif',
    size: { small: 12, medium: 16, large: 24 },
    weight: { normal: 400, bold: 700 }
  },
  effects: {
    shadows: true,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowBlur: 10,
    shadowOffsetX: 0,
    shadowOffsetY: 4,
    animations: true,
    animationDuration: 300,
    animationEasing: 'easeOutCubic',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#000000',
    borderStyle: 'solid',
    opacity: 1
  },
  view_configs: {
    wheel: {
      containerSize: 500,
      wheelRadius: 200,
      sectorGap: 2,
      sectorBorderRadius: 5,
      sectorBorderColor: '#000000',
      sectorBorderWidth: 1,
      textColor: '#000000',
      textSize: 14,
      emojiSize: 24
    },
    card: {
      cardSize: 120,
      cardBorderRadius: 8,
      cardBorderWidth: 1,
      cardBorderColor: '#000000',
      cardBackground: '#ffffff',
      textSize: 14,
      textColor: '#000000',
      emojiSize: 24,
      layout: 'grid'
    },
    bubble: {
      bubbleSize: 80,
      bubbleBorderRadius: 50,
      bubbleBorderWidth: 1,
      bubbleBorderColor: '#000000',
      bubbleBackground: '#ffffff',
      textSize: 14,
      textColor: '#000000',
      emojiSize: 24,
      layout: 'cluster'
    },
    galaxy: {
      containerSize: 600,
      starSize: 50,
      backgroundColor: '#000000',
      starColor: '#ffffff',
      textSize: 14,
      textColor: '#ffffff',
      emojiSize: 24,
      layout: 'spiral'
    }
  }
});

// 创建极简皮肤配置
const createMinimalistSkinConfig = (): SkinConfig => ({
  ...createBasicSkinConfig(),
  supported_view_types: ['wheel', 'card'],
  supported_content_modes: ['text'],
  supported_render_engines: ['SVG'],
  colors: {
    primary: '#000000',
    secondary: '#333333',
    background: '#ffffff',
    text: '#000000',
    accent: '#666666'
  },
  effects: {
    ...createBasicSkinConfig().effects,
    shadows: false,
    animations: false,
    borderWidth: 0
  },
  view_configs: {
    wheel: {
      ...createBasicSkinConfig().view_configs?.wheel,
      sectorGap: 1,
      sectorBorderRadius: 0,
      sectorBorderWidth: 0
    },
    card: {
      ...createBasicSkinConfig().view_configs?.card,
      cardBorderRadius: 0,
      cardBorderWidth: 0,
      layout: 'list'
    }
  }
});

// 创建表情皮肤配置
const createEmojiSkinConfig = (): SkinConfig => ({
  ...createBasicSkinConfig(),
  supported_view_types: ['wheel', 'bubble', 'galaxy'],
  supported_content_modes: ['emoji', 'animatedEmoji'],
  supported_render_engines: ['D3', 'R3F'],
  colors: {
    primary: '#ff9800',
    secondary: '#ffeb3b',
    background: '#fff9c4',
    text: '#ff5722',
    accent: '#f44336'
  },
  effects: {
    ...createBasicSkinConfig().effects,
    animations: true,
    animationDuration: 500,
    borderRadius: 20
  },
  view_configs: {
    wheel: {
      ...createBasicSkinConfig().view_configs?.wheel,
      emojiSize: 32,
      sectorBorderRadius: 15
    },
    bubble: {
      ...createBasicSkinConfig().view_configs?.bubble,
      bubbleSize: 100,
      bubbleBorderRadius: 100,
      emojiSize: 48,
      layout: 'float'
    },
    galaxy: {
      ...createBasicSkinConfig().view_configs?.galaxy,
      starSize: 80,
      emojiSize: 40,
      layout: 'nebula'
    }
  }
});

// 创建高级3D皮肤配置
const create3DSkinConfig = (): SkinConfig => ({
  ...createBasicSkinConfig(),
  supported_view_types: ['wheel', 'galaxy'],
  supported_content_modes: ['textEmoji', 'animatedEmoji'],
  supported_render_engines: ['R3F'],
  colors: {
    primary: '#4a148c',
    secondary: '#7b1fa2',
    background: '#000000',
    text: '#ffffff',
    accent: '#e1bee7'
  },
  effects: {
    ...createBasicSkinConfig().effects,
    shadows: true,
    shadowBlur: 20,
    shadowColor: 'rgba(255, 255, 255, 0.3)',
    animations: true,
    animationDuration: 800,
    borderRadius: 0
  },
  view_configs: {
    wheel: {
      ...createBasicSkinConfig().view_configs?.wheel,
      containerSize: 800,
      wheelRadius: 300,
      textColor: '#ffffff',
      use3DEffects: true,
      perspective: 1000,
      depth: 50
    },
    galaxy: {
      ...createBasicSkinConfig().view_configs?.galaxy,
      containerSize: 1000,
      starGlow: true,
      starGlowColor: '#e1bee7',
      starGlowSize: 20,
      backgroundType: 'nebula',
      backgroundNebulaColor: '#4a148c',
      perspective3D: true,
      perspective3DIntensity: 800
    }
  }
});

// 创建游戏风格皮肤配置
const createGameSkinConfig = (): SkinConfig => ({
  ...createBasicSkinConfig(),
  supported_view_types: ['wheel', 'card', 'bubble'],
  supported_content_modes: ['textEmoji', 'animatedEmoji'],
  supported_render_engines: ['D3', 'SVG', 'R3F'],
  colors: {
    primary: '#2196f3',
    secondary: '#03a9f4',
    background: '#bbdefb',
    text: '#01579b',
    accent: '#ff4081'
  },
  effects: {
    ...createBasicSkinConfig().effects,
    shadows: true,
    shadowBlur: 15,
    shadowColor: 'rgba(3, 169, 244, 0.5)',
    animations: true,
    animationDuration: 400,
    borderRadius: 12,
    borderWidth: 3,
    borderColor: '#01579b',
    borderStyle: 'solid'
  },
  view_configs: {
    wheel: {
      ...createBasicSkinConfig().view_configs?.wheel,
      sectorBorderRadius: 10,
      sectorBorderWidth: 3,
      sectorBorderColor: '#01579b',
      highlightOnHover: true,
      highlightColor: '#ff4081',
      selectionAnimation: 'pulse',
      selectionIndicator: 'glow'
    },
    card: {
      ...createBasicSkinConfig().view_configs?.card,
      cardBorderRadius: 12,
      cardBorderWidth: 3,
      cardBorderColor: '#01579b',
      cardHoverEffect: 'lift',
      cardHoverScale: 1.1,
      cardSelectedEffect: 'glow',
      cardSelectedGlowColor: '#ff4081'
    },
    bubble: {
      ...createBasicSkinConfig().view_configs?.bubble,
      bubbleBorderRadius: 100,
      bubbleBorderWidth: 3,
      bubbleBorderColor: '#01579b',
      bubbleHoverEffect: 'scale',
      bubbleHoverScale: 1.2,
      bubbleSelectedEffect: 'glow',
      bubbleSelectedGlowColor: '#ff4081',
      floatingAnimation: true,
      floatingSpeed: 2,
      floatingAmplitude: 10
    }
  }
});

// 创建自定义皮肤配置
const createCustomSkinConfig = (): SkinConfig => ({
  ...createBasicSkinConfig(),
  supported_view_types: ['wheel', 'card', 'bubble', 'galaxy', 'list', 'grid', 'tree', 'flow', 'tagCloud'],
  supported_content_modes: ['text', 'emoji', 'textEmoji', 'animatedEmoji', 'image'],
  supported_render_engines: ['D3', 'SVG', 'R3F', 'Canvas', 'WebGPU', 'CSS', 'WebGL'],
  // 自定义配置...
});
```

##### 2.1.2 皮肤系统集成测试

```typescript
describe('与皮肤系统集成测试', () => {
  it('应该使用基础皮肤配置创建视图', () => {
    const userConfig = createTestUserConfig();
    const skinConfig = createBasicSkinConfig();
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: 'skin-1', config: skinConfig });

    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证使用了皮肤配置
    expect(D3WheelView).toHaveBeenCalledWith('textEmoji', skinConfig);
  });

  it('应该使用极简皮肤配置创建视图', () => {
    const userConfig = {
      ...createTestUserConfig(),
      activeSkinId: 'minimalist-skin',
      contentDisplayModePreferences: {
        wheel: 'text'
      },
      renderEnginePreferences: {
        wheel: 'SVG'
      }
    };
    const skinConfig = createMinimalistSkinConfig();
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: 'minimalist-skin', config: skinConfig });

    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证使用了极简皮肤配置
    expect(SVGWheelView).toHaveBeenCalledWith('text', skinConfig);
  });

  it('应该使用表情皮肤配置创建视图', () => {
    const userConfig = {
      ...createTestUserConfig(),
      activeSkinId: 'emoji-skin',
      preferredViewType: 'bubble',
      contentDisplayModePreferences: {
        bubble: 'emoji'
      },
      renderEnginePreferences: {
        bubble: 'D3'
      },
      layoutPreferences: {
        bubble: 'float'
      }
    };
    const skinConfig = createEmojiSkinConfig();
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: 'emoji-skin', config: skinConfig });

    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证使用了表情皮肤配置
    expect(BubbleView).toHaveBeenCalledWith('emoji', skinConfig, 'float');
  });

  it('应该使用3D皮肤配置创建视图', () => {
    const userConfig = {
      ...createTestUserConfig(),
      activeSkinId: '3d-skin',
      preferredViewType: 'galaxy',
      contentDisplayModePreferences: {
        galaxy: 'textEmoji'
      },
      renderEnginePreferences: {
        galaxy: 'R3F'
      },
      layoutPreferences: {
        galaxy: 'nebula'
      }
    };
    const skinConfig = create3DSkinConfig();
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: '3d-skin', config: skinConfig });

    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证使用了3D皮肤配置
    expect(GalaxyView).toHaveBeenCalledWith('textEmoji', skinConfig, 'nebula');
  });

  it('应该使用游戏风格皮肤配置创建视图', () => {
    const userConfig = {
      ...createTestUserConfig(),
      activeSkinId: 'game-skin',
      preferredViewType: 'wheel',
      contentDisplayModePreferences: {
        wheel: 'animatedEmoji'
      },
      renderEnginePreferences: {
        wheel: 'R3F'
      }
    };
    const skinConfig = createGameSkinConfig();
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: 'game-skin', config: skinConfig });

    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证使用了游戏风格皮肤配置
    expect(R3FWheelView).toHaveBeenCalledWith('animatedEmoji', skinConfig);
  });

  it('应该处理皮肤不支持的视图类型', () => {
    const userConfig = {
      ...createTestUserConfig(),
      activeSkinId: 'minimalist-skin',
      preferredViewType: 'bubble'
    };
    const skinConfig = createMinimalistSkinConfig(); // 极简皮肤不支持气泡视图
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: 'minimalist-skin', config: skinConfig });

    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证警告消息
    expect(console.warn).toHaveBeenCalledWith('Skin minimalist-skin does not support view type bubble, falling back to wheel view');

    // 验证回退到轮盘视图
    expect(SVGWheelView).toHaveBeenCalled();
  });

  it('应该处理皮肤不支持的内容显示模式', () => {
    const userConfig = {
      ...createTestUserConfig(),
      activeSkinId: 'emoji-skin',
      preferredViewType: 'wheel',
      contentDisplayModePreferences: {
        wheel: 'text'
      }
    };
    const skinConfig = createEmojiSkinConfig(); // 表情皮肤不支持纯文本模式
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: 'emoji-skin', config: skinConfig });

    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证警告消息
    expect(console.warn).toHaveBeenCalledWith('Skin emoji-skin does not support content display mode text for view type wheel, falling back to emoji');

    // 验证使用了emoji模式
    expect(D3WheelView).toHaveBeenCalledWith('emoji', skinConfig);
  });

  it('应该处理皮肤不支持的渲染引擎', () => {
    const userConfig = {
      ...createTestUserConfig(),
      activeSkinId: '3d-skin',
      preferredViewType: 'wheel',
      renderEnginePreferences: {
        wheel: 'SVG'
      }
    };
    const skinConfig = create3DSkinConfig(); // 3D皮肤不支持SVG渲染引擎
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: '3d-skin', config: skinConfig });

    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证警告消息
    expect(console.warn).toHaveBeenCalledWith('Skin 3d-skin does not support render engine SVG for view type wheel, falling back to R3F');

    // 验证使用了R3F渲染引擎
    expect(R3FWheelView).toHaveBeenCalled();
  });
});
```

#### 2.2 与情感-表情系统集成测试

```typescript
describe('与情感-表情系统集成测试', () => {
  it('应该渲染情绪数据', () => {
    const userConfig = createTestUserConfig();
    const view = ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    const emotions = [{ id: 'emotion-1', name: 'Happy', emoji: '😊' }];

    view.render(emotions, 1, () => {});

    // 验证视图的render方法被调用
    expect(D3WheelView.prototype.render).toHaveBeenCalledWith(emotions, 1, expect.any(Function));
  });
});
```

### 3. 皮肤兼容性测试

```typescript
describe('皮肤兼容性测试', () => {
  // 准备不同的皮肤配置
  let basicSkinConfig: SkinConfig;
  let minimalistSkinConfig: SkinConfig;
  let emojiSkinConfig: SkinConfig;
  let threeDSkinConfig: SkinConfig;
  let gameSkinConfig: SkinConfig;

  beforeEach(() => {
    basicSkinConfig = createBasicSkinConfig();
    minimalistSkinConfig = createMinimalistSkinConfig();
    emojiSkinConfig = createEmojiSkinConfig();
    threeDSkinConfig = create3DSkinConfig();
    gameSkinConfig = createGameSkinConfig();

    // 模拟皮肤管理器返回不同的皮肤
    (skinManager.getSkinById as any).mockImplementation((id) => {
      switch (id) {
        case 'basic-skin':
          return { id: 'basic-skin', config: basicSkinConfig };
        case 'minimalist-skin':
          return { id: 'minimalist-skin', config: minimalistSkinConfig };
        case 'emoji-skin':
          return { id: 'emoji-skin', config: emojiSkinConfig };
        case '3d-skin':
          return { id: '3d-skin', config: threeDSkinConfig };
        case 'game-skin':
          return { id: 'game-skin', config: gameSkinConfig };
        default:
          return { id: 'basic-skin', config: basicSkinConfig };
      }
    });
  });

  it('应该能够在不同皮肤之间切换', () => {
    // 创建基础用户配置
    const baseUserConfig = createTestUserConfig();

    // 使用基础皮肤
    let userConfig = { ...baseUserConfig, activeSkinId: 'basic-skin' };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(D3WheelView).toHaveBeenCalledWith('textEmoji', basicSkinConfig);

    // 切换到极简皮肤
    vi.clearAllMocks();
    userConfig = { ...baseUserConfig, activeSkinId: 'minimalist-skin' };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(SVGWheelView).toHaveBeenCalledWith('textEmoji', minimalistSkinConfig);

    // 切换到表情皮肤
    vi.clearAllMocks();
    userConfig = { ...baseUserConfig, activeSkinId: 'emoji-skin' };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(D3WheelView).toHaveBeenCalledWith('emoji', emojiSkinConfig);

    // 切换到3D皮肤
    vi.clearAllMocks();
    userConfig = { ...baseUserConfig, activeSkinId: '3d-skin' };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(R3FWheelView).toHaveBeenCalledWith('textEmoji', threeDSkinConfig);

    // 切换到游戏皮肤
    vi.clearAllMocks();
    userConfig = { ...baseUserConfig, activeSkinId: 'game-skin' };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(D3WheelView).toHaveBeenCalledWith('textEmoji', gameSkinConfig);
  });

  it('应该在切换皮肤时保留用户的视图类型偏好', () => {
    // 创建偏好气泡视图的用户配置
    const userConfig = {
      ...createTestUserConfig(),
      preferredViewType: 'bubble',
      contentDisplayModePreferences: {
        bubble: 'emoji'
      }
    };

    // 使用基础皮肤
    userConfig.activeSkinId = 'basic-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(BubbleView).toHaveBeenCalledWith('emoji', basicSkinConfig, 'cluster');

    // 切换到表情皮肤（支持气泡视图）
    vi.clearAllMocks();
    userConfig.activeSkinId = 'emoji-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(BubbleView).toHaveBeenCalledWith('emoji', emojiSkinConfig, 'float');

    // 切换到极简皮肤（不支持气泡视图，应该回退到轮盘视图）
    vi.clearAllMocks();
    userConfig.activeSkinId = 'minimalist-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(console.warn).toHaveBeenCalledWith('Skin minimalist-skin does not support view type bubble, falling back to wheel view');
    expect(SVGWheelView).toHaveBeenCalled();
  });

  it('应该在切换皮肤时保留用户的内容显示模式偏好', () => {
    // 创建偏好文本模式的用户配置
    const userConfig = {
      ...createTestUserConfig(),
      contentDisplayModePreferences: {
        wheel: 'text'
      }
    };

    // 使用基础皮肤
    userConfig.activeSkinId = 'basic-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(D3WheelView).toHaveBeenCalledWith('text', basicSkinConfig);

    // 切换到极简皮肤（支持文本模式）
    vi.clearAllMocks();
    userConfig.activeSkinId = 'minimalist-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(SVGWheelView).toHaveBeenCalledWith('text', minimalistSkinConfig);

    // 切换到表情皮肤（不支持文本模式，应该回退到emoji模式）
    vi.clearAllMocks();
    userConfig.activeSkinId = 'emoji-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(console.warn).toHaveBeenCalledWith('Skin emoji-skin does not support content display mode text for view type wheel, falling back to emoji');
    expect(D3WheelView).toHaveBeenCalledWith('emoji', emojiSkinConfig);
  });

  it('应该在切换皮肤时保留用户的渲染引擎偏好', () => {
    // 创建偏好SVG渲染引擎的用户配置
    const userConfig = {
      ...createTestUserConfig(),
      renderEnginePreferences: {
        wheel: 'SVG'
      }
    };

    // 使用基础皮肤
    userConfig.activeSkinId = 'basic-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(SVGWheelView).toHaveBeenCalledWith('textEmoji', basicSkinConfig);

    // 切换到极简皮肤（支持SVG渲染引擎）
    vi.clearAllMocks();
    userConfig.activeSkinId = 'minimalist-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(SVGWheelView).toHaveBeenCalledWith('text', minimalistSkinConfig);

    // 切换到3D皮肤（不支持SVG渲染引擎，应该回退到R3F）
    vi.clearAllMocks();
    userConfig.activeSkinId = '3d-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(console.warn).toHaveBeenCalledWith('Skin 3d-skin does not support render engine SVG for view type wheel, falling back to R3F');
    expect(R3FWheelView).toHaveBeenCalledWith('textEmoji', threeDSkinConfig);
  });

  it('应该处理皮肤之间的布局差异', () => {
    // 创建用户配置
    const userConfig = {
      ...createTestUserConfig(),
      preferredViewType: 'card',
      layoutPreferences: {
        card: 'grid'
      }
    };

    // 使用基础皮肤
    userConfig.activeSkinId = 'basic-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(CardView).toHaveBeenCalledWith('emoji', basicSkinConfig, 'grid');

    // 切换到极简皮肤（使用list布局）
    vi.clearAllMocks();
    userConfig.activeSkinId = 'minimalist-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(CardView).toHaveBeenCalledWith('text', minimalistSkinConfig, 'list');

    // 切换到游戏皮肤
    vi.clearAllMocks();
    userConfig.activeSkinId = 'game-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    expect(CardView).toHaveBeenCalledWith('textEmoji', gameSkinConfig, 'grid');
  });

  it('应该处理皮肤特定的配置选项', () => {
    // 创建用户配置
    const userConfig = createTestUserConfig();

    // 使用游戏皮肤（有特殊的悬停和选择效果）
    userConfig.activeSkinId = 'game-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证传递给视图的皮肤配置包含特殊效果
    const passedConfig = (D3WheelView as any).mock.calls[0][1];
    expect(passedConfig.view_configs.wheel.highlightOnHover).toBe(true);
    expect(passedConfig.view_configs.wheel.highlightColor).toBe('#ff4081');
    expect(passedConfig.view_configs.wheel.selectionAnimation).toBe('pulse');
    expect(passedConfig.view_configs.wheel.selectionIndicator).toBe('glow');
  });

  it('应该处理皮肤之间的颜色方案差异', () => {
    // 创建用户配置
    const userConfig = createTestUserConfig();

    // 使用基础皮肤
    userConfig.activeSkinId = 'basic-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证传递给视图的皮肤配置包含正确的颜色
    let passedConfig = (D3WheelView as any).mock.calls[0][1];
    expect(passedConfig.colors.primary).toBe('#3498db');
    expect(passedConfig.colors.background).toBe('#ffffff');

    // 切换到3D皮肤
    vi.clearAllMocks();
    userConfig.activeSkinId = '3d-skin';
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证传递给视图的皮肤配置包含正确的颜色
    passedConfig = (R3FWheelView as any).mock.calls[0][1];
    expect(passedConfig.colors.primary).toBe('#4a148c');
    expect(passedConfig.colors.background).toBe('#000000');
  });
});

### 4. 性能测试

```typescript
describe('性能测试', () => {
  it('应该在合理时间内创建视图', () => {
    const userConfig = createTestUserConfig();

    const startTime = performance.now();
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    const endTime = performance.now();

    // 验证创建视图的时间不超过100ms
    expect(endTime - startTime).toBeLessThan(100);
  });

  it('应该在切换皮肤时保持良好性能', () => {
    const userConfig = createTestUserConfig();
    const skinIds = ['basic-skin', 'minimalist-skin', 'emoji-skin', '3d-skin', 'game-skin'];

    // 测量切换皮肤的总时间
    const startTime = performance.now();

    for (const skinId of skinIds) {
      vi.clearAllMocks();
      const config = { ...userConfig, activeSkinId: skinId };
      ViewFactory.createViewFromUserConfig(config, skinManager);
    }

    const endTime = performance.now();
    const averageTime = (endTime - startTime) / skinIds.length;

    // 验证平均切换时间不超过50ms
    expect(averageTime).toBeLessThan(50);
  });

  it('应该在处理复杂皮肤时保持良好性能', () => {
    // 创建一个复杂的皮肤配置
    const complexSkinConfig = {
      ...createBasicSkinConfig(),
      view_configs: {
        ...createBasicSkinConfig().view_configs,
        wheel: {
          ...createBasicSkinConfig().view_configs?.wheel,
          // 添加大量配置选项
          ...Array.from({ length: 100 }, (_, i) => ({ [`option${i}`]: `value${i}` })).reduce((acc, val) => ({ ...acc, ...val }), {})
        }
      }
    };

    (skinManager.getSkinById as any).mockReturnValueOnce({ id: 'complex-skin', config: complexSkinConfig });

    const userConfig = {
      ...createTestUserConfig(),
      activeSkinId: 'complex-skin'
    };

    const startTime = performance.now();
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);
    const endTime = performance.now();

    // 验证创建复杂皮肤视图的时间不超过150ms
    expect(endTime - startTime).toBeLessThan(150);
  });

  it('应该在处理大量情绪数据时保持良好性能', () => {
    const userConfig = createTestUserConfig();
    (skinManager.getSkinById as any).mockReturnValueOnce({ id: 'skin-1', config: createBasicSkinConfig() });

    const view = ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 创建大量情绪数据
    const largeEmotionSet = Array.from({ length: 1000 }, (_, i) => ({
      id: `emotion-${i}`,
      name: `Emotion ${i}`,
      emoji: '😊'
    }));

    const startTime = performance.now();
    view.render(largeEmotionSet, 1, () => {});
    const endTime = performance.now();

    // 验证渲染大量情绪数据的时间不超过200ms
    expect(endTime - startTime).toBeLessThan(200);
  });

  it('应该在频繁切换视图类型时保持良好性能', () => {
    const userConfig = createTestUserConfig();
    const viewTypes: ViewType[] = ['wheel', 'card', 'bubble', 'galaxy'];

    // 测量切换视图类型的总时间
    const startTime = performance.now();

    for (const viewType of viewTypes) {
      vi.clearAllMocks();
      ViewFactory.createViewFromUserConfig(userConfig, skinManager, viewType);
    }

    const endTime = performance.now();
    const averageTime = (endTime - startTime) / viewTypes.length;

    // 验证平均切换时间不超过40ms
    expect(averageTime).toBeLessThan(40);
  });
});
```

## 测试移除 createView 兼容性方法的影响

### 1. 识别使用 createView 方法的代码

首先，我们需要识别所有使用 `createView` 方法的代码。可以使用以下命令：

```bash
grep -r "createView(" --include="*.tsx" --include="*.ts" src/
```

### 2. 创建迁移测试

为每个使用 `createView` 方法的地方创建测试，确保使用 `createViewFromUserConfig` 方法后功能正常：

```typescript
describe('从createView迁移到createViewFromUserConfig', () => {
  it('应该使用createViewFromUserConfig替代createView', () => {
    // 原来的代码
    // const view = ViewFactory.createView('wheel', 'textEmoji', skinConfig);

    // 新的代码
    const userConfig = {
      ...createTestUserConfig(),
      preferredViewType: 'wheel',
      contentDisplayModePreferences: {
        wheel: 'textEmoji'
      }
    };
    const view = ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证功能正常
    expect(view.render).toBeDefined();
    expect(typeof view.render).toBe('function');
  });
});
```

### 3. 测试迁移后的代码

在移除 `createView` 方法后，运行所有测试，确保没有任何功能受到影响。

## 测试新增功能

随着 ViewFactory 功能的增强，我们需要为新增的功能创建测试：

### 1. 测试新的视图类型

```typescript
describe('新视图类型测试', () => {
  it('应该创建列表视图', () => {
    const userConfig = {
      ...createTestUserConfig(),
      preferredViewType: 'list'
    };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证创建了列表视图
    expect(ListView).toHaveBeenCalled();
  });

  it('应该创建网格视图', () => {
    const userConfig = {
      ...createTestUserConfig(),
      preferredViewType: 'grid'
    };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证创建了网格视图
    expect(GridView).toHaveBeenCalled();
  });
});
```

### 2. 测试新的渲染引擎

```typescript
describe('新渲染引擎测试', () => {
  it('应该创建Canvas渲染引擎的视图', () => {
    const userConfig = {
      ...createTestUserConfig(),
      renderEnginePreferences: {
        wheel: 'Canvas'
      }
    };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证创建了Canvas渲染引擎的视图
    expect(CanvasWheelView).toHaveBeenCalled();
  });
});
```

### 3. 测试新的内容显示策略

```typescript
describe('新内容显示策略测试', () => {
  it('应该创建图片模式的视图', () => {
    const userConfig = {
      ...createTestUserConfig(),
      contentDisplayModePreferences: {
        wheel: 'image'
      }
    };
    ViewFactory.createViewFromUserConfig(userConfig, skinManager);

    // 验证创建了图片模式的视图
    expect(D3WheelView).toHaveBeenCalledWith('image', expect.any(Object));
  });
});
```

## 结论

通过这个详细的测试计划，我们可以确保 ViewFactory 在移除 `createView` 兼容性方法后仍然正常工作，并且新增的功能也能正确实现。测试覆盖了单元测试、集成测试、性能测试和迁移测试，确保了代码的质量和可靠性。
