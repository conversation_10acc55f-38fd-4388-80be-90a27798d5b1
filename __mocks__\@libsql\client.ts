import type { BatchMode, Client, InStatement, ResultSet, Transaction } from '@libsql/client'; // Import types for better mocking
import { vi } from 'vitest';

// Helper to create a mock transaction
const createMockTransaction = (): Transaction => {
  const mockTx: Transaction = {
    execute: vi.fn(),
    batch: vi.fn(),
    executeMultiple: vi.fn(), // Added based on SqlExecutor
    commit: vi.fn().mockResolvedValue(undefined), // Mock promise resolution
    rollback: vi.fn().mockResolvedValue(undefined), // Mock promise resolution
    close: vi.fn(),
    get closed() {
      const commitCalled = (mockTx.commit as vi.Mock).mock.calls.length > 0;
      const rollbackCalled = (mockTx.rollback as vi.Mock).mock.calls.length > 0;
      const closeCalled = (mockTx.close as vi.Mock).mock.calls.length > 0;
      return commitCalled || rollbackCalled || closeCalled;
    },
  };
  return mockTx;
};

export const createClient = vi.fn((): Client => {
  const mockClientInstance: Client = {
    execute: vi.fn(),
    batch: vi.fn(), // Client also has batch directly
    executeMultiple: vi.fn(),
    transaction: vi.fn((mode?: 'read' | 'write' | 'deferred') => createMockTransaction()),
    sync: vi.fn().mockResolvedValue(undefined), // Mock common methods
    close: vi.fn(),
    get closed() {
      return (mockClientInstance.close as vi.Mock).mock.calls.length > 0;
    },
    protocol: 'http', // Added protocol property, common for libsql http client
  };
  return mockClientInstance;
});

// It's generally not needed to re-export types from a mock file itself,
// as the code under test should import types from the original library path.
// export type { InStatement, ResultSet, Transaction, Client };
