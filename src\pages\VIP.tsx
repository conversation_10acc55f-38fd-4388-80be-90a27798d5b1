/**
 * VIP页面
 * 显示VIP计划、状态和购买功能
 */

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useAuth';
import { useVip } from '@/hooks/useVip';
import {
  AlertCircle,
  BarChart3,
  Check,
  CreditCard,
  Crown,
  Download,
  Loader2,
  Palette,
  Shield,
  Sparkles,
  Star,
  Wifi,
  WifiOff,
  X,
} from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

export const VIP: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { isAuthenticated, isOnline } = useAuth();
  const {
    isVip,
    vipStatus,
    daysRemaining,
    vipPlans,
    isLoading,
    error,
    refresh,
    purchaseVip,
    hasFeature,
    canAccessPremiumSkins,
    canExportData,
    hasAdvancedAnalytics,
  } = useVip();

  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // 处理VIP购买
  const handlePurchaseVip = async (planId: string) => {
    if (!isAuthenticated) {
      toast.error(t('vip.login_required', { fallback: '请先登录' }));
      navigate('/login');
      return;
    }

    if (!isOnline) {
      toast.error(t('vip.purchase_requires_internet', { fallback: 'VIP购买需要网络连接' }));
      return;
    }

    try {
      setIsProcessing(true);
      setSelectedPlan(planId);

      // 模拟支付方式选择
      const paymentMethodId = 'pm_test_card';

      const result = await purchaseVip(planId, paymentMethodId);

      if (result.success) {
        toast.success(t('vip.purchase_success', { fallback: 'VIP购买成功！' }));
        await refresh();
      } else {
        toast.error(result.error || t('vip.purchase_failed', { fallback: 'VIP购买失败' }));
      }
    } catch (error) {
      console.error('VIP purchase error:', error);
      toast.error(t('vip.purchase_error', { fallback: 'VIP购买时发生错误' }));
    } finally {
      setIsProcessing(false);
      setSelectedPlan(null);
    }
  };

  // VIP功能列表
  const vipFeatures = [
    {
      id: 'unlimited_mood_entries',
      icon: <BarChart3 className="h-5 w-5" />,
      title: t('vip.feature.unlimited_entries', { fallback: '无限心情记录' }),
      description: t('vip.feature.unlimited_entries_desc', {
        fallback: '记录无限次心情，不受限制',
      }),
    },
    {
      id: 'advanced_analytics',
      icon: <BarChart3 className="h-5 w-5" />,
      title: t('vip.feature.advanced_analytics', { fallback: '高级分析' }),
      description: t('vip.feature.advanced_analytics_desc', {
        fallback: '深度分析您的情绪模式和趋势',
      }),
    },
    {
      id: 'premium_skins',
      icon: <Palette className="h-5 w-5" />,
      title: t('vip.feature.premium_skins', { fallback: '高级皮肤' }),
      description: t('vip.feature.premium_skins_desc', { fallback: '解锁所有高级主题和皮肤' }),
    },
    {
      id: 'export_data',
      icon: <Download className="h-5 w-5" />,
      title: t('vip.feature.export_data', { fallback: '数据导出' }),
      description: t('vip.feature.export_data_desc', { fallback: '导出您的所有数据进行备份' }),
    },
    {
      id: 'priority_support',
      icon: <Shield className="h-5 w-5" />,
      title: t('vip.feature.priority_support', { fallback: '优先支持' }),
      description: t('vip.feature.priority_support_desc', { fallback: '享受优先客户支持服务' }),
    },
    {
      id: 'ad_free_experience',
      icon: <Star className="h-5 w-5" />,
      title: t('vip.feature.ad_free', { fallback: '无广告体验' }),
      description: t('vip.feature.ad_free_desc', { fallback: '享受完全无广告的使用体验' }),
    },
  ];

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Crown className="h-8 w-8 text-yellow-500" />
          <h1 className="text-3xl font-bold">{t('vip.title', { fallback: 'VIP会员' })}</h1>
        </div>
        <div className="flex items-center">
          {isOnline ? (
            <Wifi className="h-4 w-4 text-green-500" />
          ) : (
            <WifiOff className="h-4 w-4 text-red-500" />
          )}
        </div>
      </div>

      {/* 网络状态提示 */}
      {!isOnline && (
        <Alert variant="destructive" className="mb-6">
          <WifiOff className="h-4 w-4" />
          <AlertDescription>
            {t('vip.offline_warning', { fallback: '当前离线，VIP功能可能受限' })}
          </AlertDescription>
        </Alert>
      )}

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* VIP状态卡片 */}
      {isAuthenticated && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isVip ? (
                <>
                  <Crown className="h-5 w-5 text-yellow-500" />
                  {t('vip.status.active', { fallback: 'VIP会员' })}
                </>
              ) : (
                <>
                  <Crown className="h-5 w-5 text-muted-foreground" />
                  {t('vip.status.inactive', { fallback: '普通用户' })}
                </>
              )}
            </CardTitle>
            <CardDescription>
              {isVip
                ? t('vip.status.active_desc', { fallback: '您正在享受VIP特权' })
                : t('vip.status.inactive_desc', { fallback: '升级到VIP解锁更多功能' })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isVip && vipStatus?.expiresAt && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t('vip.expires_in', { fallback: '剩余时间' })}</span>
                  <span className="font-medium">
                    {daysRemaining} {t('vip.days', { fallback: '天' })}
                  </span>
                </div>
                <Progress value={Math.max(0, Math.min(100, (daysRemaining / 365) * 100))} />
                <div className="text-xs text-muted-foreground">
                  {t('vip.expires_on', {
                    date: vipStatus.expiresAt.toLocaleDateString(),
                    fallback: `到期时间：${vipStatus.expiresAt.toLocaleDateString()}`,
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* VIP功能展示 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            {t('vip.features.title', { fallback: 'VIP特权' })}
          </CardTitle>
          <CardDescription>
            {t('vip.features.description', { fallback: '升级VIP解锁以下所有功能' })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {vipFeatures.map((feature) => {
              const hasThisFeature = hasFeature(feature.id);
              return (
                <div
                  key={feature.id}
                  className={`flex items-start gap-3 p-3 rounded-lg border ${
                    hasThisFeature ? 'bg-green-50 border-green-200' : 'bg-muted/50'
                  }`}
                >
                  <div
                    className={`mt-1 ${hasThisFeature ? 'text-green-600' : 'text-muted-foreground'}`}
                  >
                    {feature.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{feature.title}</h4>
                      {hasThisFeature ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <X className="h-4 w-4 text-muted-foreground" />
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{feature.description}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* VIP计划 */}
      {!isVip && (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-center">
            {t('vip.plans.title', { fallback: '选择您的VIP计划' })}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {vipPlans.map((plan) => (
              <Card
                key={plan.id}
                className={`relative ${plan.duration === 'yearly' ? 'border-primary' : ''}`}
              >
                {plan.duration === 'yearly' && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                    {t('vip.plans.recommended', { fallback: '推荐' })}
                  </Badge>
                )}
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {plan.name}
                    {plan.discount && <Badge variant="secondary">-{plan.discount}%</Badge>}
                  </CardTitle>
                  <CardDescription>
                    <div className="text-2xl font-bold">
                      ${plan.price}
                      <span className="text-sm font-normal text-muted-foreground">
                        /
                        {plan.duration === 'monthly'
                          ? t('vip.plans.month', { fallback: '月' })
                          : t('vip.plans.year', { fallback: '年' })}
                      </span>
                    </div>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-600" />
                        <span className="text-sm">
                          {t(`vip.feature.${feature}`, { fallback: feature })}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={() => handlePurchaseVip(plan.id)}
                    disabled={!isAuthenticated || !isOnline || isProcessing}
                  >
                    {isProcessing && selectedPlan === plan.id ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t('vip.purchasing', { fallback: '购买中...' })}
                      </>
                    ) : (
                      <>
                        <CreditCard className="mr-2 h-4 w-4" />
                        {t('vip.purchase', { fallback: '立即购买' })}
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* 登录提示 */}
      {!isAuthenticated && (
        <Card className="mt-6">
          <CardContent className="pt-6">
            <div className="text-center">
              <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {t('vip.login_to_upgrade', { fallback: '登录以升级VIP' })}
              </h3>
              <p className="text-muted-foreground mb-4">
                {t('vip.login_description', { fallback: '登录您的账户以购买VIP会员' })}
              </p>
              <Button onClick={() => navigate('/login')}>
                {t('vip.login_now', { fallback: '立即登录' })}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
