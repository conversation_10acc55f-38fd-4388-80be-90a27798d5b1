-- Server-Only Schema Extensions
-- This file contains sensitive tables and fields that should NEVER be synced to clients
-- These tables contain authentication data, payment information, and other sensitive data

PRAGMA foreign_keys=OFF;
PRAGMA ignore_check_constraints=ON;

-- Server-Only User Authentication Table
-- Contains sensitive authentication data that must never leave the server
DROP TABLE IF EXISTS user_auth;
CREATE TABLE IF NOT EXISTS user_auth (
    user_id TEXT PRIMARY KEY NOT NULL,
    password_hash TEXT NOT NULL, -- bcrypt/argon2 hash
    password_salt TEXT, -- Additional salt if needed
    password_reset_token TEXT, -- For password reset functionality
    password_reset_expires_at TIMESTAMP, -- Token expiration
    email_verification_token TEXT, -- For email verification
    email_verification_expires_at TIMESTAMP, -- Token expiration
    two_factor_secret TEXT, -- TOTP secret for 2FA
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    backup_codes TEXT, -- JSON array of backup codes for 2FA
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMES<PERSON><PERSON>, -- Account lockout timestamp
    last_password_change TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Server-Only Payment Transactions Table
-- Contains sensitive financial data
DROP TABLE IF EXISTS payment_transactions;
CREATE TABLE IF NOT EXISTS payment_transactions (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('subscription', 'skin_purchase', 'emoji_set_purchase', 'refund')),
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')),
    amount REAL NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    
    -- Payment provider details
    payment_provider TEXT NOT NULL, -- 'stripe', 'paypal', 'apple_pay', 'google_pay'
    provider_transaction_id TEXT, -- External transaction ID
    provider_customer_id TEXT, -- External customer ID
    provider_payment_method_id TEXT, -- External payment method ID
    
    -- Transaction metadata
    description TEXT,
    metadata TEXT, -- JSON object for additional data
    
    -- Timestamps
    initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    failed_at TIMESTAMP,
    refunded_at TIMESTAMP,
    
    -- Audit trail
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Server-Only User Activity Log
-- Tracks sensitive user activities for security and analytics
DROP TABLE IF EXISTS user_activity_log;
CREATE TABLE IF NOT EXISTS user_activity_log (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT,
    session_id TEXT,
    activity_type TEXT NOT NULL, -- 'login', 'logout', 'password_change', 'purchase', etc.
    activity_details TEXT, -- JSON object with activity-specific data
    ip_address TEXT,
    user_agent TEXT,
    location_country TEXT,
    location_city TEXT,
    risk_score REAL, -- Security risk assessment score
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES user_sessions(id) ON DELETE SET NULL
);

-- Server-Only Admin Actions Log
-- Tracks administrative actions for audit purposes
DROP TABLE IF EXISTS admin_actions_log;
CREATE TABLE IF NOT EXISTS admin_actions_log (
    id TEXT PRIMARY KEY NOT NULL,
    admin_user_id TEXT NOT NULL,
    target_user_id TEXT, -- User being acted upon (if applicable)
    action_type TEXT NOT NULL, -- 'ban_user', 'grant_vip', 'refund_transaction', etc.
    action_details TEXT, -- JSON object with action-specific data
    reason TEXT, -- Reason for the action
    ip_address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (target_user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Server-Only API Keys Table
-- For managing API access tokens and keys
DROP TABLE IF EXISTS api_keys;
CREATE TABLE IF NOT EXISTS api_keys (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT, -- NULL for system/service keys
    key_hash TEXT NOT NULL, -- Hashed API key
    key_name TEXT NOT NULL, -- Human-readable name
    permissions TEXT NOT NULL, -- JSON array of permissions
    rate_limit_per_hour INTEGER DEFAULT 1000,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Server-Only Rate Limiting Table
-- Tracks API usage for rate limiting
DROP TABLE IF EXISTS rate_limit_tracking;
CREATE TABLE IF NOT EXISTS rate_limit_tracking (
    id TEXT PRIMARY KEY NOT NULL,
    identifier TEXT NOT NULL, -- IP address, user ID, or API key
    identifier_type TEXT NOT NULL CHECK (identifier_type IN ('ip', 'user', 'api_key')),
    endpoint TEXT NOT NULL, -- API endpoint being tracked
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP NOT NULL,
    window_end TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for server-only tables
CREATE INDEX IF NOT EXISTS idx_user_auth_password_reset_token ON user_auth(password_reset_token);
CREATE INDEX IF NOT EXISTS idx_user_auth_email_verification_token ON user_auth(email_verification_token);
CREATE INDEX IF NOT EXISTS idx_user_auth_failed_attempts ON user_auth(failed_login_attempts);

CREATE INDEX IF NOT EXISTS idx_payment_transactions_user_id ON payment_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_provider_transaction_id ON payment_transactions(provider_transaction_id);

CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_activity_type ON user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_created_at ON user_activity_log(created_at);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_ip_address ON user_activity_log(ip_address);

CREATE INDEX IF NOT EXISTS idx_admin_actions_log_admin_user_id ON admin_actions_log(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_log_target_user_id ON admin_actions_log(target_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_log_action_type ON admin_actions_log(action_type);

CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);

CREATE INDEX IF NOT EXISTS idx_rate_limit_tracking_identifier ON rate_limit_tracking(identifier, identifier_type);
CREATE INDEX IF NOT EXISTS idx_rate_limit_tracking_endpoint ON rate_limit_tracking(endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limit_tracking_window ON rate_limit_tracking(window_start, window_end);

PRAGMA ignore_check_constraints=OFF;
PRAGMA foreign_keys=ON;
